(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7233],{77233:(v,l,o)=>{o.r(l),o.d(l,{MboTransfiyaAskSourcePageModule:()=>F});var m=o(17007),u=o(78007),i=o(30263),f=o(15861),r=o(24495),s=o(39904),h=o(95437),A=o(57544),C=o(20827),x=o(52484),y=o(42789),k=o(17698),T=o(73004),e=o(99877),P=o(48774),S=o(98853),M=o(2460),I=o(35641),z=o(23436),E=o(45542);function O(a,d){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275element(1,"bocc-ballot",13),e.\u0275\u0275elementStart(2,"bocc-icon",14),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const c=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(c.onRemoveContact())}),e.\u0275\u0275elementEnd()()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("title",t.phone.contact)("subtitle",t.phone.numberFormat)}}function _(a,d){1&a&&(e.\u0275\u0275elementStart(0,"div",15),e.\u0275\u0275element(1,"bocc-icon",16),e.\u0275\u0275elementStart(2,"span",17),e.\u0275\u0275text(3," N\xfamero de contacto debe iniciar con n\xfamero 3 "),e.\u0275\u0275elementEnd()())}const g=s.Z6.TRANSFERS.TRANSFIYA.ASK;let j=(()=>{class a{constructor(t,n,c,p,b){this.mboProvider=t,this.contactsProvider=n,this.requestConfiguration=c,this.managerTransfiya=p,this.cancelProvider=b,this.confirmation=!1,this.requesting=!0,this.requestingContact=!1,this.backAction={id:"btn_transfiya-ask-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(g.DESTINATION)}},this.cancelAction={id:"btn_transfiya-ask-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}},this.phoneControl=new A.FormControl({validators:[r.C1,x.O,(0,r.Dx)(10)]})}ngOnInit(){this.contactsProvider.subscribe(({number:t,name:n})=>{this.phone=new y.Ap(t,n)}),this.initializatedConfiguration()}get contactInvalid(){return!!this.phone&&!(0,C.lE)(this.phone.number)}get disabled(){return this.phone?this.contactInvalid:this.phoneControl.invalid}onContacts(){var t=this;return(0,f.Z)(function*(){t.requestingContact=!0,yield t.contactsProvider.open(),t.requestingContact=!1})()}onRemoveContact(){this.phone=void 0}onSubmit(){this.managerTransfiya.setContact(this.getTransfiyaPhone()).when({success:()=>{this.mboProvider.navigation.next(g.AMOUNT)}})}initializatedConfiguration(){var t=this;return(0,f.Z)(function*(){(yield t.requestConfiguration.source()).when({success:({confirmation:n,phone:c,product:p})=>{if(!p)return t.mboProvider.navigation.back(g.DESTINATION);if(t.confirmation=n,c){const{number:b,contact:N}=c;N?t.phone=c:t.phoneControl.setValue(b)}}},()=>{t.requesting=!1})})()}getTransfiyaPhone(){return this.phone||new y.Ap(this.phoneControl.value)}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(h.ZL),e.\u0275\u0275directiveInject(h.i),e.\u0275\u0275directiveInject(k.UT),e.\u0275\u0275directiveInject(k.Pm),e.\u0275\u0275directiveInject(T.c))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-ask-source-page"]],decls:17,vars:8,consts:[[1,"mbo-transfiya-ask-source-page__content"],[1,"mbo-transfiya-ask-source-page__header"],["title","Origen","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfiya-ask-source-page__body"],[1,"mbo-transfiya-ask-source-page__message","subtitle2-medium"],["class","mbo-transfiya-ask-source-page__contact",4,"ngIf"],["class","mbo-transfiya-ask-source-page__error",4,"ngIf"],["elementId","txt_transfiya-ask-source_phone","label","Ingresa el n\xfamero de celular","placeholder","************","type","phone","prefix","+57",3,"hidden","formControl"],[1,"mbo-transfiya-ask-source-page__info","subtitle2-medium"],["id","btn_transfiya-ask-source_contacts","icon","user-contacts",3,"disabled","click"],[1,"mbo-transfiya-ask-source-page__footer"],["id","btn_transfiya-ask-source_submit","bocc-button","raised",3,"disabled","click"],[1,"mbo-transfiya-ask-source-page__contact"],["icon","mobile-contact",3,"title","subtitle"],["icon","close",3,"click"],[1,"mbo-transfiya-ask-source-page__error"],["icon","error"],[1,"caption-medium"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," \xbfA qui\xe9n deseas solicitarle hoy? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,O,3,2,"div",5),e.\u0275\u0275template(7,_,4,0,"div",6),e.\u0275\u0275element(8,"bocc-growing-box",7),e.\u0275\u0275elementStart(9,"p",8),e.\u0275\u0275text(10," Tambi\xe9n puedes seleccionar uno de tus contactos "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"bocc-card-category",9),e.\u0275\u0275listener("click",function(){return n.onContacts()}),e.\u0275\u0275text(12," Contactos del celular "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(13,"div",10)(14,"button",11),e.\u0275\u0275listener("click",function(){return n.onSubmit()}),e.\u0275\u0275elementStart(15,"span"),e.\u0275\u0275text(16,"Continuar"),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",n.backAction)("rightAction",n.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",n.phone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.contactInvalid),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",n.phone)("formControl",n.phoneControl),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",n.requestingContact),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",n.disabled))},dependencies:[m.NgIf,P.J,S.s,M.Z,I.d,z.D,E.P],styles:["/*!\n * MBO TransfiyaAskSource Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 05/Jul/2022\n * Updated: 12/Mar/2024\n*/mbo-transfiya-ask-source-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x2);justify-content:space-between}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__contact{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;display:flex;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__contact>bocc-icon{color:var(--color-carbon-lighter-700)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__error{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__error bocc-icon{font-size:var(--sizing-x8);width:var(--sizing-x8);height:var(--sizing-x8);color:var(--color-semantic-danger-700)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__error span{color:var(--color-carbon-lighter-700)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__message{color:var(--color-carbon-darker-1000)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__info{color:var(--color-carbon-darker-1000)}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-ask-source-page .mbo-transfiya-ask-source-page__footer button{width:100%}\n"],encapsulation:2}),a})(),F=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:j}]),i.Jx,i.s,i.Zl,i.dH,i.D0,i.P8]}),a})()},20827:(v,l,o)=>{function u(r){return"3"===r.charAt(0)}o.d(l,{lE:()=>u}),o(87903)},52484:(v,l,o)=>{o.d(l,{O:()=>f,b:()=>r});var m=o(20827);const u=/^[a-z|A-Z|0-9|@|.]*$/,i=/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,f=s=>s&&!(0,m.lE)(s)?{id:"firstCharPhone",message:"Campo debe iniciar con n\xfamero 3"}:null,r=s=>!s||i.test(s)||u.test(s)?null:{id:"tagAvalOrKey",message:"Campo no cumple con los criterios para ser una llave"}}}]);