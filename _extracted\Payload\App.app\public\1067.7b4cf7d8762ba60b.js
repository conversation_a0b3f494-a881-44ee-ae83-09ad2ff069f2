(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1067],{61443:(i,a,n)=>{n.d(a,{QT:()=>E,T1:()=>l,ez:()=>e,tP:()=>s});var s=(()=>{return(t=s||(s={})).Documents="DOCUMENTS",t.Data="DATA",t.Library="LIBRARY",t.Cache="CACHE",t.External="EXTERNAL",t.ExternalStorage="EXTERNAL_STORAGE",s;var t})(),e=(()=>{return(t=e||(e={})).UTF8="utf8",t.ASCII="ascii",t.UTF16="utf16",e;var t})();const E=s,l=e},81067:(i,a,n)=>{n.r(a),n.d(a,{Directory:()=>E.tP,Encoding:()=>E.ez,Filesystem:()=>l,FilesystemDirectory:()=>E.QT,FilesystemEncoding:()=>E.T1});var s=n(17737),E=n(61443);const l=(0,s.registerPlugin)("Filesystem",{web:()=>n.e(6364).then(n.bind(n,96364)).then(t=>new t.FilesystemWeb)})}}]);