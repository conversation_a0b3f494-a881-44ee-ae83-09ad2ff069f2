(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6390],{96390:(p,d,i)=>{i.r(d),i.d(d,{startFocusVisible:()=>h});const u="ion-focused",w=["Tab","ArrowDown","Space","Escape"," ","Shift","Enter","ArrowLeft","ArrowRight","ArrowUp","Home","End"],h=n=>{let a=[],c=!0;const e=n?n.shadowRoot:document,l=n||document.body,o=t=>{a.forEach(s=>s.classList.remove(u)),t.forEach(s=>s.classList.add(u)),a=t},r=()=>{c=!1,o([])},f=t=>{c=w.includes(t.key),c||o([])},v=t=>{if(c&&void 0!==t.composedPath){const s=t.composedPath().filter(L=>!!L.classList&&L.classList.contains("ion-focusable"));o(s)}},E=()=>{e.activeElement===l&&o([])};return e.addEventListener("keydown",f),e.addEventListener("focusin",v),e.addEventListener("focusout",E),e.addEventListener("touchstart",r,{passive:!0}),e.addEventListener("mousedown",r),{destroy:()=>{e.removeEventListener("keydown",f),e.removeEventListener("focusin",v),e.removeEventListener("focusout",E),e.removeEventListener("touchstart",r),e.removeEventListener("mousedown",r)},setFocus:o}}}}]);