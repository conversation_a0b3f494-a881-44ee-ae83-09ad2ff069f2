(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4650],{94650:(FM,el,ir)=>{ir.r(el),ir.d(el,{ANALYZE_FOR_ENTRY_COMPONENTS:()=>Kg,ANIMATION_MODULE_TYPE:()=>L_,APP_BOOTSTRAP_LISTENER:()=>_h,APP_ID:()=>ah,APP_INITIALIZER:()=>sh,ApplicationInitStatus:()=>Eo,ApplicationModule:()=>CM,ApplicationRef:()=>To,Attribute:()=>cc,COMPILER_OPTIONS:()=>dh,CUSTOM_ELEMENTS_SCHEMA:()=>Mc,ChangeDetectionStrategy:()=>Me,ChangeDetectorRef:()=>Lh,Compiler:()=>$_,CompilerFactory:()=>B_,Component:()=>w_,ComponentFactory:()=>Wr,ComponentFactoryResolver:()=>Bn,ComponentRef:()=>bu,ContentChild:()=>Xg,ContentChildren:()=>Jg,DEFAULT_CURRENCY_CODE:()=>j_,DebugElement:()=>or,DebugEventListener:()=>lM,DebugNode:()=>Qa,DefaultIterableDiffer:()=>$h,Directive:()=>ih,ENVIRONMENT_INITIALIZER:()=>ki,ElementRef:()=>Un,EmbeddedViewRef:()=>aM,EnvironmentInjector:()=>It,ErrorHandler:()=>qn,EventEmitter:()=>Ue,Host:()=>hc,HostBinding:()=>x_,HostListener:()=>S_,INJECTOR:()=>Fi,Inject:()=>Ar,InjectFlags:()=>w,Injectable:()=>iv,InjectionToken:()=>V,Injector:()=>at,Input:()=>b_,IterableDiffers:()=>Ja,KeyValueDiffers:()=>Xa,LOCALE_ID:()=>Co,MissingTranslationStrategy:()=>qe,ModuleWithComponentFactories:()=>uh,NO_ERRORS_SCHEMA:()=>Ec,NgModule:()=>P_,NgModuleFactory:()=>zf,NgModuleRef:()=>pn,NgProbeToken:()=>K_,NgZone:()=>be,Optional:()=>On,Output:()=>N_,PACKAGE_ROOT_URL:()=>O_,PLATFORM_ID:()=>A_,PLATFORM_INITIALIZER:()=>ch,Pipe:()=>T_,PlatformRef:()=>Wa,Query:()=>Pn,QueryList:()=>vo,ReflectiveInjector:()=>Yt,ReflectiveKey:()=>et,Renderer2:()=>Oy,RendererFactory2:()=>xu,RendererStyleFlags2:()=>Ve,ResolvedReflectiveFactory:()=>Ku,Sanitizer:()=>Su,SecurityContext:()=>W,Self:()=>Or,SimpleChange:()=>Dl,SkipSelf:()=>Bt,TRANSLATIONS:()=>V_,TRANSLATIONS_FORMAT:()=>H_,TemplateRef:()=>nr,Testability:()=>Y_,TestabilityRegistry:()=>Ih,Type:()=>Sr,VERSION:()=>Ru,Version:()=>Pu,ViewChild:()=>tm,ViewChildren:()=>em,ViewContainerRef:()=>Do,ViewEncapsulation:()=>Ge,ViewRef:()=>Fh,asNativeElements:()=>cM,assertPlatform:()=>bh,createComponent:()=>LM,createEnvironmentInjector:()=>Ma,createNgModule:()=>Zf,createNgModuleRef:()=>VD,createPlatform:()=>Ch,createPlatformFactory:()=>Th,defineInjectable:()=>eg,destroyPlatform:()=>eM,enableProdMode:()=>rM,forwardRef:()=>sr,getDebugNode:()=>gn,getModuleFactory:()=>oM,getNgModuleById:()=>iM,getPlatform:()=>wo,importProvidersFrom:()=>Du,inject:()=>sl,isDevMode:()=>nM,isStandalone:()=>bt,makeEnvironmentProviders:()=>_y,platformCore:()=>EM,reflectComponentType:()=>kM,resolveForwardRef:()=>E,setTestabilityGetter:()=>Dh,\u0275ALLOW_MULTIPLE_PLATFORMS:()=>qa,\u0275APP_ID_RANDOM_PROVIDER:()=>R_,\u0275ComponentFactory:()=>Wr,\u0275Console:()=>k_,\u0275DEFAULT_LOCALE_ID:()=>Et,\u0275INJECTOR_SCOPE:()=>$i,\u0275LContext:()=>Sc,\u0275LifecycleHooksFeature:()=>vd,\u0275LocaleDataIndex:()=>M,\u0275NG_COMP_DEF:()=>En,\u0275NG_DIR_DEF:()=>fr,\u0275NG_ELEMENT_ID:()=>Tt,\u0275NG_INJ_DEF:()=>ur,\u0275NG_MOD_DEF:()=>ko,\u0275NG_PIPE_DEF:()=>pr,\u0275NG_PROV_DEF:()=>In,\u0275NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR:()=>Qr,\u0275NO_CHANGE:()=>T,\u0275NgModuleFactory:()=>yo,\u0275NoopNgZone:()=>mh,\u0275ReflectionCapabilities:()=>pc,\u0275Render3ComponentFactory:()=>en,\u0275Render3ComponentRef:()=>yd,\u0275Render3NgModuleRef:()=>_a,\u0275RuntimeError:()=>_,\u0275TESTABILITY:()=>yh,\u0275TESTABILITY_GETTER:()=>vh,\u0275ViewRef:()=>Xt,\u0275XSS_SECURITY_URL:()=>Po,\u0275_sanitizeHtml:()=>du,\u0275_sanitizeUrl:()=>Br,\u0275allowSanitizationBypassAndThrow:()=>Wt,\u0275bypassSanitizationTrustHtml:()=>sy,\u0275bypassSanitizationTrustResourceUrl:()=>uy,\u0275bypassSanitizationTrustScript:()=>ly,\u0275bypassSanitizationTrustStyle:()=>ay,\u0275bypassSanitizationTrustUrl:()=>cy,\u0275clearResolutionOfComponentResourcesQueue:()=>Ic,\u0275coerceToBoolean:()=>wM,\u0275compileComponent:()=>Qp,\u0275compileDirective:()=>ja,\u0275compileNgModule:()=>$p,\u0275compileNgModuleDefs:()=>Bp,\u0275compileNgModuleFactory:()=>Mh,\u0275compilePipe:()=>rh,\u0275convertToBitFlags:()=>_n,\u0275createInjector:()=>Qi,\u0275defaultIterableDiffers:()=>_M,\u0275defaultKeyValueDiffers:()=>MM,\u0275detectChanges:()=>Td,\u0275devModeEqual:()=>wd,\u0275findLocaleData:()=>ua,\u0275flushModuleScopingQueueAsMuchAsPossible:()=>Vp,\u0275formatRuntimeError:()=>yn,\u0275getDebugNode:()=>gn,\u0275getDebugNodeR2:()=>pM,\u0275getDirectives:()=>tp,\u0275getHostElement:()=>Ca,\u0275getInjectableDef:()=>vn,\u0275getLContext:()=>ue,\u0275getLocaleCurrencyCode:()=>UI,\u0275getLocalePluralCase:()=>If,\u0275getSanitizationBypassType:()=>ou,\u0275getUnknownElementStrictMode:()=>vm,\u0275getUnknownPropertyStrictMode:()=>Dm,\u0275global:()=>Z,\u0275injectChangeDetectorRef:()=>kh,\u0275internalCreateApplication:()=>X_,\u0275isBoundToModule:()=>Eh,\u0275isEnvironmentProviders:()=>ar,\u0275isInjectable:()=>tg,\u0275isNgModule:()=>Ra,\u0275isObservable:()=>Rd,\u0275isPromise:()=>Ts,\u0275isSubscribable:()=>Pd,\u0275makeDecorator:()=>Vt,\u0275noSideEffects:()=>Le,\u0275patchComponentDefWithScope:()=>ka,\u0275publishDefaultGlobalUtils:()=>q_,\u0275publishGlobalUtil:()=>Te,\u0275registerLocaleData:()=>BI,\u0275resetCompiledComponents:()=>p_,\u0275resetJitOptions:()=>u_,\u0275resolveComponentResources:()=>yc,\u0275setAllowDuplicateNgModuleIdsForTest:()=>mm,\u0275setClassMetadata:()=>rp,\u0275setCurrentInjector:()=>ut,\u0275setDocument:()=>ey,\u0275setLocaleId:()=>da,\u0275setUnknownElementStrictMode:()=>ym,\u0275setUnknownPropertyStrictMode:()=>Im,\u0275store:()=>Nd,\u0275stringify:()=>j,\u0275transitiveScopesFor:()=>Ct,\u0275unregisterLocaleData:()=>qI,\u0275unwrapSafeValue:()=>Ke,\u0275\u0275CopyDefinitionFeature:()=>Dd,\u0275\u0275FactoryTarget:()=>Y,\u0275\u0275HostDirectivesFeature:()=>_d,\u0275\u0275InheritDefinitionFeature:()=>cs,\u0275\u0275NgOnChangesFeature:()=>$o,\u0275\u0275ProvidersFeature:()=>Qf,\u0275\u0275StandaloneFeature:()=>Yf,\u0275\u0275advance:()=>Bu,\u0275\u0275attribute:()=>fs,\u0275\u0275attributeInterpolate1:()=>ps,\u0275\u0275attributeInterpolate2:()=>hs,\u0275\u0275attributeInterpolate3:()=>gs,\u0275\u0275attributeInterpolate4:()=>ms,\u0275\u0275attributeInterpolate5:()=>ys,\u0275\u0275attributeInterpolate6:()=>vs,\u0275\u0275attributeInterpolate7:()=>Is,\u0275\u0275attributeInterpolate8:()=>Ds,\u0275\u0275attributeInterpolateV:()=>_s,\u0275\u0275classMap:()=>Wd,\u0275\u0275classMapInterpolate1:()=>ef,\u0275\u0275classMapInterpolate2:()=>tf,\u0275\u0275classMapInterpolate3:()=>nf,\u0275\u0275classMapInterpolate4:()=>rf,\u0275\u0275classMapInterpolate5:()=>of,\u0275\u0275classMapInterpolate6:()=>sf,\u0275\u0275classMapInterpolate7:()=>af,\u0275\u0275classMapInterpolate8:()=>lf,\u0275\u0275classMapInterpolateV:()=>cf,\u0275\u0275classProp:()=>Bs,\u0275\u0275contentQuery:()=>Rp,\u0275\u0275defineComponent:()=>al,\u0275\u0275defineDirective:()=>dl,\u0275\u0275defineInjectable:()=>X,\u0275\u0275defineInjector:()=>cr,\u0275\u0275defineNgModule:()=>Fo,\u0275\u0275definePipe:()=>fl,\u0275\u0275directiveInject:()=>Kt,\u0275\u0275disableBindings:()=>Pl,\u0275\u0275element:()=>Cs,\u0275\u0275elementContainer:()=>ws,\u0275\u0275elementContainerEnd:()=>ao,\u0275\u0275elementContainerStart:()=>so,\u0275\u0275elementEnd:()=>io,\u0275\u0275elementStart:()=>oo,\u0275\u0275enableBindings:()=>Sl,\u0275\u0275getCurrentView:()=>Sd,\u0275\u0275getInheritedFactory:()=>ac,\u0275\u0275hostProperty:()=>la,\u0275\u0275i18n:()=>Bf,\u0275\u0275i18nApply:()=>qf,\u0275\u0275i18nAttributes:()=>Uf,\u0275\u0275i18nEnd:()=>ga,\u0275\u0275i18nExp:()=>ma,\u0275\u0275i18nPostprocess:()=>Gf,\u0275\u0275i18nStart:()=>ha,\u0275\u0275inject:()=>z,\u0275\u0275injectAttribute:()=>ii,\u0275\u0275invalidFactory:()=>td,\u0275\u0275invalidFactoryDep:()=>Oo,\u0275\u0275listener:()=>bs,\u0275\u0275loadQuery:()=>Ap,\u0275\u0275namespaceHTML:()=>Wl,\u0275\u0275namespaceMathML:()=>Gl,\u0275\u0275namespaceSVG:()=>ql,\u0275\u0275nextContext:()=>kd,\u0275\u0275ngDeclareClassMetadata:()=>bM,\u0275\u0275ngDeclareComponent:()=>NM,\u0275\u0275ngDeclareDirective:()=>TM,\u0275\u0275ngDeclareFactory:()=>xM,\u0275\u0275ngDeclareInjectable:()=>PM,\u0275\u0275ngDeclareInjector:()=>RM,\u0275\u0275ngDeclareNgModule:()=>AM,\u0275\u0275ngDeclarePipe:()=>OM,\u0275\u0275pipe:()=>Ip,\u0275\u0275pipeBind1:()=>Dp,\u0275\u0275pipeBind2:()=>_p,\u0275\u0275pipeBind3:()=>Mp,\u0275\u0275pipeBind4:()=>Ep,\u0275\u0275pipeBindV:()=>Cp,\u0275\u0275projection:()=>jd,\u0275\u0275projectionDef:()=>Fd,\u0275\u0275property:()=>Ms,\u0275\u0275propertyInterpolate:()=>xs,\u0275\u0275propertyInterpolate1:()=>lo,\u0275\u0275propertyInterpolate2:()=>Ss,\u0275\u0275propertyInterpolate3:()=>Ps,\u0275\u0275propertyInterpolate4:()=>Rs,\u0275\u0275propertyInterpolate5:()=>As,\u0275\u0275propertyInterpolate6:()=>Os,\u0275\u0275propertyInterpolate7:()=>Ls,\u0275\u0275propertyInterpolate8:()=>ks,\u0275\u0275propertyInterpolateV:()=>Fs,\u0275\u0275pureFunction0:()=>op,\u0275\u0275pureFunction1:()=>ip,\u0275\u0275pureFunction2:()=>sp,\u0275\u0275pureFunction3:()=>ap,\u0275\u0275pureFunction4:()=>lp,\u0275\u0275pureFunction5:()=>cp,\u0275\u0275pureFunction6:()=>up,\u0275\u0275pureFunction7:()=>dp,\u0275\u0275pureFunction8:()=>fp,\u0275\u0275pureFunctionV:()=>pp,\u0275\u0275queryRefresh:()=>Sp,\u0275\u0275reference:()=>xd,\u0275\u0275registerNgModuleType:()=>ci,\u0275\u0275resetView:()=>Al,\u0275\u0275resolveBody:()=>Lu,\u0275\u0275resolveDocument:()=>Ou,\u0275\u0275resolveWindow:()=>Au,\u0275\u0275restoreView:()=>Rl,\u0275\u0275sanitizeHtml:()=>fu,\u0275\u0275sanitizeResourceUrl:()=>Li,\u0275\u0275sanitizeScript:()=>hu,\u0275\u0275sanitizeStyle:()=>pu,\u0275\u0275sanitizeUrl:()=>Oi,\u0275\u0275sanitizeUrlOrResourceUrl:()=>yu,\u0275\u0275setComponentScope:()=>ll,\u0275\u0275setNgModuleScope:()=>cl,\u0275\u0275styleMap:()=>Re,\u0275\u0275styleMapInterpolate1:()=>uf,\u0275\u0275styleMapInterpolate2:()=>df,\u0275\u0275styleMapInterpolate3:()=>ff,\u0275\u0275styleMapInterpolate4:()=>pf,\u0275\u0275styleMapInterpolate5:()=>hf,\u0275\u0275styleMapInterpolate6:()=>gf,\u0275\u0275styleMapInterpolate7:()=>mf,\u0275\u0275styleMapInterpolate8:()=>yf,\u0275\u0275styleMapInterpolateV:()=>vf,\u0275\u0275styleProp:()=>$s,\u0275\u0275stylePropInterpolate1:()=>Xs,\u0275\u0275stylePropInterpolate2:()=>ea,\u0275\u0275stylePropInterpolate3:()=>ta,\u0275\u0275stylePropInterpolate4:()=>na,\u0275\u0275stylePropInterpolate5:()=>ra,\u0275\u0275stylePropInterpolate6:()=>oa,\u0275\u0275stylePropInterpolate7:()=>ia,\u0275\u0275stylePropInterpolate8:()=>sa,\u0275\u0275stylePropInterpolateV:()=>aa,\u0275\u0275syntheticHostListener:()=>Ns,\u0275\u0275syntheticHostProperty:()=>ca,\u0275\u0275template:()=>bd,\u0275\u0275templateRefExtractor:()=>Fp,\u0275\u0275text:()=>Xd,\u0275\u0275textInterpolate:()=>qs,\u0275\u0275textInterpolate1:()=>fo,\u0275\u0275textInterpolate2:()=>Gs,\u0275\u0275textInterpolate3:()=>Ws,\u0275\u0275textInterpolate4:()=>Qs,\u0275\u0275textInterpolate5:()=>zs,\u0275\u0275textInterpolate6:()=>Zs,\u0275\u0275textInterpolate7:()=>Ys,\u0275\u0275textInterpolate8:()=>Ks,\u0275\u0275textInterpolateV:()=>Js,\u0275\u0275trustConstantHtml:()=>gu,\u0275\u0275trustConstantResourceUrl:()=>mu,\u0275\u0275validateIframeAttribute:()=>Xc,\u0275\u0275viewQuery:()=>Pp});var mn=ir(42168),Qh=ir(84757);function F(e){for(let t in e)if(e[t]===F)return t;throw Error("Could not find renamed property on target object.")}function No(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function j(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(j).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function xo(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const zh=F({__forward_ref__:F});function sr(e){return e.__forward_ref__=sr,e.toString=function(){return j(this())},e}function E(e){return So(e)?e():e}function So(e){return"function"==typeof e&&e.hasOwnProperty(zh)&&e.__forward_ref__===sr}function ar(e){return e&&!!e.\u0275providers}const Po="https://g.co/ng/security#xss";class _ extends Error{constructor(t,n){super(yn(t,n)),this.code=t}}function yn(e,t){return`NG0${Math.abs(e)}${t?": "+t.trim():""}`}function N(e){return"string"==typeof e?e:null==e?"":String(e)}function O(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():N(e)}function lr(e,t){throw new _(-201,!1)}function L(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function X(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}const eg=X;function cr(e){return{providers:e.providers||[],imports:e.imports||[]}}function vn(e){return tl(e,In)||tl(e,rl)}function tg(e){return null!==vn(e)}function tl(e,t){return e.hasOwnProperty(t)?e[t]:null}function nl(e){return e&&(e.hasOwnProperty(ur)||e.hasOwnProperty(rg))?e[ur]:null}const In=F({\u0275prov:F}),ur=F({\u0275inj:F}),rl=F({ngInjectableDef:F}),rg=F({ngInjectorDef:F});var w=(()=>((w=w||{})[w.Default=0]="Default",w[w.Host=1]="Host",w[w.Self=2]="Self",w[w.SkipSelf=4]="SkipSelf",w[w.Optional=8]="Optional",w))();let Ro;function _e(e){const t=Ro;return Ro=e,t}function ol(e,t,n){const r=vn(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&w.Optional?null:void 0!==t?t:void lr(j(e))}const Z=(()=>typeof globalThis<"u"&&globalThis||typeof global<"u"&&global||typeof window<"u"&&window||typeof self<"u"&&typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&self)(),ot={},Ao="__NG_DI_FLAG__",dr="ngTempTokenPath",ig="ngTokenPath",sg=/\n/gm,ag="\u0275",il="__source";let Dn;function ut(e){const t=Dn;return Dn=e,t}function lg(e,t=w.Default){if(void 0===Dn)throw new _(-203,!1);return null===Dn?ol(e,void 0,t):Dn.get(e,t&w.Optional?null:void 0,t)}function z(e,t=w.Default){return(function og(){return Ro}()||lg)(E(e),t)}function Oo(e){throw new _(202,!1)}function sl(e,t=w.Default){return z(e,_n(t))}function _n(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Lo(e){const t=[];for(let n=0;n<e.length;n++){const r=E(e[n]);if(Array.isArray(r)){if(0===r.length)throw new _(900,!1);let o,i=w.Default;for(let s=0;s<r.length;s++){const a=r[s],l=cg(a);"number"==typeof l?-1===l?o=a.token:i|=l:o=a}t.push(z(o,i))}else t.push(z(r))}return t}function Mn(e,t){return e[Ao]=t,e.prototype[Ao]=t,e}function cg(e){return e[Ao]}function Le(e){return{toString:e}.toString()}var Me=(()=>((Me=Me||{})[Me.OnPush=0]="OnPush",Me[Me.Default=1]="Default",Me))(),Ge=(()=>{return(e=Ge||(Ge={}))[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",Ge;var e})();const We={},A=[],En=F({\u0275cmp:F}),fr=F({\u0275dir:F}),pr=F({\u0275pipe:F}),ko=F({\u0275mod:F}),Qe=F({\u0275fac:F}),Tt=F({__NG_ELEMENT_ID__:F});let fg=0;function al(e){return Le(()=>{const t=pl(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Me.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,data:e.data||{},encapsulation:e.encapsulation||Ge.Emulated,id:"c"+fg++,styles:e.styles||A,_:null,schemas:e.schemas||null,tView:null};hl(n);const r=e.dependencies;return n.directiveDefs=hr(r,!1),n.pipeDefs=hr(r,!0),n})}function ll(e,t,n){const r=e.\u0275cmp;r.directiveDefs=hr(t,!1),r.pipeDefs=hr(n,!0)}function pg(e){return k(e)||ne(e)}function hg(e){return null!==e}function Fo(e){return Le(()=>({type:e.type,bootstrap:e.bootstrap||A,declarations:e.declarations||A,imports:e.imports||A,exports:e.exports||A,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function cl(e,t){return Le(()=>{const n=ge(e,!0);n.declarations=t.declarations||A,n.imports=t.imports||A,n.exports=t.exports||A})}function ul(e,t){if(null==e)return We;const n={};for(const r in e)if(e.hasOwnProperty(r)){let o=e[r],i=o;Array.isArray(o)&&(i=o[1],o=o[0]),n[o]=r,t&&(t[o]=i)}return n}function dl(e){return Le(()=>{const t=pl(e);return hl(t),t})}function fl(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function k(e){return e[En]||null}function ne(e){return e[fr]||null}function fe(e){return e[pr]||null}function bt(e){const t=k(e)||ne(e)||fe(e);return null!==t&&t.standalone}function ge(e,t){const n=e[ko]||null;if(!n&&!0===t)throw new Error(`Type ${j(e)} does not have '\u0275mod' property.`);return n}function pl(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,exportAs:e.exportAs||null,standalone:!0===e.standalone,selectors:e.selectors||A,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ul(e.inputs,t),outputs:ul(e.outputs)}}function hl(e){e.features?.forEach(t=>t(e))}function hr(e,t){if(!e)return null;const n=t?fe:pg;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(hg)}const ze=0,I=1,S=2,U=3,Ne=4,dt=5,re=6,Nt=7,G=8,gr=9,mr=10,P=11,jo=12,Cn=13,gl=14,xt=15,oe=16,wn=17,St=18,ke=19,Tn=20,ml=21,H=22,Vo=1,yl=2,yr=7,vr=8,Pt=9,ae=10;function me(e){return Array.isArray(e)&&"object"==typeof e[Vo]}function xe(e){return Array.isArray(e)&&!0===e[Vo]}function Ho(e){return 0!=(4&e.flags)}function bn(e){return e.componentOffset>-1}function Ir(e){return 1==(1&e.flags)}function Se(e){return!!e.template}function gg(e){return 0!=(256&e[S])}function ft(e,t){return e.hasOwnProperty(Qe)?e[Qe]:null}class Dl{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function $o(){return _l}function _l(e){return e.type.prototype.ngOnChanges&&(e.setInput=Ig),vg}function vg(){const e=El(this),t=e?.current;if(t){const n=e.previous;if(n===We)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Ig(e,t,n,r){const o=this.declaredInputs[n],i=El(e)||function Dg(e,t){return e[Ml]=t}(e,{previous:We,current:null}),s=i.current||(i.current={}),a=i.previous,l=a[o];s[o]=new Dl(l&&l.currentValue,t,a===We),e[r]=t}$o.ngInherit=!0;const Ml="__ngSimpleChanges__";function El(e){return e[Ml]||null}let Bo=null;const _g=e=>{Bo=e},Ee=function(e,t,n){Bo?.(e,t,n)},Cl="svg",wl="math";function ee(e){for(;Array.isArray(e);)e=e[ze];return e}function Dr(e,t){return ee(t[e])}function ye(e,t){return ee(t[e.index])}function Tl(e,t){return e.data[t]}function Rt(e,t){return e[t]}function pe(e,t){const n=t[e];return me(n)?n:n[ze]}function _r(e){return 64==(64&e[S])}function it(e,t){return null==t?null:e[t]}function bl(e){e[St]=0}function Uo(e,t){e[dt]+=t;let n=e,r=e[U];for(;null!==r&&(1===t&&1===n[dt]||-1===t&&0===n[dt]);)r[dt]+=t,n=r,r=r[U]}const x={lFrame:$l(null),bindingsEnabled:!0};function xl(){return x.bindingsEnabled}function Sl(){x.bindingsEnabled=!0}function Pl(){x.bindingsEnabled=!1}function g(){return x.lFrame.lView}function R(){return x.lFrame.tView}function Rl(e){return x.lFrame.contextLView=e,e[G]}function Al(e){return x.lFrame.contextLView=null,e}function te(){let e=Ol();for(;null!==e&&64===e.type;)e=e.parent;return e}function Ol(){return x.lFrame.currentTNode}function Nn(){const e=x.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Fe(e,t){const n=x.lFrame;n.currentTNode=e,n.isParent=t}function qo(){return x.lFrame.isParent}function Go(){x.lFrame.isParent=!1}function le(){const e=x.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Ze(){return x.lFrame.bindingIndex}function kl(e){return x.lFrame.bindingIndex=e}function At(){return x.lFrame.bindingIndex++}function Ye(e){const t=x.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Fl(e){x.lFrame.inI18n=e}function Pg(e,t){const n=x.lFrame;n.bindingIndex=n.bindingRootIndex=e,Wo(t)}function Wo(e){x.lFrame.currentDirectiveIndex=e}function Qo(e){const t=x.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}function jl(){return x.lFrame.currentQueryIndex}function zo(e){x.lFrame.currentQueryIndex=e}function Ag(e){const t=e[I];return 2===t.type?t.declTNode:1===t.type?e[re]:null}function Vl(e,t,n){if(n&w.SkipSelf){let o=t,i=e;for(;!(o=o.parent,null!==o||n&w.Host||(o=Ag(i),null===o||(i=i[xt],10&o.type))););if(null===o)return!1;t=o,e=i}const r=x.lFrame=Hl();return r.currentTNode=t,r.lView=e,!0}function Zo(e){const t=Hl(),n=e[I];x.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Hl(){const e=x.lFrame,t=null===e?null:e.child;return null===t?$l(e):t}function $l(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Bl(){const e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Ul=Bl;function Yo(){const e=Bl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function ce(){return x.lFrame.selectedIndex}function pt(e){x.lFrame.selectedIndex=e}function B(){const e=x.lFrame;return Tl(e.tView,e.selectedIndex)}function ql(){x.lFrame.currentNamespace=Cl}function Gl(){x.lFrame.currentNamespace=wl}function Wl(){!function kg(){x.lFrame.currentNamespace=null}()}function Mr(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=i;s&&(e.contentHooks??(e.contentHooks=[])).push(-n,s),a&&((e.contentHooks??(e.contentHooks=[])).push(n,a),(e.contentCheckHooks??(e.contentCheckHooks=[])).push(n,a)),l&&(e.viewHooks??(e.viewHooks=[])).push(-n,l),c&&((e.viewHooks??(e.viewHooks=[])).push(n,c),(e.viewCheckHooks??(e.viewCheckHooks=[])).push(n,c)),null!=u&&(e.destroyHooks??(e.destroyHooks=[])).push(n,u)}}function Er(e,t,n){Ql(e,t,3,n)}function Cr(e,t,n,r){(3&e[S])===n&&Ql(e,t,n,r)}function Ko(e,t){let n=e[S];(3&n)===t&&(n&=2047,n+=1,e[S]=n)}function Ql(e,t,n,r){const i=r??-1,s=t.length-1;let a=0;for(let l=void 0!==r?65535&e[St]:0;l<s;l++)if("number"==typeof t[l+1]){if(a=t[l],null!=r&&a>=r)break}else t[l]<0&&(e[St]+=65536),(a<i||-1==i)&&(Vg(e,n,t,l),e[St]=(**********&e[St])+l+2),l++}function Vg(e,t,n,r){const o=n[r]<0,i=n[r+1],a=e[o?-n[r]:n[r]];if(o){if(e[S]>>11<e[St]>>16&&(3&e[S])===t){e[S]+=2048,Ee(4,a,i);try{i.call(a)}finally{Ee(5,a,i)}}}else{Ee(4,a,i);try{i.call(a)}finally{Ee(5,a,i)}}}const Ot=-1;class xn{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Xo(e,t,n){let r=0;for(;r<n.length;){const o=n[r];if("number"==typeof o){if(0!==o)break;r++;const i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{const i=o,s=n[++r];Zl(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function zl(e){return 3===e||4===e||6===e}function Zl(e){return 64===e.charCodeAt(0)}function Sn(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const o=t[r];"number"==typeof o?n=o:0===n||Yl(e,n,o,null,-1===n||2===n?t[++r]:null)}}return e}function Yl(e,t,n,r,o){let i=0,s=e.length;if(-1===t)s=-1;else for(;i<e.length;){const a=e[i++];if("number"==typeof a){if(a===t){s=-1;break}if(a>t){s=i-1;break}}}for(;i<e.length;){const a=e[i];if("number"==typeof a)break;if(a===n){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==s&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}function Kl(e){return e!==Ot}function wr(e){return 32767&e}function Tr(e,t){let n=function Ug(e){return e>>16}(e),r=t;for(;n>0;)r=r[xt],n--;return r}let ei=!0;function br(e){const t=ei;return ei=e,t}const Jl=255,Xl=5;let qg=0;const je={};function Nr(e,t){const n=ec(e,t);if(-1!==n)return n;const r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,ti(r.data,e),ti(t,null),ti(r.blueprint,null));const o=ni(e,t),i=e.injectorIndex;if(Kl(o)){const s=wr(o),a=Tr(o,t),l=a[I].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|l[s+c]}return t[i+8]=o,i}function ti(e,t){e.push(0,0,0,0,0,0,0,0,t)}function ec(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function ni(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;null!==o;){if(r=lc(o),null===r)return Ot;if(n++,o=o[xt],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Ot}function ri(e,t,n){!function Gg(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(Tt)&&(r=n[Tt]),null==r&&(r=n[Tt]=qg++);const o=r&Jl;t.data[e+(o>>Xl)]|=1<<o}(e,t,n)}function tc(e,t,n){if(n&w.Optional||void 0!==e)return e;lr()}function nc(e,t,n,r){if(n&w.Optional&&void 0===r&&(r=null),!(n&(w.Self|w.Host))){const o=e[gr],i=_e(void 0);try{return o?o.get(t,r,n&w.Optional):ol(t,r,n&w.Optional)}finally{_e(i)}}return tc(r,0,n)}function rc(e,t,n,r=w.Default,o){if(null!==e){if(1024&t[S]){const s=function Yg(e,t,n,r,o){let i=e,s=t;for(;null!==i&&null!==s&&1024&s[S]&&!(256&s[S]);){const a=oc(i,s,n,r|w.Self,je);if(a!==je)return a;let l=i.parent;if(!l){const c=s[ml];if(c){const u=c.get(n,je,r);if(u!==je)return u}l=lc(s),s=s[xt]}i=l}return o}(e,t,n,r,je);if(s!==je)return s}const i=oc(e,t,n,r,je);if(i!==je)return i}return nc(t,n,r,o)}function oc(e,t,n,r,o){const i=function zg(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(Tt)?e[Tt]:void 0;return"number"==typeof t?t>=0?t&Jl:Zg:t}(n);if("function"==typeof i){if(!Vl(t,e,r))return r&w.Host?tc(o,0,r):nc(t,n,r,o);try{const s=i(r);if(null!=s||r&w.Optional)return s;lr()}finally{Ul()}}else if("number"==typeof i){let s=null,a=ec(e,t),l=Ot,c=r&w.Host?t[oe][re]:null;for((-1===a||r&w.SkipSelf)&&(l=-1===a?ni(e,t):t[a+8],l!==Ot&&sc(r,!1)?(s=t[I],a=wr(l),t=Tr(l,t)):a=-1);-1!==a;){const u=t[I];if(ic(i,a,u.data)){const d=Qg(a,t,n,s,r,c);if(d!==je)return d}l=t[a+8],l!==Ot&&sc(r,t[I].data[a+8]===c)&&ic(i,a,t)?(s=u,a=wr(l),t=Tr(l,t)):a=-1}}return o}function Qg(e,t,n,r,o,i){const s=t[I],a=s.data[e+8],u=xr(a,s,n,null==r?bn(a)&&ei:r!=s&&0!=(3&a.type),o&w.Host&&i===a);return null!==u?ht(t,s,u,a):je}function xr(e,t,n,r,o){const i=e.providerIndexes,s=t.data,a=1048575&i,l=e.directiveStart,u=i>>20,f=o?a+u:e.directiveEnd;for(let p=r?a:a+u;p<f;p++){const h=s[p];if(p<l&&n===h||p>=l&&h.type===n)return p}if(o){const p=s[l];if(p&&Se(p)&&p.type===n)return l}return null}function ht(e,t,n,r){let o=e[n];const i=t.data;if(function Hg(e){return e instanceof xn}(o)){const s=o;s.resolving&&function Zh(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new _(-200,`Circular dependency in DI detected for ${e}${n}`)}(O(i[n]));const a=br(s.canSeeViewProviders);s.resolving=!0;const l=s.injectImpl?_e(s.injectImpl):null;Vl(e,r,w.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&function jg(e,t,n){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){const s=_l(t);(n.preOrderHooks??(n.preOrderHooks=[])).push(e,s),(n.preOrderCheckHooks??(n.preOrderCheckHooks=[])).push(e,s)}o&&(n.preOrderHooks??(n.preOrderHooks=[])).push(0-e,o),i&&((n.preOrderHooks??(n.preOrderHooks=[])).push(e,i),(n.preOrderCheckHooks??(n.preOrderCheckHooks=[])).push(e,i))}(n,i[n],t)}finally{null!==l&&_e(l),br(a),s.resolving=!1,Ul()}}return o}function ic(e,t,n){return!!(n[t+(e>>Xl)]&1<<e)}function sc(e,t){return!(e&w.Self||e&w.Host&&t)}class Lt{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return rc(this._tNode,this._lView,t,_n(r),n)}}function Zg(){return new Lt(te(),g())}function ac(e){return Le(()=>{const t=e.prototype.constructor,n=t[Qe]||oi(t),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const i=o[Qe]||oi(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function oi(e){return So(e)?()=>{const t=oi(E(e));return t&&t()}:ft(e)}function lc(e){const t=e[I],n=t.type;return 2===n?t.declTNode:1===n?e[re]:null}function ii(e){return function Wg(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let o=0;for(;o<r;){const i=n[o];if(zl(i))break;if(0===i)o+=2;else if("number"==typeof i)for(o++;o<r&&"string"==typeof n[o];)o++;else{if(i===t)return n[o+1];o+=2}}}return null}(te(),e)}const kt="__annotations__",Ft="__parameters__",jt="__prop__metadata__";function Vt(e,t,n,r,o){return Le(()=>{const i=si(t);function s(...a){if(this instanceof s)return i.call(this,...a),this;const l=new s(...a);return function(u){return o&&o(u,...a),(u.hasOwnProperty(kt)?u[kt]:Object.defineProperty(u,kt,{value:[]})[kt]).push(l),r&&r(u),u}}return n&&(s.prototype=Object.create(n.prototype)),s.prototype.ngMetadataName=e,s.annotationCls=s,s})}function si(e){return function(...n){if(e){const r=e(...n);for(const o in r)this[o]=r[o]}}}function Ht(e,t,n){return Le(()=>{const r=si(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;const s=new o(...i);return a.annotation=s,a;function a(l,c,u){const d=l.hasOwnProperty(Ft)?l[Ft]:Object.defineProperty(l,Ft,{value:[]})[Ft];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function st(e,t,n,r){return Le(()=>{const o=si(t);function i(...s){if(this instanceof i)return o.apply(this,s),this;const a=new i(...s);return function l(c,u){const d=c.constructor,f=d.hasOwnProperty(jt)?d[jt]:Object.defineProperty(d,jt,{value:{}})[jt];f[u]=f.hasOwnProperty(u)&&f[u]||[],f[u].unshift(a),r&&r(c,u,...s)}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}const cc=Ht("Attribute",e=>({attributeName:e,__NG_ELEMENT_ID__:()=>ii(e)}));class V{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=X({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const Kg=new V("AnalyzeForEntryComponents");class Pn{}const Jg=st("ContentChildren",(e,t={})=>({selector:e,first:!1,isViewQuery:!1,descendants:!1,emitDistinctChangesOnly:!0,...t}),Pn),Xg=st("ContentChild",(e,t={})=>({selector:e,first:!0,isViewQuery:!1,descendants:!0,...t}),Pn),em=st("ViewChildren",(e,t={})=>({selector:e,first:!1,isViewQuery:!0,descendants:!0,emitDistinctChangesOnly:!0,...t}),Pn),tm=st("ViewChild",(e,t)=>({selector:e,first:!0,isViewQuery:!0,descendants:!0,...t}),Pn);var Y=(()=>((Y=Y||{})[Y.Directive=0]="Directive",Y[Y.Component=1]="Component",Y[Y.Injectable=2]="Injectable",Y[Y.Pipe=3]="Pipe",Y[Y.NgModule=4]="NgModule",Y))();function K(e){const t=Z.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}const Sr=Function;function Rn(e){return"function"==typeof e}function Ce(e){return e.flat(Number.POSITIVE_INFINITY)}function gt(e,t){e.forEach(n=>Array.isArray(n)?gt(n,t):t(n))}function dc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Pr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function An(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}function ve(e,t,n){let r=$t(e,t);return r>=0?e[1|r]=n:(r=~r,function om(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function ai(e,t){const n=$t(e,t);if(n>=0)return e[1|n]}function $t(e,t){return function fc(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){const i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}(e,t,1)}const im=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*(arguments|(?:[^()]+\(\[\],)?[^()]+\(arguments\).*)\)/,sm=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,am=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,lm=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{[^}]*super\(\.\.\.arguments\)/;class pc{constructor(t){this._reflect=t||Z.Reflect}factory(t){return(...n)=>new t(...n)}_zipTypesAndAnnotations(t,n){let r;r=An(typeof t>"u"?n.length:t.length);for(let o=0;o<r.length;o++)r[o]=typeof t>"u"?[]:t[o]&&t[o]!=Object?[t[o]]:[],n&&null!=n[o]&&(r[o]=r[o].concat(n[o]));return r}_ownParameters(t,n){if(function cm(e){return im.test(e)||lm.test(e)||sm.test(e)&&!am.test(e)}(t.toString()))return null;if(t.parameters&&t.parameters!==n.parameters)return t.parameters;const o=t.ctorParameters;if(o&&o!==n.ctorParameters){const a="function"==typeof o?o():o,l=a.map(u=>u&&u.type),c=a.map(u=>u&&li(u.decorators));return this._zipTypesAndAnnotations(l,c)}const i=t.hasOwnProperty(Ft)&&t[Ft],s=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",t);return s||i?this._zipTypesAndAnnotations(s,i):An(t.length)}parameters(t){if(!Rn(t))return[];const n=Rr(t);let r=this._ownParameters(t,n);return!r&&n!==Object&&(r=this.parameters(n)),r||[]}_ownAnnotations(t,n){if(t.annotations&&t.annotations!==n.annotations){let r=t.annotations;return"function"==typeof r&&r.annotations&&(r=r.annotations),r}return t.decorators&&t.decorators!==n.decorators?li(t.decorators):t.hasOwnProperty(kt)?t[kt]:null}annotations(t){if(!Rn(t))return[];const n=Rr(t),r=this._ownAnnotations(t,n)||[];return(n!==Object?this.annotations(n):[]).concat(r)}_ownPropMetadata(t,n){if(t.propMetadata&&t.propMetadata!==n.propMetadata){let r=t.propMetadata;return"function"==typeof r&&r.propMetadata&&(r=r.propMetadata),r}if(t.propDecorators&&t.propDecorators!==n.propDecorators){const r=t.propDecorators,o={};return Object.keys(r).forEach(i=>{o[i]=li(r[i])}),o}return t.hasOwnProperty(jt)?t[jt]:null}propMetadata(t){if(!Rn(t))return{};const n=Rr(t),r={};if(n!==Object){const i=this.propMetadata(n);Object.keys(i).forEach(s=>{r[s]=i[s]})}const o=this._ownPropMetadata(t,n);return o&&Object.keys(o).forEach(i=>{const s=[];r.hasOwnProperty(i)&&s.push(...r[i]),s.push(...o[i]),r[i]=s}),r}ownPropMetadata(t){return Rn(t)&&this._ownPropMetadata(t,Rr(t))||{}}hasLifecycleHook(t,n){return t instanceof Sr&&n in t.prototype}}function li(e){return e?e.map(t=>new(0,t.type.annotationCls)(...t.args?t.args:[])):[]}function Rr(e){const t=e.prototype?Object.getPrototypeOf(e.prototype):null;return(t?t.constructor:null)||Object}const Ar=Mn(Ht("Inject",e=>({token:e})),-1),On=Mn(Ht("Optional"),8),Or=Mn(Ht("Self"),2),Bt=Mn(Ht("SkipSelf"),4),hc=Mn(Ht("Host"),1);let gc=null;function Ln(){return gc=gc||new pc}function Lr(e){return mc(Ln().parameters(e))}function mc(e){return e.map(t=>function um(e){const t={token:null,attribute:null,host:!1,optional:!1,self:!1,skipSelf:!1};if(Array.isArray(e)&&e.length>0)for(let n=0;n<e.length;n++){const r=e[n];if(void 0===r)continue;const o=Object.getPrototypeOf(r);if(r instanceof On||"Optional"===o.ngMetadataName)t.optional=!0;else if(r instanceof Bt||"SkipSelf"===o.ngMetadataName)t.skipSelf=!0;else if(r instanceof Or||"Self"===o.ngMetadataName)t.self=!0;else if(r instanceof hc||"Host"===o.ngMetadataName)t.host=!0;else if(r instanceof Ar)t.token=r.token;else if(r instanceof cc){if(void 0===r.attributeName)throw new _(204,!1);t.attribute=r.attributeName}else t.token=r}else t.token=void 0===e||Array.isArray(e)&&0===e.length?null:e;return t}(t))}function yc(e){const t=[],n=new Map;function r(o){let i=n.get(o);if(!i){const s=e(o);n.set(o,i=s.then(pm))}return i}return Ut.forEach((o,i)=>{const s=[];o.templateUrl&&s.push(r(o.templateUrl).then(d=>{o.template=d}));const a=o.styleUrls,l=o.styles||(o.styles=[]),c=o.styles.length;a&&a.forEach((d,f)=>{l.push(""),s.push(r(d).then(p=>{l[c+f]=p,a.splice(a.indexOf(d),1),0==a.length&&(o.styleUrls=void 0)}))});const u=Promise.all(s).then(()=>function hm(e){kn.delete(e)}(i));t.push(u)}),Ic(),Promise.all(t).then(()=>{})}let Ut=new Map;const kn=new Set;function vc(e){return!!(e.templateUrl&&!e.hasOwnProperty("template")||e.styleUrls&&e.styleUrls.length)}function Ic(){const e=Ut;return Ut=new Map,e}function pm(e){return"string"==typeof e?e:e.text()}const kr=new Map;let Dc=!0;function ci(e,t){(function gm(e,t,n){if(t&&t!==n&&Dc)throw new Error(`Duplicate module registered for ${e} - ${j(t)} vs ${j(t.name)}`)})(t,kr.get(t)||null,e),kr.set(t,e)}function _c(e){return kr.get(e)}function mm(e){Dc=!e}const Mc={name:"custom-elements"},Ec={name:"no-errors-schema"};let ui=!1;function ym(e){ui=e}function vm(){return ui}let di=!1;function Im(e){di=e}function Dm(){return di}var Ve=(()=>((Ve=Ve||{})[Ve.Important=1]="Important",Ve[Ve.DashCase=2]="DashCase",Ve))();const Mm=/^>|^->|<!--|-->|--!>|<!-$/g,Em=/(<|>)/,Cm="\u200b$1\u200b";const pi=new Map;let wm=0;function xc(e){return pi.get(e)||null}class Sc{get lView(){return xc(this.lViewId)}constructor(t,n,r){this.lViewId=t,this.nodeIndex=n,this.native=r}}function ue(e){let t=Fn(e);if(t){if(me(t)){const n=t;let r,o,i;if(Ac(e)){if(r=Lc(n,e),-1==r)throw new Error("The provided component was not found in the application");o=e}else if(function xm(e){return e&&e.constructor&&e.constructor.\u0275dir}(e)){if(r=function Pm(e,t){let n=e[I].firstChild;for(;n;){const o=n.directiveEnd;for(let i=n.directiveStart;i<o;i++)if(e[i]===t)return n.index;n=Sm(n)}return-1}(n,e),-1==r)throw new Error("The provided directive was not found in the application");i=kc(r,n)}else if(r=Oc(n,e),-1==r)return null;const s=ee(n[r]),a=Fn(s),l=a&&!Array.isArray(a)?a:hi(n,r,s);if(o&&void 0===l.component&&(l.component=o,ie(l.component,l)),i&&void 0===l.directives){l.directives=i;for(let c=0;c<i.length;c++)ie(i[c],l)}ie(l.native,l),t=l}}else{const n=e;let r=n;for(;r=r.parentNode;){const o=Fn(r);if(o){const i=Array.isArray(o)?o:o.lView;if(!i)return null;const s=Oc(i,n);if(s>=0){const a=ee(i[s]),l=hi(i,s,a);ie(a,l),t=l;break}}}}return t||null}function hi(e,t,n){return new Sc(e[Tn],t,n)}function Pc(e){let n,t=Fn(e);if(me(t)){const r=t,o=Lc(r,e);n=pe(o,r);const i=hi(r,o,n[ze]);i.component=e,ie(e,i),ie(i.native,i)}else n=pe(t.nodeIndex,t.lView);return n}const gi="__ngContext__";function ie(e,t){me(t)?(e[gi]=t[Tn],function bm(e){pi.set(e[Tn],e)}(t)):e[gi]=t}function Fn(e){const t=e[gi];return"number"==typeof t?xc(t):t||null}function Rc(e){const t=Fn(e);return t?me(t)?t:t.lView:null}function Ac(e){return e&&e.constructor&&e.constructor.\u0275cmp}function Oc(e,t){const n=e[I];for(let r=H;r<n.bindingStartIndex;r++)if(ee(e[r])===t)return r;return-1}function Sm(e){if(e.child)return e.child;if(e.next)return e.next;for(;e.parent&&!e.parent.next;)e=e.parent;return e.parent&&e.parent.next}function Lc(e,t){const n=e[I].components;if(n)for(let r=0;r<n.length;r++){const o=n[r];if(pe(o,e)[G]===t)return o}else if(pe(H,e)[G]===t)return H;return-1}function kc(e,t){const n=t[I].data[e];if(0===n.directiveStart)return A;const r=[];for(let o=n.directiveStart;o<n.directiveEnd;o++){const i=t[o];Ac(i)||r.push(i)}return r}let mi;function yi(e,t){return mi(e,t)}function jn(e){const t=e[U];return xe(t)?t[U]:t}function km(e){return function Lm(e){let t=me(e)?e:Rc(e);for(;t&&!(256&t[S]);)t=jn(t);return t}(e)[G]}function vi(e){return Fc(e[Cn])}function Ii(e){return Fc(e[Ne])}function Fc(e){for(;null!==e&&!xe(e);)e=e[Ne];return e}function qt(e,t,n,r,o){if(null!=r){let i,s=!1;xe(r)?i=r:me(r)&&(s=!0,r=r[ze]);const a=ee(r);0===e&&null!==n?null==o?Uc(t,n,a):mt(t,n,a,o||null,!0):1===e&&null!==n?mt(t,n,a,o||null,!0):2===e?Ti(t,a,s):3===e&&t.destroyNode(a),null!=i&&function Zm(e,t,n,r,o){const i=n[yr];i!==ee(n)&&qt(t,e,r,i,o);for(let a=ae;a<n.length;a++){const l=n[a];Vn(l[I],l,e,t,r,i)}}(t,e,i,n,o)}}function Di(e,t){return e.createText(t)}function jc(e,t,n){e.setValue(t,n)}function Fm(e,t){return e.createComment(function Nc(e){return e.replace(Mm,t=>t.replace(Em,Cm))}(t))}function _i(e,t,n){return e.createElement(t,n)}function Vc(e,t){const n=e[Pt],r=n.indexOf(t),o=t[U];512&t[S]&&(t[S]&=-513,Uo(o,-1)),n.splice(r,1)}function Mi(e,t){if(e.length<=ae)return;const n=ae+t,r=e[n];if(r){const o=r[wn];null!==o&&o!==e&&Vc(o,r),t>0&&(e[n-1][Ne]=r[Ne]);const i=Pr(e,ae+t);!function jm(e,t){Vn(e,t,t[P],2,null,null),t[ze]=null,t[re]=null}(r[I],r);const s=i[ke];null!==s&&s.detachView(i[I]),r[U]=null,r[Ne]=null,r[S]&=-65}return r}function Hc(e,t){if(!(128&t[S])){const n=t[P];n.destroyNode&&Vn(e,t,n,3,null,null),function $m(e){let t=e[Cn];if(!t)return Ei(e[I],e);for(;t;){let n=null;if(me(t))n=t[Cn];else{const r=t[ae];r&&(n=r)}if(!n){for(;t&&!t[Ne]&&t!==e;)me(t)&&Ei(t[I],t),t=t[U];null===t&&(t=e),me(t)&&Ei(t[I],t),n=t&&t[Ne]}t=n}}(t)}}function Ei(e,t){if(!(128&t[S])){t[S]&=-65,t[S]|=128,function Gm(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const o=t[n[r]];if(!(o instanceof xn)){const i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){const a=o[i[s]],l=i[s+1];Ee(4,a,l);try{l.call(a)}finally{Ee(5,a,l)}}else{Ee(4,o,i);try{i.call(o)}finally{Ee(5,o,i)}}}}}(e,t),function qm(e,t){const n=e.cleanup,r=t[Nt];let o=-1;if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const s=n[i+3];s>=0?r[o=s]():r[o=-s].unsubscribe(),i+=2}else{const s=r[o=n[i+1]];n[i].call(s)}if(null!==r){for(let i=o+1;i<r.length;i++)(0,r[i])();t[Nt]=null}}(e,t),1===t[I].type&&t[P].destroy();const n=t[wn];if(null!==n&&xe(t[U])){n!==t[U]&&Vc(n,t);const r=t[ke];null!==r&&r.detachView(e)}!function Nm(e){pi.delete(e[Tn])}(t)}}function $c(e,t,n){return Bc(e,t.parent,n)}function Bc(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[ze];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:i}=e.data[r.directiveStart+o];if(i===Ge.None||i===Ge.Emulated)return null}return ye(r,n)}}function mt(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Uc(e,t,n){e.appendChild(t,n)}function qc(e,t,n,r,o){null!==r?mt(e,t,n,r,o):Uc(e,t,n)}function Fr(e,t){return e.parentNode(t)}function Gc(e,t,n){return Qc(e,t,n)}function Wc(e,t,n){return 40&e.type?ye(e,n):null}let Ci,Hr,xi,$r,Qc=Wc;function zc(e,t){Qc=e,Ci=t}function jr(e,t,n,r){const o=$c(e,r,t),i=t[P],a=Gc(r.parent||t[re],r,t);if(null!=o)if(Array.isArray(n))for(let l=0;l<n.length;l++)qc(i,o,n[l],a,!1);else qc(i,o,n,a,!1);void 0!==Ci&&Ci(i,r,t,n,o)}function Vr(e,t){if(null!==t){const n=t.type;if(3&n)return ye(t,e);if(4&n)return wi(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Vr(e,r);{const o=e[t.index];return xe(o)?wi(-1,o):ee(o)}}if(32&n)return yi(t,e)()||ee(e[t.index]);{const r=Zc(e,t);return null!==r?Array.isArray(r)?r[0]:Vr(jn(e[oe]),r):Vr(e,t.next)}}return null}function Zc(e,t){return null!==t?e[oe][re].projection[t.projection]:null}function wi(e,t){const n=ae+e+1;if(n<t.length){const r=t[n],o=r[I].firstChild;if(null!==o)return Vr(r,o)}return t[yr]}function Ti(e,t,n){const r=Fr(e,t);r&&function Wm(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}function bi(e,t,n,r,o,i,s){for(;null!=n;){const a=r[n.index],l=n.type;if(s&&0===t&&(a&&ie(ee(a),r),n.flags|=2),32!=(32&n.flags))if(8&l)bi(e,t,n.child,r,o,i,!1),qt(t,e,o,a,i);else if(32&l){const c=yi(n,r);let u;for(;u=c();)qt(t,e,o,u,i);qt(t,e,o,a,i)}else 16&l?Yc(e,t,r,n,o,i):qt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Vn(e,t,n,r,o,i){bi(n,r,e.firstChild,t,o,i,!1)}function Yc(e,t,n,r,o,i){const s=n[oe],l=s[re].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)qt(t,e,o,l[c],i);else bi(e,t,l,s[U],o,i,!0)}function Kc(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Jc(e,t,n){const{mergedAttrs:r,classes:o,styles:i}=n;null!==r&&Xo(e,t,r),null!==o&&Kc(e,t,o),null!==i&&function Km(e,t,n){e.setAttribute(t,"style",n)}(e,t,i)}function Ni(){if(void 0===Hr&&(Hr=null,Z.trustedTypes))try{Hr=Z.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Hr}function Gt(e){return Ni()?.createHTML(e)||e}function Xc(e,t,n){const r=g(),o=B(),i=ye(o,r);if(2===o.type&&"iframe"===t.toLowerCase()){const s=i;throw s.src="",s.srcdoc=Gt(""),Ti(r[P],s),new _(-910,!1)}return e}function ey(e){xi=e}function eu(){return void 0!==xi?xi:typeof document<"u"?document:void 0}function Si(){if(void 0===$r&&($r=null,Z.trustedTypes))try{$r=Z.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return $r}function tu(e){return Si()?.createHTML(e)||e}function nu(e){return Si()?.createScript(e)||e}function ru(e){return Si()?.createScriptURL(e)||e}class yt{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Po})`}}class ty extends yt{getTypeName(){return"HTML"}}class ny extends yt{getTypeName(){return"Style"}}class ry extends yt{getTypeName(){return"Script"}}class oy extends yt{getTypeName(){return"URL"}}class iy extends yt{getTypeName(){return"ResourceURL"}}function Ke(e){return e instanceof yt?e.changingThisBreaksApplicationSecurity:e}function Wt(e,t){const n=ou(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Po})`)}return n===t}function ou(e){return e instanceof yt&&e.getTypeName()||null}function sy(e){return new ty(e)}function ay(e){return new ny(e)}function ly(e){return new ry(e)}function cy(e){return new oy(e)}function uy(e){return new iy(e)}function iu(e){const t=new fy(e);return function py(){try{return!!(new window.DOMParser).parseFromString(Gt(""),"text/html")}catch{return!1}}()?new dy(t):t}class dy{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{const n=(new window.DOMParser).parseFromString(Gt(t),"text/html").body;return null===n?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch{return null}}}class fy{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){const n=this.inertDocument.createElement("template");return n.innerHTML=Gt(t),n}}const hy=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Br(e){return(e=String(e)).match(hy)?e:"unsafe:"+e}function Je(e){const t={};for(const n of e.split(","))t[n]=!0;return t}function Hn(...e){const t={};for(const n of e)for(const r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}const su=Je("area,br,col,hr,img,wbr"),au=Je("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),lu=Je("rp,rt"),Pi=Hn(su,Hn(au,Je("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Hn(lu,Je("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Hn(lu,au)),Ri=Je("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),cu=Hn(Ri,Je("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Je("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext")),gy=Je("script,style,template");class my{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0;for(;n;)if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild)n=n.firstChild;else for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let o=this.checkClobberedElement(n,n.nextSibling);if(o){n=o;break}n=this.checkClobberedElement(n,n.parentNode)}return this.buf.join("")}startElement(t){const n=t.nodeName.toLowerCase();if(!Pi.hasOwnProperty(n))return this.sanitizedSomething=!0,!gy.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);const r=t.attributes;for(let o=0;o<r.length;o++){const i=r.item(o),s=i.name,a=s.toLowerCase();if(!cu.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;Ri[a]&&(l=Br(l)),this.buf.push(" ",s,'="',uu(l),'"')}return this.buf.push(">"),!0}endElement(t){const n=t.nodeName.toLowerCase();Pi.hasOwnProperty(n)&&!su.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(uu(t))}checkClobberedElement(t,n){if(n&&(t.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)===Node.DOCUMENT_POSITION_CONTAINED_BY)throw new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`);return n}}const yy=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,vy=/([^\#-~ |!])/g;function uu(e){return e.replace(/&/g,"&amp;").replace(yy,function(t){return"&#"+(1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320)+65536)+";"}).replace(vy,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}let Ur;function du(e,t){let n=null;try{Ur=Ur||iu(e);let r=t?String(t):"";n=Ur.getInertBodyElement(r);let o=5,i=r;do{if(0===o)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Ur.getInertBodyElement(r)}while(r!==i);return Gt((new my).sanitizeChildren(Ai(n)||n))}finally{if(n){const r=Ai(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function Ai(e){return"content"in e&&function Iy(e){return e.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===e.nodeName}(e)?e.content:null}var W=(()=>((W=W||{})[W.NONE=0]="NONE",W[W.HTML=1]="HTML",W[W.STYLE=2]="STYLE",W[W.SCRIPT=3]="SCRIPT",W[W.URL=4]="URL",W[W.RESOURCE_URL=5]="RESOURCE_URL",W))();function fu(e){const t=$n();return t?tu(t.sanitize(W.HTML,e)||""):Wt(e,"HTML")?tu(Ke(e)):du(eu(),N(e))}function pu(e){const t=$n();return t?t.sanitize(W.STYLE,e)||"":Wt(e,"Style")?Ke(e):N(e)}function Oi(e){const t=$n();return t?t.sanitize(W.URL,e)||"":Wt(e,"URL")?Ke(e):Br(N(e))}function Li(e){const t=$n();if(t)return ru(t.sanitize(W.RESOURCE_URL,e)||"");if(Wt(e,"ResourceURL"))return ru(Ke(e));throw new _(904,!1)}function hu(e){const t=$n();if(t)return nu(t.sanitize(W.SCRIPT,e)||"");if(Wt(e,"Script"))return nu(Ke(e));throw new _(905,!1)}function gu(e){return Gt(e[0])}function mu(e){return function Xm(e){return Ni()?.createScriptURL(e)||e}(e[0])}function yu(e,t,n){return function Dy(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?Li:Oi}(t,n)(e)}function $n(){const e=g();return e&&e[jo]}const ki=new V("ENVIRONMENT_INITIALIZER"),Fi=new V("INJECTOR",-1),vu=new V("INJECTOR_DEF_TYPES");class Iu{get(t,n=ot){if(n===ot){const r=new Error(`NullInjectorError: No provider for ${j(t)}!`);throw r.name="NullInjectorError",r}return n}}function _y(e){return{\u0275providers:e}}function Du(...e){return{\u0275providers:_u(0,e),\u0275fromNgModule:!0}}function _u(e,...t){const n=[],r=new Set;let o;return gt(t,i=>{const s=i;ji(s,n,[],r)&&(o||(o=[]),o.push(s))}),void 0!==o&&Mu(o,n),n}function Mu(e,t){for(let n=0;n<e.length;n++){const{providers:o}=e[n];Vi(o,i=>{t.push(i)})}}function ji(e,t,n,r){if(!(e=E(e)))return!1;let o=null,i=nl(e);const s=!i&&k(e);if(i||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(i=nl(l),!i)return!1;o=l}const a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)ji(c,t,n,r)}}else{if(!i)return!1;{if(null!=i.imports&&!a){let c;r.add(o);try{gt(i.imports,u=>{ji(u,t,n,r)&&(c||(c=[]),c.push(u))})}finally{}void 0!==c&&Mu(c,t)}if(!a){const c=ft(o)||(()=>new o);t.push({provide:o,useFactory:c,deps:A},{provide:vu,useValue:o,multi:!0},{provide:ki,useValue:()=>z(o),multi:!0})}const l=i.providers;null==l||a||Vi(l,u=>{t.push(u)})}}return o!==e&&void 0!==e.providers}function Vi(e,t){for(let n of e)ar(n)&&(n=n.\u0275providers),Array.isArray(n)?Vi(n,t):t(n)}const My=F({provide:String,useValue:F});function Hi(e){return null!==e&&"object"==typeof e&&My in e}function vt(e){return"function"==typeof e}const $i=new V("Set Injector scope."),qr={},Cy={};let Bi;function Gr(){return void 0===Bi&&(Bi=new Iu),Bi}class It{}class wu extends It{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,qi(t,s=>this.processProvider(s)),this.records.set(Fi,Qt(void 0,this)),o.has("environment")&&this.records.set(It,Qt(void 0,this));const i=this.records.get($i);null!=i&&"string"==typeof i.value&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(vu.multi,A,w.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const t of this._ngOnDestroyHooks)t.ngOnDestroy();for(const t of this._onDestroyHooks)t()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),this._onDestroyHooks.length=0}}onDestroy(t){this._onDestroyHooks.push(t)}runInContext(t){this.assertNotDestroyed();const n=ut(this),r=_e(void 0);try{return t()}finally{ut(n),_e(r)}}get(t,n=ot,r=w.Default){this.assertNotDestroyed(),r=_n(r);const o=ut(this),i=_e(void 0);try{if(!(r&w.SkipSelf)){let a=this.records.get(t);if(void 0===a){const l=function xy(e){return"function"==typeof e||"object"==typeof e&&e instanceof V}(t)&&vn(t);a=l&&this.injectableDefInScope(l)?Qt(Ui(t),qr):null,this.records.set(t,a)}if(null!=a)return this.hydrate(t,a)}return(r&w.Self?Gr():this.parent).get(t,n=r&w.Optional&&n===ot?null:n)}catch(s){if("NullInjectorError"===s.name){if((s[dr]=s[dr]||[]).unshift(j(t)),o)throw s;return function ug(e,t,n,r){const o=e[dr];throw t[il]&&o.unshift(t[il]),e.message=function dg(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&e.charAt(1)==ag?e.slice(2):e;let o=j(t);if(Array.isArray(t))o=t.map(j).join(" -> ");else if("object"==typeof t){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+("string"==typeof a?JSON.stringify(a):j(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(sg,"\n  ")}`}("\n"+e.message,o,n,r),e[ig]=o,e[dr]=null,e}(s,t,"R3InjectorError",this.source)}throw s}finally{_e(i),ut(o)}}resolveInjectorInitializers(){const t=ut(this),n=_e(void 0);try{const r=this.get(ki.multi,A,w.Self);for(const o of r)o()}finally{ut(t),_e(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(j(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new _(205,!1)}processProvider(t){let n=vt(t=E(t))?t:E(t&&t.provide);const r=function Ty(e){return Hi(e)?Qt(void 0,e.useValue):Qt(Tu(e),qr)}(t);if(vt(t)||!0!==t.multi)this.records.get(n);else{let o=this.records.get(n);o||(o=Qt(void 0,qr,!0),o.factory=()=>Lo(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===qr&&(n.value=Cy,n.value=n.factory()),"object"==typeof n.value&&n.value&&function Ny(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=E(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}}function Ui(e){const t=vn(e),n=null!==t?t.factory:ft(e);if(null!==n)return n;if(e instanceof V)throw new _(204,!1);if(e instanceof Function)return function wy(e){const t=e.length;if(t>0)throw An(t,"?"),new _(204,!1);const n=function ng(e){return e&&(e[In]||e[rl])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new _(204,!1)}function Tu(e,t,n){let r;if(vt(e)){const o=E(e);return ft(o)||Ui(o)}if(Hi(e))r=()=>E(e.useValue);else if(function Cu(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...Lo(e.deps||[]));else if(function Eu(e){return!(!e||!e.useExisting)}(e))r=()=>z(E(e.useExisting));else{const o=E(e&&(e.useClass||e.provide));if(!function by(e){return!!e.deps}(e))return ft(o)||Ui(o);r=()=>new o(...Lo(e.deps))}return r}function Qt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function qi(e,t){for(const n of e)Array.isArray(n)?qi(n,t):n&&ar(n)?qi(n.\u0275providers,t):t(n)}class bu{}class Wr{}class Py{resolveComponentFactory(t){throw function Sy(e){const t=Error(`No component factory found for ${j(e)}. Did you add it to @NgModule.entryComponents?`);return t.ngComponent=e,t}(t)}}let Bn=(()=>{class e{}return e.NULL=new Py,e})();function Ry(){return zt(te(),g())}function zt(e,t){return new Un(ye(e,t))}let Un=(()=>{class e{constructor(n){this.nativeElement=n}}return e.__NG_ELEMENT_ID__=Ry,e})();function Ay(e){return e instanceof Un?e.nativeElement:e}class xu{}let Oy=(()=>{class e{}return e.__NG_ELEMENT_ID__=()=>function Ly(){const e=g(),n=pe(te().index,e);return(me(n)?n:e)[P]}(),e})(),Su=(()=>{class e{}return e.\u0275prov=X({token:e,providedIn:"root",factory:()=>null}),e})();class Pu{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const Ru=new Pu("15.2.10"),Qr={},Gi="ngOriginalError";function Wi(e){return e[Gi]}class qn{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Wi(t);for(;n&&Wi(n);)n=Wi(n);return n||null}}function Au(e){return e.ownerDocument.defaultView}function Ou(e){return e.ownerDocument}function Lu(e){return e.ownerDocument.body}function Xe(e){return e instanceof Function?e():e}function Fu(e,t,n){let r=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}const ju="ng-template";function Uy(e,t,n){let r=0,o=!0;for(;r<e.length;){let i=e[r++];if("string"==typeof i&&o){const s=e[r++];if(n&&"class"===i&&-1!==Fu(s.toLowerCase(),t,0))return!0}else{if(1===i){for(;r<e.length&&"string"==typeof(i=e[r++]);)if(i.toLowerCase()===t)return!0;return!1}"number"==typeof i&&(o=!1)}}return!1}function Vu(e){return 4===e.type&&e.value!==ju}function qy(e,t,n){return t===(4!==e.type||n?e.value:ju)}function Gy(e,t,n){let r=4;const o=e.attrs||[],i=function zy(e){for(let t=0;t<e.length;t++)if(zl(e[t]))return t;return e.length}(o);let s=!1;for(let a=0;a<t.length;a++){const l=t[a];if("number"!=typeof l){if(!s)if(4&r){if(r=2|1&r,""!==l&&!qy(e,l,n)||""===l&&1===t.length){if(Pe(r))return!1;s=!0}}else{const c=8&r?l:t[++a];if(8&r&&null!==e.attrs){if(!Uy(e.attrs,c,n)){if(Pe(r))return!1;s=!0}continue}const d=Wy(8&r?"class":l,o,Vu(e),n);if(-1===d){if(Pe(r))return!1;s=!0;continue}if(""!==c){let f;f=d>i?"":o[d+1].toLowerCase();const p=8&r?f:null;if(p&&-1!==Fu(p,c,0)||2&r&&c!==f){if(Pe(r))return!1;s=!0}}}}else{if(!s&&!Pe(r)&&!Pe(l))return!1;if(s&&Pe(l))continue;s=!1,r=l|1&r}}return Pe(r)||s}function Pe(e){return 0==(1&e)}function Wy(e,t,n,r){if(null===t)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){const s=t[o];if(s===e)return o;if(3===s||6===s)i=!0;else{if(1===s||2===s){let a=t[++o];for(;"string"==typeof a;)a=t[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=i?1:2}return-1}return function Zy(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function Hu(e,t,n=!1){for(let r=0;r<t.length;r++)if(Gy(e,t[r],n))return!0;return!1}function Yy(e,t){e:for(let n=0;n<t.length;n++){const r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function $u(e,t){return e?":not("+t.trim()+")":t}function Ky(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?o+="."+s:4&r&&(o+=" "+s);else""!==o&&!Pe(s)&&(t+=$u(i,o),o=""),r=s,i=i||!Pe(r);n++}return""!==o&&(t+=$u(i,o)),t}const T={};function Bu(e){Uu(R(),g(),ce()+e,!1)}function Uu(e,t,n,r){if(!r)if(3==(3&t[S])){const i=e.preOrderCheckHooks;null!==i&&Er(t,i,n)}else{const i=e.preOrderHooks;null!==i&&Cr(t,i,0,n)}pt(n)}const qu={\u0275\u0275defineInjectable:X,\u0275\u0275defineInjector:cr,\u0275\u0275inject:z,\u0275\u0275invalidFactoryDep:Oo,resolveForwardRef:E};const tv=F({provide:String,useValue:F});function Gu(e){return void 0!==e.useClass}function Wu(e){return void 0!==e.useFactory}const iv=Vt("Injectable",void 0,void 0,void 0,(e,t)=>function ev(e,t){let n=null,r=null;e.hasOwnProperty(In)||Object.defineProperty(e,In,{get:()=>(null===n&&(n=K().compileInjectable(qu,`ng:///${e.name}/\u0275prov.js`,function ov(e,t){const n=t||{providedIn:null},r={name:e.name,type:e,typeArgumentCount:0,providedIn:n.providedIn};return(Gu(n)||Wu(n))&&void 0!==n.deps&&(r.deps=mc(n.deps)),Gu(n)?r.useClass=n.useClass:function nv(e){return tv in e}(n)?r.useValue=n.useValue:Wu(n)?r.useFactory=n.useFactory:function rv(e){return void 0!==e.useExisting}(n)&&(r.useExisting=n.useExisting),r}(e,t))),n)}),e.hasOwnProperty(Qe)||Object.defineProperty(e,Qe,{get:()=>{if(null===r){const o=K();r=o.compileFactory(qu,`ng:///${e.name}/\u0275fac.js`,{name:e.name,type:e,typeArgumentCount:0,deps:Lr(e),target:o.FactoryTarget.Injectable})}return r},configurable:!0})}(e,t));function Qi(e,t=null,n=null,r){const o=Qu(e,t,n,r);return o.resolveInjectorInitializers(),o}function Qu(e,t=null,n=null,r,o=new Set){const i=[n||A,Du(e)];return r=r||("object"==typeof e?void 0:j(e)),new wu(i,t||Gr(),r||null,o)}let at=(()=>{class e{static create(n,r){if(Array.isArray(n))return Qi({name:""},r,n,"");{const o=n.name??"";return Qi({name:o},n.parent,n.providers,o)}}}return e.THROW_IF_NOT_FOUND=ot,e.NULL=new Iu,e.\u0275prov=X({token:e,providedIn:"any",factory:()=>z(Fi)}),e.__NG_ELEMENT_ID__=-1,e})();function zi(e){return e.length>1?" ("+function sv(e){const t=[];for(let n=0;n<e.length;++n){if(t.indexOf(e[n])>-1)return t.push(e[n]),t;t.push(e[n])}return t}(e.slice().reverse()).map(r=>j(r.token)).join(" -> ")+")":""}function Zi(e,t,n,r){const o=[t],i=n(o),s=r?function ky(e,t){const n=`${e} caused by: ${t instanceof Error?t.message:t}`,r=Error(n);return r[Gi]=t,r}(i,r):Error(i);return s.addKey=av,s.keys=o,s.injectors=[e],s.constructResolvingMessage=n,s[Gi]=r,s}function av(e,t){this.injectors.push(e),this.keys.push(t),this.message=this.constructResolvingMessage(this.keys)}function zu(e,t){const n=[];for(let r=0,o=t.length;r<o;r++){const i=t[r];n.push(i&&0!=i.length?i.map(j).join(" "):"?")}return Error("Cannot resolve all parameters for '"+j(e)+"'("+n.join(", ")+"). Make sure that all the parameters are decorated with Inject or have valid type annotations and that '"+j(e)+"' is decorated with Injectable.")}function pv(e,t){return Error(`Cannot mix multi providers and regular providers, got: ${e} ${t}`)}class et{constructor(t,n){if(this.token=t,this.id=n,!t)throw new _(208,!1);this.displayName=j(this.token)}static get(t){return Zu.get(E(t))}static get numberOfKeys(){return Zu.numberOfKeys}}class hv{constructor(){this._allKeys=new Map}get(t){if(t instanceof et)return t;if(this._allKeys.has(t))return this._allKeys.get(t);const n=new et(t,et.numberOfKeys);return this._allKeys.set(t,n),n}get numberOfKeys(){return this._allKeys.size}}const Zu=new hv;class zr{constructor(t,n,r){this.key=t,this.optional=n,this.visibility=r}static fromKey(t){return new zr(t,!1,null)}}const gv=[];class Yu{constructor(t,n,r){this.key=t,this.resolvedFactories=n,this.multiProvider=r,this.resolvedFactory=this.resolvedFactories[0]}}class Ku{constructor(t,n){this.factory=t,this.dependencies=n}}function mv(e){let t,n;if(e.useClass){const r=E(e.useClass);t=Ln().factory(r),n=Xu(r)}else e.useExisting?(t=r=>r,n=[zr.fromKey(et.get(e.useExisting))]):e.useFactory?(t=e.useFactory,n=function Dv(e,t){if(t){const n=t.map(r=>[r]);return t.map(r=>ed(e,r,n))}return Xu(e)}(e.useFactory,e.deps)):(t=()=>e.useValue,n=gv);return new Ku(t,n)}function yv(e){return new Yu(et.get(e.provide),[mv(e)],e.multi||!1)}function vv(e){const r=function Iv(e,t){for(let n=0;n<e.length;n++){const r=e[n],o=t.get(r.key.id);if(o){if(r.multiProvider!==o.multiProvider)throw pv(o,r);if(r.multiProvider)for(let i=0;i<r.resolvedFactories.length;i++)o.resolvedFactories.push(r.resolvedFactories[i]);else t.set(r.key.id,r)}else{let i;i=r.multiProvider?new Yu(r.key,r.resolvedFactories.slice(),r.multiProvider):r,t.set(r.key.id,i)}}return t}(Ju(e,[]).map(yv),new Map);return Array.from(r.values())}function Ju(e,t){return e.forEach(n=>{if(n instanceof Sr)t.push({provide:n,useClass:n});else if(n&&"object"==typeof n&&void 0!==n.provide)t.push(n);else{if(!Array.isArray(n))throw function dv(e){return Error(`Invalid provider - only instances of Provider and Type are allowed, got: ${e}`)}(n);Ju(n,t)}}),t}function Xu(e){const t=Ln().parameters(e);if(!t)return[];if(t.some(n=>null==n))throw zu(e,t);return t.map(n=>ed(e,n,t))}function ed(e,t,n){let r=null,o=!1;if(!Array.isArray(t))return Yi(t instanceof Ar?t.token:t,o,null);let i=null;for(let s=0;s<t.length;++s){const a=t[s];a instanceof Sr?r=a:a instanceof Ar?r=a.token:a instanceof On?o=!0:a instanceof Or||a instanceof Bt?i=a:a instanceof V&&(r=a)}if(r=E(r),null!=r)return Yi(r,o,i);throw zu(e,n)}function Yi(e,t,n){return new zr(et.get(e),t,n)}const Gn={};class Yt{static resolve(t){return vv(t)}static resolveAndCreate(t,n){const r=Yt.resolve(t);return Yt.fromResolvedProviders(r,n)}static fromResolvedProviders(t,n){return new _v(t,n)}}let _v=(()=>{class e{constructor(n,r){this._constructionCounter=0,this._providers=n,this.parent=r||null;const o=n.length;this.keyIds=[],this.objs=[];for(let i=0;i<o;i++)this.keyIds[i]=n[i].key.id,this.objs[i]=Gn}get(n,r=ot){return this._getByKey(et.get(n),null,r)}resolveAndCreateChild(n){const r=Yt.resolve(n);return this.createChildFromResolved(r)}createChildFromResolved(n){const r=new e(n);return r.parent=this,r}resolveAndInstantiate(n){return this.instantiateResolved(Yt.resolve([n])[0])}instantiateResolved(n){return this._instantiateProvider(n)}getProviderAtIndex(n){if(n<0||n>=this._providers.length)throw function fv(e){return Error(`Index ${e} is out-of-bounds.`)}(n);return this._providers[n]}_new(n){if(this._constructionCounter++>this._getMaxNumberOfObjects())throw function cv(e,t){return Zi(e,t,function(n){return`Cannot instantiate cyclic dependency!${zi(n)}`})}(this,n.key);return this._instantiateProvider(n)}_getMaxNumberOfObjects(){return this.objs.length}_instantiateProvider(n){if(n.multiProvider){const r=[];for(let o=0;o<n.resolvedFactories.length;++o)r[o]=this._instantiate(n,n.resolvedFactories[o]);return r}return this._instantiate(n,n.resolvedFactories[0])}_instantiate(n,r){const o=r.factory;let i,s;try{i=r.dependencies.map(a=>this._getByReflectiveDependency(a))}catch(a){throw a.addKey&&a.addKey(this,n.key),a}try{s=o(...i)}catch(a){throw function uv(e,t,n,r){return Zi(e,r,function(o){const i=j(o[0].token);return`${t.message}: Error during instantiation of ${i}!${zi(o)}.`},t)}(this,a,0,n.key)}return s}_getByReflectiveDependency(n){return this._getByKey(n.key,n.visibility,n.optional?null:ot)}_getByKey(n,r,o){return n===e.INJECTOR_KEY?this:r instanceof Or?this._getByKeySelf(n,o):this._getByKeyDefault(n,o,r)}_getObjByKeyId(n){for(let r=0;r<this.keyIds.length;r++)if(this.keyIds[r]===n)return this.objs[r]===Gn&&(this.objs[r]=this._new(this._providers[r])),this.objs[r];return Gn}_throwOrNull(n,r){if(r!==ot)return r;throw function lv(e,t){return Zi(e,t,function(n){return`No provider for ${j(n[0].token)}!${zi(n)}`})}(this,n)}_getByKeySelf(n,r){const o=this._getObjByKeyId(n.id);return o!==Gn?o:this._throwOrNull(n,r)}_getByKeyDefault(n,r,o){let i;for(i=o instanceof Bt?this.parent:this;i instanceof e;){const s=i,a=s._getObjByKeyId(n.id);if(a!==Gn)return a;i=s.parent}return null!==i?i.get(n.token,r):this._throwOrNull(n,r)}get displayName(){return`ReflectiveInjector(providers: [${function Mv(e,t){const n=[];for(let r=0;r<e._providers.length;++r)n[r]=t(e.getProviderAtIndex(r));return n}(this,r=>' "'+r.key.displayName+'" ').join(", ")}])`}toString(){return this.displayName}}return e.INJECTOR_KEY=et.get(at),e})();function Kt(e,t=w.Default){const n=g();return null===n?z(e,t):rc(te(),n,E(e),t)}function td(){throw new Error("invalid")}function nd(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const i=n[r+1];if(-1!==i){const s=e.data[i];zo(n[r]),s.contentQueries(2,t[i],i)}}}function Zr(e,t,n,r,o,i,s,a,l,c,u){const d=t.blueprint.slice();return d[ze]=o,d[S]=76|r,(null!==u||e&&1024&e[S])&&(d[S]|=1024),bl(d),d[U]=d[xt]=e,d[G]=n,d[mr]=s||e&&e[mr],d[P]=a||e&&e[P],d[jo]=l||e&&e[jo]||null,d[gr]=c||e&&e[gr]||null,d[re]=i,d[Tn]=function Tm(){return wm++}(),d[ml]=u,d[oe]=2==t.type?e[oe]:d,d}function Jt(e,t,n,r,o){let i=e.data[t];if(null===i)i=Ki(e,t,n,r,o),function Sg(){return x.lFrame.inI18n}()&&(i.flags|=32);else if(64&i.type){i.type=n,i.value=r,i.attrs=o;const s=Nn();i.injectorIndex=null===s?-1:s.injectorIndex}return Fe(i,!0),i}function Ki(e,t,n,r,o){const i=Ol(),s=qo(),l=e.data[t]=function Nv(e,t,n,r,o,i){return{type:n,index:r,insertBeforeIndex:null,injectorIndex:t?t.injectorIndex:-1,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:0,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?i:i&&i.parent,n,t,r,o);return null===e.firstChild&&(e.firstChild=l),null!==i&&(s?null==i.child&&null!==l.parent&&(i.child=l):null===i.next&&(i.next=l,l.prev=i)),l}function Wn(e,t,n,r){if(0===n)return-1;const o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ji(e,t,n){Zo(t);try{const r=e.viewQuery;null!==r&&as(1,r,n);const o=e.template;null!==o&&rd(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&nd(e,t),e.staticViewQueries&&as(2,e.viewQuery,n);const i=e.components;null!==i&&function wv(e,t){for(let n=0;n<t.length;n++)zv(e,t[n])}(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[S]&=-5,Yo()}}function Yr(e,t,n,r){const o=t[S];if(128!=(128&o)){Zo(t);try{bl(t),kl(e.bindingStartIndex),null!==n&&rd(e,t,n,2,r);const s=3==(3&o);if(s){const c=e.preOrderCheckHooks;null!==c&&Er(t,c,null)}else{const c=e.preOrderHooks;null!==c&&Cr(t,c,0,null),Ko(t,0)}if(function Wv(e){for(let t=vi(e);null!==t;t=Ii(t)){if(!t[yl])continue;const n=t[Pt];for(let r=0;r<n.length;r++){const o=n[r];512&o[S]||Uo(o[U],1),o[S]|=512}}}(t),function Gv(e){for(let t=vi(e);null!==t;t=Ii(t))for(let n=ae;n<t.length;n++){const r=t[n],o=r[I];_r(r)&&Yr(o,r,o.template,r[G])}}(t),null!==e.contentQueries&&nd(e,t),s){const c=e.contentCheckHooks;null!==c&&Er(t,c)}else{const c=e.contentHooks;null!==c&&Cr(t,c,1),Ko(t,1)}!function Ev(e,t){const n=e.hostBindingOpCodes;if(null!==n)try{for(let r=0;r<n.length;r++){const o=n[r];if(o<0)pt(~o);else{const i=o,s=n[++r],a=n[++r];Pg(s,i),a(2,t[i])}}}finally{pt(-1)}}(e,t);const a=e.components;null!==a&&function Cv(e,t){for(let n=0;n<t.length;n++)Qv(e,t[n])}(t,a);const l=e.viewQuery;if(null!==l&&as(2,l,r),s){const c=e.viewCheckHooks;null!==c&&Er(t,c)}else{const c=e.viewHooks;null!==c&&Cr(t,c,2),Ko(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[S]&=-41,512&t[S]&&(t[S]&=-513,Uo(t[U],-1))}finally{Yo()}}}function rd(e,t,n,r,o){const i=ce(),s=2&r;try{pt(-1),s&&t.length>H&&Uu(e,t,H,!1),Ee(s?2:0,o),n(r,o)}finally{pt(i),Ee(s?3:1,o)}}function Xi(e,t,n){if(Ho(t)){const o=t.directiveEnd;for(let i=t.directiveStart;i<o;i++){const s=e.data[i];s.contentQueries&&s.contentQueries(1,n[i],i)}}}function es(e,t,n){xl()&&(function Lv(e,t,n,r){const o=n.directiveStart,i=n.directiveEnd;bn(n)&&function Bv(e,t,n){const r=ye(t,e),o=od(n),i=e[mr],s=Kr(e,Zr(e,o,null,n.onPush?32:16,r,t,i,i.createRenderer(r,n),null,null,null));e[t.index]=s}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Nr(n,t),ie(r,t);const s=n.initialInputs;for(let a=o;a<i;a++){const l=e.data[a],c=ht(t,e,a,n);ie(c,t),null!==s&&Uv(0,a-o,c,l,0,s),Se(l)&&(pe(n.index,t)[G]=ht(t,e,a,n))}}(e,t,n,ye(n,t)),64==(64&n.flags)&&cd(e,t,n))}function ts(e,t,n=ye){const r=t.localNames;if(null!==r){let o=t.index+1;for(let i=0;i<r.length;i+=2){const s=r[i+1],a=-1===s?n(t,e):e[s];e[o++]=a}}}function od(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=ns(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts):t}function ns(e,t,n,r,o,i,s,a,l,c){const u=H+r,d=u+o,f=function Tv(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:T);return n}(u,d),p="function"==typeof c?c():c;return f[I]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,u),bindingStartIndex:u,expandoStartIndex:d,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:p,incompleteFirstPass:!1}}function id(e,t,n,r){const o=dd(t);null===n?o.push(r):(o.push(n),e.firstCreatePass&&fd(e).push(r,o.length-1))}function sd(e,t,n,r){for(let o in e)if(e.hasOwnProperty(o)){n=null===n?{}:n;const i=e[o];null===r?ad(n,t,o,i):r.hasOwnProperty(o)&&ad(n,t,r[o],i)}return n}function ad(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function Ie(e,t,n,r,o,i,s,a){const l=ye(t,n);let u,c=t.inputs;!a&&null!=c&&(u=c[r])?(ls(e,n,u,r,o),bn(t)&&function Pv(e,t){const n=pe(t,e);16&n[S]||(n[S]|=32)}(n,t.index)):3&t.type&&(r=function Sv(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=s?s(o,t.value||"",r):o,i.setProperty(l,r,o))}function rs(e,t,n,r){if(xl()){const o=null===r?null:{"":-1},i=function Fv(e,t){const n=e.directiveRegistry;let r=null,o=null;if(n)for(let i=0;i<n.length;i++){const s=n[i];if(Hu(t,s.selectors,!1))if(r||(r=[]),Se(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s),os(e,t,a.length)}else r.unshift(s),os(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return null===r?null:[r,o]}(e,n);let s,a;null===i?s=a=null:[s,a]=i,null!==s&&ld(e,t,n,s,o,a),o&&function jv(e,t,n){if(t){const r=e.localNames=[];for(let o=0;o<t.length;o+=2){const i=n[t[o+1]];if(null==i)throw new _(-301,!1);r.push(t[o],i)}}}(n,r,o)}n.mergedAttrs=Sn(n.mergedAttrs,n.attrs)}function ld(e,t,n,r,o,i){for(let c=0;c<r.length;c++)ri(Nr(n,t),e,r[c].type);!function Hv(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let c=0;c<r.length;c++){const u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=Wn(e,t,r.length,null);for(let c=0;c<r.length;c++){const u=r[c];n.mergedAttrs=Sn(n.mergedAttrs,u.hostAttrs),$v(e,n,t,l,u),Vv(l,u,o),null!==u.contentQueries&&(n.flags|=4),(null!==u.hostBindings||null!==u.hostAttrs||0!==u.hostVars)&&(n.flags|=64);const d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??(e.preOrderHooks=[])).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??(e.preOrderCheckHooks=[])).push(n.index),a=!0),l++}!function xv(e,t,n){const o=t.directiveEnd,i=e.data,s=t.attrs,a=[];let l=null,c=null;for(let u=t.directiveStart;u<o;u++){const d=i[u],f=n?n.get(d):null,h=f?f.outputs:null;l=sd(d.inputs,u,l,f?f.inputs:null),c=sd(d.outputs,u,c,h);const y=null===l||null===s||Vu(t)?null:qv(l,u,s);a.push(y)}null!==l&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}(e,n,i)}function cd(e,t,n){const r=n.directiveStart,o=n.directiveEnd,i=n.index,s=function Rg(){return x.lFrame.currentDirectiveIndex}();try{pt(i);for(let a=r;a<o;a++){const l=e.data[a],c=t[a];Wo(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&kv(l,c)}}finally{pt(-1),Wo(s)}}function kv(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function os(e,t,n){t.componentOffset=n,(e.components??(e.components=[])).push(t.index)}function Vv(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Se(t)&&(n[""]=e)}}function $v(e,t,n,r,o){e.data[r]=o;const i=o.factory||(o.factory=ft(o.type)),s=new xn(i,Se(o),Kt);e.blueprint[r]=s,n[r]=s,function Av(e,t,n,r,o){const i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~t.index;(function Ov(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=a&&s.push(a),s.push(n,r,i)}}(e,t,r,Wn(e,n,o.hostVars,T),o)}function He(e,t,n,r,o,i){const s=ye(e,t);is(t[P],s,i,e.value,n,r,o)}function is(e,t,n,r,o,i,s){if(null==i)e.removeAttribute(t,o,n);else{const a=null==s?N(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Uv(e,t,n,r,o,i){const s=i[t];if(null!==s){const a=r.setInput;for(let l=0;l<s.length;){const c=s[l++],u=s[l++],d=s[l++];null!==a?r.setInput(n,d,c,u):n[u]=d}}}function qv(e,t,n){let r=null,o=0;for(;o<n.length;){const i=n[o];if(0!==i)if(5!==i){if("number"==typeof i)break;if(e.hasOwnProperty(i)){null===r&&(r=[]);const s=e[i];for(let a=0;a<s.length;a+=2)if(s[a]===t){r.push(i,s[a+1],n[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function ud(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null]}function Qv(e,t){const n=pe(t,e);if(_r(n)){const r=n[I];48&n[S]?Yr(r,n,r.template,n[G]):n[dt]>0&&ss(n)}}function ss(e){for(let r=vi(e);null!==r;r=Ii(r))for(let o=ae;o<r.length;o++){const i=r[o];if(_r(i))if(512&i[S]){const s=i[I];Yr(s,i,s.template,i[G])}else i[dt]>0&&ss(i)}const n=e[I].components;if(null!==n)for(let r=0;r<n.length;r++){const o=pe(n[r],e);_r(o)&&o[dt]>0&&ss(o)}}function zv(e,t){const n=pe(t,e),r=n[I];(function Zv(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])})(r,n),Ji(r,n,n[G])}function Kr(e,t){return e[Cn]?e[gl][Ne]=t:e[Cn]=t,e[gl]=t,t}function Jr(e){for(;e;){e[S]|=32;const t=jn(e);if(gg(e)&&!t)return e;e=t}return null}function Xr(e,t,n,r=!0){const o=t[mr];o.begin&&o.begin();try{Yr(e,t,e.template,n)}catch(s){throw r&&hd(t,s),s}finally{o.end&&o.end()}}function as(e,t,n){zo(0),t(e,n)}function dd(e){return e[Nt]||(e[Nt]=[])}function fd(e){return e.cleanup||(e.cleanup=[])}function pd(e,t,n){return(null===e||Se(e))&&(n=function Mg(e){for(;Array.isArray(e);){if("object"==typeof e[Vo])return e;e=e[ze]}return null}(n[t.index])),n[P]}function hd(e,t){const n=e[gr],r=n?n.get(qn,null):null;r&&r.handleError(t)}function ls(e,t,n,r,o){for(let i=0;i<n.length;){const s=n[i++],a=n[i++],l=t[s],c=e.data[s];null!==c.setInput?c.setInput(l,o,r,a):l[a]=o}}function tt(e,t,n){const r=Dr(t,e);jc(e[P],r,n)}function eo(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?i=a:1==i?o=xo(o,a):2==i&&(r=xo(r,a+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function to(e,t,n,r,o=!1){for(;null!==n;){const i=t[n.index];if(null!==i&&r.push(ee(i)),xe(i))for(let a=ae;a<i.length;a++){const l=i[a],c=l[I].firstChild;null!==c&&to(l[I],l,c,r)}const s=n.type;if(8&s)to(e,t,n.child,r);else if(32&s){const a=yi(n,t);let l;for(;l=a();)r.push(l)}else if(16&s){const a=Zc(t,n);if(Array.isArray(a))r.push(...a);else{const l=jn(t[oe]);to(l[I],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}class Xt{get rootNodes(){const t=this._lView,n=t[I];return to(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[G]}set context(t){this._lView[G]=t}get destroyed(){return 128==(128&this._lView[S])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[U];if(xe(t)){const n=t[vr],r=n?n.indexOf(this):-1;r>-1&&(Mi(t,r),Pr(n,r))}this._attachedToViewContainer=!1}Hc(this._lView[I],this._lView)}onDestroy(t){id(this._lView[I],this._lView,null,t)}markForCheck(){Jr(this._cdRefInjectingView||this._lView)}detach(){this._lView[S]&=-65}reattach(){this._lView[S]|=64}detectChanges(){Xr(this._lView[I],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function Hm(e,t){Vn(e,t,t[P],2,null,null)}(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t}}class Yv extends Xt{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;Xr(t[I],t,t[G],!1)}checkNoChanges(){}get context(){return null}}class gd extends Bn{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=k(t);return new en(n,this.ngModule)}}function md(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class Jv{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=_n(r);const o=this.injector.get(t,Qr,r);return o!==Qr||n===Qr?o:this.parentInjector.get(t,n,r)}}class en extends Wr{get inputs(){return md(this.componentDef.inputs)}get outputs(){return md(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function Jy(e){return e.map(Ky).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=(o=o||this.ngModule)instanceof It?o:o?.injector;i&&null!==this.componentDef.getStandaloneInjector&&(i=this.componentDef.getStandaloneInjector(i)||i);const s=i?new Jv(t,i):t,a=s.get(xu,null);if(null===a)throw new _(407,!1);const l=s.get(Su,null),c=a.createRenderer(null,this.componentDef),u=this.componentDef.selectors[0][0]||"div",d=r?function bv(e,t,n){return e.selectRootElement(t,n===Ge.ShadowDom)}(c,r,this.componentDef.encapsulation):_i(c,u,function Kv(e){const t=e.toLowerCase();return"svg"===t?Cl:"math"===t?wl:null}(u)),f=this.componentDef.onPush?288:272,p=ns(0,null,null,1,0,null,null,null,null,null),h=Zr(null,p,null,f,null,null,a,c,l,s,null);let y,v;Zo(h);try{const D=this.componentDef;let C,m=null;D.findHostDirectiveDefs?(C=[],m=new Map,D.findHostDirectiveDefs(D,C,m),C.push(D)):C=[D];const b=function Xv(e,t){const n=e[I],r=H;return e[r]=t,Jt(n,r,2,"#host",null)}(h,d),$=function eI(e,t,n,r,o,i,s,a){const l=o[I];!function tI(e,t,n,r){for(const o of e)t.mergedAttrs=Sn(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(eo(t,t.mergedAttrs,!0),null!==n&&Jc(r,n,t))}(r,e,t,s);const c=i.createRenderer(t,n),u=Zr(o,od(n),null,n.onPush?32:16,o[e.index],e,i,c,a||null,null,null);return l.firstCreatePass&&os(l,e,r.length-1),Kr(o,u),o[e.index]=u}(b,d,D,C,h,a,c);v=Tl(p,H),d&&function rI(e,t,n,r){if(r)Xo(e,n,["ng-version",Ru.full]);else{const{attrs:o,classes:i}=function Xy(e){const t=[],n=[];let r=1,o=2;for(;r<e.length;){let i=e[r];if("string"==typeof i)2===o?""!==i&&t.push(i,e[++r]):8===o&&n.push(i);else{if(!Pe(o))break;o=i}r++}return{attrs:t,classes:n}}(t.selectors[0]);o&&Xo(e,n,o),i&&i.length>0&&Kc(e,n,i.join(" "))}}(c,D,d,r),void 0!==n&&function oI(e,t,n){const r=e.projection=[];for(let o=0;o<t.length;o++){const i=n[o];r.push(null!=i?Array.from(i):null)}}(v,this.ngContentSelectors,n),y=function nI(e,t,n,r,o,i){const s=te(),a=o[I],l=ye(s,o);ld(a,o,s,n,null,r);for(let u=0;u<n.length;u++)ie(ht(o,a,s.directiveStart+u,s),o);cd(a,o,s),l&&ie(l,o);const c=ht(o,a,s.directiveStart+s.componentOffset,s);if(e[G]=o[G]=c,null!==i)for(const u of i)u(c,t);return Xi(a,s,e),c}($,D,C,m,h,[vd]),Ji(p,h,null)}finally{Yo()}return new yd(this.componentType,y,zt(v,h),h,v)}}class yd extends bu{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.instance=n,this.hostView=this.changeDetectorRef=new Yv(o),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[t])){const i=this._rootLView;ls(i[I],i,o,t,n),Jr(pe(this._tNode.index,i))}}get injector(){return new Lt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function vd(){const e=te();Mr(g()[I],e)}function Id(e){return Object.getPrototypeOf(e.prototype).constructor}function cs(e){let t=Id(e.type),n=!0;const r=[e];for(;t;){let o;if(Se(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);const s=e;s.inputs=us(e.inputs),s.declaredInputs=us(e.declaredInputs),s.outputs=us(e.outputs);const a=o.hostBindings;a&&lI(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&sI(e,l),c&&aI(e,c),No(e.inputs,o.inputs),No(e.declaredInputs,o.declaredInputs),No(e.outputs,o.outputs),Se(o)&&o.data.animation){const u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}const i=o.features;if(i)for(let s=0;s<i.length;s++){const a=i[s];a&&a.ngInherit&&a(e),a===cs&&(n=!1)}}t=Object.getPrototypeOf(t)}!function iI(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Sn(o.hostAttrs,n=Sn(n,o.hostAttrs))}}(r)}function us(e){return e===We?{}:e===A?[]:e}function sI(e,t){const n=e.viewQuery;e.viewQuery=n?(r,o)=>{t(r,o),n(r,o)}:t}function aI(e,t){const n=e.contentQueries;e.contentQueries=n?(r,o,i)=>{t(r,o,i),n(r,o,i)}:t}function lI(e,t){const n=e.hostBindings;e.hostBindings=n?(r,o)=>{t(r,o),n(r,o)}:t}const cI=["providersResolver"],uI=["template","decls","consts","vars","onPush","ngContentSelectors","styles","encapsulation","schemas"];function Dd(e){let n,t=Id(e.type);n=Se(e)?t.\u0275cmp:t.\u0275dir;const r=e;for(const o of cI)r[o]=n[o];if(Se(n))for(const o of uI)r[o]=n[o]}function _d(e){return t=>{t.findHostDirectiveDefs=Md,t.hostDirectives=(Array.isArray(e)?e:e()).map(n=>"function"==typeof n?{directive:E(n),inputs:We,outputs:We}:{directive:E(n.directive),inputs:Ed(n.inputs),outputs:Ed(n.outputs)})}}function Md(e,t,n){if(null!==e.hostDirectives)for(const r of e.hostDirectives){const o=ne(r.directive);dI(o.declaredInputs,r.inputs),Md(o,t,n),n.set(o,r),t.push(o)}}function Ed(e){if(void 0===e||0===e.length)return We;const t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function dI(e,t){for(const n in t)t.hasOwnProperty(n)&&(e[t[n]]=e[n])}function no(e){return!!ds(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function ds(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function wd(e,t){const n=no(e),r=no(t);return n&&r?function fI(e,t,n){const r=e[Symbol.iterator](),o=t[Symbol.iterator]();for(;;){const i=r.next(),s=o.next();if(i.done&&s.done)return!0;if(i.done||s.done||!n(i.value,s.value))return!1}}(e,t,wd):!(n||!e||"object"!=typeof e&&"function"!=typeof e||r||!t||"object"!=typeof t&&"function"!=typeof t)||Object.is(e,t)}function $e(e,t,n){return e[t]=n}function Qn(e,t){return e[t]}function se(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function Dt(e,t,n,r){const o=se(e,t,n);return se(e,t+1,r)||o}function ro(e,t,n,r,o){const i=Dt(e,t,n,r);return se(e,t+2,o)||i}function we(e,t,n,r,o,i){const s=Dt(e,t,n,r);return Dt(e,t+2,o,i)||s}function fs(e,t,n,r){const o=g();return se(o,At(),t)&&(R(),He(B(),o,e,t,n,r)),fs}function tn(e,t){let n=!1,r=Ze();for(let i=1;i<t.length;i+=2)n=se(e,r++,t[i])||n;if(kl(r),!n)return T;let o=t[0];for(let i=1;i<t.length;i+=2)o+=N(t[i])+t[i+1];return o}function nn(e,t,n,r){return se(e,At(),n)?t+N(n)+r:T}function rn(e,t,n,r,o,i){const a=Dt(e,Ze(),n,o);return Ye(2),a?t+N(n)+r+N(o)+i:T}function on(e,t,n,r,o,i,s,a){const c=ro(e,Ze(),n,o,s);return Ye(3),c?t+N(n)+r+N(o)+i+N(s)+a:T}function sn(e,t,n,r,o,i,s,a,l,c){const d=we(e,Ze(),n,o,s,l);return Ye(4),d?t+N(n)+r+N(o)+i+N(s)+a+N(l)+c:T}function an(e,t,n,r,o,i,s,a,l,c,u,d){const f=Ze();let p=we(e,f,n,o,s,l);return p=se(e,f+4,u)||p,Ye(5),p?t+N(n)+r+N(o)+i+N(s)+a+N(l)+c+N(u)+d:T}function ln(e,t,n,r,o,i,s,a,l,c,u,d,f,p){const h=Ze();let y=we(e,h,n,o,s,l);return y=Dt(e,h+4,u,f)||y,Ye(6),y?t+N(n)+r+N(o)+i+N(s)+a+N(l)+c+N(u)+d+N(f)+p:T}function cn(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y){const v=Ze();let D=we(e,v,n,o,s,l);return D=ro(e,v+4,u,f,h)||D,Ye(7),D?t+N(n)+r+N(o)+i+N(s)+a+N(l)+c+N(u)+d+N(f)+p+N(h)+y:T}function un(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D){const C=Ze();let m=we(e,C,n,o,s,l);return m=we(e,C+4,u,f,h,v)||m,Ye(8),m?t+N(n)+r+N(o)+i+N(s)+a+N(l)+c+N(u)+d+N(f)+p+N(h)+y+N(v)+D:T}function ps(e,t,n,r,o,i){const s=g(),a=nn(s,t,n,r);return a!==T&&He(B(),s,e,a,o,i),ps}function hs(e,t,n,r,o,i,s,a){const l=g(),c=rn(l,t,n,r,o,i);return c!==T&&He(B(),l,e,c,s,a),hs}function gs(e,t,n,r,o,i,s,a,l,c){const u=g(),d=on(u,t,n,r,o,i,s,a);return d!==T&&He(B(),u,e,d,l,c),gs}function ms(e,t,n,r,o,i,s,a,l,c,u,d){const f=g(),p=sn(f,t,n,r,o,i,s,a,l,c);return p!==T&&He(B(),f,e,p,u,d),ms}function ys(e,t,n,r,o,i,s,a,l,c,u,d,f,p){const h=g(),y=an(h,t,n,r,o,i,s,a,l,c,u,d);return y!==T&&He(B(),h,e,y,f,p),ys}function vs(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y){const v=g(),D=ln(v,t,n,r,o,i,s,a,l,c,u,d,f,p);return D!==T&&He(B(),v,e,D,h,y),vs}function Is(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D){const C=g(),m=cn(C,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y);return m!==T&&He(B(),C,e,m,v,D),Is}function Ds(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D,C,m){const b=g(),$=un(b,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D);return $!==T&&He(B(),b,e,$,C,m),Ds}function _s(e,t,n,r){const o=g(),i=tn(o,t);return i!==T&&He(B(),o,e,i,n,r),_s}function Td(e){const t=Pc(e);Xr(t[I],t,e)}function bd(e,t,n,r,o,i,s,a){const l=g(),c=R(),u=e+H,d=c.firstCreatePass?function hI(e,t,n,r,o,i,s,a,l){const c=t.consts,u=Jt(t,e,4,s||null,it(c,a));rs(t,n,u,it(c,l)),Mr(t,u);const d=u.tView=ns(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c);return null!==t.queries&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}(u,c,l,t,n,r,o,i,s):c.data[u];Fe(d,!1);const f=l[P].createComment("");jr(c,l,f,d),ie(f,l),Kr(l,l[u]=ud(f,l,f,d)),Ir(d)&&es(c,l,d),null!=s&&ts(l,d,a)}function Nd(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function xd(e){return Rt(function xg(){return x.lFrame.contextLView}(),H+e)}function Ms(e,t,n){const r=g();return se(r,At(),t)&&Ie(R(),B(),r,e,t,r[P],n,!1),Ms}function Es(e,t,n,r,o){const s=o?"class":"style";ls(e,n,t.inputs[s],s,r)}function oo(e,t,n,r){const o=g(),i=R(),s=H+e,a=o[P],l=i.firstCreatePass?function gI(e,t,n,r,o,i){const s=t.consts,l=Jt(t,e,2,r,it(s,o));return rs(t,n,l,it(s,i)),null!==l.attrs&&eo(l,l.attrs,!1),null!==l.mergedAttrs&&eo(l,l.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,l),l}(s,i,o,t,n,r):i.data[s],c=o[s]=_i(a,t,function Fg(){return x.lFrame.currentNamespace}()),u=Ir(l);return Fe(l,!0),Jc(a,c,l),32!=(32&l.flags)&&jr(i,o,c,l),0===function Tg(){return x.lFrame.elementDepthCount}()&&ie(c,o),function bg(){x.lFrame.elementDepthCount++}(),u&&(es(i,o,l),Xi(i,l,o)),null!==r&&ts(o,l),oo}function io(){let e=te();qo()?Go():(e=e.parent,Fe(e,!1));const t=e;!function Ng(){x.lFrame.elementDepthCount--}();const n=R();return n.firstCreatePass&&(Mr(n,e),Ho(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function $g(e){return 0!=(8&e.flags)}(t)&&Es(n,t,g(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function Bg(e){return 0!=(16&e.flags)}(t)&&Es(n,t,g(),t.stylesWithoutHost,!1),io}function Cs(e,t,n,r){return oo(e,t,n,r),io(),Cs}function so(e,t,n){const r=g(),o=R(),i=e+H,s=o.firstCreatePass?function mI(e,t,n,r,o){const i=t.consts,s=it(i,r),a=Jt(t,e,8,"ng-container",s);return null!==s&&eo(a,s,!0),rs(t,n,a,it(i,o)),null!==t.queries&&t.queries.elementStart(t,a),a}(i,o,r,t,n):o.data[i];Fe(s,!0);const a=r[i]=r[P].createComment("");return jr(o,r,a,s),ie(a,r),Ir(s)&&(es(o,r,s),Xi(o,s,r)),null!=n&&ts(r,s),so}function ao(){let e=te();const t=R();return qo()?Go():(e=e.parent,Fe(e,!1)),t.firstCreatePass&&(Mr(t,e),Ho(e)&&t.queries.elementEnd(e)),ao}function ws(e,t,n){return so(e,t,n),ao(),ws}function Sd(){return g()}function Ts(e){return!!e&&"function"==typeof e.then}function Pd(e){return!!e&&"function"==typeof e.subscribe}const Rd=Pd;function bs(e,t,n,r){const o=g(),i=R(),s=te();return Ad(i,o,o[P],s,e,t,r),bs}function Ns(e,t){const n=te(),r=g(),o=R();return Ad(o,r,pd(Qo(o.data),n,r),n,e,t),Ns}function Ad(e,t,n,r,o,i,s){const a=Ir(r),c=e.firstCreatePass&&fd(e),u=t[G],d=dd(t);let f=!0;if(3&r.type||s){const y=ye(r,t),v=s?s(y):y,D=d.length,C=s?b=>s(ee(b[r.index])):r.index;let m=null;if(!s&&a&&(m=function yI(e,t,n,r){const o=e.cleanup;if(null!=o)for(let i=0;i<o.length-1;i+=2){const s=o[i];if(s===n&&o[i+1]===r){const a=t[Nt],l=o[i+2];return a.length>l?a[l]:null}"string"==typeof s&&(i+=2)}return null}(e,t,o,r.index)),null!==m)(m.__ngLastListenerFn__||m).__ngNextListenerFn__=i,m.__ngLastListenerFn__=i,f=!1;else{i=Ld(r,t,u,i,!1);const b=n.listen(v,o,i);d.push(i,b),c&&c.push(o,C,D,D+1)}}else i=Ld(r,t,u,i,!1);const p=r.outputs;let h;if(f&&null!==p&&(h=p[o])){const y=h.length;if(y)for(let v=0;v<y;v+=2){const $=t[h[v]][h[v+1]].subscribe(i),Q=d.length;d.push(i,$),c&&c.push(o,r.index,Q,-(Q+1))}}}function Od(e,t,n,r){try{return Ee(6,t,n),!1!==n(r)}catch(o){return hd(e,o),!1}finally{Ee(7,t,n)}}function Ld(e,t,n,r,o){return function i(s){if(s===Function)return r;Jr(e.componentOffset>-1?pe(e.index,t):t);let l=Od(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)l=Od(t,n,c,s)&&l,c=c.__ngNextListenerFn__;return o&&!1===l&&(s.preventDefault(),s.returnValue=!1),l}}function kd(e=1){return function Og(e){return(x.lFrame.contextLView=function Lg(e,t){for(;e>0;)t=t[xt],e--;return t}(e,x.lFrame.contextLView))[G]}(e)}function vI(e,t){let n=null;const r=function Qy(e){const t=e.attrs;if(null!=t){const n=t.indexOf(5);if(!(1&n))return t[n+1]}return null}(e);for(let o=0;o<t.length;o++){const i=t[o];if("*"!==i){if(null===r?Hu(e,i,!0):Yy(r,i))return o}else n=o}return n}function Fd(e){const t=g()[oe][re];if(!t.projection){const r=t.projection=An(e?e.length:1,null),o=r.slice();let i=t.child;for(;null!==i;){const s=e?vI(i,e):0;null!==s&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i),i=i.next}}}function jd(e,t=0,n){const r=g(),o=R(),i=Jt(o,H+e,16,null,n||null);null===i.projection&&(i.projection=t),Go(),32!=(32&i.flags)&&function zm(e,t,n){Yc(t[P],0,t,n,$c(e,n,t),Gc(n.parent||t[re],n,t))}(o,r,i)}function xs(e,t,n){return lo(e,"",t,"",n),xs}function lo(e,t,n,r,o){const i=g(),s=nn(i,t,n,r);return s!==T&&Ie(R(),B(),i,e,s,i[P],o,!1),lo}function Ss(e,t,n,r,o,i,s){const a=g(),l=rn(a,t,n,r,o,i);return l!==T&&Ie(R(),B(),a,e,l,a[P],s,!1),Ss}function Ps(e,t,n,r,o,i,s,a,l){const c=g(),u=on(c,t,n,r,o,i,s,a);return u!==T&&Ie(R(),B(),c,e,u,c[P],l,!1),Ps}function Rs(e,t,n,r,o,i,s,a,l,c,u){const d=g(),f=sn(d,t,n,r,o,i,s,a,l,c);return f!==T&&Ie(R(),B(),d,e,f,d[P],u,!1),Rs}function As(e,t,n,r,o,i,s,a,l,c,u,d,f){const p=g(),h=an(p,t,n,r,o,i,s,a,l,c,u,d);return h!==T&&Ie(R(),B(),p,e,h,p[P],f,!1),As}function Os(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h){const y=g(),v=ln(y,t,n,r,o,i,s,a,l,c,u,d,f,p);return v!==T&&Ie(R(),B(),y,e,v,y[P],h,!1),Os}function Ls(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v){const D=g(),C=cn(D,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y);return C!==T&&Ie(R(),B(),D,e,C,D[P],v,!1),Ls}function ks(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D,C){const m=g(),b=un(m,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D);return b!==T&&Ie(R(),B(),m,e,b,m[P],C,!1),ks}function Fs(e,t,n){const r=g(),o=tn(r,t);return o!==T&&Ie(R(),B(),r,e,o,r[P],n,!1),Fs}function co(e,t){return e<<17|t<<2}function lt(e){return e>>17&32767}function js(e){return 2|e}function _t(e){return(131068&e)>>2}function Vs(e,t){return-131069&e|t<<2}function Hs(e){return 1|e}function Vd(e,t,n,r,o){const i=e[n+1],s=null===t;let a=r?lt(i):_t(i),l=!1;for(;0!==a&&(!1===l||s);){const u=e[a+1];CI(e[a],t)&&(l=!0,e[a+1]=r?Hs(u):js(u)),a=r?lt(u):_t(u)}l&&(e[n+1]=r?js(i):Hs(i))}function CI(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&$t(e,t)>=0}const J={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Hd(e){return e.substring(J.key,J.keyEnd)}function wI(e){return e.substring(J.value,J.valueEnd)}function $d(e,t){const n=J.textEnd;return n===t?-1:(t=J.keyEnd=function NI(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,J.key=t,n),dn(e,t,n))}function Bd(e,t){const n=J.textEnd;let r=J.key=dn(e,t,n);return n===r?-1:(r=J.keyEnd=function xI(e,t,n){let r;for(;t<n&&(45===(r=e.charCodeAt(t))||95===r||(-33&r)>=65&&(-33&r)<=90||r>=48&&r<=57);)t++;return t}(e,r,n),r=qd(e,r,n),r=J.value=dn(e,r,n),r=J.valueEnd=function SI(e,t,n){let r=-1,o=-1,i=-1,s=t,a=s;for(;s<n;){const l=e.charCodeAt(s++);if(59===l)return a;34===l||39===l?a=s=Gd(e,l,s,n):t===s-4&&85===i&&82===o&&76===r&&40===l?a=s=Gd(e,41,s,n):l>32&&(a=s),i=o,o=r,r=-33&l}return a}(e,r,n),qd(e,r,n))}function Ud(e){J.key=0,J.keyEnd=0,J.value=0,J.valueEnd=0,J.textEnd=e.length}function dn(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function qd(e,t,n,r){return(t=dn(e,t,n))<n&&t++,t}function Gd(e,t,n,r){let o=-1,i=n;for(;i<r;){const s=e.charCodeAt(i++);if(s==t&&92!==o)return i;o=92==s&&92===o?0:s}throw new Error}function $s(e,t,n){return Ae(e,t,n,!1),$s}function Bs(e,t){return Ae(e,t,null,!0),Bs}function Re(e){Oe(Zd,PI,e,!1)}function PI(e,t){for(let n=function bI(e){return Ud(e),Bd(e,dn(e,0,J.textEnd))}(t);n>=0;n=Bd(t,n))Zd(e,Hd(t),wI(t))}function Wd(e){Oe(FI,Be,e,!0)}function Be(e,t){for(let n=function TI(e){return Ud(e),$d(e,dn(e,0,J.textEnd))}(t);n>=0;n=$d(t,n))ve(e,Hd(t),!0)}function Ae(e,t,n,r){const o=g(),i=R(),s=Ye(2);i.firstUpdatePass&&zd(i,e,s,r),t!==T&&se(o,s,t)&&Yd(i,i.data[ce()],o,o[P],e,o[s+1]=function VI(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=j(Ke(e)))),e}(t,n),r,s)}function Oe(e,t,n,r){const o=R(),i=Ye(2);o.firstUpdatePass&&zd(o,null,i,r);const s=g();if(n!==T&&se(s,i,n)){const a=o.data[ce()];if(Jd(a,r)&&!Qd(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;null!==l&&(n=xo(l,n||"")),Es(o,a,s,n,r)}else!function jI(e,t,n,r,o,i,s,a){o===T&&(o=A);let l=0,c=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;null!==u||null!==d;){const f=l<o.length?o[l+1]:void 0,p=c<i.length?i[c+1]:void 0;let y,h=null;u===d?(l+=2,c+=2,f!==p&&(h=d,y=p)):null===d||null!==u&&u<d?(l+=2,h=u):(c+=2,h=d,y=p),null!==h&&Yd(e,t,n,r,h,y,s,a),u=l<o.length?o[l]:null,d=c<i.length?i[c]:null}}(o,a,s,s[P],s[i+1],s[i+1]=function kI(e,t,n){if(null==n||""===n)return A;const r=[],o=Ke(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if("object"==typeof o)for(const i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else"string"==typeof o&&t(r,o);return r}(e,t,n),r,i)}}function Qd(e,t){return t>=e.expandoStartIndex}function zd(e,t,n,r){const o=e.data;if(null===o[n+1]){const i=o[ce()],s=Qd(e,n);Jd(i,r)&&null===t&&!s&&(t=!1),t=function RI(e,t,n,r){const o=Qo(e);let i=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=zn(n=Us(null,e,t,n,r),t.attrs,r),i=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==o)if(n=Us(o,e,t,n,r),null===i){let l=function AI(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==_t(r))return e[lt(r)]}(e,t,r);void 0!==l&&Array.isArray(l)&&(l=Us(null,e,t,l[1],r),l=zn(l,t.attrs,r),function OI(e,t,n,r){e[lt(n?t.classBindings:t.styleBindings)]=r}(e,t,r,l))}else i=function LI(e,t,n){let r;const o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++)r=zn(r,e[i].hostAttrs,n);return zn(r,t.attrs,n)}(e,t,r)}return void 0!==i&&(r?t.residualClasses=i:t.residualStyles=i),n}(o,i,t,r),function MI(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=lt(s),l=_t(s);e[r]=n;let u,c=!1;if(Array.isArray(n)?(u=n[1],(null===u||$t(n,u)>0)&&(c=!0)):u=n,o)if(0!==l){const f=lt(e[a+1]);e[r+1]=co(f,a),0!==f&&(e[f+1]=Vs(e[f+1],r)),e[a+1]=function DI(e,t){return 131071&e|t<<17}(e[a+1],r)}else e[r+1]=co(a,0),0!==a&&(e[a+1]=Vs(e[a+1],r)),a=r;else e[r+1]=co(l,0),0===a?a=r:e[l+1]=Vs(e[l+1],r),l=r;c&&(e[r+1]=js(e[r+1])),Vd(e,u,r,!0),Vd(e,u,r,!1),function EI(e,t,n,r,o){const i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof t&&$t(i,t)>=0&&(n[r+1]=Hs(n[r+1]))}(t,u,e,r,i),s=co(a,l),i?t.classBindings=s:t.styleBindings=s}(o,i,t,n,s,r)}}function Us(e,t,n,r,o){let i=null;const s=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<s&&(i=t[a],r=zn(r,i.hostAttrs,o),i!==e);)a++;return null!==e&&(n.directiveStylingLast=a),r}function zn(e,t,n){const r=n?1:2;let o=-1;if(null!==t)for(let i=0;i<t.length;i++){const s=t[i];"number"==typeof s?o=s:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),ve(e,s,!!n||t[++i]))}return void 0===e?null:e}function Zd(e,t,n){ve(e,t,Ke(n))}function FI(e,t,n){const r=String(t);""!==r&&!r.includes(" ")&&ve(e,r,n)}function Yd(e,t,n,r,o,i,s,a){if(!(3&t.type))return;const l=e.data,c=l[a+1],u=function _I(e){return 1==(1&e)}(c)?Kd(l,t,n,o,_t(c),s):void 0;uo(u)||(uo(i)||function II(e){return 2==(2&e)}(c)&&(i=Kd(l,null,n,o,a,s)),function Ym(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=-1===r.indexOf("-")?void 0:Ve.DashCase;null==o?e.removeStyle(n,r,i):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Ve.Important),e.setStyle(n,r,o,i))}}(r,s,Dr(ce(),n),o,i))}function Kd(e,t,n,r,o,i){const s=null===t;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),u=c?l[1]:l,d=null===u;let f=n[o+1];f===T&&(f=d?A:void 0);let p=d?ai(f,r):u===r?f:void 0;if(c&&!uo(p)&&(p=ai(l,r)),uo(p)&&(a=p,s))return a;const h=e[o+1];o=s?lt(h):_t(h)}if(null!==t){let l=i?t.residualClasses:t.residualStyles;null!=l&&(a=ai(l,r))}return a}function uo(e){return void 0!==e}function Jd(e,t){return 0!=(e.flags&(t?8:16))}function Xd(e,t=""){const n=g(),r=R(),o=e+H,i=r.firstCreatePass?Jt(r,o,1,t,null):r.data[o],s=n[o]=Di(n[P],t);jr(r,n,s,i),Fe(i,!1)}function qs(e){return fo("",e,""),qs}function fo(e,t,n){const r=g(),o=nn(r,e,t,n);return o!==T&&tt(r,ce(),o),fo}function Gs(e,t,n,r,o){const i=g(),s=rn(i,e,t,n,r,o);return s!==T&&tt(i,ce(),s),Gs}function Ws(e,t,n,r,o,i,s){const a=g(),l=on(a,e,t,n,r,o,i,s);return l!==T&&tt(a,ce(),l),Ws}function Qs(e,t,n,r,o,i,s,a,l){const c=g(),u=sn(c,e,t,n,r,o,i,s,a,l);return u!==T&&tt(c,ce(),u),Qs}function zs(e,t,n,r,o,i,s,a,l,c,u){const d=g(),f=an(d,e,t,n,r,o,i,s,a,l,c,u);return f!==T&&tt(d,ce(),f),zs}function Zs(e,t,n,r,o,i,s,a,l,c,u,d,f){const p=g(),h=ln(p,e,t,n,r,o,i,s,a,l,c,u,d,f);return h!==T&&tt(p,ce(),h),Zs}function Ys(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h){const y=g(),v=cn(y,e,t,n,r,o,i,s,a,l,c,u,d,f,p,h);return v!==T&&tt(y,ce(),v),Ys}function Ks(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v){const D=g(),C=un(D,e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v);return C!==T&&tt(D,ce(),C),Ks}function Js(e){const t=g(),n=tn(t,e);return n!==T&&tt(t,ce(),n),Js}function ef(e,t,n){Oe(ve,Be,nn(g(),e,t,n),!0)}function tf(e,t,n,r,o){Oe(ve,Be,rn(g(),e,t,n,r,o),!0)}function nf(e,t,n,r,o,i,s){Oe(ve,Be,on(g(),e,t,n,r,o,i,s),!0)}function rf(e,t,n,r,o,i,s,a,l){Oe(ve,Be,sn(g(),e,t,n,r,o,i,s,a,l),!0)}function of(e,t,n,r,o,i,s,a,l,c,u){Oe(ve,Be,an(g(),e,t,n,r,o,i,s,a,l,c,u),!0)}function sf(e,t,n,r,o,i,s,a,l,c,u,d,f){Oe(ve,Be,ln(g(),e,t,n,r,o,i,s,a,l,c,u,d,f),!0)}function af(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h){Oe(ve,Be,cn(g(),e,t,n,r,o,i,s,a,l,c,u,d,f,p,h),!0)}function lf(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v){Oe(ve,Be,un(g(),e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v),!0)}function cf(e){Oe(ve,Be,tn(g(),e),!0)}function uf(e,t,n){Re(nn(g(),e,t,n))}function df(e,t,n,r,o){Re(rn(g(),e,t,n,r,o))}function ff(e,t,n,r,o,i,s){Re(on(g(),e,t,n,r,o,i,s))}function pf(e,t,n,r,o,i,s,a,l){Re(sn(g(),e,t,n,r,o,i,s,a,l))}function hf(e,t,n,r,o,i,s,a,l,c,u){Re(an(g(),e,t,n,r,o,i,s,a,l,c,u))}function gf(e,t,n,r,o,i,s,a,l,c,u,d,f){Re(ln(g(),e,t,n,r,o,i,s,a,l,c,u,d,f))}function mf(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h){Re(cn(g(),e,t,n,r,o,i,s,a,l,c,u,d,f,p,h))}function yf(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v){Re(un(g(),e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v))}function vf(e){Re(tn(g(),e))}function Xs(e,t,n,r,o){return Ae(e,nn(g(),t,n,r),o,!1),Xs}function ea(e,t,n,r,o,i,s){return Ae(e,rn(g(),t,n,r,o,i),s,!1),ea}function ta(e,t,n,r,o,i,s,a,l){return Ae(e,on(g(),t,n,r,o,i,s,a),l,!1),ta}function na(e,t,n,r,o,i,s,a,l,c,u){return Ae(e,sn(g(),t,n,r,o,i,s,a,l,c),u,!1),na}function ra(e,t,n,r,o,i,s,a,l,c,u,d,f){return Ae(e,an(g(),t,n,r,o,i,s,a,l,c,u,d),f,!1),ra}function oa(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h){return Ae(e,ln(g(),t,n,r,o,i,s,a,l,c,u,d,f,p),h,!1),oa}function ia(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v){return Ae(e,cn(g(),t,n,r,o,i,s,a,l,c,u,d,f,p,h,y),v,!1),ia}function sa(e,t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D,C){return Ae(e,un(g(),t,n,r,o,i,s,a,l,c,u,d,f,p,h,y,v,D),C,!1),sa}function aa(e,t,n){return Ae(e,tn(g(),t),n,!1),aa}function la(e,t,n){const r=g();return se(r,At(),t)&&Ie(R(),B(),r,e,t,r[P],n,!0),la}function ca(e,t,n){const r=g();if(se(r,At(),t)){const i=R(),s=B();Ie(i,s,r,e,t,pd(Qo(i.data),s,r),n,!0)}return ca}const Mt=void 0;var $I=["en",[["a","p"],["AM","PM"],Mt],[["AM","PM"],Mt,Mt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Mt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Mt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Mt,"{1} 'at' {0}",Mt],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function HI(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let fn={};function BI(e,t,n){"string"!=typeof t&&(n=t,t=e[M.LocaleId]),t=t.toLowerCase().replace(/_/g,"-"),fn[t]=e,n&&(fn[t][M.ExtraData]=n)}function ua(e){const t=function GI(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Df(t);if(n)return n;const r=t.split("-")[0];if(n=Df(r),n)return n;if("en"===r)return $I;throw new _(701,!1)}function UI(e){return ua(e)[M.CurrencyCode]||null}function If(e){return ua(e)[M.PluralCase]}function Df(e){return e in fn||(fn[e]=Z.ng&&Z.ng.common&&Z.ng.common.locales&&Z.ng.common.locales[e]),fn[e]}function qI(){fn={}}var M=(()=>((M=M||{})[M.LocaleId=0]="LocaleId",M[M.DayPeriodsFormat=1]="DayPeriodsFormat",M[M.DayPeriodsStandalone=2]="DayPeriodsStandalone",M[M.DaysFormat=3]="DaysFormat",M[M.DaysStandalone=4]="DaysStandalone",M[M.MonthsFormat=5]="MonthsFormat",M[M.MonthsStandalone=6]="MonthsStandalone",M[M.Eras=7]="Eras",M[M.FirstDayOfWeek=8]="FirstDayOfWeek",M[M.WeekendRange=9]="WeekendRange",M[M.DateFormat=10]="DateFormat",M[M.TimeFormat=11]="TimeFormat",M[M.DateTimeFormat=12]="DateTimeFormat",M[M.NumberSymbols=13]="NumberSymbols",M[M.NumberFormats=14]="NumberFormats",M[M.CurrencyCode=15]="CurrencyCode",M[M.CurrencySymbol=16]="CurrencySymbol",M[M.CurrencyName=17]="CurrencyName",M[M.Currencies=18]="Currencies",M[M.Directionality=19]="Directionality",M[M.PluralCase=20]="PluralCase",M[M.ExtraData=21]="ExtraData",M))();const WI=["zero","one","two","few","many"],Et="en-US",po={marker:"element"},ho={marker:"ICU"};var q=(()=>((q=q||{})[q.SHIFT=2]="SHIFT",q[q.APPEND_EAGERLY=1]="APPEND_EAGERLY",q[q.COMMENT=2]="COMMENT",q))();let _f=Et;function da(e){(function De(e,t){null==e&&L(t,e,null,"!=")})(e,"Expected localeId to be defined"),"string"==typeof e&&(_f=e.toLowerCase().replace(/_/g,"-"))}function Mf(e,t,n){const r=t.insertBeforeIndex,o=Array.isArray(r)?r[0]:r;return null===o?Wc(e,0,n):ee(n[o])}function Ef(e,t,n,r,o){const i=t.insertBeforeIndex;if(Array.isArray(i)){let s=r,a=null;if(3&t.type||(a=s,s=o),null!==s&&-1===t.componentOffset)for(let l=1;l<i.length;l++)mt(e,s,n[i[l]],a,!1)}}function Cf(e,t){if(e.push(t),e.length>1)for(let n=e.length-2;n>=0;n--){const r=e[n];wf(r)||YI(r,t)&&null===KI(r)&&JI(r,t.index)}}function wf(e){return!(64&e.type)}function YI(e,t){return wf(t)||e.index>t.index}function KI(e){const t=e.insertBeforeIndex;return Array.isArray(t)?t[0]:t}function JI(e,t){const n=e.insertBeforeIndex;Array.isArray(n)?n[0]=t:(zc(Mf,Ef),e.insertBeforeIndex=t)}function Zn(e,t){const n=e.data[t];return null===n||"string"==typeof n?null:n.hasOwnProperty("currentCaseLViewIndex")?n:n.value}function tD(e,t,n){const r=Ki(e,n,64,null,null);return Cf(t,r),r}function go(e,t){const n=t[e.currentCaseLViewIndex];return null===n?n:n<0?~n:n}function Tf(e){return e>>>17}function bf(e){return(131070&e)>>>1}let Yn=0,Kn=0;function xf(e,t,n,r){const o=n[P];let s,i=null;for(let a=0;a<t.length;a++){const l=t[a];if("string"==typeof l){const c=t[++a];null===n[c]&&(n[c]=Di(o,l))}else if("number"==typeof l)switch(1&l){case 0:const c=Tf(l);let u,d;if(null===i&&(i=c,s=Fr(o,r)),c===i?(u=r,d=s):(u=null,d=ee(n[c])),null!==d){const y=bf(l);mt(o,d,n[y],u,!1);const D=Zn(e,y);if(null!==D&&"object"==typeof D){const C=go(D,n);null!==C&&xf(e,D.create[C],n,n[D.anchorIdx])}}break;case 1:const p=t[++a],h=t[++a];is(o,Dr(l>>>1,n),null,null,p,h,null)}else switch(l){case ho:const c=t[++a],u=t[++a];null===n[u]&&ie(n[u]=Fm(o,c),n);break;case po:const d=t[++a],f=t[++a];null===n[f]&&ie(n[f]=_i(o,d,null),n)}}}function Sf(e,t,n,r,o){for(let i=0;i<n.length;i++){const s=n[i],a=n[++i];if(s&o){let l="";for(let c=i+1;c<=i+a;c++){const u=n[c];if("string"==typeof u)l+=u;else if("number"==typeof u)if(u<0)l+=N(t[r-u]);else{const d=u>>>2;switch(3&u){case 1:const f=n[++c],p=n[++c],h=e.data[d];"string"==typeof h?is(t[P],t[d],null,h,f,l,p):Ie(e,h,t,f,l,t[P],p,!1);break;case 0:const y=t[d];null!==y&&jc(t[P],y,l);break;case 2:sD(e,Zn(e,d),t,l);break;case 3:Pf(e,Zn(e,d),r,t)}}}}else{const l=n[i+1];if(l>0&&3==(3&l)){const u=Zn(e,l>>>2);t[u.currentCaseLViewIndex]<0&&Pf(e,u,r,t)}}i+=a}}function Pf(e,t,n,r){let o=r[t.currentCaseLViewIndex];if(null!==o){let i=Yn;o<0&&(o=r[t.currentCaseLViewIndex]=~o,i=-1),Sf(e,r,t.update[o],n,i)}}function sD(e,t,n,r){const o=function aD(e,t){let n=e.cases.indexOf(t);if(-1===n)switch(e.type){case 1:{const r=function QI(e,t){const n=If(t)(parseInt(e,10)),r=WI[n];return void 0!==r?r:"other"}(t,function ZI(){return _f}());n=e.cases.indexOf(r),-1===n&&"other"!==r&&(n=e.cases.indexOf("other"));break}case 0:n=e.cases.indexOf("other")}return-1===n?null:n}(t,r);if(go(t,n)!==o&&(Rf(e,t,n),n[t.currentCaseLViewIndex]=null===o?null:~o,null!==o)){const s=n[t.anchorIdx];s&&xf(e,t.create[o],n,s)}}function Rf(e,t,n){let r=go(t,n);if(null!==r){const o=t.remove[r];for(let i=0;i<o.length;i++){const s=o[i];if(s>0){const a=Dr(s,n);null!==a&&Ti(n[P],a)}else Rf(e,Zn(e,~s),n)}}}function lD(){const e=[];let n,r,t=-1;function i(a,l){t=0;const c=go(a,l);r=null!==c?a.remove[c]:A}function s(){if(t<r.length){const a=r[t++];return a>0?n[a]:(e.push(t,r),i(n[I].data[~a],n),s())}return 0===e.length?null:(r=e.pop(),t=e.pop(),s())}return function o(a,l){for(n=l;e.length;)e.pop();return i(a.value,l),s}}const mo=/\ufffd(\d+):?\d*\ufffd/gi,cD=/({\s*\ufffd\d+:?\d*\ufffd\s*,\s*\S{6}\s*,[\s\S]*})/gi,uD=/\ufffd(\d+)\ufffd/,Of=/^\s*(\ufffd\d+:?\d*\ufffd)\s*,\s*(select|plural)\s*,/,Jn="\ufffd",dD=/\ufffd\/?\*(\d+:\d+)\ufffd/gi,fD=/\ufffd(\/?[#*]\d+):?\d*\ufffd/gi,pD=/\uE500/g;function Lf(e,t,n,r,o,i,s){const a=Wn(e,r,1,null);let l=a<<q.SHIFT,c=Nn();t===c&&(c=null),null===c&&(l|=q.APPEND_EAGERLY),s&&(l|=q.COMMENT,function Om(e){void 0===mi&&(mi=e())}(lD)),o.push(l,null===i?"":i);const u=Ki(e,a,s?32:1,null===i?"":i,null);Cf(n,u);const d=u.index;return Fe(u,!1),null!==c&&t!==c&&function eD(e,t){let n=e.insertBeforeIndex;null===n?(zc(Mf,Ef),n=e.insertBeforeIndex=[null,t]):(function rt(e,t,n){e!=t&&L(n,e,t,"==")}(Array.isArray(n),!0,"Expecting array here"),n.push(t))}(c,d),u}function mD(e,t,n,r,o,i,s){const a=s.match(mo),l=Lf(e,t,n,i,r,a?null:s,!1);a&&Xn(o,s,l.index,null,0,null)}function Xn(e,t,n,r,o,i){const s=e.length,a=s+1;e.push(null,null);const l=s+2,c=t.split(mo);let u=0;for(let d=0;d<c.length;d++){const f=c[d];if(1&d){const p=o+parseInt(f,10);e.push(-1-p),u|=kf(p)}else""!==f&&e.push(f)}return e.push(n<<2|(r?1:0)),r&&e.push(r,i),e[s]=u,e[a]=e.length-l,u}function vD(e){let t=0;for(let n=0;n<e.length;n++){const r=e[n];"number"==typeof r&&r<0&&t++}return t}function kf(e){return 1<<Math.min(e,31)}function Ff(e){let t,i,n="",r=0,o=!1;for(;null!==(t=dD.exec(e));)o?t[0]===`${Jn}/*${i}${Jn}`&&(r=t.index,o=!1):(n+=e.substring(r,t.index+t[0].length),i=t[1],o=!0);return n+=e.slice(r),n}function jf(e,t,n,r,o,i){let s=0;const a={type:o.type,currentCaseLViewIndex:Wn(e,t,1,null),anchorIdx:i,cases:[],create:[],remove:[],update:[]};(function CD(e,t,n){e.push(kf(t.mainBinding),2,-1-t.mainBinding,n<<2|2)})(n,o,i),function XI(e,t,n){const r=e.data[t];null===r?e.data[t]=n:r.value=n}(e,i,a);const l=o.values;for(let c=0;c<l.length;c++){const u=l[c],d=[];for(let f=0;f<u.length;f++){const p=u[f];if("string"!=typeof p){const h=d.push(p)-1;u[f]=`\x3c!--\ufffd${h}\ufffd--\x3e`}}s=MD(e,a,t,n,r,o.cases[c],u.join(""),d)|s}s&&function wD(e,t,n){e.push(t,1,n<<2|3)}(n,s,i)}function _D(e){const t=[],n=[];let r=1,o=0;const i=fa(e=e.replace(Of,function(s,a,l){return r="select"===l?0:1,o=parseInt(a.slice(1),10),""}));for(let s=0;s<i.length;){let a=i[s++].trim();1===r&&(a=a.replace(/\s*(?:=)?(\w+)\s*/,"$1")),a.length&&t.push(a);const l=fa(i[s++]);t.length>n.length&&n.push(l)}return{type:r,mainBinding:o,cases:t,values:n}}function fa(e){if(!e)return[];let t=0;const n=[],r=[],o=/[{}]/g;let i;for(o.lastIndex=0;i=o.exec(e);){const a=i.index;if("}"==i[0]){if(n.pop(),0==n.length){const l=e.substring(t,a);Of.test(l)?r.push(_D(l)):r.push(l),t=a+1}}else{if(0==n.length){const l=e.substring(t,a);r.push(l),t=a+1}n.push("{")}}const s=e.substring(t);return r.push(s),r}function MD(e,t,n,r,o,i,s,a){const l=[],c=[],u=[];t.cases.push(i),t.create.push(l),t.remove.push(c),t.update.push(u);const f=iu(eu()).getInertBodyElement(s),p=Ai(f)||f;return p?Vf(e,t,n,r,l,c,u,p,o,a,0):0}function Vf(e,t,n,r,o,i,s,a,l,c,u){let d=0,f=a.firstChild;for(;f;){const p=Wn(e,n,1,null);switch(f.nodeType){case Node.ELEMENT_NODE:const h=f,y=h.tagName.toLowerCase();if(Pi.hasOwnProperty(y)){pa(o,po,y,l,p),e.data[p]=y;const m=h.attributes;for(let b=0;b<m.length;b++){const $=m.item(b),Q=$.name.toLowerCase();$.value.match(mo)?cu.hasOwnProperty(Q)&&Xn(s,$.value,p,$.name,0,Ri[Q]?Br:null):TD(o,p,$)}d=Vf(e,t,n,r,o,i,s,f,p,c,u+1)|d,Hf(i,p,u)}break;case Node.TEXT_NODE:const v=f.textContent||"",D=v.match(mo);pa(o,null,D?"":v,l,p),Hf(i,p,u),D&&(d=Xn(s,v,p,null,0,null)|d);break;case Node.COMMENT_NODE:const C=uD.exec(f.textContent||"");if(C){const b=c[parseInt(C[1],10)];pa(o,ho,"",l,p),jf(e,n,r,l,b,p),ED(i,p,u)}}f=f.nextSibling}return d}function Hf(e,t,n){0===n&&e.push(t)}function ED(e,t,n){0===n&&(e.push(~t),e.push(t))}function pa(e,t,n,r,o){null!==t&&e.push(t),e.push(n,o,function nD(e,t,n){return e|t<<17|n<<1}(0,r,o))}function TD(e,t,n){e.push(t<<1|1,n.name,n.value)}const $f=0,bD=/\[(\ufffd.+?\ufffd?)\]/,ND=/\[(\ufffd.+?\ufffd?)\]|(\ufffd\/?\*\d+:\d+\ufffd)/g,xD=/({\s*)(VAR_(PLURAL|SELECT)(_\d+)?)(\s*,)/g,SD=/{([A-Z0-9_]+)}/g,PD=/\ufffdI18N_EXP_(ICU(_\d+)?)\ufffd/g,RD=/\/\*/,AD=/\d+\:(\d+)/;function ha(e,t,n=-1){const r=R(),o=g(),i=H+e,s=it(r.consts,t),a=Nn();r.firstCreatePass&&function gD(e,t,n,r,o,i){const s=Nn(),a=[],l=[],c=[[]];o=function DD(e,t){if(function ID(e){return-1===e}(t))return Ff(e);{const n=e.indexOf(`:${t}${Jn}`)+2+t.toString().length,r=e.search(new RegExp(`${Jn}\\/\\*\\d+:${t}${Jn}`));return Ff(e.substring(n,r))}}(o,i);const u=function hD(e){return e.replace(pD," ")}(o).split(fD);for(let d=0;d<u.length;d++){let f=u[d];if(1&d){const p=47===f.charCodeAt(0),y=(f.charCodeAt(p?1:0),H+Number.parseInt(f.substring(p?2:1)));if(p)c.shift(),Fe(Nn(),!1);else{const v=tD(e,c[0],y);c.unshift([]),Fe(v,!0)}}else{const p=fa(f);for(let h=0;h<p.length;h++){let y=p[h];if(1&h){const v=y;if("object"!=typeof v)throw new Error(`Unable to parse ICU expression in "${o}" message.`);jf(e,n,l,t,v,Lf(e,s,c[0],n,a,"",!0).index)}else""!==y&&mD(e,s,c[0],a,l,n,y)}}}e.data[r]={create:a,update:l}}(r,null===a?0:a.index,o,i,s,n);const l=r.data[i],u=Bc(r,a===o[re]?null:a,o);(function iD(e,t,n,r){const o=e[P];for(let i=0;i<t.length;i++){const s=t[i++],a=t[i],c=(s&q.APPEND_EAGERLY)===q.APPEND_EAGERLY,u=s>>>q.SHIFT;let d=e[u];null===d&&(d=e[u]=(s&q.COMMENT)===q.COMMENT?o.createComment(a):Di(o,a)),c&&null!==n&&mt(o,n,d,r,!1)}})(o,l.create,u,a&&8&a.type?o[a.index]:null),Fl(!0)}function ga(){Fl(!1)}function Bf(e,t,n){ha(e,t,n),ga()}function Uf(e,t){const n=R(),r=it(n.consts,t);!function yD(e,t,n){const o=te().index,i=[];if(e.firstCreatePass&&null===e.data[t]){for(let s=0;s<n.length;s+=2){const a=n[s],l=n[s+1];if(""!==l){if(cD.test(l))throw new Error(`ICU expressions are not supported in attributes. Message: "${l}".`);Xn(i,l,o,a,vD(i),null)}}e.data[t]=i}}(n,e+H,r)}function ma(e){return function rD(e){e&&(Yn|=1<<Math.min(Kn,31)),Kn++}(se(g(),At(),e)),ma}function qf(e){!function oD(e,t,n){if(Kn>0){const r=e.data[n];Sf(e,t,Array.isArray(r)?r:r.update,Ze()-Kn-1,Yn)}Yn=0,Kn=0}(R(),g(),e+H)}function Gf(e,t={}){return function OD(e,t={}){let n=e;if(bD.test(e)){const r={},o=[$f];n=n.replace(ND,(i,s,a)=>{const l=s||a,c=r[l]||[];if(c.length||(l.split("|").forEach(y=>{const v=y.match(AD),D=v?parseInt(v[1],10):$f,C=RD.test(y);c.push([D,C,y])}),r[l]=c),!c.length)throw new Error(`i18n postprocess: unmatched placeholder - ${l}`);const u=o[o.length-1];let d=0;for(let y=0;y<c.length;y++)if(c[y][0]===u){d=y;break}const[f,p,h]=c[d];return p?o.pop():u!==f&&o.push(f),c.splice(d,1),h})}return Object.keys(t).length&&(n=n.replace(xD,(r,o,i,s,a,l)=>t.hasOwnProperty(i)?`${o}${t[i]}${l}`:r),n=n.replace(SD,(r,o)=>t.hasOwnProperty(o)?t[o]:r),n=n.replace(PD,(r,o)=>{if(t.hasOwnProperty(o)){const i=t[o];if(!i.length)throw new Error(`i18n postprocess: unmatched ICU - ${r} with key: ${o}`);return i.shift()}return r})),n}(e,t)}function ya(e,t,n,r,o){if(e=E(e),Array.isArray(e))for(let i=0;i<e.length;i++)ya(e[i],t,n,r,o);else{const i=R(),s=g();let a=vt(e)?e:E(e.provide),l=Tu(e);const c=te(),u=1048575&c.providerIndexes,d=c.directiveStart,f=c.providerIndexes>>20;if(vt(e)||!e.multi){const p=new xn(l,o,Kt),h=Ia(a,t,o?u:u+f,d);-1===h?(ri(Nr(c,s),i,a),va(i,e,t.length),t.push(a),c.directiveStart++,c.directiveEnd++,o&&(c.providerIndexes+=1048576),n.push(p),s.push(p)):(n[h]=p,s[h]=p)}else{const p=Ia(a,t,u+f,d),h=Ia(a,t,u,u+f),v=h>=0&&n[h];if(o&&!v||!o&&!(p>=0&&n[p])){ri(Nr(c,s),i,a);const D=function jD(e,t,n,r,o){const i=new xn(e,n,Kt);return i.multi=[],i.index=t,i.componentProviders=0,Wf(i,o,r&&!n),i}(o?FD:kD,n.length,o,r,l);!o&&v&&(n[h].providerFactory=D),va(i,e,t.length,0),t.push(a),c.directiveStart++,c.directiveEnd++,o&&(c.providerIndexes+=1048576),n.push(D),s.push(D)}else va(i,e,p>-1?p:h,Wf(n[o?h:p],l,!o&&r));!o&&r&&v&&n[h].componentProviders++}}}function va(e,t,n,r){const o=vt(t),i=function Ey(e){return!!e.useClass}(t);if(o||i){const l=(i?E(t.useClass):t).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const u=c.indexOf(n);-1===u?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function Wf(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Ia(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function kD(e,t,n,r){return Da(this.multi,[])}function FD(e,t,n,r){const o=this.multi;let i;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=ht(n,n[I],this.providerFactory.index,r);i=a.slice(0,s),Da(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Da(o,i);return i}function Da(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function Qf(e,t=[]){return n=>{n.providersResolver=(r,o)=>function LD(e,t,n){const r=R();if(r.firstCreatePass){const o=Se(e);ya(n,r.data,r.blueprint,o,!0),ya(t,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,t)}}class pn{}class zf{}function Zf(e,t){return new _a(e,t??null)}const VD=Zf;class _a extends pn{constructor(t,n){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new gd(this);const r=ge(t);this._bootstrapComponents=Xe(r.bootstrap),this._r3Injector=Qu(t,n,[{provide:pn,useValue:this},{provide:Bn,useValue:this.componentFactoryResolver}],j(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class yo extends zf{constructor(t){super(),this.moduleType=t}create(t){return new _a(this.moduleType,t)}}class HD extends pn{constructor(t,n,r){super(),this.componentFactoryResolver=new gd(this),this.instance=null;const o=new wu([...t,{provide:pn,useValue:this},{provide:Bn,useValue:this.componentFactoryResolver}],n||Gr(),r,new Set(["environment"]));this.injector=o,o.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function Ma(e,t,n=null){return new HD(e,t,n).injector}let $D=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n.id)){const r=_u(0,n.type),o=r.length>0?Ma([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n.id,o)}return this.cachedInjectors.get(n.id)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}}return e.\u0275prov=X({token:e,providedIn:"environment",factory:()=>new e(z(It))}),e})();function Yf(e){e.getStandaloneInjector=t=>t.get($D).getOrCreateStandaloneInjector(e)}function Ea(e){const t=ue(e);if(null===t)return null;if(void 0===t.component){const n=t.lView;if(null===n)return null;t.component=function Rm(e,t){const n=t[I].data[e],{directiveStart:r,componentOffset:o}=n;return o>-1?t[r+o]:null}(t.nodeIndex,n)}return t.component}function Kf(e){!function QD(e){if(typeof Element<"u"&&!(e instanceof Element))throw new Error("Expecting instance of DOM Element")}(e);const t=ue(e),n=t?t.lView:null;return null===n?null:n[G]}function Jf(e){const t=ue(e);let r,n=t?t.lView:null;if(null===n)return null;for(;2===n[I].type&&(r=jn(n));)n=r;return 256&n[S]?null:n[G]}function Xf(e){const t=Rc(e);return null!==t?[km(t)]:[]}function ep(e){const t=ue(e),n=t?t.lView:null;return null===n?at.NULL:new Lt(n[I].data[t.nodeIndex],n)}function tp(e){if(e instanceof Text)return[];const t=ue(e),n=t?t.lView:null;if(null===n)return[];const o=t.nodeIndex;return n[I]?.data[o]?(void 0===t.directives&&(t.directives=kc(o,n)),null===t.directives?[]:[...t.directives]):[]}function UD(e){const{constructor:t}=e;if(!t)throw new Error("Unable to find the instance constructor");const n=k(t);if(n)return{inputs:n.inputs,outputs:n.outputs,encapsulation:n.encapsulation,changeDetection:n.onPush?Me.OnPush:Me.Default};const r=ne(t);return r?{inputs:r.inputs,outputs:r.outputs}:null}function Ca(e){return ue(e).native}function np(e){const t=ue(e),n=null===t?null:t.lView;if(null===n)return[];const o=n[Nt],i=n[I].cleanup,s=[];if(i&&o)for(let a=0;a<i.length;){const l=i[a++],c=i[a++];if("string"==typeof l){const u=l,d=ee(n[c]),f=o[i[a++]],p=i[a++];e==d&&s.push({element:e,name:u,callback:f,useCapture:"boolean"==typeof p&&p,type:"boolean"==typeof p||p>=0?"dom":"output"})}}return s.sort(GD),s}function GD(e,t){return e.name==t.name?0:e.name<t.name?-1:1}function WD(e){return void 0!==e.type&&void 0!==e.declaredInputs&&void 0!==e.findHostDirectiveDefs}function rp(e,t,n,r){return Le(()=>{const o=e;null!==t&&(o.hasOwnProperty("decorators")&&void 0!==o.decorators?o.decorators.push(...t):o.decorators=t),null!==n&&(o.ctorParameters=n),null!==r&&(o.propDecorators=o.hasOwnProperty("propDecorators")&&void 0!==o.propDecorators?{...o.propDecorators,...r}:r)})}function op(e,t,n){const r=le()+e,o=g();return o[r]===T?$e(o,r,n?t.call(n):t()):Qn(o,r)}function ip(e,t,n,r){return hp(g(),le(),e,t,n,r)}function sp(e,t,n,r,o){return gp(g(),le(),e,t,n,r,o)}function ap(e,t,n,r,o,i){return mp(g(),le(),e,t,n,r,o,i)}function lp(e,t,n,r,o,i,s){return yp(g(),le(),e,t,n,r,o,i,s)}function cp(e,t,n,r,o,i,s,a){const l=le()+e,c=g(),u=we(c,l,n,r,o,i);return se(c,l+4,s)||u?$e(c,l+5,a?t.call(a,n,r,o,i,s):t(n,r,o,i,s)):Qn(c,l+5)}function up(e,t,n,r,o,i,s,a,l){const c=le()+e,u=g(),d=we(u,c,n,r,o,i);return Dt(u,c+4,s,a)||d?$e(u,c+6,l?t.call(l,n,r,o,i,s,a):t(n,r,o,i,s,a)):Qn(u,c+6)}function dp(e,t,n,r,o,i,s,a,l,c){const u=le()+e,d=g();let f=we(d,u,n,r,o,i);return ro(d,u+4,s,a,l)||f?$e(d,u+7,c?t.call(c,n,r,o,i,s,a,l):t(n,r,o,i,s,a,l)):Qn(d,u+7)}function fp(e,t,n,r,o,i,s,a,l,c,u){const d=le()+e,f=g(),p=we(f,d,n,r,o,i);return we(f,d+4,s,a,l,c)||p?$e(f,d+8,u?t.call(u,n,r,o,i,s,a,l,c):t(n,r,o,i,s,a,l,c)):Qn(f,d+8)}function pp(e,t,n,r){return vp(g(),le(),e,t,n,r)}function er(e,t){const n=e[t];return n===T?void 0:n}function hp(e,t,n,r,o,i){const s=t+n;return se(e,s,o)?$e(e,s+1,i?r.call(i,o):r(o)):er(e,s+1)}function gp(e,t,n,r,o,i,s){const a=t+n;return Dt(e,a,o,i)?$e(e,a+2,s?r.call(s,o,i):r(o,i)):er(e,a+2)}function mp(e,t,n,r,o,i,s,a){const l=t+n;return ro(e,l,o,i,s)?$e(e,l+3,a?r.call(a,o,i,s):r(o,i,s)):er(e,l+3)}function yp(e,t,n,r,o,i,s,a,l){const c=t+n;return we(e,c,o,i,s,a)?$e(e,c+4,l?r.call(l,o,i,s,a):r(o,i,s,a)):er(e,c+4)}function vp(e,t,n,r,o,i){let s=t+n,a=!1;for(let l=0;l<o.length;l++)se(e,s++,o[l])&&(a=!0);return a?$e(e,s,r.apply(i,o)):er(e,s)}function Ip(e,t){const n=R();let r;const o=e+H;n.firstCreatePass?(r=function zD(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??(n.destroyHooks=[])).push(o,r.onDestroy)):r=n.data[o];const i=r.factory||(r.factory=ft(r.type)),s=_e(Kt);try{const a=br(!1),l=i();return br(a),Nd(n,g(),o,l),l}finally{_e(s)}}function Dp(e,t,n){const r=e+H,o=g(),i=Rt(o,r);return tr(o,r)?hp(o,le(),t,i.transform,n,i):i.transform(n)}function _p(e,t,n,r){const o=e+H,i=g(),s=Rt(i,o);return tr(i,o)?gp(i,le(),t,s.transform,n,r,s):s.transform(n,r)}function Mp(e,t,n,r,o){const i=e+H,s=g(),a=Rt(s,i);return tr(s,i)?mp(s,le(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function Ep(e,t,n,r,o,i){const s=e+H,a=g(),l=Rt(a,s);return tr(a,s)?yp(a,le(),t,l.transform,n,r,o,i,l):l.transform(n,r,o,i)}function Cp(e,t,n){const r=e+H,o=g(),i=Rt(o,r);return tr(o,r)?vp(o,le(),t,i.transform,n,i):i.transform.apply(i,n)}function tr(e,t){return e[I].data[t].pure}function wa(e){return t=>{setTimeout(e,void 0,t)}}const Ue=class ZD extends mn.Subject{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&"object"==typeof t){const l=t;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=wa(i),o&&(o=wa(o)),s&&(s=wa(s)));const a=super.subscribe({next:o,error:i,complete:s});return t instanceof mn.Subscription&&t.add(a),a}};function YD(){return this._results[Symbol.iterator]()}class vo{get changes(){return this._changes||(this._changes=new Ue)}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=vo.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=YD)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const o=Ce(t);(this._changesDetected=!function nm(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}(r._results,o,n))&&(r._results=o,r.length=o.length,r.last=o[this.length-1],r.first=o[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}let nr=(()=>{class e{}return e.__NG_ELEMENT_ID__=XD,e})();const KD=nr,JD=class extends KD{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}createEmbeddedView(t,n){const r=this._declarationTContainer.tView,o=Zr(this._declarationLView,r,t,16,null,r.declTNode,null,null,null,null,n||null);o[wn]=this._declarationLView[this._declarationTContainer.index];const s=this._declarationLView[ke];return null!==s&&(o[ke]=s.createEmbeddedView(r)),Ji(r,o,t),new Xt(o)}};function XD(){return Io(te(),g())}function Io(e,t){return 4&e.type?new JD(t,e,zt(e,t)):null}let Do=(()=>{class e{}return e.__NG_ELEMENT_ID__=e_,e})();function e_(){return bp(te(),g())}const t_=Do,wp=class extends t_{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return zt(this._hostTNode,this._hostLView)}get injector(){return new Lt(this._hostTNode,this._hostLView)}get parentInjector(){const t=ni(this._hostTNode,this._hostLView);if(Kl(t)){const n=Tr(t,this._hostLView),r=wr(t);return new Lt(n[I].data[r+8],n)}return new Lt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=Tp(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-ae}createEmbeddedView(t,n,r){let o,i;"number"==typeof r?o=r:null!=r&&(o=r.index,i=r.injector);const s=t.createEmbeddedView(n||{},i);return this.insert(s,o),s}createComponent(t,n,r,o,i){const s=t&&!Rn(t);let a;if(s)a=n;else{const d=n||{};a=d.index,r=d.injector,o=d.projectableNodes,i=d.environmentInjector||d.ngModuleRef}const l=s?t:new en(k(t)),c=r||this.parentInjector;if(!i&&null==l.ngModule){const f=(s?c:this.parentInjector).get(It,null);f&&(i=f)}const u=l.create(c,o,void 0,i);return this.insert(u.hostView,a),u}insert(t,n){const r=t._lView,o=r[I];if(function wg(e){return xe(e[U])}(r)){const u=this.indexOf(t);if(-1!==u)this.detach(u);else{const d=r[U],f=new wp(d,d[re],d[U]);f.detach(f.indexOf(t))}}const i=this._adjustIndex(n),s=this._lContainer;!function Bm(e,t,n,r){const o=ae+r,i=n.length;r>0&&(n[o-1][Ne]=t),r<i-ae?(t[Ne]=n[o],dc(n,ae+r,t)):(n.push(t),t[Ne]=null),t[U]=n;const s=t[wn];null!==s&&n!==s&&function Um(e,t){const n=e[Pt];t[oe]!==t[U][U][oe]&&(e[yl]=!0),null===n?e[Pt]=[t]:n.push(t)}(s,t);const a=t[ke];null!==a&&a.insertView(e),t[S]|=64}(o,r,s,i);const a=wi(i,s),l=r[P],c=Fr(l,s[yr]);return null!==c&&function Vm(e,t,n,r,o,i){r[ze]=o,r[re]=t,Vn(e,r,n,1,o,i)}(o,s[re],l,r,c,a),t.attachToViewContainerRef(),dc(Ta(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=Tp(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=Mi(this._lContainer,n);r&&(Pr(Ta(this._lContainer),n),Hc(r[I],r))}detach(t){const n=this._adjustIndex(t,-1),r=Mi(this._lContainer,n);return r&&null!=Pr(Ta(this._lContainer),n)?new Xt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Tp(e){return e[vr]}function Ta(e){return e[vr]||(e[vr]=[])}function bp(e,t){let n;const r=t[e.index];if(xe(r))n=r;else{let o;if(8&e.type)o=ee(r);else{const i=t[P];o=i.createComment("");const s=ye(e,t);mt(i,Fr(i,s),o,function Qm(e,t){return e.nextSibling(t)}(i,s),!1)}t[e.index]=n=ud(r,t,o,e),Kr(t,n)}return new wp(n,e,t)}class ba{constructor(t){this.queryList=t,this.matches=null}clone(){return new ba(this.queryList)}setDirty(){this.queryList.setDirty()}}class Na{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){const s=n.getByIndex(i);o.push(this.queries[s.indexInDeclarationView].clone())}return new Na(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==kp(t,n).matches&&this.queries[n].setDirty()}}class Np{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class xa{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const o=null!==n?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,null!==n?n.push(i):n=[i])}return null!==n?new xa(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Sa{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Sa(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){const i=r[o];this.matchTNodeWithReadOption(t,n,n_(n,i)),this.matchTNodeWithReadOption(t,n,xr(n,t,i,!1,!1))}else r===nr?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const o=this.metadata.read;if(null!==o)if(o===Un||o===Do||o===nr&&4&n.type)this.addMatch(n.index,-2);else{const i=xr(n,t,o,!1,!1);null!==i&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function n_(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function o_(e,t,n,r){return-1===n?function r_(e,t){return 11&e.type?zt(e,t):4&e.type?Io(e,t):null}(t,e):-2===n?function i_(e,t,n){return n===Un?zt(t,e):n===nr?Io(t,e):n===Do?bp(t,e):void 0}(e,t,r):ht(e,e[I],n,t)}function xp(e,t,n,r){const o=t[ke].queries[r];if(null===o.matches){const i=e.data,s=n.matches,a=[];for(let l=0;l<s.length;l+=2){const c=s[l];a.push(c<0?null:o_(t,i[c],s[l+1],n.metadata.read))}o.matches=a}return o.matches}function Pa(e,t,n,r){const o=e.queries.getByIndex(n),i=o.matches;if(null!==i){const s=xp(e,t,o,n);for(let a=0;a<i.length;a+=2){const l=i[a];if(l>0)r.push(s[a/2]);else{const c=i[a+1],u=t[-l];for(let d=ae;d<u.length;d++){const f=u[d];f[wn]===f[U]&&Pa(f[I],f,c,r)}if(null!==u[Pt]){const d=u[Pt];for(let f=0;f<d.length;f++){const p=d[f];Pa(p[I],p,c,r)}}}}}return r}function Sp(e){const t=g(),n=R(),r=jl();zo(r+1);const o=kp(n,r);if(e.dirty&&function Cg(e){return 4==(4&e[S])}(t)===(2==(2&o.metadata.flags))){if(null===o.matches)e.reset([]);else{const i=o.crossesNgTemplate?Pa(n,t,r,[]):xp(n,t,o,r);e.reset(i,Ay),e.notifyOnChanges()}return!0}return!1}function Pp(e,t,n){const r=R();r.firstCreatePass&&(Lp(r,new Np(e,t,n),-1),2==(2&t)&&(r.staticViewQueries=!0)),Op(r,g(),t)}function Rp(e,t,n,r){const o=R();if(o.firstCreatePass){const i=te();Lp(o,new Np(t,n,r),i.index),function a_(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(o,e),2==(2&n)&&(o.staticContentQueries=!0)}Op(o,g(),n)}function Ap(){return function s_(e,t){return e[ke].queries[t].queryList}(g(),jl())}function Op(e,t,n){const r=new vo(4==(4&n));id(e,t,r,r.destroy),null===t[ke]&&(t[ke]=new Na),t[ke].queries.push(new ba(r))}function Lp(e,t,n){null===e.queries&&(e.queries=new xa),e.queries.track(new Sa(t,n))}function kp(e,t){return e.queries.getByIndex(t)}function Fp(e,t){return Io(e,t)}const de=(()=>({\u0275\u0275attribute:fs,\u0275\u0275attributeInterpolate1:ps,\u0275\u0275attributeInterpolate2:hs,\u0275\u0275attributeInterpolate3:gs,\u0275\u0275attributeInterpolate4:ms,\u0275\u0275attributeInterpolate5:ys,\u0275\u0275attributeInterpolate6:vs,\u0275\u0275attributeInterpolate7:Is,\u0275\u0275attributeInterpolate8:Ds,\u0275\u0275attributeInterpolateV:_s,\u0275\u0275defineComponent:al,\u0275\u0275defineDirective:dl,\u0275\u0275defineInjectable:X,\u0275\u0275defineInjector:cr,\u0275\u0275defineNgModule:Fo,\u0275\u0275definePipe:fl,\u0275\u0275directiveInject:Kt,\u0275\u0275getInheritedFactory:ac,\u0275\u0275inject:z,\u0275\u0275injectAttribute:ii,\u0275\u0275invalidFactory:td,\u0275\u0275invalidFactoryDep:Oo,\u0275\u0275templateRefExtractor:Fp,\u0275\u0275resetView:Al,\u0275\u0275HostDirectivesFeature:_d,\u0275\u0275NgOnChangesFeature:$o,\u0275\u0275ProvidersFeature:Qf,\u0275\u0275CopyDefinitionFeature:Dd,\u0275\u0275InheritDefinitionFeature:cs,\u0275\u0275StandaloneFeature:Yf,\u0275\u0275nextContext:kd,\u0275\u0275namespaceHTML:Wl,\u0275\u0275namespaceMathML:Gl,\u0275\u0275namespaceSVG:ql,\u0275\u0275enableBindings:Sl,\u0275\u0275disableBindings:Pl,\u0275\u0275elementStart:oo,\u0275\u0275elementEnd:io,\u0275\u0275element:Cs,\u0275\u0275elementContainerStart:so,\u0275\u0275elementContainerEnd:ao,\u0275\u0275elementContainer:ws,\u0275\u0275pureFunction0:op,\u0275\u0275pureFunction1:ip,\u0275\u0275pureFunction2:sp,\u0275\u0275pureFunction3:ap,\u0275\u0275pureFunction4:lp,\u0275\u0275pureFunction5:cp,\u0275\u0275pureFunction6:up,\u0275\u0275pureFunction7:dp,\u0275\u0275pureFunction8:fp,\u0275\u0275pureFunctionV:pp,\u0275\u0275getCurrentView:Sd,\u0275\u0275restoreView:Rl,\u0275\u0275listener:bs,\u0275\u0275projection:jd,\u0275\u0275syntheticHostProperty:ca,\u0275\u0275syntheticHostListener:Ns,\u0275\u0275pipeBind1:Dp,\u0275\u0275pipeBind2:_p,\u0275\u0275pipeBind3:Mp,\u0275\u0275pipeBind4:Ep,\u0275\u0275pipeBindV:Cp,\u0275\u0275projectionDef:Fd,\u0275\u0275hostProperty:la,\u0275\u0275property:Ms,\u0275\u0275propertyInterpolate:xs,\u0275\u0275propertyInterpolate1:lo,\u0275\u0275propertyInterpolate2:Ss,\u0275\u0275propertyInterpolate3:Ps,\u0275\u0275propertyInterpolate4:Rs,\u0275\u0275propertyInterpolate5:As,\u0275\u0275propertyInterpolate6:Os,\u0275\u0275propertyInterpolate7:Ls,\u0275\u0275propertyInterpolate8:ks,\u0275\u0275propertyInterpolateV:Fs,\u0275\u0275pipe:Ip,\u0275\u0275queryRefresh:Sp,\u0275\u0275viewQuery:Pp,\u0275\u0275loadQuery:Ap,\u0275\u0275contentQuery:Rp,\u0275\u0275reference:xd,\u0275\u0275classMap:Wd,\u0275\u0275classMapInterpolate1:ef,\u0275\u0275classMapInterpolate2:tf,\u0275\u0275classMapInterpolate3:nf,\u0275\u0275classMapInterpolate4:rf,\u0275\u0275classMapInterpolate5:of,\u0275\u0275classMapInterpolate6:sf,\u0275\u0275classMapInterpolate7:af,\u0275\u0275classMapInterpolate8:lf,\u0275\u0275classMapInterpolateV:cf,\u0275\u0275styleMap:Re,\u0275\u0275styleMapInterpolate1:uf,\u0275\u0275styleMapInterpolate2:df,\u0275\u0275styleMapInterpolate3:ff,\u0275\u0275styleMapInterpolate4:pf,\u0275\u0275styleMapInterpolate5:hf,\u0275\u0275styleMapInterpolate6:gf,\u0275\u0275styleMapInterpolate7:mf,\u0275\u0275styleMapInterpolate8:yf,\u0275\u0275styleMapInterpolateV:vf,\u0275\u0275styleProp:$s,\u0275\u0275stylePropInterpolate1:Xs,\u0275\u0275stylePropInterpolate2:ea,\u0275\u0275stylePropInterpolate3:ta,\u0275\u0275stylePropInterpolate4:na,\u0275\u0275stylePropInterpolate5:ra,\u0275\u0275stylePropInterpolate6:oa,\u0275\u0275stylePropInterpolate7:ia,\u0275\u0275stylePropInterpolate8:sa,\u0275\u0275stylePropInterpolateV:aa,\u0275\u0275classProp:Bs,\u0275\u0275advance:Bu,\u0275\u0275template:bd,\u0275\u0275text:Xd,\u0275\u0275textInterpolate:qs,\u0275\u0275textInterpolate1:fo,\u0275\u0275textInterpolate2:Gs,\u0275\u0275textInterpolate3:Ws,\u0275\u0275textInterpolate4:Qs,\u0275\u0275textInterpolate5:zs,\u0275\u0275textInterpolate6:Zs,\u0275\u0275textInterpolate7:Ys,\u0275\u0275textInterpolate8:Ks,\u0275\u0275textInterpolateV:Js,\u0275\u0275i18n:Bf,\u0275\u0275i18nAttributes:Uf,\u0275\u0275i18nExp:ma,\u0275\u0275i18nStart:ha,\u0275\u0275i18nEnd:ga,\u0275\u0275i18nApply:qf,\u0275\u0275i18nPostprocess:Gf,\u0275\u0275resolveWindow:Au,\u0275\u0275resolveDocument:Ou,\u0275\u0275resolveBody:Lu,\u0275\u0275setComponentScope:ll,\u0275\u0275setNgModuleScope:cl,\u0275\u0275registerNgModuleType:ci,\u0275\u0275sanitizeHtml:fu,\u0275\u0275sanitizeStyle:pu,\u0275\u0275sanitizeResourceUrl:Li,\u0275\u0275sanitizeScript:hu,\u0275\u0275sanitizeUrl:Oi,\u0275\u0275sanitizeUrlOrResourceUrl:yu,\u0275\u0275trustConstantHtml:gu,\u0275\u0275trustConstantResourceUrl:mu,\u0275\u0275validateIframeAttribute:Xc,forwardRef:sr,resolveForwardRef:E}))();let hn=null;function u_(){hn=null}function Ra(e){return!!ge(e)}const rr=[];let Aa=!1;function Vp(){if(!Aa){Aa=!0;try{for(let e=rr.length-1;e>=0;e--){const{moduleType:t,ngModule:n}=rr[e];n.declarations&&n.declarations.every(Hp)&&(rr.splice(e,1),h_(t,n))}}finally{Aa=!1}}}function Hp(e){return Array.isArray(e)?e.every(Hp):!!E(e)}function $p(e,t={}){Bp(e,t),void 0!==t.id&&ci(e,t.id),function d_(e,t){rr.push({moduleType:e,ngModule:t})}(e,t)}function Bp(e,t,n=!1){const r=Ce(t.declarations||A);let o=null;Object.defineProperty(e,ko,{configurable:!0,get:()=>(null===o&&(o=K().compileNgModule(de,`ng:///${e.name}/\u0275mod.js`,{type:e,bootstrap:Ce(t.bootstrap||A).map(E),declarations:r.map(E),imports:Ce(t.imports||A).map(E).map(Wp),exports:Ce(t.exports||A).map(E).map(Wp),schemas:t.schemas?Ce(t.schemas):null,id:t.id||null}),o.schemas||(o.schemas=[])),o)});let i=null;Object.defineProperty(e,Qe,{get:()=>{if(null===i){const a=K();i=a.compileFactory(de,`ng:///${e.name}/\u0275fac.js`,{name:e.name,type:e,deps:Lr(e),target:a.FactoryTarget.NgModule,typeArgumentCount:0})}return i},configurable:!1});let s=null;Object.defineProperty(e,ur,{get:()=>{if(null===s){const a={name:e.name,type:e,providers:t.providers||A,imports:[(t.imports||A).map(E),(t.exports||A).map(E)]};s=K().compileInjector(de,`ng:///${e.name}/\u0275inj.js`,a)}return s},configurable:!1})}let _o=new WeakMap,La=new WeakMap;function p_(){_o=new WeakMap,La=new WeakMap,rr.length=0}function h_(e,t){const n=Ce(t.declarations||A),r=Ct(e);n.forEach(o=>{(o=E(o)).hasOwnProperty(En)?ka(k(o),r):!o.hasOwnProperty(fr)&&!o.hasOwnProperty(pr)&&(o.ngSelectorScope=e)})}function ka(e,t){e.directiveDefs=()=>Array.from(t.compilation.directives).map(n=>n.hasOwnProperty(En)?k(n):ne(n)).filter(n=>!!n),e.pipeDefs=()=>Array.from(t.compilation.pipes).map(n=>fe(n)),e.schemas=t.schemas,e.tView=null}function Ct(e){if(Ra(e))return function g_(e){const t=ge(e,!0);if(null!==t.transitiveCompileScopes)return t.transitiveCompileScopes;const n={schemas:t.schemas||null,compilation:{directives:new Set,pipes:new Set},exported:{directives:new Set,pipes:new Set}};return Xe(t.imports).forEach(r=>{const o=Ct(r);o.exported.directives.forEach(i=>n.compilation.directives.add(i)),o.exported.pipes.forEach(i=>n.compilation.pipes.add(i))}),Xe(t.declarations).forEach(r=>{fe(r)?n.compilation.pipes.add(r):n.compilation.directives.add(r)}),Xe(t.exports).forEach(r=>{const o=r;if(Ra(o)){const i=Ct(o);i.exported.directives.forEach(s=>{n.compilation.directives.add(s),n.exported.directives.add(s)}),i.exported.pipes.forEach(s=>{n.compilation.pipes.add(s),n.exported.pipes.add(s)})}else fe(o)?n.exported.pipes.add(o):n.exported.directives.add(o)}),t.transitiveCompileScopes=n,n}(e);if(bt(e)){if(null!==(k(e)||ne(e)))return{schemas:null,compilation:{directives:new Set,pipes:new Set},exported:{directives:new Set([e]),pipes:new Set}};if(null!==fe(e))return{schemas:null,compilation:{directives:new Set,pipes:new Set},exported:{directives:new Set,pipes:new Set([e])}}}throw new Error(`${e.name} does not have a module def (\u0275mod property)`)}function Wp(e){return function jp(e){return void 0!==e.ngModule}(e)?e.ngModule:e}let Fa=0;function Qp(e,t){let n=null;(function dm(e,t){vc(t)&&(Ut.set(e,t),kn.add(e))})(e,t),Zp(e,t),Object.defineProperty(e,En,{get:()=>{if(null===n){const r=K();if(vc(t)){const c=[`Component '${e.name}' is not resolved:`];throw t.templateUrl&&c.push(` - templateUrl: ${t.templateUrl}`),t.styleUrls&&t.styleUrls.length&&c.push(` - styleUrls: ${JSON.stringify(t.styleUrls)}`),c.push("Did you run and wait for 'resolveComponentResources()'?"),new Error(c.join("\n"))}const o=function c_(){return hn}();let i=t.preserveWhitespaces;void 0===i&&(i=null!==o&&void 0!==o.preserveWhitespaces&&o.preserveWhitespaces);let s=t.encapsulation;void 0===s&&(s=null!==o&&void 0!==o.defaultEncapsulation?o.defaultEncapsulation:Ge.Emulated);const a=t.templateUrl||`ng:///${e.name}/template.html`,l={...Yp(e,t),typeSourceSpan:r.createParseSourceSpan("Component",e.name,a),template:t.template||"",preserveWhitespaces:i,styles:t.styles||A,animations:t.animations,declarations:[],changeDetection:t.changeDetection,encapsulation:s,interpolation:t.interpolation,viewProviders:t.viewProviders||null};Fa++;try{if(l.usesInheritance&&Kp(e),n=r.compileComponent(de,a,l),t.standalone){const c=Ce(t.imports||A),{directiveDefs:u,pipeDefs:d}=function y_(e,t){let n=null,r=null;return{directiveDefs:()=>{if(null===n){n=[k(e)];const s=new Set;for(const a of t){const l=E(a);if(!s.has(l))if(s.add(l),ge(l)){const c=Ct(l);for(const u of c.exported.directives){const d=k(u)||ne(u);d&&!s.has(u)&&(s.add(u),n.push(d))}}else{const c=k(l)||ne(l);c&&n.push(c)}}}return n},pipeDefs:()=>{if(null===r){r=[];const s=new Set;for(const a of t){const l=E(a);if(!s.has(l))if(s.add(l),ge(l)){const c=Ct(l);for(const u of c.exported.pipes){const d=fe(u);d&&!s.has(u)&&(s.add(u),r.push(d))}}else{const c=fe(l);c&&r.push(c)}}}return r}}}(e,c);n.directiveDefs=u,n.pipeDefs=d,n.dependencies=()=>c.map(E)}}finally{Fa--}if(0===Fa&&Vp(),function v_(e){return void 0!==e.ngSelectorScope}(e)){const c=Ct(e.ngSelectorScope);ka(n,c)}if(t.schemas){if(!t.standalone)throw new Error(`The 'schemas' was specified for the ${O(e)} but is only valid on a component that is standalone.`);n.schemas=t.schemas}else t.standalone&&(n.schemas=[])}return n},configurable:!1})}function ja(e,t){let n=null;Zp(e,t||{}),Object.defineProperty(e,fr,{get:()=>{if(null===n){const r=zp(e,t||{});n=K().compileDirective(de,r.sourceMapUrl,r.metadata)}return n},configurable:!1})}function zp(e,t){const n=e&&e.name,r=`ng:///${n}/\u0275dir.js`,o=K(),i=Yp(e,t);return i.typeSourceSpan=o.createParseSourceSpan("Directive",n,r),i.usesInheritance&&Kp(e),{metadata:i,sourceMapUrl:r}}function Zp(e,t){let n=null;Object.defineProperty(e,Qe,{get:()=>{if(null===n){const r=zp(e,t),o=K();n=o.compileFactory(de,`ng:///${e.name}/\u0275fac.js`,{name:r.metadata.name,type:r.metadata.type,typeArgumentCount:0,deps:Lr(e),target:o.FactoryTarget.Directive})}return n},configurable:!1})}function I_(e){return Object.getPrototypeOf(e.prototype)===Object.prototype}function Yp(e,t){const n=Ln(),r=n.ownPropMetadata(e);return{name:e.name,type:e,selector:void 0!==t.selector?t.selector:null,host:t.host||We,propMetadata:r,inputs:t.inputs||A,outputs:t.outputs||A,queries:Jp(e,r,Xp),lifecycle:{usesOnChanges:n.hasLifecycleHook(e,"ngOnChanges")},typeSourceSpan:null,usesInheritance:!I_(e),exportAs:M_(t.exportAs),providers:t.providers||null,viewQueries:Jp(e,r,eh),isStandalone:!!t.standalone,hostDirectives:t.hostDirectives?.map(o=>"function"==typeof o?{directive:o}:o)||null}}function Kp(e){const t=Object.prototype;let n=Object.getPrototypeOf(e.prototype).constructor;for(;n&&n!==t;)!ne(n)&&!k(n)&&C_(n)&&ja(n,null),n=Object.getPrototypeOf(n)}function D_(e){return"string"==typeof e?nh(e):E(e)}function __(e,t){return{propertyName:e,predicate:D_(t.selector),descendants:t.descendants,first:t.first,read:t.read?t.read:null,static:!!t.static,emitDistinctChangesOnly:!!t.emitDistinctChangesOnly}}function Jp(e,t,n){const r=[];for(const o in t)if(t.hasOwnProperty(o)){const i=t[o];i.forEach(s=>{if(n(s)){if(!s.selector)throw new Error(`Can't construct a query for the property "${o}" of "${O(e)}" since the query selector wasn't defined.`);if(i.some(th))throw new Error("Cannot combine @Input decorators with query decorators");r.push(__(o,s))}})}return r}function M_(e){return void 0===e?null:nh(e)}function Xp(e){const t=e.ngMetadataName;return"ContentChild"===t||"ContentChildren"===t}function eh(e){const t=e.ngMetadataName;return"ViewChild"===t||"ViewChildren"===t}function th(e){return"Input"===e.ngMetadataName}function nh(e){return e.split(",").map(t=>t.trim())}const E_=["ngOnChanges","ngOnInit","ngOnDestroy","ngDoCheck","ngAfterViewInit","ngAfterViewChecked","ngAfterContentInit","ngAfterContentChecked"];function C_(e){const t=Ln();if(E_.some(r=>t.hasLifecycleHook(e,r)))return!0;const n=t.propMetadata(e);for(const r in n){const o=n[r];for(let i=0;i<o.length;i++){const s=o[i],a=s.ngMetadataName;if(th(s)||Xp(s)||eh(s)||"Output"===a||"HostBinding"===a||"HostListener"===a)return!0}}return!1}function rh(e,t){let n=null,r=null;Object.defineProperty(e,Qe,{get:()=>{if(null===r){const o=oh(e,t),i=K();r=i.compileFactory(de,`ng:///${o.name}/\u0275fac.js`,{name:o.name,type:o.type,typeArgumentCount:0,deps:Lr(e),target:i.FactoryTarget.Pipe})}return r},configurable:!1}),Object.defineProperty(e,pr,{get:()=>{if(null===n){const o=oh(e,t);n=K().compilePipe(de,`ng:///${o.name}/\u0275pipe.js`,o)}return n},configurable:!1})}function oh(e,t){return{type:e,name:e.name,pipeName:t.name,pure:void 0===t.pure||t.pure,isStandalone:!!t.standalone}}const ih=Vt("Directive",(e={})=>e,void 0,void 0,(e,t)=>ja(e,t)),w_=Vt("Component",(e={})=>({changeDetection:Me.Default,...e}),ih,void 0,(e,t)=>Qp(e,t)),T_=Vt("Pipe",e=>({pure:!0,...e}),void 0,void 0,(e,t)=>rh(e,t)),b_=st("Input",e=>({bindingPropertyName:e})),N_=st("Output",e=>({bindingPropertyName:e})),x_=st("HostBinding",e=>({hostPropertyName:e})),S_=st("HostListener",(e,t)=>({eventName:e,args:t})),P_=Vt("NgModule",e=>e,void 0,void 0,(e,t)=>$p(e,t));function Mo(...e){}const sh=new V("Application Initializer");let Eo=(()=>{class e{constructor(n){this.appInits=n,this.resolve=Mo,this.reject=Mo,this.initialized=!1,this.done=!1,this.donePromise=new Promise((r,o)=>{this.resolve=r,this.reject=o})}runInitializers(){if(this.initialized)return;const n=[],r=()=>{this.done=!0,this.resolve()};if(this.appInits)for(let o=0;o<this.appInits.length;o++){const i=this.appInits[o]();if(Ts(i))n.push(i);else if(Rd(i)){const s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});n.push(s)}}Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),0===n.length&&r(),this.initialized=!0}}return e.\u0275fac=function(n){return new(n||e)(z(sh,8))},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const ah=new V("AppId",{providedIn:"root",factory:lh});function lh(){return`${Va()}${Va()}${Va()}`}const R_={provide:ah,useFactory:lh,deps:[]};function Va(){return String.fromCharCode(97+Math.floor(25*Math.random()))}const ch=new V("Platform Initializer"),A_=new V("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),O_=new V("Application Packages Root URL"),L_=new V("AnimationModuleType");let k_=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"platform"}),e})();const Co=new V("LocaleId",{providedIn:"root",factory:()=>sl(Co,w.Optional|w.SkipSelf)||function F_(){return typeof $localize<"u"&&$localize.locale||Et}()}),j_=new V("DefaultCurrencyCode",{providedIn:"root",factory:()=>"USD"}),V_=new V("Translations"),H_=new V("TranslationsFormat");var qe=(()=>((qe=qe||{})[qe.Error=0]="Error",qe[qe.Warning=1]="Warning",qe[qe.Ignore=2]="Ignore",qe))();class uh{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let $_=(()=>{class e{compileModuleSync(n){return new yo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),i=Xe(ge(n).declarations).reduce((s,a)=>{const l=k(a);return l&&s.push(new en(l)),s},[]);return new uh(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const dh=new V("compilerOptions");class B_{}function U_(e){Jr(Pc(e)),Xf(e).forEach(t=>Td(t))}const fh="ng";let ph=!1;function q_(){ph||(ph=!0,Te("\u0275setProfiler",_g),Te("getDirectiveMetadata",UD),Te("getComponent",Ea),Te("getContext",Kf),Te("getListeners",np),Te("getOwningComponent",Jf),Te("getHostElement",Ca),Te("getInjector",ep),Te("getRootComponents",Xf),Te("getDirectives",tp),Te("applyChanges",U_))}function Te(e,t){if((typeof COMPILED>"u"||!COMPILED)&&Z){let r=Z[fh];r||(r=Z[fh]={}),r[e]=t}}const G_=(()=>Promise.resolve(0))();function Ha(e){typeof Zone>"u"?G_.then(()=>{e&&e.apply(null,null)}):Zone.current.scheduleMicroTask("scheduleMicrotask",e)}class be{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ue(!1),this.onMicrotaskEmpty=new Ue(!1),this.onStable=new Ue(!1),this.onError=new Ue(!1),typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function W_(){let e=Z.requestAnimationFrame,t=Z.cancelAnimationFrame;if(typeof Zone<"u"&&e&&t){const n=e[Zone.__symbol__("OriginalDelegate")];n&&(e=n);const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r)}return{nativeRequestAnimationFrame:e,nativeCancelAnimationFrame:t}}().nativeRequestAnimationFrame,function Z_(e){const t=()=>{!function z_(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(Z,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Ba(e),e.isCheckStableRunning=!0,$a(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Ba(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{try{return hh(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||e.shouldCoalesceRunChangeDetection)&&t(),gh(e)}},onInvoke:(n,r,o,i,s,a,l)=>{try{return hh(e),n.invoke(o,i,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&t(),gh(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&("microTask"==i.change?(e._hasPendingMicrotasks=i.microTask,Ba(e),$a(e)):"macroTask"==i.change&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!be.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(be.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){const i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Q_,Mo,Mo);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const Q_={};function $a(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ba(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function hh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function gh(e){e._nesting--,$a(e)}class mh{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Ue,this.onMicrotaskEmpty=new Ue,this.onStable=new Ue,this.onError=new Ue}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}}const yh=new V(""),vh=new V("");let Ua,Y_=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Ua||(Dh(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{be.assertNotInAngularZone(),Ha(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())Ha(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}}return e.\u0275fac=function(n){return new(n||e)(z(be),z(Ih),z(vh))},e.\u0275prov=X({token:e,factory:e.\u0275fac}),e})(),Ih=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Ua?.findTestabilityInTree(this,n,r)??null}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"platform"}),e})();function Dh(e){Ua=e}const nt=!1;let ct=null;const qa=new V("AllowMultipleToken"),Ga=new V("PlatformDestroyListeners"),_h=new V("appBootstrapListener");function Mh(e,t,n){const r=new yo(n);return Promise.resolve(r)}function Eh(e){return e.isBoundToModule}class K_{constructor(t,n){this.name=t,this.token=n}}function Ch(e){if(ct&&!ct.get(qa,!1))throw new _(400,!1);ct=e;const t=e.get(Wa);return wh(e),t}function wh(e){const t=e.get(ch,null);t&&t.forEach(n=>n())}function X_(e){const{rootComponent:t,appProviders:n,platformProviders:r}=e;nt&&void 0!==t&&function $y(e){if(function By(e){if(!k(e))throw new _(906,`The ${O(e)} is not an Angular component, make sure it has the \`@Component\` decorator.`)}(e),!k(e).standalone)throw new _(907,`The ${O(e)} component is not marked as standalone, but Angular expects to have a standalone component here. Please make sure the ${O(e)} component has the \`standalone: true\` flag in the decorator.`)}(t);const o=function J_(e=[]){if(ct)return ct;const t=Nh(e);return ct=t,wh(t),t}(r),i=Sh("zone.js",xh());return i.run(()=>{const a=Ma([{provide:be,useValue:i},...n||[]],o,"Environment Injector"),l=a.get(qn,null);if(nt&&!l)throw new _(402,"No `ErrorHandler` found in the Dependency Injection tree.");let c;i.runOutsideAngular(()=>{c=i.onError.subscribe({next:f=>{l.handleError(f)}})});const u=()=>a.destroy(),d=o.get(Ga);return d.add(u),a.onDestroy(()=>{c.unsubscribe(),d.delete(u)}),Ph(l,i,()=>{const f=a.get(Eo);return f.runInitializers(),f.donePromise.then(()=>{da(a.get(Co,Et)||Et);const h=a.get(To);return void 0!==t&&h.bootstrap(t),h})})})}function Th(e,t,n=[]){const r=`Platform: ${t}`,o=new V(r);return(i=[])=>{let s=wo();if(!s||s.injector.get(qa,!1)){const a=[...n,...i,{provide:o,useValue:!0}];e?e(a):Ch(Nh(a,r))}return bh()}}function bh(e){const t=wo();if(!t)throw new _(401,!1);return t}function Nh(e=[],t){return at.create({name:t,providers:[{provide:$i,useValue:"platform"},{provide:Ga,useValue:new Set([()=>ct=null])},...e]})}function eM(){wo()?.destroy()}function wo(){return ct?.get(Wa)??null}let Wa=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const o=Sh(r?.ngZone,xh(r)),i=[{provide:be,useValue:o}];return o.run(()=>{const s=at.create({providers:i,parent:this.injector,name:n.moduleType.name}),a=n.create(s),l=a.injector.get(qn,null);if(!l)throw new _(402,!1);return o.runOutsideAngular(()=>{const c=o.onError.subscribe({next:u=>{l.handleError(u)}});a.onDestroy(()=>{bo(this._modules,a),c.unsubscribe()})}),Ph(l,o,()=>{const c=a.injector.get(Eo);return c.runInitializers(),c.donePromise.then(()=>(da(a.injector.get(Co,Et)||Et),this._moduleDoBootstrap(a),a))})})}bootstrapModule(n,r=[]){const o=Rh({},r);return Mh(0,0,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){const r=n.injector.get(To);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else{if(!n.instance.ngDoBootstrap)throw new _(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new _(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(Ga,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}}return e.\u0275fac=function(n){return new(n||e)(z(at))},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"platform"}),e})();function xh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:!(!e||!e.ngZoneEventCoalescing)||!1,shouldCoalesceRunChangeDetection:!(!e||!e.ngZoneRunCoalescing)||!1}}function Sh(e,t){let n;return n="noop"===e?new mh:("zone.js"===e?void 0:e)||new be(t),n}function Ph(e,t,n){try{const r=n();return Ts(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Rh(e,t){return Array.isArray(t)?t.reduce(Rh,e):{...e,...t}}let To=(()=>{class e{get destroyed(){return this._destroyed}get injector(){return this._injector}constructor(n,r,o){this._zone=n,this._injector=r,this._exceptionHandler=o,this._bootstrapListeners=[],this._views=[],this._runningTick=!1,this._stable=!0,this._destroyed=!1,this._destroyListeners=[],this.componentTypes=[],this.components=[],this._onMicrotaskEmptySubscription=this._zone.onMicrotaskEmpty.subscribe({next:()=>{this._zone.run(()=>{this.tick()})}});const i=new mn.Observable(a=>{this._stable=this._zone.isStable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks,this._zone.runOutsideAngular(()=>{a.next(this._stable),a.complete()})}),s=new mn.Observable(a=>{let l;this._zone.runOutsideAngular(()=>{l=this._zone.onStable.subscribe(()=>{be.assertNotInAngularZone(),Ha(()=>{!this._stable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks&&(this._stable=!0,a.next(!0))})})});const c=this._zone.onUnstable.subscribe(()=>{be.assertInAngularZone(),this._stable&&(this._stable=!1,this._zone.runOutsideAngular(()=>{a.next(!1)}))});return()=>{l.unsubscribe(),c.unsubscribe()}});this.isStable=(0,mn.merge)(i,s.pipe((0,Qh.share)()))}bootstrap(n,r){nt&&this.warnIfDestroyed();const o=n instanceof Wr;if(!this._injector.get(Eo).done){const p="Cannot bootstrap as there are still asynchronous initializers running."+(!o&&bt(n)?"":" Bootstrap components in the `ngDoBootstrap` method of the root module.");throw new _(405,nt&&p)}let s;s=o?n:this._injector.get(Bn).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const a=Eh(s)?void 0:this._injector.get(pn),c=s.create(at.NULL,[],r||s.selector,a),u=c.location.nativeElement,d=c.injector.get(yh,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),bo(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){if(nt&&this.warnIfDestroyed(),this._runningTick)throw new _(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this._zone.runOutsideAngular(()=>this._exceptionHandler.handleError(n))}finally{this._runningTick=!1}}attachView(n){nt&&this.warnIfDestroyed();const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){nt&&this.warnIfDestroyed();const r=n;bo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get(_h,[]);r.push(...this._bootstrapListeners),r.forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy()),this._onMicrotaskEmptySubscription.unsubscribe()}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return nt&&this.warnIfDestroyed(),this._destroyListeners.push(n),()=>bo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){nt&&this._destroyed&&console.warn(yn(406,"This instance of the `ApplicationRef` has already been destroyed."))}}return e.\u0275fac=function(n){return new(n||e)(z(be),z(It),z(qn))},e.\u0275prov=X({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();function bo(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function nM(){return!1}function rM(){}function oM(e){const t=_c(e);if(!t)throw Oh(e);return new yo(t)}function iM(e){const t=_c(e);if(!t)throw Oh(e);return t}function Oh(e){return new Error(`No module with ID ${e} loaded`)}let Lh=(()=>{class e{}return e.__NG_ELEMENT_ID__=kh,e})();function kh(e){return function sM(e,t,n){if(bn(e)&&!n){const r=pe(e.index,t);return new Xt(r,r)}return 47&e.type?new Xt(t[oe],t):null}(te(),g(),16==(16&e))}class Fh extends Lh{}class aM extends Fh{}class lM{constructor(t,n){this.name=t,this.callback=n}}function cM(e){return e.map(t=>t.nativeElement)}class Qa{constructor(t){this.nativeNode=t}get parent(){const t=this.nativeNode.parentNode;return t?new or(t):null}get injector(){return ep(this.nativeNode)}get componentInstance(){const t=this.nativeNode;return t&&(Ea(t)||Jf(t))}get context(){return Ea(this.nativeNode)||Kf(this.nativeNode)}get listeners(){return np(this.nativeNode).filter(t=>"dom"===t.type)}get references(){return function qD(e){const t=ue(e);if(null===t)return{};if(void 0===t.localRefs){const n=t.lView;if(null===n)return{};t.localRefs=function Am(e,t){const n=e[I].data[t];if(n&&n.localNames){const r={};let o=n.index+1;for(let i=0;i<n.localNames.length;i+=2)r[n.localNames[i]]=e[o],o++;return r}return null}(n,t.nodeIndex)}return t.localRefs||{}}(this.nativeNode)}get providerTokens(){return function BD(e){const t=ue(e),n=t?t.lView:null;if(null===n)return[];const r=n[I],o=r.data[t.nodeIndex],i=[],a=o.directiveEnd;for(let l=1048575&o.providerIndexes;l<a;l++){let c=r.data[l];WD(c)&&(c=c.type),i.push(c)}return i}(this.nativeNode)}}class or extends Qa{constructor(t){super(t)}get nativeElement(){return this.nativeNode.nodeType==Node.ELEMENT_NODE?this.nativeNode:null}get name(){const t=ue(this.nativeNode),n=t?t.lView:null;return null!==n?n[I].data[t.nodeIndex].value:this.nativeNode.nodeName}get properties(){const t=ue(this.nativeNode),n=t?t.lView:null;if(null===n)return{};const r=n[I].data,o=r[t.nodeIndex],i={};return function uM(e,t){if(e){let n=Object.getPrototypeOf(e);const r=Node.prototype;for(;null!==n&&n!==r;){const o=Object.getOwnPropertyDescriptors(n);for(let i in o)if(!i.startsWith("__")&&!i.startsWith("on")){const s=e[i];dM(s)&&(t[i]=s)}n=Object.getPrototypeOf(n)}}}(this.nativeElement,i),function fM(e,t,n,r){let o=t.propertyBindings;if(null!==o)for(let i=0;i<o.length;i++){const s=o[i],l=r[s].split("\ufffd"),c=l[0];if(l.length>1){let u=l[1];for(let d=1;d<l.length-1;d++)u+=N(n[s+d-1])+l[d+1];e[c]=u}else e[c]=n[s]}}(i,o,n,r),i}get attributes(){const t={},n=this.nativeElement;if(!n)return t;const r=ue(n),o=r?r.lView:null;if(null===o)return{};const i=o[I].data[r.nodeIndex].attrs,s=[];if(i){let a=0;for(;a<i.length;){const l=i[a];if("string"!=typeof l)break;t[l]=i[a+1],s.push(l.toLowerCase()),a+=2}}for(const a of n.attributes)s.includes(a.name)||(t[a.name]=a.value);return t}get styles(){return this.nativeElement&&this.nativeElement.style?this.nativeElement.style:{}}get classes(){const t={},r=this.nativeElement.className;return("string"!=typeof r?r.baseVal.split(" "):r.split(" ")).forEach(i=>t[i]=!0),t}get childNodes(){const t=this.nativeNode.childNodes,n=[];for(let r=0;r<t.length;r++)n.push(gn(t[r]));return n}get children(){const t=this.nativeElement;if(!t)return[];const n=t.children,r=[];for(let o=0;o<n.length;o++)r.push(gn(n[o]));return r}query(t){return this.queryAll(t)[0]||null}queryAll(t){const n=[];return jh(this,t,n,!0),n}queryAllNodes(t){const n=[];return jh(this,t,n,!1),n}triggerEventHandler(t,n){const r=this.nativeNode,o=[];this.listeners.forEach(i=>{if(i.name===t){const s=i.callback;s.call(r,n),o.push(s)}}),"function"==typeof r.eventListeners&&r.eventListeners(t).forEach(i=>{if(-1!==i.toString().indexOf("__ngUnwrap__")){const s=i("__ngUnwrap__");return-1===o.indexOf(s)&&s.call(r,n)}})}}function dM(e){return"string"==typeof e||"boolean"==typeof e||"number"==typeof e||null===e}function jh(e,t,n,r){const o=ue(e.nativeNode),i=o?o.lView:null;null!==i?wt(i[I].data[o.nodeIndex],i,t,n,r,e.nativeNode):Za(e.nativeNode,t,n,r)}function wt(e,t,n,r,o,i){const s=function Eg(e,t){const n=null===e?-1:e.index;return-1!==n?ee(t[n]):null}(e,t);if(11&e.type){if(za(s,n,r,o,i),bn(e)){const l=pe(e.index,t);l&&l[I].firstChild&&wt(l[I].firstChild,l,n,r,o,i)}else e.child&&wt(e.child,t,n,r,o,i),s&&Za(s,n,r,o);const a=t[e.index];xe(a)&&Vh(a,n,r,o,i)}else if(4&e.type){const a=t[e.index];za(a[yr],n,r,o,i),Vh(a,n,r,o,i)}else if(16&e.type){const a=t[oe],c=a[re].projection[e.projection];if(Array.isArray(c))for(let u of c)za(u,n,r,o,i);else if(c){const u=a[U];wt(u[I].data[c.index],u,n,r,o,i)}}else e.child&&wt(e.child,t,n,r,o,i);if(i!==s){const a=2&e.flags?e.projectionNext:e.next;a&&wt(a,t,n,r,o,i)}}function Vh(e,t,n,r,o){for(let i=ae;i<e.length;i++){const s=e[i],a=s[I].firstChild;a&&wt(a,s,t,n,r,o)}}function za(e,t,n,r,o){if(o!==e){const i=gn(e);if(!i)return;(r&&i instanceof or&&t(i)&&-1===n.indexOf(i)||!r&&t(i)&&-1===n.indexOf(i))&&n.push(i)}}function Za(e,t,n,r){const o=e.childNodes,i=o.length;for(let s=0;s<i;s++){const a=o[s],l=gn(a);l&&((r&&l instanceof or&&t(l)&&-1===n.indexOf(l)||!r&&t(l)&&-1===n.indexOf(l))&&n.push(l),Za(a,t,n,r))}}const Ka="__ng_debug__";function gn(e){return e instanceof Node?(e.hasOwnProperty(Ka)||(e[Ka]=e.nodeType==Node.ELEMENT_NODE?new or(e):new Qa(e)),e[Ka]):null}function pM(e){return null}class Hh{constructor(){}supports(t){return no(t)}create(t){return new $h(t)}}const hM=(e,t)=>t;class $h{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||hM}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){const s=!r||n&&n.currentIndex<Uh(r,o,i)?n:r,a=Uh(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)o++;else{i||(i=[]);const c=a-o,u=l-o;if(c!=u){for(let f=0;f<c;f++){const p=f<i.length?i[f]:i[f]=0,h=p+f;u<=h&&h<c&&(i[f]=p+1)}i[s.previousIndex]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!no(t))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let o,i,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)):(n=this._mismatch(n,i,s,a),r=!0),n=n._next}else o=0,function pI(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,a=>{s=this._trackByFn(o,a),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)):(n=this._mismatch(n,a,s,o),r=!0),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return null===t?i=this._itTail:(i=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new gM(n,r),i,o),t}_verifyReinsertion(t,n,r,o){let i=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==i?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const o=t._prevRemoved,i=t._nextRemoved;return null===o?this._removalsHead=i:o._nextRemoved=i,null===i?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const o=null===n?this._itHead:n._next;return t._next=o,t._prev=n,null===o?this._itTail=t:o._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new Bh),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new Bh),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class gM{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class mM{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class Bh{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new mM,this.map.set(n,r)),r.add(t)}get(t,n){const o=this.map.get(t);return o?o.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Uh(e,t,n){const r=e.previousIndex;if(null===r)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}class qh{constructor(){}supports(t){return t instanceof Map||ds(t)}create(){return new yM}}class yM{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||ds(t)))throw new _(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const o=this._records.get(t);this._maybeAddToChanges(o,n);const i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}const r=new vM(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class vM{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function Gh(){return new Ja([new Hh])}let Ja=(()=>{class e{constructor(n){this.factories=n}static create(n,r){if(null!=r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Gh()),deps:[[e,new Bt,new On]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(null!=r)return r;throw new _(901,!1)}}return e.\u0275prov=X({token:e,providedIn:"root",factory:Gh}),e})();function Wh(){return new Xa([new qh])}let Xa=(()=>{class e{constructor(n){this.factories=n}static create(n,r){if(r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Wh()),deps:[[e,new Bt,new On]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(r)return r;throw new _(901,!1)}}return e.\u0275prov=X({token:e,providedIn:"root",factory:Wh}),e})();const IM=[new qh],_M=new Ja([new Hh]),MM=new Xa(IM),EM=Th(null,"core",[]);let CM=(()=>{class e{constructor(n){}}return e.\u0275fac=function(n){return new(n||e)(z(To))},e.\u0275mod=Fo({type:e}),e.\u0275inj=cr({}),e})();function wM(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}function TM(e){return K().compileDirectiveDeclaration(de,`ng:///${e.type.name}/\u0275fac.js`,e)}function bM(e){rp(e.type,e.decorators,e.ctorParameters??null,e.propDecorators??null)}function NM(e){return K().compileComponentDeclaration(de,`ng:///${e.type.name}/\u0275cmp.js`,e)}function xM(e){return K(function SM(e){switch(e){case Y.Directive:return"directive";case Y.Component:return"component";case Y.Injectable:return"injectable";case Y.Pipe:return"pipe";case Y.NgModule:return"NgModule"}}(e.target)).compileFactoryDeclaration(de,`ng:///${e.type.name}/\u0275fac.js`,e)}function PM(e){return K().compileInjectableDeclaration(de,`ng:///${e.type.name}/\u0275prov.js`,e)}function RM(e){return K().compileInjectorDeclaration(de,`ng:///${e.type.name}/\u0275inj.js`,e)}function AM(e){return K().compileNgModuleDeclaration(de,`ng:///${e.type.name}/\u0275mod.js`,e)}function OM(e){return K().compilePipeDeclaration(de,`ng:///${e.type.name}/\u0275pipe.js`,e)}function LM(e,t){const n=k(e),r=t.elementInjector||Gr();return new en(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function kM(e){const t=k(e);if(!t)return null;const n=new en(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone}}}}}]);