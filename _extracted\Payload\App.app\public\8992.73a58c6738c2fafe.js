(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8992,5001],{45001:(Ti,Ge,re)=>{re.r(Ge),re.d(Ge,{AnimationDriver:()=>ds,\u0275Animation:()=>Vs,\u0275AnimationEngine:()=>pi,\u0275AnimationStyleNormalizer:()=>yt,\u0275NoopAnimationDriver:()=>tt,\u0275NoopAnimationStyleNormalizer:()=>Us,\u0275WebAnimationsDriver:()=>Ei,\u0275WebAnimationsPlayer:()=>We,\u0275WebAnimationsStyleNormalizer:()=>js,\u0275allowPreviousPlayerStylesMerge:()=>ht,\u0275containsElement:()=>Ne,\u0275getParentElement:()=>oe,\u0275invokeQuery:()=>Me,\u0275normalizeKeyframes:()=>rt,\u0275validateStyleProperty:()=>et});var b=re(73108),E=re(99877);function Ye(n){return new E.\u0275RuntimeError(3e3,!1)}function cs(){return typeof window<"u"&&typeof window.document<"u"}function ve(){return typeof process<"u"&&"[object process]"==={}.toString.call(process)}function U(n){switch(n.length){case 0:return new b.NoopAnimationPlayer;case 1:return n[0];default:return new b.\u0275AnimationGroupPlayer(n)}}function He(n,e,t,s,i=new Map,r=new Map){const o=[],a=[];let l=-1,u=null;if(s.forEach(c=>{const h=c.get("offset"),_=h==l,g=_&&u||new Map;c.forEach((d,y)=>{let T=y,v=d;if("offset"!==y)switch(T=e.normalizePropertyName(T,o),v){case b.\u0275PRE_STYLE:v=i.get(y);break;case b.AUTO_STYLE:v=r.get(y);break;default:v=e.normalizeStyleValue(y,T,v,o)}g.set(T,v)}),_||a.push(g),u=g,l=h}),o.length)throw function xt(n){return new E.\u0275RuntimeError(3502,!1)}();return a}function we(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&be(t,"start",n)));break;case"done":n.onDone(()=>s(t&&be(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&be(t,"destroy",n)))}}function be(n,e,t){const r=Ae(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,t.totalTime??n.totalTime,!!t.disabled),o=n._data;return null!=o&&(r._data=o),r}function Ae(n,e,t,s,i="",r=0,o){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!o}}function k(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function Xe(n){const e=n.indexOf(":");return[n.substring(1,e),n.slice(e+1)]}let Pe=(n,e)=>!1,Ze=(n,e,t)=>[],Je=null;function oe(n){const e=n.parentNode||n.host;return e===Je?null:e}(ve()||typeof Element<"u")&&(cs()?(Je=(()=>document.documentElement)(),Pe=(n,e)=>{for(;e;){if(e===n)return!0;e=oe(e)}return!1}):Pe=(n,e)=>n.contains(e),Ze=(n,e,t)=>{if(t)return Array.from(n.querySelectorAll(e));const s=n.querySelector(e);return s?[s]:[]});let G=null,xe=!1;function et(n){G||(G=function ms(){return typeof document<"u"?document.body:null}()||{},xe=!!G.style&&"WebkitAppearance"in G.style);let e=!0;return G.style&&!function fs(n){return"ebkit"==n.substring(1,6)}(n)&&(e=n in G.style,!e&&xe&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in G.style)),e}const Ne=Pe,Me=Ze;let tt=(()=>{class n{validateStyleProperty(t){return et(t)}matchesElement(t,s){return!1}containsElement(t,s){return Ne(t,s)}getParentElement(t){return oe(t)}query(t,s,i){return Me(t,s,i)}computeStyle(t,s,i){return i||""}animate(t,s,i,r,o,a=[],l){return new b.NoopAnimationPlayer(i,r)}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275prov=E.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac}),n})(),ds=(()=>{class n{}return n.NOOP=new tt,n})();const ps=1e3,De="ng-enter",ae="ng-leave",le="ng-trigger",ue=".ng-trigger",it="ng-animating",Ce=".ng-animating";function q(n){if("number"==typeof n)return n;const e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:ke(parseFloat(e[1]),e[2])}function ke(n,e){return"s"===e?n*ps:n}function he(n,e,t){return n.hasOwnProperty("duration")?n:function gs(n,e,t){let i,r=0,o="";if("string"==typeof n){const a=n.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===a)return e.push(Ye()),{duration:0,delay:0,easing:""};i=ke(parseFloat(a[1]),a[2]);const l=a[3];null!=l&&(r=ke(parseFloat(l),a[4]));const u=a[5];u&&(o=u)}else i=n;if(!t){let a=!1,l=e.length;i<0&&(e.push(function kt(){return new E.\u0275RuntimeError(3100,!1)}()),a=!0),r<0&&(e.push(function Rt(){return new E.\u0275RuntimeError(3101,!1)}()),a=!0),a&&e.splice(l,0,Ye())}return{duration:i,delay:r,easing:o}}(n,e,t)}function ee(n,e={}){return Object.keys(n).forEach(t=>{e[t]=n[t]}),e}function nt(n){const e=new Map;return Object.keys(n).forEach(t=>{e.set(t,n[t])}),e}function rt(n){return n.length?n[0]instanceof Map?n:n.map(e=>nt(e)):[]}function ot(n){const e=new Map;return Array.isArray(n)?n.forEach(t=>W(t,e)):W(n,e),e}function W(n,e=new Map,t){if(t)for(let[s,i]of t)e.set(s,i);for(let[s,i]of n)e.set(s,i);return e}function at(n,e,t){return t?e+":"+t+";":""}function lt(n){let e="";for(let t=0;t<n.style.length;t++){const s=n.style.item(t);e+=at(0,s,n.style.getPropertyValue(s))}for(const t in n.style)n.style.hasOwnProperty(t)&&!t.startsWith("_")&&(e+=at(0,Ss(t),n.style[t]));n.setAttribute("style",e)}function I(n,e,t){n.style&&(e.forEach((s,i)=>{const r=Oe(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s}),ve()&&lt(n))}function Y(n,e){n.style&&(e.forEach((t,s)=>{const i=Oe(s);n.style[i]=""}),ve()&&lt(n))}function te(n){return Array.isArray(n)?1==n.length?n[0]:(0,b.sequence)(n):n}const Re=new RegExp("{{\\s*(.+?)\\s*}}","g");function ut(n){let e=[];if("string"==typeof n){let t;for(;t=Re.exec(n);)e.push(t[1]);Re.lastIndex=0}return e}function se(n,e,t){const s=n.toString(),i=s.replace(Re,(r,o)=>{let a=e[o];return null==a&&(t.push(function Lt(n){return new E.\u0275RuntimeError(3003,!1)}()),a=""),a.toString()});return i==s?n:i}function ce(n){const e=[];let t=n.next();for(;!t.done;)e.push(t.value),t=n.next();return e}const Es=/-+([a-z0-9])/g;function Oe(n){return n.replace(Es,(...e)=>e[1].toUpperCase())}function Ss(n){return n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function ht(n,e){return 0===n||0===e}function R(n,e,t){switch(e.type){case 7:return n.visitTrigger(e,t);case 0:return n.visitState(e,t);case 1:return n.visitTransition(e,t);case 2:return n.visitSequence(e,t);case 3:return n.visitGroup(e,t);case 4:return n.visitAnimate(e,t);case 5:return n.visitKeyframes(e,t);case 6:return n.visitStyle(e,t);case 8:return n.visitReference(e,t);case 9:return n.visitAnimateChild(e,t);case 10:return n.visitAnimateRef(e,t);case 11:return n.visitQuery(e,t);case 12:return n.visitStagger(e,t);default:throw function Ft(n){return new E.\u0275RuntimeError(3004,!1)}()}}function ct(n,e){return window.getComputedStyle(n)[e]}const de="*";function As(n,e){const t=[];return"string"==typeof n?n.split(/\s*,\s*/).forEach(s=>function Ps(n,e,t){if(":"==n[0]){const l=function Ns(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(function Ht(n){return new E.\u0275RuntimeError(3016,!1)}()),"* => *"}}(n,t);if("function"==typeof l)return void e.push(l);n=l}const s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==s||s.length<4)return t.push(function Yt(n){return new E.\u0275RuntimeError(3015,!1)}()),e;const i=s[1],r=s[2],o=s[3];e.push(ft(i,o));"<"==r[0]&&!(i==de&&o==de)&&e.push(ft(o,i))}(s,t,e)):t.push(n),t}const pe=new Set(["true","1"]),ye=new Set(["false","0"]);function ft(n,e){const t=pe.has(n)||ye.has(n),s=pe.has(e)||ye.has(e);return(i,r)=>{let o=n==de||n==i,a=e==de||e==r;return!o&&t&&"boolean"==typeof i&&(o=i?pe.has(n):ye.has(n)),!a&&s&&"boolean"==typeof r&&(a=r?pe.has(e):ye.has(e)),o&&a}}const Ms=new RegExp("s*:selfs*,?","g");function Le(n,e,t,s){return new Ds(n).build(e,t,s)}class Ds{constructor(e){this._driver=e}build(e,t,s){const i=new Rs(t);return this._resetContextStyleTimingState(i),R(this,te(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector="",e.collectedStyles=new Map,e.collectedStyles.set("",new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0;const r=[],o=[];return"@"==e.name.charAt(0)&&t.errors.push(function It(){return new E.\u0275RuntimeError(3006,!1)}()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(t),0==a.type){const l=a,u=l.name;u.toString().split(/\s*,\s*/).forEach(c=>{l.name=c,r.push(this.visitState(l,t))}),l.name=u}else if(1==a.type){const l=this.visitTransition(a,t);s+=l.queryCount,i+=l.depCount,o.push(l)}else t.errors.push(function Kt(){return new E.\u0275RuntimeError(3007,!1)}())}),{type:7,name:e.name,states:r,transitions:o,queryCount:s,depCount:i,options:null}}visitState(e,t){const s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){const r=new Set,o=i||{};s.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{ut(l).forEach(u=>{o.hasOwnProperty(u)||r.add(u)})})}),r.size&&(ce(r.values()),t.errors.push(function Bt(n,e){return new E.\u0275RuntimeError(3008,!1)}()))}return{type:0,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;const s=R(this,te(e.animation),t);return{type:1,matchers:As(e.expr,t.errors),animation:s,queryCount:t.queryCount,depCount:t.depCount,options:H(e.options)}}visitSequence(e,t){return{type:2,steps:e.steps.map(s=>R(this,s,t)),options:H(e.options)}}visitGroup(e,t){const s=t.currentTime;let i=0;const r=e.steps.map(o=>{t.currentTime=s;const a=R(this,o,t);return i=Math.max(i,t.currentTime),a});return t.currentTime=i,{type:3,steps:r,options:H(e.options)}}visitAnimate(e,t){const s=function Ls(n,e){if(n.hasOwnProperty("duration"))return n;if("number"==typeof n)return Fe(he(n,e).duration,0,"");const t=n;if(t.split(/\s+/).some(r=>"{"==r.charAt(0)&&"{"==r.charAt(1))){const r=Fe(0,0,"");return r.dynamic=!0,r.strValue=t,r}const i=he(t,e);return Fe(i.duration,i.delay,i.easing)}(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:(0,b.style)({});if(5==r.type)i=this.visitKeyframes(r,t);else{let o=e.styles,a=!1;if(!o){a=!0;const u={};s.easing&&(u.easing=s.easing),o=(0,b.style)(u)}t.currentTime+=s.duration+s.delay;const l=this.visitStyle(o,t);l.isEmptyStep=a,i=l}return t.currentAnimateTimings=null,{type:4,timings:s,style:i,options:null}}visitStyle(e,t){const s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){const s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of i)"string"==typeof a?a===b.AUTO_STYLE?s.push(a):t.errors.push(new E.\u0275RuntimeError(3002,!1)):s.push(nt(a));let r=!1,o=null;return s.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(o=a.get("easing"),a.delete("easing")),!r))for(let l of a.values())if(l.toString().indexOf("{{")>=0){r=!0;break}}),{type:6,styles:s,easing:o,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){const s=t.currentAnimateTimings;let i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(o=>{"string"!=typeof o&&o.forEach((a,l)=>{const u=t.collectedStyles.get(t.currentQuerySelector),c=u.get(l);let h=!0;c&&(r!=i&&r>=c.startTime&&i<=c.endTime&&(t.errors.push(function Qt(n,e,t,s,i){return new E.\u0275RuntimeError(3010,!1)}()),h=!1),r=c.startTime),h&&u.set(l,{startTime:r,endTime:i}),t.options&&function _s(n,e,t){const s=e.params||{},i=ut(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(function Ot(n){return new E.\u0275RuntimeError(3001,!1)}())})}(a,t.options,t.errors)})})}visitKeyframes(e,t){const s={type:5,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(function $t(){return new E.\u0275RuntimeError(3011,!1)}()),s;let r=0;const o=[];let a=!1,l=!1,u=0;const c=e.steps.map(v=>{const A=this._makeStyleAst(v,t);let D=null!=A.offset?A.offset:function Os(n){if("string"==typeof n)return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){const s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){const t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}(A.styles),N=0;return null!=D&&(r++,N=A.offset=D),l=l||N<0||N>1,a=a||N<u,u=N,o.push(N),A});l&&t.errors.push(function Vt(){return new E.\u0275RuntimeError(3012,!1)}()),a&&t.errors.push(function Ut(){return new E.\u0275RuntimeError(3200,!1)}());const h=e.steps.length;let _=0;r>0&&r<h?t.errors.push(function Wt(){return new E.\u0275RuntimeError(3202,!1)}()):0==r&&(_=1/(h-1));const g=h-1,d=t.currentTime,y=t.currentAnimateTimings,T=y.duration;return c.forEach((v,A)=>{const D=_>0?A==g?1:_*A:o[A],N=D*T;t.currentTime=d+y.delay+N,y.duration=N,this._validateStyleAst(v,t),v.offset=D,s.styles.push(v)}),s}visitReference(e,t){return{type:8,animation:R(this,te(e.animation),t),options:H(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:9,options:H(e.options)}}visitAnimateRef(e,t){return{type:10,animation:this.visitReference(e.animation,t),options:H(e.options)}}visitQuery(e,t){const s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;const[r,o]=function Cs(n){const e=!!n.split(/\s*,\s*/).find(t=>":self"==t);return e&&(n=n.replace(Ms,"")),n=n.replace(/@\*/g,ue).replace(/@\w+/g,t=>ue+"-"+t.slice(1)).replace(/:animating/g,Ce),[n,e]}(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,k(t.collectedStyles,t.currentQuerySelector,new Map);const a=R(this,te(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:11,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:o,animation:a,originalSelector:e.selector,options:H(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(function jt(){return new E.\u0275RuntimeError(3013,!1)}());const s="full"===e.timings?{duration:0,delay:0,easing:"full"}:he(e.timings,t.errors,!0);return{type:12,animation:R(this,te(e.animation),t),timings:s,options:null}}}class Rs{constructor(e){this.errors=e,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}}function H(n){return n?(n=ee(n)).params&&(n.params=function ks(n){return n?ee(n):null}(n.params)):n={},n}function Fe(n,e,t){return{duration:n,delay:e,easing:t}}function ze(n,e,t,s,i,r,o=null,a=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:o,subTimeline:a}}class ge{constructor(){this._map=new Map}get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}}const Is=new RegExp(":enter","g"),Bs=new RegExp(":leave","g");function Ie(n,e,t,s,i,r=new Map,o=new Map,a,l,u=[]){return(new qs).buildKeyframes(n,e,t,s,i,r,o,a,l,u)}class qs{buildKeyframes(e,t,s,i,r,o,a,l,u,c=[]){u=u||new ge;const h=new Ke(e,t,u,i,r,c,[]);h.options=l;const _=l.delay?q(l.delay):0;h.currentTimeline.delayNextStep(_),h.currentTimeline.setStyles([o],null,h.errors,l),R(this,s,h);const g=h.timelines.filter(d=>d.containsAnimation());if(g.length&&a.size){let d;for(let y=g.length-1;y>=0;y--){const T=g[y];if(T.element===t){d=T;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([a],null,h.errors,l)}return g.length?g.map(d=>d.buildKeyframes()):[ze(t,[],[],[],0,_,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){const s=t.subInstructions.get(t.element);if(s){const i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,o=this._visitSubInstructions(s,i,i.options);r!=o&&t.transformIntoNewTimeline(o)}t.previousNode=e}visitAnimateRef(e,t){const s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(const i of e){const r=i?.delay;if(r){const o="number"==typeof r?r:q(se(r,i?.params??{},t.errors));s.delayNextStep(o)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime;const o=null!=s.duration?q(s.duration):null,a=null!=s.delay?q(s.delay):null;return 0!==o&&e.forEach(l=>{const u=t.appendInstructionToTimeline(l,o,a);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),R(this,e.animation,t),t.previousNode=e}visitSequence(e,t){const s=t.subContextCount;let i=t;const r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),null!=r.delay)){6==i.previousNode.type&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=_e);const o=q(r.delay);i.delayNextStep(o)}e.steps.length&&(e.steps.forEach(o=>R(this,o,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){const s=[];let i=t.currentTimeline.currentTime;const r=e.options&&e.options.delay?q(e.options.delay):0;e.steps.forEach(o=>{const a=t.createSubContext(e.options);r&&a.delayNextStep(r),R(this,o,a),i=Math.max(i,a.currentTimeline.currentTime),s.push(a.currentTimeline)}),s.forEach(o=>t.currentTimeline.mergeTimelineCollectedStyles(o)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){const s=e.strValue;return he(t.params?se(s,t.params,t.errors):s,t.errors)}return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){const s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());const r=e.style;5==r.type?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){const s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();const r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){const s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,a=t.createSubContext().currentTimeline;a.easing=s.easing,e.styles.forEach(l=>{a.forwardTime((l.offset||0)*r),a.setStyles(l.styles,l.easing,t.errors,t.options),a.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(a),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){const s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?q(i.delay):0;r&&(6===t.previousNode.type||0==s&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=_e);let o=s;const a=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=a.length;let l=null;a.forEach((u,c)=>{t.currentQueryIndex=c;const h=t.createSubContext(e.options,u);r&&h.delayNextStep(r),u===t.element&&(l=h.currentTimeline),R(this,e.animation,h),h.currentTimeline.applyStylesToKeyframe(),o=Math.max(o,h.currentTimeline.currentTime)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(o),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){const s=t.parentContext,i=t.currentTimeline,r=e.timings,o=Math.abs(r.duration),a=o*(t.currentQueryTotal-1);let l=o*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=a-l;break;case"full":l=s.currentStaggerTime}const c=t.currentTimeline;l&&c.delayNextStep(l);const h=c.currentTime;R(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-h+(i.startTime-s.currentTimeline.startTime)}}const _e={};class Ke{constructor(e,t,s,i,r,o,a,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=o,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=_e,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new Ee(this._driver,t,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;const s=e;let i=this.options;null!=s.duration&&(i.duration=q(s.duration)),null!=s.delay&&(i.delay=q(s.delay));const r=s.params;if(r){let o=i.params;o||(o=this.options.params={}),Object.keys(r).forEach(a=>{(!t||!o.hasOwnProperty(a))&&(o[a]=se(r[a],o,this.errors))})}}_copyOptions(){const e={};if(this.options){const t=this.options.params;if(t){const s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){const i=t||this.element,r=new Ke(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=_e,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){const i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new Qs(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,o){let a=[];if(i&&a.push(this.element),e.length>0){e=(e=e.replace(Is,"."+this._enterClassName)).replace(Bs,"."+this._leaveClassName);let u=this._driver.query(this.element,e,1!=s);0!==s&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),a.push(...u)}return!r&&0==a.length&&o.push(function Gt(n){return new E.\u0275RuntimeError(3014,!1)}()),a}}class Ee{constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){const t=1===this._keyframes.size&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new Ee(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||b.AUTO_STYLE),this._currentKeyframe.set(t,b.AUTO_STYLE);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);const r=i&&i.params||{},o=function $s(n,e){const t=new Map;let s;return n.forEach(i=>{if("*"===i){s=s||e.keys();for(let r of s)t.set(r,b.AUTO_STYLE)}else W(i,t)}),t}(e,this._globalTimelineStyles);for(let[a,l]of o){const u=se(l,r,s);this._pendingStyles.set(a,u),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??b.AUTO_STYLE),this._updateStyle(a,u)}}applyStylesToKeyframe(){0!=this._pendingStyles.size&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{const i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();const e=new Set,t=new Set,s=1===this._keyframes.size&&0===this.duration;let i=[];this._keyframes.forEach((a,l)=>{const u=W(a,new Map,this._backFill);u.forEach((c,h)=>{c===b.\u0275PRE_STYLE?e.add(h):c===b.AUTO_STYLE&&t.add(h)}),s||u.set("offset",l/this.duration),i.push(u)});const r=e.size?ce(e.values()):[],o=t.size?ce(t.values()):[];if(s){const a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return ze(this.element,i,r,o,this.duration,this.startTime,this.easing,!1)}}class Qs extends Ee{constructor(e,t,s,i,r,o,a=!1){super(e,t,o.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=a,this.timings={duration:o.duration,delay:o.delay,easing:o.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){const r=[],o=s+t,a=t/o,l=W(e[0]);l.set("offset",0),r.push(l);const u=W(e[0]);u.set("offset",pt(a)),r.push(u);const c=e.length-1;for(let h=1;h<=c;h++){let _=W(e[h]);const g=_.get("offset");_.set("offset",pt((t+g*s)/o)),r.push(_)}s=o,t=0,i="",e=r}return ze(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}}function pt(n,e=3){const t=Math.pow(10,e-1);return Math.round(n*t)/t}class Vs{constructor(e,t){this._driver=e;const s=[],i=[],r=Le(e,t,s,i);if(s.length)throw function Xt(n){return new E.\u0275RuntimeError(3500,!1)}();this._animationAst=r}buildTimelines(e,t,s,i,r){const o=Array.isArray(t)?ot(t):t,a=Array.isArray(s)?ot(s):s,l=[];r=r||new ge;const u=Ie(this._driver,e,this._animationAst,De,ae,o,a,i,r,l);if(l.length)throw function Zt(n){return new E.\u0275RuntimeError(3501,!1)}();return u}}class yt{}class Us{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,i){return s}}const Ws=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]);class js extends yt{normalizePropertyName(e,t){return Oe(e)}normalizeStyleValue(e,t,s,i){let r="";const o=s.toString().trim();if(Ws.has(t)&&0!==s&&"0"!==s)if("number"==typeof s)r="px";else{const a=s.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&0==a[1].length&&i.push(function zt(n,e){return new E.\u0275RuntimeError(3005,!1)}())}return o+r}}function gt(n,e,t,s,i,r,o,a,l,u,c,h,_){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:o,timelines:a,queriedElements:l,preStyleProps:u,postStyleProps:c,totalTime:h,errors:_}}const Be={};class _t{constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return function Gs(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return void 0!==e&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,o,a,l,u,c){const h=[],_=this.ast.options&&this.ast.options.params||Be,d=this.buildStyles(s,a&&a.params||Be,h),y=l&&l.params||Be,T=this.buildStyles(i,y,h),v=new Set,A=new Map,D=new Map,N="void"===i,x={params:Ys(y,_),delay:this.ast.options?.delay},K=c?[]:Ie(e,t,this.ast.animation,r,o,d,T,x,u,h);let C=0;if(K.forEach($=>{C=Math.max($.duration+$.delay,C)}),h.length)return gt(t,this._triggerName,s,i,N,d,T,[],[],A,D,C,h);K.forEach($=>{const V=$.element,Nt=k(A,V,new Set);$.preStyleProps.forEach(X=>Nt.add(X));const ne=k(D,V,new Set);$.postStyleProps.forEach(X=>ne.add(X)),V!==t&&v.add(V)});const Q=ce(v.values());return gt(t,this._triggerName,s,i,N,d,T,K,Q,A,D,C)}}function Ys(n,e){const t=ee(e);for(const s in n)n.hasOwnProperty(s)&&null!=n[s]&&(t[s]=n[s]);return t}class Hs{constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){const s=new Map,i=ee(this.defaultParams);return Object.keys(e).forEach(r=>{const o=e[r];null!==o&&(i[r]=o)}),this.styles.styles.forEach(r=>{"string"!=typeof r&&r.forEach((o,a)=>{o&&(o=se(o,i,t));const l=this.normalizer.normalizePropertyName(a,t);o=this.normalizer.normalizeStyleValue(a,l,o,t),s.set(a,o)})}),s}}class Zs{constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,this.transitionFactories=[],this.states=new Map,t.states.forEach(i=>{this.states.set(i.name,new Hs(i.style,i.options&&i.options.params||{},s))}),Et(this.states,"true","1"),Et(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new _t(e,i,this.states))}),this.fallbackTransition=function Js(n,e,t){return new _t(n,{type:1,animation:{type:2,steps:[],options:null},matchers:[(o,a)=>!0],options:null,queryCount:0,depCount:0},e)}(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(o=>o.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}}function Et(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}const xs=new ge;class ei{constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s,this._animations=new Map,this._playersById=new Map,this.players=[]}register(e,t){const s=[],i=[],r=Le(this._driver,t,s,i);if(s.length)throw function es(n){return new E.\u0275RuntimeError(3503,!1)}();this._animations.set(e,r)}_buildPlayer(e,t,s){const i=e.element,r=He(0,this._normalizer,0,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){const i=[],r=this._animations.get(e);let o;const a=new Map;if(r?(o=Ie(this._driver,t,r,De,ae,new Map,new Map,s,xs,i),o.forEach(c=>{const h=k(a,c.element,new Map);c.postStyleProps.forEach(_=>h.set(_,null))})):(i.push(function ts(){return new E.\u0275RuntimeError(3300,!1)}()),o=[]),i.length)throw function ss(n){return new E.\u0275RuntimeError(3504,!1)}();a.forEach((c,h)=>{c.forEach((_,g)=>{c.set(g,this._driver.computeStyle(h,g,b.AUTO_STYLE))})});const u=U(o.map(c=>{const h=a.get(c.element);return this._buildPlayer(c,new Map,h)}));return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){const t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);const s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){const t=this._playersById.get(e);if(!t)throw function is(n){return new E.\u0275RuntimeError(3301,!1)}();return t}listen(e,t,s,i){const r=Ae(t,"","","");return we(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if("register"==s)return void this.register(e,i[0]);if("create"==s)return void this.create(e,t,i[0]||{});const r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e)}}}const St="ng-animate-queued",qe="ng-animate-disabled",ri=[],Tt={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},oi={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},L="__ng_removed";class Qe{get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;const s=e&&e.hasOwnProperty("value");if(this.value=function hi(n){return n??null}(s?e.value:e),s){const r=ee(e);delete r.value,this.options=r}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){const t=e.params;if(t){const s=this.options.params;Object.keys(t).forEach(i=>{null==s[i]&&(s[i]=t[i])})}}}const ie="void",$e=new Qe(ie);class ai{constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+e,F(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw function ns(n,e){return new E.\u0275RuntimeError(3302,!1)}();if(null==s||0==s.length)throw function rs(n){return new E.\u0275RuntimeError(3303,!1)}();if(!function ci(n){return"start"==n||"done"==n}(s))throw function os(n,e){return new E.\u0275RuntimeError(3400,!1)}();const r=k(this._elementListeners,e,[]),o={name:t,phase:s,callback:i};r.push(o);const a=k(this._engine.statesByElement,e,new Map);return a.has(t)||(F(e,le),F(e,le+"-"+t),a.set(t,$e)),()=>{this._engine.afterFlush(()=>{const l=r.indexOf(o);l>=0&&r.splice(l,1),this._triggers.has(t)||a.delete(t)})}}register(e,t){return!this._triggers.has(e)&&(this._triggers.set(e,t),!0)}_getTrigger(e){const t=this._triggers.get(e);if(!t)throw function as(n){return new E.\u0275RuntimeError(3401,!1)}();return t}trigger(e,t,s,i=!0){const r=this._getTrigger(t),o=new Ve(this.id,t,e);let a=this._engine.statesByElement.get(e);a||(F(e,le),F(e,le+"-"+t),this._engine.statesByElement.set(e,a=new Map));let l=a.get(t);const u=new Qe(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),a.set(t,u),l||(l=$e),u.value!==ie&&l.value===u.value){if(!function di(n,e){const t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){const r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}(l.params,u.params)){const y=[],T=r.matchStyles(l.value,l.params,y),v=r.matchStyles(u.value,u.params,y);y.length?this._engine.reportError(y):this._engine.afterFlush(()=>{Y(e,T),I(e,v)})}return}const _=k(this._engine.playersByElement,e,[]);_.forEach(y=>{y.namespaceId==this.id&&y.triggerName==t&&y.queued&&y.destroy()});let g=r.matchTransition(l.value,u.value,e,u.params),d=!1;if(!g){if(!i)return;g=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:g,fromState:l,toState:u,player:o,isFallbackTransition:d}),d||(F(e,St),o.onStart(()=>{J(e,St)})),o.onDone(()=>{let y=this.players.indexOf(o);y>=0&&this.players.splice(y,1);const T=this._engine.playersByElement.get(e);if(T){let v=T.indexOf(o);v>=0&&T.splice(v,1)}}),this.players.push(o),_.push(o),o}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);const t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){const s=this._engine.driver.query(e,ue,!0);s.forEach(i=>{if(i[L])return;const r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(o=>o.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){const r=this._engine.statesByElement.get(e),o=new Map;if(r){const a=[];if(r.forEach((l,u)=>{if(o.set(u,l.value),this._triggers.has(u)){const c=this.trigger(e,u,ie,i);c&&a.push(c)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,o),s&&U(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){const t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){const i=new Set;t.forEach(r=>{const o=r.name;if(i.has(o))return;i.add(o);const l=this._triggers.get(o).fallbackTransition,u=s.get(o)||$e,c=new Qe(ie),h=new Ve(this.id,o,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:o,transition:l,fromState:u,toState:c,player:h,isFallbackTransition:!0})})}}removeNode(e,t){const s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){const r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let o=e;for(;o=o.parentNode;)if(s.statesByElement.get(o)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{const r=e[L];(!r||r===Tt)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){F(e,this._hostClassName)}drainQueuedTransitions(e){const t=[];return this._queue.forEach(s=>{const i=s.player;if(i.destroyed)return;const r=s.element,o=this._elementListeners.get(r);o&&o.forEach(a=>{if(a.name==s.triggerName){const l=Ae(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,we(s.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{const r=s.transition.ast.depCount,o=i.transition.ast.depCount;return 0==r||0==o?r-o:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}elementContainsData(e){let t=!1;return this._elementListeners.has(e)&&(t=!0),t=!!this._queue.find(s=>s.element===e)||t,t}}class li{_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s){this.bodyNode=e,this.driver=t,this._normalizer=s,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(i,r)=>{}}get queuedPlayers(){const e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){const s=new ai(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){const s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let o=!1,a=this.driver.getParentElement(t);for(;a;){const l=i.get(a);if(l){const u=s.indexOf(l);s.splice(u+1,0,e),o=!0;break}a=this.driver.getParentElement(a)}o||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){if(!e)return;const s=this._fetchNamespace(e);this.afterFlush(()=>{this.namespacesByHostElement.delete(s.hostElement),delete this._namespaceLookup[e];const i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1)}),this.afterFlushAnimationsDone(()=>s.destroy(t))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){const t=new Set,s=this.statesByElement.get(e);if(s)for(let i of s.values())if(i.namespaceId){const r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}return t}trigger(e,t,s,i){if(Se(t)){const r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!Se(t))return;const r=t[L];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;const o=this.collectedLeaveElements.indexOf(t);o>=0&&this.collectedLeaveElements.splice(o,1)}if(e){const o=this._fetchNamespace(e);o&&o.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),F(e,qe)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),J(e,qe))}removeNode(e,t,s,i){if(Se(t)){const r=e?this._fetchNamespace(e):null;if(r?r.removeNode(t,i):this.markElementAsRemoved(e,t,!1,i),s){const o=this.namespacesByHostElement.get(t);o&&o.id!==e&&o.removeNode(t,i)}}else this._onRemovalComplete(t,i)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[L]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return Se(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,ue,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),0!=this.playersByQueriedElement.size&&(t=this.driver.query(e,Ce,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){const t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){const t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return U(this.players).onDone(()=>e());e()})}processLeaveNode(e){const t=e[L];if(t&&t.setForRemoval){if(e[L]=Tt,t.namespaceId){this.destroyInnerAnimations(e);const s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(qe)&&this.markElementAsDisabled(e,!1),this.driver.query(e,".ng-animate-disabled",!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++)F(this.collectedEnterElements[s],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++)this.processLeaveNode(this.collectedLeaveElements[s]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){const s=this._whenQuietFns;this._whenQuietFns=[],t.length?U(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw function ls(n){return new E.\u0275RuntimeError(3402,!1)}()}_flushAnimations(e,t){const s=new ge,i=[],r=new Map,o=[],a=new Map,l=new Map,u=new Map,c=new Set;this.disabledNodes.forEach(f=>{c.add(f);const m=this.driver.query(f,".ng-animate-queued",!0);for(let p=0;p<m.length;p++)c.add(m[p])});const h=this.bodyNode,_=Array.from(this.statesByElement.keys()),g=bt(_,this.collectedEnterElements),d=new Map;let y=0;g.forEach((f,m)=>{const p=De+y++;d.set(m,p),f.forEach(S=>F(S,p))});const T=[],v=new Set,A=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){const m=this.collectedLeaveElements[f],p=m[L];p&&p.setForRemoval&&(T.push(m),v.add(m),p.hasAnimation?this.driver.query(m,".ng-star-inserted",!0).forEach(S=>v.add(S)):A.add(m))}const D=new Map,N=bt(_,Array.from(v));N.forEach((f,m)=>{const p=ae+y++;D.set(m,p),f.forEach(S=>F(S,p))}),e.push(()=>{g.forEach((f,m)=>{const p=d.get(m);f.forEach(S=>J(S,p))}),N.forEach((f,m)=>{const p=D.get(m);f.forEach(S=>J(S,p))}),T.forEach(f=>{this.processLeaveNode(f)})});const x=[],K=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(p=>{const S=p.player,P=p.element;if(x.push(S),this.collectedEnterElements.length){const M=P[L];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(p.triggerName)){const Z=M.previousTriggersValues.get(p.triggerName),z=this.statesByElement.get(p.element);if(z&&z.has(p.triggerName)){const Te=z.get(p.triggerName);Te.value=Z,z.set(p.triggerName,Te)}}return void S.destroy()}}const B=!h||!this.driver.containsElement(h,P),O=D.get(P),j=d.get(P),w=this._buildInstruction(p,s,j,O,B);if(w.errors&&w.errors.length)return void K.push(w);if(B)return S.onStart(()=>Y(P,w.fromStyles)),S.onDestroy(()=>I(P,w.toStyles)),void i.push(S);if(p.isFallbackTransition)return S.onStart(()=>Y(P,w.fromStyles)),S.onDestroy(()=>I(P,w.toStyles)),void i.push(S);const Ct=[];w.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||Ct.push(M)}),w.timelines=Ct,s.append(P,w.timelines),o.push({instruction:w,player:S,element:P}),w.queriedElements.forEach(M=>k(a,M,[]).push(S)),w.preStyleProps.forEach((M,Z)=>{if(M.size){let z=l.get(Z);z||l.set(Z,z=new Set),M.forEach((Te,je)=>z.add(je))}}),w.postStyleProps.forEach((M,Z)=>{let z=u.get(Z);z||u.set(Z,z=new Set),M.forEach((Te,je)=>z.add(je))})});if(K.length){const f=[];K.forEach(m=>{f.push(function us(n,e){return new E.\u0275RuntimeError(3505,!1)}())}),x.forEach(m=>m.destroy()),this.reportError(f)}const C=new Map,Q=new Map;o.forEach(f=>{const m=f.element;s.has(m)&&(Q.set(m,m),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,C))}),i.forEach(f=>{const m=f.element;this._getPreviousPlayers(m,!1,f.namespaceId,f.triggerName,null).forEach(S=>{k(C,m,[]).push(S),S.destroy()})});const $=T.filter(f=>Pt(f,l,u)),V=new Map;wt(V,this.driver,A,u,b.AUTO_STYLE).forEach(f=>{Pt(f,l,u)&&$.push(f)});const ne=new Map;g.forEach((f,m)=>{wt(ne,this.driver,new Set(f),l,b.\u0275PRE_STYLE)}),$.forEach(f=>{const m=V.get(f),p=ne.get(f);V.set(f,new Map([...Array.from(m?.entries()??[]),...Array.from(p?.entries()??[])]))});const X=[],Mt=[],Dt={};o.forEach(f=>{const{element:m,player:p,instruction:S}=f;if(s.has(m)){if(c.has(m))return p.onDestroy(()=>I(m,S.toStyles)),p.disabled=!0,p.overrideTotalTime(S.totalTime),void i.push(p);let P=Dt;if(Q.size>1){let O=m;const j=[];for(;O=O.parentNode;){const w=Q.get(O);if(w){P=w;break}j.push(O)}j.forEach(w=>Q.set(w,P))}const B=this._buildAnimation(p.namespaceId,S,C,r,ne,V);if(p.setRealPlayer(B),P===Dt)X.push(p);else{const O=this.playersByElement.get(P);O&&O.length&&(p.parentPlayer=U(O)),i.push(p)}}else Y(m,S.fromStyles),p.onDestroy(()=>I(m,S.toStyles)),Mt.push(p),c.has(m)&&i.push(p)}),Mt.forEach(f=>{const m=r.get(f.element);if(m&&m.length){const p=U(m);f.setRealPlayer(p)}}),i.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<T.length;f++){const m=T[f],p=m[L];if(J(m,ae),p&&p.hasAnimation)continue;let S=[];if(a.size){let B=a.get(m);B&&B.length&&S.push(...B);let O=this.driver.query(m,Ce,!0);for(let j=0;j<O.length;j++){let w=a.get(O[j]);w&&w.length&&S.push(...w)}}const P=S.filter(B=>!B.destroyed);P.length?fi(this,m,P):this.processLeaveNode(m)}return T.length=0,X.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();const m=this.players.indexOf(f);this.players.splice(m,1)}),f.play()}),X}elementContainsData(e,t){let s=!1;const i=t[L];return i&&i.setForRemoval&&(s=!0),this.playersByElement.has(t)&&(s=!0),this.playersByQueriedElement.has(t)&&(s=!0),this.statesByElement.has(t)&&(s=!0),this._fetchNamespace(e).elementContainsData(t)||s}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let o=[];if(t){const a=this.playersByQueriedElement.get(e);a&&(o=a)}else{const a=this.playersByElement.get(e);if(a){const l=!r||r==ie;a.forEach(u=>{u.queued||!l&&u.triggerName!=i||o.push(u)})}}return(s||i)&&(o=o.filter(a=>!(s&&s!=a.namespaceId||i&&i!=a.triggerName))),o}_beforeAnimationBuild(e,t,s){const r=t.element,o=t.isRemovalTransition?void 0:e,a=t.isRemovalTransition?void 0:t.triggerName;for(const l of t.timelines){const u=l.element,c=u!==r,h=k(s,u,[]);this._getPreviousPlayers(u,c,o,a,t.toState).forEach(g=>{const d=g.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),g.destroy(),h.push(g)})}Y(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,o){const a=t.triggerName,l=t.element,u=[],c=new Set,h=new Set,_=t.timelines.map(d=>{const y=d.element;c.add(y);const T=y[L];if(T&&T.removedBeforeQueried)return new b.NoopAnimationPlayer(d.duration,d.delay);const v=y!==l,A=function mi(n){const e=[];return At(n,e),e}((s.get(y)||ri).map(C=>C.getRealPlayer())).filter(C=>!!C.element&&C.element===y),D=r.get(y),N=o.get(y),x=He(0,this._normalizer,0,d.keyframes,D,N),K=this._buildPlayer(d,x,A);if(d.subTimeline&&i&&h.add(y),v){const C=new Ve(e,a,y);C.setRealPlayer(K),u.push(C)}return K});u.forEach(d=>{k(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>function ui(n,e,t){let s=n.get(e);if(s){if(s.length){const i=s.indexOf(t);s.splice(i,1)}0==s.length&&n.delete(e)}return s}(this.playersByQueriedElement,d.element,d))}),c.forEach(d=>F(d,it));const g=U(_);return g.onDestroy(()=>{c.forEach(d=>J(d,it)),I(l,t.toStyles)}),h.forEach(d=>{k(i,d,[]).push(g)}),g}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new b.NoopAnimationPlayer(e.duration,e.delay)}}class Ve{constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s,this._player=new b.NoopAnimationPlayer,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>we(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){const t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){k(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){const t=this._player;t.triggerCallback&&t.triggerCallback(e)}}function Se(n){return n&&1===n.nodeType}function vt(n,e){const t=n.style.display;return n.style.display=e??"none",t}function wt(n,e,t,s,i){const r=[];t.forEach(l=>r.push(vt(l)));const o=[];s.forEach((l,u)=>{const c=new Map;l.forEach(h=>{const _=e.computeStyle(u,h,i);c.set(h,_),(!_||0==_.length)&&(u[L]=oi,o.push(u))}),n.set(u,c)});let a=0;return t.forEach(l=>vt(l,r[a++])),o}function bt(n,e){const t=new Map;if(n.forEach(a=>t.set(a,[])),0==e.length)return t;const s=1,i=new Set(e),r=new Map;function o(a){if(!a)return s;let l=r.get(a);if(l)return l;const u=a.parentNode;return l=t.has(u)?u:i.has(u)?s:o(u),r.set(a,l),l}return e.forEach(a=>{const l=o(a);l!==s&&t.get(l).push(a)}),t}function F(n,e){n.classList?.add(e)}function J(n,e){n.classList?.remove(e)}function fi(n,e,t){U(t).onDone(()=>n.processLeaveNode(e))}function At(n,e){for(let t=0;t<n.length;t++){const s=n[t];s instanceof b.\u0275AnimationGroupPlayer?At(s.players,e):e.push(s)}}function Pt(n,e,t){const s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}class pi{constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s,this._triggerCache={},this.onRemovalComplete=(i,r)=>{},this._transitionEngine=new li(e,t,s),this._timelineEngine=new ei(e,t,s),this._transitionEngine.onRemovalComplete=(i,r)=>this.onRemovalComplete(i,r)}registerTrigger(e,t,s,i,r){const o=e+"-"+i;let a=this._triggerCache[o];if(!a){const l=[],u=[],c=Le(this._driver,r,l,u);if(l.length)throw function Jt(n,e){return new E.\u0275RuntimeError(3404,!1)}();a=function Xs(n,e,t){return new Zs(n,e,t)}(i,c,this._normalizer),this._triggerCache[o]=a}this._transitionEngine.registerTrigger(t,i,a)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s,i){this._transitionEngine.removeNode(e,t,i||!1,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if("@"==s.charAt(0)){const[r,o]=Xe(s);this._timelineEngine.command(r,t,o,i)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if("@"==s.charAt(0)){const[o,a]=Xe(s);return this._timelineEngine.listen(o,t,a,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return this._transitionEngine.players.concat(this._timelineEngine.players)}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}}let gi=(()=>{class n{constructor(t,s,i){this._element=t,this._startStyles=s,this._endStyles=i,this._state=0;let r=n.initialStylesByElement.get(t);r||n.initialStylesByElement.set(t,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&I(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(I(this._element,this._initialStyles),this._endStyles&&(I(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(n.initialStylesByElement.delete(this._element),this._startStyles&&(Y(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(Y(this._element,this._endStyles),this._endStyles=null),I(this._element,this._initialStyles),this._state=3)}}return n.initialStylesByElement=new WeakMap,n})();function Ue(n){let e=null;return n.forEach((t,s)=>{(function _i(n){return"display"===n||"position"===n})(s)&&(e=e||new Map,e.set(s,t))}),e}class We{constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map,this.domPlayer.addEventListener("finish",()=>this._onFinish())}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){const t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return this.domPlayer.currentTime/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{"offset"!==i&&e.set(i,this._finished?s:ct(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){const t="start"===e?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}}class Ei{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}matchesElement(e,t){return!1}containsElement(e,t){return Ne(e,t)}getParentElement(e){return oe(e)}query(e,t,s){return Me(e,t,s)}computeStyle(e,t,s){return window.getComputedStyle(e)[t]}animate(e,t,s,i,r,o=[]){const l={duration:s,delay:i,fill:0==i?"both":"forwards"};r&&(l.easing=r);const u=new Map,c=o.filter(g=>g instanceof We);ht(s,i)&&c.forEach(g=>{g.currentSnapshot.forEach((d,y)=>u.set(y,d))});let h=rt(t).map(g=>W(g));h=function Ts(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,o)=>{s.has(o)||i.push(o),s.set(o,r)}),i.length)for(let r=1;r<e.length;r++){let o=e[r];i.forEach(a=>o.set(a,ct(n,a)))}}return e}(e,h,u);const _=function yi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=Ue(e[0]),e.length>1&&(s=Ue(e[e.length-1]))):e instanceof Map&&(t=Ue(e)),t||s?new gi(n,t,s):null}(e,h);return new We(e,h,l,_)}}}}]);