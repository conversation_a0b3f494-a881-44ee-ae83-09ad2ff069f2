(self.webpackChunkapp=self.webpackChunkapp||[]).push([[616],{616:(M,i,n)=>{n.r(i),n.d(i,{MboTransferAdvanceSourcePageModule:()=>S});var l=n(17007),v=n(78007),m=n(79798),f=n(30263),d=n(15861),g=n(39904),p=n(95437),u=n(64847),h=n(80045),e=n(99877),b=n(48774),A=n(4663);let P=(()=>{class t{constructor(o,r,a,c){this.mboProvider=o,this.requestConfiguration=r,this.managerAdvance=a,this.cancelProvider=c,this.confirmation=!1,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_transfer-advance-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(o){var r=this;return(0,d.Z)(function*(){r.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield r.managerAdvance.setSource(o)).when({success:()=>{r.mboProvider.navigation.next(g.Z6.TRANSFERS.ADVANCE.DESTINATION)},failure:({message:a})=>{r.mboProvider.toast.error(a)}},()=>{r.mboProvider.loader.close()})})()}initializatedConfiguration(){var o=this;return(0,d.Z)(function*(){(yield o.requestConfiguration.source()).when({success:({confirmation:r,products:a})=>{o.products=a,o.confirmation=r}},()=>{o.requesting=!1})})()}}return t.\u0275fac=function(o){return new(o||t)(e.\u0275\u0275directiveInject(p.ZL),e.\u0275\u0275directiveInject(u.m),e.\u0275\u0275directiveInject(u.a),e.\u0275\u0275directiveInject(h.j))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfer-advance-source-page"]],decls:6,vars:4,consts:[[1,"mbo-transfer-advance-source-page__content"],[1,"mbo-transfer-advance-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-transfer-advance-source-page__body"],["title","DISPONIBLES PARA AVANCES",3,"skeleton","products","ghost","select"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),e.\u0275\u0275listener("select",function(c){return r.onProduct(c)}),e.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas realizar el avance hoy? "),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("rightAction",r.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",r.requesting)("products",r.products)("ghost",!0))},dependencies:[b.J,A.c],styles:["/*!\n * MBO TransferAdvanceSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 08/Jul/2022\n * Updated: 07/Ene/2024\n*/mbo-transfer-advance-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-advance-source-page .mbo-transfer-advance-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-advance-source-page .mbo-transfer-advance-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),t})(),S=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[l.CommonModule,v.RouterModule.forChild([{path:"",component:P}]),f.Jx,m.cV]}),t})()}}]);