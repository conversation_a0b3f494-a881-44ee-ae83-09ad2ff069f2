(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7423],{47423:(N,H,W)=>{W.r(H),W.d(H,{Capacitor:()=>x,CapacitorCookies:()=>oe,CapacitorException:()=>v,CapacitorHttp:()=>ce,CapacitorPlatforms:()=>_,ExceptionCode:()=>h,Plugins:()=>ee,WebPlugin:()=>I,WebView:()=>re,addPlatform:()=>Z,buildRequestInit:()=>J,registerPlugin:()=>S,registerWebPlugin:()=>te,setPlatform:()=>$});var c=W(15861);const _=(r=>r.CapacitorPlatforms=(r=>{const e=new Map;e.set("web",{name:"web"});const t=r.CapacitorPlatforms||{currentPlatform:{name:"web"},platforms:e};return t.addPlatform=(n,a)=>{t.platforms.set(n,a)},t.setPlatform=n=>{t.platforms.has(n)&&(t.currentPlatform=t.platforms.get(n))},t})(r))(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),Z=_.addPlatform,$=_.setPlatform;var h=(()=>{return(r=h||(h={})).Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE",h;var r})();class v extends Error{constructor(e,t,o){super(e),this.message=e,this.code=t,this.data=o}}const w=r=>{var e,t,o,i,n;const a=r.CapacitorCustomPlatform||null,s=r.Capacitor||{},g=s.Plugins=s.Plugins||{},l=r.CapacitorPlatforms,A=(null===(e=l?.currentPlatform)||void 0===e?void 0:e.getPlatform)||(()=>null!==a?a.name:(r=>{var e,t;return r?.androidBridge?"android":null!==(t=null===(e=r?.webkit)||void 0===e?void 0:e.messageHandlers)&&void 0!==t&&t.bridge?"ios":"web"})(r)),de=(null===(t=l?.currentPlatform)||void 0===t?void 0:t.isNativePlatform)||(()=>"web"!==A()),fe=(null===(o=l?.currentPlatform)||void 0===o?void 0:o.isPluginAvailable)||(d=>!(!B.get(d)?.platforms.has(A())&&!Q(d))),Q=(null===(i=l?.currentPlatform)||void 0===i?void 0:i.getPluginHeader)||(d=>{var u;return null===(u=s.PluginHeaders)||void 0===u?void 0:u.find(U=>U.name===d)}),B=new Map,ve=(null===(n=l?.currentPlatform)||void 0===n?void 0:n.registerPlugin)||((d,u={})=>{const U=B.get(d);if(U)return console.warn(`Capacitor plugin "${d}" already registered. Cannot register plugins twice.`),U.proxy;const L=A(),j=Q(d);let b;const Pe=function(){var f=(0,c.Z)(function*(){return!b&&L in u?b=b="function"==typeof u[L]?yield u[L]():u[L]:null!==a&&!b&&"web"in u&&(b=b="function"==typeof u.web?yield u.web():u.web),b});return function(){return f.apply(this,arguments)}}(),F=f=>{let m;const P=(...y)=>{const C=Pe().then(p=>{const k=((f,m)=>{var P,y;if(!j){if(f)return null===(y=f[m])||void 0===y?void 0:y.bind(f);throw new v(`"${d}" plugin is not implemented on ${L}`,h.Unimplemented)}{const C=j?.methods.find(p=>m===p.name);if(C)return"promise"===C.rtype?p=>s.nativePromise(d,m.toString(),p):(p,k)=>s.nativeCallback(d,m.toString(),p,k);if(f)return null===(P=f[m])||void 0===P?void 0:P.bind(f)}})(p,f);if(k){const M=k(...y);return m=M?.remove,M}throw new v(`"${d}.${f}()" is not implemented on ${L}`,h.Unimplemented)});return"addListener"===f&&(C.remove=(0,c.Z)(function*(){return m()})),C};return P.toString=()=>`${f.toString()}() { [capacitor code] }`,Object.defineProperty(P,"name",{value:f,writable:!1,configurable:!1}),P},X=F("addListener"),Y=F("removeListener"),we=(f,m)=>{const P=X({eventName:f},m),y=function(){var p=(0,c.Z)(function*(){const k=yield P;Y({eventName:f,callbackId:k},m)});return function(){return p.apply(this,arguments)}}(),C=new Promise(p=>P.then(()=>p({remove:y})));return C.remove=(0,c.Z)(function*(){console.warn("Using addListener() without 'await' is deprecated."),yield y()}),C},q=new Proxy({},{get(f,m){switch(m){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return j?we:X;case"removeListener":return Y;default:return F(m)}}});return g[d]=q,B.set(d,{name:d,proxy:q,platforms:new Set([...Object.keys(u),...j?[L]:[]])}),q});return s.convertFileSrc||(s.convertFileSrc=d=>d),s.getPlatform=A,s.handleError=d=>r.console.error(d),s.isNativePlatform=de,s.isPluginAvailable=fe,s.pluginMethodNoop=(d,u,U)=>Promise.reject(`${U} does not have an implementation of "${u}".`),s.registerPlugin=ve,s.Exception=v,s.DEBUG=!!s.DEBUG,s.isLoggingEnabled=!!s.isLoggingEnabled,s.platform=s.getPlatform(),s.isNative=s.isNativePlatform(),s},x=(r=>r.Capacitor=w(r))(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),S=x.registerPlugin,ee=x.Plugins,te=r=>((r,e)=>{var t;const o=e.config,i=r.Plugins;if(!o?.name)throw new Error('Capacitor WebPlugin is using the deprecated "registerWebPlugin()" function, but without the config. Please use "registerPlugin()" instead to register this web plugin."');console.warn(`Capacitor plugin "${o.name}" is using the deprecated "registerWebPlugin()" function`),(!i[o.name]||null!==(t=o?.platforms)&&void 0!==t&&t.includes(r.getPlatform()))&&(i[o.name]=e)})(x,r);class I{constructor(e){this.listeners={},this.retainedEventArguments={},this.windowListeners={},e&&(console.warn(`Capacitor WebPlugin "${e.name}" config object was deprecated in v3 and will be removed in v4.`),this.config=e)}addListener(e,t){var o=this;let i=!1;this.listeners[e]||(this.listeners[e]=[],i=!0),this.listeners[e].push(t);const a=this.windowListeners[e];a&&!a.registered&&this.addWindowListener(a),i&&this.sendRetainedArgumentsForEvent(e);const s=function(){var l=(0,c.Z)(function*(){return o.removeListener(e,t)});return function(){return l.apply(this,arguments)}}();return Promise.resolve({remove:s})}removeAllListeners(){var e=this;return(0,c.Z)(function*(){e.listeners={};for(const t in e.windowListeners)e.removeWindowListener(e.windowListeners[t]);e.windowListeners={}})()}notifyListeners(e,t,o){const i=this.listeners[e];if(i)i.forEach(n=>n(t));else if(o){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:o=>{this.notifyListeners(t,o)}}}unimplemented(e="not implemented"){return new x.Exception(e,h.Unimplemented)}unavailable(e="not available"){return new x.Exception(e,h.Unavailable)}removeListener(e,t){var o=this;return(0,c.Z)(function*(){const i=o.listeners[e];if(!i)return;const n=i.indexOf(t);o.listeners[e].splice(n,1),o.listeners[e].length||o.removeWindowListener(o.windowListeners[e])})()}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(o=>{this.notifyListeners(e,o)}))}}const re=S("WebView"),V=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),z=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class ne extends I{getCookies(){return(0,c.Z)(function*(){const e=document.cookie,t={};return e.split(";").forEach(o=>{if(o.length<=0)return;let[i,n]=o.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");i=z(i).trim(),n=z(n).trim(),t[i]=n}),t})()}setCookie(e){return(0,c.Z)(function*(){try{const t=V(e.key),o=V(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,n=(e.path||"/").replace("path=",""),a=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${o||""}${i}; path=${n}; ${a};`}catch(t){return Promise.reject(t)}})()}deleteCookie(e){return(0,c.Z)(function*(){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}})()}clearCookies(){return(0,c.Z)(function*(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}})()}clearAllCookies(){var e=this;return(0,c.Z)(function*(){try{yield e.clearCookies()}catch(t){return Promise.reject(t)}})()}}const oe=S("CapacitorCookies",{web:()=>new ne}),ie=function(){var r=(0,c.Z)(function*(e){return new Promise((t,o)=>{const i=new FileReader;i.onload=()=>{const n=i.result;t(n.indexOf(",")>=0?n.split(",")[1]:n)},i.onerror=n=>o(n),i.readAsDataURL(e)})});return function(t){return r.apply(this,arguments)}}(),J=(r,e={})=>{const t=Object.assign({method:r.method||"GET",headers:r.headers},e),i=((r={})=>{const e=Object.keys(r);return Object.keys(r).map(i=>i.toLocaleLowerCase()).reduce((i,n,a)=>(i[n]=r[e[a]],i),{})})(r.headers)["content-type"]||"";if("string"==typeof r.data)t.body=r.data;else if(i.includes("application/x-www-form-urlencoded")){const n=new URLSearchParams;for(const[a,s]of Object.entries(r.data||{}))n.set(a,s);t.body=n.toString()}else if(i.includes("multipart/form-data")||r.data instanceof FormData){const n=new FormData;if(r.data instanceof FormData)r.data.forEach((s,g)=>{n.append(g,s)});else for(const s of Object.keys(r.data))n.append(s,r.data[s]);t.body=n;const a=new Headers(t.headers);a.delete("content-type"),t.headers=a}else(i.includes("application/json")||"object"==typeof r.data)&&(t.body=JSON.stringify(r.data));return t};class le extends I{request(e){return(0,c.Z)(function*(){const t=J(e,e.webFetchExtra),o=((r,e=!0)=>r?Object.entries(r).reduce((o,i)=>{const[n,a]=i;let s,g;return Array.isArray(a)?(g="",a.forEach(l=>{s=e?encodeURIComponent(l):l,g+=`${n}=${s}&`}),g.slice(0,-1)):(s=e?encodeURIComponent(a):a,g=`${n}=${s}`),`${o}&${g}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),i=o?`${e.url}?${o}`:e.url,n=yield fetch(i,t),a=n.headers.get("content-type")||"";let g,l,{responseType:s="text"}=n.ok?e:{};switch(a.includes("application/json")&&(s="json"),s){case"arraybuffer":case"blob":l=yield n.blob(),g=yield ie(l);break;case"json":g=yield n.json();break;default:g=yield n.text()}const T={};return n.headers.forEach((A,G)=>{T[G]=A}),{data:g,headers:T,status:n.status,url:n.url}})()}get(e){var t=this;return(0,c.Z)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"GET"}))})()}post(e){var t=this;return(0,c.Z)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"POST"}))})()}put(e){var t=this;return(0,c.Z)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"PUT"}))})()}patch(e){var t=this;return(0,c.Z)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"PATCH"}))})()}delete(e){var t=this;return(0,c.Z)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"DELETE"}))})()}}const ce=S("CapacitorHttp",{web:()=>new le})},15861:(N,H,W)=>{function c(R,_,Z,$,O,h,v){try{var E=R[h](v),w=E.value}catch(K){return void Z(K)}E.done?_(w):Promise.resolve(w).then($,O)}function D(R){return function(){var _=this,Z=arguments;return new Promise(function($,O){var h=R.apply(_,Z);function v(w){c(h,$,O,v,E,"next",w)}function E(w){c(h,$,O,v,E,"throw",w)}v(void 0)})}}W.d(H,{Z:()=>D})}}]);