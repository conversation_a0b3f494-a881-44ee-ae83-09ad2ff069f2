(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9957],{99957:(N,d,n)=>{n.r(d),n.d(d,{MboCelToCelSendAccountPageModule:()=>I});var s=n(17007),b=n(78007),l=n(30263),v=n(24495),u=n(39904),C=n(95437),f=n(57544),m=n(17758),h=n(98487),e=n(99877),A=n(48774),x=n(70016),S=n(66613),T=n(45542);function M(t,a){if(1&t&&(e.\u0275\u0275elementStart(0,"bocc-card-radiobutton",9)(1,"div",10),e.\u0275\u0275element(2,"img",11),e.\u0275\u0275elementStart(3,"span",12),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()()),2&t){const o=a.$implicit,c=e.\u0275\u0275nextContext();e.\u0275\u0275property("invert",!0)("value",o)("formControl",c.accountControl),e.\u0275\u0275advance(2),e.\u0275\u0275property("src",o.logo,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",o.name," ")}}function y(t,a){1&t&&(e.\u0275\u0275elementStart(0,"bocc-alert",13),e.\u0275\u0275text(1," Si seleccionas "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3,"Otras entidades"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," puedes recibir cargos extra en la transacci\xf3n. "),e.\u0275\u0275elementEnd()),2&t&&e.\u0275\u0275property("visible",!0)}const g=u.Z6.TRANSFERS.CELTOCEL.SEND;let P=(()=>{class t{constructor(o,c,r,i){this.mboProvider=o,this.requestConfiguration=c,this.managerCelToCel=r,this.cancelProvider=i,this.options=[],this.backAction={id:"btn_cel-to-cel-send-account_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(g.DESTINATION)}},this.cancelAction={id:"btn_cel-to-cel-send-account_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.accountControl=new f.FormControl(void 0,[v.C1])}ngOnInit(){this.initializatedConfiguration()}get alertVisible(){return this.accountControl.valid&&!this.accountControl.value.account}onSubmit(){const o=this.accountControl.value.account;o?this.managerCelToCel.setAccount(o).when({success:()=>{this.mboProvider.navigation.next(g.AMOUNT)}}):this.mboProvider.navigation.next(u.Z6.TRANSFERS.TRANSFIYA.TRANSFER.AMOUNT)}initializatedConfiguration(){this.requestConfiguration.account().when({success:({accounts:o,account:c})=>{const r=o.map(i=>{const{bank:{logo:z,name:E}}=i,p={account:i,logo:z,name:E};return i===c&&this.accountControl.setValue(p),p});this.options=r.concat([{name:"Otras entidades - Transfiya",logo:"assets/shared/logos/banks/default-bank.svg"}])}})}}return t.\u0275fac=function(o){return new(o||t)(e.\u0275\u0275directiveInject(C.ZL),e.\u0275\u0275directiveInject(m.ZW),e.\u0275\u0275directiveInject(m.Ey),e.\u0275\u0275directiveInject(h.T))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-cel-to-cel-send-account-page"]],decls:12,vars:5,consts:[[1,"mbo-cel-to-cel-send-account-page__content"],[1,"mbo-cel-to-cel-send-account-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-cel-to-cel-send-account-page__body"],[1,"mbo-cel-to-cel-send-account-page__message","subtitle2-medium"],[3,"invert","value","formControl",4,"ngFor","ngForOf"],["icon","warning","bocc-theme","alert",3,"visible",4,"ngIf"],[1,"mbo-cel-to-cel-send-account-page__footer"],["id","btn_cel-to-cel-send-account_submit","bocc-button","raised",3,"disabled","click"],[3,"invert","value","formControl"],[1,"mbo-cel-to-cel-send-account-page__radiobutton"],[3,"src"],[1,"smalltext-medium"],["icon","warning","bocc-theme","alert",3,"visible"]],template:function(o,c){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," Selecciona la entidad destino "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,M,5,5,"bocc-card-radiobutton",5),e.\u0275\u0275template(7,y,5,1,"bocc-alert",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return c.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Continuar"),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",c.backAction)("rightAction",c.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngForOf",c.options),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",c.alertVisible),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",c.accountControl.invalid))},dependencies:[s.NgForOf,s.NgIf,A.J,x.D,S.B,T.P],styles:["/*!\n * MBO CelToCelSendAccount Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 04/Nov/2022\n * Updated: 09/Feb/2024\n*/mbo-cel-to-cel-send-account-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__message{color:var(--color-carbon-darker-1000)}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__radiobutton{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__radiobutton img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__footer{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-cel-to-cel-send-account-page .mbo-cel-to-cel-send-account-page__footer button{width:100%}\n"],encapsulation:2}),t})(),I=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[s.CommonModule,b.RouterModule.forChild([{path:"",component:P}]),l.Jx,l.DO,l.B4,l.P8]}),t})()}}]);