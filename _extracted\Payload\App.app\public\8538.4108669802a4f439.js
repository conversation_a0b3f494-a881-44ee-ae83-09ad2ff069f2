(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8538],{48538:(e,d,t)=>{t.r(d),t.d(d,{MboSecurityActivateProductsModule:()=>M});var n=t(17007),u=t(78007),a=t(99877);const l=[{path:"",redirectTo:"creditcard",pathMatch:"full"},{path:"creditcard",loadChildren:()=>t.e(7225).then(t.bind(t,37225)).then(o=>o.MboActivateCreditCardPageModule)}];let M=(()=>{class o{}return o.\u0275fac=function(c){return new(c||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[n.CommonModule,u.RouterModule.forChild(l)]}),o})()}}]);