(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6837],{16837:(I,f,r)=>{r.r(f),r.d(f,{MboTransferTrustfundResultPageModule:()=>C});var c=r(17007),m=r(78007),l=r(79798),p=r(15861),t=r(99877),d=r(39904),g=r(95437),s=r(87903),T=r(53113);function h(e){const{isError:o,message:n}=e;return{animation:(0,s.jY)(e),title:o?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:n}}function v({isError:e}){return e?[(0,s.wT)("Finalizar","finish","outline"),(0,s.wT)("Volver a intentar","retry")]:[(0,s.wT)("Hacer otra transferencia","retry","outline"),(0,s.wT)("Finalizar","finish")]}var M=r(95137),R=r(10464),P=r(78021),y=r(16442);function S(e,o){if(1&e&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"mbo-header-result",5),t.\u0275\u0275elementEnd()),2&e){const n=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("rightActions",n.rightActions)}}let E=(()=>{class e{constructor(n,a,i){this.ref=n,this.mboProvider=a,this.managerTransfer=i,this.requesting=!0,this.template=d.$d,this.rightActions=[{id:"btn_transfer-trustfund-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfer-trustfund-result-page_template"),this.initializatedTransaction()}onAction(n){this.mboProvider.navigation.next("finish"===n?d.Z6.CUSTOMER.PRODUCTS.HOME:d.Z6.TRANSFERS.TRUSTFUND.SOURCE)}initializatedTransaction(){var n=this;return(0,p.Z)(function*(){(yield n.managerTransfer.send()).when({success:a=>{n.template=function b(e){const{dateFormat:o,timeFormat:n}=new T.ou,{status:a,trustfund:i}=e,u=[(0,s.SP)("DESTINO",i.destination.nickname,i.destination.number),(0,s._f)("SUMA DE",i.amount)];return i.note&&u.push((0,s.SP)("DESCRIPCI\xd3N",i.note.description,i.note.reference)),u.push((0,s.cZ)(o,n)),{actions:v(a),error:a.isError,header:h(a),informations:u,skeleton:!1}}(a)}},()=>{n.requesting=!1,n.managerTransfer.reset()})})()}}return e.\u0275fac=function(n){return new(n||e)(t.\u0275\u0275directiveInject(t.ElementRef),t.\u0275\u0275directiveInject(g.ZL),t.\u0275\u0275directiveInject(M.M))},e.\u0275cmp=t.\u0275\u0275defineComponent({type:e,selectors:[["mbo-transfer-trustfund-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfer-trustfund-result-page__content","mbo-page__scroller"],["class","mbo-transfer-trustfund-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfer-trustfund-result-page__body"],["id","crd_transfer-trustfund-result-page_template",3,"template","action"],[1,"mbo-transfer-trustfund-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(n,a){1&n&&(t.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),t.\u0275\u0275template(2,S,2,1,"div",1),t.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),t.\u0275\u0275listener("action",function(u){return a.onAction(u)}),t.\u0275\u0275elementEnd()()()()),2&n&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",!a.requesting),t.\u0275\u0275advance(2),t.\u0275\u0275property("template",a.template))},dependencies:[c.NgIf,R.K,P.c,y.u],styles:["/*!\n * MBO TransferTrustfundResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 12/Jul/2022\n * Updated: 08/Ene/2024\n*/mbo-transfer-trustfund-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-trustfund-result-page .mbo-transfer-trustfund-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfer-trustfund-result-page .mbo-transfer-trustfund-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),e})(),C=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=t.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=t.\u0275\u0275defineInjector({imports:[c.CommonModule,m.RouterModule.forChild([{path:"",component:E}]),l.KI,l.cN,l.tu]}),e})()}}]);