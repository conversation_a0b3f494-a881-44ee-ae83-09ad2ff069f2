(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4603],{95017:(D,m,d)=>{d.r(m),d.d(m,{ArrayDataSource:()=>E,DataSource:()=>g,SelectionModel:()=>C,UniqueSelectionDispatcher:()=>y,_DisposeViewRepeaterStrategy:()=>S,_RecycleViewRepeaterStrategy:()=>w,_VIEW_REPEATER_STRATEGY:()=>T,getMultipleValuesInSingleSelectionError:()=>V,isDataSource:()=>p});var u=d(42168),f=d(99877);class g{}function p(n){return n&&"function"==typeof n.connect&&!(n instanceof u.ConnectableObservable)}class E extends g{constructor(e){super(),this._data=e}connect(){return(0,u.isObservable)(this._data)?this._data:(0,u.of)(this._data)}disconnect(){}}class S{applyChanges(e,t,s,h,i){e.forEachOperation((c,a,r)=>{let l,o;if(null==c.previousIndex){const _=s(c,a,r);l=t.createEmbeddedView(_.templateRef,_.context,_.index),o=1}else null==r?(t.remove(a),o=3):(l=t.get(a),t.move(l,r),o=2);i&&i({context:l?.context,operation:o,record:c})})}detach(){}}class w{constructor(){this.viewCacheSize=20,this._viewCache=[]}applyChanges(e,t,s,h,i){e.forEachOperation((c,a,r)=>{let l,o;null==c.previousIndex?(l=this._insertView(()=>s(c,a,r),r,t,h(c)),o=l?1:0):null==r?(this._detachAndCacheView(a,t),o=3):(l=this._moveView(a,r,t,h(c)),o=2),i&&i({context:l?.context,operation:o,record:c})})}detach(){for(const e of this._viewCache)e.destroy();this._viewCache=[]}_insertView(e,t,s,h){const i=this._insertViewFromCache(t,s);if(i)return void(i.context.$implicit=h);const c=e();return s.createEmbeddedView(c.templateRef,c.context,c.index)}_detachAndCacheView(e,t){const s=t.detach(e);this._maybeCacheView(s,t)}_moveView(e,t,s,h){const i=s.get(e);return s.move(i,t),i.context.$implicit=h,i}_maybeCacheView(e,t){if(this._viewCache.length<this.viewCacheSize)this._viewCache.push(e);else{const s=t.indexOf(e);-1===s?e.destroy():t.remove(s)}}_insertViewFromCache(e,t){const s=this._viewCache.pop();return s&&t.insert(s,e),s||null}}class C{get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}constructor(e=!1,t,s=!0,h){this._multiple=e,this._emitChanges=s,this.compareWith=h,this._selection=new Set,this._deselectedToEmit=[],this._selectedToEmit=[],this.changed=new u.Subject,t&&t.length&&(e?t.forEach(i=>this._markSelected(i)):this._markSelected(t[0]),this._selectedToEmit.length=0)}select(...e){this._verifyValueAssignment(e),e.forEach(s=>this._markSelected(s));const t=this._hasQueuedChanges();return this._emitChangeEvent(),t}deselect(...e){this._verifyValueAssignment(e),e.forEach(s=>this._unmarkSelected(s));const t=this._hasQueuedChanges();return this._emitChangeEvent(),t}setSelection(...e){this._verifyValueAssignment(e);const t=this.selected,s=new Set(e);e.forEach(i=>this._markSelected(i)),t.filter(i=>!s.has(i)).forEach(i=>this._unmarkSelected(i));const h=this._hasQueuedChanges();return this._emitChangeEvent(),h}toggle(e){return this.isSelected(e)?this.deselect(e):this.select(e)}clear(e=!0){this._unmarkAll();const t=this._hasQueuedChanges();return e&&this._emitChangeEvent(),t}isSelected(e){return this._selection.has(this._getConcreteValue(e))}isEmpty(){return 0===this._selection.size}hasValue(){return!this.isEmpty()}sort(e){this._multiple&&this.selected&&this._selected.sort(e)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(e){e=this._getConcreteValue(e),this.isSelected(e)||(this._multiple||this._unmarkAll(),this.isSelected(e)||this._selection.add(e),this._emitChanges&&this._selectedToEmit.push(e))}_unmarkSelected(e){e=this._getConcreteValue(e),this.isSelected(e)&&(this._selection.delete(e),this._emitChanges&&this._deselectedToEmit.push(e))}_unmarkAll(){this.isEmpty()||this._selection.forEach(e=>this._unmarkSelected(e))}_verifyValueAssignment(e){}_hasQueuedChanges(){return!(!this._deselectedToEmit.length&&!this._selectedToEmit.length)}_getConcreteValue(e){if(this.compareWith){for(let t of this._selection)if(this.compareWith(e,t))return t;return e}return e}}function V(){return Error("Cannot pass multiple values into SelectionModel with single-value mode.")}let y=(()=>{class n{constructor(){this._listeners=[]}notify(t,s){for(let h of this._listeners)h(t,s)}listen(t){return this._listeners.push(t),()=>{this._listeners=this._listeners.filter(s=>t!==s)}}ngOnDestroy(){this._listeners=[]}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275prov=f.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();const T=new f.InjectionToken("_ViewRepeater")}}]);