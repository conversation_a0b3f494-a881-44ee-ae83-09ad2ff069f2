(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9961],{29961:(E,l,n)=>{n.r(l),n.d(l,{MboPaymentQrModule:()=>h});var a=n(17007),d=n(78007),t=n(99877);const M=[{path:"",redirectTo:"scan",pathMatch:"full"},{path:"scan",loadChildren:()=>n.e(5282).then(n.bind(n,65282)).then(o=>o.MboPaymentQrScanPageModule)},{path:"confirmation",loadChildren:()=>n.e(2612).then(n.bind(n,62612)).then(o=>o.MboPaymentQrConfirmationPageModule)},{path:"source",loadChildren:()=>n.e(303).then(n.bind(n,50303)).then(o=>o.MboPaymentQrSourcePageModule)},{path:"cancel",loadChildren:()=>n.e(8337).then(n.bind(n,78337)).then(o=>o.MboPaymentQrCancelPageModule)},{path:"result",loadChildren:()=>n.e(3899).then(n.bind(n,83899)).then(o=>o.MboPaymentQrResultPageModule)}];let h=(()=>{class o{}return o.\u0275fac=function(m){return new(m||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[a.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);