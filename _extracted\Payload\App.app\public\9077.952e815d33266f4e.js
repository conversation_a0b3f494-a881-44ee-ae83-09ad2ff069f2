(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9077],{39077:(k,m,i)=>{i.r(m),i.d(m,{MboTransferGenericConfirmationPageModule:()=>z});var d=i(17007),p=i(78007),f=i(79798),o=i(30263),b=i(83651),g=i(15861),v=i(39904),h=i(95437),C=i(98699),u=i(99013),y=i(18767),e=i(99877),T=i(10464),I=i(48774),P=i(17941),S=i(66613),A=i(65467),M=i(45542),E=i(16450);function N(r,a){if(1&r&&e.\u0275\u0275element(0,"bocc-card-summary",16),2&r){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("title",null==n.note?null:n.note.description)("detail",null==n.note?null:n.note.reference)("actions",n.noteActions)}}function G(r,a){if(1&r&&(e.\u0275\u0275elementStart(0,"bocc-alert",17)(1,"bocc-skeleton-text",18),e.\u0275\u0275text(2," Costo de la transacci\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"boccCurrencyCop"),e.\u0275\u0275elementEnd()()()),2&r){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!n.cost),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind2(5,3,null==n.cost?null:n.cost.amount,!1)," pesos")}}function x(r,a){1&r&&(e.\u0275\u0275elementStart(0,"bocc-alert",19),e.\u0275\u0275text(1," Lo sentimos, ocurri\xf3 un error consultando el costo de tu transacci\xf3n. "),e.\u0275\u0275elementEnd()),2&r&&e.\u0275\u0275property("visible",!0)}function D(r,a){if(1&r){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",20)(1,"button",21),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const c=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(c.onNote())}),e.\u0275\u0275elementStart(2,"span"),e.\u0275\u0275text(3,"Agregar descripci\xf3n"),e.\u0275\u0275elementEnd()()()}}const s=v.Z6.TRANSFERS.GENERIC;let j=(()=>{class r{constructor(n,t,c,l,F,R){this.bottomSheetService=n,this.confirmationService=t,this.mboProvider=c,this.requestConfiguration=l,this.managerTransfer=F,this.cancelProvider=R,this.requesting=!1,this.requiredCost=!1,this.costError=!1,this.backAction={id:"btn_transfer-generic-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(s.AMOUNT)}},this.cancelAction={id:"btn_transfer-generic-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_transfer-generic-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back("unregistered"===this.transfer?.destination.type?s.UNREGISTERED:s.DESTINATION)}}],this.amountActions=[{id:"btn_transfer-generic-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(s.AMOUNT)}}],this.noteActions=[{id:"btn_transfer-generic-confirmation_remove-description",icon:"remove",click:()=>{this.removeNote()}},{id:"btn_transfer-generic-confirmation_edit-description",icon:"edit-pencil",click:()=>{this.openNote()}}]}ngOnInit(){this.initializatedConfiguration()}onNote(){this.openNote()}onSubmit(){this.mboProvider.navigation.next(s.RESULT)}initializatedConfiguration(){var n=this;return(0,g.Z)(function*(){(yield n.requestConfiguration.confirmation()).when({success:({transfer:t,cost$:c})=>{n.transfer=t,n.note=t.note,c&&(n.requiredCost=!0,n.requesting=!0,c.then(l=>{n.cost=l}).catch(()=>{n.requiredCost=!1,n.costError=!0}).finally(()=>{n.requesting=!1}))}})})()}openNote(){const n=this.bottomSheetService.create(f.bL,{componentProps:{title:"A\xf1ade una descripci\xf3n a tu transferencia",initialValue:this.note}});n.open(),(0,C.catchPromise)(n.waiting().then(t=>{this.managerTransfer.setNote(t).when({success:()=>{this.note=t}})}))}removeNote(){this.confirmationService.execute({title:"\xbfBorrar descripci\xf3n?",message:"\xbfEsta seguro de borrar la descripci\xf3n agregada en la transferencia?",accept:{label:"Borrar descripci\xf3n",click:()=>{this.managerTransfer.removeNote().when({success:()=>{this.note=void 0}})}},decline:{label:"Cancelar"}})}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(o.fG),e.\u0275\u0275directiveInject(o.$e),e.\u0275\u0275directiveInject(h.ZL),e.\u0275\u0275directiveInject(u.ow),e.\u0275\u0275directiveInject(u.Al),e.\u0275\u0275directiveInject(y.S))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfer-generic-confirmation-page"]],decls:20,vars:15,consts:[[1,"mbo-transfer-generic-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfer-generic-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfer-generic-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],["header","DESCRIPCI\xd3N",3,"title","detail","actions",4,"ngIf"],["icon","bell",3,"visible",4,"ngIf"],["icon","warning","bocc-theme","alert",3,"visible",4,"ngIf"],["class","bocc-card__footer",4,"ngIf"],[1,"mbo-transfer-generic-confirmation-page__footer"],["id","btn_transfer-generic-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"disabled","click"],["header","DESCRIPCI\xd3N",3,"title","detail","actions"],["icon","bell",3,"visible"],[3,"active"],["icon","warning","bocc-theme","alert",3,"visible"],[1,"bocc-card__footer"],["id","btn_transfer-generic-confirmation_add-note","bocc-button","note","prefixIcon","edit-note",3,"click"]],template:function(n,t){1&n&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),e.\u0275\u0275text(7," \xbfDeseas transferirle a? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",6),e.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),e.\u0275\u0275template(12,N,1,3,"bocc-card-summary",10),e.\u0275\u0275template(13,G,6,6,"bocc-alert",11),e.\u0275\u0275template(14,x,2,1,"bocc-alert",12),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(15,D,4,0,"div",13),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(16,"div",14)(17,"button",15),e.\u0275\u0275listener("click",function(){return t.onSubmit()}),e.\u0275\u0275elementStart(18,"span"),e.\u0275\u0275text(19,"Transferir"),e.\u0275\u0275elementEnd()()()()),2&n&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",t.backAction)("rightAction",t.cancelAction),e.\u0275\u0275advance(6),e.\u0275\u0275property("title",null==t.transfer?null:t.transfer.destinationName)("subtitle",null==t.transfer?null:t.transfer.destination.number)("detail",null==t.transfer?null:t.transfer.destination.bankName)("actions",t.destinationActions),e.\u0275\u0275advance(1),e.\u0275\u0275property("amount",null==t.transfer?null:t.transfer.amount)("actions",t.amountActions),e.\u0275\u0275advance(1),e.\u0275\u0275property("title",null==t.transfer?null:t.transfer.source.nickname)("subtitle",null==t.transfer?null:t.transfer.source.number),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.note),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.requiredCost),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.costError),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.note),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",t.requesting))},dependencies:[d.NgIf,T.K,I.J,P.D,S.B,A.D,M.P,E.f],styles:["/*!\n * MBO TransferGenericConfirmation Page\n * v1.0.2\n * Author: MB Frontend Developers\n * Created: 26/Jun/2022\n * Updated: 16/Jun/2024\n*/mbo-transfer-generic-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-generic-confirmation-page .mbo-transfer-generic-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-generic-confirmation-page .mbo-transfer-generic-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfer-generic-confirmation-page .mbo-transfer-generic-confirmation-page__body bocc-alert{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-transfer-generic-confirmation-page .mbo-transfer-generic-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-generic-confirmation-page .mbo-transfer-generic-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),r})(),z=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,p.RouterModule.forChild([{path:"",component:j}]),f.KI,o.Jx,o.DM,o.B4,o.Dj,b.P6,o.P8,o.b6,o.oc]}),r})()}}]);