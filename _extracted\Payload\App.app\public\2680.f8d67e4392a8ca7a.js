(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2680],{92680:(f,s,n)=>{n.r(s),n.d(s,{SplashScreenWeb:()=>_});var t=n(15861),u=n(17737);class _ extends u.WebPlugin{show(e){return(0,t.Z)(function*(){})()}hide(e){return(0,t.Z)(function*(){})()}}},15861:(f,s,n)=>{function t(c,_,i,e,d,l,o){try{var a=c[l](o),r=a.value}catch(p){return void i(p)}a.done?_(r):Promise.resolve(r).then(e,d)}function u(c){return function(){var _=this,i=arguments;return new Promise(function(e,d){var l=c.apply(_,i);function o(r){t(l,e,d,o,a,"next",r)}function a(r){t(l,e,d,o,a,"throw",r)}o(void 0)})}}n.d(s,{Z:()=>u})}}]);