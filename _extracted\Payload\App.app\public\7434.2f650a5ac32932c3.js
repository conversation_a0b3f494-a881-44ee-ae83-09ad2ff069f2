(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7434],{67434:(h,d,r)=>{r.r(d),r.d(d,{LocalNotificationsWeb:()=>c});var o=r(15861),f=r(17737);class c extends f.WebPlugin{constructor(){super(...arguments),this.pending=[],this.deliveredNotifications=[],this.hasNotificationSupport=()=>{if(!("Notification"in window)||!Notification.requestPermission)return!1;if("granted"!==Notification.permission)try{new Notification("")}catch(i){if("TypeError"==i.name)return!1}return!0}}getDeliveredNotifications(){var i=this;return(0,o.Z)(function*(){const t=[];for(const n of i.deliveredNotifications){const e={title:n.title,id:parseInt(n.tag),body:n.body};t.push(e)}return{notifications:t}})()}removeDeliveredNotifications(i){var t=this;return(0,o.Z)(function*(){for(const n of i.notifications){const e=t.deliveredNotifications.find(s=>s.tag===String(n.id));e?.close(),t.deliveredNotifications=t.deliveredNotifications.filter(()=>!e)}})()}removeAllDeliveredNotifications(){var i=this;return(0,o.Z)(function*(){for(const t of i.deliveredNotifications)t.close();i.deliveredNotifications=[]})()}createChannel(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}deleteChannel(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}listChannels(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}schedule(i){var t=this;return(0,o.Z)(function*(){if(!t.hasNotificationSupport())throw t.unavailable("Notifications not supported in this browser.");for(const n of i.notifications)t.sendNotification(n);return{notifications:i.notifications.map(n=>({id:n.id}))}})()}getPending(){var i=this;return(0,o.Z)(function*(){return{notifications:i.pending}})()}registerActionTypes(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}cancel(i){var t=this;return(0,o.Z)(function*(){t.pending=t.pending.filter(n=>!i.notifications.find(e=>e.id===n.id))})()}areEnabled(){var i=this;return(0,o.Z)(function*(){const{display:t}=yield i.checkPermissions();return{value:"granted"===t}})()}changeExactNotificationSetting(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}checkExactNotificationSetting(){var i=this;return(0,o.Z)(function*(){throw i.unimplemented("Not implemented on web.")})()}requestPermissions(){var i=this;return(0,o.Z)(function*(){if(!i.hasNotificationSupport())throw i.unavailable("Notifications not supported in this browser.");return{display:i.transformNotificationPermission(yield Notification.requestPermission())}})()}checkPermissions(){var i=this;return(0,o.Z)(function*(){if(!i.hasNotificationSupport())throw i.unavailable("Notifications not supported in this browser.");return{display:i.transformNotificationPermission(Notification.permission)}})()}transformNotificationPermission(i){switch(i){case"granted":return"granted";case"denied":return"denied";default:return"prompt"}}sendPending(){var i;const t=[],n=(new Date).getTime();for(const e of this.pending)null!==(i=e.schedule)&&void 0!==i&&i.at&&e.schedule.at.getTime()<=n&&(this.buildNotification(e),t.push(e));this.pending=this.pending.filter(e=>!t.find(s=>s===e))}sendNotification(i){var t;if(null!==(t=i.schedule)&&void 0!==t&&t.at){const n=i.schedule.at.getTime()-(new Date).getTime();return this.pending.push(i),void setTimeout(()=>{this.sendPending()},n)}this.buildNotification(i)}buildNotification(i){const t=new Notification(i.title,{body:i.body,tag:String(i.id)});return t.addEventListener("click",this.onClick.bind(this,i),!1),t.addEventListener("show",this.onShow.bind(this,i),!1),t.addEventListener("close",()=>{this.deliveredNotifications=this.deliveredNotifications.filter(()=>!this)},!1),this.deliveredNotifications.push(t),t}onClick(i){this.notifyListeners("localNotificationActionPerformed",{actionId:"tap",notification:i})}onShow(i){this.notifyListeners("localNotificationReceived",i)}}},15861:(h,d,r)=>{function o(l,c,u,i,t,n,e){try{var s=l[n](e),a=s.value}catch(m){return void u(m)}s.done?c(a):Promise.resolve(a).then(i,t)}function f(l){return function(){var c=this,u=arguments;return new Promise(function(i,t){var n=l.apply(c,u);function e(a){o(n,i,t,e,s,"next",a)}function s(a){o(n,i,t,e,s,"throw",a)}e(void 0)})}}r.d(d,{Z:()=>f})}}]);