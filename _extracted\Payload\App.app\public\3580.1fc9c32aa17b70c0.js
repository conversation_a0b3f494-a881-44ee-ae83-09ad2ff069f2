(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3580],{37907:(Q,p,o)=>{o.d(p,{L:()=>u,m:()=>c});var f=o(15861),m=o(87956),y=o(53113),i=o(98699);class h{constructor(d,e,t){this.source=d,this.destination=e,this.amount=t}}function S(r){return new h(r.source,r.destination,r.amount)}var U=o(71776),l=o(39904),E=o(87903),n=o(42168),C=o(84757),a=o(99877);let M=(()=>{class r{constructor(e){this.http=e}send(e){const t={hashCheckingAcct:e.destination.id,hashLoanAcct:e.source.id,amount:String(e.amount)};return(0,n.firstValueFrom)(this.http.post(l.bV.TRANSACTIONS.CREDIT_QUOTA,t).pipe((0,C.map)(s=>(0,E.l1)(s,"SUCCESS")))).catch(s=>(0,E.rU)(s))}}return r.\u0275fac=function(e){return new(e||r)(a.\u0275\u0275inject(U.HttpClient))},r.\u0275prov=a.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var I=o(20691);let P=(()=>{class r extends I.Store{constructor(){super({confirmation:!1,fromCustomer:!1})}setSource(e,t=!1){this.reduce(s=>({...s,source:e,fromCustomer:t}))}getSource(){return this.select(({source:e})=>e)}itIsFromCustomer(){return this.select(({fromCustomer:e})=>e)}setDestination(e){this.reduce(t=>({...t,destination:e}))}setAmount(e){this.reduce(t=>({...t,amount:e}))}selectForAmount(){return this.select(({confirmation:e,amount:t,source:s})=>({amount:t,confirmation:e,source:s}))}itIsConfirmation(){return this.select(({confirmation:e})=>e)}}return r.\u0275fac=function(e){return new(e||r)},r.\u0275prov=a.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),u=(()=>{class r{constructor(e,t,s,v){this.products=e,this.eventBusService=t,this.repository=s,this.store=v}setSource(e){var t=this;return(0,f.Z)(function*(){try{return yield t.products.requestInformation(e),i.Either.success(t.store.setSource(e))}catch({message:s}){return i.Either.failure({message:s})}})()}setDestination(e){try{return i.Either.success(this.store.setDestination(e))}catch({message:t}){return i.Either.failure({message:t})}}setAmount(e){try{return i.Either.success(this.store.setAmount(e))}catch({message:t}){return i.Either.failure({message:t})}}reset(){try{const e=this.store.itIsFromCustomer(),t=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:e,source:t})}catch({message:e}){return i.Either.failure({message:e})}}send(){var e=this;return(0,f.Z)(function*(){const t=S(e.store.currentState),s=yield e.save(t);return e.eventBusService.emit(s.channel),i.Either.success({creditUseQuota:t,status:s})})()}save(e){try{return this.repository.send(e)}catch({message:t}){return Promise.resolve(y.LN.error(t))}}}return r.\u0275fac=function(e){return new(e||r)(a.\u0275\u0275inject(m.M5),a.\u0275\u0275inject(m.Yd),a.\u0275\u0275inject(M),a.\u0275\u0275inject(P))},r.\u0275prov=a.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var g=o(89148);let c=(()=>{class r{constructor(e,t,s){this.products=e,this.productService=t,this.store=s}source(){var e=this;return(0,f.Z)(function*(){try{const t=e.store.itIsConfirmation(),s=yield e.requestCredits();return i.Either.success({confirmation:t,products:s})}catch({message:t}){return i.Either.failure({message:t})}})()}destination(e){var t=this;return(0,f.Z)(function*(){try{const s=yield t.products.requestAccountsForTransfer(),v=yield t.requestCredits(),A=t.store.itIsConfirmation(),j=t.requestCredit(v,e);return i.Either.success({accounts:s,confirmation:A,products:v,source:j})}catch({message:s}){return i.Either.failure({message:s})}})()}amount(){var e=this;return(0,f.Z)(function*(){try{const{amount:t,confirmation:s,source:v}=e.store.selectForAmount(),A=yield e.requestSection(v);return i.Either.success({confirmation:s,section:A,source:v,value:t})}catch({message:t}){return i.Either.failure({message:t})}})()}confirmation(){try{const e=S(this.store.currentState);return i.Either.success({creditUseQuota:e})}catch({message:e}){return i.Either.failure({message:e})}}requestCredits(){return this.products.requestProducts([g.Gt.ResolvingCredit])}requestCredit(e,t){let s=this.store.getSource();return!s&&t&&(s=e.find(({id:v})=>t===v),this.store.setSource(s,!0)),s}requestSection(e){return this.productService.requestInformation(e).then(t=>t?.getSection(g.Av.LoanQuotaAvailable))}}return r.\u0275fac=function(e){return new(e||r)(a.\u0275\u0275inject(m.hM),a.\u0275\u0275inject(m.M5),a.\u0275\u0275inject(P))},r.\u0275prov=a.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},44793:(Q,p,o)=>{o.d(p,{Z:()=>U});var f=o(30263),m=o(39904),y=o(95437),i=o(37907),h=o(99877);let U=(()=>{class l{constructor(n,C,a){this.modalConfirmation=n,this.mboProvider=C,this.managerUseQuota=a}execute(n=!0){n?this.modalConfirmation.execute({title:"Cancelar transacci\xf3n",message:"\xbfEstas seguro que deseas cancelar la transferencia de cupo actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerUseQuota.reset().when({success:({fromCustomer:n,source:C})=>{n?this.mboProvider.navigation.back(m.Z6.CUSTOMER.PRODUCTS.INFO,{productId:C.id}):this.mboProvider.navigation.back(m.Z6.CUSTOMER.PRODUCTS.HOME)},failure:()=>{this.mboProvider.navigation.back(m.Z6.CUSTOMER.PRODUCTS.HOME)}})}}return l.\u0275fac=function(n){return new(n||l)(h.\u0275\u0275inject(f.$e),h.\u0275\u0275inject(y.ZL),h.\u0275\u0275inject(i.L))},l.\u0275prov=h.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})()},73580:(Q,p,o)=>{o.r(p),o.d(p,{MboCreditUseQuotaSourcePageModule:()=>P});var f=o(17007),m=o(78007),y=o(30263),i=o(79798),h=o(15861),S=o(39904),U=o(95437),l=o(37907),E=o(44793),n=o(99877),C=o(48774),a=o(13043);const M=S.Z6.TRANSFERS.GENERIC;let I=(()=>{class u{constructor(c,r,d,e){this.mboProvider=c,this.requestConfiguration=r,this.managerCreditQuota=d,this.cancelProvider=e,this.confirmation=!1,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_credit-use-quota-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(c){var r=this;return(0,h.Z)(function*(){(yield r.managerCreditQuota.setSource(c)).when({success:()=>{r.mboProvider.navigation.next(M.DESTINATION)}})})()}initializatedConfiguration(){var c=this;return(0,h.Z)(function*(){(yield c.requestConfiguration.source()).when({success:({confirmation:r,products:d})=>{c.products=d,c.confirmation=r}},()=>{c.requesting=!1})})()}}return u.\u0275fac=function(c){return new(c||u)(n.\u0275\u0275directiveInject(U.ZL),n.\u0275\u0275directiveInject(l.m),n.\u0275\u0275directiveInject(l.L),n.\u0275\u0275directiveInject(E.Z))},u.\u0275cmp=n.\u0275\u0275defineComponent({type:u,selectors:[["mbo-credit-use-quota-source-page"]],decls:6,vars:3,consts:[[1,"mbo-credit-use-quota-source-page__content"],[1,"mbo-credit-use-quota-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-credit-use-quota-source-page__body"],["title","DISPONIBLES PARA USAR CUPO",3,"skeleton","products","select"]],template:function(c,r){1&c&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-destination-selector",4),n.\u0275\u0275listener("select",function(e){return r.onProduct(e)}),n.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas enviar dinero hoy? "),n.\u0275\u0275elementEnd()()()),2&c&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("rightAction",r.cancelAction),n.\u0275\u0275advance(2),n.\u0275\u0275property("skeleton",r.requesting)("products",r.products))},dependencies:[C.J,a.e],styles:["/*!\n * MBO TransferGenericSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 25/Jun/2022\n * Updated: 06/Ene/2024\n*/mbo-transfer-generic-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-generic-source-page .mbo-transfer-generic-source-page__content{display:flex;flex-direction:column}mbo-transfer-generic-source-page .mbo-transfer-generic-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),u})(),P=(()=>{class u{}return u.\u0275fac=function(c){return new(c||u)},u.\u0275mod=n.\u0275\u0275defineNgModule({type:u}),u.\u0275inj=n.\u0275\u0275defineInjector({imports:[f.CommonModule,m.RouterModule.forChild([{path:"",component:I}]),y.Jx,i.eM]}),u})()}}]);