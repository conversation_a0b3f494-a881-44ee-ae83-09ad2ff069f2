(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9609],{39609:(y,d,e)=>{e.r(d),e.d(d,{MboTransfiyaTransferDestinationPageModule:()=>j});var f=e(17007),m=e(78007),s=e(30263),g=e(15861),o=e(24495),r=e(39904),h=e(95437),x=e(57544),C=e(20827),A=e(52484),u=e(42789),T=e(17698),P=e(73004),n=e(99877),I=e(48774),M=e(98853),z=e(2460),D=e(35641),E=e(23436),_=e(45542);function S(a,l){if(1&a){const t=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",14),n.\u0275\u0275element(1,"bocc-ballot",15),n.\u0275\u0275elementStart(2,"bocc-icon",16),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(t);const c=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(c.onRemoveContact())}),n.\u0275\u0275elementEnd()()}if(2&a){const t=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("title",t.phone.contact)("subtitle",t.phone.numberFormat)}}function O(a,l){1&a&&(n.\u0275\u0275elementStart(0,"div",17),n.\u0275\u0275element(1,"bocc-icon",18),n.\u0275\u0275elementStart(2,"span",19),n.\u0275\u0275text(3," N\xfamero de contacto debe iniciar con n\xfamero 3 "),n.\u0275\u0275elementEnd()())}const p=r.Z6.TRANSFERS.TRANSFIYA.TRANSFER;let F=(()=>{class a{constructor(t,i,c,b,v){this.mboProvider=t,this.contactsProvider=i,this.requestConfiguration=c,this.managerTransfiya=b,this.cancelTransfiya=v,this.confirmation=!1,this.backInvalid=!1,this.requesting=!0,this.requestingContact=!1,this.backAction={id:"btn_transfiya-transfer-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation||this.backInvalid,click:()=>{this.mboProvider.navigation.back(p.SOURCE)}},this.cancelAction={id:"btn_transfiya-transfer-destination_cancel",label:"Cancelar",click:()=>{this.cancelTransfiya.execute(this.confirmation)}},this.phoneControl=new x.FormControl({validators:[o.C1,A.O,(0,o.Dx)(10)]})}ngOnInit(){this.contactsProvider.subscribe(({number:t,name:i})=>this.phone=new u.Ap(t,i)),this.initializatedConfiguration()}get contactInvalid(){return!!this.phone&&!(0,C.lE)(this.phone.number)}get disabled(){return this.phone?this.contactInvalid:this.phoneControl.invalid}onContacts(){var t=this;return(0,g.Z)(function*(){t.requestingContact=!0,yield t.contactsProvider.open(),t.requestingContact=!1})()}onRemoveContact(){this.phone=void 0}onSubmit(){this.managerTransfiya.setContact(this.getTransfiyaPhone()).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?p.CONFIRMATION:p.AMOUNT)}})}initializatedConfiguration(){var t=this;return(0,g.Z)(function*(){(yield t.requestConfiguration.destination()).when({success:({confirmation:i,phone:c,product:b,products:v})=>{if(!b)return t.mboProvider.navigation.back(p.DESTINATION);if(t.confirmation=i,t.backInvalid=v.length<2,c){const{number:N,contact:R}=c;R?t.phone=c:t.phoneControl.setValue(N)}}},()=>{t.requesting=!1})})()}getTransfiyaPhone(){return this.phone||new u.Ap(this.phoneControl.value)}}return a.\u0275fac=function(t){return new(t||a)(n.\u0275\u0275directiveInject(h.ZL),n.\u0275\u0275directiveInject(h.i),n.\u0275\u0275directiveInject(T.ow),n.\u0275\u0275directiveInject(T.Pm),n.\u0275\u0275directiveInject(P.c))},a.\u0275cmp=n.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-transfer-destination-page"]],decls:19,vars:8,consts:[[1,"mbo-transfiya-transfer-destination-page__content"],[1,"mbo-transfiya-transfer-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfiya-transfer-destination-page__body"],[1,"mbo-transfiya-transfer-destination-page__selection"],[1,"mbo-transfiya-transfer-destination-page__message","subtitle2-medium"],["class","mbo-transfiya-transfer-destination-page__contact",4,"ngIf"],["class","mbo-transfiya-transfer-destination-page__error",4,"ngIf"],["elementId","txt_transfiya-transfer-destination_phone","label","Ingresa el n\xfamero de celular","placeholder","************","type","phone","prefix","+57",3,"hidden","formControl"],[1,"mbo-transfiya-transfer-destination-page__contacts"],[1,"mbo-transfiya-transfer-destination-page__info","body2-medium"],["id","btn_transfiya-transfer-destination_contacts","icon","user-contacts",3,"disabled","click"],[1,"mbo-transfiya-transfer-destination-page__footer"],["id","btn_transfiya-transfer-destination_submit","bocc-button","raised",3,"disabled","click"],[1,"mbo-transfiya-transfer-destination-page__contact"],["icon","mobile-contact",3,"title","subtitle"],["icon","close",3,"click"],[1,"mbo-transfiya-transfer-destination-page__error"],["icon","error"],[1,"caption-medium"]],template:function(t,i){1&t&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"p",5),n.\u0275\u0275text(6," \xbfA qui\xe9n deseas transferirle hoy? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(7,S,3,2,"div",6),n.\u0275\u0275template(8,O,4,0,"div",7),n.\u0275\u0275element(9,"bocc-growing-box",8),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"div",9)(11,"p",10),n.\u0275\u0275text(12," Tambi\xe9n puedes seleccionar uno de tus contactos "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(13,"bocc-card-category",11),n.\u0275\u0275listener("click",function(){return i.onContacts()}),n.\u0275\u0275text(14," Contactos del celular "),n.\u0275\u0275elementEnd()()()(),n.\u0275\u0275elementStart(15,"div",12)(16,"button",13),n.\u0275\u0275listener("click",function(){return i.onSubmit()}),n.\u0275\u0275elementStart(17,"span"),n.\u0275\u0275text(18,"Continuar"),n.\u0275\u0275elementEnd()()()),2&t&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",i.backAction)("rightAction",i.cancelAction),n.\u0275\u0275advance(5),n.\u0275\u0275property("ngIf",i.phone),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",i.contactInvalid),n.\u0275\u0275advance(1),n.\u0275\u0275property("hidden",i.phone)("formControl",i.phoneControl),n.\u0275\u0275advance(4),n.\u0275\u0275property("disabled",i.requestingContact),n.\u0275\u0275advance(3),n.\u0275\u0275property("disabled",i.disabled))},dependencies:[f.NgIf,I.J,M.s,z.Z,D.d,E.D,_.P],styles:["/*!\n * MBO TransfiyaTransferDestination Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 14/Jun/2022\n * Updated: 18/Jun/2024\n*/mbo-transfiya-transfer-destination-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x2);justify-content:space-between}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x24)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__selection{display:flex;flex-direction:column;row-gap:var(--sizing-x24)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__contacts{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__contact{position:relative;display:flex;width:100%;align-items:center;padding:var(--sizing-x4);box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__contact>bocc-icon{color:var(--color-carbon-lighter-700)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__error{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__error bocc-icon{font-size:var(--sizing-x8);width:var(--sizing-x8);height:var(--sizing-x8);color:var(--color-semantic-danger-700)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__error span{color:var(--color-carbon-lighter-700)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__message{color:var(--color-carbon-darker-1000)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__info{color:var(--color-carbon-darker-1000)}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-transfer-destination-page .mbo-transfiya-transfer-destination-page__footer button{width:100%}\n"],encapsulation:2}),a})(),j=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=n.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=n.\u0275\u0275defineInjector({imports:[f.CommonModule,m.RouterModule.forChild([{path:"",component:F}]),s.Jx,s.s,s.Zl,s.dH,s.D0,s.P8]}),a})()},20827:(y,d,e)=>{function m(o){return"3"===o.charAt(0)}e.d(d,{lE:()=>m}),e(87903)},52484:(y,d,e)=>{e.d(d,{O:()=>g,b:()=>o});var f=e(20827);const m=/^[a-z|A-Z|0-9|@|.]*$/,s=/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,g=r=>r&&!(0,f.lE)(r)?{id:"firstCharPhone",message:"Campo debe iniciar con n\xfamero 3"}:null,o=r=>!r||s.test(r)||m.test(r)?null:{id:"tagAvalOrKey",message:"Campo no cumple con los criterios para ser una llave"}}}]);