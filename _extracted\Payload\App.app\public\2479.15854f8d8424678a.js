(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2479],{21260:(H,M,o)=>{o.d(M,{$:()=>T,L:()=>h});var p=o(30263),y=o(39904),v=o(75652),e=o(99877),P=o(45542),_=o(35324);let x=(()=>{class i{constructor(c){this.mboProvider=c}ngBoccPortal(c){this.portal=c}onLocation(){this.mboProvider.openUrl(y.BA.GEOLOCATION)}onClose(){this.portal?.close()}}return i.\u0275fac=function(c){return new(c||i)(e.\u0275\u0275directiveInject(v.Z))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-document-error-modal"]],decls:12,vars:1,consts:[[1,"mbo-document-error-modal"],["src","assets/shared/logos/document-error.svg"],[1,"mbo-document-error-modal__body"],[1,"smalltext-medium"],[1,"body2-medium"],[3,"whatsapp"],[1,"mbo-document-error-modal__footer"],["id","btn_statement-error-modal_close","bocc-button","raised",3,"click"]],template:function(c,d){1&c&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementStart(2,"div",2)(3,"label",3),e.\u0275\u0275text(4," DOCUMENTO NO DISPONIBLE "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"p",4),e.\u0275\u0275text(6," Puedes solicitarlo mediante nuestro canal de Whatsapp o en cualquiera de nuestras l\xedneas de atenci\xf3n "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(7,"mbo-attention-lines-form",5),e.\u0275\u0275elementStart(8,"div",6)(9,"button",7),e.\u0275\u0275listener("click",function(){return d.onClose()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Entendido"),e.\u0275\u0275elementEnd()()()()),2&c&&(e.\u0275\u0275advance(7),e.\u0275\u0275property("whatsapp",!0))},dependencies:[P.P,_.V],styles:["mbo-document-error-modal{--pvt-img-width: 44rem;--pvt-row-spacing: var(--sizing-x12)}mbo-document-error-modal .mbo-document-error-modal{display:flex;flex-direction:column;row-gap:var(--pvt-row-spacing)}mbo-document-error-modal .mbo-document-error-modal img{align-self:center;width:var(--pvt-img-width)}mbo-document-error-modal .mbo-document-error-modal__body{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-document-error-modal .mbo-document-error-modal__body label{text-align:center}mbo-document-error-modal .mbo-document-error-modal__body p{color:var(--color-carbon-lighter-700);text-align:center}mbo-document-error-modal .mbo-document-error-modal__location{width:auto;align-self:center}mbo-document-error-modal .mbo-document-error-modal__footer{padding:var(--sizing-x4);box-sizing:border-box;border-top:1px solid var(--color-carbon-lighter-400)}mbo-document-error-modal .mbo-document-error-modal__footer button{width:100%}@media screen and (max-height: 600px){mbo-document-error-modal{--pvt-img-width: 40rem;--pvt-row-spacing: var(--sizing-x8)}}\n"],encapsulation:2}),i})();o(17007),o(79798);let T=(()=>{class i{constructor(c){this.errorModal=c.create(x)}execute(){this.errorModal.open()}}return i.\u0275fac=function(c){return new(c||i)(e.\u0275\u0275inject(p.iM))},i.\u0275prov=e.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),h=(()=>{class i{constructor(c,d){this.modalConfirmation=c,this.mboProvider=d}execute(){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/search-result-none.svg",title:"SIN PRODUCTOS FIDUCIARIOS",description:"\xa1Tranquilo! Est\xe1s a un clic de empezar a cumplir tus metas",message:'Da clic en "Quiero invertir" y crea tu inversi\xf3n',accept:{label:"Quiero invertir",click:()=>{this.mboProvider.openUrl(y.BA.TRUSTFUND)}},decline:{label:"Cancelar"}})}}return i.\u0275fac=function(c){return new(c||i)(e.\u0275\u0275inject(p.$e),e.\u0275\u0275inject(v.Z))},i.\u0275prov=e.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},88039:(H,M,o)=>{o.d(M,{Oz:()=>y,fN:()=>v,vA:()=>e});var p=o(29306);class y{constructor(_,x,E,C,S){this.number=_,this.type=x,this.name=E,this.nickname=C,this.bank=S}get description(){return this.nickname||this.name}}class v{constructor(_,x,E,C,S,T,h,i,t){this.uuid=_,this.date=x,this.source=E,this.destination=C,this.amount=S,this.type=T,this.status=h,this.category=i,this.approvedCode=t}get dateFormat(){return this.date.dateFormat}get timeFormat(){return this.date.timeFormat}}class e extends p.Ay{static empty(){return new e([],0,!0)}}},93637:(H,M,o)=>{o.d(M,{U:()=>a,a:()=>m});var p=o(15861),y=o(3372),v=o(87956),e=o(98699),P=o(71776),_=o(39904),x=o(42168),E=o(84757);const C={D:{color:"success",label:"Exitosa"},R:{color:"danger",label:"Fallida"},P:{color:"alert",label:"Enviada"}};var T=o(88039),h=o(29306),i=o(53113),t=o(33876);const c={color:"primary",label:"Desconocido"};var b=o(99877);const n={items:String(10),order:"DESC",orderField:"xferEffDt"};let g=(()=>{class r{constructor(s,l){this.http=s,this.eventBusService=l,this.eventBusService.subscribes(_.PU,()=>{this.history=void 0})}request(s){if(this.history)return Promise.resolve(this.history);const l=s||_.cC,{end:I,start:O}=l.getFormat();return(0,x.firstValueFrom)(this.remote({...n,page:"0",EndDt:I,StartDt:O}).pipe((0,E.tap)(F=>{F.range=l,this.history=F})))}refresh(s){const{end:l,start:I}=s.getFormat();return(0,x.firstValueFrom)(this.remote({...n,page:"0",EndDt:l,StartDt:I}).pipe((0,E.tap)(O=>{O.range=s,this.history=O})))}requestForUuid(s){return this.history?.requestForUuid(s)}nextPage(){var s=this;return(0,p.Z)(function*(){if(!s.history)return s.request().then(({collection:O})=>O);const{end:l,start:I}=s.history.range.getFormat();return(0,x.firstValueFrom)(s.remote({...n,page:s.history.currentPage.toString(),EndDt:l,StartDt:I}).pipe((0,E.map)(({collection:O})=>(s.history.merge(O),s.history.collection))))})()}remote(s){return this.http.get(_.bV.TRANSFERS.HISTORY,{params:{...s}}).pipe((0,E.map)(({content:l,totalPage:I})=>new T.vA(l.map(O=>function d(r){return new T.fN((0,t.v4)(),new i.ou(r.xferEffDt),new T.Oz(r.fromAcctId,r.fromAcctType,r.fromAcctName,r.fromNickName,new h.Br(r.fromBankId,r.fromBankName)),new T.Oz(r.toAcctId,r.toAcctType,r.toAcctName,r.toNickName,new h.Br(r.toBankId,r.toBankName)),+r.amt,r.trnType,C[r.xferStatusDesc]||c,r.trnReccategory,r.approvalId)}(O)),I)),(0,E.catchError)(l=>{if(this.history)return(0,x.of)(T.vA.empty());throw l}))}}return r.\u0275fac=function(s){return new(s||r)(b.\u0275\u0275inject(P.HttpClient),b.\u0275\u0275inject(v.Yd))},r.\u0275prov=b.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),m=(()=>{class r{constructor(s,l,I){this.histories=s,this.products=l,this.preferencesService=I}transfers(){var s=this;return(0,p.Z)(function*(){try{const l=yield s.preferencesService.requestBoolean(y.M.TagAval),I=s.histories.request();return e.Either.success({enabledTagAval:l,history$:I})}catch({message:l}){return e.Either.failure({message:l})}})()}trustfunds(){var s=this;return(0,p.Z)(function*(){try{const l=yield s.products.requestTrustfunds();return l.length?e.Either.success(l):e.Either.failure({message:"Da clic en <b>Quiero invertir</b> y crea tu inversi\xf3n",value:!1})}catch{return e.Either.failure({message:"Ocurrio un error al tratar de consultar tus productos fiduciarios, por favor intente m\xe1s tarde",value:!0})}})()}}return r.\u0275fac=function(s){return new(s||r)(b.\u0275\u0275inject(g),b.\u0275\u0275inject(v.hM),b.\u0275\u0275inject(v.yW))},r.\u0275prov=b.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),a=(()=>{class r{constructor(s){this.histories=s}firstPage(){var s=this;return(0,p.Z)(function*(){try{return e.Either.success(yield s.histories.request())}catch({message:l}){return e.Either.failure({message:l})}})()}nextPage(){var s=this;return(0,p.Z)(function*(){try{return e.Either.success(yield s.histories.nextPage())}catch({message:l}){return e.Either.failure({message:l})}})()}refresh(s){var l=this;return(0,p.Z)(function*(){try{return e.Either.success(yield l.histories.refresh(s))}catch({message:I}){return e.Either.failure({message:I})}})()}historyForUuid(s){try{const l=this.histories.requestForUuid(s);return l?e.Either.success(l):e.Either.failure()}catch({message:l}){return e.Either.failure({message:l})}}}return r.\u0275fac=function(s){return new(s||r)(b.\u0275\u0275inject(g))},r.\u0275prov=b.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},83991:(H,M,o)=>{o.d(M,{By:()=>P,$J:()=>S,Yh:()=>g,HV:()=>m}),o(26060);var y=o(17007),v=o(30263),e=o(99877);let P=(()=>{class a{}return a.\u0275fac=function(f){return new(f||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[y.CommonModule,v.vB]}),a})();o(56921);let x=(()=>{class a{}return a.\u0275fac=function(f){return new(f||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[y.CommonModule,v.vB,v.Zl,v.Qg]}),a})();o(46165);var C=o(79798);let S=(()=>{class a{}return a.\u0275fac=function(f){return new(f||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[y.CommonModule,x,C.Aj,v.P8]}),a})();var T=o(8834),h=o(87956),i=o(40914),t=o(2460),c=o(45542);const{MIN_TRUSTFUND:d}=i.r;let g=(()=>{class a{constructor(f){this.timezoneService=f,this.balance=(0,T.b)({value:d}),this.hidden=!0}ngOnInit(){this.timezoneService.request().then(({datetime:f})=>this.checkTimeTrustfund(f)).catch(()=>this.checkTimeTrustfund(new Date))}ngBoccPortal(f){this.portal=f}onCancel(){this.portal?.close()}onSubmit(){this.portal?.send({action:"next"}),this.portal?.close()}checkTimeTrustfund(f){const s=f.getHours();this.hidden=s<7||s>=19}}return a.\u0275fac=function(f){return new(f||a)(e.\u0275\u0275directiveInject(h.By))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfer-trustfund-bluescreen"]],decls:19,vars:2,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfer-trustfund-bluescreen_cancel","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfer-trustfund-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"hidden","click"]],template:function(f,s){1&f&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4,"\xbfC\xf3mo funciona?"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Las transferencias desde y hacia la fiduciaria de Occidente estan habilitadas de 7:00 am a 7:00 pm "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9," No se permiten transferencias entre una Occirenta a otra. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(12,"div",5)(13,"button",6),e.\u0275\u0275listener("click",function(){return s.onCancel()}),e.\u0275\u0275elementStart(14,"span"),e.\u0275\u0275text(15,"Cancelar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",7),e.\u0275\u0275listener("click",function(){return s.onSubmit()}),e.\u0275\u0275elementStart(17,"span"),e.\u0275\u0275text(18,"Continuar"),e.\u0275\u0275elementEnd()()()),2&f&&(e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1(" Debes conservar por lo menos $",s.balance," pesos en la Occirenta. "),e.\u0275\u0275advance(5),e.\u0275\u0275property("hidden",s.hidden))},dependencies:[t.Z,c.P],styles:["mbo-transfer-trustfund-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),a})(),m=(()=>{class a{}return a.\u0275fac=function(f){return new(f||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[y.CommonModule,v.Zl,v.P8]}),a})()},26060:(H,M,o)=>{o.d(M,{R:()=>T});var p=o(39904),y=o(95437),e=(o(88039),o(99877)),_=o(17007),E=o(90521);function C(h,i){if(1&h){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),e.\u0275\u0275listener("event",function(d){e.\u0275\u0275restoreView(t);const b=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(b.onEventComponent(d))}),e.\u0275\u0275elementEnd()()}if(2&h){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("title",null==t.history?null:t.history.dateFormat)("statusLabel",null==t.history?null:t.history.status.label)("statusColor",null==t.history?null:t.history.status.color)("subtitle",null==t.history?null:t.history.destination.description)("number",null==t.history?null:t.history.destination.number)("description",null==t.history?null:t.history.destination.bank.name)("amount",null==t.history?null:t.history.amount)}}function S(h,i){1&h&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275element(1,"bocc-card-information",5),e.\u0275\u0275elementEnd()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}let T=(()=>{class h{constructor(t){this.mboProvider=t,this.skeleton=!1}onEventComponent(t){"component"===t&&this.history&&this.mboProvider.navigation.next(p.Z6.TRANSFERS.HISTORY_INFORMATION,{uuid:this.history.uuid,redirect:"history"})}}return h.\u0275fac=function(t){return new(t||h)(e.\u0275\u0275directiveInject(y.ZL))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-transfer-history-card"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfer-history-card",4,"ngIf"],["class","mbo-transfer-history-card__skeleton",4,"ngIf"],[1,"mbo-transfer-history-card"],[3,"title","statusLabel","statusColor","subtitle","number","description","amount","event"],[1,"mbo-transfer-history-card__skeleton"],[3,"skeleton"]],template:function(t,c){1&t&&(e.\u0275\u0275template(0,C,2,7,"div",0),e.\u0275\u0275template(1,S,2,1,"div",1)),2&t&&(e.\u0275\u0275property("ngIf",!c.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",c.skeleton))},dependencies:[_.NgIf,E.v],styles:["mbo-transfer-history-card{position:relative;width:100%;display:block}mbo-transfer-history-card .mbo-transfer-history-card{border-bottom:var(--border-1-lighter-300)}mbo-transfer-history-card .mbo-transfer-history-card .bocc-card-information__body{padding:var(--sizing-x6) var(--sizing-x4)}\n"],encapsulation:2}),h})()},56921:(H,M,o)=>{o.d(M,{d:()=>c});var p=o(39904),y=o(95437),e=(o(88039),o(99877)),_=o(17007),E=o(90521),C=o(2460),S=o(55944);function T(d,b){1&d&&e.\u0275\u0275element(0,"bocc-icon",14)}function h(d,b){1&d&&(e.\u0275\u0275elementStart(0,"div",15),e.\u0275\u0275element(1,"bocc-icon",16),e.\u0275\u0275elementStart(2,"label",17),e.\u0275\u0275text(3,"Repetir"),e.\u0275\u0275elementEnd()())}function i(d,b){if(1&d){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",2)(1,"div",3),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.onComponent())}),e.\u0275\u0275elementStart(2,"div",4)(3,"label",5),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",6),e.\u0275\u0275listener("click",function(m){e.\u0275\u0275restoreView(n);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.onStatus(m))}),e.\u0275\u0275template(6,T,1,0,"bocc-icon",7),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(9,"label",8),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"label",9),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"label",10),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",11),e.\u0275\u0275element(16,"bocc-amount",12),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(17,h,4,0,"div",13),e.\u0275\u0275elementEnd()}if(2&d){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",null==n.history?null:n.history.dateFormat," "),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("active",n.active)("pending",n.isPending),e.\u0275\u0275attribute("bocc-theme",null==n.history||null==n.history.status?null:n.history.status.color),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.isPending),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(null==n.history?null:n.history.status.label),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",null==n.history||null==n.history.destination?null:n.history.destination.description," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",null==n.history||null==n.history.destination?null:n.history.destination.number," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",null==n.history||null==n.history.destination||null==n.history.destination.bank?null:n.history.destination.bank.name," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("amount",null==n.history?null:n.history.amount),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!1)}}function t(d,b){1&d&&(e.\u0275\u0275elementStart(0,"div",18),e.\u0275\u0275element(1,"bocc-card-information",19),e.\u0275\u0275elementEnd()),2&d&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0)("expanded",!0))}let c=(()=>{class d{constructor(n){this.mboProvider=n,this.skeleton=!1,this.active=!1}get isPending(){return"Enviada"===this.history?.status?.label}onComponent(){this.history&&this.mboProvider.navigation.next(p.Z6.TRANSFERS.HISTORY_INFORMATION,{uuid:this.history.uuid})}onStatus(n){n.stopPropagation(),this.active=!this.active,this.active&&setTimeout(()=>this.active=!1,7500)}}return d.\u0275fac=function(n){return new(n||d)(e.\u0275\u0275directiveInject(y.ZL))},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-transfer-history-element"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfer-history-element__content",4,"ngIf"],["class","mbo-transfer-history-element__skeleton",4,"ngIf"],[1,"mbo-transfer-history-element__content"],[1,"mbo-transfer-history-element__component",3,"click"],[1,"mbo-transfer-history-element__title"],[1,"mbo-transfer-history-element__date","smalltext-medium"],[1,"mbo-transfer-history-element__status",3,"click"],["icon","chat-info",4,"ngIf"],[1,"mbo-transfer-history-element__subtitle","body2-medium","truncate"],[1,"mbo-transfer-history-element__number","smalltext-medium","truncate"],[1,"mbo-transfer-history-element__description","smalltext-medium","truncate"],[1,"mbo-transfer-history-element__amount"],[3,"amount"],["class","mbo-transfer-history-element__action",4,"ngIf"],["icon","chat-info"],[1,"mbo-transfer-history-element__action"],["icon","refresh"],[1,"smalltext-medium"],[1,"mbo-transfer-history-element__skeleton"],[3,"skeleton","expanded"]],template:function(n,g){1&n&&(e.\u0275\u0275template(0,i,18,13,"div",0),e.\u0275\u0275template(1,t,2,2,"div",1)),2&n&&(e.\u0275\u0275property("ngIf",!g.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.skeleton))},dependencies:[_.NgIf,E.v,C.Z,S.Q],styles:['@charset "UTF-8";mbo-transfer-history-element{position:relative;width:100%;display:block}mbo-transfer-history-element .mbo-transfer-history-element__content{position:relative;width:100%;display:flex}mbo-transfer-history-element .mbo-transfer-history-element__content bocc-card-information{border:var(--border-1-lighter-300);width:100%;overflow:hidden;border-radius:var(--sizing-x6)}mbo-transfer-history-element .mbo-transfer-history-element__component{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4);box-sizing:border-box;border:var(--border-1-lighter-300);overflow:hidden;border-radius:var(--sizing-x4)}mbo-transfer-history-element .mbo-transfer-history-element__title{display:flex;justify-content:space-between;align-items:center}mbo-transfer-history-element .mbo-transfer-history-element__date{color:var(--color-carbon-lighter-700)}mbo-transfer-history-element .mbo-transfer-history-element__status{--bocc-icon-dimension: 5rem;--pvt-information-display: none;position:relative;display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center;background:var(--color-bocc-200);color:var(--color-bocc-900);padding:0rem var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);font-size:var(--caption-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}mbo-transfer-history-element .mbo-transfer-history-element__status.pending.active:hover{--pvt-information-display: block}mbo-transfer-history-element .mbo-transfer-history-element__status bocc-icon{padding:1rem;background:var(--color-blue-700);color:var(--color-carbon-lighter-50);border-radius:50%}mbo-transfer-history-element .mbo-transfer-history-element__status:before{content:"Las transacciones a entidades fuera del grupo Aval quedar\\e1n sujetas a la confirmaci\\f3n de la entidad receptora.";display:var(--pvt-information-display);position:absolute;width:120rem;right:0;top:calc(100% + var(--sizing-x3));padding:var(--sizing-x2) var(--sizing-x4);color:var(--color-carbon-lighter-300);background:var(--color-navy-900);border-radius:var(--sizing-x4)}mbo-transfer-history-element .mbo-transfer-history-element__number{color:var(--color-amathyst-700)}mbo-transfer-history-element .mbo-transfer-history-element__description{color:var(--color-carbon-lighter-700)}mbo-transfer-history-element .mbo-transfer-history-element__amount{display:flex;justify-content:flex-end}mbo-transfer-history-element .mbo-transfer-history-element__amount bocc-amount{white-space:nowrap;font-size:var(--subtitle1-size);font-weight:var(--font-weight-medium)}mbo-transfer-history-element .mbo-transfer-history-element__action{display:flex;flex-direction:column;padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;margin:auto 0rem;border:var(--border-1-lighter-300);border-left:none;border-radius:0rem var(--sizing-x4) var(--sizing-x4) 0rem}mbo-transfer-history-element .mbo-transfer-history-element__action .bocc-icon{margin:0rem auto var(--sizing-x4) auto;color:var(--color-blue-700)}mbo-transfer-history-element .mbo-transfer-history-element__action label{margin:0rem auto;color:var(--color-blue-700)}mbo-transfer-history-element .mbo-transfer-history-element__skeleton{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}\n'],encapsulation:2}),d})()},46165:(H,M,o)=>{o.d(M,{E:()=>b});var p=o(39904),y=o(95437),e=(o(88039),o(99877)),_=o(17007),E=o(56921),C=o(50689),S=o(45542);function T(n,g){if(1&n){const m=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(m);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.onRedirectAll())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"Ver todas"),e.\u0275\u0275elementEnd()()}}function h(n,g){if(1&n&&(e.\u0275\u0275elementStart(0,"div",5)(1,"label",6),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(3,T,3,0,"button",7),e.\u0275\u0275elementEnd()),2&n){const m=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",(null==m.history||null==m.history.range?null:m.history.range.label)||"SIN RESULTADOS"," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==m.history?null:m.history.range)}}function i(n,g){1&n&&e.\u0275\u0275element(0,"mbo-transfer-history-element",11),2&n&&e.\u0275\u0275property("history",g.$implicit)}function t(n,g){if(1&n&&(e.\u0275\u0275elementStart(0,"div",9),e.\u0275\u0275template(1,i,1,1,"mbo-transfer-history-element",10),e.\u0275\u0275elementEnd()),2&n){const m=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",m.history.firstPage)}}function c(n,g){1&n&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275element(1,"mbo-transfer-history-element",13)(2,"mbo-transfer-history-element",13),e.\u0275\u0275elementEnd()),2&n&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}function d(n,g){if(1&n&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&n){const m=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",m.msgError," ")}}let b=(()=>{class n{constructor(m){this.mboProvider=m}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transferencias realizadas.":"Lo sentimos, por el momento no cuentas con transferencias realizadas."}onRedirectAll(){this.mboProvider.navigation.next(p.Z6.TRANSFERS.HISTORY)}}return n.\u0275fac=function(m){return new(m||n)(e.\u0275\u0275directiveInject(y.ZL))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-transfer-history-list"]],inputs:{history:"history"},decls:5,vars:4,consts:[[1,"mbo-transfer-history-list__content"],["class","mbo-transfer-history-list__header bocc-subheader",4,"ngIf"],["class","mbo-transfer-history-list__component",4,"ngIf"],["class","mbo-transfer-history-list__skeleton",4,"ngIf"],["class","mbo-transfer-history-list__empty",4,"ngIf"],[1,"mbo-transfer-history-list__header","bocc-subheader"],[1,"caption-medium"],["id","btn_transfer-history-list_go-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfer-history-list_go-all","bocc-button","flat",3,"click"],[1,"mbo-transfer-history-list__component"],[3,"history",4,"ngFor","ngForOf"],[3,"history"],[1,"mbo-transfer-history-list__skeleton"],[3,"skeleton"],[1,"mbo-transfer-history-list__empty"]],template:function(m,a){1&m&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,h,4,2,"div",1),e.\u0275\u0275template(2,t,2,1,"div",2),e.\u0275\u0275template(3,c,3,2,"div",3),e.\u0275\u0275template(4,d,2,1,"mbo-message-empty",4),e.\u0275\u0275elementEnd()),2&m&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.history),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.history),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!a.history),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==a.history?null:a.history.isEmpty))},dependencies:[_.NgForOf,_.NgIf,E.d,C.A,S.P],styles:["mbo-transfer-history-list{position:relative;width:100%;display:block}mbo-transfer-history-list .mbo-transfer-history-list__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-transfer-history-list .mbo-transfer-history-list__header{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-transfer-history-list .mbo-transfer-history-list__header>label{text-transform:uppercase}mbo-transfer-history-list .mbo-transfer-history-list__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-transfer-history-list .mbo-transfer-history-list__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}\n"],encapsulation:2}),n})()},12479:(H,M,o)=>{o.r(M),o.d(M,{MboTransfersHomePageModule:()=>F});var p=o(17007),y=o(78007),v=o(79798),e=o(30263),P=o(83991),_=o(15861),x=o(39904),E=o(95437),C=o(21260),S=o(70658),T=o(88039),h=o(93637),i=o(13961),t=o(99877),c=o(48774),d=o(23436),b=o(9593),n=o(85070),g=o(46165),m=o(2460);function a(A,L){if(1&A){const u=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"bocc-card-category",12),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(u);const R=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(R.onTagAval())}),t.\u0275\u0275elementStart(1,"label"),t.\u0275\u0275text(2,"A un"),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(3,"img",13),t.\u0275\u0275elementStart(4,"label",14),t.\u0275\u0275text(5,"o llave"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"bocc-icon",15),t.\u0275\u0275listener("click",function(R){t.\u0275\u0275restoreView(u);const B=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(B.onBoarding(R))}),t.\u0275\u0275elementEnd()()}}const{ADVANCE:r,CELTOCEL:f,GENERIC:s,TRUSTFUND:l,TAG_AVAL:I}=x.Z6.TRANSFERS;let O=(()=>{class A{constructor(u,D,R,B,U){this.blueScreenService=u,this.mboProvider=D,this.requestTrustfundModal=R,this.requestConfiguration=B,this.onboardingScreenService=U,this.enabledTagAval=!1,this.backAction={id:"btn_transfers-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(x.Z6.CUSTOMER.PRODUCTS.HOME)}}}ngOnInit(){this.initializatedConfiguration()}get appVersion(){return S.N.appVersion}onCelToCel(){this.mboProvider.navigation.next(f.HOME)}onGeneric(){this.mboProvider.navigation.next(s.SOURCE)}onAdvance(){this.mboProvider.navigation.next(r.SOURCE)}onTagAval(){this.mboProvider.navigation.next(I.SOURCE)}onTrustfund(){var u=this;return(0,_.Z)(function*(){u.mboProvider.loader.open("Solicitando fiducias, por favor espere..."),(yield u.requestConfiguration.trustfunds()).when({success:()=>{u.blueScreen||(u.blueScreen=u.blueScreenService.create(P.Yh),u.blueScreen.subscribe(()=>{u.mboProvider.navigation.next(l.SOURCE)})),u.blueScreen?.open(120)},failure:()=>{u.requestTrustfundModal.execute()}},()=>{u.mboProvider.loader.close()})})()}initializatedConfiguration(){var u=this;return(0,_.Z)(function*(){(yield u.requestConfiguration.transfers()).when({success:({enabledTagAval:D,history$:R})=>{u.enabledTagAval=D,R.then(B=>{u.history=B}).catch(()=>{u.history=T.vA.empty()})}})})()}onBoarding(u){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(i.Z)),this.tagAvalonboarding.open(),u.stopPropagation()}}return A.\u0275fac=function(u){return new(u||A)(t.\u0275\u0275directiveInject(e.Dl),t.\u0275\u0275directiveInject(E.ZL),t.\u0275\u0275directiveInject(C.L),t.\u0275\u0275directiveInject(h.a),t.\u0275\u0275directiveInject(v.x6))},A.\u0275cmp=t.\u0275\u0275defineComponent({type:A,selectors:[["mbo-transfers-home-page"]],decls:22,vars:3,consts:[[1,"mbo-transfers-home-page__content"],[1,"mbo-transfers-home-page__header"],["title","Transferir",3,"leftAction"],[1,"mbo-transfers-home-page__body"],["id","btn_transfers-home_aval-key","icon","tag-aval","versionNews","5.8.3",3,"click",4,"ngIf"],["id","btn_transfers-home_cell-to-cell","icon","device-mobile",3,"click"],["id","btn_transfers-home_generic","icon","money-transfer",3,"click"],["id","btn_transfers-home_advance","icon","transaction-next",3,"click"],["id","btn_transfers-home_trustfund","icon","arrow-refresh-current",3,"click"],[1,"mbo-transfers-home-page__footer"],[3,"history"],["active","transfers"],["id","btn_transfers-home_aval-key","icon","tag-aval","versionNews","5.8.3",3,"click"],["id","logo_transfers-home_aval-key","src","assets/shared/logos/tag-aval/tag-aval-line-original.png"],[1,"mbo-transfers-home-page__key"],["icon","info-solid",3,"click"]],template:function(u,D){1&u&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"mbo-customer-greeting"),t.\u0275\u0275text(5,"\xbfQu\xe9 deseas hacer hoy?"),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(6,a,7,0,"bocc-card-category",4),t.\u0275\u0275elementStart(7,"bocc-card-category",5),t.\u0275\u0275listener("click",function(){return D.onCelToCel()}),t.\u0275\u0275elementStart(8,"span"),t.\u0275\u0275text(9,"A un celular"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(10,"bocc-card-category",6),t.\u0275\u0275listener("click",function(){return D.onGeneric()}),t.\u0275\u0275elementStart(11,"span"),t.\u0275\u0275text(12,"A una cuenta"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(13,"bocc-card-category",7),t.\u0275\u0275listener("click",function(){return D.onAdvance()}),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Realizar avance"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(16,"bocc-card-category",8),t.\u0275\u0275listener("click",function(){return D.onTrustfund()}),t.\u0275\u0275elementStart(17,"span"),t.\u0275\u0275text(18,"A tus inversiones"),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(19,"div",9),t.\u0275\u0275element(20,"mbo-transfer-history-list",10),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(21,"mbo-bottom-navigation",11)),2&u&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",D.backAction),t.\u0275\u0275advance(4),t.\u0275\u0275property("ngIf",D.enabledTagAval),t.\u0275\u0275advance(14),t.\u0275\u0275property("history",D.history))},dependencies:[p.NgIf,c.J,d.D,b.k,n.f,g.E,m.Z],styles:["/*!\n * MBO TransfersHome Page\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 25/Jun/2022\n * Updated: 14/Ene/2025\n*/mbo-transfers-home-page{--transfers-footer-bottom: calc(52rem + var(--sizing-safe-bottom));position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between;row-gap:var(--sizing-x12)}mbo-transfers-home-page .mbo-transfers-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfers-home-page .mbo-transfers-home-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfers-home-page .mbo-transfers-home-page__body #btn_transfers-home_aval-key .bocc-card-category__title{-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-transfers-home-page .mbo-transfers-home-page__body #btn_transfers-home_aval-key .bocc-card-category__title img{height:var(--sizing-x8)}mbo-transfers-home-page .mbo-transfers-home-page__body #btn_transfers-home_aval-key .bocc-card-category__title bocc-icon{color:var(--color-blue-700)}mbo-transfers-home-page .mbo-transfers-home-page__footer{position:relative;width:100%;padding-bottom:var(--transfers-footer-bottom);box-sizing:border-box;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}@media screen and (max-width: 320px){mbo-transfers-home-page{--transfers-footer-bottom: calc(48rem + var(--sizing-safe-bottom))}}\n"],encapsulation:2}),A})(),F=(()=>{class A{}return A.\u0275fac=function(u){return new(u||A)},A.\u0275mod=t.\u0275\u0275defineNgModule({type:A}),A.\u0275inj=t.\u0275\u0275defineInjector({imports:[p.CommonModule,y.RouterModule.forChild([{path:"",component:O}]),e.Jx,e.D0,e.oc,v.k4,v.fi,P.$J,P.HV,e.Zl]}),A})()},40914:(H,M,o)=>{o.d(M,{R:()=>p,r:()=>y});const p={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},y={MIN_TRUSTFUND:2e5}}}]);