(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3326],{36319:(L,b,a)=>{a.d(b,{g:()=>s});var d=a(72972);const s=()=>{if(void 0!==d.w)return d.w.Capacitor}},1765:(L,b,a)=>{a.d(b,{I:()=>s,a:()=>c,b:()=>p,c:()=>g,d:()=>C,h:()=>M});var d=a(36319),s=(()=>{return(l=s||(s={})).Heavy="HEAVY",l.Medium="MEDIUM",l.Light="LIGHT",s;var l})();const o={getEngine(){const l=window.TapticEngine;if(l)return l;const y=(0,d.g)();return y?.isPluginAvailable("Haptics")?y.Plugins.Haptics:void 0},available(){return!!this.getEngine()&&("web"!==(0,d.g)()?.getPlatform()||typeof navigator<"u"&&void 0!==navigator.vibrate)},isCordova:()=>void 0!==window.TapticEngine,isCapacitor:()=>void 0!==(0,d.g)(),impact(l){const y=this.getEngine();if(!y)return;const T=this.isCapacitor()?l.style:l.style.toLowerCase();y.impact({style:T})},notification(l){const y=this.getEngine();if(!y)return;const T=this.isCapacitor()?l.type:l.type.toLowerCase();y.notification({type:T})},selection(){const l=this.isCapacitor()?s.Light:"light";this.impact({style:l})},selectionStart(){const l=this.getEngine();l&&(this.isCapacitor()?l.selectionStart():l.gestureSelectionStart())},selectionChanged(){const l=this.getEngine();l&&(this.isCapacitor()?l.selectionChanged():l.gestureSelectionChanged())},selectionEnd(){const l=this.getEngine();l&&(this.isCapacitor()?l.selectionEnd():l.gestureSelectionEnd())}},i=()=>o.available(),g=()=>{i()&&o.selection()},c=()=>{i()&&o.selectionStart()},p=()=>{i()&&o.selectionChanged()},M=()=>{i()&&o.selectionEnd()},C=l=>{i()&&o.impact(l)}},37003:(L,b,a)=>{a.d(b,{I:()=>g,a:()=>C,b:()=>i,c:()=>T,d:()=>I,f:()=>l,g:()=>M,i:()=>p,p:()=>N,r:()=>P,s:()=>y});var d=a(15861),s=a(78635),h=a(28909);const i="ion-content",g=".ion-content-scroll-host",c=`${i}, ${g}`,p=f=>"ION-CONTENT"===f.tagName,M=function(){var f=(0,d.Z)(function*(u){return p(u)?(yield new Promise(k=>(0,s.c)(u,k)),u.getScrollElement()):u});return function(k){return f.apply(this,arguments)}}(),C=f=>f.querySelector(g)||f.querySelector(c),l=f=>f.closest(c),y=(f,u)=>p(f)?f.scrollToTop(u):Promise.resolve(f.scrollTo({top:0,left:0,behavior:u>0?"smooth":"auto"})),T=(f,u,k,R)=>p(f)?f.scrollByPoint(u,k,R):Promise.resolve(f.scrollBy({top:k,left:u,behavior:R>0?"smooth":"auto"})),N=f=>(0,h.b)(f,i),I=f=>{if(p(f)){const k=f.scrollY;return f.scrollY=!1,k}return f.style.setProperty("overflow","hidden"),!0},P=(f,u)=>{p(f)?f.scrollY=u:f.style.removeProperty("overflow")}},44896:(L,b,a)=>{a.d(b,{a:()=>d,b:()=>T,c:()=>c,d:()=>N,e:()=>D,f:()=>g,g:()=>I,h:()=>h,i:()=>s,j:()=>R,k:()=>B,l:()=>p,m:()=>l,n:()=>P,o:()=>C,p:()=>i,q:()=>o,r:()=>k,s:()=>A,t:()=>y,u:()=>f,v:()=>u,w:()=>M});const d="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-miterlimit='10' stroke-width='48' d='M244 400L100 256l144-144M120 256h292' class='ionicon-fill-none'/></svg>",s="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 268l144 144 144-144M256 392V100' class='ionicon-fill-none'/></svg>",h="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M368 64L144 256l224 192V64z'/></svg>",o="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 144l192 224 192-224H64z'/></svg>",i="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 368L256 144 64 368h384z'/></svg>",g="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M416 128L192 384l-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>",c="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>",p="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",M="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M136 208l120-104 120 104M136 304l120 104 120-104' stroke-width='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none'/></svg>",C="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",l="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",y="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z'/></svg>",T="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",N="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>",I="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='192' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>",P="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='48'/><circle cx='416' cy='256' r='48'/><circle cx='96' cy='256' r='48'/></svg>",f="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>",u="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 384h384v-42.67H64zm0-106.67h384v-42.66H64zM64 128v42.67h384V128z'/></svg>",k="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>",R="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M96 256h320M96 176h320M96 336h320' class='ionicon-fill-none ionicon-stroke-width'/></svg>",B="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='44' d='M118 304h276M118 208h276' class='ionicon-fill-none'/></svg>",A="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>",D="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 428L339.92 303.9a160.48 160.48 0 0030.72-94.58C370.64 120.37 298.27 48 209.32 48S48 120.37 48 209.32s72.37 161.32 161.32 161.32a160.48 160.48 0 0094.58-30.72L428 464zM209.32 319.69a110.38 110.38 0 11110.37-110.37 110.5 110.5 0 01-110.37 110.37z'/></svg>"},93326:(L,b,a)=>{a.r(b),a.d(b,{ion_refresher:()=>z,ion_refresher_content:()=>$});var d=a(15861),s=a(42477),h=a(65069),o=a(37003),i=a(78635),g=a(1765),c=a(37943),p=a(44963),M=a(87036),C=a(44896),l=a(92677);a(36319),a(72972);const P=e=>{const t=e.querySelector("ion-spinner"),r=t.shadowRoot.querySelector("circle"),n=e.querySelector(".spinner-arrow-container"),w=e.querySelector(".arrow-container"),x=w?w.querySelector("ion-icon"):null,v=(0,p.c)().duration(1e3).easing("ease-out"),m=(0,p.c)().addElement(n).keyframes([{offset:0,opacity:"0.3"},{offset:.45,opacity:"0.3"},{offset:.55,opacity:"1"},{offset:1,opacity:"1"}]),E=(0,p.c)().addElement(r).keyframes([{offset:0,strokeDasharray:"1px, 200px"},{offset:.2,strokeDasharray:"1px, 200px"},{offset:.55,strokeDasharray:"100px, 200px"},{offset:1,strokeDasharray:"100px, 200px"}]),_=(0,p.c)().addElement(t).keyframes([{offset:0,transform:"rotate(-90deg)"},{offset:1,transform:"rotate(210deg)"}]);if(w&&x){const O=(0,p.c)().addElement(w).keyframes([{offset:0,transform:"rotate(0deg)"},{offset:.3,transform:"rotate(0deg)"},{offset:.55,transform:"rotate(280deg)"},{offset:1,transform:"rotate(400deg)"}]),S=(0,p.c)().addElement(x).keyframes([{offset:0,transform:"translateX(2px) scale(0)"},{offset:.3,transform:"translateX(2px) scale(0)"},{offset:.55,transform:"translateX(-1.5px) scale(1)"},{offset:1,transform:"translateX(-1.5px) scale(1)"}]);v.addAnimation([O,S])}return v.addAnimation([m,E,_])},D=(e,t,r=200)=>{if(!e)return Promise.resolve();const n=(0,i.t)(e,r);return(0,s.w)(()=>{e.style.setProperty("transition",`${r}ms all ease-out`),void 0===t?e.style.removeProperty("transform"):e.style.setProperty("transform",`translate3d(0px, ${t}, 0px)`)}),n},j=()=>navigator.maxTouchPoints>0&&CSS.supports("background: -webkit-named-image(apple-pay-logo-black)"),H=function(){var e=(0,d.Z)(function*(t,r){const n=t.querySelector("ion-refresher-content");if(!n)return Promise.resolve(!1);yield new Promise(v=>(0,i.c)(n,v));const w=t.querySelector("ion-refresher-content .refresher-pulling ion-spinner"),x=t.querySelector("ion-refresher-content .refresher-refreshing ion-spinner");return null!==w&&null!==x&&("ios"===r&&j()||"md"===r)});return function(r,n){return e.apply(this,arguments)}}(),z=class{constructor(e){(0,s.r)(this,e),this.ionRefresh=(0,s.d)(this,"ionRefresh",7),this.ionPull=(0,s.d)(this,"ionPull",7),this.ionStart=(0,s.d)(this,"ionStart",7),this.appliedStyles=!1,this.didStart=!1,this.progress=0,this.pointerDown=!1,this.needsCompletion=!1,this.didRefresh=!1,this.lastVelocityY=0,this.animations=[],this.nativeRefresher=!1,this.state=1,this.pullMin=60,this.pullMax=this.pullMin+60,this.closeDuration="280ms",this.snapbackDuration="280ms",this.pullFactor=1,this.disabled=!1}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}checkNativeRefresher(){var e=this;return(0,d.Z)(function*(){const t=yield H(e.el,(0,c.b)(e));if(t&&!e.nativeRefresher){const r=e.el.closest("ion-content");e.setupNativeRefresher(r)}else t||e.destroyNativeRefresher()})()}destroyNativeRefresher(){this.scrollEl&&this.scrollListenerCallback&&(this.scrollEl.removeEventListener("scroll",this.scrollListenerCallback),this.scrollListenerCallback=void 0),this.nativeRefresher=!1}resetNativeRefresher(e,t){var r=this;return(0,d.Z)(function*(){r.state=t,"ios"===(0,c.b)(r)?yield D(e,void 0,300):yield(0,i.t)(r.el.querySelector(".refresher-refreshing-icon"),200),r.didRefresh=!1,r.needsCompletion=!1,r.pointerDown=!1,r.animations.forEach(n=>n.destroy()),r.animations=[],r.progress=0,r.state=1})()}setupiOSNativeRefresher(e,t){var r=this;return(0,d.Z)(function*(){r.elementToTransform=r.scrollEl;const n=e.shadowRoot.querySelectorAll("svg");let w=.16*r.scrollEl.clientHeight;const x=n.length;(0,s.w)(()=>n.forEach(v=>v.style.setProperty("animation","none"))),r.scrollListenerCallback=()=>{!r.pointerDown&&1===r.state||(0,s.e)(()=>{const v=r.scrollEl.scrollTop,m=r.el.clientHeight;if(v>0){if(8===r.state){const S=(0,i.l)(0,v/(.5*m),1);return void(0,s.w)(()=>((e,t)=>{e.style.setProperty("opacity",t.toString())})(t,1-S))}return}r.pointerDown&&(r.didStart||(r.didStart=!0,r.ionStart.emit()),r.pointerDown&&r.ionPull.emit());const E=r.didStart?30:0,_=r.progress=(0,i.l)(0,(Math.abs(v)-E)/w,1);8===r.state||1===_?(r.pointerDown&&((e,t)=>{(0,s.w)(()=>{e.style.setProperty("--refreshing-rotation-duration",t>=1?"0.5s":"2s"),e.style.setProperty("opacity","1")})})(t,r.lastVelocityY),r.didRefresh||(r.beginRefresh(),r.didRefresh=!0,(0,g.d)({style:g.I.Light}),r.pointerDown||D(r.elementToTransform,`${m}px`))):(r.state=2,((e,t,r)=>{(0,s.w)(()=>{e.forEach((w,x)=>{const v=x*(1/t),_=(0,i.l)(0,(r-v)/(1-v),1);w.style.setProperty("opacity",_.toString())})})})(n,x,_))})},r.scrollEl.addEventListener("scroll",r.scrollListenerCallback),r.gesture=(yield Promise.resolve().then(a.bind(a,35067))).createGesture({el:r.scrollEl,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:5,onStart:()=>{r.pointerDown=!0,r.didRefresh||D(r.elementToTransform,"0px"),0===w&&(w=.16*r.scrollEl.clientHeight)},onMove:v=>{r.lastVelocityY=v.velocityY},onEnd:()=>{r.pointerDown=!1,r.didStart=!1,r.needsCompletion?(r.resetNativeRefresher(r.elementToTransform,32),r.needsCompletion=!1):r.didRefresh&&(0,s.e)(()=>D(r.elementToTransform,`${r.el.clientHeight}px`))}}),r.disabledChanged()})()}setupMDNativeRefresher(e,t,r){var n=this;return(0,d.Z)(function*(){const w=(0,i.g)(t).querySelector("circle"),x=n.el.querySelector("ion-refresher-content .refresher-pulling-icon"),v=(0,i.g)(r).querySelector("circle");null!==w&&null!==v&&(0,s.w)(()=>{w.style.setProperty("animation","none"),r.style.setProperty("animation-delay","-655ms"),v.style.setProperty("animation-delay","-655ms")}),n.gesture=(yield Promise.resolve().then(a.bind(a,35067))).createGesture({el:n.scrollEl,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:5,canStart:()=>8!==n.state&&32!==n.state&&0===n.scrollEl.scrollTop,onStart:m=>{n.progress=0,m.data={animation:void 0,didStart:!1,cancelled:!1}},onMove:m=>{if(m.velocityY<0&&0===n.progress&&!m.data.didStart||m.data.cancelled)m.data.cancelled=!0;else{if(!m.data.didStart){m.data.didStart=!0,n.state=2;const{scrollEl:E}=n,_=E.matches(o.I)?"overflow":"--overflow";(0,s.w)(()=>E.style.setProperty(_,"hidden"));const O=(e=>{const t=e.previousElementSibling;return null!==t&&"ION-HEADER"===t.tagName?"translate":"scale"})(e),S=((e,t,r)=>"scale"===e?((e,t)=>{const r=t.clientHeight,n=(0,p.c)().addElement(e).keyframes([{offset:0,transform:`scale(0) translateY(-${r}px)`},{offset:1,transform:"scale(1) translateY(100px)"}]);return P(e).addAnimation([n])})(t,r):((e,t)=>{const r=t.clientHeight,n=(0,p.c)().addElement(e).keyframes([{offset:0,transform:`translateY(-${r}px)`},{offset:1,transform:"translateY(100px)"}]);return P(e).addAnimation([n])})(t,r))(O,x,n.el);return m.data.animation=S,S.progressStart(!1,0),n.ionStart.emit(),void n.animations.push(S)}n.progress=(0,i.l)(0,m.deltaY/180*.5,1),m.data.animation.progressStep(n.progress),n.ionPull.emit()}},onEnd:m=>{if(!m.data.didStart)return;n.gesture.enable(!1);const{scrollEl:E}=n,_=E.matches(o.I)?"overflow":"--overflow";if((0,s.w)(()=>E.style.removeProperty(_)),n.progress<=.4)return void m.data.animation.progressEnd(0,n.progress,500).onFinish(()=>{n.animations.forEach(W=>W.destroy()),n.animations=[],n.gesture.enable(!0),n.state=1});const O=(0,h.g)([0,0],[0,0],[1,1],[1,1],n.progress)[0],S=(e=>(0,p.c)().duration(125).addElement(e).fromTo("transform","translateY(var(--ion-pulling-refresher-translate, 100px))","translateY(0px)"))(x);n.animations.push(S),(0,s.w)((0,d.Z)(function*(){x.style.setProperty("--ion-pulling-refresher-translate",100*O+"px"),m.data.animation.progressEnd(),yield S.play(),n.beginRefresh(),m.data.animation.destroy(),n.gesture.enable(!0)}))}}),n.disabledChanged()})()}setupNativeRefresher(e){var t=this;return(0,d.Z)(function*(){if(t.scrollListenerCallback||!e||t.nativeRefresher||!t.scrollEl)return;t.setCss(0,"",!1,""),t.nativeRefresher=!0;const r=t.el.querySelector("ion-refresher-content .refresher-pulling ion-spinner"),n=t.el.querySelector("ion-refresher-content .refresher-refreshing ion-spinner");"ios"===(0,c.b)(t)?t.setupiOSNativeRefresher(r,n):t.setupMDNativeRefresher(e,r,n)})()}componentDidUpdate(){this.checkNativeRefresher()}connectedCallback(){var e=this;return(0,d.Z)(function*(){if("fixed"!==e.el.getAttribute("slot"))return void console.error('Make sure you use: <ion-refresher slot="fixed">');const t=e.el.closest(o.b);t?(0,i.c)(t,(0,d.Z)(function*(){const r=t.querySelector(o.I);e.scrollEl=yield(0,o.g)(r??t),e.backgroundContentEl=yield t.getBackgroundElement(),(yield H(e.el,(0,c.b)(e)))?e.setupNativeRefresher(t):(e.gesture=(yield Promise.resolve().then(a.bind(a,35067))).createGesture({el:t,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:20,passive:!1,canStart:()=>e.canStart(),onStart:()=>e.onStart(),onMove:n=>e.onMove(n),onEnd:()=>e.onEnd()}),e.disabledChanged())})):(0,o.p)(e.el)})()}disconnectedCallback(){this.destroyNativeRefresher(),this.scrollEl=void 0,this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}complete(){var e=this;return(0,d.Z)(function*(){e.nativeRefresher?(e.needsCompletion=!0,e.pointerDown||(0,i.r)(()=>(0,i.r)(()=>e.resetNativeRefresher(e.elementToTransform,32)))):e.close(32,"120ms")})()}cancel(){var e=this;return(0,d.Z)(function*(){e.nativeRefresher?e.pointerDown||(0,i.r)(()=>(0,i.r)(()=>e.resetNativeRefresher(e.elementToTransform,16))):e.close(16,"")})()}getProgress(){return Promise.resolve(this.progress)}canStart(){return!(!this.scrollEl||1!==this.state||this.scrollEl.scrollTop>0)}onStart(){this.progress=0,this.state=1,this.memoizeOverflowStyle()}onMove(e){if(!this.scrollEl)return;const t=e.event;if(void 0!==t.touches&&t.touches.length>1||56&this.state)return;const r=Number.isNaN(this.pullFactor)||this.pullFactor<0?1:this.pullFactor,n=e.deltaY*r;if(n<=0)return this.progress=0,this.state=1,this.appliedStyles?void this.setCss(0,"",!1,""):void 0;if(1===this.state){if(this.scrollEl.scrollTop>0)return void(this.progress=0);this.state=2}if(t.cancelable&&t.preventDefault(),this.setCss(n,"0ms",!0,""),0===n)return void(this.progress=0);const w=this.pullMin;this.progress=n/w,this.didStart||(this.didStart=!0,this.ionStart.emit()),this.ionPull.emit(),n<w?this.state=2:n>this.pullMax?this.beginRefresh():this.state=4}onEnd(){4===this.state?this.beginRefresh():2===this.state?this.cancel():1===this.state&&this.restoreOverflowStyle()}beginRefresh(){this.state=8,this.setCss(this.pullMin,this.snapbackDuration,!0,""),this.ionRefresh.emit({complete:this.complete.bind(this)})}close(e,t){setTimeout(()=>{this.state=1,this.progress=0,this.didStart=!1,this.setCss(0,"0ms",!1,"",!0)},600),this.state=e,this.setCss(0,this.closeDuration,!0,t)}setCss(e,t,r,n,w=!1){this.nativeRefresher||(this.appliedStyles=e>0,(0,s.w)(()=>{if(this.scrollEl&&this.backgroundContentEl){const x=this.scrollEl.style,v=this.backgroundContentEl.style;x.transform=v.transform=e>0?`translateY(${e}px) translateZ(0px)`:"",x.transitionDuration=v.transitionDuration=t,x.transitionDelay=v.transitionDelay=n,x.overflow=r?"hidden":""}w&&this.restoreOverflowStyle()}))}memoizeOverflowStyle(){if(this.scrollEl){const{overflow:e,overflowX:t,overflowY:r}=this.scrollEl.style;this.overflowStyles={overflow:e??"",overflowX:t??"",overflowY:r??""}}}restoreOverflowStyle(){if(void 0!==this.overflowStyles&&void 0!==this.scrollEl){const{overflow:e,overflowX:t,overflowY:r}=this.overflowStyles;this.scrollEl.style.overflow=e,this.scrollEl.style.overflowX=t,this.scrollEl.style.overflowY=r,this.overflowStyles=void 0}}render(){const e=(0,c.b)(this);return(0,s.h)(s.H,{key:"96f4f595ebdb92a12755b642398691bcaab9f7c1",slot:"fixed",class:{[e]:!0,[`refresher-${e}`]:!0,"refresher-native":this.nativeRefresher,"refresher-active":1!==this.state,"refresher-pulling":2===this.state,"refresher-ready":4===this.state,"refresher-refreshing":8===this.state,"refresher-cancelling":16===this.state,"refresher-completing":32===this.state}})}get el(){return(0,s.f)(this)}static get watchers(){return{disabled:["disabledChanged"]}}};z.style={ios:"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, #747577)}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}",md:"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #3880ff)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #3880ff);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, #ececec);background:var(--ion-color-step-250, #ffffff);-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}"};const $=class{constructor(e){(0,s.r)(this,e),this.customHTMLEnabled=c.c.get("innerHTMLTemplatesEnabled",M.E),this.pullingIcon=void 0,this.pullingText=void 0,this.refreshingSpinner=void 0,this.refreshingText=void 0}componentWillLoad(){if(void 0===this.pullingIcon){const e=j(),t=(0,c.b)(this);this.pullingIcon=c.c.get("refreshingIcon","ios"===t&&e?c.c.get("spinner",e?"lines":C.i):"circular")}if(void 0===this.refreshingSpinner){const e=(0,c.b)(this);this.refreshingSpinner=c.c.get("refreshingSpinner",c.c.get("spinner","ios"===e?"lines":"circular"))}}renderPullingText(){const{customHTMLEnabled:e,pullingText:t}=this;return e?(0,s.h)("div",{class:"refresher-pulling-text",innerHTML:(0,M.a)(t)}):(0,s.h)("div",{class:"refresher-pulling-text"},t)}renderRefreshingText(){const{customHTMLEnabled:e,refreshingText:t}=this;return e?(0,s.h)("div",{class:"refresher-refreshing-text",innerHTML:(0,M.a)(t)}):(0,s.h)("div",{class:"refresher-refreshing-text"},t)}render(){const e=this.pullingIcon,t=null!=e&&void 0!==l.S[e],r=(0,c.b)(this);return(0,s.h)(s.H,{key:"cf3caa51c4aba8a95622f6d32cafa90b683b9d6e",class:r},(0,s.h)("div",{key:"5ad70801104bbea873d3525206660c52e4447903",class:"refresher-pulling"},this.pullingIcon&&t&&(0,s.h)("div",{key:"0f95df169fd367528bfaa5d9ccf6690a613609c4",class:"refresher-pulling-icon"},(0,s.h)("div",{key:"4b8f0465a19f017751b207807c32e1fe00fda433",class:"spinner-arrow-container"},(0,s.h)("ion-spinner",{key:"77e60179d76f0d17f8f2dc3518f97a2a924418e6",name:this.pullingIcon,paused:!0}),"md"===r&&"circular"===this.pullingIcon&&(0,s.h)("div",{key:"f78f63f08f071bead1bfe655bae6394f8a219d91",class:"arrow-container"},(0,s.h)("ion-icon",{key:"4d833d134d2b221cae2dfb0611d4029f2d664db5",icon:C.h,"aria-hidden":"true"})))),this.pullingIcon&&!t&&(0,s.h)("div",{key:"e6db19d7fa324363d2a7c3c046510f4c8461f7e6",class:"refresher-pulling-icon"},(0,s.h)("ion-icon",{key:"66c2ef1a53c5809f49891de515da5f55d9bf8dcc",icon:this.pullingIcon,lazy:!1,"aria-hidden":"true"})),void 0!==this.pullingText&&this.renderPullingText()),(0,s.h)("div",{key:"80c413e21d362a5bb0419fcd13092453b3445cee",class:"refresher-refreshing"},this.refreshingSpinner&&(0,s.h)("div",{key:"0d5511f9644de26332a1a9ed39b160691fab74d9",class:"refresher-refreshing-icon"},(0,s.h)("ion-spinner",{key:"54e4a96b081c7b453a98e00cceea7c086268a450",name:this.refreshingSpinner})),void 0!==this.refreshingText&&this.renderRefreshingText()))}get el(){return(0,s.f)(this)}}},92677:(L,b,a)=>{a.d(b,{S:()=>s});const s={bubbles:{dur:1e3,circles:9,fn:(h,o,i)=>{const g=h*o/i-h+"ms",c=2*Math.PI*o/i;return{r:5,style:{top:32*Math.sin(c)+"%",left:32*Math.cos(c)+"%","animation-delay":g}}}},circles:{dur:1e3,circles:8,fn:(h,o,i)=>{const g=o/i,c=h*g-h+"ms",p=2*Math.PI*g;return{r:5,style:{top:32*Math.sin(p)+"%",left:32*Math.cos(p)+"%","animation-delay":c}}}},circular:{dur:1400,elmDuration:!0,circles:1,fn:()=>({r:20,cx:48,cy:48,fill:"none",viewBox:"24 24 48 48",transform:"translate(0,0)",style:{}})},crescent:{dur:750,circles:1,fn:()=>({r:26,style:{}})},dots:{dur:750,circles:3,fn:(h,o)=>({r:6,style:{left:32-32*o+"%","animation-delay":-110*o+"ms"}})},lines:{dur:1e3,lines:8,fn:(h,o,i)=>({y1:14,y2:26,style:{transform:`rotate(${360/i*o+(o<i/2?180:-180)}deg)`,"animation-delay":h*o/i-h+"ms"}})},"lines-small":{dur:1e3,lines:8,fn:(h,o,i)=>({y1:12,y2:20,style:{transform:`rotate(${360/i*o+(o<i/2?180:-180)}deg)`,"animation-delay":h*o/i-h+"ms"}})},"lines-sharp":{dur:1e3,lines:12,fn:(h,o,i)=>({y1:17,y2:29,style:{transform:`rotate(${30*o+(o<6?180:-180)}deg)`,"animation-delay":h*o/i-h+"ms"}})},"lines-sharp-small":{dur:1e3,lines:12,fn:(h,o,i)=>({y1:12,y2:20,style:{transform:`rotate(${30*o+(o<6?180:-180)}deg)`,"animation-delay":h*o/i-h+"ms"}})}}}}]);