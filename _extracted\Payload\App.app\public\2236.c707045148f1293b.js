(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2236],{37907:(Q,b,r)=>{r.d(b,{L:()=>E,m:()=>P});var v=r(15861),l=r(87956),f=r(53113),i=r(98699);class c{constructor(m,t,e){this.source=m,this.destination=t,this.amount=e}}function y(s){return new c(s.source,s.destination,s.amount)}var A=r(71776),h=r(39904),p=r(87903),d=r(42168),n=r(84757),u=r(99877);let I=(()=>{class s{constructor(t){this.http=t}send(t){const e={hashCheckingAcct:t.destination.id,hashLoanAcct:t.source.id,amount:String(t.amount)};return(0,d.firstValueFrom)(this.http.post(h.bV.TRANSACTIONS.CREDIT_QUOTA,e).pipe((0,n.map)(o=>(0,p.l1)(o,"SUCCESS")))).catch(o=>(0,p.rU)(o))}}return s.\u0275fac=function(t){return new(t||s)(u.\u0275\u0275inject(A.HttpClient))},s.\u0275prov=u.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})();var S=r(20691);let U=(()=>{class s extends S.Store{constructor(){super({confirmation:!1,fromCustomer:!1})}setSource(t,e=!1){this.reduce(o=>({...o,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setDestination(t){this.reduce(e=>({...e,destination:t}))}setAmount(t){this.reduce(e=>({...e,amount:t}))}selectForAmount(){return this.select(({confirmation:t,amount:e,source:o})=>({amount:e,confirmation:t,source:o}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return s.\u0275fac=function(t){return new(t||s)},s.\u0275prov=u.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})(),E=(()=>{class s{constructor(t,e,o,a){this.products=t,this.eventBusService=e,this.repository=o,this.store=a}setSource(t){var e=this;return(0,v.Z)(function*(){try{return yield e.products.requestInformation(t),i.Either.success(e.store.setSource(t))}catch({message:o}){return i.Either.failure({message:o})}})()}setDestination(t){try{return i.Either.success(this.store.setDestination(t))}catch({message:e}){return i.Either.failure({message:e})}}setAmount(t){try{return i.Either.success(this.store.setAmount(t))}catch({message:e}){return i.Either.failure({message:e})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:t,source:e})}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,v.Z)(function*(){const e=y(t.store.currentState),o=yield t.save(e);return t.eventBusService.emit(o.channel),i.Either.success({creditUseQuota:e,status:o})})()}save(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(f.LN.error(e))}}}return s.\u0275fac=function(t){return new(t||s)(u.\u0275\u0275inject(l.M5),u.\u0275\u0275inject(l.Yd),u.\u0275\u0275inject(I),u.\u0275\u0275inject(U))},s.\u0275prov=u.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})();var M=r(89148);let P=(()=>{class s{constructor(t,e,o){this.products=t,this.productService=e,this.store=o}source(){var t=this;return(0,v.Z)(function*(){try{const e=t.store.itIsConfirmation(),o=yield t.requestCredits();return i.Either.success({confirmation:e,products:o})}catch({message:e}){return i.Either.failure({message:e})}})()}destination(t){var e=this;return(0,v.Z)(function*(){try{const o=yield e.products.requestAccountsForTransfer(),a=yield e.requestCredits(),g=e.store.itIsConfirmation(),C=e.requestCredit(a,t);return i.Either.success({accounts:o,confirmation:g,products:a,source:C})}catch({message:o}){return i.Either.failure({message:o})}})()}amount(){var t=this;return(0,v.Z)(function*(){try{const{amount:e,confirmation:o,source:a}=t.store.selectForAmount(),g=yield t.requestSection(a);return i.Either.success({confirmation:o,section:g,source:a,value:e})}catch({message:e}){return i.Either.failure({message:e})}})()}confirmation(){try{const t=y(this.store.currentState);return i.Either.success({creditUseQuota:t})}catch({message:t}){return i.Either.failure({message:t})}}requestCredits(){return this.products.requestProducts([M.Gt.ResolvingCredit])}requestCredit(t,e){let o=this.store.getSource();return!o&&e&&(o=t.find(({id:a})=>e===a),this.store.setSource(o,!0)),o}requestSection(t){return this.productService.requestInformation(t).then(e=>e?.getSection(M.Av.LoanQuotaAvailable))}}return s.\u0275fac=function(t){return new(t||s)(u.\u0275\u0275inject(l.hM),u.\u0275\u0275inject(l.M5),u.\u0275\u0275inject(U))},s.\u0275prov=u.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},44793:(Q,b,r)=>{r.d(b,{Z:()=>A});var v=r(30263),l=r(39904),f=r(95437),i=r(37907),c=r(99877);let A=(()=>{class h{constructor(d,n,u){this.modalConfirmation=d,this.mboProvider=n,this.managerUseQuota=u}execute(d=!0){d?this.modalConfirmation.execute({title:"Cancelar transacci\xf3n",message:"\xbfEstas seguro que deseas cancelar la transferencia de cupo actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerUseQuota.reset().when({success:({fromCustomer:d,source:n})=>{d?this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.INFO,{productId:n.id}):this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.HOME)},failure:()=>{this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.HOME)}})}}return h.\u0275fac=function(d){return new(d||h)(c.\u0275\u0275inject(v.$e),c.\u0275\u0275inject(f.ZL),c.\u0275\u0275inject(i.L))},h.\u0275prov=c.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h})()},2236:(Q,b,r)=>{r.r(b),r.d(b,{MboCreditUseQuotaAmountPageModule:()=>m});var v=r(17007),l=r(78007),f=r(30263),i=r(15861),c=r(24495),y=r(39904),A=r(95437),h=r(57544),p=r(37907),d=r(44793),n=r(99877),u=r(35641),I=r(48774),S=r(83413),U=r(45542);const E=y.Z6.TRANSACTIONS.CREDIT_USE_QUOTA;let s=(()=>{class t{constructor(o,a,g,C){this.mboProvider=o,this.requestConfiguration=a,this.managerUseQuota=g,this.cancelProvider=C,this.confirmation=!1,this.requesting=!0,this.backAction={id:"btn_credit-quota-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(E.DESTINATION)}},this.cancelAction={id:"btn_credit-quota-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new h.FormControl(void 0,[c.C1,c.LU])}ngOnInit(){this.initializatedConfiguration()}get disabled(){return this.amountControl.invalid||this.requesting}onSubmit(){this.managerUseQuota.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(E.CONFIRMATION)}})}initializatedConfiguration(){var o=this;return(0,i.Z)(function*(){(yield o.requestConfiguration.amount()).when({success:({confirmation:a,source:g,section:C,value:j})=>{o.confirmation=a,o.source=g,o.quotaAvailable=C?.value,o.quotaLabel=C.label,j&&o.amountControl.setValue(j),o.setValidatorsForSection(C)}},()=>{o.requesting=!1})})()}setValidatorsForSection(o){const a=[c.C1,c.LU,(0,c.Go)(2e5),(0,c.VV)(3e7)];o&&a.push((0,c.v6)(o.value)),this.amountControl.setValidators(a)}}return t.\u0275fac=function(o){return new(o||t)(n.\u0275\u0275directiveInject(A.ZL),n.\u0275\u0275directiveInject(p.m),n.\u0275\u0275directiveInject(p.L),n.\u0275\u0275directiveInject(d.Z))},t.\u0275cmp=n.\u0275\u0275defineComponent({type:t,selectors:[["mbo-credit-use-quota-amount-page"]],decls:13,vars:13,consts:[[1,"mbo-credit-use-quota-amount-page__content"],[1,"mbo-credit-use-quota-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-credit-use-quota-amount-page__body"],[1,"mbo-credit-use-quota-amount-page__message","subtitle2-medium"],["elementId","txt_credit-use-quota-amount_value","label","Valor a pagar","placeholder","0","type","money","prefix","$",3,"formControl"],[3,"color","icon","title","number","subtitle","amount","hidden"],[3,"skeleton","hidden"],[1,"mbo-credit-use-quota-amount-page__footer"],["id","btn_credit-use-quota-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(o,a){1&o&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"p",4),n.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas utilizar? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6)(8,"bocc-card-product-summary",7),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(9,"div",8)(10,"button",9),n.\u0275\u0275listener("click",function(){return a.onSubmit()}),n.\u0275\u0275elementStart(11,"span"),n.\u0275\u0275text(12,"Continuar"),n.\u0275\u0275elementEnd()()()),2&o&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",a.backAction)("rightAction",a.cancelAction),n.\u0275\u0275advance(4),n.\u0275\u0275property("formControl",a.amountControl),n.\u0275\u0275advance(1),n.\u0275\u0275property("color",null==a.source?null:a.source.color)("icon",null==a.source?null:a.source.logo)("title",null==a.source?null:a.source.nickname)("number",null==a.source?null:a.source.shortNumber)("subtitle",a.quotaLabel)("amount",a.quotaAvailable)("hidden",a.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0)("hidden",!a.requesting),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",a.disabled))},dependencies:[u.d,I.J,S.D,U.P],styles:["/*!\n * MBO CreditUseQuotaAmount Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 15/Nov/2022\n * Updated: 20/Jun/2024\n*/mbo-credit-use-quota-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-credit-use-quota-amount-page .mbo-credit-use-quota-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-credit-use-quota-amount-page .mbo-credit-use-quota-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-credit-use-quota-amount-page .mbo-credit-use-quota-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-credit-use-quota-amount-page .mbo-credit-use-quota-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-credit-use-quota-amount-page .mbo-credit-use-quota-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-credit-use-quota-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20)}}\n"],encapsulation:2}),t})(),m=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[v.CommonModule,l.RouterModule.forChild([{path:"",component:s}]),f.dH,f.Jx,f.D1,f.P8]}),t})()}}]);