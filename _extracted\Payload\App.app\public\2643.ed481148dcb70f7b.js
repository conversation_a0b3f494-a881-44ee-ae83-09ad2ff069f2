(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2643],{61589:(P,k,t)=>{t.d(k,{Q:()=>e,e:()=>y});var v=t(39904),h=t(87903),g=t(53113);function m(c){const{creditCard:u,status:{isError:a,message:b}}=c,p=u.franchise.description;return a?{animation:v.cj,title:`\xa1Tarjeta de cr\xe9dito ${p} no ha sido bloqueada!`,subtitle:b}:{animation:v.F6,title:`\xa1Tarjeta de cr\xe9dito ${p} ha sido bloqueada exitosamente!`}}function C({isError:c}){return c?[{event:"cancel",label:"Cancelar",type:"outline"},{event:"retry",label:"Volver a intentar",type:"raised"}]:[{event:"finish",label:"Finalizar",type:"raised"}]}function e(c){const{dateFormat:u,timeFormat:a}=new g.ou,{creditCard:b,status:p}=c;return{actions:C(p),error:p.isError,header:m(c),informations:[(0,h.SP)("TARJETA",b.nickname,b.number),(0,h.cZ)(u,a)],skeleton:!1}}function f(c){const{status:{isError:u,message:a}}=c;return u?{animation:v.cj,title:"\xa1Tarjeta de d\xe9bito Mastercard no ha sido bloqueada!",subtitle:a}:{animation:v.F6,title:"\xa1Tarjeta de d\xe9bito Mastercard ha sido bloqueada exitosamente!"}}function y(c){const{dateFormat:u,timeFormat:a}=new g.ou,{debitCard:b,status:p}=c;return{actions:C(p),error:p.isError,header:f(c),informations:[(0,h.SP)("TARJETA",b.name,b.number),(0,h.cZ)(u,a)],skeleton:!1}}},18275:(P,k,t)=>{t.d(k,{S:()=>p,a:()=>D});var v=t(15861),h=t(87956),g=t(53113),m=t(98699),C=t(71776),e=t(39904),f=t(89148),y=t(87903),c=t(42168),u=t(84757),a=t(99877);let b=(()=>{class d{constructor(o){this.http=o}creditCard(o){return this.card(o.id,f.Gt.CreditCard)}debitCard(o){return this.card(o.id,o.type)}card(o,l){return(0,c.firstValueFrom)(this.http.post(e.bV.PRODUCTS.BLOCK_CARD,[{cardId:o,productType:l}]).pipe((0,u.map)(([i])=>i?(0,y.l1)(i,"SUCCESS"):new g.LN("ERROR","Lo sentimos en este momento no podemos realizar esta operaci\xf3n.")),(0,u.catchError)(i=>(0,c.of)((0,y.rU)(i)))))}}return d.\u0275fac=function(o){return new(o||d)(a.\u0275\u0275inject(C.HttpClient))},d.\u0275prov=a.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})(),p=(()=>{class d{constructor(o,l,n){this.products=o,this.repository=l,this.eventBusService=n}configuration(o){var l=this;return(0,v.Z)(function*(){try{const n=(yield l.products.requestCreditCards()).find(({id:i})=>i===o);return m.Either.success(n)}catch({message:n}){return m.Either.failure({message:n})}})()}block(o){var l=this;return(0,v.Z)(function*(){const n=yield l.execute(o);return l.eventBusService.emit(n.channel),m.Either.success({creditCard:o,status:n})})()}execute(o){try{return this.repository.creditCard(o)}catch({message:l}){return Promise.resolve(g.LN.error(l))}}}return d.\u0275fac=function(o){return new(o||d)(a.\u0275\u0275inject(h.hM),a.\u0275\u0275inject(b),a.\u0275\u0275inject(h.Yd))},d.\u0275prov=a.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})(),D=(()=>{class d{constructor(o,l,n,i){this.products=o,this.productService=l,this.repository=n,this.eventBusService=i}configuration(o){var l=this;return(0,v.Z)(function*(){try{const n=(yield l.products.requestAccountsForTransfer()).find(({id:_})=>_===o);if(!n)return m.Either.success({product:n,cards:[]});const i=yield l.productService.requestDebitCards(n);return m.Either.success({product:n,cards:i})}catch({message:n}){return m.Either.failure({message:n})}})()}block(o){var l=this;return(0,v.Z)(function*(){const n=yield l.execute(o);return l.eventBusService.emit(n.channel),m.Either.success({debitCard:o,status:n})})()}execute(o){try{return this.repository.debitCard(o)}catch({message:l}){return Promise.resolve(g.LN.error(l))}}}return d.\u0275fac=function(o){return new(o||d)(a.\u0275\u0275inject(h.hM),a.\u0275\u0275inject(h.M5),a.\u0275\u0275inject(b),a.\u0275\u0275inject(h.Yd))},d.\u0275prov=a.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},21339:(P,k,t)=>{t.d(k,{w:()=>y});var v=t(17007),g=t(30263),m=t(39904),C=t(95437),e=t(99877);let y=(()=>{class c{constructor(a){this.mboProvider=a}ngBoccPortal(a){this.portal=a}onLinkRates(){this.mboProvider.openUrl(m.BA.CARD_BLOCK_RATES)}onSubmit(){this.portal.resolve(),this.portal.destroy()}onCancel(){this.portal.destroy()}}return c.\u0275fac=function(a){return new(a||c)(e.\u0275\u0275directiveInject(C.ZL))},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-block-modal"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:15,vars:0,consts:[[1,"mbo-card-block-modal__content"],[1,"mbo-card-block-modal__body"],[1,"mbo-card-block-modal__title","smalltext-medium"],[1,"mbo-card-block-modal__message","body2-medium"],["bocc-button","flat","bocc-theme","amathyst",3,"click"],[1,"mbo-card-block-modal__footer"],["id","btn_card-block-modal_submit","bocc-button","raised","prefixIcon","block","bocc-theme","danger",3,"click"],["id","btn_card-block-modal_cancel","bocc-button","outline",3,"click"]],template:function(a,b){1&a&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),e.\u0275\u0275text(3," Bloquear tarjeta "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",3),e.\u0275\u0275text(5," Recuerda que una vez bloqueada la tarjeta dejar\xe1 de funcionar definitivamente, el proceso de reposici\xf3n de tu tarjeta tendr\xe1 un costo. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",4),e.\u0275\u0275listener("click",function(){return b.onLinkRates()}),e.\u0275\u0275text(7," Ver tasas y tarifas "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",5)(9,"button",6),e.\u0275\u0275listener("click",function(){return b.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Bloquear"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"button",7),e.\u0275\u0275listener("click",function(){return b.onCancel()}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Cancelar"),e.\u0275\u0275elementEnd()()()())},dependencies:[v.CommonModule,g.P8],styles:["mbo-card-block-modal{position:relative;display:block;box-sizing:border-box}mbo-card-block-modal .mbo-card-block-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-block-modal .mbo-card-block-modal__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x6)}mbo-card-block-modal .mbo-card-block-modal__title{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-card-block-modal .mbo-card-block-modal__message{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-block-modal .mbo-card-block-modal__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-card-block-modal .mbo-card-block-modal__footer>button{width:100%}\n"],encapsulation:2}),c})()},22643:(P,k,t)=>{t.r(k),t.d(k,{MboDebitCardBlockPageModule:()=>n});var v=t(17007),h=t(78007),g=t(79798),m=t(30263),C=t(15861),e=t(99877),f=t(39904),y=t(95437),c=t(61589),u=t(18275),a=t(21339),b=t(48774),p=t(83413),D=t(45542),d=t(10464),M=t(78021),o=t(16442);let l=(()=>{class i{constructor(r,s,E,B,S,T){this.ref=r,this.activateRoute=s,this.modalConfirmation=E,this.modalService=B,this.mboProvider=S,this.managerDebitCardBlock=T,this.blocking=!1,this.requesting=!0,this.template=f.$d,this.backAction={id:"btn_debit-card-block_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(f.Z6.CUSTOMER.PRODUCTS.INFO,{productId:this.product?.id})}},this.cancelAction={id:"btn_debit-card-block_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.back(f.Z6.CUSTOMER.PRODUCTS.HOME)}},this.rightActions=[{id:"btn_debit-card-block-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_debit-card-block-page_template"),setTimeout(()=>{this.initializatedConfiguration()},120)}onAction(r){"retry"===r?this.debitCardBlock():this.mboProvider.navigation.next(f.Z6.CUSTOMER.PRODUCTS.HOME)}onSubmit(){const r=this.modalService.create(a.w);r.open(),r.waiting().then(()=>{this.debitCardBlock()})}initializatedConfiguration(){var r=this;return(0,C.Z)(function*(){r.mboProvider.loader.open("Solicitando tarjetas, por favor espere...");const{productId:s}=r.activateRoute.snapshot.queryParams;(yield r.managerDebitCardBlock.configuration(s)).when({success:({product:E,cards:B})=>{if(!E)return r.mboProvider.navigation.back(f.Z6.CUSTOMER.PRODUCTS.HOME);if(!B.length)return r.modalConfirmation.execute({title:"SIN TARJETAS D\xc9BITO",message:"Lo sentimos, este producto no cuentas con tarjetas asociadas.",accept:{label:"Aceptar"}}),r.mboProvider.navigation.back(f.Z6.CUSTOMER.PRODUCTS.INFO,{productId:E.id});const[S]=B;r.product=E,r.debitCard=S}},()=>{r.mboProvider.loader.close()})})()}debitCardBlock(){var r=this;return(0,C.Z)(function*(){r.template=f.$d,r.blocking=!0,r.requesting=!0,(yield r.managerDebitCardBlock.block(r.debitCard)).when({success:s=>{r.template=(0,c.e)(s)}},()=>{r.requesting=!1})})()}}return i.\u0275fac=function(r){return new(r||i)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(h.ActivatedRoute),e.\u0275\u0275directiveInject(m.$e),e.\u0275\u0275directiveInject(m.iM),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(u.a))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-debit-card-block-page"]],decls:18,vars:10,consts:[[1,"mbo-debit-card-block-page__product",3,"hidden"],[1,"mbo-debit-card-block-page__content"],[1,"mbo-debit-card-block-page__header"],["title","Bloqueo",3,"leftAction","rightAction"],[1,"mbo-debit-card-block-page__body"],[1,"mbo-debit-card-block-page__title","subtitle2-medium"],["statusColor","success","statusLabel","Activa para uso",3,"color","title","number"],[1,"mbo-debit-card-block-page__footer"],["id","btn_debit-card-block_submit","bocc-button","raised","bocc-theme","danger","prefixIcon","block-card",3,"click"],[3,"hidden"],[1,"mbo-debit-card-block-page__result","mbo-page__scroller"],[1,"mbo-debit-card-block-page__header","mbo-page__header",3,"hidden"],[3,"rightActions"],["id","crd_debit-card-block-page_template",3,"template","action"]],template:function(r,s){1&r&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),e.\u0275\u0275element(3,"bocc-header-form",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",4)(5,"label",5),e.\u0275\u0275text(6," \xbfDeseas bloquear la siguiente tarjeta de d\xe9bito? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(7,"bocc-card-product-summary",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return s.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Bloquear"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(12,"mbo-page",9)(13,"div",10)(14,"div",11),e.\u0275\u0275element(15,"mbo-header-result",12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"div",4)(17,"mbo-card-transaction-template",13),e.\u0275\u0275listener("action",function(B){return s.onAction(B)}),e.\u0275\u0275elementEnd()()()()),2&r&&(e.\u0275\u0275property("hidden",s.blocking),e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",s.backAction)("rightAction",s.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("color",null==s.debitCard?null:s.debitCard.color)("title",null==s.debitCard?null:s.debitCard.name)("number",null==s.debitCard?null:s.debitCard.number),e.\u0275\u0275advance(5),e.\u0275\u0275property("hidden",!s.blocking),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",s.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("rightActions",s.rightActions),e.\u0275\u0275advance(2),e.\u0275\u0275property("template",s.template))},dependencies:[b.J,p.D,D.P,d.K,M.c,o.u],styles:["/*!\n * MBO DebitCardBlock Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 17/Nov/2022\n * Updated: 05/Ene/2024\n*/mbo-debit-card-block-page{position:relative;display:block;width:100%;height:100%}mbo-debit-card-block-page mbo-page{background:var(--color-carbon-lighter-200)}mbo-debit-card-block-page .mbo-debit-card-block-page__product{position:relative;display:flex;width:100%;height:100%;overflow:hidden;flex-direction:column;justify-content:space-between}mbo-debit-card-block-page .mbo-debit-card-block-page__result{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-debit-card-block-page .mbo-debit-card-block-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-debit-card-block-page .mbo-debit-card-block-page__title{color:var(--color-carbon-darker-1000)}mbo-debit-card-block-page .mbo-debit-card-block-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-debit-card-block-page .mbo-debit-card-block-page__footer button{width:100%}\n"],encapsulation:2}),i})(),n=(()=>{class i{}return i.\u0275fac=function(r){return new(r||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[v.CommonModule,h.RouterModule.forChild([{path:"",component:l}]),m.Jx,m.D1,m.P8,g.KI,g.cN,g.tu]}),i})()}}]);