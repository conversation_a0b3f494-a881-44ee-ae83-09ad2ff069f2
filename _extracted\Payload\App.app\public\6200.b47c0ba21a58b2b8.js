(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6200],{93252:(P,C,o)=>{o.d(C,{$x:()=>m,KF:()=>f,Xh:()=>s,pb:()=>h});var g=o(53113);class m{constructor(l,c,v){this.documentCustomer=l,this.channel=c,this.owner=v}}class s{constructor(l,c,v,n){this.source=l,this.amount=c,this.currencyCode=v,this.beneficiary=n}}class f{constructor(l){this.value=l}}class h extends g.LN{constructor(l,c,v){super(l,c),this.otp=v}}},99224:(P,C,o)=>{o.d(C,{M:()=>W,i:()=>y});var g=o(15861),m=o(87956),s=o(98699),f=o(93252);function h(t){return new f.Xh(t.source,t.amount,t.currencyCode,t.beneficiary)}var E=o(71776),l=o(39904),c=o(87903),v=o(42168),n=o(84757),a=o(99877);let M=(()=>{class t{constructor(e){this.http=e}send(e){const r=function I(t){return{accId:t.source.id,amt:t.amount,curCode:t.currencyCode,channel:t.beneficiary.channel.type,documentNumber:t.beneficiary.documentCustomer.number,documentType:t.beneficiary.documentCustomer.type.code}}(e);return(0,v.firstValueFrom)(this.http.post(l.bV.TRANSACTIONS.QUICK_WITHDRAWAL,r).pipe((0,n.map)(i=>{const{type:S}=(0,c.l1)(i,"SUCCESS");return new f.pb(S,i.otpInfo?.validity.validityPeriodInfo.desc||"",new f.KF(i.otpInfo?.otpValue))}))).catch(i=>{const{message:S,type:b}=(0,c.rU)(i);return new f.pb(b,S)})}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var w=o(20691);let d=(()=>{class t extends w.Store{constructor(e){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=e,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(e,r=!1){this.reduce(i=>({...i,source:e,fromCustomer:r}))}getSource(){return this.select(({source:e})=>e)}itIsFromCustomer(){return this.select(({fromCustomer:e})=>e)}setBeneficiary(e){this.reduce(r=>({...r,beneficiary:e}))}selectForBeneficiary(){return this.select(({beneficiary:e,confirmation:r})=>({beneficiary:e,confirmation:r}))}setAmount(e){this.reduce(r=>({...r,amount:e,confirmation:!0,currencyCode:"COP"}))}selectForAmount(){return this.select(({amount:e,confirmation:r,source:i})=>({amount:e,confirmation:r,source:i}))}getCurrencyCode(){return this.select(({currencyCode:e})=>e)}itIsConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(m.Yd))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),W=(()=>{class t{constructor(e,r,i){this.eventBusService=e,this.repository=r,this.store=i}setSource(e){try{return s.Either.success(this.store.setSource(e))}catch({message:r}){return s.Either.failure({message:r})}}setBeneficiary(e){try{return s.Either.success(this.store.setBeneficiary(e))}catch({message:r}){return s.Either.failure({message:r})}}setAmount(e){try{return s.Either.success(this.store.setAmount(e))}catch({message:r}){return s.Either.failure({message:r})}}reset(){try{const e=this.store.itIsFromCustomer(),r=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:e,source:r})}catch({message:e}){return s.Either.failure({message:e})}}send(){var e=this;return(0,g.Z)(function*(){const r=h(e.store.currentState),i=yield e.execute(r);return e.eventBusService.emit(i.channel),s.Either.success({withdrawal:r,status:i})})()}execute(e){try{return this.repository.send(e)}catch({message:r}){return Promise.resolve(f.pb.error(r))}}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(m.Yd),a.\u0275\u0275inject(M),a.\u0275\u0275inject(d))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var u=o(78506);let y=(()=>{class t{constructor(e,r,i){this.products=e,this.store=r,this.session=i}source(){var e=this;return(0,g.Z)(function*(){try{const r=e.store.itIsConfirmation(),i=yield e.requestAccounts();return s.Either.success({confirmation:r,products:i})}catch({message:r}){return s.Either.failure({message:r})}})()}beneficiary(e){var r=this;return(0,g.Z)(function*(){try{const i=yield r.session.customer(),S=yield r.requestAccounts(),b=r.requestAccount(S,e),A=r.store.selectForBeneficiary(),Q=r.store.itIsConfirmation();return s.Either.success({...A,confirmation:Q,customer:i,products:S,source:b})}catch({message:i}){return s.Either.failure({message:i})}})()}amount(){try{return s.Either.success(this.store.selectForAmount())}catch({message:e}){return s.Either.failure({message:e})}}confirmation(){try{const e=h(this.store.currentState);return s.Either.success({withdrawal:e})}catch({message:e}){return s.Either.failure({message:e})}}requestAccounts(){return this.products.requestAccountsForTransfer()}requestAccount(e,r){let i=this.store.getSource();return!i&&r&&(i=e.find(({id:S})=>r===S),this.store.setSource(i,!0)),i}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(m.hM),a.\u0275\u0275inject(d),a.\u0275\u0275inject(u._I))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},20534:(P,C,o)=>{o.d(C,{v:()=>l});var g=o(30263),m=o(39904),s=o(95437),f=o(99224),h=o(99877);let l=(()=>{class c{constructor(n,a,I){this.modalConfirmation=n,this.mboProvider=a,this.managerWithdrawal=I}execute(n=!0){n?this.modalConfirmation.execute({title:"Cancelar retiro",message:"\xbfEstas seguro que deseas cancelar el retiro actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerWithdrawal.reset().when({success:({fromCustomer:n,source:a})=>{n?this.mboProvider.navigation.back(m.Z6.CUSTOMER.PRODUCTS.INFO,{productId:a.id}):this.mboProvider.navigation.back(m.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(m.Z6.TRANSFERS.HOME)}})}}return c.\u0275fac=function(n){return new(n||c)(h.\u0275\u0275inject(g.$e),h.\u0275\u0275inject(s.ZL),h.\u0275\u0275inject(f.M))},c.\u0275prov=h.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},46200:(P,C,o)=>{o.r(C),o.d(C,{MboQuickWithdrawalSourcePageModule:()=>w});var g=o(17007),m=o(78007),s=o(79798),f=o(30263),h=o(15861),E=o(39904),l=o(95437),c=o(99224),v=o(20534),n=o(99877),a=o(4663),I=o(48774);let M=(()=>{class d{constructor(u,y,t,p){this.mboProvider=u,this.requestConfiguration=y,this.managerQuickWithdrawal=t,this.cancelProvider=p,this.confirmation=!1,this.requesting=!0,this.products=[],this.cancelAction={id:"btn_quick-withdrawal-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(u){this.managerQuickWithdrawal.setSource(u).when({success:()=>{this.mboProvider.navigation.next(E.Z6.TRANSACTIONS.QUICK_WITHDRAWAL.BENEFICIARY)}})}initializatedConfiguration(){var u=this;return(0,h.Z)(function*(){(yield u.requestConfiguration.source()).when({success:({confirmation:y,products:t})=>{u.products=t,u.confirmation=y}},()=>{u.requesting=!1})})()}}return d.\u0275fac=function(u){return new(u||d)(n.\u0275\u0275directiveInject(l.ZL),n.\u0275\u0275directiveInject(c.i),n.\u0275\u0275directiveInject(c.M),n.\u0275\u0275directiveInject(v.v))},d.\u0275cmp=n.\u0275\u0275defineComponent({type:d,selectors:[["mbo-quick-withdrawal-source-page"]],decls:6,vars:3,consts:[[1,"mbo-quick-withdrawal-source-page__content"],[1,"mbo-quick-withdrawal-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-quick-withdrawal-source-page__body"],["title","DISPONIBLES PARA RETIRO",3,"skeleton","products","select"]],template:function(u,y){1&u&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),n.\u0275\u0275listener("select",function(p){return y.onProduct(p)}),n.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas retirar hoy? "),n.\u0275\u0275elementEnd()()()),2&u&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("rightAction",y.cancelAction),n.\u0275\u0275advance(2),n.\u0275\u0275property("skeleton",y.requesting)("products",y.products))},dependencies:[a.c,I.J],styles:["/*!\n * MBO QuickWithdrawalSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ene/2023\n * Updated: 04/Ene/2024\n*/mbo-quick-withdrawal-source-page{position:relative;display:block;width:100%;height:100%;overflow-x:hidden;overflow-y:auto}mbo-quick-withdrawal-source-page .mbo-quick-withdrawal-source-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-quick-withdrawal-source-page .mbo-quick-withdrawal-source-page__body{position:relative;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),d})(),w=(()=>{class d{}return d.\u0275fac=function(u){return new(u||d)},d.\u0275mod=n.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=n.\u0275\u0275defineInjector({imports:[g.CommonModule,m.RouterModule.forChild([{path:"",component:M}]),s.cV,f.Jx]}),d})()}}]);