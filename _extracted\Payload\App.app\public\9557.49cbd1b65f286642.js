(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9557],{84415:(G,I,a)=>{a.d(I,{Hc:()=>i,IV:()=>f,KR:()=>T,iD:()=>L,sT:()=>x,ve:()=>y,xg:()=>m,z8:()=>g});var c=a(83136);const g={label:"Espa\xf1ol",value:"ES"},i={label:"Ingl\xe9s",value:"EN"},A={value:c.k.RetentionGmf,fileName:"retention-gmf",label:"Gravamen movimiento financiero",tooltip:"Valores retenidos por impuesto Gravamen al Movimiento Financiero <b>(4x1.000)</b> aplicado a las transacciones financieras realizadas por el cliente",years:5,discountYear:!0},d={value:c.k.CapitalCdtCaf,fileName:"capital-cdt-caf",label:"Saldos de capital en CDT - CAF",tooltip:"Certificado de dep\xf3sito a t\xe9rmino que le permite invertir su dinero para recibir rendimientos de acuerdo al plazo establecido en el momento en que se constituye",years:5,discountYear:!0},R={value:c.k.CapitalBalance,fileName:"capital-balance",label:"Saldos de cartera e intereses pagados",tooltip:"Certificado de las obligaciones (Veh\xedculos o Motos) que los clientes tienen con el Banco",years:5,discountYear:!0},F={value:c.k.Accounts,fileName:"tax-accounts",label:"Saldos de cuentas de ahorros y corrientes",tooltip:"Constancia que contiene la informaci\xf3n de los intereses pagados y el saldo a diciembre 31 de sus productos de cuentas de ahorros y corrientes",years:5,discountYear:!0},T=[F,A,R,d,{value:c.k.ConsumerCardInterests,fileName:"customer-card-interests-mastercard",label:"Consumo e intereses de tarjeta cr\xe9dito (Mastercard)",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito MasterCard",years:5,discountYear:!0,franchise:!0,franchiseValue:"2"},{value:c.k.ConsumerCardInterests,fileName:"customer-card-interests-visa",label:"Consumo e intereses de tarjeta cr\xe9dito (Visa)",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito Visa",years:5,discountYear:!0,franchise:!0,franchiseValue:"1"}],f=[F,A,R,d,{value:c.k.ConsumerCardInterests,fileName:"customer-card-interests-visa",label:"Consumo e intereses de tarjeta cr\xe9dito",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito Visa o MasterCard",years:5,discountYear:!0,franchise:!0},{label:"Retenci\xf3n en la fuente",fileName:"retefuente",value:c.k.ReteFuente,tooltip:"Valores retenidos por concepto de impuesto de RENTA, aplicado a los clientes y/o proveedores detallado por cada tipo de pago realizado",years:5,discountYear:!0},{value:c.k.ReteIvaBank,fileName:"reteiva-bank",label:"Retenci\xf3n por IVA en Banco",tooltip:"Valores retenidos por concepto de impuesto de IVA, aplicado a los proveedores en la compra de bienes y servicios",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"},{value:c.k.ReteIcaBank,fileName:"reteica-bank",label:"Retenci\xf3n por ICA de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de industria y comercio, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito, seg\xfan las tarifas establecidas en cada municipio",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"},{value:c.k.Establishments,fileName:"tax-establishments",label:"Recaudo y cobro de comisiones de dat\xe1fonos",tooltip:" Corresponde a las comisiones cobradas durante el a\xf1o de las ventas realizadas por su comercio con tarjetas de cr\xe9dito o d\xe9bito de las franquicias MasterCard, Visa y American Express",years:5,discountYear:!0},{value:c.k.RetefuenteCard,fileName:"retefuente-card",label:"Retenci\xf3n en la fuente de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de RENTA, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito",years:5,discountYear:!0},{value:c.k.ReteIvaBankCards,fileName:"reteiva-bank-cards",label:"Retenci\xf3n por IVA de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de IVA, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"}],m=[{value:"B",label:"Bimestral",period:!0},{value:"A",label:"Anual",period:!1}],y=[{value:"1",label:"Enero - Febrero"},{value:"2",label:"Marzo - Abril"},{value:"3",label:"Mayo - Junio"},{value:"4",label:"Julio - Agosto"},{value:"5",label:"Septiembre - Octubre"},{value:"6",label:"Noviembre - Diciembre"}],L=[{value:"1",label:"Visa"},{value:"2",label:"Mastercard"}],x=[{value:"pdf",label:"PDF"},{value:"zip",label:"ZIP"}]},83136:(G,I,a)=>{a.d(I,{S:()=>g,k:()=>c});var c=(()=>{return(i=c||(c={})).ConsumerCardInterests="CERTIFICATE_CONSUMER_CARD_INTERESTS",i.CapitalBalance="CERTIFICATE_OF_CAPITAL_BALANCE",i.CapitalCdtCaf="CERTIFICATE_OF_CAPITAL_CDT_CAF",i.ReteIvaBank="CERTIFICATE_RETEIVA_BANK",i.ReteIcaBank="CERTIFICATE_RETEICA_BANK",i.ReteIvaBankCards="CERTIFICATE_RETEIVA_BANK_TC_TD",i.RetentionGmf="GMF_RETENTION_CERTIFICATE",i.ReteFuente="RETEFUENT_CERTIFICATE",i.RetefuenteCard="RETEFUENT_CERTIFICATE_TC",i.Establishments="TAX_CERTIFICATE_ESTABLISHMENTS",i.Accounts="SAVINGS_CHECKING_ACCOUNTS_CERTIFICATE",c;var i})(),g=(()=>{return(i=g||(g={})).CheckingAccount="CHECKING_ACCOUNT_CERTIFICATE",i.SavingAccount="SAVINGS_ACCOUNT_CERTIFICATE",g;var i})()},14934:(G,I,a)=>{a.d(I,{H:()=>x,w:()=>L});var c=a(15861),g=a(38165),i=a(87903),S=a(87956),E=a(98699),v=a(28640),P=a.n(v),A=a(83121),d=a(84415),R=a(89148),b=a(83136),_=a(71776),D=a(39904),M=a(42168),O=a(84757),T=a(99877);let f=(()=>{class r{constructor(e){this.http=e}requestProduct(e){return(0,M.firstValueFrom)(this.http.post(D.bV.CERTIFICATES,e).pipe((0,O.map)(({file:o})=>o)))}requestTax(e){return(0,M.firstValueFrom)(this.http.post(D.bV.CERTIFICATES,e).pipe((0,O.map)(({file:o})=>o)))}}return r.\u0275fac=function(e){return new(e||r)(T.\u0275\u0275inject(_.HttpClient))},r.\u0275prov=T.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();const m={DDA:"corriente",SDA:"ahorros"};function y({status:r},u){return r!==g.K.Saved?E.Either.failure({message:`${u} (${g.K.NotCreated?"RTC01":"RTC02"})`,value:"Generaci\xf3n no exitosa"}):E.Either.success(void 0)}let L=(()=>{class r{constructor(e){this.customerProducts=e}products(){try{return this.customerProducts.requestAccountsForTransfer().then(e=>E.Either.success(e))}catch({message:e}){return Promise.resolve(E.Either.failure({message:e}))}}taxes(){return Promise.resolve(E.Either.success({certificates:{all:d.IV,rents:d.KR},files:d.sT,franchises:d.iD,periodicities:d.xg,periods:d.ve}))}}return r.\u0275fac=function(e){return new(e||r)(T.\u0275\u0275inject(S.hM))},r.\u0275prov=T.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),x=(()=>{class r{constructor(e,o){this.fileManagerService=e,this.repository=o,this.productsName={},this.taxsName={}}product(e){return this.repository.requestProduct(function F(r){const{addressedto:u,balance:e,language:o,product:s}=r;return{addressedto:u,currentBalanceValue:e?s.amount:0,key:s.type===R.Gt.SavingAccount?b.S.SavingAccount:b.S.CheckingAccount,id:s.number,language:o,period:{periodDesc:null,periodCode:null,validityPeriod:null},transactionSource:null,withBalanceValue:e}}(e)).then(o=>{const{balance:s,product:l}=e;let p=this.productsName[l.type];return p||(p=`${m[l.type]}${s?"-saldo-":""}${l.shortNumber}-${(0,i.CW)()}.pdf`,this.productsName[l.type]=p),this.saveCertificate(o,p)}).catch(()=>E.Either.failure({message:"Lo sentimos, ocurrio un error al tratar de generar certificado, por favor intente m\xe1s tarde.",value:"Generaci\xf3n no exitosa"}))}tax(e){return this.requestTax({...e,single:!0}).then(({base64:o,name:s})=>this.saveCertificate(o,s)).catch(()=>E.Either.failure({message:"No pudimos generar certificado, por favor intente m\xe1s tarde.",value:"Generaci\xf3n no exitosa"}))}taxsForRent(e){const{certificates:o,type:s,year:l}=e;return(0,E.zipPromise)(o.map(p=>()=>this.requestTax({certificate:p,year:l,single:!1}).catch(()=>{}))).then(p=>{const C=p.filter(N=>!!N);return C.length?"pdf"===s?this.saveCertificatesRentWithPdf(C,l):this.saveCertificatesRentWithZip(C,l):E.Either.failure({message:"No cuentas con certificados para la declaraci\xf3n de renta",value:"Generaci\xf3n no exitosa"})})}requestTax(e){return this.repository.requestTax(function t(r){const{certificate:u,year:e,franchise:o,period:s,periodicity:l}=r;return{currentBalanceValue:0,key:u.value,id:null,period:{periodDesc:String(e),periodCode:l?.value||null,validityPeriod:s?.value&&"A"===l?.value?"1":null},transactionSource:(u.franchise?o?.value||u.franchiseValue:u.transactionSource)||null,withBalanceValue:!1}}(e)).then(o=>{const{certificate:s,single:l}=e,p=s.value;let C=this.taxsName[p];return C||(C=l?`${s.fileName}-${(0,i.CW)()}.pdf`:`${s.fileName}.pdf`,this.taxsName[p]=C),{base64:o,name:C}})}saveCertificate(e,o){return this.fileManagerService.downloadPdf({base64:e,name:o}).then(s=>y(s,"Lo sentimos, ocurrio un error al tratar de guardar certificado en el dispositivo. Por favor volver a intentarlo"))}saveCertificatesRentWithPdf(e,o){var s=this;return(0,c.Z)(function*(){const l=yield A.PDFDocument.create();return l.setProducer("com.grupoavaloc1.bancamovil"),l.setCreationDate(new Date),yield Promise.all(e.map(function(){var p=(0,c.Z)(function*({base64:C}){const N=yield A.PDFDocument.load(C,{ignoreEncryption:!0});(yield l.copyPages(N,N.getPageIndices())).forEach(h=>{l.addPage(h)})});return function(C){return p.apply(this,arguments)}}())),l.saveAsBase64().then(p=>s.fileManagerService.downloadPdf({base64:p,name:`occ-certificados-renta-${o}-${(0,i.CW)()}.pdf`}).then(C=>y(C,"No pudimos almacenar los certificados en el dispositivo. Por favor volver a intentarlo")))})()}saveCertificatesRentWithZip(e,o){const s=new(P());return e.forEach(({base64:l,name:p})=>{s.file(p,l,{base64:!0})}),s.generateAsync({type:"base64"}).then(l=>this.fileManagerService.downloadZip({base64:l,name:`occ-certificados-renta-${o}-${(0,i.CW)()}.zip`}).then(p=>y(p,"No pudimos almacenar los certificados en el dispositivo. Por favor volver a intentarlo")))}}return r.\u0275fac=function(e){return new(e||r)(T.\u0275\u0275inject(S.j5),T.\u0275\u0275inject(f))},r.\u0275prov=T.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},19557:(G,I,a)=>{a.r(I),a.d(I,{MboProductCertificationsPageModule:()=>x});var c=a(17007),g=a(78007),i=a(30263),S=a(15861),E=a(39904),v=a(88844),P=a(95437),A=a(84415),d=a(14934),R=a(24495),b=a(57544);class F extends b.FormGroup{constructor(){super({controls:{product:new b.FormControl(void 0,[R.C1]),language:new b.FormControl(A.z8,[R.C1]),addressedTo:new b.FormControl,balance:new b.FormControl(!1)}})}get product(){return this.controls.product}get language(){return this.controls.language}get addressedTo(){return this.controls.addressedTo}get balance(){return this.controls.balance}certification(){return{addressedto:this.controls.addressedTo.value,balance:this.controls.balance.value,language:this.controls.language.value.value,product:this.controls.product.value}}}var t=a(99877),_=a(48774),D=a(45542),M=a(60817),O=a(10219),T=a(64181),f=a(48030);const{CERTIFICATIONS:m,PRODUCTS:y}=E.Z6.CUSTOMER;let L=(()=>{class r{constructor(e,o,s){this.mboProvider=e,this.requestConfiguration=o,this.downloader=s,this.languageSpanish=A.z8,this.languageEnglish=A.Hc,this.requesting=!0,this.locked=!1,this.products=[],this.backAction={id:"btn_product-certifications_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(m.HOME)}},this.cancelAction={id:"btn_product-certifications_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.back(y.HOME)}},this.controls=new F}ngOnInit(){this.unsubscription=this.controls.language.subscribe(({value:e})=>{this.locked="EN"===e,this.locked&&(this.controls.balance.reset(),this.controls.addressedTo.reset())}),this.initializedConfiguration()}ngOnDestroy(){this.unsubscription()}onRequest(){var e=this;return(0,S.Z)(function*(){e.mboProvider.loader.open("Solicitando, por favor espere..."),(yield e.downloader.product(e.controls.certification())).when({failure:({message:o,value:s})=>{e.mboProvider.toast.error(o,s)}},()=>{e.mboProvider.loader.close()})})()}initializedConfiguration(){var e=this;return(0,S.Z)(function*(){(yield e.requestConfiguration.products()).when({success:o=>{e.products=(0,v.t1)(o)},failure:()=>{e.mboProvider.toast.error("En estos momentos no es posible generar este certificado, por favor int\xe9ntelo de nuevo.","Error en productos")}},()=>{e.requesting=!1})})()}}return r.\u0275fac=function(e){return new(e||r)(t.\u0275\u0275directiveInject(P.ZL),t.\u0275\u0275directiveInject(d.w),t.\u0275\u0275directiveInject(d.H))},r.\u0275cmp=t.\u0275\u0275defineComponent({type:r,selectors:[["mbo-product-certifications-page"]],decls:20,vars:15,consts:[[1,"mbo-product-certifications-page__content"],[1,"mbo-product-certifications-page__header"],["title","C. de productos",3,"leftAction","rightAction"],[1,"mbo-product-certifications-page__body"],["elementId","lst_product-certifications_product","placeholder","Seleccionar producto","label","Producto",3,"disabled","suggestions","formControl"],[1,"mbo-product-certifications-page__languages"],[1,"smalltext-medium"],[1,"mbo-product-certifications-page__radiobuttons"],[3,"formControl","value"],["elementId","txt_product-certifications_addressedto","label","Dirigido a (Opcional)","placeholder","Ingresa un nombre",3,"formControl"],["elementId","lst_product-certifications_balance",3,"formControl","disabled"],[1,"mbo-product-certifications-page__footer"],["id","btn_product-certifications_submit","bocc-button","raised","prefixIcon","certificate-products",3,"disabled","click"]],template:function(e,o){1&e&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3),t.\u0275\u0275element(4,"bocc-select-box",4),t.\u0275\u0275elementStart(5,"div",5)(6,"label",6),t.\u0275\u0275text(7,"Idioma"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(8,"div",7)(9,"bocc-radiobutton-label",8),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"bocc-radiobutton-label",8),t.\u0275\u0275text(12),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275element(13,"bocc-input-box",9),t.\u0275\u0275elementStart(14,"bocc-checkbox-label",10),t.\u0275\u0275text(15," Constancia con saldo actual "),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(16,"div",11)(17,"button",12),t.\u0275\u0275listener("click",function(){return o.onRequest()}),t.\u0275\u0275elementStart(18,"span"),t.\u0275\u0275text(19,"Generar certificado"),t.\u0275\u0275elementEnd()()()),2&e&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),t.\u0275\u0275advance(2),t.\u0275\u0275property("disabled",o.requesting||!o.products.length)("suggestions",o.products)("formControl",o.controls.product),t.\u0275\u0275advance(5),t.\u0275\u0275property("formControl",o.controls.language)("value",o.languageSpanish),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",o.languageSpanish.label," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",o.controls.language)("value",o.languageEnglish),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",o.languageEnglish.label," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",o.controls.addressedTo),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",o.controls.balance)("disabled",o.locked),t.\u0275\u0275advance(3),t.\u0275\u0275property("disabled",o.controls.invalid))},dependencies:[_.J,D.P,M.a,O.T,T.D,f.t],styles:["/*!\n * MBO ProductCertifications Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 04/Oct/2022\n * Updated: 21/Ago/2024\n*/mbo-product-certifications-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-product-certifications-page .mbo-product-certifications-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-certifications-page .mbo-product-certifications-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-product-certifications-page .mbo-product-certifications-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-certifications-page .mbo-product-certifications-page__languages{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-product-certifications-page .mbo-product-certifications-page__languages label{color:var(--color-carbon-lighter-700)}mbo-product-certifications-page .mbo-product-certifications-page__radiobuttons{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-certifications-page .mbo-product-certifications-page__radiobuttons bocc-radiobutton-label{width:100%}mbo-product-certifications-page .mbo-product-certifications-page__footer{padding:var(--sizing-safe-footer-x8);box-sizing:border-box;width:100%}mbo-product-certifications-page .mbo-product-certifications-page__footer button{width:100%}\n"],encapsulation:2}),r})(),x=(()=>{class r{}return r.\u0275fac=function(e){return new(e||r)},r.\u0275mod=t.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=t.\u0275\u0275defineInjector({imports:[c.CommonModule,g.RouterModule.forChild([{path:"",component:L}]),i.Jx,i.P8,i.aR,i.T9,i.DT,i.tv]}),r})()},88844:(G,I,a)=>{a.d(I,{YI:()=>A,tc:()=>M,iR:()=>D,jq:()=>R,Hv:()=>P,S6:()=>b,E2:()=>t,V4:()=>_,wp:()=>j,CE:()=>y,YQ:()=>i,ND:()=>E,t1:()=>T});var c=a(6472);class g{constructor(n){this.value=n}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:n}){return this.value.id===n}filtrable(n){return(0,c.hasPattern)(this.value.name,n)}}function i(h){return h.map(n=>new g(n))}class S{constructor(n){this.currency=n}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(n){return this.currency.code===n?.code}filtrable(n){return!0}}function E(h){return h.map(n=>new S(n))}var v=a(39904);class P{constructor(n){this.value=n}get title(){return this.value.label}get description(){return this.value.label}compareTo(n){return this.value.reference===n.reference}filtrable(n){return!0}}const A=v.Bf.map(h=>new P(h));class d{constructor(n,V){this.value=n,this.title=this.value.label,this.description=V?this.value.code:this.value.label}compareTo(n){return this.value===n}filtrable(n){return!0}}const R=new d(v.Gd),b=new d(v.XU),F=new d(v.t$),t=new d(v.j1),_=new d(v.k7),D=[R,b,F,t],M=[new d(v.Gd,!0),new d(v.XU,!0),new d(v.t$,!0),new d(v.j1,!0)];class O{constructor(n){this.product=n}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(n){return this.product.id===n?.id}filtrable(n){return!0}}function T(h){return h.map(n=>new O(n))}var f=a(89148);class m{constructor(n){this.filter=n}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(n){return this.value===n}filtrable(n){return!0}}const y=new m({label:"Todos los productos",short:"Todos",value:f.Gt.None}),L=new m({label:"Cuentas de ahorro",short:"Ahorros",value:f.Gt.SavingAccount}),x=new m({label:"Cuentas corriente",short:"Corrientes",value:f.Gt.CheckingAccount}),r=new m({label:"Depositos electr\xf3nicos",short:"Depositos",value:f.Gt.ElectronicDeposit}),u=new m({label:"Cuentas AFC",short:"AFC",value:f.Gt.AfcAccount}),e=new m({label:"Tarjetas de cr\xe9dito",short:"TC",value:f.Gt.CreditCard}),o=new m({label:"Inversiones",short:"Inversiones",value:f.Gt.CdtAccount}),s=new m({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:f.Gt.Loan}),l=new m({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:f.Gt.ResolvingCredit}),p=new m({label:"Productos Aval",short:"Aval",value:f.Gt.Aval}),C=new m({label:"Productos fiduciarios",short:"Fiducias",value:f.Gt.Trustfund}),N=new m({label:"Otros productos",short:"Otros",value:f.Gt.None}),j={SDA:L,DDA:x,EDA:r,AFC:u,CCA:e,CDA:o,DLA:s,LOC:l,AVAL:p,80:C,MDA:N,NONE:N,SBA:N,VDA:N}}}]);