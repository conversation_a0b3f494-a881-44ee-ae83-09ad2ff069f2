(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3116],{93116:(u,a,n)=>{n.r(a),n.d(a,{MboPaymentLoanModule:()=>h});var l=n(17007),d=n(78007),t=n(99877);const M=[{path:"",redirectTo:"destination",pathMatch:"full"},{path:"destination",loadChildren:()=>n.e(4554).then(n.bind(n,24554)).then(o=>o.MboPaymentLoanDestinationPageModule)},{path:"source",loadChildren:()=>n.e(9566).then(n.bind(n,79566)).then(o=>o.MboPaymentLoanSourcePageModule)},{path:"select-amount",loadChildren:()=>n.e(2799).then(n.bind(n,2799)).then(o=>o.MboPaymentLoanSelectAmountPageModule)},{path:"amount",loadChildren:()=>n.e(1765).then(n.bind(n,41765)).then(o=>o.MboPaymentLoanAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(6798).then(n.bind(n,66798)).then(o=>o.MboPaymentLoanConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(5597).then(n.bind(n,75597)).then(o=>o.MboPaymentLoanResultPageModule)}];let h=(()=>{class o{}return o.\u0275fac=function(m){return new(m||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[l.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);