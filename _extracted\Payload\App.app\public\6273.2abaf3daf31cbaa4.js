(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6273],{16273:(D,E,t)=>{t.r(E),t.d(E,{MboEnrollmentSuccessPageModule:()=>b});var a=t(17007),i=t(78007),e=t(79798),o=t(30263),m=t(33395),r=t(65518),d=t(39904),v=t(95437),l=t(98699),s=t(99877),f=t(25317),n=t(66613),c=t(45542),g=t(52528),h=t(19102);let u=(()=>{class p{constructor(P,y){this.mboProvider=P,this.enrollmentStore=y,this.password=!1}ngOnInit(){this.password=(0,l.itIsDefined)(this.enrollmentStore.currentState.password)}onSubmit(){this.mboProvider.navigation.back(d.Z6.AUTHENTICATION.LOGIN)}}return p.\u0275fac=function(P){return new(P||p)(s.\u0275\u0275directiveInject(v.ZL),s.\u0275\u0275directiveInject(r.c))},p.\u0275cmp=s.\u0275\u0275defineComponent({type:p,selectors:[["mbo-enrollment-success-page"]],decls:18,vars:2,consts:[[1,"mbo-enrollment-success-page",3,"header"],["body","",1,"mbo-enrollment-success-page__body"],["body","",1,"mbo-enrollment-success-page__info"],[1,"mbo-enrollment-success-page__title","subtitle2-medium"],[1,"mbo-enrollment-success-page__logo"],["src","assets/authentication/logos/enrollment-success.svg"],[1,"mbo-enrollment-success-page__message","body2-medium"],["icon","bell",3,"visible"],["footer","",1,"mbo-enrollment-success-page__footer"],["id","btn_enrollment-signup_exit","bocc-button","raised","boccUtagComponent","click",3,"click"]],template:function(P,y){1&P&&(s.\u0275\u0275elementStart(0,"bocc-template-form",0)(1,"div",1),s.\u0275\u0275element(2,"mbo-bank-logo"),s.\u0275\u0275elementStart(3,"div",2)(4,"div",3),s.\u0275\u0275text(5," Registro Banca M\xf3vil "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(6,"div",4),s.\u0275\u0275element(7,"img",5),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(8,"p",6),s.\u0275\u0275text(9," Hemos terminado el registro a nuestros canales digitales de manera exitosa. "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(10,"bocc-alert",7)(11,"b"),s.\u0275\u0275text(12,"Recuerda:"),s.\u0275\u0275elementEnd(),s.\u0275\u0275text(13," la contrase\xf1a que acabas de asignar ser\xe1 \xfanica y te servir\xe1 para Ingresar al Portal Transaccional y a la Aplicaci\xf3n de Banca M\xf3vil. "),s.\u0275\u0275elementEnd()()(),s.\u0275\u0275elementStart(14,"div",8)(15,"button",9),s.\u0275\u0275listener("click",function(){return y.onSubmit()}),s.\u0275\u0275elementStart(16,"span"),s.\u0275\u0275text(17,"Iniciar sesi\xf3n"),s.\u0275\u0275elementEnd()()()()),2&P&&(s.\u0275\u0275property("header",!1),s.\u0275\u0275advance(10),s.\u0275\u0275property("visible",y.password))},dependencies:[f.I,n.B,c.P,g.A,h.r],styles:["mbo-enrollment-success-page{--mbo-bank-logo-height: 26rem;--pvt-logo-margin-top: var(--sizing-x28);--pvt-body-rowgap: var(--sizing-x16);--pvt-info-rowgap: var(--sizing-x12)}mbo-enrollment-success-page .mbo-enrollment-success-page{padding:0rem var(--sizing-form);box-sizing:border-box}mbo-enrollment-success-page .mbo-enrollment-success-page__body{position:relative;display:flex;flex-direction:column;width:100%;row-gap:var(--pvt-body-rowgap);padding-top:var(--sizing-safe-top, 0rem);box-sizing:border-box}mbo-enrollment-success-page .mbo-enrollment-success-page__body mbo-bank-logo{margin-top:var(--pvt-logo-margin-top)}mbo-enrollment-success-page .mbo-enrollment-success-page__info{position:relative;display:flex;flex-direction:column;width:100%;row-gap:var(--pvt-info-rowgap)}mbo-enrollment-success-page .mbo-enrollment-success-page__title{position:relative;width:100%;text-align:center}mbo-enrollment-success-page .mbo-enrollment-success-page__logo{position:relative;display:flex;justify-content:center;width:100%}mbo-enrollment-success-page .mbo-enrollment-success-page__logo img{width:44rem;height:44rem}mbo-enrollment-success-page .mbo-enrollment-success-page__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-enrollment-success-page .mbo-enrollment-success-page__footer{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x6);width:100%;padding:var(--sizing-x8) 0rem var(--sizing-safe-bottom-x8) 0rem}mbo-enrollment-success-page .mbo-enrollment-success-page__footer button{width:100%}@media screen and (max-height: 800px){mbo-enrollment-success-page{--mbo-bank-logo-height: var(--sizing-x24)}}@media screen and (max-height: 700px){mbo-enrollment-success-page{--mbo-bank-logo-height: var(--sizing-x22)}}@media screen and (max-height: 600px){mbo-enrollment-success-page{--mbo-bank-logo-height: var(--sizing-x20);--pvt-logo-margin-top: var(--sizing-x16);--pvt-info-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),p})(),b=(()=>{class p{}return p.\u0275fac=function(P){return new(P||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[a.CommonModule,i.RouterModule.forChild([{path:"",component:u}]),m.kW,o.B4,o.P8,o.Av,e.rw]}),p})()},65518:(D,E,t)=>{t.d(E,{c:()=>m});var a=t(20691),e=t(99877);let m=(()=>{class r extends a.Store{constructor(){super({})}getDocument(){return this.select(v=>({documentType:v.documentType,documentNumber:v.documentNumber,documentRemember:v.documentRemember}))}setDocument(v,l,s){this.reduce(f=>({...f,documentType:v,documentNumber:l,documentRemember:s}))}setEnrollmentKey(v){this.reduce(l=>({...l,enrollmentKey:v}))}setPassword(v){this.reduce(l=>({...l,password:v}))}setSecureData(v){this.reduce(l=>({...l,secureData:v}))}getSecureData(){return this.select(({secureData:v})=>v)}setErrorCode(v){this.reduce(l=>({...l,errorCode:v}))}getErrorCode(){return this.select(({errorCode:v})=>v)}}return r.\u0275fac=function(v){return new(v||r)},r.\u0275prov=e.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},19102:(D,E,t)=>{t.d(E,{r:()=>m});var a=t(17007),e=t(99877);let m=(()=>{class r{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return r.\u0275fac=function(v){return new(v||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(v,l){1&v&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&v&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",l.src,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),r})()},52701:(D,E,t)=>{t.d(E,{q:()=>r});var a=t(17007),e=t(30263),o=t(99877);let r=(()=>{class d{}return d.\u0275fac=function(l){return new(l||d)},d.\u0275cmp=o.\u0275\u0275defineComponent({type:d,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(l,s){1&l&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"bocc-icon",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()),2&l&&(o.\u0275\u0275classMap(s.classTheme),o.\u0275\u0275advance(3),o.\u0275\u0275property("icon",s.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",s.label," "))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),d})()},55648:(D,E,t)=>{t.d(E,{u:()=>s});var a=t(15861),i=t(17007),o=t(30263),m=t(78506),r=t(99877);function v(f,n){if(1&f){const c=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",2),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(c);const h=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(h.onClick())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd()()}if(2&f){const c=r.\u0275\u0275nextContext();r.\u0275\u0275property("prefixIcon",c.icon)("disabled",c.disabled),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" ",c.label," ")}}function l(f,n){if(1&f){const c=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",3),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(c);const h=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(h.onClick())}),r.\u0275\u0275elementEnd()}if(2&f){const c=r.\u0275\u0275nextContext();r.\u0275\u0275property("bocc-button-action",c.icon)("disabled",c.disabled)}}let s=(()=>{class f{constructor(c){this.preferences=c,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:c})=>{this.isIncognito=c||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var c=this;return(0,a.Z)(function*(){yield c.preferences.toggleIncognito()})()}}return f.\u0275fac=function(c){return new(c||f)(r.\u0275\u0275directiveInject(m.Bx))},f.\u0275cmp=r.\u0275\u0275defineComponent({type:f,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(c,g){1&c&&(r.\u0275\u0275template(0,v,3,3,"button",0),r.\u0275\u0275template(1,l,1,2,"button",1)),2&c&&(r.\u0275\u0275property("ngIf",!g.actionMode),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",g.actionMode))},dependencies:[i.CommonModule,i.NgIf,o.P8,o.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),f})()},72765:(D,E,t)=>{t.d(E,{rw:()=>a.r,qr:()=>i.q,uf:()=>e.u,Z:()=>v,t5:()=>h,$O:()=>g});var a=t(19102),i=t(52701),e=t(55648),o=t(17007),m=t(30263),r=t(99877);const d=["*"];let v=(()=>{class u{constructor(){this.disabled=!1}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=r.\u0275\u0275defineComponent({type:u,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(p,_){1&p&&(r.\u0275\u0275projectionDef(),r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3),r.\u0275\u0275projection(4),r.\u0275\u0275elementEnd()()),2&p&&(r.\u0275\u0275classProp("mbo-poster__content--disabled",_.disabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",_.icon))},dependencies:[o.CommonModule,m.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),u})();var l=t(33395),s=t(77279),f=t(87903),n=t(87956),c=t(25317);let g=(()=>{class u{constructor(p){this.eventBusService=p}onCopy(){this.value&&((0,f.Bn)(this.value),this.eventBusService.emit(s.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return u.\u0275fac=function(p){return new(p||u)(r.\u0275\u0275directiveInject(n.Yd))},u.\u0275cmp=r.\u0275\u0275defineComponent({type:u,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(p,_){1&p&&(r.\u0275\u0275elementStart(0,"bocc-icon",0),r.\u0275\u0275listener("click",function(){return _.onCopy()}),r.\u0275\u0275elementEnd()),2&p&&r.\u0275\u0275property("id",_.elementId)},dependencies:[o.CommonModule,m.Zl,l.kW,c.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),u})(),h=(()=>{class u{}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=r.\u0275\u0275defineComponent({type:u,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(p,_){1&p&&r.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&p&&(r.\u0275\u0275property("value",_.value),r.\u0275\u0275advance(1),r.\u0275\u0275property("value",_.value))},dependencies:[o.CommonModule,m.qd,g],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),u})()},79798:(D,E,t)=>{t.d(E,{Vc:()=>i.Vc,rw:()=>a.rw,k4:()=>i.k4,qr:()=>a.qr,uf:()=>a.uf,xO:()=>o.x,A6:()=>e.A,tu:()=>c,Tj:()=>g,GI:()=>T,Uy:()=>k,To:()=>H,w7:()=>X,o2:()=>i.o2,B_:()=>i.B_,fi:()=>i.fi,XH:()=>i.XH,cN:()=>i.cN,Aj:()=>i.Aj,J5:()=>i.J5,DB:()=>G.D,NH:()=>O.N,ES:()=>$.E,Nu:()=>i.Nu,x6:()=>j.x,KI:()=>J.K,iF:()=>i.iF,u8:()=>q.u,eM:()=>te.e,ZF:()=>oe.Z,wu:()=>ne.w,$n:()=>re.$,KN:()=>ie.K,cV:()=>ae.c,t5:()=>a.t5,$O:()=>a.$O,ZS:()=>ce.Z,sO:()=>se.s,bL:()=>pe,zO:()=>ee.z});var a=t(72765),i=t(27302),e=t(1027),o=t(7427),r=(t(16442),t(17007)),d=t(30263),v=t(44487),l=t.n(v),s=t(13462),f=t(21498),n=t(99877);let c=(()=>{class I{}return I.\u0275fac=function(C){return new(C||I)},I.\u0275mod=n.\u0275\u0275defineNgModule({type:I}),I.\u0275inj=n.\u0275\u0275defineInjector({imports:[r.CommonModule,s.LottieModule.forRoot({player:()=>l()}),a.rw,d.P8,d.Dj,f.P]}),I})(),g=(()=>{class I{ngBoccPortal(C){this.portal=C}onSubmit(){this.portal?.close()}}return I.\u0275fac=function(C){return new(C||I)},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-icon",2),n.\u0275\u0275elementStart(3,"label"),n.\u0275\u0275text(4," \xa1Atenci\xf3n! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p"),n.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),n.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"li",4),n.\u0275\u0275text(11,"Transacciones a celulares."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"li",4),n.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(14,"li",4),n.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(16,"p",5),n.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(18,"div",6)(19,"button",7),n.\u0275\u0275listener("click",function(){return B.onSubmit()}),n.\u0275\u0275elementStart(20,"span"),n.\u0275\u0275text(21,"Continuar"),n.\u0275\u0275elementEnd()()())},dependencies:[r.CommonModule,d.Zl,d.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),I})();var h=t(7603),u=t(87956),b=t(74520),p=t(39904),_=t(87903);function y(I,w){if(1&I){const C=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",6)(1,"label",7),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",8),n.\u0275\u0275text(4,"Tu gerente asignado (a)"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p",8),n.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(7,"button",9),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(C);const K=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(K.onEmail(K.manager.email))}),n.\u0275\u0275elementStart(8,"span",10),n.\u0275\u0275text(9),n.\u0275\u0275elementEnd()()()}if(2&I){const C=n.\u0275\u0275nextContext(2);n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",C.manager.name," "),n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",C.manager.email," ")}}function x(I,w){if(1&I){const C=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),n.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"button",12),n.\u0275\u0275listener("click",function(K){n.\u0275\u0275restoreView(C);const Y=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(Y.onRetryManager(K))}),n.\u0275\u0275elementStart(4,"span"),n.\u0275\u0275text(5,"Recargar"),n.\u0275\u0275elementEnd()()()}}function S(I,w){if(1&I&&(n.\u0275\u0275elementStart(0,"div",3),n.\u0275\u0275template(1,y,10,2,"div",4),n.\u0275\u0275template(2,x,6,0,"div",5),n.\u0275\u0275elementEnd()),2&I){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",C.manager),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!C.manager)}}function M(I,w){1&I&&(n.\u0275\u0275elementStart(0,"div",13),n.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),n.\u0275\u0275elementEnd()),2&I&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0))}t(29306);let z=(()=>{class I{constructor(C){this.customerService=C,this.requesting=!1}onRetryManager(C){this.customerService.requestManager(),C.stopPropagation()}onEmail(C){(0,_.Gw)(`mailto:${C}`)}onWhatsapp(){(0,_.Gw)(p.BA.WHATSAPP)}}return I.\u0275fac=function(C){return new(C||I)(n.\u0275\u0275directiveInject(u.vZ))},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,S,3,2,"div",1),n.\u0275\u0275template(2,M,5,4,"div",2),n.\u0275\u0275elementEnd()),2&C&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!B.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.requesting))},dependencies:[r.CommonModule,r.NgIf,d.P8,d.Dj,i.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),I})();const L={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},N={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},F={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function U(I,w){if(1&I&&(n.\u0275\u0275elementStart(0,"div",7),n.\u0275\u0275element(1,"mbo-contact-manager",8),n.\u0275\u0275elementEnd()),2&I){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("manager",C.manager)("requesting",C.requesting)}}function W(I,w){if(1&I){const C=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"li",9)(1,"div",10),n.\u0275\u0275listener("click",function(K){const ue=n.\u0275\u0275restoreView(C).$implicit,be=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(be.onOption(ue,K))}),n.\u0275\u0275elementStart(2,"label",11),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",12)(5,"div",13),n.\u0275\u0275element(6,"bocc-icon",14),n.\u0275\u0275elementEnd()()()()}if(2&I){const C=w.$implicit;n.\u0275\u0275property("id",C.id),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",C.label," "),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",C.boccTheme),n.\u0275\u0275advance(2),n.\u0275\u0275property("icon",C.icon)}}let H=(()=>{class I{constructor(C,B,K){this.utagService=C,this.customerStore=B,this.customerService=K,this.isManagerEnabled=!1,this.requesting=!1,this.options=[L,N,F]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:C})=>{this.isManagerEnabled=C?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(B=>{this.manager=B.manager,this.requesting=B.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(C){this.portal=C}onOption(C,B){this.utagService.link("click",C.id),this.portal?.send({action:"option",value:C}),B.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return I.\u0275fac=function(C){return new(C||I)(n.\u0275\u0275directiveInject(h.D),n.\u0275\u0275directiveInject(b.f),n.\u0275\u0275directiveInject(u.vZ))},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-contact-information"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275template(1,U,2,2,"div",1),n.\u0275\u0275elementStart(2,"ul",2),n.\u0275\u0275template(3,W,7,4,"li",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275elementEnd()),2&C&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.isManagerEnabled),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",B.options))},dependencies:[r.CommonModule,r.NgForOf,r.NgIf,d.Zl,z],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),I})();var Z=t(95437);let X=(()=>{class I{constructor(C,B){this.floatingService=C,this.mboProvider=B,this.contactsFloating=this.floatingService.create(H),this.contactsFloating?.subscribe(({action:K,value:Y})=>{"option"===K?this.dispatchOption(Y):this.close()})}subscribe(C){this.subscriber=C}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(C){"PQRS"===C.action?this.mboProvider.openUrl(p.BA.PQRS):this.subscriber&&this.subscriber(C)}}return I.\u0275fac=function(C){return new(C||I)(n.\u0275\u0275inject(d.B7),n.\u0275\u0275inject(Z.ZL))},I.\u0275prov=n.\u0275\u0275defineInjectable({token:I,factory:I.\u0275fac,providedIn:"root"}),I})(),T=(()=>{class I{constructor(){this.defenderLineNumber=p._L.DEFENDER_LINE,this.defenderLinePhone=p.WB.DEFENDER_LINE}ngBoccPortal(C){}onEmail(){(0,_.Gw)("mailto:<EMAIL>")}}return I.\u0275fac=function(C){return new(C||I)},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-contact-phones"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"mbo-attention-lines-form"),n.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),n.\u0275\u0275element(5,"bocc-icon",4),n.\u0275\u0275elementStart(6,"span",5),n.\u0275\u0275text(7,"Defensor del consumidor financiero"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),n.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"label",8)(13,"span"),n.\u0275\u0275text(14,"Lorena Cerchar Rosado"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(15,"bocc-badge",9),n.\u0275\u0275text(16," Suplente "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(17,"div",10),n.\u0275\u0275element(18,"bocc-icon",11),n.\u0275\u0275elementStart(19,"div",12)(20,"span",13),n.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(22,"div",10),n.\u0275\u0275element(23,"bocc-icon",14),n.\u0275\u0275elementStart(24,"div",12)(25,"a",15),n.\u0275\u0275text(26),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(27,"span",13),n.\u0275\u0275text(28," Ext. 15318 - 15311 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(29,"div",10),n.\u0275\u0275element(30,"bocc-icon",16),n.\u0275\u0275elementStart(31,"div",12)(32,"span",17),n.\u0275\u0275listener("click",function(){return B.onEmail()}),n.\u0275\u0275text(33," <EMAIL> "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(34,"div",10),n.\u0275\u0275element(35,"bocc-icon",18),n.\u0275\u0275elementStart(36,"div",12)(37,"span",13),n.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),n.\u0275\u0275elementEnd()()()()()()),2&C&&(n.\u0275\u0275advance(25),n.\u0275\u0275property("href",B.defenderLinePhone,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",B.defenderLineNumber," "))},dependencies:[r.CommonModule,d.Zl,d.Oh,i.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),I})(),k=(()=>{class I{constructor(){this.whatsappNumber=p._L.WHATSAPP}ngBoccPortal(C){}onClick(){(0,_.Gw)(p.BA.WHATSAPP)}}return I.\u0275fac=function(C){return new(C||I)},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",2)(4,"button",3),n.\u0275\u0275listener("click",function(){return B.onClick()}),n.\u0275\u0275elementStart(5,"span"),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()()()),2&C&&(n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate(B.whatsappNumber))},dependencies:[r.CommonModule,d.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),I})();var O=t(10119),j=(t(87677),t(68789)),$=t(10455),G=t(91642),J=t(10464),q=t(75221),ee=t(88649),te=t(13043),oe=t(38116),ne=t(68819),re=t(19310),ie=t(94614),ae=(t(70957),t(91248),t(4663)),ce=t(13961),se=t(66709),V=t(24495),Q=t(57544),le=t(53113);class me extends Q.FormGroup{constructor(){const w=new Q.FormControl("",[V.zf,V.O_,V.Y2,(0,V.Mv)(24)]),C=new Q.FormControl("",[V.C1,V.zf,V.O_,V.Y2,(0,V.Mv)(24)]);super({controls:{description:C,reference:w}}),this.description=C,this.reference=w}setNote(w){this.description.setValue(w?.description),this.reference.setValue(w?.reference)}getNote(){return new le.$H(this.description.value,this.reference.value)}}function de(I,w){if(1&I&&n.\u0275\u0275element(0,"bocc-input-box",7),2&I){const C=n.\u0275\u0275nextContext();n.\u0275\u0275property("formControl",C.formControls.reference)}}let pe=(()=>{class I{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new me}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(C){this.portal=C}}return I.\u0275fac=function(C){return new(C||I)},I.\u0275cmp=n.\u0275\u0275defineComponent({type:I,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(C,B){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"div",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-input-box",5),n.\u0275\u0275template(7,de,1,1,"bocc-input-box",6),n.\u0275\u0275elementEnd()()),2&C&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",B.cancelAction)("rightAction",B.saveAction),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",B.title," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("formControl",B.formControls.description),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.requiredReference))},dependencies:[r.CommonModule,r.NgIf,d.Jx,d.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),I})()},35324:(D,E,t)=>{t.d(E,{V:()=>l});var a=t(17007),e=t(30263),o=t(39904),m=t(87903),r=t(99877);function v(s,f){if(1&s){const n=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"a",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(n);const g=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(g.onWhatsapp())}),r.\u0275\u0275elementStart(1,"div",3),r.\u0275\u0275element(2,"bocc-icon",10),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",5)(4,"label",6),r.\u0275\u0275text(5," Whatsapp "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(6,"label",7),r.\u0275\u0275text(7),r.\u0275\u0275elementEnd()()()}if(2&s){const n=r.\u0275\u0275nextContext();r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",n.whatsappNumber," ")}}let l=(()=>{class s{constructor(){this.whatsapp=!1,this.whatsappNumber=o._L.WHATSAPP,this.nationalLineNumber=o._L.NATIONAL_LINE,this.bogotaLineNumber=o._L.BOGOTA_LINE,this.nationalLinePhone=o.WB.NATIONAL_LINE,this.bogotaLinePhone=o.WB.BOGOTA_LINE}onWhatsapp(){(0,m.Gw)(o.BA.WHATSAPP)}}return s.\u0275fac=function(n){return new(n||s)},s.\u0275cmp=r.\u0275\u0275defineComponent({type:s,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(n,c){1&n&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,v,8,1,"a",1),r.\u0275\u0275elementStart(2,"a",2)(3,"div",3),r.\u0275\u0275element(4,"bocc-icon",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"div",5)(6,"label",6),r.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"label",7),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(10,"a",8)(11,"div",3),r.\u0275\u0275element(12,"bocc-icon",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"div",5)(14,"label",6),r.\u0275\u0275text(15," Bogot\xe1 "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(16,"label",7),r.\u0275\u0275text(17),r.\u0275\u0275elementEnd()()()()),2&n&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",c.whatsapp),r.\u0275\u0275advance(1),r.\u0275\u0275property("href",c.nationalLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",c.nationalLineNumber," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("href",c.bogotaLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",c.bogotaLineNumber," "))},dependencies:[a.CommonModule,a.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),s})()},9593:(D,E,t)=>{t.d(E,{k:()=>v});var a=t(17007),e=t(30263),o=t(39904),m=t(95437),r=t(99877);let v=(()=>{class l{constructor(f){this.mboProvider=f,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(o.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(o.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(o.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return l.\u0275fac=function(f){return new(f||l)(r.\u0275\u0275directiveInject(m.ZL))},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(f,n){1&f&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),r.\u0275\u0275listener("click",function(){return n.onProducts()}),r.\u0275\u0275element(3,"bocc-icon",3),r.\u0275\u0275elementStart(4,"label",4),r.\u0275\u0275text(5," Productos "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",5),r.\u0275\u0275listener("click",function(){return n.onTransfers()}),r.\u0275\u0275element(7,"bocc-icon",6),r.\u0275\u0275elementStart(8,"label",4),r.\u0275\u0275text(9," Transferir "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(10,"div",7),r.\u0275\u0275listener("click",function(){return n.onPaymentQR()}),r.\u0275\u0275elementStart(11,"div",8)(12,"div",9),r.\u0275\u0275element(13,"bocc-icon",10),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(14,"bocc-icon",11),r.\u0275\u0275elementStart(15,"label",4),r.\u0275\u0275text(16," Pago QR "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(17,"div",12),r.\u0275\u0275listener("click",function(){return n.onPayments()}),r.\u0275\u0275element(18,"bocc-icon",13),r.\u0275\u0275elementStart(19,"label",4),r.\u0275\u0275text(20," Pagar "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(21,"div",14),r.\u0275\u0275listener("click",function(){return n.onToken()}),r.\u0275\u0275element(22,"bocc-icon",15),r.\u0275\u0275elementStart(23,"label",4),r.\u0275\u0275text(24," Token "),r.\u0275\u0275elementEnd()()()()),2&f&&(r.\u0275\u0275advance(2),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isProducts),r.\u0275\u0275advance(4),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isTransfers),r.\u0275\u0275advance(11),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isPayments),r.\u0275\u0275advance(4),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),l})()},83867:(D,E,t)=>{t.d(E,{o:()=>u});var a=t(17007),e=t(30263),o=t(8834),m=t(98699),l=(t(57544),t(99877));function f(b,p){if(1&b&&(l.\u0275\u0275elementStart(0,"label",11),l.\u0275\u0275text(1),l.\u0275\u0275elementEnd()),2&b){const _=l.\u0275\u0275nextContext();l.\u0275\u0275classProp("mbo-currency-box__rate--active",_.hasValue),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate2(" ",_.valueFormat," ",_.rateCode," ")}}function n(b,p){if(1&b&&(l.\u0275\u0275elementStart(0,"div",12),l.\u0275\u0275element(1,"img",13),l.\u0275\u0275elementEnd()),2&b){const _=l.\u0275\u0275nextContext();l.\u0275\u0275advance(1),l.\u0275\u0275property("src",_.icon,l.\u0275\u0275sanitizeUrl)}}function c(b,p){if(1&b&&(l.\u0275\u0275elementStart(0,"div",14),l.\u0275\u0275text(1),l.\u0275\u0275elementEnd()),2&b){const _=l.\u0275\u0275nextContext();l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate1(" ",_.currencyCode," ")}}function g(b,p){if(1&b&&(l.\u0275\u0275elementStart(0,"div",15),l.\u0275\u0275element(1,"bocc-icon",16),l.\u0275\u0275elementStart(2,"span",17),l.\u0275\u0275text(3),l.\u0275\u0275elementEnd()()),2&b){const _=l.\u0275\u0275nextContext();l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate1(" ",null==_.formControl.error?null:_.formControl.error.message," ")}}function h(b,p){if(1&b&&(l.\u0275\u0275elementStart(0,"div",18),l.\u0275\u0275element(1,"bocc-icon",19),l.\u0275\u0275elementStart(2,"span",17),l.\u0275\u0275text(3),l.\u0275\u0275elementEnd()()),2&b){const _=l.\u0275\u0275nextContext();l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate1(" ",_.helperInfo," ")}}let u=(()=>{class b{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,m.itIsDefined)(this.rate)}get value(){const _=+this.formControl?.value;return isNaN(_)?0:this.hasRate?_/this.rate:0}get valueFormat(){return(0,o.b)({value:this.value,symbol:"$",decimals:!0})}}return b.\u0275fac=function(_){return new(_||b)},b.\u0275cmp=l.\u0275\u0275defineComponent({type:b,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[l.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(_,P){1&_&&(l.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),l.\u0275\u0275text(3),l.\u0275\u0275elementEnd(),l.\u0275\u0275template(4,f,2,4,"label",3),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(5,"div",4)(6,"div",5),l.\u0275\u0275template(7,n,2,1,"div",6),l.\u0275\u0275element(8,"bocc-currency-field",7),l.\u0275\u0275template(9,c,2,1,"div",8),l.\u0275\u0275elementEnd()(),l.\u0275\u0275template(10,g,4,1,"div",9),l.\u0275\u0275template(11,h,4,1,"div",10),l.\u0275\u0275elementEnd()),2&_&&(l.\u0275\u0275classProp("mbo-currency-box--focused",P.formControl.focused)("mbo-currency-box--error",P.formControl.invalid&&P.formControl.touched)("mbo-currency-box--disabled",P.formControl.disabled||P.disabled),l.\u0275\u0275advance(2),l.\u0275\u0275property("for",P.elementId),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate1(" ",P.label," "),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",P.hasRate),l.\u0275\u0275advance(3),l.\u0275\u0275property("ngIf",P.icon),l.\u0275\u0275advance(1),l.\u0275\u0275property("elementId",P.elementId)("placeholder",P.placeholder)("disabled",P.disabled)("formControl",P.formControl),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",P.currencyCode),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",P.formControl.invalid&&P.formControl.touched),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",P.helperInfo&&!(P.formControl.invalid&&P.formControl.touched)))},dependencies:[a.CommonModule,a.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),b})()},85070:(D,E,t)=>{t.d(E,{f:()=>d});var a=t(17007),e=t(78506),o=t(99877);const r=["*"];let d=(()=>{class v{constructor(s){this.session=s}ngOnInit(){this.session.customer().then(s=>this.customer=s)}}return v.\u0275fac=function(s){return new(s||v)(o.\u0275\u0275directiveInject(e._I))},v.\u0275cmp=o.\u0275\u0275defineComponent({type:v,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(s,f){1&s&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",2),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()),2&s&&(o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(null==f.customer?null:f.customer.shortName))},dependencies:[a.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),v})()},65887:(D,E,t)=>{t.d(E,{X:()=>s});var a=t(17007),e=t(99877),m=t(30263),r=t(24495);function l(f,n){1&f&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let s=(()=>{class f{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[r.C1]:[]),this.unsubscription=this.documentType.subscribe(c=>{c&&(this.updateNumber(c,this.required),this.inputType=this.getInputType(c))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(c){if(c.required){const g=c.required.currentValue;this.documentType.setValidators(g?[r.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,g)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(c){return"PA"===c.code?"text":"number"}updateNumber(c,g){const h=this.validatorsForNumber(c,g);this.documentNumber.setValidators(h),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(c,g){return this.validatorsFromType(c).concat(g?[r.C1]:[])}maxLength(c){return g=>g&&g.length>c?{id:"maxLength",message:`Debe tener m\xe1ximo ${c} caracteres`}:null}validatorsFromType(c){switch(c.code){case"PA":return[r.JF];case"NIT":return[r.X1,this.maxLength(15)];default:return[r.X1]}}}return f.\u0275fac=function(c){return new(c||f)},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(c,g){1&c&&(e.\u0275\u0275template(0,l,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&c&&(e.\u0275\u0275property("ngIf",g.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",g.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",g.elementSelectId)("label",g.labelType)("suggestions",g.documents)("disabled",g.disabled)("formControl",g.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",g.elementInputId)("label",g.labelNumber)("type",g.inputType)("disabled",g.disabled)("formControl",g.documentNumber))},dependencies:[a.CommonModule,a.NgIf,m.DT,m.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),f})()},78021:(D,E,t)=>{t.d(E,{c:()=>f});var a=t(17007),e=t(30263),o=t(7603),m=t(98699),d=t(99877);function l(n,c){if(1&n){const g=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",5),d.\u0275\u0275listener("click",function(){d.\u0275\u0275restoreView(g);const u=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(u.onAction(u.leftAction))}),d.\u0275\u0275elementStart(1,"span"),d.\u0275\u0275text(2),d.\u0275\u0275elementEnd()()}if(2&n){const g=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",g.leftAction.id)("bocc-button",g.leftAction.type||"flat")("prefixIcon",g.leftAction.prefixIcon)("disabled",g.itIsDisabled(g.leftAction))("hidden",g.itIsHidden(g.leftAction)),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate(g.leftAction.label)}}function s(n,c){if(1&n){const g=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",6),d.\u0275\u0275listener("click",function(){const b=d.\u0275\u0275restoreView(g).$implicit,p=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(p.onAction(b))}),d.\u0275\u0275elementEnd()}if(2&n){const g=c.$implicit,h=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",g.id)("type",g.type||"flat")("bocc-button-action",g.icon)("disabled",h.itIsDisabled(g))("hidden",h.itIsHidden(g))}}let f=(()=>{class n{constructor(g){this.utagService=g,this.rightActions=[]}itIsDisabled({disabled:g}){return(0,m.evalValueOrFunction)(g)}itIsHidden({hidden:g}){return(0,m.evalValueOrFunction)(g)}onAction(g){const{id:h}=g;h&&this.utagService.link("click",h),g.click()}}return n.\u0275fac=function(g){return new(g||n)(d.\u0275\u0275directiveInject(o.D))},n.\u0275cmp=d.\u0275\u0275defineComponent({type:n,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(g,h){1&g&&(d.\u0275\u0275elementStart(0,"div",0)(1,"div",1),d.\u0275\u0275template(2,l,3,6,"button",2),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(3,"div",3),d.\u0275\u0275template(4,s,1,5,"button",4),d.\u0275\u0275elementEnd()()),2&g&&(d.\u0275\u0275advance(2),d.\u0275\u0275property("ngIf",h.leftAction),d.\u0275\u0275advance(2),d.\u0275\u0275property("ngForOf",h.rightActions))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),n})()},27302:(D,E,t)=>{t.d(E,{Vc:()=>a.V,k4:()=>i.k,o2:()=>e.o,B_:()=>v,fi:()=>l.f,XH:()=>s.X,cN:()=>g.c,Aj:()=>h.A,J5:()=>S.J,Nu:()=>L,iF:()=>X});var a=t(35324),i=t(9593),e=t(83867),o=t(17007),m=t(99877);function d(T,k){if(1&T){const O=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"div",2),m.\u0275\u0275listener("click",function(){const $=m.\u0275\u0275restoreView(O).$implicit,G=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(G.onClickCurrency($))}),m.\u0275\u0275elementStart(1,"div",3),m.\u0275\u0275element(2,"img",4)(3,"img",5),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(4,"label",6),m.\u0275\u0275text(5),m.\u0275\u0275elementEnd()()}if(2&T){const O=k.$implicit,R=m.\u0275\u0275nextContext();m.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",R.isEnabled(O)),m.\u0275\u0275advance(2),m.\u0275\u0275property("src",O.enabledIcon,m.\u0275\u0275sanitizeUrl),m.\u0275\u0275advance(1),m.\u0275\u0275property("src",O.disabledIcon,m.\u0275\u0275sanitizeUrl),m.\u0275\u0275advance(2),m.\u0275\u0275textInterpolate1(" ",O.label," ")}}t(57544);let v=(()=>{class T{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[O]=this.currencies;this.formControl.setValue(O)}}ngOnChanges(O){const{currencies:R}=O;if(R){const[j]=R.currentValue;this.formControl&&this.formControl.setValue(j)}}isEnabled(O){return O===this.formControl?.value}onClickCurrency(O){this.formControl&&!this.disabled&&this.formControl.setValue(O)}changeCurriencies(O){if(O.currencies){const R=O.currencies.currentValue,[j]=R;this.formControl&&this.formControl.setValue(j)}}}return T.\u0275fac=function(O){return new(O||T)},T.\u0275cmp=m.\u0275\u0275defineComponent({type:T,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[m.\u0275\u0275NgOnChangesFeature,m.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(O,R){1&O&&(m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275template(1,d,6,5,"div",1),m.\u0275\u0275elementEnd()),2&O&&(m.\u0275\u0275classProp("mbo-currency-toggle--disabled",R.disabled),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngForOf",R.currencies))},dependencies:[o.CommonModule,o.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),T})();var l=t(85070),s=t(65887),f=t(30263),g=t(78021),h=t(50689),p=(t(7603),t(98699),t(72765)),S=t(88014);function M(T,k){if(1&T&&(m.\u0275\u0275elementStart(0,"div",4),m.\u0275\u0275element(1,"img",5),m.\u0275\u0275elementEnd()),2&T){const O=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275property("src",O.src,m.\u0275\u0275sanitizeUrl)}}const z=["*"];let L=(()=>{class T{}return T.\u0275fac=function(O){return new(O||T)},T.\u0275cmp=m.\u0275\u0275defineComponent({type:T,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],ngContentSelectors:z,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(O,R){1&O&&(m.\u0275\u0275projectionDef(),m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275template(1,M,2,1,"div",1),m.\u0275\u0275elementStart(2,"div",2)(3,"div",3),m.\u0275\u0275projection(4),m.\u0275\u0275elementEnd()()()),2&O&&(m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",R.src))},dependencies:[o.CommonModule,o.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),T})();var N=t(24495);const F=/[A-Z]/,A=/[a-z]/,U=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,W=T=>T&&!F.test(T)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,H=T=>T&&!A.test(T)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,Z=T=>T&&!U.test(T)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let X=(()=>{class T{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([N.C1,H,W,Z,(0,N.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const O=this.formControl.errors.reduce((j,{id:$})=>[...j,$],[]),R=O.includes("required");this.smallInvalid=O.includes("smallCase")||R,this.capitalInvalid=O.includes("capitalCase")||R,this.specialCharInvalid=O.includes("specialChar")||R,this.minLengthInvalid=O.includes("minlength")||R})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return T.\u0275fac=function(O){return new(O||T)},T.\u0275cmp=m.\u0275\u0275defineComponent({type:T,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(O,R){1&O&&(m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275element(1,"bocc-password-box",1),m.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),m.\u0275\u0275text(4," Min\xfascula "),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(5,"mbo-poster",4),m.\u0275\u0275text(6," May\xfascula "),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(7,"mbo-poster",5),m.\u0275\u0275text(8," Especial "),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(9,"mbo-poster",6),m.\u0275\u0275text(10," Caracteres "),m.\u0275\u0275elementEnd()()()),2&O&&(m.\u0275\u0275advance(1),m.\u0275\u0275property("elementId",R.elementId)("disabled",R.disabled)("formControl",R.formControl),m.\u0275\u0275advance(2),m.\u0275\u0275property("disabled",R.smallInvalid),m.\u0275\u0275advance(2),m.\u0275\u0275property("disabled",R.capitalInvalid),m.\u0275\u0275advance(2),m.\u0275\u0275property("disabled",R.specialCharInvalid),m.\u0275\u0275advance(2),m.\u0275\u0275property("disabled",R.minLengthInvalid))},dependencies:[o.CommonModule,f.sC,p.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),T})()},50689:(D,E,t)=>{t.d(E,{A:()=>r});var a=t(17007),e=t(99877);const m=["*"];let r=(()=>{class d{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return d.\u0275fac=function(l){return new(l||d)},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:m,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(l,s){1&l&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&l&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",s.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),d})()},88014:(D,E,t)=>{t.d(E,{J:()=>m});var a=t(17007),e=t(99877);let m=(()=>{class r{}return r.\u0275fac=function(v){return new(v||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(v,l){1&v&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[a.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),r})()},21498:(D,E,t)=>{t.d(E,{P:()=>n});var a=t(17007),e=t(30263),o=t(99877);function r(c,g){if(1&c&&o.\u0275\u0275element(0,"bocc-card-product-summary",7),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",h.information.product.color)("icon",h.information.product.icon)("number",h.information.product.number)("title",h.information.product.title)("subtitle",h.information.product.subtitle)}}function d(c,g){if(1&c&&o.\u0275\u0275element(0,"bocc-card-summary",8),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.standard.header)("title",h.information.standard.title)("subtitle",h.information.standard.subtitle)("detail",h.information.standard.detail)}}function v(c,g){if(1&c&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.amount.header)("amount",h.information.amount.value)("symbol",h.information.amount.symbol)("amountSmall",h.information.amount.small)}}function l(c,g){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(h.information.text.content)}}function s(c,g){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",12),o.\u0275\u0275element(1,"bocc-icon",13),o.\u0275\u0275elementStart(2,"span",14),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",15),o.\u0275\u0275elementStart(5,"span",14),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.time," ")}}function f(c,g){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&c){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",h.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",h.information.badge.label," ")}}let n=(()=>{class c{}return c.\u0275fac=function(h){return new(h||c)},c.\u0275cmp=o.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(h,u){1&h&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,r,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,d,1,4,"bocc-card-summary",2),o.\u0275\u0275template(3,v,1,4,"bocc-card-summary",3),o.\u0275\u0275template(4,l,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,s,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,f,3,4,"bocc-card-summary",6),o.\u0275\u0275elementEnd()),2&h&&(o.\u0275\u0275property("ngSwitch",u.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),c})()},7427:(D,E,t)=>{t.d(E,{x:()=>n});var a=t(17007),e=t(30263),o=t(87903),r=(t(29306),t(77279)),d=t(87956),v=t(68789),l=t(13961),s=t(99877);let n=(()=>{class c{constructor(h,u){this.eventBusService=h,this.onboardingScreenService=u,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,o.Bn)(this.product.tagAval),this.eventBusService.emit(r.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(l.Z)),this.tagAvalonboarding.open()}}return c.\u0275fac=function(h){return new(h||c)(s.\u0275\u0275directiveInject(d.Yd),s.\u0275\u0275directiveInject(v.x))},c.\u0275cmp=s.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(h,u){1&h&&(s.\u0275\u0275elementStart(0,"bocc-card-product",0),s.\u0275\u0275listener("key",function(){return u.onTagAval()})("onboarding",function(){return u.onBoarding()}),s.\u0275\u0275elementEnd()),2&h&&(s.\u0275\u0275classMap(u.product.bank.className),s.\u0275\u0275property("iconTitle",u.iconTitle)("title",u.product.nickname||u.product.name)("icon",u.product.logo)("tagAval",u.product.tagAvalFormat)("actions",u.actions)("color",u.product.color)("code",u.product.shortNumber)("label",u.product.label)("amount",u.product.amount)("incognito",u.incognito)("displayCard",!0)("statusLabel",null==u.product.status?null:u.product.status.label)("statusColor",null==u.product.status?null:u.product.status.color)("cromaline",!0)("msgError",u.msgError))},dependencies:[a.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),c})()},1027:(D,E,t)=>{t.d(E,{A:()=>h});var a=t(17007),i=t(72765),e=t(30263),o=t(99877);function m(u,b){if(1&u&&o.\u0275\u0275element(0,"bocc-card-product-summary",8),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",p.information.product.color)("icon",p.information.product.icon)("number",p.information.product.number)("title",p.information.product.title)("subtitle",p.information.product.subtitle)}}function r(u,b){if(1&u&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.standard.header)("title",p.information.standard.title)("subtitle",p.information.standard.subtitle)}}function d(u,b){if(1&u&&o.\u0275\u0275element(0,"bocc-card-summary",10),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.amount.header)("amount",p.information.amount.value)("symbol",p.information.amount.symbol)}}function v(u,b){if(1&u&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(p.information.text.content)}}function l(u,b){if(1&u&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",13),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",16),o.\u0275\u0275elementStart(5,"span",15),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",p.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",p.information.datetime.time," ")}}function s(u,b){if(1&u&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",17),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd()()),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.date.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",p.information.date.date," ")}}function f(u,b){if(1&u&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&u){const p=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",p.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",p.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",p.information.badge.label," ")}}let n=(()=>{class u{}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=o.\u0275\u0275defineComponent({type:u,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(p,_){1&p&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,m,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,r,1,3,"bocc-card-summary",2),o.\u0275\u0275template(3,d,1,3,"bocc-card-summary",3),o.\u0275\u0275template(4,v,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,l,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,s,4,3,"bocc-card-summary",6),o.\u0275\u0275template(7,f,3,4,"bocc-card-summary",7),o.\u0275\u0275elementEnd()),2&p&&(o.\u0275\u0275property("ngSwitch",_.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","date"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),u})();function c(u,b){1&u&&o.\u0275\u0275element(0,"mbo-card-information-element",8),2&u&&o.\u0275\u0275property("information",b.$implicit)}const g=["*"];let h=(()=>{class u{constructor(){this.skeleton=!1,this.informations=[]}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=o.\u0275\u0275defineComponent({type:u,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:g,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(p,_){1&p&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"mbo-bank-logo",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"div",4),o.\u0275\u0275element(5,"div",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275projection(7),o.\u0275\u0275template(8,c,1,1,"mbo-card-information-element",7),o.\u0275\u0275elementEnd()()),2&p&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("result",!0),o.\u0275\u0275advance(5),o.\u0275\u0275property("ngForOf",_.informations))},dependencies:[a.CommonModule,a.NgForOf,i.rw,n],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),u})()},16442:(D,E,t)=>{t.d(E,{u:()=>p});var a=t(99877),e=t(17007),m=t(13462),d=t(19102),v=t(45542),l=t(65467),s=t(21498);function f(_,P){if(1&_&&(a.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&_){const y=a.\u0275\u0275nextContext();a.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",y.template.skeleton),a.\u0275\u0275property("secondary",!0)("active",y.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",y.template.header.subtitle," ")}}function n(_,P){1&_&&a.\u0275\u0275element(0,"mbo-card-information",16),2&_&&a.\u0275\u0275property("information",P.$implicit)}function c(_,P){if(1&_&&(a.\u0275\u0275elementStart(0,"div",14),a.\u0275\u0275projection(1),a.\u0275\u0275template(2,n,1,1,"mbo-card-information",15),a.\u0275\u0275elementEnd()),2&_){const y=a.\u0275\u0275nextContext();a.\u0275\u0275advance(2),a.\u0275\u0275property("ngForOf",y.template.informations)}}function g(_,P){1&_&&(a.\u0275\u0275elementStart(0,"div",17),a.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),a.\u0275\u0275elementEnd()),2&_&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0))}function h(_,P){if(1&_){const y=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",23),a.\u0275\u0275listener("click",function(){const M=a.\u0275\u0275restoreView(y).$implicit,z=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(z.onAction(M))}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&_){const y=P.$implicit;a.\u0275\u0275property("bocc-button",y.type)("prefixIcon",y.prefixIcon),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate(y.label)}}function u(_,P){if(1&_&&(a.\u0275\u0275elementStart(0,"div",21),a.\u0275\u0275template(1,h,3,3,"button",22),a.\u0275\u0275elementEnd()),2&_){const y=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("ngForOf",y.template.actions)}}const b=["*"];let p=(()=>{class _{constructor(){this.disabled=!1,this.action=new a.EventEmitter}onAction({event:y}){this.action.emit(y)}}return _.\u0275fac=function(y){return new(y||_)},_.\u0275cmp=a.\u0275\u0275defineComponent({type:_,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:b,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(y,x){1&y&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275element(3,"mbo-bank-logo",3),a.\u0275\u0275elementStart(4,"div",4),a.\u0275\u0275element(5,"ng-lottie",5),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(8,f,2,5,"bocc-skeleton-text",7),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(9,"div",8),a.\u0275\u0275element(10,"div",9),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(11,c,3,1,"div",10),a.\u0275\u0275template(12,g,4,5,"div",11),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(13,u,2,1,"div",12)),2&y&&(a.\u0275\u0275classProp("animation",!x.template.skeleton),a.\u0275\u0275advance(3),a.\u0275\u0275property("result",!0),a.\u0275\u0275advance(2),a.\u0275\u0275property("options",x.template.header.animation),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",x.template.header.title," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.skeleton||x.template.header.subtitle),a.\u0275\u0275advance(3),a.\u0275\u0275property("ngIf",!x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.actions.length&&!x.disabled))},dependencies:[e.NgForOf,e.NgIf,m.LottieComponent,d.r,v.P,l.D,s.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),_})()},10119:(D,E,t)=>{t.d(E,{N:()=>g});var a=t(17007),e=t(99877),m=t(30263),r=t(7603),d=t(98699);function l(h,u){if(1&h&&e.\u0275\u0275element(0,"bocc-diamond",14),2&h){const b=u.$implicit,p=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",p.itIsSelected(b))}}function s(h,u){if(1&h){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onAction(_.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionLeft.id)("bocc-button",b.footerActionLeft.type)("prefixIcon",b.footerActionLeft.prefixIcon)("disabled",b.itIsDisabled(b.footerActionLeft))("hidden",b.itIsHidden(b.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionLeft.label)}}function f(h,u){if(1&h){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onAction(_.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionRight.id)("bocc-button",b.footerActionRight.type)("prefixIcon",b.footerActionRight.prefixIcon)("disabled",b.itIsDisabled(b.footerActionRight))("hidden",b.itIsHidden(b.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionRight.label)}}const n=["*"];let g=(()=>{class h{constructor(b,p){this.ref=b,this.utagService=p,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((b,p)=>p),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(b){return b===this.currentPosition}itIsDisabled({disabled:b}){return(0,d.evalValueOrFunction)(b)}itIsHidden({hidden:b}){return(0,d.evalValueOrFunction)(b)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(b){const{id:p}=b;p&&this.utagService.link("click",p),b.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(b){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(b),this.automatic=!1,this.setTranslatePosition(b)}setTranslatePosition(b){this.translateX=b*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(b){this.transformContent=`translateX(${b}px)`}emitPosition(b){this.finished||(this.finished=b+1===this.elements.length),this.position.emit({position:b,finished:this.finished})}getPositionSlide(b){return b>=this.elements.length?this.elements.length-1:b<0?0:b}setTouchHandler(b){let p=0,_=0;b.addEventListener("touchstart",P=>{if(P.changedTouches.length){const{clientX:y}=P.changedTouches.item(0);p=0,this.touched=!0,_=y}}),b.addEventListener("touchmove",P=>{if(P.changedTouches.length){const y=P.changedTouches.item(0),x=y.clientX-_;_=y.clientX,this.translateX+=x,p+=x,this.setTranslateContent(this.translateX)}}),b.addEventListener("touchend",P=>{this.touched=!1,P.changedTouches.length&&(Math.abs(p)/this.widthBody*100>=40&&(p>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return h.\u0275fac=function(b){return new(b||h)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(r.D))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:n,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(b,p){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return p.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return p.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,l,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return p.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,s,3,6,"button",13),e.\u0275\u0275template(16,f,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&b&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",p.headerActionLeft)("rightAction",p.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",p.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",p.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",p.widthContent)("transform",p.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",p.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!p.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",p.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!p.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",p.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.footerActionRight))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,m.P8,m.u1,m.ou,m.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),h})()},68789:(D,E,t)=>{t.d(E,{x:()=>r});var a=t(7603),i=t(10455),e=t(87677),o=t(99877);let r=(()=>{class d{constructor(l){this.portalService=l}information(){this.portal||(this.portal=this.portalService.container({component:i.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(l,s){return this.portalService.container({component:l,container:e.C,props:{container:s?.containerProps,component:s?.componentProps}})}}return d.\u0275fac=function(l){return new(l||d)(o.\u0275\u0275inject(a.v))},d.\u0275prov=o.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},87677:(D,E,t)=>{t.d(E,{C:()=>e});var a=t(99877);let e=(()=>{class o{constructor(r){this.ref=r,this.visible=!1,this.visibleChange=new a.EventEmitter}open(r=0){setTimeout(()=>{this.changeVisible(!0)},r)}close(r=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},r)}append(r){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(r)}ngBoccPortal(r){this.portal=r}changeVisible(r){this.visible=r,this.visibleChange.emit(r)}}return o.\u0275fac=function(r){return new(r||o)(a.\u0275\u0275directiveInject(a.ElementRef))},o.\u0275cmp=a.\u0275\u0275defineComponent({type:o,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(r,d){1&r&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"div",1),a.\u0275\u0275elementEnd()),2&r&&a.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",d.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),o})()},10455:(D,E,t)=>{t.d(E,{E:()=>d});var a=t(17007),e=t(99877),m=t(27302),r=t(10119);let d=(()=>{class v{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(s){this.portal=s}onPosition({finished:s}){this.finished=s,s&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return v.\u0275fac=function(s){return new(s||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(s,f){1&s&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(c){return f.onPosition(c)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&s&&e.\u0275\u0275property("footerActionLeft",f.footerLeft)("footerActionRight",f.footerRight)("gradient",!0)},dependencies:[a.CommonModule,m.Nu,r.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),v})()},91642:(D,E,t)=>{t.d(E,{D:()=>p});var a=t(17007),e=t(99877),m=t(30263),r=t(87542),d=t(70658),v=t(3372),l=t(87956),s=t(72765);function f(_,P){1&_&&e.\u0275\u0275element(0,"mbo-bank-logo")}function n(_,P){1&_&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function c(_,P){if(1&_&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&_){const y=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",y.verifying)}}function g(_,P){1&_&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function h(_,P){if(1&_){const y=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(y);const S=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(S.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&_){const y=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",y.verifying)}}const u=["*"],{OtpInputSuperuser:b}=v.M;let p=(()=>{class _{constructor(y,x,S,M){this.ref=y,this.otpService=x,this.deviceService=S,this.preferencesService=M,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=r.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new r.yV}ngOnInit(){this.otpService.onCode(y=>{this.otpControls.setCode(y),this.otpControls.valid&&this.onAutocomplete(y)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(y){const{documentNumber:x}=y;x&&this.preferencesService.applyFunctionality(b,x.currentValue).then(S=>{this.itIsDocumentSuperuser=S})}get otpVisible(){return!d.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return d.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&d.N.otpReadonlyMobile}onAutocomplete(y){this.code.emit(y)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return _.\u0275fac=function(y){return new(y||_)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(l.no),e.\u0275\u0275directiveInject(l.U8),e.\u0275\u0275directiveInject(l.yW))},_.\u0275cmp=e.\u0275\u0275defineComponent({type:_,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:u,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(y,x){1&y&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,f,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,n,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(M){return x.onAutocomplete(M)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,c,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,g,7,0,"div",8),e.\u0275\u0275template(13,h,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&y&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",x.otpVisible),e.\u0275\u0275property("formControls",x.otpControls)("readonly",x.otpReadonly)("mobile",x.otpMobile)("disabled",x.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.isIos))},dependencies:[a.CommonModule,a.NgIf,m.P8,m.Yx,s.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),_})()},10464:(D,E,t)=>{t.d(E,{K:()=>d});var a=t(17007),e=t(99877),m=t(22816);const r=["*"];let d=(()=>{class v{constructor(s){this.ref=s,this.scroller=new m.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(s){this.scroller.reset(s.target)}}return v.\u0275fac=function(s){return new(s||v)(e.\u0275\u0275directiveInject(e.ElementRef))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(s,f){1&s&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(c){return f.onScroll(c)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&s&&e.\u0275\u0275classProp("mbo-page__content--start",f.scrollStart)("mbo-page__content--end",f.scrollEnd)},dependencies:[a.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),v})()},75221:(D,E,t)=>{t.d(E,{u:()=>v});var a=t(17007),e=t(30263),o=t(27302),r=(t(88649),t(99877));let v=(()=>{class l{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return l.\u0275fac=function(f){return new(f||l)},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(f,n){1&f&&r.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&f&&(r.\u0275\u0275property("formControl",n.passwordControl.controls.password)("disabled",n.disabled)("elementId",n.elementPasswordId),r.\u0275\u0275advance(1),r.\u0275\u0275property("elementId",n.elementConfirmId)("disabled",n.disabled)("formControl",n.passwordControl.controls.repeatPassword))},dependencies:[a.CommonModule,e.sC,o.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),l})()},88649:(D,E,t)=>{t.d(E,{z:()=>o});var a=t(57544),i=t(24495);class o extends a.FormGroup{constructor(){const r=new a.FormControl(""),d=new a.FormControl("",[i.C1,(m=r,r=>r&&r!==m.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var m;super({controls:{password:r,repeatPassword:d}})}get password(){return this.controls.password.value}}},13043:(D,E,t)=>{t.d(E,{e:()=>h});var a=t(17007),e=t(99877),m=t(30263),v=(t(57544),t(27302));function l(u,b){1&u&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function s(u,b){if(1&u&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&u){const p=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",p.title," ")}}function f(u,b){if(1&u){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const y=e.\u0275\u0275restoreView(p).$implicit,x=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(x.onProduct(y))}),e.\u0275\u0275elementEnd()}if(2&u){const p=b.$implicit,_=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",p.color)("icon",p.logo)("title",p.nickname)("number",p.publicNumber)("detail",p.bank.name)("ghost",_.ghost)}}function n(u,b){if(1&u&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,s,2,1,"div",8),e.\u0275\u0275template(3,f,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&u){const p=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",p.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",p.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!p.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",p.msgError," ")}}function c(u,b){1&u&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&u&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const g=["*"];let h=(()=>{class u{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(p){return this.productControl?.value?.id===p.id}onProduct(p){this.select.emit(p),this.productControl?.setValue(p)}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:g,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(p,_){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,l,3,0,"div",1),e.\u0275\u0275template(2,n,6,5,"div",2),e.\u0275\u0275template(3,c,3,2,"div",3),e.\u0275\u0275elementEnd()),2&p&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!_.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.skeleton))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,m.w_,v.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),u})()},38116:(D,E,t)=>{t.d(E,{Z:()=>d});var a=t(17007),e=t(99877),m=t(30263);function r(v,l){if(1&v){const s=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const c=e.\u0275\u0275restoreView(s).$implicit,g=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(g.onAction(c))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&v){const s=l.$implicit,f=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(f.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",f.itIsDisabled(s)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(f.theme(s)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",s.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",s.label," ")}}let d=(()=>{class v{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(s){return s.requiredInformation&&s.errorInformation}theme(s){return this.itIsDisabled(s)?"none":s.theme}onAction(s){!this.itIsDisabled(s)&&this.action.emit(s.type)}}return v.\u0275fac=function(s){return new(s||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(s,f){1&s&&e.\u0275\u0275template(0,r,6,8,"div",0),2&s&&e.\u0275\u0275property("ngForOf",f.actions)},dependencies:[a.CommonModule,a.NgForOf,m.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),v})()},68819:(D,E,t)=>{t.d(E,{w:()=>z});var a=t(17007),e=t(99877),m=t(30263),r=t(39904),l=(t(57544),t(78506)),f=(t(29306),t(87903)),n=t(95437),c=t(27302),g=t(70957),h=t(91248),u=t(13961),b=t(68789),p=t(33395),_=t(25317);function P(L,N){if(1&L){const F=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(U){e.\u0275\u0275restoreView(F);const W=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(W.onBoarding(U))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(F);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onCopyKey(U.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(F);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&L){const F=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",F.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",F.product.tagAval)}}function y(L,N){if(1&L&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&L){const F=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",F.digitalSection)("currencyCode",null==F.currencyControl.value?null:F.currencyControl.value.code)("hidden",F.itIsVisibleMovements)}}function x(L,N){if(1&L&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&L){const F=N.$implicit,A=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",F)("currencyCode",null==A.currencyControl.value?null:A.currencyControl.value.code)}}const S=[[["","header",""]],"*"],M=["[header]","*"];let z=(()=>{class L{constructor(F,A,U){this.mboProvider=F,this.managerInformation=A,this.onboardingScreenService=U,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(F){const{movements:A,sections:U}=F;A&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!A.currentValue),U&&this.product&&this.refreshComponent(this.product,U.currentValue),this.managerInformation.requestInfoBody().then(W=>{W.when({success:({canEditTagAval:H})=>{this.canEditTagAval=H}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(F){(0,f.Bn)(F),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(r.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(F,A){const U=(0,f.A2)(F);if(this.sectionPosition=0,A?.length){const W=A.map(({title:H},Z)=>({label:H,value:Z}));U&&(this.headerMovements.value=this.sections.length,W.push(this.headerMovements)),this.headers=W}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const W=[{label:"Error",value:1}];U&&W.unshift(this.headerMovements),this.headers=W}}onBoarding(F){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(u.Z)),this.tagAvalonboarding.open(),F.stopPropagation()}}return L.\u0275fac=function(F){return new(F||L)(e.\u0275\u0275directiveInject(n.ZL),e.\u0275\u0275directiveInject(l.vu),e.\u0275\u0275directiveInject(b.x))},L.\u0275cmp=e.\u0275\u0275defineComponent({type:L,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:M,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(F,A){1&F&&(e.\u0275\u0275projectionDef(S),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(W){return A.sectionPosition=W}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,P,15,2,"div",4),e.\u0275\u0275template(6,y,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,x,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&F&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",A.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",A.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",A.headers)("value",A.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",A.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==A.product?null:A.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",A.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",A.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",A.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",A.movements)("header",A.header)("product",A.product)("currencyCode",null==A.currencyControl.value?null:A.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",A.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!A.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,m.Gf,m.qw,m.P8,m.Dj,m.qd,g.K,h.I,c.Aj,p.kW,_.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),L})()},19310:(D,E,t)=>{t.d(E,{$:()=>y});var a=t(17007),i=t(99877),e=t(30263),o=t(87903);let m=(()=>{class x{transform(M,z,L=" "){return(0,o.rd)(M,z,L)}}return x.\u0275fac=function(M){return new(M||x)},x.\u0275pipe=i.\u0275\u0275definePipe({name:"codeSplit",type:x,pure:!0}),x})(),r=(()=>{class x{}return x.\u0275fac=function(M){return new(M||x)},x.\u0275mod=i.\u0275\u0275defineNgModule({type:x}),x.\u0275inj=i.\u0275\u0275defineInjector({imports:[a.CommonModule]}),x})();t(57544);var v=t(70658),l=t(78506),f=(t(29306),t(87956)),n=t(72765),c=t(27302);function g(x,S){if(1&x){const M=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",18),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(M);const L=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(L.onDigital())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275property("prefixIcon",M.digitalIcon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(M.digitalIncognito?"Ver datos":"Ocultar datos")}}function h(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"span"),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate(null==M.product?null:M.product.publicNumber)}}function u(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"span",19),i.\u0275\u0275text(1),i.\u0275\u0275pipe(2,"codeSplit"),i.\u0275\u0275elementEnd()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind2(2,1,M.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":M.digitalNumber,4)," ")}}function b(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"bocc-badge",20),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275attribute("bocc-theme",null==M.product||null==M.product.status?null:M.product.status.color),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",null==M.product||null==M.product.status?null:M.product.status.label," ")}}function p(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"div",21),i.\u0275\u0275element(1,"bocc-progress-bar",22),i.\u0275\u0275elementEnd()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("theme",M.progressBarTheme)("width",M.progressBarStatus)}}function _(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"div",23)(1,"label",24),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),i.\u0275\u0275element(5,"bocc-amount",26),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(6,"mbo-button-incognito-mode",27),i.\u0275\u0275elementEnd()()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",null==M.product?null:M.product.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!M.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("amount",null==M.product?null:M.product.amount)("incognito",M.incognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("actionMode",!0)("hidden",!M.product)}}function P(x,S){if(1&x&&(i.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),i.\u0275\u0275text(3,"Vence"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"span",19),i.\u0275\u0275text(5),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(6,"div",29)(7,"label",20),i.\u0275\u0275text(8,"CVC"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(9,"span",19),i.\u0275\u0275text(10),i.\u0275\u0275elementEnd()()()),2&x){const M=i.\u0275\u0275nextContext();i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",M.digitalIncognito?"\u2022\u2022 | \u2022\u2022":M.digitalExpAt," "),i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",M.digitalIncognito?"\u2022\u2022\u2022":M.digitalCVC," ")}}let y=(()=>{class x{constructor(M,z){this.managerPreferences=M,this.digitalService=z,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new i.EventEmitter,this.digital=new i.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:M})=>{this.incognito=M})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:M,value:z})=>{this.product&&this.product.id===M&&this.refreshDigitalState(z)}))}ngOnChanges(M){const{product:z}=M;if(z&&z.currentValue){const L=z.currentValue;this.refreshDigitalState(this.digitalService.request(L.id)),this.activateDigitalCountdown(L)}}ngOnDestroy(){this.unsubscriptions.forEach(M=>M())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(M){const{incognito:z,requiredRequest:L,cvc:N,expirationAt:F,number:A}=M;this.digitalIncognito=z,this.digitalExpAt=F,this.digitalCVC=N,this.digitalNumber=A,this.digitalIcon=z?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=L}activateDigitalCountdown(M){const{countdown$:z}=this.digitalService.request(M.id);z?(this.progressBarRequired=!0,this.progressBarPercent=100,z.subscribe(L=>{this.progressBarRequired=!(L>=v.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-L/v.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return x.\u0275fac=function(M){return new(M||x)(i.\u0275\u0275directiveInject(l.Bx),i.\u0275\u0275directiveInject(f.ZP))},x.\u0275cmp=i.\u0275\u0275defineComponent({type:x,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[i.\u0275\u0275NgOnChangesFeature,i.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(M,z){1&M&&(i.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),i.\u0275\u0275listener("click",function(){return z.onClose()}),i.\u0275\u0275elementStart(3,"span"),i.\u0275\u0275text(4,"Atr\xe1s"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(5,"mbo-currency-toggle",3),i.\u0275\u0275template(6,g,3,2,"button",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(7,"div",5)(8,"div",6),i.\u0275\u0275element(9,"img",7),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),i.\u0275\u0275text(12),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),i.\u0275\u0275template(15,h,2,1,"span",12),i.\u0275\u0275template(16,u,3,4,"span",13),i.\u0275\u0275template(17,b,2,2,"bocc-badge",14),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(18,p,2,2,"div",15),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(19,_,7,6,"div",16),i.\u0275\u0275template(20,P,11,2,"div",17),i.\u0275\u0275elementEnd()),2&M&&(i.\u0275\u0275classMap(null==z.product?null:z.product.bank.className),i.\u0275\u0275property("color",null==z.product?null:z.product.color),i.\u0275\u0275advance(5),i.\u0275\u0275property("formControl",z.currencyControl)("currencies",z.currencies)("hidden",!(null!=z.product&&z.product.bank.isOccidente)||z.currencies.length<2),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==z.product?null:z.product.isDigital),i.\u0275\u0275advance(3),i.\u0275\u0275property("src",null==z.product?null:z.product.logo,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!z.product),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",(null==z.product?null:z.product.nickname)||(null==z.product?null:z.product.name)," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!z.product),i.\u0275\u0275advance(1),i.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!z.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=z.product&&z.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==z.product?null:z.product.isDigital),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",(null==z.product||null==z.product.status?null:z.product.status.label)&&z.digitalIncognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",z.progressBarRequired),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=z.product&&z.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==z.product?null:z.product.isDigital))},dependencies:[a.CommonModule,a.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,r,m,n.uf,c.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),x})()},94614:(D,E,t)=>{t.d(E,{K:()=>l});var a=t(17007),e=t(30263),o=t(39904),r=(t(29306),t(95437)),d=t(99877);let l=(()=>{class s{constructor(n){this.mboProvider=n,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:n,id:c,parentProduct:g}=this.product;"covered"===n&&g?this.goToPage(g.id,c):this.goToPage(c)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(n,c){this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:n,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:c})}}return s.\u0275fac=function(n){return new(n||s)(d.\u0275\u0275directiveInject(r.ZL))},s.\u0275cmp=d.\u0275\u0275defineComponent({type:s,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(n,c){1&n&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275listener("click",function(){return c.onComponent()}),d.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"div",4),d.\u0275\u0275element(7,"bocc-amount",5),d.\u0275\u0275elementEnd()()),2&n&&(d.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",c.skeleton),d.\u0275\u0275advance(2),d.\u0275\u0275property("active",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.dateFormat," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("active",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.description," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("hidden",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275property("amount",null==c.movement?null:c.movement.value)("currencyCode",null==c.movement?null:c.movement.currencyCode)("theme",!0))},dependencies:[a.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),s})()},70957:(D,E,t)=>{t.d(E,{K:()=>p});var a=t(15861),i=t(17007),o=t(99877),r=t(30263),d=t(78506),v=t(39904),s=(t(29306),t(87903)),f=t(95437),n=t(27302),c=t(94614);function g(_,P){if(1&_){const y=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",8),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(y);const S=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(S.onRedirectAll())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Ver todos"),o.\u0275\u0275elementEnd()()}}function h(_,P){if(1&_&&(o.\u0275\u0275elementStart(0,"div",5)(1,"label",6),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(3,g,3,0,"button",7),o.\u0275\u0275elementEnd()),2&_){const y=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",(null==y.productMovements||null==y.productMovements.range?null:y.productMovements.range.label)||"Sin resultados"," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==y.productMovements?null:y.productMovements.range)}}function u(_,P){if(1&_&&o.\u0275\u0275element(0,"mbo-product-info-movement",9),2&_){const y=P.$implicit,x=o.\u0275\u0275nextContext();o.\u0275\u0275property("movement",y)("product",x.product)}}function b(_,P){if(1&_&&(o.\u0275\u0275elementStart(0,"mbo-message-empty",10),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&_){const y=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",y.msgError," ")}}let p=(()=>{class _{constructor(y,x){this.mboProvider=y,this.managerProductMovements=x,this.header=!0,this.requesting=!1}ngOnChanges(y){const{currencyCode:x,product:S}=y;if(!this.movements&&(S||x)){const M=x?.currentValue||this.currencyCode,z=S?.currentValue||this.product;this.currentMovements=void 0,(0,s.A2)(z)&&this.requestFirstPage(z,M)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:y,id:x,parentProduct:S}=this.product;this.mboProvider.navigation.next(v.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===y?{productId:S?.id,coveredCardId:x}:{productId:x},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(y,x){var S=this;return(0,a.Z)(function*(){S.requesting=!0,(yield S.managerProductMovements.requestForProduct({product:y,currencyCode:x})).when({success:M=>{S.currentMovements=M},failure:()=>{S.currentMovements=void 0}},()=>{S.requesting=!1})})()}}return _.\u0275fac=function(y){return new(y||_)(o.\u0275\u0275directiveInject(f.ZL),o.\u0275\u0275directiveInject(d.sy))},_.\u0275cmp=o.\u0275\u0275defineComponent({type:_,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(y,x){1&y&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,h,4,2,"div",1),o.\u0275\u0275elementStart(2,"div",2),o.\u0275\u0275template(3,u,1,2,"mbo-product-info-movement",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(4,b,2,1,"mbo-message-empty",4),o.\u0275\u0275elementEnd()),2&y&&(o.\u0275\u0275property("hidden",x.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",x.header),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",null==x.productMovements?null:x.productMovements.firstPage),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==x.productMovements?null:x.productMovements.isEmpty))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,r.P8,c.K,n.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),_})()},91248:(D,E,t)=>{t.d(E,{I:()=>s});var a=t(17007),e=t(30263),o=t(99877);function r(f,n){if(1&f&&o.\u0275\u0275element(0,"bocc-amount",10),2&f){const c=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("amount",c.value)("currencyCode",c.currencyCode)}}function d(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"span"),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&f){const c=o.\u0275\u0275nextContext().$implicit,g=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",g.incognito||g.section.incognito?c.mask:c.value," ")}}function v(f,n){if(1&f){const c=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(c);const h=o.\u0275\u0275nextContext().$implicit;return o.\u0275\u0275resetView(h.action.click())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&f){const c=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("suffixIcon",c.action.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(c.action.label)}}function l(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275template(5,r,1,2,"bocc-amount",7),o.\u0275\u0275template(6,d,2,1,"span",8),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(7,v,3,2,"button",9),o.\u0275\u0275elementEnd()),2&f){const c=n.$implicit,g=o.\u0275\u0275nextContext();o.\u0275\u0275property("hidden",(null==c?null:c.currencyCode)!==g.currencyCode),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",c.label," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",c.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!c.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",c.action&&!(g.incognito||g.section.incognito))}}let s=(()=>{class f{constructor(){this.currencyCode="COP"}}return f.\u0275fac=function(c){return new(c||f)},f.\u0275cmp=o.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(c,g){1&c&&(o.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),o.\u0275\u0275template(2,l,8,5,"li",2),o.\u0275\u0275elementEnd()()),2&c&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",g.section.datas))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),f})()},4663:(D,E,t)=>{t.d(E,{c:()=>n});var a=t(17007),e=t(99877),m=t(30263),r=t(27302);function d(c,g){1&c&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function v(c,g){if(1&c&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&c){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",h.title," ")}}function l(c,g){if(1&c){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const p=e.\u0275\u0275restoreView(h).$implicit,_=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onProduct(p))}),e.\u0275\u0275elementEnd()}if(2&c){const h=g.$implicit,u=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",h.color)("icon",h.logo)("title",h.nickname)("number",h.publicNumber)("ghost",u.ghost)("amount",h.amount)("tagAval",h.tagAvalFormat),e.\u0275\u0275attribute("amount-status",u.amountColorProduct(h))}}function s(c,g){1&c&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const f=["*"];let n=(()=>{class c{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(h){return h.amount>0?"success":h.amount<0?"danger":"empty"}onProduct(h){this.select.emit(h)}}return c.\u0275fac=function(h){return new(h||c)},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(h,u){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,d,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,v,2,1,"div",5),e.\u0275\u0275template(8,l,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,s,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",u.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",u.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!u.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!u.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,m.w_,m.P8,r.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),c})()},13961:(D,E,t)=>{t.d(E,{Z:()=>d});var a=t(17007),e=t(27302),o=t(10119),m=t(99877);let d=(()=>{class v{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(s){this.portal=s}}return v.\u0275fac=function(s){return new(s||v)},v.\u0275cmp=m.\u0275\u0275defineComponent({type:v,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(s,f){1&s&&(m.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),m.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(4,"p"),m.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),m.\u0275\u0275elementEnd()(),m.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),m.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(9,"p"),m.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),m.\u0275\u0275elementEnd()()()),2&s&&m.\u0275\u0275property("headerActionRight",f.headerAction)("gradient",!0)},dependencies:[a.CommonModule,e.Nu,o.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),v})()},66709:(D,E,t)=>{t.d(E,{s:()=>d});var a=t(17007),i=t(99877),e=t(30263),o=t(87542);let m=(()=>{class v{ngBoccPortal(s){}}return v.\u0275fac=function(s){return new(s||v)},v.\u0275cmp=i.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(s,f){1&s&&(i.\u0275\u0275elementStart(0,"div",0)(1,"label",1),i.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),i.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),i.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(11,"p",4),i.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),i.\u0275\u0275element(13,"br"),i.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),i.\u0275\u0275element(15,"br"),i.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),i.\u0275\u0275elementStart(17,"span"),i.\u0275\u0275text(18,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(19," Configuraci\xf3n "),i.\u0275\u0275elementStart(20,"span"),i.\u0275\u0275text(21,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(22," Seguridad "),i.\u0275\u0275elementStart(23,"span"),i.\u0275\u0275text(24,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(25," Activar Token Mobile."),i.\u0275\u0275element(26,"br"),i.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),i.\u0275\u0275elementEnd()()()())},dependencies:[a.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),v})();const r=["*"];let d=(()=>{class v{constructor(s,f){this.ref=s,this.bottomSheetService=f,this.verifying=!1,this.tokenLength=o.Xi,this.code=new i.EventEmitter,this.tokenControls=new o.b2}ngOnInit(){const s=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(m),setTimeout(()=>{s?.focus()},120)}onAutocomplete(s){this.code.emit(s)}onInfo(){this.infoSheet?.open()}}return v.\u0275fac=function(s){return new(s||v)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(e.fG))},v.\u0275cmp=i.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(s,f){1&s&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275projection(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"p",2),i.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p",2),i.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),i.\u0275\u0275elementStart(7,"a"),i.\u0275\u0275text(8),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(9,". "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",3),i.\u0275\u0275element(11,"img",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),i.\u0275\u0275listener("autocomplete",function(c){return f.onAutocomplete(c)}),i.\u0275\u0275text(13," Ingresa tu clave "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(14,"button",6),i.\u0275\u0275listener("click",function(){return f.onInfo()}),i.\u0275\u0275elementStart(15,"span"),i.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()()()),2&s&&(i.\u0275\u0275advance(8),i.\u0275\u0275textInterpolate1("",f.tokenLength," d\xedgitos"),i.\u0275\u0275advance(4),i.\u0275\u0275property("disabled",f.verifying)("formControls",f.tokenControls),i.\u0275\u0275advance(2),i.\u0275\u0275property("disabled",f.verifying))},dependencies:[a.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),v})()},22816:(D,E,t)=>{t.d(E,{S:()=>a});class a{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);