(self.webpackChunkapp=self.webpackChunkapp||[]).push([[922],{90922:(v,h,a)=>{a.r(h),a.d(h,{KEYBOARD_DID_CLOSE:()=>E,KEYBOARD_DID_OPEN:()=>D,copyVisualViewport:()=>c,keyboardDidClose:()=>i,keyboardDidOpen:()=>f,keyboardDidResize:()=>p,resetKeyboardAssist:()=>O,setKeyboardClose:()=>r,setKeyboardOpen:()=>n,startKeyboardAssist:()=>g,trackViewportChanges:()=>y});var b=a(93037);a(36319),a(72972);const D="ionKeyboardDidShow",E="ionKeyboardDidHide";let d={},t={},o=!1;const O=()=>{d={},t={},o=!1},g=e=>{if(b.<PERSON>.getEngine())K(e);else{if(!e.visualViewport)return;t=c(e.visualViewport),e.visualViewport.onresize=()=>{y(e),f()||p(e)?n(e):i(e)&&r(e)}}},K=e=>{e.addEventListener("keyboardDidShow",s=>n(e,s)),e.addEventListener("keyboardDidHide",()=>r(e))},n=(e,s)=>{_(e,s),o=!0},r=e=>{u(e),o=!1},f=()=>!o&&d.width===t.width&&(d.height-t.height)*t.scale>150,p=e=>o&&!i(e),i=e=>o&&t.height===e.innerHeight,_=(e,s)=>{const k=new CustomEvent(D,{detail:{keyboardHeight:s?s.keyboardHeight:e.innerHeight-t.height}});e.dispatchEvent(k)},u=e=>{const s=new CustomEvent(E);e.dispatchEvent(s)},y=e=>{d=Object.assign({},t),t=c(e.visualViewport)},c=e=>({width:Math.round(e.width),height:Math.round(e.height),offsetTop:e.offsetTop,offsetLeft:e.offsetLeft,pageTop:e.pageTop,pageLeft:e.pageLeft,scale:e.scale})}}]);