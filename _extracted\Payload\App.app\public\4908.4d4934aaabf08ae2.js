(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4908],{44908:(b,d,l)=>{l.r(d),l.d(d,{ion_spinner:()=>i});var c=l(42477),h=l(23814),s=l(37943),e=l(92677);const i=class{constructor(o){(0,c.r)(this,o),this.color=void 0,this.duration=void 0,this.name=void 0,this.paused=!1}getName(){const o=this.name||s.c.get("spinner"),t=(0,s.b)(this);return o||("ios"===t?"lines":"circular")}render(){var o;const t=this,p=(0,s.b)(t),m=t.getName(),n=null!==(o=e.S[m])&&void 0!==o?o:e.S.lines,y="number"==typeof t.duration&&t.duration>10?t.duration:n.dur,k=[];if(void 0!==n.circles)for(let f=0;f<n.circles;f++)k.push(u(n,y,f,n.circles));else if(void 0!==n.lines)for(let f=0;f<n.lines;f++)k.push(g(n,y,f,n.lines));return(0,c.h)(c.H,{key:"9e08bf306b28bdd76884d353dcaaf31c1bb591f2",class:(0,h.c)(t.color,{[p]:!0,[`spinner-${m}`]:!0,"spinner-paused":t.paused||s.c.getBoolean("_testing")}),role:"progressbar",style:n.elmDuration?{animationDuration:y+"ms"}:{}},k)}},u=(o,t,p,m)=>{const n=o.fn(t,p,m);return n.style["animation-duration"]=t+"ms",(0,c.h)("svg",{viewBox:n.viewBox||"0 0 64 64",style:n.style},(0,c.h)("circle",{transform:n.transform||"translate(32,32)",cx:n.cx,cy:n.cy,r:n.r,style:o.elmDuration?{animationDuration:t+"ms"}:{}}))},g=(o,t,p,m)=>{const n=o.fn(t,p,m);return n.style["animation-duration"]=t+"ms",(0,c.h)("svg",{viewBox:n.viewBox||"0 0 64 64",style:n.style},(0,c.h)("line",{transform:"translate(32,32)",y1:n.y1,y2:n.y2}))};i.style=":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}"},92677:(b,d,l)=>{l.d(d,{S:()=>h});const h={bubbles:{dur:1e3,circles:9,fn:(s,e,r)=>{const a=s*e/r-s+"ms",i=2*Math.PI*e/r;return{r:5,style:{top:32*Math.sin(i)+"%",left:32*Math.cos(i)+"%","animation-delay":a}}}},circles:{dur:1e3,circles:8,fn:(s,e,r)=>{const a=e/r,i=s*a-s+"ms",u=2*Math.PI*a;return{r:5,style:{top:32*Math.sin(u)+"%",left:32*Math.cos(u)+"%","animation-delay":i}}}},circular:{dur:1400,elmDuration:!0,circles:1,fn:()=>({r:20,cx:48,cy:48,fill:"none",viewBox:"24 24 48 48",transform:"translate(0,0)",style:{}})},crescent:{dur:750,circles:1,fn:()=>({r:26,style:{}})},dots:{dur:750,circles:3,fn:(s,e)=>({r:6,style:{left:32-32*e+"%","animation-delay":-110*e+"ms"}})},lines:{dur:1e3,lines:8,fn:(s,e,r)=>({y1:14,y2:26,style:{transform:`rotate(${360/r*e+(e<r/2?180:-180)}deg)`,"animation-delay":s*e/r-s+"ms"}})},"lines-small":{dur:1e3,lines:8,fn:(s,e,r)=>({y1:12,y2:20,style:{transform:`rotate(${360/r*e+(e<r/2?180:-180)}deg)`,"animation-delay":s*e/r-s+"ms"}})},"lines-sharp":{dur:1e3,lines:12,fn:(s,e,r)=>({y1:17,y2:29,style:{transform:`rotate(${30*e+(e<6?180:-180)}deg)`,"animation-delay":s*e/r-s+"ms"}})},"lines-sharp-small":{dur:1e3,lines:12,fn:(s,e,r)=>({y1:12,y2:20,style:{transform:`rotate(${30*e+(e<6?180:-180)}deg)`,"animation-delay":s*e/r-s+"ms"}})}}}}]);