(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7511],{34793:(Wi,ht,D)=>{D.r(ht),D.d(ht,{ActivatedRoute:()=>$,ActivatedRouteSnapshot:()=>ne,ActivationEnd:()=>Ft,ActivationStart:()=>zt,BaseRouteReuseStrategy:()=>Rr,ChildActivationEnd:()=>kt,ChildActivationStart:()=>Nt,ChildrenOutletContexts:()=>H,DefaultTitleStrategy:()=>vr,DefaultUrlSerializer:()=>ge,GuardsCheckEnd:()=>Dt,GuardsCheckStart:()=>Pt,NavigationCancel:()=>te,NavigationEnd:()=>O,NavigationError:()=>we,NavigationSkipped:()=>re,NavigationStart:()=>Se,NoPreloading:()=>Ei,OutletContext:()=>Zt,PRIMARY_OUTLET:()=>p,PreloadAllModules:()=>_i,PreloadingStrategy:()=>it,ROUTER_CONFIGURATION:()=>ue,ROUTER_INITIALIZER:()=>ct,ROUTES:()=>N,ResolveEnd:()=>jt,ResolveStart:()=>xt,RouteConfigLoadEnd:()=>$t,RouteConfigLoadStart:()=>Lt,RouteReuseStrategy:()=>mr,Router:()=>C,RouterEvent:()=>_,RouterLink:()=>he,RouterLinkActive:()=>yr,RouterLinkWithHref:()=>he,RouterModule:()=>Di,RouterOutlet:()=>Ke,RouterPreloader:()=>Sr,RouterState:()=>We,RouterStateSnapshot:()=>qe,RoutesRecognized:()=>Mt,Scroll:()=>ke,TitleStrategy:()=>rt,UrlHandlingStrategy:()=>Cr,UrlSegment:()=>z,UrlSegmentGroup:()=>g,UrlSerializer:()=>F,UrlTree:()=>A,VERSION:()=>zi,convertToParamMap:()=>x,createUrlTreeFromSnapshot:()=>Jr,defaultUrlMatcher:()=>dt,provideRouter:()=>Ii,provideRoutes:()=>Ti,withDebugTracing:()=>Ur,withDisabledInitialNavigation:()=>br,withEnabledBlockingInitialNavigation:()=>Tr,withHashLocation:()=>Oi,withInMemoryScrolling:()=>bi,withNavigationErrorHandler:()=>Mi,withPreloading:()=>lt,withRouterConfig:()=>Ui,\u0275EmptyOutletComponent:()=>Ee,\u0275ROUTER_PROVIDERS:()=>Pr,\u0275afterNextNavigation:()=>_r,\u0275flatten:()=>De,\u0275withPreloading:()=>lt});var l=D(99877),h=D(42168),S=D(17007),c=D(84757),Dr=D(99428);const p="primary",Z=Symbol("RouteTitle");class xr{constructor(r){this.params=r||{}}has(r){return Object.prototype.hasOwnProperty.call(this.params,r)}get(r){if(this.has(r)){const e=this.params[r];return Array.isArray(e)?e[0]:e}return null}getAll(r){if(this.has(r)){const e=this.params[r];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}}function x(t){return new xr(t)}function dt(t,r,e){const n=e.path.split("/");if(n.length>t.length||"full"===e.pathMatch&&(r.hasChildren()||n.length<t.length))return null;const i={};for(let o=0;o<n.length;o++){const s=n[o],a=t[o];if(s.startsWith(":"))i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,n.length),posParams:i}}function I(t,r){const e=t?Object.keys(t):void 0,n=r?Object.keys(r):void 0;if(!e||!n||e.length!=n.length)return!1;let i;for(let o=0;o<e.length;o++)if(i=e[o],!ft(t[i],r[i]))return!1;return!0}function ft(t,r){if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return!1;const e=[...t].sort(),n=[...r].sort();return e.every((i,o)=>n[o]===i)}return t===r}function De(t){return Array.prototype.concat.apply([],t)}function pt(t){return t.length>0?t[t.length-1]:null}function R(t,r){for(const e in t)t.hasOwnProperty(e)&&r(t[e],e)}function U(t){return(0,l.\u0275isObservable)(t)?t:(0,l.\u0275isPromise)(t)?(0,h.from)(Promise.resolve(t)):(0,h.of)(t)}const fe=!1,Lr={exact:function mt(t,r,e){if(!j(t.segments,r.segments)||!pe(t.segments,r.segments,e)||t.numberOfChildren!==r.numberOfChildren)return!1;for(const n in r.children)if(!t.children[n]||!mt(t.children[n],r.children[n],e))return!1;return!0},subset:Rt},gt={exact:function $r(t,r){return I(t,r)},subset:function Nr(t,r){return Object.keys(r).length<=Object.keys(t).length&&Object.keys(r).every(e=>ft(t[e],r[e]))},ignored:()=>!0};function vt(t,r,e){return Lr[e.paths](t.root,r.root,e.matrixParams)&&gt[e.queryParams](t.queryParams,r.queryParams)&&!("exact"===e.fragment&&t.fragment!==r.fragment)}function Rt(t,r,e){return Ct(t,r,r.segments,e)}function Ct(t,r,e,n){if(t.segments.length>e.length){const i=t.segments.slice(0,e.length);return!(!j(i,e)||r.hasChildren()||!pe(i,e,n))}if(t.segments.length===e.length){if(!j(t.segments,e)||!pe(t.segments,e,n))return!1;for(const i in r.children)if(!t.children[i]||!Rt(t.children[i],r.children[i],n))return!1;return!0}{const i=e.slice(0,t.segments.length),o=e.slice(t.segments.length);return!!(j(t.segments,i)&&pe(t.segments,i,n)&&t.children[p])&&Ct(t.children[p],r,o,n)}}function pe(t,r,e){return r.every((n,i)=>gt[e](t[i].parameters,n.parameters))}class A{constructor(r=new g([],{}),e={},n=null){this.root=r,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=x(this.queryParams)),this._queryParamMap}toString(){return Fr.serialize(this)}}class g{constructor(r,e){this.segments=r,this.children=e,this.parent=null,R(e,(n,i)=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ve(this)}}class z{constructor(r,e){this.path=r,this.parameters=e}get parameterMap(){return this._parameterMap||(this._parameterMap=x(this.parameters)),this._parameterMap}toString(){return wt(this)}}function j(t,r){return t.length===r.length&&t.every((e,n)=>e.path===r[n].path)}let F=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:function(){return new ge},providedIn:"root"}),t})();class ge{parse(r){const e=new Zr(r);return new A(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(r){const e=`/${Y(r.root,!0)}`,n=function Br(t){const r=Object.keys(t).map(e=>{const n=t[e];return Array.isArray(n)?n.map(i=>`${me(e)}=${me(i)}`).join("&"):`${me(e)}=${me(n)}`}).filter(e=>!!e);return r.length?`?${r.join("&")}`:""}(r.queryParams);return`${e}${n}${"string"==typeof r.fragment?`#${function Wr(t){return encodeURI(t)}(r.fragment)}`:""}`}}const Fr=new ge;function ve(t){return t.segments.map(r=>wt(r)).join("/")}function Y(t,r){if(!t.hasChildren())return ve(t);if(r){const e=t.children[p]?Y(t.children[p],!1):"",n=[];return R(t.children,(i,o)=>{o!==p&&n.push(`${o}:${Y(i,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}{const e=function zr(t,r){let e=[];return R(t.children,(n,i)=>{i===p&&(e=e.concat(r(n,i)))}),R(t.children,(n,i)=>{i!==p&&(e=e.concat(r(n,i)))}),e}(t,(n,i)=>i===p?[Y(t.children[p],!1)]:[`${i}:${Y(n,!1)}`]);return 1===Object.keys(t.children).length&&null!=t.children[p]?`${ve(t)}/${e[0]}`:`${ve(t)}/(${e.join("//")})`}}function yt(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function me(t){return yt(t).replace(/%3B/gi,";")}function xe(t){return yt(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Re(t){return decodeURIComponent(t)}function St(t){return Re(t.replace(/\+/g,"%20"))}function wt(t){return`${xe(t.path)}${function qr(t){return Object.keys(t).map(r=>`;${xe(r)}=${xe(t[r])}`).join("")}(t.parameters)}`}const Vr=/^[^\/()?;=#]+/;function Ce(t){const r=t.match(Vr);return r?r[0]:""}const Hr=/^[^=?&#]+/,Kr=/^[^&#]+/;class Zr{constructor(r){this.url=r,this.remaining=r}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new g([],{}):new g([],this.parseChildren())}parseQueryParams(){const r={};if(this.consumeOptional("?"))do{this.parseQueryParam(r)}while(this.consumeOptional("&"));return r}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const r=[];for(this.peekStartsWith("(")||r.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),r.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(r.length>0||Object.keys(e).length>0)&&(n[p]=new g(r,e)),n}parseSegment(){const r=Ce(this.remaining);if(""===r&&this.peekStartsWith(";"))throw new l.\u0275RuntimeError(4009,fe);return this.capture(r),new z(Re(r),this.parseMatrixParams())}parseMatrixParams(){const r={};for(;this.consumeOptional(";");)this.parseParam(r);return r}parseParam(r){const e=Ce(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const i=Ce(this.remaining);i&&(n=i,this.capture(n))}r[Re(e)]=Re(n)}parseQueryParam(r){const e=function Gr(t){const r=t.match(Hr);return r?r[0]:""}(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const s=function Qr(t){const r=t.match(Kr);return r?r[0]:""}(this.remaining);s&&(n=s,this.capture(n))}const i=St(e),o=St(n);if(r.hasOwnProperty(i)){let s=r[i];Array.isArray(s)||(s=[s],r[i]=s),s.push(o)}else r[i]=o}parseParens(r){const e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const n=Ce(this.remaining),i=this.remaining[n.length];if("/"!==i&&")"!==i&&";"!==i)throw new l.\u0275RuntimeError(4010,fe);let o;n.indexOf(":")>-1?(o=n.slice(0,n.indexOf(":")),this.capture(o),this.capture(":")):r&&(o=p);const s=this.parseChildren();e[o]=1===Object.keys(s).length?s[p]:new g([],s),this.consumeOptional("//")}return e}peekStartsWith(r){return this.remaining.startsWith(r)}consumeOptional(r){return!!this.peekStartsWith(r)&&(this.remaining=this.remaining.substring(r.length),!0)}capture(r){if(!this.consumeOptional(r))throw new l.\u0275RuntimeError(4011,fe)}}function je(t){return t.segments.length>0?new g([],{[p]:t}):t}function ye(t){const r={};for(const n of Object.keys(t.children)){const o=ye(t.children[n]);(o.segments.length>0||o.hasChildren())&&(r[n]=o)}return function Yr(t){if(1===t.numberOfChildren&&t.children[p]){const r=t.children[p];return new g(t.segments.concat(r.segments),r.children)}return t}(new g(t.segments,r))}function L(t){return t instanceof A}const Le=!1;function Jr(t,r,e=null,n=null){return function Et(t,r,e,n){let i=t;for(;i.parent;)i=i.parent;if(0===r.length)return W(i,i,i,e,n);const o=Tt(r);if(o.toRoot())return W(i,i,new g([],{}),e,n);const s=function en(t,r,e){if(t.isAbsolute)return new q(r,!0,0);if(!e)return new q(r,!1,NaN);if(null===e.parent)return new q(e,!0,0);const n=J(t.commands[0])?0:1;return bt(e,e.segments.length-1+n,t.numberOfDoubleDots)}(o,i,t),a=s.processChildren?B(s.segmentGroup,s.index,o.commands):$e(s.segmentGroup,s.index,o.commands);return W(i,s.segmentGroup,a,e,n)}(function _t(t){let r;const n=function e(o){const s={};for(const u of o.children){const d=e(u);s[u.outlet]=d}const a=new g(o.url,s);return o===t&&(r=a),a}(t.root),i=je(n);return r??i}(t),r,e,n)}function J(t){return"object"==typeof t&&null!=t&&!t.outlets&&!t.segmentPath}function X(t){return"object"==typeof t&&null!=t&&t.outlets}function W(t,r,e,n,i){let s,o={};n&&R(n,(u,d)=>{o[d]=Array.isArray(u)?u.map(f=>`${f}`):`${u}`}),s=t===r?e:It(t,r,e);const a=je(ye(s));return new A(a,o,i)}function It(t,r,e){const n={};return R(t.children,(i,o)=>{n[o]=i===r?e:It(i,r,e)}),new g(t.segments,n)}class At{constructor(r,e,n){if(this.isAbsolute=r,this.numberOfDoubleDots=e,this.commands=n,r&&n.length>0&&J(n[0]))throw new l.\u0275RuntimeError(4003,Le&&"Root segment cannot have matrix parameters");const i=n.find(X);if(i&&i!==pt(n))throw new l.\u0275RuntimeError(4004,Le&&"{outlets:{}} has to be the last command")}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}function Tt(t){if("string"==typeof t[0]&&1===t.length&&"/"===t[0])return new At(!0,0,t);let r=0,e=!1;const n=t.reduce((i,o,s)=>{if("object"==typeof o&&null!=o){if(o.outlets){const a={};return R(o.outlets,(u,d)=>{a[d]="string"==typeof u?u.split("/"):u}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return"string"!=typeof o?[...i,o]:0===s?(o.split("/").forEach((a,u)=>{0==u&&"."===a||(0==u&&""===a?e=!0:".."===a?r++:""!=a&&i.push(a))}),i):[...i,o]},[]);return new At(e,r,n)}class q{constructor(r,e,n){this.segmentGroup=r,this.processChildren=e,this.index=n}}function bt(t,r,e){let n=t,i=r,o=e;for(;o>i;){if(o-=i,n=n.parent,!n)throw new l.\u0275RuntimeError(4005,Le&&"Invalid number of '../'");i=n.segments.length}return new q(n,!1,i-o)}function $e(t,r,e){if(t||(t=new g([],{})),0===t.segments.length&&t.hasChildren())return B(t,r,e);const n=function nn(t,r,e){let n=0,i=r;const o={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(n>=e.length)return o;const s=t.segments[i],a=e[n];if(X(a))break;const u=`${a}`,d=n<e.length-1?e[n+1]:null;if(i>0&&void 0===u)break;if(u&&d&&"object"==typeof d&&void 0===d.outlets){if(!Ot(u,d,s))return o;n+=2}else{if(!Ot(u,{},s))return o;n++}i++}return{match:!0,pathIndex:i,commandIndex:n}}(t,r,e),i=e.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){const o=new g(t.segments.slice(0,n.pathIndex),{});return o.children[p]=new g(t.segments.slice(n.pathIndex),t.children),B(o,0,i)}return n.match&&0===i.length?new g(t.segments,{}):n.match&&!t.hasChildren()?Ne(t,r,e):n.match?B(t,0,i):Ne(t,r,e)}function B(t,r,e){if(0===e.length)return new g(t.segments,{});{const n=function rn(t){return X(t[0])?t[0].outlets:{[p]:t}}(e),i={};if(!n[p]&&t.children[p]&&1===t.numberOfChildren&&0===t.children[p].segments.length){const o=B(t.children[p],r,e);return new g(t.segments,o.children)}return R(n,(o,s)=>{"string"==typeof o&&(o=[o]),null!==o&&(i[s]=$e(t.children[s],r,o))}),R(t.children,(o,s)=>{void 0===n[s]&&(i[s]=o)}),new g(t.segments,i)}}function Ne(t,r,e){const n=t.segments.slice(0,r);let i=0;for(;i<e.length;){const o=e[i];if(X(o)){const u=on(o.outlets);return new g(n,u)}if(0===i&&J(e[0])){n.push(new z(t.segments[r].path,Ut(e[0]))),i++;continue}const s=X(o)?o.outlets[p]:`${o}`,a=i<e.length-1?e[i+1]:null;s&&a&&J(a)?(n.push(new z(s,Ut(a))),i+=2):(n.push(new z(s,{})),i++)}return new g(n,{})}function on(t){const r={};return R(t,(e,n)=>{"string"==typeof e&&(e=[e]),null!==e&&(r[n]=Ne(new g([],{}),0,e))}),r}function Ut(t){const r={};return R(t,(e,n)=>r[n]=`${e}`),r}function Ot(t,r,e){return t==e.path&&I(r,e.parameters)}const ee="imperative";class _{constructor(r,e){this.id=r,this.url=e}}class Se extends _{constructor(r,e,n="imperative",i=null){super(r,e),this.type=0,this.navigationTrigger=n,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class O extends _{constructor(r,e,n){super(r,e),this.urlAfterRedirects=n,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class te extends _{constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class re extends _{constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i,this.type=16}}class we extends _{constructor(r,e,n,i){super(r,e),this.error=n,this.target=i,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class Mt extends _{constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Pt extends _{constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Dt extends _{constructor(r,e,n,i,o){super(r,e),this.urlAfterRedirects=n,this.state=i,this.shouldActivate=o,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class xt extends _{constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class jt extends _{constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Lt{constructor(r){this.route=r,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class $t{constructor(r){this.route=r,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Nt{constructor(r){this.snapshot=r,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class kt{constructor(r){this.snapshot=r,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class zt{constructor(r){this.snapshot=r,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Ft{constructor(r){this.snapshot=r,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class ke{constructor(r,e,n){this.routerEvent=r,this.position=e,this.anchor=n,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}let ln=(()=>{class t{createUrlTree(e,n,i,o,s,a){const d=function Xr(t,r,e,n,i){if(0===e.length)return W(r.root,r.root,r.root,n,i);const o=Tt(e);return o.toRoot()?W(r.root,r.root,new g([],{}),n,i):function s(u){const d=function tn(t,r,e,n){return t.isAbsolute?new q(r.root,!0,0):-1===n?new q(e,e===r.root,0):bt(e,n+(J(t.commands[0])?0:1),t.numberOfDoubleDots)}(o,r,t.snapshot?._urlSegment,u),f=d.processChildren?B(d.segmentGroup,d.index,o.commands):$e(d.segmentGroup,d.index,o.commands);return W(r.root,d.segmentGroup,f,n,i)}(t.snapshot?._lastPathIndex)}(e||n.root,i,o,s,a);return d}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac}),t})(),un=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:function(r){return ln.\u0275fac(r)},providedIn:"root"}),t})();class Wt{constructor(r){this._root=r}get root(){return this._root.value}parent(r){const e=this.pathFromRoot(r);return e.length>1?e[e.length-2]:null}children(r){const e=ze(r,this._root);return e?e.children.map(n=>n.value):[]}firstChild(r){const e=ze(r,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(r){const e=Fe(r,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==r)}pathFromRoot(r){return Fe(r,this._root).map(e=>e.value)}}function ze(t,r){if(t===r.value)return r;for(const e of r.children){const n=ze(t,e);if(n)return n}return null}function Fe(t,r){if(t===r.value)return[r];for(const e of r.children){const n=Fe(t,e);if(n.length)return n.unshift(r),n}return[]}class T{constructor(r,e){this.value=r,this.children=e}toString(){return`TreeNode(${this.value})`}}function V(t){const r={};return t&&t.children.forEach(e=>r[e.value.outlet]=e),r}class We extends Wt{constructor(r,e){super(r),this.snapshot=e,Be(this,r)}toString(){return this.snapshot.toString()}}function qt(t,r){const e=function hn(t,r){const s=new ne([],{},{},"",{},p,r,null,t.root,-1,{});return new qe("",new T(s,[]))}(t,r),n=new h.BehaviorSubject([new z("",{})]),i=new h.BehaviorSubject({}),o=new h.BehaviorSubject({}),s=new h.BehaviorSubject({}),a=new h.BehaviorSubject(""),u=new $(n,i,s,a,o,p,r,e.root);return u.snapshot=e.root,new We(new T(u,[]),e)}class ${constructor(r,e,n,i,o,s,a,u){this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.title=this.data?.pipe((0,c.map)(d=>d[Z]))??(0,h.of)(void 0),this._futureSnapshot=u}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe((0,c.map)(r=>x(r)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe((0,c.map)(r=>x(r)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Bt(t,r="emptyOnly"){const e=t.pathFromRoot;let n=0;if("always"!==r)for(n=e.length-1;n>=1;){const i=e[n],o=e[n-1];if(i.routeConfig&&""===i.routeConfig.path)n--;else{if(o.component)break;n--}}return function dn(t){return t.reduce((r,e)=>({params:{...r.params,...e.params},data:{...r.data,...e.data},resolve:{...e.data,...r.resolve,...e.routeConfig?.data,...e._resolvedData}}),{params:{},data:{},resolve:{}})}(e.slice(n))}class ne{get title(){return this.data?.[Z]}constructor(r,e,n,i,o,s,a,u,d,f,v){this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=u,this._urlSegment=d,this._lastPathIndex=f,this._resolve=v}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=x(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=x(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(n=>n.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class qe extends Wt{constructor(r,e){super(e),this.url=r,Be(this,e)}toString(){return Vt(this._root)}}function Be(t,r){r.value._routerState=t,r.children.forEach(e=>Be(t,e))}function Vt(t){const r=t.children.length>0?` { ${t.children.map(Vt).join(", ")} } `:"";return`${t.value}${r}`}function Ve(t){if(t.snapshot){const r=t.snapshot,e=t._futureSnapshot;t.snapshot=e,I(r.queryParams,e.queryParams)||t.queryParams.next(e.queryParams),r.fragment!==e.fragment&&t.fragment.next(e.fragment),I(r.params,e.params)||t.params.next(e.params),function jr(t,r){if(t.length!==r.length)return!1;for(let e=0;e<t.length;++e)if(!I(t[e],r[e]))return!1;return!0}(r.url,e.url)||t.url.next(e.url),I(r.data,e.data)||t.data.next(e.data)}else t.snapshot=t._futureSnapshot,t.data.next(t._futureSnapshot.data)}function He(t,r){const e=I(t.params,r.params)&&function kr(t,r){return j(t,r)&&t.every((e,n)=>I(e.parameters,r[n].parameters))}(t.url,r.url);return e&&!(!t.parent!=!r.parent)&&(!t.parent||He(t.parent,r.parent))}function ie(t,r,e){if(e&&t.shouldReuseRoute(r.value,e.value.snapshot)){const n=e.value;n._futureSnapshot=r.value;const i=function pn(t,r,e){return r.children.map(n=>{for(const i of e.children)if(t.shouldReuseRoute(n.value,i.value.snapshot))return ie(t,n,i);return ie(t,n)})}(t,r,e);return new T(n,i)}{if(t.shouldAttach(r.value)){const o=t.retrieve(r.value);if(null!==o){const s=o.route;return s.value._futureSnapshot=r.value,s.children=r.children.map(a=>ie(t,a)),s}}const n=function gn(t){return new $(new h.BehaviorSubject(t.url),new h.BehaviorSubject(t.params),new h.BehaviorSubject(t.queryParams),new h.BehaviorSubject(t.fragment),new h.BehaviorSubject(t.data),t.outlet,t.component,t)}(r.value),i=r.children.map(o=>ie(t,o));return new T(n,i)}}const Ge="ngNavigationCancelingError";function Ht(t,r){const{redirectTo:e,navigationBehaviorOptions:n}=L(r)?{redirectTo:r,navigationBehaviorOptions:void 0}:r,i=Gt(!1,0,r);return i.url=e,i.navigationBehaviorOptions=n,i}function Gt(t,r,e){const n=new Error("NavigationCancelingError: "+(t||""));return n[Ge]=!0,n.cancellationCode=r,e&&(n.url=e),n}function Kt(t){return Qt(t)&&L(t.url)}function Qt(t){return t&&t[Ge]}class Zt{constructor(){this.outlet=null,this.route=null,this.resolver=null,this.injector=null,this.children=new H,this.attachRef=null}}let H=(()=>{class t{constructor(){this.contexts=new Map}onChildOutletCreated(e,n){const i=this.getOrCreateContext(e);i.outlet=n,this.contexts.set(e,i)}onChildOutletDestroyed(e){const n=this.getContext(e);n&&(n.outlet=null,n.attachRef=null)}onOutletDeactivated(){const e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let n=this.getContext(e);return n||(n=new Zt,this.contexts.set(e,n)),n}getContext(e){return this.contexts.get(e)||null}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const _e=!1;let Ke=(()=>{class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=p,this.activateEvents=new l.EventEmitter,this.deactivateEvents=new l.EventEmitter,this.attachEvents=new l.EventEmitter,this.detachEvents=new l.EventEmitter,this.parentContexts=(0,l.inject)(H),this.location=(0,l.inject)(l.ViewContainerRef),this.changeDetector=(0,l.inject)(l.ChangeDetectorRef),this.environmentInjector=(0,l.inject)(l.EnvironmentInjector)}ngOnChanges(e){if(e.name){const{firstChange:n,previousValue:i}=e.name;if(n)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new l.\u0275RuntimeError(4012,_e);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new l.\u0275RuntimeError(4012,_e);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new l.\u0275RuntimeError(4012,_e);this.location.detach();const e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,n){this.activated=e,this._activatedRoute=n,this.location.insert(e.hostView),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){const e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,n){if(this.isActivated)throw new l.\u0275RuntimeError(4013,_e);this._activatedRoute=e;const i=this.location,s=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,u=new vn(e,a,i.injector);if(n&&function mn(t){return!!t.resolveComponentFactory}(n)){const d=n.resolveComponentFactory(s);this.activated=i.createComponent(d,i.length,u)}else this.activated=i.createComponent(s,{index:i.length,injector:u,environmentInjector:n??this.environmentInjector});this.changeDetector.markForCheck(),this.activateEvents.emit(this.activated.instance)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275dir=l.\u0275\u0275defineDirective({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[l.\u0275\u0275NgOnChangesFeature]}),t})();class vn{constructor(r,e,n){this.route=r,this.childContexts=e,this.parent=n}get(r,e){return r===$?this.route:r===H?this.childContexts:this.parent.get(r,e)}}let Ee=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=l.\u0275\u0275defineComponent({type:t,selectors:[["ng-component"]],standalone:!0,features:[l.\u0275\u0275StandaloneFeature],decls:1,vars:0,template:function(e,n){1&e&&l.\u0275\u0275element(0,"router-outlet")},dependencies:[Ke],encapsulation:2}),t})();function Yt(t,r){return t.providers&&!t._injector&&(t._injector=(0,l.createEnvironmentInjector)(t.providers,r,`Route: ${t.path}`)),t._injector??r}function Ze(t){const r=t.children&&t.children.map(Ze),e=r?{...t,children:r}:{...t};return!e.component&&!e.loadComponent&&(r||e.loadChildren)&&e.outlet&&e.outlet!==p&&(e.component=Ee),e}function w(t){return t.outlet||p}function Jt(t,r){const e=t.filter(n=>w(n)===r);return e.push(...t.filter(n=>w(n)!==r)),e}function oe(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let r=t.parent;r;r=r.parent){const e=r.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}class wn{constructor(r,e,n,i){this.routeReuseStrategy=r,this.futureState=e,this.currState=n,this.forwardEvent=i}activate(r){const e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,r),Ve(this.futureState.root),this.activateChildRoutes(e,n,r)}deactivateChildRoutes(r,e,n){const i=V(e);r.children.forEach(o=>{const s=o.value.outlet;this.deactivateRoutes(o,i[s],n),delete i[s]}),R(i,(o,s)=>{this.deactivateRouteAndItsChildren(o,n)})}deactivateRoutes(r,e,n){const i=r.value,o=e?e.value:null;if(i===o)if(i.component){const s=n.getContext(i.outlet);s&&this.deactivateChildRoutes(r,e,s.children)}else this.deactivateChildRoutes(r,e,n);else o&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(r,e){r.value.component&&this.routeReuseStrategy.shouldDetach(r.value.snapshot)?this.detachAndStoreRouteSubtree(r,e):this.deactivateRouteAndOutlet(r,e)}detachAndStoreRouteSubtree(r,e){const n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,o=V(r);for(const s of Object.keys(o))this.deactivateRouteAndItsChildren(o[s],i);if(n&&n.outlet){const s=n.outlet.detach(),a=n.children.onOutletDeactivated();this.routeReuseStrategy.store(r.value.snapshot,{componentRef:s,route:r,contexts:a})}}deactivateRouteAndOutlet(r,e){const n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,o=V(r);for(const s of Object.keys(o))this.deactivateRouteAndItsChildren(o[s],i);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.resolver=null,n.route=null)}activateChildRoutes(r,e,n){const i=V(e);r.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],n),this.forwardEvent(new Ft(o.value.snapshot))}),r.children.length&&this.forwardEvent(new kt(r.value.snapshot))}activateRoutes(r,e,n){const i=r.value,o=e?e.value:null;if(Ve(i),i===o)if(i.component){const s=n.getOrCreateContext(i.outlet);this.activateChildRoutes(r,e,s.children)}else this.activateChildRoutes(r,e,n);else if(i.component){const s=n.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){const a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Ve(a.route.value),this.activateChildRoutes(r,null,s.children)}else{const a=oe(i.snapshot),u=a?.get(l.ComponentFactoryResolver)??null;s.attachRef=null,s.route=i,s.resolver=u,s.injector=a,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(r,null,s.children)}}else this.activateChildRoutes(r,null,n)}}class Xt{constructor(r){this.path=r,this.route=this.path[this.path.length-1]}}class Ie{constructor(r,e){this.component=r,this.route=e}}function _n(t,r,e){const n=t._root;return se(n,r?r._root:null,e,[n.value])}function G(t,r){const e=Symbol(),n=r.get(t,e);return n===e?"function"!=typeof t||(0,l.\u0275isInjectable)(t)?r.get(t):t:n}function se(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){const o=V(r);return t.children.forEach(s=>{(function In(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){const o=t.value,s=r?r.value:null,a=e?e.getContext(t.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){const u=function An(t,r,e){if("function"==typeof e)return e(t,r);switch(e){case"pathParamsChange":return!j(t.url,r.url);case"pathParamsOrQueryParamsChange":return!j(t.url,r.url)||!I(t.queryParams,r.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!He(t,r)||!I(t.queryParams,r.queryParams);default:return!He(t,r)}}(s,o,o.routeConfig.runGuardsAndResolvers);u?i.canActivateChecks.push(new Xt(n)):(o.data=s.data,o._resolvedData=s._resolvedData),se(t,r,o.component?a?a.children:null:e,n,i),u&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Ie(a.outlet.component,s))}else s&&ae(r,a,i),i.canActivateChecks.push(new Xt(n)),se(t,null,o.component?a?a.children:null:e,n,i)})(s,o[s.value.outlet],e,n.concat([s.value]),i),delete o[s.value.outlet]}),R(o,(s,a)=>ae(s,e.getContext(a),i)),i}function ae(t,r,e){const n=V(t),i=t.value;R(n,(o,s)=>{ae(o,i.component?r?r.children.getContext(s):null:r,e)}),e.canDeactivateChecks.push(new Ie(i.component&&r&&r.outlet&&r.outlet.isActivated?r.outlet.component:null,i))}function le(t){return"function"==typeof t}function Ye(t){return t instanceof h.EmptyError||"EmptyError"===t?.name}const Ae=Symbol("INITIAL_VALUE");function K(){return(0,c.switchMap)(t=>(0,h.combineLatest)(t.map(r=>r.pipe((0,c.take)(1),(0,c.startWith)(Ae)))).pipe((0,c.map)(r=>{for(const e of r)if(!0!==e){if(e===Ae)return Ae;if(!1===e||e instanceof A)return e}return!0}),(0,c.filter)(r=>r!==Ae),(0,c.take)(1)))}function er(t){return(0,h.pipe)((0,c.tap)(r=>{if(L(r))throw Ht(0,r)}),(0,c.map)(r=>!0===r))}const Je={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function tr(t,r,e,n,i){const o=Xe(t,r,e);return o.matched?function qn(t,r,e,n){const i=r.canMatch;if(!i||0===i.length)return(0,h.of)(!0);const o=i.map(s=>{const a=G(s,t);return U(function Pn(t){return t&&le(t.canMatch)}(a)?a.canMatch(r,e):t.runInContext(()=>a(r,e)))});return(0,h.of)(o).pipe(K(),er())}(n=Yt(r,n),r,e).pipe((0,c.map)(s=>!0===s?o:{...Je})):(0,h.of)(o)}function Xe(t,r,e){if(""===r.path)return"full"===r.pathMatch&&(t.hasChildren()||e.length>0)?{...Je}:{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};const i=(r.matcher||dt)(e,t,r);if(!i)return{...Je};const o={};R(i.posParams,(a,u)=>{o[u]=a.path});const s=i.consumed.length>0?{...o,...i.consumed[i.consumed.length-1].parameters}:o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function Te(t,r,e,n){if(e.length>0&&function Hn(t,r,e){return e.some(n=>be(t,r,n)&&w(n)!==p)}(t,e,n)){const o=new g(r,function Vn(t,r,e,n){const i={};i[p]=n,n._sourceSegment=t,n._segmentIndexShift=r.length;for(const o of e)if(""===o.path&&w(o)!==p){const s=new g([],{});s._sourceSegment=t,s._segmentIndexShift=r.length,i[w(o)]=s}return i}(t,r,n,new g(e,t.children)));return o._sourceSegment=t,o._segmentIndexShift=r.length,{segmentGroup:o,slicedSegments:[]}}if(0===e.length&&function Gn(t,r,e){return e.some(n=>be(t,r,n))}(t,e,n)){const o=new g(t.segments,function Bn(t,r,e,n,i){const o={};for(const s of n)if(be(t,e,s)&&!i[w(s)]){const a=new g([],{});a._sourceSegment=t,a._segmentIndexShift=r.length,o[w(s)]=a}return{...i,...o}}(t,r,e,n,t.children));return o._sourceSegment=t,o._segmentIndexShift=r.length,{segmentGroup:o,slicedSegments:e}}const i=new g(t.segments,t.children);return i._sourceSegment=t,i._segmentIndexShift=r.length,{segmentGroup:i,slicedSegments:e}}function be(t,r,e){return(!(t.hasChildren()||r.length>0)||"full"!==e.pathMatch)&&""===e.path}function rr(t,r,e,n){return!!(w(t)===n||n!==p&&be(r,e,t))&&("**"===t.path||Xe(r,t,e).matched)}function nr(t,r,e){return 0===r.length&&!t.children[e]}const Ue=!1;class Oe{constructor(r){this.segmentGroup=r||null}}class ir{constructor(r){this.urlTree=r}}function ce(t){return(0,h.throwError)(new Oe(t))}function or(t){return(0,h.throwError)(new ir(t))}class Yn{constructor(r,e,n,i,o){this.injector=r,this.configLoader=e,this.urlSerializer=n,this.urlTree=i,this.config=o,this.allowRedirects=!0}apply(){const r=Te(this.urlTree.root,[],[],this.config).segmentGroup,e=new g(r.segments,r.children);return this.expandSegmentGroup(this.injector,this.config,e,p).pipe((0,c.map)(o=>this.createUrlTree(ye(o),this.urlTree.queryParams,this.urlTree.fragment))).pipe((0,c.catchError)(o=>{if(o instanceof ir)return this.allowRedirects=!1,this.match(o.urlTree);throw o instanceof Oe?this.noMatchError(o):o}))}match(r){return this.expandSegmentGroup(this.injector,this.config,r.root,p).pipe((0,c.map)(i=>this.createUrlTree(ye(i),r.queryParams,r.fragment))).pipe((0,c.catchError)(i=>{throw i instanceof Oe?this.noMatchError(i):i}))}noMatchError(r){return new l.\u0275RuntimeError(4002,Ue)}createUrlTree(r,e,n){const i=je(r);return new A(i,e,n)}expandSegmentGroup(r,e,n,i){return 0===n.segments.length&&n.hasChildren()?this.expandChildren(r,e,n).pipe((0,c.map)(o=>new g([],o))):this.expandSegment(r,n,e,n.segments,i,!0)}expandChildren(r,e,n){const i=[];for(const o of Object.keys(n.children))"primary"===o?i.unshift(o):i.push(o);return(0,h.from)(i).pipe((0,c.concatMap)(o=>{const s=n.children[o],a=Jt(e,o);return this.expandSegmentGroup(r,a,s,o).pipe((0,c.map)(u=>({segment:u,outlet:o})))}),(0,c.scan)((o,s)=>(o[s.outlet]=s.segment,o),{}),(0,c.last)())}expandSegment(r,e,n,i,o,s){return(0,h.from)(n).pipe((0,c.concatMap)(a=>this.expandSegmentAgainstRoute(r,e,n,a,i,o,s).pipe((0,c.catchError)(d=>{if(d instanceof Oe)return(0,h.of)(null);throw d}))),(0,c.first)(a=>!!a),(0,c.catchError)((a,u)=>{if(Ye(a))return nr(e,i,o)?(0,h.of)(new g([],{})):ce(e);throw a}))}expandSegmentAgainstRoute(r,e,n,i,o,s,a){return rr(i,e,o,s)?void 0===i.redirectTo?this.matchSegmentAgainstRoute(r,e,i,o,s):a&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(r,e,n,i,o,s):ce(e):ce(e)}expandSegmentAgainstRouteUsingRedirect(r,e,n,i,o,s){return"**"===i.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(r,n,i,s):this.expandRegularSegmentAgainstRouteUsingRedirect(r,e,n,i,o,s)}expandWildCardWithParamsAgainstRouteUsingRedirect(r,e,n,i){const o=this.applyRedirectCommands([],n.redirectTo,{});return n.redirectTo.startsWith("/")?or(o):this.lineralizeSegments(n,o).pipe((0,c.mergeMap)(s=>{const a=new g(s,{});return this.expandSegment(r,a,e,s,i,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(r,e,n,i,o,s){const{matched:a,consumedSegments:u,remainingSegments:d,positionalParamSegments:f}=Xe(e,i,o);if(!a)return ce(e);const v=this.applyRedirectCommands(u,i.redirectTo,f);return i.redirectTo.startsWith("/")?or(v):this.lineralizeSegments(i,v).pipe((0,c.mergeMap)(m=>this.expandSegment(r,e,n,m.concat(d),s,!1)))}matchSegmentAgainstRoute(r,e,n,i,o){return"**"===n.path?(r=Yt(n,r),n.loadChildren?(n._loadedRoutes?(0,h.of)({routes:n._loadedRoutes,injector:n._loadedInjector}):this.configLoader.loadChildren(r,n)).pipe((0,c.map)(a=>(n._loadedRoutes=a.routes,n._loadedInjector=a.injector,new g(i,{})))):(0,h.of)(new g(i,{}))):tr(e,n,i,r).pipe((0,c.switchMap)(({matched:s,consumedSegments:a,remainingSegments:u})=>s?this.getChildConfig(r=n._injector??r,n,i).pipe((0,c.mergeMap)(f=>{const v=f.injector??r,m=f.routes,{segmentGroup:P,slicedSegments:b}=Te(e,a,u,m),k=new g(P.segments,P.children);if(0===b.length&&k.hasChildren())return this.expandChildren(v,m,k).pipe((0,c.map)(Fi=>new g(a,Fi)));if(0===m.length&&0===b.length)return(0,h.of)(new g(a,{}));const E=w(n)===o;return this.expandSegment(v,k,m,b,E?p:o,!0).pipe((0,c.map)(ut=>new g(a.concat(ut.segments),ut.children)))})):ce(e)))}getChildConfig(r,e,n){return e.children?(0,h.of)({routes:e.children,injector:r}):e.loadChildren?void 0!==e._loadedRoutes?(0,h.of)({routes:e._loadedRoutes,injector:e._loadedInjector}):function Wn(t,r,e,n){const i=r.canLoad;if(void 0===i||0===i.length)return(0,h.of)(!0);const o=i.map(s=>{const a=G(s,t);return U(function bn(t){return t&&le(t.canLoad)}(a)?a.canLoad(r,e):t.runInContext(()=>a(r,e)))});return(0,h.of)(o).pipe(K(),er())}(r,e,n).pipe((0,c.mergeMap)(i=>i?this.configLoader.loadChildren(r,e).pipe((0,c.tap)(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):function Qn(t){return(0,h.throwError)(Gt(Ue,3))}())):(0,h.of)({routes:[],injector:r})}lineralizeSegments(r,e){let n=[],i=e.root;for(;;){if(n=n.concat(i.segments),0===i.numberOfChildren)return(0,h.of)(n);if(i.numberOfChildren>1||!i.children[p])return r.redirectTo,(0,h.throwError)(new l.\u0275RuntimeError(4e3,Ue));i=i.children[p]}}applyRedirectCommands(r,e,n){return this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),r,n)}applyRedirectCreateUrlTree(r,e,n,i){const o=this.createSegmentGroup(r,e.root,n,i);return new A(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(r,e){const n={};return R(r,(i,o)=>{if("string"==typeof i&&i.startsWith(":")){const a=i.substring(1);n[o]=e[a]}else n[o]=i}),n}createSegmentGroup(r,e,n,i){const o=this.createSegments(r,e.segments,n,i);let s={};return R(e.children,(a,u)=>{s[u]=this.createSegmentGroup(r,a,n,i)}),new g(o,s)}createSegments(r,e,n,i){return e.map(o=>o.path.startsWith(":")?this.findPosParam(r,o,i):this.findOrReturn(o,n))}findPosParam(r,e,n){const i=n[e.path.substring(1)];if(!i)throw new l.\u0275RuntimeError(4001,Ue);return i}findOrReturn(r,e){let n=0;for(const i of e){if(i.path===r.path)return e.splice(n),i;n++}return r}}class Xn{}class ri{constructor(r,e,n,i,o,s,a){this.injector=r,this.rootComponentType=e,this.config=n,this.urlTree=i,this.url=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a}recognize(){const r=Te(this.urlTree.root,[],[],this.config.filter(e=>void 0===e.redirectTo)).segmentGroup;return this.processSegmentGroup(this.injector,this.config,r,p).pipe((0,c.map)(e=>{if(null===e)return null;const n=new ne([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},p,this.rootComponentType,null,this.urlTree.root,-1,{}),i=new T(n,e),o=new qe(this.url,i);return this.inheritParamsAndData(o._root),o}))}inheritParamsAndData(r){const e=r.value,n=Bt(e,this.paramsInheritanceStrategy);e.params=Object.freeze(n.params),e.data=Object.freeze(n.data),r.children.forEach(i=>this.inheritParamsAndData(i))}processSegmentGroup(r,e,n,i){return 0===n.segments.length&&n.hasChildren()?this.processChildren(r,e,n):this.processSegment(r,e,n,n.segments,i)}processChildren(r,e,n){return(0,h.from)(Object.keys(n.children)).pipe((0,c.concatMap)(i=>{const o=n.children[i],s=Jt(e,i);return this.processSegmentGroup(r,s,o,i)}),(0,c.scan)((i,o)=>i&&o?(i.push(...o),i):null),(0,c.takeWhile)(i=>null!==i),(0,c.defaultIfEmpty)(null),(0,c.last)(),(0,c.map)(i=>{if(null===i)return null;const o=ar(i);return function ni(t){t.sort((r,e)=>r.value.outlet===p?-1:e.value.outlet===p?1:r.value.outlet.localeCompare(e.value.outlet))}(o),o}))}processSegment(r,e,n,i,o){return(0,h.from)(e).pipe((0,c.concatMap)(s=>this.processSegmentAgainstRoute(s._injector??r,s,n,i,o)),(0,c.first)(s=>!!s),(0,c.catchError)(s=>{if(Ye(s))return nr(n,i,o)?(0,h.of)([]):(0,h.of)(null);throw s}))}processSegmentAgainstRoute(r,e,n,i,o){if(e.redirectTo||!rr(e,n,i,o))return(0,h.of)(null);let s;if("**"===e.path){const a=i.length>0?pt(i).parameters:{},u=cr(n)+i.length,d=new ne(i,a,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,ur(e),w(e),e.component??e._loadedComponent??null,e,lr(n),u,hr(e));s=(0,h.of)({snapshot:d,consumedSegments:[],remainingSegments:[]})}else s=tr(n,e,i,r).pipe((0,c.map)(({matched:a,consumedSegments:u,remainingSegments:d,parameters:f})=>{if(!a)return null;const v=cr(n)+u.length;return{snapshot:new ne(u,f,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,ur(e),w(e),e.component??e._loadedComponent??null,e,lr(n),v,hr(e)),consumedSegments:u,remainingSegments:d}}));return s.pipe((0,c.switchMap)(a=>{if(null===a)return(0,h.of)(null);const{snapshot:u,consumedSegments:d,remainingSegments:f}=a;r=e._injector??r;const v=e._loadedInjector??r,m=function ii(t){return t.children?t.children:t.loadChildren?t._loadedRoutes:[]}(e),{segmentGroup:P,slicedSegments:b}=Te(n,d,f,m.filter(E=>void 0===E.redirectTo));if(0===b.length&&P.hasChildren())return this.processChildren(v,m,P).pipe((0,c.map)(E=>null===E?null:[new T(u,E)]));if(0===m.length&&0===b.length)return(0,h.of)([new T(u,[])]);const k=w(e)===o;return this.processSegment(v,m,P,b,k?p:o).pipe((0,c.map)(E=>null===E?null:[new T(u,E)]))}))}}function oi(t){const r=t.value.routeConfig;return r&&""===r.path&&void 0===r.redirectTo}function ar(t){const r=[],e=new Set;for(const n of t){if(!oi(n)){r.push(n);continue}const i=r.find(o=>n.value.routeConfig===o.value.routeConfig);void 0!==i?(i.children.push(...n.children),e.add(i)):r.push(n)}for(const n of e){const i=ar(n.children);r.push(new T(n.value,i))}return r.filter(n=>!e.has(n))}function lr(t){let r=t;for(;r._sourceSegment;)r=r._sourceSegment;return r}function cr(t){let r=t,e=r._segmentIndexShift??0;for(;r._sourceSegment;)r=r._sourceSegment,e+=r._segmentIndexShift??0;return e-1}function ur(t){return t.data||{}}function hr(t){return t.resolve||{}}function dr(t){return"string"==typeof t.title||null===t.title}function et(t){return(0,c.switchMap)(r=>{const e=t(r);return e?(0,h.from)(e).pipe((0,c.map)(()=>r)):(0,h.of)(r)})}const N=new l.InjectionToken("ROUTES");let tt=(()=>{class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=(0,l.inject)(l.Compiler)}loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return(0,h.of)(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);const n=U(e.loadComponent()).pipe((0,c.map)(pr),(0,c.tap)(o=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=o}),(0,c.finalize)(()=>{this.componentLoaders.delete(e)})),i=new h.ConnectableObservable(n,()=>new h.Subject).pipe((0,c.refCount)());return this.componentLoaders.set(e,i),i}loadChildren(e,n){if(this.childrenLoaders.get(n))return this.childrenLoaders.get(n);if(n._loadedRoutes)return(0,h.of)({routes:n._loadedRoutes,injector:n._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(n);const o=this.loadModuleFactoryOrRoutes(n.loadChildren).pipe((0,c.map)(a=>{this.onLoadEndListener&&this.onLoadEndListener(n);let u,d,f=!1;Array.isArray(a)?d=a:(u=a.create(e).injector,d=De(u.get(N,[],l.InjectFlags.Self|l.InjectFlags.Optional)));return{routes:d.map(Ze),injector:u}}),(0,c.finalize)(()=>{this.childrenLoaders.delete(n)})),s=new h.ConnectableObservable(o,()=>new h.Subject).pipe((0,c.refCount)());return this.childrenLoaders.set(n,s),s}loadModuleFactoryOrRoutes(e){return U(e()).pipe((0,c.map)(pr),(0,c.mergeMap)(n=>n instanceof l.NgModuleFactory||Array.isArray(n)?(0,h.of)(n):(0,h.from)(this.compiler.compileModuleAsync(n))))}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function pr(t){return function fi(t){return t&&"object"==typeof t&&"default"in t}(t)?t.default:t}let Pe=(()=>{class t{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.lastSuccessfulNavigation=null,this.events=new h.Subject,this.configLoader=(0,l.inject)(tt),this.environmentInjector=(0,l.inject)(l.EnvironmentInjector),this.urlSerializer=(0,l.inject)(F),this.rootContexts=(0,l.inject)(H),this.navigationId=0,this.afterPreactivation=()=>(0,h.of)(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=i=>this.events.next(new $t(i)),this.configLoader.onLoadStartListener=i=>this.events.next(new Lt(i))}complete(){this.transitions?.complete()}handleNavigationRequest(e){const n=++this.navigationId;this.transitions?.next({...this.transitions.value,...e,id:n})}setupNavigations(e){return this.transitions=new h.BehaviorSubject({id:0,targetPageId:0,currentUrlTree:e.currentUrlTree,currentRawUrl:e.currentUrlTree,extractedUrl:e.urlHandlingStrategy.extract(e.currentUrlTree),urlAfterRedirects:e.urlHandlingStrategy.extract(e.currentUrlTree),rawUrl:e.currentUrlTree,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:ee,restoredState:null,currentSnapshot:e.routerState.snapshot,targetSnapshot:null,currentRouterState:e.routerState,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe((0,c.filter)(n=>0!==n.id),(0,c.map)(n=>({...n,extractedUrl:e.urlHandlingStrategy.extract(n.rawUrl)})),(0,c.switchMap)(n=>{let i=!1,o=!1;return(0,h.of)(n).pipe((0,c.tap)(s=>{this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),(0,c.switchMap)(s=>{const a=e.browserUrlTree.toString(),u=!e.navigated||s.extractedUrl.toString()!==a||a!==e.currentUrlTree.toString();if(!u&&"reload"!==(s.extras.onSameUrlNavigation??e.onSameUrlNavigation)){const f="";return this.events.next(new re(s.id,e.serializeUrl(n.rawUrl),f,0)),e.rawUrlTree=s.rawUrl,s.resolve(null),h.EMPTY}if(e.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return gr(s.source)&&(e.browserUrlTree=s.extractedUrl),(0,h.of)(s).pipe((0,c.switchMap)(f=>{const v=this.transitions?.getValue();return this.events.next(new Se(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),v!==this.transitions?.getValue()?h.EMPTY:Promise.resolve(f)}),function Jn(t,r,e,n){return(0,c.switchMap)(i=>function Zn(t,r,e,n,i){return new Yn(t,r,e,n,i).apply()}(t,r,e,i.extractedUrl,n).pipe((0,c.map)(o=>({...i,urlAfterRedirects:o}))))}(this.environmentInjector,this.configLoader,this.urlSerializer,e.config),(0,c.tap)(f=>{this.currentNavigation={...this.currentNavigation,finalUrl:f.urlAfterRedirects},n.urlAfterRedirects=f.urlAfterRedirects}),function ai(t,r,e,n,i){return(0,c.mergeMap)(o=>function ti(t,r,e,n,i,o,s="emptyOnly"){return new ri(t,r,e,n,i,s,o).recognize().pipe((0,c.switchMap)(a=>null===a?function ei(t){return new h.Observable(r=>r.error(t))}(new Xn):(0,h.of)(a)))}(t,r,e,o.urlAfterRedirects,n.serialize(o.urlAfterRedirects),n,i).pipe((0,c.map)(s=>({...o,targetSnapshot:s}))))}(this.environmentInjector,this.rootComponentType,e.config,this.urlSerializer,e.paramsInheritanceStrategy),(0,c.tap)(f=>{if(n.targetSnapshot=f.targetSnapshot,"eager"===e.urlUpdateStrategy){if(!f.extras.skipLocationChange){const m=e.urlHandlingStrategy.merge(f.urlAfterRedirects,f.rawUrl);e.setBrowserUrl(m,f)}e.browserUrlTree=f.urlAfterRedirects}const v=new Mt(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(v)}));if(u&&e.urlHandlingStrategy.shouldProcessUrl(e.rawUrlTree)){const{id:f,extractedUrl:v,source:m,restoredState:P,extras:b}=s,k=new Se(f,this.urlSerializer.serialize(v),m,P);this.events.next(k);const E=qt(v,this.rootComponentType).snapshot;return n={...s,targetSnapshot:E,urlAfterRedirects:v,extras:{...b,skipLocationChange:!1,replaceUrl:!1}},(0,h.of)(n)}{const f="";return this.events.next(new re(s.id,e.serializeUrl(n.extractedUrl),f,1)),e.rawUrlTree=s.rawUrl,s.resolve(null),h.EMPTY}}),(0,c.tap)(s=>{const a=new Pt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),(0,c.map)(s=>n={...s,guards:_n(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),function xn(t,r){return(0,c.mergeMap)(e=>{const{targetSnapshot:n,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=e;return 0===s.length&&0===o.length?(0,h.of)({...e,guardsResult:!0}):function jn(t,r,e,n){return(0,h.from)(t).pipe((0,c.mergeMap)(i=>function Fn(t,r,e,n,i){const o=r&&r.routeConfig?r.routeConfig.canDeactivate:null;if(!o||0===o.length)return(0,h.of)(!0);const s=o.map(a=>{const u=oe(r)??i,d=G(a,u);return U(function Mn(t){return t&&le(t.canDeactivate)}(d)?d.canDeactivate(t,r,e,n):u.runInContext(()=>d(t,r,e,n))).pipe((0,c.first)())});return(0,h.of)(s).pipe(K())}(i.component,i.route,e,r,n)),(0,c.first)(i=>!0!==i,!0))}(s,n,i,t).pipe((0,c.mergeMap)(a=>a&&function Tn(t){return"boolean"==typeof t}(a)?function Ln(t,r,e,n){return(0,h.from)(r).pipe((0,c.concatMap)(i=>(0,h.concat)(function Nn(t,r){return null!==t&&r&&r(new Nt(t)),(0,h.of)(!0)}(i.route.parent,n),function $n(t,r){return null!==t&&r&&r(new zt(t)),(0,h.of)(!0)}(i.route,n),function zn(t,r,e){const n=r[r.length-1],o=r.slice(0,r.length-1).reverse().map(s=>function En(t){const r=t.routeConfig?t.routeConfig.canActivateChild:null;return r&&0!==r.length?{node:t,guards:r}:null}(s)).filter(s=>null!==s).map(s=>(0,h.defer)(()=>{const a=s.guards.map(u=>{const d=oe(s.node)??e,f=G(u,d);return U(function On(t){return t&&le(t.canActivateChild)}(f)?f.canActivateChild(n,t):d.runInContext(()=>f(n,t))).pipe((0,c.first)())});return(0,h.of)(a).pipe(K())}));return(0,h.of)(o).pipe(K())}(t,i.path,e),function kn(t,r,e){const n=r.routeConfig?r.routeConfig.canActivate:null;if(!n||0===n.length)return(0,h.of)(!0);const i=n.map(o=>(0,h.defer)(()=>{const s=oe(r)??e,a=G(o,s);return U(function Un(t){return t&&le(t.canActivate)}(a)?a.canActivate(r,t):s.runInContext(()=>a(r,t))).pipe((0,c.first)())}));return(0,h.of)(i).pipe(K())}(t,i.route,e))),(0,c.first)(i=>!0!==i,!0))}(n,o,t,r):(0,h.of)(a)),(0,c.map)(a=>({...e,guardsResult:a})))})}(this.environmentInjector,s=>this.events.next(s)),(0,c.tap)(s=>{if(n.guardsResult=s.guardsResult,L(s.guardsResult))throw Ht(0,s.guardsResult);const a=new Dt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),(0,c.filter)(s=>!!s.guardsResult||(e.restoreHistory(s),this.cancelNavigationTransition(s,"",3),!1)),et(s=>{if(s.guards.canActivateChecks.length)return(0,h.of)(s).pipe((0,c.tap)(a=>{const u=new xt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}),(0,c.switchMap)(a=>{let u=!1;return(0,h.of)(a).pipe(function li(t,r){return(0,c.mergeMap)(e=>{const{targetSnapshot:n,guards:{canActivateChecks:i}}=e;if(!i.length)return(0,h.of)(e);let o=0;return(0,h.from)(i).pipe((0,c.concatMap)(s=>function ci(t,r,e,n){const i=t.routeConfig,o=t._resolve;return void 0!==i?.title&&!dr(i)&&(o[Z]=i.title),function ui(t,r,e,n){const i=function hi(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}(t);if(0===i.length)return(0,h.of)({});const o={};return(0,h.from)(i).pipe((0,c.mergeMap)(s=>function di(t,r,e,n){const i=oe(r)??n,o=G(t,i);return U(o.resolve?o.resolve(r,e):i.runInContext(()=>o(r,e)))}(t[s],r,e,n).pipe((0,c.first)(),(0,c.tap)(a=>{o[s]=a}))),(0,c.takeLast)(1),(0,c.mapTo)(o),(0,c.catchError)(s=>Ye(s)?h.EMPTY:(0,h.throwError)(s)))}(o,t,r,n).pipe((0,c.map)(s=>(t._resolvedData=s,t.data=Bt(t,e).resolve,i&&dr(i)&&(t.data[Z]=i.title),null)))}(s.route,n,t,r)),(0,c.tap)(()=>o++),(0,c.takeLast)(1),(0,c.mergeMap)(s=>o===i.length?(0,h.of)(e):h.EMPTY))})}(e.paramsInheritanceStrategy,this.environmentInjector),(0,c.tap)({next:()=>u=!0,complete:()=>{u||(e.restoreHistory(a),this.cancelNavigationTransition(a,"",2))}}))}),(0,c.tap)(a=>{const u=new jt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}))}),et(s=>{const a=u=>{const d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe((0,c.tap)(f=>{u.component=f}),(0,c.map)(()=>{})));for(const f of u.children)d.push(...a(f));return d};return(0,h.combineLatest)(a(s.targetSnapshot.root)).pipe((0,c.defaultIfEmpty)(),(0,c.take)(1))}),et(()=>this.afterPreactivation()),(0,c.map)(s=>{const a=function fn(t,r,e){const n=ie(t,r._root,e?e._root:void 0);return new We(n,r)}(e.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return n={...s,targetRouterState:a}}),(0,c.tap)(s=>{e.currentUrlTree=s.urlAfterRedirects,e.rawUrlTree=e.urlHandlingStrategy.merge(s.urlAfterRedirects,s.rawUrl),e.routerState=s.targetRouterState,"deferred"===e.urlUpdateStrategy&&(s.extras.skipLocationChange||e.setBrowserUrl(e.rawUrlTree,s),e.browserUrlTree=s.urlAfterRedirects)}),((t,r,e)=>(0,c.map)(n=>(new wn(r,n.targetRouterState,n.currentRouterState,e).activate(t),n)))(this.rootContexts,e.routeReuseStrategy,s=>this.events.next(s)),(0,c.take)(1),(0,c.tap)({next:s=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,e.navigated=!0,this.events.next(new O(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(e.currentUrlTree))),e.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{i=!0}}),(0,c.finalize)(()=>{i||o||this.cancelNavigationTransition(n,"",1),this.currentNavigation?.id===n.id&&(this.currentNavigation=null)}),(0,c.catchError)(s=>{if(o=!0,Qt(s)){Kt(s)||(e.navigated=!0,e.restoreHistory(n,!0));const a=new te(n.id,this.urlSerializer.serialize(n.extractedUrl),s.message,s.cancellationCode);if(this.events.next(a),Kt(s)){const u=e.urlHandlingStrategy.merge(s.url,e.rawUrlTree),d={skipLocationChange:n.extras.skipLocationChange,replaceUrl:"eager"===e.urlUpdateStrategy||gr(n.source)};e.scheduleNavigation(u,ee,null,d,{resolve:n.resolve,reject:n.reject,promise:n.promise})}else n.resolve(!1)}else{e.restoreHistory(n,!0);const a=new we(n.id,this.urlSerializer.serialize(n.extractedUrl),s,n.targetSnapshot??void 0);this.events.next(a);try{n.resolve(e.errorHandler(s))}catch(u){n.reject(u)}}return h.EMPTY}))}))}cancelNavigationTransition(e,n,i){const o=new te(e.id,this.urlSerializer.serialize(e.extractedUrl),n,i);this.events.next(o),e.resolve(!1)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function gr(t){return t!==ee}let rt=(()=>{class t{buildTitle(e){let n,i=e.root;for(;void 0!==i;)n=this.getResolvedTitleForRoute(i)??n,i=i.children.find(o=>o.outlet===p);return n}getResolvedTitleForRoute(e){return e.data[Z]}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:function(){return(0,l.inject)(vr)},providedIn:"root"}),t})(),vr=(()=>{class t extends rt{constructor(e){super(),this.title=e}updateTitle(e){const n=this.buildTitle(e);void 0!==n&&this.title.setTitle(n)}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(Dr.Title))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),mr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:function(){return(0,l.inject)(pi)},providedIn:"root"}),t})();class Rr{shouldDetach(r){return!1}store(r,e){}shouldAttach(r){return!1}retrieve(r){return null}shouldReuseRoute(r,e){return r.routeConfig===e.routeConfig}}let pi=(()=>{class t extends Rr{}return t.\u0275fac=function(){let r;return function(n){return(r||(r=l.\u0275\u0275getInheritedFactory(t)))(n||t)}}(),t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const ue=new l.InjectionToken("",{providedIn:"root",factory:()=>({})});let Cr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:function(){return(0,l.inject)(vi)},providedIn:"root"}),t})(),vi=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,n){return e}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function mi(t){throw t}function Ri(t,r,e){return r.parse("/")}const Ci={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},yi={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let C=(()=>{class t{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){if("computed"===this.canceledNavigationResolution)return this.location.getState()?.\u0275routerPageId}get events(){return this.navigationTransitions.events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=(0,l.inject)(l.\u0275Console),this.isNgZoneEnabled=!1,this.options=(0,l.inject)(ue,{optional:!0})||{},this.errorHandler=this.options.errorHandler||mi,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||Ri,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=(0,l.inject)(Cr),this.routeReuseStrategy=(0,l.inject)(mr),this.urlCreationStrategy=(0,l.inject)(un),this.titleStrategy=(0,l.inject)(rt),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=De((0,l.inject)(N,{optional:!0})??[]),this.navigationTransitions=(0,l.inject)(Pe),this.urlSerializer=(0,l.inject)(F),this.location=(0,l.inject)(S.Location),this.isNgZoneEnabled=(0,l.inject)(l.NgZone)instanceof l.NgZone&&l.NgZone.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new A,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=qt(this.currentUrlTree,null),this.navigationTransitions.setupNavigations(this).subscribe(e=>{this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId??0},e=>{this.console.warn(`Unhandled Navigation Error: ${e}`)})}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const e=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),ee,e)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(e=>{const n="popstate"===e.type?"popstate":"hashchange";"popstate"===n&&setTimeout(()=>{this.navigateToSyncWithBrowser(e.url,n,e.state)},0)}))}navigateToSyncWithBrowser(e,n,i){const o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){const u={...i};delete u.navigationId,delete u.\u0275routerPageId,0!==Object.keys(u).length&&(o.state=u)}const a=this.parseUrl(e);this.scheduleNavigation(a,n,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}resetConfig(e){this.config=e.map(Ze),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0}createUrlTree(e,n={}){const{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:u}=n,d=u?this.currentUrlTree.fragment:s;let f=null;switch(a){case"merge":f={...this.currentUrlTree.queryParams,...o};break;case"preserve":f=this.currentUrlTree.queryParams;break;default:f=o||null}return null!==f&&(f=this.removeEmptyProps(f)),this.urlCreationStrategy.createUrlTree(i,this.routerState,this.currentUrlTree,e,f,d??null)}navigateByUrl(e,n={skipLocationChange:!1}){const i=L(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,ee,null,n)}navigate(e,n={skipLocationChange:!1}){return function Si(t){for(let r=0;r<t.length;r++){const e=t[r];if(null==e)throw new l.\u0275RuntimeError(4008,false)}}(e),this.navigateByUrl(this.createUrlTree(e,n),n)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){let n;try{n=this.urlSerializer.parse(e)}catch(i){n=this.malformedUriErrorHandler(i,this.urlSerializer,e)}return n}isActive(e,n){let i;if(i=!0===n?{...Ci}:!1===n?{...yi}:n,L(e))return vt(this.currentUrlTree,e,i);const o=this.parseUrl(e);return vt(this.currentUrlTree,o,i)}removeEmptyProps(e){return Object.keys(e).reduce((n,i)=>{const o=e[i];return null!=o&&(n[i]=o),n},{})}scheduleNavigation(e,n,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,u,d,f;return s?(a=s.resolve,u=s.reject,d=s.promise):d=new Promise((v,m)=>{a=v,u=m}),f="computed"===this.canceledNavigationResolution?i&&i.\u0275routerPageId?i.\u0275routerPageId:(this.browserPageId??0)+1:0,this.navigationTransitions.handleNavigationRequest({targetPageId:f,source:n,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:a,reject:u,promise:d,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),d.catch(v=>Promise.reject(v))}setBrowserUrl(e,n){const i=this.urlSerializer.serialize(e);if(this.location.isCurrentPathEqualTo(i)||n.extras.replaceUrl){const s={...n.extras.state,...this.generateNgRouterState(n.id,this.browserPageId)};this.location.replaceState(i,"",s)}else{const o={...n.extras.state,...this.generateNgRouterState(n.id,n.targetPageId)};this.location.go(i,"",o)}}restoreHistory(e,n=!1){if("computed"===this.canceledNavigationResolution){const o=this.currentPageId-(this.browserPageId??this.currentPageId);0!==o?this.location.historyGo(o):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===o&&(this.resetState(e),this.browserUrlTree=e.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(n&&this.resetState(e),this.resetUrlToCurrentUrlTree())}resetState(e){this.routerState=e.currentRouterState,this.currentUrlTree=e.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,n){return"computed"===this.canceledNavigationResolution?{navigationId:e,\u0275routerPageId:n}:{navigationId:e}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),he=(()=>{class t{constructor(e,n,i,o,s,a){this.router=e,this.route=n,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this._preserveFragment=!1,this._skipLocationChange=!1,this._replaceUrl=!1,this.href=null,this.commands=null,this.onChanges=new h.Subject;const u=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement="a"===u||"area"===u,this.isAnchorElement?this.subscription=e.events.subscribe(d=>{d instanceof O&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}set preserveFragment(e){this._preserveFragment=(0,l.\u0275coerceToBoolean)(e)}get preserveFragment(){return this._preserveFragment}set skipLocationChange(e){this._skipLocationChange=(0,l.\u0275coerceToBoolean)(e)}get skipLocationChange(){return this._skipLocationChange}set replaceUrl(e){this._replaceUrl=(0,l.\u0275coerceToBoolean)(e)}get replaceUrl(){return this._replaceUrl}setTabIndexIfNotOnNativeEl(e){null!=this.tabIndexAttribute||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(e){null!=e?(this.commands=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(e,n,i,o,s){return!!(null===this.urlTree||this.isAnchorElement&&(0!==e||n||i||o||s||"string"==typeof this.target&&"_self"!=this.target))||(this.router.navigateByUrl(this.urlTree,{skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state}),!this.isAnchorElement)}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){this.href=null!==this.urlTree&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(this.urlTree)):null;const e=null===this.href?null:(0,l.\u0275\u0275sanitizeUrlOrResourceUrl)(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",e)}applyAttributeValue(e,n){const i=this.renderer,o=this.el.nativeElement;null!==n?i.setAttribute(o,e,n):i.removeAttribute(o,e)}get urlTree(){return null===this.commands?null:this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275directiveInject(C),l.\u0275\u0275directiveInject($),l.\u0275\u0275injectAttribute("tabindex"),l.\u0275\u0275directiveInject(l.Renderer2),l.\u0275\u0275directiveInject(l.ElementRef),l.\u0275\u0275directiveInject(S.LocationStrategy))},t.\u0275dir=l.\u0275\u0275defineDirective({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(e,n){1&e&&l.\u0275\u0275listener("click",function(o){return n.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),2&e&&l.\u0275\u0275attribute("target",n.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",relativeTo:"relativeTo",preserveFragment:"preserveFragment",skipLocationChange:"skipLocationChange",replaceUrl:"replaceUrl",routerLink:"routerLink"},standalone:!0,features:[l.\u0275\u0275NgOnChangesFeature]}),t})(),yr=(()=>{class t{get isActive(){return this._isActive}constructor(e,n,i,o,s){this.router=e,this.element=n,this.renderer=i,this.cdr=o,this.link=s,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new l.EventEmitter,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof O&&this.update()})}ngAfterContentInit(){(0,h.of)(this.links.changes,(0,h.of)(null)).pipe((0,c.mergeAll)()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();const e=[...this.links.toArray(),this.link].filter(n=>!!n).map(n=>n.onChanges);this.linkInputChangesSubscription=(0,h.from)(e).pipe((0,c.mergeAll)()).subscribe(n=>{this._isActive!==this.isLinkActive(this.router)(n)&&this.update()})}set routerLinkActive(e){const n=Array.isArray(e)?e:e.split(" ");this.classes=n.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||Promise.resolve().then(()=>{const e=this.hasActiveLinks();this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.classes.forEach(n=>{e?this.renderer.addClass(this.element.nativeElement,n):this.renderer.removeClass(this.element.nativeElement,n)}),e&&void 0!==this.ariaCurrentWhenActive?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this.isActiveChange.emit(e))})}isLinkActive(e){const n=function wi(t){return!!t.paths}(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>!!i.urlTree&&e.isActive(i.urlTree,n)}hasActiveLinks(){const e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275directiveInject(C),l.\u0275\u0275directiveInject(l.ElementRef),l.\u0275\u0275directiveInject(l.Renderer2),l.\u0275\u0275directiveInject(l.ChangeDetectorRef),l.\u0275\u0275directiveInject(he,8))},t.\u0275dir=l.\u0275\u0275defineDirective({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(e,n,i){if(1&e&&l.\u0275\u0275contentQuery(i,he,5),2&e){let o;l.\u0275\u0275queryRefresh(o=l.\u0275\u0275loadQuery())&&(n.links=o)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[l.\u0275\u0275NgOnChangesFeature]}),t})();class it{}let _i=(()=>{class t{preload(e,n){return n().pipe((0,c.catchError)(()=>(0,h.of)(null)))}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),Ei=(()=>{class t{preload(e,n){return(0,h.of)(null)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),Sr=(()=>{class t{constructor(e,n,i,o,s){this.router=e,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe((0,c.filter)(e=>e instanceof O),(0,c.concatMap)(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,n){const i=[];for(const o of n){o.providers&&!o._injector&&(o._injector=(0,l.createEnvironmentInjector)(o.providers,e,`Route: ${o.path}`));const s=o._injector??e,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&void 0===o.canLoad||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return(0,h.from)(i).pipe((0,c.mergeAll)())}preloadConfig(e,n){return this.preloadingStrategy.preload(n,()=>{let i;i=n.loadChildren&&void 0===n.canLoad?this.loader.loadChildren(e,n):(0,h.of)(null);const o=i.pipe((0,c.mergeMap)(s=>null===s?(0,h.of)(void 0):(n._loadedRoutes=s.routes,n._loadedInjector=s.injector,this.processRoutes(s.injector??e,s.routes))));if(n.loadComponent&&!n._loadedComponent){const s=this.loader.loadComponent(n);return(0,h.from)([o,s]).pipe((0,c.mergeAll)())}return o})}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(C),l.\u0275\u0275inject(l.Compiler),l.\u0275\u0275inject(l.EnvironmentInjector),l.\u0275\u0275inject(it),l.\u0275\u0275inject(tt))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const ot=new l.InjectionToken("");let wr=(()=>{class t{constructor(e,n,i,o,s={}){this.urlSerializer=e,this.transitions=n,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration=s.scrollPositionRestoration||"disabled",s.anchorScrolling=s.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof Se?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof O&&(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ke&&(e.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(e.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,n){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new ke(e,"popstate"===this.lastSource?this.store[this.restoredId]:null,n))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}}return t.\u0275fac=function(e){l.\u0275\u0275invalidFactory()},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac}),t})();var y=(()=>((y=y||{})[y.COMPLETE=0]="COMPLETE",y[y.FAILED=1]="FAILED",y[y.REDIRECTING=2]="REDIRECTING",y))();function _r(t,r){t.events.pipe((0,c.filter)(e=>e instanceof O||e instanceof te||e instanceof we||e instanceof re),(0,c.map)(e=>e instanceof O||e instanceof re?y.COMPLETE:e instanceof te&&(0===e.code||1===e.code)?y.REDIRECTING:y.FAILED),(0,c.filter)(e=>e!==y.REDIRECTING),(0,c.take)(1)).subscribe(()=>{r()})}const Q=!1;function Ii(t,...r){return(0,l.makeEnvironmentProviders)([{provide:N,multi:!0,useValue:t},Q?{provide:st,useValue:!0}:[],{provide:$,useFactory:Er,deps:[C]},{provide:l.APP_BOOTSTRAP_LISTENER,multi:!0,useFactory:Ir},r.map(e=>e.\u0275providers)])}function Er(t){return t.routerState.root}function M(t,r){return{\u0275kind:t,\u0275providers:r}}const st=new l.InjectionToken("",{providedIn:"root",factory:()=>!1}),Ai={provide:l.ENVIRONMENT_INITIALIZER,multi:!0,useFactory:()=>()=>{(0,l.inject)(st)||console.warn("`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. This is likely a mistake.")}};function Ti(t){return[{provide:N,multi:!0,useValue:t},Q?Ai:[]]}function bi(t={}){return M(4,[{provide:ot,useFactory:()=>{const e=(0,l.inject)(S.ViewportScroller),n=(0,l.inject)(l.NgZone),i=(0,l.inject)(Pe),o=(0,l.inject)(F);return new wr(o,i,e,n,t)}}])}function Ir(){const t=(0,l.inject)(l.Injector);return r=>{const e=t.get(l.ApplicationRef);if(r!==e.components[0])return;const n=t.get(C),i=t.get(Ar);1===t.get(at)&&n.initialNavigation(),t.get(Or,null,l.InjectFlags.Optional)?.setUpPreloading(),t.get(ot,null,l.InjectFlags.Optional)?.init(),n.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}const Ar=new l.InjectionToken(Q?"bootstrap done indicator":"",{factory:()=>new h.Subject}),at=new l.InjectionToken(Q?"initial navigation":"",{providedIn:"root",factory:()=>1});function Tr(){return M(2,[{provide:at,useValue:0},{provide:l.APP_INITIALIZER,multi:!0,deps:[l.Injector],useFactory:r=>{const e=r.get(S.LOCATION_INITIALIZED,Promise.resolve());return()=>e.then(()=>new Promise(n=>{const i=r.get(C),o=r.get(Ar);_r(i,()=>{n(!0)}),r.get(Pe).afterPreactivation=()=>(n(!0),o.closed?(0,h.of)(void 0):o),i.initialNavigation()}))}}])}function br(){return M(3,[{provide:l.APP_INITIALIZER,multi:!0,useFactory:()=>{const r=(0,l.inject)(C);return()=>{r.setUpLocationChangeListener()}}},{provide:at,useValue:2}])}function Ur(){let t=[];return t=Q?[{provide:l.ENVIRONMENT_INITIALIZER,multi:!0,useFactory:()=>{const r=(0,l.inject)(C);return()=>r.events.subscribe(e=>{console.group?.(`Router Event: ${e.constructor.name}`),console.log(function sn(t){if(!("type"in t))return`Unknown Router Event: ${t.constructor.name}`;switch(t.type){case 14:return`ActivationEnd(path: '${t.snapshot.routeConfig?.path||""}')`;case 13:return`ActivationStart(path: '${t.snapshot.routeConfig?.path||""}')`;case 12:return`ChildActivationEnd(path: '${t.snapshot.routeConfig?.path||""}')`;case 11:return`ChildActivationStart(path: '${t.snapshot.routeConfig?.path||""}')`;case 8:return`GuardsCheckEnd(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}', state: ${t.state}, shouldActivate: ${t.shouldActivate})`;case 7:return`GuardsCheckStart(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}', state: ${t.state})`;case 2:return`NavigationCancel(id: ${t.id}, url: '${t.url}')`;case 16:return`NavigationSkipped(id: ${t.id}, url: '${t.url}')`;case 1:return`NavigationEnd(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}')`;case 3:return`NavigationError(id: ${t.id}, url: '${t.url}', error: ${t.error})`;case 0:return`NavigationStart(id: ${t.id}, url: '${t.url}')`;case 6:return`ResolveEnd(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}', state: ${t.state})`;case 5:return`ResolveStart(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}', state: ${t.state})`;case 10:return`RouteConfigLoadEnd(path: ${t.route.path})`;case 9:return`RouteConfigLoadStart(path: ${t.route.path})`;case 4:return`RoutesRecognized(id: ${t.id}, url: '${t.url}', urlAfterRedirects: '${t.urlAfterRedirects}', state: ${t.state})`;case 15:return`Scroll(anchor: '${t.anchor}', position: '${t.position?`${t.position[0]}, ${t.position[1]}`:null}')`}}(e)),console.log(e),console.groupEnd?.()})}}]:[],M(1,t)}const Or=new l.InjectionToken(Q?"router preloader":"");function lt(t){return M(0,[{provide:Or,useExisting:Sr},{provide:it,useExisting:t}])}function Ui(t){return M(5,[{provide:ue,useValue:t}])}function Oi(){return M(5,[{provide:S.LocationStrategy,useClass:S.HashLocationStrategy}])}function Mi(t){return M(7,[{provide:l.ENVIRONMENT_INITIALIZER,multi:!0,useValue:()=>{const e=(0,l.inject)(l.EnvironmentInjector);(0,l.inject)(C).events.subscribe(n=>{n instanceof we&&e.runInContext(()=>t(n))})}}])}const de=!1,Mr=new l.InjectionToken(de?"router duplicate forRoot guard":"ROUTER_FORROOT_GUARD"),Pr=[S.Location,{provide:F,useClass:ge},C,H,{provide:$,useFactory:Er,deps:[C]},tt,de?{provide:st,useValue:!0}:[]];function Pi(){return new l.NgProbeToken("Router",C)}let Di=(()=>{class t{constructor(e){}static forRoot(e,n){return{ngModule:t,providers:[Pr,de&&n?.enableTracing?Ur().\u0275providers:[],{provide:N,multi:!0,useValue:e},{provide:Mr,useFactory:$i,deps:[[C,new l.Optional,new l.SkipSelf]]},{provide:ue,useValue:n||{}},n?.useHash?{provide:S.LocationStrategy,useClass:S.HashLocationStrategy}:{provide:S.LocationStrategy,useClass:S.PathLocationStrategy},{provide:ot,useFactory:()=>{const t=(0,l.inject)(S.ViewportScroller),r=(0,l.inject)(l.NgZone),e=(0,l.inject)(ue),n=(0,l.inject)(Pe),i=(0,l.inject)(F);return e.scrollOffset&&t.setOffset(e.scrollOffset),new wr(i,n,t,r,e)}},n?.preloadingStrategy?lt(n.preloadingStrategy).\u0275providers:[],{provide:l.NgProbeToken,multi:!0,useFactory:Pi},n?.initialNavigation?Ni(n):[],[{provide:ct,useFactory:Ir},{provide:l.APP_BOOTSTRAP_LISTENER,multi:!0,useExisting:ct}]]}}static forChild(e){return{ngModule:t,providers:[{provide:N,multi:!0,useValue:e}]}}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(Mr,8))},t.\u0275mod=l.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=l.\u0275\u0275defineInjector({imports:[Ee]}),t})();function $i(t){if(de&&t)throw new l.\u0275RuntimeError(4007,"The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector. Lazy loaded modules should use RouterModule.forChild() instead.");return"guarded"}function Ni(t){return["disabled"===t.initialNavigation?br().\u0275providers:[],"enabledBlocking"===t.initialNavigation?Tr().\u0275providers:[]]}const ct=new l.InjectionToken(de?"Router Initializer":""),zi=new l.Version("15.2.10")}}]);