(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7420],{97420:(f,d,n)=>{n.r(d),n.d(d,{MboTransferGenericModule:()=>h});var a=n(17007),l=n(78007),M=n(18632),t=n(99877);const e=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(9696).then(n.bind(n,79696)).then(o=>o.MboTransferGenericSourcePageModule)},{path:"destination",loadChildren:()=>n.e(3242).then(n.bind(n,43242)).then(o=>o.MboTransferGenericDestinationPageModule)},{path:"unregistered",loadChildren:()=>n.e(6797).then(n.bind(n,46797)).then(o=>o.MboTransferGenericUnregisteredPageModule),canActivate:[M.a]},{path:"amount",loadChildren:()=>n.e(4006).then(n.bind(n,94006)).then(o=>o.MboTransferGenericAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(9077).then(n.bind(n,39077)).then(o=>o.MboTransferGenericConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(9221).then(n.bind(n,49221)).then(o=>o.MboTransferGenericResultPageModule)}];let h=(()=>{class o{}return o.\u0275fac=function(r){return new(r||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[a.CommonModule,l.RouterModule.forChild(e)]}),o})()}}]);