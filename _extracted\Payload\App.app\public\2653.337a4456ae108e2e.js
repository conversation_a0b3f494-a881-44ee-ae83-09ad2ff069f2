(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2653,3159],{73159:(Ht,Ce,de)=>{de.r(Ce),de.d(Ce,{ClientCacheState:()=>_,DataGovernance:()=>ge,FormattableLogMessage:()=>d,LogLevel:()=>g,OverrideBehaviour:()=>W,PollingMode:()=>Q,PrerequisiteFlagComparator:()=>Z,RefreshResult:()=>re,SegmentComparator:()=>J,SettingKeyValue:()=>pe,SettingType:()=>B,User:()=>ir,UserComparator:()=>f,createConsoleLogger:()=>dr,createFlagOverridesFromMap:()=>yr,default:()=>Tr,disposeAllClients:()=>pr,getClient:()=>Vt});var m=de(97582),H=(()=>{return(t=H||(H={}))[t.Fetched=0]="Fetched",t[t.NotModified=1]="NotModified",t[t.Errored=2]="Errored",H;var t})(),V=function(){function t(e,n,r,i){this.status=e,this.config=n,this.errorMessage=r,this.errorException=i}return t.success=function(e){return new t(H.Fetched,e)},t.notModified=function(e){return new t(H.NotModified,e)},t.error=function(e,n,r){return new t(H.Errored,e,n??"Unknown error.",r)},t}(),se=function(t){function e(n){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];var o=t.call(this,function(s,a){switch(s){case"abort":return"Request was aborted.";case"timeout":return"Request timed out. Timeout value: "+a[0]+"ms";case"failure":var l=a[0],y="Request failed due to a network or protocol error.";return l?y+" "+(l instanceof Error?l.message:l+""):y}}(n,r))||this;return o.cause=n,o instanceof e||(Object.setPrototypeOf||function(s,a){return s.__proto__=a})(o,e.prototype),o.args=r,o}return(0,m.ZT)(e,t),e}(Error),Y=(()=>{return(t=Y||(Y={}))[t.No=0]="No",t[t.Should=1]="Should",t[t.Force=2]="Force",Y;var t})(),B=(()=>{return(t=B||(B={}))[t.Boolean=0]="Boolean",t[t.String=1]="String",t[t.Int=2]="Int",t[t.Double=3]="Double",B;var t})(),f=(()=>{return(t=f||(f={}))[t.TextIsOneOf=0]="TextIsOneOf",t[t.TextIsNotOneOf=1]="TextIsNotOneOf",t[t.TextContainsAnyOf=2]="TextContainsAnyOf",t[t.TextNotContainsAnyOf=3]="TextNotContainsAnyOf",t[t.SemVerIsOneOf=4]="SemVerIsOneOf",t[t.SemVerIsNotOneOf=5]="SemVerIsNotOneOf",t[t.SemVerLess=6]="SemVerLess",t[t.SemVerLessOrEquals=7]="SemVerLessOrEquals",t[t.SemVerGreater=8]="SemVerGreater",t[t.SemVerGreaterOrEquals=9]="SemVerGreaterOrEquals",t[t.NumberEquals=10]="NumberEquals",t[t.NumberNotEquals=11]="NumberNotEquals",t[t.NumberLess=12]="NumberLess",t[t.NumberLessOrEquals=13]="NumberLessOrEquals",t[t.NumberGreater=14]="NumberGreater",t[t.NumberGreaterOrEquals=15]="NumberGreaterOrEquals",t[t.SensitiveTextIsOneOf=16]="SensitiveTextIsOneOf",t[t.SensitiveTextIsNotOneOf=17]="SensitiveTextIsNotOneOf",t[t.DateTimeBefore=18]="DateTimeBefore",t[t.DateTimeAfter=19]="DateTimeAfter",t[t.SensitiveTextEquals=20]="SensitiveTextEquals",t[t.SensitiveTextNotEquals=21]="SensitiveTextNotEquals",t[t.SensitiveTextStartsWithAnyOf=22]="SensitiveTextStartsWithAnyOf",t[t.SensitiveTextNotStartsWithAnyOf=23]="SensitiveTextNotStartsWithAnyOf",t[t.SensitiveTextEndsWithAnyOf=24]="SensitiveTextEndsWithAnyOf",t[t.SensitiveTextNotEndsWithAnyOf=25]="SensitiveTextNotEndsWithAnyOf",t[t.SensitiveArrayContainsAnyOf=26]="SensitiveArrayContainsAnyOf",t[t.SensitiveArrayNotContainsAnyOf=27]="SensitiveArrayNotContainsAnyOf",t[t.TextEquals=28]="TextEquals",t[t.TextNotEquals=29]="TextNotEquals",t[t.TextStartsWithAnyOf=30]="TextStartsWithAnyOf",t[t.TextNotStartsWithAnyOf=31]="TextNotStartsWithAnyOf",t[t.TextEndsWithAnyOf=32]="TextEndsWithAnyOf",t[t.TextNotEndsWithAnyOf=33]="TextNotEndsWithAnyOf",t[t.ArrayContainsAnyOf=34]="ArrayContainsAnyOf",t[t.ArrayNotContainsAnyOf=35]="ArrayNotContainsAnyOf",f;var t})(),Z=(()=>{return(t=Z||(Z={}))[t.Equals=0]="Equals",t[t.NotEquals=1]="NotEquals",Z;var t})(),J=(()=>{return(t=J||(J={}))[t.IsIn=0]="IsIn",t[t.IsNotIn=1]="IsNotIn",J;var t})(),K=function(){function t(e,n,r,i){this.configJson=e,this.config=n,this.timestamp=r,this.httpETag=i}return t.equals=function(e,n){return e.httpETag&&n.httpETag?e.httpETag===n.httpETag:e.configJson===n.configJson},t.prototype.with=function(e){return new t(this.configJson,this.config,e,this.httpETag)},Object.defineProperty(t.prototype,"isEmpty",{get:function(){return!this.config},enumerable:!1,configurable:!0}),t.prototype.isExpired=function(e){return this===t.empty||this.timestamp+e<t.generateTimestamp()},t.generateTimestamp=function(){return(new Date).getTime()},t.serialize=function(e){var n,r;return e.timestamp+"\n"+(null!==(n=e.httpETag)&&void 0!==n?n:"")+"\n"+(null!==(r=e.configJson)&&void 0!==r?r:"")},t.deserialize=function(e){for(var n=Array(2),r=0,i=0;i<n.length;i++){if((r=e.indexOf("\n",r))<0)throw new Error("Number of values is fewer than expected.");n[i]=r++}var o=n[0],s=e.substring(0,o),a=parseInt(s);if(isNaN(a))throw new Error("Invalid fetch time: "+s);var l,y,u=(s=e.substring(r=o+1,o=n[1])).length>0?s:void 0;return(s=e.substring(r=o+1)).length>0&&(l=ce.deserialize(s),y=s),new t(y,l,a,u)},t.serializationFormatVersion="v2",t.empty=new t(void 0,void 0,0,void 0),t}(),ce=function(){function t(e){var r,i,n=this;this.preferences=null!=e.p?new Ne(e.p):void 0,this.segments=null!==(i=null===(r=e.s)||void 0===r?void 0:r.map(function(o){return new Le(o)}))&&void 0!==i?i:[],this.settings=null!=e.f?Object.fromEntries(Object.entries(e.f).map(function(o){return[o[0],new Re(o[1],n)]})):{}}return t.deserialize=function(e){var n=JSON.parse(e);if("object"!=typeof n||!n)throw new Error("Invalid config JSON content:"+e);return new t(n)},Object.defineProperty(t.prototype,"salt",{get:function(){var e;return null===(e=this.preferences)||void 0===e?void 0:e.salt},enumerable:!1,configurable:!0}),t}(),Ne=function t(e){this.baseUrl=e.u,this.redirectMode=e.r,this.salt=e.s},Le=function t(e){var n,r;this.name=e.n,this.conditions=null!==(r=null===(n=e.r)||void 0===n?void 0:n.map(function(i){return new Fe(i)}))&&void 0!==r?r:[]},ae=function t(e,n){void 0===n&&(n=!1),this.value=n?e.v:$(e.v),this.variationId=e.i},Re=function(t){function e(n,r){var i,o,s,a,u,l,y=t.call(this,n,n.t<0)||this;return y.type=n.t,y.percentageOptionsAttribute=null!==(i=n.a)&&void 0!==i?i:"Identifier",y.targetingRules=null!==(s=null===(o=n.r)||void 0===o?void 0:o.map(function(O){return new ye(O,r)}))&&void 0!==s?s:[],y.percentageOptions=null!==(u=null===(a=n.p)||void 0===a?void 0:a.map(function(O){return new me(O)}))&&void 0!==u?u:[],y.configJsonSalt=null!==(l=r?.salt)&&void 0!==l?l:"",y}return(0,m.ZT)(e,t),e.fromValue=function(n){return new e({t:-1,v:n})},e}(ae),ye=function t(e,n){var r,i;this.conditions=null!==(i=null===(r=e.c)||void 0===r?void 0:r.map(function(o){return null!=o.u?new Fe(o.u):null!=o.p?new De(o.p):null!=o.s?new Pe(o.s,n):void 0}))&&void 0!==i?i:[],this.then=null!=e.p?e.p.map(function(o){return new me(o)}):new ae(e.s)},me=function(t){function e(n){var r=t.call(this,n)||this;return r.percentage=n.p,r}return(0,m.ZT)(e,t),e}(ae),Fe=function t(e){var n,r;this.type="UserCondition",this.comparisonAttribute=e.a,this.comparator=e.c,this.comparisonValue=null!==(r=null!==(n=e.s)&&void 0!==n?n:e.d)&&void 0!==r?r:e.l},De=function t(e){this.type="PrerequisiteFlagCondition",this.prerequisiteFlagKey=e.f,this.comparator=e.c,this.comparisonValue=$(e.v)},Pe=function t(e,n){this.type="SegmentCondition",this.segment=n.segments[e.s],this.comparator=e.c};function $(t){var e,n,r;return null!==(r=null!==(n=null!==(e=t.b)&&void 0!==e?e:t.s)&&void 0!==n?n:t.i)&&void 0!==r?r:t.d}var re=function(){function t(e,n){this.errorMessage=e,this.errorException=n}return Object.defineProperty(t.prototype,"isSuccess",{get:function(){return null===this.errorMessage},enumerable:!1,configurable:!0}),t.from=function(e){return e.status!==H.Errored?t.success():t.failure(e.errorMessage,e.errorException)},t.success=function(){return new t(null)},t.failure=function(e,n){return new t(e,n)},t}(),_=(()=>{return(t=_||(_={}))[t.NoFlagData=0]="NoFlagData",t[t.HasLocalOverrideFlagDataOnly=1]="HasLocalOverrideFlagDataOnly",t[t.HasCachedFlagDataOnly=2]="HasCachedFlagDataOnly",t[t.HasUpToDateFlagData=3]="HasUpToDateFlagData",_;var t})(),x=(()=>{return(t=x||(x={}))[t.Online=0]="Online",t[t.Offline=1]="Offline",t[t.Disposed=2]="Disposed",x;var t})(),fe=function(){function t(e,n){this.configFetcher=e,this.options=n,this.pendingFetch=null,this.cacheKey=n.getCacheKey(),this.configFetcher=e,this.options=n,this.status=n.offline?x.Offline:x.Online}return t.prototype.dispose=function(){this.status=x.Disposed},Object.defineProperty(t.prototype,"disposed",{get:function(){return this.status===x.Disposed},enumerable:!1,configurable:!0}),t.prototype.refreshConfigAsync=function(){return(0,m.mG)(this,void 0,void 0,function(){var e,n,i,o;return(0,m.Jh)(this,function(s){switch(s.label){case 0:return[4,this.options.cache.get(this.cacheKey)];case 1:return e=s.sent(),this.isOffline?[3,3]:[4,this.refreshConfigCoreAsync(e)];case 2:return n=s.sent(),i=n[1],[2,[re.from(n[0]),i]];case 3:return o=this.options.logger.configServiceCannotInitiateHttpCalls().toString(),[2,[re.failure(o),e]]}})})},t.prototype.refreshConfigCoreAsync=function(e){return(0,m.mG)(this,void 0,void 0,function(){var n,r,i;return(0,m.Jh)(this,function(o){switch(o.label){case 0:return[4,this.fetchAsync(e)];case 1:return n=o.sent(),r=!1,(i=n.status===H.Fetched)||n.config.timestamp>e.timestamp&&(!n.config.isEmpty||e.isEmpty)?[4,this.options.cache.set(this.cacheKey,n.config)]:[3,3];case 2:o.sent(),r=i&&!K.equals(n.config,e),e=n.config,o.label=3;case 3:return this.onConfigFetched(n.config),r&&this.onConfigChanged(n.config),[2,[n,e]]}})})},t.prototype.onConfigFetched=function(e){},t.prototype.onConfigChanged=function(e){var n;this.options.logger.debug("config changed"),this.options.hooks.emit("configChanged",null!==(n=e.config)&&void 0!==n?n:new ce({}))},t.prototype.fetchAsync=function(e){var r;return null!==(r=this.pendingFetch)&&void 0!==r?r:this.pendingFetch=(0,m.mG)(this,void 0,void 0,function(){return(0,m.Jh)(this,function(i){switch(i.label){case 0:return i.trys.push([0,,2,3]),[4,this.fetchLogicAsync(e)];case 1:return[2,i.sent()];case 2:return this.pendingFetch=null,[7];case 3:return[2]}})})},t.prototype.fetchLogicAsync=function(e){var n;return(0,m.mG)(this,void 0,void 0,function(){var r,i,o,s,a,u;return(0,m.Jh)(this,function(l){switch(l.label){case 0:(r=this.options).logger.debug("ConfigServiceBase.fetchLogicAsync() - called."),l.label=1;case 1:return l.trys.push([1,3,,4]),[4,this.fetchRequestAsync(null!==(n=e.httpETag)&&void 0!==n?n:null)];case 2:switch(o=l.sent(),a=o[1],(s=o[0]).statusCode){case 200:return a instanceof ce?(r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was successful. Returning new config."),[2,V.success(new K(s.body,a,K.generateTimestamp(),s.eTag))]):(i=r.logger.fetchReceived200WithInvalidBody(a).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): "+s.statusCode+" "+s.reasonPhrase+" was received but the HTTP response content was invalid. Returning null."),[2,V.error(e,i,a)]);case 304:return e?(r.logger.debug("ConfigServiceBase.fetchLogicAsync(): content was not modified. Returning last config with updated timestamp."),[2,V.notModified(e.with(K.generateTimestamp()))]):(i=r.logger.fetchReceived304WhenLocalCacheIsEmpty(s.statusCode,s.reasonPhrase).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): "+s.statusCode+" "+s.reasonPhrase+" was received when no config is cached locally. Returning null."),[2,V.error(e,i)]);case 403:case 404:return i=r.logger.fetchFailedDueToInvalidSdkKey().toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning last config (if any) with updated timestamp."),[2,V.error(e.with(K.generateTimestamp()),i)];default:return i=r.logger.fetchFailedDueToUnexpectedHttpResponse(s.statusCode,s.reasonPhrase).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning null."),[2,V.error(e,i)]}return[3,4];case 3:return u=l.sent(),i=(u instanceof se&&"timeout"===u.cause?r.logger.fetchFailedDueToRequestTimeout(u.args[0],u):r.logger.fetchFailedDueToUnexpectedError(u)).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning null."),[2,V.error(e,i,u)];case 4:return[2]}})})},t.prototype.fetchRequestAsync=function(e,n){return void 0===n&&(n=2),(0,m.mG)(this,void 0,void 0,function(){var r,i,o,s,a,u,l;return(0,m.Jh)(this,function(y){switch(y.label){case 0:(r=this.options).logger.debug("ConfigServiceBase.fetchRequestAsync() - called."),i=0,y.label=1;case 1:return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): calling fetchLogic()"+(i>0?", retry "+i+"/"+n:"")),[4,this.configFetcher.fetchLogic(r,e)];case 2:if(200!==(o=y.sent()).statusCode)return[2,[o]];if(!o.body)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): no response body."),[2,[o,new Error("No response body.")]];s=void 0;try{s=ce.deserialize(o.body)}catch(E){return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): invalid response body."),[2,[o,E]]}if(!(a=s.preferences))return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): preferences is empty."),[2,[o,s]];if(!(u=a.baseUrl)||u===r.baseUrl)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): baseUrl OK."),[2,[o,s]];if(l=a.redirectMode,r.baseUrlOverriden&&l!==Y.Force)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): options.baseUrlOverriden && redirect !== 2."),[2,[o,s]];if(r.baseUrl=u,l===Y.No)return[2,[o,s]];if(l===Y.Should&&r.logger.dataGovernanceIsOutOfSync(),i>=n)return r.logger.fetchFailedDueToRedirectLoop(),[2,[o,s]];y.label=3;case 3:return i++,[3,1];case 4:return[2]}})})},Object.defineProperty(t.prototype,"isOfflineExactly",{get:function(){return this.status===x.Offline},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isOffline",{get:function(){return this.status!==x.Online},enumerable:!1,configurable:!0}),t.prototype.setOnlineCore=function(){},t.prototype.setOnline=function(){this.status===x.Offline?(this.setOnlineCore(),this.status=x.Online,this.options.logger.configServiceStatusChanged(x[this.status])):this.disposed&&this.options.logger.configServiceMethodHasNoEffectDueToDisposedClient("setOnline")},t.prototype.setOfflineCore=function(){},t.prototype.setOffline=function(){this.status===x.Online?(this.setOfflineCore(),this.status=x.Offline,this.options.logger.configServiceStatusChanged(x[this.status])):this.disposed&&this.options.logger.configServiceMethodHasNoEffectDueToDisposedClient("setOffline")},t.prototype.syncUpWithCache=function(){return this.options.cache.get(this.cacheKey)},t.prototype.getReadyPromise=function(e,n){return(0,m.mG)(this,void 0,void 0,function(){var r;return(0,m.Jh)(this,function(i){switch(i.label){case 0:return[4,n(e)];case 1:return r=i.sent(),this.options.hooks.emit("clientReady",r),[2,r]}})})},t}(),Ee=function(){function t(){this.callbacks=[]}return Object.defineProperty(t.prototype,"aborted",{get:function(){return!this.callbacks},enumerable:!1,configurable:!0}),t.prototype.abort=function(){if(!this.aborted){var e=this.callbacks;this.callbacks=null;for(var n=0,r=e;n<r.length;n++)(0,r[n])()}},t.prototype.registerCallback=function(e){var n=this;return this.aborted?(e(),function(){}):(this.callbacks.push(e),function(){var i,r=n.callbacks;r&&(i=r.indexOf(e))>=0&&r.splice(i,1)})},t}();function ke(t,e){var n;return new Promise(function(r){var i=e?.registerCallback(function(){clearTimeout(n),r(!1)});n=setTimeout(function(){i?.(),r(!0)},t)})}function X(t,e){return void 0===e&&(e=!1),t instanceof Error?e&&t.stack?t.stack:t.toString():t+""}function xe(t){throw t}function he(t){return Array.isArray(t)}function Oe(t){return he(t)&&!t.some(function(e){return"string"!=typeof e})}function be(t,e,n,r){void 0===e&&(e=0),void 0===r&&(r=", ");var i=t.length;if(!i)return"";var o="";return e>0&&i>e&&(t=t.slice(0,e),n&&(o=n(i-e))),"'"+t.join("'"+r+"'")+"'"+o}function ue(t){function e(a,u){var l=a.charCodeAt(u);if(55296<=l&&l<56320){var y=a.charCodeAt(u+1);if(56320<=y&&y<=57343)return(l<<10)+y-56613888}return l}var o,n="",r=0,i=String.fromCharCode;for(o=0;o<t.length;o++){var s=e(t,o);s<=127||(n+=t.slice(r,o),s<=2047?(n+=i(192|s>>6),n+=i(128|63&s)):s<=65535?(n+=i(224|s>>12),n+=i(128|s>>6&63),n+=i(128|63&s)):(n+=i(240|s>>18),n+=i(128|s>>12&63),n+=i(128|s>>6&63),n+=i(128|63&s),++o),r=o+1)}return n+t.slice(r,o)}function ze(t){return"number"==typeof t?t:"string"!=typeof t||!t.length||/^\s*$|^\s*0[^\d.e]/.test(t)?NaN:+t}var M,T,F,D,ne,N,U,j,G,z,at,ut,lt,ct,ft,ht,gt,vt,pt,dt,c=500,h=function(t){function e(n,r){var i=t.call(this,n,r)||this;i.signalInitialization=function(){},i.stopToken=new Ee,i.pollIntervalMs=1e3*r.pollIntervalSeconds,i.pollExpirationMs=i.pollIntervalMs-c;var o=i.syncUpWithCache();if(0!==r.maxInitWaitTimeSeconds){i.initialized=!1;var s=new Promise(function(a){return i.signalInitialization=a});i.initializationPromise=i.waitForInitializationAsync(s).then(function(a){return i.initialized=!0,a})}else i.initialized=!0,i.initializationPromise=Promise.resolve(!1);return i.readyPromise=i.getReadyPromise(i.initializationPromise,function(a){return(0,m.mG)(i,void 0,void 0,function(){return(0,m.Jh)(this,function(u){switch(u.label){case 0:return[4,a];case 1:return u.sent(),[2,this.getCacheState(this.options.cache.getInMemory())]}})})}),r.offline||i.startRefreshWorker(o,i.stopToken),i}return(0,m.ZT)(e,t),e.prototype.waitForInitializationAsync=function(n){return(0,m.mG)(this,void 0,void 0,function(){var r,i;return(0,m.Jh)(this,function(o){switch(o.label){case 0:return this.options.maxInitWaitTimeSeconds<0?[4,n]:[3,2];case 1:return o.sent(),[2,!0];case 2:return r=new Ee,[4,Promise.race([n.then(function(){return!0}),ke(1e3*this.options.maxInitWaitTimeSeconds,r).then(function(){return!1})])];case 3:return i=o.sent(),r.abort(),[2,i]}})})},e.prototype.getConfig=function(){return(0,m.mG)(this,void 0,void 0,function(){function n(i){i.debug("AutoPollConfigService.getConfig() - returning value from cache.")}var r;return(0,m.Jh)(this,function(i){switch(i.label){case 0:return this.options.logger.debug("AutoPollConfigService.getConfig() called."),this.isOffline||this.initialized?[3,3]:[4,this.options.cache.get(this.cacheKey)];case 1:return(r=i.sent()).isExpired(this.pollIntervalMs)?(this.options.logger.debug("AutoPollConfigService.getConfig() - cache is empty or expired, waiting for initialization."),[4,this.initializationPromise]):(n(this.options.logger),[2,r]);case 2:i.sent(),i.label=3;case 3:return[4,this.options.cache.get(this.cacheKey)];case 4:return(r=i.sent()).isExpired(this.pollIntervalMs)?this.options.logger.debug("AutoPollConfigService.getConfig() - cache is empty or expired."):n(this.options.logger),[2,r]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("AutoPollConfigService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e.prototype.dispose=function(){this.options.logger.debug("AutoPollConfigService.dispose() called."),t.prototype.dispose.call(this),this.stopToken.aborted||this.stopRefreshWorker()},e.prototype.onConfigFetched=function(n){t.prototype.onConfigFetched.call(this,n),this.signalInitialization()},e.prototype.setOnlineCore=function(){this.startRefreshWorker(null,this.stopToken)},e.prototype.setOfflineCore=function(){this.stopRefreshWorker(),this.stopToken=new Ee},e.prototype.startRefreshWorker=function(n,r){return(0,m.mG)(this,void 0,void 0,function(){var i,o,s,a,u;return(0,m.Jh)(this,function(l){switch(l.label){case 0:this.options.logger.debug("AutoPollConfigService.startRefreshWorker() called."),i=!0,l.label=1;case 1:if(r.aborted)return[3,11];l.label=2;case 2:l.trys.push([2,9,,10]),o=(new Date).getTime()+this.pollIntervalMs,l.label=3;case 3:return l.trys.push([3,5,,6]),[4,this.refreshWorkerLogic(i,n)];case 4:return l.sent(),[3,6];case 5:return s=l.sent(),this.options.logger.autoPollConfigServiceErrorDuringPolling(s),[3,6];case 6:return(a=o-(new Date).getTime())>0?[4,ke(a,r)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:return u=l.sent(),this.options.logger.autoPollConfigServiceErrorDuringPolling(u),[3,10];case 10:return i=!1,n=null,[3,1];case 11:return[2]}})})},e.prototype.stopRefreshWorker=function(){this.options.logger.debug("AutoPollConfigService.stopRefreshWorker() called."),this.stopToken.abort()},e.prototype.refreshWorkerLogic=function(n,r){return(0,m.mG)(this,void 0,void 0,function(){var i;return(0,m.Jh)(this,function(o){switch(o.label){case 0:return this.options.logger.debug("AutoPollConfigService.refreshWorkerLogic() - called."),[4,r??this.options.cache.get(this.cacheKey)];case 1:return(i=o.sent()).isExpired(this.pollExpirationMs)?(n?this.isOfflineExactly:this.isOffline)?[3,3]:[4,this.refreshConfigCoreAsync(i)]:[3,4];case 2:o.sent(),o.label=3;case 3:return[3,5];case 4:n&&this.signalInitialization(),o.label=5;case 5:return[2]}})})},e.prototype.getCacheState=function(n){return n.isEmpty?_.NoFlagData:n.isExpired(this.pollIntervalMs)?_.HasCachedFlagDataOnly:_.HasUpToDateFlagData},e}(fe),v=function(){function t(){this.cachedConfig=K.empty}return t.prototype.set=function(e,n){this.cachedConfig=n},t.prototype.get=function(e){return this.cachedConfig},t.prototype.getInMemory=function(){return this.cachedConfig},t}(),p=function(){function t(e,n){this.cache=e,this.logger=n,this.cachedConfig=K.empty}return t.prototype.set=function(e,n){return(0,m.mG)(this,void 0,void 0,function(){var r;return(0,m.Jh)(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),n.isEmpty?(this.cachedSerializedConfig=void 0,this.cachedConfig=n,[2]):(this.cachedSerializedConfig=K.serialize(n),this.cachedConfig=n,[4,this.cache.set(e,this.cachedSerializedConfig)]);case 1:return i.sent(),[3,3];case 2:return r=i.sent(),this.logger.configServiceCacheWriteError(r),[3,3];case 3:return[2]}})})},t.prototype.updateCachedConfig=function(e){null==e||e===this.cachedSerializedConfig||(this.cachedConfig=K.deserialize(e),this.cachedSerializedConfig=e)},t.prototype.get=function(e){var i;try{var r=this.cache.get(e);if(function Ke(t){var e;return"function"==typeof(null===(e=t)||void 0===e?void 0:e.then)}(r))return i=r,(0,m.mG)(this,void 0,void 0,function(){var o,s;return(0,m.Jh)(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),o=this.updateCachedConfig,[4,i];case 1:return o.apply(this,[a.sent()]),[3,3];case 2:return s=a.sent(),this.logger.configServiceCacheReadError(s),[3,3];case 3:return[2,this.cachedConfig]}})});this.updateCachedConfig(r)}catch(i){this.logger.configServiceCacheReadError(i)}return Promise.resolve(this.cachedConfig)},t.prototype.getInMemory=function(){return this.cachedConfig},t}(),g=(()=>{return(t=g||(g={}))[t.Debug=4]="Debug",t[t.Info=3]="Info",t[t.Warn=2]="Warn",t[t.Error=1]="Error",t[t.Off=-1]="Off",g;var t})(),d=function(){function t(e,n,r){this.strings=e,this.argNames=n,this.argValues=r}return t.from=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(r){for(var i=[],o=1;o<arguments.length;o++)i[o-1]=arguments[o];return new t(r,e,i)}},Object.defineProperty(t.prototype,"defaultFormattedMessage",{get:function(){var e=this.cachedDefaultFormattedMessage;if(void 0===e){e="";for(var r=this.strings,i=this.argValues,o=0;o<r.length-1;o++)e+=r[o],e+=i[o];this.cachedDefaultFormattedMessage=e+=r[o]}return e},enumerable:!1,configurable:!0}),t.prototype.toString=function(){return this.defaultFormattedMessage},t}(),S=function(){function t(e,n){this.logger=e,this.hooks=n}return Object.defineProperty(t.prototype,"level",{get:function(){var e;return null!==(e=this.logger.level)&&void 0!==e?e:g.Warn},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"eol",{get:function(){var e;return null!==(e=this.logger.eol)&&void 0!==e?e:"\n"},enumerable:!1,configurable:!0}),t.prototype.isEnabled=function(e){return this.level>=e},t.prototype.log=function(e,n,r,i){var o;return this.isEnabled(e)&&this.logger.log(e,n,r,i),e===g.Error&&(null===(o=this.hooks)||void 0===o||o.emit("clientError",r.toString(),i)),r},t.prototype.debug=function(e){this.log(g.Debug,0,e)},t.prototype.configJsonIsNotPresent=function(e){return this.log(g.Error,1e3,d.from("DEFAULT_RETURN_VALUE")(M||(M=(0,m.cy)(["Config JSON is not present. Returning ","."],["Config JSON is not present. Returning ","."])),e))},t.prototype.configJsonIsNotPresentSingle=function(e,n,r){return this.log(g.Error,1e3,d.from("KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE")(T||(T=(0,m.cy)(["Config JSON is not present when evaluating setting '","'. Returning the `","` parameter that you specified in your application: '","'."],["Config JSON is not present when evaluating setting '","'. Returning the \\`","\\` parameter that you specified in your application: '","'."])),e,n,r))},t.prototype.settingEvaluationFailedDueToMissingKey=function(e,n,r,i){return this.log(g.Error,1001,d.from("KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE","AVAILABLE_KEYS")(F||(F=(0,m.cy)(["Failed to evaluate setting '","' (the key was not found in config JSON). Returning the `","` parameter that you specified in your application: '","'. Available keys: [","]."],["Failed to evaluate setting '","' (the key was not found in config JSON). Returning the \\`","\\` parameter that you specified in your application: '","'. Available keys: [","]."])),e,n,r,i))},t.prototype.settingEvaluationError=function(e,n,r){return this.log(g.Error,1002,d.from("METHOD_NAME","DEFAULT_RETURN_VALUE")(D||(D=(0,m.cy)(["Error occurred in the `","` method. Returning ","."],["Error occurred in the \\`","\\` method. Returning ","."])),e,n),r)},t.prototype.settingEvaluationErrorSingle=function(e,n,r,i,o){return this.log(g.Error,1002,d.from("METHOD_NAME","KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE")(ne||(ne=(0,m.cy)(["Error occurred in the `","` method while evaluating setting '","'. Returning the `","` parameter that you specified in your application: '","'."],["Error occurred in the \\`","\\` method while evaluating setting '","'. Returning the \\`","\\` parameter that you specified in your application: '","'."])),e,n,r,i),o)},t.prototype.forceRefreshError=function(e,n){return this.log(g.Error,1003,d.from("METHOD_NAME")(N||(N=(0,m.cy)(["Error occurred in the `","` method."],["Error occurred in the \\`","\\` method."])),e),n)},t.prototype.fetchFailedDueToInvalidSdkKey=function(){return this.log(g.Error,1100,"Your SDK Key seems to be wrong. You can find the valid SDK Key at https://app.configcat.com/sdkkey")},t.prototype.fetchFailedDueToUnexpectedHttpResponse=function(e,n){return this.log(g.Error,1101,d.from("STATUS_CODE","REASON_PHRASE")(U||(U=(0,m.cy)(["Unexpected HTTP response was received while trying to fetch config JSON: "," ",""],["Unexpected HTTP response was received while trying to fetch config JSON: "," ",""])),e,n))},t.prototype.fetchFailedDueToRequestTimeout=function(e,n){return this.log(g.Error,1102,d.from("TIMEOUT")(j||(j=(0,m.cy)(["Request timed out while trying to fetch config JSON. Timeout value: ","ms"],["Request timed out while trying to fetch config JSON. Timeout value: ","ms"])),e),n)},t.prototype.fetchFailedDueToUnexpectedError=function(e){return this.log(g.Error,1103,"Unexpected error occurred while trying to fetch config JSON. It is most likely due to a local network issue. Please make sure your application can reach the ConfigCat CDN servers (or your proxy server) over HTTP.",e)},t.prototype.fetchFailedDueToRedirectLoop=function(){return this.log(g.Error,1104,"Redirection loop encountered while trying to fetch config JSON. Please contact us at https://configcat.com/support/")},t.prototype.fetchReceived200WithInvalidBody=function(e){return this.log(g.Error,1105,"Fetching config JSON was successful but the HTTP response content was invalid.",e)},t.prototype.fetchReceived304WhenLocalCacheIsEmpty=function(e,n){return this.log(g.Error,1106,d.from("STATUS_CODE","REASON_PHRASE")(G||(G=(0,m.cy)(["Unexpected HTTP response was received when no config JSON is cached locally: "," ",""],["Unexpected HTTP response was received when no config JSON is cached locally: "," ",""])),e,n))},t.prototype.autoPollConfigServiceErrorDuringPolling=function(e){return this.log(g.Error,1200,"Error occurred during auto polling.",e)},t.prototype.settingForVariationIdIsNotPresent=function(e){return this.log(g.Error,2011,d.from("VARIATION_ID")(z||(z=(0,m.cy)(["Could not find the setting for the specified variation ID: '","'."],["Could not find the setting for the specified variation ID: '","'."])),e))},t.prototype.configServiceCacheReadError=function(e){return this.log(g.Error,2200,"Error occurred while reading the cache.",e)},t.prototype.configServiceCacheWriteError=function(e){return this.log(g.Error,2201,"Error occurred while writing the cache.",e)},t.prototype.clientIsAlreadyCreated=function(e){return this.log(g.Warn,3e3,d.from("SDK_KEY")(at||(at=(0,m.cy)(["There is an existing client instance for the specified SDK Key. No new client instance will be created and the specified options are ignored. Returning the existing client instance. SDK Key: '","'."],["There is an existing client instance for the specified SDK Key. No new client instance will be created and the specified options are ignored. Returning the existing client instance. SDK Key: '","'."])),e))},t.prototype.userObjectIsMissing=function(e){return this.log(g.Warn,3001,d.from("KEY")(ut||(ut=(0,m.cy)(["Cannot evaluate targeting rules and % options for setting '","' (User Object is missing). You should pass a User Object to the evaluation methods like `getValueAsync()` in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate targeting rules and % options for setting '","' (User Object is missing). You should pass a User Object to the evaluation methods like \\`getValueAsync()\\` in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e))},t.prototype.dataGovernanceIsOutOfSync=function(){return this.log(g.Warn,3002,"The `dataGovernance` parameter specified at the client initialization is not in sync with the preferences on the ConfigCat Dashboard. Read more: https://configcat.com/docs/advanced/data-governance/")},t.prototype.userObjectAttributeIsMissingPercentage=function(e,n){return this.log(g.Warn,3003,d.from("KEY","ATTRIBUTE_NAME","ATTRIBUTE_NAME")(lt||(lt=(0,m.cy)(["Cannot evaluate % options for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate % options for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e,n,n))},t.prototype.userObjectAttributeIsMissingCondition=function(e,n,r){return this.log(g.Warn,3003,d.from("CONDITION","KEY","ATTRIBUTE_NAME","ATTRIBUTE_NAME")(ct||(ct=(0,m.cy)(["Cannot evaluate condition (",") for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate condition (",") for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e,n,r,r))},t.prototype.userObjectAttributeIsInvalid=function(e,n,r,i){return this.log(g.Warn,3004,d.from("CONDITION","KEY","REASON","ATTRIBUTE_NAME")(ft||(ft=(0,m.cy)(["Cannot evaluate condition (",") for setting '","' (","). Please check the User."," attribute and make sure that its value corresponds to the comparison operator."],["Cannot evaluate condition (",") for setting '","' (","). Please check the User."," attribute and make sure that its value corresponds to the comparison operator."])),e,n,r,i))},t.prototype.userObjectAttributeIsAutoConverted=function(e,n,r,i){return this.log(g.Warn,3005,d.from("CONDITION","KEY","ATTRIBUTE_NAME","ATTRIBUTE_VALUE")(ht||(ht=(0,m.cy)(["Evaluation of condition (",") for setting '","' may not produce the expected result (the User."," attribute is not a string value, thus it was automatically converted to the string value '","'). Please make sure that using a non-string value was intended."],["Evaluation of condition (",") for setting '","' may not produce the expected result (the User."," attribute is not a string value, thus it was automatically converted to the string value '","'). Please make sure that using a non-string value was intended."])),e,n,r,i))},t.prototype.configServiceCannotInitiateHttpCalls=function(){return this.log(g.Warn,3200,"Client is in offline mode, it cannot initiate HTTP calls.")},t.prototype.configServiceMethodHasNoEffectDueToDisposedClient=function(e){return this.log(g.Warn,3201,d.from("METHOD_NAME")(gt||(gt=(0,m.cy)(["The client object is already disposed, thus `","()` has no effect."],["The client object is already disposed, thus \\`","()\\` has no effect."])),e))},t.prototype.configServiceMethodHasNoEffectDueToOverrideBehavior=function(e,n){return this.log(g.Warn,3202,d.from("OVERRIDE_BEHAVIOR","METHOD_NAME")(vt||(vt=(0,m.cy)(["Client is configured to use the `","` override behavior, thus `","()` has no effect."],["Client is configured to use the \\`","\\` override behavior, thus \\`","()\\` has no effect."])),e,n))},t.prototype.settingEvaluated=function(e){return this.log(g.Info,5e3,d.from("EVALUATE_LOG")(pt||(pt=(0,m.cy)(["",""],["",""])),e))},t.prototype.configServiceStatusChanged=function(e){return this.log(g.Info,5200,d.from("MODE")(dt||(dt=(0,m.cy)(["Switched to "," mode."],["Switched to "," mode."])),e.toUpperCase()))},t}(),I=function(){function t(e,n){void 0===e&&(e=g.Warn),void 0===n&&(n="\n"),this.level=e,this.eol=n,this.SOURCE="ConfigCat"}return t.prototype.log=function(e,n,r,i){var o=e===g.Debug?[console.info,"DEBUG"]:e===g.Info?[console.info,"INFO"]:e===g.Warn?[console.warn,"WARN"]:e===g.Error?[console.error,"ERROR"]:[console.log,g[e].toUpperCase()],s=o[0],a=o[1],u=void 0!==i?this.eol+X(i,!0):"";s(this.SOURCE+" - "+a+" - ["+n+"] "+r+u)},t}();function Se(t){return!!t.fn}var Jt=function(){function t(){this.events={},this.eventCount=0,this.addListener=this.on,this.off=this.removeListener}return t.prototype.addListenerCore=function(e,n,r){if("function"!=typeof n)throw new TypeError("Listener must be a function");var i=this.events[e],o={fn:n,once:r};return i?Se(i)?this.events[e]=[i,o]:i.push(o):(this.events[e]=o,this.eventCount++),this},t.prototype.removeListenerCore=function(e,n,r){var i=this.events[e];if(!i)return this;if(Se(i))r(i,n)&&this.removeEvent(e);else for(var o=i.length-1;o>=0;o--)if(r(i[o],n)){i.splice(o,1),i.length?1===i.length&&(this.events[e]=i[0]):this.removeEvent(e);break}return this},t.prototype.removeEvent=function(e){0==--this.eventCount?this.events={}:delete this.events[e]},t.prototype.on=function(e,n){return this.addListenerCore(e,n,!1)},t.prototype.once=function(e,n){return this.addListenerCore(e,n,!0)},t.prototype.removeListener=function(e,n){if("function"!=typeof n)throw new TypeError("Listener must be a function");return this.removeListenerCore(e,n,function(r,i){return r.fn===i})},t.prototype.removeAllListeners=function(e){return e?this.events[e]&&this.removeEvent(e):(this.events={},this.eventCount=0),this},t.prototype.listeners=function(e){var n=this.events[e];if(!n)return[];if(Se(n))return[n.fn];for(var r=n.length,i=new Array(r),o=0;o<r;o++)i[o]=n[o].fn;return i},t.prototype.listenerCount=function(e){var n=this.events[e];return n?Se(n)?1:n.length:0},t.prototype.eventNames=function(){var e=[];if(0===this.eventCount)return e;var n=this.events;for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.push(r);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(n)):e},t.prototype.emit=function(e,n,r,i,o){for(var s,a,u=[],l=5;l<arguments.length;l++)u[l-5]=arguments[l];var E,O,y=this.events[e];if(!y)return!1;Se(y)?(E=(s=[y,1])[0],O=s[1]):(E=(a=[(y=y.slice())[0],y.length])[0],O=a[1]);for(var b=arguments.length-1,A=0;;){switch(E.once&&this.removeListenerCore(e,E,function(P,k){return P===k}),b){case 0:E.fn.call(this);break;case 1:E.fn.call(this,n);break;case 2:E.fn.call(this,n,r);break;case 3:E.fn.call(this,n,r,i);break;case 4:E.fn.call(this,n,r,i,o);break;default:for(var w=new Array(b),R=0;R<b;R++)w[R]=arguments[R+1];E.fn.apply(this,w)}if(++A>=O)break;E=y[A]}return!0},t}(),yt=function(){function t(){this.addListener=this.on,this.off=this.removeListener}return t.prototype.on=function(){return this},t.prototype.once=function(){return this},t.prototype.removeListener=function(){return this},t.prototype.removeAllListeners=function(){return this},t.prototype.listeners=function(){return[]},t.prototype.listenerCount=function(){return 0},t.prototype.eventNames=function(){return[]},t.prototype.emit=function(){return!1},t}();function mt(t){function e(ie,oe){return ie<<oe|ie>>>32-oe}var n,r,i,E,O,b,A,w,R,o=new Array(80),s=1732584193,a=4023233417,u=2562383102,l=271733878,y=3285377520,P=(t=ue(t)).length,k=new Array;for(r=0;r<P-3;r+=4)i=t.charCodeAt(r)<<24|t.charCodeAt(r+1)<<16|t.charCodeAt(r+2)<<8|t.charCodeAt(r+3),k.push(i);switch(P%4){case 0:r=2147483648;break;case 1:r=t.charCodeAt(P-1)<<24|8388608;break;case 2:r=t.charCodeAt(P-2)<<24|t.charCodeAt(P-1)<<16|32768;break;case 3:r=t.charCodeAt(P-3)<<24|t.charCodeAt(P-2)<<16|t.charCodeAt(P-1)<<8|128}for(k.push(r);k.length%16!=14;)k.push(0);for(k.push(P>>>29),k.push(P<<3&4294967295),n=0;n<k.length;n+=16){for(r=0;r<16;r++)o[r]=k[n+r];for(r=16;r<=79;r++)o[r]=e(o[r-3]^o[r-8]^o[r-14]^o[r-16],1);for(E=s,O=a,b=u,A=l,w=y,r=0;r<=19;r++)R=e(E,5)+(O&b|~O&A)+w+o[r]+1518500249&4294967295,w=A,A=b,b=e(O,30),O=E,E=R;for(r=20;r<=39;r++)R=e(E,5)+(O^b^A)+w+o[r]+1859775393&4294967295,w=A,A=b,b=e(O,30),O=E,E=R;for(r=40;r<=59;r++)R=e(E,5)+(O&b|O&A|b&A)+w+o[r]+2400959708&4294967295,w=A,A=b,b=e(O,30),O=E,E=R;for(r=60;r<=79;r++)R=e(E,5)+(O^b^A)+w+o[r]+3395469782&4294967295,w=A,A=b,b=e(O,30),O=E,E=R;s=s+E&4294967295,a=a+O&4294967295,u=u+b&4294967295,l=l+A&4294967295,y=y+w&4294967295}return Ot([s,a,u,l,y])}function Et(t){function e(Bt,Gt){return Bt>>>Gt|Bt<<32-Gt}var o,s,n="length",r=Math.pow,i=r(2,32),a=Et,u=a.h,l=a.k;if(!l){u=[],l=[];for(var y={},E=2,O=0;O<64;E++)if(!y[E]){for(o=0;o<313;o+=E)y[o]=E;u[O]=r(E,.5)*i|0,l[O++]=r(E,1/3)*i|0}a.h=u=u.slice(0,8),a.k=l}var b=8*t[n];t+="\x80";for(var A=[];t[n]%64-56;)t+="\0";for(o=0;o<t[n];o++)s=t.charCodeAt(o),A[o>>2]|=s<<(3-o)%4*8;for(A[A[n]]=b/i|0,A[A[n]]=b,s=0;s<A[n];){var w=A.slice(s,s+=16),R=u;for(u=u.slice(0,8),o=0;o<64;o++){var P=w[o-15],k=w[o-2],ie=u[0],oe=u[4],Je=u[7]+(e(oe,6)^e(oe,11)^e(oe,25))+(oe&u[5]^~oe&u[6])+l[o]+(w[o]=o<16?w[o]:w[o-16]+(e(P,7)^e(P,18)^P>>>3)+w[o-7]+(e(k,17)^e(k,19)^k>>>10)|0);(u=[Je+((e(ie,2)^e(ie,13)^e(ie,22))+(ie&u[1]^ie&u[2]^u[1]&u[2]))|0].concat(u))[4]=u[4]+Je|0}for(o=0;o<8;o++)u[o]=u[o]+R[o]|0}return Ot(u,8)}function Ot(t,e){var n="0123456789abcdef",r="";e??(e=t.length);for(var i=0;i<e;i++)for(var o=3;o>=0;o--){var s=t[i]>>(o<<3)&255;r+=n[s>>4],r+=n[15&s]}return r}var bt=new yt,St=function(){function t(e){this.addListener=this.on,this.off=this.removeListener,this.eventEmitter=e}return t.prototype.tryDisconnect=function(){var e=this.eventEmitter;return this.eventEmitter=bt,e!==bt},t.prototype.on=function(e,n){return this.eventEmitter.on(e,n),this},t.prototype.once=function(e,n){return this.eventEmitter.once(e,n),this},t.prototype.removeListener=function(e,n){return this.eventEmitter.removeListener(e,n),this},t.prototype.removeAllListeners=function(e){return this.eventEmitter.removeAllListeners(e),this},t.prototype.listeners=function(e){return this.eventEmitter.listeners(e)},t.prototype.listenerCount=function(e){return this.eventEmitter.listenerCount(e)},t.prototype.eventNames=function(){return this.eventEmitter.eventNames()},t.prototype.emit=function(e){for(var n,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return(n=this.eventEmitter).emit.apply(n,(0,m.pr)([e],r))},t}();function zt(t){for(var e=[],n=0,r=Object.keys(t);n<r.length;n++)e.push(t[r[n]]);return e}function Yt(t){for(var e=[],n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];e.push([i,t[i]])}return e}function Zt(t){var e,n={};if(Array.isArray(t))for(var r=0,i=t;r<i.length;r++){var o=i[r];n[o[0]]=o[1]}else{if(!(typeof Symbol<"u"&&t?.[Symbol.iterator]))throw new TypeError("Object.fromEntries() requires a single iterable argument");for(var u=t[Symbol.iterator](),l=void 0;l=(e=u.next()).value,!e.done;)n[l[0]]=l[1]}return n}function At(){var t=function(e){this.target=e};return t.prototype.deref=function(){return this.target},t.isFallback=!0,t}var Tt=function(){return"function"==typeof WeakRef},Q=(()=>{return(t=Q||(Q={}))[t.AutoPoll=0]="AutoPoll",t[t.LazyLoad=1]="LazyLoad",t[t.ManualPoll=2]="ManualPoll",Q;var t})(),ge=(()=>{return(t=ge||(ge={}))[t.Global=0]="Global",t[t.EuOnly=1]="EuOnly",ge;var t})(),Ye=function(){function t(e,n,r,i,o){var s,a,u;if(this.requestTimeoutMs=3e4,this.baseUrlOverriden=!1,this.proxy="",this.offline=!1,!e)throw new Error("Invalid 'sdkKey' value");this.sdkKey=e,this.clientVersion=n,this.dataGovernance=null!==(s=r?.dataGovernance)&&void 0!==s?s:ge.Global,this.baseUrl=this.dataGovernance===ge.EuOnly?"https://cdn-eu.configcat.com":"https://cdn-global.configcat.com";var O,b,l=null!==(a=o?.())&&void 0!==a?a:new Jt,y=new St(l),E=new(Tt()?WeakRef:At())(y);if(this.hooks={hooks:y,hooksWeakRef:E,emit:function(A){for(var w,R,P=[],k=1;k<arguments.length;k++)P[k-1]=arguments[k];return null!==(R=null===(w=this.hooksWeakRef.deref())||void 0===w?void 0:w.emit.apply(w,(0,m.pr)([A],P)))&&void 0!==R&&R}},r){if(O=r.logger,b=r.cache,r.requestTimeoutMs){if(r.requestTimeoutMs<0)throw new Error("Invalid 'requestTimeoutMs' value");this.requestTimeoutMs=r.requestTimeoutMs}r.baseUrl&&(this.baseUrl=r.baseUrl,this.baseUrlOverriden=!0),r.proxy&&(this.proxy=r.proxy),r.flagOverrides&&(this.flagOverrides=r.flagOverrides),r.defaultUser&&(this.defaultUser=r.defaultUser),r.offline&&(this.offline=r.offline),null===(u=r.setupHooks)||void 0===u||u.call(r,y)}this.logger=new S(O??new I,this.hooks),this.cache=b?new p(b,this.logger):i?i(this):new v}return t.prototype.yieldHooks=function(){var e=this.hooks,n=e.hooks;return delete e.hooks,n??new St(new yt)},t.prototype.getUrl=function(){return this.baseUrl+"/configuration-files/"+this.sdkKey+"/"+t.configFileName+"?sdk="+this.clientVersion},t.prototype.getCacheKey=function(){return mt(this.sdkKey+"_"+t.configFileName+"_"+K.serializationFormatVersion)},t.configFileName="config_v6.json",t}(),wt=function(t){function e(n,r,i,o,s,a){var u=t.call(this,n,r+"/a-"+i,o,s,a)||this;u.pollIntervalSeconds=60,u.maxInitWaitTimeSeconds=5,o&&(null!=o.pollIntervalSeconds&&(u.pollIntervalSeconds=o.pollIntervalSeconds),null!=o.maxInitWaitTimeSeconds&&(u.maxInitWaitTimeSeconds=o.maxInitWaitTimeSeconds));var l=2147483;if(!("number"==typeof u.pollIntervalSeconds&&1<=u.pollIntervalSeconds&&u.pollIntervalSeconds<=l))throw new Error("Invalid 'pollIntervalSeconds' value");if(!("number"==typeof u.maxInitWaitTimeSeconds&&u.maxInitWaitTimeSeconds<=l))throw new Error("Invalid 'maxInitWaitTimeSeconds' value");return u}return(0,m.ZT)(e,t),e}(Ye),It=function(t){function e(n,r,i,o,s,a){return t.call(this,n,r+"/m-"+i,o,s,a)||this}return(0,m.ZT)(e,t),e}(Ye),Ct=function(t){function e(n,r,i,o,s,a){var u=t.call(this,n,r+"/l-"+i,o,s,a)||this;if(u.cacheTimeToLiveSeconds=60,o&&null!=o.cacheTimeToLiveSeconds&&(u.cacheTimeToLiveSeconds=o.cacheTimeToLiveSeconds),!("number"==typeof u.cacheTimeToLiveSeconds&&1<=u.cacheTimeToLiveSeconds&&u.cacheTimeToLiveSeconds<=2147483647))throw new Error("Invalid 'cacheTimeToLiveSeconds' value");return u}return(0,m.ZT)(e,t),e}(Ye),W=(()=>{return(t=W||(W={}))[t.LocalOnly=0]="LocalOnly",t[t.LocalOverRemote=1]="LocalOverRemote",t[t.RemoteOverLocal=2]="RemoteOverLocal",W;var t})(),$t=function(){function t(e,n){this.initialSettings=this.constructor.getCurrentSettings(e),n&&(this.map=e)}return t.getCurrentSettings=function(e){return Object.fromEntries(Object.entries(e).map(function(n){return[n[0],Re.fromValue(n[1])]}))},t.prototype.getOverrides=function(){return Promise.resolve(this.getOverridesSync())},t.prototype.getOverridesSync=function(){return this.map?this.constructor.getCurrentSettings(this.map):this.initialSettings},t}(),Xt=function t(e,n){this.dataSource=e,this.behaviour=n},Qt=function(t){function e(n,r){var i=t.call(this,n,r)||this;i.cacheTimeToLiveMs=1e3*r.cacheTimeToLiveSeconds;var o=i.syncUpWithCache();return i.readyPromise=i.getReadyPromise(o,function(s){return(0,m.mG)(i,void 0,void 0,function(){var a;return(0,m.Jh)(this,function(u){switch(u.label){case 0:return a=this.getCacheState,[4,s];case 1:return[2,a.apply(this,[u.sent()])]}})})}),i}return(0,m.ZT)(e,t),e.prototype.getConfig=function(){return(0,m.mG)(this,void 0,void 0,function(){function n(o,s){void 0===s&&(s=""),o.debug("LazyLoadConfigService.getConfig(): cache is empty or expired"+s+".")}var r,i;return(0,m.Jh)(this,function(o){switch(o.label){case 0:return this.options.logger.debug("LazyLoadConfigService.getConfig() called."),[4,this.options.cache.get(this.cacheKey)];case 1:return(r=o.sent()).isExpired(this.cacheTimeToLiveMs)?this.isOffline?[3,3]:(n(this.options.logger,", calling refreshConfigCoreAsync()"),[4,this.refreshConfigCoreAsync(r)]):[3,5];case 2:return i=o.sent(),r=i[1],[3,4];case 3:n(this.options.logger),o.label=4;case 4:return[2,r];case 5:return this.options.logger.debug("LazyLoadConfigService.getConfig(): cache is valid, returning from cache."),[2,r]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("LazyLoadConfigService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e.prototype.getCacheState=function(n){return n.isEmpty?_.NoFlagData:n.isExpired(this.cacheTimeToLiveMs)?_.HasCachedFlagDataOnly:_.HasUpToDateFlagData},e}(fe),er=function(t){function e(n,r){var i=t.call(this,n,r)||this,o=i.syncUpWithCache();return i.readyPromise=i.getReadyPromise(o,function(s){return(0,m.mG)(i,void 0,void 0,function(){var a;return(0,m.Jh)(this,function(u){switch(u.label){case 0:return a=this.getCacheState,[4,s];case 1:return[2,a.apply(this,[u.sent()])]}})})}),i}return(0,m.ZT)(e,t),e.prototype.getCacheState=function(n){return n.isEmpty?_.NoFlagData:_.HasCachedFlagDataOnly},e.prototype.getConfig=function(){return(0,m.mG)(this,void 0,void 0,function(){return(0,m.Jh)(this,function(n){switch(n.label){case 0:return this.options.logger.debug("ManualPollService.getConfig() called."),[4,this.options.cache.get(this.cacheKey)];case 1:return[2,n.sent()]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("ManualPollService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e}(fe),Nt="<invalid value>",Ze="<invalid name>",$e="<invalid operator>",Lt="<invalid reference>",Rt=function(){function t(e){this.eol=e,this.log="",this.indent=""}return t.prototype.resetIndent=function(){return this.indent="",this},t.prototype.increaseIndent=function(){return this.indent+="  ",this},t.prototype.decreaseIndent=function(){return this.indent=this.indent.slice(0,-2),this},t.prototype.newLine=function(e){return this.log+=this.eol+this.indent+(e??""),this},t.prototype.append=function(e){return this.log+=e,this},t.prototype.toString=function(){return this.log},t.prototype.appendUserConditionCore=function(e,n,r){return this.append("User."+e+" "+Xe(n)+" '"+(r??Nt)+"'")},t.prototype.appendUserConditionString=function(e,n,r,i){return"string"!=typeof r?this.appendUserConditionCore(e,n):this.appendUserConditionCore(e,n,i?"<hashed value>":r)},t.prototype.appendUserConditionStringList=function(e,n,r,i){if(!Oe(r))return this.appendUserConditionCore(e,n);var o="value",s="values",a=Xe(n);if(i)return this.append("User."+e+" "+a+" [<"+r.length+" hashed "+(1===r.length?o:s)+">]");var u=be(r,10,function(l){return", ... <"+l+" more "+(1===l?o:s)+">"});return this.append("User."+e+" "+a+" ["+u+"]")},t.prototype.appendUserConditionNumber=function(e,n,r,i){if("number"!=typeof r)return this.appendUserConditionCore(e,n);var s,o=Xe(n);return i&&!isNaN(s=new Date(1e3*r))?this.append("User."+e+" "+o+" '"+r+"' ("+s.toISOString()+" UTC)"):this.append("User."+e+" "+o+" '"+r+"'")},t.prototype.appendUserCondition=function(e){var n="string"==typeof e.comparisonAttribute?e.comparisonAttribute:Ze,r=e.comparator;switch(e.comparator){case f.TextIsOneOf:case f.TextIsNotOneOf:case f.TextContainsAnyOf:case f.TextNotContainsAnyOf:case f.SemVerIsOneOf:case f.SemVerIsNotOneOf:case f.TextStartsWithAnyOf:case f.TextNotStartsWithAnyOf:case f.TextEndsWithAnyOf:case f.TextNotEndsWithAnyOf:case f.ArrayContainsAnyOf:case f.ArrayNotContainsAnyOf:return this.appendUserConditionStringList(n,r,e.comparisonValue,!1);case f.SemVerLess:case f.SemVerLessOrEquals:case f.SemVerGreater:case f.SemVerGreaterOrEquals:case f.TextEquals:case f.TextNotEquals:return this.appendUserConditionString(n,r,e.comparisonValue,!1);case f.NumberEquals:case f.NumberNotEquals:case f.NumberLess:case f.NumberLessOrEquals:case f.NumberGreater:case f.NumberGreaterOrEquals:return this.appendUserConditionNumber(n,r,e.comparisonValue);case f.SensitiveTextIsOneOf:case f.SensitiveTextIsNotOneOf:case f.SensitiveTextStartsWithAnyOf:case f.SensitiveTextNotStartsWithAnyOf:case f.SensitiveTextEndsWithAnyOf:case f.SensitiveTextNotEndsWithAnyOf:case f.SensitiveArrayContainsAnyOf:case f.SensitiveArrayNotContainsAnyOf:return this.appendUserConditionStringList(n,r,e.comparisonValue,!0);case f.DateTimeBefore:case f.DateTimeAfter:return this.appendUserConditionNumber(n,r,e.comparisonValue,!0);case f.SensitiveTextEquals:case f.SensitiveTextNotEquals:return this.appendUserConditionString(n,r,e.comparisonValue,!0);default:return this.appendUserConditionCore(n,r,e.comparisonValue)}},t.prototype.appendPrerequisiteFlagCondition=function(e,n){var o=e.comparisonValue;return this.append("Flag '"+("string"!=typeof e.prerequisiteFlagKey?Ze:e.prerequisiteFlagKey in n?e.prerequisiteFlagKey:Lt)+"' "+function rr(t){switch(t){case Z.Equals:return"EQUALS";case Z.NotEquals:return"NOT EQUALS";default:return $e}}(e.comparator)+" '"+Me(o)+"'")},t.prototype.appendSegmentCondition=function(e){var n=e.segment,i=null==n?Lt:"string"==typeof n.name&&n.name?n.name:Ze;return this.append("User "+Ft(e.comparator)+" '"+i+"'")},t.prototype.appendConditionResult=function(e){return this.append(""+e)},t.prototype.appendConditionConsequence=function(e){return this.append(" => ").appendConditionResult(e),e?this:this.append(", skipping the remaining AND conditions")},t.prototype.appendTargetingRuleThenPart=function(e,n){(n?this.newLine():this.append(" ")).append("THEN");var r=e.then;return this.append(he(r)?" % options":" '"+Me(r.value)+"'")},t.prototype.appendTargetingRuleConsequence=function(e,n,r){return this.increaseIndent(),this.appendTargetingRuleThenPart(e,r).append(" => ").append(!0===n?"MATCH, applying rule":!1===n?"no match":n),this.decreaseIndent()},t}();function Xe(t){switch(t){case f.TextIsOneOf:case f.SensitiveTextIsOneOf:case f.SemVerIsOneOf:return"IS ONE OF";case f.TextIsNotOneOf:case f.SensitiveTextIsNotOneOf:case f.SemVerIsNotOneOf:return"IS NOT ONE OF";case f.TextContainsAnyOf:return"CONTAINS ANY OF";case f.TextNotContainsAnyOf:return"NOT CONTAINS ANY OF";case f.SemVerLess:case f.NumberLess:return"<";case f.SemVerLessOrEquals:case f.NumberLessOrEquals:return"<=";case f.SemVerGreater:case f.NumberGreater:return">";case f.SemVerGreaterOrEquals:case f.NumberGreaterOrEquals:return">=";case f.NumberEquals:return"=";case f.NumberNotEquals:return"!=";case f.DateTimeBefore:return"BEFORE";case f.DateTimeAfter:return"AFTER";case f.TextEquals:case f.SensitiveTextEquals:return"EQUALS";case f.TextNotEquals:case f.SensitiveTextNotEquals:return"NOT EQUALS";case f.TextStartsWithAnyOf:case f.SensitiveTextStartsWithAnyOf:return"STARTS WITH ANY OF";case f.TextNotStartsWithAnyOf:case f.SensitiveTextNotStartsWithAnyOf:return"NOT STARTS WITH ANY OF";case f.TextEndsWithAnyOf:case f.SensitiveTextEndsWithAnyOf:return"ENDS WITH ANY OF";case f.TextNotEndsWithAnyOf:case f.SensitiveTextNotEndsWithAnyOf:return"NOT ENDS WITH ANY OF";case f.ArrayContainsAnyOf:case f.SensitiveArrayContainsAnyOf:return"ARRAY CONTAINS ANY OF";case f.ArrayNotContainsAnyOf:case f.SensitiveArrayNotContainsAnyOf:return"ARRAY NOT CONTAINS ANY OF";default:return $e}}function Qe(t){return new Rt("").appendUserCondition(t).toString()}function Ft(t){switch(t){case J.IsIn:return"IS IN SEGMENT";case J.IsNotIn:return"IS NOT IN SEGMENT";default:return $e}}function Me(t){return we(t)?t.toString():Nt}var Dt=/^[0-9]+$/,ve=function(t,e){var n=Dt.test(t),r=Dt.test(e);return n&&r&&(t=+t,e=+e),t===e?0:n&&!r?-1:r&&!n?1:t<e?-1:1},et=256,_e=Number.MAX_SAFE_INTEGER||9007199254740991,Ae=[],L=[],C={},nr=0,q=function(t,e){var n=nr++;C[t]=n,L[n]=e,Ae[n]=new RegExp(e)};q("NUMERICIDENTIFIER","0|[1-9]\\d*"),q("NUMERICIDENTIFIERLOOSE","[0-9]+"),q("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),q("MAINVERSION","("+L[C.NUMERICIDENTIFIER]+")\\.("+L[C.NUMERICIDENTIFIER]+")\\.("+L[C.NUMERICIDENTIFIER]+")"),q("MAINVERSIONLOOSE","("+L[C.NUMERICIDENTIFIERLOOSE]+")\\.("+L[C.NUMERICIDENTIFIERLOOSE]+")\\.("+L[C.NUMERICIDENTIFIERLOOSE]+")"),q("PRERELEASEIDENTIFIER","(?:"+L[C.NUMERICIDENTIFIER]+"|"+L[C.NONNUMERICIDENTIFIER]+")"),q("PRERELEASEIDENTIFIERLOOSE","(?:"+L[C.NUMERICIDENTIFIERLOOSE]+"|"+L[C.NONNUMERICIDENTIFIER]+")"),q("PRERELEASE","(?:-("+L[C.PRERELEASEIDENTIFIER]+"(?:\\."+L[C.PRERELEASEIDENTIFIER]+")*))"),q("PRERELEASELOOSE","(?:-?("+L[C.PRERELEASEIDENTIFIERLOOSE]+"(?:\\."+L[C.PRERELEASEIDENTIFIERLOOSE]+")*))"),q("BUILDIDENTIFIER","[0-9A-Za-z-]+"),q("BUILD","(?:\\+("+L[C.BUILDIDENTIFIER]+"(?:\\."+L[C.BUILDIDENTIFIER]+")*))"),q("FULLPLAIN","v?"+L[C.MAINVERSION]+L[C.PRERELEASE]+"?"+L[C.BUILD]+"?"),q("FULL","^"+L[C.FULLPLAIN]+"$"),q("LOOSEPLAIN","[v=\\s]*"+L[C.MAINVERSIONLOOSE]+L[C.PRERELEASELOOSE]+"?"+L[C.BUILD]+"?"),q("LOOSE","^"+L[C.LOOSEPLAIN]+"$");var Pt=function(){function t(e,n){if((!n||"object"!=typeof n)&&(n={loose:!!n,includePrerelease:!1}),e instanceof t){if(e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>et)throw new TypeError("version is longer than "+et+" characters");this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;var r=e.trim().match(n.loose?Ae[C.LOOSE]:Ae[C.FULL]);if(!r)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>_e||this.major<0)throw new TypeError("Invalid major version");if(this.minor>_e||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>_e||this.patch<0)throw new TypeError("Invalid patch version");this.prerelease=r[4]?r[4].split(".").map(function(i){if(/^[0-9]+$/.test(i)){var o=+i;if(o>=0&&o<_e)return o}return i}):[],this.build=r[5]?r[5].split("."):[],this.format()}return t.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},t.prototype.toString=function(){return this.version},t.prototype.compare=function(e){if(!(e instanceof t)){if("string"==typeof e&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)},t.prototype.compareMain=function(e){return e instanceof t||(e=new t(e,this.options)),ve(this.major,e.major)||ve(this.minor,e.minor)||ve(this.patch,e.patch)},t.prototype.comparePre=function(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var n=0;do{var r=this.prerelease[n],i=e.prerelease[n];if(void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return ve(r,i)}while(++n)},t.prototype.compareBuild=function(e){e instanceof t||(e=new t(e,this.options));var n=0;do{var r=this.build[n],i=e.build[n];if(void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return ve(r,i)}while(++n)},t.prototype.inc=function(e,n){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n),this.inc("pre",n);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",n),this.inc("pre",n);break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var r=this.prerelease.length;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);-1===r&&this.prerelease.push(0)}n&&(this.prerelease[0]===n?isNaN(this.prerelease[1])&&(this.prerelease=[n,0]):this.prerelease=[n,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this},t}(),tt=function(t,e){if((!e||"object"!=typeof e)&&(e={loose:!!e,includePrerelease:!1}),t instanceof Pt)return t;if("string"!=typeof t||t.length>et)return null;if(!(e.loose?Ae[C.LOOSE]:Ae[C.FULL]).test(t))return null;try{return new Pt(t,e)}catch{return null}},ir=function t(e,n,r,i){void 0===i&&(i={}),this.identifier=e,this.email=n,this.country=r,this.custom=i};function kt(t,e){var n,r;switch(e){case"Identifier":return null!==(n=t.identifier)&&void 0!==n?n:"";case"Email":return t.email;case"Country":return t.country;default:return null===(r=t.custom)||void 0===r?void 0:r[e]}}var rt=function(){function t(e,n,r,i){this.key=e,this.setting=n,this.user=r,this.settings=i}return Object.defineProperty(t.prototype,"visitedFlags",{get:function(){var e;return null!==(e=this.$visitedFlags)&&void 0!==e?e:this.$visitedFlags=[]},enumerable:!1,configurable:!0}),t.forPrerequisiteFlag=function(e,n,r){var i=new t(e,n,r.user,r.settings);return i.$visitedFlags=r.visitedFlags,i.logBuilder=r.logBuilder,i},t}(),xt="The current targeting rule is ignored and the evaluation continues with the next rule.",nt="cannot evaluate, User Object is missing",ar=function(t,e){return"cannot evaluate, the User."+t+" attribute is invalid ("+e+")"},ur=function(){function t(e){this.logger=e}return t.prototype.evaluate=function(e,n){this.logger.debug("RolloutEvaluator.evaluate() called.");var i,r=n.logBuilder;this.logger.isEnabled(g.Info)&&(n.logBuilder=r=new Rt(this.logger.eol),r.append("Evaluating '"+n.key+"'"),n.user&&r.append(" for User '"+JSON.stringify(function or(t){var e,n={},r="Identifier",i="Email",o="Country";if(n[r]=null!==(e=t.identifier)&&void 0!==e?e:"",null!=t.email&&(n[i]=t.email),null!=t.country&&(n[o]=t.country),null!=t.custom)for(var s=[r,i,o],a=0,u=Object.entries(t.custom);a<u.length;a++){var l=u[a],y=l[0],E=l[1];null!=E&&s.indexOf(y)<0&&(n[y]=E)}return n}(n.user))+"'"),r.increaseIndent());try{var o=void 0,s=void 0;if(null!=e){var a=n.setting.type;if(a>=0&&!function fr(t,e){switch(e){case B.Boolean:return"boolean"==typeof t;case B.String:return"string"==typeof t;case B.Int:case B.Double:return"number"==typeof t;default:return!1}}(e,a))throw new TypeError("The type of a setting must match the type of the specified default value. Setting's type was "+B[a]+" but the default value's type was "+typeof e+". Please use a default value which corresponds to the setting type "+B[a]+". Learn more: https://configcat.com/docs/sdk-reference/js/#setting-type-mapping");s=typeof(i=(o=this.evaluateSetting(n)).selectedValue.value)==typeof e}else s=we(i=(o=this.evaluateSetting(n)).selectedValue.value);return s||st(i),o}catch(u){throw r?.resetIndent().increaseIndent(),i=e,u}finally{r&&(r.newLine("Returning '"+i+"'.").decreaseIndent(),this.logger.settingEvaluated(r.toString()))}},t.prototype.evaluateSetting=function(e){var n,r=e.setting.targetingRules;if(r.length>0&&(n=this.evaluateTargetingRules(r,e)))return n;var i=e.setting.percentageOptions;return i.length>0&&(n=this.evaluatePercentageOptions(i,void 0,e))?n:{selectedValue:e.setting}},t.prototype.evaluateTargetingRules=function(e,n){var r=n.logBuilder;r?.newLine("Evaluating targeting rules and applying the first match if any:");for(var i=0;i<e.length;i++){var o=e[i],a=this.evaluateConditions(o.conditions,o,n.key,n);if(!0===a){if(!he(o.then))return{selectedValue:o.then,matchedTargetingRule:o};var u=o.then;r?.increaseIndent();var l=this.evaluatePercentageOptions(u,o,n);if(l)return r?.decreaseIndent(),l;r?.newLine(xt).decreaseIndent()}else Te(a)&&r?.increaseIndent().newLine(xt).decreaseIndent()}},t.prototype.evaluatePercentageOptions=function(e,n,r){var i,o=r.logBuilder;if(!r.user)return o?.newLine("Skipping % options because the User Object is missing."),void(r.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(r.key),r.isMissingUserObjectLogged=!0));var a,s=r.setting.percentageOptionsAttribute;if(null==s?(s="Identifier",a=null!==(i=r.user.identifier)&&void 0!==i?i:""):a=kt(r.user,s),null==a)return o?.newLine("Skipping % options because the User."+s+" attribute is missing."),void(r.isMissingUserObjectAttributeLogged||(this.logger.userObjectAttributeIsMissingPercentage(r.key,s),r.isMissingUserObjectAttributeLogged=!0));o?.newLine("Evaluating % options based on the User."+s+" attribute:");var u=mt(r.key+_t(a)),l=parseInt(u.substring(0,7),16)%100;o?.newLine("- Computing hash in the [0..99] range from User."+s+" => "+l+" (this value is sticky and consistent across all SDKs)");for(var y=0,E=0;E<e.length;E++){var O=e[E];if(!(l>=(y+=O.percentage)))return o?.newLine("- Hash value "+l+" selects % option "+(E+1)+" ("+O.percentage+"%), '"+Me(O.value)+"'."),{selectedValue:O,matchedTargetingRule:n,matchedPercentageOption:O}}throw new Error("Sum of percentage option percentages is less than 100.")},t.prototype.evaluateConditions=function(e,n,r,i){var o=!0,s=i.logBuilder,a=!1;s?.newLine("- ");for(var u=0;u<e.length;u++){var l=e[u];switch(s&&(u?s.increaseIndent().newLine("AND "):s.append("IF ").increaseIndent()),l.type){case"UserCondition":o=this.evaluateUserCondition(l,r,i),a=e.length>1;break;case"PrerequisiteFlagCondition":o=this.evaluatePrerequisiteFlagCondition(l,i),a=!0;break;case"SegmentCondition":a=!Te(o=this.evaluateSegmentCondition(l,i))||o!==nt||e.length>1;break;default:throw new Error}var y=!0===o;if(s&&((!n||e.length>1)&&s.appendConditionConsequence(y),s.decreaseIndent()),!y)break}return n&&s?.appendTargetingRuleConsequence(n,o,a),o},t.prototype.evaluateUserCondition=function(e,n,r){if(r.logBuilder?.appendUserCondition(e),!r.user)return r.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(r.key),r.isMissingUserObjectLogged=!0),nt;var a,u,l,y,o=e.comparisonAttribute,s=kt(r.user,o);if(null==s||""===s)return this.logger.userObjectAttributeIsMissingCondition(Qe(e),r.key,o),function(t){return"cannot evaluate, the User."+t+" attribute is missing"}(o);switch(e.comparator){case f.TextEquals:case f.TextNotEquals:return a=ee(o,s,e,r.key,this.logger),this.evaluateTextEquals(a,e.comparisonValue,e.comparator===f.TextNotEquals);case f.SensitiveTextEquals:case f.SensitiveTextNotEquals:return a=ee(o,s,e,r.key,this.logger),this.evaluateSensitiveTextEquals(a,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===f.SensitiveTextNotEquals);case f.TextIsOneOf:case f.TextIsNotOneOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateTextIsOneOf(a,e.comparisonValue,e.comparator===f.TextIsNotOneOf);case f.SensitiveTextIsOneOf:case f.SensitiveTextIsNotOneOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateSensitiveTextIsOneOf(a,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===f.SensitiveTextIsNotOneOf);case f.TextStartsWithAnyOf:case f.TextNotStartsWithAnyOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateTextSliceEqualsAnyOf(a,e.comparisonValue,!0,e.comparator===f.TextNotStartsWithAnyOf);case f.SensitiveTextStartsWithAnyOf:case f.SensitiveTextNotStartsWithAnyOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateSensitiveTextSliceEqualsAnyOf(a,e.comparisonValue,r.setting.configJsonSalt,n,!0,e.comparator===f.SensitiveTextNotStartsWithAnyOf);case f.TextEndsWithAnyOf:case f.TextNotEndsWithAnyOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateTextSliceEqualsAnyOf(a,e.comparisonValue,!1,e.comparator===f.TextNotEndsWithAnyOf);case f.SensitiveTextEndsWithAnyOf:case f.SensitiveTextNotEndsWithAnyOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateSensitiveTextSliceEqualsAnyOf(a,e.comparisonValue,r.setting.configJsonSalt,n,!1,e.comparator===f.SensitiveTextNotEndsWithAnyOf);case f.TextContainsAnyOf:case f.TextNotContainsAnyOf:return a=ee(o,s,e,r.key,this.logger),this.evaluateTextContainsAnyOf(a,e.comparisonValue,e.comparator===f.TextNotContainsAnyOf);case f.SemVerIsOneOf:case f.SemVerIsNotOneOf:return"string"!=typeof(u=Ut(o,s,e,r.key,this.logger))?this.evaluateSemVerIsOneOf(u,e.comparisonValue,e.comparator===f.SemVerIsNotOneOf):u;case f.SemVerLess:case f.SemVerLessOrEquals:case f.SemVerGreater:case f.SemVerGreaterOrEquals:return"string"!=typeof(u=Ut(o,s,e,r.key,this.logger))?this.evaluateSemVerRelation(u,e.comparator,e.comparisonValue):u;case f.NumberEquals:case f.NumberNotEquals:case f.NumberLess:case f.NumberLessOrEquals:case f.NumberGreater:case f.NumberGreaterOrEquals:return l=function lr(t,e,n,r,i){return"number"==typeof e?e:"string"!=typeof e||isNaN(o=ze(e.replace(",",".")))&&"NaN"!==e.trim()?Ue(i,n,r,t,"'"+e+"' is not a valid decimal number"):o;var o}(o,s,e,r.key,this.logger),"string"!=typeof l?this.evaluateNumberRelation(l,e.comparator,e.comparisonValue):l;case f.DateTimeBefore:case f.DateTimeAfter:return l=function cr(t,e,n,r,i){return e instanceof Date?e.getTime()/1e3:"number"==typeof e?e:"string"!=typeof e||isNaN(o=ze(e.replace(",",".")))&&"NaN"!==e.trim()?Ue(i,n,r,t,"'"+e+"' is not a valid Unix timestamp (number of seconds elapsed since Unix epoch)"):o;var o}(o,s,e,r.key,this.logger),"string"!=typeof l?this.evaluateDateTimeRelation(l,e.comparisonValue,e.comparator===f.DateTimeBefore):l;case f.ArrayContainsAnyOf:case f.ArrayNotContainsAnyOf:return"string"!=typeof(y=qt(o,s,e,r.key,this.logger))?this.evaluateArrayContainsAnyOf(y,e.comparisonValue,e.comparator===f.ArrayNotContainsAnyOf):y;case f.SensitiveArrayContainsAnyOf:case f.SensitiveArrayNotContainsAnyOf:return"string"!=typeof(y=qt(o,s,e,r.key,this.logger))?this.evaluateSensitiveArrayContainsAnyOf(y,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===f.SensitiveArrayNotContainsAnyOf):y;default:throw new Error}},t.prototype.evaluateTextEquals=function(e,n,r){return e===n!==r},t.prototype.evaluateSensitiveTextEquals=function(e,n,r,i,o){return it(e,r,i)===n!==o},t.prototype.evaluateTextIsOneOf=function(e,n,r){return n.indexOf(e)>=0!==r},t.prototype.evaluateSensitiveTextIsOneOf=function(e,n,r,i,o){var s=it(e,r,i);return n.indexOf(s)>=0!==o},t.prototype.evaluateTextSliceEqualsAnyOf=function(e,n,r,i){for(var o=0;o<n.length;o++){var s=n[o];if(!(e.length<s.length)&&(r?e.lastIndexOf(s,0):e.indexOf(s,e.length-s.length))>=0)return!i}return i},t.prototype.evaluateSensitiveTextSliceEqualsAnyOf=function(e,n,r,i,o,s){for(var a=ue(e),u=0;u<n.length;u++){var l=n[u],y=l.indexOf("_"),E=parseInt(l.slice(0,y));if(!(a.length<E)&&Mt(o?a.slice(0,E):a.slice(a.length-E),r,i)===l.slice(y+1))return!s}return s},t.prototype.evaluateTextContainsAnyOf=function(e,n,r){for(var i=0;i<n.length;i++)if(e.indexOf(n[i])>=0)return!r;return r},t.prototype.evaluateSemVerIsOneOf=function(e,n,r){for(var i=!1,o=0;o<n.length;o++){var s=n[o];if(s.length){var a=tt(s.trim());if(!a)return!1;!i&&0===e.compare(a)&&(i=!0)}}return i!==r},t.prototype.evaluateSemVerRelation=function(e,n,r){var i=tt(r.trim());if(!i)return!1;var o=e.compare(i);switch(n){case f.SemVerLess:return o<0;case f.SemVerLessOrEquals:return o<=0;case f.SemVerGreater:return o>0;case f.SemVerGreaterOrEquals:return o>=0}},t.prototype.evaluateNumberRelation=function(e,n,r){switch(n){case f.NumberEquals:return e===r;case f.NumberNotEquals:return e!==r;case f.NumberLess:return e<r;case f.NumberLessOrEquals:return e<=r;case f.NumberGreater:return e>r;case f.NumberGreaterOrEquals:return e>=r}},t.prototype.evaluateDateTimeRelation=function(e,n,r){return r?e<n:e>n},t.prototype.evaluateArrayContainsAnyOf=function(e,n,r){for(var i=0;i<e.length;i++)if(n.indexOf(e[i])>=0)return!r;return r},t.prototype.evaluateSensitiveArrayContainsAnyOf=function(e,n,r,i,o){for(var s=0;s<e.length;s++){var a=it(e[s],r,i);if(n.indexOf(a)>=0)return!o}return o},t.prototype.evaluatePrerequisiteFlagCondition=function(e,n){var r=n.logBuilder;r?.appendPrerequisiteFlagCondition(e,n.settings);var i=e.prerequisiteFlagKey,o=n.settings[i];if(n.visitedFlags.push(n.key),n.visitedFlags.indexOf(i)>=0){n.visitedFlags.push(i);var s=be(n.visitedFlags,void 0,void 0," -> ");throw new Error("Circular dependency detected between the following depending flags: "+s+".")}var a=rt.forPrerequisiteFlag(i,o,n);r?.newLine("(").increaseIndent().newLine("Evaluating prerequisite flag '"+i+"':");var u=this.evaluateSetting(a);n.visitedFlags.pop();var y,l=u.selectedValue.value;if(typeof l!=typeof e.comparisonValue){if(we(l))throw new Error("Type mismatch between comparison value '"+e.comparisonValue+"' and prerequisite flag '"+i+"'.");st(l)}switch(e.comparator){case Z.Equals:y=l===e.comparisonValue;break;case Z.NotEquals:y=l!==e.comparisonValue;break;default:throw new Error}return r?.newLine("Prerequisite flag evaluation result: '"+Me(l)+"'.").newLine("Condition (").appendPrerequisiteFlagCondition(e,n.settings).append(") evaluates to ").appendConditionResult(y).append(".").decreaseIndent().newLine(")"),y},t.prototype.evaluateSegmentCondition=function(e,n){var r=n.logBuilder;if(r?.appendSegmentCondition(e),!n.user)return n.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(n.key),n.isMissingUserObjectLogged=!0),nt;var i=e.segment;r?.newLine("(").increaseIndent().newLine("Evaluating segment '"+i.name+"':");var o=this.evaluateConditions(i.conditions,void 0,i.name,n),s=o;if(!Te(s))switch(e.comparator){case J.IsIn:break;case J.IsNotIn:s=!s;break;default:throw new Error}return r&&(r.newLine("Segment evaluation result: "),(Te(s)?r.append(s):r.append("User "+Ft(o?J.IsIn:J.IsNotIn))).append("."),r.newLine("Condition (").appendSegmentCondition(e).append(")"),(Te(s)?r.append(" failed to evaluate"):r.append(" evaluates to ").appendConditionResult(s)).append("."),r.decreaseIndent().newLine(")")),s},t}();function Te(t){return"string"==typeof t}function it(t,e,n){return Mt(ue(t),e,n)}function Mt(t,e,n){return Et(t+ue(e)+ue(n))}function _t(t){return"string"==typeof t?t:t instanceof Date?t.getTime()/1e3+"":Oe(t)?JSON.stringify(t):t+""}function ee(t,e,n,r,i){return"string"==typeof e||(e=_t(e),i.userObjectAttributeIsAutoConverted(Qe(n),r,t,e)),e}function Ut(t,e,n,r,i){var o;return"string"==typeof e&&(o=tt(e.trim()))?o:Ue(i,n,r,t,"'"+e+"' is not a valid semantic version")}function qt(t,e,n,r,i){var o=e;if("string"==typeof o)try{o=JSON.parse(o)}catch{}return Oe(o)?o:Ue(i,n,r,t,"'"+e+"' is not a valid string array")}function Ue(t,e,n,r,i){return t.userObjectAttributeIsInvalid(Qe(e),n,i,r),ar(r,i)}function jt(t,e,n,r){return{key:t,value:e.selectedValue.value,variationId:e.selectedValue.variationId,fetchTime:n,user:r,isDefaultValue:!1,matchedTargetingRule:e.matchedTargetingRule,matchedPercentageOption:e.matchedPercentageOption}}function le(t,e,n,r,i,o){return{key:t,value:e,fetchTime:n,user:r,isDefaultValue:!0,errorMessage:i,errorException:o}}function qe(t,e,n,r,i,o,s){var a;if(!e)return a=s.configJsonIsNotPresentSingle(n,"defaultValue",r).toString(),le(n,r,te(o),i,a);var u=e[n];return u?jt(n,t.evaluate(r,new rt(n,u,i,e)),te(o),i):(a=s.settingEvaluationFailedDueToMissingKey(n,"defaultValue",r,be(Object.keys(e))).toString(),le(n,r,te(o),i,a))}function Wt(t,e,n,r,i,o){var s;if(!ot(e,i,o))return[[],s];for(var a=[],u=0,l=Object.entries(e);u<l.length;u++){var y=l[u],E=y[0],O=y[1],b=void 0;try{b=jt(E,t.evaluate(null,new rt(E,O,n,e)),te(r),n)}catch(w){s??(s=[]),s.push(w),b=le(E,null,te(r),n,X(w),w)}a.push(b)}return[a,s]}function ot(t,e,n){return!!t||(e.configJsonIsNotPresent(n),!1)}function we(t){return"boolean"==typeof t||"string"==typeof t||"number"==typeof t}function st(t){throw new TypeError(null===t?"Setting value is null.":void 0===t?"Setting value is undefined.":"Setting value '"+t+"' is of an unsupported type ("+typeof t+").")}function te(t){return t?new Date(t.timestamp):void 0}var Ie=new(function(){function t(){this.instances={}}return t.prototype.getOrCreate=function(e,n){var r,i=this.instances[e.sdkKey];if(i&&(r=i[0].deref()))return[r,!0];var s={};r=new je(e,n,s);var a=Tt()?WeakRef:At();return this.instances[e.sdkKey]=[new a(r),s],[r,!1]},t.prototype.remove=function(e,n){var r=this.instances[e];if(r){var o=r[1],s=!!r[0].deref();if(!s||o===n)return delete this.instances[e],s}return!1},t.prototype.clear=function(){for(var e=[],n=0,r=Object.entries(this.instances);n<r.length;n++){var i=r[n],o=i[0],a=i[1][0].deref();a&&e.push(a),delete this.instances[o]}return e},t}()),je=function(){function t(e,n,r){var i;if(this.cacheToken=r,this.addListener=this.on,this.off=this.removeListener,!e)throw new Error("Invalid 'options' value");if(this.options=e,this.options.logger.debug("Initializing ConfigCatClient. Options: "+JSON.stringify(this.options)),!n)throw new Error("Invalid 'configCatKernel' value");if(!n.configFetcher)throw new Error("Invalid 'configCatKernel.configFetcher' value");this.hooks=e.yieldHooks(),e.defaultUser&&this.setDefaultUser(e.defaultUser),this.evaluator=new ur(e.logger),(null===(i=e.flagOverrides)||void 0===i?void 0:i.behaviour)!==W.LocalOnly?this.configService=e instanceof wt?new h(n.configFetcher,e):e instanceof It?new er(n.configFetcher,e):e instanceof Ct?new Qt(n.configFetcher,e):xe(new Error("Invalid 'options' value")):this.hooks.emit("clientReady",_.HasLocalOverrideFlagDataOnly),this.suppressFinalize=He(this,{sdkKey:e.sdkKey,cacheToken:r,configService:this.configService,logger:e.logger})}return Object.defineProperty(t,"instanceCache",{get:function(){return Ie},enumerable:!1,configurable:!0}),t.get=function(e,n,r,i){var o,s="Invalid 'sdkKey' value";if(!e)throw new Error(s);var u=new(n===Q.AutoPoll?wt:n===Q.ManualPoll?It:n===Q.LazyLoad?Ct:xe(new Error("Invalid 'pollingMode' value")))(e,i.sdkType,i.sdkVersion,r,i.defaultCacheFactory,i.eventEmitterFactory);if((null===(o=u.flagOverrides)||void 0===o?void 0:o.behaviour)!==W.LocalOnly&&!function gr(t,e){var n="configcat-proxy/";if(e&&t.length>n.length&&0===t.lastIndexOf(n,0))return!0;var r=t.split("/");switch(r.length){case 2:return 22===r[0].length&&22===r[1].length;case 3:return"configcat-sdk-1"===r[0]&&22===r[1].length&&22===r[2].length;default:return!1}}(e,u.baseUrlOverriden))throw new Error(s);var l=Ie.getOrCreate(u,i),y=l[0];return l[1]&&r&&u.logger.clientIsAlreadyCreated(e),y},t.finalize=function(e){var n;null===(n=e.logger)||void 0===n||n.debug("finalize() called"),e.cacheToken&&Ie.remove(e.sdkKey,e.cacheToken),t.close(e.configService,e.logger)},t.close=function(e,n,r){n?.debug("close() called"),r?.tryDisconnect(),e?.dispose()},t.prototype.dispose=function(){var e=this.options;e.logger.debug("dispose() called"),this.cacheToken&&Ie.remove(e.sdkKey,this.cacheToken),t.close(this.configService,e.logger,this.hooks),this.suppressFinalize()},t.disposeAll=function(){for(var n,r=0,i=Ie.clear();r<i.length;r++){var o=i[r];try{t.close(o.configService,o.options.logger,o.hooks),o.suppressFinalize()}catch(s){n??(n=[]),n.push(s)}}if(n)throw typeof AggregateError<"u"?new AggregateError(n):n.pop()},t.prototype.getValueAsync=function(e,n,r){return(0,m.mG)(this,void 0,void 0,function(){var i,o,s,u,l;return(0,m.Jh)(this,function(y){switch(y.label){case 0:this.options.logger.debug("getValueAsync() called."),Ve(e),Be(n),s=null,r??(r=this.defaultUser),y.label=1;case 1:return y.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return l=y.sent(),o=qe(this.evaluator,l[0],e,n,r,s=l[1],this.options.logger),i=o.value,[3,4];case 3:return u=y.sent(),this.options.logger.settingEvaluationErrorSingle("getValueAsync",e,"defaultValue",n,u),o=le(e,n,te(s),r,X(u),u),i=n,[3,4];case 4:return this.hooks.emit("flagEvaluated",o),[2,i]}})})},t.prototype.getValueDetailsAsync=function(e,n,r){return(0,m.mG)(this,void 0,void 0,function(){var i,o,a,u;return(0,m.Jh)(this,function(l){switch(l.label){case 0:this.options.logger.debug("getValueDetailsAsync() called."),Ve(e),Be(n),o=null,r??(r=this.defaultUser),l.label=1;case 1:return l.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return u=l.sent(),i=qe(this.evaluator,u[0],e,n,r,o=u[1],this.options.logger),[3,4];case 3:return a=l.sent(),this.options.logger.settingEvaluationErrorSingle("getValueDetailsAsync",e,"defaultValue",n,a),i=le(e,n,te(o),r,X(a),a),[3,4];case 4:return this.hooks.emit("flagEvaluated",i),[2,i]}})})},t.prototype.getAllKeysAsync=function(){return(0,m.mG)(this,void 0,void 0,function(){var e,n,r;return(0,m.Jh)(this,function(i){switch(i.label){case 0:this.options.logger.debug("getAllKeysAsync() called."),e="empty array",i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return ot(n=i.sent()[0],this.options.logger,e)?[2,Object.keys(n)]:[2,[]];case 3:return r=i.sent(),this.options.logger.settingEvaluationError("getAllKeysAsync",e,r),[2,[]];case 4:return[2]}})})},t.prototype.getAllValuesAsync=function(e){return(0,m.mG)(this,void 0,void 0,function(){var n,r,i,o,s,l,y,E,b;return(0,m.Jh)(this,function(A){switch(A.label){case 0:this.options.logger.debug("getAllValuesAsync() called."),n="empty array",e??(e=this.defaultUser),A.label=1;case 1:return A.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return s=A.sent(),b=Wt(this.evaluator,s[0],e,s[1],this.options.logger,n),o=b[1],r=(i=b[0]).map(function(w){return new pe(w.key,w.value)}),[3,4];case 3:return l=A.sent(),this.options.logger.settingEvaluationError("getAllValuesAsync",n,l),[2,[]];case 4:for(o?.length&&this.options.logger.settingEvaluationError("getAllValuesAsync","evaluation result",typeof AggregateError<"u"?new AggregateError(o):o.pop()),y=0,E=i;y<E.length;y++)this.hooks.emit("flagEvaluated",E[y]);return[2,r]}})})},t.prototype.getAllValueDetailsAsync=function(e){return(0,m.mG)(this,void 0,void 0,function(){var n,r,i,o,u,l,y,O;return(0,m.Jh)(this,function(b){switch(b.label){case 0:this.options.logger.debug("getAllValueDetailsAsync() called."),n="empty array",e??(e=this.defaultUser),b.label=1;case 1:return b.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return o=b.sent(),O=Wt(this.evaluator,o[0],e,o[1],this.options.logger,n),r=O[0],i=O[1],[3,4];case 3:return u=b.sent(),this.options.logger.settingEvaluationError("getAllValueDetailsAsync",n,u),[2,[]];case 4:for(i?.length&&this.options.logger.settingEvaluationError("getAllValueDetailsAsync","evaluation result",typeof AggregateError<"u"?new AggregateError(i):i.pop()),l=0,y=r;l<y.length;l++)this.hooks.emit("flagEvaluated",y[l]);return[2,r]}})})},t.prototype.getKeyAndValueAsync=function(e){return(0,m.mG)(this,void 0,void 0,function(){var n,r,i,o,s,a,u,l,y,E,O,b,A,w;return(0,m.Jh)(this,function(R){switch(R.label){case 0:this.options.logger.debug("getKeyAndValueAsync() called."),n="null",R.label=1;case 1:return R.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:if(!ot(r=R.sent()[0],this.options.logger,n))return[2,null];for(i=0,o=Object.entries(r);i<o.length;i++){if(a=(s=o[i])[0],e===(u=s[1]).variationId)return[2,new pe(a,Ge(u.value))];if((l=r[a].targetingRules)&&l.length>0)for(b=0;b<l.length;b++)if(he(y=l[b].then)){for(E=0;E<y.length;E++)if(e===(A=y[E]).variationId)return[2,new pe(a,Ge(A.value))]}else if(e===y.variationId)return[2,new pe(a,Ge(y.value))];if((O=r[a].percentageOptions)&&O.length>0)for(b=0;b<O.length;b++)if(e===(A=O[b]).variationId)return[2,new pe(a,Ge(A.value))]}return this.options.logger.settingForVariationIdIsNotPresent(e),[3,4];case 3:return w=R.sent(),this.options.logger.settingEvaluationError("getKeyAndValueAsync",n,w),[3,4];case 4:return[2,null]}})})},t.prototype.forceRefreshAsync=function(){return(0,m.mG)(this,void 0,void 0,function(){var n;return(0,m.Jh)(this,function(r){switch(r.label){case 0:if(this.options.logger.debug("forceRefreshAsync() called."),!this.configService)return[3,5];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.configService.refreshConfigAsync()];case 2:return[2,r.sent()[0]];case 3:return n=r.sent(),this.options.logger.forceRefreshError("forceRefreshAsync",n),[2,re.failure(X(n),n)];case 4:return[3,6];case 5:return[2,re.failure("Client is configured to use the LocalOnly override behavior, which prevents making HTTP requests.")];case 6:return[2]}})})},t.prototype.setDefaultUser=function(e){this.defaultUser=e},t.prototype.clearDefaultUser=function(){this.defaultUser=void 0},Object.defineProperty(t.prototype,"isOffline",{get:function(){var e,n;return null===(n=null===(e=this.configService)||void 0===e?void 0:e.isOffline)||void 0===n||n},enumerable:!1,configurable:!0}),t.prototype.setOnline=function(){this.configService?this.configService.setOnline():this.options.logger.configServiceMethodHasNoEffectDueToOverrideBehavior(W[W.LocalOnly],"setOnline")},t.prototype.setOffline=function(){var e;null===(e=this.configService)||void 0===e||e.setOffline()},t.prototype.waitForReady=function(){var e=this.configService;return e?e.readyPromise:Promise.resolve(_.HasLocalOverrideFlagDataOnly)},t.prototype.snapshot=function(){var e,n,r,o,a,u,i=this,s=function(){var E=i.options.cache.getInMemory();return[E.isEmpty?null:E.config.settings,E]},l=null===(o=this.options)||void 0===o?void 0:o.flagOverrides;if(l){var y=l.dataSource.getOverridesSync();switch(l.behaviour){case W.LocalOnly:return new We(y,null,this);case W.LocalOverRemote:return u=(e=s())[1],new We((0,m.pi)((0,m.pi)({},(a=e[0])??{}),y),u,this);case W.RemoteOverLocal:return a=(n=s())[0],u=n[1],new We((0,m.pi)((0,m.pi)({},y),a??{}),u,this)}}return r=s(),new We(a=r[0],u=r[1],this)},t.prototype.getSettingsAsync=function(){var e;return(0,m.mG)(this,void 0,void 0,function(){var n,r,i,o,s,u,l,y=this;return(0,m.Jh)(this,function(E){switch(E.label){case 0:return this.options.logger.debug("getSettingsAsync() called."),n=function(){return(0,m.mG)(y,void 0,void 0,function(){var O;return(0,m.Jh)(this,function(A){switch(A.label){case 0:return[4,this.configService.getConfig()];case 1:return[2,[(O=A.sent()).isEmpty?null:O.config.settings,O]]}})})},(r=null===(e=this.options)||void 0===e?void 0:e.flagOverrides)?(i=void 0,o=void 0,[4,r.dataSource.getOverrides()]):[3,7];case 1:switch(s=E.sent(),r.behaviour){case W.LocalOnly:return[3,2];case W.LocalOverRemote:return[3,3];case W.RemoteOverLocal:return[3,5]}return[3,7];case 2:return[2,[s,null]];case 3:case 5:case 7:return[4,n()];case 4:return u=E.sent(),o=u[1],[2,[(0,m.pi)((0,m.pi)({},(i=u[0])??{}),s),o]];case 6:return l=E.sent(),i=l[0],o=l[1],[2,[(0,m.pi)((0,m.pi)({},s),i??{}),o]];case 8:return[2,E.sent()]}})})},t.prototype.on=function(e,n){return this.hooks.on(e,n),this},t.prototype.once=function(e,n){return this.hooks.once(e,n),this},t.prototype.removeListener=function(e,n){return this.hooks.removeListener(e,n),this},t.prototype.removeAllListeners=function(e){return this.hooks.removeAllListeners(e),this},t.prototype.listeners=function(e){return this.hooks.listeners(e)},t.prototype.listenerCount=function(e){return this.hooks.listenerCount(e)},t.prototype.eventNames=function(){return this.hooks.eventNames()},t}(),We=function(){function t(e,n,r){this.mergedSettings=e,this.remoteConfig=n,this.defaultUser=r.defaultUser,this.evaluator=r.evaluator,this.options=r.options,this.cacheState=n?r.configService.getCacheState(n):_.HasLocalOverrideFlagDataOnly}return Object.defineProperty(t.prototype,"fetchedConfig",{get:function(){var e=this.remoteConfig;return e&&!e.isEmpty?e.config:null},enumerable:!1,configurable:!0}),t.prototype.getAllKeys=function(){return this.mergedSettings?Object.keys(this.mergedSettings):[]},t.prototype.getValue=function(e,n,r){var i,o;this.options.logger.debug("Snapshot.getValue() called."),Ve(e),Be(n),r??(r=this.defaultUser);try{i=(o=qe(this.evaluator,this.mergedSettings,e,n,r,this.remoteConfig,this.options.logger)).value}catch(s){this.options.logger.settingEvaluationErrorSingle("Snapshot.getValue",e,"defaultValue",n,s),o=le(e,n,te(this.remoteConfig),r,X(s),s),i=n}return this.options.hooks.emit("flagEvaluated",o),i},t.prototype.getValueDetails=function(e,n,r){var i;this.options.logger.debug("Snapshot.getValueDetails() called."),Ve(e),Be(n),r??(r=this.defaultUser);try{i=qe(this.evaluator,this.mergedSettings,e,n,r,this.remoteConfig,this.options.logger)}catch(o){this.options.logger.settingEvaluationErrorSingle("Snapshot.getValueDetails",e,"defaultValue",n,o),i=le(e,n,te(this.remoteConfig),r,X(o),o)}return this.options.hooks.emit("flagEvaluated",i),i},t}(),pe=function t(e,n){this.settingKey=e,this.settingValue=n};function Ve(t){if(!t)throw new Error("Invalid 'key' value")}function Be(t){if(null!=t&&!we(t))throw new TypeError("The default value must be boolean, number, string, null or undefined.")}function Ge(t){return we(t)?t:st(t)}var He=function(t,e){if(typeof FinalizationRegistry<"u"){var n=new FinalizationRegistry(function(r){return je.finalize(r)});He=function(r,i){var o={};return n.register(r,i,o),function(){return n.unregister(o)}}}else He=function(){return function(){}};return He(t,e)};function pr(){je.disposeAll()}function dr(t,e){return new I(t,e)}function yr(t,e,n){return new Xt(new $t(t,n),e)}!function Kt(){typeof Object.values>"u"&&(Object.values=zt),typeof Object.entries>"u"&&(Object.entries=Yt),typeof Object.fromEntries>"u"&&(Object.fromEntries=Zt)}();var mr=function(){function t(e){this.storage=e}return t.setup=function(e,n){var r=(n??Er)();return r&&(e.defaultCacheFactory=function(i){return new p(new t(r),i.logger)}),e},t.prototype.set=function(e,n){this.storage.setItem(e,function Or(t){return t=(t=encodeURIComponent(t)).replace(/%([0-9A-F]{2})/g,function(e,n){return String.fromCharCode(parseInt(n,16))}),btoa(t)}(n))},t.prototype.get=function(e){var n=this.storage.getItem(e);if(n)return function br(t){return t=(t=atob(t)).replace(/[%\x80-\xFF]/g,function(e){return"%"+e.charCodeAt(0).toString(16)}),decodeURIComponent(t)}(n)},t}();function Er(){var t="__configcat_localStorage_test";try{var e=window.localStorage;e.setItem(t,t);var n=void 0;try{n=e.getItem(t)}finally{e.removeItem(t)}if(n===t)return e}catch{}return null}var Sr=function(){function t(){}return t.prototype.handleStateChange=function(e,n,r){var i;try{if(4===e.readyState){var o=e.status,s=e.statusText;200===o?n({statusCode:o,reasonPhrase:s,eTag:null!==(i=e.getResponseHeader("ETag"))&&void 0!==i?i:void 0,body:e.responseText}):o&&n({statusCode:o,reasonPhrase:s})}}catch(u){r(u)}},t.prototype.fetchLogic=function(e,n){var r=this;return new Promise(function(i,o){try{e.logger.debug("HttpConfigFetcher.fetchLogic() called.");var s=new XMLHttpRequest;s.onreadystatechange=function(){return r.handleStateChange(s,i,o)},s.ontimeout=function(){return o(new se("timeout",e.requestTimeoutMs))},s.onabort=function(){return o(new se("abort"))},s.onerror=function(){return o(new se("failure"))};var a=e.getUrl();n&&(a+="&ccetag="+encodeURIComponent(n)),s.open("GET",a,!0),s.timeout=e.requestTimeoutMs,s.send(null)}catch(u){o(u)}})},t}();const Ar="9.5.1";function Vt(t,e,n){return function vr(t,e,n,r){return je.get(t,e,n,r)}(t,e??Q.AutoPoll,n,mr.setup({configFetcher:new Sr,sdkType:"ConfigCat-JS",sdkVersion:Ar}))}function Tr(t,e){return Vt(t,Q.AutoPoll,e)}},97582:(Ht,Ce,de)=>{de.d(Ce,{FC:()=>re,Jh:()=>Le,KL:()=>x,ZT:()=>H,_T:()=>se,cy:()=>fe,ev:()=>Pe,gn:()=>Y,mG:()=>Ne,pi:()=>V,pr:()=>De,qq:()=>$});var m=function(c,h){return(m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(v,p){v.__proto__=p}||function(v,p){for(var g in p)Object.prototype.hasOwnProperty.call(p,g)&&(v[g]=p[g])})(c,h)};function H(c,h){if("function"!=typeof h&&null!==h)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");function v(){this.constructor=c}m(c,h),c.prototype=null===h?Object.create(h):(v.prototype=h.prototype,new v)}var V=function(){return V=Object.assign||function(h){for(var v,p=1,g=arguments.length;p<g;p++)for(var d in v=arguments[p])Object.prototype.hasOwnProperty.call(v,d)&&(h[d]=v[d]);return h},V.apply(this,arguments)};function se(c,h){var v={};for(var p in c)Object.prototype.hasOwnProperty.call(c,p)&&h.indexOf(p)<0&&(v[p]=c[p]);if(null!=c&&"function"==typeof Object.getOwnPropertySymbols){var g=0;for(p=Object.getOwnPropertySymbols(c);g<p.length;g++)h.indexOf(p[g])<0&&Object.prototype.propertyIsEnumerable.call(c,p[g])&&(v[p[g]]=c[p[g]])}return v}function Y(c,h,v,p){var S,g=arguments.length,d=g<3?h:null===p?p=Object.getOwnPropertyDescriptor(h,v):p;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)d=Reflect.decorate(c,h,v,p);else for(var I=c.length-1;I>=0;I--)(S=c[I])&&(d=(g<3?S(d):g>3?S(h,v,d):S(h,v))||d);return g>3&&d&&Object.defineProperty(h,v,d),d}function Ne(c,h,v,p){return new(v||(v=Promise))(function(d,S){function I(F){try{T(p.next(F))}catch(D){S(D)}}function M(F){try{T(p.throw(F))}catch(D){S(D)}}function T(F){F.done?d(F.value):function g(d){return d instanceof v?d:new v(function(S){S(d)})}(F.value).then(I,M)}T((p=p.apply(c,h||[])).next())})}function Le(c,h){var p,g,d,v={label:0,sent:function(){if(1&d[0])throw d[1];return d[1]},trys:[],ops:[]},S=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return S.next=I(0),S.throw=I(1),S.return=I(2),"function"==typeof Symbol&&(S[Symbol.iterator]=function(){return this}),S;function I(T){return function(F){return function M(T){if(p)throw new TypeError("Generator is already executing.");for(;S&&(S=0,T[0]&&(v=0)),v;)try{if(p=1,g&&(d=2&T[0]?g.return:T[0]?g.throw||((d=g.return)&&d.call(g),0):g.next)&&!(d=d.call(g,T[1])).done)return d;switch(g=0,d&&(T=[2&T[0],d.value]),T[0]){case 0:case 1:d=T;break;case 4:return v.label++,{value:T[1],done:!1};case 5:v.label++,g=T[1],T=[0];continue;case 7:T=v.ops.pop(),v.trys.pop();continue;default:if(!(d=(d=v.trys).length>0&&d[d.length-1])&&(6===T[0]||2===T[0])){v=0;continue}if(3===T[0]&&(!d||T[1]>d[0]&&T[1]<d[3])){v.label=T[1];break}if(6===T[0]&&v.label<d[1]){v.label=d[1],d=T;break}if(d&&v.label<d[2]){v.label=d[2],v.ops.push(T);break}d[2]&&v.ops.pop(),v.trys.pop();continue}T=h.call(c,v)}catch(F){T=[6,F],g=0}finally{p=d=0}if(5&T[0])throw T[1];return{value:T[0]?T[1]:void 0,done:!0}}([T,F])}}}function De(){for(var c=0,h=0,v=arguments.length;h<v;h++)c+=arguments[h].length;var p=Array(c),g=0;for(h=0;h<v;h++)for(var d=arguments[h],S=0,I=d.length;S<I;S++,g++)p[g]=d[S];return p}function Pe(c,h,v){if(v||2===arguments.length)for(var d,p=0,g=h.length;p<g;p++)(d||!(p in h))&&(d||(d=Array.prototype.slice.call(h,0,p)),d[p]=h[p]);return c.concat(d||Array.prototype.slice.call(h))}function $(c){return this instanceof $?(this.v=c,this):new $(c)}function re(c,h,v){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var g,p=v.apply(c,h||[]),d=[];return g=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),I("next"),I("throw"),I("return",function S(N){return function(U){return Promise.resolve(U).then(N,D)}}),g[Symbol.asyncIterator]=function(){return this},g;function I(N,U){p[N]&&(g[N]=function(j){return new Promise(function(G,z){d.push([N,j,G,z])>1||M(N,j)})},U&&(g[N]=U(g[N])))}function M(N,U){try{!function T(N){N.value instanceof $?Promise.resolve(N.value.v).then(F,D):ne(d[0][2],N)}(p[N](U))}catch(j){ne(d[0][3],j)}}function F(N){M("next",N)}function D(N){M("throw",N)}function ne(N,U){N(U),d.shift(),d.length&&M(d[0][0],d[0][1])}}function x(c){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var v,h=c[Symbol.asyncIterator];return h?h.call(c):(c=function ye(c){var h="function"==typeof Symbol&&Symbol.iterator,v=h&&c[h],p=0;if(v)return v.call(c);if(c&&"number"==typeof c.length)return{next:function(){return c&&p>=c.length&&(c=void 0),{value:c&&c[p++],done:!c}}};throw new TypeError(h?"Object is not iterable.":"Symbol.iterator is not defined.")}(c),v={},p("next"),p("throw"),p("return"),v[Symbol.asyncIterator]=function(){return this},v);function p(d){v[d]=c[d]&&function(S){return new Promise(function(I,M){!function g(d,S,I,M){Promise.resolve(M).then(function(T){d({value:T,done:I})},S)}(I,M,(S=c[d](S)).done,S.value)})}}}function fe(c,h){return Object.defineProperty?Object.defineProperty(c,"raw",{value:h}):c.raw=h,c}"function"==typeof SuppressedError&&SuppressedError}}]);