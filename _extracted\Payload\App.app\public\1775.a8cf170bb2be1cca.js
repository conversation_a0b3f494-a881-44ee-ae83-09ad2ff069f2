(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1775],{93252:(W,b,o)=>{o.d(b,{$x:()=>p,KF:()=>l,Xh:()=>a,pb:()=>h});var g=o(53113);class p{constructor(d,u,f){this.documentCustomer=d,this.channel=u,this.owner=f}}class a{constructor(d,u,f,m){this.source=d,this.amount=u,this.currencyCode=f,this.beneficiary=m}}class l{constructor(d){this.value=d}}class h extends g.LN{constructor(d,u,f){super(d,u),this.otp=f}}},99224:(W,b,o)=>{o.d(b,{M:()=>M,i:()=>j});var g=o(15861),p=o(87956),a=o(98699),l=o(93252);function h(n){return new l.Xh(n.source,n.amount,n.currencyCode,n.beneficiary)}var C=o(71776),d=o(39904),u=o(87903),f=o(42168),m=o(84757),e=o(99877);let P=(()=>{class n{constructor(t){this.http=t}send(t){const r=function A(n){return{accId:n.source.id,amt:n.amount,curCode:n.currencyCode,channel:n.beneficiary.channel.type,documentNumber:n.beneficiary.documentCustomer.number,documentType:n.beneficiary.documentCustomer.type.code}}(t);return(0,f.firstValueFrom)(this.http.post(d.bV.TRANSACTIONS.QUICK_WITHDRAWAL,r).pipe((0,m.map)(i=>{const{type:s}=(0,u.l1)(i,"SUCCESS");return new l.pb(s,i.otpInfo?.validity.validityPeriodInfo.desc||"",new l.KF(i.otpInfo?.otpValue))}))).catch(i=>{const{message:s,type:w}=(0,u.rU)(i);return new l.pb(w,s)})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275inject(C.HttpClient))},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var S=o(20691);let I=(()=>{class n extends S.Store{constructor(t){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=t,this.eventBusService.subscribes(d.PU,()=>{this.reset()})}setSource(t,r=!1){this.reduce(i=>({...i,source:t,fromCustomer:r}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setBeneficiary(t){this.reduce(r=>({...r,beneficiary:t}))}selectForBeneficiary(){return this.select(({beneficiary:t,confirmation:r})=>({beneficiary:t,confirmation:r}))}setAmount(t){this.reduce(r=>({...r,amount:t,confirmation:!0,currencyCode:"COP"}))}selectForAmount(){return this.select(({amount:t,confirmation:r,source:i})=>({amount:t,confirmation:r,source:i}))}getCurrencyCode(){return this.select(({currencyCode:t})=>t)}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275inject(p.Yd))},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),M=(()=>{class n{constructor(t,r,i){this.eventBusService=t,this.repository=r,this.store=i}setSource(t){try{return a.Either.success(this.store.setSource(t))}catch({message:r}){return a.Either.failure({message:r})}}setBeneficiary(t){try{return a.Either.success(this.store.setBeneficiary(t))}catch({message:r}){return a.Either.failure({message:r})}}setAmount(t){try{return a.Either.success(this.store.setAmount(t))}catch({message:r}){return a.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),r=this.store.getSource();return this.store.reset(),a.Either.success({fromCustomer:t,source:r})}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,g.Z)(function*(){const r=h(t.store.currentState),i=yield t.execute(r);return t.eventBusService.emit(i.channel),a.Either.success({withdrawal:r,status:i})})()}execute(t){try{return this.repository.send(t)}catch({message:r}){return Promise.resolve(l.pb.error(r))}}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275inject(p.Yd),e.\u0275\u0275inject(P),e.\u0275\u0275inject(I))},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var Q=o(78506);let j=(()=>{class n{constructor(t,r,i){this.products=t,this.store=r,this.session=i}source(){var t=this;return(0,g.Z)(function*(){try{const r=t.store.itIsConfirmation(),i=yield t.requestAccounts();return a.Either.success({confirmation:r,products:i})}catch({message:r}){return a.Either.failure({message:r})}})()}beneficiary(t){var r=this;return(0,g.Z)(function*(){try{const i=yield r.session.customer(),s=yield r.requestAccounts(),w=r.requestAccount(s,t),v=r.store.selectForBeneficiary(),c=r.store.itIsConfirmation();return a.Either.success({...v,confirmation:c,customer:i,products:s,source:w})}catch({message:i}){return a.Either.failure({message:i})}})()}amount(){try{return a.Either.success(this.store.selectForAmount())}catch({message:t}){return a.Either.failure({message:t})}}confirmation(){try{const t=h(this.store.currentState);return a.Either.success({withdrawal:t})}catch({message:t}){return a.Either.failure({message:t})}}requestAccounts(){return this.products.requestAccountsForTransfer()}requestAccount(t,r){let i=this.store.getSource();return!i&&r&&(i=t.find(({id:s})=>r===s),this.store.setSource(i,!0)),i}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275inject(p.hM),e.\u0275\u0275inject(I),e.\u0275\u0275inject(Q._I))},n.\u0275prov=e.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},20534:(W,b,o)=>{o.d(b,{v:()=>d});var g=o(30263),p=o(39904),a=o(95437),l=o(99224),h=o(99877);let d=(()=>{class u{constructor(m,e,A){this.modalConfirmation=m,this.mboProvider=e,this.managerWithdrawal=A}execute(m=!0){m?this.modalConfirmation.execute({title:"Cancelar retiro",message:"\xbfEstas seguro que deseas cancelar el retiro actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerWithdrawal.reset().when({success:({fromCustomer:m,source:e})=>{m?this.mboProvider.navigation.back(p.Z6.CUSTOMER.PRODUCTS.INFO,{productId:e.id}):this.mboProvider.navigation.back(p.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(p.Z6.TRANSFERS.HOME)}})}}return u.\u0275fac=function(m){return new(m||u)(h.\u0275\u0275inject(g.$e),h.\u0275\u0275inject(a.ZL),h.\u0275\u0275inject(l.M))},u.\u0275prov=h.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},51775:(W,b,o)=>{o.r(b),o.d(b,{MboQuickWithdrawalAmountPageModule:()=>i});var g=o(17007),p=o(78007),a=o(30263),l=o(24495),h=o(39904),C=o(87903),d=o(95437),u=o(57544),f=o(99224),m=o(20534),e=o(99877),A=o(83413),P=o(35641),S=o(48774),I=o(45542);const M=h.Z6.TRANSACTIONS.QUICK_WITHDRAWAL,y=[1e4,3e4],t=s=>s&&y.includes(+s)?{id:"amountInvalid",message:"El valor establecido no esta permitido para retirar"}:null;let r=(()=>{class s{constructor(v,c,E,k){this.mboProvider=v,this.requestConfiguration=c,this.managerQuickWithdrawal=E,this.cancelProvider=k,this.confirmation=!1,this.backAction={id:"btn_quick-withdrawal-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(M.BENEFICIARY)}},this.cancelAction={id:"btn_quick-withdrawal-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new u.FormControl}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerQuickWithdrawal.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(M.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({confirmation:v,source:c,value:E})=>{E&&this.amountControl.setValue(E),this.source=c,this.confirmation=v;const k=[l.C1,l.LU,(0,l.f6)(1e4),t,(0,l.Go)(2e4),(0,l.VV)(2e6)];(0,C.VN)(c)&&k.push((0,l.vB)(c.amount)),this.amountControl.setValidators(k)}})}}return s.\u0275fac=function(v){return new(v||s)(e.\u0275\u0275directiveInject(d.ZL),e.\u0275\u0275directiveInject(f.i),e.\u0275\u0275directiveInject(f.M),e.\u0275\u0275directiveInject(m.v))},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-quick-withdrawal-amount-page"]],decls:12,vars:9,consts:[[1,"mbo-quick-withdrawal-amount-page__content"],[1,"mbo-quick-withdrawal-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-quick-withdrawal-amount-page__body"],[1,"mbo-quick-withdrawal-amount-page__message","subtitle2-medium"],["elementId","txt_quick-withdrawal-amount_value","label","Valor a retirar","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-quick-withdrawal-amount-page__footer"],["id","btn_quick-withdrawal-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(v,c){1&v&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," \xbfCuanto dinero deseas retirar? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return c.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Continuar"),e.\u0275\u0275elementEnd()()()),2&v&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",c.backAction)("rightAction",c.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("formControl",c.amountControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("color",null==c.source?null:c.source.color)("icon",null==c.source?null:c.source.logo)("title",null==c.source?null:c.source.nickname)("number",null==c.source?null:c.source.publicNumber)("amount",null==c.source?null:c.source.amount),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",c.amountControl.invalid))},dependencies:[A.D,P.d,S.J,I.P],styles:["/*!\n * MBO QuickWithdrawalAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ene/2023\n * Updated: 04/Ene/2024\n*/mbo-quick-withdrawal-amount-page{--pvt-summary-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-quick-withdrawal-amount-page .mbo-quick-withdrawal-amount-page__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-quick-withdrawal-amount-page .mbo-quick-withdrawal-amount-page__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-quick-withdrawal-amount-page .mbo-quick-withdrawal-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-summary-margin-top)}mbo-quick-withdrawal-amount-page .mbo-quick-withdrawal-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-quick-withdrawal-amount-page .mbo-quick-withdrawal-amount-page__footer button{width:100%}@media screen and (max-width: 320px){mbo-quick-withdrawal-amount-page{--pvt-summary-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),s})(),i=(()=>{class s{}return s.\u0275fac=function(v){return new(v||s)},s.\u0275mod=e.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=e.\u0275\u0275defineInjector({imports:[g.CommonModule,p.RouterModule.forChild([{path:"",component:r}]),a.D1,a.dH,a.Jx,a.P8]}),s})()}}]);