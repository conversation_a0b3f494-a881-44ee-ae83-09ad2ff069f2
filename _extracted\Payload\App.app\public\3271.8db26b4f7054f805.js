(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3271],{77660:(V,E,o)=>{o.d(E,{M:()=>i,w:()=>c});var i=(()=>{return(e=i||(i={})).INIT="Init",e.END_PROCESS="EndProcess",e.BIOMETRICS_VALIDATION="BiometricsValidation",e.OTP_INICIAL="OtpInicial",e.SECURE_QUESTION="SecureQuestion",e.PASSWORD="Password",i;var e})(),c=(()=>((c||(c={})).FORGOT_PASSWORD="forgotPassword",c))()},24613:(V,E,o)=>{o.d(E,{y:()=>G,n:()=>J});var i=o(15861),c=o(39904),e=o(87956),t=o(53113),n=o(13973),a=o(98699),m=o(77660);class u extends a.PartialSealed{static error(U){return new u("error",U)}static finish(U=""){return new u("finish",U)}static next(U){return new u("next",U)}}var s=o(71776),p=o(42168);class r{constructor(U,M,P,F,Z,j,C){this.success=U,this.error=M,this.value=P,this.executionArn=F,this.taskToken=Z,this.bodyStep=j,this.errorMessage=C}get message(){return this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP01)."}static error(){return new r(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP02).")}}function y(B,U=!1,M=!0){const P=B.body;let F;return P&&(F=Object.keys(P).find(j=>void 0!==P[j])),new r(M,U,F,B.executionArn,B.taskToken,B.body,B.error)}var h=o(51176),g=o(70658),d=o(99877);const b=g.N.cognitoForgotPassword.url;let f=(()=>{class B{constructor(M,P){this.http=M,this.forgotPasswordStore=P}cognitoForgotpassword(){const M=(new s.HttpParams).set("grant_type","client_credentials").set("client_id",g.N.cognitoForgotPassword.clientId).set("client_secret",g.N.cognitoForgotPassword.clientSecret),P=new s.HttpHeaders({"Content-Type":"application/x-www-form-urlencoded"});return(0,p.firstValueFrom)(this.http.post(b,M.toString(),{headers:P}).pipe((0,p.map)(F=>(this.forgotPasswordStore.setCognitoToken(F.access_token),F))))}requestForgotPassword(M,P){var F=this;return(0,i.Z)(function*(){const Z=new s.HttpHeaders({Authorization:P});return(0,p.firstValueFrom)(F.http.post(c.bV.FORGOT_PASSWORD,{...M},{headers:Z,observe:"response"}).pipe((0,p.map)(j=>{if(206===j.status&&j.body)throw new Error;return y(j.body)}),(0,p.tap)(({bodyStep:j})=>{j&&(F.forgotPasswordStore.setBodystep(j),j.SecureQuestion&&F.forgotPasswordStore.setSecureData(j.SecureQuestion))}),(0,p.catchError)(j=>{throw y(j,!0,!1),j}))).catch(j=>j?y(j,!0,!1):r.error())})()}}return B.\u0275fac=function(M){return new(M||B)(d.\u0275\u0275inject(s.HttpClient),d.\u0275\u0275inject(h.N))},B.\u0275prov=d.\u0275\u0275defineInjectable({token:B,factory:B.\u0275fac,providedIn:"root"}),B})();var I=o(81536),S=o(95437);let x=(()=>{class B{constructor(M,P,F,Z,j,C){this.forgotPasswordRepository=M,this.deviceService=P,this.forgotPasswordStore=F,this.cryptoService=Z,this.publicKeyRepository=j,this.mboProvider=C}token(){var M=this;return(0,i.Z)(function*(){try{return yield M.forgotPasswordRepository.cognitoForgotpassword()}catch(P){throw M.mboProvider.toast.error("Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (cognito)"),P}})()}init(M){var P=this;return(0,i.Z)(function*(){return Promise.all([P.deviceService.getFingerprint(),P.deviceService.getInfo()]).then(([F,Z])=>P.service({documentNumber:M.number,documentType:M.type,ipAddress:"0.0.0.0",deviceSerial:`${Z.uuid}-${Z.model}`,deviceFingerPrint:F})).then(F=>(P.forgotPasswordStore.setDocument(M.type,M.number),P.forgotPasswordStore.setExecutionArn(F.executionArn),P.forgotPasswordStore.setTaskToken(F.taskToken),F))})()}password(M){var P=this;return(0,i.Z)(function*(){const{executionArn:F,taskToken:Z,rescue:j}=P.forgotPasswordStore.currentState,W={universalPassword:yield P.publicKeyRepository.requestEnrollment().then(A=>P.cryptoService.encodeRSA(A,M))};return j||(W.responseBiometricsValidation=!0),P.service({executionArn:F,body:W,taskToken:Z})})()}rescue(){var M=this;return(0,i.Z)(function*(){const{executionArn:P,taskToken:F,rescue:Z}=M.forgotPasswordStore.currentState,j=yield M.deviceService.getInfo();return M.service({executionArn:P,body:{responseBiometricsValidation:!Z,...Z&&{deviceOS:j.operatingSystem,deviceName:j.name}},taskToken:F}).then(C=>(M.forgotPasswordStore.setExecutionArn(C.executionArn),M.forgotPasswordStore.setTaskToken(C.taskToken),C))})()}otp(M){var P=this;return(0,i.Z)(function*(){const{executionArn:F,taskToken:Z}=P.forgotPasswordStore.currentState,j=yield P.deviceService.getInfo();return P.service({executionArn:F,body:{otpValue:(yield P.cryptoService.encodeKeyEnrollment(M)).toString(),deviceOS:j.operatingSystem,deviceName:j.name},taskToken:Z}).then(C=>(P.forgotPasswordStore.setExecutionArn(C.executionArn),P.forgotPasswordStore.setTaskToken(C.taskToken),C))})()}product(M){var P=this;return(0,i.Z)(function*(){const{executionArn:F,taskToken:Z}=P.forgotPasswordStore.currentState;return P.service({executionArn:F,body:{responseSecureQuestion:M},taskToken:Z}).then(j=>(P.forgotPasswordStore.setExecutionArn(j.executionArn),P.forgotPasswordStore.setTaskToken(j.taskToken),j))})()}service(M){const P=this.forgotPasswordStore.getCognitoToken();return this.forgotPasswordRepository.requestForgotPassword(M,P)}}return B.\u0275fac=function(M){return new(M||B)(d.\u0275\u0275inject(f),d.\u0275\u0275inject(e.U8),d.\u0275\u0275inject(h.N),d.\u0275\u0275inject(e.$I),d.\u0275\u0275inject(I.aH),d.\u0275\u0275inject(S.ZL))},B.\u0275prov=d.\u0275\u0275defineInjectable({token:B,factory:B.\u0275fac,providedIn:"root"}),B})();const{AUTHENTICATION:{FORGOT_PASSWORD:K,ERRORS:D,LOGIN:L}}=c.Z6;let G=(()=>{class B{constructor(M){this.customerService=M}welcome(){return this.customerService.request().then(M=>a.Either.success(M)).catch(({message:M})=>a.Either.failure({message:M}))}}return B.\u0275fac=function(M){return new(M||B)(d.\u0275\u0275inject(e.vZ))},B.\u0275prov=d.\u0275\u0275defineInjectable({token:B,factory:B.\u0275fac,providedIn:"root"}),B})(),J=(()=>{class B{constructor(M,P,F){this.facialBiometricsStore=M,this.forgotPasswordStore=P,this.forgotPasswordStepService=F}document(M){var P=this;return(0,i.Z)(function*(){try{yield P.forgotPasswordStepService.token();const F=yield P.forgotPasswordStepService.init(M);return P.getNextStepForgotPassword(F)}catch(F){return u.error(F)}})()}password(M){var P=this;return(0,i.Z)(function*(){try{const F=yield P.forgotPasswordStepService.password(M);return P.forgotPasswordStore.setPassword(M),P.getNextStepForgotPassword(F)}catch({message:F}){return u.error(F)}})()}rescue(){var M=this;return(0,i.Z)(function*(){M.forgotPasswordStore.setRescue(!0);try{const P=yield M.forgotPasswordStepService.rescue(),F=M.getNextStepForgotPassword(P);return new Promise((Z,j)=>{F.when({next:C=>Z(C),error:C=>j(C)})})}catch(P){return Promise.reject(u.error(P))}})()}getCustomerDocument(){const{documentNumber:M,documentType:P}=this.forgotPasswordStore.currentState;return P&&M?new t.dp(P,M):void 0}otp(M){var P=this;return(0,i.Z)(function*(){try{const F=yield P.forgotPasswordStepService.otp(M);return P.getNextStepForgotPassword(F)}catch({message:F}){return u.error(F)}})()}product(M){var P=this;return(0,i.Z)(function*(){try{const F=yield P.forgotPasswordStepService.product(M);return P.getNextStepForgotPassword(F)}catch({message:F}){return u.error(F)}})()}getNextStepForgotPassword(M){const{success:P,value:F,error:Z}=M,j=this.getStepForRedirect(F);return P&&F===m.M.END_PROCESS?(this.clearStoreState(),u.finish(j)):Z?(this.clearStoreState(),u.error(j)):P?u.next(j):void 0}getStepForRedirect(M){switch(M){case m.M.BIOMETRICS_VALIDATION:return m.M.BIOMETRICS_VALIDATION.toString();case m.M.OTP_INICIAL:return K.OTP_VERIFICATION;case m.M.SECURE_QUESTION:return K.PRODUCT_VERIFICATION;case m.M.PASSWORD:return K.PASSWORD_ASSIGNMENT;case m.M.END_PROCESS:return L;default:return D.DEFAULT_MESSAGE}}clearStoreState(){this.forgotPasswordStore.clearState(),this.facialBiometricsStore.clearState()}}return B.\u0275fac=function(M){return new(M||B)(d.\u0275\u0275inject(n.H),d.\u0275\u0275inject(h.N),d.\u0275\u0275inject(x))},B.\u0275prov=d.\u0275\u0275defineInjectable({token:B,factory:B.\u0275fac,providedIn:"root"}),B})()},51176:(V,E,o)=>{o.d(E,{N:()=>n});var i=o(20691),e=o(99877);let n=(()=>{class a extends i.Store{constructor(){super({rescue:!1,cognitoToken:null})}setDocument(u,s){this.reduce(p=>({...p,documentType:u,documentNumber:s}))}setRescue(u){this.reduce(s=>({...s,rescue:u}))}setExecutionArn(u){this.reduce(s=>({...s,executionArn:u}))}setPassword(u){this.reduce(s=>({...s,password:u}))}setBodystep(u){this.reduce(s=>({...s,body:u}))}setTaskToken(u){this.reduce(s=>({...s,taskToken:u}))}setSecureData(u){this.reduce(s=>({...s,secureData:u}))}getSecureData(){return this.select(({secureData:u})=>u)}setErrorCode(u){this.reduce(s=>({...s,errorCode:u}))}setCognitoToken(u){this.reduce(s=>({...s,cognitoToken:u}))}getExecutionArn(){return this.select(({executionArn:u})=>u)}getTaskToken(){return this.select(({taskToken:u})=>u)}getErrorCode(){return this.select(({errorCode:u})=>u)}getCognitoToken(){return this.select(({cognitoToken:u})=>u)}clearState(){this.reduce(()=>({}))}}return a.\u0275fac=function(u){return new(u||a)},a.\u0275prov=e.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},19102:(V,E,o)=>{o.d(E,{r:()=>n});var i=o(17007),e=o(99877);let n=(()=>{class a{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return a.\u0275fac=function(u){return new(u||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(u,s){1&u&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&u&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",s.src,e.\u0275\u0275sanitizeUrl))},dependencies:[i.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),a})()},52701:(V,E,o)=>{o.d(E,{q:()=>a});var i=o(17007),e=o(30263),t=o(99877);let a=(()=>{class m{}return m.\u0275fac=function(s){return new(s||m)},m.\u0275cmp=t.\u0275\u0275defineComponent({type:m,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(s,p){1&s&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),t.\u0275\u0275element(3,"bocc-icon",3),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(4,"label",4),t.\u0275\u0275text(5),t.\u0275\u0275elementEnd()()),2&s&&(t.\u0275\u0275classMap(p.classTheme),t.\u0275\u0275advance(3),t.\u0275\u0275property("icon",p.icon),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",p.label," "))},dependencies:[i.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),m})()},55648:(V,E,o)=>{o.d(E,{u:()=>p});var i=o(15861),c=o(17007),t=o(30263),n=o(78506),a=o(99877);function u(v,r){if(1&v){const l=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",2),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(l);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&v){const l=a.\u0275\u0275nextContext();a.\u0275\u0275property("prefixIcon",l.icon)("disabled",l.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate1(" ",l.label," ")}}function s(v,r){if(1&v){const l=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",3),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(l);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementEnd()}if(2&v){const l=a.\u0275\u0275nextContext();a.\u0275\u0275property("bocc-button-action",l.icon)("disabled",l.disabled)}}let p=(()=>{class v{constructor(l){this.preferences=l,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:l})=>{this.isIncognito=l||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var l=this;return(0,i.Z)(function*(){yield l.preferences.toggleIncognito()})()}}return v.\u0275fac=function(l){return new(l||v)(a.\u0275\u0275directiveInject(n.Bx))},v.\u0275cmp=a.\u0275\u0275defineComponent({type:v,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(l,y){1&l&&(a.\u0275\u0275template(0,u,3,3,"button",0),a.\u0275\u0275template(1,s,1,2,"button",1)),2&l&&(a.\u0275\u0275property("ngIf",!y.actionMode),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",y.actionMode))},dependencies:[c.CommonModule,c.NgIf,t.P8,t.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),v})()},72765:(V,E,o)=>{o.d(E,{rw:()=>i.r,qr:()=>c.q,uf:()=>e.u,Z:()=>u,t5:()=>h,$O:()=>y});var i=o(19102),c=o(52701),e=o(55648),t=o(17007),n=o(30263),a=o(99877);const m=["*"];let u=(()=>{class g{constructor(){this.disabled=!1}}return g.\u0275fac=function(b){return new(b||g)},g.\u0275cmp=a.\u0275\u0275defineComponent({type:g,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],ngContentSelectors:m,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(b,f){1&b&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275element(2,"bocc-icon",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",3),a.\u0275\u0275projection(4),a.\u0275\u0275elementEnd()()),2&b&&(a.\u0275\u0275classProp("mbo-poster__content--disabled",f.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275property("icon",f.icon))},dependencies:[t.CommonModule,n.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),g})();var s=o(33395),p=o(77279),v=o(87903),r=o(87956),l=o(25317);let y=(()=>{class g{constructor(b){this.eventBusService=b}onCopy(){this.value&&((0,v.Bn)(this.value),this.eventBusService.emit(p.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return g.\u0275fac=function(b){return new(b||g)(a.\u0275\u0275directiveInject(r.Yd))},g.\u0275cmp=a.\u0275\u0275defineComponent({type:g,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(b,f){1&b&&(a.\u0275\u0275elementStart(0,"bocc-icon",0),a.\u0275\u0275listener("click",function(){return f.onCopy()}),a.\u0275\u0275elementEnd()),2&b&&a.\u0275\u0275property("id",f.elementId)},dependencies:[t.CommonModule,n.Zl,s.kW,l.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),g})(),h=(()=>{class g{}return g.\u0275fac=function(b){return new(b||g)},g.\u0275cmp=a.\u0275\u0275defineComponent({type:g,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(b,f){1&b&&a.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&b&&(a.\u0275\u0275property("value",f.value),a.\u0275\u0275advance(1),a.\u0275\u0275property("value",f.value))},dependencies:[t.CommonModule,n.qd,y],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),g})()},79798:(V,E,o)=>{o.d(E,{Vc:()=>c.Vc,rw:()=>i.rw,k4:()=>c.k4,qr:()=>i.qr,uf:()=>i.uf,xO:()=>t.x,A6:()=>e.A,tu:()=>l,Tj:()=>y,GI:()=>C,Uy:()=>W,To:()=>F,w7:()=>j,o2:()=>c.o2,B_:()=>c.B_,fi:()=>c.fi,XH:()=>c.XH,cN:()=>c.cN,Aj:()=>c.Aj,J5:()=>c.J5,DB:()=>Y.D,NH:()=>A.N,ES:()=>N.E,Nu:()=>c.Nu,x6:()=>T.x,KI:()=>te.K,iF:()=>c.iF,u8:()=>R.u,eM:()=>w.e,ZF:()=>z.Z,wu:()=>X.w,$n:()=>ee.$,KN:()=>oe.K,cV:()=>ce.c,t5:()=>i.t5,$O:()=>i.$O,ZS:()=>se.Z,sO:()=>le.s,bL:()=>pe,zO:()=>Q.z});var i=o(72765),c=o(27302),e=o(1027),t=o(7427),a=(o(16442),o(17007)),m=o(30263),u=o(44487),s=o.n(u),p=o(13462),v=o(21498),r=o(99877);let l=(()=>{class k{}return k.\u0275fac=function(O){return new(O||k)},k.\u0275mod=r.\u0275\u0275defineNgModule({type:k}),k.\u0275inj=r.\u0275\u0275defineInjector({imports:[a.CommonModule,p.LottieModule.forRoot({player:()=>s()}),i.rw,m.P8,m.Dj,v.P]}),k})(),y=(()=>{class k{ngBoccPortal(O){this.portal=O}onSubmit(){this.portal?.close()}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementStart(3,"label"),r.\u0275\u0275text(4," \xa1Atenci\xf3n! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p"),r.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),r.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"li",4),r.\u0275\u0275text(11,"Transacciones a celulares."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"li",4),r.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"li",4),r.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(16,"p",5),r.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(18,"div",6)(19,"button",7),r.\u0275\u0275listener("click",function(){return $.onSubmit()}),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,"Continuar"),r.\u0275\u0275elementEnd()()())},dependencies:[a.CommonModule,m.Zl,m.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),k})();var h=o(7603),g=o(87956),d=o(74520),b=o(39904),f=o(87903);function S(k,H){if(1&k){const O=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",6)(1,"label",7),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"span",8),r.\u0275\u0275text(4,"Tu gerente asignado (a)"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",8),r.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"button",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(O);const q=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(q.onEmail(q.manager.email))}),r.\u0275\u0275elementStart(8,"span",10),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()()}if(2&k){const O=r.\u0275\u0275nextContext(2);r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",O.manager.name," "),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",O.manager.email," ")}}function x(k,H){if(1&k){const O=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),r.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"button",12),r.\u0275\u0275listener("click",function(q){r.\u0275\u0275restoreView(O);const re=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(re.onRetryManager(q))}),r.\u0275\u0275elementStart(4,"span"),r.\u0275\u0275text(5,"Recargar"),r.\u0275\u0275elementEnd()()()}}function K(k,H){if(1&k&&(r.\u0275\u0275elementStart(0,"div",3),r.\u0275\u0275template(1,S,10,2,"div",4),r.\u0275\u0275template(2,x,6,0,"div",5),r.\u0275\u0275elementEnd()),2&k){const O=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",O.manager),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!O.manager)}}function D(k,H){1&k&&(r.\u0275\u0275elementStart(0,"div",13),r.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),r.\u0275\u0275elementEnd()),2&k&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0))}o(29306);let L=(()=>{class k{constructor(O){this.customerService=O,this.requesting=!1}onRetryManager(O){this.customerService.requestManager(),O.stopPropagation()}onEmail(O){(0,f.Gw)(`mailto:${O}`)}onWhatsapp(){(0,f.Gw)(b.BA.WHATSAPP)}}return k.\u0275fac=function(O){return new(O||k)(r.\u0275\u0275directiveInject(g.vZ))},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,K,3,2,"div",1),r.\u0275\u0275template(2,D,5,4,"div",2),r.\u0275\u0275elementEnd()),2&O&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!$.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",$.requesting))},dependencies:[a.CommonModule,a.NgIf,m.P8,m.Dj,c.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),k})();const G={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},J={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},B={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function M(k,H){if(1&k&&(r.\u0275\u0275elementStart(0,"div",7),r.\u0275\u0275element(1,"mbo-contact-manager",8),r.\u0275\u0275elementEnd()),2&k){const O=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("manager",O.manager)("requesting",O.requesting)}}function P(k,H){if(1&k){const O=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"li",9)(1,"div",10),r.\u0275\u0275listener("click",function(q){const be=r.\u0275\u0275restoreView(O).$implicit,_e=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(_e.onOption(be,q))}),r.\u0275\u0275elementStart(2,"label",11),r.\u0275\u0275text(3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",12)(5,"div",13),r.\u0275\u0275element(6,"bocc-icon",14),r.\u0275\u0275elementEnd()()()()}if(2&k){const O=H.$implicit;r.\u0275\u0275property("id",O.id),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",O.label," "),r.\u0275\u0275advance(1),r.\u0275\u0275attribute("bocc-theme",O.boccTheme),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",O.icon)}}let F=(()=>{class k{constructor(O,$,q){this.utagService=O,this.customerStore=$,this.customerService=q,this.isManagerEnabled=!1,this.requesting=!1,this.options=[G,J,B]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:O})=>{this.isManagerEnabled=O?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager($=>{this.manager=$.manager,this.requesting=$.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(O){this.portal=O}onOption(O,$){this.utagService.link("click",O.id),this.portal?.send({action:"option",value:O}),$.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return k.\u0275fac=function(O){return new(O||k)(r.\u0275\u0275directiveInject(h.D),r.\u0275\u0275directiveInject(d.f),r.\u0275\u0275directiveInject(g.vZ))},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-contact-information"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275listener("click",function(){return $.onClose()}),r.\u0275\u0275template(1,M,2,2,"div",1),r.\u0275\u0275elementStart(2,"ul",2),r.\u0275\u0275template(3,P,7,4,"li",3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),r.\u0275\u0275listener("click",function(){return $.onClose()}),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(6,"div",6),r.\u0275\u0275listener("click",function(){return $.onClose()}),r.\u0275\u0275elementEnd()),2&O&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",$.isManagerEnabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngForOf",$.options))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,m.Zl,L],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),k})();var Z=o(95437);let j=(()=>{class k{constructor(O,$){this.floatingService=O,this.mboProvider=$,this.contactsFloating=this.floatingService.create(F),this.contactsFloating?.subscribe(({action:q,value:re})=>{"option"===q?this.dispatchOption(re):this.close()})}subscribe(O){this.subscriber=O}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(O){"PQRS"===O.action?this.mboProvider.openUrl(b.BA.PQRS):this.subscriber&&this.subscriber(O)}}return k.\u0275fac=function(O){return new(O||k)(r.\u0275\u0275inject(m.B7),r.\u0275\u0275inject(Z.ZL))},k.\u0275prov=r.\u0275\u0275defineInjectable({token:k,factory:k.\u0275fac,providedIn:"root"}),k})(),C=(()=>{class k{constructor(){this.defenderLineNumber=b._L.DEFENDER_LINE,this.defenderLinePhone=b.WB.DEFENDER_LINE}ngBoccPortal(O){}onEmail(){(0,f.Gw)("mailto:<EMAIL>")}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-contact-phones"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275element(1,"mbo-attention-lines-form"),r.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),r.\u0275\u0275element(5,"bocc-icon",4),r.\u0275\u0275elementStart(6,"span",5),r.\u0275\u0275text(7,"Defensor del consumidor financiero"),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),r.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"label",8)(13,"span"),r.\u0275\u0275text(14,"Lorena Cerchar Rosado"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(15,"bocc-badge",9),r.\u0275\u0275text(16," Suplente "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(17,"div",10),r.\u0275\u0275element(18,"bocc-icon",11),r.\u0275\u0275elementStart(19,"div",12)(20,"span",13),r.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(22,"div",10),r.\u0275\u0275element(23,"bocc-icon",14),r.\u0275\u0275elementStart(24,"div",12)(25,"a",15),r.\u0275\u0275text(26),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(27,"span",13),r.\u0275\u0275text(28," Ext. 15318 - 15311 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(29,"div",10),r.\u0275\u0275element(30,"bocc-icon",16),r.\u0275\u0275elementStart(31,"div",12)(32,"span",17),r.\u0275\u0275listener("click",function(){return $.onEmail()}),r.\u0275\u0275text(33," <EMAIL> "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(34,"div",10),r.\u0275\u0275element(35,"bocc-icon",18),r.\u0275\u0275elementStart(36,"div",12)(37,"span",13),r.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),r.\u0275\u0275elementEnd()()()()()()),2&O&&(r.\u0275\u0275advance(25),r.\u0275\u0275property("href",$.defenderLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",$.defenderLineNumber," "))},dependencies:[a.CommonModule,m.Zl,m.Oh,c.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),k})(),W=(()=>{class k{constructor(){this.whatsappNumber=b._L.WHATSAPP}ngBoccPortal(O){}onClick(){(0,f.Gw)(b.BA.WHATSAPP)}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",2)(4,"button",3),r.\u0275\u0275listener("click",function(){return $.onClick()}),r.\u0275\u0275elementStart(5,"span"),r.\u0275\u0275text(6),r.\u0275\u0275elementEnd()()()()),2&O&&(r.\u0275\u0275advance(6),r.\u0275\u0275textInterpolate($.whatsappNumber))},dependencies:[a.CommonModule,m.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),k})();var A=o(10119),T=(o(87677),o(68789)),N=o(10455),Y=o(91642),te=o(10464),R=o(75221),Q=o(88649),w=o(13043),z=o(38116),X=o(68819),ee=o(19310),oe=o(94614),ce=(o(70957),o(91248),o(4663)),se=o(13961),le=o(66709),ne=o(24495),ie=o(57544),de=o(53113);class me extends ie.FormGroup{constructor(){const H=new ie.FormControl("",[ne.zf,ne.O_,ne.Y2,(0,ne.Mv)(24)]),O=new ie.FormControl("",[ne.C1,ne.zf,ne.O_,ne.Y2,(0,ne.Mv)(24)]);super({controls:{description:O,reference:H}}),this.description=O,this.reference=H}setNote(H){this.description.setValue(H?.description),this.reference.setValue(H?.reference)}getNote(){return new de.$H(this.description.value,this.reference.value)}}function ue(k,H){if(1&k&&r.\u0275\u0275element(0,"bocc-input-box",7),2&k){const O=r.\u0275\u0275nextContext();r.\u0275\u0275property("formControl",O.formControls.reference)}}let pe=(()=>{class k{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new me}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(O){this.portal=O}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=r.\u0275\u0275defineComponent({type:k,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(O,$){1&O&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"bocc-input-box",5),r.\u0275\u0275template(7,ue,1,1,"bocc-input-box",6),r.\u0275\u0275elementEnd()()),2&O&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",$.cancelAction)("rightAction",$.saveAction),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",$.title," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("formControl",$.formControls.description),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",$.requiredReference))},dependencies:[a.CommonModule,a.NgIf,m.Jx,m.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),k})()},35324:(V,E,o)=>{o.d(E,{V:()=>s});var i=o(17007),e=o(30263),t=o(39904),n=o(87903),a=o(99877);function u(p,v){if(1&p){const r=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"a",9),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(r);const y=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(y.onWhatsapp())}),a.\u0275\u0275elementStart(1,"div",3),a.\u0275\u0275element(2,"bocc-icon",10),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",5)(4,"label",6),a.\u0275\u0275text(5," Whatsapp "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"label",7),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd()()()}if(2&p){const r=a.\u0275\u0275nextContext();a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",r.whatsappNumber," ")}}let s=(()=>{class p{constructor(){this.whatsapp=!1,this.whatsappNumber=t._L.WHATSAPP,this.nationalLineNumber=t._L.NATIONAL_LINE,this.bogotaLineNumber=t._L.BOGOTA_LINE,this.nationalLinePhone=t.WB.NATIONAL_LINE,this.bogotaLinePhone=t.WB.BOGOTA_LINE}onWhatsapp(){(0,n.Gw)(t.BA.WHATSAPP)}}return p.\u0275fac=function(r){return new(r||p)},p.\u0275cmp=a.\u0275\u0275defineComponent({type:p,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(r,l){1&r&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275template(1,u,8,1,"a",1),a.\u0275\u0275elementStart(2,"a",2)(3,"div",3),a.\u0275\u0275element(4,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(5,"div",5)(6,"label",6),a.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(8,"label",7),a.\u0275\u0275text(9),a.\u0275\u0275elementEnd()()(),a.\u0275\u0275elementStart(10,"a",8)(11,"div",3),a.\u0275\u0275element(12,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(13,"div",5)(14,"label",6),a.\u0275\u0275text(15," Bogot\xe1 "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(16,"label",7),a.\u0275\u0275text(17),a.\u0275\u0275elementEnd()()()()),2&r&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",l.whatsapp),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",l.nationalLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",l.nationalLineNumber," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",l.bogotaLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",l.bogotaLineNumber," "))},dependencies:[i.CommonModule,i.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),p})()},9593:(V,E,o)=>{o.d(E,{k:()=>u});var i=o(17007),e=o(30263),t=o(39904),n=o(95437),a=o(99877);let u=(()=>{class s{constructor(v){this.mboProvider=v,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(t.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(t.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(t.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(t.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return s.\u0275fac=function(v){return new(v||s)(a.\u0275\u0275directiveInject(n.ZL))},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(v,r){1&v&&(a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275listener("click",function(){return r.onProducts()}),a.\u0275\u0275element(3,"bocc-icon",3),a.\u0275\u0275elementStart(4,"label",4),a.\u0275\u0275text(5," Productos "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(6,"div",5),a.\u0275\u0275listener("click",function(){return r.onTransfers()}),a.\u0275\u0275element(7,"bocc-icon",6),a.\u0275\u0275elementStart(8,"label",4),a.\u0275\u0275text(9," Transferir "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(10,"div",7),a.\u0275\u0275listener("click",function(){return r.onPaymentQR()}),a.\u0275\u0275elementStart(11,"div",8)(12,"div",9),a.\u0275\u0275element(13,"bocc-icon",10),a.\u0275\u0275elementEnd()(),a.\u0275\u0275element(14,"bocc-icon",11),a.\u0275\u0275elementStart(15,"label",4),a.\u0275\u0275text(16," Pago QR "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(17,"div",12),a.\u0275\u0275listener("click",function(){return r.onPayments()}),a.\u0275\u0275element(18,"bocc-icon",13),a.\u0275\u0275elementStart(19,"label",4),a.\u0275\u0275text(20," Pagar "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(21,"div",14),a.\u0275\u0275listener("click",function(){return r.onToken()}),a.\u0275\u0275element(22,"bocc-icon",15),a.\u0275\u0275elementStart(23,"label",4),a.\u0275\u0275text(24," Token "),a.\u0275\u0275elementEnd()()()()),2&v&&(a.\u0275\u0275advance(2),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isProducts),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isTransfers),a.\u0275\u0275advance(11),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isPayments),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[i.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),s})()},83867:(V,E,o)=>{o.d(E,{o:()=>g});var i=o(17007),e=o(30263),t=o(8834),n=o(98699),s=(o(57544),o(99877));function v(d,b){if(1&d&&(s.\u0275\u0275elementStart(0,"label",11),s.\u0275\u0275text(1),s.\u0275\u0275elementEnd()),2&d){const f=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-currency-box__rate--active",f.hasValue),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate2(" ",f.valueFormat," ",f.rateCode," ")}}function r(d,b){if(1&d&&(s.\u0275\u0275elementStart(0,"div",12),s.\u0275\u0275element(1,"img",13),s.\u0275\u0275elementEnd()),2&d){const f=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("src",f.icon,s.\u0275\u0275sanitizeUrl)}}function l(d,b){if(1&d&&(s.\u0275\u0275elementStart(0,"div",14),s.\u0275\u0275text(1),s.\u0275\u0275elementEnd()),2&d){const f=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",f.currencyCode," ")}}function y(d,b){if(1&d&&(s.\u0275\u0275elementStart(0,"div",15),s.\u0275\u0275element(1,"bocc-icon",16),s.\u0275\u0275elementStart(2,"span",17),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd()()),2&d){const f=s.\u0275\u0275nextContext();s.\u0275\u0275advance(3),s.\u0275\u0275textInterpolate1(" ",null==f.formControl.error?null:f.formControl.error.message," ")}}function h(d,b){if(1&d&&(s.\u0275\u0275elementStart(0,"div",18),s.\u0275\u0275element(1,"bocc-icon",19),s.\u0275\u0275elementStart(2,"span",17),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd()()),2&d){const f=s.\u0275\u0275nextContext();s.\u0275\u0275advance(3),s.\u0275\u0275textInterpolate1(" ",f.helperInfo," ")}}let g=(()=>{class d{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,n.itIsDefined)(this.rate)}get value(){const f=+this.formControl?.value;return isNaN(f)?0:this.hasRate?f/this.rate:0}get valueFormat(){return(0,t.b)({value:this.value,symbol:"$",decimals:!0})}}return d.\u0275fac=function(f){return new(f||d)},d.\u0275cmp=s.\u0275\u0275defineComponent({type:d,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(f,I){1&f&&(s.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd(),s.\u0275\u0275template(4,v,2,4,"label",3),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"div",4)(6,"div",5),s.\u0275\u0275template(7,r,2,1,"div",6),s.\u0275\u0275element(8,"bocc-currency-field",7),s.\u0275\u0275template(9,l,2,1,"div",8),s.\u0275\u0275elementEnd()(),s.\u0275\u0275template(10,y,4,1,"div",9),s.\u0275\u0275template(11,h,4,1,"div",10),s.\u0275\u0275elementEnd()),2&f&&(s.\u0275\u0275classProp("mbo-currency-box--focused",I.formControl.focused)("mbo-currency-box--error",I.formControl.invalid&&I.formControl.touched)("mbo-currency-box--disabled",I.formControl.disabled||I.disabled),s.\u0275\u0275advance(2),s.\u0275\u0275property("for",I.elementId),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",I.label," "),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.hasRate),s.\u0275\u0275advance(3),s.\u0275\u0275property("ngIf",I.icon),s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",I.elementId)("placeholder",I.placeholder)("disabled",I.disabled)("formControl",I.formControl),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.currencyCode),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.formControl.invalid&&I.formControl.touched),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.helperInfo&&!(I.formControl.invalid&&I.formControl.touched)))},dependencies:[i.CommonModule,i.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),d})()},85070:(V,E,o)=>{o.d(E,{f:()=>m});var i=o(17007),e=o(78506),t=o(99877);const a=["*"];let m=(()=>{class u{constructor(p){this.session=p}ngOnInit(){this.session.customer().then(p=>this.customer=p)}}return u.\u0275fac=function(p){return new(p||u)(t.\u0275\u0275directiveInject(e._I))},u.\u0275cmp=t.\u0275\u0275defineComponent({type:u,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(p,v){1&p&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0)(1,"label",1),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"span",2),t.\u0275\u0275projection(4),t.\u0275\u0275elementEnd()()),2&p&&(t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(null==v.customer?null:v.customer.shortName))},dependencies:[i.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),u})()},65887:(V,E,o)=>{o.d(E,{X:()=>p});var i=o(17007),e=o(99877),n=o(30263),a=o(24495);function s(v,r){1&v&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}o(57544);let p=(()=>{class v{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[a.C1]:[]),this.unsubscription=this.documentType.subscribe(l=>{l&&(this.updateNumber(l,this.required),this.inputType=this.getInputType(l))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(l){if(l.required){const y=l.required.currentValue;this.documentType.setValidators(y?[a.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,y)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(l){return"PA"===l.code?"text":"number"}updateNumber(l,y){const h=this.validatorsForNumber(l,y);this.documentNumber.setValidators(h),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(l,y){return this.validatorsFromType(l).concat(y?[a.C1]:[])}maxLength(l){return y=>y&&y.length>l?{id:"maxLength",message:`Debe tener m\xe1ximo ${l} caracteres`}:null}validatorsFromType(l){switch(l.code){case"PA":return[a.JF];case"NIT":return[a.X1,this.maxLength(15)];default:return[a.X1]}}}return v.\u0275fac=function(l){return new(l||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(l,y){1&l&&(e.\u0275\u0275template(0,s,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&l&&(e.\u0275\u0275property("ngIf",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementSelectId)("label",y.labelType)("suggestions",y.documents)("disabled",y.disabled)("formControl",y.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementInputId)("label",y.labelNumber)("type",y.inputType)("disabled",y.disabled)("formControl",y.documentNumber))},dependencies:[i.CommonModule,i.NgIf,n.DT,n.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),v})()},78021:(V,E,o)=>{o.d(E,{c:()=>v});var i=o(17007),e=o(30263),t=o(7603),n=o(98699),m=o(99877);function s(r,l){if(1&r){const y=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",5),m.\u0275\u0275listener("click",function(){m.\u0275\u0275restoreView(y);const g=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(g.onAction(g.leftAction))}),m.\u0275\u0275elementStart(1,"span"),m.\u0275\u0275text(2),m.\u0275\u0275elementEnd()()}if(2&r){const y=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",y.leftAction.id)("bocc-button",y.leftAction.type||"flat")("prefixIcon",y.leftAction.prefixIcon)("disabled",y.itIsDisabled(y.leftAction))("hidden",y.itIsHidden(y.leftAction)),m.\u0275\u0275advance(2),m.\u0275\u0275textInterpolate(y.leftAction.label)}}function p(r,l){if(1&r){const y=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",6),m.\u0275\u0275listener("click",function(){const d=m.\u0275\u0275restoreView(y).$implicit,b=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(b.onAction(d))}),m.\u0275\u0275elementEnd()}if(2&r){const y=l.$implicit,h=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",y.id)("type",y.type||"flat")("bocc-button-action",y.icon)("disabled",h.itIsDisabled(y))("hidden",h.itIsHidden(y))}}let v=(()=>{class r{constructor(y){this.utagService=y,this.rightActions=[]}itIsDisabled({disabled:y}){return(0,n.evalValueOrFunction)(y)}itIsHidden({hidden:y}){return(0,n.evalValueOrFunction)(y)}onAction(y){const{id:h}=y;h&&this.utagService.link("click",h),y.click()}}return r.\u0275fac=function(y){return new(y||r)(m.\u0275\u0275directiveInject(t.D))},r.\u0275cmp=m.\u0275\u0275defineComponent({type:r,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(y,h){1&y&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1),m.\u0275\u0275template(2,s,3,6,"button",2),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(3,"div",3),m.\u0275\u0275template(4,p,1,5,"button",4),m.\u0275\u0275elementEnd()()),2&y&&(m.\u0275\u0275advance(2),m.\u0275\u0275property("ngIf",h.leftAction),m.\u0275\u0275advance(2),m.\u0275\u0275property("ngForOf",h.rightActions))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),r})()},27302:(V,E,o)=>{o.d(E,{Vc:()=>i.V,k4:()=>c.k,o2:()=>e.o,B_:()=>u,fi:()=>s.f,XH:()=>p.X,cN:()=>y.c,Aj:()=>h.A,J5:()=>K.J,Nu:()=>G,iF:()=>j});var i=o(35324),c=o(9593),e=o(83867),t=o(17007),n=o(99877);function m(C,W){if(1&C){const A=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",2),n.\u0275\u0275listener("click",function(){const N=n.\u0275\u0275restoreView(A).$implicit,Y=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(Y.onClickCurrency(N))}),n.\u0275\u0275elementStart(1,"div",3),n.\u0275\u0275element(2,"img",4)(3,"img",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"label",6),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()()}if(2&C){const A=W.$implicit,_=n.\u0275\u0275nextContext();n.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",_.isEnabled(A)),n.\u0275\u0275advance(2),n.\u0275\u0275property("src",A.enabledIcon,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(1),n.\u0275\u0275property("src",A.disabledIcon,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",A.label," ")}}o(57544);let u=(()=>{class C{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[A]=this.currencies;this.formControl.setValue(A)}}ngOnChanges(A){const{currencies:_}=A;if(_){const[T]=_.currentValue;this.formControl&&this.formControl.setValue(T)}}isEnabled(A){return A===this.formControl?.value}onClickCurrency(A){this.formControl&&!this.disabled&&this.formControl.setValue(A)}changeCurriencies(A){if(A.currencies){const _=A.currencies.currentValue,[T]=_;this.formControl&&this.formControl.setValue(T)}}}return C.\u0275fac=function(A){return new(A||C)},C.\u0275cmp=n.\u0275\u0275defineComponent({type:C,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[n.\u0275\u0275NgOnChangesFeature,n.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(A,_){1&A&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,m,6,5,"div",1),n.\u0275\u0275elementEnd()),2&A&&(n.\u0275\u0275classProp("mbo-currency-toggle--disabled",_.disabled),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngForOf",_.currencies))},dependencies:[t.CommonModule,t.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),C})();var s=o(85070),p=o(65887),v=o(30263),y=o(78021),h=o(50689),b=(o(7603),o(98699),o(72765)),K=o(88014);function D(C,W){if(1&C&&(n.\u0275\u0275elementStart(0,"div",4),n.\u0275\u0275element(1,"img",5),n.\u0275\u0275elementEnd()),2&C){const A=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("src",A.src,n.\u0275\u0275sanitizeUrl)}}const L=["*"];let G=(()=>{class C{}return C.\u0275fac=function(A){return new(A||C)},C.\u0275cmp=n.\u0275\u0275defineComponent({type:C,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:L,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(A,_){1&A&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,D,2,1,"div",1),n.\u0275\u0275elementStart(2,"div",2)(3,"div",3),n.\u0275\u0275projection(4),n.\u0275\u0275elementEnd()()()),2&A&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",_.src))},dependencies:[t.CommonModule,t.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),C})();var J=o(24495);const B=/[A-Z]/,U=/[a-z]/,M=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,P=C=>C&&!B.test(C)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,F=C=>C&&!U.test(C)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,Z=C=>C&&!M.test(C)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let j=(()=>{class C{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([J.C1,F,P,Z,(0,J.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const A=this.formControl.errors.reduce((T,{id:N})=>[...T,N],[]),_=A.includes("required");this.smallInvalid=A.includes("smallCase")||_,this.capitalInvalid=A.includes("capitalCase")||_,this.specialCharInvalid=A.includes("specialChar")||_,this.minLengthInvalid=A.includes("minlength")||_})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return C.\u0275fac=function(A){return new(A||C)},C.\u0275cmp=n.\u0275\u0275defineComponent({type:C,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(A,_){1&A&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"bocc-password-box",1),n.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),n.\u0275\u0275text(4," Min\xfascula "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"mbo-poster",4),n.\u0275\u0275text(6," May\xfascula "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(7,"mbo-poster",5),n.\u0275\u0275text(8," Especial "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"mbo-poster",6),n.\u0275\u0275text(10," Caracteres "),n.\u0275\u0275elementEnd()()()),2&A&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("elementId",_.elementId)("disabled",_.disabled)("formControl",_.formControl),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",_.smallInvalid),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",_.capitalInvalid),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",_.specialCharInvalid),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",_.minLengthInvalid))},dependencies:[t.CommonModule,v.sC,b.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),C})()},50689:(V,E,o)=>{o.d(E,{A:()=>a});var i=o(17007),e=o(99877);const n=["*"];let a=(()=>{class m{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return m.\u0275fac=function(s){return new(s||m)},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:n,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(s,p){1&s&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&s&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",p.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[i.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),m})()},88014:(V,E,o)=>{o.d(E,{J:()=>n});var i=o(17007),e=o(99877);let n=(()=>{class a{}return a.\u0275fac=function(u){return new(u||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(u,s){1&u&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[i.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),a})()},21498:(V,E,o)=>{o.d(E,{P:()=>r});var i=o(17007),e=o(30263),t=o(99877);function a(l,y){if(1&l&&t.\u0275\u0275element(0,"bocc-card-product-summary",7),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("color",h.information.product.color)("icon",h.information.product.icon)("number",h.information.product.number)("title",h.information.product.title)("subtitle",h.information.product.subtitle)}}function m(l,y){if(1&l&&t.\u0275\u0275element(0,"bocc-card-summary",8),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",h.information.standard.header)("title",h.information.standard.title)("subtitle",h.information.standard.subtitle)("detail",h.information.standard.detail)}}function u(l,y){if(1&l&&t.\u0275\u0275element(0,"bocc-card-summary",9),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",h.information.amount.header)("amount",h.information.amount.value)("symbol",h.information.amount.symbol)("amountSmall",h.information.amount.small)}}function s(l,y){if(1&l&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",h.information.text.header)("customizedContent",!0),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(h.information.text.content)}}function p(l,y){if(1&l&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",12),t.\u0275\u0275element(1,"bocc-icon",13),t.\u0275\u0275elementStart(2,"span",14),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(4,"bocc-icon",15),t.\u0275\u0275elementStart(5,"span",14),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd()()),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",h.information.datetime.header)("customizedContent",!0),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",h.information.datetime.date," "),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",h.information.datetime.time," ")}}function v(l,y){if(1&l&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()),2&l){const h=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",h.information.badge.header)("customizedContent",!0),t.\u0275\u0275advance(1),t.\u0275\u0275attribute("bocc-theme",h.information.badge.color),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",h.information.badge.label," ")}}let r=(()=>{class l{}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(h,g){1&h&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,a,1,5,"bocc-card-product-summary",1),t.\u0275\u0275template(2,m,1,4,"bocc-card-summary",2),t.\u0275\u0275template(3,u,1,4,"bocc-card-summary",3),t.\u0275\u0275template(4,s,3,3,"bocc-card-summary",4),t.\u0275\u0275template(5,p,7,4,"bocc-card-summary",5),t.\u0275\u0275template(6,v,3,4,"bocc-card-summary",6),t.\u0275\u0275elementEnd()),2&h&&(t.\u0275\u0275property("ngSwitch",g.information.type),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","product"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","standard"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","amount"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","text"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","datetime"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[i.CommonModule,i.NgSwitch,i.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),l})()},7427:(V,E,o)=>{o.d(E,{x:()=>r});var i=o(17007),e=o(30263),t=o(87903),a=(o(29306),o(77279)),m=o(87956),u=o(68789),s=o(13961),p=o(99877);let r=(()=>{class l{constructor(h,g){this.eventBusService=h,this.onboardingScreenService=g,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,t.Bn)(this.product.tagAval),this.eventBusService.emit(a.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(s.Z)),this.tagAvalonboarding.open()}}return l.\u0275fac=function(h){return new(h||l)(p.\u0275\u0275directiveInject(m.Yd),p.\u0275\u0275directiveInject(u.x))},l.\u0275cmp=p.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(h,g){1&h&&(p.\u0275\u0275elementStart(0,"bocc-card-product",0),p.\u0275\u0275listener("key",function(){return g.onTagAval()})("onboarding",function(){return g.onBoarding()}),p.\u0275\u0275elementEnd()),2&h&&(p.\u0275\u0275classMap(g.product.bank.className),p.\u0275\u0275property("iconTitle",g.iconTitle)("title",g.product.nickname||g.product.name)("icon",g.product.logo)("tagAval",g.product.tagAvalFormat)("actions",g.actions)("color",g.product.color)("code",g.product.shortNumber)("label",g.product.label)("amount",g.product.amount)("incognito",g.incognito)("displayCard",!0)("statusLabel",null==g.product.status?null:g.product.status.label)("statusColor",null==g.product.status?null:g.product.status.color)("cromaline",!0)("msgError",g.msgError))},dependencies:[i.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),l})()},1027:(V,E,o)=>{o.d(E,{A:()=>h});var i=o(17007),c=o(72765),e=o(30263),t=o(99877);function n(g,d){if(1&g&&t.\u0275\u0275element(0,"bocc-card-product-summary",8),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("color",b.information.product.color)("icon",b.information.product.icon)("number",b.information.product.number)("title",b.information.product.title)("subtitle",b.information.product.subtitle)}}function a(g,d){if(1&g&&t.\u0275\u0275element(0,"bocc-card-summary",9),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.standard.header)("title",b.information.standard.title)("subtitle",b.information.standard.subtitle)}}function m(g,d){if(1&g&&t.\u0275\u0275element(0,"bocc-card-summary",10),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.amount.header)("amount",b.information.amount.value)("symbol",b.information.amount.symbol)}}function u(g,d){if(1&g&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.text.header)("customizedContent",!0),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(b.information.text.content)}}function s(g,d){if(1&g&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",13),t.\u0275\u0275element(1,"bocc-icon",14),t.\u0275\u0275elementStart(2,"span",15),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(4,"bocc-icon",16),t.\u0275\u0275elementStart(5,"span",15),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd()()),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.datetime.header)("customizedContent",!0),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",b.information.datetime.date," "),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",b.information.datetime.time," ")}}function p(g,d){if(1&g&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",17),t.\u0275\u0275element(1,"bocc-icon",14),t.\u0275\u0275elementStart(2,"span",15),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd()()),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.date.header)("customizedContent",!0),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",b.information.date.date," ")}}function v(g,d){if(1&g&&(t.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()),2&g){const b=t.\u0275\u0275nextContext();t.\u0275\u0275property("header",b.information.badge.header)("customizedContent",!0),t.\u0275\u0275advance(1),t.\u0275\u0275attribute("bocc-theme",b.information.badge.color),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",b.information.badge.label," ")}}let r=(()=>{class g{}return g.\u0275fac=function(b){return new(b||g)},g.\u0275cmp=t.\u0275\u0275defineComponent({type:g,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(b,f){1&b&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,n,1,5,"bocc-card-product-summary",1),t.\u0275\u0275template(2,a,1,3,"bocc-card-summary",2),t.\u0275\u0275template(3,m,1,3,"bocc-card-summary",3),t.\u0275\u0275template(4,u,3,3,"bocc-card-summary",4),t.\u0275\u0275template(5,s,7,4,"bocc-card-summary",5),t.\u0275\u0275template(6,p,4,3,"bocc-card-summary",6),t.\u0275\u0275template(7,v,3,4,"bocc-card-summary",7),t.\u0275\u0275elementEnd()),2&b&&(t.\u0275\u0275property("ngSwitch",f.information.type),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","product"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","standard"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","amount"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","text"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","datetime"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","date"),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[i.CommonModule,i.NgSwitch,i.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),g})();function l(g,d){1&g&&t.\u0275\u0275element(0,"mbo-card-information-element",8),2&g&&t.\u0275\u0275property("information",d.$implicit)}const y=["*"];let h=(()=>{class g{constructor(){this.skeleton=!1,this.informations=[]}}return g.\u0275fac=function(b){return new(b||g)},g.\u0275cmp=t.\u0275\u0275defineComponent({type:g,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(b,f){1&b&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),t.\u0275\u0275element(3,"mbo-bank-logo",3),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(4,"div",4),t.\u0275\u0275element(5,"div",5),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"div",6),t.\u0275\u0275projection(7),t.\u0275\u0275template(8,l,1,1,"mbo-card-information-element",7),t.\u0275\u0275elementEnd()()),2&b&&(t.\u0275\u0275advance(3),t.\u0275\u0275property("result",!0),t.\u0275\u0275advance(5),t.\u0275\u0275property("ngForOf",f.informations))},dependencies:[i.CommonModule,i.NgForOf,c.rw,r],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),g})()},16442:(V,E,o)=>{o.d(E,{u:()=>b});var i=o(99877),e=o(17007),n=o(13462),m=o(19102),u=o(45542),s=o(65467),p=o(21498);function v(f,I){if(1&f&&(i.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&f){const S=i.\u0275\u0275nextContext();i.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",S.template.skeleton),i.\u0275\u0275property("secondary",!0)("active",S.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",S.template.header.subtitle," ")}}function r(f,I){1&f&&i.\u0275\u0275element(0,"mbo-card-information",16),2&f&&i.\u0275\u0275property("information",I.$implicit)}function l(f,I){if(1&f&&(i.\u0275\u0275elementStart(0,"div",14),i.\u0275\u0275projection(1),i.\u0275\u0275template(2,r,1,1,"mbo-card-information",15),i.\u0275\u0275elementEnd()),2&f){const S=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275property("ngForOf",S.template.informations)}}function y(f,I){1&f&&(i.\u0275\u0275elementStart(0,"div",17),i.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),i.\u0275\u0275elementEnd()),2&f&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0)("secondary",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0)("secondary",!0))}function h(f,I){if(1&f){const S=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",23),i.\u0275\u0275listener("click",function(){const D=i.\u0275\u0275restoreView(S).$implicit,L=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(L.onAction(D))}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&f){const S=I.$implicit;i.\u0275\u0275property("bocc-button",S.type)("prefixIcon",S.prefixIcon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(S.label)}}function g(f,I){if(1&f&&(i.\u0275\u0275elementStart(0,"div",21),i.\u0275\u0275template(1,h,3,3,"button",22),i.\u0275\u0275elementEnd()),2&f){const S=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("ngForOf",S.template.actions)}}const d=["*"];let b=(()=>{class f{constructor(){this.disabled=!1,this.action=new i.EventEmitter}onAction({event:S}){this.action.emit(S)}}return f.\u0275fac=function(S){return new(S||f)},f.\u0275cmp=i.\u0275\u0275defineComponent({type:f,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:d,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(S,x){1&S&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275element(3,"mbo-bank-logo",3),i.\u0275\u0275elementStart(4,"div",4),i.\u0275\u0275element(5,"ng-lottie",5),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),i.\u0275\u0275text(7),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(8,v,2,5,"bocc-skeleton-text",7),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(9,"div",8),i.\u0275\u0275element(10,"div",9),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(11,l,3,1,"div",10),i.\u0275\u0275template(12,y,4,5,"div",11),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(13,g,2,1,"div",12)),2&S&&(i.\u0275\u0275classProp("animation",!x.template.skeleton),i.\u0275\u0275advance(3),i.\u0275\u0275property("result",!0),i.\u0275\u0275advance(2),i.\u0275\u0275property("options",x.template.header.animation),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",x.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",x.template.header.title," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",x.template.skeleton||x.template.header.subtitle),i.\u0275\u0275advance(3),i.\u0275\u0275property("ngIf",!x.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",x.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",x.template.actions.length&&!x.disabled))},dependencies:[e.NgForOf,e.NgIf,n.LottieComponent,m.r,u.P,s.D,p.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),f})()},10119:(V,E,o)=>{o.d(E,{N:()=>y});var i=o(17007),e=o(99877),n=o(30263),a=o(7603),m=o(98699);function s(h,g){if(1&h&&e.\u0275\u0275element(0,"bocc-diamond",14),2&h){const d=g.$implicit,b=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",b.itIsSelected(d))}}function p(h,g){if(1&h){const d=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(d);const f=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(f.onAction(f.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const d=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",d.footerActionLeft.id)("bocc-button",d.footerActionLeft.type)("prefixIcon",d.footerActionLeft.prefixIcon)("disabled",d.itIsDisabled(d.footerActionLeft))("hidden",d.itIsHidden(d.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(d.footerActionLeft.label)}}function v(h,g){if(1&h){const d=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(d);const f=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(f.onAction(f.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const d=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",d.footerActionRight.id)("bocc-button",d.footerActionRight.type)("prefixIcon",d.footerActionRight.prefixIcon)("disabled",d.itIsDisabled(d.footerActionRight))("hidden",d.itIsHidden(d.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(d.footerActionRight.label)}}const r=["*"];let y=(()=>{class h{constructor(d,b){this.ref=d,this.utagService=b,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((d,b)=>b),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(d){return d===this.currentPosition}itIsDisabled({disabled:d}){return(0,m.evalValueOrFunction)(d)}itIsHidden({hidden:d}){return(0,m.evalValueOrFunction)(d)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(d){const{id:b}=d;b&&this.utagService.link("click",b),d.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(d){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(d),this.automatic=!1,this.setTranslatePosition(d)}setTranslatePosition(d){this.translateX=d*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(d){this.transformContent=`translateX(${d}px)`}emitPosition(d){this.finished||(this.finished=d+1===this.elements.length),this.position.emit({position:d,finished:this.finished})}getPositionSlide(d){return d>=this.elements.length?this.elements.length-1:d<0?0:d}setTouchHandler(d){let b=0,f=0;d.addEventListener("touchstart",I=>{if(I.changedTouches.length){const{clientX:S}=I.changedTouches.item(0);b=0,this.touched=!0,f=S}}),d.addEventListener("touchmove",I=>{if(I.changedTouches.length){const S=I.changedTouches.item(0),x=S.clientX-f;f=S.clientX,this.translateX+=x,b+=x,this.setTranslateContent(this.translateX)}}),d.addEventListener("touchend",I=>{this.touched=!1,I.changedTouches.length&&(Math.abs(b)/this.widthBody*100>=40&&(b>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return h.\u0275fac=function(d){return new(d||h)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(a.D))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(d,b){1&d&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return b.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return b.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,s,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return b.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,p,3,6,"button",13),e.\u0275\u0275template(16,v,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&d&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",b.headerActionLeft)("rightAction",b.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",b.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",b.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",b.widthContent)("transform",b.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",b.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!b.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",b.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!b.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",b.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.footerActionRight))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,n.P8,n.u1,n.ou,n.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),h})()},68789:(V,E,o)=>{o.d(E,{x:()=>a});var i=o(7603),c=o(10455),e=o(87677),t=o(99877);let a=(()=>{class m{constructor(s){this.portalService=s}information(){this.portal||(this.portal=this.portalService.container({component:c.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(s,p){return this.portalService.container({component:s,container:e.C,props:{container:p?.containerProps,component:p?.componentProps}})}}return m.\u0275fac=function(s){return new(s||m)(t.\u0275\u0275inject(i.v))},m.\u0275prov=t.\u0275\u0275defineInjectable({token:m,factory:m.\u0275fac,providedIn:"root"}),m})()},87677:(V,E,o)=>{o.d(E,{C:()=>e});var i=o(99877);let e=(()=>{class t{constructor(a){this.ref=a,this.visible=!1,this.visibleChange=new i.EventEmitter}open(a=0){setTimeout(()=>{this.changeVisible(!0)},a)}close(a=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},a)}append(a){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(a)}ngBoccPortal(a){this.portal=a}changeVisible(a){this.visible=a,this.visibleChange.emit(a)}}return t.\u0275fac=function(a){return new(a||t)(i.\u0275\u0275directiveInject(i.ElementRef))},t.\u0275cmp=i.\u0275\u0275defineComponent({type:t,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(a,m){1&a&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275element(1,"div",1),i.\u0275\u0275elementEnd()),2&a&&i.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",m.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),t})()},10455:(V,E,o)=>{o.d(E,{E:()=>m});var i=o(17007),e=o(99877),n=o(27302),a=o(10119);let m=(()=>{class u{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(p){this.portal=p}onPosition({finished:p}){this.finished=p,p&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(p,v){1&p&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(l){return v.onPosition(l)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&p&&e.\u0275\u0275property("footerActionLeft",v.footerLeft)("footerActionRight",v.footerRight)("gradient",!0)},dependencies:[i.CommonModule,n.Nu,a.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),u})()},91642:(V,E,o)=>{o.d(E,{D:()=>b});var i=o(17007),e=o(99877),n=o(30263),a=o(87542),m=o(70658),u=o(3372),s=o(87956),p=o(72765);function v(f,I){1&f&&e.\u0275\u0275element(0,"mbo-bank-logo")}function r(f,I){1&f&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function l(f,I){if(1&f&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&f){const S=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",S.verifying)}}function y(f,I){1&f&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function h(f,I){if(1&f){const S=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(S);const K=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(K.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&f){const S=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",S.verifying)}}const g=["*"],{OtpInputSuperuser:d}=u.M;let b=(()=>{class f{constructor(S,x,K,D){this.ref=S,this.otpService=x,this.deviceService=K,this.preferencesService=D,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=a.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new a.yV}ngOnInit(){this.otpService.onCode(S=>{this.otpControls.setCode(S),this.otpControls.valid&&this.onAutocomplete(S)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(S){const{documentNumber:x}=S;x&&this.preferencesService.applyFunctionality(d,x.currentValue).then(K=>{this.itIsDocumentSuperuser=K})}get otpVisible(){return!m.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return m.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&m.N.otpReadonlyMobile}onAutocomplete(S){this.code.emit(S)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return f.\u0275fac=function(S){return new(S||f)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(s.no),e.\u0275\u0275directiveInject(s.U8),e.\u0275\u0275directiveInject(s.yW))},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:g,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(S,x){1&S&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,v,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,r,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(D){return x.onAutocomplete(D)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,l,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,y,7,0,"div",8),e.\u0275\u0275template(13,h,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&S&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",x.otpVisible),e.\u0275\u0275property("formControls",x.otpControls)("readonly",x.otpReadonly)("mobile",x.otpMobile)("disabled",x.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.isIos))},dependencies:[i.CommonModule,i.NgIf,n.P8,n.Yx,p.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),f})()},10464:(V,E,o)=>{o.d(E,{K:()=>m});var i=o(17007),e=o(99877),n=o(22816);const a=["*"];let m=(()=>{class u{constructor(p){this.ref=p,this.scroller=new n.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(p){this.scroller.reset(p.target)}}return u.\u0275fac=function(p){return new(p||u)(e.\u0275\u0275directiveInject(e.ElementRef))},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(p,v){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(l){return v.onScroll(l)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&p&&e.\u0275\u0275classProp("mbo-page__content--start",v.scrollStart)("mbo-page__content--end",v.scrollEnd)},dependencies:[i.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),u})()},75221:(V,E,o)=>{o.d(E,{u:()=>u});var i=o(17007),e=o(30263),t=o(27302),a=(o(88649),o(99877));let u=(()=>{class s{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return s.\u0275fac=function(v){return new(v||s)},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(v,r){1&v&&a.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&v&&(a.\u0275\u0275property("formControl",r.passwordControl.controls.password)("disabled",r.disabled)("elementId",r.elementPasswordId),a.\u0275\u0275advance(1),a.\u0275\u0275property("elementId",r.elementConfirmId)("disabled",r.disabled)("formControl",r.passwordControl.controls.repeatPassword))},dependencies:[i.CommonModule,e.sC,t.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),s})()},88649:(V,E,o)=>{o.d(E,{z:()=>t});var i=o(57544),c=o(24495);class t extends i.FormGroup{constructor(){const a=new i.FormControl(""),m=new i.FormControl("",[c.C1,(n=a,a=>a&&a!==n.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var n;super({controls:{password:a,repeatPassword:m}})}get password(){return this.controls.password.value}}},13043:(V,E,o)=>{o.d(E,{e:()=>h});var i=o(17007),e=o(99877),n=o(30263),u=(o(57544),o(27302));function s(g,d){1&g&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function p(g,d){if(1&g&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&g){const b=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",b.title," ")}}function v(g,d){if(1&g){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const S=e.\u0275\u0275restoreView(b).$implicit,x=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(x.onProduct(S))}),e.\u0275\u0275elementEnd()}if(2&g){const b=d.$implicit,f=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",b.color)("icon",b.logo)("title",b.nickname)("number",b.publicNumber)("detail",b.bank.name)("ghost",f.ghost)}}function r(g,d){if(1&g&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,p,2,1,"div",8),e.\u0275\u0275template(3,v,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&g){const b=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",b.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",b.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!b.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",b.msgError," ")}}function l(g,d){1&g&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const y=["*"];let h=(()=>{class g{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(b){return this.productControl?.value?.id===b.id}onProduct(b){this.select.emit(b),this.productControl?.setValue(b)}}return g.\u0275fac=function(b){return new(b||g)},g.\u0275cmp=e.\u0275\u0275defineComponent({type:g,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(b,f){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,s,3,0,"div",1),e.\u0275\u0275template(2,r,6,5,"div",2),e.\u0275\u0275template(3,l,3,2,"div",3),e.\u0275\u0275elementEnd()),2&b&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!f.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.skeleton))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,n.w_,u.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),g})()},38116:(V,E,o)=>{o.d(E,{Z:()=>m});var i=o(17007),e=o(99877),n=o(30263);function a(u,s){if(1&u){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(p).$implicit,y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onAction(l))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&u){const p=s.$implicit,v=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(v.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",v.itIsDisabled(p)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(v.theme(p)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",p.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",p.label," ")}}let m=(()=>{class u{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(p){return p.requiredInformation&&p.errorInformation}theme(p){return this.itIsDisabled(p)?"none":p.theme}onAction(p){!this.itIsDisabled(p)&&this.action.emit(p.type)}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(p,v){1&p&&e.\u0275\u0275template(0,a,6,8,"div",0),2&p&&e.\u0275\u0275property("ngForOf",v.actions)},dependencies:[i.CommonModule,i.NgForOf,n.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),u})()},68819:(V,E,o)=>{o.d(E,{w:()=>L});var i=o(17007),e=o(99877),n=o(30263),a=o(39904),s=(o(57544),o(78506)),v=(o(29306),o(87903)),r=o(95437),l=o(27302),y=o(70957),h=o(91248),g=o(13961),d=o(68789),b=o(33395),f=o(25317);function I(G,J){if(1&G){const B=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(M){e.\u0275\u0275restoreView(B);const P=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(P.onBoarding(M))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(B);const M=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(M.onCopyKey(M.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(B);const M=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(M.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&G){const B=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",B.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",B.product.tagAval)}}function S(G,J){if(1&G&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&G){const B=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",B.digitalSection)("currencyCode",null==B.currencyControl.value?null:B.currencyControl.value.code)("hidden",B.itIsVisibleMovements)}}function x(G,J){if(1&G&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&G){const B=J.$implicit,U=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",B)("currencyCode",null==U.currencyControl.value?null:U.currencyControl.value.code)}}const K=[[["","header",""]],"*"],D=["[header]","*"];let L=(()=>{class G{constructor(B,U,M){this.mboProvider=B,this.managerInformation=U,this.onboardingScreenService=M,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(B){const{movements:U,sections:M}=B;U&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!U.currentValue),M&&this.product&&this.refreshComponent(this.product,M.currentValue),this.managerInformation.requestInfoBody().then(P=>{P.when({success:({canEditTagAval:F})=>{this.canEditTagAval=F}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(B){(0,v.Bn)(B),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(a.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(B,U){const M=(0,v.A2)(B);if(this.sectionPosition=0,U?.length){const P=U.map(({title:F},Z)=>({label:F,value:Z}));M&&(this.headerMovements.value=this.sections.length,P.push(this.headerMovements)),this.headers=P}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const P=[{label:"Error",value:1}];M&&P.unshift(this.headerMovements),this.headers=P}}onBoarding(B){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(g.Z)),this.tagAvalonboarding.open(),B.stopPropagation()}}return G.\u0275fac=function(B){return new(B||G)(e.\u0275\u0275directiveInject(r.ZL),e.\u0275\u0275directiveInject(s.vu),e.\u0275\u0275directiveInject(d.x))},G.\u0275cmp=e.\u0275\u0275defineComponent({type:G,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:D,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(B,U){1&B&&(e.\u0275\u0275projectionDef(K),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(P){return U.sectionPosition=P}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,I,15,2,"div",4),e.\u0275\u0275template(6,S,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,x,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&B&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",U.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",U.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",U.headers)("value",U.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",U.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==U.product?null:U.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",U.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",U.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",U.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",U.movements)("header",U.header)("product",U.product)("currencyCode",null==U.currencyControl.value?null:U.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",U.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!U.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,n.Gf,n.qw,n.P8,n.Dj,n.qd,y.K,h.I,l.Aj,b.kW,f.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),G})()},19310:(V,E,o)=>{o.d(E,{$:()=>S});var i=o(17007),c=o(99877),e=o(30263),t=o(87903);let n=(()=>{class x{transform(D,L,G=" "){return(0,t.rd)(D,L,G)}}return x.\u0275fac=function(D){return new(D||x)},x.\u0275pipe=c.\u0275\u0275definePipe({name:"codeSplit",type:x,pure:!0}),x})(),a=(()=>{class x{}return x.\u0275fac=function(D){return new(D||x)},x.\u0275mod=c.\u0275\u0275defineNgModule({type:x}),x.\u0275inj=c.\u0275\u0275defineInjector({imports:[i.CommonModule]}),x})();o(57544);var u=o(70658),s=o(78506),v=(o(29306),o(87956)),r=o(72765),l=o(27302);function y(x,K){if(1&x){const D=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",18),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(D);const G=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(G.onDigital())}),c.\u0275\u0275elementStart(1,"span"),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd()()}if(2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275property("prefixIcon",D.digitalIcon),c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate(D.digitalIncognito?"Ver datos":"Ocultar datos")}}function h(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"span"),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate(null==D.product?null:D.product.publicNumber)}}function g(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"span",19),c.\u0275\u0275text(1),c.\u0275\u0275pipe(2,"codeSplit"),c.\u0275\u0275elementEnd()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",c.\u0275\u0275pipeBind2(2,1,D.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":D.digitalNumber,4)," ")}}function d(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"bocc-badge",20),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275attribute("bocc-theme",null==D.product||null==D.product.status?null:D.product.status.color),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",null==D.product||null==D.product.status?null:D.product.status.label," ")}}function b(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"div",21),c.\u0275\u0275element(1,"bocc-progress-bar",22),c.\u0275\u0275elementEnd()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("theme",D.progressBarTheme)("width",D.progressBarStatus)}}function f(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"div",23)(1,"label",24),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),c.\u0275\u0275element(5,"bocc-amount",26),c.\u0275\u0275elementEnd(),c.\u0275\u0275element(6,"mbo-button-incognito-mode",27),c.\u0275\u0275elementEnd()()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate1(" ",null==D.product?null:D.product.label," "),c.\u0275\u0275advance(2),c.\u0275\u0275property("active",!D.product),c.\u0275\u0275advance(1),c.\u0275\u0275property("amount",null==D.product?null:D.product.amount)("incognito",D.incognito),c.\u0275\u0275advance(1),c.\u0275\u0275property("actionMode",!0)("hidden",!D.product)}}function I(x,K){if(1&x&&(c.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),c.\u0275\u0275text(3,"Vence"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(4,"span",19),c.\u0275\u0275text(5),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(6,"div",29)(7,"label",20),c.\u0275\u0275text(8,"CVC"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(9,"span",19),c.\u0275\u0275text(10),c.\u0275\u0275elementEnd()()()),2&x){const D=c.\u0275\u0275nextContext();c.\u0275\u0275advance(5),c.\u0275\u0275textInterpolate1(" ",D.digitalIncognito?"\u2022\u2022 | \u2022\u2022":D.digitalExpAt," "),c.\u0275\u0275advance(5),c.\u0275\u0275textInterpolate1(" ",D.digitalIncognito?"\u2022\u2022\u2022":D.digitalCVC," ")}}let S=(()=>{class x{constructor(D,L){this.managerPreferences=D,this.digitalService=L,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new c.EventEmitter,this.digital=new c.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:D})=>{this.incognito=D})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:D,value:L})=>{this.product&&this.product.id===D&&this.refreshDigitalState(L)}))}ngOnChanges(D){const{product:L}=D;if(L&&L.currentValue){const G=L.currentValue;this.refreshDigitalState(this.digitalService.request(G.id)),this.activateDigitalCountdown(G)}}ngOnDestroy(){this.unsubscriptions.forEach(D=>D())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(D){const{incognito:L,requiredRequest:G,cvc:J,expirationAt:B,number:U}=D;this.digitalIncognito=L,this.digitalExpAt=B,this.digitalCVC=J,this.digitalNumber=U,this.digitalIcon=L?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=G}activateDigitalCountdown(D){const{countdown$:L}=this.digitalService.request(D.id);L?(this.progressBarRequired=!0,this.progressBarPercent=100,L.subscribe(G=>{this.progressBarRequired=!(G>=u.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-G/u.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return x.\u0275fac=function(D){return new(D||x)(c.\u0275\u0275directiveInject(s.Bx),c.\u0275\u0275directiveInject(v.ZP))},x.\u0275cmp=c.\u0275\u0275defineComponent({type:x,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[c.\u0275\u0275NgOnChangesFeature,c.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(D,L){1&D&&(c.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),c.\u0275\u0275listener("click",function(){return L.onClose()}),c.\u0275\u0275elementStart(3,"span"),c.\u0275\u0275text(4,"Atr\xe1s"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275element(5,"mbo-currency-toggle",3),c.\u0275\u0275template(6,y,3,2,"button",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(7,"div",5)(8,"div",6),c.\u0275\u0275element(9,"img",7),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),c.\u0275\u0275text(12),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),c.\u0275\u0275template(15,h,2,1,"span",12),c.\u0275\u0275template(16,g,3,4,"span",13),c.\u0275\u0275template(17,d,2,2,"bocc-badge",14),c.\u0275\u0275elementEnd()(),c.\u0275\u0275template(18,b,2,2,"div",15),c.\u0275\u0275elementEnd()(),c.\u0275\u0275template(19,f,7,6,"div",16),c.\u0275\u0275template(20,I,11,2,"div",17),c.\u0275\u0275elementEnd()),2&D&&(c.\u0275\u0275classMap(null==L.product?null:L.product.bank.className),c.\u0275\u0275property("color",null==L.product?null:L.product.color),c.\u0275\u0275advance(5),c.\u0275\u0275property("formControl",L.currencyControl)("currencies",L.currencies)("hidden",!(null!=L.product&&L.product.bank.isOccidente)||L.currencies.length<2),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital),c.\u0275\u0275advance(3),c.\u0275\u0275property("src",null==L.product?null:L.product.logo,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(2),c.\u0275\u0275property("active",!L.product),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",(null==L.product?null:L.product.nickname)||(null==L.product?null:L.product.name)," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!L.product),c.\u0275\u0275advance(1),c.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!L.product),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!(null!=L.product&&L.product.isDigital)),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",(null==L.product||null==L.product.status?null:L.product.status.label)&&L.digitalIncognito),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",L.progressBarRequired),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!(null!=L.product&&L.product.isDigital)),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital))},dependencies:[i.CommonModule,i.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,a,n,r.uf,l.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),x})()},94614:(V,E,o)=>{o.d(E,{K:()=>s});var i=o(17007),e=o(30263),t=o(39904),a=(o(29306),o(95437)),m=o(99877);let s=(()=>{class p{constructor(r){this.mboProvider=r,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:r,id:l,parentProduct:y}=this.product;"covered"===r&&y?this.goToPage(y.id,l):this.goToPage(l)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(r,l){this.mboProvider.navigation.next(t.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:r,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:l})}}return p.\u0275fac=function(r){return new(r||p)(m.\u0275\u0275directiveInject(a.ZL))},p.\u0275cmp=m.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(r,l){1&r&&(m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275listener("click",function(){return l.onComponent()}),m.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),m.\u0275\u0275text(5),m.\u0275\u0275elementEnd()(),m.\u0275\u0275elementStart(6,"div",4),m.\u0275\u0275element(7,"bocc-amount",5),m.\u0275\u0275elementEnd()()),2&r&&(m.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",l.skeleton),m.\u0275\u0275advance(2),m.\u0275\u0275property("active",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.dateFormat," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("active",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.description," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("hidden",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275property("amount",null==l.movement?null:l.movement.value)("currencyCode",null==l.movement?null:l.movement.currencyCode)("theme",!0))},dependencies:[i.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),p})()},70957:(V,E,o)=>{o.d(E,{K:()=>b});var i=o(15861),c=o(17007),t=o(99877),a=o(30263),m=o(78506),u=o(39904),p=(o(29306),o(87903)),v=o(95437),r=o(27302),l=o(94614);function y(f,I){if(1&f){const S=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",8),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(S);const K=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(K.onRedirectAll())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2,"Ver todos"),t.\u0275\u0275elementEnd()()}}function h(f,I){if(1&f&&(t.\u0275\u0275elementStart(0,"div",5)(1,"label",6),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(3,y,3,0,"button",7),t.\u0275\u0275elementEnd()),2&f){const S=t.\u0275\u0275nextContext();t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",(null==S.productMovements||null==S.productMovements.range?null:S.productMovements.range.label)||"Sin resultados"," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",null==S.productMovements?null:S.productMovements.range)}}function g(f,I){if(1&f&&t.\u0275\u0275element(0,"mbo-product-info-movement",9),2&f){const S=I.$implicit,x=t.\u0275\u0275nextContext();t.\u0275\u0275property("movement",S)("product",x.product)}}function d(f,I){if(1&f&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",10),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&f){const S=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",S.msgError," ")}}let b=(()=>{class f{constructor(S,x){this.mboProvider=S,this.managerProductMovements=x,this.header=!0,this.requesting=!1}ngOnChanges(S){const{currencyCode:x,product:K}=S;if(!this.movements&&(K||x)){const D=x?.currentValue||this.currencyCode,L=K?.currentValue||this.product;this.currentMovements=void 0,(0,p.A2)(L)&&this.requestFirstPage(L,D)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:S,id:x,parentProduct:K}=this.product;this.mboProvider.navigation.next(u.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===S?{productId:K?.id,coveredCardId:x}:{productId:x},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(S,x){var K=this;return(0,i.Z)(function*(){K.requesting=!0,(yield K.managerProductMovements.requestForProduct({product:S,currencyCode:x})).when({success:D=>{K.currentMovements=D},failure:()=>{K.currentMovements=void 0}},()=>{K.requesting=!1})})()}}return f.\u0275fac=function(S){return new(S||f)(t.\u0275\u0275directiveInject(v.ZL),t.\u0275\u0275directiveInject(m.sy))},f.\u0275cmp=t.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[t.\u0275\u0275NgOnChangesFeature,t.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(S,x){1&S&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,h,4,2,"div",1),t.\u0275\u0275elementStart(2,"div",2),t.\u0275\u0275template(3,g,1,2,"mbo-product-info-movement",3),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(4,d,2,1,"mbo-message-empty",4),t.\u0275\u0275elementEnd()),2&S&&(t.\u0275\u0275property("hidden",x.requesting),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",x.header),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngForOf",null==x.productMovements?null:x.productMovements.firstPage),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",null==x.productMovements?null:x.productMovements.isEmpty))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,a.P8,l.K,r.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),f})()},91248:(V,E,o)=>{o.d(E,{I:()=>p});var i=o(17007),e=o(30263),t=o(99877);function a(v,r){if(1&v&&t.\u0275\u0275element(0,"bocc-amount",10),2&v){const l=t.\u0275\u0275nextContext().$implicit;t.\u0275\u0275property("amount",l.value)("currencyCode",l.currencyCode)}}function m(v,r){if(1&v&&(t.\u0275\u0275elementStart(0,"span"),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&v){const l=t.\u0275\u0275nextContext().$implicit,y=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",y.incognito||y.section.incognito?l.mask:l.value," ")}}function u(v,r){if(1&v){const l=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",11),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(l);const h=t.\u0275\u0275nextContext().$implicit;return t.\u0275\u0275resetView(h.action.click())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()}if(2&v){const l=t.\u0275\u0275nextContext().$implicit;t.\u0275\u0275property("suffixIcon",l.action.icon),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(l.action.label)}}function s(v,r){if(1&v&&(t.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"label",6),t.\u0275\u0275template(5,a,1,2,"bocc-amount",7),t.\u0275\u0275template(6,m,2,1,"span",8),t.\u0275\u0275elementEnd()(),t.\u0275\u0275template(7,u,3,2,"button",9),t.\u0275\u0275elementEnd()),2&v){const l=r.$implicit,y=t.\u0275\u0275nextContext();t.\u0275\u0275property("hidden",(null==l?null:l.currencyCode)!==y.currencyCode),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",l.label," "),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",l.money),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!l.money),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",l.action&&!(y.incognito||y.section.incognito))}}let p=(()=>{class v{constructor(){this.currencyCode="COP"}}return v.\u0275fac=function(l){return new(l||v)},v.\u0275cmp=t.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(l,y){1&l&&(t.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),t.\u0275\u0275template(2,s,8,5,"li",2),t.\u0275\u0275elementEnd()()),2&l&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("ngForOf",y.section.datas))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),v})()},4663:(V,E,o)=>{o.d(E,{c:()=>r});var i=o(17007),e=o(99877),n=o(30263),a=o(27302);function m(l,y){1&l&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function u(l,y){if(1&l&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&l){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",h.title," ")}}function s(l,y){if(1&l){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const b=e.\u0275\u0275restoreView(h).$implicit,f=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(f.onProduct(b))}),e.\u0275\u0275elementEnd()}if(2&l){const h=y.$implicit,g=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",h.color)("icon",h.logo)("title",h.nickname)("number",h.publicNumber)("ghost",g.ghost)("amount",h.amount)("tagAval",h.tagAvalFormat),e.\u0275\u0275attribute("amount-status",g.amountColorProduct(h))}}function p(l,y){1&l&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const v=["*"];let r=(()=>{class l{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(h){return h.amount>0?"success":h.amount<0?"danger":"empty"}onProduct(h){this.select.emit(h)}}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:v,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(h,g){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,u,2,1,"div",5),e.\u0275\u0275template(8,s,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,p,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",g.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",g.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!g.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!g.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,n.w_,n.P8,a.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),l})()},13961:(V,E,o)=>{o.d(E,{Z:()=>m});var i=o(17007),e=o(27302),t=o(10119),n=o(99877);let m=(()=>{class u{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(p){this.portal=p}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=n.\u0275\u0275defineComponent({type:u,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(p,v){1&p&&(n.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),n.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"p"),n.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),n.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"p"),n.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),n.\u0275\u0275elementEnd()()()),2&p&&n.\u0275\u0275property("headerActionRight",v.headerAction)("gradient",!0)},dependencies:[i.CommonModule,e.Nu,t.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),u})()},66709:(V,E,o)=>{o.d(E,{s:()=>m});var i=o(17007),c=o(99877),e=o(30263),t=o(87542);let n=(()=>{class u{ngBoccPortal(p){}}return u.\u0275fac=function(p){return new(p||u)},u.\u0275cmp=c.\u0275\u0275defineComponent({type:u,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(p,v){1&p&&(c.\u0275\u0275elementStart(0,"div",0)(1,"label",1),c.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),c.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),c.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(11,"p",4),c.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),c.\u0275\u0275element(13,"br"),c.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),c.\u0275\u0275element(15,"br"),c.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),c.\u0275\u0275elementStart(17,"span"),c.\u0275\u0275text(18,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(19," Configuraci\xf3n "),c.\u0275\u0275elementStart(20,"span"),c.\u0275\u0275text(21,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(22," Seguridad "),c.\u0275\u0275elementStart(23,"span"),c.\u0275\u0275text(24,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(25," Activar Token Mobile."),c.\u0275\u0275element(26,"br"),c.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),c.\u0275\u0275elementEnd()()()())},dependencies:[i.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),u})();const a=["*"];let m=(()=>{class u{constructor(p,v){this.ref=p,this.bottomSheetService=v,this.verifying=!1,this.tokenLength=t.Xi,this.code=new c.EventEmitter,this.tokenControls=new t.b2}ngOnInit(){const p=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(n),setTimeout(()=>{p?.focus()},120)}onAutocomplete(p){this.code.emit(p)}onInfo(){this.infoSheet?.open()}}return u.\u0275fac=function(p){return new(p||u)(c.\u0275\u0275directiveInject(c.ElementRef),c.\u0275\u0275directiveInject(e.fG))},u.\u0275cmp=c.\u0275\u0275defineComponent({type:u,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(p,v){1&p&&(c.\u0275\u0275projectionDef(),c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275projection(2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"p",2),c.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"p",2),c.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),c.\u0275\u0275elementStart(7,"a"),c.\u0275\u0275text(8),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(9,". "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(10,"div",3),c.\u0275\u0275element(11,"img",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),c.\u0275\u0275listener("autocomplete",function(l){return v.onAutocomplete(l)}),c.\u0275\u0275text(13," Ingresa tu clave "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(14,"button",6),c.\u0275\u0275listener("click",function(){return v.onInfo()}),c.\u0275\u0275elementStart(15,"span"),c.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),c.\u0275\u0275elementEnd()()()),2&p&&(c.\u0275\u0275advance(8),c.\u0275\u0275textInterpolate1("",v.tokenLength," d\xedgitos"),c.\u0275\u0275advance(4),c.\u0275\u0275property("disabled",v.verifying)("formControls",v.tokenControls),c.\u0275\u0275advance(2),c.\u0275\u0275property("disabled",v.verifying))},dependencies:[i.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),u})()},88844:(V,E,o)=>{o.d(E,{YI:()=>u,tc:()=>g,iR:()=>h,jq:()=>p,Hv:()=>m,S6:()=>v,E2:()=>l,V4:()=>y,wp:()=>Z,CE:()=>S,YQ:()=>e,ND:()=>n,t1:()=>b});var i=o(6472);class c{constructor(C){this.value=C}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:C}){return this.value.id===C}filtrable(C){return(0,i.hasPattern)(this.value.name,C)}}function e(j){return j.map(C=>new c(C))}class t{constructor(C){this.currency=C}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(C){return this.currency.code===C?.code}filtrable(C){return!0}}function n(j){return j.map(C=>new t(C))}var a=o(39904);class m{constructor(C){this.value=C}get title(){return this.value.label}get description(){return this.value.label}compareTo(C){return this.value.reference===C.reference}filtrable(C){return!0}}const u=a.Bf.map(j=>new m(j));class s{constructor(C,W){this.value=C,this.title=this.value.label,this.description=W?this.value.code:this.value.label}compareTo(C){return this.value===C}filtrable(C){return!0}}const p=new s(a.Gd),v=new s(a.XU),r=new s(a.t$),l=new s(a.j1),y=new s(a.k7),h=[p,v,r,l],g=[new s(a.Gd,!0),new s(a.XU,!0),new s(a.t$,!0),new s(a.j1,!0)];class d{constructor(C){this.product=C}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(C){return this.product.id===C?.id}filtrable(C){return!0}}function b(j){return j.map(C=>new d(C))}var f=o(89148);class I{constructor(C){this.filter=C}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(C){return this.value===C}filtrable(C){return!0}}const S=new I({label:"Todos los productos",short:"Todos",value:f.Gt.None}),x=new I({label:"Cuentas de ahorro",short:"Ahorros",value:f.Gt.SavingAccount}),K=new I({label:"Cuentas corriente",short:"Corrientes",value:f.Gt.CheckingAccount}),D=new I({label:"Depositos electr\xf3nicos",short:"Depositos",value:f.Gt.ElectronicDeposit}),L=new I({label:"Cuentas AFC",short:"AFC",value:f.Gt.AfcAccount}),G=new I({label:"Tarjetas de cr\xe9dito",short:"TC",value:f.Gt.CreditCard}),J=new I({label:"Inversiones",short:"Inversiones",value:f.Gt.CdtAccount}),B=new I({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:f.Gt.Loan}),U=new I({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:f.Gt.ResolvingCredit}),M=new I({label:"Productos Aval",short:"Aval",value:f.Gt.Aval}),P=new I({label:"Productos fiduciarios",short:"Fiducias",value:f.Gt.Trustfund}),F=new I({label:"Otros productos",short:"Otros",value:f.Gt.None}),Z={SDA:x,DDA:K,EDA:D,AFC:L,CCA:G,CDA:J,DLA:B,LOC:U,AVAL:M,80:P,MDA:F,NONE:F,SBA:F,VDA:F}},96977:(V,E,o)=>{o.d(E,{J:()=>u});var i=o(39904),c=o(95437),e=o(30263),t=o(9811),n=o(99877);const{LOGIN:m}=i.Z6.AUTHENTICATION;let u=(()=>{class s{constructor(v,r,l){this.modalConfirmationService=v,this.mboProvider=r,this.facialBiometricsInteractor=l}execute(){this.modalConfirmationService.execute({title:"CANCELAR REGISTRO",message:"\xbfDeseas cancelar el registro a Banca M\xf3vil?",accept:{label:"Continuar el registro"},decline:{label:"Salir",click:()=>{this.facialBiometricsInteractor.clearStoreState(),this.mboProvider.navigation.back(m)}}})}}return s.\u0275fac=function(v){return new(v||s)(n.\u0275\u0275inject(e.$e),n.\u0275\u0275inject(c.ZL),n.\u0275\u0275inject(t.U))},s.\u0275prov=n.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},85239:(V,E,o)=>{o.d(E,{g:()=>y});var i=o(99877),e=o(17007);const n=["slide"],a=["slideElement"];function m(h,g){1&h&&i.\u0275\u0275elementContainer(0)}function u(h,g){if(1&h&&i.\u0275\u0275template(0,m,1,0,"ng-container",5),2&h){i.\u0275\u0275nextContext();const d=i.\u0275\u0275reference(7);i.\u0275\u0275property("ngTemplateOutlet",d)}}function s(h,g){1&h&&i.\u0275\u0275elementContainer(0)}function p(h,g){if(1&h&&i.\u0275\u0275template(0,s,1,0,"ng-container",5),2&h){i.\u0275\u0275nextContext();const d=i.\u0275\u0275reference(7);i.\u0275\u0275property("ngTemplateOutlet",d)}}function v(h,g){if(1&h){const d=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"span",8),i.\u0275\u0275listener("click",function(){const I=i.\u0275\u0275restoreView(d).$implicit,S=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(S.goToSlide(I))}),i.\u0275\u0275elementEnd()}if(2&h){const d=g.$implicit,b=i.\u0275\u0275nextContext(2);i.\u0275\u0275classProp("active",d===b.slideIndex)}}function r(h,g){if(1&h&&(i.\u0275\u0275elementStart(0,"div",6),i.\u0275\u0275template(1,v,1,2,"span",7),i.\u0275\u0275elementEnd()),2&h){const d=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("ngForOf",d.dotIndexes)}}const l=["*"];let y=(()=>{class h{constructor(d){this.elementRef=d,this.slideChanged=new i.EventEmitter,this.showDotsTop=!0,this.autoSlide=!1,this.slideIndex=0,this.moveThreshold=50,this.autoSlideInterval=1e4,this.calculateSlideHeight(this.slideIndex)}ngAfterContentInit(){this.slides&&this.slides.length>0&&(this.initSlides(),setTimeout(()=>{this.calculateSlideHeight(this.slideIndex)},50)),this.autoSlide&&this.startAutoSlide()}ngOnDestroy(){this.stopAutoSlide()}initSlides(){if(this.slides&&this.slides.length>0){const d=this.slides.first;d&&d.nativeElement&&(this.slideWidth=d.nativeElement.clientWidth,this.dotIndexes=Array.from({length:this.slides.length},(b,f)=>f),this.calculateSlideHeight(this.slideIndex))}}prevSlide(){this.slideIndex=(this.slideIndex-1+this.dotIndexes.length)%this.dotIndexes.length,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}nextSlide(){this.slideIndex=(this.slideIndex+1)%this.dotIndexes.length,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}onTouchStart(d){this.startX=d.touches[0].clientX,this.startY=d.touches[0].clientY}onTouchMove(d){if(!this.startX||!this.startY)return;const b=d.touches[0].clientX-this.startX,f=d.touches[0].clientY-this.startY;Math.abs(b)>Math.abs(f)&&Math.abs(b)>this.moveThreshold&&(b>0?this.prevSlide():this.nextSlide(),this.startX=null,this.startY=null)}startAutoSlide(){this.autoSlideTimer_=setInterval(()=>{this.nextSlide()},this.autoSlideInterval)}stopAutoSlide(){clearInterval(this.autoSlideTimer_)}goToSlide(d){this.slideIndex=d,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}calculateSlideHeight(d){if(this.slides&&this.slides.length>0&&this.slides.length>d){const b=this.slides.toArray()[d].nativeElement.querySelector(".auto-height-box");if(b){const f=b.scrollHeight+"px",I=this.elementRef.nativeElement.querySelector(".slider-container"),S=this.elementRef.nativeElement.querySelector(".slides");I&&(I.style.height=f,S.style.height=f)}}}}return h.\u0275fac=function(d){return new(d||h)(i.\u0275\u0275directiveInject(i.ElementRef))},h.\u0275cmp=i.\u0275\u0275defineComponent({type:h,selectors:[["bocc-slider"]],contentQueries:function(d,b,f){if(1&d&&i.\u0275\u0275contentQuery(f,n,4),2&d){let I;i.\u0275\u0275queryRefresh(I=i.\u0275\u0275loadQuery())&&(b.slides=I)}},viewQuery:function(d,b){if(1&d&&i.\u0275\u0275viewQuery(a,5),2&d){let f;i.\u0275\u0275queryRefresh(f=i.\u0275\u0275loadQuery())&&(b.slideElement=f.first)}},inputs:{showDotsTop:"showDotsTop",autoSlide:"autoSlide"},outputs:{slideChanged:"slideChanged"},ngContentSelectors:l,decls:8,vars:4,consts:[[1,"bocc-slide"],[3,"ngIf"],[1,"slider-container"],[1,"slides",3,"touchstart","touchmove"],["dotsTemplate",""],[4,"ngTemplateOutlet"],[1,"dots"],["class","dot","tabindex","0",3,"active","click",4,"ngFor","ngForOf"],["tabindex","0",1,"dot",3,"click"]],template:function(d,b){1&d&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,u,1,1,"ng-template",1),i.\u0275\u0275elementStart(2,"div",2)(3,"div",3),i.\u0275\u0275listener("touchstart",function(I){return b.onTouchStart(I)})("touchmove",function(I){return b.onTouchMove(I)}),i.\u0275\u0275projection(4),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(5,p,1,1,"ng-template",1),i.\u0275\u0275template(6,r,2,1,"ng-template",null,4,i.\u0275\u0275templateRefExtractor),i.\u0275\u0275elementEnd()),2&d&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",b.showDotsTop),i.\u0275\u0275advance(2),i.\u0275\u0275styleProp("transform","translateX("+-b.slideIndex*b.slideWidth+"px)"),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!b.showDotsTop))},dependencies:[e.NgForOf,e.NgIf,e.NgTemplateOutlet],styles:['.slider-container{position:relative;width:100%;overflow:hidden}.slides{display:flex;transition:transform .5s ease}.prev,.next{position:absolute;top:50%;transform:translateY(-50%);background-color:transparent;border:none;color:#fff;font-size:20px;cursor:pointer}.prev{left:10px}.next{right:10px}.dots{text-align:center;margin-top:5px}.dot{display:inline-block;width:4px;height:4px;margin:0 5px;position:relative;background-color:#0081ff;transform:rotate(45deg)}.dot.active{background-color:#0056cb;width:5px;height:5px}.dot.active:after{content:"";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);border:1px solid #0081FF;box-sizing:border-box;transform:rotate(90deg)}\n'],encapsulation:2}),h})()},83271:(V,E,o)=>{o.r(E),o.d(E,{BoccFacialBiometricsPageModule:()=>te});var i=o(17007),c=o(78007),e=o(30263),t=o(99877);let n=(()=>{class R{}return R.\u0275fac=function(w){return new(w||R)},R.\u0275mod=t.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=t.\u0275\u0275defineInjector({imports:[i.CommonModule,e.Zl,e.P8]}),R})();var a=o(79798);let m=(()=>{class R{}return R.\u0275fac=function(w){return new(w||R)},R.\u0275mod=t.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=t.\u0275\u0275defineInjector({imports:[i.CommonModule]}),R})(),u=(()=>{class R{}return R.\u0275fac=function(w){return new(w||R)},R.\u0275mod=t.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=t.\u0275\u0275defineInjector({imports:[i.CommonModule,e.P8,e.Zl,m]}),R})(),s=(()=>{class R{}return R.\u0275fac=function(w){return new(w||R)},R.\u0275mod=t.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=t.\u0275\u0275defineInjector({imports:[i.CommonModule,e.P8,e.Zl,m]}),R})();var p=o(15861),v=o(95437),r=o(39904),l=o(57544),y=o(88844),h=o(24495),g=o(53113),d=o(9811),b=o(48774),f=o(45542),I=o(19102),S=o(60817),x=o(65887);let K=(()=>{class R{constructor(w,z,X){this.mboProvider=w,this.cdr=z,this.facialBiometricsInteractor=X,this.documents=y.iR,this.requesting=!1,this.isCapture=!1,this.termsAndConditionsControl=new l.FormControl(!1),this.policyControl=new l.FormControl(!1),this.documentNumber=new l.FormControl(void 0,[h.C1]),this.documentType=new l.FormControl({state:r.Gd,validators:[h.C1]}),this.documentControls=new l.FormGroup({controls:{documentType:this.documentType,documentNumber:this.documentNumber}})}ngOnInit(){this.backAction={id:"btn_welcome_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(r.Z6.TRANSFERS.HOME)}},this.subsTriggerCapture=this.facialBiometricsInteractor.triggerCapture.subscribe(w=>{this.isCapture=w})}ngOnDestroy(){this.subsTriggerCapture.unsubscribe()}get invalid(){return this.termsAndConditionsControl.value&&this.documentNumber.value&&!this.requesting}onSubmit(){var w=this;return(0,p.Z)(function*(){const z=new g.dp(w.documentType.value.code,w.documentNumber.value);w.requesting=!0,(yield w.facialBiometricsInteractor.verifySTep(z)).when({completed:X=>{w.facialBiometricsInteractor.saveDataParameters(X),w.facialBiometricsInteractor.handlerSheet(X)},error:X=>{w.mboProvider.toast.warning(X.messages.title,X.messages.description)}},()=>w.requesting=!1),w.cdr.detectChanges()})()}onTermsAndConditions(){this.mboProvider.openUrl(r.BA.BANK_TYC)}onPolicy(){this.mboProvider.openUrl(r.BA.BANK_TYC)}}return R.\u0275fac=function(w){return new(w||R)(t.\u0275\u0275directiveInject(v.ZL),t.\u0275\u0275directiveInject(t.ChangeDetectorRef),t.\u0275\u0275directiveInject(d.U))},R.\u0275cmp=t.\u0275\u0275defineComponent({type:R,selectors:[["bocc-facial-biometrics-welcome-page"]],decls:22,vars:11,consts:[[1,"bocc-welcome-page"],[1,"bocc-welcome-page__header"],[3,"leftAction"],["body","",1,"bocc-welcome-page__body"],[1,"body2-medium","bocc-welcome-page__body--center"],[1,"bocc-welcome-page__body__box-data"],["pageId","facial-biometrics-document",3,"documents","documentNumber","documentType","disabled"],[1,"bocc-welcome-page__body__chck"],["elementId","chck_facial-biometrics-welcome_tyc",3,"formControl","disabled"],["id","lnk_facial-biometrics-welcome_tyc",3,"click"],["elementId","chck_facial-biometrics-welcome_policy",3,"formControl","disabled"],[1,"bocc-welcome-page__footer"],["id","btn_security-welcome_submit","bocc-button","raised",3,"spinner","disabled","click"]],template:function(w,z){1&w&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3),t.\u0275\u0275element(4,"mbo-bank-logo"),t.\u0275\u0275elementStart(5,"div",4),t.\u0275\u0275text(6," Registro a Banca M\xf3vil "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"div",5),t.\u0275\u0275element(8,"mbo-document-customer",6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(9,"div",7)(10,"bocc-checkbox-label",8),t.\u0275\u0275text(11," Acepto "),t.\u0275\u0275elementStart(12,"a",9),t.\u0275\u0275listener("click",function(){return z.onTermsAndConditions()}),t.\u0275\u0275text(13," T\xe9rminos y condiciones "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(14,"bocc-checkbox-label",10),t.\u0275\u0275text(15," Acepto la "),t.\u0275\u0275elementStart(16,"a",9),t.\u0275\u0275listener("click",function(){return z.onPolicy()}),t.\u0275\u0275text(17," Politica de tratamiento de datos "),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(18,"div",11)(19,"button",12),t.\u0275\u0275listener("click",function(){return z.onSubmit()}),t.\u0275\u0275elementStart(20,"span"),t.\u0275\u0275text(21,"Continuar"),t.\u0275\u0275elementEnd()()()()),2&w&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",z.backAction),t.\u0275\u0275advance(6),t.\u0275\u0275property("documents",z.documents)("documentNumber",z.documentNumber)("documentType",z.documentType)("disabled",z.requesting),t.\u0275\u0275advance(2),t.\u0275\u0275property("formControl",z.termsAndConditionsControl)("disabled",z.requesting),t.\u0275\u0275advance(4),t.\u0275\u0275property("formControl",z.policyControl)("disabled",z.requesting),t.\u0275\u0275advance(5),t.\u0275\u0275property("spinner",z.requesting)("disabled",!z.invalid&&!z.isCapture||z.requesting))},dependencies:[b.J,f.P,I.r,S.a,x.X],styles:[".bocc-welcome-page[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-welcome-page__body[_ngcontent-%COMP%]{padding:var(--sizing-x8)}.bocc-welcome-page__body[_ngcontent-%COMP%]   mbo-bank-logo[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x16)}.bocc-welcome-page__body__box-data[_ngcontent-%COMP%]{padding:var(--sizing-x16) 0}.bocc-welcome-page__body__chck[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x24)}.bocc-welcome-page__body__chck[_ngcontent-%COMP%]   bocc-checkbox-label[_ngcontent-%COMP%]:nth-child(2){padding-top:var(--sizing-x8)}.bocc-welcome-page__body--center[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x8);text-align:center}.bocc-welcome-page__footer[_ngcontent-%COMP%]{padding:var(--sizing-x8)}.bocc-welcome-page__footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}"],changeDetection:0}),R})();var D=o(63560);const G={title:"Foto frontal",message:"Revisa que tu foto se encuentre bien ",messageSemibold:"iluminada, n\xedtida y alineada."},J={title:"Foto trasera",message:"Revisa que tu foto se encuentre bien ",messageSemibold:"iluminada, n\xedtida y alineada."},B={title:"Foto frontal",message:"Revisa que tu foto se encuentre bien ",messageSemibold:"iluminada, n\xedtida y alineada."};var U=o(8775),M=o(40670),P=o(85239);let F=(()=>{class R{constructor(w){this.facialBiometricsInteractor=w,this.loop=!0,this.progress="0%",this.src=D.z.Timer,this.isIndeterminate=!1,this.template=G}ngBoccPortal(w){this.portal=w}ngOnInit(){this.subsProgressBar=this.facialBiometricsInteractor.progress$.subscribe(w=>{this.progress=`${w}%`}),this.subsIsIndeterminate=this.facialBiometricsInteractor.isIndeterminate.subscribe(w=>{this.isIndeterminate=w})}ngOnDestroy(){this.subsProgressBar&&this.subsProgressBar.unsubscribe(),this.subsIsIndeterminate&&this.subsIsIndeterminate.unsubscribe()}}return R.\u0275fac=function(w){return new(w||R)(t.\u0275\u0275directiveInject(d.U))},R.\u0275cmp=t.\u0275\u0275defineComponent({type:R,selectors:[["bocc-facial-biometrics-confirmation-page"]],inputs:{loop:"loop"},decls:35,vars:7,consts:[[1,"bocc-facial-biometrics-confirmation-page"],[1,"bocc-facial-biometrics-confirmation-page__header"],[1,"mbo-loader-logo__lottie",3,"src","loop"],["for","bocc-progress-bar",1,"overline-regular"],[3,"indeterminate","width","theme"],[1,"smalltext-regular","bocc-facial-biometrics-confirmation-page__body--center"],["body","",1,"bocc-facial-biometrics-confirmation-page__body"],[1,"bocc-facial-biometrics-confirmation-page__body--center"],[3,"showDotsTop","autoSlide"],[1,"slide"],["slide",""],[1,""],[1,"bocc-facial-biometrics-confirmation__box-tab__header"],[1,"bocc-facial-biometrics-confirmation__box-tab__box-img"],["src","assets/shared/backgrounds/tips_1.jpg","alt","news"],[1,"bocc-facial-biometrics-confirmation__box-tab__body"],[1,"bocc-facial-biometrics-confirmation__box-tab__title","body1-regular"],[1,"bocc-facial-biometrics-confirmation__box-tab__message","body2-regular"],["src","assets/shared/backgrounds/tips_2.jpg","alt","news"],[1,"bocc-facial-biometrics-confirmation-page__footer"]],template:function(w,z){1&w&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-ngx-lottie",2),t.\u0275\u0275elementStart(3,"label",3),t.\u0275\u0275text(4,"Cargando tus datos biometricos..."),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(5,"bocc-progress-bar",4),t.\u0275\u0275elementStart(6,"div")(7,"p",5),t.\u0275\u0275text(8," La validaci\xf3n puede tardar algunos minutos. "),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(9,"div",6)(10,"div",7)(11,"bocc-slider",8)(12,"div",9,10)(14,"div",11)(15,"div",12)(16,"div",13),t.\u0275\u0275element(17,"img",14),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(18,"div",15)(19,"div",16),t.\u0275\u0275text(20," Realiza tus pagos en l\xednea "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(21,"p",17),t.\u0275\u0275text(22," Paga tus servicios y tarjetas desde tus canales digitales "),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(23,"div",9,10)(25,"div",11)(26,"div",12)(27,"div",13),t.\u0275\u0275element(28,"img",18),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(29,"div",15)(30,"div",16),t.\u0275\u0275text(31," Paga en pesos y d\xf3lares "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(32,"p",17),t.\u0275\u0275text(33," Paga tus compras con Mastercard usando la moneda de tu elecci\xf3n "),t.\u0275\u0275elementEnd()()()()()(),t.\u0275\u0275element(34,"div",19),t.\u0275\u0275elementEnd()()),2&w&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("src",z.src)("loop",z.loop),t.\u0275\u0275advance(3),t.\u0275\u0275property("indeterminate",z.isIndeterminate)("width",z.progress)("theme","success"),t.\u0275\u0275advance(6),t.\u0275\u0275property("showDotsTop",!1)("autoSlide",!0))},dependencies:[U.K,M.c,P.g],styles:[".bocc-facial-biometrics-confirmation-page[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-facial-biometrics-confirmation-page[_ngcontent-%COMP%]   bocc-ngx-lottie.mbo-loader-logo__lottie[_ngcontent-%COMP%]{width:64px;height:64px;margin:auto;padding:32px 0 24px}.bocc-facial-biometrics-confirmation-page[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{text-align:center}.bocc-facial-biometrics-confirmation-page[_ngcontent-%COMP%]   bocc-progress-bar[_ngcontent-%COMP%]{margin:0 auto 16px;max-width:75%}.bocc-facial-biometrics-confirmation-page__header[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;margin-top:auto;margin-bottom:auto}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]{height:65vh;background-color:var(--color-navy-900);border-top-left-radius:16px;border-top-right-radius:16px;display:flex;flex-direction:column}.bocc-facial-biometrics-confirmation-page__body--center[_ngcontent-%COMP%]{text-align:center}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:var(--color-brand-blue-700-p, #0081ff);font-size:14px}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]   .bocc-facial-biometrics-confirmation__box-tab__title[_ngcontent-%COMP%]{padding-bottom:8px;color:var(--color-carbon-lighter-50)}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]   .bocc-facial-biometrics-confirmation__box-tab__box-img[_ngcontent-%COMP%]{padding-bottom:32px}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]   .bocc-facial-biometrics-confirmation__box-tab__box-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:90%;max-height:339px;border-radius:0% 0% 16% 16%}.bocc-facial-biometrics-confirmation-page__body[_ngcontent-%COMP%]   .bocc-facial-biometrics-confirmation__box-tab__message[_ngcontent-%COMP%]{padding-left:16px;padding-right:16px;padding-bottom:16px;color:var(--color-carbon-lighter-50)}.slide[_ngcontent-%COMP%]{flex:0 0 100%;padding:0}.auto-height-box[_ngcontent-%COMP%]{height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;padding:0 var(--sizing-x7)}"]}),R})();var Z=o(96977),j=o(13973),C=o(7324);const{WELCOME:W}=r.Z6.AUTHENTICATION.FORGOT_PASSWORD;let A=(()=>{class R{constructor(w,z,X,ee,oe){this.mboProvider=w,this.modalCancel=z,this.facialBiometricsStore=X,this.facialBiometricsInteractor=ee,this.cdr=oe,this.template=G,this.requesting=!1,this.color="ocher",this.imageFrontSide=null,this.imageFrontSide=this.facialBiometricsStore.getCaptureIdFront().Image}ngOnInit(){this.stringSubscription=this.facialBiometricsInteractor.captureUpdated.subscribe(w=>{this.imageFrontSide=w,this.cdr.detectChanges()}),this.backAction={id:"btn_summary_id_front_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting,click:()=>{this.mboProvider.navigation.back(W)}},this.cancelAction={id:"btn_summary_id_front_cancel",label:"Cancelar",hidden:()=>this.requesting,click:()=>{this.modalCancel.execute()}},this.subsTriggerCapture=this.facialBiometricsInteractor.triggerCapture.subscribe(w=>{this.requesting=w})}ngOnDestroy(){this.stringSubscription.unsubscribe(),this.subsTriggerCapture.unsubscribe()}retry(){this.facialBiometricsInteractor.handlerCapture()}onSubmit(){this.facialBiometricsStore.setStep(C.$j.CAPTURE_ID_BACK),this.facialBiometricsInteractor.handlerCapture()}}return R.\u0275fac=function(w){return new(w||R)(t.\u0275\u0275directiveInject(v.ZL),t.\u0275\u0275directiveInject(Z.J),t.\u0275\u0275directiveInject(j.H),t.\u0275\u0275directiveInject(d.U),t.\u0275\u0275directiveInject(t.ChangeDetectorRef))},R.\u0275cmp=t.\u0275\u0275defineComponent({type:R,selectors:[["bocc-facial-biometrics-summary-id-front-page"]],decls:20,vars:9,consts:[[1,"bocc-summary-id-front-page"],[1,"bocc-summary-id-front-page__header"],[3,"leftAction","rightAction"],["body","",1,"bocc-summary-id-front-page__body"],[1,"bocc-summary-id-front-page__body--center"],[1,"bocc-summary-id-front-page__body__title","subtitle2-semibold"],[1,"bocc-summary-id-front-page__body__message","body2-medium"],[1,"body2-semibold"],[1,"bocc-summary-id-front-page__body__box-id"],["alt","img-if-front","loading","lazy",3,"src"],["bocc-button","flat","prefixIcon","reload",3,"disabled","click"],[1,"bocc-summary-id-front-page__footer"],["id","btn_summary_id_front_submit","bocc-button","raised",3,"disabled","spinner","click"]],template:function(w,z){1&w&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"p",6),t.\u0275\u0275text(8),t.\u0275\u0275elementStart(9,"span",7),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(11,"div",8),t.\u0275\u0275element(12,"img",9),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(13,"button",10),t.\u0275\u0275listener("click",function(){return z.retry()}),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Repetir foto"),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(16,"div",11)(17,"button",12),t.\u0275\u0275listener("click",function(){return z.onSubmit()}),t.\u0275\u0275elementStart(18,"span"),t.\u0275\u0275text(19,"Continuar"),t.\u0275\u0275elementEnd()()()()),2&w&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",z.backAction)("rightAction",z.cancelAction),t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.title," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.message," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(null==z.template?null:z.template.messageSemibold),t.\u0275\u0275advance(2),t.\u0275\u0275property("src","data:image/jpeg;base64,"+z.imageFrontSide,t.\u0275\u0275sanitizeUrl),t.\u0275\u0275advance(1),t.\u0275\u0275property("disabled",z.requesting),t.\u0275\u0275advance(4),t.\u0275\u0275property("disabled",z.requesting)("spinner",z.requesting))},dependencies:[b.J,f.P],styles:[".bocc-summary-id-front-page[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-summary-id-front-page__header[_ngcontent-%COMP%]{padding:var(--sizing-x8) var(--sizing-x4) 0 var(--sizing-x4)}.bocc-summary-id-front-page__body[_ngcontent-%COMP%]{padding:var(--spacing-16)}.bocc-summary-id-front-page__body--center[_ngcontent-%COMP%]{text-align:center}.bocc-summary-id-front-page__body__message[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x14);color:var(--color-carbon-lighter-700)}.bocc-summary-id-front-page__body__title[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x4)}.bocc-summary-id-front-page__body__box-id[_ngcontent-%COMP%]{width:289px;height:182px;border-radius:16px;border:solid var(--sizing-x4) var(--color-blue-200);margin:auto}.bocc-summary-id-front-page__body__box-id[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:289px;height:182px;border-radius:8px}.bocc-summary-id-front-page__body[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:var(--sizing-x14)}.bocc-summary-id-front-page__footer[_ngcontent-%COMP%]{margin:0 var(--sizing-x8) var(--sizing-x8) var(--sizing-x8)}.bocc-summary-id-front-page__footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}"]}),R})();const{SUMMARY_ID_FRONT:_}=r.Z6.AUTHENTICATION.ENROLLMENT.FACIAL_BIOMETRICS,Y=[{path:"confirmation",component:F},{path:"summary-id-front",component:A},{path:"summary-id-back",component:(()=>{class R{constructor(w,z,X,ee,oe){this.mboProvider=w,this.modalCancel=z,this.facialBiometricsStore=X,this.facialBiometricsInteractor=ee,this.cdr=oe,this.template=J,this.requesting=!1,this.color="ocher",this.imageBackSide=null,this.imageBackSide=this.facialBiometricsStore.getCaptureIdBack().Image}ngOnInit(){this.stringSubscription=this.facialBiometricsInteractor.captureUpdated.subscribe(w=>{this.imageBackSide=w,this.cdr.detectChanges()}),this.backAction={id:"btn_summary_id_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting,click:()=>{this.mboProvider.navigation.back(_)}},this.cancelAction={id:"btn_summary_id_back_cancel",label:"Cancelar",hidden:()=>this.requesting,click:()=>{this.modalCancel.execute()}},this.subsTriggerCapture=this.facialBiometricsInteractor.triggerCapture.subscribe(w=>{this.requesting=w})}ngOnDestroy(){this.stringSubscription.unsubscribe()}onSubmit(){this.facialBiometricsInteractor.handlerSheet()}retry(){this.facialBiometricsInteractor.handlerCapture()}}return R.\u0275fac=function(w){return new(w||R)(t.\u0275\u0275directiveInject(v.ZL),t.\u0275\u0275directiveInject(Z.J),t.\u0275\u0275directiveInject(j.H),t.\u0275\u0275directiveInject(d.U),t.\u0275\u0275directiveInject(t.ChangeDetectorRef))},R.\u0275cmp=t.\u0275\u0275defineComponent({type:R,selectors:[["bocc-facial-biometrics-summary-id-back-page"]],decls:20,vars:9,consts:[[1,"bocc-summary-id-back-page"],[1,"bocc-summary-id-back-page__header"],[3,"leftAction","rightAction"],["body","",1,"bocc-summary-id-back-page__body"],[1,"bocc-summary-id-back-page__body--center"],[1,"bocc-summary-id-back-page__body__title","subtitle2-semibold"],[1,"bocc-summary-id-back-page__body__message","body2-medium"],[1,"body2-semibold"],[1,"bocc-summary-id-back-page__body__box-id"],["alt","img-id-back","loading","lazy",3,"src"],["bocc-button","flat","prefixIcon","reload",3,"disabled","click"],[1,"bocc-summary-id-back-page__footer"],["id","btn_summary_id_back_submit","bocc-button","raised",3,"disabled","spinner","click"]],template:function(w,z){1&w&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"p",6),t.\u0275\u0275text(8),t.\u0275\u0275elementStart(9,"span",7),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(11,"div",8),t.\u0275\u0275element(12,"img",9),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(13,"button",10),t.\u0275\u0275listener("click",function(){return z.retry()}),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Repetir foto"),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(16,"div",11)(17,"button",12),t.\u0275\u0275listener("click",function(){return z.onSubmit()}),t.\u0275\u0275elementStart(18,"span"),t.\u0275\u0275text(19,"Continuar"),t.\u0275\u0275elementEnd()()()()),2&w&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",z.backAction)("rightAction",z.cancelAction),t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.title," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.message," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(null==z.template?null:z.template.messageSemibold),t.\u0275\u0275advance(2),t.\u0275\u0275property("src","data:image/jpeg;base64,"+z.imageBackSide,t.\u0275\u0275sanitizeUrl),t.\u0275\u0275advance(1),t.\u0275\u0275property("disabled",z.requesting),t.\u0275\u0275advance(4),t.\u0275\u0275property("disabled",z.requesting)("spinner",z.requesting))},dependencies:[b.J,f.P],styles:[".bocc-summary-id-back-page[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-summary-id-back-page__header[_ngcontent-%COMP%]{padding:var(--sizing-x8) var(--sizing-x4) 0 var(--sizing-x4)}.bocc-summary-id-back-page__body[_ngcontent-%COMP%]{padding:var(--spacing-16)}.bocc-summary-id-back-page__body--center[_ngcontent-%COMP%]{text-align:center}.bocc-summary-id-back-page__body__message[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x14);color:var(--color-carbon-lighter-700)}.bocc-summary-id-back-page__body__title[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x4)}.bocc-summary-id-back-page__body__box-id[_ngcontent-%COMP%]{width:289px;height:182px;border-radius:16px;border:solid var(--sizing-x4) var(--color-blue-200);margin:auto}.bocc-summary-id-back-page__body__box-id[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:289px;height:182px;border-radius:8px}.bocc-summary-id-back-page__body[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:var(--sizing-x14)}.bocc-summary-id-back-page__footer[_ngcontent-%COMP%]{margin:0 var(--sizing-x8) var(--sizing-x8) var(--sizing-x8)}.bocc-summary-id-back-page__footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}"]}),R})()},{path:"summary-face",component:(()=>{class R{constructor(w,z,X,ee,oe){this.mboProvider=w,this.modalCancel=z,this.facialBiometricsStore=X,this.facialBiometricsInteractor=ee,this.cdr=oe,this.template=B,this.requesting=!1,this.color="ocher",this.imageFace=null,this.imageFace=this.facialBiometricsStore.getCaptureFace().Image}ngOnInit(){this.stringSubscription=this.facialBiometricsInteractor.captureUpdated.subscribe(w=>{this.imageFace=w,this.cdr.detectChanges()}),this.cancelAction={id:"btn_summary_face_cancel",label:"Cancelar",hidden:()=>this.requesting,click:()=>{this.modalCancel.execute()}},this.subsTriggerCapture=this.facialBiometricsInteractor.triggerCapture.subscribe(w=>{this.requesting=w})}ngOnDestroy(){this.stringSubscription.unsubscribe(),this.subsTriggerCapture.unsubscribe()}onSubmit(){this.facialBiometricsStore.setStep(C.$j.CAPTURE_FACE),this.facialBiometricsInteractor.onSubmit()}retry(){this.facialBiometricsInteractor.handlerCapture()}}return R.\u0275fac=function(w){return new(w||R)(t.\u0275\u0275directiveInject(v.ZL),t.\u0275\u0275directiveInject(Z.J),t.\u0275\u0275directiveInject(j.H),t.\u0275\u0275directiveInject(d.U),t.\u0275\u0275directiveInject(t.ChangeDetectorRef))},R.\u0275cmp=t.\u0275\u0275defineComponent({type:R,selectors:[["bocc-facial-biometrics-summary-face-page"]],decls:20,vars:8,consts:[[1,"bocc-summary-face-page"],[1,"bocc-summary-face-page__header"],[3,"rightAction"],["body","",1,"bocc-summary-face-page__body"],[1,"bocc-summary-face-page__body--center"],[1,"bocc-summary-face-page__body__title","subtitle2-semibold"],[1,"bocc-summary-face-page__body__message","body2-medium"],[1,"body2-semibold"],[1,"bocc-summary-face-page__body__box-id"],["alt","img-face","loading","lazy",3,"src"],["bocc-button","flat","prefixIcon","reload",3,"disabled","click"],[1,"bocc-summary-face-page__footer"],["id","btn_summary_face_submit","bocc-button","raised",3,"disabled","spinner","click"]],template:function(w,z){1&w&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"p",6),t.\u0275\u0275text(8),t.\u0275\u0275elementStart(9,"span",7),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(11,"div",8),t.\u0275\u0275element(12,"img",9),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(13,"button",10),t.\u0275\u0275listener("click",function(){return z.retry()}),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Repetir foto"),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(16,"div",11)(17,"button",12),t.\u0275\u0275listener("click",function(){return z.onSubmit()}),t.\u0275\u0275elementStart(18,"span"),t.\u0275\u0275text(19,"Continuar"),t.\u0275\u0275elementEnd()()()()),2&w&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("rightAction",z.cancelAction),t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.title," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==z.template?null:z.template.message," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(null==z.template?null:z.template.messageSemibold),t.\u0275\u0275advance(2),t.\u0275\u0275property("src","data:image/jpeg;base64,"+z.imageFace,t.\u0275\u0275sanitizeUrl),t.\u0275\u0275advance(1),t.\u0275\u0275property("disabled",z.requesting),t.\u0275\u0275advance(4),t.\u0275\u0275property("disabled",z.requesting)("spinner",z.requesting))},dependencies:[b.J,f.P],styles:[".bocc-summary-face-page[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-summary-face-page__header[_ngcontent-%COMP%]{padding:var(--sizing-x8) var(--sizing-x4) 0 var(--sizing-x4)}.bocc-summary-face-page__body[_ngcontent-%COMP%]{padding:var(--spacing-16)}.bocc-summary-face-page__body--center[_ngcontent-%COMP%]{text-align:center}.bocc-summary-face-page__body__message[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x14);color:var(--color-carbon-lighter-700)}.bocc-summary-face-page__body__title[_ngcontent-%COMP%]{padding-bottom:var(--sizing-x4)}.bocc-summary-face-page__body__box-id[_ngcontent-%COMP%]{width:177px;height:177px;overflow:hidden;border-radius:50%;position:relative;margin:auto;border:solid var(--sizing-x3) var(--color-blue-200)}.bocc-summary-face-page__body__box-id[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;object-position:center;position:absolute;top:0;left:0}.bocc-summary-face-page__body[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:var(--sizing-x14)}.bocc-summary-face-page__footer[_ngcontent-%COMP%]{margin:0 var(--sizing-x8) var(--sizing-x8) var(--sizing-x8)}.bocc-summary-face-page__footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}"]}),R})()},{path:"welcome",component:K}];let te=(()=>{class R{}return R.\u0275fac=function(w){return new(w||R)},R.\u0275mod=t.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=t.\u0275\u0275defineInjector({imports:[i.CommonModule,e.Av,e.Jx,e.B4,e.D0,e.P8,n,a.rw,e.aR,a.XH,u,s,e.Zl,e.Bc,e.cp,m,c.RouterModule.forChild(Y)]}),R})()},9811:(V,E,o)=>{o.d(E,{U:()=>C});var i=o(15861),c=o(42168),e=o(30263),t=o(50203),n=o(99877),a=o(23730),m=o(13973);let u=(()=>{class W{constructor(_,T){this.facialStepBiometricsServices=_,this.facialBiometricsStore=T}document(_){try{return this.facialStepBiometricsServices.getParams({documentType:_.type,identificationNumber:_.number})}catch(T){throw console.error("Error en document():",T),T}}saveImages(){var _=this;return(0,i.Z)(function*(){const T=_.facialBiometricsStore.currentState;if(!T)throw new Error("FacialBiometricsStore.currentState es undefined");try{const N=_.makeRequestImages(T);return yield _.facialStepBiometricsServices.setDataCapture(N)}catch(N){throw console.error("Error en saveImages():",N),N}})()}startProcess(){var _=this;return(0,i.Z)(function*(){const T=_.facialBiometricsStore.currentState;if(!T)throw new Error("FacialBiometricsStore.currentState es undefined");try{const N=_.makeRequestStart(T);return yield _.facialStepBiometricsServices.startProcess(N)}catch(N){throw console.error("Error en startProcess():",N),N}})()}makeRequestImages(_){if(!(_.captureFace?.Image&&_.documentType&&_.documentNumber&&_.presignedUrls?.uploadLiveness))throw new Error("Datos insuficientes en currentState para makeRequestImages()");const T={livenes:_.captureFace.Image},N={uploadLiveness:_.presignedUrls.uploadLiveness};return _.captureFrontSide?.Image&&_.captureBackSide?.Image&&_.presignedUrls?.uploadDocumentFrontSide&&_.presignedUrls?.uploadDocumentBackSide&&(T.documentFrontSide=_.captureFrontSide.Image,T.documentBackSide=_.captureBackSide.Image,N.uploadDocumentFrontSide=_.presignedUrls.uploadDocumentFrontSide,N.uploadDocumentBackSide=_.presignedUrls.uploadDocumentBackSide),{documentType:_.documentType,identificationNumber:_.documentNumber,dataImages:T,presignedUrl:N}}makeRequestStart(_){if(!_.captureFace?.keyProcessLiveness||!_.uiDevice)throw new Error("Datos insuficientes en currentState para makeRequestStart()");return{documentType:_.documentType,identificationNumber:_.documentNumber,keyProcessLiveness:_.captureFace.keyProcessLiveness,sessionId:_.uiDevice}}}return W.\u0275fac=function(_){return new(_||W)(n.\u0275\u0275inject(a.z),n.\u0275\u0275inject(m.H))},W.\u0275prov=n.\u0275\u0275defineInjectable({token:W,factory:W.\u0275fac,providedIn:"root"}),W})();var s=o(7324),p=o(85239),v=o(96977),r=o(17007),l=o(45542),y=o(2460);const h=["foo"];function g(W,A){if(1&W){const _=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",23),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(_);const N=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(N.nextSlide())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Siguiente"),n.\u0275\u0275elementEnd()()}}function d(W,A){if(1&W){const _=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",23),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(_);const N=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(N.onNext())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Continuar"),n.\u0275\u0275elementEnd()()}}let b=(()=>{class W{constructor(_,T,N,Y){this.elementRef=_,this.modalCancel=T,this.facialBiometricsInteractor=N,this.facialBiometricsStore=Y,this.slideIndex=0,this.showButton=!0}ngBoccPortal(_){this.portal=_}ngOnInit(){this.scrollToBottom()}onNext(){var _=this;return(0,i.Z)(function*(){_.facialBiometricsStore.setStep(s.$j.CAPTURE_ID_FRONT),_.facialBiometricsInteractor.handlerCapture(),_.portal.close()})()}onCancel(){this.modalCancel.execute(),this.portal.close()}nextSlide(){this.boccSlider.nextSlide(),this.showButton=!1}scrollToBottom(){const _=this.elementRef.nativeElement.querySelector(".foo");_&&setTimeout(()=>{_.scrollIntoView({behavior:"smooth",block:"end"})},500)}}return W.\u0275fac=function(_){return new(_||W)(n.\u0275\u0275directiveInject(n.ElementRef),n.\u0275\u0275directiveInject(v.J),n.\u0275\u0275directiveInject(C),n.\u0275\u0275directiveInject(m.H))},W.\u0275cmp=n.\u0275\u0275defineComponent({type:W,selectors:[["mbo-guide-line-document-sheet"]],viewQuery:function(_,T){if(1&_&&(n.\u0275\u0275viewQuery(p.g,5),n.\u0275\u0275viewQuery(h,5)),2&_){let N;n.\u0275\u0275queryRefresh(N=n.\u0275\u0275loadQuery())&&(T.boccSlider=N.first),n.\u0275\u0275queryRefresh(N=n.\u0275\u0275loadQuery())&&(T.boxTabElement=N.first)}},decls:52,vars:2,consts:[[1,"mbo-guide-line-document-sheet"],["src","assets/shared/backgrounds/image_id.jpg","alt","img-guide-line_bkg",1,"mbo-guide-line-document-sheet--img"],[1,"mbo-guide-line-document-sheet__box-tab"],[3,"slideChanged"],[1,"slide"],["slide",""],[1,"auto-height-box"],[1,"mbo-guide-line-document-sheet__box-tab__header"],[1,"mbo-guide-line-document-sheet__box-tab__title","smalltext-semibold"],[1,"mbo-guide-line-document-sheet__box-tab__body"],[1,"mbo-guide-line-document-sheet__box-tab__message","body2-default"],[1,"body2-semibold"],[1,"mbo-guide-line-document-sheet__box-tab__box-info"],[1,"mbo-guide-line-document-sheet__box-tab__box-info--icon"],["icon","lock-circular",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-document-sheet__box-tab__info","body2-default"],["icon","product-certification",2,"color","var(--color-blue-700)"],["icon","eye-visible",2,"color","var(--color-blue-700)"],["icon","light-bulb-2",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-document-sheet__box-tab__footer"],["id","btn_login-customer_close","bocc-button","flat",3,"click"],["id","btn_login-customer_remove","bocc-button","raised",3,"click",4,"ngIf"],[1,"mbo-guide-line-document-sheet__box-tab__bottomTab","foo"],["id","btn_login-customer_remove","bocc-button","raised",3,"click"]],template:function(_,T){1&_&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"img",1),n.\u0275\u0275elementStart(2,"div",2)(3,"bocc-slider",3),n.\u0275\u0275listener("slideChanged",function(){return T.scrollToBottom()}),n.\u0275\u0275elementStart(4,"div",4,5)(6,"div",6)(7,"div",7)(8,"div",8),n.\u0275\u0275text(9," REGISTRO A BANCA M\xd3VIL "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(10,"div",9)(11,"p",10),n.\u0275\u0275text(12," Primero, toma una fotograf\xeda de tu "),n.\u0275\u0275elementStart(13,"span",11),n.\u0275\u0275text(14,"c\xe9dula de ciudadan\xeda"),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(15," por ambos lados. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(16,"div",12)(17,"div",13),n.\u0275\u0275element(18,"bocc-icon",14),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(19,"p",15),n.\u0275\u0275text(20," Tu informaci\xf3n estar\xe1 segura y solo se utilizar\xe1 para verificar tu identidad. "),n.\u0275\u0275elementEnd()()()()(),n.\u0275\u0275elementStart(21,"div",4,5)(23,"div",6)(24,"div",7)(25,"div",8),n.\u0275\u0275text(26," IMPORTANTE "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(27,"div",9)(28,"p",10),n.\u0275\u0275text(29," Antes de tomar la foto ten encuenta "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(30,"div",12)(31,"div",13),n.\u0275\u0275element(32,"bocc-icon",16),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(33,"p",15),n.\u0275\u0275text(34," Tu c\xe9dula debe estar centrada con la plantilla que aparecer\xe1 para tomar la foto. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(35,"div",12)(36,"div",13),n.\u0275\u0275element(37,"bocc-icon",17),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(38,"p",15),n.\u0275\u0275text(39," Verifica que tu foto sea clara y los textos legibles. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(40,"div",12)(41,"div",13),n.\u0275\u0275element(42,"bocc-icon",18),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(43,"p",15),n.\u0275\u0275text(44," La foto no debe tener reflejos o sombras que impidan la claridad del documento. "),n.\u0275\u0275elementEnd()()()()()(),n.\u0275\u0275elementStart(45,"div",19)(46,"button",20),n.\u0275\u0275listener("click",function(){return T.onCancel()}),n.\u0275\u0275elementStart(47,"span"),n.\u0275\u0275text(48,"Cancelar"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(49,g,3,0,"button",21),n.\u0275\u0275template(50,d,3,0,"button",21),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(51,"div",22),n.\u0275\u0275elementEnd()()),2&_&&(n.\u0275\u0275advance(49),n.\u0275\u0275property("ngIf",T.showButton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!T.showButton))},dependencies:[r.NgIf,l.P,y.Z,p.g],styles:["mbo-guide-line-document-sheet{position:relative;width:100%;display:block}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet{position:relative;display:flex;width:100%;flex-direction:column;box-sizing:border-box}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet--img{background-size:cover;background-repeat:no-repeat;background-position:center;background-attachment:fixed;display:block;width:100%}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab{background-color:var(--color-carbon-lighter-200);transform:translateY(-12px);border-top-left-radius:15px;border-top-right-radius:15px;padding-top:var(--sizing-x8);text-align:center}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-info{display:flex}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-info--icon{position:relative;background:var(--color-blue-200);border-radius:var(--sizing-x4);margin:auto 0rem;padding:var(--sizing-x3)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__title{font-size:12px;text-align:center;color:var(--color-carbon-darker-1000);padding-top:36px}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__message{text-align:center;color:var(--color-carbon-lighter-700);padding:var(--sizing-x8) 0}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__info{text-align:left;color:var(--color-carbon-lighter-700);padding:var(--sizing-x4) 0px var(--sizing-x4) var(--sizing-x4)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer{display:flex;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x8) 0 var(--sizing-x8)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button{width:100%}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button:nth-child(1){margin-right:var(--sizing-x2)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button:nth-child(2){margin-left:var(--sizing-x2)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-slide{padding:var(--sizing-x8)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__bottomTab{width:100%;height:var(--sizing-x6);background-color:var(--color-carbon-lighter-200);position:absolute}.slide{flex:0 0 100%;padding:0}.auto-height-box{height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;padding:0 var(--sizing-x7)}\n"],encapsulation:2}),W})();const f=["foo"];function I(W,A){if(1&W){const _=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",23),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(_);const N=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(N.nextSlide())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Siguiente"),n.\u0275\u0275elementEnd()()}}function S(W,A){if(1&W){const _=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",23),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(_);const N=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(N.onNext())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Continuar"),n.\u0275\u0275elementEnd()()}}let x=(()=>{class W{constructor(_,T,N,Y){this.elementRef=_,this.modalCancel=T,this.facialBiometricsInteractor=N,this.facialBiometricsStore=Y,this.showButton=!0}ngBoccPortal(_){this.portal=_}ngOnInit(){}nextSlide(){this.boccSlider.nextSlide(),this.showButton=!1}onNext(){this.facialBiometricsStore.setStep(s.$j.CAPTURE_FACE),this.facialBiometricsInteractor.handlerCapture(),this.portal.close()}onCancel(){this.modalCancel.execute(),this.portal.close()}scrollToBottom(){const _=this.elementRef.nativeElement.querySelector(".foo");_&&setTimeout(()=>{_.scrollIntoView({behavior:"smooth",block:"end"})},500)}}return W.\u0275fac=function(_){return new(_||W)(n.\u0275\u0275directiveInject(n.ElementRef),n.\u0275\u0275directiveInject(v.J),n.\u0275\u0275directiveInject(C),n.\u0275\u0275directiveInject(m.H))},W.\u0275cmp=n.\u0275\u0275defineComponent({type:W,selectors:[["mbo-guide-line-face-sheet"]],viewQuery:function(_,T){if(1&_&&(n.\u0275\u0275viewQuery(p.g,5),n.\u0275\u0275viewQuery(f,5)),2&_){let N;n.\u0275\u0275queryRefresh(N=n.\u0275\u0275loadQuery())&&(T.boccSlider=N.first),n.\u0275\u0275queryRefresh(N=n.\u0275\u0275loadQuery())&&(T.boxTabElement=N.first)}},decls:44,vars:2,consts:[[1,"mbo-guide-line-face-sheet"],["src","assets/shared/backgrounds/face-capture.png","alt","img-guide-line_bkg",1,"mbo-guide-line-face-sheet--img"],[1,"mbo-guide-line-face-sheet__box-tab"],[3,"slideChanged"],[1,"slide"],["slide",""],[1,"auto-height-box"],[1,"mbo-guide-line-face-sheet__box-tab__header"],[1,"mbo-guide-line-face-sheet__box-tab__title","smalltext-semibold"],[1,"mbo-guide-line-face-sheet__box-tab__body"],[1,"mbo-guide-line-face-sheet__box-tab__message","body2-medium"],[1,"mbo-guide-line-face-sheet__box-tab__title_","smalltext-semibold"],[1,"mbo-guide-line-face-sheet__box-tab__message_","body2-default"],[1,"mbo-guide-line-face-sheet__box-tab__box-info"],[1,"mbo-guide-line-face-sheet__box-tab__box-info--icon"],["icon","face-id",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-face-sheet__box-tab__info","body2-default"],["icon","light-bulb-2",2,"color","var(--color-blue-700)"],["icon","glasses",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-face-sheet__box-tab__footer"],["id","btn_login-customer_close","bocc-button","flat",3,"click"],["id","btn_login-customer_remove","bocc-button","raised",3,"click",4,"ngIf"],[1,"mbo-guide-line-face-sheet__box-tab__bottomTab","foo"],["id","btn_login-customer_remove","bocc-button","raised",3,"click"]],template:function(_,T){1&_&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"img",1),n.\u0275\u0275elementStart(2,"div",2)(3,"bocc-slider",3),n.\u0275\u0275listener("slideChanged",function(){return T.scrollToBottom()}),n.\u0275\u0275elementStart(4,"div",4,5)(6,"div",6)(7,"div",7)(8,"div",8),n.\u0275\u0275text(9," REGISTRO A BANCA M\xd3VIL "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(10,"div",9)(11,"p",10),n.\u0275\u0275text(12," Ahora, t\xf3mate una foto "),n.\u0275\u0275elementEnd()()()(),n.\u0275\u0275elementStart(13,"div",4,5)(15,"div",6)(16,"div",7)(17,"div",11),n.\u0275\u0275text(18," TEN ENCUENTA "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(19,"div",9)(20,"p",12),n.\u0275\u0275text(21," Antes de tomar la foto ten en cuenta "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(22,"div",13)(23,"div",14),n.\u0275\u0275element(24,"bocc-icon",15),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(25,"p",16),n.\u0275\u0275text(26," Mant\xe9n tu rostro alineado con la c\xe1mara "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(27,"div",13)(28,"div",14),n.\u0275\u0275element(29,"bocc-icon",17),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(30,"p",16),n.\u0275\u0275text(31," Tu rostro debe estar bien iluminado, evita que la luz venga detr\xe1s de ti "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(32,"div",13)(33,"div",14),n.\u0275\u0275element(34,"bocc-icon",18),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(35,"p",16),n.\u0275\u0275text(36," Recuerda no usar accesorios como gafas, tapabocas, gorros o aud\xedfonos "),n.\u0275\u0275elementEnd()()()()()(),n.\u0275\u0275elementStart(37,"div",19)(38,"button",20),n.\u0275\u0275listener("click",function(){return T.onCancel()}),n.\u0275\u0275elementStart(39,"span"),n.\u0275\u0275text(40,"Cancelar"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(41,I,3,0,"button",21),n.\u0275\u0275template(42,S,3,0,"button",21),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(43,"div",22),n.\u0275\u0275elementEnd()()),2&_&&(n.\u0275\u0275advance(41),n.\u0275\u0275property("ngIf",T.showButton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!T.showButton))},dependencies:[r.NgIf,l.P,y.Z,p.g],styles:["mbo-guide-line-face-sheet{width:100%;position:relative;display:block}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet{width:100%;position:relative;display:flex;flex-direction:column;box-sizing:border-box}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet--img{width:100%;display:block;background-size:cover;background-repeat:no-repeat;background-position:center;background-attachment:fixed}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab{background-color:var(--color-carbon-lighter-200);transform:translateY(-12px);border-top-left-radius:15px;border-top-right-radius:15px;padding-top:var(--sizing-x8);text-align:center}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-info{display:flex}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-info--icon{position:relative;background:var(--color-blue-200);border-radius:var(--sizing-x4);margin:auto 0rem;padding:var(--sizing-x3)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__title{text-align:center;color:var(--color-carbon-darker-1000);padding-top:var(--sizing-x24)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__title_{text-align:center;color:var(--color-carbon-darker-1000);padding-top:var(--sizing-x16)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__message{text-align:center;color:var(--color-carbon-lighter-700);padding-top:var(--sizing-x8);padding-bottom:var(--sizing-x16);padding-left:0;padding-right:0}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__message_{text-align:center;color:var(--color-carbon-lighter-700);padding:var(--sizing-x8) 0}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__info{text-align:left;color:var(--color-carbon-lighter-700);padding:var(--sizing-x4) 0px var(--sizing-x4) var(--sizing-x4)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer{display:flex;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x8) 0 var(--sizing-x8)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button{width:100%}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button:nth-child(1){margin-right:var(--sizing-x2)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button:nth-child(2){margin-left:var(--sizing-x2)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-slide{padding:var(--sizing-x8)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__bottomTab{width:100%;height:var(--sizing-x6);background-color:var(--color-carbon-lighter-200);position:absolute}.slide{flex:0 0 100%;padding:0;margin:auto}.auto-height-box{height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;padding:0 var(--sizing-x7)}\n"],encapsulation:2}),W})();var K=o(95437),D=o(39904),L=o(24613),G=o(96764),J=o(63805);const{LOGIN:B,ENROLLMENT:U,FORGOT_PASSWORD:M}=D.Z6.AUTHENTICATION,{CAPTURE_ID_FRONT:P,CAPTURE_FACE:F,CAPTURE_ID_BACK:Z,INIT:j}=s.$j;let C=(()=>{class W{constructor(_,T,N,Y,te,R){this.amplifyFacialBiometricsService=_,this.facialBiometricsRepository=T,this.facialBiometricsStore=N,this.mboProvider=Y,this.sheetService=te,this.verifyForgotPasswordStep=R,this.progressSource=new c.BehaviorSubject(0),this.portal=null,this.stepMappings={[P]:{method:"setCaptureFrontSide",route:U.FACIAL_BIOMETRICS.SUMMARY_ID_FRONT},[Z]:{method:"setCaptureBackSide",route:U.FACIAL_BIOMETRICS.SUMMARY_ID_BACK},[F]:{method:"setCaptureFace",route:U.FACIAL_BIOMETRICS.SUMMARY_FACE}},this.captureUpdated=new n.EventEmitter,this.triggerCapture=new n.EventEmitter,this.isIndeterminate=new n.EventEmitter,this.content=null,this.progress$=this.progressSource.asObservable(),this.liveness=4,this.cardCapture=3,this.progressDuration=240,this.isResultReceived=!1}ngOnDestroy(){this.unsubscribeFromMessages(),this.stopProgressBar()}onSubmit(){var _=this;return(0,i.Z)(function*(){_.mboProvider.loader.open("Por favor espere...");try{if(!(yield _.tryWithRetries((0,i.Z)(function*(){return!0===(yield _.facialBiometricsRepository.saveImages())?.success}))))return void _.redirectProcess(!1);if(!(yield _.tryWithRetries((0,i.Z)(function*(){return!0===(yield _.facialBiometricsRepository.startProcess())?.success}))))return void _.redirectProcess(!1);_.mboProvider.navigation.next(U.FACIAL_BIOMETRICS.CONFIRMATION),_.openSubscription()}catch(T){console.error(T),_.redirectProcess(!1)}finally{_.mboProvider.loader.close()}})()}tryWithRetries(_){return(0,i.Z)(function*(){const T=N=>new Promise(Y=>setTimeout(Y,N));for(let N=1;N<=2;N++){try{if(yield _())return!0}catch(Y){console.error(Y)}N<2&&(yield T(1e3))}return!1})()}openSubscription(){this.unsubscribeFromMessages(),this.startProgressBar(),this.amplifySubscription=this.amplifyFacialBiometricsService.publicResult().subscribe({next:_=>{try{const T=_?.data?.onBiometricsResult?.data;if(!T)return void this.handleError();const Y=JSON.parse(T)?.data;Y&&Y.message&&(this.isResultReceived=!0,Y.message===s.x5?this.handleUnsatisfactoryResult(Y):Y.message===s.gi&&this.handleSatisfactoryResult())}catch{this.handleError()}finally{this.stopProgressBar()}},error:_=>{console.error("Error en la suscripci\xf3n:",_),this.isResultReceived=!0,this.handleError()}})}handleUnsatisfactoryResult(_){this.mboProvider.toast.warning(_.detail,"Registro biom\xe9trico no exitoso"),_.rejectionCode&&s.u.includes(_.rejectionCode)||_.messageCode&&s.u.includes(_.messageCode)?this.cancelProcess():this.redirectProcess(!1)}handleSatisfactoryResult(){this.mboProvider.toast.success("Lo usaremos para validar tu identidad en transacciones m\xf3viles.","Registro biom\xe9trico exitoso"),this.redirectProcess(!0)}handleError(){this.mboProvider.toast.warning("Lo sentimos, ocurri\xf3 un error. Por favor, intenta nuevamente.","No pudimos verificar la imagen"),this.redirectProcess(!1),this.unsubscribeFromMessages(),this.stopProgressBar()}startProgressBar(){this.progressSource.next(0);let _=0;const T=100/this.progressDuration;this.progressInterval=setInterval(()=>{_<100?(_+=T,this.progressSource.next(Math.min(_,100))):clearInterval(this.progressInterval)},1e3),this.progressTimeout=setTimeout(()=>{this.onProgressCompleteUntilResponse()},1e3*this.progressDuration)}stopProgressBar(){this.progressSource.next(0),this.progressInterval&&clearInterval(this.progressInterval),this.progressTimeout&&clearTimeout(this.progressTimeout)}onProgressCompleteUntilResponse(){this.isResultReceived||(this.stopProgressBar(),this.unsubscribeFromMessages(),this.redirectProcess(!1))}redirectProcess(_){var T=this;return(0,i.Z)(function*(){try{const N=T.facialBiometricsStore.getProcessExecutor().toString();if("forgotPassword"===(T.isIndeterminate.emit(!0),N)){if(_)return T.mboProvider.navigation.next(M.PASSWORD_ASSIGNMENT);try{const Y=yield T.verifyForgotPasswordStep.rescue();return T.mboProvider.navigation.next(Y)}catch(Y){return T.mboProvider.navigation.next(Y)}}return T.mboProvider.navigation.next(B)}finally{T.isIndeterminate.emit(!1),T.clearStoreState(),T.unsubscribeFromMessages()}})()}unsubscribeFromMessages(){this.amplifySubscription&&(this.amplifySubscription.unsubscribe(),this.amplifySubscription=void 0)}verifySTep(_){var T=this;return(0,i.Z)(function*(){try{const N=yield T.facialBiometricsRepository.document(_);if(N.success){const{type:Y,number:te}=_;return T.facialBiometricsStore.setDocument(Y,te),t.F.completed(N)}throw new Error}catch(N){return t.F.error(N)}})()}verifyCaptureProcess(_){return!!_?.presignedUrls&&Object.keys(_.presignedUrls).length>1}getSheetGuideLine(){const _=this.verifyCaptureProcess(this.content);return this.facialBiometricsStore.setStep(_?P:F),_?b:x}handlerSheet(_){this.portal&&(this.portal.destroy(),this.portal=null),this.portal=this.sheetService.create(void 0===_?x:this.getSheetGuideLine(),{containerProps:{condense:!0}}),this.portal.open()}saveDataParameters(_,T){this.facialBiometricsStore.setStep(j),this.content=_,this.getInfoDevice(),this.facialBiometricsStore.setParamsSDK(_.params,_.presignedUrls),this.facialBiometricsStore.setProcessExecutor(T)}evaluateStepDdl(_){return _===F?this.liveness:this.cardCapture}handlerCapture(){const{uiDevice:_,currentStep:T}=this.facialBiometricsStore.currentState,{access_token:N}=this.facialBiometricsStore.currentState.params.tokenAdo,{baseUrl:Y,projectName:te,apiKey:R,productId:Q}=this.facialBiometricsStore.currentState.params,w=this.evaluateStepDdl(T),z=T===P;this.triggerCapture.emit(!0),capture(Y,te,R,Q,w,z,_,N).then(X=>{if(T===F&&!1===X.IsAlive)throw new Error;this.captureUpdated.emit(X.Image);const ee=this.stepMappings[T];if(ee){const{method:oe,route:ae}=ee;this.facialBiometricsStore[oe](X),this.mboProvider.navigation.next(ae)}this.triggerCapture.emit(!1)}).catch(()=>{this.mboProvider.toast.warning("Lo sentimos, por favor sigue los pasos para finalizar tu registro.","No pudimos verificar la imagen")}).finally(()=>{this.triggerCapture.emit(!1)})}getInfoDevice(){G.Device.getId().then(_=>{this.facialBiometricsStore.setUiDevice(_?.identifier)}).catch(_=>{console.error("Error al obtener el deviceId:",_)})}clearStoreState(){this.facialBiometricsStore.clearState()}cancelProcess(){this.unsubscribeFromMessages(),this.stopProgressBar(),this.clearStoreState(),this.mboProvider.navigation.next(B)}}return W.\u0275fac=function(_){return new(_||W)(n.\u0275\u0275inject(J.K),n.\u0275\u0275inject(u),n.\u0275\u0275inject(m.H),n.\u0275\u0275inject(K.ZL),n.\u0275\u0275inject(e.fG),n.\u0275\u0275inject(L.n))},W.\u0275prov=n.\u0275\u0275defineInjectable({token:W,factory:W.\u0275fac,providedIn:"root"}),W})()},63805:(V,E,o)=>{o.d(E,{K:()=>p});var i=o(42168),e=o(65520),n=o(98778),m=o(13973),u=o(99877);let p=(()=>{class v{constructor(l){this.facialBiometricsStore=l,this.subscriptionInstance=null,this.api=new e.GraphQLAPIClass}publicResult(){const y={sessionId:this.facialBiometricsStore.currentState.uiDevice};return new i.Observable(g=>{this.subscriptionInstance=this.api.graphql(n.Amplify,{query:"\n      subscription onBiometricsResult($sessionId: ID!) {\n        onBiometricsResult(sessionId: $sessionId) {\n          sessionId\n          date\n          code\n          data\n        }\n      }\n    ",variables:y,authMode:"apiKey"});const d=this.subscriptionInstance.subscribe({next:b=>{g.next(b)},error:b=>{g.error(b)},complete:()=>{g.complete()}});return()=>{d&&d.unsubscribe()}})}}return v.\u0275fac=function(l){return new(l||v)(u.\u0275\u0275inject(m.H))},v.\u0275prov=u.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},50203:(V,E,o)=>{o.d(E,{F:()=>e});var i=o(98699);class e extends i.PartialSealed{static error(n){return new e("error",n)}static completed(n){return new e("completed",n)}}},23730:(V,E,o)=>{o.d(E,{z:()=>u});var i=o(15861);class c{constructor(p,v,r,l){this.params=p,this.presignedUrls=v,this.success=r,this.error=l}get messages(){return{title:"Lo sentimos, por favor sigue los pasos para finalizar tu registro.",description:"No pudimos verificar la imagen"}}static error(){return new c(void 0,null,!1,!0)}}function e(s,p=!1){return new c(s.params,s.presignedUrls,!!s.params,p)}var t=o(39904),n=o(42168),a=o(71776),m=o(99877);let u=(()=>{class s{constructor(v){this.http=v}getParams(v){return(0,n.firstValueFrom)(this.http.post(t.bV.FACIAL_BIOMETRICS.GET_PARAMS,{...v}).pipe((0,n.map)(r=>e(r)))).catch(({error:r})=>r?e(r,!0):c.error())}setDataCapture(v){var r=this;return(0,i.Z)(function*(){try{return yield(0,n.firstValueFrom)(r.http.post(t.bV.FACIAL_BIOMETRICS.UPLOAD_IMAGES,{...v}).pipe((0,n.map)(l=>l)))}catch(l){return console.error("Error en setDataCapture:",l),{success:!1,httpCode:"500",msgRsHdr:null}}})()}startProcess(v){var r=this;return(0,i.Z)(function*(){try{return yield(0,n.firstValueFrom)(r.http.post(t.bV.FACIAL_BIOMETRICS.START_ANALYSIS,{...v}).pipe((0,n.map)(l=>l)))}catch(l){return console.error("Error en startProcess:",l),{success:!1,httpCode:500,msgRsHdr:null}}})()}}return s.\u0275fac=function(v){return new(v||s)(m.\u0275\u0275inject(a.HttpClient))},s.\u0275prov=m.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},13973:(V,E,o)=>{o.d(E,{H:()=>n});var i=o(20691),e=o(99877);let n=(()=>{class a extends i.Store{constructor(){super({})}setDocument(u,s){this.reduce(p=>({...p,documentType:u,documentNumber:s}))}setParamsSDK(u,s){this.reduce(p=>({...p,params:u,presignedUrls:s}))}setUiDevice(u){this.reduce(s=>({...s,uiDevice:u}))}setStep(u){this.reduce(s=>({...s,currentStep:u}))}setCaptureFace(u){this.reduce(s=>({...s,captureFace:u}))}setCaptureFrontSide(u){this.reduce(s=>({...s,captureFrontSide:u}))}setCaptureBackSide(u){this.reduce(s=>({...s,captureBackSide:u}))}setUrlLiveness(u){this.reduce(s=>({...s,UploadLiveness:u}))}setUrlDocument(u,s){this.reduce(p=>({...p,UploadDocumentFrontSide:u,UploadDocumentBackSide:s}))}setProcessExecutor(u){this.reduce(s=>({...s,processExecutor:u}))}clearState(){this.reduce(()=>({}))}getCaptureIdFront(){return this.select(({captureFrontSide:u})=>u)}getCaptureIdBack(){return this.select(({captureBackSide:u})=>u)}getCaptureFace(){return this.select(({captureFace:u})=>u)}getProcessExecutor(){return this.select(({processExecutor:u})=>u)}}return a.\u0275fac=function(u){return new(u||a)},a.\u0275prov=e.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},7324:(V,E,o)=>{o.d(E,{$j:()=>i,gi:()=>c,u:()=>t,x5:()=>e});var i=(()=>{return(n=i||(i={})).INIT="INIT",n.CAPTURE_ID_FRONT="CAPTURE ID FRONT",n.CAPTURE_ID_BACK="CAPTURE ID BACK",n.CAPTURE_FACE="CAPTURE FACE",i;var n})();const c="SATISFACTORIO",e="NO SATISFACTORIO",t=["NSA01-3","NSA01-4","NSA01-5","NSA01-6","NSA01-7","NSA01-14","NSA01-15","NSA01-16","NSA09","NSA05","NSA04","NSA03","NSA02"]},22816:(V,E,o)=>{o.d(E,{S:()=>i});class i{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);