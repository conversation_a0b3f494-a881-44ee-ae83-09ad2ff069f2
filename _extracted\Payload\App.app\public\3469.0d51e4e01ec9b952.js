(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3469,12],{44963:(<PERSON>,ye,<PERSON>)=>{M.d(ye,{c:()=>K});var e=M(72972),se=M(78635);let Z;const me=S=>S.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),G=S=>(void 0===Z&&(Z=void 0===S.style.animationName&&void 0!==S.style.webkitAnimationName?"-webkit-":""),Z),h=(S,y,v)=>{const q=y.startsWith("animation")?G(S):"";S.style.setProperty(q+y,v)},k=(S,y)=>{const v=y.startsWith("animation")?G(S):"";S.style.removeProperty(v+y)},F=[],P=(S=[],y)=>{if(void 0!==y){const v=Array.isArray(y)?y:[y];return[...S,...v]}return S},K=S=>{let y,v,q,V,ce,ie,f,l,a,c,p,_,oe,ae=[],A=[],z=[],Y=!1,O={},j=[],B=[],re={},pe=0,$e=!1,R=!1,E=!0,m=!1,J=!0,ue=!1;const he=S,De=[],je=[],ve=[],ge=[],x=[],$=[],D=[],le=[],Ie=[],Ae=[],Ee=[],Se="function"==typeof AnimationEffect||void 0!==e.w&&"function"==typeof e.w.AnimationEffect,Ce="function"==typeof Element&&"function"==typeof Element.prototype.animate&&Se,Be=()=>Ee,gt=(b,ee)=>{const o=ee.findIndex(r=>r.c===b);o>-1&&ee.splice(o,1)},et=(b,ee)=>((ee?.oneTimeCallback?je:De).push({c:b,o:ee}),oe),Ze=()=>{if(Ce)Ee.forEach(b=>{b.cancel()}),Ee.length=0;else{const b=ge.slice();(0,se.r)(()=>{b.forEach(ee=>{k(ee,"animation-name"),k(ee,"animation-duration"),k(ee,"animation-timing-function"),k(ee,"animation-iteration-count"),k(ee,"animation-delay"),k(ee,"animation-play-state"),k(ee,"animation-fill-mode"),k(ee,"animation-direction")})})}},ct=()=>{$.forEach(b=>{b?.parentNode&&b.parentNode.removeChild(b)}),$.length=0},tt=()=>void 0!==ce?ce:f?f.getFill():"both",Ke=()=>void 0!==a?a:void 0!==ie?ie:f?f.getDirection():"normal",Ve=()=>$e?"linear":void 0!==q?q:f?f.getEasing():"linear",Re=()=>R?0:void 0!==c?c:void 0!==v?v:f?f.getDuration():0,ze=()=>void 0!==V?V:f?f.getIterations():1,Ye=()=>void 0!==p?p:void 0!==y?y:f?f.getDelay():0,We=()=>{0!==pe&&(pe--,0===pe&&((()=>{ut(),Ie.forEach(d=>d()),Ae.forEach(d=>d());const b=E?1:0,ee=j,o=B,r=re;ge.forEach(d=>{const u=d.classList;ee.forEach(C=>u.add(C)),o.forEach(C=>u.remove(C));for(const C in r)r.hasOwnProperty(C)&&h(d,C,r[C])}),c=void 0,a=void 0,p=void 0,De.forEach(d=>d.c(b,oe)),je.forEach(d=>d.c(b,oe)),je.length=0,J=!0,E&&(m=!0),E=!0})(),f&&f.animationFinish()))},Fe=(b=!0)=>{ct();const ee=(S=>(S.forEach(y=>{for(const v in y)if(y.hasOwnProperty(v)){const q=y[v];if("easing"===v)y["animation-timing-function"]=q,delete y[v];else{const V=me(v);V!==v&&(y[V]=q,delete y[v])}}}),S))(ae);ge.forEach(o=>{if(ee.length>0){const r=((S=[])=>S.map(y=>{const v=y.offset,q=[];for(const V in y)y.hasOwnProperty(V)&&"offset"!==V&&q.push(`${V}: ${y[V]};`);return`${100*v}% { ${q.join(" ")} }`}).join(" "))(ee);_=void 0!==S?S:(S=>{let y=F.indexOf(S);return y<0&&(y=F.push(S)-1),`ion-animation-${y}`})(r);const d=((S,y,v)=>{var q;const V=(S=>{const y=void 0!==S.getRootNode?S.getRootNode():S;return y.head||y})(v),ce=G(v),ie=V.querySelector("#"+S);if(ie)return ie;const ae=(null!==(q=v.ownerDocument)&&void 0!==q?q:document).createElement("style");return ae.id=S,ae.textContent=`@${ce}keyframes ${S} { ${y} } @${ce}keyframes ${S}-alt { ${y} }`,V.appendChild(ae),ae})(_,r,o);$.push(d),h(o,"animation-duration",`${Re()}ms`),h(o,"animation-timing-function",Ve()),h(o,"animation-delay",`${Ye()}ms`),h(o,"animation-fill-mode",tt()),h(o,"animation-direction",Ke());const u=ze()===1/0?"infinite":ze().toString();h(o,"animation-iteration-count",u),h(o,"animation-play-state","paused"),b&&h(o,"animation-name",`${d.id}-alt`),(0,se.r)(()=>{h(o,"animation-name",d.id||null)})}})},$t=(b=!0)=>{(()=>{D.forEach(r=>r()),le.forEach(r=>r());const b=A,ee=z,o=O;ge.forEach(r=>{const d=r.classList;b.forEach(u=>d.add(u)),ee.forEach(u=>d.remove(u));for(const u in o)o.hasOwnProperty(u)&&h(r,u,o[u])})})(),ae.length>0&&(Ce?(ge.forEach(b=>{const ee=b.animate(ae,{id:he,delay:Ye(),duration:Re(),easing:Ve(),iterations:ze(),fill:tt(),direction:Ke()});ee.pause(),Ee.push(ee)}),Ee.length>0&&(Ee[0].onfinish=()=>{We()})):Fe(b)),Y=!0},qe=b=>{if(b=Math.min(Math.max(b,0),.9999),Ce)Ee.forEach(ee=>{ee.currentTime=ee.effect.getComputedTiming().delay+Re()*b,ee.pause()});else{const ee=`-${Re()*b}ms`;ge.forEach(o=>{ae.length>0&&(h(o,"animation-delay",ee),h(o,"animation-play-state","paused"))})}},Xe=b=>{Ee.forEach(ee=>{ee.effect.updateTiming({delay:Ye(),duration:Re(),easing:Ve(),iterations:ze(),fill:tt(),direction:Ke()})}),void 0!==b&&qe(b)},dt=(b=!0,ee)=>{(0,se.r)(()=>{ge.forEach(o=>{h(o,"animation-name",_||null),h(o,"animation-duration",`${Re()}ms`),h(o,"animation-timing-function",Ve()),h(o,"animation-delay",void 0!==ee?`-${ee*Re()}ms`:`${Ye()}ms`),h(o,"animation-fill-mode",tt()||null),h(o,"animation-direction",Ke()||null);const r=ze()===1/0?"infinite":ze().toString();h(o,"animation-iteration-count",r),b&&h(o,"animation-name",`${_}-alt`),(0,se.r)(()=>{h(o,"animation-name",_||null)})})})},Le=(b=!1,ee=!0,o)=>(b&&x.forEach(r=>{r.update(b,ee,o)}),Ce?Xe(o):dt(ee,o),oe),xe=()=>{Y&&(Ce?Ee.forEach(b=>{b.pause()}):ge.forEach(b=>{h(b,"animation-play-state","paused")}),ue=!0)},Ht=()=>{l=void 0,We()},ut=()=>{l&&clearTimeout(l)},st=b=>new Promise(ee=>{b?.sync&&(R=!0,et(()=>R=!1,{oneTimeCallback:!0})),Y||$t(),m&&(Ce?(qe(0),Xe()):dt(),m=!1),J&&(pe=x.length+1,J=!1);const o=()=>{gt(r,je),ee()},r=()=>{gt(o,ve),ee()};et(r,{oneTimeCallback:!0}),((b,ee)=>{ve.push({c:b,o:{oneTimeCallback:!0}})})(o),x.forEach(d=>{d.play()}),Ce?(Ee.forEach(b=>{b.play()}),(0===ae.length||0===ge.length)&&We()):(()=>{if(ut(),(0,se.r)(()=>{ge.forEach(b=>{ae.length>0&&h(b,"animation-play-state","running")})}),0===ae.length||0===ge.length)We();else{const b=Ye()||0,ee=Re()||0,o=ze()||1;isFinite(o)&&(l=setTimeout(Ht,b+ee*o+100)),((S,y)=>{let v;const q={passive:!0},ce=ie=>{S===ie.target&&(v&&v(),ut(),(0,se.r)(()=>{ge.forEach(b=>{k(b,"animation-duration"),k(b,"animation-delay"),k(b,"animation-play-state")}),(0,se.r)(We)}))};S&&(S.addEventListener("webkitAnimationEnd",ce,q),S.addEventListener("animationend",ce,q),v=()=>{S.removeEventListener("webkitAnimationEnd",ce,q),S.removeEventListener("animationend",ce,q)})})(ge[0])}})(),ue=!1}),pt=(b,ee)=>{const o=ae[0];return void 0===o||void 0!==o.offset&&0!==o.offset?ae=[{offset:0,[b]:ee},...ae]:o[b]=ee,oe};return oe={parentAnimation:f,elements:ge,childAnimations:x,id:he,animationFinish:We,from:pt,to:(b,ee)=>{const o=ae[ae.length-1];return void 0===o||void 0!==o.offset&&1!==o.offset?ae=[...ae,{offset:1,[b]:ee}]:o[b]=ee,oe},fromTo:(b,ee,o)=>pt(b,ee).to(b,o),parent:b=>(f=b,oe),play:st,pause:()=>(x.forEach(b=>{b.pause()}),xe(),oe),stop:()=>{x.forEach(b=>{b.stop()}),Y&&(Ze(),Y=!1),$e=!1,R=!1,J=!0,a=void 0,c=void 0,p=void 0,pe=0,m=!1,E=!0,ue=!1,ve.forEach(b=>b.c(0,oe)),ve.length=0},destroy:b=>(x.forEach(ee=>{ee.destroy(b)}),(b=>{Ze(),b&&ct()})(b),ge.length=0,x.length=0,ae.length=0,De.length=0,je.length=0,Y=!1,J=!0,oe),keyframes:b=>{const ee=ae!==b;return ae=b,ee&&(b=>{Ce?Be().forEach(ee=>{const o=ee.effect;if(o.setKeyframes)o.setKeyframes(b);else{const r=new KeyframeEffect(o.target,b,o.getTiming());ee.effect=r}}):Fe()})(ae),oe},addAnimation:b=>{if(null!=b)if(Array.isArray(b))for(const ee of b)ee.parent(oe),x.push(ee);else b.parent(oe),x.push(b);return oe},addElement:b=>{if(null!=b)if(1===b.nodeType)ge.push(b);else if(b.length>=0)for(let ee=0;ee<b.length;ee++)ge.push(b[ee]);else console.error("Invalid addElement value");return oe},update:Le,fill:b=>(ce=b,Le(!0),oe),direction:b=>(ie=b,Le(!0),oe),iterations:b=>(V=b,Le(!0),oe),duration:b=>(!Ce&&0===b&&(b=1),v=b,Le(!0),oe),easing:b=>(q=b,Le(!0),oe),delay:b=>(y=b,Le(!0),oe),getWebAnimations:Be,getKeyframes:()=>ae,getFill:tt,getDirection:Ke,getDelay:Ye,getIterations:ze,getEasing:Ve,getDuration:Re,afterAddRead:b=>(Ie.push(b),oe),afterAddWrite:b=>(Ae.push(b),oe),afterClearStyles:(b=[])=>{for(const ee of b)re[ee]="";return oe},afterStyles:(b={})=>(re=b,oe),afterRemoveClass:b=>(B=P(B,b),oe),afterAddClass:b=>(j=P(j,b),oe),beforeAddRead:b=>(D.push(b),oe),beforeAddWrite:b=>(le.push(b),oe),beforeClearStyles:(b=[])=>{for(const ee of b)O[ee]="";return oe},beforeStyles:(b={})=>(O=b,oe),beforeRemoveClass:b=>(z=P(z,b),oe),beforeAddClass:b=>(A=P(A,b),oe),onFinish:et,isRunning:()=>0!==pe&&!ue,progressStart:(b=!1,ee)=>(x.forEach(o=>{o.progressStart(b,ee)}),xe(),$e=b,Y||$t(),Le(!1,!0,ee),oe),progressStep:b=>(x.forEach(ee=>{ee.progressStep(b)}),qe(b),oe),progressEnd:(b,ee,o)=>($e=!1,x.forEach(r=>{r.progressEnd(b,ee,o)}),void 0!==o&&(c=o),m=!1,E=!0,0===b?(a="reverse"===Ke()?"normal":"reverse","reverse"===a&&(E=!1),Ce?(Le(),qe(1-ee)):(p=(1-ee)*Re()*-1,Le(!1,!1))):1===b&&(Ce?(Le(),qe(ee)):(p=ee*Re()*-1,Le(!1,!1))),void 0!==b&&!f&&st(),oe)}}},87036:(Me,ye,M)=>{M.d(ye,{E:()=>H,I:()=>h,a:()=>e,s:()=>k});const e=F=>{try{if(F instanceof h)return F.value;if(!T()||"string"!=typeof F||""===F)return F;if(F.includes("onload="))return"";const ne=document.createDocumentFragment(),w=document.createElement("div");ne.appendChild(w),w.innerHTML=F,G.forEach(S=>{const y=ne.querySelectorAll(S);for(let v=y.length-1;v>=0;v--){const q=y[v];q.parentNode?q.parentNode.removeChild(q):ne.removeChild(q);const V=Z(q);for(let ce=0;ce<V.length;ce++)se(V[ce])}});const L=Z(ne);for(let S=0;S<L.length;S++)se(L[S]);const P=document.createElement("div");P.appendChild(ne);const K=P.querySelector("div");return null!==K?K.innerHTML:P.innerHTML}catch(ne){return console.error(ne),""}},se=F=>{if(F.nodeType&&1!==F.nodeType)return;if(typeof NamedNodeMap<"u"&&!(F.attributes instanceof NamedNodeMap))return void F.remove();for(let w=F.attributes.length-1;w>=0;w--){const L=F.attributes.item(w),P=L.name;if(!me.includes(P.toLowerCase())){F.removeAttribute(P);continue}const K=L.value,S=F[P];(null!=K&&K.toLowerCase().includes("javascript:")||null!=S&&S.toLowerCase().includes("javascript:"))&&F.removeAttribute(P)}const ne=Z(F);for(let w=0;w<ne.length;w++)se(ne[w])},Z=F=>null!=F.children?F.children:F.childNodes,T=()=>{var F;const w=null===(F=window?.Ionic)||void 0===F?void 0:F.config;return!w||(w.get?w.get("sanitizerEnabled",!0):!0===w.sanitizerEnabled||void 0===w.sanitizerEnabled)},me=["class","id","href","src","name","slot"],G=["script","style","iframe","meta","link","object","embed"];class h{constructor(ne){this.value=ne}}const k=F=>{const ne=window,w=ne.Ionic;if(!w||!w.config||"Object"===w.config.constructor.name)return ne.Ionic=ne.Ionic||{},ne.Ionic.config=Object.assign(Object.assign({},ne.Ionic.config),F),ne.Ionic.config},H=!1},65069:(Me,ye,M)=>{M.d(ye,{g:()=>e});const e=(G,h,k,Q,H)=>Z(G[1],h[1],k[1],Q[1],H).map(F=>se(G[0],h[0],k[0],Q[0],F)),se=(G,h,k,Q,H)=>H*(3*h*Math.pow(H-1,2)+H*(-3*k*H+3*k+Q*H))-G*Math.pow(H-1,3),Z=(G,h,k,Q,H)=>me((Q-=H)-3*(k-=H)+3*(h-=H)-(G-=H),3*k-6*h+3*G,3*h-3*G,G).filter(ne=>ne>=0&&ne<=1),me=(G,h,k,Q)=>{if(0===G)return((G,h,k)=>{const Q=h*h-4*G*k;return Q<0?[]:[(-h+Math.sqrt(Q))/(2*G),(-h-Math.sqrt(Q))/(2*G)]})(h,k,Q);const H=(3*(k/=G)-(h/=G)*h)/3,F=(2*h*h*h-9*h*k+27*(Q/=G))/27;if(0===H)return[Math.pow(-F,1/3)];if(0===F)return[Math.sqrt(-H),-Math.sqrt(-H)];const ne=Math.pow(F/2,2)+Math.pow(H/3,3);if(0===ne)return[Math.pow(F/2,.5)-h/3];if(ne>0)return[Math.pow(-F/2+Math.sqrt(ne),1/3)-Math.pow(F/2+Math.sqrt(ne),1/3)-h/3];const w=Math.sqrt(Math.pow(-H/3,3)),L=Math.acos(-F/(2*Math.sqrt(Math.pow(-H/3,3)))),P=2*Math.pow(w,1/3);return[P*Math.cos(L/3)-h/3,P*Math.cos((L+2*Math.PI)/3)-h/3,P*Math.cos((L+4*Math.PI)/3)-h/3]}},25030:(Me,ye,M)=>{M.d(ye,{C:()=>me,a:()=>Z,d:()=>T});var e=M(15861),se=M(78635);const Z=function(){var G=(0,e.Z)(function*(h,k,Q,H,F,ne){var w;if(h)return h.attachViewToDom(k,Q,F,H);if(!(ne||"string"==typeof Q||Q instanceof HTMLElement))throw new Error("framework delegate is missing");const L="string"==typeof Q?null===(w=k.ownerDocument)||void 0===w?void 0:w.createElement(Q):Q;return H&&H.forEach(P=>L.classList.add(P)),F&&Object.assign(L,F),k.appendChild(L),yield new Promise(P=>(0,se.c)(L,P)),L});return function(k,Q,H,F,ne,w){return G.apply(this,arguments)}}(),T=(G,h)=>{if(h){if(G)return G.removeViewFromDom(h.parentElement,h);h.remove()}return Promise.resolve()},me=()=>{let G,h;return{attachViewToDom:function(){var H=(0,e.Z)(function*(F,ne,w={},L=[]){var P,K;let S;if(G=F,ne){const v="string"==typeof ne?null===(P=G.ownerDocument)||void 0===P?void 0:P.createElement(ne):ne;L.forEach(q=>v.classList.add(q)),Object.assign(v,w),G.appendChild(v),S=v,yield new Promise(q=>(0,se.c)(v,q))}else if(G.children.length>0&&("ION-MODAL"===G.tagName||"ION-POPOVER"===G.tagName)&&!(S=G.children[0]).classList.contains("ion-delegate-host")){const q=null===(K=G.ownerDocument)||void 0===K?void 0:K.createElement("div");q.classList.add("ion-delegate-host"),L.forEach(V=>q.classList.add(V)),q.append(...G.children),G.appendChild(q),S=q}const y=document.querySelector("ion-app")||document.body;return h=document.createComment("ionic teleport"),G.parentNode.insertBefore(h,G),y.appendChild(G),S??G});return function(ne,w){return H.apply(this,arguments)}}(),removeViewFromDom:()=>(G&&h&&(h.parentNode.insertBefore(G,h),h.remove()),Promise.resolve())}}},22889:(Me,ye,M)=>{M.d(ye,{G:()=>me});class se{constructor(h,k,Q,H,F){this.id=k,this.name=Q,this.disableScroll=F,this.priority=1e6*H+k,this.ctrl=h}canStart(){return!!this.ctrl&&this.ctrl.canStart(this.name)}start(){return!!this.ctrl&&this.ctrl.start(this.name,this.id,this.priority)}capture(){if(!this.ctrl)return!1;const h=this.ctrl.capture(this.name,this.id,this.priority);return h&&this.disableScroll&&this.ctrl.disableScroll(this.id),h}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}}class Z{constructor(h,k,Q,H){this.id=k,this.disable=Q,this.disableScroll=H,this.ctrl=h}block(){if(this.ctrl){if(this.disable)for(const h of this.disable)this.ctrl.disableGesture(h,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(const h of this.disable)this.ctrl.enableGesture(h,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}}const T="backdrop-no-scroll",me=new class e{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(h){var k;return new se(this,this.newID(),h.name,null!==(k=h.priority)&&void 0!==k?k:0,!!h.disableScroll)}createBlocker(h={}){return new Z(this,this.newID(),h.disable,!!h.disableScroll)}start(h,k,Q){return this.canStart(h)?(this.requestedStart.set(k,Q),!0):(this.requestedStart.delete(k),!1)}capture(h,k,Q){if(!this.start(h,k,Q))return!1;const H=this.requestedStart;let F=-1e4;if(H.forEach(ne=>{F=Math.max(F,ne)}),F===Q){this.capturedId=k,H.clear();const ne=new CustomEvent("ionGestureCaptured",{detail:{gestureName:h}});return document.dispatchEvent(ne),!0}return H.delete(k),!1}release(h){this.requestedStart.delete(h),this.capturedId===h&&(this.capturedId=void 0)}disableGesture(h,k){let Q=this.disabledGestures.get(h);void 0===Q&&(Q=new Set,this.disabledGestures.set(h,Q)),Q.add(k)}enableGesture(h,k){const Q=this.disabledGestures.get(h);void 0!==Q&&Q.delete(k)}disableScroll(h){this.disabledScroll.add(h),1===this.disabledScroll.size&&document.body.classList.add(T)}enableScroll(h){this.disabledScroll.delete(h),0===this.disabledScroll.size&&document.body.classList.remove(T)}canStart(h){return!(void 0!==this.capturedId||this.isDisabled(h))}isCaptured(){return void 0!==this.capturedId}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(h){const k=this.disabledGestures.get(h);return!!(k&&k.size>0)}newID(){return this.gestureId++,this.gestureId}}},33006:(Me,ye,M)=>{M.r(ye),M.d(ye,{MENU_BACK_BUTTON_PRIORITY:()=>Q,OVERLAY_BACK_BUTTON_PRIORITY:()=>k,blockHardwareBackButton:()=>G,shouldUseCloseWatcher:()=>me,startHardwareBackButton:()=>h});var e=M(15861),se=M(72972),Z=M(37943);M(42477);const me=()=>Z.c.get("experimentalCloseWatcher",!1)&&void 0!==se.w&&"CloseWatcher"in se.w,G=()=>{document.addEventListener("backbutton",()=>{})},h=()=>{const H=document;let F=!1;const ne=()=>{if(F)return;let w=0,L=[];const P=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(y,v){L.push({priority:y,handler:v,id:w++})}}});H.dispatchEvent(P);const K=function(){var y=(0,e.Z)(function*(v){try{if(v?.handler){const q=v.handler(S);null!=q&&(yield q)}}catch(q){console.error(q)}});return function(q){return y.apply(this,arguments)}}(),S=()=>{if(L.length>0){let y={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};L.forEach(v=>{v.priority>=y.priority&&(y=v)}),F=!0,L=L.filter(v=>v.id!==y.id),K(y).then(()=>F=!1)}};S()};if(me()){let w;const L=()=>{w?.destroy(),w=new se.w.CloseWatcher,w.onclose=()=>{ne(),L()}};L()}else H.addEventListener("backbutton",ne)},k=100,Q=99},78635:(Me,ye,M)=>{M.d(ye,{a:()=>k,b:()=>Q,c:()=>Z,d:()=>K,e:()=>P,f:()=>L,g:()=>H,h:()=>w,i:()=>h,j:()=>ce,k:()=>me,l:()=>S,m:()=>T,n:()=>ne,o:()=>y,p:()=>V,q:()=>ie,r:()=>F,s:()=>ae,t:()=>e,u:()=>v,v:()=>q});const e=(A,z=0)=>new Promise(Y=>{se(A,z,Y)}),se=(A,z=0,Y)=>{let f,O;const j={passive:!0},re=()=>{f&&f()},pe=$e=>{(void 0===$e||A===$e.target)&&(re(),Y($e))};return A&&(A.addEventListener("webkitTransitionEnd",pe,j),A.addEventListener("transitionend",pe,j),O=setTimeout(pe,z+500),f=()=>{void 0!==O&&(clearTimeout(O),O=void 0),A.removeEventListener("webkitTransitionEnd",pe,j),A.removeEventListener("transitionend",pe,j)}),re},Z=(A,z)=>{A.componentOnReady?A.componentOnReady().then(Y=>z(Y)):F(()=>z(A))},T=A=>void 0!==A.componentOnReady,me=(A,z=[])=>{const Y={};return z.forEach(f=>{A.hasAttribute(f)&&(null!==A.getAttribute(f)&&(Y[f]=A.getAttribute(f)),A.removeAttribute(f))}),Y},G=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],h=(A,z)=>{let Y=G;return z&&z.length>0&&(Y=Y.filter(f=>!z.includes(f))),me(A,Y)},k=(A,z,Y,f)=>{var O;if(typeof window<"u"){const B=null===(O=window?.Ionic)||void 0===O?void 0:O.config;if(B){const re=B.get("_ael");if(re)return re(A,z,Y,f);if(B._ael)return B._ael(A,z,Y,f)}}return A.addEventListener(z,Y,f)},Q=(A,z,Y,f)=>{var O;if(typeof window<"u"){const B=null===(O=window?.Ionic)||void 0===O?void 0:O.config;if(B){const re=B.get("_rel");if(re)return re(A,z,Y,f);if(B._rel)return B._rel(A,z,Y,f)}}return A.removeEventListener(z,Y,f)},H=(A,z=A)=>A.shadowRoot||z,F=A=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(A):"function"==typeof requestAnimationFrame?requestAnimationFrame(A):setTimeout(A),ne=A=>!!A.shadowRoot&&!!A.attachShadow,w=A=>{const z=A.closest("ion-item");return z?z.querySelector("ion-label"):null},L=A=>{if(A.focus(),A.classList.contains("ion-focusable")){const z=A.closest("ion-app");z&&z.setFocus([A])}},P=(A,z)=>{let Y;const f=A.getAttribute("aria-labelledby"),O=A.id;let j=null!==f&&""!==f.trim()?f:z+"-lbl",B=null!==f&&""!==f.trim()?document.getElementById(f):w(A);return B?(null===f&&(B.id=j),Y=B.textContent,B.setAttribute("aria-hidden","true")):""!==O.trim()&&(B=document.querySelector(`label[for="${O}"]`),B&&(""!==B.id?j=B.id:B.id=j=`${O}-lbl`,Y=B.textContent)),{label:B,labelId:j,labelText:Y}},K=(A,z,Y,f,O)=>{if(A||ne(z)){let j=z.querySelector("input.aux-input");j||(j=z.ownerDocument.createElement("input"),j.type="hidden",j.classList.add("aux-input"),z.appendChild(j)),j.disabled=O,j.name=Y,j.value=f||""}},S=(A,z,Y)=>Math.max(A,Math.min(z,Y)),y=(A,z)=>{if(!A){const Y="ASSERT: "+z;throw console.error(Y),new Error(Y)}},v=A=>A.timeStamp||Date.now(),q=A=>{if(A){const z=A.changedTouches;if(z&&z.length>0){const Y=z[0];return{x:Y.clientX,y:Y.clientY}}if(void 0!==A.pageX)return{x:A.pageX,y:A.pageY}}return{x:0,y:0}},V=A=>{const z="rtl"===document.dir;switch(A){case"start":return z;case"end":return!z;default:throw new Error(`"${A}" is not a valid value for [side]. Use "start" or "end" instead.`)}},ce=(A,z)=>{const Y=A._original||A;return{_original:A,emit:ie(Y.emit.bind(Y),z)}},ie=(A,z=0)=>{let Y;return(...f)=>{clearTimeout(Y),Y=setTimeout(A,z,...f)}},ae=(A,z)=>{if(A??(A={}),z??(z={}),A===z)return!0;const Y=Object.keys(A);if(Y.length!==Object.keys(z).length)return!1;for(const f of Y)if(!(f in z)||A[f]!==z[f])return!1;return!0}},35067:(Me,ye,M)=>{M.r(ye),M.d(ye,{GESTURE_CONTROLLER:()=>e.G,createGesture:()=>Q});var e=M(22889);const se=(w,L,P,K)=>{const S=Z(w)?{capture:!!K.capture,passive:!!K.passive}:!!K.capture;let y,v;return w.__zone_symbol__addEventListener?(y="__zone_symbol__addEventListener",v="__zone_symbol__removeEventListener"):(y="addEventListener",v="removeEventListener"),w[y](L,P,S),()=>{w[v](L,P,S)}},Z=w=>{if(void 0===T)try{const L=Object.defineProperty({},"passive",{get:()=>{T=!0}});w.addEventListener("optsTest",()=>{},L)}catch{T=!1}return!!T};let T;const h=w=>w instanceof Document?w:w.ownerDocument,Q=w=>{let L=!1,P=!1,K=!0,S=!1;const y=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},w),v=y.canStart,q=y.onWillStart,V=y.onStart,ce=y.onEnd,ie=y.notCaptured,ae=y.onMove,A=y.threshold,z=y.passive,Y=y.blurOnStart,f={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},O=((w,L,P)=>{const K=P*(Math.PI/180),S="x"===w,y=Math.cos(K),v=L*L;let q=0,V=0,ce=!1,ie=0;return{start(ae,A){q=ae,V=A,ie=0,ce=!0},detect(ae,A){if(!ce)return!1;const z=ae-q,Y=A-V,f=z*z+Y*Y;if(f<v)return!1;const O=Math.sqrt(f),j=(S?z:Y)/O;return ie=j>y?1:j<-y?-1:0,ce=!1,!0},isGesture:()=>0!==ie,getDirection:()=>ie}})(y.direction,y.threshold,y.maxAngle),j=e.G.createGesture({name:w.gestureName,priority:w.gesturePriority,disableScroll:w.disableScroll}),pe=()=>{L&&(S=!1,ae&&ae(f))},$e=()=>!!j.capture()&&(L=!0,K=!1,f.startX=f.currentX,f.startY=f.currentY,f.startTime=f.currentTime,q?q(f).then(l):l(),!0),l=()=>{Y&&(()=>{if(typeof document<"u"){const m=document.activeElement;m?.blur&&m.blur()}})(),V&&V(f),K=!0},a=()=>{L=!1,P=!1,S=!1,K=!0,j.release()},c=m=>{const J=L,_=K;if(a(),_){if(H(f,m),J)return void(ce&&ce(f));ie&&ie(f)}},p=((w,L,P,K,S)=>{let y,v,q,V,ce,ie,ae,A=0;const z=R=>{A=Date.now()+2e3,L(R)&&(!v&&P&&(v=se(w,"touchmove",P,S)),q||(q=se(R.target,"touchend",f,S)),V||(V=se(R.target,"touchcancel",f,S)))},Y=R=>{A>Date.now()||L(R)&&(!ie&&P&&(ie=se(h(w),"mousemove",P,S)),ae||(ae=se(h(w),"mouseup",O,S)))},f=R=>{j(),K&&K(R)},O=R=>{B(),K&&K(R)},j=()=>{v&&v(),q&&q(),V&&V(),v=q=V=void 0},B=()=>{ie&&ie(),ae&&ae(),ie=ae=void 0},re=()=>{j(),B()},pe=(R=!0)=>{R?(y||(y=se(w,"touchstart",z,S)),ce||(ce=se(w,"mousedown",Y,S))):(y&&y(),ce&&ce(),y=ce=void 0,re())};return{enable:pe,stop:re,destroy:()=>{pe(!1),K=P=L=void 0}}})(y.el,m=>{const J=ne(m);return!(P||!K||(F(m,f),f.startX=f.currentX,f.startY=f.currentY,f.startTime=f.currentTime=J,f.velocityX=f.velocityY=f.deltaX=f.deltaY=0,f.event=m,v&&!1===v(f))||(j.release(),!j.start()))&&(P=!0,0===A?$e():(O.start(f.startX,f.startY),!0))},m=>{L?!S&&K&&(S=!0,H(f,m),requestAnimationFrame(pe)):(H(f,m),O.detect(f.currentX,f.currentY)&&(!O.isGesture()||!$e())&&E())},c,{capture:!1,passive:z}),E=()=>{a(),p.stop(),ie&&ie(f)};return{enable(m=!0){m||(L&&c(void 0),a()),p.enable(m)},destroy(){j.destroy(),p.destroy()}}},H=(w,L)=>{if(!L)return;const P=w.currentX,K=w.currentY,S=w.currentTime;F(L,w);const y=w.currentX,v=w.currentY,V=(w.currentTime=ne(L))-S;if(V>0&&V<100){const ie=(v-K)/V;w.velocityX=(y-P)/V*.7+.3*w.velocityX,w.velocityY=.7*ie+.3*w.velocityY}w.deltaX=y-w.startX,w.deltaY=v-w.startY,w.event=L},F=(w,L)=>{let P=0,K=0;if(w){const S=w.changedTouches;if(S&&S.length>0){const y=S[0];P=y.clientX,K=y.clientY}else void 0!==w.pageX&&(P=w.pageX,K=w.pageY)}L.currentX=P,L.currentY=K},ne=w=>w.timeStamp||Date.now()},16523:(Me,ye,M)=>{M.d(ye,{m:()=>w});var e=M(15861),se=M(72972),Z=M(33006),T=M(28909),me=M(78635),G=M(37943),h=M(44963);const k=L=>(0,h.c)().duration(L?400:300),Q=L=>{let P,K;const S=L.width+8,y=(0,h.c)(),v=(0,h.c)();L.isEndSide?(P=S+"px",K="0px"):(P=-S+"px",K="0px"),y.addElement(L.menuInnerEl).fromTo("transform",`translateX(${P})`,`translateX(${K})`);const V="ios"===(0,G.b)(L),ce=V?.2:.25;return v.addElement(L.backdropEl).fromTo("opacity",.01,ce),k(V).addAnimation([y,v])},H=L=>{let P,K;const S=(0,G.b)(L),y=L.width;L.isEndSide?(P=-y+"px",K=y+"px"):(P=y+"px",K=-y+"px");const v=(0,h.c)().addElement(L.menuInnerEl).fromTo("transform",`translateX(${K})`,"translateX(0px)"),q=(0,h.c)().addElement(L.contentEl).fromTo("transform","translateX(0px)",`translateX(${P})`),V=(0,h.c)().addElement(L.backdropEl).fromTo("opacity",.01,.32);return k("ios"===S).addAnimation([v,q,V])},F=L=>{const P=(0,G.b)(L),K=L.width*(L.isEndSide?-1:1)+"px",S=(0,h.c)().addElement(L.contentEl).fromTo("transform","translateX(0px)",`translateX(${K})`);return k("ios"===P).addAnimation(S)},w=(()=>{const L=new Map,P=[],K=function(){var a=(0,e.Z)(function*(c){const p=yield ie(c,!0);return!!p&&p.open()});return function(p){return a.apply(this,arguments)}}(),S=function(){var a=(0,e.Z)(function*(c){const p=yield void 0!==c?ie(c,!0):ae();return void 0!==p&&p.close()});return function(p){return a.apply(this,arguments)}}(),y=function(){var a=(0,e.Z)(function*(c){const p=yield ie(c,!0);return!!p&&p.toggle()});return function(p){return a.apply(this,arguments)}}(),v=function(){var a=(0,e.Z)(function*(c,p){const E=yield ie(p);return E&&(E.disabled=!c),E});return function(p,E){return a.apply(this,arguments)}}(),q=function(){var a=(0,e.Z)(function*(c,p){const E=yield ie(p);return E&&(E.swipeGesture=c),E});return function(p,E){return a.apply(this,arguments)}}(),V=function(){var a=(0,e.Z)(function*(c){if(null!=c){const p=yield ie(c);return void 0!==p&&p.isOpen()}return void 0!==(yield ae())});return function(p){return a.apply(this,arguments)}}(),ce=function(){var a=(0,e.Z)(function*(c){const p=yield ie(c);return!!p&&!p.disabled});return function(p){return a.apply(this,arguments)}}(),ie=function(){var a=(0,e.Z)(function*(c,p=!1){if(yield l(),"start"===c||"end"===c){const m=P.filter(_=>_.side===c&&!_.disabled);if(m.length>=1)return m.length>1&&p&&(0,T.p)(`menuController queried for a menu on the "${c}" side, but ${m.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,m.map(_=>_.el)),m[0].el;const J=P.filter(_=>_.side===c);if(J.length>=1)return J.length>1&&p&&(0,T.p)(`menuController queried for a menu on the "${c}" side, but ${J.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,J.map(_=>_.el)),J[0].el}else if(null!=c)return R(m=>m.menuId===c);return R(m=>!m.disabled)||(P.length>0?P[0].el:void 0)});return function(p){return a.apply(this,arguments)}}(),ae=function(){var a=(0,e.Z)(function*(){return yield l(),re()});return function(){return a.apply(this,arguments)}}(),A=function(){var a=(0,e.Z)(function*(){return yield l(),pe()});return function(){return a.apply(this,arguments)}}(),z=function(){var a=(0,e.Z)(function*(){return yield l(),$e()});return function(){return a.apply(this,arguments)}}(),Y=(a,c)=>{L.set(a,c)},j=function(){var a=(0,e.Z)(function*(c,p,E){if($e())return!1;if(p){const m=yield ae();m&&c.el!==m&&(yield m.setOpen(!1,!1))}return c._setOpen(p,E)});return function(p,E,m){return a.apply(this,arguments)}}(),re=()=>R(a=>a._isOpen),pe=()=>P.map(a=>a.el),$e=()=>P.some(a=>a.isAnimating),R=a=>{const c=P.find(a);if(void 0!==c)return c.el},l=()=>Promise.all(Array.from(document.querySelectorAll("ion-menu")).map(a=>new Promise(c=>(0,me.c)(a,c))));return Y("reveal",F),Y("push",H),Y("overlay",Q),null==se.d||se.d.addEventListener("ionBackButton",a=>{const c=re();c&&a.detail.register(Z.MENU_BACK_BUTTON_PRIORITY,()=>c.close())}),{registerAnimation:Y,get:ie,getMenus:A,getOpen:ae,isEnabled:ce,swipeGesture:q,isAnimating:z,isOpen:V,enable:v,toggle:y,close:S,open:K,_getOpenSync:re,_createAnimation:(a,c)=>{const p=L.get(a);if(!p)throw new Error("animation not registered");return p(c)},_register:a=>{P.indexOf(a)<0&&P.push(a)},_unregister:a=>{const c=P.indexOf(a);c>-1&&P.splice(c,1)},_setOpen:j}})()},28909:(Me,ye,M)=>{M.d(ye,{a:()=>se,b:()=>Z,p:()=>e});const e=(T,...me)=>console.warn(`[Ionic Warning]: ${T}`,...me),se=(T,...me)=>console.error(`[Ionic Error]: ${T}`,...me),Z=(T,...me)=>console.error(`<${T.tagName.toLowerCase()}> must be used inside ${me.join(" or ")}.`)},42477:(Me,ye,M)=>{M.d(ye,{B:()=>ne,H:()=>$e,a:()=>Ht,b:()=>Nt,c:()=>_,d:()=>De,e:()=>b,f:()=>he,g:()=>oe,h:()=>re,i:()=>Ke,j:()=>w,r:()=>Gt,w:()=>ee});var e=M(15861);let T,me,G,h=!1,k=!1,Q=!1,H=!1,F=!1;const ne={isDev:!1,isBrowser:!0,isServer:!1,isTesting:!1},w=o=>{const r=new URL(o,Te.$resourcesUrl$);return r.origin!==Je.location.origin?r.href:r.pathname},q="s-id",V="sty-id",ae="slot-fb{display:contents}slot-fb[hidden]{display:none}",A="http://www.w3.org/1999/xlink",z={},j=o=>"object"==(o=typeof o)||"function"===o;function B(o){var r,d,u;return null!==(u=null===(d=null===(r=o.head)||void 0===r?void 0:r.querySelector('meta[name="csp-nonce"]'))||void 0===d?void 0:d.getAttribute("content"))&&void 0!==u?u:void 0}const re=(o,r,...d)=>{let u=null,C=null,I=null,X=!1,N=!1;const U=[],W=de=>{for(let fe=0;fe<de.length;fe++)u=de[fe],Array.isArray(u)?W(u):null!=u&&"boolean"!=typeof u&&((X="function"!=typeof o&&!j(u))&&(u=String(u)),X&&N?U[U.length-1].$text$+=u:U.push(X?pe(null,u):u),N=X)};if(W(d),r){r.key&&(C=r.key),r.name&&(I=r.name);{const de=r.className||r.class;de&&(r.class="object"!=typeof de?de:Object.keys(de).filter(fe=>de[fe]).join(" "))}}if("function"==typeof o)return o(null===r?{}:r,U,l);const te=pe(o,null);return te.$attrs$=r,U.length>0&&(te.$children$=U),te.$key$=C,te.$name$=I,te},pe=(o,r)=>({$flags$:0,$tag$:o,$text$:r,$elm$:null,$children$:null,$attrs$:null,$key$:null,$name$:null}),$e={},l={forEach:(o,r)=>o.map(a).forEach(r),map:(o,r)=>o.map(a).map(r).map(c)},a=o=>({vattrs:o.$attrs$,vchildren:o.$children$,vkey:o.$key$,vname:o.$name$,vtag:o.$tag$,vtext:o.$text$}),c=o=>{if("function"==typeof o.vtag){const d=Object.assign({},o.vattrs);return o.vkey&&(d.key=o.vkey),o.vname&&(d.name=o.vname),re(o.vtag,d,...o.vchildren||[])}const r=pe(o.vtag,o.vtext);return r.$attrs$=o.vattrs,r.$children$=o.vchildren,r.$key$=o.vkey,r.$name$=o.vname,r},E=(o,r,d,u,C,I,X)=>{let N,U,W,te;if(1===I.nodeType){for(N=I.getAttribute("c-id"),N&&(U=N.split("."),(U[0]===X||"0"===U[0])&&(W={$flags$:0,$hostId$:U[0],$nodeId$:U[1],$depth$:U[2],$index$:U[3],$tag$:I.tagName.toLowerCase(),$elm$:I,$attrs$:null,$children$:null,$key$:null,$name$:null,$text$:null},r.push(W),I.removeAttribute("c-id"),o.$children$||(o.$children$=[]),o.$children$[W.$index$]=W,o=W,u&&"0"===W.$depth$&&(u[W.$index$]=W.$elm$))),te=I.childNodes.length-1;te>=0;te--)E(o,r,d,u,C,I.childNodes[te],X);if(I.shadowRoot)for(te=I.shadowRoot.childNodes.length-1;te>=0;te--)E(o,r,d,u,C,I.shadowRoot.childNodes[te],X)}else if(8===I.nodeType)U=I.nodeValue.split("."),(U[1]===X||"0"===U[1])&&(N=U[0],W={$flags$:0,$hostId$:U[1],$nodeId$:U[2],$depth$:U[3],$index$:U[4],$elm$:I,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null},"t"===N?(W.$elm$=I.nextSibling,W.$elm$&&3===W.$elm$.nodeType&&(W.$text$=W.$elm$.textContent,r.push(W),I.remove(),o.$children$||(o.$children$=[]),o.$children$[W.$index$]=W,u&&"0"===W.$depth$&&(u[W.$index$]=W.$elm$))):W.$hostId$===X&&("s"===N?(W.$tag$="slot",I["s-sn"]=U[5]?W.$name$=U[5]:"",I["s-sr"]=!0,u&&(W.$elm$=xe.createElement(W.$tag$),W.$name$&&W.$elm$.setAttribute("name",W.$name$),I.parentNode.insertBefore(W.$elm$,I),I.remove(),"0"===W.$depth$&&(u[W.$index$]=W.$elm$)),d.push(W),o.$children$||(o.$children$=[]),o.$children$[W.$index$]=W):"r"===N&&(u?I.remove():(C["s-cr"]=I,I["s-cn"]=!0))));else if(o&&"style"===o.$tag$){const de=pe(null,I.textContent);de.$elm$=I,de.$index$="0",o.$children$=[de]}},m=(o,r)=>{if(1===o.nodeType){let d=0;for(;d<o.childNodes.length;d++)m(o.childNodes[d],r);if(o.shadowRoot)for(d=0;d<o.shadowRoot.childNodes.length;d++)m(o.shadowRoot.childNodes[d],r)}else if(8===o.nodeType){const d=o.nodeValue.split(".");"o"===d[0]&&(r.set(d[1]+"."+d[2],o),o.nodeValue="",o["s-en"]=d[3])}},_=o=>Tt.push(o),oe=o=>Fe(o).$modeName$,he=o=>Fe(o).$hostElement$,De=(o,r,d)=>{const u=he(o);return{emit:C=>je(u,r,{bubbles:!!(4&d),composed:!!(2&d),cancelable:!!(1&d),detail:C})}},je=(o,r,d)=>{const u=Te.ce(r,d);return o.dispatchEvent(u),u},ve=new WeakMap,ge=(o,r,d)=>{let u=Qe.get(o);rt&&d?(u=u||new CSSStyleSheet,"string"==typeof u?u=r:u.replaceSync(r)):u=r,Qe.set(o,u)},x=(o,r,d)=>{var u;const C=D(r,d),I=Qe.get(C);if(o=11===o.nodeType?o:xe,I)if("string"==typeof I){let N,X=ve.get(o=o.head||o);if(X||ve.set(o,X=new Set),!X.has(C)){if(o.host&&(N=o.querySelector(`[${V}="${C}"]`)))N.innerHTML=I;else{N=xe.createElement("style"),N.innerHTML=I;const U=null!==(u=Te.$nonce$)&&void 0!==u?u:B(xe);null!=U&&N.setAttribute("nonce",U),o.insertBefore(N,o.querySelector("link"))}4&r.$flags$&&(N.innerHTML+=ae),X&&X.add(C)}}else o.adoptedStyleSheets.includes(I)||(o.adoptedStyleSheets=[...o.adoptedStyleSheets,I]);return C},D=(o,r)=>"sc-"+(r&&32&o.$flags$?o.$tagName$+"-"+r:o.$tagName$),le=o=>o.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),Ie=(o,r,d,u,C,I)=>{if(d!==u){let X=qe(o,r),N=r.toLowerCase();if("class"===r){const U=o.classList,W=Ee(d),te=Ee(u);U.remove(...W.filter(de=>de&&!te.includes(de))),U.add(...te.filter(de=>de&&!W.includes(de)))}else if("style"===r){for(const U in d)(!u||null==u[U])&&(U.includes("-")?o.style.removeProperty(U):o.style[U]="");for(const U in u)(!d||u[U]!==d[U])&&(U.includes("-")?o.style.setProperty(U,u[U]):o.style[U]=u[U])}else if("key"!==r)if("ref"===r)u&&u(o);else if(X||"o"!==r[0]||"n"!==r[1]){const U=j(u);if((X||U&&null!==u)&&!C)try{if(o.tagName.includes("-"))o[r]=u;else{const te=u??"";"list"===r?X=!1:(null==d||o[r]!=te)&&(o[r]=te)}}catch{}let W=!1;N!==(N=N.replace(/^xlink\:?/,""))&&(r=N,W=!0),null==u||!1===u?(!1!==u||""===o.getAttribute(r))&&(W?o.removeAttributeNS(A,r):o.removeAttribute(r)):(!X||4&I||C)&&!U&&(u=!0===u?"":u,W?o.setAttributeNS(A,r,u):o.setAttribute(r,u))}else if(r="-"===r[2]?r.slice(3):qe(Je,N)?N.slice(2):N[2]+r.slice(3),d||u){const U=r.endsWith(Se);r=r.replace(Ce,""),d&&Te.rel(o,r,d,U),u&&Te.ael(o,r,u,U)}}},Ae=/\s/,Ee=o=>o?o.split(Ae):[],Se="Capture",Ce=new RegExp(Se+"$"),Oe=(o,r,d,u)=>{const C=11===r.$elm$.nodeType&&r.$elm$.host?r.$elm$.host:r.$elm$,I=o&&o.$attrs$||z,X=r.$attrs$||z;for(u of Be(Object.keys(I)))u in X||Ie(C,u,I[u],void 0,d,r.$flags$);for(u of Be(Object.keys(X)))Ie(C,u,I[u],X[u],d,r.$flags$)};function Be(o){return o.includes("ref")?[...o.filter(r=>"ref"!==r),"ref"]:o}const He=(o,r,d,u)=>{var C;const I=r.$children$[d];let N,U,W,X=0;if(h||(Q=!0,"slot"===I.$tag$&&(T&&u.classList.add(T+"-s"),I.$flags$|=I.$children$?2:1)),null!==I.$text$)N=I.$elm$=xe.createTextNode(I.$text$);else if(1&I.$flags$)N=I.$elm$=xe.createTextNode("");else{if(H||(H="svg"===I.$tag$),N=I.$elm$=xe.createElementNS(H?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",2&I.$flags$?"slot-fb":I.$tag$),H&&"foreignObject"===I.$tag$&&(H=!1),Oe(null,I,H),(o=>null!=o)(T)&&N["s-si"]!==T&&N.classList.add(N["s-si"]=T),I.$children$)for(X=0;X<I.$children$.length;++X)U=He(o,I,X,N),U&&N.appendChild(U);"svg"===I.$tag$?H=!1:"foreignObject"===N.tagName&&(H=!0)}return N["s-hn"]=G,3&I.$flags$&&(N["s-sr"]=!0,N["s-cr"]=me,N["s-sn"]=I.$name$||"",N["s-rf"]=null===(C=I.$attrs$)||void 0===C?void 0:C.ref,W=o&&o.$children$&&o.$children$[d],W&&W.$tag$===I.$tag$&&o.$elm$&&Ue(o.$elm$,!1)),N},Ue=(o,r)=>{Te.$flags$|=1;const d=Array.from(o.childNodes);for(let u=d.length-1;u>=0;u--){const C=d[u];C["s-hn"]!==G&&C["s-ol"]&&(ht(C).insertBefore(C,et(C)),C["s-ol"].remove(),C["s-ol"]=void 0,C["s-sh"]=void 0,Q=!0),r&&Ue(C,r)}Te.$flags$&=-2},nt=(o,r,d,u,C,I)=>{let N,X=o["s-cr"]&&o["s-cr"].parentNode||o;for(X.shadowRoot&&X.tagName===G&&(X=X.shadowRoot);C<=I;++C)u[C]&&(N=He(null,d,C,o),N&&(u[C].$elm$=N,X.insertBefore(N,et(r))))},mt=(o,r,d)=>{for(let u=r;u<=d;++u){const C=o[u];if(C){const I=C.$elm$;It(C),I&&(k=!0,I["s-ol"]?I["s-ol"].remove():Ue(I,!0),I.remove())}}},ot=(o,r,d=!1)=>o.$tag$===r.$tag$&&("slot"===o.$tag$?o.$name$===r.$name$:!!d||o.$key$===r.$key$),et=o=>o&&o["s-ol"]||o,ht=o=>(o["s-ol"]?o["s-ol"]:o).parentNode,Ze=(o,r,d=!1)=>{const u=r.$elm$=o.$elm$,C=o.$children$,I=r.$children$,X=r.$tag$,N=r.$text$;let U;null===N?(H="svg"===X||"foreignObject"!==X&&H,"slot"===X&&!h||Oe(o,r,H),null!==C&&null!==I?((o,r,d,u,C=!1)=>{let ke,Pe,I=0,X=0,N=0,U=0,W=r.length-1,te=r[0],de=r[W],fe=u.length-1,_e=u[0],we=u[fe];for(;I<=W&&X<=fe;)if(null==te)te=r[++I];else if(null==de)de=r[--W];else if(null==_e)_e=u[++X];else if(null==we)we=u[--fe];else if(ot(te,_e,C))Ze(te,_e,C),te=r[++I],_e=u[++X];else if(ot(de,we,C))Ze(de,we,C),de=r[--W],we=u[--fe];else if(ot(te,we,C))("slot"===te.$tag$||"slot"===we.$tag$)&&Ue(te.$elm$.parentNode,!1),Ze(te,we,C),o.insertBefore(te.$elm$,de.$elm$.nextSibling),te=r[++I],we=u[--fe];else if(ot(de,_e,C))("slot"===te.$tag$||"slot"===we.$tag$)&&Ue(de.$elm$.parentNode,!1),Ze(de,_e,C),o.insertBefore(de.$elm$,te.$elm$),de=r[--W],_e=u[++X];else{for(N=-1,U=I;U<=W;++U)if(r[U]&&null!==r[U].$key$&&r[U].$key$===_e.$key$){N=U;break}N>=0?(Pe=r[N],Pe.$tag$!==_e.$tag$?ke=He(r&&r[X],d,N,o):(Ze(Pe,_e,C),r[N]=void 0,ke=Pe.$elm$),_e=u[++X]):(ke=He(r&&r[X],d,X,o),_e=u[++X]),ke&&ht(te.$elm$).insertBefore(ke,et(te.$elm$))}I>W?nt(o,null==u[fe+1]?null:u[fe+1].$elm$,d,u,X,fe):X>fe&&mt(r,I,W)})(u,C,r,I,d):null!==I?(null!==o.$text$&&(u.textContent=""),nt(u,null,r,I,0,I.length-1)):null!==C&&mt(C,0,C.length-1),H&&"svg"===X&&(H=!1)):(U=u["s-cr"])?U.parentNode.textContent=N:o.$text$!==N&&(u.data=N)},ct=o=>{const r=o.childNodes;for(const d of r)if(1===d.nodeType){if(d["s-sr"]){const u=d["s-sn"];d.hidden=!1;for(const C of r)if(C!==d)if(C["s-hn"]!==d["s-hn"]||""!==u){if(1===C.nodeType&&(u===C.getAttribute("slot")||u===C["s-sn"])){d.hidden=!0;break}}else if(1===C.nodeType||3===C.nodeType&&""!==C.textContent.trim()){d.hidden=!0;break}}ct(d)}},Ne=[],vt=o=>{let r,d,u;for(const C of o.childNodes){if(C["s-sr"]&&(r=C["s-cr"])&&r.parentNode){d=r.parentNode.childNodes;const I=C["s-sn"];for(u=d.length-1;u>=0;u--)if(r=d[u],!r["s-cn"]&&!r["s-nr"]&&r["s-hn"]!==C["s-hn"])if(yt(r,I)){let X=Ne.find(N=>N.$nodeToRelocate$===r);k=!0,r["s-sn"]=r["s-sn"]||I,X?(X.$nodeToRelocate$["s-sh"]=C["s-hn"],X.$slotRefNode$=C):(r["s-sh"]=C["s-hn"],Ne.push({$slotRefNode$:C,$nodeToRelocate$:r})),r["s-sr"]&&Ne.map(N=>{yt(N.$nodeToRelocate$,r["s-sn"])&&(X=Ne.find(U=>U.$nodeToRelocate$===r),X&&!N.$slotRefNode$&&(N.$slotRefNode$=X.$slotRefNode$))})}else Ne.some(X=>X.$nodeToRelocate$===r)||Ne.push({$nodeToRelocate$:r})}1===C.nodeType&&vt(C)}},yt=(o,r)=>1===o.nodeType?null===o.getAttribute("slot")&&""===r||o.getAttribute("slot")===r:o["s-sn"]===r||""===r,It=o=>{o.$attrs$&&o.$attrs$.ref&&o.$attrs$.ref(null),o.$children$&&o.$children$.map(It)},bt=(o,r)=>{r&&!o.$onRenderResolve$&&r["s-p"]&&r["s-p"].push(new Promise(d=>o.$onRenderResolve$=d))},it=(o,r)=>{if(o.$flags$|=16,!(4&o.$flags$))return bt(o,o.$ancestorComponent$),ee(()=>wt(o,r));o.$flags$|=512},wt=(o,r)=>{const u=o.$lazyInstance$;let C;return r&&(o.$flags$|=256,o.$queuedListeners$&&(o.$queuedListeners$.map(([I,X])=>Re(u,I,X)),o.$queuedListeners$=void 0),C=Re(u,"componentWillLoad")),C=_t(C,()=>Re(u,"componentWillRender")),_t(C,()=>Mt(o,u,r))},_t=(o,r)=>Rt(o)?o.then(r):r(),Rt=o=>o instanceof Promise||o&&o.then&&"function"==typeof o.then,Mt=function(){var o=(0,e.Z)(function*(r,d,u){var C;const I=r.$hostElement$,N=I["s-rc"];u&&(o=>{const r=o.$cmpMeta$,d=o.$hostElement$,u=r.$flags$,I=x(d.shadowRoot?d.shadowRoot:d.getRootNode(),r,o.$modeName$);10&u&&(d["s-sc"]=I,d.classList.add(I+"-h"),2&u&&d.classList.add(I+"-s"))})(r);Ot(r,d,I,u),N&&(N.map(W=>W()),I["s-rc"]=void 0);{const W=null!==(C=I["s-p"])&&void 0!==C?C:[],te=()=>tt(r);0===W.length?te():(Promise.all(W).then(te),r.$flags$|=4,W.length=0)}});return function(d,u,C){return o.apply(this,arguments)}}(),Ot=(o,r,d,u)=>{try{r=r.render&&r.render(),o.$flags$&=-17,o.$flags$|=2,((o,r,d=!1)=>{var u,C,I,X;const N=o.$hostElement$,U=o.$cmpMeta$,W=o.$vnode$||pe(null,null),te=(o=>o&&o.$tag$===$e)(r)?r:re(null,null,r);if(G=N.tagName,U.$attrsToReflect$&&(te.$attrs$=te.$attrs$||{},U.$attrsToReflect$.map(([de,fe])=>te.$attrs$[fe]=N[de])),d&&te.$attrs$)for(const de of Object.keys(te.$attrs$))N.hasAttribute(de)&&!["key","ref","style","class"].includes(de)&&(te.$attrs$[de]=N[de]);if(te.$tag$=null,te.$flags$|=4,o.$vnode$=te,te.$elm$=W.$elm$=N.shadowRoot||N,T=N["s-sc"],h=0!=(1&U.$flags$),me=N["s-cr"],k=!1,Ze(W,te,d),Te.$flags$|=1,Q){vt(te.$elm$);for(const de of Ne){const fe=de.$nodeToRelocate$;if(!fe["s-ol"]){const _e=xe.createTextNode("");_e["s-nr"]=fe,fe.parentNode.insertBefore(fe["s-ol"]=_e,fe)}}for(const de of Ne){const fe=de.$nodeToRelocate$,_e=de.$slotRefNode$;if(_e){const we=_e.parentNode;let ke=_e.nextSibling;{let Pe=null===(u=fe["s-ol"])||void 0===u?void 0:u.previousSibling;for(;Pe;){let t=null!==(C=Pe["s-nr"])&&void 0!==C?C:null;if(t&&t["s-sn"]===fe["s-sn"]&&we===t.parentNode){for(t=t.nextSibling;t===fe||t?.["s-sr"];)t=t?.nextSibling;if(!t||!t["s-nr"]){ke=t;break}}Pe=Pe.previousSibling}}(!ke&&we!==fe.parentNode||fe.nextSibling!==ke)&&fe!==ke&&(!fe["s-hn"]&&fe["s-ol"]&&(fe["s-hn"]=fe["s-ol"].parentNode.nodeName),we.insertBefore(fe,ke),1===fe.nodeType&&(fe.hidden=null!==(I=fe["s-ih"])&&void 0!==I&&I)),fe&&"function"==typeof _e["s-rf"]&&_e["s-rf"](fe)}else 1===fe.nodeType&&(d&&(fe["s-ih"]=null!==(X=fe.hidden)&&void 0!==X&&X),fe.hidden=!0)}}k&&ct(te.$elm$),Te.$flags$&=-2,Ne.length=0,me=void 0})(o,r,u)}catch(C){Xe(C,o.$hostElement$)}return null},tt=o=>{const d=o.$hostElement$,C=o.$lazyInstance$,I=o.$ancestorComponent$;Re(C,"componentDidRender"),64&o.$flags$?Re(C,"componentDidUpdate"):(o.$flags$|=64,ze(d),Re(C,"componentDidLoad"),o.$onReadyResolve$(d),I||Ve()),o.$onInstanceResolve$(d),o.$onRenderResolve$&&(o.$onRenderResolve$(),o.$onRenderResolve$=void 0),512&o.$flags$&&jt(()=>it(o,!1)),o.$flags$&=-517},Ke=o=>{{const r=Fe(o),d=r.$hostElement$.isConnected;return d&&2==(18&r.$flags$)&&it(r,!1),d}},Ve=o=>{ze(xe.documentElement),jt(()=>je(Je,"appload",{detail:{namespace:"ionic"}}))},Re=(o,r,d)=>{if(o&&o[r])try{return o[r](d)}catch(u){Xe(u)}},ze=o=>o.classList.add("hydrated"),Ct=(o,r,d)=>{var u;const C=o.prototype;if(r.$members$){o.watchers&&(r.$watchers$=o.watchers);const I=Object.entries(r.$members$);if(I.map(([X,[N]])=>{31&N||2&d&&32&N?Object.defineProperty(C,X,{get(){return((o,r)=>Fe(this).$instanceValues$.get(r))(0,X)},set(U){((o,r,d,u)=>{const C=Fe(o),I=C.$hostElement$,X=C.$instanceValues$.get(r),N=C.$flags$,U=C.$lazyInstance$;d=((o,r)=>null==o||j(o)?o:4&r?"false"!==o&&(""===o||!!o):2&r?parseFloat(o):1&r?String(o):o)(d,u.$members$[r][0]);const W=Number.isNaN(X)&&Number.isNaN(d);if((!(8&N)||void 0===X)&&d!==X&&!W&&(C.$instanceValues$.set(r,d),U)){if(u.$watchers$&&128&N){const de=u.$watchers$[r];de&&de.map(fe=>{try{U[fe](d,X,r)}catch(_e){Xe(_e,I)}})}2==(18&N)&&it(C,!1)}})(this,X,U,r)},configurable:!0,enumerable:!0}):1&d&&64&N&&Object.defineProperty(C,X,{value(...U){var W;const te=Fe(this);return null===(W=te?.$onInstancePromise$)||void 0===W?void 0:W.then(()=>{var de;return null===(de=te.$lazyInstance$)||void 0===de?void 0:de[X](...U)})}})}),1&d){const X=new Map;C.attributeChangedCallback=function(N,U,W){Te.jmp(()=>{var te;const de=X.get(N);if(this.hasOwnProperty(de))W=this[de],delete this[de];else{if(C.hasOwnProperty(de)&&"number"==typeof this[de]&&this[de]==W)return;if(null==de){const fe=Fe(this),_e=fe?.$flags$;if(_e&&!(8&_e)&&128&_e&&W!==U){const we=fe.$lazyInstance$;(null===(te=r.$watchers$)||void 0===te?void 0:te[N])?.forEach(Pe=>{null!=we[Pe]&&we[Pe].call(we,W,U,N)})}return}}this[de]=(null!==W||"boolean"!=typeof this[de])&&W})},o.observedAttributes=Array.from(new Set([...Object.keys(null!==(u=r.$watchers$)&&void 0!==u?u:{}),...I.filter(([N,U])=>15&U[0]).map(([N,U])=>{var W;const te=U[1]||N;return X.set(te,N),512&U[0]&&(null===(W=r.$attrsToReflect$)||void 0===W||W.push([N,te])),te})]))}}return o},kt=function(){var o=(0,e.Z)(function*(r,d,u,C){let I;if(!(32&d.$flags$)){if(d.$flags$|=32,u.$lazyBundleId$){if(I=Le(u),I.then){const te=()=>{};I=yield I,te()}I.isProxied||(u.$watchers$=I.watchers,Ct(I,u,2),I.isProxied=!0);const W=()=>{};d.$flags$|=8;try{new I(d)}catch(te){Xe(te)}d.$flags$&=-9,d.$flags$|=128,W(),lt(d.$lazyInstance$)}else I=r.constructor,customElements.whenDefined(u.$tagName$).then(()=>d.$flags$|=128);if(I.style){let W=I.style;"string"!=typeof W&&(W=W[d.$modeName$=(o=>Tt.map(r=>r(o)).find(r=>!!r))(r)]);const te=D(u,d.$modeName$);if(!Qe.has(te)){const de=()=>{};ge(te,W,!!(1&u.$flags$)),de()}}}const X=d.$ancestorComponent$,N=()=>it(d,!0);X&&X["s-rc"]?X["s-rc"].push(N):N()});return function(d,u,C,I){return o.apply(this,arguments)}}(),lt=o=>{Re(o,"connectedCallback")},Lt=o=>{const r=o["s-cr"]=xe.createComment("");r["s-cn"]=!0,o.insertBefore(r,o.firstChild)},Dt=o=>{Re(o,"disconnectedCallback")},Ft=function(){var o=(0,e.Z)(function*(r){if(!(1&Te.$flags$)){const d=Fe(r);d.$rmListeners$&&(d.$rmListeners$.map(u=>u()),d.$rmListeners$=void 0),d?.$lazyInstance$?Dt(d.$lazyInstance$):d?.$onReadyPromise$&&d.$onReadyPromise$.then(()=>Dt(d.$lazyInstance$))}});return function(d){return o.apply(this,arguments)}}(),Nt=(o,r={})=>{var d;const C=[],I=r.exclude||[],X=Je.customElements,N=xe.head,U=N.querySelector("meta[charset]"),W=xe.createElement("style"),te=[],de=xe.querySelectorAll(`[${V}]`);let fe,_e=!0,we=0;for(Object.assign(Te,r),Te.$resourcesUrl$=new URL(r.resourcesUrl||"./",xe.baseURI).href,Te.$flags$|=2;we<de.length;we++)ge(de[we].getAttribute(V),le(de[we].innerHTML),!0);let ke=!1;if(o.map(Pe=>{Pe[1].map(t=>{var i;const n={$flags$:t[0],$tagName$:t[1],$members$:t[2],$listeners$:t[3]};4&n.$flags$&&(ke=!0),n.$members$=t[2],n.$listeners$=t[3],n.$attrsToReflect$=[],n.$watchers$=null!==(i=t[4])&&void 0!==i?i:{};const s=n.$tagName$,g=class extends HTMLElement{constructor(be){super(be),$t(be=this,n),1&n.$flags$&&be.attachShadow({mode:"open",delegatesFocus:!!(16&n.$flags$)})}connectedCallback(){fe&&(clearTimeout(fe),fe=null),_e?te.push(this):Te.jmp(()=>(o=>{if(!(1&Te.$flags$)){const r=Fe(o),d=r.$cmpMeta$,u=()=>{};if(1&r.$flags$)Et(o,r,d.$listeners$),r?.$lazyInstance$?lt(r.$lazyInstance$):r?.$onReadyPromise$&&r.$onReadyPromise$.then(()=>lt(r.$lazyInstance$));else{let C;if(r.$flags$|=1,C=o.getAttribute(q),C){if(1&d.$flags$){const I=x(o.shadowRoot,d,o.getAttribute("s-mode"));o.classList.remove(I+"-h",I+"-s")}((o,r,d,u)=>{const I=o.shadowRoot,X=[],U=I?[]:null,W=u.$vnode$=pe(r,null);Te.$orgLocNodes$||m(xe.body,Te.$orgLocNodes$=new Map),o[q]=d,o.removeAttribute(q),E(W,X,[],U,o,o,d),X.map(te=>{const de=te.$hostId$+"."+te.$nodeId$,fe=Te.$orgLocNodes$.get(de),_e=te.$elm$;fe&&ut&&""===fe["s-en"]&&fe.parentNode.insertBefore(_e,fe.nextSibling),I||(_e["s-hn"]=r,fe&&(_e["s-ol"]=fe,_e["s-ol"]["s-nr"]=_e)),Te.$orgLocNodes$.delete(de)}),I&&U.map(te=>{te&&I.appendChild(te)})})(o,d.$tagName$,C,r)}C||12&d.$flags$&&Lt(o);{let I=o;for(;I=I.parentNode||I.host;)if(1===I.nodeType&&I.hasAttribute("s-id")&&I["s-p"]||I["s-p"]){bt(r,r.$ancestorComponent$=I);break}}d.$members$&&Object.entries(d.$members$).map(([I,[X]])=>{if(31&X&&o.hasOwnProperty(I)){const N=o[I];delete o[I],o[I]=N}}),kt(o,r,d)}u()}})(this))}disconnectedCallback(){Te.jmp(()=>Ft(this))}componentOnReady(){return Fe(this).$onReadyPromise$}};n.$lazyBundleId$=Pe[0],!I.includes(s)&&!X.get(s)&&(C.push(s),X.define(s,Ct(g,n,1)))})}),C.length>0&&(ke&&(W.textContent+=ae),W.textContent+=C+"{visibility:hidden}.hydrated{visibility:inherit}",W.innerHTML.length)){W.setAttribute("data-styles","");const Pe=null!==(d=Te.$nonce$)&&void 0!==d?d:B(xe);null!=Pe&&W.setAttribute("nonce",Pe),N.insertBefore(W,U?U.nextSibling:N.firstChild)}_e=!1,te.length?te.map(Pe=>Pe.connectedCallback()):Te.jmp(()=>fe=setTimeout(Ve,30))},Et=(o,r,d,u)=>{d&&d.map(([C,I,X])=>{const N=Wt(o,C),U=zt(r,X),W=Xt(C);Te.ael(N,I,U,W),(r.$rmListeners$=r.$rmListeners$||[]).push(()=>Te.rel(N,I,U,W))})},zt=(o,r)=>d=>{try{256&o.$flags$?o.$lazyInstance$[r](d):(o.$queuedListeners$=o.$queuedListeners$||[]).push([r,d])}catch(u){Xe(u)}},Wt=(o,r)=>4&r?xe:8&r?Je:16&r?xe.body:o,Xt=o=>Zt?{passive:0!=(1&o),capture:0!=(2&o)}:0!=(2&o),We=new WeakMap,Fe=o=>We.get(o),Gt=(o,r)=>We.set(r.$lazyInstance$=o,r),$t=(o,r)=>{const d={$flags$:0,$hostElement$:o,$cmpMeta$:r,$instanceValues$:new Map};return d.$onInstancePromise$=new Promise(u=>d.$onInstanceResolve$=u),d.$onReadyPromise$=new Promise(u=>d.$onReadyResolve$=u),o["s-p"]=[],o["s-rc"]=[],Et(o,d,r.$listeners$),We.set(o,d)},qe=(o,r)=>r in o,Xe=(o,r)=>(0,console.error)(o,r),dt=new Map,Le=(o,r,d)=>{const u=o.$tagName$.replace(/-/g,"_"),C=o.$lazyBundleId$,I=dt.get(C);return I?I[u]:M(50863)(`./${C}.entry.js`).then(X=>(dt.set(C,X),X[u]),Xe)},Qe=new Map,Tt=[],Je=typeof window<"u"?window:{},xe=Je.document||{head:{}},Te={$flags$:0,$resourcesUrl$:"",jmp:o=>o(),raf:o=>requestAnimationFrame(o),ael:(o,r,d,u)=>o.addEventListener(r,d,u),rel:(o,r,d,u)=>o.removeEventListener(r,d,u),ce:(o,r)=>new CustomEvent(o,r)},Ht=o=>{Object.assign(Te,o)},ut=!0,Zt=(()=>{let o=!1;try{xe.addEventListener("e",null,Object.defineProperty({},"passive",{get(){o=!0}}))}catch{}return o})(),rt=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch{}return!1})(),ft=[],st=[],St=(o,r)=>d=>{o.push(d),F||(F=!0,r&&4&Te.$flags$?jt(At):Te.raf(At))},pt=o=>{for(let r=0;r<o.length;r++)try{o[r](performance.now())}catch(d){Xe(d)}o.length=0},At=()=>{pt(ft),pt(st),(F=ft.length>0)&&Te.raf(At)},jt=o=>Promise.resolve(void 0).then(o),b=St(ft,!1),ee=St(st,!0)},72972:(Me,ye,M)=>{M.d(ye,{d:()=>se,w:()=>e});const e=typeof window<"u"?window:void 0,se=typeof document<"u"?document:void 0},39721:(Me,ye,M)=>{M.d(ye,{b:()=>G,c:()=>h,d:()=>k,e:()=>A,g:()=>f,l:()=>ie,s:()=>z,t:()=>F,w:()=>ae});var e=M(15861),se=M(42477),Z=M(78635);const G="ionViewWillLeave",h="ionViewDidLeave",k="ionViewWillUnload",F=O=>new Promise((j,B)=>{(0,se.w)(()=>{ne(O),w(O).then(re=>{re.animation&&re.animation.destroy(),L(O),j(re)},re=>{L(O),B(re)})})}),ne=O=>{const j=O.enteringEl,B=O.leavingEl;Y(j,B,O.direction),O.showGoBack?j.classList.add("can-go-back"):j.classList.remove("can-go-back"),z(j,!1),j.style.setProperty("pointer-events","none"),B&&(z(B,!1),B.style.setProperty("pointer-events","none"))},w=function(){var O=(0,e.Z)(function*(j){const B=yield P(j);return B&&se.B.isBrowser?K(B,j):S(j)});return function(B){return O.apply(this,arguments)}}(),L=O=>{const j=O.enteringEl,B=O.leavingEl;j.classList.remove("ion-page-invisible"),j.style.removeProperty("pointer-events"),void 0!==B&&(B.classList.remove("ion-page-invisible"),B.style.removeProperty("pointer-events"))},P=function(){var O=(0,e.Z)(function*(j){return j.leavingEl&&j.animated&&0!==j.duration?j.animationBuilder?j.animationBuilder:"ios"===j.mode?(yield Promise.resolve().then(M.bind(M,59758))).iosTransitionAnimation:(yield Promise.resolve().then(M.bind(M,36160))).mdTransitionAnimation:void 0});return function(B){return O.apply(this,arguments)}}(),K=function(){var O=(0,e.Z)(function*(j,B){yield y(B,!0);const re=j(B.baseEl,B);V(B.enteringEl,B.leavingEl);const pe=yield q(re,B);return B.progressCallback&&B.progressCallback(void 0),pe&&ce(B.enteringEl,B.leavingEl),{hasCompleted:pe,animation:re}});return function(B,re){return O.apply(this,arguments)}}(),S=function(){var O=(0,e.Z)(function*(j){const B=j.enteringEl,re=j.leavingEl;return yield y(j,!1),V(B,re),ce(B,re),{hasCompleted:!0}});return function(B){return O.apply(this,arguments)}}(),y=function(){var O=(0,e.Z)(function*(j,B){(void 0!==j.deepWait?j.deepWait:B)&&(yield Promise.all([A(j.enteringEl),A(j.leavingEl)])),yield v(j.viewIsReady,j.enteringEl)});return function(B,re){return O.apply(this,arguments)}}(),v=function(){var O=(0,e.Z)(function*(j,B){j&&(yield j(B))});return function(B,re){return O.apply(this,arguments)}}(),q=(O,j)=>{const B=j.progressCallback,re=new Promise(pe=>{O.onFinish($e=>pe(1===$e))});return B?(O.progressStart(!0),B(O)):O.play(),re},V=(O,j)=>{ie(j,G),ie(O,"ionViewWillEnter")},ce=(O,j)=>{ie(O,"ionViewDidEnter"),ie(j,h)},ie=(O,j)=>{if(O){const B=new CustomEvent(j,{bubbles:!1,cancelable:!1});O.dispatchEvent(B)}},ae=()=>new Promise(O=>(0,Z.r)(()=>(0,Z.r)(()=>O()))),A=function(){var O=(0,e.Z)(function*(j){const B=j;if(B){if(null!=B.componentOnReady){if(null!=(yield B.componentOnReady()))return}else if(null!=B.__registerHost)return void(yield new Promise(pe=>(0,Z.r)(pe)));yield Promise.all(Array.from(B.children).map(A))}});return function(B){return O.apply(this,arguments)}}(),z=(O,j)=>{j?(O.setAttribute("aria-hidden","true"),O.classList.add("ion-page-hidden")):(O.hidden=!1,O.removeAttribute("aria-hidden"),O.classList.remove("ion-page-hidden"))},Y=(O,j,B)=>{void 0!==O&&(O.style.zIndex="back"===B?"99":"101"),void 0!==j&&(j.style.zIndex="100")},f=O=>O.classList.contains("ion-page")?O:O.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||O},37943:(Me,ye,M)=>{M.d(ye,{a:()=>F,b:()=>pe,c:()=>Z,g:()=>H,i:()=>$e});var e=M(42477);class se{constructor(){this.m=new Map}reset(l){this.m=new Map(Object.entries(l))}get(l,a){const c=this.m.get(l);return void 0!==c?c:a}getBoolean(l,a=!1){const c=this.m.get(l);return void 0===c?a:"string"==typeof c?"true"===c:!!c}getNumber(l,a){const c=parseFloat(this.m.get(l));return isNaN(c)?void 0!==a?a:NaN:c}set(l,a){this.m.set(l,a)}}const Z=new se,k="ionic:",Q="ionic-persist-config",H=R=>ne(R),F=(R,l)=>("string"==typeof R&&(l=R,R=void 0),H(R).includes(l)),ne=(R=window)=>{if(typeof R>"u")return[];R.Ionic=R.Ionic||{};let l=R.Ionic.platforms;return null==l&&(l=R.Ionic.platforms=w(R),l.forEach(a=>R.document.documentElement.classList.add(`plt-${a}`))),l},w=R=>{const l=Z.get("platform");return Object.keys(B).filter(a=>{const c=l?.[a];return"function"==typeof c?c(R):B[a](R)})},P=R=>!!(O(R,/iPad/i)||O(R,/Macintosh/i)&&ce(R)),y=R=>O(R,/android|sink/i),ce=R=>j(R,"(any-pointer:coarse)"),ae=R=>A(R)||z(R),A=R=>!!(R.cordova||R.phonegap||R.PhoneGap),z=R=>!!R.Capacitor?.isNative,O=(R,l)=>l.test(R.navigator.userAgent),j=(R,l)=>{var a;return null===(a=R.matchMedia)||void 0===a?void 0:a.call(R,l).matches},B={ipad:P,iphone:R=>O(R,/iPhone/i),ios:R=>O(R,/iPhone|iPod/i)||P(R),android:y,phablet:R=>{const l=R.innerWidth,a=R.innerHeight,c=Math.min(l,a),p=Math.max(l,a);return c>390&&c<520&&p>620&&p<800},tablet:R=>{const l=R.innerWidth,a=R.innerHeight,c=Math.min(l,a),p=Math.max(l,a);return P(R)||(R=>y(R)&&!O(R,/mobile/i))(R)||c>460&&c<820&&p>780&&p<1400},cordova:A,capacitor:z,electron:R=>O(R,/electron/i),pwa:R=>{var l;return!!(null!==(l=R.matchMedia)&&void 0!==l&&l.call(R,"(display-mode: standalone)").matches||R.navigator.standalone)},mobile:ce,mobileweb:R=>ce(R)&&!ae(R),desktop:R=>!ce(R),hybrid:ae};let re;const pe=R=>R&&(0,e.g)(R)||re,$e=(R={})=>{if(typeof window>"u")return;const l=window.document,a=window,c=a.Ionic=a.Ionic||{},p={};R._ael&&(p.ael=R._ael),R._rel&&(p.rel=R._rel),R._ce&&(p.ce=R._ce),(0,e.a)(p);const E=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(R=>{try{const l=R.sessionStorage.getItem(Q);return null!==l?JSON.parse(l):{}}catch{return{}}})(a)),{persistConfig:!1}),c.config),(R=>{const l={};return R.location.search.slice(1).split("&").map(a=>a.split("=")).map(([a,c])=>[decodeURIComponent(a),decodeURIComponent(c)]).filter(([a])=>((R,l)=>R.substr(0,l.length)===l)(a,k)).map(([a,c])=>[a.slice(k.length),c]).forEach(([a,c])=>{l[a]=c}),l})(a)),R);Z.reset(E),Z.getBoolean("persistConfig")&&((R,l)=>{try{R.sessionStorage.setItem(Q,JSON.stringify(l))}catch{return}})(a,E),ne(a),c.config=Z,c.mode=re=Z.get("mode",l.documentElement.getAttribute("mode")||(F(a,"ios")?"ios":"md")),Z.set("mode",re),l.documentElement.setAttribute("mode",re),l.documentElement.classList.add(re),Z.getBoolean("_testing")&&Z.set("animated",!1);const m=_=>{var oe;return null===(oe=_.tagName)||void 0===oe?void 0:oe.startsWith("ION-")},J=_=>["ios","md"].includes(_);(0,e.c)(_=>{for(;_;){const oe=_.mode||_.getAttribute("mode");if(oe){if(J(oe))return oe;m(_)&&console.warn('Invalid ionic mode: "'+oe+'", expected: "ios" or "md"')}_=_.parentElement}return re})}},59758:(Me,ye,M)=>{M.r(ye),M.d(ye,{iosTransitionAnimation:()=>w,shadow:()=>h});var e=M(44963),se=M(39721);M(72972),M(42477);const G=P=>document.querySelector(`${P}.ion-cloned-element`),h=P=>P.shadowRoot||P,k=P=>{const K="ION-TABS"===P.tagName?P:P.querySelector("ion-tabs"),S="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(null!=K){const y=K.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return null!=y?y.querySelector(S):null}return P.querySelector(S)},Q=(P,K)=>{const S="ION-TABS"===P.tagName?P:P.querySelector("ion-tabs");let y=[];if(null!=S){const v=S.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");null!=v&&(y=v.querySelectorAll("ion-buttons"))}else y=P.querySelectorAll("ion-buttons");for(const v of y){const q=v.closest("ion-header"),V=q&&!q.classList.contains("header-collapse-condense-inactive"),ce=v.querySelector("ion-back-button"),ie=v.classList.contains("buttons-collapse");if(null!==ce&&("start"===v.slot||""===v.slot)&&(ie&&V&&K||!ie))return ce}return null},F=(P,K,S,y,v,q,V,ce,ie)=>{var ae,A;const z=K?`calc(100% - ${v.right+4}px)`:v.left-4+"px",Y=K?"right":"left",f=K?"left":"right",O=K?"right":"left",j=(null===(ae=q.textContent)||void 0===ae?void 0:ae.trim())===(null===(A=ce.textContent)||void 0===A?void 0:A.trim()),re=(ie.height-L)/V.height,pe=j?`scale(${ie.width/V.width}, ${re})`:`scale(${re})`,$e="scale(1)",l=h(y).querySelector("ion-icon").getBoundingClientRect(),a=K?l.width/2-(l.right-v.right)+"px":v.left-l.width/2+"px",c=K?`-${window.innerWidth-v.right}px`:`${v.left}px`,p=`${ie.top}px`,E=`${v.top}px`,_=S?[{offset:0,transform:`translate3d(${c}, ${E}, 0)`},{offset:1,transform:`translate3d(${a}, ${p}, 0)`}]:[{offset:0,transform:`translate3d(${a}, ${p}, 0)`},{offset:1,transform:`translate3d(${c}, ${E}, 0)`}],he=S?[{offset:0,opacity:1,transform:$e},{offset:1,opacity:0,transform:pe}]:[{offset:0,opacity:0,transform:pe},{offset:1,opacity:1,transform:$e}],ve=S?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],ge=(0,e.c)(),x=(0,e.c)(),$=(0,e.c)(),D=G("ion-back-button"),le=h(D).querySelector(".button-text"),Ie=h(D).querySelector("ion-icon");D.text=y.text,D.mode=y.mode,D.icon=y.icon,D.color=y.color,D.disabled=y.disabled,D.style.setProperty("display","block"),D.style.setProperty("position","fixed"),x.addElement(Ie),ge.addElement(le),$.addElement(D),$.beforeStyles({position:"absolute",top:"0px",[O]:"0px"}).keyframes(_),ge.beforeStyles({"transform-origin":`${Y} top`}).beforeAddWrite(()=>{y.style.setProperty("display","none"),D.style.setProperty(Y,z)}).afterAddWrite(()=>{y.style.setProperty("display",""),D.style.setProperty("display","none"),D.style.removeProperty(Y)}).keyframes(he),x.beforeStyles({"transform-origin":`${f} center`}).keyframes(ve),P.addAnimation([ge,x,$])},ne=(P,K,S,y,v,q,V,ce)=>{var ie,ae;const A=K?"right":"left",z=K?`calc(100% - ${v.right}px)`:`${v.left}px`,f=`${v.top}px`,j=K?`-${window.innerWidth-ce.right-8}px`:ce.x-8+"px",re=ce.y-2+"px",pe=(null===(ie=V.textContent)||void 0===ie?void 0:ie.trim())===(null===(ae=y.textContent)||void 0===ae?void 0:ae.trim()),R=ce.height/(q.height-L),l="scale(1)",a=pe?`scale(${ce.width/q.width}, ${R})`:`scale(${R})`,E=S?[{offset:0,opacity:0,transform:`translate3d(${j}, ${re}, 0) ${a}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(0px, ${f}, 0) ${l}`}]:[{offset:0,opacity:.99,transform:`translate3d(0px, ${f}, 0) ${l}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${j}, ${re}, 0) ${a}`}],m=G("ion-title"),J=(0,e.c)();m.innerText=y.innerText,m.size=y.size,m.color=y.color,J.addElement(m),J.beforeStyles({"transform-origin":`${A} top`,height:`${v.height}px`,display:"",position:"relative",[A]:z}).beforeAddWrite(()=>{y.style.setProperty("opacity","0")}).afterAddWrite(()=>{y.style.setProperty("opacity",""),m.style.setProperty("display","none")}).keyframes(E),P.addAnimation(J)},w=(P,K)=>{var S;try{const y="cubic-bezier(0.32,0.72,0,1)",v="opacity",q="transform",V="0%",ie="rtl"===P.ownerDocument.dir,ae=ie?"-99.5%":"99.5%",A=ie?"33%":"-33%",z=K.enteringEl,Y=K.leavingEl,f="back"===K.direction,O=z.querySelector(":scope > ion-content"),j=z.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),B=z.querySelectorAll(":scope > ion-header > ion-toolbar"),re=(0,e.c)(),pe=(0,e.c)();if(re.addElement(z).duration((null!==(S=K.duration)&&void 0!==S?S:0)||540).easing(K.easing||y).fill("both").beforeRemoveClass("ion-page-invisible"),Y&&null!=P){const a=(0,e.c)();a.addElement(P),re.addAnimation(a)}if(O||0!==B.length||0!==j.length?(pe.addElement(O),pe.addElement(j)):pe.addElement(z.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),re.addAnimation(pe),f?pe.beforeClearStyles([v]).fromTo("transform",`translateX(${A})`,`translateX(${V})`).fromTo(v,.8,1):pe.beforeClearStyles([v]).fromTo("transform",`translateX(${ae})`,`translateX(${V})`),O){const a=h(O).querySelector(".transition-effect");if(a){const c=a.querySelector(".transition-cover"),p=a.querySelector(".transition-shadow"),E=(0,e.c)(),m=(0,e.c)(),J=(0,e.c)();E.addElement(a).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),m.addElement(c).beforeClearStyles([v]).fromTo(v,0,.1),J.addElement(p).beforeClearStyles([v]).fromTo(v,.03,.7),E.addAnimation([m,J]),pe.addAnimation([E])}}const $e=z.querySelector("ion-header.header-collapse-condense"),{forward:R,backward:l}=((P,K,S,y,v)=>{const q=Q(y,S),V=k(v),ce=k(y),ie=Q(v,S),ae=null!==q&&null!==V&&!S,A=null!==ce&&null!==ie&&S;if(ae){const z=V.getBoundingClientRect(),Y=q.getBoundingClientRect(),f=h(q).querySelector(".button-text"),O=f.getBoundingClientRect(),B=h(V).querySelector(".toolbar-title").getBoundingClientRect();ne(P,K,S,V,z,B,f,O),F(P,K,S,q,Y,f,O,V,B)}else if(A){const z=ce.getBoundingClientRect(),Y=ie.getBoundingClientRect(),f=h(ie).querySelector(".button-text"),O=f.getBoundingClientRect(),B=h(ce).querySelector(".toolbar-title").getBoundingClientRect();ne(P,K,S,ce,z,B,f,O),F(P,K,S,ie,Y,f,O,ce,B)}return{forward:ae,backward:A}})(re,ie,f,z,Y);if(B.forEach(a=>{const c=(0,e.c)();c.addElement(a),re.addAnimation(c);const p=(0,e.c)();p.addElement(a.querySelector("ion-title"));const E=(0,e.c)(),m=Array.from(a.querySelectorAll("ion-buttons,[menuToggle]")),J=a.closest("ion-header"),_=J?.classList.contains("header-collapse-condense-inactive");let oe;oe=m.filter(f?ve=>{const ge=ve.classList.contains("buttons-collapse");return ge&&!_||!ge}:ve=>!ve.classList.contains("buttons-collapse")),E.addElement(oe);const ue=(0,e.c)();ue.addElement(a.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const he=(0,e.c)();he.addElement(h(a).querySelector(".toolbar-background"));const De=(0,e.c)(),je=a.querySelector("ion-back-button");if(je&&De.addElement(je),c.addAnimation([p,E,ue,he,De]),E.fromTo(v,.01,1),ue.fromTo(v,.01,1),f)_||p.fromTo("transform",`translateX(${A})`,`translateX(${V})`).fromTo(v,.01,1),ue.fromTo("transform",`translateX(${A})`,`translateX(${V})`),De.fromTo(v,.01,1);else if($e||p.fromTo("transform",`translateX(${ae})`,`translateX(${V})`).fromTo(v,.01,1),ue.fromTo("transform",`translateX(${ae})`,`translateX(${V})`),he.beforeClearStyles([v,"transform"]),J?.translucent?he.fromTo("transform",ie?"translateX(-100%)":"translateX(100%)","translateX(0px)"):he.fromTo(v,.01,"var(--opacity)"),R||De.fromTo(v,.01,1),je&&!R){const ge=(0,e.c)();ge.addElement(h(je).querySelector(".button-text")).fromTo("transform",ie?"translateX(-100px)":"translateX(100px)","translateX(0px)"),c.addAnimation(ge)}}),Y){const a=(0,e.c)(),c=Y.querySelector(":scope > ion-content"),p=Y.querySelectorAll(":scope > ion-header > ion-toolbar"),E=Y.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if(c||0!==p.length||0!==E.length?(a.addElement(c),a.addElement(E)):a.addElement(Y.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),re.addAnimation(a),f){a.beforeClearStyles([v]).fromTo("transform",`translateX(${V})`,ie?"translateX(-100%)":"translateX(100%)");const m=(0,se.g)(Y);re.afterAddWrite(()=>{"normal"===re.getDirection()&&m.style.setProperty("display","none")})}else a.fromTo("transform",`translateX(${V})`,`translateX(${A})`).fromTo(v,1,.8);if(c){const m=h(c).querySelector(".transition-effect");if(m){const J=m.querySelector(".transition-cover"),_=m.querySelector(".transition-shadow"),oe=(0,e.c)(),ue=(0,e.c)(),he=(0,e.c)();oe.addElement(m).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),ue.addElement(J).beforeClearStyles([v]).fromTo(v,.1,0),he.addElement(_).beforeClearStyles([v]).fromTo(v,.7,.03),oe.addAnimation([ue,he]),a.addAnimation([oe])}}p.forEach(m=>{const J=(0,e.c)();J.addElement(m);const _=(0,e.c)();_.addElement(m.querySelector("ion-title"));const oe=(0,e.c)(),ue=m.querySelectorAll("ion-buttons,[menuToggle]"),he=m.closest("ion-header"),De=he?.classList.contains("header-collapse-condense-inactive"),je=Array.from(ue).filter(le=>{const Ie=le.classList.contains("buttons-collapse");return Ie&&!De||!Ie});oe.addElement(je);const ve=(0,e.c)(),ge=m.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");ge.length>0&&ve.addElement(ge);const x=(0,e.c)();x.addElement(h(m).querySelector(".toolbar-background"));const $=(0,e.c)(),D=m.querySelector("ion-back-button");if(D&&$.addElement(D),J.addAnimation([_,oe,ve,$,x]),re.addAnimation(J),$.fromTo(v,.99,0),oe.fromTo(v,.99,0),ve.fromTo(v,.99,0),f){if(De||_.fromTo("transform",`translateX(${V})`,ie?"translateX(-100%)":"translateX(100%)").fromTo(v,.99,0),ve.fromTo("transform",`translateX(${V})`,ie?"translateX(-100%)":"translateX(100%)"),x.beforeClearStyles([v,"transform"]),he?.translucent?x.fromTo("transform","translateX(0px)",ie?"translateX(-100%)":"translateX(100%)"):x.fromTo(v,"var(--opacity)",0),D&&!l){const Ie=(0,e.c)();Ie.addElement(h(D).querySelector(".button-text")).fromTo("transform",`translateX(${V})`,`translateX(${(ie?-124:124)+"px"})`),J.addAnimation(Ie)}}else De||_.fromTo("transform",`translateX(${V})`,`translateX(${A})`).fromTo(v,.99,0).afterClearStyles([q,v]),ve.fromTo("transform",`translateX(${V})`,`translateX(${A})`).afterClearStyles([q,v]),$.afterClearStyles([v]),_.afterClearStyles([v]),oe.afterClearStyles([v])})}return re}catch(y){throw y}},L=10},36160:(Me,ye,M)=>{M.r(ye),M.d(ye,{mdTransitionAnimation:()=>me});var e=M(44963),se=M(39721);M(72972),M(42477);const me=(G,h)=>{var k,Q,H;const F="40px",w="back"===h.direction,P=h.leavingEl,K=(0,se.g)(h.enteringEl),S=K.querySelector("ion-toolbar"),y=(0,e.c)();if(y.addElement(K).fill("both").beforeRemoveClass("ion-page-invisible"),w?y.duration((null!==(k=h.duration)&&void 0!==k?k:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):y.duration((null!==(Q=h.duration)&&void 0!==Q?Q:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform",`translateY(${F})`,"translateY(0px)").fromTo("opacity",.01,1),S){const v=(0,e.c)();v.addElement(S),y.addAnimation(v)}if(P&&w){y.duration((null!==(H=h.duration)&&void 0!==H?H:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const v=(0,e.c)();v.addElement((0,se.g)(P)).onFinish(q=>{1===q&&v.elements.length>0&&v.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)",`translateY(${F})`).fromTo("opacity",1,0),y.addAnimation(v)}return y}},57346:(Me,ye,M)=>{M.d(ye,{B:()=>oe,G:()=>ue,O:()=>he,a:()=>ne,b:()=>w,c:()=>S,d:()=>De,e:()=>je,f:()=>$e,g:()=>l,h:()=>p,i:()=>m,j:()=>v,k:()=>q,l:()=>L,m:()=>P,n:()=>ae,o:()=>re,p:()=>K,s:()=>_,t:()=>y});var e=M(15861),se=M(72972),Z=M(33006),T=M(37943),me=M(25030),G=M(78635),h=M(28909);let k=0,Q=0;const H=new WeakMap,F=x=>({create:$=>V(x,$),dismiss:($,D,le)=>O(document,$,D,x,le),getTop:()=>(0,e.Z)(function*(){return re(document,x)})()}),ne=F("ion-alert"),w=F("ion-action-sheet"),L=F("ion-loading"),P=F("ion-modal"),K=F("ion-picker"),S=F("ion-popover"),y=F("ion-toast"),v=x=>{typeof document<"u"&&f(document);const $=k++;x.overlayIndex=$},q=x=>(x.hasAttribute("id")||(x.id="ion-overlay-"+ ++Q),x.id),V=(x,$)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(x).then(()=>{const D=document.createElement(x);return D.classList.add("overlay-hidden"),Object.assign(D,Object.assign(Object.assign({},$),{hasController:!0})),a(document).appendChild(D),new Promise(le=>(0,G.c)(D,le))}):Promise.resolve(),ce='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',ae=(x,$)=>{const D=x.querySelector(ce);z(D,$)},A=(x,$)=>{const D=Array.from(x.querySelectorAll(ce));z(D.length>0?D[D.length-1]:null,$)},z=(x,$)=>{let D=x;const le=x?.shadowRoot;le&&(D=le.querySelector(ce)||x),D?(0,G.f)(D):$.focus()},f=x=>{0===k&&(k=1,x.addEventListener("focus",$=>{((x,$)=>{const D=re($,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover"),le=x.target;D&&le&&!D.classList.contains("ion-disable-focus-trap")&&(D.shadowRoot?(()=>{if(D.contains(le))D.lastFocus=le;else if("ION-TOAST"===le.tagName)z(D.lastFocus,D);else{const Ee=D.lastFocus;ae(D,D),Ee===$.activeElement&&A(D,D),D.lastFocus=$.activeElement}})():(()=>{if(D===le)D.lastFocus=void 0;else if("ION-TOAST"===le.tagName)z(D.lastFocus,D);else{const Ee=(0,G.g)(D);if(!Ee.contains(le))return;const Se=Ee.querySelector(".ion-overlay-wrapper");if(!Se)return;if(Se.contains(le)||le===Ee.querySelector("ion-backdrop"))D.lastFocus=le;else{const Ce=D.lastFocus;ae(Se,D),Ce===$.activeElement&&A(Se,D),D.lastFocus=$.activeElement}}})())})($,x)},!0),x.addEventListener("ionBackButton",$=>{const D=re(x);D?.backdropDismiss&&$.detail.register(Z.OVERLAY_BACK_BUTTON_PRIORITY,()=>{D.dismiss(void 0,oe)})}),(0,Z.shouldUseCloseWatcher)()||x.addEventListener("keydown",$=>{if("Escape"===$.key){const D=re(x);D?.backdropDismiss&&D.dismiss(void 0,oe)}}))},O=(x,$,D,le,Ie)=>{const Ae=re(x,le,Ie);return Ae?Ae.dismiss($,D):Promise.reject("overlay does not exist")},B=(x,$)=>((x,$)=>(void 0===$&&($="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover,ion-toast"),Array.from(x.querySelectorAll($)).filter(D=>D.overlayIndex>0)))(x,$).filter(D=>!(x=>x.classList.contains("overlay-hidden"))(D)),re=(x,$,D)=>{const le=B(x,$);return void 0===D?le[le.length-1]:le.find(Ie=>Ie.id===D)},pe=(x=!1)=>{const D=a(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");D&&(x?D.setAttribute("aria-hidden","true"):D.removeAttribute("aria-hidden"))},$e=function(){var x=(0,e.Z)(function*($,D,le,Ie,Ae){var Ee,Se;if($.presented)return;pe(!0),ve($.el),$.presented=!0,$.willPresent.emit(),null===(Ee=$.willPresentShorthand)||void 0===Ee||Ee.emit();const Ce=(0,T.b)($),Oe=$.enterAnimation?$.enterAnimation:T.c.get(D,"ios"===Ce?le:Ie);(yield c($,Oe,$.el,Ae))&&($.didPresent.emit(),null===(Se=$.didPresentShorthand)||void 0===Se||Se.emit()),"ION-TOAST"!==$.el.tagName&&R($.el),$.keyboardClose&&(null===document.activeElement||!$.el.contains(document.activeElement))&&$.el.focus(),$.el.removeAttribute("aria-hidden")});return function(D,le,Ie,Ae,Ee){return x.apply(this,arguments)}}(),R=function(){var x=(0,e.Z)(function*($){let D=document.activeElement;if(!D)return;const le=D?.shadowRoot;le&&(D=le.querySelector(ce)||D),yield $.onDidDismiss(),(null===document.activeElement||document.activeElement===document.body)&&D.focus()});return function(D){return x.apply(this,arguments)}}(),l=function(){var x=(0,e.Z)(function*($,D,le,Ie,Ae,Ee,Se){var Ce,Oe;if(!$.presented)return!1;void 0!==se.d&&1===B(se.d).length&&pe(!1),$.presented=!1;try{$.el.style.setProperty("pointer-events","none"),$.willDismiss.emit({data:D,role:le}),null===(Ce=$.willDismissShorthand)||void 0===Ce||Ce.emit({data:D,role:le});const Be=(0,T.b)($),He=$.leaveAnimation?$.leaveAnimation:T.c.get(Ie,"ios"===Be?Ae:Ee);le!==ue&&(yield c($,He,$.el,Se)),$.didDismiss.emit({data:D,role:le}),null===(Oe=$.didDismissShorthand)||void 0===Oe||Oe.emit({data:D,role:le}),(H.get($)||[]).forEach(nt=>nt.destroy()),H.delete($),$.el.classList.add("overlay-hidden"),$.el.style.removeProperty("pointer-events"),void 0!==$.el.lastFocus&&($.el.lastFocus=void 0)}catch(Be){console.error(Be)}return $.el.remove(),ge(),!0});return function(D,le,Ie,Ae,Ee,Se,Ce){return x.apply(this,arguments)}}(),a=x=>x.querySelector("ion-app")||x.body,c=function(){var x=(0,e.Z)(function*($,D,le,Ie){le.classList.remove("overlay-hidden");const Ee=D($.el,Ie);(!$.animated||!T.c.getBoolean("animated",!0))&&Ee.duration(0),$.keyboardClose&&Ee.beforeAddWrite(()=>{const Ce=le.ownerDocument.activeElement;Ce?.matches("input,ion-input, ion-textarea")&&Ce.blur()});const Se=H.get($)||[];return H.set($,[...Se,Ee]),yield Ee.play(),!0});return function(D,le,Ie,Ae){return x.apply(this,arguments)}}(),p=(x,$)=>{let D;const le=new Promise(Ie=>D=Ie);return E(x,$,Ie=>{D(Ie.detail)}),le},E=(x,$,D)=>{const le=Ie=>{(0,G.b)(x,$,le),D(Ie)};(0,G.a)(x,$,le)},m=x=>"cancel"===x||x===oe,J=x=>x(),_=(x,$)=>{if("function"==typeof x)return T.c.get("_zoneGate",J)(()=>{try{return x($)}catch(le){throw le}})},oe="backdrop",ue="gesture",he=39,De=x=>{let D,$=!1;const le=(0,me.C)(),Ie=(Se=!1)=>{if(D&&!Se)return{delegate:D,inline:$};const{el:Ce,hasController:Oe,delegate:Be}=x;return $=null!==Ce.parentNode&&!Oe,D=$?Be||le:Be,{inline:$,delegate:D}};return{attachViewToDom:function(){var Se=(0,e.Z)(function*(Ce){const{delegate:Oe}=Ie(!0);if(Oe)return yield Oe.attachViewToDom(x.el,Ce);const{hasController:Be}=x;if(Be&&void 0!==Ce)throw new Error("framework delegate is missing");return null});return function(Oe){return Se.apply(this,arguments)}}(),removeViewFromDom:()=>{const{delegate:Se}=Ie();Se&&void 0!==x.el&&Se.removeViewFromDom(x.el.parentElement,x.el)}}},je=()=>{let x;const $=()=>{x&&(x(),x=void 0)};return{addClickListener:(le,Ie)=>{$();const Ae=void 0!==Ie?document.getElementById(Ie):null;Ae?x=((Se,Ce)=>{const Oe=()=>{Ce.present()};return Se.addEventListener("click",Oe),()=>{Se.removeEventListener("click",Oe)}})(Ae,le):(0,h.p)(`A trigger element with the ID "${Ie}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,le)},removeClickListener:$}},ve=x=>{var $;if(void 0===se.d)return;const D=B(se.d);for(let le=D.length-1;le>=0;le--){const Ie=D[le],Ae=null!==($=D[le+1])&&void 0!==$?$:x;(Ae.hasAttribute("aria-hidden")||"ION-TOAST"!==Ae.tagName)&&Ie.setAttribute("aria-hidden","true")}},ge=()=>{if(void 0===se.d)return;const x=B(se.d);for(let $=x.length-1;$>=0;$--){const D=x[$];if(D.removeAttribute("aria-hidden"),"ION-TOAST"!==D.tagName)break}}},23814:(Me,ye,M)=>{M.d(ye,{c:()=>Z,g:()=>me,h:()=>se,o:()=>h});var e=M(15861);const se=(k,Q)=>null!==Q.closest(k),Z=(k,Q)=>"string"==typeof k&&k.length>0?Object.assign({"ion-color":!0,[`ion-color-${k}`]:!0},Q):Q,me=k=>{const Q={};return(k=>void 0!==k?(Array.isArray(k)?k:k.split(" ")).filter(H=>null!=H).map(H=>H.trim()).filter(H=>""!==H):[])(k).forEach(H=>Q[H]=!0),Q},G=/^[a-z][a-z0-9+\-.]*:/,h=function(){var k=(0,e.Z)(function*(Q,H,F,ne){if(null!=Q&&"#"!==Q[0]&&!G.test(Q)){const w=document.querySelector("ion-router");if(w)return H?.preventDefault(),w.push(Q,F,ne)}return!1});return function(H,F,ne,w){return k.apply(this,arguments)}}()},50863:(Me,ye,M)=>{var e={"./ion-accordion_2.entry.js":[89654,9654],"./ion-action-sheet.entry.js":[3648,3648],"./ion-alert.entry.js":[11118,1118],"./ion-app_8.entry.js":[80053,53],"./ion-avatar_3.entry.js":[54753,3793],"./ion-back-button.entry.js":[92073,2073],"./ion-backdrop.entry.js":[98939,8939],"./ion-breadcrumb_2.entry.js":[47544,7544],"./ion-button_2.entry.js":[15652,5652],"./ion-card_5.entry.js":[50388,388],"./ion-checkbox.entry.js":[9922,9922],"./ion-chip.entry.js":[10657,657],"./ion-col_3.entry.js":[19824,9824],"./ion-datetime-button.entry.js":[49230,9230],"./ion-datetime_3.entry.js":[54959,4959],"./ion-fab_3.entry.js":[65836,5836],"./ion-img.entry.js":[71033,1033],"./ion-infinite-scroll_2.entry.js":[8034,8034],"./ion-input.entry.js":[51217,1217],"./ion-item-option_3.entry.js":[52933,2933],"./ion-item_8.entry.js":[94711,4711],"./ion-loading.entry.js":[79434,9434],"./ion-menu_3.entry.js":[38136,8136],"./ion-modal.entry.js":[42349,2349],"./ion-nav_2.entry.js":[45349,5349],"./ion-picker-column-internal.entry.js":[7602,7602],"./ion-picker-internal.entry.js":[9016,9016],"./ion-popover.entry.js":[83804,3804],"./ion-progress-bar.entry.js":[54174,4174],"./ion-radio_2.entry.js":[24432,3182],"./ion-range.entry.js":[31709,1709],"./ion-refresher_2.entry.js":[93326,3326],"./ion-reorder_2.entry.js":[93583,3583],"./ion-ripple-effect.entry.js":[99958,9958],"./ion-route_4.entry.js":[4330,4330],"./ion-searchbar.entry.js":[98628,8628],"./ion-segment_2.entry.js":[59325,9325],"./ion-select_3.entry.js":[12773,2773],"./ion-spinner.entry.js":[44908,4908],"./ion-split-pane.entry.js":[39536,9536],"./ion-tab-bar_2.entry.js":[438,438],"./ion-tab_2.entry.js":[91536,1536],"./ion-text.entry.js":[74376,4376],"./ion-textarea.entry.js":[56560,6560],"./ion-toast.entry.js":[76120,6120],"./ion-toggle.entry.js":[85168,5168]};function se(Z){if(!M.o(e,Z))return Promise.resolve().then(()=>{var G=new Error("Cannot find module '"+Z+"'");throw G.code="MODULE_NOT_FOUND",G});var T=e[Z],me=T[0];return M.e(T[1]).then(()=>M(me))}se.keys=()=>Object.keys(e),se.id=50863,Me.exports=se},20012:(Me,ye,M)=>{M.r(ye),M.d(ye,{ActionSheetController:()=>I,AlertController:()=>u,AngularDelegate:()=>Z.AngularDelegate,AnimationController:()=>C,BooleanValueAccessor:()=>$e,Config:()=>Z.Config,DomController:()=>Z.DomController,GestureController:()=>X,ION_MAX_VALIDATOR:()=>ee,ION_MIN_VALIDATOR:()=>r,IonAccordion:()=>oe,IonAccordionGroup:()=>ue,IonActionSheet:()=>he,IonAlert:()=>De,IonApp:()=>je,IonAvatar:()=>ve,IonBackButton:()=>st,IonBackButtonDelegate:()=>st,IonBackdrop:()=>ge,IonBadge:()=>x,IonBreadcrumb:()=>$,IonBreadcrumbs:()=>D,IonButton:()=>le,IonButtons:()=>Ie,IonCard:()=>Ae,IonCardContent:()=>Ee,IonCardHeader:()=>Se,IonCardSubtitle:()=>Ce,IonCardTitle:()=>Oe,IonCheckbox:()=>Be,IonChip:()=>He,IonCol:()=>Ue,IonContent:()=>nt,IonDatetime:()=>mt,IonDatetimeButton:()=>gt,IonFab:()=>ot,IonFabButton:()=>et,IonFabList:()=>ht,IonFooter:()=>Ze,IonGrid:()=>ct,IonHeader:()=>Ne,IonIcon:()=>vt,IonImg:()=>yt,IonInfiniteScroll:()=>It,IonInfiniteScrollContent:()=>xt,IonInput:()=>bt,IonItem:()=>it,IonItemDivider:()=>wt,IonItemGroup:()=>_t,IonItemOption:()=>Rt,IonItemOptions:()=>Mt,IonItemSliding:()=>Ot,IonLabel:()=>tt,IonList:()=>Ke,IonListHeader:()=>Ve,IonLoading:()=>Re,IonMaxValidator:()=>o,IonMenu:()=>ze,IonMenuButton:()=>Ye,IonMenuToggle:()=>Pt,IonMinValidator:()=>d,IonModal:()=>jt,IonNav:()=>St,IonNavLink:()=>Ct,IonNote:()=>kt,IonPicker:()=>lt,IonPopover:()=>b,IonProgressBar:()=>Bt,IonRadio:()=>Lt,IonRadioGroup:()=>Dt,IonRange:()=>Ft,IonRefresher:()=>Nt,IonRefresherContent:()=>Et,IonReorder:()=>zt,IonReorderGroup:()=>Wt,IonRippleEffect:()=>Xt,IonRouterOutlet:()=>rt,IonRow:()=>Kt,IonSearchbar:()=>We,IonSegment:()=>Fe,IonSegmentButton:()=>Gt,IonSelect:()=>$t,IonSelectOption:()=>qe,IonSkeletonText:()=>Xe,IonSpinner:()=>dt,IonSplitPane:()=>Le,IonTabBar:()=>Qe,IonTabButton:()=>Tt,IonTabs:()=>ft,IonText:()=>Je,IonTextarea:()=>xe,IonThumbnail:()=>Te,IonTitle:()=>Ht,IonToast:()=>ut,IonToggle:()=>Zt,IonToolbar:()=>Ut,IonicModule:()=>Pe,IonicRouteStrategy:()=>Z.IonicRouteStrategy,IonicSafeString:()=>P.I,IonicSlides:()=>ie,LoadingController:()=>N,MenuController:()=>U,ModalController:()=>W,NavController:()=>Z.NavController,NavParams:()=>Z.NavParams,NumericValueAccessor:()=>R,PickerController:()=>te,Platform:()=>Z.Platform,PopoverController:()=>de,RadioValueAccessor:()=>l,RouterLinkDelegate:()=>pt,RouterLinkWithHrefDelegate:()=>At,SelectValueAccessor:()=>a,TextValueAccessor:()=>c,ToastController:()=>fe,createAnimation:()=>k.c,createGesture:()=>w.createGesture,getIonPageElement:()=>Q.g,getPlatforms:()=>L.g,getTimeGivenProgression:()=>ne.g,iosTransitionAnimation:()=>H.iosTransitionAnimation,isPlatform:()=>L.a,mdTransitionAnimation:()=>F.mdTransitionAnimation,openURL:()=>K.o});var e=M(99877),se=M(88191),Z=M(86789),T=M(97582),me=M(42168),G=M(17007),h=M(78007),k=M(44963),Q=M(39721),H=M(59758),F=M(36160),ne=M(65069),w=M(35067),L=M(37943),P=M(87036),K=M(23814),S=M(16523),y=M(57346),q=(M(72972),M(42477));M(22889),M(33006);const ie=t=>{const{swiper:i,extendParams:n}=t,s={effect:void 0,direction:"horizontal",initialSlide:0,loop:!1,parallax:!1,slidesPerView:1,spaceBetween:0,speed:300,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,centeredSlides:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,touchEventsTarget:"container",freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,zoom:{maxRatio:3,minRatio:1,toggle:!1},touchRatio:1,touchAngle:45,simulateTouch:!0,touchStartPreventDefault:!1,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,threshold:0,touchMoveStopPropagation:!0,touchReleaseOnEdges:!1,iOSEdgeSwipeDetection:!1,iOSEdgeSwipeThreshold:20,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loopAdditionalSlides:0,noSwiping:!0,runCallbacksOnInit:!0,coverflowEffect:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0},flipEffect:{slideShadows:!0,limitRotation:!0},cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94},fadeEffect:{crossFade:!1},a11y:{prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide"}};i.pagination&&(s.pagination={type:"bullets",clickable:!1,hideOnClick:!1}),i.scrollbar&&(s.scrollbar={hide:!0}),n(s)};var A=M(15861);const z=L.i,Y=function(){var t=(0,A.Z)(function*(i,n){if(!(typeof window>"u"))return yield z(),(0,q.b)(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-input",[[38,"ion-input",{"color":[513],"accept":[1],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[4],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[4],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"size":[2],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"placeholder":["placeholderChanged"],"value":["valueChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"legacy":[4],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[1,"ion-skeleton-text",{"animated":[4]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[49,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[4],"download":[1],"fill":[1],"shape":[1],"href":[1],"rel":[1],"lines":[1],"counter":[4],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"counterFormatter":[16],"multipleInputs":[32],"focusable":[32],"counterString":[32]},[[0,"ionInput","handleIonInput"],[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"],"counterFormatter":["counterFormatterChanged"]}],[34,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-note",{"color":[513]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"isExpanded":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-picker-internal",[[33,"ion-picker-internal",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"],"checked":["styleChanged"],"color":["styleChanged"],"disabled":["styleChanged"]}],[0,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[34,"ion-buttons",{"collapse":[4]}]]],["ion-picker-column-internal",[[33,"ion-picker-column-internal",{"disabled":[4],"items":[16],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64]},null,{"value":["valueChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"legacy":[4]},null,{"checked":["styleChanged"],"disabled":["styleChanged"]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]]]'),n)});return function(n,s){return t.apply(this,arguments)}}(),f=["*"],O=["outlet"],j=[[["","slot","top"]],"*"],B=["[slot=top]","*"];function re(t,i){if(1&t&&(e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275elementContainer(1,2),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngTemplateOutlet",n.template)}}function pe(t,i){if(1&t&&e.\u0275\u0275elementContainer(0,1),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngTemplateOutlet",n.template)}}let $e=(()=>{class t extends Z.ValueAccessor{constructor(n,s){super(n,s)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,(0,Z.setIonicClasses)(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionChange",function(be){return s._handleIonChange(be.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:se.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),R=(()=>{class t extends Z.ValueAccessor{constructor(n,s){super(n,s)}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){super.registerOnChange(s=>{n(""===s?null:parseFloat(s))})}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionInput",function(be){return s.handleInputEvent(be.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:se.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),l=(()=>{class t extends Z.ValueAccessor{constructor(n,s){super(n,s)}_handleIonSelect(n){this.handleValueChange(n,n.checked)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-radio"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionSelect",function(be){return s._handleIonSelect(be.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:se.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),a=(()=>{class t extends Z.ValueAccessor{constructor(n,s){super(n,s)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionChange",function(be){return s._handleChangeEvent(be.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:se.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),c=(()=>{class t extends Z.ValueAccessor{constructor(n,s){super(n,s)}_handleInputEvent(n){this.handleValueChange(n,n.value)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"],["ion-range"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionInput",function(be){return s._handleInputEvent(be.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:se.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})();const p=(t,i)=>{const n=t.prototype;i.forEach(s=>{Object.defineProperty(n,s,{get(){return this.el[s]},set(g){this.z.runOutsideAngular(()=>this.el[s]=g)},configurable:!0})})},E=(t,i)=>{const n=t.prototype;i.forEach(s=>{n[s]=function(){const g=arguments;return this.z.runOutsideAngular(()=>this.el[s].apply(this.el,g))}})},m=(t,i,n)=>{n.forEach(s=>t[s]=(0,me.fromEvent)(i,s))};function _(t){return function(n){const{defineCustomElementFn:s,inputs:g,methods:be}=t;return void 0!==s&&s(),g&&p(n,g),be&&E(n,be),n}}let oe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],t),t})(),ue=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],t),t})(),he=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),De=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),je=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-app"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),ve=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-avatar"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),ge=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionBackdropTap"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["stopPropagation","tappable","visible"]})],t),t})(),x=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),$=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],t),t})(),D=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionCollapsedClick"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],t),t})(),le=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],t),t})(),Ie=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["collapse"]})],t),t})(),Ae=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),Ee=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["mode"]})],t),t})(),Se=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode","translucent"]})],t),t})(),Ce=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),Oe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),Be=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["alignment","checked","color","disabled","indeterminate","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),He=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","disabled","mode","outline"]})],t),t})(),Ue=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],t),t})(),nt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-content"]],inputs:{color:"color",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],t),t})(),mt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],t),t})(),gt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","datetime","disabled","mode"]})],t),t})(),ot=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],t),t})(),et=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],t),t})(),ht=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["activated","side"]})],t),t})(),Ze=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["collapse","mode","translucent"]})],t),t})(),ct=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["fixed"]})],t),t})(),Ne=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["collapse","mode","translucent"]})],t),t})(),vt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],t),t})(),yt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["alt","src"]})],t),t})(),It=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionInfinite"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled","position","threshold"],methods:["complete"]})],t),t})(),xt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["loadingSpinner","loadingText"]})],t),t})(),bt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-input"]],inputs:{accept:"accept",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",size:"size",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["accept","autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","legacy","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","size","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),it=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item"]],inputs:{button:"button",color:"color",counter:"counter",counterFormatter:"counterFormatter",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",fill:"fill",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",target:"target",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["button","color","counter","counterFormatter","detail","detailIcon","disabled","download","fill","href","lines","mode","rel","routerAnimation","routerDirection","shape","target","type"]})],t),t})(),wt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode","sticky"]})],t),t})(),_t=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-group"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),Rt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],t),t})(),Mt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionSwipe"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-options"]],inputs:{side:"side"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["side"]})],t),t})(),Ot=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionDrag"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],t),t})(),tt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode","position"]})],t),t})(),Ke=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],t),t})(),Ve=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","lines","mode"]})],t),t})(),Re=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),ze=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],t),t})(),Ye=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["autoHide","color","disabled","menu","mode","type"]})],t),t})(),Pt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["autoHide","menu"]})],t),t})(),Ct=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["component","componentProps","routerAnimation","routerDirection"]})],t),t})(),kt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),lt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-picker"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],t),t})(),Bt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["buffer","color","mode","reversed","type","value"]})],t),t})(),Lt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["alignment","color","disabled","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),Dt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",name:"name",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["allowEmptySelection","compareWith","name","value"]})],t),t})(),Ft=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","legacy","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],t),t})(),Nt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionRefresh","ionPull","ionStart"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],t),t})(),Et=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],t),t})(),zt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-reorder"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),Wt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionItemReorder"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled"],methods:["complete"]})],t),t})(),Xt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["type"],methods:["addRipple"]})],t),t})(),Kt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-row"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),We=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),Fe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],t),t})(),Gt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-segment-button"]],inputs:{disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled","layout","mode","type","value"]})],t),t})(),$t=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",expandedIcon:"expandedIcon",fill:"fill",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["cancelText","color","compareWith","disabled","expandedIcon","fill","interface","interfaceOptions","justify","label","labelPlacement","legacy","mode","multiple","name","okText","placeholder","selectedText","shape","toggleIcon","value"],methods:["open"]})],t),t})(),qe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled","value"]})],t),t})(),Xe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated"]})],t),t})(),dt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","duration","name","paused"]})],t),t})(),Le=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionSplitPaneVisible"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["contentId","disabled","when"]})],t),t})(),Qe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode","selectedTab","translucent"]})],t),t})(),Tt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],t),t})(),Je=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),xe=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","legacy","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],t),t})(),Te=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-thumbnail"]],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({})],t),t})(),Ht=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","size"]})],t),t})(),ut=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),Zt=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement,m(this,this.el,["ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["alignment","checked","color","disabled","enableOnOffLabels","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),Ut=(()=>{let t=class{constructor(n,s,g){this.z=g,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,T.gn)([_({inputs:["color","mode"]})],t),t})(),rt=(()=>{class t extends Z.IonRouterOutlet{constructor(n,s,g,be,Ge,at,Vt,Yt){super(n,s,g,be,Ge,at,Vt,Yt),this.parentOutlet=Yt}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275injectAttribute("name"),e.\u0275\u0275injectAttribute("tabs"),e.\u0275\u0275directiveInject(G.Location),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(h.Router),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(h.ActivatedRoute),e.\u0275\u0275directiveInject(t,12))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-router-outlet"]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),ft=(()=>{class t extends Z.IonTabs{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tabs"]],contentQueries:function(n,s,g){if(1&n&&(e.\u0275\u0275contentQuery(g,Qe,5),e.\u0275\u0275contentQuery(g,Qe,4)),2&n){let be;e.\u0275\u0275queryRefresh(be=e.\u0275\u0275loadQuery())&&(s.tabBar=be.first),e.\u0275\u0275queryRefresh(be=e.\u0275\u0275loadQuery())&&(s.tabBars=be)}},viewQuery:function(n,s){if(1&n&&e.\u0275\u0275viewQuery(O,5,rt),2&n){let g;e.\u0275\u0275queryRefresh(g=e.\u0275\u0275loadQuery())&&(s.outlet=g.first)}},features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:B,decls:6,vars:0,consts:[[1,"tabs-inner"],["tabsInner",""],["tabs","true",3,"stackWillChange","stackDidChange"],["outlet",""]],template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(j),e.\u0275\u0275projection(0),e.\u0275\u0275elementStart(1,"div",0,1)(3,"ion-router-outlet",2,3),e.\u0275\u0275listener("stackWillChange",function(be){return s.onStackWillChange(be)})("stackDidChange",function(be){return s.onStackDidChange(be)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275projection(5,1))},dependencies:[rt],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]}),t})(),st=(()=>{class t extends Z.IonBackButton{constructor(n,s,g,be,Ge,at){super(n,s,g,be,Ge,at)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(rt,8),e.\u0275\u0275directiveInject(Z.NavController),e.\u0275\u0275directiveInject(Z.Config),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(e.ChangeDetectorRef))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-back-button"]],features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t})(),St=(()=>{class t extends Z.IonNav{constructor(n,s,g,be,Ge,at){super(n,s,g,be,Ge,at)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.EnvironmentInjector),e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(Z.AngularDelegate),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(e.ChangeDetectorRef))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-nav"]],features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:f,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t})(),pt=(()=>{class t extends Z.RouterLinkDelegateDirective{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["","routerLink","",5,"a",5,"area"]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),At=(()=>{class t extends Z.RouterLinkWithHrefDelegateDirective{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["a","routerLink",""],["area","routerLink",""]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),jt=(()=>{class t extends Z.IonModal{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-modal"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(n,s){1&n&&e.\u0275\u0275template(0,re,2,1,"div",0),2&n&&e.\u0275\u0275property("ngIf",s.isCmpOpen||s.keepContentsMounted)},dependencies:[G.NgIf,G.NgTemplateOutlet],encapsulation:2,changeDetection:0}),t})(),b=(()=>{class t extends Z.IonPopover{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-popover"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(n,s){1&n&&e.\u0275\u0275template(0,pe,1,1,"ng-container",0),2&n&&e.\u0275\u0275property("ngIf",s.isCmpOpen||s.keepContentsMounted)},dependencies:[G.NgIf,G.NgTemplateOutlet],encapsulation:2,changeDetection:0}),t})();const ee={provide:se.NG_VALIDATORS,useExisting:(0,e.forwardRef)(()=>o),multi:!0};let o=(()=>{class t extends se.MaxValidator{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(n,s){2&n&&e.\u0275\u0275attribute("max",s._enabled?s.max:null)},features:[e.\u0275\u0275ProvidersFeature([ee]),e.\u0275\u0275InheritDefinitionFeature]}),t})();const r={provide:se.NG_VALIDATORS,useExisting:(0,e.forwardRef)(()=>d),multi:!0};let d=(()=>{class t extends se.MinValidator{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(n,s){2&n&&e.\u0275\u0275attribute("min",s._enabled?s.min:null)},features:[e.\u0275\u0275ProvidersFeature([r]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),u=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.a)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),C=(()=>{class t{create(n){return(0,k.c)(n)}easingTime(n,s,g,be,Ge){return(0,ne.g)(n,s,g,be,Ge)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),I=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.b)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),X=(()=>{class t{constructor(n){this.zone=n}create(n,s=!1){return s&&Object.getOwnPropertyNames(n).forEach(g=>{if("function"==typeof n[g]){const be=n[g];n[g]=(...Ge)=>this.zone.run(()=>be(...Ge))}}),(0,w.createGesture)(n)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275inject(e.NgZone))},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),N=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.l)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),U=(()=>{class t extends Z.MenuController{constructor(){super(S.m)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),W=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.m),this.angularDelegate=(0,e.inject)(Z.AngularDelegate),this.injector=(0,e.inject)(e.Injector),this.environmentInjector=(0,e.inject)(e.EnvironmentInjector)}create(n){return super.create({...n,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")})}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac}),t})(),te=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.p)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();class de extends Z.OverlayBaseController{constructor(){super(y.c),this.angularDelegate=(0,e.inject)(Z.AngularDelegate),this.injector=(0,e.inject)(e.Injector),this.environmentInjector=(0,e.inject)(e.EnvironmentInjector)}create(i){return super.create({...i,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")})}}let fe=(()=>{class t extends Z.OverlayBaseController{constructor(){super(y.t)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const _e=(t,i,n)=>()=>{const s=i.defaultView;if(s&&typeof window<"u"){(0,P.s)({...t,_zoneGate:be=>n.run(be)});const g="__zone_symbol__addEventListener"in i.body?"__zone_symbol__addEventListener":"addEventListener";return function ae(){var t=[];if(typeof window<"u"){var i=window;(!i.customElements||i.Element&&(!i.Element.prototype.closest||!i.Element.prototype.matches||!i.Element.prototype.remove||!i.Element.prototype.getRootNode))&&t.push(M.e(6748).then(M.t.bind(M,30723,23))),("function"!=typeof Object.assign||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||i.NodeList&&!i.NodeList.prototype.forEach||!i.fetch||!function(){try{var s=new URL("b","http://a");return s.pathname="c%20d","http://a/c%20d"===s.href&&s.searchParams}catch{return!1}}()||typeof WeakMap>"u")&&t.push(M.e(2214).then(M.t.bind(M,24144,23)))}return Promise.all(t)}().then(()=>Y(s,{exclude:["ion-tabs","ion-tab"],syncQueue:!0,raf:Z.raf,jmp:be=>n.runOutsideAngular(be),ael(be,Ge,at,Vt){be[g](Ge,at,Vt)},rel(be,Ge,at,Vt){be.removeEventListener(Ge,at,Vt)}}))}};let Pe=(()=>{class t{static forRoot(n){return{ngModule:t,providers:[{provide:Z.ConfigToken,useValue:n},{provide:e.APP_INITIALIZER,useFactory:_e,multi:!0,deps:[Z.ConfigToken,G.DOCUMENT,e.NgZone]},(0,Z.provideComponentInputBinding)()]}}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({providers:[Z.AngularDelegate,W,de],imports:[G.CommonModule]}),t})()},15861:(Me,ye,M)=>{function e(Z,T,me,G,h,k,Q){try{var H=Z[k](Q),F=H.value}catch(ne){return void me(ne)}H.done?T(F):Promise.resolve(F).then(G,h)}function se(Z){return function(){var T=this,me=arguments;return new Promise(function(G,h){var k=Z.apply(T,me);function Q(F){e(k,G,h,Q,H,"next",F)}function H(F){e(k,G,h,Q,H,"throw",F)}Q(void 0)})}}M.d(ye,{Z:()=>se})},97582:(Me,ye,M)=>{M.d(ye,{FC:()=>ce,Jh:()=>w,KL:()=>ae,ZT:()=>se,_T:()=>T,cy:()=>A,ev:()=>q,gn:()=>me,mG:()=>ne,pi:()=>Z,pr:()=>v,qq:()=>V});var e=function(l,a){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,p){c.__proto__=p}||function(c,p){for(var E in p)Object.prototype.hasOwnProperty.call(p,E)&&(c[E]=p[E])})(l,a)};function se(l,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function c(){this.constructor=l}e(l,a),l.prototype=null===a?Object.create(a):(c.prototype=a.prototype,new c)}var Z=function(){return Z=Object.assign||function(a){for(var c,p=1,E=arguments.length;p<E;p++)for(var m in c=arguments[p])Object.prototype.hasOwnProperty.call(c,m)&&(a[m]=c[m]);return a},Z.apply(this,arguments)};function T(l,a){var c={};for(var p in l)Object.prototype.hasOwnProperty.call(l,p)&&a.indexOf(p)<0&&(c[p]=l[p]);if(null!=l&&"function"==typeof Object.getOwnPropertySymbols){var E=0;for(p=Object.getOwnPropertySymbols(l);E<p.length;E++)a.indexOf(p[E])<0&&Object.prototype.propertyIsEnumerable.call(l,p[E])&&(c[p[E]]=l[p[E]])}return c}function me(l,a,c,p){var J,E=arguments.length,m=E<3?a:null===p?p=Object.getOwnPropertyDescriptor(a,c):p;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)m=Reflect.decorate(l,a,c,p);else for(var _=l.length-1;_>=0;_--)(J=l[_])&&(m=(E<3?J(m):E>3?J(a,c,m):J(a,c))||m);return E>3&&m&&Object.defineProperty(a,c,m),m}function ne(l,a,c,p){return new(c||(c=Promise))(function(m,J){function _(he){try{ue(p.next(he))}catch(De){J(De)}}function oe(he){try{ue(p.throw(he))}catch(De){J(De)}}function ue(he){he.done?m(he.value):function E(m){return m instanceof c?m:new c(function(J){J(m)})}(he.value).then(_,oe)}ue((p=p.apply(l,a||[])).next())})}function w(l,a){var p,E,m,c={label:0,sent:function(){if(1&m[0])throw m[1];return m[1]},trys:[],ops:[]},J=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return J.next=_(0),J.throw=_(1),J.return=_(2),"function"==typeof Symbol&&(J[Symbol.iterator]=function(){return this}),J;function _(ue){return function(he){return function oe(ue){if(p)throw new TypeError("Generator is already executing.");for(;J&&(J=0,ue[0]&&(c=0)),c;)try{if(p=1,E&&(m=2&ue[0]?E.return:ue[0]?E.throw||((m=E.return)&&m.call(E),0):E.next)&&!(m=m.call(E,ue[1])).done)return m;switch(E=0,m&&(ue=[2&ue[0],m.value]),ue[0]){case 0:case 1:m=ue;break;case 4:return c.label++,{value:ue[1],done:!1};case 5:c.label++,E=ue[1],ue=[0];continue;case 7:ue=c.ops.pop(),c.trys.pop();continue;default:if(!(m=(m=c.trys).length>0&&m[m.length-1])&&(6===ue[0]||2===ue[0])){c=0;continue}if(3===ue[0]&&(!m||ue[1]>m[0]&&ue[1]<m[3])){c.label=ue[1];break}if(6===ue[0]&&c.label<m[1]){c.label=m[1],m=ue;break}if(m&&c.label<m[2]){c.label=m[2],c.ops.push(ue);break}m[2]&&c.ops.pop(),c.trys.pop();continue}ue=a.call(l,c)}catch(he){ue=[6,he],E=0}finally{p=m=0}if(5&ue[0])throw ue[1];return{value:ue[0]?ue[1]:void 0,done:!0}}([ue,he])}}}function v(){for(var l=0,a=0,c=arguments.length;a<c;a++)l+=arguments[a].length;var p=Array(l),E=0;for(a=0;a<c;a++)for(var m=arguments[a],J=0,_=m.length;J<_;J++,E++)p[E]=m[J];return p}function q(l,a,c){if(c||2===arguments.length)for(var m,p=0,E=a.length;p<E;p++)(m||!(p in a))&&(m||(m=Array.prototype.slice.call(a,0,p)),m[p]=a[p]);return l.concat(m||Array.prototype.slice.call(a))}function V(l){return this instanceof V?(this.v=l,this):new V(l)}function ce(l,a,c){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var E,p=c.apply(l,a||[]),m=[];return E=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),_("next"),_("throw"),_("return",function J(ve){return function(ge){return Promise.resolve(ge).then(ve,De)}}),E[Symbol.asyncIterator]=function(){return this},E;function _(ve,ge){p[ve]&&(E[ve]=function(x){return new Promise(function($,D){m.push([ve,x,$,D])>1||oe(ve,x)})},ge&&(E[ve]=ge(E[ve])))}function oe(ve,ge){try{!function ue(ve){ve.value instanceof V?Promise.resolve(ve.value.v).then(he,De):je(m[0][2],ve)}(p[ve](ge))}catch(x){je(m[0][3],x)}}function he(ve){oe("next",ve)}function De(ve){oe("throw",ve)}function je(ve,ge){ve(ge),m.shift(),m.length&&oe(m[0][0],m[0][1])}}function ae(l){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c,a=l[Symbol.asyncIterator];return a?a.call(l):(l=function K(l){var a="function"==typeof Symbol&&Symbol.iterator,c=a&&l[a],p=0;if(c)return c.call(l);if(l&&"number"==typeof l.length)return{next:function(){return l&&p>=l.length&&(l=void 0),{value:l&&l[p++],done:!l}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")}(l),c={},p("next"),p("throw"),p("return"),c[Symbol.asyncIterator]=function(){return this},c);function p(m){c[m]=l[m]&&function(J){return new Promise(function(_,oe){!function E(m,J,_,oe){Promise.resolve(oe).then(function(ue){m({value:ue,done:_})},J)}(_,oe,(J=l[m](J)).done,J.value)})}}}function A(l,a){return Object.defineProperty?Object.defineProperty(l,"raw",{value:a}):l.raw=a,l}"function"==typeof SuppressedError&&SuppressedError}}]);