(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2654],{38550:(M,p,o)=>{o.d(p,{I$:()=>E,xu:()=>v,O0:()=>h,LZ:()=>f});var d=o(15861),y=o(81536),s=o(87956),l=o(98699),b=o(33414),a=o(99877);let E=(()=>{class t{constructor(i,r,m,n,c){this.deviceService=i,this.cryptoService=r,this.biometricService=m,this.publicKeyRepository=n,this.passwordRepository=c}execute(i){var r=this;return(0,d.Z)(function*(){try{const{currentPassword:m,newPassword:n}=i,c=yield r.publicKeyRepository.requestEnrollment(),{model:g,uuid:I}=yield r.deviceService.getInfo(),{status:S,message:R}=yield r.passwordRepository.change({currentPassword:r.cryptoService.encodeRSA(c,m),newPassword:r.cryptoService.encodeRSA(c,n),deviceSerial:`${I}-${g}`});return S?l.Either.success(S&&(yield r.biometricService.savePassword(n).catch(()=>!1))):l.Either.failure({message:R})}catch({message:m}){return l.Either.failure({message:m})}})()}}return t.\u0275fac=function(i){return new(i||t)(a.\u0275\u0275inject(s.U8),a.\u0275\u0275inject(s.$I),a.\u0275\u0275inject(s.oy),a.\u0275\u0275inject(y.aH),a.\u0275\u0275inject(b.d))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),v=(()=>{class t{constructor(i){this.biometricService=i}execute(){var i=this;return(0,d.Z)(function*(){try{return yield i.biometricService.linked(),l.Either.success()}catch({message:r}){return l.Either.failure({message:r})}})()}}return t.\u0275fac=function(i){return new(i||t)(a.\u0275\u0275inject(s.oy))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var C=o(78506),e=o(27236),P=o(87903);let h=(()=>{class t{constructor(i,r,m,n){this.storageService=i,this.biometricService=r,this.customerPreferences=m,this.managerSession=n}home(){var i=this;return(0,d.Z)(function*(){try{const r=yield i.storageService.get(e.Z.BiometricLinked),m=(0,P.Q3)(yield i.biometricService.getMode()),n=yield i.customerPreferences.request(),c=yield i.managerSession.customer();return l.Either.success({biometric:m,biometricLinked:r,customer:c,preferences:n})}catch({message:r}){return l.Either.failure({message:r})}})()}}return t.\u0275fac=function(i){return new(i||t)(a.\u0275\u0275inject(s.V1),a.\u0275\u0275inject(s.x1),a.\u0275\u0275inject(s.fT),a.\u0275\u0275inject(C._I))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),f=(()=>{class t{constructor(i){this.biometricService=i}execute(){var i=this;return(0,d.Z)(function*(){try{return yield i.biometricService.unlinked(),l.Either.success()}catch({message:r}){return l.Either.failure({message:r})}})()}}return t.\u0275fac=function(i){return new(i||t)(a.\u0275\u0275inject(s.oy))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},33414:(M,p,o)=>{o.d(p,{K:()=>f,d:()=>h});var d=o(71776),s=o(39904),l=o(29306),b=o(5164),a=o(42168),v=o(84757),e=o(99877);let h=(()=>{class t{constructor(i){this.http=i}change(i){return(0,a.firstValueFrom)(this.http.post(s.bV.CHANGE_PASSWORD,i).pipe((0,v.map)(()=>({status:!0})),(0,v.catchError)(({error:r})=>(0,a.of)({status:!1,message:"123"===r?.status?.statusCode?"Por favor, elige una contrase\xf1a que no hayas usado antes.":"La contrase\xf1a no pudo ser modificada debido a un error interno, por favor intente m\xe1s tarde."}))))}}return t.\u0275fac=function(i){return new(i||t)(e.\u0275\u0275inject(d.HttpClient))},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),f=(()=>{class t{constructor(i){this.http=i}requestKey(){return this.publicKey$||(this.publicKey$=(0,a.firstValueFrom)(this.http.get(s.bV.PRODUCTS.ACTIVATE.CREDIT_CARD.PUBLIC_KEY).pipe((0,v.map)(({publicKey:i})=>new l.nh(i)))).catch(i=>{throw this.publicKey$=void 0,new b.Kr(i,"Ocurri\xf3 un error inesperado. No pudimos consultar datos de la plataforma (CK03)")})),this.publicKey$}request(i){return(0,a.firstValueFrom)(this.http.post(s.bV.PRODUCTS.ACTIVATE.CREDIT_CARD.REQUEST,{creditCard:i}))}}return t.\u0275fac=function(i){return new(i||t)(e.\u0275\u0275inject(d.HttpClient))},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},42654:(M,p,o)=>{o.r(p),o.d(p,{MboSecurityBiometricPageModule:()=>i});var d=o(17007),y=o(78007),s=o(30263),l=o(15861),b=o(78506),a=o(39904),E=o(87956),v=o(95437),C=o(38550),e=o(99877),P=o(48774),h=o(66613),f=o(45542);const t=a.Z6.CUSTOMER.SECURITY;let u=(()=>{class r{constructor(n,c,g,I,S){this.activateRoute=n,this.mboProvider=c,this.biometricService=g,this.requestBiometric=I,this.linkedBiometric=S,this.template=a.tz,this.backAction={id:"btn_security-biometric_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(t.HOME)}},this.cancelAction={id:"btn_security-biometric_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.back(a.Z6.CUSTOMER.PRODUCTS.HOME)}}}ngOnInit(){this.requestConfiguration()}onBiometric(){var n=this;return(0,l.Z)(function*(){const{from:c}=n.activateRoute.snapshot.queryParams;(yield n.biometricService.authentication())&&(yield n.linkedBiometric.execute()).when({success:()=>{n.mboProvider.navigation.next(t.BIOMETRIC_SUCCESS,{from:c})},failure:()=>{n.mboProvider.toast.error(`No pudimos activar tu servicio biom\xe9trico ${n.template.label}, por favor intenta nuevamente.`)}})})()}onCancel(){this.mboProvider.navigation.next(t.HOME)}requestConfiguration(){var n=this;return(0,l.Z)(function*(){(yield n.requestBiometric.execute()).when({success:c=>{n.template=c}})})()}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(y.ActivatedRoute),e.\u0275\u0275directiveInject(v.ZL),e.\u0275\u0275directiveInject(E.x1),e.\u0275\u0275directiveInject(b.aW),e.\u0275\u0275directiveInject(C.xu))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-security-biometric-page"]],decls:18,vars:8,consts:[[1,"mbo-security-biometric-page__content"],[1,"mbo-security-biometric-page__header"],["title","Biometr\xeda",3,"leftAction","rightAction"],[1,"mbo-security-biometric-page__body"],[1,"mbo-security-biometric-page__title","subtitle2-medium"],[1,"mbo-security-biometric-page__logo"],["alt","BOCC Login Enabled Biometric Page",3,"src"],[1,"mbo-security-biometric-page__message","body2-medium"],["icon","bell",3,"visible"],[1,"mbo-security-biometric-page__footer"],["id","btn_security-biometric_submit","bocc-button","raised",3,"disabled","click"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",5),e.\u0275\u0275element(7,"img",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"p",7),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"bocc-alert",8)(11,"b"),e.\u0275\u0275text(12,"Recuerda:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(14,"div",9)(15,"button",10),e.\u0275\u0275listener("click",function(){return c.onBiometric()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Entendido"),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",c.backAction)("rightAction",c.cancelAction),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==c.template?null:c.template.title," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("src",null==c.template?null:c.template.logo,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",null==c.template?null:c.template.message," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==c.template?null:c.template.alert," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",!(null!=c.template&&c.template.enabled)))},dependencies:[P.J,h.B,f.P],styles:["mbo-security-biometric-page{position:relative;display:flex;width:100%;height:100%;row-gap:var(--sizing-x12);overflow:auto;flex-direction:column;justify-content:space-between}mbo-security-biometric-page .mbo-security-biometric-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-security-biometric-page .mbo-security-biometric-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-security-biometric-page .mbo-security-biometric-page__title{position:relative;width:100%;text-align:center}mbo-security-biometric-page .mbo-security-biometric-page__logo{position:relative;display:flex;width:100%;justify-content:center}mbo-security-biometric-page .mbo-security-biometric-page__logo img{width:44rem;height:44rem}mbo-security-biometric-page .mbo-security-biometric-page__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-security-biometric-page .mbo-security-biometric-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-security-biometric-page .mbo-security-biometric-page__footer button{width:100%}\n"],encapsulation:2}),r})(),i=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,y.RouterModule.forChild([{path:"",component:u}]),s.Jx,s.B4,s.P8]}),r})()}}]);