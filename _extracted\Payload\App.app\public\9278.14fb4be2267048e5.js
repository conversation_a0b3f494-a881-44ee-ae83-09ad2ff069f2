(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9278],{49278:(W,y,s)=>{s.r(y),s.d(y,{APOLLO_FLAGS:()=>v,APOLLO_NAMED_OPTIONS:()=>P,APOLLO_OPTIONS:()=>I,Apollo:()=>a,ApolloBase:()=>f,ApolloModule:()=>j,Mutation:()=>R,Query:()=>m,QueryRef:()=>w,Subscription:()=>S,gql:()=>T,graphql:()=>k});var i=s(99877),u=s(42168),l=s(61595),h=s(84757);function L(t){return new u.Observable(n=>(t().then(e=>{n.closed||(n.next(e),n.complete())},e=>{n.closed||n.error(e)}),()=>n.unsubscribe()))}class C{zone;constructor(n){this.zone=n}now=Date.now?Date.now:()=>+new Date;schedule(n,e=0,r){return this.zone.run(()=>u.queueScheduler.schedule(n,e,r))}}function b(t){return t[u.observable]=()=>t,t}function O(t,n){return t.pipe((0,h.observeOn)(new C(n)))}function M(t,n,e){return t&&typeof t[n]<"u"?t[n]:e}class w{obsQuery;valueChanges;queryId;constructor(n,e,r){this.obsQuery=n;const o=O((0,u.from)(b(this.obsQuery)),e);this.valueChanges=r.useInitialLoading?o.pipe(function _(t){return function(e){return new u.Observable(function(o){const d=t.getCurrentResult(),{loading:c,errors:p,error:q,partial:N,data:Q}=d,{partialRefetch:Z,fetchPolicy:F}=t.options,B=p||q;return Z&&N&&(!Q||0===Object.keys(Q).length)&&"cache-only"!==F&&!c&&!B&&o.next({...d,loading:!0,networkStatus:l.NetworkStatus.loading}),e.subscribe(o)})}}(this.obsQuery)):o,this.queryId=this.obsQuery.queryId}get options(){return this.obsQuery.options}get variables(){return this.obsQuery.variables}result(){return this.obsQuery.result()}getCurrentResult(){return this.obsQuery.getCurrentResult()}getLastResult(){return this.obsQuery.getLastResult()}getLastError(){return this.obsQuery.getLastError()}resetLastResults(){return this.obsQuery.resetLastResults()}refetch(n){return this.obsQuery.refetch(n)}fetchMore(n){return this.obsQuery.fetchMore(n)}subscribeToMore(n){return this.obsQuery.subscribeToMore(n)}updateQuery(n){return this.obsQuery.updateQuery(n)}stopPolling(){return this.obsQuery.stopPolling()}startPolling(n){return this.obsQuery.startPolling(n)}setOptions(n){return this.obsQuery.setOptions(n)}setVariables(n){return this.obsQuery.setVariables(n)}}const v=new i.InjectionToken("APOLLO_FLAGS"),I=new i.InjectionToken("APOLLO_OPTIONS"),P=new i.InjectionToken("APOLLO_NAMED_OPTIONS");class f{ngZone;flags;_client;useInitialLoading;useMutationLoading;constructor(n,e,r){this.ngZone=n,this.flags=e,this._client=r,this.useInitialLoading=M(e,"useInitialLoading",!1),this.useMutationLoading=M(e,"useMutationLoading",!1)}watchQuery(n){return new w(this.ensureClient().watchQuery({...n}),this.ngZone,{useInitialLoading:this.useInitialLoading,...n})}query(n){return L(()=>this.ensureClient().query({...n}))}mutate(n){return function A(t,n){return n?t.pipe((0,h.startWith)({loading:!0}),(0,h.map)(e=>({...e,loading:!!e.loading}))):t.pipe((0,h.map)(e=>({...e,loading:!1})))}(L(()=>this.ensureClient().mutate({...n})),n.useMutationLoading??this.useMutationLoading)}subscribe(n,e){const r=(0,u.from)(b(this.ensureClient().subscribe({...n})));return e&&!0!==e.useZone?r:O(r,this.ngZone)}getClient(){return this.client}setClient(n){this.client=n}get client(){return this._client}set client(n){if(this._client)throw new Error("Client has been already defined");this._client=n}ensureClient(){return this.checkInstance(),this._client}checkInstance(){if(!this._client)throw new Error("Client has not been defined yet")}}let a=(()=>{class t extends f{_ngZone;map=new Map;constructor(e,r,o,d){if(super(e,d),this._ngZone=e,r&&this.createDefault(r),o&&"object"==typeof o)for(let c in o)o.hasOwnProperty(c)&&this.create(o[c],c)}create(e,r){g(r)?this.createDefault(e):this.createNamed(r,e)}default(){return this}use(e){return g(e)?this.default():this.map.get(e)}createDefault(e){if(this.getClient())throw new Error("Apollo has been already created.");return this.setClient(new l.ApolloClient(e))}createNamed(e,r){if(this.map.has(e))throw new Error(`Client ${e} has been already created`);this.map.set(e,new f(this._ngZone,this.flags,new l.ApolloClient(r)))}removeClient(e){g(e)?this._client=void 0:this.map.delete(e)}static \u0275fac=function(r){return new(r||t)(i.\u0275\u0275inject(i.NgZone),i.\u0275\u0275inject(I,8),i.\u0275\u0275inject(P,8),i.\u0275\u0275inject(v,8))};static \u0275prov=i.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac})}return t})();function g(t){return!t||"default"===t}const E=[a];let j=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275mod=i.\u0275\u0275defineNgModule({type:t});static \u0275inj=i.\u0275\u0275defineInjector({providers:E})}return t})(),m=(()=>{class t{apollo;document;client="default";constructor(e){this.apollo=e}watch(e,r){return this.apollo.use(this.client).watchQuery({...r,variables:e,query:this.document})}fetch(e,r){return this.apollo.use(this.client).query({...r,variables:e,query:this.document})}static \u0275fac=function(r){return new(r||t)(i.\u0275\u0275inject(a))};static \u0275prov=i.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac})}return t})(),R=(()=>{class t{apollo;document;client="default";constructor(e){this.apollo=e}mutate(e,r){return this.apollo.use(this.client).mutate({...r,variables:e,mutation:this.document})}static \u0275fac=function(r){return new(r||t)(i.\u0275\u0275inject(a))};static \u0275prov=i.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac})}return t})(),S=(()=>{class t{apollo;document;client="default";constructor(e){this.apollo=e}subscribe(e,r,o){return this.apollo.use(this.client).subscribe({...r,variables:e,query:this.document},o)}static \u0275fac=function(r){return new(r||t)(i.\u0275\u0275inject(a))};static \u0275prov=i.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac})}return t})();function D(t,...n){return(0,l.gql)(t,...n)}const T=D,k=D}}]);