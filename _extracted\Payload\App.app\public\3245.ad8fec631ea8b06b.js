(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3245],{83245:(f,a,n)=>{n.r(a),n.d(a,{MboTransferAdvanceModule:()=>h});var t=n(17007),l=n(78007),d=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(616).then(n.bind(n,616)).then(o=>o.MboTransferAdvanceSourcePageModule)},{path:"destination",loadChildren:()=>n.e(351).then(n.bind(n,20351)).then(o=>o.MboTransferAdvanceDestinationPageModule)},{path:"amount",loadChildren:()=>n.e(788).then(n.bind(n,90788)).then(o=>o.MboTransferAdvanceAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(344).then(n.bind(n,90344)).then(o=>o.MboTransferAdvanceConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(158).then(n.bind(n,10158)).then(o=>o.MboTransferAdvanceResultPageModule)}];let h=(()=>{class o{}return o.\u0275fac=function(e){return new(e||o)},o.\u0275mod=d.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=d.\u0275\u0275defineInjector({imports:[t.CommonModule,l.RouterModule.forChild(M)]}),o})()}}]);