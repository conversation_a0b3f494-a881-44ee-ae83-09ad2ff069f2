(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4709],{94709:(h,s,t)=>{t.r(s),t.d(s,{MboCelToCelSendDestinationPageModule:()=>Z});var p=t(17007),g=t(78007),a=t(30263),d=t(15861),c=t(24495),l=t(39904),f=t(95437),P=t(57544),S=t(20827),T=t(52484),C=t(56868),x=t(17758),z=t(98487),y=t(6468),e=t(99877),A=t(48774),M=t(35641),E=t(98853),D=t(2460),I=t(23436),_=t(45542);function j(i,m){if(1&i){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14),e.\u0275\u0275element(1,"bocc-ballot",15),e.\u0275\u0275elementStart(2,"bocc-icon",16),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onRemovePhone())}),e.\u0275\u0275elementEnd()()}if(2&i){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("title",n.phone.contact)("subtitle",n.phone.numberFormat)}}function F(i,m){1&i&&(e.\u0275\u0275elementStart(0,"div",17),e.\u0275\u0275element(1,"bocc-icon",18),e.\u0275\u0275elementStart(2,"span",19),e.\u0275\u0275text(3," N\xfamero de contacto debe iniciar con n\xfamero 3 "),e.\u0275\u0275elementEnd()())}const O=l.Z6.TRANSFERS.CELTOCEL.SEND;let N=(()=>{class i{constructor(n,o,r,b,v,u){this.blueScreenService=n,this.mboProvider=o,this.contactsProvider=r,this.requestConfiguration=b,this.managerCelToCel=v,this.cancelProvider=u,this.confirmation=!1,this.backInvalid=!1,this.requesting=!0,this.requestingContact=!1,this.backAction={id:"btn_cel-to-cel-send-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting||this.confirmation||this.backInvalid,click:()=>{this.cancelProvider.backCustomer(this.confirmation)}},this.cancelAction={id:"btn_cel-to-cel-send-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}},this.phoneControl=new P.FormControl({validators:[c.C1,T.O,(0,c.Dx)(10)]})}ngOnInit(){this.contactsProvider.subscribe(({number:n,name:o})=>{this.phone=new C.cw(n,o)}),this.initializatedConfiguration()}get contactInvalid(){return!!this.phone&&!(0,S.lE)(this.phone.number)}get disabled(){return this.phone?this.contactInvalid:this.phoneControl.invalid}onContacts(){var n=this;return(0,d.Z)(function*(){n.requestingContact=!0,yield n.contactsProvider.open(),n.requestingContact=!1})()}onRemovePhone(){this.phone=void 0}onSubmit(){var n=this;return(0,d.Z)(function*(){n.mboProvider.loader.open("Verificando datos, por favor espere...");const o=n.getCelToCelPhone();(yield n.managerCelToCel.verfiyContact(o)).when({success:r=>{r?(n.blueScreenService.create(y.Qz,{componentProps:{phoneNumber:o.numberFormat}}).open(120),n.mboProvider.navigation.next(O.ACCOUNT)):n.blueScreenService.create(y.ze,{componentProps:{phoneNumber:o.numberFormat}}).open()},failure:()=>{n.mboProvider.toast.error("No pudimos realizar la verificaci\xf3n del n\xfamero de telefono, por favor vuelva a intentarlo")}},()=>{n.mboProvider.loader.close()})})()}initializatedConfiguration(){var n=this;return(0,d.Z)(function*(){(yield n.requestConfiguration.destination()).when({success:({confirmation:o,hasOneSource:r,phone:b})=>{if(b){const{number:v,contact:u}=b;u?n.phone=b:n.phoneControl.setValue(v),n.backInvalid=r,n.confirmation=o}}},()=>{n.requesting=!1})})()}getCelToCelPhone(){return this.phone??new C.cw(this.phoneControl.value)}}return i.\u0275fac=function(n){return new(n||i)(e.\u0275\u0275directiveInject(a.Dl),e.\u0275\u0275directiveInject(f.ZL),e.\u0275\u0275directiveInject(f.i),e.\u0275\u0275directiveInject(x.ZW),e.\u0275\u0275directiveInject(x.Ey),e.\u0275\u0275directiveInject(z.T))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-cel-to-cel-send-destination-page"]],decls:19,vars:8,consts:[[1,"mbo-cel-to-cel-send-destination-page__content"],[1,"mbo-cel-to-cel-send-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-cel-to-cel-send-destination-page__body"],[1,"mbo-cel-to-cel-send-destination-page__selection"],[1,"subtitle2-medium"],["class","mbo-cel-to-cel-send-destination-page__contact",4,"ngIf"],["class","mbo-cel-to-cel-send-destination-page__error",4,"ngIf"],["id","txt_cel-to-cel-send-destination_phone","label","Ingresa el n\xfamero de celular","placeholder","************","type","phone","prefix","+57",3,"hidden","formControl"],[1,"mbo-cel-to-cel-send-destination-page__contacts"],[1,"mbo-cel-to-cel-send-destination-page__info","body2-medium"],["id","btn_cel-to-cel-send-destination_contacts","icon","user-contacts",3,"disabled","click"],[1,"mbo-cel-to-cel-send-destination-page__footer"],["id","btn_cel-to-cel-send-destination_submit","bocc-button","raised",3,"disabled","click"],[1,"mbo-cel-to-cel-send-destination-page__contact"],["icon","mobile-contact",3,"title","subtitle"],["icon","close",3,"click"],[1,"mbo-cel-to-cel-send-destination-page__error"],["icon","error"],[1,"caption-medium"]],template:function(n,o){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"p",5),e.\u0275\u0275text(6,"\xbfA qui\xe9n deseas transferirle hoy?"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(7,j,3,2,"div",6),e.\u0275\u0275template(8,F,4,0,"div",7),e.\u0275\u0275element(9,"bocc-growing-box",8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",9)(11,"p",10),e.\u0275\u0275text(12," Tambi\xe9n puedes seleccionar uno de tus contactos "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"bocc-card-category",11),e.\u0275\u0275listener("click",function(){return o.onContacts()}),e.\u0275\u0275text(14," Contactos del celular "),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(15,"div",12)(16,"button",13),e.\u0275\u0275listener("click",function(){return o.onSubmit()}),e.\u0275\u0275elementStart(17,"span"),e.\u0275\u0275text(18,"Continuar"),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",o.phone),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.contactInvalid),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",o.phone)("formControl",o.phoneControl),e.\u0275\u0275advance(4),e.\u0275\u0275property("disabled",o.requestingContact),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",o.disabled))},dependencies:[p.NgIf,A.J,M.d,E.s,D.Z,I.D,_.P],styles:["/*!\n * MBO CelToCelSendDestination Page\n * v2.1.1\n * Author: MB Frontend Developers\n * Created: 04/Nov/2022\n * Updated: 18/Jun/2024\n*/mbo-cel-to-cel-send-destination-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x24)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__selection{display:flex;flex-direction:column;row-gap:var(--sizing-x24)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__contacts{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__contact{position:relative;display:flex;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__contact>bocc-icon{margin:auto 0rem auto var(--sizing-x2);color:var(--color-carbon-lighter-700)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__error{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__error bocc-icon{font-size:var(--sizing-x8);width:var(--sizing-x8);height:var(--sizing-x8);color:var(--color-semantic-danger-700)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__error span{color:var(--color-carbon-lighter-700)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__message{color:var(--color-carbon-darker-1000)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__info{position:relative;color:var(--color-carbon-darker-1000)}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-cel-to-cel-send-destination-page .mbo-cel-to-cel-send-destination-page__footer button{width:100%}\n"],encapsulation:2}),i})(),Z=(()=>{class i{}return i.\u0275fac=function(n){return new(n||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[p.CommonModule,g.RouterModule.forChild([{path:"",component:N}]),a.Jx,a.dH,a.s,a.Zl,a.D0,a.P8,a.bE]}),i})()},20827:(h,s,t)=>{function g(c){return"3"===c.charAt(0)}t.d(s,{lE:()=>g}),t(87903)},52484:(h,s,t)=>{t.d(s,{O:()=>d,b:()=>c});var p=t(20827);const g=/^[a-z|A-Z|0-9|@|.]*$/,a=/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,d=l=>l&&!(0,p.lE)(l)?{id:"firstCharPhone",message:"Campo debe iniciar con n\xfamero 3"}:null,c=l=>!l||a.test(l)||g.test(l)?null:{id:"tagAvalOrKey",message:"Campo no cumple con los criterios para ser una llave"}}}]);