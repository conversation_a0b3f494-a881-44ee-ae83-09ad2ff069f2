(self.webpackChunkapp=self.webpackChunkapp||[]).push([[260],{9736:(T,P,o)=>{o.d(P,{Hu:()=>i,wG:()=>B,rQ:()=>U,_G:()=>L});var f=o(15861),g=o(87956),b=o(53113),a=o(98699);class p{constructor(u,e,n,c,d){this.id=u,this.nit=e,this.name=n,this.city=c,this.exampleUrl=d}}class R{constructor(u,e,n,c){this.number=u,this.amount=e,this.expirationDate=n,this.companyId=c}get expirationFormat(){return this.expirationDate.dateFormat}}class S{constructor(u,e,n){this.agreement=u,this.invoice=e,this.source=n}}function v(t){return new S(t.agreement,t.invoice,t.source)}function C(t){return new p(t.orgId<PERSON>um,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var I=o(71776),E=o(39904),F=o(87903),M=o(42168),y=o(84757),s=o(99877);let O=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,M.firstValueFrom)(this.http.get(E.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,y.map)(({content:n})=>n.map(c=>C(c)))))}requestCompanyId(e){return(0,M.firstValueFrom)(this.http.get(E.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,y.map)(({content:n})=>n.length?C(n[0]):null)))}requestInvoice(e,n){return(0,M.firstValueFrom)(this.http.get(E.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,y.map)(c=>function h(t){return new R(t.nie||t.invoiceNum,+t.amt,new b.ou(t.expDt),t.orgIdNum)}(c))))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),l=(()=>{class t{constructor(e){this.http=e}send(e){return(0,M.firstValueFrom)(this.http.post(E.bV.PAYMENTS.INVOICE_MANUAL,function r(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,y.map)(([n])=>(0,F.l1)(n,"SUCCESS")))).catch(n=>(0,F.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var A=o(20691);let m=(()=>{class t extends A.Store{constructor(e){super({confirmation:!1}),e.subscribes(E.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(g.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),i=(()=>{class t{constructor(e,n,c){this.repository=e,this.store=n,this.eventBusService=c}setAgreement(e){try{return a.Either.success(this.store.setAgreement(e))}catch({message:n}){return a.Either.failure({message:n})}}setInvoice(e){try{return a.Either.success(this.store.setInvoice(e))}catch({message:n}){return a.Either.failure({message:n})}}setSource(e){try{return a.Either.success(this.store.setSource(e))}catch({message:n}){return a.Either.failure({message:n})}}reset(){try{return a.Either.success(this.store.reset())}catch({message:e}){return a.Either.failure({message:e})}}send(){var e=this;return(0,f.Z)(function*(){const n=v(e.store.currentState),c=yield e.execute(n);return e.eventBusService.emit(c.channel),a.Either.success({invoice:n,status:c})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(b.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(l),s.\u0275\u0275inject(m),s.\u0275\u0275inject(g.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),B=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,f.Z)(function*(){try{return a.Either.success(yield n.repository.requestAll(e))}catch({message:c,status:d}){return 400===d?a.Either.success([]):a.Either.failure({message:c})}})()}invoice(e,{id:n}){var c=this;return(0,f.Z)(function*(){try{return a.Either.success(yield c.repository.requestInvoice(e,n))}catch({message:d,status:N}){return a.Either.failure({value:400===N,message:d})}})()}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(O))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),U=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return a.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return a.Either.failure({message:e})}}source(){var e=this;return(0,f.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),c=e.store.getAgreement(),d=e.store.getInvoice();return a.Either.success({agreement:c,invoice:d,products:n})}catch({message:n}){return a.Either.failure({message:n})}})()}confirmation(){try{const e=v(this.store.currentState);return a.Either.success({payment:e})}catch({message:e}){return a.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(g.hM),s.\u0275\u0275inject(m))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const V=/^415(\d+)8020(\d+)$/;let x=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,f.Z)(function*(){const c=e.replace(/\D/g,"").match(V);if(!c)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const d=yield n.requestNuraCodes(),N=c[1],D=d.find(({ean_code:K})=>K===N);if(!D)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:c[2].slice(0,+D.length),companyId:D.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,M.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,y.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),L=(()=>{class t{constructor(e,n,c){this.repository=e,this.store=n,this.barcodeService=c}execute(e){var n=this;return(0,f.Z)(function*(){try{const{reference:c,companyId:d}=yield n.barcodeService.execute(e),N=yield n.repository.requestCompanyId(d),D=yield n.repository.requestInvoice(c,d);return n.store.setAgreement(N),n.store.setInvoice(D),a.Either.success()}catch{return a.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(O),s.\u0275\u0275inject(m),s.\u0275\u0275inject(x))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},74242:(T,P,o)=>{o.d(P,{s:()=>S});var f=o(39904),g=o(95437),b=o(30263),a=o(9736),p=o(99877);let S=(()=>{class v{constructor(h,r,I){this.modalConfirmation=h,this.mboProvider=r,this.managerInvoice=I}execute(h=!0){h?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(f.Z6.PAYMENTS.HOME)}}return v.\u0275fac=function(h){return new(h||v)(p.\u0275\u0275inject(b.$e),p.\u0275\u0275inject(g.ZL),p.\u0275\u0275inject(a.Hu))},v.\u0275prov=p.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},40260:(T,P,o)=>{o.r(P),o.d(P,{MboPaymentInvoiceManualConfirmationPageModule:()=>O});var f=o(17007),g=o(78007),b=o(79798),a=o(30263),p=o(83651),R=o(15861),S=o(39904),v=o(95437),C=o(9736),h=o(74242),r=o(99877),I=o(10464),E=o(48774),F=o(17941),M=o(45542);const y=S.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL;let s=(()=>{class l{constructor(m,i,j){this.mboProvider=m,this.requestConfiguration=i,this.cancelProvider=j,this.backAction={id:"btn_payment-invoice-manual-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(y.SOURCE)}},this.cancelAction={id:"btn_payment-invoice-manual-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}}}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(y.RESULT)}initializatedConfiguration(){var m=this;return(0,R.Z)(function*(){(yield m.requestConfiguration.confirmation()).when({success:({payment:i})=>{m.payment=i}})})()}}return l.\u0275fac=function(m){return new(m||l)(r.\u0275\u0275directiveInject(v.ZL),r.\u0275\u0275directiveInject(C.rQ),r.\u0275\u0275directiveInject(h.s))},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-invoice-manual-confirmation-page"]],decls:16,vars:8,consts:[[1,"mbo-payment-invoice-manual-confirmation-page__content","mbo-page__scroller"],[1,"mbo-payment-invoice-manual-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-manual-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail"],["header","LA SUMA DE",3,"amount"],["header","DESDE",3,"title","subtitle"],[1,"mbo-payment-invoice-manual-confirmation-page__footer"],["id","btn_payment-invoice-manual-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"click"]],template:function(m,i){1&m&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),r.\u0275\u0275element(3,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),r.\u0275\u0275text(7," \xbfDeseas pagar la factura? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"div",6),r.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),r.\u0275\u0275elementEnd()()()(),r.\u0275\u0275elementStart(12,"div",10)(13,"button",11),r.\u0275\u0275listener("click",function(){return i.onSubmit()}),r.\u0275\u0275elementStart(14,"span"),r.\u0275\u0275text(15,"Pagar"),r.\u0275\u0275elementEnd()()()()),2&m&&(r.\u0275\u0275advance(3),r.\u0275\u0275property("leftAction",i.backAction)("rightAction",i.cancelAction),r.\u0275\u0275advance(6),r.\u0275\u0275property("title",null==i.payment||null==i.payment.invoice?null:i.payment.invoice.nickname)("subtitle",null==i.payment||null==i.payment.invoice?null:i.payment.invoice.number)("detail",null==i.payment||null==i.payment.agreement?null:i.payment.agreement.name),r.\u0275\u0275advance(1),r.\u0275\u0275property("amount",null==i.payment||null==i.payment.invoice?null:i.payment.invoice.amount),r.\u0275\u0275advance(1),r.\u0275\u0275property("title",null==i.payment||null==i.payment.source?null:i.payment.source.nickname)("subtitle",null==i.payment||null==i.payment.source?null:i.payment.source.number))},dependencies:[I.K,E.J,F.D,M.P],styles:["/*!\n * MBO PaymentInvoiceManualConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-invoice-manual-confirmation-page .mbo-payment-invoice-manual-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-invoice-manual-confirmation-page .mbo-payment-invoice-manual-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-invoice-manual-confirmation-page .mbo-payment-invoice-manual-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-payment-invoice-manual-confirmation-page .mbo-payment-invoice-manual-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),l})(),O=(()=>{class l{}return l.\u0275fac=function(m){return new(m||l)},l.\u0275mod=r.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=r.\u0275\u0275defineInjector({imports:[f.CommonModule,g.RouterModule.forChild([{path:"",component:s}]),b.KI,a.Jx,a.DM,a.B4,a.Dj,p.P6,a.P8]}),l})()}}]);