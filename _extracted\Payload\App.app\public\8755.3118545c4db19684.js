(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8755],{88844:(S,x,e)=>{e.d(x,{YI:()=>C,tc:()=>y,iR:()=>P,jq:()=>D,Hv:()=>I,S6:()=>M,E2:()=>r,V4:()=>m,wp:()=>H,CE:()=>a,YQ:()=>E,ND:()=>A,t1:()=>o});var g=e(6472);class T{constructor(d){this.value=d}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:d}){return this.value.id===d}filtrable(d){return(0,g.hasPattern)(this.value.name,d)}}function E(R){return R.map(d=>new T(d))}class t{constructor(d){this.currency=d}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(d){return this.currency.code===d?.code}filtrable(d){return!0}}function A(R){return R.map(d=>new t(d))}var h=e(39904);class I{constructor(d){this.value=d}get title(){return this.value.label}get description(){return this.value.label}compareTo(d){return this.value.reference===d.reference}filtrable(d){return!0}}const C=h.Bf.map(R=>new I(R));class b{constructor(d,B){this.value=d,this.title=this.value.label,this.description=B?this.value.code:this.value.label}compareTo(d){return this.value===d}filtrable(d){return!0}}const D=new b(h.Gd),M=new b(h.XU),p=new b(h.t$),r=new b(h.j1),m=new b(h.k7),P=[D,M,p,r],y=[new b(h.Gd,!0),new b(h.XU,!0),new b(h.t$,!0),new b(h.j1,!0)];class v{constructor(d){this.product=d}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(d){return this.product.id===d?.id}filtrable(d){return!0}}function o(R){return R.map(d=>new v(d))}var f=e(89148);class c{constructor(d){this.filter=d}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(d){return this.value===d}filtrable(d){return!0}}const a=new c({label:"Todos los productos",short:"Todos",value:f.Gt.None}),s=new c({label:"Cuentas de ahorro",short:"Ahorros",value:f.Gt.SavingAccount}),_=new c({label:"Cuentas corriente",short:"Corrientes",value:f.Gt.CheckingAccount}),n=new c({label:"Depositos electr\xf3nicos",short:"Depositos",value:f.Gt.ElectronicDeposit}),l=new c({label:"Cuentas AFC",short:"AFC",value:f.Gt.AfcAccount}),i=new c({label:"Tarjetas de cr\xe9dito",short:"TC",value:f.Gt.CreditCard}),u=new c({label:"Inversiones",short:"Inversiones",value:f.Gt.CdtAccount}),O=new c({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:f.Gt.Loan}),N=new c({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:f.Gt.ResolvingCredit}),F=new c({label:"Productos Aval",short:"Aval",value:f.Gt.Aval}),U=new c({label:"Productos fiduciarios",short:"Fiducias",value:f.Gt.Trustfund}),L=new c({label:"Otros productos",short:"Otros",value:f.Gt.None}),H={SDA:s,DDA:_,EDA:n,AFC:l,CCA:i,CDA:u,DLA:O,LOC:N,AVAL:F,80:U,MDA:L,NONE:L,SBA:L,VDA:L}},88039:(S,x,e)=>{e.d(x,{Oz:()=>T,fN:()=>E,vA:()=>t});var g=e(29306);class T{constructor(h,I,C,b,D){this.number=h,this.type=I,this.name=C,this.nickname=b,this.bank=D}get description(){return this.nickname||this.name}}class E{constructor(h,I,C,b,D,M,p,r,m){this.uuid=h,this.date=I,this.source=C,this.destination=b,this.amount=D,this.type=M,this.status=p,this.category=r,this.approvedCode=m}get dateFormat(){return this.date.dateFormat}get timeFormat(){return this.date.timeFormat}}class t extends g.Ay{static empty(){return new t([],0,!0)}}},93637:(S,x,e)=>{e.d(x,{U:()=>a,a:()=>c});var g=e(15861),T=e(3372),E=e(87956),t=e(98699),A=e(71776),h=e(39904),I=e(42168),C=e(84757);const b={D:{color:"success",label:"Exitosa"},R:{color:"danger",label:"Fallida"},P:{color:"alert",label:"Enviada"}};var M=e(88039),p=e(29306),r=e(53113),m=e(33876);const P={color:"primary",label:"Desconocido"};var v=e(99877);const o={items:String(10),order:"DESC",orderField:"xferEffDt"};let f=(()=>{class s{constructor(n,l){this.http=n,this.eventBusService=l,this.eventBusService.subscribes(h.PU,()=>{this.history=void 0})}request(n){if(this.history)return Promise.resolve(this.history);const l=n||h.cC,{end:i,start:u}=l.getFormat();return(0,I.firstValueFrom)(this.remote({...o,page:"0",EndDt:i,StartDt:u}).pipe((0,C.tap)(O=>{O.range=l,this.history=O})))}refresh(n){const{end:l,start:i}=n.getFormat();return(0,I.firstValueFrom)(this.remote({...o,page:"0",EndDt:l,StartDt:i}).pipe((0,C.tap)(u=>{u.range=n,this.history=u})))}requestForUuid(n){return this.history?.requestForUuid(n)}nextPage(){var n=this;return(0,g.Z)(function*(){if(!n.history)return n.request().then(({collection:u})=>u);const{end:l,start:i}=n.history.range.getFormat();return(0,I.firstValueFrom)(n.remote({...o,page:n.history.currentPage.toString(),EndDt:l,StartDt:i}).pipe((0,C.map)(({collection:u})=>(n.history.merge(u),n.history.collection))))})()}remote(n){return this.http.get(h.bV.TRANSFERS.HISTORY,{params:{...n}}).pipe((0,C.map)(({content:l,totalPage:i})=>new M.vA(l.map(u=>function y(s){return new M.fN((0,m.v4)(),new r.ou(s.xferEffDt),new M.Oz(s.fromAcctId,s.fromAcctType,s.fromAcctName,s.fromNickName,new p.Br(s.fromBankId,s.fromBankName)),new M.Oz(s.toAcctId,s.toAcctType,s.toAcctName,s.toNickName,new p.Br(s.toBankId,s.toBankName)),+s.amt,s.trnType,b[s.xferStatusDesc]||P,s.trnReccategory,s.approvalId)}(u)),i)),(0,C.catchError)(l=>{if(this.history)return(0,I.of)(M.vA.empty());throw l}))}}return s.\u0275fac=function(n){return new(n||s)(v.\u0275\u0275inject(A.HttpClient),v.\u0275\u0275inject(E.Yd))},s.\u0275prov=v.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})(),c=(()=>{class s{constructor(n,l,i){this.histories=n,this.products=l,this.preferencesService=i}transfers(){var n=this;return(0,g.Z)(function*(){try{const l=yield n.preferencesService.requestBoolean(T.M.TagAval),i=n.histories.request();return t.Either.success({enabledTagAval:l,history$:i})}catch({message:l}){return t.Either.failure({message:l})}})()}trustfunds(){var n=this;return(0,g.Z)(function*(){try{const l=yield n.products.requestTrustfunds();return l.length?t.Either.success(l):t.Either.failure({message:"Da clic en <b>Quiero invertir</b> y crea tu inversi\xf3n",value:!1})}catch{return t.Either.failure({message:"Ocurrio un error al tratar de consultar tus productos fiduciarios, por favor intente m\xe1s tarde",value:!0})}})()}}return s.\u0275fac=function(n){return new(n||s)(v.\u0275\u0275inject(f),v.\u0275\u0275inject(E.hM),v.\u0275\u0275inject(E.yW))},s.\u0275prov=v.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})(),a=(()=>{class s{constructor(n){this.histories=n}firstPage(){var n=this;return(0,g.Z)(function*(){try{return t.Either.success(yield n.histories.request())}catch({message:l}){return t.Either.failure({message:l})}})()}nextPage(){var n=this;return(0,g.Z)(function*(){try{return t.Either.success(yield n.histories.nextPage())}catch({message:l}){return t.Either.failure({message:l})}})()}refresh(n){var l=this;return(0,g.Z)(function*(){try{return t.Either.success(yield l.histories.refresh(n))}catch({message:i}){return t.Either.failure({message:i})}})()}historyForUuid(n){try{const l=this.histories.requestForUuid(n);return l?t.Either.success(l):t.Either.failure()}catch({message:l}){return t.Either.failure({message:l})}}}return s.\u0275fac=function(n){return new(n||s)(v.\u0275\u0275inject(f))},s.\u0275prov=v.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},83991:(S,x,e)=>{e.d(x,{By:()=>A,$J:()=>D,Yh:()=>f,HV:()=>c}),e(26060);var T=e(17007),E=e(30263),t=e(99877);let A=(()=>{class a{}return a.\u0275fac=function(_){return new(_||a)},a.\u0275mod=t.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=t.\u0275\u0275defineInjector({imports:[T.CommonModule,E.vB]}),a})();e(56921);let I=(()=>{class a{}return a.\u0275fac=function(_){return new(_||a)},a.\u0275mod=t.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=t.\u0275\u0275defineInjector({imports:[T.CommonModule,E.vB,E.Zl,E.Qg]}),a})();e(46165);var b=e(79798);let D=(()=>{class a{}return a.\u0275fac=function(_){return new(_||a)},a.\u0275mod=t.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=t.\u0275\u0275defineInjector({imports:[T.CommonModule,I,b.Aj,E.P8]}),a})();var M=e(8834),p=e(87956),r=e(40914),m=e(2460),P=e(45542);const{MIN_TRUSTFUND:y}=r.r;let f=(()=>{class a{constructor(_){this.timezoneService=_,this.balance=(0,M.b)({value:y}),this.hidden=!0}ngOnInit(){this.timezoneService.request().then(({datetime:_})=>this.checkTimeTrustfund(_)).catch(()=>this.checkTimeTrustfund(new Date))}ngBoccPortal(_){this.portal=_}onCancel(){this.portal?.close()}onSubmit(){this.portal?.send({action:"next"}),this.portal?.close()}checkTimeTrustfund(_){const n=_.getHours();this.hidden=n<7||n>=19}}return a.\u0275fac=function(_){return new(_||a)(t.\u0275\u0275directiveInject(p.By))},a.\u0275cmp=t.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfer-trustfund-bluescreen"]],decls:19,vars:2,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfer-trustfund-bluescreen_cancel","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfer-trustfund-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"hidden","click"]],template:function(_,n){1&_&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-icon",2),t.\u0275\u0275elementStart(3,"label"),t.\u0275\u0275text(4,"\xbfC\xf3mo funciona?"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),t.\u0275\u0275text(7," Las transferencias desde y hacia la fiduciaria de Occidente estan habilitadas de 7:00 am a 7:00 pm "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(8,"li",4),t.\u0275\u0275text(9," No se permiten transferencias entre una Occirenta a otra. "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(10,"li",4),t.\u0275\u0275text(11),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(12,"div",5)(13,"button",6),t.\u0275\u0275listener("click",function(){return n.onCancel()}),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Cancelar"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(16,"button",7),t.\u0275\u0275listener("click",function(){return n.onSubmit()}),t.\u0275\u0275elementStart(17,"span"),t.\u0275\u0275text(18,"Continuar"),t.\u0275\u0275elementEnd()()()),2&_&&(t.\u0275\u0275advance(11),t.\u0275\u0275textInterpolate1(" Debes conservar por lo menos $",n.balance," pesos en la Occirenta. "),t.\u0275\u0275advance(5),t.\u0275\u0275property("hidden",n.hidden))},dependencies:[m.Z,P.P],styles:["mbo-transfer-trustfund-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),a})(),c=(()=>{class a{}return a.\u0275fac=function(_){return new(_||a)},a.\u0275mod=t.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=t.\u0275\u0275defineInjector({imports:[T.CommonModule,E.Zl,E.P8]}),a})()},26060:(S,x,e)=>{e.d(x,{R:()=>M});var g=e(39904),T=e(95437),t=(e(88039),e(99877)),h=e(17007),C=e(90521);function b(p,r){if(1&p){const m=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),t.\u0275\u0275listener("event",function(y){t.\u0275\u0275restoreView(m);const v=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(v.onEventComponent(y))}),t.\u0275\u0275elementEnd()()}if(2&p){const m=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==m.history?null:m.history.dateFormat)("statusLabel",null==m.history?null:m.history.status.label)("statusColor",null==m.history?null:m.history.status.color)("subtitle",null==m.history?null:m.history.destination.description)("number",null==m.history?null:m.history.destination.number)("description",null==m.history?null:m.history.destination.bank.name)("amount",null==m.history?null:m.history.amount)}}function D(p,r){1&p&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"bocc-card-information",5),t.\u0275\u0275elementEnd()),2&p&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}let M=(()=>{class p{constructor(m){this.mboProvider=m,this.skeleton=!1}onEventComponent(m){"component"===m&&this.history&&this.mboProvider.navigation.next(g.Z6.TRANSFERS.HISTORY_INFORMATION,{uuid:this.history.uuid,redirect:"history"})}}return p.\u0275fac=function(m){return new(m||p)(t.\u0275\u0275directiveInject(T.ZL))},p.\u0275cmp=t.\u0275\u0275defineComponent({type:p,selectors:[["mbo-transfer-history-card"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfer-history-card",4,"ngIf"],["class","mbo-transfer-history-card__skeleton",4,"ngIf"],[1,"mbo-transfer-history-card"],[3,"title","statusLabel","statusColor","subtitle","number","description","amount","event"],[1,"mbo-transfer-history-card__skeleton"],[3,"skeleton"]],template:function(m,P){1&m&&(t.\u0275\u0275template(0,b,2,7,"div",0),t.\u0275\u0275template(1,D,2,1,"div",1)),2&m&&(t.\u0275\u0275property("ngIf",!P.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",P.skeleton))},dependencies:[h.NgIf,C.v],styles:["mbo-transfer-history-card{position:relative;width:100%;display:block}mbo-transfer-history-card .mbo-transfer-history-card{border-bottom:var(--border-1-lighter-300)}mbo-transfer-history-card .mbo-transfer-history-card .bocc-card-information__body{padding:var(--sizing-x6) var(--sizing-x4)}\n"],encapsulation:2}),p})()},56921:(S,x,e)=>{e.d(x,{d:()=>P});var g=e(39904),T=e(95437),t=(e(88039),e(99877)),h=e(17007),C=e(90521),b=e(2460),D=e(55944);function M(y,v){1&y&&t.\u0275\u0275element(0,"bocc-icon",14)}function p(y,v){1&y&&(t.\u0275\u0275elementStart(0,"div",15),t.\u0275\u0275element(1,"bocc-icon",16),t.\u0275\u0275elementStart(2,"label",17),t.\u0275\u0275text(3,"Repetir"),t.\u0275\u0275elementEnd()())}function r(y,v){if(1&y){const o=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"div",3),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(o);const c=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(c.onComponent())}),t.\u0275\u0275elementStart(2,"div",4)(3,"label",5),t.\u0275\u0275text(4),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(5,"label",6),t.\u0275\u0275listener("click",function(c){t.\u0275\u0275restoreView(o);const a=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(a.onStatus(c))}),t.\u0275\u0275template(6,M,1,0,"bocc-icon",7),t.\u0275\u0275elementStart(7,"span"),t.\u0275\u0275text(8),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(9,"label",8),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"label",9),t.\u0275\u0275text(12),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(13,"label",10),t.\u0275\u0275text(14),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(15,"div",11),t.\u0275\u0275element(16,"bocc-amount",12),t.\u0275\u0275elementEnd()(),t.\u0275\u0275template(17,p,4,0,"div",13),t.\u0275\u0275elementEnd()}if(2&y){const o=t.\u0275\u0275nextContext();t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate1(" ",null==o.history?null:o.history.dateFormat," "),t.\u0275\u0275advance(1),t.\u0275\u0275classProp("active",o.active)("pending",o.isPending),t.\u0275\u0275attribute("bocc-theme",null==o.history||null==o.history.status?null:o.history.status.color),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",o.isPending),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(null==o.history?null:o.history.status.label),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==o.history||null==o.history.destination?null:o.history.destination.description," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==o.history||null==o.history.destination?null:o.history.destination.number," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",null==o.history||null==o.history.destination||null==o.history.destination.bank?null:o.history.destination.bank.name," "),t.\u0275\u0275advance(2),t.\u0275\u0275property("amount",null==o.history?null:o.history.amount),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!1)}}function m(y,v){1&y&&(t.\u0275\u0275elementStart(0,"div",18),t.\u0275\u0275element(1,"bocc-card-information",19),t.\u0275\u0275elementEnd()),2&y&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0)("expanded",!0))}let P=(()=>{class y{constructor(o){this.mboProvider=o,this.skeleton=!1,this.active=!1}get isPending(){return"Enviada"===this.history?.status?.label}onComponent(){this.history&&this.mboProvider.navigation.next(g.Z6.TRANSFERS.HISTORY_INFORMATION,{uuid:this.history.uuid})}onStatus(o){o.stopPropagation(),this.active=!this.active,this.active&&setTimeout(()=>this.active=!1,7500)}}return y.\u0275fac=function(o){return new(o||y)(t.\u0275\u0275directiveInject(T.ZL))},y.\u0275cmp=t.\u0275\u0275defineComponent({type:y,selectors:[["mbo-transfer-history-element"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfer-history-element__content",4,"ngIf"],["class","mbo-transfer-history-element__skeleton",4,"ngIf"],[1,"mbo-transfer-history-element__content"],[1,"mbo-transfer-history-element__component",3,"click"],[1,"mbo-transfer-history-element__title"],[1,"mbo-transfer-history-element__date","smalltext-medium"],[1,"mbo-transfer-history-element__status",3,"click"],["icon","chat-info",4,"ngIf"],[1,"mbo-transfer-history-element__subtitle","body2-medium","truncate"],[1,"mbo-transfer-history-element__number","smalltext-medium","truncate"],[1,"mbo-transfer-history-element__description","smalltext-medium","truncate"],[1,"mbo-transfer-history-element__amount"],[3,"amount"],["class","mbo-transfer-history-element__action",4,"ngIf"],["icon","chat-info"],[1,"mbo-transfer-history-element__action"],["icon","refresh"],[1,"smalltext-medium"],[1,"mbo-transfer-history-element__skeleton"],[3,"skeleton","expanded"]],template:function(o,f){1&o&&(t.\u0275\u0275template(0,r,18,13,"div",0),t.\u0275\u0275template(1,m,2,2,"div",1)),2&o&&(t.\u0275\u0275property("ngIf",!f.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",f.skeleton))},dependencies:[h.NgIf,C.v,b.Z,D.Q],styles:['@charset "UTF-8";mbo-transfer-history-element{position:relative;width:100%;display:block}mbo-transfer-history-element .mbo-transfer-history-element__content{position:relative;width:100%;display:flex}mbo-transfer-history-element .mbo-transfer-history-element__content bocc-card-information{border:var(--border-1-lighter-300);width:100%;overflow:hidden;border-radius:var(--sizing-x6)}mbo-transfer-history-element .mbo-transfer-history-element__component{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4);box-sizing:border-box;border:var(--border-1-lighter-300);overflow:hidden;border-radius:var(--sizing-x4)}mbo-transfer-history-element .mbo-transfer-history-element__title{display:flex;justify-content:space-between;align-items:center}mbo-transfer-history-element .mbo-transfer-history-element__date{color:var(--color-carbon-lighter-700)}mbo-transfer-history-element .mbo-transfer-history-element__status{--bocc-icon-dimension: 5rem;--pvt-information-display: none;position:relative;display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center;background:var(--color-bocc-200);color:var(--color-bocc-900);padding:0rem var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);font-size:var(--caption-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}mbo-transfer-history-element .mbo-transfer-history-element__status.pending.active:hover{--pvt-information-display: block}mbo-transfer-history-element .mbo-transfer-history-element__status bocc-icon{padding:1rem;background:var(--color-blue-700);color:var(--color-carbon-lighter-50);border-radius:50%}mbo-transfer-history-element .mbo-transfer-history-element__status:before{content:"Las transacciones a entidades fuera del grupo Aval quedar\\e1n sujetas a la confirmaci\\f3n de la entidad receptora.";display:var(--pvt-information-display);position:absolute;width:120rem;right:0;top:calc(100% + var(--sizing-x3));padding:var(--sizing-x2) var(--sizing-x4);color:var(--color-carbon-lighter-300);background:var(--color-navy-900);border-radius:var(--sizing-x4)}mbo-transfer-history-element .mbo-transfer-history-element__number{color:var(--color-amathyst-700)}mbo-transfer-history-element .mbo-transfer-history-element__description{color:var(--color-carbon-lighter-700)}mbo-transfer-history-element .mbo-transfer-history-element__amount{display:flex;justify-content:flex-end}mbo-transfer-history-element .mbo-transfer-history-element__amount bocc-amount{white-space:nowrap;font-size:var(--subtitle1-size);font-weight:var(--font-weight-medium)}mbo-transfer-history-element .mbo-transfer-history-element__action{display:flex;flex-direction:column;padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;margin:auto 0rem;border:var(--border-1-lighter-300);border-left:none;border-radius:0rem var(--sizing-x4) var(--sizing-x4) 0rem}mbo-transfer-history-element .mbo-transfer-history-element__action .bocc-icon{margin:0rem auto var(--sizing-x4) auto;color:var(--color-blue-700)}mbo-transfer-history-element .mbo-transfer-history-element__action label{margin:0rem auto;color:var(--color-blue-700)}mbo-transfer-history-element .mbo-transfer-history-element__skeleton{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}\n'],encapsulation:2}),y})()},46165:(S,x,e)=>{e.d(x,{E:()=>v});var g=e(39904),T=e(95437),t=(e(88039),e(99877)),h=e(17007),C=e(56921),b=e(50689),D=e(45542);function M(o,f){if(1&o){const c=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",8),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(c);const s=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(s.onRedirectAll())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2,"Ver todas"),t.\u0275\u0275elementEnd()()}}function p(o,f){if(1&o&&(t.\u0275\u0275elementStart(0,"div",5)(1,"label",6),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(3,M,3,0,"button",7),t.\u0275\u0275elementEnd()),2&o){const c=t.\u0275\u0275nextContext();t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",(null==c.history||null==c.history.range?null:c.history.range.label)||"SIN RESULTADOS"," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",null==c.history?null:c.history.range)}}function r(o,f){1&o&&t.\u0275\u0275element(0,"mbo-transfer-history-element",11),2&o&&t.\u0275\u0275property("history",f.$implicit)}function m(o,f){if(1&o&&(t.\u0275\u0275elementStart(0,"div",9),t.\u0275\u0275template(1,r,1,1,"mbo-transfer-history-element",10),t.\u0275\u0275elementEnd()),2&o){const c=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",c.history.firstPage)}}function P(o,f){1&o&&(t.\u0275\u0275elementStart(0,"div",12),t.\u0275\u0275element(1,"mbo-transfer-history-element",13)(2,"mbo-transfer-history-element",13),t.\u0275\u0275elementEnd()),2&o&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}function y(o,f){if(1&o&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",14),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&o){const c=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",c.msgError," ")}}let v=(()=>{class o{constructor(c){this.mboProvider=c}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transferencias realizadas.":"Lo sentimos, por el momento no cuentas con transferencias realizadas."}onRedirectAll(){this.mboProvider.navigation.next(g.Z6.TRANSFERS.HISTORY)}}return o.\u0275fac=function(c){return new(c||o)(t.\u0275\u0275directiveInject(T.ZL))},o.\u0275cmp=t.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfer-history-list"]],inputs:{history:"history"},decls:5,vars:4,consts:[[1,"mbo-transfer-history-list__content"],["class","mbo-transfer-history-list__header bocc-subheader",4,"ngIf"],["class","mbo-transfer-history-list__component",4,"ngIf"],["class","mbo-transfer-history-list__skeleton",4,"ngIf"],["class","mbo-transfer-history-list__empty",4,"ngIf"],[1,"mbo-transfer-history-list__header","bocc-subheader"],[1,"caption-medium"],["id","btn_transfer-history-list_go-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfer-history-list_go-all","bocc-button","flat",3,"click"],[1,"mbo-transfer-history-list__component"],[3,"history",4,"ngFor","ngForOf"],[3,"history"],[1,"mbo-transfer-history-list__skeleton"],[3,"skeleton"],[1,"mbo-transfer-history-list__empty"]],template:function(c,a){1&c&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,p,4,2,"div",1),t.\u0275\u0275template(2,m,2,1,"div",2),t.\u0275\u0275template(3,P,3,2,"div",3),t.\u0275\u0275template(4,y,2,1,"mbo-message-empty",4),t.\u0275\u0275elementEnd()),2&c&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",a.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",a.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!a.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",null==a.history?null:a.history.isEmpty))},dependencies:[h.NgForOf,h.NgIf,C.d,b.A,D.P],styles:["mbo-transfer-history-list{position:relative;width:100%;display:block}mbo-transfer-history-list .mbo-transfer-history-list__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-transfer-history-list .mbo-transfer-history-list__header{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-transfer-history-list .mbo-transfer-history-list__header>label{text-transform:uppercase}mbo-transfer-history-list .mbo-transfer-history-list__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-transfer-history-list .mbo-transfer-history-list__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}\n"],encapsulation:2}),o})()},98755:(S,x,e)=>{e.r(x),e.d(x,{MboTransfersHistoryPageModule:()=>_});var g=e(17007),T=e(78007),E=e(30263),t=e(79798),A=e(83991),h=e(15861),I=e(22816),C=e(39904),b=e(88844),D=e(95437),M=e(57544),p=e(93637),r=e(99877),m=e(48774),P=e(48030),y=e(50689),v=e(26060);function o(n,l){1&n&&r.\u0275\u0275element(0,"mbo-transfer-history-card",13),2&n&&r.\u0275\u0275property("history",l.$implicit)}function f(n,l){if(1&n&&(r.\u0275\u0275elementStart(0,"mbo-message-empty",14),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&n){const i=r.\u0275\u0275nextContext(2);r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",i.msgError," ")}}function c(n,l){if(1&n&&(r.\u0275\u0275elementStart(0,"div",10),r.\u0275\u0275template(1,o,1,1,"mbo-transfer-history-card",11),r.\u0275\u0275template(2,f,2,1,"mbo-message-empty",12),r.\u0275\u0275elementEnd()),2&n){const i=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngForOf",i.collection),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",i.isEmpty)}}function a(n,l){1&n&&(r.\u0275\u0275elementStart(0,"div",15),r.\u0275\u0275element(1,"mbo-transfer-history-card",16)(2,"mbo-transfer-history-card",16),r.\u0275\u0275elementEnd()),2&n&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("skeleton",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("skeleton",!0))}let s=(()=>{class n{constructor(i,u){this.mboProvider=i,this.requestConfiguration=u,this.requesting=!1,this.collection=[],this.ranges=b.YI,this.backAction={id:"btn_transfers-history_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(C.Z6.TRANSFERS.HOME)}},this.scroller=new I.S,this.rangeControl=new M.FormControl}ngOnInit(){this.initializatedConfiguration(),this.unsubscription=this.rangeControl.subscribe(i=>{this.history&&i&&!i.equals(this.history.range)&&this.refresh(i)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get isEmpty(){return this.history&&(0===this.history.collection.length||this.history.isError)}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transferencias realizadas.":"Lo sentimos, por el momento no cuentas con transferencias realizadas."}get isSkeleton(){return this.requesting||!this.history}onScroll(i){this.scroller.reset(i.target),this.scroller.verticalPercentage>90&&!this.history.finished&&!this.requesting&&this.nextPage()}initializatedConfiguration(){var i=this;return(0,h.Z)(function*(){(yield i.requestConfiguration.firstPage()).when({success:u=>{i.collection=u.collection,i.history=u,i.rangeControl.setValue(u.range)}})})()}refresh(i){var u=this;return(0,h.Z)(function*(){u.history=void 0,u.requesting=!0,(yield u.requestConfiguration.refresh(i)).when({success:O=>{u.collection=O.collection,u.history=O}},()=>{u.requesting=!1})})()}nextPage(){var i=this;return(0,h.Z)(function*(){i.requesting=!0,(yield i.requestConfiguration.nextPage()).when({success:u=>{i.collection=u}},()=>{i.requesting=!1})})()}}return n.\u0275fac=function(i){return new(i||n)(r.\u0275\u0275directiveInject(D.ZL),r.\u0275\u0275directiveInject(p.U))},n.\u0275cmp=r.\u0275\u0275defineComponent({type:n,selectors:[["mbo-transfers-history-page"]],decls:11,vars:7,consts:[[1,"mbo-transfers-history-page__content",3,"scroll"],[1,"mbo-transfers-history-page__header"],["title","Historial",3,"leftAction"],[1,"mbo-transfers-history-page__body"],[1,"mbo-transfers-history-page__title"],[1,"subtitle2-medium"],[1,"mbo-transfers-history-page__subheader",3,"hidden"],["preffixIcon","filter-settings",3,"suggestions","formControl","disabled"],["class","mbo-transfers-history-page__list",4,"ngIf"],["class","mbo-transfers-history-page__skeleton",4,"ngIf"],[1,"mbo-transfers-history-page__list"],[3,"history",4,"ngFor","ngForOf"],["class","mbo-transfer-history-page__empty",4,"ngIf"],[3,"history"],[1,"mbo-transfer-history-page__empty"],[1,"mbo-transfers-history-page__skeleton"],[3,"skeleton"]],template:function(i,u){1&i&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275listener("scroll",function(N){return u.onScroll(N)}),r.\u0275\u0275elementStart(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"label",5),r.\u0275\u0275text(6,"Mis \xfaltimas transferencias"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(7,"div",6),r.\u0275\u0275element(8,"bocc-select-box",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275template(9,c,3,2,"div",8),r.\u0275\u0275template(10,a,3,2,"div",9),r.\u0275\u0275elementEnd()()),2&i&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",u.backAction),r.\u0275\u0275advance(5),r.\u0275\u0275property("hidden",u.isSkeleton),r.\u0275\u0275advance(1),r.\u0275\u0275property("suggestions",u.ranges)("formControl",u.rangeControl)("disabled",u.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",u.history),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",u.isSkeleton))},dependencies:[g.NgForOf,g.NgIf,m.J,P.t,y.A,v.R],styles:["/*!\n * MBO TransfersHistory Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 18/Oct/2022\n * Updated: 08/Jul/2024\n*/mbo-transfers-history-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between}mbo-transfers-history-page .mbo-transfers-history-page__content{position:relative;width:100%;overflow:auto}mbo-transfers-history-page .mbo-transfers-history-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x4);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-transfers-history-page .mbo-transfers-history-page__title{position:relative;width:100%;text-align:center}mbo-transfers-history-page .mbo-transfers-history-page__subheader{width:calc(100% - var(--sizing-x8));margin:0rem var(--sizing-x4)}mbo-transfers-history-page .mbo-transfers-history-page__subheader bocc-icon{color:var(--color-blue-700)}mbo-transfers-history-page .mbo-transfers-history-page__subheader bocc-select-button{--bocc-button-padding: 0rem var(--sizing-x1);width:100%}mbo-transfers-history-page .mbo-transfers-history-page__subheader bocc-select-button .bocc-button__content{justify-content:space-between}\n"],encapsulation:2}),n})(),_=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=r.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=r.\u0275\u0275defineInjector({imports:[g.CommonModule,T.RouterModule.forChild([{path:"",component:s}]),E.Jx,E.tv,t.Aj,A.By]}),n})()},40914:(S,x,e)=>{e.d(x,{R:()=>g,r:()=>T});const g={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},T={MIN_TRUSTFUND:2e5}}}]);