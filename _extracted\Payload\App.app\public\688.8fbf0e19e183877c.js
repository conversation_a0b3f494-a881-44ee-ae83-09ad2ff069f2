(self.webpackChunkapp=self.webpackChunkapp||[]).push([[688],{80688:(S,i,r)=>{r.r(i),r.d(i,{MboTransfiyaTransferSourcePageModule:()=>P});var f=r(17007),u=r(78007),l=r(79798),m=r(30263),g=r(15861),p=r(39904),v=r(95437),c=r(17698),y=r(73004),e=r(99877),h=r(48774),T=r(4663);const b=p.Z6.TRANSFERS.TRANSFIYA.TRANSFER;let M=(()=>{class o{constructor(n,t,d,s){this.mboProvider=n,this.requestConfiguration=t,this.managerTransfiya=d,this.cancelProvider=s,this.confirmation=!1,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_transfiya-transfer-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(n){this.managerTransfiya.setProduct(n).when({success:()=>{this.mboProvider.navigation.next(b.DESTINATION)}})}initializatedConfiguration(){var n=this;return(0,g.Z)(function*(){(yield n.requestConfiguration.source()).when({success:({products:t})=>{n.products=t}},()=>{n.requesting=!1})})()}}return o.\u0275fac=function(n){return new(n||o)(e.\u0275\u0275directiveInject(v.ZL),e.\u0275\u0275directiveInject(c.ow),e.\u0275\u0275directiveInject(c.Pm),e.\u0275\u0275directiveInject(y.c))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-transfer-source-page"]],decls:6,vars:3,consts:[[1,"mbo-transfiya-transfer-source-page__content"],[1,"mbo-transfiya-transfer-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-transfiya-transfer-source-page__body"],[3,"skeleton","products","select"]],template:function(n,t){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),e.\u0275\u0275listener("select",function(s){return t.onProduct(s)}),e.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas transferir hoy? "),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("rightAction",t.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",t.requesting)("products",t.products))},dependencies:[h.J,T.c],styles:["/*!\n * MBO TransfiyaTransferSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 14/Jun/2022\n * Updated: 09/Feb/2024\n*/mbo-transfiya-transfer-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-transfer-source-page .mbo-transfiya-transfer-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-transfer-source-page .mbo-transfiya-transfer-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),o})(),P=(()=>{class o{}return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[f.CommonModule,u.RouterModule.forChild([{path:"",component:M}]),m.Jx,l.cV]}),o})()}}]);