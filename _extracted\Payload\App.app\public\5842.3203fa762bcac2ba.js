(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5842],{95842:(D,c,e)=>{e.r(c),e.d(c,{MboTransferTrustfundDestinationPageModule:()=>A});var l=e(17007),m=e(78007),u=e(79798),p=e(30263),g=e(15861),v=e(39904),h=e(95437),f=e(95137),T=e(12884),t=e(99877),b=e(48774),M=e(13043);const a=v.Z6.TRANSFERS.TRUSTFUND;let P=(()=>{class o{constructor(n,i,s,d){this.mboProvider=n,this.requestConfiguration=i,this.managerTransfer=s,this.cancelProvider=d,this.confirmation=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_transfer-trustfund-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(a.SOURCE)}},this.cancelAction={id:"btn_transfer-trustfund-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(n){this.managerTransfer.setDestination(n).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?a.CONFIRMATION:a.AMOUNT)}})}initializatedConfiguration(){var n=this;return(0,g.Z)(function*(){(yield n.requestConfiguration.destination()).when({success:({confirmation:i,products:s})=>{n.products=s,n.confirmation=i}},()=>{n.requesting=!1})})()}}return o.\u0275fac=function(n){return new(n||o)(t.\u0275\u0275directiveInject(h.ZL),t.\u0275\u0275directiveInject(f.X),t.\u0275\u0275directiveInject(f.M),t.\u0275\u0275directiveInject(T.S))},o.\u0275cmp=t.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfer-trustfund-destination-page"]],decls:6,vars:5,consts:[[1,"mbo-transfer-trustfund-destination-page__content"],[1,"mbo-transfer-trustfund-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfer-trustfund-destination-page__body"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","automaticSelection","select"]],template:function(n,i){1&n&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-destination-selector",4),t.\u0275\u0275listener("select",function(d){return i.onProduct(d)}),t.\u0275\u0275text(5," \xbfA quien deseas transferirle hoy? "),t.\u0275\u0275elementEnd()()()),2&n&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",i.backAction)("rightAction",i.cancelAction),t.\u0275\u0275advance(2),t.\u0275\u0275property("skeleton",i.requesting)("products",i.products)("automaticSelection",!0))},dependencies:[b.J,M.e],styles:["/*!\n * MBO TransferTrustfundDestination Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 11/Jul/2022\n * Updated: 08/Ene/2024\n*/mbo-transfer-trustfund-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-trustfund-destination-page .mbo-transfer-trustfund-destination-page__content{position:relative;width:100%;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfer-trustfund-destination-page .mbo-transfer-trustfund-destination-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}\n"],encapsulation:2}),o})(),A=(()=>{class o{}return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[l.CommonModule,m.RouterModule.forChild([{path:"",component:P}]),u.cV,p.Jx,u.eM]}),o})()}}]);