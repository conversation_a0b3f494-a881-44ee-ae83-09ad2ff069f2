(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6767],{56767:(D,E,a)=>{a.r(E),a.d(E,{MboTransferCelToCelHomePageModule:()=>F});var u=a(17007),d=a(78007),e=a(30263),t=a(65715),P=a(15861),p=a(39904),R=a(95437),C=a(70658),v=a(42789),S=a(17698),M=a(17758),o=a(99877),_=a(48774),i=a(23436),f=a(77691);function A(h,I){if(1&h){const y=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"bocc-card-category",15),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(y);const g=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(g.onFavoriteAccount())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Cuenta favorita"),o.\u0275\u0275elementEnd()()}}const{APPROVED:l,ASK:T,CONTACTS:m,FAVORITE_ACCOUNT:k,PENDING:O,TRANSFER:z}=p.Z6.TRANSFERS.TRANSFIYA;let N=(()=>{class h{constructor(y,b,g){this.mboProvider=y,this.requestConfiguration=b,this.managerTransfiya=g,this.canTransfiya=C.N.navigatorEnabled,this.backAction={id:"btn_transfer-cel-to-cel-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(p.Z6.TRANSFERS.HOME)}}}ngOnInit(){this.initializatedConfiguration()}onTransfer(){this.mboProvider.navigation.next(p.Z6.TRANSFERS.CELTOCEL.SEND.SOURCE)}onTransfiya(){this.managerTransfiya.transfer().when({success:()=>{this.mboProvider.navigation.next(z.SOURCE)}})}onFavoriteAccount(){this.mboProvider.navigation.next(k)}onApproved(){this.managerTransfiya.approved().when({success:()=>{this.mboProvider.navigation.next(l.HOME)}})}onRequest(){this.managerTransfiya.request().when({success:()=>{this.mboProvider.navigation.next(T.DESTINATION)}})}onPending(){this.managerTransfiya.pending().when({success:()=>{this.mboProvider.navigation.next(O.HOME)}})}onContacts(){this.managerTransfiya.contacts().when({success:()=>{this.mboProvider.navigation.next(m)}})}initializatedConfiguration(){var y=this;return(0,P.Z)(function*(){(yield y.requestConfiguration.home()).when({success:({history$:b})=>{b.then(g=>{y.history=g}).catch(()=>{y.history=v.bq.empty()})},failure:()=>{y.history=v.bq.empty()}})})()}}return h.\u0275fac=function(y){return new(y||h)(o.\u0275\u0275directiveInject(R.ZL),o.\u0275\u0275directiveInject(S.tE),o.\u0275\u0275directiveInject(M.Pm))},h.\u0275cmp=o.\u0275\u0275defineComponent({type:h,selectors:[["mbo-transfer-cel-to-cel-home-page"]],decls:28,vars:4,consts:[[1,"mbo-transfer-cel-to-cel-home-page__content"],[1,"mbo-transfer-cel-to-cel-home-page__header"],["title","A celulares",3,"leftAction"],[1,"mbo-transfer-cel-to-cel-home-page__body"],["id","btn_transfer-cel-to-cel-home_transfer","icon","mobile-transfer",3,"click"],[1,"mbo-transfer-cel-to-cel-home-page__footer"],[1,"mbo-transfer-cel-to-cel-home-page__transfiya"],[1,"mbo-transfer-cel-to-cel-home-page__transfiya__title","overline-medium"],["id","btn_transfer-cel-to-cel-home_transfer","icon","mobile-transfer",3,"hidden","click"],["id","btn_transfer-cel-to-cel-home_favorite-account","icon","document-file-favorite","versionNews","5.8.0",3,"click",4,"ngIf"],["id","btn_transfer-cel-to-cel-home_approved","icon","money-hand",3,"click"],["id","btn_transfer-cel-to-cel-home_request","icon","money-request",3,"click"],["id","btn_transfer-cel-to-cel-home_pending","icon","exchange-data",3,"click"],["id","btn_transfer-cel-to-cel-home_phones","icon","user-profile",3,"click"],[3,"history"],["id","btn_transfer-cel-to-cel-home_favorite-account","icon","document-file-favorite","versionNews","5.8.0",3,"click"]],template:function(y,b){1&y&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"div",3)(4,"bocc-card-category",4),o.\u0275\u0275listener("click",function(){return b.onTransfer()}),o.\u0275\u0275elementStart(5,"span"),o.\u0275\u0275text(6,"Transferir dinero"),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(7,"div",5)(8,"div",6)(9,"div",7),o.\u0275\u0275text(10," Opciones transfiya "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(11,"bocc-card-category",8),o.\u0275\u0275listener("click",function(){return b.onTransfiya()}),o.\u0275\u0275elementStart(12,"span"),o.\u0275\u0275text(13,"Transferir dinero"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(14,A,3,0,"bocc-card-category",9),o.\u0275\u0275elementStart(15,"bocc-card-category",10),o.\u0275\u0275listener("click",function(){return b.onApproved()}),o.\u0275\u0275elementStart(16,"span"),o.\u0275\u0275text(17,"Aceptar dinero"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(18,"bocc-card-category",11),o.\u0275\u0275listener("click",function(){return b.onRequest()}),o.\u0275\u0275elementStart(19,"span"),o.\u0275\u0275text(20,"Solicitar dinero"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(21,"bocc-card-category",12),o.\u0275\u0275listener("click",function(){return b.onPending()}),o.\u0275\u0275elementStart(22,"span"),o.\u0275\u0275text(23,"Solicitudes recibidas"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(24,"bocc-card-category",13),o.\u0275\u0275listener("click",function(){return b.onContacts()}),o.\u0275\u0275elementStart(25,"span"),o.\u0275\u0275text(26,"Celulares de confianza"),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275element(27,"mbo-transfiya-history-list",14),o.\u0275\u0275elementEnd()),2&y&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("leftAction",b.backAction),o.\u0275\u0275advance(9),o.\u0275\u0275property("hidden",!b.canTransfiya),o.\u0275\u0275advance(3),o.\u0275\u0275property("ngIf",!1),o.\u0275\u0275advance(13),o.\u0275\u0275property("history",b.history))},dependencies:[u.NgIf,_.J,i.D,f.L],styles:["/*!\n * MBO TransferCelToCelHome Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 14/Jun/2022\n * Updated: 05/Feb/2024\n*/mbo-transfer-cel-to-cel-home-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x12)}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__body,mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__transfiya{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);box-sizing:border-box}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__body{padding:0rem var(--sizing-form)}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__transfiya__title{text-transform:uppercase;padding:0rem var(--sizing-x8);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__transfiya .bocc-card-category__content{box-shadow:none}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-safe-form-x8);box-sizing:border-box;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-transfer-cel-to-cel-home-page .mbo-transfer-cel-to-cel-home-page__footer mbo-transfiya-history-list .mbo-transfiya-history-list__content{padding:0rem}\n"],encapsulation:2}),h})(),F=(()=>{class h{}return h.\u0275fac=function(y){return new(y||h)},h.\u0275mod=o.\u0275\u0275defineNgModule({type:h}),h.\u0275inj=o.\u0275\u0275defineInjector({imports:[u.CommonModule,d.RouterModule.forChild([{path:"",component:N}]),e.Jx,e.D0,t.vl]}),h})()},65715:(D,E,a)=>{a.d(E,{F8:()=>m,tM:()=>P,KW:()=>U,$g:()=>R,QG:()=>_,fc:()=>z,vl:()=>y,x3:()=>j});var u=a(17007),d=a(30263),e=a(99877);function t(r,s){if(1&r&&e.\u0275\u0275element(0,"bocc-tag-aval",14),2&r){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("value",null==n.account?null:n.account.tagAval)}}let P=(()=>{class r{ngBoccPortal(n){this.portal=n}onApproved(){this.portal?.destroy()}onDecline(){this.portal?.destroy()}}return r.\u0275fac=function(n){return new(n||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-account-checked-modal"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:22,vars:3,consts:[["src","assets/shared/logos/modals/transfiya-account-checked.svg"],[1,"smalltext-semibold"],[1,"mbo-transfiya-account-checked-modal__body"],[1,"body2-medium"],["color","none"],[1,"mbo-transfiya-account-checked-modal__product"],[1,"mbo-transfiya-account-checked-modal__title"],[1,"smalltext-medium"],[3,"value",4,"ngIf"],[1,"mbo-transfiya-account-checked-modal__number"],[1,"body1-medium"],[1,"mbo-transfiya-account-checked-modal__footer"],["id","btn_transfiya-account-checked_approved","bocc-button","raised","prefixIcon","refresh",3,"click"],["id","btn_transfiya-account-checked_decline","bocc-button","flat",3,"click"],[3,"value"]],template:function(n,c){1&n&&(e.\u0275\u0275element(0,"bocc-logo-modal",0),e.\u0275\u0275elementStart(1,"label",1),e.\u0275\u0275text(2," CAMBIO DE CUENTA FAVORITA "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",2)(4,"p",3),e.\u0275\u0275text(5," Recibir\xe1s todas las transferencias de Transfiya en esta cuenta: "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-card-product-background",4)(7,"div",5)(8,"div",6)(9,"span",7),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(11,t,1,1,"bocc-tag-aval",8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",9)(13,"span",10),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(15,"div",11)(16,"button",12),e.\u0275\u0275listener("click",function(){return c.onApproved()}),e.\u0275\u0275elementStart(17,"span"),e.\u0275\u0275text(18,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"button",13),e.\u0275\u0275listener("click",function(){return c.onDecline()}),e.\u0275\u0275elementStart(20,"span"),e.\u0275\u0275text(21,"Cancelar"),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate(null==c.account?null:c.account.nickname),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==c.account?null:c.account.tagAval),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==c.account?null:c.account.publicNumber))},dependencies:[u.CommonModule,u.NgIf,d.bG,d.P8,d.qd,d.X6],styles:["mbo-transfiya-account-checked-modal{--bocc-logo-modal-translate-x:calc(-50% - 8rem);position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:36rem 0rem var(--sizing-x8) 0rem;box-sizing:border-box}mbo-transfiya-account-checked-modal bocc-logo-modal{position:absolute;top:0rem;left:50%}mbo-transfiya-account-checked-modal bocc-card-product-background{margin:var(--sizing-x4) 0rem}mbo-transfiya-account-checked-modal bocc-card-product-background .bocc-card-product-background{border-radius:var(--sizing-x4)}mbo-transfiya-account-checked-modal>label{text-align:center}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__body{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__body p{color:var(--color-carbon-lighter-700);text-align:center}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__product{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x4) var(--sizing-x6);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__title{display:flex;align-items:center;justify-content:space-between}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__title>span{color:var(--bocc-card-product-color-title)}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__number{color:var(--bocc-card-product-color-code)}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__footer{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__footer button{width:100%}\n"],encapsulation:2}),r})();function p(r,s){if(1&r&&(e.\u0275\u0275elementStart(0,"span",10),e.\u0275\u0275element(1,"bocc-tag-aval",11),e.\u0275\u0275elementEnd()),2&r){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("value",null==n.product?null:n.product.tagAval)}}let R=(()=>{class r{constructor(){this.checked=!1}get icon(){return this.checked?"check":"favorite"}}return r.\u0275fac=function(n){return new(n||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-account-selector"]],inputs:{product:"product",checked:"checked"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:12,vars:8,consts:[[1,"mbo-transfiya-account-selector__content",3,"color"],[1,"mbo-transfiya-account-selector__avatar"],["alt","mbo-transfiya-account-selector-img",1,"mbo-transfiya-account-selector__avatar__icon",3,"src"],[1,"mbo-transfiya-account-selector__component"],[1,"mbo-transfiya-account-selector__title","body2-medium"],[1,"mbo-transfiya-account-selector__number","body1-medium"],[1,"mbo-transfiya-account-selector__number__value"],["class","mbo-transfiya-account-selector__aval-key",4,"ngIf"],[1,"mbo-transfiya-account-selector__check"],[3,"icon"],[1,"mbo-transfiya-account-selector__aval-key"],[3,"value"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"label",5)(7,"span",6),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,p,2,1,"span",7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-icon",9),e.\u0275\u0275elementEnd()()),2&n&&(e.\u0275\u0275classProp("mbo-transfiya-account-selector__content--checked",c.checked),e.\u0275\u0275property("color",null==c.product?null:c.product.color),e.\u0275\u0275advance(2),e.\u0275\u0275property("src",null==c.product?null:c.product.logo,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==c.product?null:c.product.nickname," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==c.product?null:c.product.publicNumber," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==c.product?null:c.product.tagAval),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",c.icon))},dependencies:[u.CommonModule,u.NgIf,d.X6,d.qd,d.Zl],styles:['/*!\n * MBO TransfiyaAccountSelector Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 09/Sep/2024\n * Updated: 09/Sep/2024\n*/mbo-transfiya-account-selector{--bocc-card-product-color-title: var(--color-carbon-darker-1000);--bocc-card-background-none: var(--color-carbon-lighter-50);--pvt-check-background-color: transparent;--pvt-check-font-color: var(--color-semantic-alert-700);position:relative;width:100%;display:block;border-radius:var(--sizing-x4)}mbo-transfiya-account-selector .bocc-card-product-background{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x4)}mbo-transfiya-account-selector .bocc-card-product-background.none{--bocc-card-product-color-code: var(--color-amathyst-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__content--checked{--pvt-check-background-color: var(--color-semantic-success-700);--pvt-check-font-color: var(--color-carbon-lighter-50)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__avatar{z-index:var(--z-index-4)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__avatar__icon{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__component{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);z-index:var(--z-index-4);overflow:hidden}mbo-transfiya-account-selector .mbo-transfiya-account-selector__title{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--bocc-card-product-color-title)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__number{display:inline-flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);white-space:nowrap}mbo-transfiya-account-selector .mbo-transfiya-account-selector__number__value{color:var(--bocc-card-product-color-code)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key{--bocc-icon-dimension: var(--sizing-x10);display:flex;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key:before{display:block;content:"";width:1px;height:var(--sizing-x10);background-color:var(--color-carbon-lighter-400);margin-right:var(--sizing-x1)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key bocc-icon{color:var(--color-navy-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key span{color:var(--color-navy-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__check{background:var(--pvt-check-background-color);border-radius:50%}mbo-transfiya-account-selector .mbo-transfiya-account-selector__check bocc-icon{color:var(--pvt-check-font-color)}\n'],encapsulation:2}),r})();var C=a(15861),v=a(17698),S=a(2460),M=a(45542);function o(r,s){if(1&r){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onIgnore())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"No mostrar m\xe1s"),e.\u0275\u0275elementEnd()()}}let _=(()=>{class r{constructor(n){this.managerTransfiya=n}ngBoccPortal(n){this.portal=n}onSubmit(){this.close()}onIgnore(){var n=this;return(0,C.Z)(function*(){(yield n.managerTransfiya.ignoreAsk()).when({},()=>n.close())})()}close(){this.portal?.close(),setTimeout(()=>this.portal?.destroy(),240)}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(v.Pm))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-ask-bluescreen"]],decls:17,vars:1,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfiya-ask-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfiya-ask-bluescreen_hidden","class","bocc-bluescreen__button","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfiya-ask-bluescreen_hidden","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Tu contacto recibir\xe1 una notificaci\xf3n con la solicitud a trav\xe9s de mensaje de texto. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9," Realiza hasta 15 solicitudes al d\xeda que sumen un valor m\xe1ximo de 2'000.000 "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11," Tu contacto podr\xe1 enviar el dinero a trav\xe9s de la entidad bancaria que elija, siempre y cuando \xe9sta tenga activo el servicio de TransfiYa. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(12,"div",5)(13,"button",6),e.\u0275\u0275listener("click",function(){return c.onSubmit()}),e.\u0275\u0275elementStart(14,"span"),e.\u0275\u0275text(15,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(16,o,3,0,"button",7),e.\u0275\u0275elementEnd()),2&n&&(e.\u0275\u0275advance(16),e.\u0275\u0275property("ngIf",!1))},dependencies:[u.NgIf,S.Z,M.P],styles:["mbo-transfiya-ask-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),r})();var f=a(24495),A=a(57544),l=a(48774),T=a(64181);let m=(()=>{class r{constructor(n){this.managerTransfiya=n,this.initialValue="",this.cancelAction={id:"btn_transfiya-description-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transfiya-description-sheet_save",label:"Guardar",disabled:()=>this.valueControl.invalid,click:()=>{this.resolveDescription(this.valueControl.value)}},this.valueControl=new A.FormControl("",[f.C1,f.zf,f.O_,f.Y2,(0,f.Mv)(24)])}ngOnInit(){this.valueControl.setValue(this.initialValue)}ngBoccPortal(n){this.portal=n}resolveDescription(n){this.managerTransfiya.setDescription(n).when({success:()=>{this.portal?.resolve(n),this.portal?.destroy()}})}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(v.Pm))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-description-sheet"]],decls:7,vars:3,consts:[[1,"mbo-transfiya-description-sheet__content"],[1,"mbo-transfiya-description-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transfiya-description-sheet__body"],[1,"mbo-transfiya-description-sheet__title","subtitle2-medium"],["elementId","txt_transfer-description-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4),e.\u0275\u0275text(5," A\xf1ade una descripci\xf3n a tu transferencia "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"bocc-input-box",5),e.\u0275\u0275elementEnd()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",c.cancelAction)("rightAction",c.saveAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("formControl",c.valueControl))},dependencies:[l.J,T.D],styles:["mbo-transfiya-description-sheet{position:relative;width:100%;display:block}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),r})();a(64561);let z=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[u.CommonModule,d.vB]}),r})();a(81502);let F=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[u.CommonModule,d.vB,d.Zl]}),r})();a(77691);var I=a(79798);let y=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[u.CommonModule,I.Aj,d.P8,F]}),r})();var b=a(8834),g=a(40914);function H(r,s){if(1&r){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onIgnore())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"No mostrar m\xe1s"),e.\u0275\u0275elementEnd()()}}const{MAX_TRANSFIYA:L}=g.R;let j=(()=>{class r{constructor(n){this.managerTransfiya=n,this.limit=(0,b.b)({value:L})}ngBoccPortal(n){this.portal=n}onSubmit(){this.portal?.close()}onIgnore(){var n=this;return(0,C.Z)(function*(){(yield n.managerTransfiya.ignoreTransfer()).when({success:()=>{n.portal?.close(),setTimeout(()=>n.portal?.destroy(),240)}})})()}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(v.Pm))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-transfer-bluescreen"]],decls:19,vars:2,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfiya-send-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfiya-send-bluescreen_hidden","class","bocc-bluescreen__button","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfiya-send-bluescreen_hidden","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Le notificaremos a tu contacto el env\xedo del dinero por mensaje de texto. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9," Si en 12 horas tu contacto no acepta el dinero, regresar\xe1 a tu cuenta. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"li",4),e.\u0275\u0275text(13," Tu contacto podr\xe1 recibir el dinero a trav\xe9s de la entidad bancaria que elija, siempre y cuando tenga activo el servicio de TransfiYa. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(14,"div",5)(15,"button",6),e.\u0275\u0275listener("click",function(){return c.onSubmit()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(18,H,3,0,"button",7),e.\u0275\u0275elementEnd()),2&n&&(e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1(" Realiza hasta 15 transferencias al d\xeda que sumen un valor m\xe1ximo de $",c.limit," "),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngIf",!1))},dependencies:[u.NgIf,S.Z,M.P],styles:["mbo-transfiya-transfer-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),r})();var B=a(39904),w=a(95437),Y=a(60817);let U=(()=>{class r{constructor(n,c,x){this.mboProvider=n,this.managerTransfiya=c,this.legalDocumentProvider=x,this.downloading=!1,this.termAndConditionControl=new A.FormControl(!1)}ngBoccPortal(n){this.portal=n}onSubmit(){var n=this;return(0,C.Z)(function*(){(yield n.managerTransfiya.approvedTyC()).when({success:c=>{switch(c){case"APPROVED":n.mboProvider.navigation.next(B.Z6.TRANSFERS.TRANSFIYA.APPROVED.HOME);break;case"REQUEST":n.mboProvider.navigation.next(B.Z6.TRANSFERS.TRANSFIYA.ASK.SOURCE);break;case"PENDING":n.mboProvider.navigation.next(B.Z6.TRANSFERS.TRANSFIYA.PENDING.HOME);break;case"CONTACTS":n.mboProvider.navigation.next(B.Z6.TRANSFERS.TRANSFIYA.CONTACTS);break;default:n.mboProvider.navigation.next(B.Z6.TRANSFERS.TRANSFIYA.TRANSFER.AMOUNT)}}},()=>{n.portal?.close(),n.portal?.destroy()})})()}onTermAndConditions(){this.downloading||(this.downloading=!0,this.legalDocumentProvider.termsAndConditions().finally(()=>{this.downloading=!1}))}}return r.\u0275fac=function(n){return new(n||r)(e.\u0275\u0275directiveInject(w.ZL),e.\u0275\u0275directiveInject(v.Pm),e.\u0275\u0275directiveInject(w.uD))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-tyc-modal"]],decls:14,vars:4,consts:[[1,"mbo-transfiya-tyc-modal__content"],[1,"mbo-transfiya-tyc-modal__body"],[1,"mbo-transfiya-tyc-modal__title","smalltext-medium"],[1,"mbo-transfiya-tyc-modal__message","body2-medium"],["elementId","chck_transfiya-tyc-modal_accept",3,"formControl"],["id","lnk_transfiya-modal_tyc",3,"click"],[1,"mbo-transfiya-tyc-modal__footer"],["id","btn_transfiya-tyc-modal_submit","bocc-button","raised",3,"disabled","click"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),e.\u0275\u0275text(3," \xa1Te damos la bienvenida al servicio de transferencias inmediatas a n\xfameros celulares! "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",3),e.\u0275\u0275text(5," Para continuar debes "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-checkbox-label",4),e.\u0275\u0275text(7," Aceptar "),e.\u0275\u0275elementStart(8,"a",5),e.\u0275\u0275listener("click",function(){return c.onTermAndConditions()}),e.\u0275\u0275text(9," T\xe9rminos y condiciones. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(10,"div",6)(11,"button",7),e.\u0275\u0275listener("click",function(){return c.onSubmit()}),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13,"Continuar"),e.\u0275\u0275elementEnd()()()()),2&n&&(e.\u0275\u0275advance(6),e.\u0275\u0275property("formControl",c.termAndConditionControl),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("downloading",c.downloading),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",!c.termAndConditionControl.value))},dependencies:[Y.a,M.P],styles:["mbo-transfiya-tyc-modal{position:relative;display:block;box-sizing:border-box}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x16)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x6)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__body bocc-checkbox-label a.downloading{pointer-events:none;opacity:.36}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__title{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__message{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__footer{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__footer>button{width:100%}\n"],encapsulation:2}),r})()},64561:(D,E,a)=>{a.d(E,{r:()=>M});var u=a(39904),d=a(95437),t=(a(42789),a(99877)),p=a(17007),C=a(90521);function v(o,_){if(1&o){const i=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),t.\u0275\u0275listener("event",function(A){t.\u0275\u0275restoreView(i);const l=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(l.onComponent(A))}),t.\u0275\u0275elementEnd()()}if(2&o){const i=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==i.history?null:i.history.date.dateFormat)("statusLabel",null==i.history?null:i.history.category)("statusColor",null==i.history?null:i.history.color)("subtitle",null==i.history?null:i.history.title)("number",null==i.history?null:i.history.phoneFormat)("description",null==i.history?null:i.history.description)("amount",null==i.history?null:i.history.amount)}}function S(o,_){1&o&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"bocc-card-information",5),t.\u0275\u0275elementEnd()),2&o&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}let M=(()=>{class o{constructor(i){this.mboProvider=i,this.skeleton=!1}onComponent(i){"component"===i&&this.history&&this.mboProvider.navigation.next(u.Z6.TRANSFERS.TRANSFIYA.HISTORY_INFORMATION,{redirect:"history",uuid:this.history.uuid})}}return o.\u0275fac=function(i){return new(i||o)(t.\u0275\u0275directiveInject(d.ZL))},o.\u0275cmp=t.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-history-card"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfiya-history-card",4,"ngIf"],["class","mbo-transfiya-history-card__skeleton",4,"ngIf"],[1,"mbo-transfiya-history-card"],[3,"title","statusLabel","statusColor","subtitle","number","description","amount","event"],[1,"mbo-transfiya-history-card__skeleton"],[3,"skeleton"]],template:function(i,f){1&i&&(t.\u0275\u0275template(0,v,2,7,"div",0),t.\u0275\u0275template(1,S,2,1,"div",1)),2&i&&(t.\u0275\u0275property("ngIf",!f.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",f.skeleton))},dependencies:[p.NgIf,C.v],styles:["mbo-transfiya-history-card{position:relative;width:100%;display:block}mbo-transfiya-history-card .mbo-transfiya-history-card{border-bottom:var(--border-1-lighter-300)}mbo-transfiya-history-card .mbo-transfiya-history-card .bocc-card-information__body{padding:var(--sizing-x6) var(--sizing-x4)}\n"],encapsulation:2}),o})()},81502:(D,E,a)=>{a.d(E,{s:()=>M});var u=a(39904),d=a(95437),t=(a(42789),a(99877)),p=a(17007),C=a(90521);function v(o,_){if(1&o){const i=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),t.\u0275\u0275listener("event",function(A){t.\u0275\u0275restoreView(i);const l=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(l.onComponent(A))}),t.\u0275\u0275elementEnd()()}if(2&o){const i=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==i.history?null:i.history.date.dateFormat)("statusColor",null==i.history?null:i.history.color)("statusLabel",null==i.history?null:i.history.category)("subtitle",null==i.history?null:i.history.title)("number",null==i.history?null:i.history.phoneFormat)("description",null==i.history?null:i.history.description)("amount",null==i.history?null:i.history.amount)("expanded",!0)}}function S(o,_){1&o&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"bocc-card-information",5),t.\u0275\u0275elementEnd()),2&o&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0)("expanded",!0))}let M=(()=>{class o{constructor(i){this.mboProvider=i,this.skeleton=!1}onComponent(i){"component"===i&&this.history&&this.mboProvider.navigation.next(u.Z6.TRANSFERS.TRANSFIYA.HISTORY_INFORMATION,{uuid:this.history.uuid})}}return o.\u0275fac=function(i){return new(i||o)(t.\u0275\u0275directiveInject(d.ZL))},o.\u0275cmp=t.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-history-element"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfiya-history-element__content",4,"ngIf"],["class","mbo-transfiya-history-element__skeleton",4,"ngIf"],[1,"mbo-transfiya-history-element__content"],[3,"title","statusColor","statusLabel","subtitle","number","description","amount","expanded","event"],[1,"mbo-transfiya-history-element__skeleton"],[3,"skeleton","expanded"]],template:function(i,f){1&i&&(t.\u0275\u0275template(0,v,2,8,"div",0),t.\u0275\u0275template(1,S,2,2,"div",1)),2&i&&(t.\u0275\u0275property("ngIf",!f.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",f.skeleton))},dependencies:[p.NgIf,C.v],styles:["mbo-transfiya-history-element{position:relative;width:100%;display:block}mbo-transfiya-history-element .mbo-transfiya-history-element__content{position:relative;width:100%;display:flex}mbo-transfiya-history-element .mbo-transfiya-history-element__content bocc-card-information{border:var(--border-1-lighter-300);overflow:hidden;border-radius:var(--sizing-x6)}mbo-transfiya-history-element .mbo-transfiya-history-element__skeleton{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}\n"],encapsulation:2}),o})()},77691:(D,E,a)=>{a.d(E,{L:()=>A});var u=a(39904),d=a(95437),t=(a(42789),a(99877)),p=a(17007),C=a(50689),v=a(45542),S=a(81502);function M(l,T){if(1&l){const m=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",5)(1,"label",6),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"button",7),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(m);const O=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(O.onRedirectAll())}),t.\u0275\u0275elementStart(4,"span"),t.\u0275\u0275text(5,"Ver todas"),t.\u0275\u0275elementEnd()()()}if(2&l){const m=t.\u0275\u0275nextContext();t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",(null==m.history||null==m.history.range?null:m.history.range.label)||"SIN RESULTADOS"," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("disabled",m.isEmpty||!(null!=m.history&&null!=m.history.range&&m.history.range.priority))}}function o(l,T){1&l&&t.\u0275\u0275element(0,"mbo-transfiya-history-element",10),2&l&&t.\u0275\u0275property("history",T.$implicit)}function _(l,T){if(1&l&&(t.\u0275\u0275elementStart(0,"div",8),t.\u0275\u0275template(1,o,1,1,"mbo-transfiya-history-element",9),t.\u0275\u0275elementEnd()),2&l){const m=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",m.history.firstPage)}}function i(l,T){1&l&&(t.\u0275\u0275elementStart(0,"div",11),t.\u0275\u0275element(1,"mbo-transfiya-history-element",12)(2,"mbo-transfiya-history-element",12),t.\u0275\u0275elementEnd()),2&l&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}function f(l,T){if(1&l&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",13),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&l){const m=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",m.msgError," ")}}let A=(()=>{class l{constructor(m){this.mboProvider=m}get isEmpty(){return this.history?.isError||0===this.history?.collection.length}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transacciones realizadas.":"Lo sentimos, por el momento no cuentas con transacciones realizadas."}onRedirectAll(){this.mboProvider.navigation.next(u.Z6.TRANSFERS.TRANSFIYA.HISTORY)}}return l.\u0275fac=function(m){return new(m||l)(t.\u0275\u0275directiveInject(d.ZL))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-transfiya-history-list"]],inputs:{history:"history"},decls:5,vars:4,consts:[[1,"mbo-transfiya-history-list__content"],["class","mbo-transfiya-history-list__header bocc-subheader",4,"ngIf"],["class","mbo-transfiya-history-list__component",4,"ngIf"],["class","mbo-transfiya-history-list__skeleton",4,"ngIf"],["class","mbo-transfiya-history-list__empty",4,"ngIf"],[1,"mbo-transfiya-history-list__header","bocc-subheader"],[1,"caption-medium"],["id","btn_transfiya-history-list_all","bocc-button","flat",3,"disabled","click"],[1,"mbo-transfiya-history-list__component"],[3,"history",4,"ngFor","ngForOf"],[3,"history"],[1,"mbo-transfiya-history-list__skeleton"],[3,"skeleton"],[1,"mbo-transfiya-history-list__empty"]],template:function(m,k){1&m&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,M,6,2,"div",1),t.\u0275\u0275template(2,_,2,1,"div",2),t.\u0275\u0275template(3,i,3,2,"div",3),t.\u0275\u0275template(4,f,2,1,"mbo-message-empty",4),t.\u0275\u0275elementEnd()),2&m&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",k.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",k.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!k.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",k.isEmpty))},dependencies:[p.NgForOf,p.NgIf,C.A,v.P,S.s],styles:["mbo-transfiya-history-list{position:relative;width:100%;display:block}mbo-transfiya-history-list .mbo-transfiya-history-list__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-transfiya-history-list .mbo-transfiya-history-list__header{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-transfiya-history-list .mbo-transfiya-history-list__header>label{text-transform:uppercase}mbo-transfiya-history-list .mbo-transfiya-history-list__component,mbo-transfiya-history-list .mbo-transfiya-history-list__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}\n"],encapsulation:2}),l})()}}]);