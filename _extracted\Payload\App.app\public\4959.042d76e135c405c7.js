(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4959,6390],{36319:(re,U,D)=>{D.d(U,{g:()=>s});var A=D(72972);const s=()=>{if(void 0!==A.w)return A.w.Capacitor}},99291:(re,U,D)=>{D.d(U,{A:()=>W,B:()=>ze,C:()=>oe,D:()=>ue,E:()=>Ve,F:()=>u,G:()=>Oe,H:()=>C,I:()=>z,J:()=>De,K:()=>K,L:()=>$,M:()=>Be,N:()=>i,O:()=>v,P:()=>ee,Q:()=>ne,R:()=>we,a:()=>Me,b:()=>L,c:()=>s,d:()=>ce,e:()=>se,f:()=>pe,g:()=>We,h:()=>Ye,i:()=>G,j:()=>Ie,k:()=>ge,l:()=>xe,m:()=>Te,n:()=>Re,o:()=>He,p:()=>Ae,q:()=>me,r:()=>de,s:()=>J,t:()=>ie,u:()=>ae,v:()=>Se,w:()=>T,x:()=>I,y:()=>Le,z:()=>Q});var A=D(28909);const s=(t,o)=>t.month===o.month&&t.day===o.day&&t.year===o.year,G=(t,o)=>t.year<o.year||t.year===o.year&&t.month<o.month||t.year===o.year&&t.month===o.month&&null!==t.day&&t.day<o.day,L=(t,o)=>t.year>o.year||t.year===o.year&&t.month>o.month||t.year===o.year&&t.month===o.month&&null!==t.day&&t.day>o.day,T=(t,o,r)=>{const l=Array.isArray(t)?t:[t];for(const h of l)if(void 0!==o&&G(h,o)||void 0!==r&&L(h,r)){(0,A.p)(`The value provided to ion-datetime is out of bounds.\n\nMin: ${JSON.stringify(o)}\nMax: ${JSON.stringify(r)}\nValue: ${JSON.stringify(t)}`);break}},$=(t,o)=>{if(void 0!==o)return o;const r=new Intl.DateTimeFormat(t,{hour:"numeric"}),l=r.resolvedOptions();if(void 0!==l.hourCycle)return l.hourCycle;const c=r.formatToParts(new Date("5/18/2021 00:00")).find(p=>"hour"===p.type);if(!c)throw new Error("Hour value not found from DateTimeFormat");switch(c.value){case"0":return"h11";case"12":return"h12";case"00":return"h23";case"24":return"h24";default:throw new Error(`Invalid hour cycle "${o}"`)}},j=t=>"h23"===t||"h24"===t,W=(t,o)=>4===t||6===t||9===t||11===t?30:2===t?(t=>t%4==0&&t%100!=0||t%400==0)(o)?29:28:31,u=(t,o={month:"numeric",year:"numeric"})=>"month"===new Intl.DateTimeFormat(t,o).formatToParts(new Date)[0].type,C=t=>"dayPeriod"===new Intl.DateTimeFormat(t,{hour:"numeric"}).formatToParts(new Date)[0].type,_=/^(\d{4}|[+\-]\d{6})(?:-(\d{2})(?:-(\d{2}))?)?(?:T(\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/,Z=/^((\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/,ie=t=>{if(void 0===t)return;let r,o=t;return"string"==typeof t&&(o=t.replace(/\[|\]|\s/g,"").split(",")),r=Array.isArray(o)?o.map(l=>parseInt(l,10)).filter(isFinite):[o],r},pe=t=>({month:parseInt(t.getAttribute("data-month"),10),day:parseInt(t.getAttribute("data-day"),10),year:parseInt(t.getAttribute("data-year"),10),dayOfWeek:parseInt(t.getAttribute("data-day-of-week"),10)});function J(t){if(Array.isArray(t)){const r=[];for(const l of t){const h=J(l);if(!h)return;r.push(h)}return r}let o=null;if(null!=t&&""!==t&&(o=Z.exec(t),o?(o.unshift(void 0,void 0),o[2]=o[3]=void 0):o=_.exec(t)),null!==o){for(let r=1;r<8;r++)o[r]=void 0!==o[r]?parseInt(o[r],10):void 0;return{year:o[1],month:o[2],day:o[3],hour:o[4],minute:o[5],ampm:o[4]<12?"am":"pm"}}(0,A.p)(`Unable to parse date string: ${t}. Please provide a valid ISO 8601 datetime string.`)}const ee=(t,o,r)=>o&&G(t,o)?o:r&&L(t,r)?r:t,ne=t=>t>=12?"pm":"am",de=(t,o)=>{const r=J(t);if(void 0===r)return;const{month:l,day:h,year:g,hour:c,minute:p}=r,w=g??o.year,M=l??12;return{month:M,day:h??W(M,w),year:w,hour:c??23,minute:p??59}},me=(t,o)=>{const r=J(t);if(void 0===r)return;const{month:l,day:h,year:g,hour:c,minute:p}=r;return{month:l??1,day:h??1,year:g??o.year,hour:c??0,minute:p??0}},X=t=>("0"+(void 0!==t?Math.abs(t):"0")).slice(-2),fe=t=>("000"+(void 0!==t?Math.abs(t):"0")).slice(-4);function ae(t){if(Array.isArray(t))return t.map(r=>ae(r));let o="";return void 0!==t.year?(o=fe(t.year),void 0!==t.month&&(o+="-"+X(t.month),void 0!==t.day&&(o+="-"+X(t.day),void 0!==t.hour&&(o+=`T${X(t.hour)}:${X(t.minute)}:00`)))):void 0!==t.hour&&(o=X(t.hour)+":"+X(t.minute)),o}const ke=(t,o)=>void 0===o?t:"am"===o?12===t?0:t:12===t?12:t+12,xe=t=>{const{dayOfWeek:o}=t;if(null==o)throw new Error("No day of week provided");return le(t,o)},ge=t=>{const{dayOfWeek:o}=t;if(null==o)throw new Error("No day of week provided");return ye(t,6-o)},Re=t=>ye(t,1),Te=t=>le(t,1),He=t=>le(t,7),Ae=t=>ye(t,7),le=(t,o)=>{const{month:r,day:l,year:h}=t;if(null===l)throw new Error("No day provided");const g={month:r,day:l,year:h};if(g.day=l-o,g.day<1&&(g.month-=1),g.month<1&&(g.month=12,g.year-=1),g.day<1){const c=W(g.month,g.year);g.day=c+g.day}return g},ye=(t,o)=>{const{month:r,day:l,year:h}=t;if(null===l)throw new Error("No day provided");const g={month:r,day:l,year:h},c=W(r,h);return g.day=l+o,g.day>c&&(g.day-=c,g.month+=1),g.month>12&&(g.month=1,g.year+=1),g},ce=t=>{const o=1===t.month?12:t.month-1,r=1===t.month?t.year-1:t.year,l=W(o,r);return{month:o,year:r,day:l<t.day?l:t.day}},se=t=>{const o=12===t.month?1:t.month+1,r=12===t.month?t.year+1:t.year,l=W(o,r);return{month:o,year:r,day:l<t.day?l:t.day}},Ee=(t,o)=>{const r=t.month,l=t.year+o,h=W(r,l);return{month:r,year:l,day:h<t.day?h:t.day}},Ie=t=>Ee(t,-1),Ye=t=>Ee(t,1),je=(t,o,r)=>o?t:ke(t,r),we=(t,o)=>{const{ampm:r,hour:l}=t;let h=l;return"am"===r&&"pm"===o?h=ke(h,"pm"):"pm"===r&&"am"===o&&(h=Math.abs(h-12)),h},Se=(t,o,r)=>{const{month:l,day:h,year:g}=t,c=ee(Object.assign({},t),o,r),p=W(l,g);return null!==h&&p<h&&(c.day=p),void 0!==o&&s(c,o)&&void 0!==c.hour&&void 0!==o.hour&&(c.hour<o.hour?(c.hour=o.hour,c.minute=o.minute):c.hour===o.hour&&void 0!==c.minute&&void 0!==o.minute&&c.minute<o.minute&&(c.minute=o.minute)),void 0!==r&&s(t,r)&&void 0!==c.hour&&void 0!==r.hour&&(c.hour>r.hour?(c.hour=r.hour,c.minute=r.minute):c.hour===r.hour&&void 0!==c.minute&&void 0!==r.minute&&c.minute>r.minute&&(c.minute=r.minute)),c},Le=({refParts:t,monthValues:o,dayValues:r,yearValues:l,hourValues:h,minuteValues:g,minParts:c,maxParts:p})=>{const{hour:w,minute:M,day:O,month:Y,year:R}=t,k=Object.assign(Object.assign({},t),{dayOfWeek:void 0});if(void 0!==l){const B=l.filter(H=>!(void 0!==c&&H<c.year||void 0!==p&&H>p.year));k.year=he(R,B)}if(void 0!==o){const B=o.filter(H=>!(void 0!==c&&k.year===c.year&&H<c.month||void 0!==p&&k.year===p.year&&H>p.month));k.month=he(Y,B)}if(null!==O&&void 0!==r){const B=r.filter(H=>!(void 0!==c&&G(Object.assign(Object.assign({},k),{day:H}),c)||void 0!==p&&L(Object.assign(Object.assign({},k),{day:H}),p)));k.day=he(O,B)}if(void 0!==w&&void 0!==h){const B=h.filter(H=>!(void 0!==c?.hour&&s(k,c)&&H<c.hour||void 0!==p?.hour&&s(k,p)&&H>p.hour));k.hour=he(w,B),k.ampm=ne(k.hour)}if(void 0!==M&&void 0!==g){const B=g.filter(H=>!(void 0!==c?.minute&&s(k,c)&&k.hour===c.hour&&H<c.minute||void 0!==p?.minute&&s(k,p)&&k.hour===p.hour&&H>p.minute));k.minute=he(M,B)}return k},he=(t,o)=>{let r=o[0],l=Math.abs(r-t);for(let h=1;h<o.length;h++){const g=o[h],c=Math.abs(g-t);c<l&&(r=g,l=c)}return r},Fe=t=>Object.assign(Object.assign({},t),{timeZone:"UTC",timeZoneName:void 0}),Be=(t,o,r,l={hour:"numeric",minute:"numeric"})=>{const h={hour:o.hour,minute:o.minute};return void 0===h.hour||void 0===h.minute?"Invalid Time":new Intl.DateTimeFormat(t,Object.assign(Object.assign({},Fe(l)),{hourCycle:r})).format(new Date(ae(Object.assign({year:2023,day:1,month:1},h))+"Z"))},be=t=>{const o=t.toString();return o.length>1?o:`0${o}`},Ce=(t,o)=>{if(0===t)switch(o){case"h11":return"0";case"h12":return"12";case"h23":return"00";case"h24":return"24";default:throw new Error(`Invalid hour cycle "${o}"`)}return j(o)?be(t):t.toString()},We=(t,o,r)=>{if(null===r.day)return null;const l=n(r),h=new Intl.DateTimeFormat(t,{weekday:"long",month:"long",day:"numeric",timeZone:"UTC"}).format(l);return o?`Today, ${h}`:h},De=(t,o)=>{const r=n(o);return new Intl.DateTimeFormat(t,{month:"long",year:"numeric",timeZone:"UTC"}).format(r)},Me=(t,o)=>a(t,o,{day:"numeric"}).find(r=>"day"===r.type).value,e=(t,o)=>i(t,o,{year:"numeric"}),n=t=>{var o,r,l;return new Date(`${null!==(o=t.month)&&void 0!==o?o:1}/${null!==(r=t.day)&&void 0!==r?r:1}/${null!==(l=t.year)&&void 0!==l?l:2023}${void 0!==t.hour&&void 0!==t.minute?` ${t.hour}:${t.minute}`:""} GMT+0000`)},i=(t,o,r)=>{const l=n(o);return d(t,Fe(r)).format(l)},a=(t,o,r)=>{const l=n(o);return d(t,r).formatToParts(l)},d=(t,o)=>new Intl.DateTimeFormat(t,Object.assign(Object.assign({},o),{timeZone:"UTC"})),m=t=>{if("RelativeTimeFormat"in Intl){const o=new Intl.RelativeTimeFormat(t,{numeric:"auto"}).format(0,"day");return o.charAt(0).toUpperCase()+o.slice(1)}return"Today"},y=t=>{const o=t.getTimezoneOffset();return t.setMinutes(t.getMinutes()-o),t},f=y(new Date("2022T01:00")),b=y(new Date("2022T13:00")),x=(t,o)=>{const r="am"===o?f:b,l=new Intl.DateTimeFormat(t,{hour:"numeric",timeZone:"UTC"}).formatToParts(r).find(h=>"dayPeriod"===h.type);return l?l.value:(t=>void 0===t?"":t.toUpperCase())(o)},v=t=>Array.isArray(t)?t.join(","):t,I=()=>y(new Date).toISOString(),P=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59],E=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,1,2,3,4,5,6,7,8,9,10,11],V=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],F=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,0],z=(t,o,r=0)=>{const h=new Intl.DateTimeFormat(t,{weekday:"ios"===o?"short":"narrow"}),g=new Date("11/01/2020"),c=[];for(let p=r;p<r+7;p++){const w=new Date(g);w.setDate(w.getDate()+p),c.push(h.format(w))}return c},K=(t,o,r)=>{const l=W(t,o),h=new Date(`${t}/1/${o}`).getDay(),g=h>=r?h-(r+1):6-(r-h);let c=[];for(let p=1;p<=l;p++)c.push({day:p,dayOfWeek:(g+p)%7});for(let p=0;p<=g;p++)c=[{day:null,dayOfWeek:null},...c];return c},Q=(t,o)=>{const r={month:t.month,year:t.year,day:t.day};if(void 0!==o&&(t.month!==o.month||t.year!==o.year)){const l={month:o.month,year:o.year,day:o.day};return G(l,r)?[l,r,se(t)]:[ce(t),r,l]}return[ce(t),r,se(t)]},oe=(t,o,r,l,h,g={month:"long"})=>{const{year:c}=o,p=[];if(void 0!==h){let w=h;void 0!==l?.month&&(w=w.filter(M=>M<=l.month)),void 0!==r?.month&&(w=w.filter(M=>M>=r.month)),w.forEach(M=>{const O=new Date(`${M}/1/${c} GMT+0000`),Y=new Intl.DateTimeFormat(t,Object.assign(Object.assign({},g),{timeZone:"UTC"})).format(O);p.push({text:Y,value:M})})}else{const w=l&&l.year===c?l.month:12;for(let O=r&&r.year===c?r.month:1;O<=w;O++){const Y=new Date(`${O}/1/${c} GMT+0000`),R=new Intl.DateTimeFormat(t,Object.assign(Object.assign({},g),{timeZone:"UTC"})).format(Y);p.push({text:R,value:O})}}return p},ue=(t,o,r,l,h,g={day:"numeric"})=>{const{month:c,year:p}=o,w=[],M=W(c,p),O=null!=l?.day&&l.year===p&&l.month===c?l.day:M,Y=null!=r?.day&&r.year===p&&r.month===c?r.day:1;if(void 0!==h){let R=h;R=R.filter(k=>k>=Y&&k<=O),R.forEach(k=>{const B=new Date(`${c}/${k}/${p} GMT+0000`),H=new Intl.DateTimeFormat(t,Object.assign(Object.assign({},g),{timeZone:"UTC"})).format(B);w.push({text:H,value:k})})}else for(let R=Y;R<=O;R++){const k=new Date(`${c}/${R}/${p} GMT+0000`),B=new Intl.DateTimeFormat(t,Object.assign(Object.assign({},g),{timeZone:"UTC"})).format(k);w.push({text:B,value:R})}return w},Ve=(t,o,r,l,h)=>{var g,c;let p=[];if(void 0!==h)p=h,void 0!==l?.year&&(p=p.filter(w=>w<=l.year)),void 0!==r?.year&&(p=p.filter(w=>w>=r.year));else{const{year:w}=o,M=null!==(g=l?.year)&&void 0!==g?g:w;for(let Y=null!==(c=r?.year)&&void 0!==c?c:w-100;Y<=M;Y++)p.push(Y)}return p.map(w=>({text:e(t,{year:w,month:o.month,day:o.day}),value:w}))},_e=(t,o)=>t.month===o.month&&t.year===o.year?[t]:[t,..._e(se(t),o)],ze=(t,o,r,l,h,g)=>{let c=[],p=[],w=_e(r,l);return g&&(w=w.filter(({month:M})=>g.includes(M))),w.forEach(M=>{const O={month:M.month,day:null,year:M.year},Y=ue(t,O,r,l,h,{month:"short",day:"numeric",weekday:"short"}),R=[],k=[];Y.forEach(B=>{const H=s(Object.assign(Object.assign({},O),{day:B.value}),o);k.push({text:H?m(t):B.text,value:`${O.year}-${O.month}-${B.value}`}),R.push({month:O.month,year:O.year,day:B.value})}),p=[...p,...R],c=[...c,...k]}),{parts:p,items:c}},Oe=(t,o,r,l,h,g,c)=>{const p=$(t,r),w=j(p),{hours:M,minutes:O,am:Y,pm:R}=((t,o,r="h12",l,h,g,c)=>{const p=$(t,r),w=j(p);let M=(t=>{switch(t){case"h11":return E;case"h12":return S;case"h23":return V;case"h24":return F;default:throw new Error(`Invalid hour cycle "${t}"`)}})(p),O=P,Y=!0,R=!0;if(g&&(M=M.filter(k=>g.includes(k))),c&&(O=O.filter(k=>c.includes(k))),l)if(s(o,l)){if(void 0!==l.hour&&(M=M.filter(k=>(w?k:"pm"===o.ampm?(k+12)%24:k)>=l.hour),Y=l.hour<13),void 0!==l.minute){let k=!1;void 0!==l.hour&&void 0!==o.hour&&o.hour>l.hour&&(k=!0),O=O.filter(B=>!!k||B>=l.minute)}}else G(o,l)&&(M=[],O=[],Y=R=!1);return h&&(s(o,h)?(void 0!==h.hour&&(M=M.filter(k=>(w?k:"pm"===o.ampm?(k+12)%24:k)<=h.hour),R=h.hour>=12),void 0!==h.minute&&o.hour===h.hour&&(O=O.filter(k=>k<=h.minute))):L(o,h)&&(M=[],O=[],Y=R=!1)),{hours:M,minutes:O,am:Y,pm:R}})(t,o,p,l,h,g,c),k=M.map(ve=>({text:Ce(ve,p),value:je(ve,w,o.ampm)})),B=O.map(ve=>({text:be(ve),value:ve})),H=[];return Y&&!w&&H.push({text:x(t,"am"),value:"am"}),R&&!w&&H.push({text:x(t,"pm"),value:"pm"}),{minutesData:B,hoursData:k,dayPeriodData:H}}},56879:(re,U,D)=>{D.d(U,{i:()=>A});const A=s=>s&&""!==s.dir?"rtl"===s.dir.toLowerCase():"rtl"===document?.dir.toLowerCase()},96390:(re,U,D)=>{D.r(U),D.d(U,{startFocusVisible:()=>L});const A="ion-focused",G=["Tab","ArrowDown","Space","Escape"," ","Shift","Enter","ArrowLeft","ArrowRight","ArrowUp","Home","End"],L=T=>{let N=[],$=!0;const j=T?T.shadowRoot:document,W=T||document.body,u=J=>{N.forEach(ee=>ee.classList.remove(A)),J.forEach(ee=>ee.classList.add(A)),N=J},C=()=>{$=!1,u([])},_=J=>{$=G.includes(J.key),$||u([])},Z=J=>{if($&&void 0!==J.composedPath){const ee=J.composedPath().filter(ne=>!!ne.classList&&ne.classList.contains("ion-focusable"));u(ee)}},ie=()=>{j.activeElement===W&&u([])};return j.addEventListener("keydown",_),j.addEventListener("focusin",Z),j.addEventListener("focusout",ie),j.addEventListener("touchstart",C,{passive:!0}),j.addEventListener("mousedown",C),{destroy:()=>{j.removeEventListener("keydown",_),j.removeEventListener("focusin",Z),j.removeEventListener("focusout",ie),j.removeEventListener("touchstart",C),j.removeEventListener("mousedown",C)},setFocus:u}}},1765:(re,U,D)=>{D.d(U,{I:()=>s,a:()=>$,b:()=>j,c:()=>N,d:()=>u,h:()=>W});var A=D(36319),s=(()=>{return(C=s||(s={})).Heavy="HEAVY",C.Medium="MEDIUM",C.Light="LIGHT",s;var C})();const L={getEngine(){const C=window.TapticEngine;if(C)return C;const _=(0,A.g)();return _?.isPluginAvailable("Haptics")?_.Plugins.Haptics:void 0},available(){return!!this.getEngine()&&("web"!==(0,A.g)()?.getPlatform()||typeof navigator<"u"&&void 0!==navigator.vibrate)},isCordova:()=>void 0!==window.TapticEngine,isCapacitor:()=>void 0!==(0,A.g)(),impact(C){const _=this.getEngine();if(!_)return;const Z=this.isCapacitor()?C.style:C.style.toLowerCase();_.impact({style:Z})},notification(C){const _=this.getEngine();if(!_)return;const Z=this.isCapacitor()?C.type:C.type.toLowerCase();_.notification({type:Z})},selection(){const C=this.isCapacitor()?s.Light:"light";this.impact({style:C})},selectionStart(){const C=this.getEngine();C&&(this.isCapacitor()?C.selectionStart():C.gestureSelectionStart())},selectionChanged(){const C=this.getEngine();C&&(this.isCapacitor()?C.selectionChanged():C.gestureSelectionChanged())},selectionEnd(){const C=this.getEngine();C&&(this.isCapacitor()?C.selectionEnd():C.gestureSelectionEnd())}},T=()=>L.available(),N=()=>{T()&&L.selection()},$=()=>{T()&&L.selectionStart()},j=()=>{T()&&L.selectionChanged()},W=()=>{T()&&L.selectionEnd()},u=C=>{T()&&L.impact(C)}},44896:(re,U,D)=>{D.d(U,{a:()=>A,b:()=>Z,c:()=>$,d:()=>ie,e:()=>ae,f:()=>N,g:()=>pe,h:()=>G,i:()=>s,j:()=>me,k:()=>X,l:()=>j,m:()=>C,n:()=>J,o:()=>u,p:()=>T,q:()=>L,r:()=>de,s:()=>fe,t:()=>_,u:()=>ee,v:()=>ne,w:()=>W});const A="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-miterlimit='10' stroke-width='48' d='M244 400L100 256l144-144M120 256h292' class='ionicon-fill-none'/></svg>",s="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 268l144 144 144-144M256 392V100' class='ionicon-fill-none'/></svg>",G="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M368 64L144 256l224 192V64z'/></svg>",L="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 144l192 224 192-224H64z'/></svg>",T="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 368L256 144 64 368h384z'/></svg>",N="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M416 128L192 384l-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>",$="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>",j="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",W="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M136 208l120-104 120 104M136 304l120 104 120-104' stroke-width='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none'/></svg>",u="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",C="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",_="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z'/></svg>",Z="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",ie="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>",pe="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='192' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>",J="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='48'/><circle cx='416' cy='256' r='48'/><circle cx='96' cy='256' r='48'/></svg>",ee="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>",ne="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 384h384v-42.67H64zm0-106.67h384v-42.66H64zM64 128v42.67h384V128z'/></svg>",de="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>",me="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M96 256h320M96 176h320M96 336h320' class='ionicon-fill-none ionicon-stroke-width'/></svg>",X="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='44' d='M118 304h276M118 208h276' class='ionicon-fill-none'/></svg>",fe="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>",ae="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 428L339.92 303.9a160.48 160.48 0 0030.72-94.58C370.64 120.37 298.27 48 209.32 48S48 120.37 48 209.32s72.37 161.32 161.32 161.32a160.48 160.48 0 0094.58-30.72L428 464zM209.32 319.69a110.38 110.38 0 11110.37-110.37 110.5 110.5 0 01-110.37 110.37z'/></svg>"},54959:(re,U,D)=>{D.r(U),D.d(U,{ion_datetime:()=>le,ion_picker:()=>we,ion_picker_column:()=>be});var A=D(15861),s=D(42477),G=D(96390),L=D(78635),T=D(28909),N=D(56879),$=D(23814),j=D(44896),W=D(37943),u=D(99291),C=D(37389),_=D(57346),Z=D(44963),ie=D(1765);D(72972),D(33006),D(36319);const de=(e,n,i,a)=>!!(null===e.day||void 0!==a&&!a.includes(e.day)||n&&(0,u.i)(e,n)||i&&(0,u.b)(e,i)),X=(e,{minParts:n,maxParts:i})=>!!(((e,n,i)=>!!(n&&n.year>e||i&&i.year<e))(e.year,n,i)||n&&(0,u.i)(e,n)||i&&(0,u.b)(e,i)),xe=(e,n)=>{var i,a,d,m;(null!==(i=n?.date)&&void 0!==i&&i.timeZone||null!==(a=n?.date)&&void 0!==a&&a.timeZoneName||null!==(d=n?.time)&&void 0!==d&&d.timeZone||null!==(m=n?.time)&&void 0!==m&&m.timeZoneName)&&(0,T.p)('Datetime: "timeZone" and "timeZoneName" are not supported in "formatOptions".',e)},ge=(e,n,i)=>{if(i)switch(n){case"date":case"month-year":case"month":case"year":void 0===i.date&&(0,T.p)(`Datetime: The '${n}' presentation requires a date object in formatOptions.`,e);break;case"time":void 0===i.time&&(0,T.p)("Datetime: The 'time' presentation requires a time object in formatOptions.",e);break;case"date-time":case"time-date":void 0===i.date&&void 0===i.time&&(0,T.p)(`Datetime: The '${n}' presentation requires either a date or time object (or both) in formatOptions.`,e)}},le=class{constructor(e){var n=this;(0,s.r)(this,e),this.ionCancel=(0,s.d)(this,"ionCancel",7),this.ionChange=(0,s.d)(this,"ionChange",7),this.ionValueChange=(0,s.d)(this,"ionValueChange",7),this.ionFocus=(0,s.d)(this,"ionFocus",7),this.ionBlur=(0,s.d)(this,"ionBlur",7),this.ionStyle=(0,s.d)(this,"ionStyle",7),this.ionRender=(0,s.d)(this,"ionRender",7),this.inputId="ion-dt-"+ye++,this.prevPresentation=null,this.warnIfIncorrectValueUsage=()=>{const{multiple:i,value:a}=this;!i&&Array.isArray(a)&&(0,T.p)(`ion-datetime was passed an array of values, but multiple="false". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the "value" property when multiple="false".\n\n  Value Passed: [${a.map(d=>`'${d}'`).join(", ")}]\n`,this.el)},this.setValue=i=>{this.value=i,this.ionChange.emit({value:i})},this.getActivePartsWithFallback=()=>{var i;const{defaultParts:a}=this;return null!==(i=this.getActivePart())&&void 0!==i?i:a},this.getActivePart=()=>{const{activeParts:i}=this;return Array.isArray(i)?i[0]:i},this.closeParentOverlay=()=>{const i=this.el.closest("ion-modal, ion-popover");i&&i.dismiss()},this.setWorkingParts=i=>{this.workingParts=Object.assign({},i)},this.setActiveParts=(i,a=!1)=>{if(this.readonly)return;const{multiple:d,minParts:m,maxParts:y,activeParts:f}=this,b=(0,u.v)(i,m,y);if(this.setWorkingParts(b),d){const v=Array.isArray(f)?f:[f];this.activeParts=a?v.filter(I=>!(0,u.c)(I,b)):[...v,b]}else this.activeParts=Object.assign({},b);null!==this.el.querySelector('[slot="buttons"]')||this.showDefaultButtons||this.confirm()},this.initializeKeyboardListeners=()=>{const i=this.calendarBodyRef;if(!i)return;const a=this.el.shadowRoot,d=i.querySelector(".calendar-month:nth-of-type(2)"),y=new MutationObserver(f=>{var b;null!==(b=f[0].oldValue)&&void 0!==b&&b.includes("ion-focused")||!i.classList.contains("ion-focused")||this.focusWorkingDay(d)});y.observe(i,{attributeFilter:["class"],attributeOldValue:!0}),this.destroyKeyboardMO=()=>{y?.disconnect()},i.addEventListener("keydown",f=>{const b=a.activeElement;if(!b||!b.classList.contains("calendar-day"))return;const x=(0,u.f)(b);let v;switch(f.key){case"ArrowDown":f.preventDefault(),v=(0,u.p)(x);break;case"ArrowUp":f.preventDefault(),v=(0,u.o)(x);break;case"ArrowRight":f.preventDefault(),v=(0,u.n)(x);break;case"ArrowLeft":f.preventDefault(),v=(0,u.m)(x);break;case"Home":f.preventDefault(),v=(0,u.l)(x);break;case"End":f.preventDefault(),v=(0,u.k)(x);break;case"PageUp":f.preventDefault(),v=f.shiftKey?(0,u.j)(x):(0,u.d)(x);break;case"PageDown":f.preventDefault(),v=f.shiftKey?(0,u.h)(x):(0,u.e)(x);break;default:return}de(v,this.minParts,this.maxParts)||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),v)),requestAnimationFrame(()=>this.focusWorkingDay(d)))})},this.focusWorkingDay=i=>{const a=i.querySelectorAll(".calendar-day-padding"),{day:d}=this.workingParts;if(null===d)return;const m=i.querySelector(`.calendar-day-wrapper:nth-of-type(${a.length+d}) .calendar-day`);m&&m.focus()},this.processMinParts=()=>{const{min:i,defaultParts:a}=this;this.minParts=void 0!==i?(0,u.q)(i,a):void 0},this.processMaxParts=()=>{const{max:i,defaultParts:a}=this;this.maxParts=void 0!==i?(0,u.r)(i,a):void 0},this.initializeCalendarListener=()=>{const i=this.calendarBodyRef;if(!i)return;const a=i.querySelectorAll(".calendar-month"),d=a[0],m=a[1],y=a[2],b="ios"===(0,W.b)(this)&&typeof navigator<"u"&&navigator.maxTouchPoints>1;(0,s.w)(()=>{i.scrollLeft=d.clientWidth*((0,N.i)(this.el)?-1:1);const x=S=>{const V=i.getBoundingClientRect(),F=i.scrollLeft<=2?d:y,z=F.getBoundingClientRect();if(Math.abs(z.x-V.x)>2)return;const{forceRenderDate:K}=this;return void 0!==K?{month:K.month,year:K.year,day:K.day}:F===d?(0,u.d)(S):F===y?(0,u.e)(S):void 0},v=()=>{b&&(i.style.removeProperty("pointer-events"),P=!1);const S=x(this.workingParts);if(!S)return;const{month:V,day:F,year:z}=S;X({month:V,year:z,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})})||(i.style.setProperty("overflow","hidden"),(0,s.w)(()=>{this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:V,day:F,year:z})),i.scrollLeft=m.clientWidth*((0,N.i)(this.el)?-1:1),i.style.removeProperty("overflow"),this.resolveForceDateScrolling&&this.resolveForceDateScrolling()}))};let I,P=!1;const E=()=>{I&&clearTimeout(I),!P&&b&&(i.style.setProperty("pointer-events","none"),P=!0),I=setTimeout(v,50)};i.addEventListener("scroll",E),this.destroyCalendarListener=()=>{i.removeEventListener("scroll",E)}})},this.destroyInteractionListeners=()=>{const{destroyCalendarListener:i,destroyKeyboardMO:a}=this;void 0!==i&&i(),void 0!==a&&a()},this.processValue=i=>{const a=null!=i&&(!Array.isArray(i)||i.length>0),d=a?(0,u.s)(i):this.defaultParts,{minParts:m,maxParts:y,workingParts:f,el:b}=this;if(this.warnIfIncorrectValueUsage(),!d)return;a&&(0,u.w)(d,m,y);const x=Array.isArray(d)?d[0]:d,v=(0,u.P)(x,m,y),{month:I,day:P,year:E,hour:S,minute:V}=v,F=(0,u.Q)(S);this.activeParts=a?Array.isArray(d)?[...d]:{month:I,day:P,year:E,hour:S,minute:V,ampm:F}:[];const z=void 0!==I&&I!==f.month||void 0!==E&&E!==f.year,K=b.classList.contains("datetime-ready"),{isGridStyle:te,showMonthAndYear:q}=this;let Q=!0;if(Array.isArray(d)){const oe=d[0].month;for(const ue of d)if(ue.month!==oe){Q=!1;break}}Q&&(te&&z&&K&&!q?this.animateToDate(v):this.setWorkingParts({month:I,day:P,year:E,hour:S,minute:V,ampm:F}))},this.animateToDate=function(){var i=(0,A.Z)(function*(a){const{workingParts:d}=n;n.forceRenderDate=a;const m=new Promise(f=>{n.resolveForceDateScrolling=f});(0,u.i)(a,d)?n.prevMonth():n.nextMonth(),yield m,n.resolveForceDateScrolling=void 0,n.forceRenderDate=void 0});return function(a){return i.apply(this,arguments)}}(),this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.hasValue=()=>null!=this.value,this.nextMonth=()=>{const i=this.calendarBodyRef;if(!i)return;const a=i.querySelector(".calendar-month:last-of-type");a&&i.scrollTo({top:0,left:2*a.offsetWidth*((0,N.i)(this.el)?-1:1),behavior:"smooth"})},this.prevMonth=()=>{const i=this.calendarBodyRef;!i||!i.querySelector(".calendar-month:first-of-type")||i.scrollTo({top:0,left:0,behavior:"smooth"})},this.toggleMonthAndYearView=()=>{this.showMonthAndYear=!this.showMonthAndYear},this.showMonthAndYear=!1,this.activeParts=[],this.workingParts={month:5,day:28,year:2021,hour:13,minute:52,ampm:"pm"},this.isTimePopoverOpen=!1,this.forceRenderDate=void 0,this.color="primary",this.name=this.inputId,this.disabled=!1,this.formatOptions=void 0,this.readonly=!1,this.isDateEnabled=void 0,this.min=void 0,this.max=void 0,this.presentation="date-time",this.cancelText="Cancel",this.doneText="Done",this.clearText="Clear",this.yearValues=void 0,this.monthValues=void 0,this.dayValues=void 0,this.hourValues=void 0,this.minuteValues=void 0,this.locale="default",this.firstDayOfWeek=0,this.titleSelectedDatesFormatter=void 0,this.multiple=!1,this.highlightedDates=void 0,this.value=void 0,this.showDefaultTitle=!1,this.showDefaultButtons=!1,this.showClearButton=!1,this.showDefaultTimeLabel=!0,this.hourCycle=void 0,this.size="fixed",this.preferWheel=!1}formatOptionsChanged(){const{el:e,formatOptions:n,presentation:i}=this;ge(e,i,n),xe(e,n)}disabledChanged(){this.emitStyle()}minChanged(){this.processMinParts()}maxChanged(){this.processMaxParts()}presentationChanged(){const{el:e,formatOptions:n,presentation:i}=this;ge(e,i,n)}get isGridStyle(){const{presentation:e,preferWheel:n}=this;return("date"===e||"date-time"===e||"time-date"===e)&&!n}yearValuesChanged(){this.parsedYearValues=(0,u.t)(this.yearValues)}monthValuesChanged(){this.parsedMonthValues=(0,u.t)(this.monthValues)}dayValuesChanged(){this.parsedDayValues=(0,u.t)(this.dayValues)}hourValuesChanged(){this.parsedHourValues=(0,u.t)(this.hourValues)}minuteValuesChanged(){this.parsedMinuteValues=(0,u.t)(this.minuteValues)}valueChanged(){var e=this;return(0,A.Z)(function*(){const{value:n}=e;e.hasValue()&&e.processValue(n),e.emitStyle(),e.ionValueChange.emit({value:n})})()}confirm(e=!1){var n=this;return(0,A.Z)(function*(){const{isCalendarPicker:i,activeParts:a,preferWheel:d,workingParts:m}=n;(void 0!==a||!i)&&(Array.isArray(a)&&0===a.length?n.setValue(d?(0,u.u)(m):void 0):n.setValue((0,u.u)(a))),e&&n.closeParentOverlay()})()}reset(e){var n=this;return(0,A.Z)(function*(){n.processValue(e)})()}cancel(e=!1){var n=this;return(0,A.Z)(function*(){n.ionCancel.emit(),e&&n.closeParentOverlay()})()}get isCalendarPicker(){const{presentation:e}=this;return"date"===e||"date-time"===e||"time-date"===e}connectedCallback(){this.clearFocusVisible=(0,G.startFocusVisible)(this.el).destroy}disconnectedCallback(){this.clearFocusVisible&&(this.clearFocusVisible(),this.clearFocusVisible=void 0)}initializeListeners(){this.initializeCalendarListener(),this.initializeKeyboardListeners()}componentDidLoad(){const{el:e,intersectionTrackerRef:n}=this,a=new IntersectionObserver(f=>{f[0].isIntersecting&&(this.initializeListeners(),(0,s.w)(()=>{this.el.classList.add("datetime-ready")}))},{threshold:.01,root:e});(0,L.r)(()=>a?.observe(n));const m=new IntersectionObserver(f=>{f[0].isIntersecting||(this.destroyInteractionListeners(),this.showMonthAndYear=!1,(0,s.w)(()=>{this.el.classList.remove("datetime-ready")}))},{threshold:0,root:e});(0,L.r)(()=>m?.observe(n));const y=(0,L.g)(this.el);y.addEventListener("ionFocus",f=>f.stopPropagation()),y.addEventListener("ionBlur",f=>f.stopPropagation())}componentDidRender(){const{presentation:e,prevPresentation:n,calendarBodyRef:i,minParts:a,preferWheel:d,forceRenderDate:m}=this,y=!d&&["date-time","time-date","date"].includes(e);if(void 0!==a&&y&&i){const f=i.querySelector(".calendar-month:nth-of-type(1)");f&&void 0===m&&(i.scrollLeft=f.clientWidth*((0,N.i)(this.el)?-1:1))}null!==n?e!==n&&(this.prevPresentation=e,this.destroyInteractionListeners(),this.initializeListeners(),this.showMonthAndYear=!1,(0,L.r)(()=>{this.ionRender.emit()})):this.prevPresentation=e}componentWillLoad(){const{el:e,formatOptions:n,highlightedDates:i,multiple:a,presentation:d,preferWheel:m}=this;a&&("date"!==d&&(0,T.p)('Multiple date selection is only supported for presentation="date".',e),m&&(0,T.p)('Multiple date selection is not supported with preferWheel="true".',e)),void 0!==i&&("date"!==d&&"date-time"!==d&&"time-date"!==d&&(0,T.p)("The highlightedDates property is only supported with the date, date-time, and time-date presentations.",e),m&&(0,T.p)('The highlightedDates property is not supported with preferWheel="true".',e)),n&&(ge(e,d,n),xe(e,n));const y=this.parsedHourValues=(0,u.t)(this.hourValues),f=this.parsedMinuteValues=(0,u.t)(this.minuteValues),b=this.parsedMonthValues=(0,u.t)(this.monthValues),x=this.parsedYearValues=(0,u.t)(this.yearValues),v=this.parsedDayValues=(0,u.t)(this.dayValues),I=this.todayParts=(0,u.s)((0,u.x)());this.processMinParts(),this.processMaxParts(),this.defaultParts=(0,u.y)({refParts:I,monthValues:b,dayValues:v,yearValues:x,hourValues:y,minuteValues:f,minParts:this.minParts,maxParts:this.maxParts}),this.processValue(this.value),this.emitStyle()}emitStyle(){this.ionStyle.emit({interactive:!0,datetime:!0,"interactive-disabled":this.disabled})}renderFooter(){const{disabled:e,readonly:n,showDefaultButtons:i,showClearButton:a}=this,d=e||n;if(null===this.el.querySelector('[slot="buttons"]')&&!i&&!a)return;const y=()=>{this.reset(),this.setValue(void 0)};return(0,s.h)("div",{class:"datetime-footer"},(0,s.h)("div",{class:"datetime-buttons"},(0,s.h)("div",{class:{"datetime-action-buttons":!0,"has-clear-button":this.showClearButton}},(0,s.h)("slot",{name:"buttons"},(0,s.h)("ion-buttons",null,i&&(0,s.h)("ion-button",{id:"cancel-button",color:this.color,onClick:()=>this.cancel(!0),disabled:d},this.cancelText),(0,s.h)("div",{class:"datetime-action-buttons-container"},a&&(0,s.h)("ion-button",{id:"clear-button",color:this.color,onClick:()=>y(),disabled:d},this.clearText),i&&(0,s.h)("ion-button",{id:"confirm-button",color:this.color,onClick:()=>this.confirm(!0),disabled:d},this.doneText)))))))}renderWheelPicker(e=this.presentation){const n="time-date"===e?[this.renderTimePickerColumns(e),this.renderDatePickerColumns(e)]:[this.renderDatePickerColumns(e),this.renderTimePickerColumns(e)];return(0,s.h)("ion-picker-internal",null,n)}renderDatePickerColumns(e){return"date-time"===e||"time-date"===e?this.renderCombinedDatePickerColumn():this.renderIndividualDatePickerColumns(e)}renderCombinedDatePickerColumn(){const{defaultParts:e,disabled:n,workingParts:i,locale:a,minParts:d,maxParts:m,todayParts:y,isDateEnabled:f}=this,b=this.getActivePartsWithFallback(),x=(0,u.z)(i),v=x[x.length-1];x[0].day=1,v.day=(0,u.A)(v.month,v.year);const I=void 0!==d&&(0,u.b)(d,x[0])?d:x[0],P=void 0!==m&&(0,u.i)(m,v)?m:v,E=(0,u.B)(a,y,I,P,this.parsedDayValues,this.parsedMonthValues);let S=E.items;const V=E.parts;return f&&(S=S.map((z,K)=>{const te=V[K];let q;try{q=!f((0,u.u)(te))}catch(Q){(0,T.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",Q)}return Object.assign(Object.assign({},z),{disabled:q})})),(0,s.h)("ion-picker-column-internal",{class:"date-column",color:this.color,disabled:n,items:S,value:null!==i.day?`${i.year}-${i.month}-${i.day}`:`${e.year}-${e.month}-${e.day}`,onIonChange:z=>{this.destroyCalendarListener&&this.destroyCalendarListener();const{value:K}=z.detail,te=V.find(({month:q,day:Q,year:oe})=>K===`${oe}-${q}-${Q}`);this.setWorkingParts(Object.assign(Object.assign({},i),te)),this.setActiveParts(Object.assign(Object.assign({},b),te)),this.initializeCalendarListener(),z.stopPropagation()}})}renderIndividualDatePickerColumns(e){const{workingParts:n,isDateEnabled:i}=this,d="year"!==e&&"time"!==e?(0,u.C)(this.locale,n,this.minParts,this.maxParts,this.parsedMonthValues):[];let y="date"===e?(0,u.D)(this.locale,n,this.minParts,this.maxParts,this.parsedDayValues):[];i&&(y=y.map(I=>{const{value:P}=I,E="string"==typeof P?parseInt(P):P,S={month:n.month,day:E,year:n.year};let V;try{V=!i((0,u.u)(S))}catch(F){(0,T.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",F)}return Object.assign(Object.assign({},I),{disabled:V})}));const b="month"!==e&&"time"!==e?(0,u.E)(this.locale,this.defaultParts,this.minParts,this.maxParts,this.parsedYearValues):[];let v=[];return v=(0,u.F)(this.locale,{month:"numeric",day:"numeric"})?[this.renderMonthPickerColumn(d),this.renderDayPickerColumn(y),this.renderYearPickerColumn(b)]:[this.renderDayPickerColumn(y),this.renderMonthPickerColumn(d),this.renderYearPickerColumn(b)],v}renderDayPickerColumn(e){var n;if(0===e.length)return[];const{disabled:i,workingParts:a}=this,d=this.getActivePartsWithFallback();return(0,s.h)("ion-picker-column-internal",{class:"day-column",color:this.color,disabled:i,items:e,value:null!==(n=null!==a.day?a.day:this.defaultParts.day)&&void 0!==n?n:void 0,onIonChange:m=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},a),{day:m.detail.value})),this.setActiveParts(Object.assign(Object.assign({},d),{day:m.detail.value})),this.initializeCalendarListener(),m.stopPropagation()}})}renderMonthPickerColumn(e){if(0===e.length)return[];const{disabled:n,workingParts:i}=this,a=this.getActivePartsWithFallback();return(0,s.h)("ion-picker-column-internal",{class:"month-column",color:this.color,disabled:n,items:e,value:i.month,onIonChange:d=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},i),{month:d.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{month:d.detail.value})),this.initializeCalendarListener(),d.stopPropagation()}})}renderYearPickerColumn(e){if(0===e.length)return[];const{disabled:n,workingParts:i}=this,a=this.getActivePartsWithFallback();return(0,s.h)("ion-picker-column-internal",{class:"year-column",color:this.color,disabled:n,items:e,value:i.year,onIonChange:d=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},i),{year:d.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{year:d.detail.value})),this.initializeCalendarListener(),d.stopPropagation()}})}renderTimePickerColumns(e){if(["date","month","month-year","year"].includes(e))return[];const i=void 0!==this.getActivePart(),{hoursData:a,minutesData:d,dayPeriodData:m}=(0,u.G)(this.locale,this.workingParts,this.hourCycle,i?this.minParts:void 0,i?this.maxParts:void 0,this.parsedHourValues,this.parsedMinuteValues);return[this.renderHourPickerColumn(a),this.renderMinutePickerColumn(d),this.renderDayPeriodPickerColumn(m)]}renderHourPickerColumn(e){const{disabled:n,workingParts:i}=this;if(0===e.length)return[];const a=this.getActivePartsWithFallback();return(0,s.h)("ion-picker-column-internal",{color:this.color,disabled:n,value:a.hour,items:e,numericInput:!0,onIonChange:d=>{this.setWorkingParts(Object.assign(Object.assign({},i),{hour:d.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{hour:d.detail.value})),d.stopPropagation()}})}renderMinutePickerColumn(e){const{disabled:n,workingParts:i}=this;if(0===e.length)return[];const a=this.getActivePartsWithFallback();return(0,s.h)("ion-picker-column-internal",{color:this.color,disabled:n,value:a.minute,items:e,numericInput:!0,onIonChange:d=>{this.setWorkingParts(Object.assign(Object.assign({},i),{minute:d.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{minute:d.detail.value})),d.stopPropagation()}})}renderDayPeriodPickerColumn(e){const{disabled:n,workingParts:i}=this;if(0===e.length)return[];const a=this.getActivePartsWithFallback(),d=(0,u.H)(this.locale);return(0,s.h)("ion-picker-column-internal",{style:d?{order:"-1"}:{},color:this.color,disabled:n,value:a.ampm,items:e,onIonChange:m=>{const y=(0,u.R)(i,m.detail.value);this.setWorkingParts(Object.assign(Object.assign({},i),{ampm:m.detail.value,hour:y})),this.setActiveParts(Object.assign(Object.assign({},a),{ampm:m.detail.value,hour:y})),m.stopPropagation()}})}renderWheelView(e){const{locale:n}=this,a=(0,u.F)(n)?"month-first":"year-first";return(0,s.h)("div",{class:{[`wheel-order-${a}`]:!0}},this.renderWheelPicker(e))}renderCalendarHeader(e){const{disabled:n}=this,i="ios"===e?j.l:j.p,a="ios"===e?j.o:j.q,d=n||((e,n,i)=>{const a=Object.assign(Object.assign({},(0,u.d)(this.workingParts)),{day:null});return X(a,{minParts:n,maxParts:i})})(0,this.minParts,this.maxParts),m=n||((e,n)=>{const i=Object.assign(Object.assign({},(0,u.e)(this.workingParts)),{day:null});return X(i,{maxParts:n})})(0,this.maxParts),y=this.el.getAttribute("dir")||void 0;return(0,s.h)("div",{class:"calendar-header"},(0,s.h)("div",{class:"calendar-action-buttons"},(0,s.h)("div",{class:"calendar-month-year"},(0,s.h)("ion-item",{part:"month-year-button",ref:f=>this.monthYearToggleItemRef=f,button:!0,"aria-label":"Show year picker",detail:!1,lines:"none",disabled:n,onClick:()=>{var f;this.toggleMonthAndYearView();const{monthYearToggleItemRef:b}=this;if(b){const x=null===(f=b.shadowRoot)||void 0===f?void 0:f.querySelector(".item-native");x&&x.setAttribute("aria-label",this.showMonthAndYear?"Hide year picker":"Show year picker")}}},(0,s.h)("ion-label",null,(0,u.J)(this.locale,this.workingParts),(0,s.h)("ion-icon",{"aria-hidden":"true",icon:this.showMonthAndYear?i:a,lazy:!1,flipRtl:!0})))),(0,s.h)("div",{class:"calendar-next-prev"},(0,s.h)("ion-buttons",null,(0,s.h)("ion-button",{"aria-label":"Previous month",disabled:d,onClick:()=>this.prevMonth()},(0,s.h)("ion-icon",{dir:y,"aria-hidden":"true",slot:"icon-only",icon:j.c,lazy:!1,flipRtl:!0})),(0,s.h)("ion-button",{"aria-label":"Next month",disabled:m,onClick:()=>this.nextMonth()},(0,s.h)("ion-icon",{dir:y,"aria-hidden":"true",slot:"icon-only",icon:j.o,lazy:!1,flipRtl:!0}))))),(0,s.h)("div",{class:"calendar-days-of-week","aria-hidden":"true"},(0,u.I)(this.locale,e,this.firstDayOfWeek%7).map(f=>(0,s.h)("div",{class:"day-of-week"},f))))}renderMonth(e,n){const{disabled:i,readonly:a}=this,d=void 0===this.parsedYearValues||this.parsedYearValues.includes(n),m=void 0===this.parsedMonthValues||this.parsedMonthValues.includes(e),y=!d||!m,f=i||a,b=i||X({month:e,year:n,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})}),x=this.workingParts.month===e&&this.workingParts.year===n,v=this.getActivePartsWithFallback();return(0,s.h)("div",{"aria-hidden":x?null:"true",class:{"calendar-month":!0,"calendar-month-disabled":!x&&b}},(0,s.h)("div",{class:"calendar-month-grid"},(0,u.K)(e,n,this.firstDayOfWeek%7).map((I,P)=>{const{day:E,dayOfWeek:S}=I,{el:V,highlightedDates:F,isDateEnabled:z,multiple:K}=this,te={month:e,day:E,year:n},q=null===E,{isActive:Q,isToday:oe,ariaLabel:ue,ariaSelected:Ve,disabled:_e,text:ze}=((e,n,i,a,d,m,y)=>{const b=void 0!==(Array.isArray(i)?i:[i]).find(I=>(0,u.c)(n,I)),x=(0,u.c)(n,a);return{disabled:de(n,d,m,y),isActive:b,isToday:x,ariaSelected:b?"true":null,ariaLabel:(0,u.g)(e,x,n),text:null!=n.day?(0,u.a)(e,n):null}})(this.locale,te,this.activeParts,this.todayParts,this.minParts,this.maxParts,this.parsedDayValues),Oe=(0,u.u)(te);let t=y||_e;if(!t&&void 0!==z)try{t=!z(Oe)}catch(g){(0,T.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",V,g)}const o=t&&f,r=t||f;let l,h;return void 0!==F&&!Q&&null!==E&&(l=((e,n,i)=>{if(Array.isArray(e)){const a=n.split("T")[0],d=e.find(m=>m.date===a);if(d)return{textColor:d.textColor,backgroundColor:d.backgroundColor}}else try{return e(n)}catch(a){(0,T.a)("Exception thrown from provided `highlightedDates` callback. Please check your function and try again.",i,a)}})(F,Oe,V)),q||(h=`calendar-day${Q?" active":""}${oe?" today":""}${t?" disabled":""}`),(0,s.h)("div",{class:"calendar-day-wrapper"},(0,s.h)("button",{ref:g=>{g&&(g.style.setProperty("color",`${l?l.textColor:""}`,"important"),g.style.setProperty("background-color",`${l?l.backgroundColor:""}`,"important"))},tabindex:"-1","data-day":E,"data-month":e,"data-year":n,"data-index":P,"data-day-of-week":S,disabled:r,class:{"calendar-day-padding":q,"calendar-day":!0,"calendar-day-active":Q,"calendar-day-constrained":o,"calendar-day-today":oe},part:h,"aria-hidden":q?"true":null,"aria-selected":Ve,"aria-label":ue,onClick:()=>{q||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:e,day:E,year:n})),K?this.setActiveParts({month:e,day:E,year:n},Q):this.setActiveParts(Object.assign(Object.assign({},v),{month:e,day:E,year:n})))}},ze))})))}renderCalendarBody(){return(0,s.h)("div",{class:"calendar-body ion-focusable",ref:e=>this.calendarBodyRef=e,tabindex:"0"},(0,u.z)(this.workingParts,this.forceRenderDate).map(({month:e,year:n})=>this.renderMonth(e,n)))}renderCalendar(e){return(0,s.h)("div",{class:"datetime-calendar",key:"datetime-calendar"},this.renderCalendarHeader(e),this.renderCalendarBody())}renderTimeLabel(){if(null!==this.el.querySelector('[slot="time-label"]')||this.showDefaultTimeLabel)return(0,s.h)("slot",{name:"time-label"},"Time")}renderTimeOverlay(){var e=this;const{disabled:n,hourCycle:i,isTimePopoverOpen:a,locale:d,formatOptions:m}=this,y=(0,u.L)(d,i),f=this.getActivePartsWithFallback();return[(0,s.h)("div",{class:"time-header"},this.renderTimeLabel()),(0,s.h)("button",{class:{"time-body":!0,"time-body-active":a},part:"time-button"+(a?" active":""),"aria-expanded":"false","aria-haspopup":"true",disabled:n,onClick:(b=(0,A.Z)(function*(x){const{popoverRef:v}=e;v&&(e.isTimePopoverOpen=!0,v.present(new CustomEvent("ionShadowTarget",{detail:{ionShadowTarget:x.target}})),yield v.onWillDismiss(),e.isTimePopoverOpen=!1)}),function(v){return b.apply(this,arguments)})},(0,u.M)(d,f,y,m?.time)),(0,s.h)("ion-popover",{alignment:"center",translucent:!0,overlayIndex:1,arrow:!1,onWillPresent:b=>{b.target.querySelectorAll("ion-picker-column-internal").forEach(v=>v.scrollActiveItemIntoView())},style:{"--offset-y":"-10px","--min-width":"fit-content"},keyboardEvents:!0,ref:b=>this.popoverRef=b},this.renderWheelPicker("time"))];var b}getHeaderSelectedDateText(){var e;const{activeParts:n,formatOptions:i,multiple:a,titleSelectedDatesFormatter:d}=this,m=Array.isArray(n);let y;if(a&&m&&1!==n.length){if(y=`${n.length} days`,void 0!==d)try{y=d((0,u.u)(n))}catch(f){(0,T.a)("Exception in provided `titleSelectedDatesFormatter`: ",f)}}else y=(0,u.N)(this.locale,this.getActivePartsWithFallback(),null!==(e=i?.date)&&void 0!==e?e:{weekday:"short",month:"short",day:"numeric"});return y}renderHeader(e=!0){if(null!==this.el.querySelector('[slot="title"]')||this.showDefaultTitle)return(0,s.h)("div",{class:"datetime-header"},(0,s.h)("div",{class:"datetime-title"},(0,s.h)("slot",{name:"title"},"Select Date")),e&&(0,s.h)("div",{class:"datetime-selected-date"},this.getHeaderSelectedDateText()))}renderTime(){const{presentation:e}=this;return(0,s.h)("div",{class:"datetime-time"},"time"===e?this.renderWheelPicker():this.renderTimeOverlay())}renderCalendarViewMonthYearPicker(){return(0,s.h)("div",{class:"datetime-year"},this.renderWheelView("month-year"))}renderDatetime(e){const{presentation:n,preferWheel:i}=this;if(i&&("date"===n||"date-time"===n||"time-date"===n))return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];switch(n){case"date-time":return[this.renderHeader(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderTime(),this.renderFooter()];case"time-date":return[this.renderHeader(),this.renderTime(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderFooter()];case"time":return[this.renderHeader(!1),this.renderTime(),this.renderFooter()];case"month":case"month-year":case"year":return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];default:return[this.renderHeader(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderFooter()]}}render(){const{name:e,value:n,disabled:i,el:a,color:d,readonly:m,showMonthAndYear:y,preferWheel:f,presentation:b,size:x,isGridStyle:v}=this,I=(0,W.b)(this),P="year"===b||"month"===b||"month-year"===b,E=y||P,S=y&&!P,F=("date"===b||"date-time"===b||"time-date"===b)&&f;return(0,L.d)(!0,a,e,(0,u.O)(n),i),(0,s.h)(s.H,{key:"8490192beb6c5c6064ed8f2a7be2d51846f84f36","aria-disabled":i?"true":null,onFocus:this.onFocus,onBlur:this.onBlur,class:Object.assign({},(0,$.c)(d,{[I]:!0,"datetime-readonly":m,"datetime-disabled":i,"show-month-and-year":E,"month-year-picker-open":S,[`datetime-presentation-${b}`]:!0,[`datetime-size-${x}`]:!0,"datetime-prefer-wheel":F,"datetime-grid":v}))},(0,s.h)("div",{key:"a2959c07ed871f9004a2f11ab1385a5a7b5737fd",class:"intersection-tracker",ref:z=>this.intersectionTrackerRef=z}),this.renderDatetime(I))}get el(){return(0,s.f)(this)}static get watchers(){return{formatOptions:["formatOptionsChanged"],disabled:["disabledChanged"],min:["minChanged"],max:["maxChanged"],presentation:["presentationChanged"],yearValues:["yearValuesChanged"],monthValues:["monthValuesChanged"],dayValues:["dayValuesChanged"],hourValues:["hourValuesChanged"],minuteValues:["minuteValuesChanged"],value:["valueChanged"]}}};let ye=0;le.style={ios:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-light, #ffffff);--background-rgb:var(--ion-color-light-rgb);--title-color:var(--ion-color-step-600, #666666)}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, #cccccc);font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}:host .calendar-action-buttons ion-item{--padding-start:16px;--background-hover:transparent;--background-activated:transparent;font-size:min(1rem, 25.6px);font-weight:600}:host .calendar-action-buttons ion-item ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, #b3b3b3);font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, #cccccc)}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-step-100, #ffffff);--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .datetime-calendar .calendar-action-buttons ion-item{--padding-start:20px}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, #595959)}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, gray);font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, #595959)}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}"};const ce=e=>{const n=(0,Z.c)(),i=(0,Z.c)(),a=(0,Z.c)();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),a.addElement(e.querySelector(".picker-wrapper")).fromTo("transform","translateY(100%)","translateY(0%)"),n.addElement(e).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},se=e=>{const n=(0,Z.c)(),i=(0,Z.c)(),a=(0,Z.c)();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",.01),a.addElement(e.querySelector(".picker-wrapper")).fromTo("transform","translateY(0%)","translateY(100%)"),n.addElement(e).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},we=class{constructor(e){(0,s.r)(this,e),this.didPresent=(0,s.d)(this,"ionPickerDidPresent",7),this.willPresent=(0,s.d)(this,"ionPickerWillPresent",7),this.willDismiss=(0,s.d)(this,"ionPickerWillDismiss",7),this.didDismiss=(0,s.d)(this,"ionPickerDidDismiss",7),this.didPresentShorthand=(0,s.d)(this,"didPresent",7),this.willPresentShorthand=(0,s.d)(this,"willPresent",7),this.willDismissShorthand=(0,s.d)(this,"willDismiss",7),this.didDismissShorthand=(0,s.d)(this,"didDismiss",7),this.delegateController=(0,_.d)(this),this.lockController=(0,C.c)(),this.triggerController=(0,_.e)(),this.onBackdropTap=()=>{this.dismiss(void 0,_.B)},this.dispatchCancelHandler=n=>{if((0,_.i)(n.detail.role)){const a=this.buttons.find(d=>"cancel"===d.role);this.callButtonHandler(a)}},this.presented=!1,this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.buttons=[],this.columns=[],this.cssClass=void 0,this.duration=0,this.showBackdrop=!0,this.backdropDismiss=!0,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(e,n){!0===e&&!1===n?this.present():!1===e&&!0===n&&this.dismiss()}triggerChanged(){const{trigger:e,el:n,triggerController:i}=this;e&&i.addClickListener(n,e)}connectedCallback(){(0,_.j)(this.el),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){(0,_.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,L.r)(()=>this.present()),this.triggerChanged()}present(){var e=this;return(0,A.Z)(function*(){const n=yield e.lockController.lock();yield e.delegateController.attachViewToDom(),yield(0,_.f)(e,"pickerEnter",ce,ce,void 0),e.duration>0&&(e.durationTimeout=setTimeout(()=>e.dismiss(),e.duration)),n()})()}dismiss(e,n){var i=this;return(0,A.Z)(function*(){const a=yield i.lockController.lock();i.durationTimeout&&clearTimeout(i.durationTimeout);const d=yield(0,_.g)(i,e,n,"pickerLeave",se,se);return d&&i.delegateController.removeViewFromDom(),a(),d})()}onDidDismiss(){return(0,_.h)(this.el,"ionPickerDidDismiss")}onWillDismiss(){return(0,_.h)(this.el,"ionPickerWillDismiss")}getColumn(e){return Promise.resolve(this.columns.find(n=>n.name===e))}buttonClick(e){var n=this;return(0,A.Z)(function*(){const i=e.role;return(0,_.i)(i)?n.dismiss(void 0,i):(yield n.callButtonHandler(e))?n.dismiss(n.getSelected(),e.role):Promise.resolve()})()}callButtonHandler(e){var n=this;return(0,A.Z)(function*(){return!(e&&!1===(yield(0,_.s)(e.handler,n.getSelected())))})()}getSelected(){const e={};return this.columns.forEach((n,i)=>{const a=void 0!==n.selectedIndex?n.options[n.selectedIndex]:void 0;e[n.name]={text:a?a.text:void 0,value:a?a.value:void 0,columnIndex:i}}),e}render(){const{htmlAttributes:e}=this,n=(0,W.b)(this);return(0,s.h)(s.H,Object.assign({key:"eb5f91ea74fb11daa6942f779ef461742cad9ecb","aria-modal":"true",tabindex:"-1"},e,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[n]:!0,[`picker-${n}`]:!0,"overlay-hidden":!0},(0,$.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonPickerWillDismiss:this.dispatchCancelHandler}),(0,s.h)("ion-backdrop",{key:"7ea872d939e62f14129fff15334b2822ad2360c9",visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,s.h)("div",{key:"2d77c225091eacab0207e28c96b966122afafef0",tabindex:"0"}),(0,s.h)("div",{key:"630d21e0c60ad97b71462cdc540858bb6ced0b8f",class:"picker-wrapper ion-overlay-wrapper",role:"dialog"},(0,s.h)("div",{key:"fa8553ec8d2ce8bf93e16e02334b6475cb51b5d4",class:"picker-toolbar"},this.buttons.map(i=>(0,s.h)("div",{class:Se(i)},(0,s.h)("button",{type:"button",onClick:()=>this.buttonClick(i),class:Le(i)},i.text)))),(0,s.h)("div",{key:"177d1bcbd0ce38f16d9c936295a917fb981d02d7",class:"picker-columns"},(0,s.h)("div",{key:"be99b6e0279c210ef91a88ccc81acc7d37917a53",class:"picker-above-highlight"}),this.presented&&this.columns.map(i=>(0,s.h)("ion-picker-column",{col:i})),(0,s.h)("div",{key:"b36b21e8133b59e873e1d3447a1279f1b971c854",class:"picker-below-highlight"}))),(0,s.h)("div",{key:"17cea6dd24dbb0a08073ca4a84bfe027eb24833d",tabindex:"0"}))}get el(){return(0,s.f)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Se=e=>({[`picker-toolbar-${e.role}`]:void 0!==e.role,"picker-toolbar-button":!0}),Le=e=>Object.assign({"picker-button":!0,"ion-activatable":!0},(0,$.g)(e.cssClass));we.style={ios:".sc-ion-picker-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-ios-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-ios-h{left:0}[dir=rtl].sc-ion-picker-ios-h,[dir=rtl] .sc-ion-picker-ios-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-ios-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-ios-h{display:none}.picker-wrapper.sc-ion-picker-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-ios:active,.picker-button.sc-ion-picker-ios:focus{outline:none}.picker-columns.sc-ion-picker-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-ios,.picker-below-highlight.sc-ion-picker-ios{display:none;pointer-events:none}.sc-ion-picker-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-ios:last-child .picker-button.sc-ion-picker-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:16px}.picker-columns.sc-ion-picker-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}",md:".sc-ion-picker-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-md-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-md-h{left:0}[dir=rtl].sc-ion-picker-md-h,[dir=rtl] .sc-ion-picker-md-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-md-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-md-h{display:none}.picker-wrapper.sc-ion-picker-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-md:active,.picker-button.sc-ion-picker-md:focus{outline:none}.picker-columns.sc-ion-picker-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-md,.picker-below-highlight.sc-ion-picker-md{display:none;pointer-events:none}.sc-ion-picker-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}"};const be=class{constructor(e){(0,s.r)(this,e),this.ionPickerColChange=(0,s.d)(this,"ionPickerColChange",7),this.optHeight=0,this.rotateFactor=0,this.scaleFactor=1,this.velocity=0,this.y=0,this.noAnimate=!0,this.colDidChange=!1,this.col=void 0}colChanged(){this.colDidChange=!0}connectedCallback(){var e=this;return(0,A.Z)(function*(){let n=0,i=.81;"ios"===(0,W.b)(e)&&(n=-.46,i=1),e.rotateFactor=n,e.scaleFactor=i,e.gesture=(yield Promise.resolve().then(D.bind(D,35067))).createGesture({el:e.el,gestureName:"picker-swipe",gesturePriority:100,threshold:0,passive:!1,onStart:d=>e.onStart(d),onMove:d=>e.onMove(d),onEnd:d=>e.onEnd(d)}),e.gesture.enable(),e.tmrId=setTimeout(()=>{e.noAnimate=!1,e.refresh(!0)},250)})()}componentDidLoad(){this.onDomChange()}componentDidUpdate(){this.colDidChange&&(this.onDomChange(!0,!1),this.colDidChange=!1)}disconnectedCallback(){void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.tmrId&&clearTimeout(this.tmrId),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}emitColChange(){this.ionPickerColChange.emit(this.col)}setSelected(e,n){const i=e>-1?-e*this.optHeight:0;this.velocity=0,void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.update(i,n,!0),this.emitColChange()}update(e,n,i){if(!this.optsEl)return;let a=0,d=0;const{col:m,rotateFactor:y}=this,f=m.selectedIndex,b=m.selectedIndex=this.indexForY(-e),x=0===n?"":n+"ms",v=`scale(${this.scaleFactor})`,I=this.optsEl.children;for(let P=0;P<I.length;P++){const E=I[P],S=m.options[P],V=P*this.optHeight+e;let F="";if(0!==y){const K=V*y;Math.abs(K)<=90?(a=0,d=90,F=`rotateX(${K}deg) `):a=-9999}else d=0,a=V;const z=b===P;F+=`translate3d(0px,${a}px,${d}px) `,1!==this.scaleFactor&&!z&&(F+=v),this.noAnimate?(S.duration=0,E.style.transitionDuration=""):n!==S.duration&&(S.duration=n,E.style.transitionDuration=x),F!==S.transform&&(S.transform=F),E.style.transform=F,S.selected=z,z?E.classList.add(Ce):E.classList.remove(Ce)}this.col.prevSelected=f,i&&(this.y=e),this.lastIndex!==b&&((0,ie.b)(),this.lastIndex=b)}decelerate(){if(0!==this.velocity){this.velocity*=We,this.velocity=this.velocity>0?Math.max(this.velocity,1):Math.min(this.velocity,-1);let e=this.y+this.velocity;e>this.minY?(e=this.minY,this.velocity=0):e<this.maxY&&(e=this.maxY,this.velocity=0),this.update(e,0,!0),Math.round(e)%this.optHeight!=0||Math.abs(this.velocity)>1?this.rafId=requestAnimationFrame(()=>this.decelerate()):(this.velocity=0,this.emitColChange(),(0,ie.h)())}else if(this.y%this.optHeight!=0){const e=Math.abs(this.y%this.optHeight);this.velocity=e>this.optHeight/2?1:-1,this.decelerate()}}indexForY(e){return Math.min(Math.max(Math.abs(Math.round(e/this.optHeight)),0),this.col.options.length-1)}onStart(e){e.event.cancelable&&e.event.preventDefault(),e.event.stopPropagation(),(0,ie.a)(),void 0!==this.rafId&&cancelAnimationFrame(this.rafId);const n=this.col.options;let i=n.length-1,a=0;for(let d=0;d<n.length;d++)n[d].disabled||(i=Math.min(i,d),a=Math.max(a,d));this.minY=-i*this.optHeight,this.maxY=-a*this.optHeight}onMove(e){e.event.cancelable&&e.event.preventDefault(),e.event.stopPropagation();let n=this.y+e.deltaY;n>this.minY?(n=Math.pow(n,.8),this.bounceFrom=n):n<this.maxY?(n+=Math.pow(this.maxY-n,.9),this.bounceFrom=n):this.bounceFrom=0,this.update(n,0,!1)}onEnd(e){if(this.bounceFrom>0)return this.update(this.minY,100,!0),void this.emitColChange();if(this.bounceFrom<0)return this.update(this.maxY,100,!0),void this.emitColChange();if(this.velocity=(0,L.l)(-De,23*e.velocityY,De),0===this.velocity&&0===e.deltaY){const n=e.event.target.closest(".picker-opt");n?.hasAttribute("opt-index")&&this.setSelected(parseInt(n.getAttribute("opt-index"),10),Me)}else{if(this.y+=e.deltaY,Math.abs(e.velocityY)<.05){const n=e.deltaY>0,i=Math.abs(this.y)%this.optHeight/this.optHeight;n&&i>.5?this.velocity=-1*Math.abs(this.velocity):!n&&i<=.5&&(this.velocity=Math.abs(this.velocity))}this.decelerate()}}refresh(e,n){var i;let a=this.col.options.length-1,d=0;const m=this.col.options;for(let f=0;f<m.length;f++)m[f].disabled||(a=Math.min(a,f),d=Math.max(d,f));if(0!==this.velocity)return;const y=(0,L.l)(a,null!==(i=this.col.selectedIndex)&&void 0!==i?i:0,d);if(this.col.prevSelected!==y||e){const f=y*this.optHeight*-1,b=n?Me:0;this.velocity=0,this.update(f,b,!0)}}onDomChange(e,n){const i=this.optsEl;i&&(this.optHeight=i.firstElementChild?i.firstElementChild.clientHeight:0),this.refresh(e,n)}render(){const e=this.col,n=(0,W.b)(this);return(0,s.h)(s.H,{key:"49bb4c67a67c7318d4c305df78ceabae36355112",class:Object.assign({[n]:!0,"picker-col":!0,"picker-opts-left":"left"===this.col.align,"picker-opts-right":"right"===this.col.align},(0,$.g)(e.cssClass)),style:{"max-width":this.col.columnWidth}},e.prefix&&(0,s.h)("div",{key:"7e65761d24473e4ba0ce2d4fc707a5c5e8127903",class:"picker-prefix",style:{width:e.prefixWidth}},e.prefix),(0,s.h)("div",{key:"65c3aea609401e8ae4ea6d363a1b9436796c0a86",class:"picker-opts",style:{maxWidth:e.optionsWidth},ref:i=>this.optsEl=i},e.options.map((i,a)=>(0,s.h)("button",{"aria-label":i.ariaLabel,class:{"picker-opt":!0,"picker-opt-disabled":!!i.disabled},"opt-index":a},i.text))),e.suffix&&(0,s.h)("div",{key:"c2e5a324ba95dd8832d3eb81b139e1f674d74a35",class:"picker-suffix",style:{width:e.suffixWidth}},e.suffix))}get el(){return(0,s.f)(this)}static get watchers(){return{col:["colChanged"]}}},Ce="picker-opt-selected",We=.97,De=90,Me=150;be.style={ios:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}",md:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #3880ff)}"}},37389:(re,U,D)=>{D.d(U,{c:()=>s});var A=D(15861);const s=()=>{let G;return{lock:function(){var T=(0,A.Z)(function*(){const N=G;let $;return G=new Promise(j=>$=j),void 0!==N&&(yield N),$});return function(){return T.apply(this,arguments)}}()}}}}]);