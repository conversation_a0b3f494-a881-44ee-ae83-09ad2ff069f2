(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4059],{86437:(ee,L,g)=>{g.d(L,{f:()=>I});var b=g(97582),p=64,R=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),v=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],O=Math.pow(2,53)-1,y=function(){function E(){this.state=Int32Array.from(v),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return E.prototype.update=function(h){if(this.finished)throw new Error("Attempted to update an already finished hash.");var T=0,C=h.byteLength;if(this.bytesHashed+=C,8*this.bytesHashed>O)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;C>0;)this.buffer[this.bufferLength++]=h[T++],C--,this.bufferLength===p&&(this.hashBuffer(),this.bufferLength=0)},E.prototype.digest=function(){if(!this.finished){var h=8*this.bytesHashed,T=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),C=this.bufferLength;if(T.setUint8(this.bufferLength++,128),C%p>=56){for(var G=this.bufferLength;G<p;G++)T.setUint8(G,0);this.hashBuffer(),this.bufferLength=0}for(G=this.bufferLength;G<56;G++)T.setUint8(G,0);T.setUint32(56,Math.floor(h/4294967296),!0),T.setUint32(60,h),this.hashBuffer(),this.finished=!0}var J=new Uint8Array(32);for(G=0;G<8;G++)J[4*G]=this.state[G]>>>24&255,J[4*G+1]=this.state[G]>>>16&255,J[4*G+2]=this.state[G]>>>8&255,J[4*G+3]=this.state[G]>>>0&255;return J},E.prototype.hashBuffer=function(){for(var T=this.buffer,C=this.state,G=C[0],J=C[1],ae=C[2],re=C[3],V=C[4],me=C[5],pe=C[6],le=C[7],Y=0;Y<p;Y++){if(Y<16)this.temp[Y]=(255&T[4*Y])<<24|(255&T[4*Y+1])<<16|(255&T[4*Y+2])<<8|255&T[4*Y+3];else{var oe=this.temp[Y-2];this.temp[Y]=(((oe>>>17|oe<<15)^(oe>>>19|oe<<13)^oe>>>10)+this.temp[Y-7]|0)+((((oe=this.temp[Y-15])>>>7|oe<<25)^(oe>>>18|oe<<14)^oe>>>3)+this.temp[Y-16]|0)}var Ne=(((V>>>6|V<<26)^(V>>>11|V<<21)^(V>>>25|V<<7))+(V&me^~V&pe)|0)+(le+(R[Y]+this.temp[Y]|0)|0)|0,we=((G>>>2|G<<30)^(G>>>13|G<<19)^(G>>>22|G<<10))+(G&J^G&ae^J&ae)|0;le=pe,pe=me,me=V,V=re+Ne|0,re=ae,ae=J,J=G,G=Ne+we|0}C[0]+=G,C[1]+=J,C[2]+=ae,C[3]+=re,C[4]+=V,C[5]+=me,C[6]+=pe,C[7]+=le},E}(),j=typeof Buffer<"u"&&Buffer.from?function(E){return Buffer.from(E,"utf8")}:E=>(new TextEncoder).encode(E);function P(E){return E instanceof Uint8Array?E:"string"==typeof E?j(E):ArrayBuffer.isView(E)?new Uint8Array(E.buffer,E.byteOffset,E.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(E)}var I=function(){function E(h){this.secret=h,this.hash=new y,this.reset()}return E.prototype.update=function(h){if(!function k(E){return"string"==typeof E?0===E.length:0===E.byteLength}(h)&&!this.error)try{this.hash.update(P(h))}catch(T){this.error=T}},E.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},E.prototype.digest=function(){return(0,b.mG)(this,void 0,void 0,function(){return(0,b.Jh)(this,function(h){return[2,this.digestSync()]})})},E.prototype.reset=function(){if(this.hash=new y,this.secret){this.outer=new y;var h=function N(E){var h=P(E);if(h.byteLength>p){var T=new y;T.update(h),h=T.digest()}var C=new Uint8Array(p);return C.set(h),C}(this.secret),T=new Uint8Array(p);T.set(h);for(var C=0;C<p;C++)h[C]^=54,T[C]^=92;for(this.hash.update(h),this.outer.update(T),C=0;C<h.byteLength;C++)h[C]=0}},E}()},95472:(ee,L,g)=>{g.d(L,{N:()=>R});const b={},p={};for(let v=0;v<256;v++){let O=v.toString(16).toLowerCase();1===O.length&&(O=`0${O}`),b[v]=O,p[O]=v}function R(v){let O="";for(let y=0;y<v.byteLength;y++)O+=b[v[y]];return O}},34059:(ee,L,g)=>{g.r(L),g.d(L,{CONNECTION_STATE_CHANGE:()=>j,CONTROL_MSG:()=>de,ConnectionState:()=>X,GraphQLAPI:()=>vr,GraphQLAPIClass:()=>Xt,GraphQLAuthError:()=>Oe,__amplify:()=>Ct,__apiKey:()=>wt,__authMode:()=>Ot,__authToken:()=>Nt,__endpoint:()=>Pt,__headers:()=>Rt,events:()=>b,getInternals:()=>kn,graphqlOperation:()=>Sr});var b={};g.r(b),g.d(b,{closeAll:()=>Wn,connect:()=>Qn,post:()=>Bn});var p=g(15861),x=g(98778),R=g(92261),v=g(5919);const y=[400,401,403],M=["BadRequestException","UnauthorizedException"],j="ConnectionStateChange";var P=(()=>{return(e=P||(P={})).GQL_CONNECTION_INIT="connection_init",e.GQL_CONNECTION_ERROR="connection_error",e.GQL_CONNECTION_ACK="connection_ack",e.GQL_START="start",e.GQL_START_ACK="start_ack",e.DATA="data",e.GQL_CONNECTION_KEEP_ALIVE="ka",e.GQL_STOP="stop",e.GQL_COMPLETE="complete",e.GQL_ERROR="error",e.EVENT_SUBSCRIBE="subscribe",e.EVENT_PUBLISH="publish",e.EVENT_SUBSCRIBE_ACK="subscribe_success",e.EVENT_PUBLISH_ACK="publish_success",e.EVENT_STOP="unsubscribe",e.EVENT_COMPLETE="unsubscribe_success",P;var e})(),k=(()=>{return(e=k||(k={}))[e.PENDING=0]="PENDING",e[e.CONNECTED=1]="CONNECTED",e[e.FAILED=2]="FAILED",k;var e})(),I=(()=>{return(e=I||(I={}))[e.CLOSED=0]="CLOSED",e[e.READY=1]="READY",e[e.CONNECTING=2]="CONNECTING",I;var e})();const N={accept:"application/json, text/javascript","content-encoding":"amz-1.0","content-type":"application/json; charset=UTF-8"},T=3e5;var re=g(42168),V=g(54646),me=g(23192);class pe extends Error{constructor(){super(...arguments),this.nonRetryable=!0}}var le=g(38261);const Y=e=>e&&e.nonRetryable;var oe=g(10180);const Ee={convert(e,t={urlSafe:!1,skipPadding:!1}){const n="string"==typeof e?e:function be(e){return Array.from(e,t=>String.fromCodePoint(t)).join("")}(e);let r=(0,oe.Ds)()(n);return t.urlSafe&&(r=r.replace(/\+/g,"-").replace(/\//g,"_")),t.skipPadding&&(r=r.replace(/=/g,"")),r}};var Ne=g(90206),we=g(75599);const Re=new(g(55502).k)("retryUtil");function ne(){return ne=(0,p.Z)(function*(e,t,n,r){if("function"!=typeof e)throw Error("functionToRetry must be a function");return new Promise(function(){var o=(0,p.Z)(function*(s,i){let c,f,a=0,l=!1,u=()=>{};for(r&&r.then(()=>{l=!0,clearTimeout(c),u()});!l;){a++,Re.debug(`${e.name} attempt #${a} with this vars: ${JSON.stringify(t)}`);try{return void s(yield e(...t))}catch(d){if(f=d,Re.debug(`error on ${e.name}`,d),Y(d))return Re.debug(`${e.name} non retryable error`,d),void i(d);const A=n(a,t,d);if(Re.debug(`${e.name} retrying in ${A} ms`),!1===A||l)return void i(d);yield new Promise(_=>{u=_,c=setTimeout(u,A)})}}i(f)});return function(s,i){return o.apply(this,arguments)}}())}),ne.apply(this,arguments)}const _e=(e,t,n=Ne.t,r)=>function Ie(e,t,n,r){return ne.apply(this,arguments)}(e,t,(0,we.k)(n),r);var de=(()=>{return(e=de||(de={})).CONNECTION_CLOSED="Connection closed",e.CONNECTION_FAILED="Connection failed",e.REALTIME_SUBSCRIPTION_INIT_ERROR="AppSync Realtime subscription init error",e.SUBSCRIPTION_ACK="Subscription ack",e.TIMEOUT_DISCONNECT="Timeout disconnect",de;var e})(),X=(()=>{return(e=X||(X={})).Connected="Connected",e.ConnectedPendingNetwork="ConnectedPendingNetwork",e.ConnectionDisrupted="ConnectionDisrupted",e.ConnectionDisruptedPendingNetwork="ConnectionDisruptedPendingNetwork",e.Connecting="Connecting",e.ConnectedPendingDisconnect="ConnectedPendingDisconnect",e.Disconnected="Disconnected",e.ConnectedPendingKeepAlive="ConnectedPendingKeepAlive",X;var e})();let H=(()=>{class e{networkMonitor(n){const r=(()=>{if(typeof self>"u")return!1;const e=self;return typeof e.WorkerGlobalScope<"u"&&self instanceof e.WorkerGlobalScope})()?self:typeof window<"u"&&window;return r?new re.Observable(o=>{o.next({online:r.navigator.onLine});const s=()=>{o.next({online:!0})},i=()=>{o.next({online:!1})};return r.addEventListener("online",s),r.addEventListener("offline",i),e._observers.push(o),()=>{r.removeEventListener("online",s),r.removeEventListener("offline",i),e._observers=e._observers.filter(a=>a!==o)}}):(0,re.from)([{online:!0}])}static _observerOverride(n){for(const r of this._observers)r.closed?this._observers=this._observers.filter(o=>o!==r):r?.next&&r.next(n)}}return e._observers=[],e})();const K=()=>(new H).networkMonitor(),Z={KEEP_ALIVE_MISSED:{keepAliveState:"unhealthy"},KEEP_ALIVE:{keepAliveState:"healthy"},CONNECTION_ESTABLISHED:{connectionState:"connected"},CONNECTION_FAILED:{intendedConnectionState:"disconnected",connectionState:"disconnected"},CLOSING_CONNECTION:{intendedConnectionState:"disconnected"},OPENING_CONNECTION:{intendedConnectionState:"connected",connectionState:"connecting"},CLOSED:{connectionState:"disconnected"},ONLINE:{networkState:"connected"},OFFLINE:{networkState:"disconnected"}};class ye{constructor(){this._networkMonitoringSubscription=void 0,this._linkedConnectionState={networkState:"connected",connectionState:"disconnected",intendedConnectionState:"disconnected",keepAliveState:"healthy"},this._initialNetworkStateSubscription=K().subscribe(({online:t})=>{this.record(t?Z.ONLINE:Z.OFFLINE),this._initialNetworkStateSubscription?.unsubscribe()}),this._linkedConnectionStateObservable=new re.Observable(t=>{t.next(this._linkedConnectionState),this._linkedConnectionStateObserver=t})}enableNetworkMonitoring(){this._initialNetworkStateSubscription?.unsubscribe(),void 0===this._networkMonitoringSubscription&&(this._networkMonitoringSubscription=K().subscribe(({online:t})=>{this.record(t?Z.ONLINE:Z.OFFLINE)}))}disableNetworkMonitoring(){this._networkMonitoringSubscription?.unsubscribe(),this._networkMonitoringSubscription=void 0}get connectionStateObservable(){let t;return this._linkedConnectionStateObservable.pipe((0,re.map)(n=>this.connectionStatesTranslator(n))).pipe((0,re.filter)(n=>{const r=n!==t;return t=n,r}))}record(t){"connected"===t.intendedConnectionState?this.enableNetworkMonitoring():"disconnected"===t.intendedConnectionState&&this.disableNetworkMonitoring();const n={...this._linkedConnectionState,...t};this._linkedConnectionState={...n},this._linkedConnectionStateObserver?.next(this._linkedConnectionState)}connectionStatesTranslator({connectionState:t,networkState:n,intendedConnectionState:r,keepAliveState:o}){return"connected"===t&&"disconnected"===n?X.ConnectedPendingNetwork:"connected"===t&&"disconnected"===r?X.ConnectedPendingDisconnect:"disconnected"===t&&"connected"===r&&"disconnected"===n?X.ConnectionDisruptedPendingNetwork:"disconnected"===t&&"connected"===r?X.ConnectionDisrupted:"connected"===t&&"unhealthy"===o?X.ConnectedPendingKeepAlive:"connecting"===t?X.Connecting:"disconnected"===t?X.Disconnected:X.Connected}}var he=(()=>{return(e=he||(he={})).START_RECONNECT="START_RECONNECT",e.HALT_RECONNECT="HALT_RECONNECT",he;var e})();class Se{constructor(){this.reconnectObservers=[]}addObserver(t){this.reconnectObservers.push(t)}record(t){t===he.START_RECONNECT&&void 0===this.reconnectSetTimeoutId&&void 0===this.reconnectIntervalId&&(this.reconnectSetTimeoutId=setTimeout(()=>{this._triggerReconnect(),this.reconnectIntervalId=setInterval(()=>{this._triggerReconnect()},6e4)},5e3)),t===he.HALT_RECONNECT&&(this.reconnectIntervalId&&(clearInterval(this.reconnectIntervalId),this.reconnectIntervalId=void 0),this.reconnectSetTimeoutId&&(clearTimeout(this.reconnectSetTimeoutId),this.reconnectSetTimeoutId=void 0))}close(){this.reconnectObservers.forEach(t=>{t.complete?.()})}_triggerReconnect(){this.reconnectObservers.forEach(t=>{t.next?.()})}}var se=g(99120);const Pe=/^https:\/\/\w{26}\.appsync-api\.\w{2}(?:(?:-\w{2,})+)-\d\.amazonaws.com(?:\.cn)?\/graphql$/i,Me=/^https:\/\/\w{26}\.\w+-api\.\w{2}(?:(?:-\w{2,})+)-\d\.amazonaws.com(?:\.cn)?\/event$/i,$e="/realtime",Je=function(){var e=(0,p.Z)(function*(t){const{appSyncGraphqlEndpoint:n,query:r,libraryConfigHeaders:o=(()=>({})),additionalHeaders:s={},authToken:i}=t;let a={};const l=yield o();return a="function"==typeof s?yield s({url:n||"",queryString:r||""}):s,i&&(a={...a,Authorization:i}),{additionalCustomHeaders:a,libraryConfigHeaders:l}});return function(n){return e.apply(this,arguments)}}();var sn=g(74109);const gt=new x.ConsoleLogger("AWSAppSyncRealTimeProvider Auth"),yt=function(){var e=(0,p.Z)(function*({host:t}){return{Authorization:(yield(0,x.fetchAuthSession)())?.tokens?.accessToken?.toString(),host:t}});return function(n){return e.apply(this,arguments)}}(),an=function(){var e=(0,p.Z)(function*({apiKey:t,host:n}){return{host:n,"x-amz-date":(new Date).toISOString().replace(/[:-]|\.\d{3}/g,""),"x-api-key":t}});return function(n){return e.apply(this,arguments)}}(),cn=function(){var e=(0,p.Z)(function*({payload:t,canonicalUri:n,appSyncGraphqlEndpoint:r,region:o}){const s={region:o,service:"appsync"},i=(yield(0,x.fetchAuthSession)()).credentials,a={url:`${r}${n}`,data:t,method:"POST",headers:{...N}};return(0,sn.C)({headers:a.headers,method:a.method,url:new se.a(a.url),body:a.data},{credentials:i,signingRegion:s.region,signingService:s.service}).headers});return function(n){return e.apply(this,arguments)}}(),mt=function(){var e=(0,p.Z)(function*({host:t,additionalCustomHeaders:n}){if(!n?.Authorization)throw new Error("No auth token specified");return{Authorization:n.Authorization,host:t}});return function(n){return e.apply(this,arguments)}}(),Ye=function(){var e=(0,p.Z)(function*({apiKey:t,authenticationType:n,canonicalUri:r,appSyncGraphqlEndpoint:o,region:s,additionalCustomHeaders:i,payload:a}){const l={apiKey:an,iam:cn,oidc:yt,userPool:yt,lambda:mt,none:mt};if(n&&l[n]){const c=l[n],u=o?new se.a(o).host:void 0,f="apiKey"===n?t:void 0;return gt.debug(`Authenticating with ${JSON.stringify(n)}`),yield c({payload:a,canonicalUri:r,appSyncGraphqlEndpoint:o,apiKey:f,region:s,host:u,additionalCustomHeaders:i})}gt.debug(`Authentication type ${n} not supported`)});return function(n){return e.apply(this,arguments)}}(),At=e=>{x.Hub.dispatch("api",e,"PubSub",me.SQ)};class bt{constructor(t){var n=this;this.subscriptionObserverMap=new Map,this.socketStatus=I.CLOSED,this.keepAliveTimestamp=Date.now(),this.promiseArray=[],this.connectionStateMonitor=new ye,this.reconnectionMonitor=new Se,this._establishConnection=function(){var r=(0,p.Z)(function*(o,s){n.logger.debug(`Establishing WebSocket connection to ${o}`);try{yield n._openConnection(o,s),yield n._initiateHandshake()}catch(i){const{errorType:a,errorCode:l}=i;throw y.includes(l)||M.includes(a)?new pe(a):a?new Error(a):i}});return function(o,s){return r.apply(this,arguments)}}(),this.logger=new x.ConsoleLogger(t.providerName),this.wsProtocolName=t.wsProtocolName,this.wsConnectUri=t.connectUri,this.connectionStateMonitorSubscription=this._startConnectionStateMonitoring()}close(){return this.socketStatus=I.CLOSED,this.connectionStateMonitor.record(Z.CONNECTION_FAILED),this.connectionStateMonitorSubscription.unsubscribe(),this.reconnectionMonitor.close(),new Promise((t,n)=>{this.awsRealTimeSocket?(this.awsRealTimeSocket.onclose=r=>{this._closeSocket(),this.subscriptionObserverMap=new Map,this.awsRealTimeSocket=void 0,t()},this.awsRealTimeSocket.onerror=r=>{n(r)},this.awsRealTimeSocket.close()):t()})}subscribe(t,n){var r=this;return new re.Observable(o=>{if(!t?.appSyncGraphqlEndpoint)return o.error({errors:[{...new V.GraphQLError("Subscribe only available for AWS AppSync endpoint")}]}),void o.complete();let s=!1;const i=(0,le.r)(),a=()=>{s||(s=!0,this._startSubscriptionWithAWSAppSyncRealTime({options:t,observer:o,subscriptionId:i,customUserAgentDetails:n}).catch(c=>{this.logger.debug(`${de.REALTIME_SUBSCRIPTION_INIT_ERROR}: ${c}`),this._closeSocket()}).finally(()=>{s=!1}))},l=new re.Observable(c=>{this.reconnectionMonitor.addObserver(c)}).subscribe(()=>{a()});return a(),(0,p.Z)(function*(){yield r._cleanupSubscription(i,l)})})}connect(t){var n=this;return(0,p.Z)(function*(){n.socketStatus!==I.READY&&(yield n._connectWebSocket(t))})()}publish(t,n){var r=this;return(0,p.Z)(function*(){if(r.socketStatus!==I.READY)throw new Error("Subscription has not been initialized");return r._publishMessage(t,n)})()}_connectWebSocket(t){var n=this;return(0,p.Z)(function*(){const{apiKey:r,appSyncGraphqlEndpoint:o,authenticationType:s,region:i}=t,{additionalCustomHeaders:a}=yield Je(t);n.connectionStateMonitor.record(Z.OPENING_CONNECTION),yield n._initializeWebSocketConnection({apiKey:r,appSyncGraphqlEndpoint:o,authenticationType:s,region:i,additionalCustomHeaders:a})})()}_publishMessage(t,n){var r=this;return(0,p.Z)(function*(){const o=(0,le.r)(),{additionalCustomHeaders:s,libraryConfigHeaders:i}=yield Je(t),a=yield r._prepareSubscriptionPayload({options:t,subscriptionId:o,customUserAgentDetails:n,additionalCustomHeaders:s,libraryConfigHeaders:i,publish:!0});return new Promise((l,c)=>{if(r.awsRealTimeSocket){const u=f=>{const d=JSON.parse(f.data);d.id===o&&"publish_success"===d.type&&(r.awsRealTimeSocket&&r.awsRealTimeSocket.removeEventListener("message",u),l())};r.awsRealTimeSocket.addEventListener("message",u),r.awsRealTimeSocket.addEventListener("close",()=>{c(new Error("WebSocket is closed"))}),r.awsRealTimeSocket.send(a)}})})()}_cleanupSubscription(t,n){var r=this;return(0,p.Z)(function*(){n?.unsubscribe();try{yield r._waitForSubscriptionToBeConnected(t);const{subscriptionState:o}=r.subscriptionObserverMap.get(t)||{};if(!o)return;if(o!==k.CONNECTED)throw new Error("Subscription never connected");r._sendUnsubscriptionMessage(t)}catch(o){r.logger.debug(`Error while unsubscribing ${o}`)}finally{r._removeSubscriptionObserver(t)}})()}_startConnectionStateMonitoring(){return this.connectionStateMonitor.connectionStateObservable.subscribe(t=>{At({event:j,data:{provider:this,connectionState:t},message:`Connection state is ${t}`}),this.connectionState=t,t===X.ConnectionDisrupted&&this.reconnectionMonitor.record(he.START_RECONNECT),[X.Connected,X.ConnectedPendingDisconnect,X.ConnectedPendingKeepAlive,X.ConnectedPendingNetwork,X.ConnectionDisruptedPendingNetwork,X.Disconnected].includes(t)&&this.reconnectionMonitor.record(he.HALT_RECONNECT)})}_startSubscriptionWithAWSAppSyncRealTime({options:t,observer:n,subscriptionId:r,customUserAgentDetails:o}){var s=this;return(0,p.Z)(function*(){const{query:i,variables:a}=t,{additionalCustomHeaders:l,libraryConfigHeaders:c}=yield Je(t);s.subscriptionObserverMap.set(r,{observer:n,query:i??"",variables:a??{},subscriptionState:k.PENDING,startAckTimeoutId:void 0});const u=yield s._prepareSubscriptionPayload({options:t,subscriptionId:r,customUserAgentDetails:o,additionalCustomHeaders:l,libraryConfigHeaders:c});try{yield s._connectWebSocket(t)}catch(A){return void s._logStartSubscriptionError(r,n,A)}const{subscriptionFailedCallback:f,subscriptionReadyCallback:d}=s.subscriptionObserverMap.get(r)??{};s.subscriptionObserverMap.set(r,{observer:n,subscriptionState:k.PENDING,query:i??"",variables:a??{},subscriptionReadyCallback:d,subscriptionFailedCallback:f,startAckTimeoutId:setTimeout(()=>{s._timeoutStartSubscriptionAck(r)},15e3)}),s.awsRealTimeSocket&&s.awsRealTimeSocket.send(u)})()}_logStartSubscriptionError(t,n,r){this.logger.debug({err:r});const o=String(r.message??"");if(this._closeSocket(),this.connectionState!==X.ConnectionDisruptedPendingNetwork){Y(r)?n.error({errors:[{...new V.GraphQLError(`${de.CONNECTION_FAILED}: ${o}`)}]}):this.logger.debug(`${de.CONNECTION_FAILED}: ${o}`);const{subscriptionFailedCallback:s}=this.subscriptionObserverMap.get(t)||{};"function"==typeof s&&s()}}_waitForSubscriptionToBeConnected(t){var n=this;return(0,p.Z)(function*(){const r=n.subscriptionObserverMap.get(t);if(r){const{subscriptionState:o}=r;if(o===k.PENDING)return new Promise((s,i)=>{const{observer:a,subscriptionState:l,variables:c,query:u}=r;n.subscriptionObserverMap.set(t,{observer:a,subscriptionState:l,variables:c,query:u,subscriptionReadyCallback:s,subscriptionFailedCallback:i})})}})()}_sendUnsubscriptionMessage(t){try{if(this.awsRealTimeSocket&&this.awsRealTimeSocket.readyState===WebSocket.OPEN&&this.socketStatus===I.READY){const n=this._unsubscribeMessage(t),r=JSON.stringify(n);this.awsRealTimeSocket.send(r)}}catch(n){this.logger.debug({err:n})}}_removeSubscriptionObserver(t){this.subscriptionObserverMap.delete(t),setTimeout(this._closeSocketIfRequired.bind(this),1e3)}_closeSocketIfRequired(){if(!(this.subscriptionObserverMap.size>0)){if(!this.awsRealTimeSocket)return void(this.socketStatus=I.CLOSED);if(this.connectionStateMonitor.record(Z.CLOSING_CONNECTION),this.awsRealTimeSocket.bufferedAmount>0)setTimeout(this._closeSocketIfRequired.bind(this),1e3);else{this.logger.debug("closing WebSocket...");const t=this.awsRealTimeSocket;t.onclose=null,t.onerror=null,t.close(1e3),this.awsRealTimeSocket=void 0,this.socketStatus=I.CLOSED,this._closeSocket()}}}maintainKeepAlive(){this.keepAliveTimestamp=Date.now()}keepAliveHeartbeat(t){const n=Date.now();this.connectionStateMonitor.record(n-this.keepAliveTimestamp>65e3?Z.KEEP_ALIVE_MISSED:Z.KEEP_ALIVE),n-this.keepAliveTimestamp>t&&this._errorDisconnect(de.TIMEOUT_DISCONNECT)}_handleIncomingSubscriptionMessage(t){if("string"!=typeof t.data)return;const[n,r]=this._handleSubscriptionData(t);if(n)return void this.maintainKeepAlive();const{type:o,id:s,payload:i}=r,{observer:a=null,query:l="",variables:c={},startAckTimeoutId:u,subscriptionReadyCallback:f,subscriptionFailedCallback:d}=this.subscriptionObserverMap.get(s)||{};if(o===P.GQL_START_ACK||o===P.EVENT_SUBSCRIBE_ACK)return this.logger.debug(`subscription ready for ${JSON.stringify({query:l,variables:c})}`),"function"==typeof f&&f(),u&&clearTimeout(u),At({event:de.SUBSCRIPTION_ACK,data:{query:l,variables:c},message:"Connection established for subscription"}),a&&this.subscriptionObserverMap.set(s,{observer:a,query:l,variables:c,startAckTimeoutId:void 0,subscriptionState:k.CONNECTED,subscriptionReadyCallback:f,subscriptionFailedCallback:d}),void this.connectionStateMonitor.record(Z.CONNECTION_ESTABLISHED);o!==P.GQL_CONNECTION_KEEP_ALIVE?o===P.GQL_ERROR&&a&&(this.subscriptionObserverMap.set(s,{observer:a,query:l,variables:c,startAckTimeoutId:u,subscriptionReadyCallback:f,subscriptionFailedCallback:d,subscriptionState:k.FAILED}),this.logger.debug(`${de.CONNECTION_FAILED}: ${JSON.stringify(i??r)}`),a.error({errors:[{...new V.GraphQLError(`${de.CONNECTION_FAILED}: ${JSON.stringify(i??r)}`)}]}),u&&clearTimeout(u),"function"==typeof d&&d()):this.maintainKeepAlive()}_errorDisconnect(t){this.logger.debug(`Disconnect error: ${t}`),this.awsRealTimeSocket&&(this._closeSocket(),this.awsRealTimeSocket.close()),this.socketStatus=I.CLOSED}_closeSocket(){this.keepAliveHeartbeatIntervalId&&(clearInterval(this.keepAliveHeartbeatIntervalId),this.keepAliveHeartbeatIntervalId=void 0),this.connectionStateMonitor.record(Z.CLOSED)}_timeoutStartSubscriptionAck(t){const n=this.subscriptionObserverMap.get(t);if(n){const{observer:r,query:o,variables:s}=n;if(!r)return;this.subscriptionObserverMap.set(t,{observer:r,query:o,variables:s,subscriptionState:k.FAILED}),this._closeSocket(),this.logger.debug("timeoutStartSubscription",JSON.stringify({query:o,variables:s}))}}_initializeWebSocketConnection({appSyncGraphqlEndpoint:t,authenticationType:n,apiKey:r,region:o,additionalCustomHeaders:s}){var i=this;if(this.socketStatus!==I.READY)return new Promise(function(){var a=(0,p.Z)(function*(l,c){if(i.promiseArray.push({res:l,rej:c}),i.socketStatus===I.CLOSED)try{i.socketStatus=I.CONNECTING;const f=yield Ye({authenticationType:n,payload:"{}",canonicalUri:i.wsConnectUri,apiKey:r,appSyncGraphqlEndpoint:t,region:o,additionalCustomHeaders:s}),d=f?JSON.stringify(f):"",_=`header-${Ee.convert(d,{urlSafe:!0,skipPadding:!0})}`,m=(e=>{const t=(e=>{if(!e)return{};if("Authorization"in e){const{Authorization:t,...n}=e;return n}return e})(e),n=new se.z;return Object.entries(t).forEach(([r,o])=>{n.append(r,o)}),n})(s),S=((e,t)=>{const n=(e=>{let t=e??"";return t=(e=>null!==e.match(Me))(t)?t.concat($e).replace("ddpg-api","grt-gamma").replace("appsync-api","appsync-realtime-api"):(e=>null===e.match(Pe))(t)?t.concat($e):t.replace("appsync-api","appsync-realtime-api").replace("gogi-beta","grt-beta").replace("ddpg-api","grt-gamma"),t=t.replace("https://","wss://").replace("http://","wss://"),new se.a(t)})(e),r=new se.z(n.search);for(const[o,s]of t.entries())r.append(o,s);return n.search=r.toString(),n.toString()})(t,m);yield i._establishRetryableConnection(S,_),i.promiseArray.forEach(({res:w})=>{i.logger.debug("Notifying connection successful"),w()}),i.socketStatus=I.READY,i.promiseArray=[]}catch(u){i.logger.debug("Connection exited with",u),i.promiseArray.forEach(({rej:f})=>{f(u)}),i.promiseArray=[],i.awsRealTimeSocket&&i.awsRealTimeSocket.readyState===WebSocket.OPEN&&i.awsRealTimeSocket.close(3001),i.awsRealTimeSocket=void 0,i.socketStatus=I.CLOSED}});return function(l,c){return a.apply(this,arguments)}}())}_establishRetryableConnection(t,n){var r=this;return(0,p.Z)(function*(){r.logger.debug("Establishing retryable connection"),yield _e(r._establishConnection.bind(r),[t,n],5e3)})()}_openConnection(t,n){var r=this;return(0,p.Z)(function*(){return new Promise((o,s)=>{const i=r._getNewWebSocket(t,[r.wsProtocolName,n]);i.onerror=()=>{r.logger.debug("WebSocket connection error")},i.onclose=()=>{r._closeSocket(),s(new Error("Connection handshake error"))},i.onopen=()=>{r.awsRealTimeSocket=i,o()}})})()}_getNewWebSocket(t,n){return new WebSocket(t,n)}_initiateHandshake(){var t=this;return(0,p.Z)(function*(){return new Promise((n,r)=>{if(!t.awsRealTimeSocket)return void r(new Error("awsRealTimeSocket undefined"));let o=!1;t.awsRealTimeSocket.onerror=a=>{t.logger.debug(`WebSocket error ${JSON.stringify(a)}`)},t.awsRealTimeSocket.onclose=a=>{t.logger.debug(`WebSocket closed ${a.reason}`),t._closeSocket(),r(new Error(JSON.stringify(a)))},t.awsRealTimeSocket.onmessage=a=>{if("string"!=typeof a.data)return;t.logger.debug(`subscription message from AWS AppSyncRealTime: ${a.data} `);const l=JSON.parse(a.data),{type:c}=l,u=t._extractConnectionTimeout(l);if(c===P.GQL_CONNECTION_ACK)return o=!0,t._registerWebsocketHandlers(u),void n("Connected to AWS AppSyncRealTime");if(c===P.GQL_CONNECTION_ERROR){const{errorType:f,errorCode:d}=t._extractErrorCodeAndType(l);r({errorType:f,errorCode:d})}},t.awsRealTimeSocket.send(JSON.stringify({type:P.GQL_CONNECTION_INIT})),setTimeout(()=>{o||(t.connectionStateMonitor.record(Z.CONNECTION_FAILED),r(new Error("Connection timeout: ack from AWSAppSyncRealTime was not received after 15000 ms")))},15e3)})})()}_registerWebsocketHandlers(t){this.awsRealTimeSocket&&(this.keepAliveHeartbeatIntervalId=setInterval(()=>{this.keepAliveHeartbeat(t)},5e3),this.awsRealTimeSocket.onmessage=this._handleIncomingSubscriptionMessage.bind(this),this.awsRealTimeSocket.onerror=n=>{this.logger.debug(n),this._errorDisconnect(de.CONNECTION_CLOSED)},this.awsRealTimeSocket.onclose=n=>{this.logger.debug(`WebSocket closed ${n.reason}`),this._closeSocket(),this._errorDisconnect(de.CONNECTION_CLOSED)})}}const _t="AWSAppSyncEventsProvider",Xe=new class dn extends bt{constructor(){super({providerName:_t,wsProtocolName:"aws-appsync-event-ws",connectUri:""})}getProviderName(){return _t}connect(t){var n=()=>super.connect,r=this;return(0,p.Z)(function*(){n().call(r,t)})()}subscribe(t,n){return super.subscribe(t,n).pipe()}publish(t,n){var r=()=>super.publish,o=this;return(0,p.Z)(function*(){r().call(o,t,n)})()}_prepareSubscriptionPayload({options:t,subscriptionId:n,customUserAgentDetails:r,additionalCustomHeaders:o,libraryConfigHeaders:s,publish:i}){return(0,p.Z)(function*(){const{appSyncGraphqlEndpoint:a,authenticationType:l,query:c,apiKey:u,region:f}=t,d=JSON.stringify({channel:c}),A={...yield Ye({apiKey:u,appSyncGraphqlEndpoint:a,authenticationType:l,payload:d,canonicalUri:"",region:f,additionalCustomHeaders:o}),...s,...o,[R.Mt]:(0,v.Zm)(r)},_={id:n,channel:c,authorization:{...A},type:i?P.EVENT_PUBLISH:P.EVENT_SUBSCRIBE};return JSON.stringify(_)})()}_handleSubscriptionData(t){this.logger.debug(`subscription message from AWS AppSync Events: ${t.data}`);const{id:n="",event:r,type:o}=JSON.parse(String(t.data)),{observer:s=null,query:i="",variables:a={}}=this.subscriptionObserverMap.get(n)||{};if(this.logger.debug({id:n,observer:s,query:i,variables:a}),o===P.DATA&&r){const l=JSON.parse(r);return s?s.next({id:n,type:o,event:l}):this.logger.debug(`observer not found for id: ${n}`),[!0,{id:n,type:o,payload:l}]}return[!1,{id:n,type:o,payload:r}]}_unsubscribeMessage(t){return{id:t,type:P.EVENT_STOP}}_extractConnectionTimeout(t){const{connectionTimeoutMs:n=T}=t;return n}_extractErrorCodeAndType(t){const{errors:[{errorType:n="",errorCode:r=0}={}]=[]}=t;return{errorCode:r,errorType:n}}};var Et=g(91935);class Be extends Et._{get response(){return this._response?fn(this._response):void 0}constructor(t){super(t),this.constructor=Be,Object.setPrototypeOf(this,Be.prototype),t.response&&(this._response=t.response)}}const fn=e=>({...e,headers:{...e.headers}});class je extends Be{constructor(t){super(t),this.constructor=je,Object.setPrototypeOf(this,je.prototype)}}class He extends je{constructor(t={}){super({name:"CanceledError",message:"Request is canceled by user",...t}),this.constructor=He,Object.setPrototypeOf(this,He.prototype)}}const pn=e=>!!e&&e instanceof He;var hn=g(54974);const St=function(){var e=(0,p.Z)(function*(t){if(!t)return;const n=yield(0,hn.f)(gn(t));if(n){const r=yield t.body?.text();return yn(n,{statusCode:t.statusCode,headers:t.headers,body:r})}});return function(n){return e.apply(this,arguments)}}(),gn=e=>{let t;const n=new Proxy(e.body,{get:(o,s,i)=>"json"===s?(0,p.Z)(function*(){t||(t=o.text());try{return JSON.parse(yield t)}catch{return{}}}):"text"===s?(0,p.Z)(function*(){return t||(t=o.text()),t}):Reflect.get(o,s,i)});return new Proxy(e,{get:(o,s,i)=>"body"===s?n:Reflect.get(o,s,i)})},yn=(e,t)=>{const n=new je({name:e?.name,message:e.message,underlyingError:e,response:t});return Object.assign(n,{$metadata:e.$metadata})},et=new x.ConsoleLogger("RestApis"),An=({headers:e},t)=>!e.authorization&&!e["x-api-key"]&&!!t;var bn=g(67834),_n=g(79987),En=g(6639),Sn=g(54473);const vt="execute-api",vn="us-east-1",Tn=/^.+\.([a-z0-9-]+)\.([a-z0-9-]+)\.amazonaws\.com/,Nn=function(){var e=(0,p.Z)(function*(t,n,r,o){const{url:s,method:i,headers:a,body:l,withCredentials:c,abortSignal:u}=n,f=l?l instanceof FormData?l:JSON.stringify(l??""):void 0,d=((e,t)=>{const n={};for(const r in e)n[r.toLowerCase()]=e[r];return t&&(n["content-type"]="application/json; charset=UTF-8",t instanceof FormData&&delete n["content-type"]),n})(a,l),A={url:s,headers:d,method:i,body:f},_={retryDecider:(0,bn.j)(St),computeDelay:_n.k,withCrossDomainCredentials:c,abortSignal:u},m=r(A,o);let S;const w=yield wn(t);if(m&&w){const F=((e,t)=>{const{service:n=vt,region:r=vn}={},{hostname:o}=e,[,s,i]=Tn.exec(o)??[];return s===vt?{service:s,region:i??r}:"appsync-api"===s?{service:"appsync",region:i??r}:{service:n,region:r}})(s),D=o?.service??F.service,B=o?.region??F.region;S=yield(0,En.Z)(A,{..._,credentials:w,region:B,service:D})}else S=yield(0,Sn.y)(A,{..._});return{statusCode:S.statusCode,headers:S.headers,body:S.body}});return function(n,r,o,s){return e.apply(this,arguments)}}(),wn=function(){var e=(0,p.Z)(function*(t){try{const{credentials:n}=yield t.Auth.fetchAuthSession();if(n)return n}catch{et.debug("No credentials available, the request will be unsigned.")}return null});return function(n){return e.apply(this,arguments)}}(),tt=new WeakMap,Tt=(e,{url:t,options:n,abortController:r})=>{const o=r??new AbortController,i=function mn(e,t){const n=l=>!!t,r=new AbortController,o=r.signal,s=t?.signal;let i;const a=function(){var l=(0,p.Z)(function*(){try{const c=yield n()?e():e(o);if(c.statusCode>=300)throw yield St(c);return c}catch(c){const u=s??o,f=i??u.reason;if("AbortError"===c.name||!0===u?.aborted){const d=new He({...f&&{message:f},underlyingError:c,recoverySuggestion:"The API request was explicitly canceled. If this is not intended, validate if you called the `cancel()` function on the API request erroneously."});throw et.debug(c),d}throw et.debug(c),c}});return function(){return l.apply(this,arguments)}}();if(n())return a();{const l=c=>{!0!==o.aborted&&(r.abort(c),c&&o.reason!==c&&(i=c))};return{response:a(),cancel:l}}}((0,p.Z)(function*(){return Nn(e,{url:t,method:"POST",...n,abortSignal:o.signal},An,n?.signingServiceInfo)}),o).finally(()=>{tt.delete(i)});return i},Rn=(e,t)=>{const n=tt.get(e);return!!n&&(n.abort(t),t&&n.signal.reason!==t&&(n.signal.reason=t),!0)},Pn=(e,t)=>{tt.set(e,t)},ze=e=>{const t=e.libraryOptions?.API?.GraphQL?.headers,n=e.libraryOptions?.API?.GraphQL?.withCredentials;return{headers:t,withCredentials:n}};function nt(e){return e.errors&&Array.isArray(e.errors)&&e.errors.forEach(t=>{(function Mn(e){return!!(e?.originalError?.name?.startsWith("UnauthorizedException")||e.message?.startsWith("Connection failed:")&&e.message?.includes("Permission denied"))})(t)&&(t.message="Unauthorized",t.recoverySuggestion="If you're calling an Amplify-generated API, make sure to set the \"authMode\" in generateClient({ authMode: '...' }) to the backend authorization rule's auth provider ('apiKey', 'userPool', 'iam', 'oidc', 'lambda')")}),e}class Te extends Et._{constructor(t){super(t),this.constructor=Te,Object.setPrototypeOf(this,Te.prototype)}}var Oe=(()=>{return(e=Oe||(Oe={})).NO_API_KEY="No api-key configured",e.NO_CURRENT_USER="No current user",e.NO_CREDENTIALS="No credentials",e.NO_FEDERATED_JWT="No federated jwt",e.NO_AUTH_TOKEN="No auth token specified",Oe;var e})();const Ct=Symbol("amplify"),Ot=Symbol("authMode"),Nt=Symbol("authToken"),wt=Symbol("apiKey"),Rt=Symbol("headers"),Pt=Symbol("endpoint");function kn(e){return{amplify:e[Ct],apiKey:e[wt],authMode:e[Ot],authToken:e[Nt],endpoint:e[Pt],headers:e[Rt]}}const In={name:"NoApiKey",message:Oe.NO_API_KEY,recoverySuggestion:'The API request was made with `authMode: "apiKey"` but no API Key was passed into `Amplify.configure()`. Review if your API key is passed into the `Amplify.configure()` function.'},xn={name:"NoCredentials",message:Oe.NO_CREDENTIALS,recoverySuggestion:'The API request was made with `authMode: "iam"` but no authentication credentials are available.\n\nIf you intended to make a request using an authenticated role, review if your user is signed in before making the request.\n\nIf you intend to make a request using an unauthenticated role or also known as "guest access", verify if "Auth.Cognito.allowGuestAccess" is set to "true" in the `Amplify.configure()` function.'},Dn={name:"NoValidAuthTokens",message:Oe.NO_FEDERATED_JWT,recoverySuggestion:"If you intended to make an authenticated API request, review if the current user is signed in."},Ln={name:"NoSignedUser",message:Oe.NO_CURRENT_USER,recoverySuggestion:"Review the underlying exception field for more details. If you intended to make an authenticated API request, review if the current user is signed in."},Un={name:"NoAuthorizationHeader",message:Oe.NO_AUTH_TOKEN,recoverySuggestion:'The API request was made with `authMode: "lambda"` but no `authToken` is set. Review if a valid authToken is passed into the request options or in the `Amplify.configure()` function.'},Gn={name:"NoEndpoint",message:"No GraphQL endpoint configured in `Amplify.configure()`.",recoverySuggestion:"Review if the GraphQL API endpoint is set in the `Amplify.configure()` function."};function Mt(e,t,n){return rt.apply(this,arguments)}function rt(){return(rt=(0,p.Z)(function*(e,t,n,r={}){let o={};switch(t){case"apiKey":if(!n)throw new Te(In);o={"X-Api-Key":n};break;case"iam":if(void 0===(yield e.Auth.fetchAuthSession()).credentials)throw new Te(xn);break;case"oidc":case"userPool":{let s;try{s=(yield e.Auth.fetchAuthSession()).tokens?.accessToken.toString()}catch(i){throw new Te({...Ln,underlyingError:i})}if(!s)throw new Te(Dn);o={Authorization:s};break}case"lambda":if("object"==typeof r&&!r.Authorization)throw new Te(Un);o={Authorization:r.Authorization}}return o})).apply(this,arguments)}function kt(e){if(!e)return!1;const t=e;return Array.isArray(t.errors)&&t.errors.length>0}const Fn="x-amz-user-agent";function ot(){return ot=(0,p.Z)(function*(e,t,n={},r,o){const{region:s,appSyncGraphqlEndpoint:i,authenticationType:a,query:l,variables:c}=t;if(!i)throw new Error("No endpoint");const{withCredentials:u}=ze(e),f=yield function jn(e,t,n,r){return st.apply(this,arguments)}(e,t,n,o),d={channel:l,events:c},A=["apiKey","none"].includes(a)?void 0:{service:"appsync",region:s},{body:_}=yield Tt(e,{url:new se.a(i),options:{headers:f,body:d,signingServiceInfo:A,withCredentials:u},abortController:r}),m=yield _.json();if(kt(m))throw nt(m);return m}),ot.apply(this,arguments)}function st(){return(st=(0,p.Z)(function*(e,t,n,r){const{apiKey:o,appSyncGraphqlEndpoint:s,authenticationType:i,query:a,variables:l,authToken:c}=t,{headers:u}=ze(e);let f;if("function"==typeof n){const m={method:"POST",url:new se.a(s).toString(),queryString:a};f=yield n(m)}else f=n;return c&&(f={...f,Authorization:c}),{...yield Mt(e,i,o,f),...u&&(yield u({query:a,variables:l})),...f,[Fn]:(0,v.Zm)(r)}})).apply(this,arguments)}const Ke=(e,t)=>e?"identityPool"===e?"iam":e:t,It=()=>{const t=x.Amplify.getConfig().API?.Events;if(!t)throw new Error("Amplify configuration is missing. Have you called Amplify.configure()?");const n=Ke(t.defaultAuthMode,"apiKey");return{appSyncGraphqlEndpoint:t.endpoint,region:t.region,authenticationType:n,apiKey:t.apiKey}},Hn=e=>{if(Array.isArray(e))return e.map((n,r)=>{const o=JSON.stringify(n);if(void 0===o)throw new Error(`Event must be a valid JSON value. Received ${n} at index ${r}`);return o});const t=JSON.stringify(e);if(void 0===t)throw new Error(`Event must be a valid JSON value. Received ${e}`);return[t]};function Qn(e,t){return it.apply(this,arguments)}function it(){return(it=(0,p.Z)(function*(e,t){const n=It();let r;return n.authenticationType=Ke(t?.authMode,n.authenticationType),yield Xe.connect(n),{subscribe:(i,a)=>{const l={...n,query:e};return l.authenticationType=Ke(a?.authMode,l.authenticationType),r=Xe.subscribe(l).subscribe(i),r},close:()=>{r&&r.unsubscribe()}}})).apply(this,arguments)}function Bn(e,t,n){return at.apply(this,arguments)}function at(){return at=(0,p.Z)(function*(e,t,n){const r=It();r.authenticationType=Ke(n?.authMode,r.authenticationType);const o="/"===e[0]?e:`/${e}`,s={...r,query:o,variables:Hn(t),authToken:n?.authToken},i=new AbortController,a=yield function $n(e,t){return ot.apply(this,arguments)}(x.Amplify,s,{},i);if(a.failed?.length>0)return a.failed}),at.apply(this,arguments)}function Wn(){return ct.apply(this,arguments)}function ct(){return(ct=(0,p.Z)(function*(){yield Xe.close()})).apply(this,arguments)}var xt=g(81220);Symbol();const ft=Symbol("INTERNAL_USER_AGENT_OVERRIDE"),Ue={},Qe=new Array(64);for(let e=0,t="A".charCodeAt(0),n="Z".charCodeAt(0);e+t<=n;e++){const r=String.fromCharCode(e+t);Ue[r]=e,Qe[e]=r}for(let e=0,t="a".charCodeAt(0),n="z".charCodeAt(0);e+t<=n;e++){const r=String.fromCharCode(e+t),o=e+26;Ue[r]=o,Qe[o]=r}for(let e=0;e<10;e++){Ue[e.toString(10)]=e+52;const t=e.toString(10),n=e+52;Ue[t]=n,Qe[n]=t}Ue["+"]=62,Qe[62]="+",Ue["/"]=63,Qe[63]="/";const Vt="AWSAppSyncRealTimeProvider";class gr extends bt{constructor(){super({providerName:Vt,wsProtocolName:"graphql-ws",connectUri:"/connect"})}getProviderName(){return Vt}subscribe(t,n){return super.subscribe(t,n)}_prepareSubscriptionPayload({options:t,subscriptionId:n,customUserAgentDetails:r,additionalCustomHeaders:o,libraryConfigHeaders:s}){return(0,p.Z)(function*(){const{appSyncGraphqlEndpoint:i,authenticationType:a,query:l,variables:c,apiKey:u,region:f}=t,A=JSON.stringify({query:l,variables:c}),_={...yield Ye({apiKey:u,appSyncGraphqlEndpoint:i,authenticationType:a,payload:A,canonicalUri:"",region:f,additionalCustomHeaders:o}),...s,...o,[R.Mt]:(0,v.Zm)(r)},m={id:n,payload:{data:A,extensions:{authorization:{..._}}},type:P.GQL_START};return JSON.stringify(m)})()}_handleSubscriptionData(t){this.logger.debug(`subscription message from AWS AppSync RealTime: ${t.data}`);const{id:n="",payload:r,type:o}=JSON.parse(String(t.data)),{observer:s=null,query:i="",variables:a={}}=this.subscriptionObserverMap.get(n)||{};return this.logger.debug({id:n,observer:s,query:i,variables:a}),o===P.DATA&&r&&r.data?(s?s.next(r):this.logger.debug(`observer not found for id: ${n}`),[!0,{id:n,type:o,payload:r}]):[!1,{id:n,type:o,payload:r}]}_unsubscribeMessage(t){return{id:t,type:P.GQL_STOP}}_extractConnectionTimeout(t){const{payload:{connectionTimeoutMs:n=T}={}}=t;return n}_extractErrorCodeAndType(t){const{payload:{errors:[{errorType:n="",errorCode:r=0}={}]=[]}={}}=t;return{errorCode:r,errorType:n}}}var ke=(()=>{return(e=ke||(ke={})).NoAuthSession="NoAuthSession",e.NoRegion="NoRegion",e.NoCustomEndpoint="NoCustomEndpoint",ke;var e})();const yr={[ke.NoAuthSession]:{message:"Auth session should not be empty."},[ke.NoRegion]:{message:"Missing region."},[ke.NoCustomEndpoint]:{message:"Custom endpoint region is present without custom endpoint."}},Ar=new x.ConsoleLogger("GraphQLAPI resolveConfig"),Zt=e=>{const t=e.getConfig();t.API?.GraphQL||Ar.warn("The API configuration is missing. This is likely due to Amplify.configure() not being called prior to generateClient().");const{apiKey:n,customEndpoint:r,customEndpointRegion:o,defaultAuthMode:s,endpoint:i,region:a}=t.API?.GraphQL??{};return function mr(e,t){const{message:n,recoverySuggestion:r}=yr[t];if(!e)throw new Te({name:t,message:n,recoverySuggestion:r})}(!(!r&&o),ke.NoCustomEndpoint),{apiKey:n,customEndpoint:r,customEndpointRegion:o,defaultAuthMode:s,endpoint:i,region:a}},Jt=e=>({data:{},errors:[new V.GraphQLError(e.message,null,null,null,null,e)]});class Yt{constructor(){this.appSyncRealTime=new Map,this._api={post:Tt,cancelREST:Rn,isCancelErrorREST:pn,updateRequestToBeCancellable:Pn}}getModuleName(){return"InternalGraphQLAPI"}getGraphqlOperationType(t){const r=(0,V.parse)(t).definitions,[{operation:o}]=r;return o}graphql(t,{query:n,variables:r={},authMode:o,authToken:s,endpoint:i,apiKey:a},l,c){var u=this;const f=(0,V.parse)("string"==typeof n?n:(0,V.print)(n)),[d={}]=f.definitions.filter(m=>"OperationDefinition"===m.kind),{operation:A}=d,_=l||{};switch(A){case"query":case"mutation":{const m=new AbortController;let S;return S="function"!=typeof t?this._graphql(t,{query:f,variables:r,authMode:o,apiKey:a,endpoint:i},_,m,c,s):t(function(){var F=(0,p.Z)(function*(D){return yield u._graphql(D,{query:f,variables:r,authMode:o,apiKey:a,endpoint:i},_,m,c,s)});return function(B){return F.apply(this,arguments)}}()),this._api.updateRequestToBeCancellable(S,m),S}case"subscription":return this._graphqlSubscribe(t,{query:f,variables:r,authMode:o,apiKey:a,endpoint:i},_,c,s);default:throw new Error(`invalid operation type: ${A}`)}}_graphql(t,{query:n,variables:r,authMode:o,endpoint:s,apiKey:i},a={},l,c,u){var f=this;return(0,p.Z)(function*(){const{apiKey:d,region:A,endpoint:_,customEndpoint:m,customEndpointRegion:S,defaultAuthMode:w}=Zt(t),F=o||w||"iam",D="identityPool"===F?"iam":F,{headers:B,withCredentials:Q}=ze(t);let W;if("function"==typeof a){const z={method:"POST",url:new se.a(s||m||_||"").toString(),queryString:(0,V.print)(n)};W=yield a(z)}else W=a;u&&(W={...W,Authorization:u});const q=yield Mt(t,D,i??d,W),ce={...!m&&q,...m&&(S?q:{})||{},...B&&(yield B({query:(0,V.print)(n),variables:r})),...W,...!m&&{"x-amz-user-agent":(0,v.Zm)(c)}},fe={query:(0,V.print)(n),variables:r||null};let ge;ge=m&&!S||"oidc"!==D&&"userPool"!==D&&"iam"!==D&&"lambda"!==D?void 0:{service:S?"execute-api":"appsync",region:S||A};const te=s||m||_;if(!te)throw Jt(new Te(Gn));let $;try{const{body:z}=yield f._api.post(t,{url:new se.a(te),options:{headers:ce,body:fe,signingServiceInfo:ge,withCredentials:Q},abortController:l});$=yield z.json()}catch(z){if(f.isCancelError(z))throw z;$=Jt(z)}if(kt($))throw nt($);return $})()}isCancelError(t){return this._api.isCancelErrorREST(t)}cancel(t,n){return this._api.cancelREST(t,n)}_graphqlSubscribe(t,{query:n,variables:r,authMode:o,apiKey:s,endpoint:i},a={},l,c){const u=Zt(t),f=o||u?.defaultAuthMode||"iam",d="identityPool"===f?"iam":f,{headers:A}=ze(t),_=i??u?.endpoint,m=_??"none",S=this.appSyncRealTime.get(m)??new gr;return this.appSyncRealTime.set(m,S),S.subscribe({query:(0,V.print)(n),variables:r,appSyncGraphqlEndpoint:_,region:u?.region,authenticationType:d,apiKey:s??u?.apiKey,additionalHeaders:a,authToken:c,libraryConfigHeaders:A},l).pipe((0,re.catchError)(w=>{throw w.errors?nt(w):w}))}}new Yt;const Sr=(e,t={},n)=>({query:e,variables:t,authToken:n});class Xt extends Yt{getModuleName(){return"GraphQLAPI"}graphql(t,n,r){const o={category:xt.WD.API,action:xt.gq.GraphQl};if(function Er(e){return ft in e}(n)){const{[ft]:s,...i}=n;return super.graphql(t,i,r,{...o,...s})}return super.graphql(t,n,r,{...o})}isCancelError(t){return super.isCancelError(t)}cancel(t,n){return super.cancel(t,n)}}const vr=new Xt},23192:(ee,L,g)=>{g.d(L,{SQ:()=>R,Xb:()=>y});var b=g(55502),p=g(92261),x=g(91935);const R=typeof Symbol<"u"?Symbol("amplify_default"):"@@amplify_default",v=new b.k("Hub");class O{constructor(P){this.listeners=new Map,this.protectedChannels=["core","auth","api","analytics","interactions","pubsub","storage","ui","xr"],this.name=P}_remove(P,k){const I=this.listeners.get(P);I?this.listeners.set(P,[...I.filter(({callback:N})=>N!==k)]):v.warn(`No listeners for ${P}`)}dispatch(P,k,I,N){"string"==typeof P&&this.protectedChannels.indexOf(P)>-1&&(N===R||v.warn(`WARNING: ${P} is protected and dispatching on it can have unintended consequences`));const E={channel:P,payload:{...k},source:I,patternInfo:[]};try{this._toListeners(E)}catch(h){v.error(h)}}listen(P,k,I="noname"){let N;if("function"!=typeof k)throw new x._({name:p.z2,message:"No callback supplied to Hub"});N=k;let E=this.listeners.get(P);return E||(E=[],this.listeners.set(P,E)),E.push({name:I,callback:N}),()=>{this._remove(P,N)}}_toListeners(P){const{channel:k,payload:I}=P,N=this.listeners.get(k);N&&N.forEach(E=>{v.debug(`Dispatching to ${k} with `,I);try{E.callback(P)}catch(h){v.error(h)}})}}const y=new O("__default__");new O("internal-hub")},55502:(ee,L,g)=>{g.d(L,{k:()=>R});var b=g(92261),p=(()=>{return(v=p||(p={})).DEBUG="DEBUG",v.ERROR="ERROR",v.INFO="INFO",v.WARN="WARN",v.VERBOSE="VERBOSE",v.NONE="NONE",p;var v})();const x={VERBOSE:1,DEBUG:2,INFO:3,WARN:4,ERROR:5,NONE:6};let R=(()=>{class v{constructor(y,M=p.WARN){this.name=y,this.level=M,this._pluggables=[]}_padding(y){return y<10?"0"+y:""+y}_ts(){const y=new Date;return[this._padding(y.getMinutes()),this._padding(y.getSeconds())].join(":")+"."+y.getMilliseconds()}configure(y){return y?(this._config=y,this._config):this._config}_log(y,...M){let j=this.level;if(v.LOG_LEVEL&&(j=v.LOG_LEVEL),typeof window<"u"&&window.LOG_LEVEL&&(j=window.LOG_LEVEL),!(x[y]>=x[j]))return;let I=console.log.bind(console);y===p.ERROR&&console.error&&(I=console.error.bind(console)),y===p.WARN&&console.warn&&(I=console.warn.bind(console)),v.BIND_ALL_LOG_LEVELS&&(y===p.INFO&&console.info&&(I=console.info.bind(console)),y===p.DEBUG&&console.debug&&(I=console.debug.bind(console)));const N=`[${y}] ${this._ts()} ${this.name}`;let E="";if(1===M.length&&"string"==typeof M[0])E=`${N} - ${M[0]}`,I(E);else if(1===M.length)E=`${N} ${M[0]}`,I(N,M[0]);else if("string"==typeof M[0]){let h=M.slice(1);1===h.length&&(h=h[0]),E=`${N} - ${M[0]} ${h}`,I(`${N} - ${M[0]}`,h)}else E=`${N} ${M}`,I(N,M);for(const h of this._pluggables){const T={message:E,timestamp:Date.now()};h.pushLogs([T])}}log(...y){this._log(p.INFO,...y)}info(...y){this._log(p.INFO,...y)}warn(...y){this._log(p.WARN,...y)}error(...y){this._log(p.ERROR,...y)}debug(...y){this._log(p.DEBUG,...y)}verbose(...y){this._log(p.VERBOSE,...y)}addPluggable(y){y&&y.getCategoryName()===b.YG&&(this._pluggables.push(y),y.configure(this._config))}listPluggables(){return this._pluggables}}return v.LOG_LEVEL=null,v.BIND_ALL_LOG_LEVELS=!1,v})()},91396:(ee,L,g)=>{g.d(L,{Cj:()=>we,QW:()=>Fe});var b=g(81220);const p=()=>typeof global<"u",R=()=>typeof window<"u",v=()=>typeof document<"u",O=()=>typeof process<"u",y=(ne,_e)=>!!Object.keys(ne).find(de=>de.startsWith(_e)),me=[{platform:b.gQ.Expo,detectionMethod:function re(){return p()&&typeof global.expo<"u"}},{platform:b.gQ.ReactNative,detectionMethod:function ae(){return typeof navigator<"u"&&typeof navigator.product<"u"&&"ReactNative"===navigator.product}},{platform:b.gQ.NextJs,detectionMethod:function E(){return R()&&window.next&&"object"==typeof window.next}},{platform:b.gQ.Nuxt,detectionMethod:function T(){return R()&&(void 0!==window.__NUXT__||void 0!==window.$nuxt)}},{platform:b.gQ.Angular,detectionMethod:function G(){const ne=Boolean(v()&&document.querySelector("[ng-version]")),_e=Boolean(R()&&typeof window.ng<"u");return ne||_e}},{platform:b.gQ.React,detectionMethod:function M(){const ne=X=>X.startsWith("_react")||X.startsWith("__react");return v()&&Array.from(document.querySelectorAll("[id]")).some(X=>Object.keys(X).find(ne))}},{platform:b.gQ.VueJs,detectionMethod:function P(){return R()&&y(window,"__VUE")}},{platform:b.gQ.Svelte,detectionMethod:function I(){return R()&&y(window,"__SVELTE")}},{platform:b.gQ.WebUnknown,detectionMethod:function V(){return R()}},{platform:b.gQ.NextJsSSR,detectionMethod:function h(){return p()&&(y(global,"__next")||y(global,"__NEXT"))}},{platform:b.gQ.NuxtSSR,detectionMethod:function C(){return p()&&typeof global.__NUXT_PATHS__<"u"}},{platform:b.gQ.ReactSSR,detectionMethod:function j(){return O()&&typeof process.env<"u"&&!!Object.keys(process.env).find(ne=>ne.includes("react"))}},{platform:b.gQ.VueJsSSR,detectionMethod:function k(){return p()&&y(global,"__VUE")}},{platform:b.gQ.AngularSSR,detectionMethod:function J(){return O()&&"object"==typeof process.env&&process.env.npm_lifecycle_script?.startsWith("ng ")||!1}},{platform:b.gQ.SvelteSSR,detectionMethod:function N(){return O()&&typeof process.env<"u"&&!!Object.keys(process.env).find(ne=>ne.includes("svelte"))}}];let le;const Y=[];let oe=!1;const Ne=1e3,we=()=>{if(!le){if(le=function pe(){return me.find(ne=>ne.detectionMethod())?.platform||b.gQ.ServerSideUnknown}(),oe)for(;Y.length;)Y.pop()?.();else Y.forEach(ne=>{ne()});Ie(b.gQ.ServerSideUnknown,10),Ie(b.gQ.WebUnknown,10)}return le},Fe=ne=>{oe||Y.push(ne)};function Ie(ne,_e){le===ne&&!oe&&setTimeout(()=>{(function Re(){le=void 0})(),oe=!0,setTimeout(we,Ne)},_e)}},5919:(ee,L,g)=>{g.d(L,{Zm:()=>I});var b=g(81220);const p="6.13.1";var x=g(91396);const R={},y="aws-amplify",M=N=>N.replace(/\+.*/,"");new class j{constructor(){this.userAgent=`${y}/${M(p)}`}get framework(){return(0,x.Cj)()}get isReactNative(){return this.framework===b.gQ.ReactNative||this.framework===b.gQ.Expo}observeFrameworkChanges(E){(0,x.QW)(E)}};const I=N=>(({category:N,action:E}={})=>{const h=[[y,M(p)]];if(N&&h.push([N,E]),h.push(["framework",(0,x.Cj)()]),N&&E){const T=((N,E)=>R[N]?.[E]?.additionalDetails)(N,E);T&&T.forEach(C=>{h.push(C)})}return h})(N).map(([T,C])=>T&&C?`${T}/${C}`:T).join(" ")},81220:(ee,L,g)=>{g.d(L,{WD:()=>p,gQ:()=>b,gq:()=>v});var b=(()=>{return(h=b||(b={})).WebUnknown="0",h.React="1",h.NextJs="2",h.Angular="3",h.VueJs="4",h.Nuxt="5",h.Svelte="6",h.ServerSideUnknown="100",h.ReactSSR="101",h.NextJsSSR="102",h.AngularSSR="103",h.VueJsSSR="104",h.NuxtSSR="105",h.SvelteSSR="106",h.ReactNative="201",h.Expo="202",b;var h})(),p=(()=>{return(h=p||(p={})).AI="ai",h.API="api",h.Auth="auth",h.Analytics="analytics",h.DataStore="datastore",h.Geo="geo",h.InAppMessaging="inappmessaging",h.Interactions="interactions",h.Predictions="predictions",h.PubSub="pubsub",h.PushNotification="pushnotification",h.Storage="storage",p;var h})(),v=(()=>{return(h=v||(v={})).GraphQl="1",h.Get="2",h.Post="3",h.Put="4",h.Patch="5",h.Del="6",h.Head="7",v;var h})()},6639:(ee,L,g)=>{g.d(L,{Z:()=>h});var b=g(3454),p=g(15861),x=g(74109);g(86437),g(95472);const O=T=>new Date(Date.now()+T);var I=g(46570),N=g(4079),E=g(6990);const h=(0,N.V)(E.S,[I.n,b.d,({credentials:T,region:C,service:G,uriEscapePath:J=!0})=>{let ae;return(re,V)=>function(){var me=(0,p.Z)(function*(le){ae=ae??0;const Y={credentials:"function"==typeof T?yield T({forceRefresh:!!V?.isCredentialsExpired}):T,signingDate:O(ae),signingRegion:C,signingService:G,uriEscapePath:J},oe=yield(0,x.C)(le,Y),be=yield re(oe),Ee=(({headers:T}={})=>T?.date??T?.Date??T?.["x-amz-date"])(be);return Ee&&(ae=((T,C)=>((T,C)=>Math.abs(O(C).getTime()-T)>=3e5)(T,C)?T-Date.now():C)(Date.parse(Ee),ae)),be});return function pe(le){return me.apply(this,arguments)}}()}])},6990:(ee,L,g)=>{g.d(L,{S:()=>O});var b=g(15861),p=g(91935),x=g(87199);const R=y=>{let M;return()=>(M||(M=y()),M)},v=y=>!["HEAD","GET","DELETE"].includes(y.toUpperCase()),O=function(){var y=(0,b.Z)(function*({url:M,method:j,headers:P,body:k},{abortSignal:I,cache:N,withCrossDomainCredentials:E}){let h;try{h=yield fetch(M,{method:j,headers:P,body:v(j)?k:void 0,signal:I,cache:N,credentials:E?"include":"same-origin"})}catch(J){throw J instanceof TypeError?new p._({name:x.Z.NetworkError,message:"A network error has occurred.",underlyingError:J}):J}const T={};return h.headers?.forEach((J,ae)=>{T[ae.toLowerCase()]=J}),{statusCode:h.status,headers:T,body:null,body:Object.assign(h.body??{},{text:R(()=>h.text()),blob:R(()=>h.blob()),json:R(()=>h.json())})}});return function(j,P){return y.apply(this,arguments)}}()},54473:(ee,L,g)=>{g.d(L,{y:()=>v});var b=g(3454),p=g(46570),x=g(4079),R=g(6990);const v=(0,x.V)(R.S,[p.n,b.d])},4079:(ee,L,g)=>{g.d(L,{V:()=>b});const b=(p,x)=>(R,v)=>{const O={};let y=M=>p(M,v);for(let M=x.length-1;M>=0;M--)y=(0,x[M])(v)(y,O);return y(R)}},67834:(ee,L,g)=>{g.d(L,{j:()=>v});var b=g(15861),p=g(87199);const x=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch","BadRequestException"],R=k=>!!k&&x.includes(k),v=k=>function(){var I=(0,b.Z)(function*(N,E){const h=E??(yield k(N))??void 0,T=h?.code||h?.name,C=N?.statusCode;return{retryable:j(E)||M(C,T)||R(T)||P(C,T)}});return function(N,E){return I.apply(this,arguments)}}(),O=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException"],y=["TimeoutError","RequestTimeout","RequestTimeoutException"],M=(k,I)=>429===k||!!I&&O.includes(I),j=k=>[p.Z.NetworkError,"ERR_NETWORK"].includes(k?.name),P=(k,I)=>!!k&&[500,502,503,504].includes(k)||!!I&&y.includes(I)},79987:(ee,L,g)=>{g.d(L,{k:()=>x});var b=g(75599);const p=3e5,x=R=>{const O=(0,b.k)(p)(R);return!1===O?p:O}},3454:(ee,L,g)=>{g.d(L,{d:()=>x});var b=g(15861);const x=({maxAttempts:O=3,retryDecider:y,computeDelay:M,abortSignal:j})=>{if(O<1)throw new Error("maxAttempts must be greater than 0");return(P,k)=>function(){var I=(0,b.Z)(function*(E){let h,C,T=k.attemptsCount??0;const G=()=>{if(C)return v(C,T),C;throw v(h,T),h};for(;!j?.aborted&&T<O;){try{C=yield P(E),h=void 0}catch(re){h=re,C=void 0}T=(k.attemptsCount??0)>T?k.attemptsCount??0:T+1,k.attemptsCount=T;const{isCredentialsExpiredError:J,retryable:ae}=yield y(C,h,k);if(!ae)return G();if(k.isCredentialsExpired=!!J,!j?.aborted&&T<O){const re=M(T);yield R(re,j)}}if(j?.aborted)throw new Error("Request aborted.");return G()});return function N(E){return I.apply(this,arguments)}}()},R=(O,y)=>{if(y?.aborted)return Promise.resolve();let M,j;const P=new Promise(k=>{j=k,M=setTimeout(k,O)});return y?.addEventListener("abort",function k(I){clearTimeout(M),y?.removeEventListener("abort",k),j()}),P},v=(O,y)=>{"[object Object]"===Object.prototype.toString.call(O)&&(O.$metadata={...O.$metadata??{},attempts:y})}},74109:(ee,L,g)=>{g.d(L,{C:()=>X});const b=U=>Object.keys(U).map(H=>H.toLowerCase()).sort().join(";"),I="X-Amz-Date".toLowerCase(),N="X-Amz-Security-Token".toLowerCase(),E="aws4_request",h="AWS4-HMAC-SHA256";var V=g(86437),me=g(95472);const pe=(U,H)=>{const K=new V.f(U??void 0);return K.update(H),K.digestSync()},le=(U,H)=>{const K=pe(U,H);return(0,me.N)(K)},Y=U=>Object.entries(U).map(([H,K])=>({key:H.toLowerCase(),value:K?.trim().replace(/\s+/g," ")??""})).sort((H,K)=>H.key<K.key?-1:1).map(H=>`${H.key}:${H.value}\n`).join(""),oe=U=>Array.from(U).sort(([H,K],[Z,ye])=>H===Z?K<ye?-1:1:H<Z?-1:1).map(([H,K])=>`${be(H)}=${be(K)}`).join("&"),be=U=>encodeURIComponent(U).replace(/[!'()*]/g,Ee),Ee=U=>`%${U.charCodeAt(0).toString(16).toUpperCase()}`,Ne=(U,H=!0)=>U?H?encodeURIComponent(U).replace(/%2F/g,"/"):U:"/",we=U=>null==U?"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855":Fe(U)?le(null,U):"UNSIGNED-PAYLOAD",Fe=U=>"string"==typeof U||ArrayBuffer.isView(U)||Re(U),Re=U=>"function"==typeof ArrayBuffer&&U instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(U),X=(U,H)=>{const K=(({credentials:U,signingDate:H=new Date,signingRegion:K,signingService:Z,uriEscapePath:ye=!0})=>{const{accessKeyId:he,secretAccessKey:Se,sessionToken:se}=U,{longDate:ve,shortDate:Pe}=(U=>{const H=U.toISOString().replace(/[:-]|\.\d{3}/g,"");return{longDate:H,shortDate:H.slice(0,8)}})(H),Me=((U,H,K)=>`${U}/${H}/${K}/${E}`)(Pe,K,Z);return{accessKeyId:he,credentialScope:Me,longDate:ve,secretAccessKey:Se,sessionToken:se,shortDate:Pe,signingRegion:K,signingService:Z,uriEscapePath:ye}})(H),{accessKeyId:Z,credentialScope:ye,longDate:he,sessionToken:Se}=K,se={...U.headers};se.host=U.url.host,se[I]=he,Se&&(se[N]=Se);const ve={...U,headers:se},Pe=((U,{credentialScope:H,longDate:K,secretAccessKey:Z,shortDate:ye,signingRegion:he,signingService:Se,uriEscapePath:se})=>{const ve=(({body:U,headers:H,method:K,url:Z},ye=!0)=>[K,Ne(Z.pathname,ye),oe(Z.searchParams),Y(H),b(H),we(U)].join("\n"))(U,se),Me=((U,H,K)=>[h,U,H,K].join("\n"))(K,H,le(null,ve));return le(((U,H,K,Z)=>{const he=pe(`AWS4${U}`,H),Se=pe(he,K),se=pe(Se,Z);return pe(se,E)})(Z,ye,he,Se),Me)})(ve,K),Me=`Credential=${Z}/${ye}`,$e=`SignedHeaders=${b(se)}`;return se.authorization=`${h} ${Me}, ${$e}, Signature=${Pe}`,ve}},46570:(ee,L,g)=>{g.d(L,{n:()=>p});var b=g(15861);const p=({userAgentHeader:x="x-amz-user-agent",userAgentValue:R=""})=>v=>function(){var O=(0,b.Z)(function*(M){if(0===R.trim().length)return yield v(M);{const j=x.toLowerCase();return M.headers[j]=M.headers[j]?`${M.headers[j]} ${R}`:R,yield v(M)}});return function y(M){return O.apply(this,arguments)}}()},54974:(ee,L,g)=>{g.d(L,{e:()=>R,f:()=>x});var b=g(15861),p=g(97282);const x=function(){var v=(0,b.Z)(function*(O){if(!O||O.statusCode<300)return;const y=yield R(O),j=(I=>{const[N]=I.toString().split(/[,:]+/);return N.includes("#")?N.split("#")[1]:N})(O.headers["x-amzn-errortype"]??y.code??y.__type??"UnknownError"),k=new Error(y.message??y.Message??"Unknown error");return Object.assign(k,{name:j,$metadata:(0,p.B)(O)})});return function(y){return v.apply(this,arguments)}}(),R=function(){var v=(0,b.Z)(function*(O){if(!O.body)throw new Error("Missing response payload");const y=yield O.body.json();return Object.assign(y,{$metadata:(0,p.B)(O)})});return function(y){return v.apply(this,arguments)}}()},97282:(ee,L,g)=>{g.d(L,{B:()=>b});const b=x=>{const{headers:R,statusCode:v}=x;return{...p(x)?x.$metadata:{},httpStatusCode:v,requestId:R["x-amzn-requestid"]??R["x-amzn-request-id"]??R["x-amz-request-id"],extendedRequestId:R["x-amz-id-2"],cfId:R["x-amz-cf-id"]}},p=x=>"object"==typeof x?.$metadata},92261:(ee,L,g)=>{g.d(L,{Mt:()=>p,YG:()=>b,z2:()=>x});const b="Logging",p="x-amz-user-agent",x="NoHubcallbackProvidedException"},91935:(ee,L,g)=>{g.d(L,{_:()=>b});class b extends Error{constructor({message:x,name:R,recoverySuggestion:v,underlyingError:O}){super(x),this.name=R,this.underlyingError=O,this.recoverySuggestion=v,this.constructor=b,Object.setPrototypeOf(this,b.prototype)}}},87199:(ee,L,g)=>{g.d(L,{Z:()=>b});var b=(()=>{return(p=b||(b={})).NoEndpointId="NoEndpointId",p.PlatformNotSupported="PlatformNotSupported",p.Unknown="Unknown",p.NetworkError="NetworkError",b;var p})()},99120:(ee,L,g)=>{g.d(L,{a:()=>b,z:()=>p});const b=URL,p=URLSearchParams},38261:(ee,L,g)=>{g.d(L,{r:()=>p});const p=g(83364).v4},10180:(ee,L,g)=>{g.d(L,{Ds:()=>x,tl:()=>R});var b=g(91935);const x=()=>{if(typeof window<"u"&&"function"==typeof window.btoa)return window.btoa;if("function"==typeof btoa)return btoa;throw new b._({name:"Base64EncoderError",message:"Cannot resolve the `btoa` function from the environment."})},R=()=>{if(typeof window<"u"&&"function"==typeof window.atob)return window.atob;if("function"==typeof atob)return atob;throw new b._({name:"Base64EncoderError",message:"Cannot resolve the `atob` function from the environment."})}},90206:(ee,L,g)=>{g.d(L,{t:()=>b});const b=3e5},75599:(ee,L,g)=>{g.d(L,{k:()=>p});var b=g(90206);function p(x=b.t){return O=>{const y=2**O*100+100*Math.random();return!(y>x)&&y}}}}]);