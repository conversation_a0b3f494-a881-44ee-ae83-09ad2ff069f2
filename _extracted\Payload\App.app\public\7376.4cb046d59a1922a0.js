(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7376,9223],{67376:(Z,y,f)=>{f.r(y),f.d(y,{CdkFixedSizeVirtualScroll:()=>L,CdkScrollable:()=>R,CdkScrollableModule:()=>k,CdkVirtualForOf:()=>B,CdkVirtualScrollViewport:()=>b,CdkVirtualScrollable:()=>C,CdkVirtualScrollableElement:()=>j,CdkVirtualScrollableWindow:()=>A,DEFAULT_RESIZE_TIME:()=>V,DEFAULT_SCROLL_TIME:()=>O,FixedSizeVirtualScrollStrategy:()=>D,ScrollDispatcher:()=>m,ScrollingModule:()=>W,VIRTUAL_SCROLLABLE:()=>S,VIRTUAL_SCROLL_STRATEGY:()=>w,ViewportRuler:()=>z,_fixedSizeVirtualScrollStrategyFactory:()=>x});var p=f(32565),i=f(99877),c=f(42168),u=f(84757),_=f(66354),E=f(17007),g=f(32044),v=f(95592);const T=["contentWrapper"],M=["*"],w=new i.InjectionToken("VIRTUAL_SCROLL_STRATEGY");class D{constructor(s,e,t){this._scrolledIndexChange=new c.Subject,this.scrolledIndexChange=this._scrolledIndexChange.pipe((0,u.distinctUntilChanged)()),this._viewport=null,this._itemSize=s,this._minBufferPx=e,this._maxBufferPx=t}attach(s){this._viewport=s,this._updateTotalContentSize(),this._updateRenderedRange()}detach(){this._scrolledIndexChange.complete(),this._viewport=null}updateItemAndBufferSize(s,e,t){this._itemSize=s,this._minBufferPx=e,this._maxBufferPx=t,this._updateTotalContentSize(),this._updateRenderedRange()}onContentScrolled(){this._updateRenderedRange()}onDataLengthChanged(){this._updateTotalContentSize(),this._updateRenderedRange()}onContentRendered(){}onRenderedOffsetChanged(){}scrollToIndex(s,e){this._viewport&&this._viewport.scrollToOffset(s*this._itemSize,e)}_updateTotalContentSize(){this._viewport&&this._viewport.setTotalContentSize(this._viewport.getDataLength()*this._itemSize)}_updateRenderedRange(){if(!this._viewport)return;const s=this._viewport.getRenderedRange(),e={start:s.start,end:s.end},t=this._viewport.getViewportSize(),r=this._viewport.getDataLength();let o=this._viewport.measureScrollOffset(),l=this._itemSize>0?o/this._itemSize:0;if(e.end>r){const a=Math.ceil(t/this._itemSize),d=Math.max(0,Math.min(l,r-a));l!=d&&(l=d,o=d*this._itemSize,e.start=Math.floor(l)),e.end=Math.max(0,Math.min(r,e.start+a))}const h=o-e.start*this._itemSize;if(h<this._minBufferPx&&0!=e.start){const a=Math.ceil((this._maxBufferPx-h)/this._itemSize);e.start=Math.max(0,e.start-a),e.end=Math.min(r,Math.ceil(l+(t+this._minBufferPx)/this._itemSize))}else{const a=e.end*this._itemSize-(o+t);if(a<this._minBufferPx&&e.end!=r){const d=Math.ceil((this._maxBufferPx-a)/this._itemSize);d>0&&(e.end=Math.min(r,e.end+d),e.start=Math.max(0,Math.floor(l-this._minBufferPx/this._itemSize)))}}this._viewport.setRenderedRange(e),this._viewport.setRenderedContentOffset(this._itemSize*e.start),this._scrolledIndexChange.next(Math.floor(l))}}function x(n){return n._scrollStrategy}let L=(()=>{class n{constructor(){this._itemSize=20,this._minBufferPx=100,this._maxBufferPx=200,this._scrollStrategy=new D(this.itemSize,this.minBufferPx,this.maxBufferPx)}get itemSize(){return this._itemSize}set itemSize(e){this._itemSize=(0,p.coerceNumberProperty)(e)}get minBufferPx(){return this._minBufferPx}set minBufferPx(e){this._minBufferPx=(0,p.coerceNumberProperty)(e)}get maxBufferPx(){return this._maxBufferPx}set maxBufferPx(e){this._maxBufferPx=(0,p.coerceNumberProperty)(e)}ngOnChanges(){this._scrollStrategy.updateItemAndBufferSize(this.itemSize,this.minBufferPx,this.maxBufferPx)}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["cdk-virtual-scroll-viewport","itemSize",""]],inputs:{itemSize:"itemSize",minBufferPx:"minBufferPx",maxBufferPx:"maxBufferPx"},standalone:!0,features:[i.\u0275\u0275ProvidersFeature([{provide:w,useFactory:x,deps:[(0,i.forwardRef)(()=>n)]}]),i.\u0275\u0275NgOnChangesFeature]}),n})();const O=20;let m=(()=>{class n{constructor(e,t,r){this._ngZone=e,this._platform=t,this._scrolled=new c.Subject,this._globalSubscription=null,this._scrolledCount=0,this.scrollContainers=new Map,this._document=r}register(e){this.scrollContainers.has(e)||this.scrollContainers.set(e,e.elementScrolled().subscribe(()=>this._scrolled.next(e)))}deregister(e){const t=this.scrollContainers.get(e);t&&(t.unsubscribe(),this.scrollContainers.delete(e))}scrolled(e=O){return this._platform.isBrowser?new c.Observable(t=>{this._globalSubscription||this._addGlobalListener();const r=e>0?this._scrolled.pipe((0,u.auditTime)(e)).subscribe(t):this._scrolled.subscribe(t);return this._scrolledCount++,()=>{r.unsubscribe(),this._scrolledCount--,this._scrolledCount||this._removeGlobalListener()}}):(0,c.of)()}ngOnDestroy(){this._removeGlobalListener(),this.scrollContainers.forEach((e,t)=>this.deregister(t)),this._scrolled.complete()}ancestorScrolled(e,t){const r=this.getAncestorScrollContainers(e);return this.scrolled(t).pipe((0,u.filter)(o=>!o||r.indexOf(o)>-1))}getAncestorScrollContainers(e){const t=[];return this.scrollContainers.forEach((r,o)=>{this._scrollableContainsElement(o,e)&&t.push(o)}),t}_getWindow(){return this._document.defaultView||window}_scrollableContainsElement(e,t){let r=(0,p.coerceElement)(t),o=e.getElementRef().nativeElement;do{if(r==o)return!0}while(r=r.parentElement);return!1}_addGlobalListener(){this._globalSubscription=this._ngZone.runOutsideAngular(()=>{const e=this._getWindow();return(0,c.fromEvent)(e.document,"scroll").subscribe(()=>this._scrolled.next())})}_removeGlobalListener(){this._globalSubscription&&(this._globalSubscription.unsubscribe(),this._globalSubscription=null)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275inject(i.NgZone),i.\u0275\u0275inject(_.Platform),i.\u0275\u0275inject(E.DOCUMENT,8))},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),R=(()=>{class n{constructor(e,t,r,o){this.elementRef=e,this.scrollDispatcher=t,this.ngZone=r,this.dir=o,this._destroyed=new c.Subject,this._elementScrolled=new c.Observable(l=>this.ngZone.runOutsideAngular(()=>(0,c.fromEvent)(this.elementRef.nativeElement,"scroll").pipe((0,u.takeUntil)(this._destroyed)).subscribe(l)))}ngOnInit(){this.scrollDispatcher.register(this)}ngOnDestroy(){this.scrollDispatcher.deregister(this),this._destroyed.next(),this._destroyed.complete()}elementScrolled(){return this._elementScrolled}getElementRef(){return this.elementRef}scrollTo(e){const t=this.elementRef.nativeElement,r=this.dir&&"rtl"==this.dir.value;null==e.left&&(e.left=r?e.end:e.start),null==e.right&&(e.right=r?e.start:e.end),null!=e.bottom&&(e.top=t.scrollHeight-t.clientHeight-e.bottom),r&&0!=(0,_.getRtlScrollAxisType)()?(null!=e.left&&(e.right=t.scrollWidth-t.clientWidth-e.left),2==(0,_.getRtlScrollAxisType)()?e.left=e.right:1==(0,_.getRtlScrollAxisType)()&&(e.left=e.right?-e.right:e.right)):null!=e.right&&(e.left=t.scrollWidth-t.clientWidth-e.right),this._applyScrollToOptions(e)}_applyScrollToOptions(e){const t=this.elementRef.nativeElement;(0,_.supportsScrollBehavior)()?t.scrollTo(e):(null!=e.top&&(t.scrollTop=e.top),null!=e.left&&(t.scrollLeft=e.left))}measureScrollOffset(e){const t="left",r="right",o=this.elementRef.nativeElement;if("top"==e)return o.scrollTop;if("bottom"==e)return o.scrollHeight-o.clientHeight-o.scrollTop;const l=this.dir&&"rtl"==this.dir.value;return"start"==e?e=l?r:t:"end"==e&&(e=l?t:r),l&&2==(0,_.getRtlScrollAxisType)()?e==t?o.scrollWidth-o.clientWidth-o.scrollLeft:o.scrollLeft:l&&1==(0,_.getRtlScrollAxisType)()?e==t?o.scrollLeft+o.scrollWidth-o.clientWidth:-o.scrollLeft:e==t?o.scrollLeft:o.scrollWidth-o.clientWidth-o.scrollLeft}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(m),i.\u0275\u0275directiveInject(i.NgZone),i.\u0275\u0275directiveInject(g.Directionality,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","cdk-scrollable",""],["","cdkScrollable",""]],standalone:!0}),n})();const V=20;let z=(()=>{class n{constructor(e,t,r){this._platform=e,this._change=new c.Subject,this._changeListener=o=>{this._change.next(o)},this._document=r,t.runOutsideAngular(()=>{if(e.isBrowser){const o=this._getWindow();o.addEventListener("resize",this._changeListener),o.addEventListener("orientationchange",this._changeListener)}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){if(this._platform.isBrowser){const e=this._getWindow();e.removeEventListener("resize",this._changeListener),e.removeEventListener("orientationchange",this._changeListener)}this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();const e={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),e}getViewportRect(){const e=this.getViewportScrollPosition(),{width:t,height:r}=this.getViewportSize();return{top:e.top,left:e.left,bottom:e.top+r,right:e.left+t,height:r,width:t}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};const e=this._document,t=this._getWindow(),r=e.documentElement,o=r.getBoundingClientRect();return{top:-o.top||e.body.scrollTop||t.scrollY||r.scrollTop||0,left:-o.left||e.body.scrollLeft||t.scrollX||r.scrollLeft||0}}change(e=V){return e>0?this._change.pipe((0,u.auditTime)(e)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){const e=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:e.innerWidth,height:e.innerHeight}:{width:0,height:0}}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275inject(_.Platform),i.\u0275\u0275inject(i.NgZone),i.\u0275\u0275inject(E.DOCUMENT,8))},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();const S=new i.InjectionToken("VIRTUAL_SCROLLABLE");let C=(()=>{class n extends R{constructor(e,t,r,o){super(e,t,r,o)}measureViewportSize(e){const t=this.elementRef.nativeElement;return"horizontal"===e?t.clientWidth:t.clientHeight}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(m),i.\u0275\u0275directiveInject(i.NgZone),i.\u0275\u0275directiveInject(g.Directionality,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,features:[i.\u0275\u0275InheritDefinitionFeature]}),n})();const P=typeof requestAnimationFrame<"u"?c.animationFrameScheduler:c.asapScheduler;let b=(()=>{class n extends C{get orientation(){return this._orientation}set orientation(e){this._orientation!==e&&(this._orientation=e,this._calculateSpacerSize())}get appendOnly(){return this._appendOnly}set appendOnly(e){this._appendOnly=(0,p.coerceBooleanProperty)(e)}constructor(e,t,r,o,l,h,a,d){super(e,h,r,l),this.elementRef=e,this._changeDetectorRef=t,this._scrollStrategy=o,this.scrollable=d,this._platform=(0,i.inject)(_.Platform),this._detachedSubject=new c.Subject,this._renderedRangeSubject=new c.Subject,this._orientation="vertical",this._appendOnly=!1,this.scrolledIndexChange=new c.Observable(N=>this._scrollStrategy.scrolledIndexChange.subscribe(U=>Promise.resolve().then(()=>this.ngZone.run(()=>N.next(U))))),this.renderedRangeStream=this._renderedRangeSubject,this._totalContentSize=0,this._totalContentWidth="",this._totalContentHeight="",this._renderedRange={start:0,end:0},this._dataLength=0,this._viewportSize=0,this._renderedContentOffset=0,this._renderedContentOffsetNeedsRewrite=!1,this._isChangeDetectionPending=!1,this._runAfterChangeDetection=[],this._viewportChanges=c.Subscription.EMPTY,this._viewportChanges=a.change().subscribe(()=>{this.checkViewportSize()}),this.scrollable||(this.elementRef.nativeElement.classList.add("cdk-virtual-scrollable"),this.scrollable=this)}ngOnInit(){this._platform.isBrowser&&(this.scrollable===this&&super.ngOnInit(),this.ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>{this._measureViewportSize(),this._scrollStrategy.attach(this),this.scrollable.elementScrolled().pipe((0,u.startWith)(null),(0,u.auditTime)(0,P)).subscribe(()=>this._scrollStrategy.onContentScrolled()),this._markChangeDetectionNeeded()})))}ngOnDestroy(){this.detach(),this._scrollStrategy.detach(),this._renderedRangeSubject.complete(),this._detachedSubject.complete(),this._viewportChanges.unsubscribe(),super.ngOnDestroy()}attach(e){this.ngZone.runOutsideAngular(()=>{this._forOf=e,this._forOf.dataStream.pipe((0,u.takeUntil)(this._detachedSubject)).subscribe(t=>{const r=t.length;r!==this._dataLength&&(this._dataLength=r,this._scrollStrategy.onDataLengthChanged()),this._doChangeDetection()})})}detach(){this._forOf=null,this._detachedSubject.next()}getDataLength(){return this._dataLength}getViewportSize(){return this._viewportSize}getRenderedRange(){return this._renderedRange}measureBoundingClientRectWithScrollOffset(e){return this.getElementRef().nativeElement.getBoundingClientRect()[e]}setTotalContentSize(e){this._totalContentSize!==e&&(this._totalContentSize=e,this._calculateSpacerSize(),this._markChangeDetectionNeeded())}setRenderedRange(e){(function F(n,s){return n.start==s.start&&n.end==s.end})(this._renderedRange,e)||(this.appendOnly&&(e={start:0,end:Math.max(this._renderedRange.end,e.end)}),this._renderedRangeSubject.next(this._renderedRange=e),this._markChangeDetectionNeeded(()=>this._scrollStrategy.onContentRendered()))}getOffsetToRenderedContentStart(){return this._renderedContentOffsetNeedsRewrite?null:this._renderedContentOffset}setRenderedContentOffset(e,t="to-start"){e=this.appendOnly&&"to-start"===t?0:e;const o="horizontal"==this.orientation,l=o?"X":"Y";let a=`translate${l}(${Number((o&&this.dir&&"rtl"==this.dir.value?-1:1)*e)}px)`;this._renderedContentOffset=e,"to-end"===t&&(a+=` translate${l}(-100%)`,this._renderedContentOffsetNeedsRewrite=!0),this._renderedContentTransform!=a&&(this._renderedContentTransform=a,this._markChangeDetectionNeeded(()=>{this._renderedContentOffsetNeedsRewrite?(this._renderedContentOffset-=this.measureRenderedContentSize(),this._renderedContentOffsetNeedsRewrite=!1,this.setRenderedContentOffset(this._renderedContentOffset)):this._scrollStrategy.onRenderedOffsetChanged()}))}scrollToOffset(e,t="auto"){const r={behavior:t};"horizontal"===this.orientation?r.start=e:r.top=e,this.scrollable.scrollTo(r)}scrollToIndex(e,t="auto"){this._scrollStrategy.scrollToIndex(e,t)}measureScrollOffset(e){let t;return t=this.scrollable==this?r=>super.measureScrollOffset(r):r=>this.scrollable.measureScrollOffset(r),Math.max(0,t(e??("horizontal"===this.orientation?"start":"top"))-this.measureViewportOffset())}measureViewportOffset(e){let t;const r="left",o="right",l="rtl"==this.dir?.value;t="start"==e?l?o:r:"end"==e?l?r:o:e||("horizontal"===this.orientation?"left":"top");const h=this.scrollable.measureBoundingClientRectWithScrollOffset(t);return this.elementRef.nativeElement.getBoundingClientRect()[t]-h}measureRenderedContentSize(){const e=this._contentWrapper.nativeElement;return"horizontal"===this.orientation?e.offsetWidth:e.offsetHeight}measureRangeSize(e){return this._forOf?this._forOf.measureRangeSize(e,this.orientation):0}checkViewportSize(){this._measureViewportSize(),this._scrollStrategy.onDataLengthChanged()}_measureViewportSize(){this._viewportSize=this.scrollable.measureViewportSize(this.orientation)}_markChangeDetectionNeeded(e){e&&this._runAfterChangeDetection.push(e),this._isChangeDetectionPending||(this._isChangeDetectionPending=!0,this.ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>{this._doChangeDetection()})))}_doChangeDetection(){this._isChangeDetectionPending=!1,this._contentWrapper.nativeElement.style.transform=this._renderedContentTransform,this.ngZone.run(()=>this._changeDetectorRef.markForCheck());const e=this._runAfterChangeDetection;this._runAfterChangeDetection=[];for(const t of e)t()}_calculateSpacerSize(){this._totalContentHeight="horizontal"===this.orientation?"":`${this._totalContentSize}px`,this._totalContentWidth="horizontal"===this.orientation?`${this._totalContentSize}px`:""}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(i.ChangeDetectorRef),i.\u0275\u0275directiveInject(i.NgZone),i.\u0275\u0275directiveInject(w,8),i.\u0275\u0275directiveInject(g.Directionality,8),i.\u0275\u0275directiveInject(m),i.\u0275\u0275directiveInject(z),i.\u0275\u0275directiveInject(S,8))},n.\u0275cmp=i.\u0275\u0275defineComponent({type:n,selectors:[["cdk-virtual-scroll-viewport"]],viewQuery:function(e,t){if(1&e&&i.\u0275\u0275viewQuery(T,7),2&e){let r;i.\u0275\u0275queryRefresh(r=i.\u0275\u0275loadQuery())&&(t._contentWrapper=r.first)}},hostAttrs:[1,"cdk-virtual-scroll-viewport"],hostVars:4,hostBindings:function(e,t){2&e&&i.\u0275\u0275classProp("cdk-virtual-scroll-orientation-horizontal","horizontal"===t.orientation)("cdk-virtual-scroll-orientation-vertical","horizontal"!==t.orientation)},inputs:{orientation:"orientation",appendOnly:"appendOnly"},outputs:{scrolledIndexChange:"scrolledIndexChange"},standalone:!0,features:[i.\u0275\u0275ProvidersFeature([{provide:R,useFactory:(s,e)=>s||e,deps:[[new i.Optional,new i.Inject(S)],n]}]),i.\u0275\u0275InheritDefinitionFeature,i.\u0275\u0275StandaloneFeature],ngContentSelectors:M,decls:4,vars:4,consts:[[1,"cdk-virtual-scroll-content-wrapper"],["contentWrapper",""],[1,"cdk-virtual-scroll-spacer"]],template:function(e,t){1&e&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0,1),i.\u0275\u0275projection(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(3,"div",2)),2&e&&(i.\u0275\u0275advance(3),i.\u0275\u0275styleProp("width",t._totalContentWidth)("height",t._totalContentHeight))},styles:["cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}"],encapsulation:2,changeDetection:0}),n})();function I(n,s,e){if(!e.getBoundingClientRect)return 0;const r=e.getBoundingClientRect();return"horizontal"===n?"start"===s?r.left:r.right:"start"===s?r.top:r.bottom}let B=(()=>{class n{get cdkVirtualForOf(){return this._cdkVirtualForOf}set cdkVirtualForOf(e){this._cdkVirtualForOf=e,(0,v.isDataSource)(e)?this._dataSourceChanges.next(e):this._dataSourceChanges.next(new v.ArrayDataSource((0,c.isObservable)(e)?e:Array.from(e||[])))}get cdkVirtualForTrackBy(){return this._cdkVirtualForTrackBy}set cdkVirtualForTrackBy(e){this._needsUpdate=!0,this._cdkVirtualForTrackBy=e?(t,r)=>e(t+(this._renderedRange?this._renderedRange.start:0),r):void 0}set cdkVirtualForTemplate(e){e&&(this._needsUpdate=!0,this._template=e)}get cdkVirtualForTemplateCacheSize(){return this._viewRepeater.viewCacheSize}set cdkVirtualForTemplateCacheSize(e){this._viewRepeater.viewCacheSize=(0,p.coerceNumberProperty)(e)}constructor(e,t,r,o,l,h){this._viewContainerRef=e,this._template=t,this._differs=r,this._viewRepeater=o,this._viewport=l,this.viewChange=new c.Subject,this._dataSourceChanges=new c.Subject,this.dataStream=this._dataSourceChanges.pipe((0,u.startWith)(null),(0,u.pairwise)(),(0,u.switchMap)(([a,d])=>this._changeDataSource(a,d)),(0,u.shareReplay)(1)),this._differ=null,this._needsUpdate=!1,this._destroyed=new c.Subject,this.dataStream.subscribe(a=>{this._data=a,this._onRenderedDataChange()}),this._viewport.renderedRangeStream.pipe((0,u.takeUntil)(this._destroyed)).subscribe(a=>{this._renderedRange=a,this.viewChange.observers.length&&h.run(()=>this.viewChange.next(this._renderedRange)),this._onRenderedDataChange()}),this._viewport.attach(this)}measureRangeSize(e,t){if(e.start>=e.end)return 0;const r=e.start-this._renderedRange.start,o=e.end-e.start;let l,h;for(let a=0;a<o;a++){const d=this._viewContainerRef.get(a+r);if(d&&d.rootNodes.length){l=h=d.rootNodes[0];break}}for(let a=o-1;a>-1;a--){const d=this._viewContainerRef.get(a+r);if(d&&d.rootNodes.length){h=d.rootNodes[d.rootNodes.length-1];break}}return l&&h?I(t,"end",h)-I(t,"start",l):0}ngDoCheck(){if(this._differ&&this._needsUpdate){const e=this._differ.diff(this._renderedItems);e?this._applyChanges(e):this._updateContext(),this._needsUpdate=!1}}ngOnDestroy(){this._viewport.detach(),this._dataSourceChanges.next(void 0),this._dataSourceChanges.complete(),this.viewChange.complete(),this._destroyed.next(),this._destroyed.complete(),this._viewRepeater.detach()}_onRenderedDataChange(){this._renderedRange&&(this._renderedItems=this._data.slice(this._renderedRange.start,this._renderedRange.end),this._differ||(this._differ=this._differs.find(this._renderedItems).create((e,t)=>this.cdkVirtualForTrackBy?this.cdkVirtualForTrackBy(e,t):t)),this._needsUpdate=!0)}_changeDataSource(e,t){return e&&e.disconnect(this),this._needsUpdate=!0,t?t.connect(this):(0,c.of)()}_updateContext(){const e=this._data.length;let t=this._viewContainerRef.length;for(;t--;){const r=this._viewContainerRef.get(t);r.context.index=this._renderedRange.start+t,r.context.count=e,this._updateComputedContextProperties(r.context),r.detectChanges()}}_applyChanges(e){this._viewRepeater.applyChanges(e,this._viewContainerRef,(o,l,h)=>this._getEmbeddedViewArgs(o,h),o=>o.item),e.forEachIdentityChange(o=>{this._viewContainerRef.get(o.currentIndex).context.$implicit=o.item});const t=this._data.length;let r=this._viewContainerRef.length;for(;r--;){const o=this._viewContainerRef.get(r);o.context.index=this._renderedRange.start+r,o.context.count=t,this._updateComputedContextProperties(o.context)}}_updateComputedContextProperties(e){e.first=0===e.index,e.last=e.index===e.count-1,e.even=e.index%2==0,e.odd=!e.even}_getEmbeddedViewArgs(e,t){return{templateRef:this._template,context:{$implicit:e.item,cdkVirtualForOf:this._cdkVirtualForOf,index:-1,count:-1,first:!1,last:!1,odd:!1,even:!1},index:t}}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ViewContainerRef),i.\u0275\u0275directiveInject(i.TemplateRef),i.\u0275\u0275directiveInject(i.IterableDiffers),i.\u0275\u0275directiveInject(v._VIEW_REPEATER_STRATEGY),i.\u0275\u0275directiveInject(b,4),i.\u0275\u0275directiveInject(i.NgZone))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","cdkVirtualFor","","cdkVirtualForOf",""]],inputs:{cdkVirtualForOf:"cdkVirtualForOf",cdkVirtualForTrackBy:"cdkVirtualForTrackBy",cdkVirtualForTemplate:"cdkVirtualForTemplate",cdkVirtualForTemplateCacheSize:"cdkVirtualForTemplateCacheSize"},standalone:!0,features:[i.\u0275\u0275ProvidersFeature([{provide:v._VIEW_REPEATER_STRATEGY,useClass:v._RecycleViewRepeaterStrategy}])]}),n})(),j=(()=>{class n extends C{constructor(e,t,r,o){super(e,t,r,o)}measureBoundingClientRectWithScrollOffset(e){return this.getElementRef().nativeElement.getBoundingClientRect()[e]-this.measureScrollOffset(e)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(m),i.\u0275\u0275directiveInject(i.NgZone),i.\u0275\u0275directiveInject(g.Directionality,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","cdkVirtualScrollingElement",""]],hostAttrs:[1,"cdk-virtual-scrollable"],standalone:!0,features:[i.\u0275\u0275ProvidersFeature([{provide:S,useExisting:n}]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),A=(()=>{class n extends C{constructor(e,t,r){super(new i.ElementRef(document.documentElement),e,t,r),this._elementScrolled=new c.Observable(o=>this.ngZone.runOutsideAngular(()=>(0,c.fromEvent)(document,"scroll").pipe((0,u.takeUntil)(this._destroyed)).subscribe(o)))}measureBoundingClientRectWithScrollOffset(e){return this.getElementRef().nativeElement.getBoundingClientRect()[e]}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(m),i.\u0275\u0275directiveInject(i.NgZone),i.\u0275\u0275directiveInject(g.Directionality,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["cdk-virtual-scroll-viewport","scrollWindow",""]],standalone:!0,features:[i.\u0275\u0275ProvidersFeature([{provide:S,useExisting:n}]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),k=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({}),n})(),W=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({imports:[g.BidiModule,k,b,g.BidiModule,k]}),n})()}}]);