(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3242],{43242:(B,E,t)=>{t.r(E),t.d(E,{MboTransferGenericDestinationPageModule:()=>z});var d=t(17007),I=t(78007),b=t(79798),i=t(30263),T=t(15861),S=t(24495),h=t(39904),l=t(95437),n=t(57544),r=t(99013),s=t(18767),p=t(8834),N=t(40914),e=t(99877),x=t(2460),y=t(45542);function C(v,P){if(1&v){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(o);const D=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(D.onHidden())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"No mostrar m\xe1s"),e.\u0275\u0275elementEnd()()}}const{MAX_UNREGISTERED:a,MIN_UNREGISTERED:m}=N.R,A=h.Z6.TRANSFERS.GENERIC;let _=(()=>{class v{constructor(o,g){this.mboProvider=o,this.unregisteredInformation=g,this.minAmount=(0,p.b)({value:m}),this.maxAmount=(0,p.b)({value:a})}ngBoccPortal(o){this.overlay=o}onSubmit(){this.redirect()}onHidden(){var o=this;return(0,T.Z)(function*(){(yield o.unregisteredInformation.hidden()).when({success:()=>o.redirect()})})()}redirect(){this.overlay?.close(),this.mboProvider.navigation.next(A.UNREGISTERED)}}return v.\u0275fac=function(o){return new(o||v)(e.\u0275\u0275directiveInject(l.ZL),e.\u0275\u0275directiveInject(r.eF))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-transfer-unregistered-bluescreen"]],decls:18,vars:3,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfer-unregistered-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfer-unregistered-bluescreen_hidden","bocc-button","flat","class","bocc-bluescreen__button",3,"click",4,"ngIf"],["id","btn_transfer-unregistered-bluescreen_hidden","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(o,g){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4,"\xa1Recuerda!"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Las transacciones que realices a cuentas diferentes del grupo AVAL despu\xe9s de las 3:00 PM y durante el fin de semana, ser\xe1n efectivas al siguiente d\xeda h\xe1bil. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9),e.\u0275\u0275element(10,"br")(11,"br"),e.\u0275\u0275text(12," (M\xe1ximo 3 operaciones diarias, y 6 al mes.) "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(13,"div",5)(14,"button",6),e.\u0275\u0275listener("click",function(){return g.onSubmit()}),e.\u0275\u0275elementStart(15,"span"),e.\u0275\u0275text(16,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(17,C,3,0,"button",7),e.\u0275\u0275elementEnd()),2&o&&(e.\u0275\u0275advance(9),e.\u0275\u0275textInterpolate2(" Ten en cuenta, solo podr\xe1s transferir $",g.maxAmount," al mes y hasta $",g.minAmount," diarios sin necesidad de inscripci\xf3n a cuentas del Banco de Occidente."),e.\u0275\u0275advance(8),e.\u0275\u0275property("ngIf",!1))},dependencies:[d.NgIf,x.Z,y.P],styles:["mbo-transfer-unregistered-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),v})();var F=t(93637),c=t(97549),f=t(50142),M=t(13961),R=t(48774),O=t(23436),V=t(13043);function k(v,P){if(1&v){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-category",9),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(o);const D=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(D.onTagAval())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"A un"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(3,"img",10),e.\u0275\u0275elementStart(4,"label",11),e.\u0275\u0275text(5,"o llave"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-icon",12),e.\u0275\u0275listener("click",function(D){e.\u0275\u0275restoreView(o);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onBoarding(D))}),e.\u0275\u0275elementEnd()()}if(2&v){const o=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",o.requesting)}}function Z(v,P){if(1&v){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-category",13),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(o);const D=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(D.onCelToCel())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"A un celular"),e.\u0275\u0275elementEnd()()}if(2&v){const o=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",o.requesting)}}const{CELTOCEL:K,GENERIC:j,TAG_AVAL:H}=h.Z6.TRANSFERS;let W=(()=>{class v{constructor(o,g,D,U,G,L,$,Y,w,X,J){this.activateRoute=o,this.blueScreenService=g,this.mboProvider=D,this.requestConfiguration=U,this.requestTransfersConfiguration=G,this.unregisteredInformation=L,this.managerTransfer=$,this.cancelProvider=Y,this.managerCelToCel=w,this.managerTagAval=X,this.onboardingScreenService=J,this.hasError=!1,this.confirmation=!1,this.backInvalid=!1,this.requesting=!0,this.affiliations=[],this.enabledTagAval=!1,this.title="Destino",this.backAction={id:"btn_transfer-generic-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation||this.requesting||this.backInvalid,click:()=>{this.cancelProvider.execute(this.confirmation)}},this.cancelAction={id:"btn_transfer-generic-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}},this.affiliationControl=new n.FormControl(void 0,[S.C1])}ngOnInit(){this.initializatedConfiguration(),this.initializatedTagAval(),this.unregisteredBlueScreen=this.blueScreenService.create(_),this.productId&&(this.title="Transferir")}get msgError(){return this.hasError?"Debido a una falla t\xe9cnica no pudimos cargar tus cuentas inscritas":"No tienes cuentas inscritas."}onUnregistered(){var o=this;return(0,T.Z)(function*(){(yield o.unregisteredInformation.verify()).when({success:()=>{o.mboProvider.navigation.next(j.UNREGISTERED)},failure:({value:g})=>{g?o.unregisteredBlueScreen?.open():o.mboProvider.navigation.next(j.UNREGISTERED)}})})()}onAffiliation(o){this.managerTransfer.setAffiliation(o).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?j.CONFIRMATION:j.AMOUNT)}})}onCelToCel(){this.managerCelToCel.setSource(this.source,!0).when({success:()=>{this.mboProvider.navigation.next(K.SEND.DESTINATION)}})}onTagAval(){this.managerTagAval.setSource(this.source,!0).when({success:()=>{this.mboProvider.navigation.next(H.DESTINATION)}})}onBoarding(o){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(M.Z)),this.tagAvalonboarding.open(),o.stopPropagation()}initializatedConfiguration(){var o=this;return(0,T.Z)(function*(){o.productId=o.activateRoute.snapshot.queryParams.productId,(yield o.requestConfiguration.destination(o.productId)).when({success:({affiliations:g,confirmation:D,destination:U,products:G,source:L})=>{if(o.source=L,!L)return o.cancelProvider.execute(o.confirmation);U&&o.affiliationControl.setValue(U),o.backInvalid=G.length<2,o.hasError=!1,o.affiliations=g,o.confirmation=D},failure:()=>{o.hasError=!0,o.affiliations=[]}},()=>{o.requesting=!1})})()}initializatedTagAval(){var o=this;return(0,T.Z)(function*(){(yield o.requestTransfersConfiguration.transfers()).when({success:({enabledTagAval:g})=>{o.enabledTagAval=g}})})()}}return v.\u0275fac=function(o){return new(o||v)(e.\u0275\u0275directiveInject(I.ActivatedRoute),e.\u0275\u0275directiveInject(i.Dl),e.\u0275\u0275directiveInject(l.ZL),e.\u0275\u0275directiveInject(r.ow),e.\u0275\u0275directiveInject(F.a),e.\u0275\u0275directiveInject(r.eF),e.\u0275\u0275directiveInject(r.Al),e.\u0275\u0275directiveInject(s.S),e.\u0275\u0275directiveInject(c.E),e.\u0275\u0275directiveInject(f.$),e.\u0275\u0275directiveInject(b.x6))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-transfer-generic-destination-page"]],decls:11,vars:12,consts:[[1,"mbo-transfer-generic-destination-page__content"],[1,"mbo-transfer-generic-destination-page__header"],["progress","50%",3,"leftAction","title","rightAction"],[1,"mbo-transfer-generic-destination-page__body"],[1,"mbo-transfer-generic-destination-page__message","subtitle2-medium"],["id","btn_transfers-product_aval-key_destination","icon","tag-aval","versionNews","5.8.2",3,"disabled","click",4,"ngIf"],["id","btn_transfers-product_cell-to-cell-destination","icon","device-mobile","versionNews","5.8.2",3,"disabled","click",4,"ngIf"],["id","btn_transfer-generic-destination_quickly","icon","transfer-quickly",3,"disabled","click"],["title","A CUENTAS INSCRITAS",3,"products","skeleton","msgError","header","automaticSelection","productControl","select"],["id","btn_transfers-product_aval-key_destination","icon","tag-aval","versionNews","5.8.2",3,"disabled","click"],["id","logo_transfers-product_aval-key_destination","src","assets/shared/logos/tag-aval/tag-aval-line-original.png"],[1,"mbo-transfers-product-page__key"],["icon","info-solid",3,"click"],["id","btn_transfers-product_cell-to-cell-destination","icon","device-mobile","versionNews","5.8.2",3,"disabled","click"]],template:function(o,g){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," \xbfA qui\xe9n deseas transferirle hoy? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,k,7,1,"bocc-card-category",5),e.\u0275\u0275template(7,Z,3,1,"bocc-card-category",6),e.\u0275\u0275elementStart(8,"bocc-card-category",7),e.\u0275\u0275listener("click",function(){return g.onUnregistered()}),e.\u0275\u0275text(9," A una cuenta no inscrita "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"mbo-product-destination-selector",8),e.\u0275\u0275listener("select",function(U){return g.onAffiliation(U)}),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",g.backAction)("title",g.title)("rightAction",g.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",g.enabledTagAval&&g.productId),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.productId),e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",g.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("products",g.affiliations)("skeleton",g.requesting)("msgError",g.msgError)("header",!1)("automaticSelection",!0)("productControl",g.affiliationControl))},dependencies:[d.NgIf,R.J,O.D,V.e,x.Z],styles:["/*!\n * MBO TransferGenericDestination Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 25/Jun/2022\n * Updated: 14/Mar/2025\n*/mbo-transfer-generic-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-generic-destination-page #btn_transfers-product_aval-key_destination img{height:var(--sizing-x8);margin-left:var(--sizing-x2)}mbo-transfer-generic-destination-page #btn_transfers-product_aval-key_destination bocc-icon{color:var(--color-blue-700)}mbo-transfer-generic-destination-page #btn_transfers-product_aval-key_destination label{padding-right:var(--sizing-x2)}mbo-transfer-generic-destination-page .mbo-transfer-generic-destination-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-transfer-generic-destination-page .mbo-transfer-generic-destination-page__body .mbo-transfers-product-page__key{margin-left:var(--sizing-x2)}mbo-transfer-generic-destination-page .mbo-transfer-generic-destination-page__message{color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),v})(),z=(()=>{class v{}return v.\u0275fac=function(o){return new(o||v)},v.\u0275mod=e.\u0275\u0275defineNgModule({type:v}),v.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,I.RouterModule.forChild([{path:"",component:W}]),i.bE,i.Jx,i.D0,b.eM,i.Zl]}),v})()},53644:(B,E,t)=>{t.d(E,{MK:()=>S,NM:()=>b,Rm:()=>T,VS:()=>i,ay:()=>I});var d=t(90806);class I{constructor(l){this.tagAval=l}}class b{constructor(l,n){this.subtitle=l,this.title=n}}class i{constructor(l,n,r){this.fullName=l,this.documentType=n,this.documentNumber=r,this.maskName=(0,d.Z)(l)}}class T{constructor(l,n,r,s,p,N){this.keyType=l,this.tagAval=n,this.accountReceptor=r,this.type=s,this.bank=p,this.customer=N}get customerMaskName(){return this.customer.maskName}}class S{constructor(l,n,r,s,p,N,e){this.source=l,this.account=n,this.contact=r,this.customerName=s,this.ipAddress=p,this.amount=N,this.note=e}}},30786:(B,E,t)=>{t.d(E,{$:()=>S,Ry:()=>l,iK:()=>h});var d=t(29306),I=t(64892),b=t(87903),i=t(53644);const T={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function S(n){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:r,CustIdentType:s,GovIssueIdent:p,PersonName:{FirstName:N}},DepAcctId:{AcctId:e,AcctType:x,BankInfo:y}},RefInfo:C}=n,a=p?.GovIssueIdentType||s,m=p?.IdentSerialNum||r,{RefId:A,RefType:_}=C[0];return new i.Rm(_,A,e,x,new d.Br(y.BankId,y.Name,y.BankId===I.qE.Occidente),new i.VS(N,(0,b.nX)(T[a]),m))}function h(n){return{fromDepAcctId:n.source.id,fromDepAcctName:n.source.name,fromDepAcctType:n.source.type,fromNickName:n.source.nickname,toDepAcctBankId:n.account.bank.id,toDepAcctType:n.account.type,toDepAcctId:n.account.accountReceptor,toDepAcctName:n.account.customer.fullName,toNickName:"",toUserIdNumber:n.account.customer.documentNumber,toUserIdType:n.account.customer.documentType.code,keyInfo:{key:n.account.tagAval,type:n.account.keyType},personInfoTo:{fullName:n.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:n.account.customer.documentType.code,identSerialNum:n.account.customer.documentNumber}},personInfoFrom:{firstName:n.customerName.clientFirstName,lastName:n.customerName.clientLastName,legalName:`${n.customerName.clientFirstName} ${n.customerName.clientLastName}`},curAmt:n.amount.toString(),refId:n.note?.reference||"",memo:n.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function l(n){return new i.MK(n.source,n.account,new i.NM(n.destination.tagAval,n.account.customer.fullName),n.customerName,n.ipAddress,n.amount,n.note)}},90806:(B,E,t)=>{t.d(E,{D:()=>T,Z:()=>S});var d=t(87903),I=t(53113);function b(h){const{isError:l,message:n}=h;return{animation:(0,d.jY)(h),title:l?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:n}}function i({isError:h}){return h?[(0,d.wT)("Finalizar","finish","outline"),(0,d.wT)("Volver a intentar","retry")]:[(0,d.wT)("Hacer otra transferencia","retry","outline"),(0,d.wT)("Finalizar","finish")]}function T(h){const{dateFormat:l,timeFormat:n}=new I.ou,{status:r,tagAval:s}=h,p=[(0,d.SP)("DESTINO",s.account.customer.maskName,s.account.tagAval,s.account.bank.name),(0,d._f)("SUMA DE",s.amount)];return s.note&&p.push((0,d.SP)("DESCRIPCI\xd3N",s.note.description,"",s.note.reference)),p.push((0,d.cZ)(l,n)),{actions:i(r),error:r.isError,header:b(r),informations:p,skeleton:!1}}function S(h){const l=h.split(" "),[n]=l,r=l[l.length-1],s=r.length,N=s>3?3:2;return`${n.substring(0,n.length>4?4:2)}*****${r.substring(s-N,s)}`}},50142:(B,E,t)=>{t.d(E,{$:()=>x});var d=t(15861),I=t(87956),b=t(53113),i=t(98699),T=t(30786),S=t(71776),h=t(39904),l=t(87903),n=t(42168),r=t(84757),s=t(99877);let p=(()=>{class y{constructor(a){this.http=a}requestVerifyAccount(a){var m=this;return(0,d.Z)(function*(){return(0,n.firstValueFrom)(m.http.post(h.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:a},pilot:!0}).pipe((0,r.map)(A=>A.map(_=>(0,T.$)(_))),(0,r.catchError)(A=>{if("206"===A.error?.MsgRsHdr?.Status?.StatusCode)return(0,n.of)([]);throw A})))})()}send(a){return(0,n.firstValueFrom)(this.http.post(h.bV.TRANSFERS.TAG_AVAL,(0,T.iK)(a),{headers:{"X-Customer-Ip":a.ipAddress}}).pipe((0,r.map)(m=>(0,l.l1)(m,"SUCCESS")))).catch(m=>(0,l.rU)(m))}}return y.\u0275fac=function(a){return new(a||y)(s.\u0275\u0275inject(S.HttpClient))},y.\u0275prov=s.\u0275\u0275defineInjectable({token:y,factory:y.\u0275fac,providedIn:"root"}),y})();var N=t(23604),e=t(74520);let x=(()=>{class y{constructor(a,m,A,_){this.repository=a,this.store=m,this.eventBusService=A,this.customerStore=_}setSource(a,m=!1){try{return i.Either.success(this.store.setSource(a,m))}catch({message:A}){return i.Either.failure({message:A})}}verfiyAccount(a){var m=this;return(0,d.Z)(function*(){try{const A=yield m.requestAccount(a);return m.store.setTagAval(a),i.Either.success(!!A)}catch({message:A}){return i.Either.failure({message:A})}})()}setAmount(a){try{return i.Either.success(this.store.setAmount(a))}catch({message:m}){return i.Either.failure({message:m})}}reset(){try{const a=this.store.itIsFromCustomer(),m=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:a,source:m})}catch({message:a}){return i.Either.failure({message:a})}}send(){var a=this;return(0,d.Z)(function*(){const m=a.customerStore.currentState,{session:{ip:A,customer:{clientFirstName:_,clientLastName:u}}}=m;a.store.setIpAddress(A),a.store.setCustomerName({clientFirstName:_,clientLastName:u});const F=(0,T.Ry)(a.store.currentState),c=yield a.execute(F);return a.eventBusService.emit(c.channel),i.Either.success({tagAval:F,status:c})})()}execute(a){try{return this.repository.send(a)}catch({message:m}){return Promise.resolve(b.LN.error(m))}}requestAccount(a){var m=this;return(0,d.Z)(function*(){const A=m.store.getTagAval();let _=m.store.getAccount();const{tagAval:u}=a;return(A?.tagAval!==u||!_)&&([_]=yield m.repository.requestVerifyAccount(u),m.store.setAccount(_)),_})()}setNote(a){try{return i.Either.success(this.store.setNote(a))}catch({message:m}){return i.Either.failure({message:m})}}removeNote(){try{return i.Either.success(this.store.removeNote())}catch({message:a}){return i.Either.failure({message:a})}}}return y.\u0275fac=function(a){return new(a||y)(s.\u0275\u0275inject(p),s.\u0275\u0275inject(N.B),s.\u0275\u0275inject(I.Yd),s.\u0275\u0275inject(e.f))},y.\u0275prov=s.\u0275\u0275defineInjectable({token:y,factory:y.\u0275fac,providedIn:"root"}),y})()},23604:(B,E,t)=>{t.d(E,{B:()=>h});var d=t(39904),I=t(87956),b=t(20691),T=t(99877);let h=(()=>{class l extends b.Store{constructor(r){super({confirmation:!1,fromCustomer:!1}),r.subscribes(d.PU,()=>{this.reset()})}setIpAddress(r){this.reduce(s=>({...s,ipAddress:r}))}setCustomerName(r){this.reduce(s=>({...s,customerName:r}))}itIsFromCustomer(){return this.select(({fromCustomer:r})=>r)}setSource(r,s=!1){this.reduce(p=>({...p,source:r,fromCustomer:s}))}getSource(){return this.select(({source:r})=>r)}setTagAval(r){this.reduce(s=>({...s,destination:r}))}getTagAval(){return this.select(({destination:r})=>r)}setAccount(r){this.reduce(s=>({...s,account:r}))}getAccount(){return this.select(({account:r})=>r)}setAmount(r){this.reduce(s=>({...s,amount:r,confirmation:!0}))}getAmount(){return this.select(({amount:r})=>r)}itIsConfirmation(){return this.select(({confirmation:r})=>r)}setNote(r){this.reduce(s=>({...s,note:r}))}removeNote(){this.reduce(r=>({...r,note:void 0}))}}return l.\u0275fac=function(r){return new(r||l)(T.\u0275\u0275inject(I.Yd))},l.\u0275prov=T.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})()},88039:(B,E,t)=>{t.d(E,{Oz:()=>I,fN:()=>b,vA:()=>i});var d=t(29306);class I{constructor(S,h,l,n,r){this.number=S,this.type=h,this.name=l,this.nickname=n,this.bank=r}get description(){return this.nickname||this.name}}class b{constructor(S,h,l,n,r,s,p,N,e){this.uuid=S,this.date=h,this.source=l,this.destination=n,this.amount=r,this.type=s,this.status=p,this.category=N,this.approvedCode=e}get dateFormat(){return this.date.dateFormat}get timeFormat(){return this.date.timeFormat}}class i extends d.Ay{static empty(){return new i([],0,!0)}}},93637:(B,E,t)=>{t.d(E,{U:()=>_,a:()=>A});var d=t(15861),I=t(3372),b=t(87956),i=t(98699),T=t(71776),S=t(39904),h=t(42168),l=t(84757);const n={D:{color:"success",label:"Exitosa"},R:{color:"danger",label:"Fallida"},P:{color:"alert",label:"Enviada"}};var s=t(88039),p=t(29306),N=t(53113),e=t(33876);const x={color:"primary",label:"Desconocido"};var C=t(99877);const a={items:String(10),order:"DESC",orderField:"xferEffDt"};let m=(()=>{class u{constructor(c,f){this.http=c,this.eventBusService=f,this.eventBusService.subscribes(S.PU,()=>{this.history=void 0})}request(c){if(this.history)return Promise.resolve(this.history);const f=c||S.cC,{end:M,start:R}=f.getFormat();return(0,h.firstValueFrom)(this.remote({...a,page:"0",EndDt:M,StartDt:R}).pipe((0,l.tap)(O=>{O.range=f,this.history=O})))}refresh(c){const{end:f,start:M}=c.getFormat();return(0,h.firstValueFrom)(this.remote({...a,page:"0",EndDt:f,StartDt:M}).pipe((0,l.tap)(R=>{R.range=c,this.history=R})))}requestForUuid(c){return this.history?.requestForUuid(c)}nextPage(){var c=this;return(0,d.Z)(function*(){if(!c.history)return c.request().then(({collection:R})=>R);const{end:f,start:M}=c.history.range.getFormat();return(0,h.firstValueFrom)(c.remote({...a,page:c.history.currentPage.toString(),EndDt:f,StartDt:M}).pipe((0,l.map)(({collection:R})=>(c.history.merge(R),c.history.collection))))})()}remote(c){return this.http.get(S.bV.TRANSFERS.HISTORY,{params:{...c}}).pipe((0,l.map)(({content:f,totalPage:M})=>new s.vA(f.map(R=>function y(u){return new s.fN((0,e.v4)(),new N.ou(u.xferEffDt),new s.Oz(u.fromAcctId,u.fromAcctType,u.fromAcctName,u.fromNickName,new p.Br(u.fromBankId,u.fromBankName)),new s.Oz(u.toAcctId,u.toAcctType,u.toAcctName,u.toNickName,new p.Br(u.toBankId,u.toBankName)),+u.amt,u.trnType,n[u.xferStatusDesc]||x,u.trnReccategory,u.approvalId)}(R)),M)),(0,l.catchError)(f=>{if(this.history)return(0,h.of)(s.vA.empty());throw f}))}}return u.\u0275fac=function(c){return new(c||u)(C.\u0275\u0275inject(T.HttpClient),C.\u0275\u0275inject(b.Yd))},u.\u0275prov=C.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),A=(()=>{class u{constructor(c,f,M){this.histories=c,this.products=f,this.preferencesService=M}transfers(){var c=this;return(0,d.Z)(function*(){try{const f=yield c.preferencesService.requestBoolean(I.M.TagAval),M=c.histories.request();return i.Either.success({enabledTagAval:f,history$:M})}catch({message:f}){return i.Either.failure({message:f})}})()}trustfunds(){var c=this;return(0,d.Z)(function*(){try{const f=yield c.products.requestTrustfunds();return f.length?i.Either.success(f):i.Either.failure({message:"Da clic en <b>Quiero invertir</b> y crea tu inversi\xf3n",value:!1})}catch{return i.Either.failure({message:"Ocurrio un error al tratar de consultar tus productos fiduciarios, por favor intente m\xe1s tarde",value:!0})}})()}}return u.\u0275fac=function(c){return new(c||u)(C.\u0275\u0275inject(m),C.\u0275\u0275inject(b.hM),C.\u0275\u0275inject(b.yW))},u.\u0275prov=C.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),_=(()=>{class u{constructor(c){this.histories=c}firstPage(){var c=this;return(0,d.Z)(function*(){try{return i.Either.success(yield c.histories.request())}catch({message:f}){return i.Either.failure({message:f})}})()}nextPage(){var c=this;return(0,d.Z)(function*(){try{return i.Either.success(yield c.histories.nextPage())}catch({message:f}){return i.Either.failure({message:f})}})()}refresh(c){var f=this;return(0,d.Z)(function*(){try{return i.Either.success(yield f.histories.refresh(c))}catch({message:M}){return i.Either.failure({message:M})}})()}historyForUuid(c){try{const f=this.histories.requestForUuid(c);return f?i.Either.success(f):i.Either.failure()}catch({message:f}){return i.Either.failure({message:f})}}}return u.\u0275fac=function(c){return new(c||u)(C.\u0275\u0275inject(m))},u.\u0275prov=C.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},40914:(B,E,t)=>{t.d(E,{R:()=>d,r:()=>I});const d={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},I={MIN_TRUSTFUND:2e5}}}]);