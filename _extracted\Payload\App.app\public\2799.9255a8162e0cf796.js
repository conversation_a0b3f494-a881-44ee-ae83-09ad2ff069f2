(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2799],{77493:(T,f,a)=>{a.d(f,{P:()=>_,G:()=>O});var m=a(15861),u=a(87956),s=a(53113),i=a(98699),g=a(38074),S=a(29306),C=a(87903),v=a(66067);class R{constructor(l,t,o,r){this.destination=l,this.source=t,this.amount=o,this.manual=r}}function p(e){return new R(e.destination,e.source,e.amount,e.manual)}var I=a(71776),n=a(39904),E=a(42168),b=a(84757),c=a(99877);let M=(()=>{class e{constructor(t,o){this.http=t,o.subscribes(n.PU,()=>{this.loans=void 0})}request(){return this.loans?Promise.resolve(this.loans):(0,E.firstValueFrom)(this.http.get(n.bV.PAYMENTS.DEBTS.CATALOG,{params:{exclude:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,b.map)(({content:t})=>t.map(o=>function P(e){return new v.T2(e.id,e.acctType,e.acctTypeName,e.loanName,e.acctId,new S.Br(e.bankId,e.bankName),e.isAval,e.dynamo||!1,e.isOwner,e.isOwner?null:e.owner,e.isOwner?null:new s.dp((0,C.nX)(e.ownerIdType),e.ownerId))}(o))),(0,b.tap)(t=>{this.loans=t})))}send(t){return(0,E.firstValueFrom)(this.http.post(n.bV.PAYMENTS.LOAN,function y(e){return{acctIdFrom:e.source.id,acctNickNameFrom:e.source.nickname,bankIdFrom:e.source.bank.id,acctIdTo:e.destination.id,acctNameTo:e.destination.nickname,bankIdTo:e.destination.bank.id,bankNameTo:e.destination.bank.name,amt:Math.ceil(e.amount),curCode:"COP",paymentDesc:""}}(t)).pipe((0,b.map)(o=>(0,C.l1)(o,"SUCCESS")))).catch(o=>(0,C.rU)(o))}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(I.HttpClient),c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var A=a(20691);let D=(()=>{class e extends A.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,manual:!1}),this.eventBusService=t,this.eventBusService.subscribes(n.PU,()=>{this.reset()})}setDestination(t,o=!1){this.reduce(r=>({...r,destination:t,fromCustomer:o}))}getDestination(){return this.select(({destination:t})=>t)}setProduct(t){this.reduce(o=>({...o,product:t}))}getProduct(){return this.select(({product:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(o=>({...o,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount({amount:t,manual:o}){this.reduce(r=>({...r,manual:o,amount:t}))}selectForAmount(){return this.select(({amount:t,confirmation:o,destination:r,source:h})=>({amount:t,confirmation:o,destination:r,source:h}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),_=(()=>{class e{constructor(t,o,r,h){this.productService=t,this.repository=o,this.store=r,this.eventBusService=h}setDestination(t){var o=this;return(0,m.Z)(function*(){try{return(0,g.p)(t)&&(yield o.productService.requestInformation(t)),i.Either.success(o.store.setDestination(t))}catch{return i.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return i.Either.success(this.store.setSource(t))}catch({message:o}){return i.Either.failure({message:o})}}setAmount(t,o=!1){try{return i.Either.success(this.store.setAmount({amount:t,manual:o}))}catch({message:r}){return i.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),o=this.store.getProduct();return this.store.reset(),i.Either.success({fromCustomer:t,product:o})}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const o=p(t.store.currentState),r=yield t.execute(o);return t.eventBusService.emit(r.channel),i.Either.success({loan:o,status:r})})()}execute(t){try{return this.repository.send(t)}catch({message:o}){return Promise.resolve(s.LN.error(o))}}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(M),c.\u0275\u0275inject(D),c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var L=a(89148);const{DlaPayMin:x,DlaPayTotal:U,LoanPayMin:V,LoanPayTotal:d}=L.Av;let O=(()=>{class e{constructor(t,o,r,h){this.products=t,this.productService=o,this.repository=r,this.store=h}destination(){var t=this;return(0,m.Z)(function*(){try{return i.Either.success(yield t.requestLoans())}catch({message:o}){return i.Either.failure({message:o})}})()}source(t){var o=this;return(0,m.Z)(function*(){try{const r=yield o.products.requestAccountsForTransfer(),h=o.store.itIsConfirmation(),N=yield o.requestLoan(t);return i.Either.success({confirmation:h,destination:N,products:r})}catch({message:r}){return i.Either.failure({message:r})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const o=t.store.itIsConfirmation(),r=t.store.getSource(),h=t.store.getDestination(),N=yield t.productService.requestInformation(h),j=t.getMinPayment(N),F=t.getTotalPayment(N);return i.Either.success({confirmation:o,cop:{min:j,total:F},source:r})}catch({message:o}){return i.Either.failure({message:o})}})()}amount(){var t=this;return(0,m.Z)(function*(){try{const o=t.store.getDestination(),r=(0,g.p)(o)?yield t.productService.requestInformation(t.store.getDestination()):void 0,h=r&&t.getTotalPayment(r);return i.Either.success({...t.store.selectForAmount(),total:h})}catch({message:o}){return i.Either.failure({message:o})}})()}confirmation(){try{const t=p(this.store.currentState);return i.Either.success({payment:t})}catch({message:t}){return i.Either.failure({message:t})}}requestLoans(){return this.repository.request().then(t=>t.reduce((o,r)=>{const{others:h,principals:N}=o;return(r.bank.isOccidente?N:h).push(r),o},{others:[],principals:[]}))}requestLoan(t){var o=this;return(0,m.Z)(function*(){let r=o.store.getDestination();if(!r&&t){const h=yield o.products.requestProductForId(t);if(o.store.setProduct(h),h){const{principals:N}=yield o.requestLoans(),j=N.find(({number:F})=>F===h.number);j&&(yield o.productService.requestInformation(j)),r=j||h,o.store.setDestination(r,!0)}}return r})()}getMinPayment(t){return t?.getSection(V)||t?.getSection(x)}getTotalPayment(t){return t?.getSection(d)||t?.getSection(U)}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.hM),c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(M),c.\u0275\u0275inject(D))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},20225:(T,f,a)=>{a.d(f,{w:()=>C});var m=a(30263),u=a(39904),s=a(95437),i=a(77493),g=a(99877);let C=(()=>{class v{constructor(P,y,p){this.modalConfirmation=P,this.mboProvider=y,this.managerLoan=p}execute(P=!0){P?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de cr\xe9dito actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerLoan.reset().when({success:({fromCustomer:P,product:y})=>{P?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:y.id}):this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)}})}}return v.\u0275fac=function(P){return new(P||v)(g.\u0275\u0275inject(m.$e),g.\u0275\u0275inject(s.ZL),g.\u0275\u0275inject(i.P))},v.\u0275prov=g.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},2799:(T,f,a)=>{a.r(f),a.d(f,{MboPaymentLoanSelectAmountPageModule:()=>V});var m=a(17007),u=a(78007),s=a(30263),i=a(83651),g=a(15861),S=a(39904),C=a(87903),v=a(95437),R=a(98699),P=a(57544),y=a(63674),p=a(77493),I=a(20225),n=a(99877),E=a(70016),b=a(48774),c=a(45542),M=a(99194),A=a(16450);function D(d,O){if(1&d&&(n.\u0275\u0275elementStart(0,"bocc-card-radiobutton",13)(1,"span",9),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",14),n.\u0275\u0275text(4),n.\u0275\u0275pipe(5,"boccCurrencyCop"),n.\u0275\u0275elementEnd()()),2&d){const e=n.\u0275\u0275nextContext();n.\u0275\u0275property("formControl",e.amountControl)("value",e.payMin),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",e.payMin.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(5,4,e.payMin.value,!1)," ")}}function _(d,O){if(1&d&&(n.\u0275\u0275elementStart(0,"bocc-card-radiobutton",15)(1,"span",9),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",14),n.\u0275\u0275text(4),n.\u0275\u0275pipe(5,"boccCurrencyCop"),n.\u0275\u0275elementEnd()()),2&d){const e=n.\u0275\u0275nextContext();n.\u0275\u0275property("formControl",e.amountControl)("value",e.payTotal),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",e.payTotal.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(5,4,e.payTotal.value,!1)," ")}}function L(d,O){if(1&d&&(n.\u0275\u0275elementStart(0,"bocc-message-icon",16),n.\u0275\u0275text(1),n.\u0275\u0275pipe(2,"boccCurrencyCop"),n.\u0275\u0275elementEnd()),2&d){const e=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" Valor a pagar supera saldo disponible ",n.\u0275\u0275pipeBind2(2,1,null==e.source?null:e.source.amount.toString(),!1)," ")}}const x=S.Z6.PAYMENTS.LOAN;let U=(()=>{class d{constructor(e,l,t,o){this.mboProvider=e,this.requestConfiguration=l,this.managerLoan=t,this.cancelProvider=o,this.confirmation=!1,this.requesting=!0,this.currencyCode="COP",this.backAction={id:"btn_payment-loan-select-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(x.SOURCE)}},this.cancelAction={id:"btn_payment-loan-select-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new P.FormControl(y.tl)}ngOnInit(){this.initializatedConfiguration()}get payLoan(){return this.cop}get payMin(){return this.payLoan?.min}get payTotal(){return this.payLoan?.total}get payManual(){return y.tl}get sourceValid(){const{value:e}=this.amountControl.value;return!e||!(0,C.VN)(this.source)||e<=this.source.amount}onSubmit(){const{value:e}=this.amountControl.value;(0,R.itIsDefined)(e)?this.managerLoan.setAmount(e,!1).when({success:()=>{this.mboProvider.navigation.next(x.CONFIRMATION)}}):this.mboProvider.navigation.next(x.AMOUNT)}initializatedConfiguration(){var e=this;return(0,g.Z)(function*(){(yield e.requestConfiguration.selectAmount()).when({success:({confirmation:l,cop:t,source:o})=>{const{min:r,total:h}=t;e.source=o,e.cop=t,e.confirmation=l,r?e.amountControl.setValue(r):h&&e.amountControl.setValue(h)}},()=>{e.requesting=!1})})()}}return d.\u0275fac=function(e){return new(e||d)(n.\u0275\u0275directiveInject(v.ZL),n.\u0275\u0275directiveInject(p.G),n.\u0275\u0275directiveInject(p.P),n.\u0275\u0275directiveInject(I.w))},d.\u0275cmp=n.\u0275\u0275defineComponent({type:d,selectors:[["mbo-payment-loan-select-amount-page"]],decls:17,vars:9,consts:[[1,"mbo-payment-loan-select-amount-page__content"],[1,"mbo-payment-loan-select-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-payment-loan-select-amount-page__body"],[1,"mbo-payment-loan-select-amount-page__title","subtitle2-medium"],[1,"mbo-payment-loan-select-amount-page__options",3,"hidden"],["id","rdb_payment-loan-select-amount_pay-min",3,"formControl","value",4,"ngIf"],["id","rdb_payment-loan-select-amount_pay-total",3,"formControl","value",4,"ngIf"],["id","rdb_payment-loan-select-amount_pay-manual",3,"formControl","value"],[1,"bocc-card-radiobutton__label","subtitle2-medium"],["icon","error","theme","danger",4,"ngIf"],[1,"mbo-payment-loan-select-amount-page__footer"],["id","btn_payment-creditcard-select-amount_submit","bocc-button","raised",3,"disabled","click"],["id","rdb_payment-loan-select-amount_pay-min",3,"formControl","value"],[1,"bocc-card-radiobutton__amount","subtitle2-medium"],["id","rdb_payment-loan-select-amount_pay-total",3,"formControl","value"],["icon","error","theme","danger"]],template:function(e,l){1&e&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"div",4),n.\u0275\u0275text(5," \xbfCu\xe1nto deseas pagar? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div",5),n.\u0275\u0275template(7,D,6,7,"bocc-card-radiobutton",6),n.\u0275\u0275template(8,_,6,7,"bocc-card-radiobutton",7),n.\u0275\u0275elementStart(9,"bocc-card-radiobutton",8)(10,"span",9),n.\u0275\u0275text(11," Otro valor "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(12,L,3,4,"bocc-message-icon",10),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(13,"div",11)(14,"button",12),n.\u0275\u0275listener("click",function(){return l.onSubmit()}),n.\u0275\u0275elementStart(15,"span"),n.\u0275\u0275text(16,"Continuar"),n.\u0275\u0275elementEnd()()()),2&e&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",l.backAction)("rightAction",l.cancelAction),n.\u0275\u0275advance(4),n.\u0275\u0275property("hidden",l.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.payMin),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.payTotal),n.\u0275\u0275advance(1),n.\u0275\u0275property("formControl",l.amountControl)("value",l.payManual),n.\u0275\u0275advance(3),n.\u0275\u0275property("ngIf",!l.sourceValid),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",!l.sourceValid||l.requesting))},dependencies:[m.NgIf,E.D,b.J,c.P,M.m,A.f],styles:["/*!\n * MBO PaymentLoanSelectAmount Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 18/Jul/2022\n * Updated: 14/Jun/2024\n*/mbo-payment-loan-select-amount-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__title{color:var(--color-carbon-darker-1000)}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__options{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__options .bocc-card-radiobutton__component{display:flex;justify-content:space-between;align-items:center}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__options .bocc-card-radiobutton__label{color:var(--color-carbon-darker-1000)}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__options bocc-message-icon{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-loan-select-amount-page .mbo-payment-loan-select-amount-page__footer button{width:100%}\n"],encapsulation:2}),d})(),V=(()=>{class d{}return d.\u0275fac=function(e){return new(e||d)},d.\u0275mod=n.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=n.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:U}]),s.DO,s.Jx,i.P6,s.P8,s.mn]}),d})()},63674:(T,f,a)=>{a.d(f,{Eg:()=>v,Lo:()=>i,Wl:()=>g,ZC:()=>S,_f:()=>u,br:()=>C,tl:()=>s});var m=a(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},s=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),i={color:"success",key:"paid",label:"Pagada"},g={color:"alert",key:"pending",label:"Por pagar"},S={color:"danger",key:"expired",label:"Vencida"},C={color:"info",key:"recurring",label:"Pago recurrente"},v={color:"info",key:"programmed",label:"Programado"}},66067:(T,f,a)=>{a.d(f,{S6:()=>R,T2:()=>C,UQ:()=>P,mZ:()=>v});var m=a(39904),u=a(6472),i=a(63674),g=a(31707);class C{constructor(p,I,n,E,b,c,M,A,D,_,L){this.id=p,this.type=I,this.name=n,this.nickname=E,this.number=b,this.bank=c,this.isAval=M,this.isProtected=A,this.isOwner=D,this.ownerName=_,this.ownerDocument=L,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,u.initials)(E),this.shortNumber=b.substring(b.length-4),this.descriptionNumber=`${n} ${b}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(p){this.informationValue||(this.informationValue=p)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(p){return this.currenciesValue.includes(p)}}class v{constructor(p,I){this.id=p,this.type=I}}class R{constructor(p,I,n,E,b,c,M,A,D,_,L,x){this.uuid=p,this.number=I,this.nie=n,this.nickname=E,this.companyId=b,this.companyName=c,this.amount=M,this.registerDate=A,this.expirationDate=D,this.paid=_,this.statusCode=L,this.references=x,this.recurring=x.length>0,this.status=function S(y){switch(y){case g.U.EXPIRED:return i.ZC;case g.U.PENDING:return i.Wl;case g.U.PROGRAMMED:return i.Eg;case g.U.RECURRING:return i.br;default:return i.Lo}}(L)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class P{constructor(p,I,n,E,b,c,M,A){this.uuid=p,this.number=I,this.nickname=n,this.companyId=E,this.companyName=b,this.city=c,this.amount=M,this.isBiller=A}}},38074:(T,f,a)=>{a.d(f,{p:()=>u});var m=a(29306);function u(s){return s instanceof m.xs||s.isRequiredInformation}},31707:(T,f,a)=>{a.d(f,{U:()=>m,f:()=>u});var m=(()=>{return(s=m||(m={})).RECURRING="1",s.EXPIRED="2",s.PENDING="3",s.PROGRAMMED="4",m;var s})(),u=(()=>{return(s=u||(u={})).BILLER="Servicio",s.NON_BILLER="Servicio",s.PSE="Servicio",s.TAX="Impuesto",s.LOAN="Obligaci\xf3n financiera",s.CREDIT_CARD="Obligaci\xf3n financiera",u;var s})()}}]);