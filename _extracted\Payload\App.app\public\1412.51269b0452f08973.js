(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1412],{41412:(u,c,s)=>{s.r(c),s.d(c,{firstChar:()=>i,hasPattern:()=>h,initials:()=>f,interpolation:()=>C,lastChar:()=>g,normalize:()=>o});var l=s(3334);const i=t=>0===t.length?"":t.charAt(0),g=t=>t.length?t.charAt(t.length-1):"",o=t=>t.slice().replace(/\xe1/g,"a").replace(/\xc1/g,"A").replace(/\xe9/g,"e").replace(/\xc9/g,"E").replace(/\xed/g,"i").replace(/\xcd/g,"I").replace(/\xf3/g,"o").replace(/\xd3/g,"O").replace(/\xfa/g,"u").replace(/\xda/g,"U"),h=(t,r,a=!1)=>{let e=r.toLowerCase(),n=t.toLowerCase();return a&&(n=o(n),e=o(e)),!!n.match(`^.*${e}.*$`)},f=(t,r=2)=>{const a=t.split(" ");if(1===a.length)return t.slice(0,r).toUpperCase();const e=(0,l.firstElement)(a),n=(0,l.lastElement)(a);return`${i(e)}${i(n)}`.toUpperCase()},p=/{([^{}]*)}/g;function C(t,r){return r?t.replace(p,(a,e)=>String(Array.isArray(r)?r[+e]:r[e])):p.test(t)?"":t}}}]);