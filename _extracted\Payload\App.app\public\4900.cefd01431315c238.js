(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4900],{74900:(q,E,f)=>{f.r(E),f.d(E,{HttpBatchLink:()=>P,HttpBatchLinkHandler:()=>D,HttpLink:()=>k,HttpLinkHandler:()=>Q});var M=f(50642),x=f(99877),g=f(61595),w=f(42168),C=f(71776),B=f(97582),A=f(40484),O=f(64302),F=function(){function r(t){var e=t.batchDebounce,n=t.batchInterval,o=t.batchMax,i=t.batchHandler,s=t.batchKey;this.batchesByKey=new Map,this.scheduledBatchTimerByKey=new Map,this.batchDebounce=e,this.batchInterval=n,this.batchMax=o||0,this.batchHandler=i,this.batchKey=s||function(){return""}}return r.prototype.enqueueRequest=function(t){var e=this,n=(0,B.pi)((0,B.pi)({},t),{next:[],error:[],complete:[],subscribers:new Set}),o=this.batchKey(t.operation);return n.observable||(n.observable=new O.y(function(i){var s=e.batchesByKey.get(o);s||e.batchesByKey.set(o,s=new Set);var l=0===s.size,u=0===n.subscribers.size;return n.subscribers.add(i),u&&s.add(n),i.next&&n.next.push(i.next.bind(i)),i.error&&n.error.push(i.error.bind(i)),i.complete&&n.complete.push(i.complete.bind(i)),(l||e.batchDebounce)&&e.scheduleQueueConsumption(o),s.size===e.batchMax&&e.consumeQueue(o),function(){var c;n.subscribers.delete(i)&&n.subscribers.size<1&&s.delete(n)&&s.size<1&&(e.consumeQueue(o),null===(c=s.subscription)||void 0===c||c.unsubscribe())}})),n.observable},r.prototype.consumeQueue=function(t){void 0===t&&(t="");var e=this.batchesByKey.get(t);if(this.batchesByKey.delete(t),e&&e.size){var n=[],o=[],i=[],s=[],l=[],u=[];e.forEach(function(a){n.push(a.operation),o.push(a.forward),i.push(a.observable),s.push(a.next),l.push(a.error),u.push(a.complete)});var c=this.batchHandler(n,o)||O.y.of(),d=function(a){l.forEach(function(h){h&&h.forEach(function(p){return p(a)})})};return e.subscription=c.subscribe({next:function(a){if(Array.isArray(a)||(a=[a]),s.length!==a.length){var h=new Error("server returned results with length ".concat(a.length,", expected length of ").concat(s.length));return h.result=a,d(h)}a.forEach(function(p,b){s[b]&&s[b].forEach(function(m){return m(p)})})},error:d,complete:function(){u.forEach(function(a){a&&a.forEach(function(h){return h()})})}}),i}},r.prototype.scheduleQueueConsumption=function(t){var e=this;clearTimeout(this.scheduledBatchTimerByKey.get(t)),this.scheduledBatchTimerByKey.set(t,setTimeout(function(){e.consumeQueue(t),e.scheduledBatchTimerByKey.delete(t)},this.batchInterval))},r}(),S=function(r){function t(e){var n=r.call(this)||this,o=e||{},s=o.batchInterval,u=o.batchMax,d=o.batchHandler,h=o.batchKey;return n.batcher=new F({batchDebounce:o.batchDebounce,batchInterval:void 0===s?10:s,batchMax:void 0===u?0:u,batchHandler:void 0===d?function(){return null}:d,batchKey:void 0===h?function(){return""}:h}),e.batchHandler.length<=1&&(n.request=function(b){return n.batcher.enqueueRequest({operation:b})}),n}return(0,B.ZT)(t,r),t.prototype.request=function(e,n){return this.batcher.enqueueRequest({operation:e,forward:n})},t}(A.i);const K=(r,t,e)=>{const n=-1!==["POST","PUT","PATCH"].indexOf(r.method.toUpperCase()),i=r.body.length;let l,s=r.options&&r.options.useMultipart;if(s){if(i)return new w.Observable(c=>c.error(new Error("File upload is not available when combined with Batching")));if(!n)return new w.Observable(c=>c.error(new Error("File upload is not available when GET is used")));if(!e)return new w.Observable(c=>c.error(new Error('To use File upload you need to pass "extractFiles" function from "extract-files" library to HttpLink\'s options')));l=e(r.body),s=!!l.files.size}let u={};if(i){if(!n)return new w.Observable(c=>c.error(new Error("Batching is not available for GET requests")));u={body:r.body}}else u=n?{body:s?l.clone:r.body}:{params:Object.keys(r.body).reduce((a,h)=>{const p=r.body[h];return a[h]=-1!==["variables","extensions"].indexOf(h.toLowerCase())?JSON.stringify(p):p,a},{})};if(s&&n){const c=new FormData;c.append("operations",JSON.stringify(u.body));const d={},a=l.files;let h=0;a.forEach(p=>{d[++h]=p}),c.append("map",JSON.stringify(d)),h=0,a.forEach((p,b)=>{c.append(++h+"",b,b.name)}),u.body=c}return t.request(r.method,r.url,{observe:"response",responseType:"json",reportProgress:!1,...u,...r.options})},I=(r,t)=>r&&t?t.keys().reduce((n,o)=>n.set(o,t.getAll(o)),r):t||r;function v(...r){const t=r.find(e=>typeof e<"u");return typeof t>"u"?r[r.length-1]:t}function T(r){let t=r.headers&&r.headers instanceof C.HttpHeaders?r.headers:new C.HttpHeaders(r.headers);if(r.clientAwareness){const{name:e,version:n}=r.clientAwareness;e&&!t.has("apollographql-client-name")&&(t=t.set("apollographql-client-name",e)),n&&!t.has("apollographql-client-version")&&(t=t.set("apollographql-client-version",n))}return t}class Q extends g.ApolloLink{httpClient;options;requester;print=M.print;constructor(t,e){super(),this.httpClient=t,this.options=e,this.options.operationPrinter&&(this.print=this.options.operationPrinter),this.requester=n=>new g.Observable(o=>{const i=n.getContext(),s=(y,z)=>v(i[y],this.options[y],z);let l=s("method","POST");const u=s("includeQuery",!0),c=s("includeExtensions",!1),d=s("uri","graphql"),a=s("withCredentials"),h=s("useMultipart"),p=!0===this.options.useGETForQueries,b=n.query.definitions.some(y=>"OperationDefinition"===y.kind&&"query"===y.operation);p&&b&&(l="GET");const m={method:l,url:"function"==typeof d?d(n):d,body:{operationName:n.operationName,variables:n.variables},options:{withCredentials:a,useMultipart:h,headers:this.options.headers}};c&&(m.body.extensions=n.extensions),u&&(m.body.query=this.print(n.query));const j=T(i);m.options.headers=I(m.options.headers,j);const L=K(m,this.httpClient,this.options.extractFiles).subscribe({next:y=>{n.setContext({response:y}),o.next(y.body)},error:y=>o.error(y),complete:()=>o.complete()});return()=>{L.closed||L.unsubscribe()}})}request(t){return this.requester(t)}}let k=(()=>{class r{httpClient;constructor(e){this.httpClient=e}create(e){return new Q(this.httpClient,e)}static \u0275fac=function(n){return new(n||r)(x.\u0275\u0275inject(C.HttpClient))};static \u0275prov=x.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();class D extends g.ApolloLink{httpClient;options;batcher;batchInterval;batchMax;print=M.print;constructor(t,e){super(),this.httpClient=t,this.options=e,this.batchInterval=e.batchInterval||10,this.batchMax=e.batchMax||10,this.options.operationPrinter&&(this.print=this.options.operationPrinter),this.batcher=new S({batchInterval:this.batchInterval,batchMax:this.batchMax,batchKey:e.batchKey||(i=>this.createBatchKey(i)),batchHandler:i=>new g.Observable(s=>{const l=this.createBody(i),u=this.createHeaders(i),{method:c,uri:d,withCredentials:a}=this.createOptions(i);if("function"==typeof d)throw new Error("Option 'uri' is a function, should be a string");const p=K({method:c,url:d,body:l,options:{withCredentials:a,headers:u}},this.httpClient,()=>{throw new Error("File upload is not available when combined with Batching")}).subscribe({next:b=>s.next(b.body),error:b=>s.error(b),complete:()=>s.complete()});return()=>{p.closed||p.unsubscribe()}})})}createOptions(t){const e=t[0].getContext();return{method:v(e.method,this.options.method,"POST"),uri:v(e.uri,this.options.uri,"graphql"),withCredentials:v(e.withCredentials,this.options.withCredentials)}}createBody(t){return t.map(e=>{const n=v(e.getContext().includeExtensions,this.options.includeExtensions,!1),o=v(e.getContext().includeQuery,this.options.includeQuery,!0),i={operationName:e.operationName,variables:e.variables};return n&&(i.extensions=e.extensions),o&&(i.query=this.print(e.query)),i})}createHeaders(t){return t.reduce((e,n)=>I(e,n.getContext().headers),T({headers:this.options.headers,clientAwareness:t[0]?.getContext()?.clientAwareness}))}createBatchKey(t){const e=t.getContext();if(e.skipBatching)return Math.random().toString(36).substr(2,9);const n=e.headers&&e.headers.keys().map(i=>e.headers.get(i)),o=JSON.stringify({includeQuery:e.includeQuery,includeExtensions:e.includeExtensions,headers:n});return v(e.uri,this.options.uri)+o}request(t){return this.batcher.request(t)}}let P=(()=>{class r{httpClient;constructor(e){this.httpClient=e}create(e){return new D(this.httpClient,e)}static \u0275fac=function(n){return new(n||r)(x.\u0275\u0275inject(C.HttpClient))};static \u0275prov=x.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})()}}]);