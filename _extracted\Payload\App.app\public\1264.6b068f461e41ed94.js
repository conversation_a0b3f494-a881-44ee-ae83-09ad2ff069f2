(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1264],{41264:(A,m,a)=>{a.r(m),a.d(m,{MboCustomerCoveredCardsPageModule:()=>O});var l=a(17007),u=a(78007),v=a(30263),p=a(79798),b=a(15861),g=a(39904),C=a(95437),h=a(54747),e=a(99877),f=a(48774),y=a(90039),M=a(55648);function P(t,n){if(1&t){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product",7),e.\u0275\u0275listener("click",function(){const c=e.\u0275\u0275restoreView(o).$implicit,i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onSelect(c))}),e.\u0275\u0275elementEnd()}if(2&t){const o=n.$implicit,r=e.\u0275\u0275nextContext();e.\u0275\u0275property("displayCard",!0)("title",o.nickname)("color",o.color)("icon",o.logo)("code",o.shortNumber)("statusLabel",o.status.label)("statusColor",o.status.color)("label",o.label)("amount",o.amount)("incognito",r.incognito)}}const{HOME:d,INFO:x}=g.Z6.CUSTOMER.PRODUCTS;let I=(()=>{class t{constructor(o,r,s){this.activateRoute=o,this.mboProvider=r,this.managerCovereds=s,this.coveredCards=[],this.incognito=!1,this.backAction={id:"btn_covered-cards_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{const{productId:c}=this.activateRoute.snapshot.queryParams;this.mboProvider.navigation.back(d,{productId:c})}}}ngOnInit(){const{productId:o}=this.activateRoute.snapshot.queryParams;o?this.initializatedConfiguration(o):this.mboProvider.navigation.back(d)}ngOnDestroy(){this.unsubscription&&this.unsubscription()}onSelect(o){const{productId:r}=this.activateRoute.snapshot.queryParams;this.mboProvider.navigation.next(x,{productId:r,coveredCardId:o.id})}initializatedConfiguration(o){var r=this;return(0,b.Z)(function*(){(yield r.managerCovereds.requestAll(o)).when({success:({coveredCards:s,preferences$:c})=>{r.coveredCards=s,r.unsubscription=c(({isIncognito:i})=>{r.incognito=i})},failure:()=>{r.mboProvider.navigation.back(d)}})})()}}return t.\u0275fac=function(o){return new(o||t)(e.\u0275\u0275directiveInject(u.ActivatedRoute),e.\u0275\u0275directiveInject(C.ZL),e.\u0275\u0275directiveInject(h._L))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-customer-covered-cards-page"]],decls:8,vars:2,consts:[[1,"mbo-customer-covered-cards-page__content"],[1,"mbo-customer-covered-cards-page__header"],["title","Amparadas",3,"leftAction"],[1,"mbo-customer-covered-cards-page__body"],[1,"mbo-customer-covered-cards-page__title","subtitle1-medium"],["id","btn_customer-covered-cards_incognito"],[3,"displayCard","title","color","icon","code","statusLabel","statusColor","label","amount","incognito","click",4,"ngFor","ngForOf"],[3,"displayCard","title","color","icon","code","statusLabel","statusColor","label","amount","incognito","click"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," \xbfC\xfaal tarjeta amparada deseas consultar? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"mbo-button-incognito-mode",5),e.\u0275\u0275template(7,P,1,10,"bocc-card-product",6),e.\u0275\u0275elementEnd()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",r.backAction),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",r.coveredCards))},dependencies:[l.NgForOf,f.J,y.u,M.u],styles:["/*!\n * MBO CustomerCoveredCards Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 16/Oct/2022\n * Updated: 18/Jul/2024\n*/mbo-customer-covered-cards-page{position:relative;display:block;width:100%;height:100%;overflow:hidden}mbo-customer-covered-cards-page mbo-button-incognito-mode{--bocc-button-padding: 0rem var(--sizing-x2);align-self:flex-start}mbo-customer-covered-cards-page mbo-button-incognito-mode .bocc-button__label{color:var(--color-carbon-darker-1000)}mbo-customer-covered-cards-page .mbo-customer-covered-cards-page__content{position:relative;width:100%}mbo-customer-covered-cards-page .mbo-customer-covered-cards-page__header{position:relative;width:100%}mbo-customer-covered-cards-page .mbo-customer-covered-cards-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-customer-covered-cards-page .mbo-customer-covered-cards-page__title{color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),t})(),O=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[l.CommonModule,u.RouterModule.forChild([{path:"",component:I}]),v.Jx,v.ud,p.uf]}),t})()}}]);