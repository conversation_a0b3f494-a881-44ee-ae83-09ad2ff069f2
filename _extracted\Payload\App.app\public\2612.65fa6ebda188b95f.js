(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2612],{57929:(U,b,a)=>{a.d(b,{Be:()=>h,q3:()=>d,qQ:()=>M});const d=36,M=0,h=[{code:"0002015502010102125802CO5921DIEGO ANDRES CORREDOR49250103RBM0014CO.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124O2dhtQbToI1IP7xYOHkR1SUm0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099360041740013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064310002ES0121DIEGO ANDRES CORREDOR54061200006304C1DE",name:"DIEGO ANDRES CORREDOR",type:"onlyAccount"},{code:"000201550202010211560105802CO5922ALMACEN Y SASTRERIA *******************.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124bAK0LiWIA7a7GGPS3ZTGmmCv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601315238 DUITAMA8223010100014CO.COM.RBM.IVA503001099100104110013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573154033178070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122ALMACEN Y SASTRERIA IN6304EA05",name:"ALMACEN Y SASTRERIA IN",type:"onlyAccount"},{code:"000201550202010211560105802CO5917SACOS AZULES BOCC49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124cJbE3StP8jyiME/aY+8d/Qo80014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601505664 SAN PEDRO8223010100014CO.COM.RBM.IVA503001099353177830013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573108133170070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064270002ES0117SACOS AZULES BOCC6304C439",name:"SACOS AZULES BOCC",type:"onlyAccount"},{code:"00020101021249250014CO.COM.CRB.RED0103CRB50300013CO.COM.CRB.CU01090164845945204581453031705405150005802CO5914Cci*boxBurguer6011BOGOTA D.C.622501031530708000DE40808020080270016CO.COM.CRB.CANAL0103POS81250015CO.COM.CRB.CIVA01020282230014CO.COM.CRB.IVA0101083240015CO.COM.CRB.BASE0101084250015CO.COM.CRB.CINC01020285230014CO.COM.CRB.INC0101090300016CO.COM.CRB.TRXID010600015491260014CO.COM.CRB.SEC0104627c6304df69",name:"CCI BOX BURGER",type:"onlyCards"},{code:"00020155020201021256076750.005802CO5918PRUEBAS QR REDEBAN49250103RBM0014CO.COM.RBM.RED902701035360016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124oC4KvIdTb9ouPsgVLNsLPwj00014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.*********************.000015CO.COM.RBM.BASE62180708SRB0008208020084250102020015CO.COM.RBM.CINC52040004852601040.000014CO.COM.RBM.INC530317064280002ES0118PRUEBAS QR REDEBAN5405450006304FAD5",name:"PRUEBAS QR REDEBAN",type:"cardsAndAccounts"},{code:"00020155020201021256040.005802CO5912COMERCIO POS49250103RBM0014CO.COM.RBM.RED9028010432620016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124HYImT9C9mng/eqME88+mrObw0014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.********************.000015CO.COM.RBM.BASE5125010454220013CO.COM.RBM.CA62180708SRB0068308020084250102020015CO.COM.RBM.CINC52040000852601040.000014CO.COM.RBM.INC530317064220002ES0112COMERCIO POS5405112206304B678",name:"COMERCIO POS",type:"cardsAndAccounts"},{code:"0002010102115802CO5915BOGOTA BICYCLES49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80260102IM0016CO.COM.RBM.CANAL91270105477470014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA60131100100 BOGOT8226010419.00014CO.COM.RBM.IVA50290108165934100013CO.COM.RBM.*****************.COM.RBM.BASE62180708BL01222608020084250102030015CO.COM.RBM.CINC520459418523010100014CO.COM.RBM.INC530317064250002ES0115BOGOTA BICYCLES6304E56D",name:"BOGOTA BICYCLES",type:"cardsAndAccounts"},{code:"000201550202010211560105802CO5922Hierros de occidente m49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124eH1E5X5opSOneQRXjtmvYMIX0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601227099 BOJAYA8223010100014CO.COM.RBM.IVA503001099353192840013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444454070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122Hierros de occidente m63049CC9",name:"HIERROS OCCIDENTE M",type:"onlyAccount"},{code:"000201550202010211560105802CO5914CAMISAS BOGOTA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL914601244nOdGGoa7JhbdMHsdz6/ZTfw0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601550001 VILLAVICE8223010100014CO.COM.RBM.IVA503001099353189710013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444450070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064240002ES0114CAMISAS BOGOTA63049B14",name:"CAMISAS BOGOTA",type:"onlyAccount"},{code:"000201550202010211560105802CO5911LA PLAZUELA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124kLbT32m0FcJ/Ws+o6IsRzz/C0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099236850430013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573215009881070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064210002ES0111LA PLAZUELA63041E06",name:"LA PLAZUELA",type:"onlyAccount"},{code:"000201550202010211560105802CO5912FLORES JUANA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124sz0Z69d6TSQ0H2oXwdNV1JzE0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099233753060013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573124012500070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064220002ES0112FLORES JUANA6304CD95",name:"FLORES JUANA",type:"onlyAccount"},{code:"0002015502010102115802CO5922COMERCIO DE SIEMPRE *******************.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124YOCYxVWUOwVMrGNJJvp2u8Uv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601366001 PEREIRA8223010100014CO.COM.RBM.IVA503001099102030400013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520405118523010100014CO.COM.RBM.INC530317064320002ES0122COMERCIO DE SIEMPRE BC63040D54",name:"COMERCIO DE SIEMPRE BC",type:"onlyAccount"}]},69595:(U,b,a)=>{a.d(b,{tG:()=>H,bE:()=>J,PY:()=>W});var d=a(15861),M=a(87956),h=a(53113),u=a(98699),v=a(89148),R=a(57929),y=(()=>{return(o=y||(y={})).Static="11",o.Dinamic="12",y;var o})();const{CcaBuyAvailableCop:g}=v.Av;class T{constructor(s){this.name=s}}class A{constructor(s,t,n,i,c,l,f,S,P,V,L,$){this.reference=s,this.codeQr=t,this.type=n,this.merchant=i,this.baseAmount=c,this.ivaAmount=l,this.incAmount=f,this.tipAmount=S,this.totalAmount=P,this.betweenAccounts=V,this.belongCommerce=L,this.approvalId=$}get codeDinamic(){return this.type===y.Dinamic}get codeStatic(){return this.type===y.Static}}class N{constructor(s,t,n,i,c,l,f,S,P,V,L){this.product=s,this.id=t,this.name=n,this.number=i,this.amountValue=c,this.logo=l,this.icon=f,this.color=S,this.requiredQuotas=P,this.numberQuotas=V,this.type=L,this.shortNumber=i.substring(i.length-4)}get amount(){return this.amountValue}isAvailableAmount(s){return this.amountValue>=s}}class D extends N{constructor(s,t){super(s,t.id,t.name,t.number,s.amount,t.franchise?t.franchise.getLogoContrast(t.color):s.bank.logo,t.franchise?{dark:t.franchise.logo.dark,light:t.franchise.logo.light,standard:t.franchise.getLogoContrast(t.color)}:{dark:s.bank.logo,light:s.bank.logo,standard:s.bank.logo},t.color,!1,R.qQ,"debit")}}class j extends N{constructor(s,t){if(super(s,s.id,s.name,s.publicNumber,s.amount,s.logo,s.icon,s.color,!0,R.q3,"credit"),t){const n=t.getSection(g);this.amountValue=+n?.value||s.amount}}}class e{constructor(s,t,n,i,c){this.invoice=s,this.type=t,this.amount=n,this.source=i,this.quotas=c}}var w=a(71776),E=a(39904),I=a(87903),_=a(42168),B=a(84757),O=a(99877);let z=(()=>{class o{constructor(t,n){this.http=t,this.fingerprintService=n}read(t){var n=this;return(0,_.firstValueFrom)(this.http.post(E.bV.PAYMENTS.QR.READ,{metadata:t}).pipe((0,B.tap)(i=>{if(Object.keys(i).every(l=>!i[l]))throw new Error("Invalid QR")}),(0,B.switchMap)(function(){var i=(0,d.Z)(function*(c){const{acquirerCode:l,merchantCode:f}=c,S=("Redeban"===l||"RBM"===l)&&9===f.length&&"9"===f.charAt(0),P=!S&&(yield n.verifyCommerce(t));return function F(o,s,t,n){return new A(s.billingNumber||s.trnConsecutiveCode,o,s.qrType,new T(s.merchantName),+s.netTrxAmount,+s.ivaValue,+s.incValue,+s.tipValue,+s.totalTrxAmount,t,n,s.approvalId)}(t,c,S,P)});return function(c){return i.apply(this,arguments)}}())))}verifyCommerce(t){return(0,_.firstValueFrom)(this.http.post(E.bV.PAYMENTS.QR.COMMERCE,{metadata:t}).pipe((0,B.map)(n=>"320"===n?.msgRsHdr?.status?.serverStatusCode)))}send(t){return"card"===t.type?this.sendForQr(t):this.sendForAccount(t)}cancel(t){return(0,_.firstValueFrom)(this.http.post(E.bV.PAYMENTS.QR.CANCEL,{code:t.invoice.codeQr}).pipe((0,B.map)(n=>(0,I.l1)(n,"SUCCESS")))).catch(n=>(0,I.rU)(n))}sendForQr(t){return(0,_.firstValueFrom)(this.http.post(E.bV.PAYMENTS.QR.PAY,function G(o){return{code:o.invoice.codeQr,installments:o.quotas,origin:o.source.id}}(t)).pipe((0,B.map)(n=>(0,I.l1)(n,"SUCCESS")))).catch(n=>(0,I.rU)(n))}sendForAccount(t){var n=this;return(0,d.Z)(function*(){const i=yield n.fingerprintService.getInfo();return(0,_.firstValueFrom)(n.http.post(E.bV.PAYMENTS.QR.PAY_ACCOUNT,function Z(o,s){return{code:o.invoice.codeQr,curAmt:{amt:o.amount,curCode:"COP"},deviceAdmin:s,origin:o.source.id}}(t,i)).pipe((0,B.map)(c=>[201,"201"].includes(c.msgRsHdr?.status?.statusCode)?new h.LN("SUCCESS",`Ref: ${c.approvalId}`):"397"===c.msgRsHdr?.status?.additionalStatus?.statusCode?new h.LN("INFO",c.msgRsHdr.status.additionalStatus.statusDesc):(0,I.l1)(c,"SUCCESS")))).catch(c=>(0,I.rU)(c))})()}}return o.\u0275fac=function(t){return new(t||o)(O.\u0275\u0275inject(w.HttpClient),O.\u0275\u0275inject(M.ew))},o.\u0275prov=O.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var X=a(20691);let q=(()=>{class o extends X.Store{constructor(t){super({amount:0,creditCards:[],debitCards:[],products:[],pay:!0,requiredQuotas:!0,type:"card"}),t.subscribes(E.PU,()=>{this.reset()})}setProducts(t){this.reduce(n=>({...n,products:t}))}getProducts(){return this.select(({products:t})=>t)}setCreditCards(t){this.reduce(n=>({...n,creditCards:t}))}addCreditCard(t){const n=this.getCreditCards();n.filter(({id:c})=>c===t.id).length||this.reduce(c=>({...c,creditCards:[...n,t]}))}getCreditCards(){return this.select(({creditCards:t})=>t)}setDebitCards(t){this.reduce(n=>({...n,debitCards:t}))}addDebitCard(t){const n=this.getDebitCards();n.filter(({id:c})=>c===t.id).length||this.reduce(c=>({...c,debitCards:[...n,t]}))}getDebitCards(){return this.select(({debitCards:t})=>t)}setInvoice(t,n=!0){this.reduce(i=>({...i,invoice:t,pay:n}))}getInvoice(){return this.select(({invoice:t})=>t)}setSourceCard(t){this.reduce(n=>({...n,source:t,requiredQuotas:t.requiredQuotas,type:"card"}))}setSourceProduct(t){this.reduce(n=>({...n,source:t,quotas:0,requiredQuotas:!1,type:"account"}))}getSource(){return this.select(({source:t})=>t)}selectForSource(){return this.select(({amount:t,creditCards:n,debitCards:i,invoice:c,products:l,source:f})=>({amount:c.codeDinamic?c.totalAmount:t,creditCards:n,codeStatic:c.codeStatic,debitCards:i,invoice:c,products:l,source:f}))}setQuotas(t){this.reduce(n=>({...n,quotas:t}))}getQuotas(){return this.select(({quotas:t})=>t)}setAmount(t){this.reduce(n=>({...n,amount:t}))}getAmount(){return this.select(({amount:t})=>t)}getPay(){return this.select(({pay:t})=>t)}selectForConfirmation(){return this.select(({amount:t,invoice:n,quotas:i,requiredQuotas:c,source:l})=>({amount:t,invoice:n,quotas:i,requiredQuotas:c,source:l}))}}return o.\u0275fac=function(t){return new(t||o)(O.\u0275\u0275inject(M.Yd))},o.\u0275prov=O.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),H=(()=>{class o{constructor(t,n,i){this.repository=t,this.store=n,this.eventBusService=i}setSourceCard(t){try{const n=this.store.getSource();return n&&n.type!==t.type&&this.store.setQuotas(t.numberQuotas),u.Either.success(this.store.setSourceCard(t))}catch{return u.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setSourceProduct(t){try{return u.Either.success(this.store.setSourceProduct(t))}catch{return u.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setQuotas(t){try{return u.Either.success(this.store.setQuotas(t))}catch({message:n}){return u.Either.failure({message:n})}}setAmount(t){try{return u.Either.success(this.store.setAmount(t))}catch({message:n}){return u.Either.failure({message:n})}}reset(){try{return u.Either.success(this.store.reset())}catch({message:t}){return u.Either.failure({message:t})}}send(){var t=this;return(0,d.Z)(function*(){const n=function Y(o){return new e(o.invoice,o.type,o.amount,o.source,o.quotas)}(t.store.currentState),i=yield t.execute(n);return t.eventBusService.emit(i.channel),u.Either.success({paymentQr:n,status:i})})()}execute(t){try{return this.repository.send(t)}catch({message:n}){return Promise.resolve(h.LN.error(n))}}}return o.\u0275fac=function(t){return new(t||o)(O.\u0275\u0275inject(z),O.\u0275\u0275inject(q),O.\u0275\u0275inject(M.Yd))},o.\u0275prov=O.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),J=(()=>{class o{constructor(t){this.store=t}source(){var t=this;return(0,d.Z)(function*(){try{return u.Either.success(t.store.selectForSource())}catch({message:n}){return u.Either.failure({message:n})}})()}cancel(){var t=this;return(0,d.Z)(function*(){try{const n=t.store.getInvoice();return u.Either.success({invoice:n})}catch({message:n}){return u.Either.failure({message:n})}})()}confirmation(){var t=this;return(0,d.Z)(function*(){try{return u.Either.success(t.store.selectForConfirmation())}catch({message:n}){return u.Either.failure({message:n})}})()}}return o.\u0275fac=function(t){return new(t||o)(O.\u0275\u0275inject(q))},o.\u0275prov=O.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var K=a(70658);let W=(()=>{class o{constructor(t,n,i,c,l){this.products=t,this.productService=n,this.repository=i,this.store=c,this.scannerService=l}execute(){var t=this;return(0,d.Z)(function*(){try{const{code:n,cancelled:i,granted:c}=yield t.scanQrCode();return c||K.N.navigatorEnabled?i?u.Either.failure({value:!1,message:"Escaneo del c\xf3digo QR cancelado para realizar pago"}):u.Either.success(n):u.Either.failure({value:!1,message:"Banca m\xf3vil no cuenta con permisos para escanear de c\xf3digo QR, por favor habilite los permisos y vuelva a intentarlo"})}catch({message:n}){return u.Either.failure({value:!0,message:n})}})()}payment(t){var n=this;return(0,d.Z)(function*(){try{return n.verifyInvoice(yield n.repository.read(t))}catch({message:i}){return n.store.reset(),u.Either.failure({message:i})}})()}cancel(t){var n=this;return(0,d.Z)(function*(){try{const i=yield n.repository.read(t);return i.approvalId?u.Either.success(n.store.setInvoice(i,!1)):u.Either.failure({message:"C\xf3digo escaneado para pago QR es inv\xe1lido"})}catch({message:i}){return n.store.reset(),u.Either.failure({message:i})}})()}scanQrCode(){var t=this;return(0,d.Z)(function*(){return t.scannerService.qrCode({orientation:"portrait"})})()}verifyInvoice(t){var n=this;return(0,d.Z)(function*(){try{if(t.betweenAccounts){const c=yield n.verifyAccount(!0);return c&&n.store.setSourceProduct(c),u.Either.success(n.store.setInvoice(t))}const i=yield n.verifyCards();if(!t.belongCommerce&&!i)throw Error("Actualmente no cuentas con tarjetas activas para realizar pago QR");if(i&&(n.store.setSourceCard(i),n.store.setQuotas(i.numberQuotas)),t.belongCommerce){const c=yield n.verifyAccount(!1);if(!c&&!i)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");c&&n.store.setSourceProduct(c)}else n.store.setProducts([]);return u.Either.success(n.store.setInvoice(t))}catch({message:i}){return u.Either.failure({message:i})}})()}verifyAccount(t){var n=this;return(0,d.Z)(function*(){const i=(yield n.products.requestAccountsForTransfer()).sort((l,f)=>l.amount>f.amount?-1:1);n.store.setProducts(i);const[c]=i;if(!c&&t)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");return c})()}verifyCards(){var t=this;return(0,d.Z)(function*(){const[[n],[i]]=yield Promise.all([t.requestCreditCards(),t.requestDebitCards()]);return n||i})()}requestCreditCards(){var t=this;return(0,d.Z)(function*(){const n=yield t.products.requestCreditCards(),c=(yield Promise.all(n.map(l=>t.requestCreditCardInformation(l)))).filter(l=>(0,u.itIsDefined)(l)).sort((l,f)=>l.amount>f.amount?-1:1);return t.store.setCreditCards(c),c})()}requestCreditCardInformation(t){return this.productService.requestInformation(t).then(n=>n&&new j(t,n)).catch(()=>{})}requestDebitCards(){var t=this;return(0,d.Z)(function*(){const n=yield t.products.requestAccountsForTransfer(),c=(yield Promise.all(n.map(l=>t.requestDebitCardsInformation(l)))).reduce((l,f)=>l.concat(f),[]).sort((l,f)=>l.amount>f.amount?-1:1);return t.store.setDebitCards(c),c})()}requestDebitCardsInformation(t){return this.productService.requestDebitCards(t).then(n=>n.map(i=>new D(t,i))).catch(()=>[])}}return o.\u0275fac=function(t){return new(t||o)(O.\u0275\u0275inject(M.hM),O.\u0275\u0275inject(M.M5),O.\u0275\u0275inject(z),O.\u0275\u0275inject(q),O.\u0275\u0275inject(M.LQ))},o.\u0275prov=O.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},50965:(U,b,a)=>{a.d(b,{q:()=>y});var d=a(30263),M=a(39904),h=a(95437),u=a(69595),v=a(99877);let y=(()=>{class g{constructor(A,N,D){this.modalConfirmation=A,this.mboProvider=N,this.managerQr=D}execute(A=!0){A?this.modalConfirmation.execute({title:"Abandonar pago",message:"\xbfEstas seguro que deseas cancelar el pago QR actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed()}},decline:{label:"Continuar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerQr.reset(),this.mboProvider.navigation.back(M.Z6.PAYMENTS.QR.SCAN)}}return g.\u0275fac=function(A){return new(A||g)(v.\u0275\u0275inject(d.$e),v.\u0275\u0275inject(h.ZL),v.\u0275\u0275inject(u.tG))},g.\u0275prov=v.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},62612:(U,b,a)=>{a.r(b),a.d(b,{MboPaymentQrConfirmationPageModule:()=>tt});var d=a(17007),M=a(78007),h=a(79798),u=a(30263),v=a(15861),R=a(24495),y=a(39904),g=a(95437),T=a(57544),A=a(63674),N=a(57929),D=a(69595),j=a(50965),e=a(99877),F=a(10464),G=a(48774),Z=a(1027),Y=a(2460),w=a(55944),E=a(17941),I=a(92275),_=a(80751),B=a(45542),O=a(45834),z=a(18128),X=a(83867);function q(m,p){if(1&m&&(e.\u0275\u0275elementStart(0,"label",28),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" Transacci\xf3n ",null==r.invoice?null:r.invoice.reference," ")}}function H(m,p){if(1&m&&(e.\u0275\u0275elementStart(0,"label",29),e.\u0275\u0275element(1,"bocc-amount",30),e.\u0275\u0275elementEnd()),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("amount",null==r.invoice?null:r.invoice.baseAmount)("decimals",!1)}}function J(m,p){if(1&m&&e.\u0275\u0275element(0,"bocc-card-summary",31),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("amount",null==r.invoice?null:r.invoice.ivaAmount)("amountSmall",!0)}}function K(m,p){if(1&m&&e.\u0275\u0275element(0,"bocc-card-summary",32),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("amount",null==r.invoice?null:r.invoice.incAmount)("amountSmall",!0)}}function W(m,p){if(1&m&&e.\u0275\u0275element(0,"bocc-card-summary",33),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("amount",null==r.invoice?null:r.invoice.tipAmount)("amountSmall",!0)}}function o(m,p){if(1&m&&e.\u0275\u0275element(0,"bocc-card-summary",34),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("amount",null==r.invoice?null:r.invoice.totalAmount)}}function s(m,p){if(1&m&&e.\u0275\u0275element(0,"mbo-currency-box",35),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("formControl",r.payValueControl)}}function t(m,p){1&m&&(e.\u0275\u0275elementStart(0,"div")(1,"bocc-badge",41),e.\u0275\u0275text(2,"Fondos insuficientes"),e.\u0275\u0275elementEnd()())}function n(m,p){if(1&m&&(e.\u0275\u0275elementStart(0,"div",36),e.\u0275\u0275element(1,"bocc-avatar-product",37),e.\u0275\u0275elementStart(2,"div",38),e.\u0275\u0275element(3,"bocc-card-summary",39),e.\u0275\u0275template(4,t,3,0,"div",40),e.\u0275\u0275elementEnd()()),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("color",r.source.color)("srcImg",r.source.logo),e.\u0275\u0275advance(2),e.\u0275\u0275property("title",r.source.name)("subtitle",r.source.shortNumber)("actions",r.sourceActions),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",r.insufficientFunds)}}function i(m,p){if(1&m&&(e.\u0275\u0275elementStart(0,"div",42),e.\u0275\u0275element(1,"bocc-card-summary",43),e.\u0275\u0275elementEnd()),2&m){const r=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("title",r.quotas)("actions",r.quotasActions)}}function c(m,p){if(1&m){const r=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-button-diamond",44),e.\u0275\u0275listener("click",function(){const x=e.\u0275\u0275restoreView(r).$implicit,k=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(k.onQuota(x))}),e.\u0275\u0275elementStart(1,"span",45),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&m){const r=p.$implicit,C=e.\u0275\u0275nextContext();e.\u0275\u0275attribute("bocc-theme",C.getQuotaStatus(r)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",r.value," ")}}const{MAX_QR_DINAMIC:l,MAX_QR_STATIC:f,MIN_QR_STATIC:S}=A._f,P=y.Z6.PAYMENTS.QR;class V{constructor(p){this.value=p}}let $=(()=>{class m{constructor(r,C,Q,x){this.mboProvider=r,this.requestConfiguration=C,this.managerPaymentQr=Q,this.cancelProvider=x,this.requiredQuotas=!0,this.quotas=0,this.quotasVisible=!1,this.quotasNumber=[],this.cancelAction={id:"btn_payment-qr-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.sourceActions=[{id:"btn_payment-qr-confirmation_edit-source",icon:"edit-pencil",click:()=>{this.invoice?.codeStatic&&this.managerPaymentQr.setAmount(+this.payValueControl.value),this.mboProvider.navigation.back(P.SOURCE)}}],this.quotasActions=[{id:"btn_payment-qr-confirmation_edit-quotas",icon:"edit-pencil",click:()=>{this.quotasVisible=!0}}],this.quotasNumber=function L(m){const p=[];for(let r=1;r<=m;r++)p.push(new V(r));return p}(N.q3),this.payValueControl=new T.FormControl({validators:[R.LU,(0,R.Go)(S),(0,R.VV)(f)]})}ngOnInit(){this.initializatedConfiguration()}get insufficientFunds(){return this.invoice?.codeStatic?this.source?.amount<this.payValueControl.value:this.source?.amount<this.invoice?.totalAmount}get isInvoiceOverflow(){return(this.invoice?.totalAmount||0)>l}get disabled(){return this.invoice?.codeStatic?this.payValueControl.invalid:this.isInvoiceOverflow||this.insufficientFunds}getQuotaStatus({value:r}){return this.quotas===r?"bocc":void 0}onQuota({value:r}){this.managerPaymentQr.setQuotas(r).when({success:()=>{this.quotas=r,this.quotasVisible=!1}})}onClose(){this.quotasVisible=!1}onSubmit(){this.invoice?.codeStatic&&this.managerPaymentQr.setAmount(+this.payValueControl.value),this.mboProvider.navigation.next(P.RESULT)}initializatedConfiguration(){var r=this;return(0,v.Z)(function*(){(yield r.requestConfiguration.confirmation()).when({success:({amount:C,invoice:Q,quotas:x,requiredQuotas:k,source:et})=>{r.payValueControl.setValue(C),r.requiredQuotas=k,r.quotas=x,r.source=et,r.invoice=Q;const{codeDinamic:nt,totalAmount:ot}=Q;nt&&ot>l&&r.mboProvider.toast.warning("No es posible realizar esta compra, superas el monto autorizado")}})})()}}return m.\u0275fac=function(r){return new(r||m)(e.\u0275\u0275directiveInject(g.ZL),e.\u0275\u0275directiveInject(D.bE),e.\u0275\u0275directiveInject(D.tG),e.\u0275\u0275directiveInject(j.q))},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-payment-qr-confirmation-page"]],decls:35,vars:14,consts:[[1,"mbo-payment-qr-confirmation-page__content","mbo-page__scroller"],[1,"mbo-payment-qr-confirmation-page__header"],["title","Confirmar",3,"rightAction"],[1,"mbo-payment-qr-confirmation-page__body"],[1,"mbo-payment-qr-confirmation-page__title","subtitle2-medium"],[1,"mbo-payment-qr-confirmation-page__information"],[1,"mbo-payment-qr-confirmation-page__icon"],["icon","buy-shop"],[1,"mbo-payment-qr-confirmation-page__merchant"],[1,"subtitle2-medium","truncate"],["class","smalltext-medium truncate font-amathyst-700",4,"ngIf"],["class","smalltext-medium truncate",4,"ngIf"],["header","TIPO","title","PAGO QR",1,"mbo-payment-qr-confirmation-page__type"],["class","mbo-payment-qr-confirmation-page__iva","header","IVA",3,"amount","amountSmall",4,"ngIf"],["class","mbo-payment-qr-confirmation-page__inc","header","VALOR INC",3,"amount","amountSmall",4,"ngIf"],["class","mbo-payment-qr-confirmation-page__tip","header","PROPINA",3,"amount","amountSmall",4,"ngIf"],[1,"bocc-divider"],["class","mbo-payment-qr-confirmation-page__total","header","TOTAL",3,"amount",4,"ngIf"],["label","Valor a pagar","placeholder","$ 0",3,"formControl",4,"ngIf"],["class","mbo-payment-qr-confirmation-page__source bocc-card",4,"ngIf"],["class","mbo-payment-qr-confirmation-page__quotas bocc-card",4,"ngIf"],[1,"mbo-payment-qr-confirmation-page__footer"],["id","btn_payment-qr-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"disabled","click"],[3,"visible","visibleChange"],[1,"mbo-payment-qr-confirmation-page__sheet"],[1,"mbo-payment-qr-confirmation-page__sheet__title"],[1,"mbo-payment-qr-confirmation-page__sheet__grid"],[3,"click",4,"ngFor","ngForOf"],[1,"smalltext-medium","truncate","font-amathyst-700"],[1,"smalltext-medium","truncate"],[3,"amount","decimals"],["header","IVA",1,"mbo-payment-qr-confirmation-page__iva",3,"amount","amountSmall"],["header","VALOR INC",1,"mbo-payment-qr-confirmation-page__inc",3,"amount","amountSmall"],["header","PROPINA",1,"mbo-payment-qr-confirmation-page__tip",3,"amount","amountSmall"],["header","TOTAL",1,"mbo-payment-qr-confirmation-page__total",3,"amount"],["label","Valor a pagar","placeholder","$ 0",3,"formControl"],[1,"mbo-payment-qr-confirmation-page__source","bocc-card"],[3,"color","srcImg"],[1,"mbo-payment-qr-confirmation-page__source__info"],["header","ORIGEN","detail","Banco de occidente",1,"",3,"title","subtitle","actions"],[4,"ngIf"],["bocc-theme","danger"],[1,"mbo-payment-qr-confirmation-page__quotas","bocc-card"],["header","CUOTAS",1,"mbo-payment-qr-confirmation-page__quotas__info",3,"title","actions"],[3,"click"],[1,"smalltext-medium"]],template:function(r,C){1&r&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information")(6,"label",4),e.\u0275\u0275text(7," \xbfDeseas realizar esta compra? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",5)(9,"div",6),e.\u0275\u0275element(10,"bocc-icon",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"label",9),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(14,q,2,1,"label",10),e.\u0275\u0275template(15,H,2,2,"label",11),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(16,"bocc-card-summary",12),e.\u0275\u0275template(17,J,1,2,"bocc-card-summary",13),e.\u0275\u0275template(18,K,1,2,"bocc-card-summary",14),e.\u0275\u0275template(19,W,1,2,"bocc-card-summary",15),e.\u0275\u0275element(20,"div",16),e.\u0275\u0275template(21,o,1,1,"bocc-card-summary",17),e.\u0275\u0275template(22,s,1,1,"mbo-currency-box",18),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(23,n,5,6,"div",19),e.\u0275\u0275template(24,i,2,2,"div",20),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(25,"div",21)(26,"button",22),e.\u0275\u0275listener("click",function(){return C.onSubmit()}),e.\u0275\u0275elementStart(27,"span"),e.\u0275\u0275text(28,"Pagar"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(29,"bocc-bottom-sheet",23),e.\u0275\u0275listener("visibleChange",function(x){return C.quotasVisible=x}),e.\u0275\u0275elementStart(30,"div",24)(31,"div",25),e.\u0275\u0275text(32," Seleccione n\xfamero de cuotas "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(33,"div",26),e.\u0275\u0275template(34,c,3,2,"bocc-button-diamond",27),e.\u0275\u0275elementEnd()()()),2&r&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("rightAction",C.cancelAction),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",null==C.invoice?null:C.invoice.merchant.name," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeDinamic),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==C.invoice?null:C.invoice.codeStatic),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",C.source),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",C.requiredQuotas),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",C.disabled),e.\u0275\u0275advance(3),e.\u0275\u0275property("visible",C.quotasVisible),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",C.quotasNumber))},dependencies:[d.NgForOf,d.NgIf,F.K,G.J,Z.A,Y.Z,w.Q,E.D,I.O,_.m,B.P,O.l,z.b,X.o],styles:["/*!\n * MBO PaymentQrConfirmation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 26/Oct/2022\n * Updated: 19/Jul/2024\n*/mbo-payment-qr-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__title{text-align:center}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__information{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__icon{position:relative;padding:var(--sizing-x2);box-sizing:border-box;border-radius:var(--sizing-x2);background:var(--color-blue-200)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__icon bocc-icon{color:var(--color-blue-700)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__merchant{display:flex;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__merchant bocc-amount{color:var(--color-carbon-lighter-700);font-size:inherit;letter-spacing:inherit}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__source{display:flex;flex-direction:row;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);border-radius:var(--sizing-x4);padding:var(--sizing-x6);box-sizing:border-box}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__source .bocc-card-summary__content{padding:0rem}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__source__info{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__quotas{padding:var(--sizing-x2);box-sizing:border-box;border-radius:var(--sizing-x4)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__footer button{width:100%}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__sheet{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__sheet__title{position:sticky;top:0rem;width:100%;text-align:center;padding:var(--sizing-x8) 0rem;box-sizing:border-box;z-index:var(--z-index-2);background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);color:var(--color-carbon-lighter-700);font-size:var(--body1-size);letter-spacing:var(--body1-letter-spacing);line-height:var(--body1-line-height);font-weight:var(--font-weight-semibold)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__sheet__grid{position:relative;display:grid;width:100%;grid-template-columns:1fr 1fr 1fr 1fr;row-gap:var(--sizing-x8)}mbo-payment-qr-confirmation-page .mbo-payment-qr-confirmation-page__sheet bocc-button-diamond{justify-self:center}\n"],encapsulation:2}),m})(),tt=(()=>{class m{}return m.\u0275fac=function(r){return new(r||m)},m.\u0275mod=e.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,M.RouterModule.forChild([{path:"",component:$}]),h.KI,u.Jx,h.A6,u.Zl,u.Qg,u.D1,u.DM,u.Oh,u.mV,u.P8,u.lx,u.b6,u.ks,h.o2]}),m})()},63674:(U,b,a)=>{a.d(b,{Eg:()=>g,Lo:()=>u,Wl:()=>v,ZC:()=>R,_f:()=>M,br:()=>y,tl:()=>h});var d=a(29306);const M={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},h=new d.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),u={color:"success",key:"paid",label:"Pagada"},v={color:"alert",key:"pending",label:"Por pagar"},R={color:"danger",key:"expired",label:"Vencida"},y={color:"info",key:"recurring",label:"Pago recurrente"},g={color:"info",key:"programmed",label:"Programado"}}}]);