(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9041],{108:(I,p,r)=>{r.d(p,{a:()=>b,z:()=>v});var n=r(87903),d=r(53113);function u(t){const{isError:o,message:a,type:c}=t;return{animation:(0,n.jY)(t),title:o?"\xa1Transferencia fallida!":"PENDING"===c?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:a}}function e({isError:t}){return t?[(0,n.wT)("Finalizar","finish","outline"),(0,n.wT)("Volver a intentar","retry")]:[(0,n.wT)("Hacer otra transferencia","retry","outline"),(0,n.wT)("Finalizar","finish")]}function g(t){const{approved:o,pending:a}=t;if(o)return function l(t,o){const{dateFormat:a,timeFormat:c}=new d.ou,i=[(0,n.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,n._f)("SUMA DE",o.amount),(0,n.SP)("DESDE",o.phone)];return o.description&&i.push((0,n.SP)("DESCRIPCI\xd3N",o.description)),i.push((0,n.cZ)(a,c)),i}(t,o);if(a)return function y(t,o){const{dateFormat:a,timeFormat:c}=new d.ou,i=[(0,n.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,n._f)("SUMA DE",o.amount),(0,n.SP)("DESDE",o.phone)];return o.description&&i.push((0,n.SP)("DESCRIPCI\xd3N",o.description)),i.push((0,n.cZ)(a,c)),i}(t,a);const{dateFormat:c,timeFormat:i}=new d.ou,m=[(0,n.SP)("TRANSFER"===t.type?"ENVIADO A":"SOLICITADO A",t.contact?.name,t.contact?.number),(0,n._f)("SUMA DE",t.amount)];return t.description&&m.push((0,n.SP)("DESCRIPCI\xd3N","","",t.description)),m.push((0,n.cZ)(c,i)),m}function v(t){const{status:o,transfiya:a}=t;return{actions:e(o),error:o.isError,header:u(o),informations:g(a),skeleton:!1}}function b(t){const o=[],{amount:a,category:c,color:i,date:{dateFormat:m,timeFormat:s},description:f,phoneFormat:h,reference:E}=t;return o.push((0,n.SP)("REFERENCIA",E)),o.push((0,n.fW)("TIPO DE TRANSACCI\xd3N",i,c)),o.push((0,n.SP)("CONTACTO",h)),o.push((0,n._f)("LA SUMA DE",a)),f&&o.push((0,n.Kt)("DESCRIPCI\xd3N",f)),o.push((0,n.cZ)(m,s)),o}},69041:(I,p,r)=>{r.r(p),r.d(p,{MboTransfiyaHistoryInformationPageModule:()=>c});var n=r(17007),d=r(78007),u=r(79798),e=r(99877),l=r(39904),y=r(95437),g=r(108),v=r(17698),b=r(10464),t=r(78021),o=r(1027);let a=(()=>{class i{constructor(s,f,h,E){this.ref=s,this.activateRoute=f,this.mboProvider=h,this.requestConfiguration=E,this.redirectPage=l.Z6.TRANSFERS.CELTOCEL.HOME,this.informations=[],this.backAction={id:"btn_transfiya-history-information_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(this.redirectPage)}},this.rightActions=[{id:"btn_transfiya-history-information_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){const{redirect:s,uuid:f}=this.activateRoute.snapshot.queryParams;this.redirectPage="history"===s?l.Z6.TRANSFERS.TRANSFIYA.HISTORY:l.Z6.TRANSFERS.CELTOCEL.HOME,this.element=this.ref.nativeElement.querySelector("#crd_transfiya-history-information_result"),this.ref.nativeElement.classList.add(l.fc),this.requestConfiguration.historyForUuid(f).when({success:h=>{this.informations=(0,g.a)(h)},failure:()=>{this.mboProvider.navigation.back(this.redirectPage)}})}}return i.\u0275fac=function(s){return new(s||i)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(d.ActivatedRoute),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(v.UD))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-transfiya-history-information-page"]],decls:8,vars:3,consts:[[1,"mbo-transfiya-history-information-page__content","mbo-page__scroller"],[1,"mbo-page__header"],[3,"leftAction","rightActions"],[1,"mbo-transfiya-history-information-page__body"],["id","crd_transfiya-history-information_result",3,"informations"],[1,"mbo-transfiya-history-information-page__title","subtitle2-medium"]],template:function(s,f){1&s&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"mbo-header-result",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information",4)(6,"label",5),e.\u0275\u0275text(7," Datos de la transacci\xf3n "),e.\u0275\u0275elementEnd()()()()()),2&s&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",f.backAction)("rightActions",f.rightActions),e.\u0275\u0275advance(2),e.\u0275\u0275property("informations",f.informations))},dependencies:[b.K,t.c,o.A],styles:["/*!\n * MBO TransfiyaHistoryInformation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 06/Jul/2023\n * Updated: 10/Jul/2024\n*/mbo-transfiya-history-information-page{--mbo-header-result-padding: calc( var(--mbo-application-body-safe-spacing) + var(--sizing-x4) ) var(--sizing-x4) var(--sizing-x4) var(--sizing-x4);position:relative;width:100%;height:100%;display:block}mbo-transfiya-history-information-page .mbo-transfiya-history-information-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-transfiya-history-information-page .mbo-transfiya-history-information-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-transfiya-history-information-page .mbo-transfiya-history-information-page__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),i})(),c=(()=>{class i{}return i.\u0275fac=function(s){return new(s||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[n.CommonModule,d.RouterModule.forChild([{path:"",component:a}]),u.KI,u.cN,u.A6]}),i})()}}]);