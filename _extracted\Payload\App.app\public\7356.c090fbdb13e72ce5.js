(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7356],{47356:(h,t,n)=>{n.r(t),n.d(t,{MboTransferTagAvalModule:()=>T});var l=n(17007),d=n(78007),a=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadComponent:()=>n.e(7762).then(n.bind(n,77762)).then(o=>o.MboTransferTagAvalSourcePage)},{path:"destination",loadComponent:()=>n.e(3882).then(n.bind(n,43882)).then(o=>o.MboTransferTagAvalDestinationPage)},{path:"amount",loadComponent:()=>n.e(7581).then(n.bind(n,7581)).then(o=>o.MboTransferTagAvalAmountPage)},{path:"confirmation",loadComponent:()=>n.e(2354).then(n.bind(n,82354)).then(o=>o.MboTransferTagAvalConfirmationPage)},{path:"result",loadComponent:()=>n.e(5156).then(n.bind(n,85156)).then(o=>o.MboTransferTagAvalResultPage)}];let T=(()=>{class o{}return o.\u0275fac=function(f){return new(f||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[l.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);