(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8879],{38879:(t,s,r)=>{r.r(s),r.d(s,{DigitalWalletWeb:()=>l});var o=r(17737);class l extends o.WebPlugin{connect(){return Promise.resolve({connect:!1})}destroy(){return Promise.resolve({destroy:!1})}provisionActivationCode(e){return Promise.resolve()}isConnected(){return Promise.resolve({connected:!1})}getWalletId(){return Promise.resolve({walletId:""})}getCards(){return Promise.resolve({cards:[]})}isCardEnrolled(e){return Promise.resolve({enrolled:!1})}enrollDigitalCard(e){return Promise.resolve()}setDisplayCard(e){return Promise.resolve({display:!1})}verifyCardStatus(e){return Promise.resolve({status:"cardNotSupport"})}pushCard(e){return Promise.resolve({status:"cardError"})}getTokens(e){return Promise.resolve({enabled:!1,tokens:[]})}suspendToken(e){return Promise.resolve({result:!1})}resumeToken(e){return Promise.resolve({result:!1})}deleteToken(e){return Promise.resolve({result:!1})}disconnect(){return Promise.resolve({disconnect:!1})}}}}]);