(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9484],{93252:(A,y,a)=>{a.d(y,{$x:()=>g,KF:()=>f,Xh:()=>s,pb:()=>b});var v=a(53113);class g{constructor(u,h,m){this.documentCustomer=u,this.channel=h,this.owner=m}}class s{constructor(u,h,m,w){this.source=u,this.amount=h,this.currencyCode=m,this.beneficiary=w}}class f{constructor(u){this.value=u}}class b extends v.LN{constructor(u,h,m){super(u,h),this.otp=m}}},99224:(A,y,a)=>{a.d(y,{M:()=>M,i:()=>k});var v=a(15861),g=a(87956),s=a(98699),f=a(93252);function b(n){return new f.Xh(n.source,n.amount,n.currencyCode,n.beneficiary)}var i=a(71776),u=a(39904),h=a(87903),m=a(42168),w=a(84757),l=a(99877);let E=(()=>{class n{constructor(t){this.http=t}send(t){const r=function S(n){return{accId:n.source.id,amt:n.amount,curCode:n.currencyCode,channel:n.beneficiary.channel.type,documentNumber:n.beneficiary.documentCustomer.number,documentType:n.beneficiary.documentCustomer.type.code}}(t);return(0,m.firstValueFrom)(this.http.post(u.bV.TRANSACTIONS.QUICK_WITHDRAWAL,r).pipe((0,w.map)(e=>{const{type:c}=(0,h.l1)(e,"SUCCESS");return new f.pb(c,e.otpInfo?.validity.validityPeriodInfo.desc||"",new f.KF(e.otpInfo?.otpValue))}))).catch(e=>{const{message:c,type:o}=(0,h.rU)(e);return new f.pb(o,c)})}}return n.\u0275fac=function(t){return new(t||n)(l.\u0275\u0275inject(i.HttpClient))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var I=a(20691);let W=(()=>{class n extends I.Store{constructor(t){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=t,this.eventBusService.subscribes(u.PU,()=>{this.reset()})}setSource(t,r=!1){this.reduce(e=>({...e,source:t,fromCustomer:r}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setBeneficiary(t){this.reduce(r=>({...r,beneficiary:t}))}selectForBeneficiary(){return this.select(({beneficiary:t,confirmation:r})=>({beneficiary:t,confirmation:r}))}setAmount(t){this.reduce(r=>({...r,amount:t,confirmation:!0,currencyCode:"COP"}))}selectForAmount(){return this.select(({amount:t,confirmation:r,source:e})=>({amount:t,confirmation:r,source:e}))}getCurrencyCode(){return this.select(({currencyCode:t})=>t)}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(l.\u0275\u0275inject(g.Yd))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),M=(()=>{class n{constructor(t,r,e){this.eventBusService=t,this.repository=r,this.store=e}setSource(t){try{return s.Either.success(this.store.setSource(t))}catch({message:r}){return s.Either.failure({message:r})}}setBeneficiary(t){try{return s.Either.success(this.store.setBeneficiary(t))}catch({message:r}){return s.Either.failure({message:r})}}setAmount(t){try{return s.Either.success(this.store.setAmount(t))}catch({message:r}){return s.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),r=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:t,source:r})}catch({message:t}){return s.Either.failure({message:t})}}send(){var t=this;return(0,v.Z)(function*(){const r=b(t.store.currentState),e=yield t.execute(r);return t.eventBusService.emit(e.channel),s.Either.success({withdrawal:r,status:e})})()}execute(t){try{return this.repository.send(t)}catch({message:r}){return Promise.resolve(f.pb.error(r))}}}return n.\u0275fac=function(t){return new(t||n)(l.\u0275\u0275inject(g.Yd),l.\u0275\u0275inject(E),l.\u0275\u0275inject(W))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var Q=a(78506);let k=(()=>{class n{constructor(t,r,e){this.products=t,this.store=r,this.session=e}source(){var t=this;return(0,v.Z)(function*(){try{const r=t.store.itIsConfirmation(),e=yield t.requestAccounts();return s.Either.success({confirmation:r,products:e})}catch({message:r}){return s.Either.failure({message:r})}})()}beneficiary(t){var r=this;return(0,v.Z)(function*(){try{const e=yield r.session.customer(),c=yield r.requestAccounts(),o=r.requestAccount(c,t),d=r.store.selectForBeneficiary(),C=r.store.itIsConfirmation();return s.Either.success({...d,confirmation:C,customer:e,products:c,source:o})}catch({message:e}){return s.Either.failure({message:e})}})()}amount(){try{return s.Either.success(this.store.selectForAmount())}catch({message:t}){return s.Either.failure({message:t})}}confirmation(){try{const t=b(this.store.currentState);return s.Either.success({withdrawal:t})}catch({message:t}){return s.Either.failure({message:t})}}requestAccounts(){return this.products.requestAccountsForTransfer()}requestAccount(t,r){let e=this.store.getSource();return!e&&r&&(e=t.find(({id:c})=>r===c),this.store.setSource(e,!0)),e}}return n.\u0275fac=function(t){return new(t||n)(l.\u0275\u0275inject(g.hM),l.\u0275\u0275inject(W),l.\u0275\u0275inject(Q._I))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},29484:(A,y,a)=>{a.r(y),a.d(y,{MboQuickWithdrawalResultPageModule:()=>r});var v=a(17007),g=a(78007),s=a(79798),f=a(30263),b=a(15861),i=a(99877),u=a(39904),h=a(95437),m=a(87903),w=a(53113);function l({isError:e,message:c}){return e?{animation:u.cj,title:"Operaci\xf3n fallida",subtitle:c}:{animation:u.F6,title:"La clave para retirar ha sido enviada a trav\xe9s de SMS a tu celular"}}function S({isError:e}){return e?[{event:"finish",label:"Finalizar",type:"outline"},{event:"retry",label:"Volver a intentar",type:"raised"}]:[{event:"finish",label:"Finalizar",type:"raised"}]}var I=a(99224),W=a(66613),M=a(10464),Q=a(78021),k=a(16442);function n(e,c){if(1&e&&(i.\u0275\u0275elementStart(0,"div",5),i.\u0275\u0275element(1,"mbo-header-result",6),i.\u0275\u0275elementEnd()),2&e){const o=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("rightActions",o.rightActions)}}function p(e,c){1&e&&(i.\u0275\u0275elementStart(0,"bocc-alert",7)(1,"b"),i.\u0275\u0275text(2,"Recuerda:"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(3," Esta clave s\xf3lo tiene una vigencia de 120 minutos. "),i.\u0275\u0275elementEnd()),2&e&&i.\u0275\u0275property("visible",!0)}let t=(()=>{class e{constructor(o,d,C){this.ref=o,this.mboProvider=d,this.managerWithdrawal=C,this.requesting=!0,this.template=u.$d,this.rightActions=[{id:"btn_quick-withdrawal-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_quick-withdrawal-result-page_template"),this.intializatedTransaction()}onAction(o){this.mboProvider.navigation.next("finish"===o?u.Z6.CUSTOMER.PRODUCTS.HOME:u.Z6.TRANSACTIONS.QUICK_WITHDRAWAL.SOURCE)}intializatedTransaction(){var o=this;return(0,b.Z)(function*(){(yield o.managerWithdrawal.send()).when({success:d=>{o.template=function E({status:e,withdrawal:c}){const{dateFormat:o,timeFormat:d}=new w.ou;return{actions:S(e),error:e.isError,header:l(e),informations:[(0,m._f)("SUMA DE",c.amount),(0,m.cZ)(o,d)],skeleton:!1}}(d)}},()=>{o.requesting=!1,o.managerWithdrawal.reset()})})()}}return e.\u0275fac=function(o){return new(o||e)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(h.ZL),i.\u0275\u0275directiveInject(I.M))},e.\u0275cmp=i.\u0275\u0275defineComponent({type:e,selectors:[["mbo-quick-withdrawal-result-page"]],decls:6,vars:3,consts:[[1,"mbo-quick-withdrawal-result-page__content","mbo-page__scroller"],["class","mbo-quick-withdrawal-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-quick-withdrawal-result-page__body"],["id","crd_quick-withdrawal-result-page_template",3,"template","action"],["icon","bell",3,"visible",4,"ngIf"],[1,"mbo-quick-withdrawal-result-page__header","mbo-page__header"],[3,"rightActions"],["icon","bell",3,"visible"]],template:function(o,d){1&o&&(i.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),i.\u0275\u0275template(2,n,2,1,"div",1),i.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),i.\u0275\u0275listener("action",function(R){return d.onAction(R)}),i.\u0275\u0275template(5,p,4,1,"bocc-alert",4),i.\u0275\u0275elementEnd()()()()),2&o&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!d.requesting),i.\u0275\u0275advance(2),i.\u0275\u0275property("template",d.template),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!d.requesting&&!d.template.error))},dependencies:[v.NgIf,W.B,M.K,Q.c,k.u],styles:["/*!\n * MBO QuickWithdrawalResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ene/2023\n * Updated: 05/Ene/2024\n*/mbo-quick-withdrawal-result-page{position:relative;display:block;width:100%;height:100%}mbo-quick-withdrawal-result-page .mbo-quick-withdrawal-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-quick-withdrawal-result-page .mbo-quick-withdrawal-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-quick-withdrawal-result-page .mbo-quick-withdrawal-result-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}\n"],encapsulation:2}),e})(),r=(()=>{class e{}return e.\u0275fac=function(o){return new(o||e)},e.\u0275mod=i.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,g.RouterModule.forChild([{path:"",component:t}]),f.B4,s.KI,s.cN,s.tu]}),e})()}}]);