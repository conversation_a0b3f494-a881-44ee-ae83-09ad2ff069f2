(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4813],{14813:(g,a,n)=>{n.r(a),n.d(a,{MboTransfiyaPendingModule:()=>P});var l=n(17007),t=n(78007),d=n(99877);const M=[{path:"",loadChildren:()=>n.e(4597).then(n.bind(n,94597)).then(o=>o.MboTransfiyaPendingHomePageModule)},{path:"source",loadChildren:()=>n.e(5145).then(n.bind(n,55145)).then(o=>o.MboTransfiyaPendingSourcePageModule)},{path:"confirmation",loadChildren:()=>n.e(7632).then(n.bind(n,87632)).then(o=>o.MboTransfiyaPendingConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(7432).then(n.bind(n,67432)).then(o=>o.MboTransfiyaPendingResultPageModule)}];let P=(()=>{class o{}return o.\u0275fac=function(h){return new(h||o)},o.\u0275mod=d.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=d.\u0275\u0275defineInjector({imports:[l.CommonModule,t.RouterModule.forChild(M)]}),o})()}}]);