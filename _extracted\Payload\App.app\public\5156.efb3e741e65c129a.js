(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5156],{53644:(y,_,s)=>{s.d(_,{MK:()=>A,NM:()=>T,Rm:()=>f,VS:()=>r,ay:()=>g});var m=s(90806);class g{constructor(n){this.tagAval=n}}class T{constructor(n,t){this.subtitle=n,this.title=t}}class r{constructor(n,t,o){this.fullName=n,this.documentType=t,this.documentNumber=o,this.maskName=(0,m.Z)(n)}}class f{constructor(n,t,o,e,u,v){this.keyType=n,this.tagAval=t,this.accountReceptor=o,this.type=e,this.bank=u,this.customer=v}get customerMaskName(){return this.customer.maskName}}class A{constructor(n,t,o,e,u,v,d){this.source=n,this.account=t,this.contact=o,this.customerName=e,this.ipAddress=u,this.amount=v,this.note=d}}},30786:(y,_,s)=>{s.d(_,{$:()=>A,Ry:()=>n,iK:()=>l});var m=s(29306),g=s(64892),T=s(87903),r=s(53644);const f={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function A(t){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:o,CustIdentType:e,GovIssueIdent:u,PersonName:{FirstName:v}},DepAcctId:{AcctId:d,AcctType:I,BankInfo:c}},RefInfo:E}=t,a=u?.GovIssueIdentType||e,i=u?.IdentSerialNum||o,{RefId:h,RefType:p}=E[0];return new r.Rm(p,h,d,I,new m.Br(c.BankId,c.Name,c.BankId===g.qE.Occidente),new r.VS(v,(0,T.nX)(f[a]),i))}function l(t){return{fromDepAcctId:t.source.id,fromDepAcctName:t.source.name,fromDepAcctType:t.source.type,fromNickName:t.source.nickname,toDepAcctBankId:t.account.bank.id,toDepAcctType:t.account.type,toDepAcctId:t.account.accountReceptor,toDepAcctName:t.account.customer.fullName,toNickName:"",toUserIdNumber:t.account.customer.documentNumber,toUserIdType:t.account.customer.documentType.code,keyInfo:{key:t.account.tagAval,type:t.account.keyType},personInfoTo:{fullName:t.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:t.account.customer.documentType.code,identSerialNum:t.account.customer.documentNumber}},personInfoFrom:{firstName:t.customerName.clientFirstName,lastName:t.customerName.clientLastName,legalName:`${t.customerName.clientFirstName} ${t.customerName.clientLastName}`},curAmt:t.amount.toString(),refId:t.note?.reference||"",memo:t.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function n(t){return new r.MK(t.source,t.account,new r.NM(t.destination.tagAval,t.account.customer.fullName),t.customerName,t.ipAddress,t.amount,t.note)}},90806:(y,_,s)=>{s.d(_,{D:()=>f,Z:()=>A});var m=s(87903),g=s(53113);function T(l){const{isError:n,message:t}=l;return{animation:(0,m.jY)(l),title:n?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:t}}function r({isError:l}){return l?[(0,m.wT)("Finalizar","finish","outline"),(0,m.wT)("Volver a intentar","retry")]:[(0,m.wT)("Hacer otra transferencia","retry","outline"),(0,m.wT)("Finalizar","finish")]}function f(l){const{dateFormat:n,timeFormat:t}=new g.ou,{status:o,tagAval:e}=l,u=[(0,m.SP)("DESTINO",e.account.customer.maskName,e.account.tagAval,e.account.bank.name),(0,m._f)("SUMA DE",e.amount)];return e.note&&u.push((0,m.SP)("DESCRIPCI\xd3N",e.note.description,"",e.note.reference)),u.push((0,m.cZ)(n,t)),{actions:r(o),error:o.isError,header:T(o),informations:u,skeleton:!1}}function A(l){const n=l.split(" "),[t]=n,o=n[n.length-1],e=o.length,v=e>3?3:2;return`${t.substring(0,t.length>4?4:2)}*****${o.substring(e-v,e)}`}},90596:(y,_,s)=>{s.d(_,{$:()=>m.$,N:()=>n});var m=s(50142),g=s(15861),T=s(87956),r=s(98699),f=s(30786),A=s(23604),l=s(99877);let n=(()=>{class t{constructor(e,u){this.store=e,this.products=u}source(){var e=this;return(0,g.Z)(function*(){try{const u=yield e.products.requestAccountsForTransfer(),v=e.store.itIsConfirmation();return r.Either.success({confirmation:v,products:u})}catch({message:u}){return r.Either.failure({message:u})}})()}destination(){var e=this;return(0,g.Z)(function*(){try{const u=yield e.products.requestAccountsForTransfer(),v=e.store.itIsConfirmation();return r.Either.success({confirmation:v,hasOneSource:u.length<2,destination:e.store.getTagAval()})}catch({message:u}){return r.Either.failure({message:u})}})()}amount(){try{const e=this.store.itIsConfirmation(),u=this.store.getSource(),v=this.store.getAmount(),d=this.store.getAccount();return r.Either.success({account:d,amount:v,confirmation:e,source:u})}catch({message:e}){return r.Either.failure({message:e})}}confirmation(){try{const e=(0,f.Ry)(this.store.currentState);return r.Either.success({transfer:e})}catch({message:e}){return r.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(A.B),l.\u0275\u0275inject(T.hM))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},50142:(y,_,s)=>{s.d(_,{$:()=>I});var m=s(15861),g=s(87956),T=s(53113),r=s(98699),f=s(30786),A=s(71776),l=s(39904),n=s(87903),t=s(42168),o=s(84757),e=s(99877);let u=(()=>{class c{constructor(a){this.http=a}requestVerifyAccount(a){var i=this;return(0,m.Z)(function*(){return(0,t.firstValueFrom)(i.http.post(l.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:a},pilot:!0}).pipe((0,o.map)(h=>h.map(p=>(0,f.$)(p))),(0,o.catchError)(h=>{if("206"===h.error?.MsgRsHdr?.Status?.StatusCode)return(0,t.of)([]);throw h})))})()}send(a){return(0,t.firstValueFrom)(this.http.post(l.bV.TRANSFERS.TAG_AVAL,(0,f.iK)(a),{headers:{"X-Customer-Ip":a.ipAddress}}).pipe((0,o.map)(i=>(0,n.l1)(i,"SUCCESS")))).catch(i=>(0,n.rU)(i))}}return c.\u0275fac=function(a){return new(a||c)(e.\u0275\u0275inject(A.HttpClient))},c.\u0275prov=e.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();var v=s(23604),d=s(74520);let I=(()=>{class c{constructor(a,i,h,p){this.repository=a,this.store=i,this.eventBusService=h,this.customerStore=p}setSource(a,i=!1){try{return r.Either.success(this.store.setSource(a,i))}catch({message:h}){return r.Either.failure({message:h})}}verfiyAccount(a){var i=this;return(0,m.Z)(function*(){try{const h=yield i.requestAccount(a);return i.store.setTagAval(a),r.Either.success(!!h)}catch({message:h}){return r.Either.failure({message:h})}})()}setAmount(a){try{return r.Either.success(this.store.setAmount(a))}catch({message:i}){return r.Either.failure({message:i})}}reset(){try{const a=this.store.itIsFromCustomer(),i=this.store.getSource();return this.store.reset(),r.Either.success({fromCustomer:a,source:i})}catch({message:a}){return r.Either.failure({message:a})}}send(){var a=this;return(0,m.Z)(function*(){const i=a.customerStore.currentState,{session:{ip:h,customer:{clientFirstName:p,clientLastName:P}}}=i;a.store.setIpAddress(h),a.store.setCustomerName({clientFirstName:p,clientLastName:P});const M=(0,f.Ry)(a.store.currentState),C=yield a.execute(M);return a.eventBusService.emit(C.channel),r.Either.success({tagAval:M,status:C})})()}execute(a){try{return this.repository.send(a)}catch({message:i}){return Promise.resolve(T.LN.error(i))}}requestAccount(a){var i=this;return(0,m.Z)(function*(){const h=i.store.getTagAval();let p=i.store.getAccount();const{tagAval:P}=a;return(h?.tagAval!==P||!p)&&([p]=yield i.repository.requestVerifyAccount(P),i.store.setAccount(p)),p})()}setNote(a){try{return r.Either.success(this.store.setNote(a))}catch({message:i}){return r.Either.failure({message:i})}}removeNote(){try{return r.Either.success(this.store.removeNote())}catch({message:a}){return r.Either.failure({message:a})}}}return c.\u0275fac=function(a){return new(a||c)(e.\u0275\u0275inject(u),e.\u0275\u0275inject(v.B),e.\u0275\u0275inject(g.Yd),e.\u0275\u0275inject(d.f))},c.\u0275prov=e.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},23604:(y,_,s)=>{s.d(_,{B:()=>l});var m=s(39904),g=s(87956),T=s(20691),f=s(99877);let l=(()=>{class n extends T.Store{constructor(o){super({confirmation:!1,fromCustomer:!1}),o.subscribes(m.PU,()=>{this.reset()})}setIpAddress(o){this.reduce(e=>({...e,ipAddress:o}))}setCustomerName(o){this.reduce(e=>({...e,customerName:o}))}itIsFromCustomer(){return this.select(({fromCustomer:o})=>o)}setSource(o,e=!1){this.reduce(u=>({...u,source:o,fromCustomer:e}))}getSource(){return this.select(({source:o})=>o)}setTagAval(o){this.reduce(e=>({...e,destination:o}))}getTagAval(){return this.select(({destination:o})=>o)}setAccount(o){this.reduce(e=>({...e,account:o}))}getAccount(){return this.select(({account:o})=>o)}setAmount(o){this.reduce(e=>({...e,amount:o,confirmation:!0}))}getAmount(){return this.select(({amount:o})=>o)}itIsConfirmation(){return this.select(({confirmation:o})=>o)}setNote(o){this.reduce(e=>({...e,note:o}))}removeNote(){this.reduce(o=>({...o,note:void 0}))}}return n.\u0275fac=function(o){return new(o||n)(f.\u0275\u0275inject(g.Yd))},n.\u0275prov=f.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},85156:(y,_,s)=>{s.r(_),s.d(_,{MboTransferTagAvalResultPage:()=>v});var m=s(15861),g=s(17007),r=s(99877),A=s(79798),l=s(39904),n=s(95437),t=s(90806),o=s(90596),e=s(16442);function u(d,I){if(1&d&&(r.\u0275\u0275elementStart(0,"div",4),r.\u0275\u0275element(1,"mbo-header-result",5),r.\u0275\u0275elementEnd()),2&d){const c=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("rightActions",c.rightActions)}}let v=(()=>{class d{constructor(c,E,a){this.ref=c,this.mboProvider=E,this.managerTagAval=a,this.requesting=!0,this.template=l.$d,this.rightActions=[{id:"btn_aval-key-send-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfer-tag-aval-result-page_template"),this.initializatedTransaction()}onAction(c){this.mboProvider.navigation.next("finish"===c?l.Z6.CUSTOMER.PRODUCTS.HOME:l.Z6.TRANSFERS.TAG_AVAL.SOURCE)}initializatedTransaction(){var c=this;return(0,m.Z)(function*(){(yield c.managerTagAval.send()).when({success:E=>{c.template=(0,t.D)(E)}},()=>{c.requesting=!1,c.managerTagAval.reset()})})()}}return d.\u0275fac=function(c){return new(c||d)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(n.ZL),r.\u0275\u0275directiveInject(o.$))},d.\u0275cmp=r.\u0275\u0275defineComponent({type:d,selectors:[["mbo-transfer-tag-aval-result-page"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-transfer-tag-aval-result-page__content","mbo-page__scroller"],["class","mbo-transfer-tag-aval-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfer-tag-aval-result-page__body"],["id","crd_transfer-tag-aval-result-page_template",3,"template","action"],[1,"mbo-transfer-tag-aval-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(c,E){1&c&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),r.\u0275\u0275template(2,u,2,1,"div",1),r.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),r.\u0275\u0275listener("action",function(i){return E.onAction(i)}),r.\u0275\u0275elementEnd()()()()),2&c&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("ngIf",!E.requesting),r.\u0275\u0275advance(2),r.\u0275\u0275property("template",E.template))},dependencies:[g.CommonModule,g.NgIf,A.KI,A.cN,A.tu,e.u],styles:["/*!\n * MBO TransferTagAvalResult Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 13/Aug/2024\n * Updated: 13/Aug/2024\n*/mbo-transfer-tag-aval-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-tag-aval-result-page .mbo-transfer-tag-aval-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfer-tag-aval-result-page .mbo-transfer-tag-aval-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),d})()}}]);