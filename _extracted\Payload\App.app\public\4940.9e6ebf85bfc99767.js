(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4940],{64940:(x,C,s)=>{s.r(C),s.d(C,{MboProductMovementsPageModule:()=>S});var h=s(17007),E=s(78007),T=s(30263),I=s(79798),b=s(15861),m=s(22816),P=s(78506),p=s(39904),d=s(88844),M=s(95437),y=s(57544),e=s(99877),_=s(48774),R=s(48030),D=s(94614),A=s(50689);function N(r,u){if(1&r&&e.\u0275\u0275element(0,"mbo-product-info-movement",13),2&r){const t=u.$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("movement",t)("productId",n.productId)("coveredCardId",n.coveredCardId)}}function O(r,u){1&r&&(e.\u0275\u0275elementStart(0,"mbo-message-empty")(1,"p"),e.\u0275\u0275text(2,"De momento no cuentas con movimientos en el periodo seleccionado"),e.\u0275\u0275elementEnd()())}function a(r,u){if(1&r&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275template(1,N,1,3,"mbo-product-info-movement",12),e.\u0275\u0275template(2,O,3,0,"mbo-message-empty",9),e.\u0275\u0275elementEnd()),2&r){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.collection),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.collection.length)}}function i(r,u){1&r&&(e.\u0275\u0275elementStart(0,"mbo-message-empty")(1,"p"),e.\u0275\u0275text(2," Lo sentimos, ocurrio un error al tratar de consultar tus movimientos "),e.\u0275\u0275elementEnd()())}function L(r,u){1&r&&(e.\u0275\u0275elementStart(0,"div",14),e.\u0275\u0275element(1,"mbo-product-info-movement",15)(2,"mbo-product-info-movement",15),e.\u0275\u0275elementEnd()),2&r&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const F=[p.$J,p.GW,p.Xy,p.N1].map(r=>new d.Hv(r));let w=(()=>{class r{constructor(t,n,c){this.activateRoute=t,this.mboProvider=n,this.managerProductMovements=c,this.requesting=!1,this.collection=[],this.productId="",this.coveredCardId="",this.ranges=F,this.backAction={id:"btn_product-movements_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.back()}},this.scroller=new m.S,this.rangeControl=new y.FormControl}ngOnInit(){this.initializatedConfiguration(),this.unsubscription=this.rangeControl.subscribe(t=>{this.movements&&t&&!t.equals(this.movements.range)&&this.refresh(t)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get skeleton(){return this.requesting||!this.movements}onScroll(t){this.scroller.reset(t.target),this.scroller.verticalPercentage>90&&!this.movements.finished&&!this.requesting&&this.nextPage()}initializatedConfiguration(){var t=this;return(0,b.Z)(function*(){const{coveredCardId:n,currencyCode:c,productId:v}=t.activateRoute.snapshot.queryParams;t.productId=v,t.coveredCardId=n,t.resolveMovements(n?yield t.managerProductMovements.requestForCoveredCard({coveredCardId:n,creditCardId:t.productId,currencyCode:c}):yield t.managerProductMovements.requestForProductId(t.productId,c))})()}back(){const{coveredCardId:t,currencyCode:n,productId:c}=this.activateRoute.snapshot.queryParams;this.mboProvider.navigation.back(p.Z6.CUSTOMER.PRODUCTS.INFO,{productId:c,coveredCardId:t,currencyCode:n})}resolveMovements(t){t.when({success:n=>{const{collection:c,range:v}=n;this.collection=c,this.movements=n,this.rangeControl.setValue(v)},failure:()=>{this.back()}})}refresh(t){var n=this;return(0,b.Z)(function*(){const{coveredCardId:c,currencyCode:v,productId:f}=n.activateRoute.snapshot.queryParams;n.movements=void 0,n.requesting=!0,(yield n.managerProductMovements.refresh({currencyCode:v,productId:f,range:t,coveredCardId:c})).when({success:g=>{n.collection=g.collection,n.movements=g}},()=>{n.requesting=!1})})()}nextPage(){var t=this;return(0,b.Z)(function*(){const{coveredCardId:n,currencyCode:c,productId:v}=t.activateRoute.snapshot.queryParams;t.requesting=!0,(yield t.managerProductMovements.nextPage({currencyCode:c,productId:v,range:t.rangeControl.value,coveredCardId:n})).when({success:f=>{t.collection=f}},()=>{t.requesting=!1})})()}}return r.\u0275fac=function(t){return new(t||r)(e.\u0275\u0275directiveInject(E.ActivatedRoute),e.\u0275\u0275directiveInject(M.ZL),e.\u0275\u0275directiveInject(P.sy))},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-product-movements-page"]],decls:12,vars:7,consts:[[1,"mbo-product-movements-page__content",3,"scroll"],[1,"mbo-product-movements-page__header"],["title","Movimientos",3,"leftAction"],[1,"mbo-product-movements-page__body"],[1,"mbo-product-movements-page__title"],[1,"subtitle2-medium"],[1,"mbo-product-movements-page__subheader"],["preffixIcon","filter-settings",3,"suggestions","formControl","disabled"],["class","mbo-product-movements-page__list",4,"ngIf"],[4,"ngIf"],["class","mbo-product-movements-page__skeleton",4,"ngIf"],[1,"mbo-product-movements-page__list"],[3,"movement","productId","coveredCardId",4,"ngFor","ngForOf"],[3,"movement","productId","coveredCardId"],[1,"mbo-product-movements-page__skeleton"],[3,"skeleton"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(v){return n.onScroll(v)}),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"label",5),e.\u0275\u0275text(6,"Mis \xfaltimos movimientos"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6),e.\u0275\u0275element(8,"bocc-select-box",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,a,3,2,"div",8),e.\u0275\u0275template(10,i,3,0,"mbo-message-empty",9),e.\u0275\u0275template(11,L,3,2,"div",10),e.\u0275\u0275elementEnd()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",n.backAction),e.\u0275\u0275advance(6),e.\u0275\u0275property("suggestions",n.ranges)("formControl",n.rangeControl)("disabled",n.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.movements),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.movements&&!n.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.skeleton))},dependencies:[h.NgForOf,h.NgIf,_.J,R.t,D.K,A.A],styles:["/*!\n * MBO ProductMovements Page\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 20/Oct/2022\n * Updated: 08/Jul/2024\n*/mbo-product-movements-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between}mbo-product-movements-page .mbo-product-movements-page__content{position:relative;width:100%;overflow:auto}mbo-product-movements-page .mbo-product-movements-page__header{position:relative;width:100%}mbo-product-movements-page .mbo-product-movements-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x8);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-product-movements-page .mbo-product-movements-page__title{text-align:center}mbo-product-movements-page .mbo-product-movements-page__subheader bocc-icon{color:var(--color-blue-700)}mbo-product-movements-page .mbo-product-movements-page__subheader bocc-select-button{--bocc-button-padding: 0rem var(--sizing-x1);width:100%}mbo-product-movements-page .mbo-product-movements-page__subheader bocc-select-button .bocc-button__content{justify-content:space-between}mbo-product-movements-page .mbo-product-movements-page__list{position:relative;width:100%}\n"],encapsulation:2}),r})(),S=(()=>{class r{}return r.\u0275fac=function(t){return new(t||r)},r.\u0275mod=e.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=e.\u0275\u0275defineInjector({imports:[h.CommonModule,E.RouterModule.forChild([{path:"",component:w}]),T.Jx,T.tv,I.KN,I.Aj]}),r})()},88844:(x,C,s)=>{s.d(C,{YI:()=>p,tc:()=>A,iR:()=>D,jq:()=>M,Hv:()=>P,S6:()=>y,E2:()=>_,V4:()=>R,wp:()=>U,CE:()=>L,YQ:()=>T,ND:()=>b,t1:()=>O});var h=s(6472);class E{constructor(o){this.value=o}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:o}){return this.value.id===o}filtrable(o){return(0,h.hasPattern)(this.value.name,o)}}function T(l){return l.map(o=>new E(o))}class I{constructor(o){this.currency=o}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(o){return this.currency.code===o?.code}filtrable(o){return!0}}function b(l){return l.map(o=>new I(o))}var m=s(39904);class P{constructor(o){this.value=o}get title(){return this.value.label}get description(){return this.value.label}compareTo(o){return this.value.reference===o.reference}filtrable(o){return!0}}const p=m.Bf.map(l=>new P(l));class d{constructor(o,G){this.value=o,this.title=this.value.label,this.description=G?this.value.code:this.value.label}compareTo(o){return this.value===o}filtrable(o){return!0}}const M=new d(m.Gd),y=new d(m.XU),e=new d(m.t$),_=new d(m.j1),R=new d(m.k7),D=[M,y,e,_],A=[new d(m.Gd,!0),new d(m.XU,!0),new d(m.t$,!0),new d(m.j1,!0)];class N{constructor(o){this.product=o}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(o){return this.product.id===o?.id}filtrable(o){return!0}}function O(l){return l.map(o=>new N(o))}var a=s(89148);class i{constructor(o){this.filter=o}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(o){return this.value===o}filtrable(o){return!0}}const L=new i({label:"Todos los productos",short:"Todos",value:a.Gt.None}),F=new i({label:"Cuentas de ahorro",short:"Ahorros",value:a.Gt.SavingAccount}),w=new i({label:"Cuentas corriente",short:"Corrientes",value:a.Gt.CheckingAccount}),S=new i({label:"Depositos electr\xf3nicos",short:"Depositos",value:a.Gt.ElectronicDeposit}),r=new i({label:"Cuentas AFC",short:"AFC",value:a.Gt.AfcAccount}),u=new i({label:"Tarjetas de cr\xe9dito",short:"TC",value:a.Gt.CreditCard}),t=new i({label:"Inversiones",short:"Inversiones",value:a.Gt.CdtAccount}),n=new i({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:a.Gt.Loan}),c=new i({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:a.Gt.ResolvingCredit}),v=new i({label:"Productos Aval",short:"Aval",value:a.Gt.Aval}),f=new i({label:"Productos fiduciarios",short:"Fiducias",value:a.Gt.Trustfund}),g=new i({label:"Otros productos",short:"Otros",value:a.Gt.None}),U={SDA:F,DDA:w,EDA:S,AFC:r,CCA:u,CDA:t,DLA:n,LOC:c,AVAL:v,80:f,MDA:g,NONE:g,SBA:g,VDA:g}}}]);