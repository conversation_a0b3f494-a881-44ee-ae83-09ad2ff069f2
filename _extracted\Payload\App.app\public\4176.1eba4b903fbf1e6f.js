(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4176],{14176:(g,i,t)=>{t.r(i),t.d(i,{OnespanBindingWeb:()=>c});var a=t(17737);class c extends a.WebPlugin{getFingerprint(l){const n=localStorage.getItem("aval_device_fingerprint");if(n)return Promise.resolve({fingerPrint:n});const e=function s(o,l){return o.split("").map(n=>{const e=n.charCodeAt(0);if(e>=32&&e<=126){let r=e+l;for(;r>126;)r-=95;for(;r<32;)r+=95;return String.fromCharCode(r)}return n}).join("")}(`${window.navigator.userAgent}:${Date.now()}`,4);return localStorage.setItem("aval_device_fingerprint",e),Promise.resolve({fingerPrint:e})}cleanFingerprint(){return Promise.resolve()}}}}]);