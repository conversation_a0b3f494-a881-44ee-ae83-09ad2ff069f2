(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4810],{5917:(D,M,t)=>{t.d(M,{n:()=>b,Z:()=>g});var a=t(15861),r=t(29306),e=t(87956),n=t(53113),s=t(98699),i=t(83328),d=t(12263),v=t(85911),m=t(65518),_=t(81536),C=t(64892),o=t(81781),c=t(99877);let y=(()=>{class u{constructor(l,x,h,f,O,E){this.deviceService=l,this.digipassService=x,this.cryptoService=h,this.publicKeyRepository=f,this.enrollmentStore=O,this.enrollmentService=E,this.enrollmentKey="",O.subscribe(({enrollmentKey:P})=>{this.enrollmentKey=P||""})}document(l){var x=this;return(0,a.Z)(function*(){const[h,f]=yield Promise.all([x.deviceService.getInfo(),x.deviceService.getFingerprint()]);x.enrollmentService.reset();const O=yield x.service({companyId:C.qE.Occidente,deviceName:h.name||h.model,deviceOS:h.operatingSystem,deviceOperatingSystem:h.operatingSystem,deviceSerial:f,deviceUuid:h.uuid,id:l.number,idType:l.type.code,login:null,serial:f});return x.enrollmentStore.setDocument(l.type,l.number,!1),O})()}otp(l){var x=this;return(0,a.Z)(function*(){const h=yield x.publicKeyRepository.requestEnrollment();return x.service({otpValue:x.cryptoService.encodeRSA(h,l),forceOtpGeneration:!1,isOtpGeneratedByOtherChannel:!1})})()}digipass(){var l=this;return(0,a.Z)(function*(){const x=yield l.activateLicense();if(!x.success)throw Error(x.errorMessage||x.additionalErrorMessage);return l.activateInstance()})()}product(l){var x=this;return(0,a.Z)(function*(){const h=yield x.publicKeyRepository.requestEnrollment();return x.service({secureDataSecret:x.cryptoService.encodeRSA(h,l)})})()}password(l){var x=this;return(0,a.Z)(function*(){const h=yield x.publicKeyRepository.requestEnrollment();return x.service({universalPassword:x.cryptoService.encodeRSA(h,l),forgotPassword:!1})})()}device(l,x){var h=this;return(0,a.Z)(function*(){const[f,O]=yield Promise.all([h.deviceService.getInfo(),h.deviceService.getFingerprint()]);return h.service({deviceAppVersion:f.appVersion,deviceAppBuild:f.appCompilation,deviceManufacturer:f.manufacturer,deviceModel:f.model,deviceName:x,deviceOsVersion:f.osVersion,deviceOperatingSystem:f.operatingSystem,devicePlatform:f.platform,deviceSerial:O,deviceUuid:f.uuid,id:l.number,idType:l.type.code,isVirtual:!1,serial:O})})()}activateLicense(){var l=this;return(0,a.Z)(function*(){const x=yield l.digipassService.activeLicense(l.enrollmentKey);return l.service({deviceCode:x})})()}activateInstance(){var l=this;return(0,a.Z)(function*(){const x=yield l.digipassService.activeInstance(l.enrollmentKey);return l.service({signatureCode:x})})()}service(l){var x=this;return(0,a.Z)(function*(){const h=yield x.enrollmentService.execute(l);return x.enrollmentStore.setEnrollmentKey(h.enrollmentKey),h})()}}return u.\u0275fac=function(l){return new(l||u)(c.\u0275\u0275inject(e.U8),c.\u0275\u0275inject(e._L),c.\u0275\u0275inject(e.$I),c.\u0275\u0275inject(_.aH),c.\u0275\u0275inject(m.c),c.\u0275\u0275inject(o.s))},u.\u0275prov=c.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),g=(()=>{class u{constructor(l,x){this.enrollmentRepository=l,this.enrollmentStore=x}document(l){var x=this;return(0,a.Z)(function*(){try{const h=l||x.getCustomerDocument();if(!h)return v.d.finish();const f=yield x.enrollmentRepository.document(h);return f.value===i.l.FILL_UNIVERSAL_PASSWORD?v.d.finish("Ya te encuentras registrado en Banca M\xf3vil"):(0,d.F)(f)}catch({message:h}){return v.d.error(h)}})()}otp(l){var x=this;return(0,a.Z)(function*(){try{let h=yield x.enrollmentRepository.otp(l);return h.value===i.l.ONESPAN_ACTIVATE_LICENSE&&(h=yield x.enrollmentRepository.digipass()),(0,d.F)(h)}catch({message:h}){return v.d.error(h)}})()}product(l){var x=this;return(0,a.Z)(function*(){try{const h=yield x.enrollmentRepository.product(l),{success:f,value:O}=h;return f&&O===i.l.FILL_SECURE_DATA?v.d.finish():(0,d.F)(h)}catch({message:h}){return v.d.error(h)}})()}password(l){var x=this;return(0,a.Z)(function*(){try{const h=yield x.enrollmentRepository.password(l);return x.enrollmentStore.setPassword(l),(0,d.F)(h)}catch({message:h}){return v.d.error(h)}})()}device(l){var x=this;return(0,a.Z)(function*(){try{const{documentNumber:h,documentType:f}=x.enrollmentStore.currentState,O=new n.dp(f,h),E=yield x.enrollmentRepository.device(O,l);return(0,d.F)(E)}catch({message:h}){return v.d.error(h)}})()}getCustomerDocument(){const{documentNumber:l,documentType:x}=this.enrollmentStore.getDocument();if(x&&l)return new n.dp(x,l)}}return u.\u0275fac=function(l){return new(l||u)(c.\u0275\u0275inject(y),c.\u0275\u0275inject(m.c))},u.\u0275prov=c.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),b=(()=>{class u{constructor(l,x){this.biometricService=l,this.store=x}execute(){var l=this;return(0,a.Z)(function*(){try{const{documentNumber:x,documentType:h,password:f}=l.store.currentState;return yield l.biometricService.save(new r.J1(h,x,f),!0),s.Either.success(!0)}catch({message:x}){return s.Either.failure({message:x})}})()}}return u.\u0275fac=function(l){return new(l||u)(c.\u0275\u0275inject(e.oy),c.\u0275\u0275inject(m.c))},u.\u0275prov=c.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},24810:(D,M,t)=>{t.r(M),t.d(M,{MboEnrollmentBiometricPageModule:()=>h});var a=t(17007),r=t(78007),e=t(79798),n=t(30263),s=t(33395),i=t(15861),d=t(78506),v=t(39904),m=t(95437),_=t(87956),C=t(5917),o=t(99877),c=t(25317),y=t(52528),g=t(66613),b=t(45542),u=t(19102);function p(f,O){if(1&f){const E=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(E);const R=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(R.onCancel())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&f){const E=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1("Activar ",null==E.template?null:E.template.label," en otro momento")}}const l=v.Z6.AUTHENTICATION.ENROLLMENT;let x=(()=>{class f{constructor(E,P,R,K){this.mboProvider=E,this.biometricService=P,this.requestTemplate=R,this.saveBiometric=K,this.template=v.tz}ngOnInit(){this.requestConfiguration()}onSubmit(){var E=this;return(0,i.Z)(function*(){E.template.enabled?(yield E.biometricService.authentication())&&(yield E.saveBiometric.execute()).when({success:()=>{E.mboProvider.navigation.next(l.SUCCESS_SIGNUP)}}):E.mboProvider.navigation.next(l.SUCCESS_SIGNUP)})()}onCancel(){this.mboProvider.navigation.next(l.SUCCESS_SIGNUP)}requestConfiguration(){var E=this;return(0,i.Z)(function*(){(yield E.requestTemplate.execute()).when({success:P=>{E.template=P},failure:()=>{E.template=v.tz}})})()}}return f.\u0275fac=function(E){return new(E||f)(o.\u0275\u0275directiveInject(m.ZL),o.\u0275\u0275directiveInject(_.x1),o.\u0275\u0275directiveInject(d.aW),o.\u0275\u0275directiveInject(C.n))},f.\u0275cmp=o.\u0275\u0275defineComponent({type:f,selectors:[["mbo-enrollment-biometric-page"]],decls:19,vars:8,consts:[[1,"mbo-enrollment-biometric-page",3,"header"],["body","",1,"mbo-enrollment-biometric-page__body"],[1,"mbo-enrollment-biometric-page__info"],[1,"mbo-enrollment-biometric-page__title","subtitle2-medium"],[1,"mbo-enrollment-biometric-page__message","body2-medium"],[1,"mbo-enrollment-biometric-page__logo"],[3,"src"],["icon","bell",3,"visible"],["footer","",1,"mbo-enrollment-biometric-page__footer"],["id","btn_enrollment-biometric_cancel","bocc-button","outline","boccUtagComponent","click",3,"click",4,"ngIf"],["id","btn_enrollment-biometric_submit","bocc-button","raised","boccUtagComponent","click",3,"prefixIcon","click"],["id","btn_enrollment-biometric_cancel","bocc-button","outline","boccUtagComponent","click",3,"click"]],template:function(E,P){1&E&&(o.\u0275\u0275elementStart(0,"bocc-template-form",0)(1,"div",1),o.\u0275\u0275element(2,"mbo-bank-logo"),o.\u0275\u0275elementStart(3,"div",2)(4,"div",3),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"p",4),o.\u0275\u0275text(7),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(8,"div",5),o.\u0275\u0275element(9,"img",6),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(10,"bocc-alert",7)(11,"b"),o.\u0275\u0275text(12,"Recuerda:"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(13),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(14,"div",8),o.\u0275\u0275template(15,p,3,1,"button",9),o.\u0275\u0275elementStart(16,"button",10),o.\u0275\u0275listener("click",function(){return P.onSubmit()}),o.\u0275\u0275elementStart(17,"span"),o.\u0275\u0275text(18,"Continuar"),o.\u0275\u0275elementEnd()()()()),2&E&&(o.\u0275\u0275property("header",!1),o.\u0275\u0275advance(5),o.\u0275\u0275textInterpolate1(" ",null==P.template?null:P.template.title," "),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",null==P.template?null:P.template.message," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("src",null==P.template?null:P.template.logo,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(1),o.\u0275\u0275property("visible",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",null==P.template?null:P.template.alert," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",null==P.template?null:P.template.enabled),o.\u0275\u0275advance(1),o.\u0275\u0275property("prefixIcon",null==P.template?null:P.template.action))},dependencies:[a.NgIf,c.I,y.A,g.B,b.P,u.r],styles:["mbo-enrollment-biometric-page{--mbo-bank-logo-height: 26rem;--pvt-logo-margin-top: var(--sizing-x28);--pvt-body-rowgap: var(--sizing-x16);--pvt-info-rowgap: var(--sizing-x12)}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page{padding:0rem var(--sizing-form);box-sizing:border-box}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__body{position:relative;display:flex;flex-direction:column;width:100%;row-gap:var(--pvt-body-rowgap);padding-top:var(--sizing-safe-top, 0rem);box-sizing:border-box}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__body mbo-bank-logo{margin-top:var(--pvt-logo-margin-top)}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__info{position:relative;display:flex;flex-direction:column;width:100%;row-gap:var(--pvt-info-rowgap)}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__title{position:relative;width:100%;text-align:center}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__logo{position:relative;display:flex;justify-content:center;width:100%}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__logo img{width:44rem;height:44rem}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__footer{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x6);width:100%;padding:var(--sizing-x8) 0rem var(--sizing-safe-bottom-x8) 0rem}mbo-enrollment-biometric-page .mbo-enrollment-biometric-page__footer button{width:100%}@media screen and (max-height: 800px){mbo-enrollment-biometric-page{--mbo-bank-logo-height: var(--sizing-x24)}}@media screen and (max-height: 700px){mbo-enrollment-biometric-page{--mbo-bank-logo-height: var(--sizing-x22);--pvt-logo-margin-top: var(--sizing-x16);--pvt-body-rowgap: var(--sizing-x12);--pvt-info-rowgap: var(--sizing-x8)}}@media screen and (max-height: 600px){mbo-enrollment-biometric-page{--mbo-bank-logo-height: var(--sizing-x20)}}\n"],encapsulation:2}),f})(),h=(()=>{class f{}return f.\u0275fac=function(E){return new(E||f)},f.\u0275mod=o.\u0275\u0275defineNgModule({type:f}),f.\u0275inj=o.\u0275\u0275defineInjector({imports:[a.CommonModule,r.RouterModule.forChild([{path:"",component:x}]),s.kW,n.Av,n.B4,n.P8,e.rw]}),f})()},83328:(D,M,t)=>{t.d(M,{U:()=>r,l:()=>a});var a=(()=>{return(e=a||(a={})).CANNOT_REGISTER_DEVICE="CANNOT REGISTER DEVICE",e.CHANNEL_IS_BLOCKED="CHANNEL IS BLOCKED",e.COMPLETED="COMPLETED",e.DEVICE_ALREADY_REGISTERED="DEVICE ALREADY REGISTERED",e.ERROR_SIM_INVALID="ERROR SIM INVALID",e.FILL_CURRENT_CHANNEL_PASSWORD="FILL CURRENT CHANNEL PASSWORD",e.FILL_DEVICE_NAME="FILL DEVICE NAME",e.FILL_NEW_UNIVERSAL_PASSWORD="FILL NEW UNIVERSAL PASSWORD",e.FILL_OTP_DATA="FILL OTP DATA",e.FILL_SECURE_DATA="FILL SECURE DATA",e.FILL_UNIVERSAL_PASSWORD="FILL UNIVERSAL PASSWORD",e.INIT="INIT",e.LOGIN_VALIDATION_ERROR="LOGIN VALIDATION ERROR",e.ONESPAN_ACTIVATE_LICENSE="ENR20",e.ONESPAN_ACTIVATE_INSTANCE="ENR19",e.REGISTER_DEVICE_ERROR="REGISTER DEVICE ERROR",e.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION="RETRIES LIMIT EXCEED ON OTP GENERATION",e.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION="RETRIES LIMIT EXCEED ON SECURE DATA GENERATION",e.SERVICE_ERROR="SERVICE ERROR",e.USER_DOES_NOT_EXISTS="USER DOES NOT EXISTS",e.ENROLLMENT_ERROR_CODE="ENROLLMENT ERROR CODE",a;var e})(),r=(()=>{return(e=r||(r={})).LOGIN_ERROR="1",e.SECURE_DATA="121",e.VALIDATION_PRODUCT="1842",e.VALIDATION_SIM="112",e.PORTABILITY="186",r;var e})()},12263:(D,M,t)=>{t.d(M,{F:()=>_});var a=t(39904),r=t(83328),e=t(85911);const{AUTHENTICATION:{ENROLLMENT:n,ERRORS:s}}=a.Z6,i=[r.l.COMPLETED,r.l.DEVICE_ALREADY_REGISTERED],d=[r.l.LOGIN_VALIDATION_ERROR,r.l.ERROR_SIM_INVALID,r.l.CHANNEL_IS_BLOCKED,r.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION,r.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION,r.l.SERVICE_ERROR,r.l.USER_DOES_NOT_EXISTS,r.l.CANNOT_REGISTER_DEVICE],v=[r.U.PORTABILITY,r.U.SECURE_DATA,r.U.VALIDATION_PRODUCT,r.U.VALIDATION_SIM];function m(C){switch(C){case r.l.LOGIN_VALIDATION_ERROR:return n.WELCOME;case r.l.FILL_OTP_DATA:return n.OTP_VERIFICATION;case r.l.FILL_SECURE_DATA:return n.PRODUCT_VERIFICATION;case r.l.FILL_NEW_UNIVERSAL_PASSWORD:return n.PASSWORD_ASSIGNMENT;case r.l.FILL_DEVICE_NAME:return n.DEVICE_SIGNUP;case r.l.ERROR_SIM_INVALID:return s.SIM_INVALID;case r.l.CHANNEL_IS_BLOCKED:return s.CHANNEL_BLOCKED;case r.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION:return s.EXCEED_ATTEMPTS;case r.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION:return s.DEFAULT_MESSAGE;case r.l.CANNOT_REGISTER_DEVICE:return s.MAX_DEVICES;case r.l.SERVICE_ERROR:return s.SERVICE_FAILURE;default:return s.DEFAULT_MESSAGE}}function _(C){const{error:o,errorCode:c,message:y,success:g,value:b}=C;return g?i.includes(b)?e.d.finish():e.d.next(m(b)):b===r.l.ENROLLMENT_ERROR_CODE&&c&&v.includes(c)?e.d.back(s.DEFAULT_MESSAGE):d.includes(b)?e.d.back(m(b)):o?e.d.error(y):e.d.warning(y)}},85911:(D,M,t)=>{t.d(M,{d:()=>e});var a=t(98699);class e extends a.PartialSealed{static back(s){return new e("back",s)}static error(s){return new e("error",s)}static finish(s=""){return new e("finish",s)}static next(s){return new e("next",s)}static warning(s){return new e("warning",s)}}},81781:(D,M,t)=>{t.d(M,{s:()=>y});var a=t(71776),r=t(39904),e=t(42168);class i{constructor(b,u,p,l,x){this.accountType=b,this.productType=u,this.length=p,this.question=l,this.questionType=x}}class d{constructor(b,u,p,l,x,h,f,O,E){this.success=b,this.error=u,this.value=p,this.enrollmentKey=l,this.processId=x,this.secureData=h,this.errorMessage=f,this.errorCode=O,this.additionalErrorMessage=E}get message(){const b=this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR01).";return this.additionalErrorMessage?`${b}, ${this.additionalErrorMessage}`:b}static error(){return new d(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR02).")}}var v=t(83328);function _(g,b=!1){return new d(!!g.success,b,g.step,g.enrollmentKey,g.processId,g.secureDataBriefQuestion&&function m(g){return new i(g.accountType,g.productType,g.length,g.question,g.questionType)}(g.secureDataBriefQuestion),g.errorMessage,g.errorCode,g.additionalErrorMessage)}var C=t(65518),o=t(99877);let y=(()=>{class g{constructor(u,p){this.http=u,this.enrollmentStore=p,this.processId=""}execute(u){return(0,e.firstValueFrom)(this.http.post(r.bV.ENROLLMENT,{processId:this.processId,content:u,ipAddress:""}).pipe((0,e.map)(p=>_(p)),(0,e.tap)(({errorCode:p,processId:l,secureData:x})=>{this.enrollmentStore.setErrorCode(function c(g){switch(g){case v.U.PORTABILITY:return"PT87";case v.U.SECURE_DATA:return"43DS";case v.U.VALIDATION_PRODUCT:return"606ENP";case v.U.VALIDATION_SIM:return"VS61";default:return""}}(p)),this.processId=l,x&&this.enrollmentStore.setSecureData(x)}))).catch(({error:p})=>p?_(p,!0):d.error())}reset(){this.processId=""}}return g.\u0275fac=function(u){return new(u||g)(o.\u0275\u0275inject(a.HttpClient),o.\u0275\u0275inject(C.c))},g.\u0275prov=o.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},65518:(D,M,t)=>{t.d(M,{c:()=>s});var a=t(20691),e=t(99877);let s=(()=>{class i extends a.Store{constructor(){super({})}getDocument(){return this.select(v=>({documentType:v.documentType,documentNumber:v.documentNumber,documentRemember:v.documentRemember}))}setDocument(v,m,_){this.reduce(C=>({...C,documentType:v,documentNumber:m,documentRemember:_}))}setEnrollmentKey(v){this.reduce(m=>({...m,enrollmentKey:v}))}setPassword(v){this.reduce(m=>({...m,password:v}))}setSecureData(v){this.reduce(m=>({...m,secureData:v}))}getSecureData(){return this.select(({secureData:v})=>v)}setErrorCode(v){this.reduce(m=>({...m,errorCode:v}))}getErrorCode(){return this.select(({errorCode:v})=>v)}}return i.\u0275fac=function(v){return new(v||i)},i.\u0275prov=e.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},19102:(D,M,t)=>{t.d(M,{r:()=>s});var a=t(17007),e=t(99877);let s=(()=>{class i{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return i.\u0275fac=function(v){return new(v||i)},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(v,m){1&v&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&v&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",m.src,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),i})()},52701:(D,M,t)=>{t.d(M,{q:()=>i});var a=t(17007),e=t(30263),n=t(99877);let i=(()=>{class d{}return d.\u0275fac=function(m){return new(m||d)},d.\u0275cmp=n.\u0275\u0275defineComponent({type:d,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(m,_){1&m&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"bocc-icon",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"label",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()()),2&m&&(n.\u0275\u0275classMap(_.classTheme),n.\u0275\u0275advance(3),n.\u0275\u0275property("icon",_.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",_.label," "))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),d})()},55648:(D,M,t)=>{t.d(M,{u:()=>_});var a=t(15861),r=t(17007),n=t(30263),s=t(78506),i=t(99877);function v(C,o){if(1&C){const c=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",2),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(c);const g=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(g.onClick())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&C){const c=i.\u0275\u0275nextContext();i.\u0275\u0275property("prefixIcon",c.icon)("disabled",c.disabled),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",c.label," ")}}function m(C,o){if(1&C){const c=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",3),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(c);const g=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(g.onClick())}),i.\u0275\u0275elementEnd()}if(2&C){const c=i.\u0275\u0275nextContext();i.\u0275\u0275property("bocc-button-action",c.icon)("disabled",c.disabled)}}let _=(()=>{class C{constructor(c){this.preferences=c,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:c})=>{this.isIncognito=c||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var c=this;return(0,a.Z)(function*(){yield c.preferences.toggleIncognito()})()}}return C.\u0275fac=function(c){return new(c||C)(i.\u0275\u0275directiveInject(s.Bx))},C.\u0275cmp=i.\u0275\u0275defineComponent({type:C,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(c,y){1&c&&(i.\u0275\u0275template(0,v,3,3,"button",0),i.\u0275\u0275template(1,m,1,2,"button",1)),2&c&&(i.\u0275\u0275property("ngIf",!y.actionMode),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",y.actionMode))},dependencies:[r.CommonModule,r.NgIf,n.P8,n.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),C})()},72765:(D,M,t)=>{t.d(M,{rw:()=>a.r,qr:()=>r.q,uf:()=>e.u,Z:()=>v,t5:()=>g,$O:()=>y});var a=t(19102),r=t(52701),e=t(55648),n=t(17007),s=t(30263),i=t(99877);const d=["*"];let v=(()=>{class b{constructor(){this.disabled=!1}}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=i.\u0275\u0275defineComponent({type:b,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(p,l){1&p&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-icon",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3),i.\u0275\u0275projection(4),i.\u0275\u0275elementEnd()()),2&p&&(i.\u0275\u0275classProp("mbo-poster__content--disabled",l.disabled),i.\u0275\u0275advance(2),i.\u0275\u0275property("icon",l.icon))},dependencies:[n.CommonModule,s.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),b})();var m=t(33395),_=t(77279),C=t(87903),o=t(87956),c=t(25317);let y=(()=>{class b{constructor(p){this.eventBusService=p}onCopy(){this.value&&((0,C.Bn)(this.value),this.eventBusService.emit(_.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return b.\u0275fac=function(p){return new(p||b)(i.\u0275\u0275directiveInject(o.Yd))},b.\u0275cmp=i.\u0275\u0275defineComponent({type:b,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(p,l){1&p&&(i.\u0275\u0275elementStart(0,"bocc-icon",0),i.\u0275\u0275listener("click",function(){return l.onCopy()}),i.\u0275\u0275elementEnd()),2&p&&i.\u0275\u0275property("id",l.elementId)},dependencies:[n.CommonModule,s.Zl,m.kW,c.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),b})(),g=(()=>{class b{}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=i.\u0275\u0275defineComponent({type:b,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(p,l){1&p&&i.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&p&&(i.\u0275\u0275property("value",l.value),i.\u0275\u0275advance(1),i.\u0275\u0275property("value",l.value))},dependencies:[n.CommonModule,s.qd,y],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),b})()},79798:(D,M,t)=>{t.d(M,{Vc:()=>r.Vc,rw:()=>a.rw,k4:()=>r.k4,qr:()=>a.qr,uf:()=>a.uf,xO:()=>n.x,A6:()=>e.A,tu:()=>c,Tj:()=>y,GI:()=>z,Uy:()=>U,To:()=>Z,w7:()=>Y,o2:()=>r.o2,B_:()=>r.B_,fi:()=>r.fi,XH:()=>r.XH,cN:()=>r.cN,Aj:()=>r.Aj,J5:()=>r.J5,DB:()=>$.D,NH:()=>T.N,ES:()=>G.E,Nu:()=>r.Nu,x6:()=>V.x,KI:()=>J.K,iF:()=>r.iF,u8:()=>q.u,eM:()=>te.e,ZF:()=>oe.Z,wu:()=>ne.w,$n:()=>re.$,KN:()=>ie.K,cV:()=>ae.c,t5:()=>a.t5,$O:()=>a.$O,ZS:()=>ce.Z,sO:()=>se.s,bL:()=>pe,zO:()=>ee.z});var a=t(72765),r=t(27302),e=t(1027),n=t(7427),i=(t(16442),t(17007)),d=t(30263),v=t(44487),m=t.n(v),_=t(13462),C=t(21498),o=t(99877);let c=(()=>{class S{}return S.\u0275fac=function(I){return new(I||S)},S.\u0275mod=o.\u0275\u0275defineNgModule({type:S}),S.\u0275inj=o.\u0275\u0275defineInjector({imports:[i.CommonModule,_.LottieModule.forRoot({player:()=>m()}),a.rw,d.P8,d.Dj,C.P]}),S})(),y=(()=>{class S{ngBoccPortal(I){this.portal=I}onSubmit(){this.portal?.close()}}return S.\u0275fac=function(I){return new(I||S)},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-icon",2),o.\u0275\u0275elementStart(3,"label"),o.\u0275\u0275text(4," \xa1Atenci\xf3n! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"p"),o.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),o.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(10,"li",4),o.\u0275\u0275text(11,"Transacciones a celulares."),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(12,"li",4),o.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(14,"li",4),o.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(16,"p",5),o.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(18,"div",6)(19,"button",7),o.\u0275\u0275listener("click",function(){return L.onSubmit()}),o.\u0275\u0275elementStart(20,"span"),o.\u0275\u0275text(21,"Continuar"),o.\u0275\u0275elementEnd()()())},dependencies:[i.CommonModule,d.Zl,d.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),S})();var g=t(7603),b=t(87956),u=t(74520),p=t(39904),l=t(87903);function h(S,w){if(1&S){const I=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"div",6)(1,"label",7),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",8),o.\u0275\u0275text(4,"Tu gerente asignado (a)"),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"p",8),o.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(7,"button",9),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(I);const W=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(W.onEmail(W.manager.email))}),o.\u0275\u0275elementStart(8,"span",10),o.\u0275\u0275text(9),o.\u0275\u0275elementEnd()()()}if(2&S){const I=o.\u0275\u0275nextContext(2);o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",I.manager.name," "),o.\u0275\u0275advance(7),o.\u0275\u0275textInterpolate1(" ",I.manager.email," ")}}function f(S,w){if(1&S){const I=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),o.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"button",12),o.\u0275\u0275listener("click",function(W){o.\u0275\u0275restoreView(I);const X=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(X.onRetryManager(W))}),o.\u0275\u0275elementStart(4,"span"),o.\u0275\u0275text(5,"Recargar"),o.\u0275\u0275elementEnd()()()}}function O(S,w){if(1&S&&(o.\u0275\u0275elementStart(0,"div",3),o.\u0275\u0275template(1,h,10,2,"div",4),o.\u0275\u0275template(2,f,6,0,"div",5),o.\u0275\u0275elementEnd()),2&S){const I=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",I.manager),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!I.manager)}}function E(S,w){1&S&&(o.\u0275\u0275elementStart(0,"div",13),o.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),o.\u0275\u0275elementEnd()),2&S&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("active",!0),o.\u0275\u0275advance(1),o.\u0275\u0275property("active",!0),o.\u0275\u0275advance(1),o.\u0275\u0275property("active",!0),o.\u0275\u0275advance(1),o.\u0275\u0275property("active",!0))}t(29306);let P=(()=>{class S{constructor(I){this.customerService=I,this.requesting=!1}onRetryManager(I){this.customerService.requestManager(),I.stopPropagation()}onEmail(I){(0,l.Gw)(`mailto:${I}`)}onWhatsapp(){(0,l.Gw)(p.BA.WHATSAPP)}}return S.\u0275fac=function(I){return new(I||S)(o.\u0275\u0275directiveInject(b.vZ))},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,O,3,2,"div",1),o.\u0275\u0275template(2,E,5,4,"div",2),o.\u0275\u0275elementEnd()),2&I&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!L.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",L.requesting))},dependencies:[i.CommonModule,i.NgIf,d.P8,d.Dj,r.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),S})();const R={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},K={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},B={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function k(S,w){if(1&S&&(o.\u0275\u0275elementStart(0,"div",7),o.\u0275\u0275element(1,"mbo-contact-manager",8),o.\u0275\u0275elementEnd()),2&S){const I=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("manager",I.manager)("requesting",I.requesting)}}function N(S,w){if(1&S){const I=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"li",9)(1,"div",10),o.\u0275\u0275listener("click",function(W){const ue=o.\u0275\u0275restoreView(I).$implicit,be=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(be.onOption(ue,W))}),o.\u0275\u0275elementStart(2,"label",11),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"div",12)(5,"div",13),o.\u0275\u0275element(6,"bocc-icon",14),o.\u0275\u0275elementEnd()()()()}if(2&S){const I=w.$implicit;o.\u0275\u0275property("id",I.id),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",I.label," "),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",I.boccTheme),o.\u0275\u0275advance(2),o.\u0275\u0275property("icon",I.icon)}}let Z=(()=>{class S{constructor(I,L,W){this.utagService=I,this.customerStore=L,this.customerService=W,this.isManagerEnabled=!1,this.requesting=!1,this.options=[R,K,B]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:I})=>{this.isManagerEnabled=I?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(L=>{this.manager=L.manager,this.requesting=L.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(I){this.portal=I}onOption(I,L){this.utagService.link("click",I.id),this.portal?.send({action:"option",value:I}),L.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return S.\u0275fac=function(I){return new(I||S)(o.\u0275\u0275directiveInject(g.D),o.\u0275\u0275directiveInject(u.f),o.\u0275\u0275directiveInject(b.vZ))},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-contact-information"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275listener("click",function(){return L.onClose()}),o.\u0275\u0275template(1,k,2,2,"div",1),o.\u0275\u0275elementStart(2,"ul",2),o.\u0275\u0275template(3,N,7,4,"li",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),o.\u0275\u0275listener("click",function(){return L.onClose()}),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275listener("click",function(){return L.onClose()}),o.\u0275\u0275elementEnd()),2&I&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",L.isManagerEnabled),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",L.options))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,d.Zl,P],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),S})();var H=t(95437);let Y=(()=>{class S{constructor(I,L){this.floatingService=I,this.mboProvider=L,this.contactsFloating=this.floatingService.create(Z),this.contactsFloating?.subscribe(({action:W,value:X})=>{"option"===W?this.dispatchOption(X):this.close()})}subscribe(I){this.subscriber=I}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(I){"PQRS"===I.action?this.mboProvider.openUrl(p.BA.PQRS):this.subscriber&&this.subscriber(I)}}return S.\u0275fac=function(I){return new(I||S)(o.\u0275\u0275inject(d.B7),o.\u0275\u0275inject(H.ZL))},S.\u0275prov=o.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})(),z=(()=>{class S{constructor(){this.defenderLineNumber=p._L.DEFENDER_LINE,this.defenderLinePhone=p.WB.DEFENDER_LINE}ngBoccPortal(I){}onEmail(){(0,l.Gw)("mailto:<EMAIL>")}}return S.\u0275fac=function(I){return new(I||S)},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-contact-phones"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"mbo-attention-lines-form"),o.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),o.\u0275\u0275element(5,"bocc-icon",4),o.\u0275\u0275elementStart(6,"span",5),o.\u0275\u0275text(7,"Defensor del consumidor financiero"),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),o.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(12,"label",8)(13,"span"),o.\u0275\u0275text(14,"Lorena Cerchar Rosado"),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(15,"bocc-badge",9),o.\u0275\u0275text(16," Suplente "),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(17,"div",10),o.\u0275\u0275element(18,"bocc-icon",11),o.\u0275\u0275elementStart(19,"div",12)(20,"span",13),o.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(22,"div",10),o.\u0275\u0275element(23,"bocc-icon",14),o.\u0275\u0275elementStart(24,"div",12)(25,"a",15),o.\u0275\u0275text(26),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(27,"span",13),o.\u0275\u0275text(28," Ext. 15318 - 15311 "),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(29,"div",10),o.\u0275\u0275element(30,"bocc-icon",16),o.\u0275\u0275elementStart(31,"div",12)(32,"span",17),o.\u0275\u0275listener("click",function(){return L.onEmail()}),o.\u0275\u0275text(33," <EMAIL> "),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(34,"div",10),o.\u0275\u0275element(35,"bocc-icon",18),o.\u0275\u0275elementStart(36,"div",12)(37,"span",13),o.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),o.\u0275\u0275elementEnd()()()()()()),2&I&&(o.\u0275\u0275advance(25),o.\u0275\u0275property("href",L.defenderLinePhone,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",L.defenderLineNumber," "))},dependencies:[i.CommonModule,d.Zl,d.Oh,r.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),S})(),U=(()=>{class S{constructor(){this.whatsappNumber=p._L.WHATSAPP}ngBoccPortal(I){}onClick(){(0,l.Gw)(p.BA.WHATSAPP)}}return S.\u0275fac=function(I){return new(I||S)},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"div",2)(4,"button",3),o.\u0275\u0275listener("click",function(){return L.onClick()}),o.\u0275\u0275elementStart(5,"span"),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()()()),2&I&&(o.\u0275\u0275advance(6),o.\u0275\u0275textInterpolate(L.whatsappNumber))},dependencies:[i.CommonModule,d.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),S})();var T=t(10119),V=(t(87677),t(68789)),G=t(10455),$=t(91642),J=t(10464),q=t(75221),ee=t(88649),te=t(13043),oe=t(38116),ne=t(68819),re=t(19310),ie=t(94614),ae=(t(70957),t(91248),t(4663)),ce=t(13961),se=t(66709),j=t(24495),Q=t(57544),le=t(53113);class me extends Q.FormGroup{constructor(){const w=new Q.FormControl("",[j.zf,j.O_,j.Y2,(0,j.Mv)(24)]),I=new Q.FormControl("",[j.C1,j.zf,j.O_,j.Y2,(0,j.Mv)(24)]);super({controls:{description:I,reference:w}}),this.description=I,this.reference=w}setNote(w){this.description.setValue(w?.description),this.reference.setValue(w?.reference)}getNote(){return new le.$H(this.description.value,this.reference.value)}}function de(S,w){if(1&S&&o.\u0275\u0275element(0,"bocc-input-box",7),2&S){const I=o.\u0275\u0275nextContext();o.\u0275\u0275property("formControl",I.formControls.reference)}}let pe=(()=>{class S{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new me}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(I){this.portal=I}}return S.\u0275fac=function(I){return new(I||S)},S.\u0275cmp=o.\u0275\u0275defineComponent({type:S,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(I,L){1&I&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"div",3)(4,"div",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(6,"bocc-input-box",5),o.\u0275\u0275template(7,de,1,1,"bocc-input-box",6),o.\u0275\u0275elementEnd()()),2&I&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("leftAction",L.cancelAction)("rightAction",L.saveAction),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",L.title," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("formControl",L.formControls.description),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",L.requiredReference))},dependencies:[i.CommonModule,i.NgIf,d.Jx,d.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),S})()},35324:(D,M,t)=>{t.d(M,{V:()=>m});var a=t(17007),e=t(30263),n=t(39904),s=t(87903),i=t(99877);function v(_,C){if(1&_){const o=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"a",9),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(o);const y=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(y.onWhatsapp())}),i.\u0275\u0275elementStart(1,"div",3),i.\u0275\u0275element(2,"bocc-icon",10),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",5)(4,"label",6),i.\u0275\u0275text(5," Whatsapp "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"label",7),i.\u0275\u0275text(7),i.\u0275\u0275elementEnd()()()}if(2&_){const o=i.\u0275\u0275nextContext();i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",o.whatsappNumber," ")}}let m=(()=>{class _{constructor(){this.whatsapp=!1,this.whatsappNumber=n._L.WHATSAPP,this.nationalLineNumber=n._L.NATIONAL_LINE,this.bogotaLineNumber=n._L.BOGOTA_LINE,this.nationalLinePhone=n.WB.NATIONAL_LINE,this.bogotaLinePhone=n.WB.BOGOTA_LINE}onWhatsapp(){(0,s.Gw)(n.BA.WHATSAPP)}}return _.\u0275fac=function(o){return new(o||_)},_.\u0275cmp=i.\u0275\u0275defineComponent({type:_,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(o,c){1&o&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,v,8,1,"a",1),i.\u0275\u0275elementStart(2,"a",2)(3,"div",3),i.\u0275\u0275element(4,"bocc-icon",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"div",5)(6,"label",6),i.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(8,"label",7),i.\u0275\u0275text(9),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(10,"a",8)(11,"div",3),i.\u0275\u0275element(12,"bocc-icon",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(13,"div",5)(14,"label",6),i.\u0275\u0275text(15," Bogot\xe1 "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(16,"label",7),i.\u0275\u0275text(17),i.\u0275\u0275elementEnd()()()()),2&o&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",c.whatsapp),i.\u0275\u0275advance(1),i.\u0275\u0275property("href",c.nationalLinePhone,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",c.nationalLineNumber," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("href",c.bogotaLinePhone,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",c.bogotaLineNumber," "))},dependencies:[a.CommonModule,a.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),_})()},9593:(D,M,t)=>{t.d(M,{k:()=>v});var a=t(17007),e=t(30263),n=t(39904),s=t(95437),i=t(99877);let v=(()=>{class m{constructor(C){this.mboProvider=C,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(n.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(n.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(n.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return m.\u0275fac=function(C){return new(C||m)(i.\u0275\u0275directiveInject(s.ZL))},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(C,o){1&C&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275listener("click",function(){return o.onProducts()}),i.\u0275\u0275element(3,"bocc-icon",3),i.\u0275\u0275elementStart(4,"label",4),i.\u0275\u0275text(5," Productos "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(6,"div",5),i.\u0275\u0275listener("click",function(){return o.onTransfers()}),i.\u0275\u0275element(7,"bocc-icon",6),i.\u0275\u0275elementStart(8,"label",4),i.\u0275\u0275text(9," Transferir "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(10,"div",7),i.\u0275\u0275listener("click",function(){return o.onPaymentQR()}),i.\u0275\u0275elementStart(11,"div",8)(12,"div",9),i.\u0275\u0275element(13,"bocc-icon",10),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(14,"bocc-icon",11),i.\u0275\u0275elementStart(15,"label",4),i.\u0275\u0275text(16," Pago QR "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(17,"div",12),i.\u0275\u0275listener("click",function(){return o.onPayments()}),i.\u0275\u0275element(18,"bocc-icon",13),i.\u0275\u0275elementStart(19,"label",4),i.\u0275\u0275text(20," Pagar "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(21,"div",14),i.\u0275\u0275listener("click",function(){return o.onToken()}),i.\u0275\u0275element(22,"bocc-icon",15),i.\u0275\u0275elementStart(23,"label",4),i.\u0275\u0275text(24," Token "),i.\u0275\u0275elementEnd()()()()),2&C&&(i.\u0275\u0275advance(2),i.\u0275\u0275classProp("bocc-footer-form__element--active",o.isProducts),i.\u0275\u0275advance(4),i.\u0275\u0275classProp("bocc-footer-form__element--active",o.isTransfers),i.\u0275\u0275advance(11),i.\u0275\u0275classProp("bocc-footer-form__element--active",o.isPayments),i.\u0275\u0275advance(4),i.\u0275\u0275classProp("bocc-footer-form__element--active",o.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),m})()},83867:(D,M,t)=>{t.d(M,{o:()=>b});var a=t(17007),e=t(30263),n=t(8834),s=t(98699),m=(t(57544),t(99877));function C(u,p){if(1&u&&(m.\u0275\u0275elementStart(0,"label",11),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&u){const l=m.\u0275\u0275nextContext();m.\u0275\u0275classProp("mbo-currency-box__rate--active",l.hasValue),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate2(" ",l.valueFormat," ",l.rateCode," ")}}function o(u,p){if(1&u&&(m.\u0275\u0275elementStart(0,"div",12),m.\u0275\u0275element(1,"img",13),m.\u0275\u0275elementEnd()),2&u){const l=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275property("src",l.icon,m.\u0275\u0275sanitizeUrl)}}function c(u,p){if(1&u&&(m.\u0275\u0275elementStart(0,"div",14),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&u){const l=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",l.currencyCode," ")}}function y(u,p){if(1&u&&(m.\u0275\u0275elementStart(0,"div",15),m.\u0275\u0275element(1,"bocc-icon",16),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&u){const l=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",null==l.formControl.error?null:l.formControl.error.message," ")}}function g(u,p){if(1&u&&(m.\u0275\u0275elementStart(0,"div",18),m.\u0275\u0275element(1,"bocc-icon",19),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&u){const l=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",l.helperInfo," ")}}let b=(()=>{class u{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,s.itIsDefined)(this.rate)}get value(){const l=+this.formControl?.value;return isNaN(l)?0:this.hasRate?l/this.rate:0}get valueFormat(){return(0,n.b)({value:this.value,symbol:"$",decimals:!0})}}return u.\u0275fac=function(l){return new(l||u)},u.\u0275cmp=m.\u0275\u0275defineComponent({type:u,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(l,x){1&l&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275template(4,C,2,4,"label",3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(5,"div",4)(6,"div",5),m.\u0275\u0275template(7,o,2,1,"div",6),m.\u0275\u0275element(8,"bocc-currency-field",7),m.\u0275\u0275template(9,c,2,1,"div",8),m.\u0275\u0275elementEnd()(),m.\u0275\u0275template(10,y,4,1,"div",9),m.\u0275\u0275template(11,g,4,1,"div",10),m.\u0275\u0275elementEnd()),2&l&&(m.\u0275\u0275classProp("mbo-currency-box--focused",x.formControl.focused)("mbo-currency-box--error",x.formControl.invalid&&x.formControl.touched)("mbo-currency-box--disabled",x.formControl.disabled||x.disabled),m.\u0275\u0275advance(2),m.\u0275\u0275property("for",x.elementId),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",x.label," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",x.hasRate),m.\u0275\u0275advance(3),m.\u0275\u0275property("ngIf",x.icon),m.\u0275\u0275advance(1),m.\u0275\u0275property("elementId",x.elementId)("placeholder",x.placeholder)("disabled",x.disabled)("formControl",x.formControl),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",x.currencyCode),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",x.formControl.invalid&&x.formControl.touched),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",x.helperInfo&&!(x.formControl.invalid&&x.formControl.touched)))},dependencies:[a.CommonModule,a.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),u})()},85070:(D,M,t)=>{t.d(M,{f:()=>d});var a=t(17007),e=t(78506),n=t(99877);const i=["*"];let d=(()=>{class v{constructor(_){this.session=_}ngOnInit(){this.session.customer().then(_=>this.customer=_)}}return v.\u0275fac=function(_){return new(_||v)(n.\u0275\u0275directiveInject(e._I))},v.\u0275cmp=n.\u0275\u0275defineComponent({type:v,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(_,C){1&_&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"label",1),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",2),n.\u0275\u0275projection(4),n.\u0275\u0275elementEnd()()),2&_&&(n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==C.customer?null:C.customer.shortName))},dependencies:[a.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),v})()},65887:(D,M,t)=>{t.d(M,{X:()=>_});var a=t(17007),e=t(99877),s=t(30263),i=t(24495);function m(C,o){1&C&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let _=(()=>{class C{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[i.C1]:[]),this.unsubscription=this.documentType.subscribe(c=>{c&&(this.updateNumber(c,this.required),this.inputType=this.getInputType(c))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(c){if(c.required){const y=c.required.currentValue;this.documentType.setValidators(y?[i.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,y)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(c){return"PA"===c.code?"text":"number"}updateNumber(c,y){const g=this.validatorsForNumber(c,y);this.documentNumber.setValidators(g),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(c,y){return this.validatorsFromType(c).concat(y?[i.C1]:[])}maxLength(c){return y=>y&&y.length>c?{id:"maxLength",message:`Debe tener m\xe1ximo ${c} caracteres`}:null}validatorsFromType(c){switch(c.code){case"PA":return[i.JF];case"NIT":return[i.X1,this.maxLength(15)];default:return[i.X1]}}}return C.\u0275fac=function(c){return new(c||C)},C.\u0275cmp=e.\u0275\u0275defineComponent({type:C,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(c,y){1&c&&(e.\u0275\u0275template(0,m,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&c&&(e.\u0275\u0275property("ngIf",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementSelectId)("label",y.labelType)("suggestions",y.documents)("disabled",y.disabled)("formControl",y.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementInputId)("label",y.labelNumber)("type",y.inputType)("disabled",y.disabled)("formControl",y.documentNumber))},dependencies:[a.CommonModule,a.NgIf,s.DT,s.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),C})()},78021:(D,M,t)=>{t.d(M,{c:()=>C});var a=t(17007),e=t(30263),n=t(7603),s=t(98699),d=t(99877);function m(o,c){if(1&o){const y=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",5),d.\u0275\u0275listener("click",function(){d.\u0275\u0275restoreView(y);const b=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(b.onAction(b.leftAction))}),d.\u0275\u0275elementStart(1,"span"),d.\u0275\u0275text(2),d.\u0275\u0275elementEnd()()}if(2&o){const y=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",y.leftAction.id)("bocc-button",y.leftAction.type||"flat")("prefixIcon",y.leftAction.prefixIcon)("disabled",y.itIsDisabled(y.leftAction))("hidden",y.itIsHidden(y.leftAction)),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate(y.leftAction.label)}}function _(o,c){if(1&o){const y=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",6),d.\u0275\u0275listener("click",function(){const u=d.\u0275\u0275restoreView(y).$implicit,p=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(p.onAction(u))}),d.\u0275\u0275elementEnd()}if(2&o){const y=c.$implicit,g=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",y.id)("type",y.type||"flat")("bocc-button-action",y.icon)("disabled",g.itIsDisabled(y))("hidden",g.itIsHidden(y))}}let C=(()=>{class o{constructor(y){this.utagService=y,this.rightActions=[]}itIsDisabled({disabled:y}){return(0,s.evalValueOrFunction)(y)}itIsHidden({hidden:y}){return(0,s.evalValueOrFunction)(y)}onAction(y){const{id:g}=y;g&&this.utagService.link("click",g),y.click()}}return o.\u0275fac=function(y){return new(y||o)(d.\u0275\u0275directiveInject(n.D))},o.\u0275cmp=d.\u0275\u0275defineComponent({type:o,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(y,g){1&y&&(d.\u0275\u0275elementStart(0,"div",0)(1,"div",1),d.\u0275\u0275template(2,m,3,6,"button",2),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(3,"div",3),d.\u0275\u0275template(4,_,1,5,"button",4),d.\u0275\u0275elementEnd()()),2&y&&(d.\u0275\u0275advance(2),d.\u0275\u0275property("ngIf",g.leftAction),d.\u0275\u0275advance(2),d.\u0275\u0275property("ngForOf",g.rightActions))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),o})()},27302:(D,M,t)=>{t.d(M,{Vc:()=>a.V,k4:()=>r.k,o2:()=>e.o,B_:()=>v,fi:()=>m.f,XH:()=>_.X,cN:()=>y.c,Aj:()=>g.A,J5:()=>O.J,Nu:()=>R,iF:()=>Y});var a=t(35324),r=t(9593),e=t(83867),n=t(17007),s=t(99877);function d(z,U){if(1&z){const T=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"div",2),s.\u0275\u0275listener("click",function(){const G=s.\u0275\u0275restoreView(T).$implicit,$=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView($.onClickCurrency(G))}),s.\u0275\u0275elementStart(1,"div",3),s.\u0275\u0275element(2,"img",4)(3,"img",5),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"label",6),s.\u0275\u0275text(5),s.\u0275\u0275elementEnd()()}if(2&z){const T=U.$implicit,F=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",F.isEnabled(T)),s.\u0275\u0275advance(2),s.\u0275\u0275property("src",T.enabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(1),s.\u0275\u0275property("src",T.disabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate1(" ",T.label," ")}}t(57544);let v=(()=>{class z{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[T]=this.currencies;this.formControl.setValue(T)}}ngOnChanges(T){const{currencies:F}=T;if(F){const[V]=F.currentValue;this.formControl&&this.formControl.setValue(V)}}isEnabled(T){return T===this.formControl?.value}onClickCurrency(T){this.formControl&&!this.disabled&&this.formControl.setValue(T)}changeCurriencies(T){if(T.currencies){const F=T.currencies.currentValue,[V]=F;this.formControl&&this.formControl.setValue(V)}}}return z.\u0275fac=function(T){return new(T||z)},z.\u0275cmp=s.\u0275\u0275defineComponent({type:z,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275NgOnChangesFeature,s.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(T,F){1&T&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,d,6,5,"div",1),s.\u0275\u0275elementEnd()),2&T&&(s.\u0275\u0275classProp("mbo-currency-toggle--disabled",F.disabled),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngForOf",F.currencies))},dependencies:[n.CommonModule,n.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),z})();var m=t(85070),_=t(65887),C=t(30263),y=t(78021),g=t(50689),p=(t(7603),t(98699),t(72765)),O=t(88014);function E(z,U){if(1&z&&(s.\u0275\u0275elementStart(0,"div",4),s.\u0275\u0275element(1,"img",5),s.\u0275\u0275elementEnd()),2&z){const T=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("src",T.src,s.\u0275\u0275sanitizeUrl)}}const P=["*"];let R=(()=>{class z{}return z.\u0275fac=function(T){return new(T||z)},z.\u0275cmp=s.\u0275\u0275defineComponent({type:z,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],ngContentSelectors:P,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(T,F){1&T&&(s.\u0275\u0275projectionDef(),s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,E,2,1,"div",1),s.\u0275\u0275elementStart(2,"div",2)(3,"div",3),s.\u0275\u0275projection(4),s.\u0275\u0275elementEnd()()()),2&T&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",F.src))},dependencies:[n.CommonModule,n.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),z})();var K=t(24495);const B=/[A-Z]/,A=/[a-z]/,k=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,N=z=>z&&!B.test(z)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,Z=z=>z&&!A.test(z)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,H=z=>z&&!k.test(z)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let Y=(()=>{class z{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([K.C1,Z,N,H,(0,K.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const T=this.formControl.errors.reduce((V,{id:G})=>[...V,G],[]),F=T.includes("required");this.smallInvalid=T.includes("smallCase")||F,this.capitalInvalid=T.includes("capitalCase")||F,this.specialCharInvalid=T.includes("specialChar")||F,this.minLengthInvalid=T.includes("minlength")||F})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return z.\u0275fac=function(T){return new(T||z)},z.\u0275cmp=s.\u0275\u0275defineComponent({type:z,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(T,F){1&T&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275element(1,"bocc-password-box",1),s.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),s.\u0275\u0275text(4," Min\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"mbo-poster",4),s.\u0275\u0275text(6," May\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(7,"mbo-poster",5),s.\u0275\u0275text(8," Especial "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"mbo-poster",6),s.\u0275\u0275text(10," Caracteres "),s.\u0275\u0275elementEnd()()()),2&T&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",F.elementId)("disabled",F.disabled)("formControl",F.formControl),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.smallInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.capitalInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.specialCharInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.minLengthInvalid))},dependencies:[n.CommonModule,C.sC,p.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),z})()},50689:(D,M,t)=>{t.d(M,{A:()=>i});var a=t(17007),e=t(99877);const s=["*"];let i=(()=>{class d{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return d.\u0275fac=function(m){return new(m||d)},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(m,_){1&m&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&m&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",_.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),d})()},88014:(D,M,t)=>{t.d(M,{J:()=>s});var a=t(17007),e=t(99877);let s=(()=>{class i{}return i.\u0275fac=function(v){return new(v||i)},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(v,m){1&v&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[a.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),i})()},21498:(D,M,t)=>{t.d(M,{P:()=>o});var a=t(17007),e=t(30263),n=t(99877);function i(c,y){if(1&c&&n.\u0275\u0275element(0,"bocc-card-product-summary",7),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",g.information.product.color)("icon",g.information.product.icon)("number",g.information.product.number)("title",g.information.product.title)("subtitle",g.information.product.subtitle)}}function d(c,y){if(1&c&&n.\u0275\u0275element(0,"bocc-card-summary",8),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.standard.header)("title",g.information.standard.title)("subtitle",g.information.standard.subtitle)("detail",g.information.standard.detail)}}function v(c,y){if(1&c&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.amount.header)("amount",g.information.amount.value)("symbol",g.information.amount.symbol)("amountSmall",g.information.amount.small)}}function m(c,y){if(1&c&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(g.information.text.content)}}function _(c,y){if(1&c&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",12),n.\u0275\u0275element(1,"bocc-icon",13),n.\u0275\u0275elementStart(2,"span",14),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",15),n.\u0275\u0275elementStart(5,"span",14),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",g.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",g.information.datetime.time," ")}}function C(c,y){if(1&c&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&c){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",g.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",g.information.badge.label," ")}}let o=(()=>{class c{}return c.\u0275fac=function(g){return new(g||c)},c.\u0275cmp=n.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(g,b){1&g&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,i,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,d,1,4,"bocc-card-summary",2),n.\u0275\u0275template(3,v,1,4,"bocc-card-summary",3),n.\u0275\u0275template(4,m,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,_,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,C,3,4,"bocc-card-summary",6),n.\u0275\u0275elementEnd()),2&g&&(n.\u0275\u0275property("ngSwitch",b.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),c})()},7427:(D,M,t)=>{t.d(M,{x:()=>o});var a=t(17007),e=t(30263),n=t(87903),i=(t(29306),t(77279)),d=t(87956),v=t(68789),m=t(13961),_=t(99877);let o=(()=>{class c{constructor(g,b){this.eventBusService=g,this.onboardingScreenService=b,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,n.Bn)(this.product.tagAval),this.eventBusService.emit(i.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(m.Z)),this.tagAvalonboarding.open()}}return c.\u0275fac=function(g){return new(g||c)(_.\u0275\u0275directiveInject(d.Yd),_.\u0275\u0275directiveInject(v.x))},c.\u0275cmp=_.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[_.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(g,b){1&g&&(_.\u0275\u0275elementStart(0,"bocc-card-product",0),_.\u0275\u0275listener("key",function(){return b.onTagAval()})("onboarding",function(){return b.onBoarding()}),_.\u0275\u0275elementEnd()),2&g&&(_.\u0275\u0275classMap(b.product.bank.className),_.\u0275\u0275property("iconTitle",b.iconTitle)("title",b.product.nickname||b.product.name)("icon",b.product.logo)("tagAval",b.product.tagAvalFormat)("actions",b.actions)("color",b.product.color)("code",b.product.shortNumber)("label",b.product.label)("amount",b.product.amount)("incognito",b.incognito)("displayCard",!0)("statusLabel",null==b.product.status?null:b.product.status.label)("statusColor",null==b.product.status?null:b.product.status.color)("cromaline",!0)("msgError",b.msgError))},dependencies:[a.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),c})()},1027:(D,M,t)=>{t.d(M,{A:()=>g});var a=t(17007),r=t(72765),e=t(30263),n=t(99877);function s(b,u){if(1&b&&n.\u0275\u0275element(0,"bocc-card-product-summary",8),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",p.information.product.color)("icon",p.information.product.icon)("number",p.information.product.number)("title",p.information.product.title)("subtitle",p.information.product.subtitle)}}function i(b,u){if(1&b&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.standard.header)("title",p.information.standard.title)("subtitle",p.information.standard.subtitle)}}function d(b,u){if(1&b&&n.\u0275\u0275element(0,"bocc-card-summary",10),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.amount.header)("amount",p.information.amount.value)("symbol",p.information.amount.symbol)}}function v(b,u){if(1&b&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(p.information.text.content)}}function m(b,u){if(1&b&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",13),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",16),n.\u0275\u0275elementStart(5,"span",15),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",p.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",p.information.datetime.time," ")}}function _(b,u){if(1&b&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",17),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd()()),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.date.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",p.information.date.date," ")}}function C(b,u){if(1&b&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&b){const p=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",p.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",p.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",p.information.badge.label," ")}}let o=(()=>{class b{}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=n.\u0275\u0275defineComponent({type:b,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(p,l){1&p&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,s,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,i,1,3,"bocc-card-summary",2),n.\u0275\u0275template(3,d,1,3,"bocc-card-summary",3),n.\u0275\u0275template(4,v,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,m,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,_,4,3,"bocc-card-summary",6),n.\u0275\u0275template(7,C,3,4,"bocc-card-summary",7),n.\u0275\u0275elementEnd()),2&p&&(n.\u0275\u0275property("ngSwitch",l.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","date"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),b})();function c(b,u){1&b&&n.\u0275\u0275element(0,"mbo-card-information-element",8),2&b&&n.\u0275\u0275property("information",u.$implicit)}const y=["*"];let g=(()=>{class b{constructor(){this.skeleton=!1,this.informations=[]}}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=n.\u0275\u0275defineComponent({type:b,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(p,l){1&p&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"mbo-bank-logo",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"div",4),n.\u0275\u0275element(5,"div",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275projection(7),n.\u0275\u0275template(8,c,1,1,"mbo-card-information-element",7),n.\u0275\u0275elementEnd()()),2&p&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("result",!0),n.\u0275\u0275advance(5),n.\u0275\u0275property("ngForOf",l.informations))},dependencies:[a.CommonModule,a.NgForOf,r.rw,o],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),b})()},16442:(D,M,t)=>{t.d(M,{u:()=>p});var a=t(99877),e=t(17007),s=t(13462),d=t(19102),v=t(45542),m=t(65467),_=t(21498);function C(l,x){if(1&l&&(a.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&l){const h=a.\u0275\u0275nextContext();a.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",h.template.skeleton),a.\u0275\u0275property("secondary",!0)("active",h.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",h.template.header.subtitle," ")}}function o(l,x){1&l&&a.\u0275\u0275element(0,"mbo-card-information",16),2&l&&a.\u0275\u0275property("information",x.$implicit)}function c(l,x){if(1&l&&(a.\u0275\u0275elementStart(0,"div",14),a.\u0275\u0275projection(1),a.\u0275\u0275template(2,o,1,1,"mbo-card-information",15),a.\u0275\u0275elementEnd()),2&l){const h=a.\u0275\u0275nextContext();a.\u0275\u0275advance(2),a.\u0275\u0275property("ngForOf",h.template.informations)}}function y(l,x){1&l&&(a.\u0275\u0275elementStart(0,"div",17),a.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),a.\u0275\u0275elementEnd()),2&l&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0))}function g(l,x){if(1&l){const h=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",23),a.\u0275\u0275listener("click",function(){const E=a.\u0275\u0275restoreView(h).$implicit,P=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(P.onAction(E))}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&l){const h=x.$implicit;a.\u0275\u0275property("bocc-button",h.type)("prefixIcon",h.prefixIcon),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate(h.label)}}function b(l,x){if(1&l&&(a.\u0275\u0275elementStart(0,"div",21),a.\u0275\u0275template(1,g,3,3,"button",22),a.\u0275\u0275elementEnd()),2&l){const h=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("ngForOf",h.template.actions)}}const u=["*"];let p=(()=>{class l{constructor(){this.disabled=!1,this.action=new a.EventEmitter}onAction({event:h}){this.action.emit(h)}}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=a.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:u,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(h,f){1&h&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275element(3,"mbo-bank-logo",3),a.\u0275\u0275elementStart(4,"div",4),a.\u0275\u0275element(5,"ng-lottie",5),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(8,C,2,5,"bocc-skeleton-text",7),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(9,"div",8),a.\u0275\u0275element(10,"div",9),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(11,c,3,1,"div",10),a.\u0275\u0275template(12,y,4,5,"div",11),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(13,b,2,1,"div",12)),2&h&&(a.\u0275\u0275classProp("animation",!f.template.skeleton),a.\u0275\u0275advance(3),a.\u0275\u0275property("result",!0),a.\u0275\u0275advance(2),a.\u0275\u0275property("options",f.template.header.animation),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",f.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",f.template.header.title," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",f.template.skeleton||f.template.header.subtitle),a.\u0275\u0275advance(3),a.\u0275\u0275property("ngIf",!f.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",f.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",f.template.actions.length&&!f.disabled))},dependencies:[e.NgForOf,e.NgIf,s.LottieComponent,d.r,v.P,m.D,_.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),l})()},10119:(D,M,t)=>{t.d(M,{N:()=>y});var a=t(17007),e=t(99877),s=t(30263),i=t(7603),d=t(98699);function m(g,b){if(1&g&&e.\u0275\u0275element(0,"bocc-diamond",14),2&g){const u=b.$implicit,p=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",p.itIsSelected(u))}}function _(g,b){if(1&g){const u=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(u);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onAction(l.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&g){const u=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",u.footerActionLeft.id)("bocc-button",u.footerActionLeft.type)("prefixIcon",u.footerActionLeft.prefixIcon)("disabled",u.itIsDisabled(u.footerActionLeft))("hidden",u.itIsHidden(u.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(u.footerActionLeft.label)}}function C(g,b){if(1&g){const u=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(u);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onAction(l.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&g){const u=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",u.footerActionRight.id)("bocc-button",u.footerActionRight.type)("prefixIcon",u.footerActionRight.prefixIcon)("disabled",u.itIsDisabled(u.footerActionRight))("hidden",u.itIsHidden(u.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(u.footerActionRight.label)}}const o=["*"];let y=(()=>{class g{constructor(u,p){this.ref=u,this.utagService=p,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((u,p)=>p),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(u){return u===this.currentPosition}itIsDisabled({disabled:u}){return(0,d.evalValueOrFunction)(u)}itIsHidden({hidden:u}){return(0,d.evalValueOrFunction)(u)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(u){const{id:p}=u;p&&this.utagService.link("click",p),u.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(u){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(u),this.automatic=!1,this.setTranslatePosition(u)}setTranslatePosition(u){this.translateX=u*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(u){this.transformContent=`translateX(${u}px)`}emitPosition(u){this.finished||(this.finished=u+1===this.elements.length),this.position.emit({position:u,finished:this.finished})}getPositionSlide(u){return u>=this.elements.length?this.elements.length-1:u<0?0:u}setTouchHandler(u){let p=0,l=0;u.addEventListener("touchstart",x=>{if(x.changedTouches.length){const{clientX:h}=x.changedTouches.item(0);p=0,this.touched=!0,l=h}}),u.addEventListener("touchmove",x=>{if(x.changedTouches.length){const h=x.changedTouches.item(0),f=h.clientX-l;l=h.clientX,this.translateX+=f,p+=f,this.setTranslateContent(this.translateX)}}),u.addEventListener("touchend",x=>{this.touched=!1,x.changedTouches.length&&(Math.abs(p)/this.widthBody*100>=40&&(p>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return g.\u0275fac=function(u){return new(u||g)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(i.D))},g.\u0275cmp=e.\u0275\u0275defineComponent({type:g,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:o,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(u,p){1&u&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return p.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return p.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,m,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return p.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,_,3,6,"button",13),e.\u0275\u0275template(16,C,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&u&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",p.headerActionLeft)("rightAction",p.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",p.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",p.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",p.widthContent)("transform",p.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",p.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!p.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",p.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!p.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",p.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.footerActionRight))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.P8,s.u1,s.ou,s.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),g})()},68789:(D,M,t)=>{t.d(M,{x:()=>i});var a=t(7603),r=t(10455),e=t(87677),n=t(99877);let i=(()=>{class d{constructor(m){this.portalService=m}information(){this.portal||(this.portal=this.portalService.container({component:r.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(m,_){return this.portalService.container({component:m,container:e.C,props:{container:_?.containerProps,component:_?.componentProps}})}}return d.\u0275fac=function(m){return new(m||d)(n.\u0275\u0275inject(a.v))},d.\u0275prov=n.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},87677:(D,M,t)=>{t.d(M,{C:()=>e});var a=t(99877);let e=(()=>{class n{constructor(i){this.ref=i,this.visible=!1,this.visibleChange=new a.EventEmitter}open(i=0){setTimeout(()=>{this.changeVisible(!0)},i)}close(i=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},i)}append(i){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(i)}ngBoccPortal(i){this.portal=i}changeVisible(i){this.visible=i,this.visibleChange.emit(i)}}return n.\u0275fac=function(i){return new(i||n)(a.\u0275\u0275directiveInject(a.ElementRef))},n.\u0275cmp=a.\u0275\u0275defineComponent({type:n,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(i,d){1&i&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"div",1),a.\u0275\u0275elementEnd()),2&i&&a.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",d.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),n})()},10455:(D,M,t)=>{t.d(M,{E:()=>d});var a=t(17007),e=t(99877),s=t(27302),i=t(10119);let d=(()=>{class v{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(_){this.portal=_}onPosition({finished:_}){this.finished=_,_&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(_,C){1&_&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(c){return C.onPosition(c)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&_&&e.\u0275\u0275property("footerActionLeft",C.footerLeft)("footerActionRight",C.footerRight)("gradient",!0)},dependencies:[a.CommonModule,s.Nu,i.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),v})()},91642:(D,M,t)=>{t.d(M,{D:()=>p});var a=t(17007),e=t(99877),s=t(30263),i=t(87542),d=t(70658),v=t(3372),m=t(87956),_=t(72765);function C(l,x){1&l&&e.\u0275\u0275element(0,"mbo-bank-logo")}function o(l,x){1&l&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function c(l,x){if(1&l&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&l){const h=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",h.verifying)}}function y(l,x){1&l&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function g(l,x){if(1&l){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(h);const O=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(O.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&l){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",h.verifying)}}const b=["*"],{OtpInputSuperuser:u}=v.M;let p=(()=>{class l{constructor(h,f,O,E){this.ref=h,this.otpService=f,this.deviceService=O,this.preferencesService=E,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=i.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new i.yV}ngOnInit(){this.otpService.onCode(h=>{this.otpControls.setCode(h),this.otpControls.valid&&this.onAutocomplete(h)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(h){const{documentNumber:f}=h;f&&this.preferencesService.applyFunctionality(u,f.currentValue).then(O=>{this.itIsDocumentSuperuser=O})}get otpVisible(){return!d.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return d.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&d.N.otpReadonlyMobile}onAutocomplete(h){this.code.emit(h)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return l.\u0275fac=function(h){return new(h||l)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(m.no),e.\u0275\u0275directiveInject(m.U8),e.\u0275\u0275directiveInject(m.yW))},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:b,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(h,f){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,C,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,o,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(E){return f.onAutocomplete(E)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,c,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,y,7,0,"div",8),e.\u0275\u0275template(13,g,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",f.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",f.otpVisible),e.\u0275\u0275property("formControls",f.otpControls)("readonly",f.otpReadonly)("mobile",f.otpMobile)("disabled",f.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",f.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.isIos))},dependencies:[a.CommonModule,a.NgIf,s.P8,s.Yx,_.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),l})()},10464:(D,M,t)=>{t.d(M,{K:()=>d});var a=t(17007),e=t(99877),s=t(22816);const i=["*"];let d=(()=>{class v{constructor(_){this.ref=_,this.scroller=new s.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(_){this.scroller.reset(_.target)}}return v.\u0275fac=function(_){return new(_||v)(e.\u0275\u0275directiveInject(e.ElementRef))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(_,C){1&_&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(c){return C.onScroll(c)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&_&&e.\u0275\u0275classProp("mbo-page__content--start",C.scrollStart)("mbo-page__content--end",C.scrollEnd)},dependencies:[a.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),v})()},75221:(D,M,t)=>{t.d(M,{u:()=>v});var a=t(17007),e=t(30263),n=t(27302),i=(t(88649),t(99877));let v=(()=>{class m{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return m.\u0275fac=function(C){return new(C||m)},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(C,o){1&C&&i.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&C&&(i.\u0275\u0275property("formControl",o.passwordControl.controls.password)("disabled",o.disabled)("elementId",o.elementPasswordId),i.\u0275\u0275advance(1),i.\u0275\u0275property("elementId",o.elementConfirmId)("disabled",o.disabled)("formControl",o.passwordControl.controls.repeatPassword))},dependencies:[a.CommonModule,e.sC,n.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),m})()},88649:(D,M,t)=>{t.d(M,{z:()=>n});var a=t(57544),r=t(24495);class n extends a.FormGroup{constructor(){const i=new a.FormControl(""),d=new a.FormControl("",[r.C1,(s=i,i=>i&&i!==s.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var s;super({controls:{password:i,repeatPassword:d}})}get password(){return this.controls.password.value}}},13043:(D,M,t)=>{t.d(M,{e:()=>g});var a=t(17007),e=t(99877),s=t(30263),v=(t(57544),t(27302));function m(b,u){1&b&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function _(b,u){if(1&b&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&b){const p=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",p.title," ")}}function C(b,u){if(1&b){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(p).$implicit,f=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(f.onProduct(h))}),e.\u0275\u0275elementEnd()}if(2&b){const p=u.$implicit,l=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",p.color)("icon",p.logo)("title",p.nickname)("number",p.publicNumber)("detail",p.bank.name)("ghost",l.ghost)}}function o(b,u){if(1&b&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,_,2,1,"div",8),e.\u0275\u0275template(3,C,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&b){const p=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",p.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",p.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!p.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",p.msgError," ")}}function c(b,u){1&b&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&b&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const y=["*"];let g=(()=>{class b{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(p){return this.productControl?.value?.id===p.id}onProduct(p){this.select.emit(p),this.productControl?.setValue(p)}}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=e.\u0275\u0275defineComponent({type:b,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(p,l){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275template(2,o,6,5,"div",2),e.\u0275\u0275template(3,c,3,2,"div",3),e.\u0275\u0275elementEnd()),2&p&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!l.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.skeleton))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.w_,v.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),b})()},38116:(D,M,t)=>{t.d(M,{Z:()=>d});var a=t(17007),e=t(99877),s=t(30263);function i(v,m){if(1&v){const _=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const c=e.\u0275\u0275restoreView(_).$implicit,y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onAction(c))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&v){const _=m.$implicit,C=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(C.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",C.itIsDisabled(_)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(C.theme(_)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",_.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",_.label," ")}}let d=(()=>{class v{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(_){return _.requiredInformation&&_.errorInformation}theme(_){return this.itIsDisabled(_)?"none":_.theme}onAction(_){!this.itIsDisabled(_)&&this.action.emit(_.type)}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(_,C){1&_&&e.\u0275\u0275template(0,i,6,8,"div",0),2&_&&e.\u0275\u0275property("ngForOf",C.actions)},dependencies:[a.CommonModule,a.NgForOf,s.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),v})()},68819:(D,M,t)=>{t.d(M,{w:()=>P});var a=t(17007),e=t(99877),s=t(30263),i=t(39904),m=(t(57544),t(78506)),C=(t(29306),t(87903)),o=t(95437),c=t(27302),y=t(70957),g=t(91248),b=t(13961),u=t(68789),p=t(33395),l=t(25317);function x(R,K){if(1&R){const B=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(k){e.\u0275\u0275restoreView(B);const N=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(N.onBoarding(k))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(B);const k=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(k.onCopyKey(k.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(B);const k=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(k.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&R){const B=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",B.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",B.product.tagAval)}}function h(R,K){if(1&R&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&R){const B=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",B.digitalSection)("currencyCode",null==B.currencyControl.value?null:B.currencyControl.value.code)("hidden",B.itIsVisibleMovements)}}function f(R,K){if(1&R&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&R){const B=K.$implicit,A=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",B)("currencyCode",null==A.currencyControl.value?null:A.currencyControl.value.code)}}const O=[[["","header",""]],"*"],E=["[header]","*"];let P=(()=>{class R{constructor(B,A,k){this.mboProvider=B,this.managerInformation=A,this.onboardingScreenService=k,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(B){const{movements:A,sections:k}=B;A&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!A.currentValue),k&&this.product&&this.refreshComponent(this.product,k.currentValue),this.managerInformation.requestInfoBody().then(N=>{N.when({success:({canEditTagAval:Z})=>{this.canEditTagAval=Z}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(B){(0,C.Bn)(B),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(i.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(B,A){const k=(0,C.A2)(B);if(this.sectionPosition=0,A?.length){const N=A.map(({title:Z},H)=>({label:Z,value:H}));k&&(this.headerMovements.value=this.sections.length,N.push(this.headerMovements)),this.headers=N}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const N=[{label:"Error",value:1}];k&&N.unshift(this.headerMovements),this.headers=N}}onBoarding(B){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(b.Z)),this.tagAvalonboarding.open(),B.stopPropagation()}}return R.\u0275fac=function(B){return new(B||R)(e.\u0275\u0275directiveInject(o.ZL),e.\u0275\u0275directiveInject(m.vu),e.\u0275\u0275directiveInject(u.x))},R.\u0275cmp=e.\u0275\u0275defineComponent({type:R,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:E,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(B,A){1&B&&(e.\u0275\u0275projectionDef(O),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(N){return A.sectionPosition=N}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,x,15,2,"div",4),e.\u0275\u0275template(6,h,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,f,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&B&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",A.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",A.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",A.headers)("value",A.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",A.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==A.product?null:A.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",A.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",A.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",A.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",A.movements)("header",A.header)("product",A.product)("currencyCode",null==A.currencyControl.value?null:A.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",A.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!A.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.Gf,s.qw,s.P8,s.Dj,s.qd,y.K,g.I,c.Aj,p.kW,l.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),R})()},19310:(D,M,t)=>{t.d(M,{$:()=>h});var a=t(17007),r=t(99877),e=t(30263),n=t(87903);let s=(()=>{class f{transform(E,P,R=" "){return(0,n.rd)(E,P,R)}}return f.\u0275fac=function(E){return new(E||f)},f.\u0275pipe=r.\u0275\u0275definePipe({name:"codeSplit",type:f,pure:!0}),f})(),i=(()=>{class f{}return f.\u0275fac=function(E){return new(E||f)},f.\u0275mod=r.\u0275\u0275defineNgModule({type:f}),f.\u0275inj=r.\u0275\u0275defineInjector({imports:[a.CommonModule]}),f})();t(57544);var v=t(70658),m=t(78506),C=(t(29306),t(87956)),o=t(72765),c=t(27302);function y(f,O){if(1&f){const E=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",18),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(E);const R=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(R.onDigital())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd()()}if(2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275property("prefixIcon",E.digitalIcon),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate(E.digitalIncognito?"Ver datos":"Ocultar datos")}}function g(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"span"),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate(null==E.product?null:E.product.publicNumber)}}function b(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"span",19),r.\u0275\u0275text(1),r.\u0275\u0275pipe(2,"codeSplit"),r.\u0275\u0275elementEnd()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(2,1,E.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":E.digitalNumber,4)," ")}}function u(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"bocc-badge",20),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275attribute("bocc-theme",null==E.product||null==E.product.status?null:E.product.status.color),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",null==E.product||null==E.product.status?null:E.product.status.label," ")}}function p(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"div",21),r.\u0275\u0275element(1,"bocc-progress-bar",22),r.\u0275\u0275elementEnd()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("theme",E.progressBarTheme)("width",E.progressBarStatus)}}function l(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"div",23)(1,"label",24),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),r.\u0275\u0275element(5,"bocc-amount",26),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"mbo-button-incognito-mode",27),r.\u0275\u0275elementEnd()()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" ",null==E.product?null:E.product.label," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!E.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("amount",null==E.product?null:E.product.amount)("incognito",E.incognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("actionMode",!0)("hidden",!E.product)}}function x(f,O){if(1&f&&(r.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),r.\u0275\u0275text(3,"Vence"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"span",19),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",29)(7,"label",20),r.\u0275\u0275text(8,"CVC"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(9,"span",19),r.\u0275\u0275text(10),r.\u0275\u0275elementEnd()()()),2&f){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",E.digitalIncognito?"\u2022\u2022 | \u2022\u2022":E.digitalExpAt," "),r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",E.digitalIncognito?"\u2022\u2022\u2022":E.digitalCVC," ")}}let h=(()=>{class f{constructor(E,P){this.managerPreferences=E,this.digitalService=P,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new r.EventEmitter,this.digital=new r.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:E})=>{this.incognito=E})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:E,value:P})=>{this.product&&this.product.id===E&&this.refreshDigitalState(P)}))}ngOnChanges(E){const{product:P}=E;if(P&&P.currentValue){const R=P.currentValue;this.refreshDigitalState(this.digitalService.request(R.id)),this.activateDigitalCountdown(R)}}ngOnDestroy(){this.unsubscriptions.forEach(E=>E())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(E){const{incognito:P,requiredRequest:R,cvc:K,expirationAt:B,number:A}=E;this.digitalIncognito=P,this.digitalExpAt=B,this.digitalCVC=K,this.digitalNumber=A,this.digitalIcon=P?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=R}activateDigitalCountdown(E){const{countdown$:P}=this.digitalService.request(E.id);P?(this.progressBarRequired=!0,this.progressBarPercent=100,P.subscribe(R=>{this.progressBarRequired=!(R>=v.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-R/v.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return f.\u0275fac=function(E){return new(E||f)(r.\u0275\u0275directiveInject(m.Bx),r.\u0275\u0275directiveInject(C.ZP))},f.\u0275cmp=r.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[r.\u0275\u0275NgOnChangesFeature,r.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(E,P){1&E&&(r.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),r.\u0275\u0275listener("click",function(){return P.onClose()}),r.\u0275\u0275elementStart(3,"span"),r.\u0275\u0275text(4,"Atr\xe1s"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(5,"mbo-currency-toggle",3),r.\u0275\u0275template(6,y,3,2,"button",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"div",5)(8,"div",6),r.\u0275\u0275element(9,"img",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),r.\u0275\u0275text(12),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),r.\u0275\u0275template(15,g,2,1,"span",12),r.\u0275\u0275template(16,b,3,4,"span",13),r.\u0275\u0275template(17,u,2,2,"bocc-badge",14),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(18,p,2,2,"div",15),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(19,l,7,6,"div",16),r.\u0275\u0275template(20,x,11,2,"div",17),r.\u0275\u0275elementEnd()),2&E&&(r.\u0275\u0275classMap(null==P.product?null:P.product.bank.className),r.\u0275\u0275property("color",null==P.product?null:P.product.color),r.\u0275\u0275advance(5),r.\u0275\u0275property("formControl",P.currencyControl)("currencies",P.currencies)("hidden",!(null!=P.product&&P.product.bank.isOccidente)||P.currencies.length<2),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==P.product?null:P.product.isDigital),r.\u0275\u0275advance(3),r.\u0275\u0275property("src",null==P.product?null:P.product.logo,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!P.product),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",(null==P.product?null:P.product.nickname)||(null==P.product?null:P.product.name)," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!P.product),r.\u0275\u0275advance(1),r.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!P.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=P.product&&P.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==P.product?null:P.product.isDigital),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",(null==P.product||null==P.product.status?null:P.product.status.label)&&P.digitalIncognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",P.progressBarRequired),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=P.product&&P.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==P.product?null:P.product.isDigital))},dependencies:[a.CommonModule,a.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,i,s,o.uf,c.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),f})()},94614:(D,M,t)=>{t.d(M,{K:()=>m});var a=t(17007),e=t(30263),n=t(39904),i=(t(29306),t(95437)),d=t(99877);let m=(()=>{class _{constructor(o){this.mboProvider=o,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:o,id:c,parentProduct:y}=this.product;"covered"===o&&y?this.goToPage(y.id,c):this.goToPage(c)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(o,c){this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:o,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:c})}}return _.\u0275fac=function(o){return new(o||_)(d.\u0275\u0275directiveInject(i.ZL))},_.\u0275cmp=d.\u0275\u0275defineComponent({type:_,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(o,c){1&o&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275listener("click",function(){return c.onComponent()}),d.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"div",4),d.\u0275\u0275element(7,"bocc-amount",5),d.\u0275\u0275elementEnd()()),2&o&&(d.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",c.skeleton),d.\u0275\u0275advance(2),d.\u0275\u0275property("active",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.dateFormat," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("active",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.description," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("hidden",c.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275property("amount",null==c.movement?null:c.movement.value)("currencyCode",null==c.movement?null:c.movement.currencyCode)("theme",!0))},dependencies:[a.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),_})()},70957:(D,M,t)=>{t.d(M,{K:()=>p});var a=t(15861),r=t(17007),n=t(99877),i=t(30263),d=t(78506),v=t(39904),_=(t(29306),t(87903)),C=t(95437),o=t(27302),c=t(94614);function y(l,x){if(1&l){const h=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",8),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(h);const O=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(O.onRedirectAll())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Ver todos"),n.\u0275\u0275elementEnd()()}}function g(l,x){if(1&l&&(n.\u0275\u0275elementStart(0,"div",5)(1,"label",6),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(3,y,3,0,"button",7),n.\u0275\u0275elementEnd()),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",(null==h.productMovements||null==h.productMovements.range?null:h.productMovements.range.label)||"Sin resultados"," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==h.productMovements?null:h.productMovements.range)}}function b(l,x){if(1&l&&n.\u0275\u0275element(0,"mbo-product-info-movement",9),2&l){const h=x.$implicit,f=n.\u0275\u0275nextContext();n.\u0275\u0275property("movement",h)("product",f.product)}}function u(l,x){if(1&l&&(n.\u0275\u0275elementStart(0,"mbo-message-empty",10),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",h.msgError," ")}}let p=(()=>{class l{constructor(h,f){this.mboProvider=h,this.managerProductMovements=f,this.header=!0,this.requesting=!1}ngOnChanges(h){const{currencyCode:f,product:O}=h;if(!this.movements&&(O||f)){const E=f?.currentValue||this.currencyCode,P=O?.currentValue||this.product;this.currentMovements=void 0,(0,_.A2)(P)&&this.requestFirstPage(P,E)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:h,id:f,parentProduct:O}=this.product;this.mboProvider.navigation.next(v.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===h?{productId:O?.id,coveredCardId:f}:{productId:f},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(h,f){var O=this;return(0,a.Z)(function*(){O.requesting=!0,(yield O.managerProductMovements.requestForProduct({product:h,currencyCode:f})).when({success:E=>{O.currentMovements=E},failure:()=>{O.currentMovements=void 0}},()=>{O.requesting=!1})})()}}return l.\u0275fac=function(h){return new(h||l)(n.\u0275\u0275directiveInject(C.ZL),n.\u0275\u0275directiveInject(d.sy))},l.\u0275cmp=n.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[n.\u0275\u0275NgOnChangesFeature,n.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(h,f){1&h&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,g,4,2,"div",1),n.\u0275\u0275elementStart(2,"div",2),n.\u0275\u0275template(3,b,1,2,"mbo-product-info-movement",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(4,u,2,1,"mbo-message-empty",4),n.\u0275\u0275elementEnd()),2&h&&(n.\u0275\u0275property("hidden",f.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",f.header),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",null==f.productMovements?null:f.productMovements.firstPage),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==f.productMovements?null:f.productMovements.isEmpty))},dependencies:[r.CommonModule,r.NgForOf,r.NgIf,i.P8,c.K,o.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),l})()},91248:(D,M,t)=>{t.d(M,{I:()=>_});var a=t(17007),e=t(30263),n=t(99877);function i(C,o){if(1&C&&n.\u0275\u0275element(0,"bocc-amount",10),2&C){const c=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("amount",c.value)("currencyCode",c.currencyCode)}}function d(C,o){if(1&C&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&C){const c=n.\u0275\u0275nextContext().$implicit,y=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",y.incognito||y.section.incognito?c.mask:c.value," ")}}function v(C,o){if(1&C){const c=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",11),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(c);const g=n.\u0275\u0275nextContext().$implicit;return n.\u0275\u0275resetView(g.action.click())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()}if(2&C){const c=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("suffixIcon",c.action.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(c.action.label)}}function m(C,o){if(1&C&&(n.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"label",6),n.\u0275\u0275template(5,i,1,2,"bocc-amount",7),n.\u0275\u0275template(6,d,2,1,"span",8),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(7,v,3,2,"button",9),n.\u0275\u0275elementEnd()),2&C){const c=o.$implicit,y=n.\u0275\u0275nextContext();n.\u0275\u0275property("hidden",(null==c?null:c.currencyCode)!==y.currencyCode),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",c.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",c.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!c.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",c.action&&!(y.incognito||y.section.incognito))}}let _=(()=>{class C{constructor(){this.currencyCode="COP"}}return C.\u0275fac=function(c){return new(c||C)},C.\u0275cmp=n.\u0275\u0275defineComponent({type:C,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(c,y){1&c&&(n.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),n.\u0275\u0275template(2,m,8,5,"li",2),n.\u0275\u0275elementEnd()()),2&c&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",y.section.datas))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),C})()},4663:(D,M,t)=>{t.d(M,{c:()=>o});var a=t(17007),e=t(99877),s=t(30263),i=t(27302);function d(c,y){1&c&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function v(c,y){if(1&c&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&c){const g=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.title," ")}}function m(c,y){if(1&c){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const p=e.\u0275\u0275restoreView(g).$implicit,l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onProduct(p))}),e.\u0275\u0275elementEnd()}if(2&c){const g=y.$implicit,b=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",g.color)("icon",g.logo)("title",g.nickname)("number",g.publicNumber)("ghost",b.ghost)("amount",g.amount)("tagAval",g.tagAvalFormat),e.\u0275\u0275attribute("amount-status",b.amountColorProduct(g))}}function _(c,y){1&c&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const C=["*"];let o=(()=>{class c{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(g){return g.amount>0?"success":g.amount<0?"danger":"empty"}onProduct(g){this.select.emit(g)}}return c.\u0275fac=function(g){return new(g||c)},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:C,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(g,b){1&g&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,d,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,v,2,1,"div",5),e.\u0275\u0275template(8,m,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,_,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",b.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",b.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!b.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!b.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.w_,s.P8,i.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),c})()},13961:(D,M,t)=>{t.d(M,{Z:()=>d});var a=t(17007),e=t(27302),n=t(10119),s=t(99877);let d=(()=>{class v{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(_){this.portal=_}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=s.\u0275\u0275defineComponent({type:v,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(_,C){1&_&&(s.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),s.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"p"),s.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),s.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"p"),s.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),s.\u0275\u0275elementEnd()()()),2&_&&s.\u0275\u0275property("headerActionRight",C.headerAction)("gradient",!0)},dependencies:[a.CommonModule,e.Nu,n.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),v})()},66709:(D,M,t)=>{t.d(M,{s:()=>d});var a=t(17007),r=t(99877),e=t(30263),n=t(87542);let s=(()=>{class v{ngBoccPortal(_){}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=r.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(_,C){1&_&&(r.\u0275\u0275elementStart(0,"div",0)(1,"label",1),r.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),r.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),r.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(11,"p",4),r.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),r.\u0275\u0275element(13,"br"),r.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),r.\u0275\u0275element(15,"br"),r.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),r.\u0275\u0275elementStart(17,"span"),r.\u0275\u0275text(18,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(19," Configuraci\xf3n "),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(22," Seguridad "),r.\u0275\u0275elementStart(23,"span"),r.\u0275\u0275text(24,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(25," Activar Token Mobile."),r.\u0275\u0275element(26,"br"),r.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),r.\u0275\u0275elementEnd()()()())},dependencies:[a.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),v})();const i=["*"];let d=(()=>{class v{constructor(_,C){this.ref=_,this.bottomSheetService=C,this.verifying=!1,this.tokenLength=n.Xi,this.code=new r.EventEmitter,this.tokenControls=new n.b2}ngOnInit(){const _=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(s),setTimeout(()=>{_?.focus()},120)}onAutocomplete(_){this.code.emit(_)}onInfo(){this.infoSheet?.open()}}return v.\u0275fac=function(_){return new(_||v)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(e.fG))},v.\u0275cmp=r.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(_,C){1&_&&(r.\u0275\u0275projectionDef(),r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275projection(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"p",2),r.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",2),r.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),r.\u0275\u0275elementStart(7,"a"),r.\u0275\u0275text(8),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(9,". "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",3),r.\u0275\u0275element(11,"img",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),r.\u0275\u0275listener("autocomplete",function(c){return C.onAutocomplete(c)}),r.\u0275\u0275text(13," Ingresa tu clave "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"button",6),r.\u0275\u0275listener("click",function(){return C.onInfo()}),r.\u0275\u0275elementStart(15,"span"),r.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()()()),2&_&&(r.\u0275\u0275advance(8),r.\u0275\u0275textInterpolate1("",C.tokenLength," d\xedgitos"),r.\u0275\u0275advance(4),r.\u0275\u0275property("disabled",C.verifying)("formControls",C.tokenControls),r.\u0275\u0275advance(2),r.\u0275\u0275property("disabled",C.verifying))},dependencies:[a.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),v})()},22816:(D,M,t)=>{t.d(M,{S:()=>a});class a{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);