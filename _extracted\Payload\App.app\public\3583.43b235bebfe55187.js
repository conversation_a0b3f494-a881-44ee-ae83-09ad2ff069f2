(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3583],{36319:(O,w,i)=>{i.d(w,{g:()=>l});var m=i(72972);const l=()=>{if(void 0!==m.w)return m.w.Capacitor}},1765:(O,w,i)=>{i.d(w,{I:()=>l,a:()=>_,b:()=>v,c:()=>E,d:()=>T,h:()=>I});var m=i(36319),l=(()=>{return(n=l||(l={})).Heavy="HEAVY",n.Medium="MEDIUM",n.Light="LIGHT",l;var n})();const u={getEngine(){const n=window.TapticEngine;if(n)return n;const a=(0,m.g)();return a?.isPluginAvailable("Haptics")?a.Plugins.Haptics:void 0},available(){return!!this.getEngine()&&("web"!==(0,m.g)()?.getPlatform()||typeof navigator<"u"&&void 0!==navigator.vibrate)},isCordova:()=>void 0!==window.TapticEngine,isCapacitor:()=>void 0!==(0,m.g)(),impact(n){const a=this.getEngine();if(!a)return;const x=this.isCapacitor()?n.style:n.style.toLowerCase();a.impact({style:x})},notification(n){const a=this.getEngine();if(!a)return;const x=this.isCapacitor()?n.type:n.type.toLowerCase();a.notification({type:x})},selection(){const n=this.isCapacitor()?l.Light:"light";this.impact({style:n})},selectionStart(){const n=this.getEngine();n&&(this.isCapacitor()?n.selectionStart():n.gestureSelectionStart())},selectionChanged(){const n=this.getEngine();n&&(this.isCapacitor()?n.selectionChanged():n.gestureSelectionChanged())},selectionEnd(){const n=this.getEngine();n&&(this.isCapacitor()?n.selectionEnd():n.gestureSelectionEnd())}},h=()=>u.available(),E=()=>{h()&&u.selection()},_=()=>{h()&&u.selectionStart()},v=()=>{h()&&u.selectionChanged()},I=()=>{h()&&u.selectionEnd()},T=n=>{h()&&u.impact(n)}},37003:(O,w,i)=>{i.d(w,{I:()=>E,a:()=>T,b:()=>h,c:()=>x,d:()=>L,f:()=>n,g:()=>I,i:()=>v,p:()=>k,r:()=>B,s:()=>a});var m=i(15861),l=i(78635),M=i(28909);const h="ion-content",E=".ion-content-scroll-host",_=`${h}, ${E}`,v=s=>"ION-CONTENT"===s.tagName,I=function(){var s=(0,m.Z)(function*(r){return v(r)?(yield new Promise(f=>(0,l.c)(r,f)),r.getScrollElement()):r});return function(f){return s.apply(this,arguments)}}(),T=s=>s.querySelector(E)||s.querySelector(_),n=s=>s.closest(_),a=(s,r)=>v(s)?s.scrollToTop(r):Promise.resolve(s.scrollTo({top:0,left:0,behavior:r>0?"smooth":"auto"})),x=(s,r,f,C)=>v(s)?s.scrollByPoint(r,f,C):Promise.resolve(s.scrollBy({top:f,left:r,behavior:C>0?"smooth":"auto"})),k=s=>(0,M.b)(s,h),L=s=>{if(v(s)){const f=s.scrollY;return s.scrollY=!1,f}return s.style.setProperty("overflow","hidden"),!0},B=(s,r)=>{v(s)?s.scrollY=r:s.style.removeProperty("overflow")}},44896:(O,w,i)=>{i.d(w,{a:()=>m,b:()=>x,c:()=>_,d:()=>k,e:()=>D,f:()=>E,g:()=>L,h:()=>M,i:()=>l,j:()=>C,k:()=>S,l:()=>v,m:()=>n,n:()=>B,o:()=>T,p:()=>h,q:()=>u,r:()=>f,s:()=>P,t:()=>a,u:()=>s,v:()=>r,w:()=>I});const m="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-miterlimit='10' stroke-width='48' d='M244 400L100 256l144-144M120 256h292' class='ionicon-fill-none'/></svg>",l="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 268l144 144 144-144M256 392V100' class='ionicon-fill-none'/></svg>",M="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M368 64L144 256l224 192V64z'/></svg>",u="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 144l192 224 192-224H64z'/></svg>",h="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 368L256 144 64 368h384z'/></svg>",E="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M416 128L192 384l-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>",_="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>",v="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",I="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M136 208l120-104 120 104M136 304l120 104 120-104' stroke-width='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none'/></svg>",T="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",n="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",a="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z'/></svg>",x="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",k="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>",L="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='192' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>",B="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='48'/><circle cx='416' cy='256' r='48'/><circle cx='96' cy='256' r='48'/></svg>",s="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>",r="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 384h384v-42.67H64zm0-106.67h384v-42.66H64zM64 128v42.67h384V128z'/></svg>",f="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>",C="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M96 256h320M96 176h320M96 336h320' class='ionicon-fill-none ionicon-stroke-width'/></svg>",S="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='44' d='M118 304h276M118 208h276' class='ionicon-fill-none'/></svg>",P="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>",D="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 428L339.92 303.9a160.48 160.48 0 0030.72-94.58C370.64 120.37 298.27 48 209.32 48S48 120.37 48 209.32s72.37 161.32 161.32 161.32a160.48 160.48 0 0094.58-30.72L428 464zM209.32 319.69a110.38 110.38 0 11110.37-110.37 110.5 110.5 0 01-110.37 110.37z'/></svg>"},93583:(O,w,i)=>{i.r(w),i.d(w,{ion_reorder:()=>k,ion_reorder_group:()=>s});var m=i(15861),l=i(42477),M=i(44896),u=i(37943),h=i(37003),E=i(78635),_=i(1765);i(36319),i(72972);const k=class{constructor(t){(0,l.r)(this,t)}onClick(t){const e=this.el.closest("ion-reorder-group");t.preventDefault(),(!e||!e.disabled)&&t.stopImmediatePropagation()}render(){const t=(0,u.b)(this);return(0,l.h)(l.H,{key:"5747b63aa64b05bfed96bbce8087186c7e14c6d5",class:t},(0,l.h)("slot",{key:"a745f29a23c905cd0bff572acb755b597a3fb3c3"},(0,l.h)("ion-icon",{key:"48f433e85a3a68c16bc426623b2b74957b4b2eb7",icon:"ios"===t?M.j:M.k,lazy:!1,class:"reorder-icon",part:"icon","aria-hidden":"true"})))}get el(){return(0,l.f)(this)}};k.style={ios:":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:2.125rem;opacity:0.4}",md:":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:1.9375rem;opacity:0.3}"};const s=class{constructor(t){(0,l.r)(this,t),this.ionItemReorder=(0,l.d)(this,"ionItemReorder",7),this.lastToIndex=-1,this.cachedHeights=[],this.scrollElTop=0,this.scrollElBottom=0,this.scrollElInitial=0,this.containerTop=0,this.containerBottom=0,this.state=0,this.disabled=!0}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}connectedCallback(){var t=this;return(0,m.Z)(function*(){const e=(0,h.f)(t.el);e&&(t.scrollEl=yield(0,h.g)(e)),t.gesture=(yield Promise.resolve().then(i.bind(i,35067))).createGesture({el:t.el,gestureName:"reorder",gesturePriority:110,threshold:0,direction:"y",passive:!1,canStart:o=>t.canStart(o),onStart:o=>t.onStart(o),onMove:o=>t.onMove(o),onEnd:()=>t.onEnd()}),t.disabledChanged()})()}disconnectedCallback(){this.onEnd(),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}complete(t){return Promise.resolve(this.completeReorder(t))}canStart(t){if(this.selectedItemEl||0!==this.state)return!1;const o=t.event.target.closest("ion-reorder");if(!o)return!1;const d=f(o,this.el);return!!d&&(t.data=d,!0)}onStart(t){t.event.preventDefault();const e=this.selectedItemEl=t.data,o=this.cachedHeights;o.length=0;const d=this.el,c=d.children;if(!c||0===c.length)return;let p=0;for(let b=0;b<c.length;b++){const y=c[b];p+=y.offsetHeight,o.push(p),y.$ionIndex=b}const g=d.getBoundingClientRect();if(this.containerTop=g.top,this.containerBottom=g.bottom,this.scrollEl){const b=this.scrollEl.getBoundingClientRect();this.scrollElInitial=this.scrollEl.scrollTop,this.scrollElTop=b.top+C,this.scrollElBottom=b.bottom-C}else this.scrollElInitial=0,this.scrollElTop=0,this.scrollElBottom=0;this.lastToIndex=r(e),this.selectedItemHeight=e.offsetHeight,this.state=1,e.classList.add(P),(0,_.a)()}onMove(t){const e=this.selectedItemEl;if(!e)return;const o=this.autoscroll(t.currentY),d=this.containerTop-o,p=Math.max(d,Math.min(t.currentY,this.containerBottom-o)),g=o+p-t.startY,y=this.itemIndexForTop(p-d);if(y!==this.lastToIndex){const R=r(e);this.lastToIndex=y,(0,_.b)(),this.reorderMove(R,y)}e.style.transform=`translateY(${g}px)`}onEnd(){const t=this.selectedItemEl;if(this.state=2,!t)return void(this.state=0);const e=this.lastToIndex,o=r(t);e===o?this.completeReorder():this.ionItemReorder.emit({from:o,to:e,complete:this.completeReorder.bind(this)}),(0,_.h)()}completeReorder(t){const e=this.selectedItemEl;if(e&&2===this.state){const o=this.el.children,d=o.length,c=this.lastToIndex,p=r(e);(0,E.r)(()=>{c===p||void 0!==t&&!0!==t||this.el.insertBefore(e,p<c?o[c+1]:o[c]);for(let g=0;g<d;g++)o[g].style.transform=""}),Array.isArray(t)&&(t=D(t,p,c)),e.style.transition="",e.classList.remove(P),this.selectedItemEl=void 0,this.state=0}return t}itemIndexForTop(t){const e=this.cachedHeights;for(let o=0;o<e.length;o++)if(e[o]>t)return o;return e.length-1}reorderMove(t,e){const o=this.selectedItemHeight,d=this.el.children;for(let c=0;c<d.length;c++){let g="";c>t&&c<=e?g=`translateY(${-o}px)`:c<t&&c>=e&&(g=`translateY(${o}px)`),d[c].style.transform=g}}autoscroll(t){if(!this.scrollEl)return 0;let e=0;return t<this.scrollElTop?e=-S:t>this.scrollElBottom&&(e=S),0!==e&&this.scrollEl.scrollBy(0,e),this.scrollEl.scrollTop-this.scrollElInitial}render(){const t=(0,u.b)(this);return(0,l.h)(l.H,{key:"68b5e5fa00a0531c74597964d84c82da8bc3252f",class:{[t]:!0,"reorder-enabled":!this.disabled,"reorder-list-active":0!==this.state}})}get el(){return(0,l.f)(this)}static get watchers(){return{disabled:["disabledChanged"]}}},r=t=>t.$ionIndex,f=(t,e)=>{let o;for(;t;){if(o=t.parentElement,o===e)return t;t=o}},C=60,S=10,P="reorder-selected",D=(t,e,o)=>{const d=t[e];return t.splice(e,1),t.splice(o,0,d),t.slice()};s.style=".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}"}}]);