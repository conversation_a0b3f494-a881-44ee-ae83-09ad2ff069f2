(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8722],{5917:(A,E,t)=>{t.d(E,{n:()=>l,Z:()=>g});var a=t(15861),r=t(29306),e=t(87956),o=t(53113),s=t(98699),i=t(83328),p=t(12263),v=t(85911),m=t(65518),_=t(81536),f=t(64892),n=t(81781),c=t(99877);let y=(()=>{class b{constructor(d,C,h,x,D,I){this.deviceService=d,this.digipassService=C,this.cryptoService=h,this.publicKeyRepository=x,this.enrollmentStore=D,this.enrollmentService=I,this.enrollmentKey="",D.subscribe(({enrollmentKey:T})=>{this.enrollmentKey=T||""})}document(d){var C=this;return(0,a.Z)(function*(){const[h,x]=yield Promise.all([C.deviceService.getInfo(),C.deviceService.getFingerprint()]);C.enrollmentService.reset();const D=yield C.service({companyId:f.qE.Occidente,deviceName:h.name||h.model,deviceOS:h.operatingSystem,deviceOperatingSystem:h.operatingSystem,deviceSerial:x,deviceUuid:h.uuid,id:d.number,idType:d.type.code,login:null,serial:x});return C.enrollmentStore.setDocument(d.type,d.number,!1),D})()}otp(d){var C=this;return(0,a.Z)(function*(){const h=yield C.publicKeyRepository.requestEnrollment();return C.service({otpValue:C.cryptoService.encodeRSA(h,d),forceOtpGeneration:!1,isOtpGeneratedByOtherChannel:!1})})()}digipass(){var d=this;return(0,a.Z)(function*(){const C=yield d.activateLicense();if(!C.success)throw Error(C.errorMessage||C.additionalErrorMessage);return d.activateInstance()})()}product(d){var C=this;return(0,a.Z)(function*(){const h=yield C.publicKeyRepository.requestEnrollment();return C.service({secureDataSecret:C.cryptoService.encodeRSA(h,d)})})()}password(d){var C=this;return(0,a.Z)(function*(){const h=yield C.publicKeyRepository.requestEnrollment();return C.service({universalPassword:C.cryptoService.encodeRSA(h,d),forgotPassword:!1})})()}device(d,C){var h=this;return(0,a.Z)(function*(){const[x,D]=yield Promise.all([h.deviceService.getInfo(),h.deviceService.getFingerprint()]);return h.service({deviceAppVersion:x.appVersion,deviceAppBuild:x.appCompilation,deviceManufacturer:x.manufacturer,deviceModel:x.model,deviceName:C,deviceOsVersion:x.osVersion,deviceOperatingSystem:x.operatingSystem,devicePlatform:x.platform,deviceSerial:D,deviceUuid:x.uuid,id:d.number,idType:d.type.code,isVirtual:!1,serial:D})})()}activateLicense(){var d=this;return(0,a.Z)(function*(){const C=yield d.digipassService.activeLicense(d.enrollmentKey);return d.service({deviceCode:C})})()}activateInstance(){var d=this;return(0,a.Z)(function*(){const C=yield d.digipassService.activeInstance(d.enrollmentKey);return d.service({signatureCode:C})})()}service(d){var C=this;return(0,a.Z)(function*(){const h=yield C.enrollmentService.execute(d);return C.enrollmentStore.setEnrollmentKey(h.enrollmentKey),h})()}}return b.\u0275fac=function(d){return new(d||b)(c.\u0275\u0275inject(e.U8),c.\u0275\u0275inject(e._L),c.\u0275\u0275inject(e.$I),c.\u0275\u0275inject(_.aH),c.\u0275\u0275inject(m.c),c.\u0275\u0275inject(n.s))},b.\u0275prov=c.\u0275\u0275defineInjectable({token:b,factory:b.\u0275fac,providedIn:"root"}),b})(),g=(()=>{class b{constructor(d,C){this.enrollmentRepository=d,this.enrollmentStore=C}document(d){var C=this;return(0,a.Z)(function*(){try{const h=d||C.getCustomerDocument();if(!h)return v.d.finish();const x=yield C.enrollmentRepository.document(h);return x.value===i.l.FILL_UNIVERSAL_PASSWORD?v.d.finish("Ya te encuentras registrado en Banca M\xf3vil"):(0,p.F)(x)}catch({message:h}){return v.d.error(h)}})()}otp(d){var C=this;return(0,a.Z)(function*(){try{let h=yield C.enrollmentRepository.otp(d);return h.value===i.l.ONESPAN_ACTIVATE_LICENSE&&(h=yield C.enrollmentRepository.digipass()),(0,p.F)(h)}catch({message:h}){return v.d.error(h)}})()}product(d){var C=this;return(0,a.Z)(function*(){try{const h=yield C.enrollmentRepository.product(d),{success:x,value:D}=h;return x&&D===i.l.FILL_SECURE_DATA?v.d.finish():(0,p.F)(h)}catch({message:h}){return v.d.error(h)}})()}password(d){var C=this;return(0,a.Z)(function*(){try{const h=yield C.enrollmentRepository.password(d);return C.enrollmentStore.setPassword(d),(0,p.F)(h)}catch({message:h}){return v.d.error(h)}})()}device(d){var C=this;return(0,a.Z)(function*(){try{const{documentNumber:h,documentType:x}=C.enrollmentStore.currentState,D=new o.dp(x,h),I=yield C.enrollmentRepository.device(D,d);return(0,p.F)(I)}catch({message:h}){return v.d.error(h)}})()}getCustomerDocument(){const{documentNumber:d,documentType:C}=this.enrollmentStore.getDocument();if(C&&d)return new o.dp(C,d)}}return b.\u0275fac=function(d){return new(d||b)(c.\u0275\u0275inject(y),c.\u0275\u0275inject(m.c))},b.\u0275prov=c.\u0275\u0275defineInjectable({token:b,factory:b.\u0275fac,providedIn:"root"}),b})(),l=(()=>{class b{constructor(d,C){this.biometricService=d,this.store=C}execute(){var d=this;return(0,a.Z)(function*(){try{const{documentNumber:C,documentType:h,password:x}=d.store.currentState;return yield d.biometricService.save(new r.J1(h,C,x),!0),s.Either.success(!0)}catch({message:C}){return s.Either.failure({message:C})}})()}}return b.\u0275fac=function(d){return new(d||b)(c.\u0275\u0275inject(e.oy),c.\u0275\u0275inject(m.c))},b.\u0275prov=c.\u0275\u0275defineInjectable({token:b,factory:b.\u0275fac,providedIn:"root"}),b})()},19209:(A,E,t)=>{t.d(E,{s:()=>p});var a=t(30263),r=t(39904),e=t(95437),o=t(65518),s=t(99877);let p=(()=>{class v{constructor(_,f,n){this.modalConfirmationService=_,this.mboProvider=f,this.enrollmentStore=n}execute(){this.modalConfirmationService.execute({title:"Abandonar registro",message:"\xbfEstas seguro que no deseas continuar con el registro en la Banca M\xf3vil?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed()}},decline:{label:"Continuar"}})}cancelConfirmed(){this.enrollmentStore.reset(),this.mboProvider.navigation.back(r.Z6.AUTHENTICATION.LOGIN)}}return v.\u0275fac=function(_){return new(_||v)(s.\u0275\u0275inject(a.$e),s.\u0275\u0275inject(e.ZL),s.\u0275\u0275inject(o.c))},v.\u0275prov=s.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},18722:(A,E,t)=>{t.r(E),t.d(E,{MboEnrollmentDevicePageModule:()=>T});var a=t(17007),r=t(78007),e=t(30263),o=t(33395),s=t(79798),i=t(15861),p=t(24495),v=t(39904),m=t(31711),_=t(95437),f=t(87956),n=t(57544),c=t(65518),y=t(5917),g=t(19209),l=t(99877),b=t(25317),u=t(45542),d=t(64181),C=t(48774),h=t(52528),x=t(19102);const D=v.Z6.AUTHENTICATION.ENROLLMENT;let I=(()=>{class R{constructor(O,z,U,k,V,H){this.mboProvider=O,this.cancelProvider=z,this.deviceService=U,this.biometricService=k,this.verifyEnrollment=V,this.enrollmentStore=H,this.requesting=!1,this.deviceControl=new n.FormControl("",[p.C1,p.zf,p.O_,(0,p.Je)(5),(0,p.Mv)(50)]),this.cancelAction={id:"btn_enrollment-device_cancel",label:"Cancelar",disabled:()=>this.requesting,click:()=>{this.cancelProvider.execute()}}}ngOnInit(){this.deviceService.getInfo().then(({model:O})=>{this.deviceControl.setValue(O)})}get invalid(){return this.deviceControl.invalid||this.requesting}onSubmit(){var O=this;return(0,i.Z)(function*(){O.requesting=!0,(yield O.verifyEnrollment.device(O.deviceControl.value.trim())).when({finish:()=>{O.enrollmentFinished()},next:z=>{O.mboProvider.navigation.next(z)},back:z=>{O.mboProvider.navigation.back(z)},error:z=>{O.mboProvider.toast.error(z)},warning:z=>{O.mboProvider.toast.warning(z)}},()=>{O.requesting=!1})})()}enrollmentFinished(){var O=this;return(0,i.Z)(function*(){const z=yield O.biometricService.getMode(),{password:U}=O.enrollmentStore.currentState;O.mboProvider.navigation.next(z!==m._.None&&U?D.BIOMETRIC:D.SUCCESS_SIGNUP)})()}}return R.\u0275fac=function(O){return new(O||R)(l.\u0275\u0275directiveInject(_.ZL),l.\u0275\u0275directiveInject(g.s),l.\u0275\u0275directiveInject(f.U8),l.\u0275\u0275directiveInject(f.x1),l.\u0275\u0275directiveInject(y.Z),l.\u0275\u0275directiveInject(c.c))},R.\u0275cmp=l.\u0275\u0275defineComponent({type:R,selectors:[["mbo-enrollment-device-page"]],decls:15,vars:6,consts:[[1,"mbo-enrollment-device-page",3,"header"],["body","",1,"mbo-enrollment-device-page__content"],["header","",3,"rightAction"],["body","",1,"mbo-enrollment-device-page__body"],["body","",1,"mbo-enrollment-device-page__info"],[1,"mbo-enrollment-device-page__title","subtitle2-medium"],[1,"mbo-enrollment-device-page__message","body2-medium"],["elementId","txt_enrollment-device_name","placeholder","Android/Iphone personal","label","Nombre del equipo",3,"disabled","formControl"],["footer","",1,"mbo-enrollment-device-page__footer"],["id","btn_enrollment-device_submit","bocc-button","raised","boccUtagComponent","click",3,"spinner","disabled","click"]],template:function(O,z){1&O&&(l.\u0275\u0275elementStart(0,"bocc-template-form",0)(1,"div",1),l.\u0275\u0275element(2,"bocc-header-form",2),l.\u0275\u0275elementStart(3,"div",3),l.\u0275\u0275element(4,"mbo-bank-logo"),l.\u0275\u0275elementStart(5,"div",4)(6,"div",5),l.\u0275\u0275text(7," Registro Banca M\xf3vil "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(8,"p",6),l.\u0275\u0275text(9," Ingresa un nombre para identificar este dispositivo "),l.\u0275\u0275elementEnd(),l.\u0275\u0275element(10,"bocc-input-box",7),l.\u0275\u0275elementEnd()()(),l.\u0275\u0275elementStart(11,"div",8)(12,"button",9),l.\u0275\u0275listener("click",function(){return z.onSubmit()}),l.\u0275\u0275elementStart(13,"span"),l.\u0275\u0275text(14,"Continuar"),l.\u0275\u0275elementEnd()()()()),2&O&&(l.\u0275\u0275property("header",!1),l.\u0275\u0275advance(2),l.\u0275\u0275property("rightAction",z.cancelAction),l.\u0275\u0275advance(8),l.\u0275\u0275property("disabled",z.requesting)("formControl",z.deviceControl),l.\u0275\u0275advance(2),l.\u0275\u0275property("spinner",z.requesting)("disabled",z.invalid))},dependencies:[b.I,u.P,d.D,C.J,h.A,x.r],styles:["mbo-enrollment-device-page{--mbo-bank-logo-height: 26rem;--pvt-body-rowgap: var(--sizing-x16);--pvt-info-rowgap: var(--sizing-x8)}mbo-enrollment-device-page .mbo-enrollment-device-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-body-rowgap);padding-top:var(--sizing-safe-top)}mbo-enrollment-device-page .mbo-enrollment-device-page__body{position:relative;display:flex;flex-direction:column;width:100%;row-gap:var(--pvt-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-enrollment-device-page .mbo-enrollment-device-page__info{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-info-rowgap)}mbo-enrollment-device-page .mbo-enrollment-device-page__title{position:relative;width:100%;text-align:center}mbo-enrollment-device-page .mbo-enrollment-device-page__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-enrollment-device-page .mbo-enrollment-device-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-enrollment-device-page .mbo-enrollment-device-page__footer button{width:100%}@media screen and (max-height: 800px){mbo-enrollment-device-page{--mbo-bank-logo-height: var(--sizing-x24)}}@media screen and (max-height: 700px){mbo-enrollment-device-page{--mbo-bank-logo-height: var(--sizing-x22)}}@media screen and (max-height: 600px){mbo-enrollment-device-page{--mbo-bank-logo-height: var(--sizing-x20);--pvt-body-rowgap: var(--sizing-x12)}}\n"],encapsulation:2}),R})(),T=(()=>{class R{}return R.\u0275fac=function(O){return new(O||R)},R.\u0275mod=l.\u0275\u0275defineNgModule({type:R}),R.\u0275inj=l.\u0275\u0275defineInjector({imports:[a.CommonModule,r.RouterModule.forChild([{path:"",component:I}]),o.kW,e.P8,e.DT,e.Jx,e.Av,s.rw]}),R})()},83328:(A,E,t)=>{t.d(E,{U:()=>r,l:()=>a});var a=(()=>{return(e=a||(a={})).CANNOT_REGISTER_DEVICE="CANNOT REGISTER DEVICE",e.CHANNEL_IS_BLOCKED="CHANNEL IS BLOCKED",e.COMPLETED="COMPLETED",e.DEVICE_ALREADY_REGISTERED="DEVICE ALREADY REGISTERED",e.ERROR_SIM_INVALID="ERROR SIM INVALID",e.FILL_CURRENT_CHANNEL_PASSWORD="FILL CURRENT CHANNEL PASSWORD",e.FILL_DEVICE_NAME="FILL DEVICE NAME",e.FILL_NEW_UNIVERSAL_PASSWORD="FILL NEW UNIVERSAL PASSWORD",e.FILL_OTP_DATA="FILL OTP DATA",e.FILL_SECURE_DATA="FILL SECURE DATA",e.FILL_UNIVERSAL_PASSWORD="FILL UNIVERSAL PASSWORD",e.INIT="INIT",e.LOGIN_VALIDATION_ERROR="LOGIN VALIDATION ERROR",e.ONESPAN_ACTIVATE_LICENSE="ENR20",e.ONESPAN_ACTIVATE_INSTANCE="ENR19",e.REGISTER_DEVICE_ERROR="REGISTER DEVICE ERROR",e.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION="RETRIES LIMIT EXCEED ON OTP GENERATION",e.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION="RETRIES LIMIT EXCEED ON SECURE DATA GENERATION",e.SERVICE_ERROR="SERVICE ERROR",e.USER_DOES_NOT_EXISTS="USER DOES NOT EXISTS",e.ENROLLMENT_ERROR_CODE="ENROLLMENT ERROR CODE",a;var e})(),r=(()=>{return(e=r||(r={})).LOGIN_ERROR="1",e.SECURE_DATA="121",e.VALIDATION_PRODUCT="1842",e.VALIDATION_SIM="112",e.PORTABILITY="186",r;var e})()},12263:(A,E,t)=>{t.d(E,{F:()=>_});var a=t(39904),r=t(83328),e=t(85911);const{AUTHENTICATION:{ENROLLMENT:o,ERRORS:s}}=a.Z6,i=[r.l.COMPLETED,r.l.DEVICE_ALREADY_REGISTERED],p=[r.l.LOGIN_VALIDATION_ERROR,r.l.ERROR_SIM_INVALID,r.l.CHANNEL_IS_BLOCKED,r.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION,r.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION,r.l.SERVICE_ERROR,r.l.USER_DOES_NOT_EXISTS,r.l.CANNOT_REGISTER_DEVICE],v=[r.U.PORTABILITY,r.U.SECURE_DATA,r.U.VALIDATION_PRODUCT,r.U.VALIDATION_SIM];function m(f){switch(f){case r.l.LOGIN_VALIDATION_ERROR:return o.WELCOME;case r.l.FILL_OTP_DATA:return o.OTP_VERIFICATION;case r.l.FILL_SECURE_DATA:return o.PRODUCT_VERIFICATION;case r.l.FILL_NEW_UNIVERSAL_PASSWORD:return o.PASSWORD_ASSIGNMENT;case r.l.FILL_DEVICE_NAME:return o.DEVICE_SIGNUP;case r.l.ERROR_SIM_INVALID:return s.SIM_INVALID;case r.l.CHANNEL_IS_BLOCKED:return s.CHANNEL_BLOCKED;case r.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION:return s.EXCEED_ATTEMPTS;case r.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION:return s.DEFAULT_MESSAGE;case r.l.CANNOT_REGISTER_DEVICE:return s.MAX_DEVICES;case r.l.SERVICE_ERROR:return s.SERVICE_FAILURE;default:return s.DEFAULT_MESSAGE}}function _(f){const{error:n,errorCode:c,message:y,success:g,value:l}=f;return g?i.includes(l)?e.d.finish():e.d.next(m(l)):l===r.l.ENROLLMENT_ERROR_CODE&&c&&v.includes(c)?e.d.back(s.DEFAULT_MESSAGE):p.includes(l)?e.d.back(m(l)):n?e.d.error(y):e.d.warning(y)}},85911:(A,E,t)=>{t.d(E,{d:()=>e});var a=t(98699);class e extends a.PartialSealed{static back(s){return new e("back",s)}static error(s){return new e("error",s)}static finish(s=""){return new e("finish",s)}static next(s){return new e("next",s)}static warning(s){return new e("warning",s)}}},81781:(A,E,t)=>{t.d(E,{s:()=>y});var a=t(71776),r=t(39904),e=t(42168);class i{constructor(l,b,u,d,C){this.accountType=l,this.productType=b,this.length=u,this.question=d,this.questionType=C}}class p{constructor(l,b,u,d,C,h,x,D,I){this.success=l,this.error=b,this.value=u,this.enrollmentKey=d,this.processId=C,this.secureData=h,this.errorMessage=x,this.errorCode=D,this.additionalErrorMessage=I}get message(){const l=this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR01).";return this.additionalErrorMessage?`${l}, ${this.additionalErrorMessage}`:l}static error(){return new p(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR02).")}}var v=t(83328);function _(g,l=!1){return new p(!!g.success,l,g.step,g.enrollmentKey,g.processId,g.secureDataBriefQuestion&&function m(g){return new i(g.accountType,g.productType,g.length,g.question,g.questionType)}(g.secureDataBriefQuestion),g.errorMessage,g.errorCode,g.additionalErrorMessage)}var f=t(65518),n=t(99877);let y=(()=>{class g{constructor(b,u){this.http=b,this.enrollmentStore=u,this.processId=""}execute(b){return(0,e.firstValueFrom)(this.http.post(r.bV.ENROLLMENT,{processId:this.processId,content:b,ipAddress:""}).pipe((0,e.map)(u=>_(u)),(0,e.tap)(({errorCode:u,processId:d,secureData:C})=>{this.enrollmentStore.setErrorCode(function c(g){switch(g){case v.U.PORTABILITY:return"PT87";case v.U.SECURE_DATA:return"43DS";case v.U.VALIDATION_PRODUCT:return"606ENP";case v.U.VALIDATION_SIM:return"VS61";default:return""}}(u)),this.processId=d,C&&this.enrollmentStore.setSecureData(C)}))).catch(({error:u})=>u?_(u,!0):p.error())}reset(){this.processId=""}}return g.\u0275fac=function(b){return new(b||g)(n.\u0275\u0275inject(a.HttpClient),n.\u0275\u0275inject(f.c))},g.\u0275prov=n.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},65518:(A,E,t)=>{t.d(E,{c:()=>s});var a=t(20691),e=t(99877);let s=(()=>{class i extends a.Store{constructor(){super({})}getDocument(){return this.select(v=>({documentType:v.documentType,documentNumber:v.documentNumber,documentRemember:v.documentRemember}))}setDocument(v,m,_){this.reduce(f=>({...f,documentType:v,documentNumber:m,documentRemember:_}))}setEnrollmentKey(v){this.reduce(m=>({...m,enrollmentKey:v}))}setPassword(v){this.reduce(m=>({...m,password:v}))}setSecureData(v){this.reduce(m=>({...m,secureData:v}))}getSecureData(){return this.select(({secureData:v})=>v)}setErrorCode(v){this.reduce(m=>({...m,errorCode:v}))}getErrorCode(){return this.select(({errorCode:v})=>v)}}return i.\u0275fac=function(v){return new(v||i)},i.\u0275prov=e.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},19102:(A,E,t)=>{t.d(E,{r:()=>s});var a=t(17007),e=t(99877);let s=(()=>{class i{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return i.\u0275fac=function(v){return new(v||i)},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(v,m){1&v&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&v&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",m.src,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),i})()},52701:(A,E,t)=>{t.d(E,{q:()=>i});var a=t(17007),e=t(30263),o=t(99877);let i=(()=>{class p{}return p.\u0275fac=function(m){return new(m||p)},p.\u0275cmp=o.\u0275\u0275defineComponent({type:p,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(m,_){1&m&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"bocc-icon",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()),2&m&&(o.\u0275\u0275classMap(_.classTheme),o.\u0275\u0275advance(3),o.\u0275\u0275property("icon",_.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",_.label," "))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),p})()},55648:(A,E,t)=>{t.d(E,{u:()=>_});var a=t(15861),r=t(17007),o=t(30263),s=t(78506),i=t(99877);function v(f,n){if(1&f){const c=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",2),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(c);const g=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(g.onClick())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&f){const c=i.\u0275\u0275nextContext();i.\u0275\u0275property("prefixIcon",c.icon)("disabled",c.disabled),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",c.label," ")}}function m(f,n){if(1&f){const c=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",3),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(c);const g=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(g.onClick())}),i.\u0275\u0275elementEnd()}if(2&f){const c=i.\u0275\u0275nextContext();i.\u0275\u0275property("bocc-button-action",c.icon)("disabled",c.disabled)}}let _=(()=>{class f{constructor(c){this.preferences=c,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:c})=>{this.isIncognito=c||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var c=this;return(0,a.Z)(function*(){yield c.preferences.toggleIncognito()})()}}return f.\u0275fac=function(c){return new(c||f)(i.\u0275\u0275directiveInject(s.Bx))},f.\u0275cmp=i.\u0275\u0275defineComponent({type:f,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(c,y){1&c&&(i.\u0275\u0275template(0,v,3,3,"button",0),i.\u0275\u0275template(1,m,1,2,"button",1)),2&c&&(i.\u0275\u0275property("ngIf",!y.actionMode),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",y.actionMode))},dependencies:[r.CommonModule,r.NgIf,o.P8,o.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),f})()},72765:(A,E,t)=>{t.d(E,{rw:()=>a.r,qr:()=>r.q,uf:()=>e.u,Z:()=>v,t5:()=>g,$O:()=>y});var a=t(19102),r=t(52701),e=t(55648),o=t(17007),s=t(30263),i=t(99877);const p=["*"];let v=(()=>{class l{constructor(){this.disabled=!1}}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:p,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(u,d){1&u&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-icon",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3),i.\u0275\u0275projection(4),i.\u0275\u0275elementEnd()()),2&u&&(i.\u0275\u0275classProp("mbo-poster__content--disabled",d.disabled),i.\u0275\u0275advance(2),i.\u0275\u0275property("icon",d.icon))},dependencies:[o.CommonModule,s.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),l})();var m=t(33395),_=t(77279),f=t(87903),n=t(87956),c=t(25317);let y=(()=>{class l{constructor(u){this.eventBusService=u}onCopy(){this.value&&((0,f.Bn)(this.value),this.eventBusService.emit(_.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return l.\u0275fac=function(u){return new(u||l)(i.\u0275\u0275directiveInject(n.Yd))},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(u,d){1&u&&(i.\u0275\u0275elementStart(0,"bocc-icon",0),i.\u0275\u0275listener("click",function(){return d.onCopy()}),i.\u0275\u0275elementEnd()),2&u&&i.\u0275\u0275property("id",d.elementId)},dependencies:[o.CommonModule,s.Zl,m.kW,c.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),l})(),g=(()=>{class l{}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(u,d){1&u&&i.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&u&&(i.\u0275\u0275property("value",d.value),i.\u0275\u0275advance(1),i.\u0275\u0275property("value",d.value))},dependencies:[o.CommonModule,s.qd,y],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),l})()},79798:(A,E,t)=>{t.d(E,{Vc:()=>r.Vc,rw:()=>a.rw,k4:()=>r.k4,qr:()=>a.qr,uf:()=>a.uf,xO:()=>o.x,A6:()=>e.A,tu:()=>c,Tj:()=>y,GI:()=>w,Uy:()=>N,To:()=>V,w7:()=>Y,o2:()=>r.o2,B_:()=>r.B_,fi:()=>r.fi,XH:()=>r.XH,cN:()=>r.cN,Aj:()=>r.Aj,J5:()=>r.J5,DB:()=>$.D,NH:()=>S.N,ES:()=>G.E,Nu:()=>r.Nu,x6:()=>j.x,KI:()=>J.K,iF:()=>r.iF,u8:()=>q.u,eM:()=>te.e,ZF:()=>oe.Z,wu:()=>ne.w,$n:()=>re.$,KN:()=>ie.K,cV:()=>ae.c,t5:()=>a.t5,$O:()=>a.$O,ZS:()=>ce.Z,sO:()=>se.s,bL:()=>pe,zO:()=>ee.z});var a=t(72765),r=t(27302),e=t(1027),o=t(7427),i=(t(16442),t(17007)),p=t(30263),v=t(44487),m=t.n(v),_=t(13462),f=t(21498),n=t(99877);let c=(()=>{class P{}return P.\u0275fac=function(M){return new(M||P)},P.\u0275mod=n.\u0275\u0275defineNgModule({type:P}),P.\u0275inj=n.\u0275\u0275defineInjector({imports:[i.CommonModule,_.LottieModule.forRoot({player:()=>m()}),a.rw,p.P8,p.Dj,f.P]}),P})(),y=(()=>{class P{ngBoccPortal(M){this.portal=M}onSubmit(){this.portal?.close()}}return P.\u0275fac=function(M){return new(M||P)},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-icon",2),n.\u0275\u0275elementStart(3,"label"),n.\u0275\u0275text(4," \xa1Atenci\xf3n! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p"),n.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),n.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"li",4),n.\u0275\u0275text(11,"Transacciones a celulares."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"li",4),n.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(14,"li",4),n.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(16,"p",5),n.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(18,"div",6)(19,"button",7),n.\u0275\u0275listener("click",function(){return B.onSubmit()}),n.\u0275\u0275elementStart(20,"span"),n.\u0275\u0275text(21,"Continuar"),n.\u0275\u0275elementEnd()()())},dependencies:[i.CommonModule,p.Zl,p.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),P})();var g=t(7603),l=t(87956),b=t(74520),u=t(39904),d=t(87903);function h(P,L){if(1&P){const M=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",6)(1,"label",7),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",8),n.\u0275\u0275text(4,"Tu gerente asignado (a)"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p",8),n.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(7,"button",9),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(M);const K=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(K.onEmail(K.manager.email))}),n.\u0275\u0275elementStart(8,"span",10),n.\u0275\u0275text(9),n.\u0275\u0275elementEnd()()()}if(2&P){const M=n.\u0275\u0275nextContext(2);n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",M.manager.name," "),n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",M.manager.email," ")}}function x(P,L){if(1&P){const M=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),n.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"button",12),n.\u0275\u0275listener("click",function(K){n.\u0275\u0275restoreView(M);const X=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(X.onRetryManager(K))}),n.\u0275\u0275elementStart(4,"span"),n.\u0275\u0275text(5,"Recargar"),n.\u0275\u0275elementEnd()()()}}function D(P,L){if(1&P&&(n.\u0275\u0275elementStart(0,"div",3),n.\u0275\u0275template(1,h,10,2,"div",4),n.\u0275\u0275template(2,x,6,0,"div",5),n.\u0275\u0275elementEnd()),2&P){const M=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",M.manager),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!M.manager)}}function I(P,L){1&P&&(n.\u0275\u0275elementStart(0,"div",13),n.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),n.\u0275\u0275elementEnd()),2&P&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0))}t(29306);let T=(()=>{class P{constructor(M){this.customerService=M,this.requesting=!1}onRetryManager(M){this.customerService.requestManager(),M.stopPropagation()}onEmail(M){(0,d.Gw)(`mailto:${M}`)}onWhatsapp(){(0,d.Gw)(u.BA.WHATSAPP)}}return P.\u0275fac=function(M){return new(M||P)(n.\u0275\u0275directiveInject(l.vZ))},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,D,3,2,"div",1),n.\u0275\u0275template(2,I,5,4,"div",2),n.\u0275\u0275elementEnd()),2&M&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!B.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.requesting))},dependencies:[i.CommonModule,i.NgIf,p.P8,p.Dj,r.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),P})();const R={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},W={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},O={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function U(P,L){if(1&P&&(n.\u0275\u0275elementStart(0,"div",7),n.\u0275\u0275element(1,"mbo-contact-manager",8),n.\u0275\u0275elementEnd()),2&P){const M=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("manager",M.manager)("requesting",M.requesting)}}function k(P,L){if(1&P){const M=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"li",9)(1,"div",10),n.\u0275\u0275listener("click",function(K){const ue=n.\u0275\u0275restoreView(M).$implicit,be=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(be.onOption(ue,K))}),n.\u0275\u0275elementStart(2,"label",11),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",12)(5,"div",13),n.\u0275\u0275element(6,"bocc-icon",14),n.\u0275\u0275elementEnd()()()()}if(2&P){const M=L.$implicit;n.\u0275\u0275property("id",M.id),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",M.label," "),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",M.boccTheme),n.\u0275\u0275advance(2),n.\u0275\u0275property("icon",M.icon)}}let V=(()=>{class P{constructor(M,B,K){this.utagService=M,this.customerStore=B,this.customerService=K,this.isManagerEnabled=!1,this.requesting=!1,this.options=[R,W,O]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:M})=>{this.isManagerEnabled=M?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(B=>{this.manager=B.manager,this.requesting=B.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(M){this.portal=M}onOption(M,B){this.utagService.link("click",M.id),this.portal?.send({action:"option",value:M}),B.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return P.\u0275fac=function(M){return new(M||P)(n.\u0275\u0275directiveInject(g.D),n.\u0275\u0275directiveInject(b.f),n.\u0275\u0275directiveInject(l.vZ))},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-contact-information"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275template(1,U,2,2,"div",1),n.\u0275\u0275elementStart(2,"ul",2),n.\u0275\u0275template(3,k,7,4,"li",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275listener("click",function(){return B.onClose()}),n.\u0275\u0275elementEnd()),2&M&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.isManagerEnabled),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",B.options))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,p.Zl,T],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),P})();var H=t(95437);let Y=(()=>{class P{constructor(M,B){this.floatingService=M,this.mboProvider=B,this.contactsFloating=this.floatingService.create(V),this.contactsFloating?.subscribe(({action:K,value:X})=>{"option"===K?this.dispatchOption(X):this.close()})}subscribe(M){this.subscriber=M}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(M){"PQRS"===M.action?this.mboProvider.openUrl(u.BA.PQRS):this.subscriber&&this.subscriber(M)}}return P.\u0275fac=function(M){return new(M||P)(n.\u0275\u0275inject(p.B7),n.\u0275\u0275inject(H.ZL))},P.\u0275prov=n.\u0275\u0275defineInjectable({token:P,factory:P.\u0275fac,providedIn:"root"}),P})(),w=(()=>{class P{constructor(){this.defenderLineNumber=u._L.DEFENDER_LINE,this.defenderLinePhone=u.WB.DEFENDER_LINE}ngBoccPortal(M){}onEmail(){(0,d.Gw)("mailto:<EMAIL>")}}return P.\u0275fac=function(M){return new(M||P)},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-contact-phones"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"mbo-attention-lines-form"),n.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),n.\u0275\u0275element(5,"bocc-icon",4),n.\u0275\u0275elementStart(6,"span",5),n.\u0275\u0275text(7,"Defensor del consumidor financiero"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),n.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"label",8)(13,"span"),n.\u0275\u0275text(14,"Lorena Cerchar Rosado"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(15,"bocc-badge",9),n.\u0275\u0275text(16," Suplente "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(17,"div",10),n.\u0275\u0275element(18,"bocc-icon",11),n.\u0275\u0275elementStart(19,"div",12)(20,"span",13),n.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(22,"div",10),n.\u0275\u0275element(23,"bocc-icon",14),n.\u0275\u0275elementStart(24,"div",12)(25,"a",15),n.\u0275\u0275text(26),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(27,"span",13),n.\u0275\u0275text(28," Ext. 15318 - 15311 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(29,"div",10),n.\u0275\u0275element(30,"bocc-icon",16),n.\u0275\u0275elementStart(31,"div",12)(32,"span",17),n.\u0275\u0275listener("click",function(){return B.onEmail()}),n.\u0275\u0275text(33," <EMAIL> "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(34,"div",10),n.\u0275\u0275element(35,"bocc-icon",18),n.\u0275\u0275elementStart(36,"div",12)(37,"span",13),n.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),n.\u0275\u0275elementEnd()()()()()()),2&M&&(n.\u0275\u0275advance(25),n.\u0275\u0275property("href",B.defenderLinePhone,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",B.defenderLineNumber," "))},dependencies:[i.CommonModule,p.Zl,p.Oh,r.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),P})(),N=(()=>{class P{constructor(){this.whatsappNumber=u._L.WHATSAPP}ngBoccPortal(M){}onClick(){(0,d.Gw)(u.BA.WHATSAPP)}}return P.\u0275fac=function(M){return new(M||P)},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",2)(4,"button",3),n.\u0275\u0275listener("click",function(){return B.onClick()}),n.\u0275\u0275elementStart(5,"span"),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()()()),2&M&&(n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate(B.whatsappNumber))},dependencies:[i.CommonModule,p.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),P})();var S=t(10119),j=(t(87677),t(68789)),G=t(10455),$=t(91642),J=t(10464),q=t(75221),ee=t(88649),te=t(13043),oe=t(38116),ne=t(68819),re=t(19310),ie=t(94614),ae=(t(70957),t(91248),t(4663)),ce=t(13961),se=t(66709),Z=t(24495),Q=t(57544),le=t(53113);class de extends Q.FormGroup{constructor(){const L=new Q.FormControl("",[Z.zf,Z.O_,Z.Y2,(0,Z.Mv)(24)]),M=new Q.FormControl("",[Z.C1,Z.zf,Z.O_,Z.Y2,(0,Z.Mv)(24)]);super({controls:{description:M,reference:L}}),this.description=M,this.reference=L}setNote(L){this.description.setValue(L?.description),this.reference.setValue(L?.reference)}getNote(){return new le.$H(this.description.value,this.reference.value)}}function me(P,L){if(1&P&&n.\u0275\u0275element(0,"bocc-input-box",7),2&P){const M=n.\u0275\u0275nextContext();n.\u0275\u0275property("formControl",M.formControls.reference)}}let pe=(()=>{class P{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new de}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(M){this.portal=M}}return P.\u0275fac=function(M){return new(M||P)},P.\u0275cmp=n.\u0275\u0275defineComponent({type:P,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(M,B){1&M&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"div",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-input-box",5),n.\u0275\u0275template(7,me,1,1,"bocc-input-box",6),n.\u0275\u0275elementEnd()()),2&M&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",B.cancelAction)("rightAction",B.saveAction),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",B.title," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("formControl",B.formControls.description),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",B.requiredReference))},dependencies:[i.CommonModule,i.NgIf,p.Jx,p.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),P})()},35324:(A,E,t)=>{t.d(E,{V:()=>m});var a=t(17007),e=t(30263),o=t(39904),s=t(87903),i=t(99877);function v(_,f){if(1&_){const n=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"a",9),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(n);const y=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(y.onWhatsapp())}),i.\u0275\u0275elementStart(1,"div",3),i.\u0275\u0275element(2,"bocc-icon",10),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",5)(4,"label",6),i.\u0275\u0275text(5," Whatsapp "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"label",7),i.\u0275\u0275text(7),i.\u0275\u0275elementEnd()()()}if(2&_){const n=i.\u0275\u0275nextContext();i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",n.whatsappNumber," ")}}let m=(()=>{class _{constructor(){this.whatsapp=!1,this.whatsappNumber=o._L.WHATSAPP,this.nationalLineNumber=o._L.NATIONAL_LINE,this.bogotaLineNumber=o._L.BOGOTA_LINE,this.nationalLinePhone=o.WB.NATIONAL_LINE,this.bogotaLinePhone=o.WB.BOGOTA_LINE}onWhatsapp(){(0,s.Gw)(o.BA.WHATSAPP)}}return _.\u0275fac=function(n){return new(n||_)},_.\u0275cmp=i.\u0275\u0275defineComponent({type:_,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(n,c){1&n&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,v,8,1,"a",1),i.\u0275\u0275elementStart(2,"a",2)(3,"div",3),i.\u0275\u0275element(4,"bocc-icon",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"div",5)(6,"label",6),i.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(8,"label",7),i.\u0275\u0275text(9),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(10,"a",8)(11,"div",3),i.\u0275\u0275element(12,"bocc-icon",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(13,"div",5)(14,"label",6),i.\u0275\u0275text(15," Bogot\xe1 "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(16,"label",7),i.\u0275\u0275text(17),i.\u0275\u0275elementEnd()()()()),2&n&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",c.whatsapp),i.\u0275\u0275advance(1),i.\u0275\u0275property("href",c.nationalLinePhone,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",c.nationalLineNumber," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("href",c.bogotaLinePhone,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",c.bogotaLineNumber," "))},dependencies:[a.CommonModule,a.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),_})()},9593:(A,E,t)=>{t.d(E,{k:()=>v});var a=t(17007),e=t(30263),o=t(39904),s=t(95437),i=t(99877);let v=(()=>{class m{constructor(f){this.mboProvider=f,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(o.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(o.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(o.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return m.\u0275fac=function(f){return new(f||m)(i.\u0275\u0275directiveInject(s.ZL))},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(f,n){1&f&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275listener("click",function(){return n.onProducts()}),i.\u0275\u0275element(3,"bocc-icon",3),i.\u0275\u0275elementStart(4,"label",4),i.\u0275\u0275text(5," Productos "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(6,"div",5),i.\u0275\u0275listener("click",function(){return n.onTransfers()}),i.\u0275\u0275element(7,"bocc-icon",6),i.\u0275\u0275elementStart(8,"label",4),i.\u0275\u0275text(9," Transferir "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(10,"div",7),i.\u0275\u0275listener("click",function(){return n.onPaymentQR()}),i.\u0275\u0275elementStart(11,"div",8)(12,"div",9),i.\u0275\u0275element(13,"bocc-icon",10),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(14,"bocc-icon",11),i.\u0275\u0275elementStart(15,"label",4),i.\u0275\u0275text(16," Pago QR "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(17,"div",12),i.\u0275\u0275listener("click",function(){return n.onPayments()}),i.\u0275\u0275element(18,"bocc-icon",13),i.\u0275\u0275elementStart(19,"label",4),i.\u0275\u0275text(20," Pagar "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(21,"div",14),i.\u0275\u0275listener("click",function(){return n.onToken()}),i.\u0275\u0275element(22,"bocc-icon",15),i.\u0275\u0275elementStart(23,"label",4),i.\u0275\u0275text(24," Token "),i.\u0275\u0275elementEnd()()()()),2&f&&(i.\u0275\u0275advance(2),i.\u0275\u0275classProp("bocc-footer-form__element--active",n.isProducts),i.\u0275\u0275advance(4),i.\u0275\u0275classProp("bocc-footer-form__element--active",n.isTransfers),i.\u0275\u0275advance(11),i.\u0275\u0275classProp("bocc-footer-form__element--active",n.isPayments),i.\u0275\u0275advance(4),i.\u0275\u0275classProp("bocc-footer-form__element--active",n.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),m})()},83867:(A,E,t)=>{t.d(E,{o:()=>l});var a=t(17007),e=t(30263),o=t(8834),s=t(98699),m=(t(57544),t(99877));function f(b,u){if(1&b&&(m.\u0275\u0275elementStart(0,"label",11),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&b){const d=m.\u0275\u0275nextContext();m.\u0275\u0275classProp("mbo-currency-box__rate--active",d.hasValue),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate2(" ",d.valueFormat," ",d.rateCode," ")}}function n(b,u){if(1&b&&(m.\u0275\u0275elementStart(0,"div",12),m.\u0275\u0275element(1,"img",13),m.\u0275\u0275elementEnd()),2&b){const d=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275property("src",d.icon,m.\u0275\u0275sanitizeUrl)}}function c(b,u){if(1&b&&(m.\u0275\u0275elementStart(0,"div",14),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&b){const d=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",d.currencyCode," ")}}function y(b,u){if(1&b&&(m.\u0275\u0275elementStart(0,"div",15),m.\u0275\u0275element(1,"bocc-icon",16),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&b){const d=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",null==d.formControl.error?null:d.formControl.error.message," ")}}function g(b,u){if(1&b&&(m.\u0275\u0275elementStart(0,"div",18),m.\u0275\u0275element(1,"bocc-icon",19),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&b){const d=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",d.helperInfo," ")}}let l=(()=>{class b{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,s.itIsDefined)(this.rate)}get value(){const d=+this.formControl?.value;return isNaN(d)?0:this.hasRate?d/this.rate:0}get valueFormat(){return(0,o.b)({value:this.value,symbol:"$",decimals:!0})}}return b.\u0275fac=function(d){return new(d||b)},b.\u0275cmp=m.\u0275\u0275defineComponent({type:b,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(d,C){1&d&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275template(4,f,2,4,"label",3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(5,"div",4)(6,"div",5),m.\u0275\u0275template(7,n,2,1,"div",6),m.\u0275\u0275element(8,"bocc-currency-field",7),m.\u0275\u0275template(9,c,2,1,"div",8),m.\u0275\u0275elementEnd()(),m.\u0275\u0275template(10,y,4,1,"div",9),m.\u0275\u0275template(11,g,4,1,"div",10),m.\u0275\u0275elementEnd()),2&d&&(m.\u0275\u0275classProp("mbo-currency-box--focused",C.formControl.focused)("mbo-currency-box--error",C.formControl.invalid&&C.formControl.touched)("mbo-currency-box--disabled",C.formControl.disabled||C.disabled),m.\u0275\u0275advance(2),m.\u0275\u0275property("for",C.elementId),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",C.label," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",C.hasRate),m.\u0275\u0275advance(3),m.\u0275\u0275property("ngIf",C.icon),m.\u0275\u0275advance(1),m.\u0275\u0275property("elementId",C.elementId)("placeholder",C.placeholder)("disabled",C.disabled)("formControl",C.formControl),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",C.currencyCode),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",C.formControl.invalid&&C.formControl.touched),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",C.helperInfo&&!(C.formControl.invalid&&C.formControl.touched)))},dependencies:[a.CommonModule,a.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),b})()},85070:(A,E,t)=>{t.d(E,{f:()=>p});var a=t(17007),e=t(78506),o=t(99877);const i=["*"];let p=(()=>{class v{constructor(_){this.session=_}ngOnInit(){this.session.customer().then(_=>this.customer=_)}}return v.\u0275fac=function(_){return new(_||v)(o.\u0275\u0275directiveInject(e._I))},v.\u0275cmp=o.\u0275\u0275defineComponent({type:v,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(_,f){1&_&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",2),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()),2&_&&(o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(null==f.customer?null:f.customer.shortName))},dependencies:[a.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),v})()},65887:(A,E,t)=>{t.d(E,{X:()=>_});var a=t(17007),e=t(99877),s=t(30263),i=t(24495);function m(f,n){1&f&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let _=(()=>{class f{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[i.C1]:[]),this.unsubscription=this.documentType.subscribe(c=>{c&&(this.updateNumber(c,this.required),this.inputType=this.getInputType(c))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(c){if(c.required){const y=c.required.currentValue;this.documentType.setValidators(y?[i.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,y)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(c){return"PA"===c.code?"text":"number"}updateNumber(c,y){const g=this.validatorsForNumber(c,y);this.documentNumber.setValidators(g),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(c,y){return this.validatorsFromType(c).concat(y?[i.C1]:[])}maxLength(c){return y=>y&&y.length>c?{id:"maxLength",message:`Debe tener m\xe1ximo ${c} caracteres`}:null}validatorsFromType(c){switch(c.code){case"PA":return[i.JF];case"NIT":return[i.X1,this.maxLength(15)];default:return[i.X1]}}}return f.\u0275fac=function(c){return new(c||f)},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(c,y){1&c&&(e.\u0275\u0275template(0,m,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&c&&(e.\u0275\u0275property("ngIf",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",y.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementSelectId)("label",y.labelType)("suggestions",y.documents)("disabled",y.disabled)("formControl",y.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",y.elementInputId)("label",y.labelNumber)("type",y.inputType)("disabled",y.disabled)("formControl",y.documentNumber))},dependencies:[a.CommonModule,a.NgIf,s.DT,s.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),f})()},78021:(A,E,t)=>{t.d(E,{c:()=>f});var a=t(17007),e=t(30263),o=t(7603),s=t(98699),p=t(99877);function m(n,c){if(1&n){const y=p.\u0275\u0275getCurrentView();p.\u0275\u0275elementStart(0,"button",5),p.\u0275\u0275listener("click",function(){p.\u0275\u0275restoreView(y);const l=p.\u0275\u0275nextContext();return p.\u0275\u0275resetView(l.onAction(l.leftAction))}),p.\u0275\u0275elementStart(1,"span"),p.\u0275\u0275text(2),p.\u0275\u0275elementEnd()()}if(2&n){const y=p.\u0275\u0275nextContext();p.\u0275\u0275property("id",y.leftAction.id)("bocc-button",y.leftAction.type||"flat")("prefixIcon",y.leftAction.prefixIcon)("disabled",y.itIsDisabled(y.leftAction))("hidden",y.itIsHidden(y.leftAction)),p.\u0275\u0275advance(2),p.\u0275\u0275textInterpolate(y.leftAction.label)}}function _(n,c){if(1&n){const y=p.\u0275\u0275getCurrentView();p.\u0275\u0275elementStart(0,"button",6),p.\u0275\u0275listener("click",function(){const b=p.\u0275\u0275restoreView(y).$implicit,u=p.\u0275\u0275nextContext();return p.\u0275\u0275resetView(u.onAction(b))}),p.\u0275\u0275elementEnd()}if(2&n){const y=c.$implicit,g=p.\u0275\u0275nextContext();p.\u0275\u0275property("id",y.id)("type",y.type||"flat")("bocc-button-action",y.icon)("disabled",g.itIsDisabled(y))("hidden",g.itIsHidden(y))}}let f=(()=>{class n{constructor(y){this.utagService=y,this.rightActions=[]}itIsDisabled({disabled:y}){return(0,s.evalValueOrFunction)(y)}itIsHidden({hidden:y}){return(0,s.evalValueOrFunction)(y)}onAction(y){const{id:g}=y;g&&this.utagService.link("click",g),y.click()}}return n.\u0275fac=function(y){return new(y||n)(p.\u0275\u0275directiveInject(o.D))},n.\u0275cmp=p.\u0275\u0275defineComponent({type:n,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(y,g){1&y&&(p.\u0275\u0275elementStart(0,"div",0)(1,"div",1),p.\u0275\u0275template(2,m,3,6,"button",2),p.\u0275\u0275elementEnd(),p.\u0275\u0275elementStart(3,"div",3),p.\u0275\u0275template(4,_,1,5,"button",4),p.\u0275\u0275elementEnd()()),2&y&&(p.\u0275\u0275advance(2),p.\u0275\u0275property("ngIf",g.leftAction),p.\u0275\u0275advance(2),p.\u0275\u0275property("ngForOf",g.rightActions))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),n})()},27302:(A,E,t)=>{t.d(E,{Vc:()=>a.V,k4:()=>r.k,o2:()=>e.o,B_:()=>v,fi:()=>m.f,XH:()=>_.X,cN:()=>y.c,Aj:()=>g.A,J5:()=>D.J,Nu:()=>R,iF:()=>Y});var a=t(35324),r=t(9593),e=t(83867),o=t(17007),s=t(99877);function p(w,N){if(1&w){const S=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"div",2),s.\u0275\u0275listener("click",function(){const G=s.\u0275\u0275restoreView(S).$implicit,$=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView($.onClickCurrency(G))}),s.\u0275\u0275elementStart(1,"div",3),s.\u0275\u0275element(2,"img",4)(3,"img",5),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"label",6),s.\u0275\u0275text(5),s.\u0275\u0275elementEnd()()}if(2&w){const S=N.$implicit,F=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",F.isEnabled(S)),s.\u0275\u0275advance(2),s.\u0275\u0275property("src",S.enabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(1),s.\u0275\u0275property("src",S.disabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate1(" ",S.label," ")}}t(57544);let v=(()=>{class w{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[S]=this.currencies;this.formControl.setValue(S)}}ngOnChanges(S){const{currencies:F}=S;if(F){const[j]=F.currentValue;this.formControl&&this.formControl.setValue(j)}}isEnabled(S){return S===this.formControl?.value}onClickCurrency(S){this.formControl&&!this.disabled&&this.formControl.setValue(S)}changeCurriencies(S){if(S.currencies){const F=S.currencies.currentValue,[j]=F;this.formControl&&this.formControl.setValue(j)}}}return w.\u0275fac=function(S){return new(S||w)},w.\u0275cmp=s.\u0275\u0275defineComponent({type:w,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275NgOnChangesFeature,s.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(S,F){1&S&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,p,6,5,"div",1),s.\u0275\u0275elementEnd()),2&S&&(s.\u0275\u0275classProp("mbo-currency-toggle--disabled",F.disabled),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngForOf",F.currencies))},dependencies:[o.CommonModule,o.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),w})();var m=t(85070),_=t(65887),f=t(30263),y=t(78021),g=t(50689),u=(t(7603),t(98699),t(72765)),D=t(88014);function I(w,N){if(1&w&&(s.\u0275\u0275elementStart(0,"div",4),s.\u0275\u0275element(1,"img",5),s.\u0275\u0275elementEnd()),2&w){const S=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("src",S.src,s.\u0275\u0275sanitizeUrl)}}const T=["*"];let R=(()=>{class w{}return w.\u0275fac=function(S){return new(S||w)},w.\u0275cmp=s.\u0275\u0275defineComponent({type:w,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],ngContentSelectors:T,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(S,F){1&S&&(s.\u0275\u0275projectionDef(),s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,I,2,1,"div",1),s.\u0275\u0275elementStart(2,"div",2)(3,"div",3),s.\u0275\u0275projection(4),s.\u0275\u0275elementEnd()()()),2&S&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",F.src))},dependencies:[o.CommonModule,o.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),w})();var W=t(24495);const O=/[A-Z]/,z=/[a-z]/,U=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,k=w=>w&&!O.test(w)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,V=w=>w&&!z.test(w)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,H=w=>w&&!U.test(w)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let Y=(()=>{class w{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([W.C1,V,k,H,(0,W.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const S=this.formControl.errors.reduce((j,{id:G})=>[...j,G],[]),F=S.includes("required");this.smallInvalid=S.includes("smallCase")||F,this.capitalInvalid=S.includes("capitalCase")||F,this.specialCharInvalid=S.includes("specialChar")||F,this.minLengthInvalid=S.includes("minlength")||F})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return w.\u0275fac=function(S){return new(S||w)},w.\u0275cmp=s.\u0275\u0275defineComponent({type:w,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(S,F){1&S&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275element(1,"bocc-password-box",1),s.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),s.\u0275\u0275text(4," Min\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"mbo-poster",4),s.\u0275\u0275text(6," May\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(7,"mbo-poster",5),s.\u0275\u0275text(8," Especial "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"mbo-poster",6),s.\u0275\u0275text(10," Caracteres "),s.\u0275\u0275elementEnd()()()),2&S&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",F.elementId)("disabled",F.disabled)("formControl",F.formControl),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.smallInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.capitalInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.specialCharInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",F.minLengthInvalid))},dependencies:[o.CommonModule,f.sC,u.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),w})()},50689:(A,E,t)=>{t.d(E,{A:()=>i});var a=t(17007),e=t(99877);const s=["*"];let i=(()=>{class p{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return p.\u0275fac=function(m){return new(m||p)},p.\u0275cmp=e.\u0275\u0275defineComponent({type:p,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(m,_){1&m&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&m&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",_.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),p})()},88014:(A,E,t)=>{t.d(E,{J:()=>s});var a=t(17007),e=t(99877);let s=(()=>{class i{}return i.\u0275fac=function(v){return new(v||i)},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(v,m){1&v&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[a.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),i})()},21498:(A,E,t)=>{t.d(E,{P:()=>n});var a=t(17007),e=t(30263),o=t(99877);function i(c,y){if(1&c&&o.\u0275\u0275element(0,"bocc-card-product-summary",7),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",g.information.product.color)("icon",g.information.product.icon)("number",g.information.product.number)("title",g.information.product.title)("subtitle",g.information.product.subtitle)}}function p(c,y){if(1&c&&o.\u0275\u0275element(0,"bocc-card-summary",8),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",g.information.standard.header)("title",g.information.standard.title)("subtitle",g.information.standard.subtitle)("detail",g.information.standard.detail)}}function v(c,y){if(1&c&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",g.information.amount.header)("amount",g.information.amount.value)("symbol",g.information.amount.symbol)("amountSmall",g.information.amount.small)}}function m(c,y){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",g.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(g.information.text.content)}}function _(c,y){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",12),o.\u0275\u0275element(1,"bocc-icon",13),o.\u0275\u0275elementStart(2,"span",14),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",15),o.\u0275\u0275elementStart(5,"span",14),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",g.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",g.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",g.information.datetime.time," ")}}function f(c,y){if(1&c&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&c){const g=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",g.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",g.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",g.information.badge.label," ")}}let n=(()=>{class c{}return c.\u0275fac=function(g){return new(g||c)},c.\u0275cmp=o.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(g,l){1&g&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,i,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,p,1,4,"bocc-card-summary",2),o.\u0275\u0275template(3,v,1,4,"bocc-card-summary",3),o.\u0275\u0275template(4,m,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,_,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,f,3,4,"bocc-card-summary",6),o.\u0275\u0275elementEnd()),2&g&&(o.\u0275\u0275property("ngSwitch",l.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),c})()},7427:(A,E,t)=>{t.d(E,{x:()=>n});var a=t(17007),e=t(30263),o=t(87903),i=(t(29306),t(77279)),p=t(87956),v=t(68789),m=t(13961),_=t(99877);let n=(()=>{class c{constructor(g,l){this.eventBusService=g,this.onboardingScreenService=l,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,o.Bn)(this.product.tagAval),this.eventBusService.emit(i.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(m.Z)),this.tagAvalonboarding.open()}}return c.\u0275fac=function(g){return new(g||c)(_.\u0275\u0275directiveInject(p.Yd),_.\u0275\u0275directiveInject(v.x))},c.\u0275cmp=_.\u0275\u0275defineComponent({type:c,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[_.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(g,l){1&g&&(_.\u0275\u0275elementStart(0,"bocc-card-product",0),_.\u0275\u0275listener("key",function(){return l.onTagAval()})("onboarding",function(){return l.onBoarding()}),_.\u0275\u0275elementEnd()),2&g&&(_.\u0275\u0275classMap(l.product.bank.className),_.\u0275\u0275property("iconTitle",l.iconTitle)("title",l.product.nickname||l.product.name)("icon",l.product.logo)("tagAval",l.product.tagAvalFormat)("actions",l.actions)("color",l.product.color)("code",l.product.shortNumber)("label",l.product.label)("amount",l.product.amount)("incognito",l.incognito)("displayCard",!0)("statusLabel",null==l.product.status?null:l.product.status.label)("statusColor",null==l.product.status?null:l.product.status.color)("cromaline",!0)("msgError",l.msgError))},dependencies:[a.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),c})()},1027:(A,E,t)=>{t.d(E,{A:()=>g});var a=t(17007),r=t(72765),e=t(30263),o=t(99877);function s(l,b){if(1&l&&o.\u0275\u0275element(0,"bocc-card-product-summary",8),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",u.information.product.color)("icon",u.information.product.icon)("number",u.information.product.number)("title",u.information.product.title)("subtitle",u.information.product.subtitle)}}function i(l,b){if(1&l&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.standard.header)("title",u.information.standard.title)("subtitle",u.information.standard.subtitle)}}function p(l,b){if(1&l&&o.\u0275\u0275element(0,"bocc-card-summary",10),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.amount.header)("amount",u.information.amount.value)("symbol",u.information.amount.symbol)}}function v(l,b){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(u.information.text.content)}}function m(l,b){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",13),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",16),o.\u0275\u0275elementStart(5,"span",15),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.datetime.time," ")}}function _(l,b){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",17),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd()()),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.date.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.date.date," ")}}function f(l,b){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&l){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",u.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",u.information.badge.label," ")}}let n=(()=>{class l{}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=o.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(u,d){1&u&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,s,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,i,1,3,"bocc-card-summary",2),o.\u0275\u0275template(3,p,1,3,"bocc-card-summary",3),o.\u0275\u0275template(4,v,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,m,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,_,4,3,"bocc-card-summary",6),o.\u0275\u0275template(7,f,3,4,"bocc-card-summary",7),o.\u0275\u0275elementEnd()),2&u&&(o.\u0275\u0275property("ngSwitch",d.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","date"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),l})();function c(l,b){1&l&&o.\u0275\u0275element(0,"mbo-card-information-element",8),2&l&&o.\u0275\u0275property("information",b.$implicit)}const y=["*"];let g=(()=>{class l{constructor(){this.skeleton=!1,this.informations=[]}}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=o.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(u,d){1&u&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"mbo-bank-logo",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"div",4),o.\u0275\u0275element(5,"div",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275projection(7),o.\u0275\u0275template(8,c,1,1,"mbo-card-information-element",7),o.\u0275\u0275elementEnd()()),2&u&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("result",!0),o.\u0275\u0275advance(5),o.\u0275\u0275property("ngForOf",d.informations))},dependencies:[a.CommonModule,a.NgForOf,r.rw,n],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),l})()},16442:(A,E,t)=>{t.d(E,{u:()=>u});var a=t(99877),e=t(17007),s=t(13462),p=t(19102),v=t(45542),m=t(65467),_=t(21498);function f(d,C){if(1&d&&(a.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&d){const h=a.\u0275\u0275nextContext();a.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",h.template.skeleton),a.\u0275\u0275property("secondary",!0)("active",h.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",h.template.header.subtitle," ")}}function n(d,C){1&d&&a.\u0275\u0275element(0,"mbo-card-information",16),2&d&&a.\u0275\u0275property("information",C.$implicit)}function c(d,C){if(1&d&&(a.\u0275\u0275elementStart(0,"div",14),a.\u0275\u0275projection(1),a.\u0275\u0275template(2,n,1,1,"mbo-card-information",15),a.\u0275\u0275elementEnd()),2&d){const h=a.\u0275\u0275nextContext();a.\u0275\u0275advance(2),a.\u0275\u0275property("ngForOf",h.template.informations)}}function y(d,C){1&d&&(a.\u0275\u0275elementStart(0,"div",17),a.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),a.\u0275\u0275elementEnd()),2&d&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0))}function g(d,C){if(1&d){const h=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",23),a.\u0275\u0275listener("click",function(){const I=a.\u0275\u0275restoreView(h).$implicit,T=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(T.onAction(I))}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&d){const h=C.$implicit;a.\u0275\u0275property("bocc-button",h.type)("prefixIcon",h.prefixIcon),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate(h.label)}}function l(d,C){if(1&d&&(a.\u0275\u0275elementStart(0,"div",21),a.\u0275\u0275template(1,g,3,3,"button",22),a.\u0275\u0275elementEnd()),2&d){const h=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("ngForOf",h.template.actions)}}const b=["*"];let u=(()=>{class d{constructor(){this.disabled=!1,this.action=new a.EventEmitter}onAction({event:h}){this.action.emit(h)}}return d.\u0275fac=function(h){return new(h||d)},d.\u0275cmp=a.\u0275\u0275defineComponent({type:d,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:b,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(h,x){1&h&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275element(3,"mbo-bank-logo",3),a.\u0275\u0275elementStart(4,"div",4),a.\u0275\u0275element(5,"ng-lottie",5),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(8,f,2,5,"bocc-skeleton-text",7),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(9,"div",8),a.\u0275\u0275element(10,"div",9),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(11,c,3,1,"div",10),a.\u0275\u0275template(12,y,4,5,"div",11),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(13,l,2,1,"div",12)),2&h&&(a.\u0275\u0275classProp("animation",!x.template.skeleton),a.\u0275\u0275advance(3),a.\u0275\u0275property("result",!0),a.\u0275\u0275advance(2),a.\u0275\u0275property("options",x.template.header.animation),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",x.template.header.title," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.skeleton||x.template.header.subtitle),a.\u0275\u0275advance(3),a.\u0275\u0275property("ngIf",!x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.template.actions.length&&!x.disabled))},dependencies:[e.NgForOf,e.NgIf,s.LottieComponent,p.r,v.P,m.D,_.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),d})()},10119:(A,E,t)=>{t.d(E,{N:()=>y});var a=t(17007),e=t(99877),s=t(30263),i=t(7603),p=t(98699);function m(g,l){if(1&g&&e.\u0275\u0275element(0,"bocc-diamond",14),2&g){const b=l.$implicit,u=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",u.itIsSelected(b))}}function _(g,l){if(1&g){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const d=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(d.onAction(d.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&g){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionLeft.id)("bocc-button",b.footerActionLeft.type)("prefixIcon",b.footerActionLeft.prefixIcon)("disabled",b.itIsDisabled(b.footerActionLeft))("hidden",b.itIsHidden(b.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionLeft.label)}}function f(g,l){if(1&g){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const d=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(d.onAction(d.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&g){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionRight.id)("bocc-button",b.footerActionRight.type)("prefixIcon",b.footerActionRight.prefixIcon)("disabled",b.itIsDisabled(b.footerActionRight))("hidden",b.itIsHidden(b.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionRight.label)}}const n=["*"];let y=(()=>{class g{constructor(b,u){this.ref=b,this.utagService=u,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((b,u)=>u),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(b){return b===this.currentPosition}itIsDisabled({disabled:b}){return(0,p.evalValueOrFunction)(b)}itIsHidden({hidden:b}){return(0,p.evalValueOrFunction)(b)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(b){const{id:u}=b;u&&this.utagService.link("click",u),b.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(b){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(b),this.automatic=!1,this.setTranslatePosition(b)}setTranslatePosition(b){this.translateX=b*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(b){this.transformContent=`translateX(${b}px)`}emitPosition(b){this.finished||(this.finished=b+1===this.elements.length),this.position.emit({position:b,finished:this.finished})}getPositionSlide(b){return b>=this.elements.length?this.elements.length-1:b<0?0:b}setTouchHandler(b){let u=0,d=0;b.addEventListener("touchstart",C=>{if(C.changedTouches.length){const{clientX:h}=C.changedTouches.item(0);u=0,this.touched=!0,d=h}}),b.addEventListener("touchmove",C=>{if(C.changedTouches.length){const h=C.changedTouches.item(0),x=h.clientX-d;d=h.clientX,this.translateX+=x,u+=x,this.setTranslateContent(this.translateX)}}),b.addEventListener("touchend",C=>{this.touched=!1,C.changedTouches.length&&(Math.abs(u)/this.widthBody*100>=40&&(u>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return g.\u0275fac=function(b){return new(b||g)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(i.D))},g.\u0275cmp=e.\u0275\u0275defineComponent({type:g,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:n,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(b,u){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return u.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return u.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,m,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return u.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,_,3,6,"button",13),e.\u0275\u0275template(16,f,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&b&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",u.headerActionLeft)("rightAction",u.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",u.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",u.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",u.widthContent)("transform",u.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",u.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!u.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",u.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!u.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",u.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.footerActionRight))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.P8,s.u1,s.ou,s.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),g})()},68789:(A,E,t)=>{t.d(E,{x:()=>i});var a=t(7603),r=t(10455),e=t(87677),o=t(99877);let i=(()=>{class p{constructor(m){this.portalService=m}information(){this.portal||(this.portal=this.portalService.container({component:r.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(m,_){return this.portalService.container({component:m,container:e.C,props:{container:_?.containerProps,component:_?.componentProps}})}}return p.\u0275fac=function(m){return new(m||p)(o.\u0275\u0275inject(a.v))},p.\u0275prov=o.\u0275\u0275defineInjectable({token:p,factory:p.\u0275fac,providedIn:"root"}),p})()},87677:(A,E,t)=>{t.d(E,{C:()=>e});var a=t(99877);let e=(()=>{class o{constructor(i){this.ref=i,this.visible=!1,this.visibleChange=new a.EventEmitter}open(i=0){setTimeout(()=>{this.changeVisible(!0)},i)}close(i=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},i)}append(i){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(i)}ngBoccPortal(i){this.portal=i}changeVisible(i){this.visible=i,this.visibleChange.emit(i)}}return o.\u0275fac=function(i){return new(i||o)(a.\u0275\u0275directiveInject(a.ElementRef))},o.\u0275cmp=a.\u0275\u0275defineComponent({type:o,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(i,p){1&i&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"div",1),a.\u0275\u0275elementEnd()),2&i&&a.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",p.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),o})()},10455:(A,E,t)=>{t.d(E,{E:()=>p});var a=t(17007),e=t(99877),s=t(27302),i=t(10119);let p=(()=>{class v{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(_){this.portal=_}onPosition({finished:_}){this.finished=_,_&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(_,f){1&_&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(c){return f.onPosition(c)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&_&&e.\u0275\u0275property("footerActionLeft",f.footerLeft)("footerActionRight",f.footerRight)("gradient",!0)},dependencies:[a.CommonModule,s.Nu,i.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),v})()},91642:(A,E,t)=>{t.d(E,{D:()=>u});var a=t(17007),e=t(99877),s=t(30263),i=t(87542),p=t(70658),v=t(3372),m=t(87956),_=t(72765);function f(d,C){1&d&&e.\u0275\u0275element(0,"mbo-bank-logo")}function n(d,C){1&d&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function c(d,C){if(1&d&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&d){const h=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",h.verifying)}}function y(d,C){1&d&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function g(d,C){if(1&d){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(h);const D=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(D.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&d){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",h.verifying)}}const l=["*"],{OtpInputSuperuser:b}=v.M;let u=(()=>{class d{constructor(h,x,D,I){this.ref=h,this.otpService=x,this.deviceService=D,this.preferencesService=I,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=i.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new i.yV}ngOnInit(){this.otpService.onCode(h=>{this.otpControls.setCode(h),this.otpControls.valid&&this.onAutocomplete(h)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(h){const{documentNumber:x}=h;x&&this.preferencesService.applyFunctionality(b,x.currentValue).then(D=>{this.itIsDocumentSuperuser=D})}get otpVisible(){return!p.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return p.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&p.N.otpReadonlyMobile}onAutocomplete(h){this.code.emit(h)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return d.\u0275fac=function(h){return new(h||d)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(m.no),e.\u0275\u0275directiveInject(m.U8),e.\u0275\u0275directiveInject(m.yW))},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:l,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(h,x){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,f,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,n,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(I){return x.onAutocomplete(I)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,c,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,y,7,0,"div",8),e.\u0275\u0275template(13,g,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",x.otpVisible),e.\u0275\u0275property("formControls",x.otpControls)("readonly",x.otpReadonly)("mobile",x.otpMobile)("disabled",x.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",x.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",x.isIos))},dependencies:[a.CommonModule,a.NgIf,s.P8,s.Yx,_.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),d})()},10464:(A,E,t)=>{t.d(E,{K:()=>p});var a=t(17007),e=t(99877),s=t(22816);const i=["*"];let p=(()=>{class v{constructor(_){this.ref=_,this.scroller=new s.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(_){this.scroller.reset(_.target)}}return v.\u0275fac=function(_){return new(_||v)(e.\u0275\u0275directiveInject(e.ElementRef))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(_,f){1&_&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(c){return f.onScroll(c)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&_&&e.\u0275\u0275classProp("mbo-page__content--start",f.scrollStart)("mbo-page__content--end",f.scrollEnd)},dependencies:[a.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),v})()},75221:(A,E,t)=>{t.d(E,{u:()=>v});var a=t(17007),e=t(30263),o=t(27302),i=(t(88649),t(99877));let v=(()=>{class m{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return m.\u0275fac=function(f){return new(f||m)},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(f,n){1&f&&i.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&f&&(i.\u0275\u0275property("formControl",n.passwordControl.controls.password)("disabled",n.disabled)("elementId",n.elementPasswordId),i.\u0275\u0275advance(1),i.\u0275\u0275property("elementId",n.elementConfirmId)("disabled",n.disabled)("formControl",n.passwordControl.controls.repeatPassword))},dependencies:[a.CommonModule,e.sC,o.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),m})()},88649:(A,E,t)=>{t.d(E,{z:()=>o});var a=t(57544),r=t(24495);class o extends a.FormGroup{constructor(){const i=new a.FormControl(""),p=new a.FormControl("",[r.C1,(s=i,i=>i&&i!==s.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var s;super({controls:{password:i,repeatPassword:p}})}get password(){return this.controls.password.value}}},13043:(A,E,t)=>{t.d(E,{e:()=>g});var a=t(17007),e=t(99877),s=t(30263),v=(t(57544),t(27302));function m(l,b){1&l&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function _(l,b){if(1&l&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&l){const u=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",u.title," ")}}function f(l,b){if(1&l){const u=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(u).$implicit,x=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(x.onProduct(h))}),e.\u0275\u0275elementEnd()}if(2&l){const u=b.$implicit,d=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",u.color)("icon",u.logo)("title",u.nickname)("number",u.publicNumber)("detail",u.bank.name)("ghost",d.ghost)}}function n(l,b){if(1&l&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,_,2,1,"div",8),e.\u0275\u0275template(3,f,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&l){const u=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",u.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",u.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!u.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",u.msgError," ")}}function c(l,b){1&l&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&l&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const y=["*"];let g=(()=>{class l{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(u){return this.productControl?.value?.id===u.id}onProduct(u){this.select.emit(u),this.productControl?.setValue(u)}}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(u,d){1&u&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275template(2,n,6,5,"div",2),e.\u0275\u0275template(3,c,3,2,"div",3),e.\u0275\u0275elementEnd()),2&u&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",d.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!d.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",d.skeleton))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.w_,v.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),l})()},38116:(A,E,t)=>{t.d(E,{Z:()=>p});var a=t(17007),e=t(99877),s=t(30263);function i(v,m){if(1&v){const _=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const c=e.\u0275\u0275restoreView(_).$implicit,y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onAction(c))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&v){const _=m.$implicit,f=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(f.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",f.itIsDisabled(_)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(f.theme(_)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",_.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",_.label," ")}}let p=(()=>{class v{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(_){return _.requiredInformation&&_.errorInformation}theme(_){return this.itIsDisabled(_)?"none":_.theme}onAction(_){!this.itIsDisabled(_)&&this.action.emit(_.type)}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(_,f){1&_&&e.\u0275\u0275template(0,i,6,8,"div",0),2&_&&e.\u0275\u0275property("ngForOf",f.actions)},dependencies:[a.CommonModule,a.NgForOf,s.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),v})()},68819:(A,E,t)=>{t.d(E,{w:()=>T});var a=t(17007),e=t(99877),s=t(30263),i=t(39904),m=(t(57544),t(78506)),f=(t(29306),t(87903)),n=t(95437),c=t(27302),y=t(70957),g=t(91248),l=t(13961),b=t(68789),u=t(33395),d=t(25317);function C(R,W){if(1&R){const O=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(U){e.\u0275\u0275restoreView(O);const k=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(k.onBoarding(U))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(O);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onCopyKey(U.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(O);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&R){const O=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",O.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",O.product.tagAval)}}function h(R,W){if(1&R&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&R){const O=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",O.digitalSection)("currencyCode",null==O.currencyControl.value?null:O.currencyControl.value.code)("hidden",O.itIsVisibleMovements)}}function x(R,W){if(1&R&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&R){const O=W.$implicit,z=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",O)("currencyCode",null==z.currencyControl.value?null:z.currencyControl.value.code)}}const D=[[["","header",""]],"*"],I=["[header]","*"];let T=(()=>{class R{constructor(O,z,U){this.mboProvider=O,this.managerInformation=z,this.onboardingScreenService=U,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(O){const{movements:z,sections:U}=O;z&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!z.currentValue),U&&this.product&&this.refreshComponent(this.product,U.currentValue),this.managerInformation.requestInfoBody().then(k=>{k.when({success:({canEditTagAval:V})=>{this.canEditTagAval=V}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(O){(0,f.Bn)(O),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(i.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(O,z){const U=(0,f.A2)(O);if(this.sectionPosition=0,z?.length){const k=z.map(({title:V},H)=>({label:V,value:H}));U&&(this.headerMovements.value=this.sections.length,k.push(this.headerMovements)),this.headers=k}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const k=[{label:"Error",value:1}];U&&k.unshift(this.headerMovements),this.headers=k}}onBoarding(O){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(l.Z)),this.tagAvalonboarding.open(),O.stopPropagation()}}return R.\u0275fac=function(O){return new(O||R)(e.\u0275\u0275directiveInject(n.ZL),e.\u0275\u0275directiveInject(m.vu),e.\u0275\u0275directiveInject(b.x))},R.\u0275cmp=e.\u0275\u0275defineComponent({type:R,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:I,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(O,z){1&O&&(e.\u0275\u0275projectionDef(D),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(k){return z.sectionPosition=k}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,C,15,2,"div",4),e.\u0275\u0275template(6,h,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,x,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&O&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",z.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",z.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",z.headers)("value",z.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",z.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==z.product?null:z.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",z.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",z.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",z.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",z.movements)("header",z.header)("product",z.product)("currencyCode",null==z.currencyControl.value?null:z.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",z.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!z.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.Gf,s.qw,s.P8,s.Dj,s.qd,y.K,g.I,c.Aj,u.kW,d.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),R})()},19310:(A,E,t)=>{t.d(E,{$:()=>h});var a=t(17007),r=t(99877),e=t(30263),o=t(87903);let s=(()=>{class x{transform(I,T,R=" "){return(0,o.rd)(I,T,R)}}return x.\u0275fac=function(I){return new(I||x)},x.\u0275pipe=r.\u0275\u0275definePipe({name:"codeSplit",type:x,pure:!0}),x})(),i=(()=>{class x{}return x.\u0275fac=function(I){return new(I||x)},x.\u0275mod=r.\u0275\u0275defineNgModule({type:x}),x.\u0275inj=r.\u0275\u0275defineInjector({imports:[a.CommonModule]}),x})();t(57544);var v=t(70658),m=t(78506),f=(t(29306),t(87956)),n=t(72765),c=t(27302);function y(x,D){if(1&x){const I=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",18),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(I);const R=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(R.onDigital())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd()()}if(2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275property("prefixIcon",I.digitalIcon),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate(I.digitalIncognito?"Ver datos":"Ocultar datos")}}function g(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"span"),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate(null==I.product?null:I.product.publicNumber)}}function l(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"span",19),r.\u0275\u0275text(1),r.\u0275\u0275pipe(2,"codeSplit"),r.\u0275\u0275elementEnd()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(2,1,I.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":I.digitalNumber,4)," ")}}function b(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"bocc-badge",20),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275attribute("bocc-theme",null==I.product||null==I.product.status?null:I.product.status.color),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",null==I.product||null==I.product.status?null:I.product.status.label," ")}}function u(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"div",21),r.\u0275\u0275element(1,"bocc-progress-bar",22),r.\u0275\u0275elementEnd()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("theme",I.progressBarTheme)("width",I.progressBarStatus)}}function d(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"div",23)(1,"label",24),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),r.\u0275\u0275element(5,"bocc-amount",26),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"mbo-button-incognito-mode",27),r.\u0275\u0275elementEnd()()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" ",null==I.product?null:I.product.label," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!I.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("amount",null==I.product?null:I.product.amount)("incognito",I.incognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("actionMode",!0)("hidden",!I.product)}}function C(x,D){if(1&x&&(r.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),r.\u0275\u0275text(3,"Vence"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"span",19),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",29)(7,"label",20),r.\u0275\u0275text(8,"CVC"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(9,"span",19),r.\u0275\u0275text(10),r.\u0275\u0275elementEnd()()()),2&x){const I=r.\u0275\u0275nextContext();r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",I.digitalIncognito?"\u2022\u2022 | \u2022\u2022":I.digitalExpAt," "),r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",I.digitalIncognito?"\u2022\u2022\u2022":I.digitalCVC," ")}}let h=(()=>{class x{constructor(I,T){this.managerPreferences=I,this.digitalService=T,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new r.EventEmitter,this.digital=new r.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:I})=>{this.incognito=I})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:I,value:T})=>{this.product&&this.product.id===I&&this.refreshDigitalState(T)}))}ngOnChanges(I){const{product:T}=I;if(T&&T.currentValue){const R=T.currentValue;this.refreshDigitalState(this.digitalService.request(R.id)),this.activateDigitalCountdown(R)}}ngOnDestroy(){this.unsubscriptions.forEach(I=>I())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(I){const{incognito:T,requiredRequest:R,cvc:W,expirationAt:O,number:z}=I;this.digitalIncognito=T,this.digitalExpAt=O,this.digitalCVC=W,this.digitalNumber=z,this.digitalIcon=T?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=R}activateDigitalCountdown(I){const{countdown$:T}=this.digitalService.request(I.id);T?(this.progressBarRequired=!0,this.progressBarPercent=100,T.subscribe(R=>{this.progressBarRequired=!(R>=v.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-R/v.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return x.\u0275fac=function(I){return new(I||x)(r.\u0275\u0275directiveInject(m.Bx),r.\u0275\u0275directiveInject(f.ZP))},x.\u0275cmp=r.\u0275\u0275defineComponent({type:x,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[r.\u0275\u0275NgOnChangesFeature,r.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(I,T){1&I&&(r.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),r.\u0275\u0275listener("click",function(){return T.onClose()}),r.\u0275\u0275elementStart(3,"span"),r.\u0275\u0275text(4,"Atr\xe1s"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(5,"mbo-currency-toggle",3),r.\u0275\u0275template(6,y,3,2,"button",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"div",5)(8,"div",6),r.\u0275\u0275element(9,"img",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),r.\u0275\u0275text(12),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),r.\u0275\u0275template(15,g,2,1,"span",12),r.\u0275\u0275template(16,l,3,4,"span",13),r.\u0275\u0275template(17,b,2,2,"bocc-badge",14),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(18,u,2,2,"div",15),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(19,d,7,6,"div",16),r.\u0275\u0275template(20,C,11,2,"div",17),r.\u0275\u0275elementEnd()),2&I&&(r.\u0275\u0275classMap(null==T.product?null:T.product.bank.className),r.\u0275\u0275property("color",null==T.product?null:T.product.color),r.\u0275\u0275advance(5),r.\u0275\u0275property("formControl",T.currencyControl)("currencies",T.currencies)("hidden",!(null!=T.product&&T.product.bank.isOccidente)||T.currencies.length<2),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==T.product?null:T.product.isDigital),r.\u0275\u0275advance(3),r.\u0275\u0275property("src",null==T.product?null:T.product.logo,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!T.product),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",(null==T.product?null:T.product.nickname)||(null==T.product?null:T.product.name)," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!T.product),r.\u0275\u0275advance(1),r.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!T.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=T.product&&T.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==T.product?null:T.product.isDigital),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",(null==T.product||null==T.product.status?null:T.product.status.label)&&T.digitalIncognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",T.progressBarRequired),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=T.product&&T.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==T.product?null:T.product.isDigital))},dependencies:[a.CommonModule,a.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,i,s,n.uf,c.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),x})()},94614:(A,E,t)=>{t.d(E,{K:()=>m});var a=t(17007),e=t(30263),o=t(39904),i=(t(29306),t(95437)),p=t(99877);let m=(()=>{class _{constructor(n){this.mboProvider=n,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:n,id:c,parentProduct:y}=this.product;"covered"===n&&y?this.goToPage(y.id,c):this.goToPage(c)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(n,c){this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:n,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:c})}}return _.\u0275fac=function(n){return new(n||_)(p.\u0275\u0275directiveInject(i.ZL))},_.\u0275cmp=p.\u0275\u0275defineComponent({type:_,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(n,c){1&n&&(p.\u0275\u0275elementStart(0,"div",0),p.\u0275\u0275listener("click",function(){return c.onComponent()}),p.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),p.\u0275\u0275text(3),p.\u0275\u0275elementEnd(),p.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),p.\u0275\u0275text(5),p.\u0275\u0275elementEnd()(),p.\u0275\u0275elementStart(6,"div",4),p.\u0275\u0275element(7,"bocc-amount",5),p.\u0275\u0275elementEnd()()),2&n&&(p.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",c.skeleton),p.\u0275\u0275advance(2),p.\u0275\u0275property("active",c.skeleton),p.\u0275\u0275advance(1),p.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.dateFormat," "),p.\u0275\u0275advance(1),p.\u0275\u0275property("active",c.skeleton),p.\u0275\u0275advance(1),p.\u0275\u0275textInterpolate1(" ",null==c.movement?null:c.movement.description," "),p.\u0275\u0275advance(1),p.\u0275\u0275property("hidden",c.skeleton),p.\u0275\u0275advance(1),p.\u0275\u0275property("amount",null==c.movement?null:c.movement.value)("currencyCode",null==c.movement?null:c.movement.currencyCode)("theme",!0))},dependencies:[a.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),_})()},70957:(A,E,t)=>{t.d(E,{K:()=>u});var a=t(15861),r=t(17007),o=t(99877),i=t(30263),p=t(78506),v=t(39904),_=(t(29306),t(87903)),f=t(95437),n=t(27302),c=t(94614);function y(d,C){if(1&d){const h=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",8),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(h);const D=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(D.onRedirectAll())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Ver todos"),o.\u0275\u0275elementEnd()()}}function g(d,C){if(1&d&&(o.\u0275\u0275elementStart(0,"div",5)(1,"label",6),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(3,y,3,0,"button",7),o.\u0275\u0275elementEnd()),2&d){const h=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",(null==h.productMovements||null==h.productMovements.range?null:h.productMovements.range.label)||"Sin resultados"," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==h.productMovements?null:h.productMovements.range)}}function l(d,C){if(1&d&&o.\u0275\u0275element(0,"mbo-product-info-movement",9),2&d){const h=C.$implicit,x=o.\u0275\u0275nextContext();o.\u0275\u0275property("movement",h)("product",x.product)}}function b(d,C){if(1&d&&(o.\u0275\u0275elementStart(0,"mbo-message-empty",10),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&d){const h=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",h.msgError," ")}}let u=(()=>{class d{constructor(h,x){this.mboProvider=h,this.managerProductMovements=x,this.header=!0,this.requesting=!1}ngOnChanges(h){const{currencyCode:x,product:D}=h;if(!this.movements&&(D||x)){const I=x?.currentValue||this.currencyCode,T=D?.currentValue||this.product;this.currentMovements=void 0,(0,_.A2)(T)&&this.requestFirstPage(T,I)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:h,id:x,parentProduct:D}=this.product;this.mboProvider.navigation.next(v.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===h?{productId:D?.id,coveredCardId:x}:{productId:x},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(h,x){var D=this;return(0,a.Z)(function*(){D.requesting=!0,(yield D.managerProductMovements.requestForProduct({product:h,currencyCode:x})).when({success:I=>{D.currentMovements=I},failure:()=>{D.currentMovements=void 0}},()=>{D.requesting=!1})})()}}return d.\u0275fac=function(h){return new(h||d)(o.\u0275\u0275directiveInject(f.ZL),o.\u0275\u0275directiveInject(p.sy))},d.\u0275cmp=o.\u0275\u0275defineComponent({type:d,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(h,x){1&h&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,g,4,2,"div",1),o.\u0275\u0275elementStart(2,"div",2),o.\u0275\u0275template(3,l,1,2,"mbo-product-info-movement",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(4,b,2,1,"mbo-message-empty",4),o.\u0275\u0275elementEnd()),2&h&&(o.\u0275\u0275property("hidden",x.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",x.header),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",null==x.productMovements?null:x.productMovements.firstPage),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==x.productMovements?null:x.productMovements.isEmpty))},dependencies:[r.CommonModule,r.NgForOf,r.NgIf,i.P8,c.K,n.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),d})()},91248:(A,E,t)=>{t.d(E,{I:()=>_});var a=t(17007),e=t(30263),o=t(99877);function i(f,n){if(1&f&&o.\u0275\u0275element(0,"bocc-amount",10),2&f){const c=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("amount",c.value)("currencyCode",c.currencyCode)}}function p(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"span"),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&f){const c=o.\u0275\u0275nextContext().$implicit,y=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",y.incognito||y.section.incognito?c.mask:c.value," ")}}function v(f,n){if(1&f){const c=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(c);const g=o.\u0275\u0275nextContext().$implicit;return o.\u0275\u0275resetView(g.action.click())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&f){const c=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("suffixIcon",c.action.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(c.action.label)}}function m(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275template(5,i,1,2,"bocc-amount",7),o.\u0275\u0275template(6,p,2,1,"span",8),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(7,v,3,2,"button",9),o.\u0275\u0275elementEnd()),2&f){const c=n.$implicit,y=o.\u0275\u0275nextContext();o.\u0275\u0275property("hidden",(null==c?null:c.currencyCode)!==y.currencyCode),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",c.label," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",c.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!c.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",c.action&&!(y.incognito||y.section.incognito))}}let _=(()=>{class f{constructor(){this.currencyCode="COP"}}return f.\u0275fac=function(c){return new(c||f)},f.\u0275cmp=o.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(c,y){1&c&&(o.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),o.\u0275\u0275template(2,m,8,5,"li",2),o.\u0275\u0275elementEnd()()),2&c&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",y.section.datas))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),f})()},4663:(A,E,t)=>{t.d(E,{c:()=>n});var a=t(17007),e=t(99877),s=t(30263),i=t(27302);function p(c,y){1&c&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function v(c,y){if(1&c&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&c){const g=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.title," ")}}function m(c,y){if(1&c){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const u=e.\u0275\u0275restoreView(g).$implicit,d=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(d.onProduct(u))}),e.\u0275\u0275elementEnd()}if(2&c){const g=y.$implicit,l=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",g.color)("icon",g.logo)("title",g.nickname)("number",g.publicNumber)("ghost",l.ghost)("amount",g.amount)("tagAval",g.tagAvalFormat),e.\u0275\u0275attribute("amount-status",l.amountColorProduct(g))}}function _(c,y){1&c&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const f=["*"];let n=(()=>{class c{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(g){return g.amount>0?"success":g.amount<0?"danger":"empty"}onProduct(g){this.select.emit(g)}}return c.\u0275fac=function(g){return new(g||c)},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(g,l){1&g&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,p,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,v,2,1,"div",5),e.\u0275\u0275template(8,m,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,_,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",l.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",l.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!l.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!l.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,s.w_,s.P8,i.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),c})()},13961:(A,E,t)=>{t.d(E,{Z:()=>p});var a=t(17007),e=t(27302),o=t(10119),s=t(99877);let p=(()=>{class v{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(_){this.portal=_}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=s.\u0275\u0275defineComponent({type:v,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(_,f){1&_&&(s.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),s.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"p"),s.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),s.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"p"),s.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),s.\u0275\u0275elementEnd()()()),2&_&&s.\u0275\u0275property("headerActionRight",f.headerAction)("gradient",!0)},dependencies:[a.CommonModule,e.Nu,o.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),v})()},66709:(A,E,t)=>{t.d(E,{s:()=>p});var a=t(17007),r=t(99877),e=t(30263),o=t(87542);let s=(()=>{class v{ngBoccPortal(_){}}return v.\u0275fac=function(_){return new(_||v)},v.\u0275cmp=r.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(_,f){1&_&&(r.\u0275\u0275elementStart(0,"div",0)(1,"label",1),r.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),r.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),r.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(11,"p",4),r.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),r.\u0275\u0275element(13,"br"),r.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),r.\u0275\u0275element(15,"br"),r.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),r.\u0275\u0275elementStart(17,"span"),r.\u0275\u0275text(18,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(19," Configuraci\xf3n "),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(22," Seguridad "),r.\u0275\u0275elementStart(23,"span"),r.\u0275\u0275text(24,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(25," Activar Token Mobile."),r.\u0275\u0275element(26,"br"),r.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),r.\u0275\u0275elementEnd()()()())},dependencies:[a.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),v})();const i=["*"];let p=(()=>{class v{constructor(_,f){this.ref=_,this.bottomSheetService=f,this.verifying=!1,this.tokenLength=o.Xi,this.code=new r.EventEmitter,this.tokenControls=new o.b2}ngOnInit(){const _=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(s),setTimeout(()=>{_?.focus()},120)}onAutocomplete(_){this.code.emit(_)}onInfo(){this.infoSheet?.open()}}return v.\u0275fac=function(_){return new(_||v)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(e.fG))},v.\u0275cmp=r.\u0275\u0275defineComponent({type:v,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(_,f){1&_&&(r.\u0275\u0275projectionDef(),r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275projection(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"p",2),r.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",2),r.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),r.\u0275\u0275elementStart(7,"a"),r.\u0275\u0275text(8),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(9,". "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",3),r.\u0275\u0275element(11,"img",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),r.\u0275\u0275listener("autocomplete",function(c){return f.onAutocomplete(c)}),r.\u0275\u0275text(13," Ingresa tu clave "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"button",6),r.\u0275\u0275listener("click",function(){return f.onInfo()}),r.\u0275\u0275elementStart(15,"span"),r.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()()()),2&_&&(r.\u0275\u0275advance(8),r.\u0275\u0275textInterpolate1("",f.tokenLength," d\xedgitos"),r.\u0275\u0275advance(4),r.\u0275\u0275property("disabled",f.verifying)("formControls",f.tokenControls),r.\u0275\u0275advance(2),r.\u0275\u0275property("disabled",f.verifying))},dependencies:[a.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),v})()},22816:(A,E,t)=>{t.d(E,{S:()=>a});class a{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);