(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4858],{54858:(ke,fe,I)=>{I.r(fe),I.d(fe,{ApolloCache:()=>Yt,ApolloClient:()=>pn,ApolloError:()=>he,ApolloLink:()=>V.i,Cache:()=>Kt,DocumentTransform:()=>qt,HttpLink:()=>kr,InMemoryCache:()=>qn,MissingFieldError:()=>Ht,NetworkStatus:()=>pe,Observable:()=>j.y,ObservableQuery:()=>gt,checkFetcher:()=>vr,concat:()=>pa,createHttpLink:()=>Sr,createSignalIfSupported:()=>va,defaultDataIdFromObject:()=>$t,defaultPrinter:()=>St,disableExperimentalFragmentVariables:()=>Gn,disableFragmentWarnings:()=>Wn,empty:()=>fa,enableExperimentalFragmentVariables:()=>Bn,execute:()=>H,fallbackHttpConfig:()=>yr,from:()=>la,fromError:()=>vt,fromPromise:()=>ya,gql:()=>$e,isApolloError:()=>R,isNetworkRequestSettled:()=>qi,isReference:()=>K.Yk,makeReference:()=>K.kQ,makeVar:()=>ln,mergeOptions:()=>bt,parseAndCheckHttpResponse:()=>pr,resetCaches:()=>zn,rewriteURIForGET:()=>mr,selectHttpOptionsAndBody:()=>ni,selectHttpOptionsAndBodyInternal:()=>kt,selectURI:()=>ae,serializeFetchParameter:()=>X,setLogVerbosity:()=>Ln.U6,split:()=>ha,throwServerError:()=>y,toPromise:()=>da});var c=I(97582),D=I(11253),V=I(40484),H=V.i.execute,W=I(56497),Q=I(50642);function $(n,e){var t=n.directives;return!t||!t.length||function N(n){var e=[];return n&&n.length&&n.forEach(function(t){if(function F(n){var e=n.name.value;return"skip"===e||"include"===e}(t)){var r=t.arguments,i=t.name.value;(0,D.kG)(r&&1===r.length,71,i);var a=r[0];(0,D.kG)(a.name&&"if"===a.name.value,72,i);var o=a.value;(0,D.kG)(o&&("Variable"===o.kind||"BooleanValue"===o.kind),73,i),e.push({directive:t,ifArgument:a})}}),e}(t).every(function(r){var i=r.directive,a=r.ifArgument,o=!1;return"Variable"===a.value.kind?(0,D.kG)(void 0!==(o=e&&e[a.value.name.value]),70,i.name.value):o=a.value.value,"skip"===i.name.value?!o:o})}function A(n,e,t){var r=new Set(n),i=r.size;return(0,Q.visit)(e,{Directive:function(a){if(r.delete(a.name.value)&&(!t||!r.size))return Q.BREAK}}),t?!r.size:r.size<i}function E(n){return n&&A(["client","export"],n,!0)}var j=I(64302),X=function(n,e){var t;try{t=JSON.stringify(n)}catch(i){var r=(0,D._K)(40,e,i.message);throw r.parseError=i,r}return t},ae=function(n,e){return n.getContext().uri||("function"==typeof e?e(n):e||"/graphql")},L="ReactNative"==(0,D.wY)(function(){return navigator.product}),ue="function"==typeof WeakMap&&!(L&&!global.HermesInternal),de="function"==typeof WeakSet,Ee="function"==typeof Symbol&&"function"==typeof Symbol.for,Re=Ee&&Symbol.asyncIterator;function q(n){var e={next:function(){return n.read()}};return Re&&(e[Symbol.asyncIterator]=function(){return this}),e}function d(n){var e=n;if(function ee(n){return!!n.body}(n)&&(e=n.body),function U(n){return!(!Re||!n[Symbol.asyncIterator])}(e))return function B(n){var e,t=n[Symbol.asyncIterator]();return(e={next:function(){return t.next()}})[Symbol.asyncIterator]=function(){return this},e}(e);if(function Y(n){return!!n.getReader}(e))return q(e.getReader());if(function ne(n){return!!n.stream}(e))return q(e.stream().getReader());if(function oe(n){return!!n.arrayBuffer}(e))return function P(n){var e=!1,t={next:function(){return e?Promise.resolve({value:void 0,done:!0}):(e=!0,new Promise(function(r,i){n.then(function(a){r({value:a,done:!1})}).catch(i)}))}};return Re&&(t[Symbol.asyncIterator]=function(){return this}),t}(e.arrayBuffer());if(function ye(n){return!!n.pipe}(e))return function g(n){var e=null,t=null,r=!1,i=[],a=[];function o(p){if(!t){if(a.length){var h=a.shift();if(Array.isArray(h)&&h[0])return h[0]({value:p,done:!1})}i.push(p)}}function s(p){t=p,a.slice().forEach(function(v){v[1](p)}),!e||e()}function u(){r=!0,a.slice().forEach(function(h){h[0]({value:void 0,done:!0})}),!e||e()}e=function(){e=null,n.removeListener("data",o),n.removeListener("error",s),n.removeListener("end",u),n.removeListener("finish",u),n.removeListener("close",u)},n.on("data",o),n.on("error",s),n.on("end",u),n.on("finish",u),n.on("close",u);var l={next:function(){return function f(){return new Promise(function(p,h){return t?h(t):i.length?p({value:i.shift(),done:!1}):r?p({value:void 0,done:!0}):void a.push([p,h])})}()}};return Re&&(l[Symbol.asyncIterator]=function(){return this}),l}(e);throw new Error("Unknown body type for responseIterator. Please pass a streamable response.")}(0,D.wY)(function(){return window.document.createElement}),(0,D.wY)(function(){return navigator.userAgent.indexOf("jsdom")>=0});var y=function(n,e,t){var r=new Error(t);throw r.name="ServerError",r.response=n,r.statusCode=n.status,r.result=e,r},m=I(27062),O=Symbol();function R(n){return n.hasOwnProperty("graphQLErrors")}var re=function(n){var e=(0,c.ev)((0,c.ev)((0,c.ev)([],n.graphQLErrors,!0),n.clientErrors,!0),n.protocolErrors,!0);return n.networkError&&e.push(n.networkError),e.map(function(t){return(0,m.s)(t)&&t.message||"Error message not found."}).join("\n")},he=function(n){function e(t){var r=t.graphQLErrors,i=t.protocolErrors,a=t.clientErrors,o=t.networkError,s=t.errorMessage,u=t.extraInfo,f=n.call(this,s)||this;return f.name="ApolloError",f.graphQLErrors=r||[],f.protocolErrors=i||[],f.clientErrors=a||[],f.networkError=o||null,f.message=s||re(f),f.extraInfo=u,f.cause=(0,c.ev)((0,c.ev)((0,c.ev)([o],r||[],!0),i||[],!0),a||[],!0).find(function(l){return!!l})||null,f.__proto__=e.prototype,f}return(0,c.ZT)(e,n),e}(Error),le=Array.isArray;function te(n){return Array.isArray(n)&&n.length>0}var _e=Object.prototype.hasOwnProperty;function Se(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return je(n)}function je(n){var e=n[0]||{},t=n.length;if(t>1)for(var r=new we,i=1;i<t;++i)e=r.merge(e,n[i]);return e}var me=function(n,e,t){return this.merge(n[t],e[t])},we=function(){function n(e){void 0===e&&(e=me),this.reconciler=e,this.isObject=m.s,this.pastCopies=new Set}return n.prototype.merge=function(e,t){for(var r=this,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];return(0,m.s)(t)&&(0,m.s)(e)?(Object.keys(t).forEach(function(o){if(_e.call(e,o)){var s=e[o];if(t[o]!==s){var u=r.reconciler.apply(r,(0,c.ev)([e,t,o],i,!1));u!==s&&((e=r.shallowCopyForMerge(e))[o]=u)}}else(e=r.shallowCopyForMerge(e))[o]=t[o]}),e):t},n.prototype.shallowCopyForMerge=function(e){return(0,m.s)(e)&&(this.pastCopies.has(e)||(e=Array.isArray(e)?e.slice(0):(0,c.pi)({__proto__:Object.getPrototypeOf(e)},e),this.pastCopies.add(e))),e},n}();function De(n){return"incremental"in n}function Jn(n){return(0,m.s)(n)&&"payload"in n}function fr(n,e){var t=n,r=new we;return De(e)&&te(e.incremental)&&e.incremental.forEach(function(i){for(var a=i.data,o=i.path,s=o.length-1;s>=0;--s){var u=o[s],l=isNaN(+u)?{}:[];l[u]=a,a=l}t=r.merge(t,a)}),t}var lr=Object.prototype.hasOwnProperty;function Zn(n){var e={};return n.split("\n").forEach(function(t){var r=t.indexOf(":");if(r>-1){var i=t.slice(0,r).trim().toLowerCase(),a=t.slice(r+1).trim();e[i]=a}}),e}function hr(n,e){n.status>=300&&y(n,function(){try{return JSON.parse(e)}catch{return e}}(),"Response not successful: Received status code ".concat(n.status));try{return JSON.parse(e)}catch(i){var r=i;throw r.name="ServerParseError",r.response=n,r.statusCode=n.status,r.bodyText=e,r}}function pr(n){return function(e){return e.text().then(function(t){return hr(e,t)}).then(function(t){return!Array.isArray(t)&&!lr.call(t,"data")&&!lr.call(t,"errors")&&y(e,t,"Server response was missing for query '".concat(Array.isArray(n)?n.map(function(r){return r.operationName}):n.operationName,"'.")),t})}}var tt,vr=function(n){if(!n&&typeof fetch>"u")throw(0,D._K)(38)},dr=I(19162),We=I(64171),ht=I(72905),pt=Object.assign(function(n){var e=tt.get(n);return e||(e=(0,Q.print)(n),tt.set(n,e)),e},{reset:function(){tt=new dr.s(We.Q.print||2e3)}});pt.reset(),!1!==globalThis.__DEV__&&(0,ht.zP)("print",function(){return tt?tt.size:0});var yr={http:{includeQuery:!0,includeExtensions:!1,preserveHeaderCase:!1},headers:{accept:"*/*","content-type":"application/json"},options:{method:"POST"}},St=function(n,e){return e(n)};function ni(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];return t.unshift(e),kt.apply(void 0,(0,c.ev)([n,St],t,!1))}function kt(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var i={},a={};t.forEach(function(p){i=(0,c.pi)((0,c.pi)((0,c.pi)({},i),p.options),{headers:(0,c.pi)((0,c.pi)({},i.headers),p.headers)}),p.credentials&&(i.credentials=p.credentials),a=(0,c.pi)((0,c.pi)({},a),p.http)}),i.headers&&(i.headers=function ii(n,e){if(!e){var t={};return Object.keys(Object(n)).forEach(function(a){t[a.toLowerCase()]=n[a]}),t}var r={};Object.keys(Object(n)).forEach(function(a){r[a.toLowerCase()]={originalName:a,value:n[a]}});var i={};return Object.keys(r).forEach(function(a){i[r[a].originalName]=r[a].value}),i}(i.headers,a.preserveHeaderCase));var f=n.query,l={operationName:n.operationName,variables:n.variables};return a.includeExtensions&&(l.extensions=n.extensions),a.includeQuery&&(l.query=e(f,pt)),{options:i,body:l}}function mr(n,e){var t=[],r=function(p,h){t.push("".concat(p,"=").concat(encodeURIComponent(h)))};if("query"in e&&r("query",e.query),e.operationName&&r("operationName",e.operationName),e.variables){var i=void 0;try{i=X(e.variables,"Variables map")}catch(p){return{parseError:p}}r("variables",i)}if(e.extensions){var a=void 0;try{a=X(e.extensions,"Extensions map")}catch(p){return{parseError:p}}r("extensions",a)}var o="",s=n,u=n.indexOf("#");-1!==u&&(o=n.substr(u),s=n.substr(0,u));var f=-1===s.indexOf("?")?"?":"&";return{newURI:s+f+t.join("&")+o}}function vt(n){return new j.y(function(e){e.error(n)})}var ge=I(70591),K=I(97634),Ae=I(89661),gr={kind:Q.Kind.FIELD,name:{kind:Q.Kind.NAME,value:"__typename"}};function br(n,e){return!n||n.selectionSet.selections.every(function(t){return t.kind===Q.Kind.FRAGMENT_SPREAD&&br(e[t.name.value],e)})}function Dt(n){return br((0,ge.$H)(n)||(0,ge.pD)(n),(0,Ae.F)((0,ge.kU)(n)))?null:n}function wr(n){var e=new Map;return function(r){void 0===r&&(r=n);var i=e.get(r);return i||e.set(r,i={variables:new Set,fragmentSpreads:new Set}),i}}function Tt(n,e){(0,ge.A$)(e);for(var t=wr(""),r=wr(""),i=function(b){for(var x=0,S=void 0;x<b.length&&(S=b[x]);++x)if(!le(S)){if(S.kind===Q.Kind.OPERATION_DEFINITION)return t(S.name&&S.name.value);if(S.kind===Q.Kind.FRAGMENT_DEFINITION)return r(S.name.value)}return!1!==globalThis.__DEV__&&D.kG.error(86),null},a=0,o=e.definitions.length-1;o>=0;--o)e.definitions[o].kind===Q.Kind.OPERATION_DEFINITION&&++a;var s=function _r(n){var e=new Map,t=new Map;return n.forEach(function(r){r&&(r.name?e.set(r.name,r):r.test&&t.set(r.test,r))}),function(r){var i=e.get(r.name.value);return!i&&t.size&&t.forEach(function(a,o){o(r)&&(i=a)}),i}}(n),u=function(b){return te(b)&&b.map(s).some(function(x){return x&&x.remove})},f=new Map,l=!1,p={enter:function(b){if(u(b.directives))return l=!0,null}},h=(0,Q.visit)(e,{Field:p,InlineFragment:p,VariableDefinition:{enter:function(){return!1}},Variable:{enter:function(b,x,S,G,z){var Z=i(z);Z&&Z.variables.add(b.name.value)}},FragmentSpread:{enter:function(b,x,S,G,z){if(u(b.directives))return l=!0,null;var Z=i(z);Z&&Z.fragmentSpreads.add(b.name.value)}},FragmentDefinition:{enter:function(b,x,S,G){f.set(JSON.stringify(G),b)},leave:function(b,x,S,G){return b===f.get(JSON.stringify(G))?b:a>0&&b.selectionSet.selections.every(function(Z){return Z.kind===Q.Kind.FIELD&&"__typename"===Z.name.value})?(r(b.name.value).removed=!0,l=!0,null):void 0}},Directive:{leave:function(b){if(s(b))return l=!0,null}}});if(!l)return e;var v=function(b){return b.transitiveVars||(b.transitiveVars=new Set(b.variables),b.removed||b.fragmentSpreads.forEach(function(x){v(r(x)).transitiveVars.forEach(function(S){b.transitiveVars.add(S)})})),b},w=new Set;h.definitions.forEach(function(b){b.kind===Q.Kind.OPERATION_DEFINITION?v(t(b.name&&b.name.value)).fragmentSpreads.forEach(function(x){w.add(x)}):b.kind===Q.Kind.FRAGMENT_DEFINITION&&0===a&&!r(b.name.value).removed&&w.add(b.name.value)}),w.forEach(function(b){v(r(b)).fragmentSpreads.forEach(function(x){w.add(x)})});var k={enter:function(b){if(function(b){return!(w.has(b)&&!r(b).removed)}(b.name.value))return null}};return Dt((0,Q.visit)(h,{FragmentSpread:k,FragmentDefinition:k,OperationDefinition:{leave:function(b){if(b.variableDefinitions){var x=v(t(b.name&&b.name.value)).transitiveVars;if(x.size<b.variableDefinitions.length)return(0,c.pi)((0,c.pi)({},b),{variableDefinitions:b.variableDefinitions.filter(function(S){return x.has(S.variable.name.value)})})}}}}))}var Ft=Object.assign(function(n){return(0,Q.visit)(n,{SelectionSet:{enter:function(e,t,r){if(!r||r.kind!==Q.Kind.OPERATION_DEFINITION){var i=e.selections;if(i&&!i.some(function(s){return(0,K.My)(s)&&("__typename"===s.name.value||0===s.name.value.lastIndexOf("__",0))})){var o=r;if(!((0,K.My)(o)&&o.directives&&o.directives.some(function(s){return"export"===s.name.value})))return(0,c.pi)((0,c.pi)({},e),{selections:(0,c.ev)((0,c.ev)([],i,!0),[gr],!1)})}}}}})},{added:function(n){return n===gr}});function fi(n){return"query"===(0,ge.p$)(n).operation?n:(0,Q.visit)(n,{OperationDefinition:{enter:function(i){return(0,c.pi)((0,c.pi)({},i),{operation:"query"})}}})}function Or(n){return(0,ge.A$)(n),Tt([{test:function(t){return"client"===t.name.value},remove:!0}],n)}var Er=(0,D.wY)(function(){return fetch}),Sr=function(n){void 0===n&&(n={});var e=n.uri,t=void 0===e?"/graphql":e,r=n.fetch,i=n.print,a=void 0===i?St:i,o=n.includeExtensions,s=n.preserveHeaderCase,u=n.useGETForQueries,f=n.includeUnusedVariables,l=void 0!==f&&f,p=(0,c._T)(n,["uri","fetch","print","includeExtensions","preserveHeaderCase","useGETForQueries","includeUnusedVariables"]);!1!==globalThis.__DEV__&&vr(r||Er);var h={http:{includeExtensions:o,preserveHeaderCase:s},options:p.fetchOptions,credentials:p.credentials,headers:p.headers};return new V.i(function(v){var w=ae(v,t),_=v.getContext(),k={};if(_.clientAwareness){var b=_.clientAwareness,x=b.name,S=b.version;x&&(k["apollographql-client-name"]=x),S&&(k["apollographql-client-version"]=S)}var G=(0,c.pi)((0,c.pi)({},k),_.headers),z={http:_.http,options:_.fetchOptions,credentials:_.credentials,headers:G};if(A(["client"],v.query)){var Z=Or(v.query);if(!Z)return vt(new Error("HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`."));v.query=Z}var Oe,ce=kt(v,a,yr,h,z),se=ce.options,ve=ce.body;ve.variables&&!l&&(ve.variables=function ai(n,e){var t=(0,c.pi)({},n),r=new Set(Object.keys(n));return(0,Q.visit)(e,{Variable:function(i,a,o){o&&"VariableDefinition"!==o.kind&&r.delete(i.name.value)}}),r.forEach(function(i){delete t[i]}),t}(ve.variables,v.query)),!se.signal&&typeof AbortController<"u"&&(Oe=new AbortController,se.signal=Oe.signal);var Ce,Pe="OperationDefinition"===(Ce=(0,ge.p$)(v.query)).kind&&"subscription"===Ce.operation,ze=A(["defer"],v.query);if(u&&!v.query.definitions.some(function(Ce){return"OperationDefinition"===Ce.kind&&"mutation"===Ce.operation})&&(se.method="GET"),ze||Pe){se.headers=se.headers||{};var ur="multipart/mixed;";Pe&&ze&&!1!==globalThis.__DEV__&&D.kG.warn(39),Pe?ur+="boundary=graphql;subscriptionSpec=1.0,application/json":ze&&(ur+="deferSpec=20220824,application/json"),se.headers.accept=ur}if("GET"===se.method){var Un=mr(w,ve),wa=Un.newURI,Kn=Un.parseError;if(Kn)return vt(Kn);w=wa}else try{se.body=X(ve,"Payload")}catch(Ce){return vt(Ce)}return new j.y(function(Ce){var Oa=r||(0,D.wY)(function(){return fetch})||Er,Yn=Ce.next.bind(Ce);return Oa(w,se).then(function(et){var cr;v.setContext({response:et});var Hn=null===(cr=et.headers)||void 0===cr?void 0:cr.get("content-type");return null!==Hn&&/^multipart\/mixed/i.test(Hn)?function $n(n,e){return(0,c.mG)(this,void 0,void 0,function(){var t,r,i,a,o,s,u,f,l,p,h,v,w,_,k,b,x,S,G,z,Z,ce,se,ve;return(0,c.Jh)(this,function(Oe){switch(Oe.label){case 0:if(void 0===TextDecoder)throw new Error("TextDecoder must be defined in the environment: please import a polyfill.");t=new TextDecoder("utf-8"),r=null===(ve=n.headers)||void 0===ve?void 0:ve.get("content-type"),a=r?.includes(i="boundary=")?r?.substring(r?.indexOf(i)+i.length).replace(/['"]/g,"").replace(/\;(.*)/gm,"").trim():"-",o="\r\n--".concat(a),s="",u=d(n),f=!0,Oe.label=1;case 1:return f?[4,u.next()]:[3,3];case 2:for(l=Oe.sent(),h=l.done,v="string"==typeof(p=l.value)?p:t.decode(p),w=s.length-o.length+1,f=!h,_=(s+=v).indexOf(o,w);_>-1;){if(k=void 0,ce=[s.slice(0,_),s.slice(_+o.length)],s=ce[1],b=(k=ce[0]).indexOf("\r\n\r\n"),x=Zn(k.slice(0,b)),(S=x["content-type"])&&-1===S.toLowerCase().indexOf("application/json"))throw new Error("Unsupported patch content type: application/json is required.");if(G=k.slice(b))if(z=hr(n,G),Object.keys(z).length>1||"data"in z||"incremental"in z||"errors"in z||"payload"in z)if(Jn(z)){if(Z={},"payload"in z){if(1===Object.keys(z).length&&null===z.payload)return[2];Z=(0,c.pi)({},z.payload)}"errors"in z&&(Z=(0,c.pi)((0,c.pi)({},Z),{extensions:(0,c.pi)((0,c.pi)({},"extensions"in Z?Z.extensions:null),(se={},se[O]=z.errors,se))})),e(Z)}else e(z);else if(1===Object.keys(z).length&&"hasNext"in z&&!z.hasNext)return[2];_=s.indexOf(o)}return[3,1];case 3:return[2]}})})}(et,Yn):pr(v)(et).then(Yn)}).then(function(){Oe=void 0,Ce.complete()}).catch(function(et){Oe=void 0,function Xn(n,e){n.result&&n.result.errors&&n.result.data&&e.next(n.result),e.error(n)}(et,Ce)}),function(){Oe&&Oe.abort()}})})},kr=function(n){function e(t){void 0===t&&(t={});var r=n.call(this,Sr(t).request)||this;return r.options=t,r}return(0,c.ZT)(e,n),e}(V.i);const{toString:Dr,hasOwnProperty:li}=Object.prototype,Tr=Function.prototype.toString,Pt=new Map;function be(n,e){try{return Rt(n,e)}finally{Pt.clear()}}const Fr=be;function Rt(n,e){if(n===e)return!0;const t=Dr.call(n);if(t!==Dr.call(e))return!1;switch(t){case"[object Array]":if(n.length!==e.length)return!1;case"[object Object]":{if(Rr(n,e))return!0;const i=Pr(n),a=Pr(e),o=i.length;if(o!==a.length)return!1;for(let s=0;s<o;++s)if(!li.call(e,i[s]))return!1;for(let s=0;s<o;++s){const u=i[s];if(!Rt(n[u],e[u]))return!1}return!0}case"[object Error]":return n.name===e.name&&n.message===e.message;case"[object Number]":if(n!=n)return e!=e;case"[object Boolean]":case"[object Date]":return+n==+e;case"[object RegExp]":case"[object String]":return n==`${e}`;case"[object Map]":case"[object Set]":{if(n.size!==e.size)return!1;if(Rr(n,e))return!0;const i=n.entries(),a="[object Map]"===t;for(;;){const o=i.next();if(o.done)break;const[s,u]=o.value;if(!e.has(s)||a&&!Rt(u,e.get(s)))return!1}return!0}case"[object Uint16Array]":case"[object Uint8Array]":case"[object Uint32Array]":case"[object Int32Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object ArrayBuffer]":n=new Uint8Array(n),e=new Uint8Array(e);case"[object DataView]":{let i=n.byteLength;if(i===e.byteLength)for(;i--&&n[i]===e[i];);return-1===i}case"[object AsyncFunction]":case"[object GeneratorFunction]":case"[object AsyncGeneratorFunction]":case"[object Function]":{const i=Tr.call(n);return i===Tr.call(e)&&!function vi(n,e){const t=n.length-e.length;return t>=0&&n.indexOf(e,t)===t}(i,pi)}}return!1}function Pr(n){return Object.keys(n).filter(hi,n)}function hi(n){return void 0!==this[n]}const pi="{ [native code] }";function Rr(n,e){let t=Pt.get(n);if(t){if(t.has(e))return!0}else Pt.set(n,t=new Set);return t.add(e),!1}var Be=I(13395);const di=()=>Object.create(null),{forEach:yi,slice:Mr}=Array.prototype,{hasOwnProperty:mi}=Object.prototype;class qe{constructor(e=!0,t=di){this.weakness=e,this.makeData=t}lookup(){return this.lookupArray(arguments)}lookupArray(e){let t=this;return yi.call(e,r=>t=t.getChildTrie(r)),mi.call(t,"data")?t.data:t.data=this.makeData(Mr.call(e))}peek(){return this.peekArray(arguments)}peekArray(e){let t=this;for(let r=0,i=e.length;t&&r<i;++r){const a=t.mapFor(e[r],!1);t=a&&a.get(e[r])}return t&&t.data}remove(){return this.removeArray(arguments)}removeArray(e){let t;if(e.length){const r=e[0],i=this.mapFor(r,!1),a=i&&i.get(r);a&&(t=a.removeArray(Mr.call(e,1)),!a.data&&!a.weak&&(!a.strong||!a.strong.size)&&i.delete(r))}else t=this.data,delete this.data;return t}getChildTrie(e){const t=this.mapFor(e,!0);let r=t.get(e);return r||t.set(e,r=new qe(this.weakness,this.makeData)),r}mapFor(e,t){return this.weakness&&function gi(n){switch(typeof n){case"object":if(null===n)break;case"function":return!0}return!1}(e)?this.weak||(t?this.weak=new WeakMap:void 0):this.strong||(t?this.strong=new Map:void 0)}}var Cr=I(5058);const bi=()=>Object.create(null),{forEach:_i,slice:wi}=Array.prototype,{hasOwnProperty:Oi}=Object.prototype;class Mt{constructor(e=!0,t=bi){this.weakness=e,this.makeData=t}lookup(...e){return this.lookupArray(e)}lookupArray(e){let t=this;return _i.call(e,r=>t=t.getChildTrie(r)),Oi.call(t,"data")?t.data:t.data=this.makeData(wi.call(e))}peek(...e){return this.peekArray(e)}peekArray(e){let t=this;for(let r=0,i=e.length;t&&r<i;++r){const a=this.weakness&&Ir(e[r])?t.weak:t.strong;t=a&&a.get(e[r])}return t&&t.data}getChildTrie(e){const t=this.weakness&&Ir(e)?this.weak||(this.weak=new WeakMap):this.strong||(this.strong=new Map);let r=t.get(e);return r||t.set(e,r=new Mt(this.weakness,this.makeData)),r}}function Ir(n){switch(typeof n){case"object":if(null===n)break;case"function":return!0}return!1}var Ei=I(38678);let Fe=null;const jr={};let Si=1;function xr(n){try{return n()}catch{}}const Ct="@wry/context:Slot",Qr=xr(()=>globalThis)||xr(()=>global)||Object.create(null),It=Qr[Ct]||Array[Ct]||function(n){try{Object.defineProperty(Qr,Ct,{value:n,enumerable:!1,writable:!1,configurable:!0})}finally{return n}}(class{constructor(){this.id=["slot",Si++,Date.now(),Math.random().toString(36).slice(2)].join(":")}hasValue(){for(let e=Fe;e;e=e.parent)if(this.id in e.slots){const t=e.slots[this.id];if(t===jr)break;return e!==Fe&&(Fe.slots[this.id]=t),!0}return Fe&&(Fe.slots[this.id]=jr),!1}getValue(){if(this.hasValue())return Fe.slots[this.id]}withValue(e,t,r,i){const o=Fe;Fe={parent:o,slots:{__proto__:null,[this.id]:e}};try{return t.apply(i,r)}finally{Fe=o}}static bind(e){const t=Fe;return function(){const r=Fe;try{return Fe=t,e.apply(this,arguments)}finally{Fe=r}}}static noContext(e,t,r){if(!Fe)return e.apply(r,t);{const i=Fe;try{return Fe=null,e.apply(r,t)}finally{Fe=i}}}}),rt=new It,{hasOwnProperty:Fi}=Object.prototype,xt=Array.from||function(n){const e=[];return n.forEach(t=>e.push(t)),e};function dt(n){const{unsubscribe:e}=n;"function"==typeof e&&(n.unsubscribe=void 0,e())}const nt=[],Pi=100;function Ge(n,e){if(!n)throw new Error(e||"assertion failure")}function qr(n,e){const t=n.length;return t>0&&t===e.length&&n[t-1]===e[t-1]}function Lr(n){switch(n.length){case 0:throw new Error("unknown value");case 1:return n[0];case 2:throw n[1]}}function Vr(n){return n.slice(0)}let Ri=(()=>{class n{constructor(t){this.fn=t,this.parents=new Set,this.childValues=new Map,this.dirtyChildren=null,this.dirty=!0,this.recomputing=!1,this.value=[],this.deps=null,++n.count}peek(){if(1===this.value.length&&!Le(this))return Nr(this),this.value[0]}recompute(t){return Ge(!this.recomputing,"already recomputing"),Nr(this),Le(this)?function Mi(n,e){return Kr(n),rt.withValue(n,Ci,[n,e]),function ji(n,e){if("function"==typeof n.subscribe)try{dt(n),n.unsubscribe=n.subscribe.apply(null,e)}catch{return n.setDirty(),!1}return!0}(n,e)&&function Ii(n){n.dirty=!1,!Le(n)&&Wr(n)}(n),Lr(n.value)}(this,t):Lr(this.value)}setDirty(){this.dirty||(this.dirty=!0,zr(this),dt(this))}dispose(){this.setDirty(),Kr(this),Qt(this,(t,r)=>{t.setDirty(),Yr(t,this)})}forget(){this.dispose()}dependOn(t){t.add(this),this.deps||(this.deps=nt.pop()||new Set),this.deps.add(t)}forgetDeps(){this.deps&&(xt(this.deps).forEach(t=>t.delete(this)),this.deps.clear(),nt.push(this.deps),this.deps=null)}}return n.count=0,n})();function Nr(n){const e=rt.getValue();if(e)return n.parents.add(e),e.childValues.has(n)||e.childValues.set(n,[]),Le(n)?Br(e,n):Gr(e,n),e}function Ci(n,e){n.recomputing=!0;const{normalizeResult:t}=n;let r;t&&1===n.value.length&&(r=Vr(n.value)),n.value.length=0;try{if(n.value[0]=n.fn.apply(null,e),t&&r&&!qr(r,n.value))try{n.value[0]=t(n.value[0],r[0])}catch{}}catch(i){n.value[1]=i}n.recomputing=!1}function Le(n){return n.dirty||!(!n.dirtyChildren||!n.dirtyChildren.size)}function zr(n){Qt(n,Br)}function Wr(n){Qt(n,Gr)}function Qt(n,e){const t=n.parents.size;if(t){const r=xt(n.parents);for(let i=0;i<t;++i)e(r[i],n)}}function Br(n,e){Ge(n.childValues.has(e)),Ge(Le(e));const t=!Le(n);if(n.dirtyChildren){if(n.dirtyChildren.has(e))return}else n.dirtyChildren=nt.pop()||new Set;n.dirtyChildren.add(e),t&&zr(n)}function Gr(n,e){Ge(n.childValues.has(e)),Ge(!Le(e));const t=n.childValues.get(e);0===t.length?n.childValues.set(e,Vr(e.value)):qr(t,e.value)||n.setDirty(),Ur(n,e),!Le(n)&&Wr(n)}function Ur(n,e){const t=n.dirtyChildren;t&&(t.delete(e),0===t.size&&(nt.length<Pi&&nt.push(t),n.dirtyChildren=null))}function Kr(n){n.childValues.size>0&&n.childValues.forEach((e,t)=>{Yr(n,t)}),n.forgetDeps(),Ge(null===n.dirtyChildren)}function Yr(n,e){e.parents.delete(n),n.childValues.delete(e),Ur(n,e)}const xi={setDirty:!0,dispose:!0,forget:!0};function Hr(n){const e=new Map,t=n&&n.subscribe;function r(i){const a=rt.getValue();if(a){let o=e.get(i);o||e.set(i,o=new Set),a.dependOn(o),"function"==typeof t&&(dt(o),o.unsubscribe=t(i))}}return r.dirty=function(a,o){const s=e.get(a);if(s){const u=o&&Fi.call(xi,o)?o:"setDirty";xt(s).forEach(f=>f[u]()),e.delete(a),dt(s)}},r}let Jr;function Qi(...n){return(Jr||(Jr=new Mt("function"==typeof WeakMap))).lookupArray(n)}const At=new Set;function it(n,{max:e=Math.pow(2,16),keyArgs:t,makeCacheKey:r=Qi,normalizeResult:i,subscribe:a,cache:o=Ei.e}=Object.create(null)){const s="function"==typeof o?new o(e,h=>h.dispose()):o,u=function(){const h=r.apply(null,t?t.apply(null,arguments):arguments);if(void 0===h)return n.apply(null,arguments);let v=s.get(h);v||(s.set(h,v=new Ri(n)),v.normalizeResult=i,v.subscribe=a,v.forget=()=>s.delete(h));const w=v.recompute(Array.prototype.slice.call(arguments));return s.set(h,v),At.add(s),rt.hasValue()||(At.forEach(_=>_.clean()),At.clear()),w};function f(h){const v=h&&s.get(h);v&&v.setDirty()}function l(h){const v=h&&s.get(h);if(v)return v.peek()}function p(h){return!!h&&s.delete(h)}return Object.defineProperty(u,"size",{get:()=>s.size,configurable:!1,enumerable:!1}),Object.freeze(u.options={max:e,keyArgs:t,makeCacheKey:r,normalizeResult:i,subscribe:a,cache:s}),u.dirtyKey=f,u.dirty=function(){f(r.apply(null,arguments))},u.peekKey=l,u.peek=function(){return l(r.apply(null,arguments))},u.forgetKey=p,u.forget=function(){return p(r.apply(null,arguments))},u.makeCacheKey=r,u.getKey=t?function(){return r.apply(null,t.apply(null,arguments))}:r,Object.freeze(u)}function Ai(n){return n}var qt=function(){function n(e,t){void 0===t&&(t=Object.create(null)),this.resultCache=de?new WeakSet:new Set,this.transform=e,t.getCacheKey&&(this.getCacheKey=t.getCacheKey),this.cached=!1!==t.cache,this.resetCache()}return n.prototype.getCacheKey=function(e){return[e]},n.identity=function(){return new n(Ai,{cache:!1})},n.split=function(e,t,r){return void 0===r&&(r=n.identity()),Object.assign(new n(function(i){return(e(i)?t:r).transformDocument(i)},{cache:!1}),{left:t,right:r})},n.prototype.resetCache=function(){var e=this;if(this.cached){var t=new qe(ue);this.performWork=it(n.prototype.performWork.bind(this),{makeCacheKey:function(r){var i=e.getCacheKey(r);if(i)return(0,D.kG)(Array.isArray(i),69),t.lookupArray(i)},max:We.Q["documentTransform.cache"],cache:Cr.k})}},n.prototype.performWork=function(e){return(0,ge.A$)(e),this.transform(e)},n.prototype.transformDocument=function(e){if(this.resultCache.has(e))return e;var t=this.performWork(e);return this.resultCache.add(t),t},n.prototype.concat=function(e){var t=this;return Object.assign(new n(function(r){return e.transformDocument(t.transformDocument(r))},{cache:!1}),{left:this,right:e})},n}();function Lt(n,e,t){return new j.y(function(r){var i={then:function(u){return new Promise(function(f){return f(u())})}};function a(u,f){return function(l){if(u){var p=function(){return r.closed?0:u(l)};i=i.then(p,p).then(function(h){return r.next(h)},function(h){return r.error(h)})}else r[f](l)}}var o={next:a(e,"next"),error:a(t,"error"),complete:function(){i.then(function(){return r.complete()})}},s=n.subscribe(o);return function(){return s.unsubscribe()}})}function yt(n){return te(Vt(n))}function Vt(n){var e=te(n.errors)?n.errors.slice(0):[];return De(n)&&te(n.incremental)&&n.incremental.forEach(function(t){t.errors&&e.push.apply(e,t.errors)}),e}var $r=I(48561);function at(n,e,t){var r=[];n.forEach(function(i){return i[e]&&r.push(i)}),r.forEach(function(i){return i[e](t)})}function Zr(n){function e(t){Object.defineProperty(n,t,{value:j.y})}return Ee&&Symbol.species&&e(Symbol.species),e("@@species"),n}function Xr(n){return n&&"function"==typeof n.then}var Ue=function(n){function e(t){var r=n.call(this,function(i){return r.addObserver(i),function(){return r.removeObserver(i)}})||this;return r.observers=new Set,r.promise=new Promise(function(i,a){r.resolve=i,r.reject=a}),r.handlers={next:function(i){null!==r.sub&&(r.latest=["next",i],r.notify("next",i),at(r.observers,"next",i))},error:function(i){var a=r.sub;null!==a&&(a&&setTimeout(function(){return a.unsubscribe()}),r.sub=null,r.latest=["error",i],r.reject(i),r.notify("error",i),at(r.observers,"error",i))},complete:function(){var a=r.sub,o=r.sources;if(null!==a){var u=(void 0===o?[]:o).shift();u?Xr(u)?u.then(function(f){return r.sub=f.subscribe(r.handlers)},r.handlers.error):r.sub=u.subscribe(r.handlers):(a&&setTimeout(function(){return a.unsubscribe()}),r.sub=null,r.latest&&"next"===r.latest[0]?r.resolve(r.latest[1]):r.resolve(),r.notify("complete"),at(r.observers,"complete"))}}},r.nextResultListeners=new Set,r.cancel=function(i){r.reject(i),r.sources=[],r.handlers.error(i)},r.promise.catch(function(i){}),"function"==typeof t&&(t=[new j.y(t)]),Xr(t)?t.then(function(i){return r.start(i)},r.handlers.error):r.start(t),r}return(0,c.ZT)(e,n),e.prototype.start=function(t){void 0===this.sub&&(this.sources=Array.from(t),this.handlers.complete())},e.prototype.deliverLastMessage=function(t){if(this.latest){var r=this.latest[0],i=t[r];i&&i.call(t,this.latest[1]),null===this.sub&&"next"===r&&t.complete&&t.complete()}},e.prototype.addObserver=function(t){this.observers.has(t)||(this.deliverLastMessage(t),this.observers.add(t))},e.prototype.removeObserver=function(t){this.observers.delete(t)&&this.observers.size<1&&this.handlers.complete()},e.prototype.notify=function(t,r){var i=this.nextResultListeners;i.size&&(this.nextResultListeners=new Set,i.forEach(function(a){return a(t,r)}))},e.prototype.beforeNext=function(t){var r=!1;this.nextResultListeners.add(function(i,a){r||(r=!0,t(i,a))})},e}(j.y);Zr(Ue);var pe=(()=>{return(n=pe||(pe={}))[n.loading=1]="loading",n[n.setVariables=2]="setVariables",n[n.fetchMore=3]="fetchMore",n[n.refetch=4]="refetch",n[n.poll=6]="poll",n[n.ready=7]="ready",n[n.error=8]="error",pe;var n})();function ot(n){return!!n&&n<7}function qi(n){return 7===n||8===n}function Ke(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=Object.create(null);return n.forEach(function(r){r&&Object.keys(r).forEach(function(i){var a=r[i];void 0!==a&&(t[i]=a)})}),t}var Li=Object.prototype.toString;function en(n){return Nt(n)}function Nt(n,e){switch(Li.call(n)){case"[object Array]":if((e=e||new Map).has(n))return e.get(n);var t=n.slice(0);return e.set(n,t),t.forEach(function(i,a){t[a]=Nt(i,e)}),t;case"[object Object]":if((e=e||new Map).has(n))return e.get(n);var r=Object.create(Object.getPrototypeOf(n));return e.set(n,r),Object.keys(n).forEach(function(i){r[i]=Nt(n[i],e)}),r;default:return n}}function tn(n,e,t,r){var i=e.data,a=(0,c._T)(e,["data"]),o=t.data,s=(0,c._T)(t,["data"]);return Fr(a,s)&&mt((0,ge.p$)(n).selectionSet,i,o,{fragmentMap:(0,Ae.F)((0,ge.kU)(n)),variables:r})}function mt(n,e,t,r){if(e===t)return!0;var i=new Set;return n.selections.every(function(a){if(i.has(a)||(i.add(a),!$(a,r.variables))||rn(a))return!0;if((0,K.My)(a)){var o=(0,K.u2)(a),s=e&&e[o],u=t&&t[o],f=a.selectionSet;if(!f)return Fr(s,u);var l=Array.isArray(s),p=Array.isArray(u);if(l!==p)return!1;if(l&&p){var h=s.length;if(u.length!==h)return!1;for(var v=0;v<h;++v)if(!mt(f,s[v],u[v],r))return!1;return!0}return mt(f,s,u,r)}var w=(0,Ae.hi)(a,r.fragmentMap);return w?!!rn(w)||mt(w.selectionSet,e,t,r):void 0})}function rn(n){return!!n.directives&&n.directives.some(Vi)}function Vi(n){return"nonreactive"===n.name.value}var nn=Object.assign,Ni=Object.hasOwnProperty,gt=function(n){function e(t){var r=t.queryManager,i=t.queryInfo,a=t.options,o=n.call(this,function(k){try{var b=k._subscription._observer;b&&!b.error&&(b.error=zi)}catch{}var x=!o.observers.size;o.observers.add(k);var S=o.last;return S&&S.error?k.error&&k.error(S.error):S&&S.result&&k.next&&k.next(S.result),x&&o.reobserve().catch(function(){}),function(){o.observers.delete(k)&&!o.observers.size&&o.tearDownQuery()}})||this;o.observers=new Set,o.subscriptions=new Set,o.queryInfo=i,o.queryManager=r,o.waitForOwnResult=zt(a.fetchPolicy),o.isTornDown=!1,o.subscribeToMore=o.subscribeToMore.bind(o);var s=r.defaultOptions.watchQuery,f=(void 0===s?{}:s).fetchPolicy,l=void 0===f?"cache-first":f,p=a.fetchPolicy,h=void 0===p?l:p,v=a.initialFetchPolicy,w=void 0===v?"standby"===h?l:h:v;o.options=(0,c.pi)((0,c.pi)({},a),{initialFetchPolicy:w,fetchPolicy:h}),o.queryId=i.queryId||r.generateQueryId();var _=(0,ge.$H)(o.query);return o.queryName=_&&_.name&&_.name.value,o}return(0,c.ZT)(e,n),Object.defineProperty(e.prototype,"query",{get:function(){return this.lastQuery||this.options.query},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"variables",{get:function(){return this.options.variables},enumerable:!1,configurable:!0}),e.prototype.result=function(){var t=this;return new Promise(function(r,i){var a={next:function(s){r(s),t.observers.delete(a),t.observers.size||t.queryManager.removeQuery(t.queryId),setTimeout(function(){o.unsubscribe()},0)},error:i},o=t.subscribe(a)})},e.prototype.resetDiff=function(){this.queryInfo.resetDiff()},e.prototype.getCurrentResult=function(t){void 0===t&&(t=!0);var r=this.getLastResult(!0),i=this.queryInfo.networkStatus||r&&r.networkStatus||pe.ready,a=(0,c.pi)((0,c.pi)({},r),{loading:ot(i),networkStatus:i}),o=this.options.fetchPolicy,s=void 0===o?"cache-first":o;if(!zt(s)&&!this.queryManager.getDocumentInfo(this.query).hasForcedResolvers)if(this.waitForOwnResult)this.queryInfo.updateWatch();else{var u=this.queryInfo.getDiff();(u.complete||this.options.returnPartialData)&&(a.data=u.result),be(a.data,{})&&(a.data=void 0),u.complete?(delete a.partial,u.complete&&a.networkStatus===pe.loading&&("cache-first"===s||"cache-only"===s)&&(a.networkStatus=pe.ready,a.loading=!1)):a.partial=!0,!1!==globalThis.__DEV__&&!u.complete&&!this.options.partialRefetch&&!a.loading&&!a.data&&!a.error&&on(u.missing)}return t&&this.updateLastResult(a),a},e.prototype.isDifferentFromLastResult=function(t,r){return!this.last||(this.queryManager.getDocumentInfo(this.query).hasNonreactiveDirective?!tn(this.query,this.last.result,t,this.variables):!be(this.last.result,t))||r&&!be(this.last.variables,r)},e.prototype.getLast=function(t,r){var i=this.last;if(i&&i[t]&&(!r||be(i.variables,this.variables)))return i[t]},e.prototype.getLastResult=function(t){return this.getLast("result",t)},e.prototype.getLastError=function(t){return this.getLast("error",t)},e.prototype.resetLastResults=function(){delete this.last,this.isTornDown=!1},e.prototype.resetQueryStoreErrors=function(){this.queryManager.resetErrors(this.queryId)},e.prototype.refetch=function(t){var r,i={pollInterval:0},a=this.options.fetchPolicy;if(i.fetchPolicy="cache-and-network"===a?a:"no-cache"===a?"no-cache":"network-only",!1!==globalThis.__DEV__&&t&&Ni.call(t,"variables")){var o=(0,ge.iW)(this.query),s=o.variableDefinitions;(!s||!s.some(function(u){return"variables"===u.variable.name.value}))&&!1!==globalThis.__DEV__&&D.kG.warn(20,t,(null===(r=o.name)||void 0===r?void 0:r.value)||o)}return t&&!be(this.options.variables,t)&&(i.variables=this.options.variables=(0,c.pi)((0,c.pi)({},this.options.variables),t)),this.queryInfo.resetLastWrite(),this.reobserve(i,pe.refetch)},e.prototype.fetchMore=function(t){var r=this,i=(0,c.pi)((0,c.pi)({},t.query?t:(0,c.pi)((0,c.pi)((0,c.pi)((0,c.pi)({},this.options),{query:this.options.query}),t),{variables:(0,c.pi)((0,c.pi)({},this.options.variables),t.variables)})),{fetchPolicy:"no-cache"});i.query=this.transformDocument(i.query);var a=this.queryManager.generateQueryId();this.lastQuery=t.query?this.transformDocument(this.options.query):i.query;var o=this.queryInfo,s=o.networkStatus;o.networkStatus=pe.fetchMore,i.notifyOnNetworkStatusChange&&this.observe();var u=new Set,f=t?.updateQuery,l="no-cache"!==this.options.fetchPolicy;return l||(0,D.kG)(f,21),this.queryManager.fetchQuery(a,i,pe.fetchMore).then(function(p){if(r.queryManager.removeQuery(a),o.networkStatus===pe.fetchMore&&(o.networkStatus=s),l)r.queryManager.cache.batch({update:function(w){var _=t.updateQuery;_?w.updateQuery({query:r.query,variables:r.variables,returnPartialData:!0,optimistic:!1},function(k){return _(k,{fetchMoreResult:p.data,variables:i.variables})}):w.writeQuery({query:i.query,variables:i.variables,data:p.data})},onWatchUpdated:function(w){u.add(w.query)}});else{var h=r.getLast("result"),v=f(h.data,{fetchMoreResult:p.data,variables:i.variables});r.reportResult((0,c.pi)((0,c.pi)({},h),{data:v}),r.variables)}return p}).finally(function(){l&&!u.has(r.query)&&an(r)})},e.prototype.subscribeToMore=function(t){var r=this,i=this.queryManager.startGraphQLSubscription({query:t.document,variables:t.variables,context:t.context}).subscribe({next:function(a){var o=t.updateQuery;o&&r.updateQuery(function(s,u){return o(s,{subscriptionData:a,variables:u.variables})})},error:function(a){t.onError?t.onError(a):!1!==globalThis.__DEV__&&D.kG.error(22,a)}});return this.subscriptions.add(i),function(){r.subscriptions.delete(i)&&i.unsubscribe()}},e.prototype.setOptions=function(t){return this.reobserve(t)},e.prototype.silentSetOptions=function(t){var r=Ke(this.options,t||{});nn(this.options,r)},e.prototype.setVariables=function(t){return be(this.variables,t)?this.observers.size?this.result():Promise.resolve():(this.options.variables=t,this.observers.size?this.reobserve({fetchPolicy:this.options.initialFetchPolicy,variables:t},pe.setVariables):Promise.resolve())},e.prototype.updateQuery=function(t){var r=this.queryManager,a=t(r.cache.diff({query:this.options.query,variables:this.variables,returnPartialData:!0,optimistic:!1}).result,{variables:this.variables});a&&(r.cache.writeQuery({query:this.options.query,data:a,variables:this.variables}),r.broadcastQueries())},e.prototype.startPolling=function(t){this.options.pollInterval=t,this.updatePolling()},e.prototype.stopPolling=function(){this.options.pollInterval=0,this.updatePolling()},e.prototype.applyNextFetchPolicy=function(t,r){if(r.nextFetchPolicy){var i=r.fetchPolicy,a=void 0===i?"cache-first":i,o=r.initialFetchPolicy,s=void 0===o?a:o;"standby"===a||(r.fetchPolicy="function"==typeof r.nextFetchPolicy?r.nextFetchPolicy(a,{reason:t,options:r,observable:this,initialFetchPolicy:s}):"variables-changed"===t?s:r.nextFetchPolicy)}return r.fetchPolicy},e.prototype.fetch=function(t,r,i){return this.queryManager.setObservableQuery(this),this.queryManager.fetchConcastWithInfo(this.queryId,t,r,i)},e.prototype.updatePolling=function(){var t=this;if(!this.queryManager.ssrMode){var i=this.pollingInfo,a=this.options.pollInterval;if(!a||!this.hasObservers())return void(i&&(clearTimeout(i.timeout),delete this.pollingInfo));if(!i||i.interval!==a){(0,D.kG)(a,23),(i||(this.pollingInfo={})).interval=a;var s=function(){var f,l;t.pollingInfo&&(ot(t.queryInfo.networkStatus)||null!==(l=(f=t.options).skipPollAttempt)&&void 0!==l&&l.call(f)?u():t.reobserve({fetchPolicy:"no-cache"===t.options.initialFetchPolicy?"no-cache":"network-only"},pe.poll).then(u,u))},u=function(){var f=t.pollingInfo;f&&(clearTimeout(f.timeout),f.timeout=setTimeout(s,f.interval))};u()}}},e.prototype.updateLastResult=function(t,r){void 0===r&&(r=this.variables);var i=this.getLastError();return i&&this.last&&!be(r,this.last.variables)&&(i=void 0),this.last=(0,c.pi)({result:this.queryManager.assumeImmutableResults?t:en(t),variables:r},i?{error:i}:null)},e.prototype.reobserveAsConcast=function(t,r){var i=this;this.isTornDown=!1;var a=r===pe.refetch||r===pe.fetchMore||r===pe.poll,o=this.options.variables,s=this.options.fetchPolicy,u=Ke(this.options,t||{}),f=a?u:nn(this.options,u),l=this.transformDocument(f.query);this.lastQuery=l,a||(this.updatePolling(),t&&t.variables&&!be(t.variables,o)&&"standby"!==f.fetchPolicy&&(f.fetchPolicy===s||"function"==typeof f.nextFetchPolicy)&&(this.applyNextFetchPolicy("variables-changed",f),void 0===r&&(r=pe.setVariables))),this.waitForOwnResult&&(this.waitForOwnResult=zt(f.fetchPolicy));var p=function(){i.concast===w&&(i.waitForOwnResult=!1)},h=f.variables&&(0,c.pi)({},f.variables),v=this.fetch(f,r,l),w=v.concast,k={next:function(b){be(i.variables,h)&&(p(),i.reportResult(b,h))},error:function(b){be(i.variables,h)&&(R(b)||(b=new he({networkError:b})),p(),i.reportError(b,h))}};return!a&&(v.fromLink||!this.concast)&&(this.concast&&this.observer&&this.concast.removeObserver(this.observer),this.concast=w,this.observer=k),w.addObserver(k),w},e.prototype.reobserve=function(t,r){return this.reobserveAsConcast(t,r).promise},e.prototype.resubscribeAfterError=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var i=this.last;this.resetLastResults();var a=this.subscribe.apply(this,t);return this.last=i,a},e.prototype.observe=function(){this.reportResult(this.getCurrentResult(!1),this.variables)},e.prototype.reportResult=function(t,r){var i=this.getLastError(),a=this.isDifferentFromLastResult(t,r);(i||!t.partial||this.options.returnPartialData)&&this.updateLastResult(t,r),(i||a)&&at(this.observers,"next",t)},e.prototype.reportError=function(t,r){var i=(0,c.pi)((0,c.pi)({},this.getLastResult()),{error:t,errors:t.graphQLErrors,networkStatus:pe.error,loading:!1});this.updateLastResult(i,r),at(this.observers,"error",this.last.error=t)},e.prototype.hasObservers=function(){return this.observers.size>0},e.prototype.tearDownQuery=function(){this.isTornDown||(this.concast&&this.observer&&(this.concast.removeObserver(this.observer),delete this.concast,delete this.observer),this.stopPolling(),this.subscriptions.forEach(function(t){return t.unsubscribe()}),this.subscriptions.clear(),this.queryManager.stopQuery(this.queryId),this.observers.clear(),this.isTornDown=!0)},e.prototype.transformDocument=function(t){return this.queryManager.transform(t)},e}(j.y);function an(n){var e=n.options,t=e.fetchPolicy,r=e.nextFetchPolicy;return"cache-and-network"===t||"network-only"===t?n.reobserve({fetchPolicy:"cache-first",nextFetchPolicy:function(i,a){return this.nextFetchPolicy=r,"function"==typeof this.nextFetchPolicy?this.nextFetchPolicy(i,a):t}}):n.reobserve()}function zi(n){!1!==globalThis.__DEV__&&D.kG.error(24,n.message,n.stack)}function on(n){!1!==globalThis.__DEV__&&n&&!1!==globalThis.__DEV__&&D.kG.debug(25,n)}function zt(n){return"network-only"===n||"no-cache"===n||"standby"===n}Zr(gt);var Ye=new(ue?WeakMap:Map);function Wt(n,e){var t=n[e];"function"==typeof t&&(n[e]=function(){return Ye.set(n,(Ye.get(n)+1)%1e15),t.apply(this,arguments)})}function sn(n){n.notifyTimeout&&(clearTimeout(n.notifyTimeout),n.notifyTimeout=void 0)}var Bt=function(){function n(e,t){void 0===t&&(t=e.generateQueryId()),this.queryId=t,this.listeners=new Set,this.document=null,this.lastRequestId=1,this.stopped=!1,this.dirty=!1,this.observableQuery=null;var r=this.cache=e.cache;Ye.has(r)||(Ye.set(r,0),Wt(r,"evict"),Wt(r,"modify"),Wt(r,"reset"))}return n.prototype.init=function(e){var t=e.networkStatus||pe.loading;return this.variables&&this.networkStatus!==pe.loading&&!be(this.variables,e.variables)&&(t=pe.setVariables),be(e.variables,this.variables)||(this.lastDiff=void 0),Object.assign(this,{document:e.document,variables:e.variables,networkError:null,graphQLErrors:this.graphQLErrors||[],networkStatus:t}),e.observableQuery&&this.setObservableQuery(e.observableQuery),e.lastRequestId&&(this.lastRequestId=e.lastRequestId),this},n.prototype.reset=function(){sn(this),this.dirty=!1},n.prototype.resetDiff=function(){this.lastDiff=void 0},n.prototype.getDiff=function(){var e=this.getDiffOptions();if(this.lastDiff&&be(e,this.lastDiff.options))return this.lastDiff.diff;this.updateWatch(this.variables);var t=this.observableQuery;if(t&&"no-cache"===t.options.fetchPolicy)return{complete:!1};var r=this.cache.diff(e);return this.updateLastDiff(r,e),r},n.prototype.updateLastDiff=function(e,t){this.lastDiff=e?{diff:e,options:t||this.getDiffOptions()}:void 0},n.prototype.getDiffOptions=function(e){var t;return void 0===e&&(e=this.variables),{query:this.document,variables:e,returnPartialData:!0,optimistic:!0,canonizeResults:null===(t=this.observableQuery)||void 0===t?void 0:t.options.canonizeResults}},n.prototype.setDiff=function(e){var r,t=this,i=this.lastDiff&&this.lastDiff.diff;e&&!e.complete&&null!==(r=this.observableQuery)&&void 0!==r&&r.getLastError()||(this.updateLastDiff(e),!this.dirty&&!be(i&&i.result,e&&e.result)&&(this.dirty=!0,this.notifyTimeout||(this.notifyTimeout=setTimeout(function(){return t.notify()},0))))},n.prototype.setObservableQuery=function(e){var t=this;e!==this.observableQuery&&(this.oqListener&&this.listeners.delete(this.oqListener),this.observableQuery=e,e?(e.queryInfo=this,this.listeners.add(this.oqListener=function(){t.getDiff().fromOptimisticTransaction?e.observe():an(e)})):delete this.oqListener)},n.prototype.notify=function(){var e=this;sn(this),this.shouldNotify()&&this.listeners.forEach(function(t){return t(e)}),this.dirty=!1},n.prototype.shouldNotify=function(){if(!this.dirty||!this.listeners.size)return!1;if(ot(this.networkStatus)&&this.observableQuery){var e=this.observableQuery.options.fetchPolicy;if("cache-only"!==e&&"cache-and-network"!==e)return!1}return!0},n.prototype.stop=function(){if(!this.stopped){this.stopped=!0,this.reset(),this.cancel(),this.cancel=n.prototype.cancel;var e=this.observableQuery;e&&e.stopPolling()}},n.prototype.cancel=function(){},n.prototype.updateWatch=function(e){var t=this;void 0===e&&(e=this.variables);var r=this.observableQuery;if(!r||"no-cache"!==r.options.fetchPolicy){var i=(0,c.pi)((0,c.pi)({},this.getDiffOptions(e)),{watcher:this,callback:function(a){return t.setDiff(a)}});(!this.lastWatch||!be(i,this.lastWatch))&&(this.cancel(),this.cancel=this.cache.watch(this.lastWatch=i))}},n.prototype.resetLastWrite=function(){this.lastWrite=void 0},n.prototype.shouldWrite=function(e,t){var r=this.lastWrite;return!(r&&r.dmCount===Ye.get(this.cache)&&be(t,r.variables)&&be(e.data,r.result.data))},n.prototype.markResult=function(e,t,r,i){var a=this,o=new we,s=te(e.errors)?e.errors.slice(0):[];if(this.reset(),"incremental"in e&&te(e.incremental)){var u=fr(this.getDiff().result,e);e.data=u}else if("hasNext"in e&&e.hasNext){var f=this.getDiff();e.data=o.merge(f.result,e.data)}this.graphQLErrors=s,"no-cache"===r.fetchPolicy?this.updateLastDiff({result:e.data,complete:!0},this.getDiffOptions(r.variables)):0!==i&&(Gt(e,r.errorPolicy)?this.cache.performTransaction(function(l){if(a.shouldWrite(e,r.variables))l.writeQuery({query:t,data:e.data,variables:r.variables,overwrite:1===i}),a.lastWrite={result:e,variables:r.variables,dmCount:Ye.get(a.cache)};else if(a.lastDiff&&a.lastDiff.diff.complete)return void(e.data=a.lastDiff.diff.result);var p=a.getDiffOptions(r.variables),h=l.diff(p);!a.stopped&&be(a.variables,r.variables)&&a.updateWatch(r.variables),a.updateLastDiff(h,p),h.complete&&(e.data=h.result)}):this.lastWrite=void 0)},n.prototype.markReady=function(){return this.networkError=null,this.networkStatus=pe.ready},n.prototype.markError=function(e){return this.networkStatus=pe.error,this.lastWrite=void 0,this.reset(),e.graphQLErrors&&(this.graphQLErrors=e.graphQLErrors),e.networkError&&(this.networkError=e.networkError),e},n}();function Gt(n,e){void 0===e&&(e="none");var t="ignore"===e||"all"===e,r=!yt(n);return!r&&t&&n.data&&(r=!0),r}var Wi=Object.prototype.hasOwnProperty,un=Object.create(null),Bi=function(){function n(e){var t=this;this.clientAwareness={},this.queries=new Map,this.fetchCancelFns=new Map,this.transformCache=new dr.s(We.Q["queryManager.getDocumentInfo"]||2e3),this.queryIdCounter=1,this.requestIdCounter=1,this.mutationIdCounter=1,this.inFlightLinkObservables=new qe(!1);var r=new qt(function(a){return t.cache.transformDocument(a)},{cache:!1});this.cache=e.cache,this.link=e.link,this.defaultOptions=e.defaultOptions,this.queryDeduplication=e.queryDeduplication,this.clientAwareness=e.clientAwareness,this.localState=e.localState,this.ssrMode=e.ssrMode,this.assumeImmutableResults=e.assumeImmutableResults;var i=e.documentTransform;this.documentTransform=i?r.concat(i).concat(r):r,this.defaultContext=e.defaultContext||Object.create(null),(this.onBroadcast=e.onBroadcast)&&(this.mutationStore=Object.create(null))}return n.prototype.stop=function(){var e=this;this.queries.forEach(function(t,r){e.stopQueryNoBroadcast(r)}),this.cancelPendingFetches((0,D._K)(26))},n.prototype.cancelPendingFetches=function(e){this.fetchCancelFns.forEach(function(t){return t(e)}),this.fetchCancelFns.clear()},n.prototype.mutate=function(e){return(0,c.mG)(this,arguments,void 0,function(t){var r,i,a,o,s,u,f,l=t.mutation,p=t.variables,h=t.optimisticResponse,v=t.updateQueries,w=t.refetchQueries,_=void 0===w?[]:w,k=t.awaitRefetchQueries,b=void 0!==k&&k,x=t.update,S=t.onQueryUpdated,G=t.fetchPolicy,z=void 0===G?(null===(u=this.defaultOptions.mutate)||void 0===u?void 0:u.fetchPolicy)||"network-only":G,Z=t.errorPolicy,ce=void 0===Z?(null===(f=this.defaultOptions.mutate)||void 0===f?void 0:f.errorPolicy)||"none":Z,se=t.keepRootFields,ve=t.context;return(0,c.Jh)(this,function(Oe){switch(Oe.label){case 0:return(0,D.kG)(l,27),(0,D.kG)("network-only"===z||"no-cache"===z,28),r=this.generateMutationId(),l=this.cache.transformForLink(this.transform(l)),i=this.getDocumentInfo(l).hasClientExports,p=this.getVariables(l,p),i?[4,this.localState.addExportedVariables(l,p,ve)]:[3,2];case 1:p=Oe.sent(),Oe.label=2;case 2:return a=this.mutationStore&&(this.mutationStore[r]={mutation:l,variables:p,loading:!0,error:null}),o=h&&this.markMutationOptimistic(h,{mutationId:r,document:l,variables:p,fetchPolicy:z,errorPolicy:ce,context:ve,updateQueries:v,update:x,keepRootFields:se}),this.broadcastQueries(),s=this,[2,new Promise(function(Ze,Xe){return Lt(s.getObservableFromLink(l,(0,c.pi)((0,c.pi)({},ve),{optimisticResponse:o?h:void 0}),p,{},!1),function(Pe){if(yt(Pe)&&"none"===ce)throw new he({graphQLErrors:Vt(Pe)});a&&(a.loading=!1,a.error=null);var ze=(0,c.pi)({},Pe);return"function"==typeof _&&(_=_(ze)),"ignore"===ce&&yt(ze)&&delete ze.errors,s.markMutationResult({mutationId:r,result:ze,document:l,variables:p,fetchPolicy:z,errorPolicy:ce,context:ve,update:x,updateQueries:v,awaitRefetchQueries:b,refetchQueries:_,removeOptimistic:o?r:void 0,onQueryUpdated:S,keepRootFields:se})}).subscribe({next:function(Pe){s.broadcastQueries(),(!("hasNext"in Pe)||!1===Pe.hasNext)&&Ze(Pe)},error:function(Pe){a&&(a.loading=!1,a.error=Pe),o&&s.cache.removeOptimistic(r),s.broadcastQueries(),Xe(Pe instanceof he?Pe:new he({networkError:Pe}))}})})]}})})},n.prototype.markMutationResult=function(e,t){var r=this;void 0===t&&(t=this.cache);var i=e.result,a=[],o="no-cache"===e.fetchPolicy;if(!o&&Gt(i,e.errorPolicy)){if(De(i)||a.push({result:i.data,dataId:"ROOT_MUTATION",query:e.document,variables:e.variables}),De(i)&&te(i.incremental)){var s=t.diff({id:"ROOT_MUTATION",query:this.getDocumentInfo(e.document).asQuery,variables:e.variables,optimistic:!1,returnPartialData:!0}),u=void 0;s.result&&(u=fr(s.result,i)),typeof u<"u"&&(i.data=u,a.push({result:u,dataId:"ROOT_MUTATION",query:e.document,variables:e.variables}))}var f=e.updateQueries;f&&this.queries.forEach(function(p,h){var v=p.observableQuery,w=v&&v.queryName;if(w&&Wi.call(f,w)){var _=f[w],k=r.queries.get(h),b=k.document,x=k.variables,S=t.diff({query:b,variables:x,returnPartialData:!0,optimistic:!1}),G=S.result;if(S.complete&&G){var Z=_(G,{mutationResult:i,queryName:b&&(0,ge.rY)(b)||void 0,queryVariables:x});Z&&a.push({result:Z,dataId:"ROOT_QUERY",query:b,variables:x})}}})}if(a.length>0||(e.refetchQueries||"").length>0||e.update||e.onQueryUpdated||e.removeOptimistic){var l=[];if(this.refetchQueries({updateCache:function(p){o||a.forEach(function(_){return p.write(_)});var h=e.update,v=!function xe(n){return De(n)||function Ie(n){return"hasNext"in n&&"data"in n}(n)}(i)||De(i)&&!i.hasNext;if(h){if(!o){var w=p.diff({id:"ROOT_MUTATION",query:r.getDocumentInfo(e.document).asQuery,variables:e.variables,optimistic:!1,returnPartialData:!0});w.complete&&("incremental"in(i=(0,c.pi)((0,c.pi)({},i),{data:w.result}))&&delete i.incremental,"hasNext"in i&&delete i.hasNext)}v&&h(p,i,{context:e.context,variables:e.variables})}!o&&!e.keepRootFields&&v&&p.modify({id:"ROOT_MUTATION",fields:function(_,k){return"__typename"===k.fieldName?_:k.DELETE}})},include:e.refetchQueries,optimistic:!1,removeOptimistic:e.removeOptimistic,onQueryUpdated:e.onQueryUpdated||null}).forEach(function(p){return l.push(p)}),e.awaitRefetchQueries||e.onQueryUpdated)return Promise.all(l).then(function(){return i})}return Promise.resolve(i)},n.prototype.markMutationOptimistic=function(e,t){var r=this,i="function"==typeof e?e(t.variables,{IGNORE:un}):e;return i!==un&&(this.cache.recordOptimisticTransaction(function(a){try{r.markMutationResult((0,c.pi)((0,c.pi)({},t),{result:{data:i}}),a)}catch(o){!1!==globalThis.__DEV__&&D.kG.error(o)}},t.mutationId),!0)},n.prototype.fetchQuery=function(e,t,r){return this.fetchConcastWithInfo(e,t,r).concast.promise},n.prototype.getQueryStore=function(){var e=Object.create(null);return this.queries.forEach(function(t,r){e[r]={variables:t.variables,networkStatus:t.networkStatus,networkError:t.networkError,graphQLErrors:t.graphQLErrors}}),e},n.prototype.resetErrors=function(e){var t=this.queries.get(e);t&&(t.networkError=void 0,t.graphQLErrors=[])},n.prototype.transform=function(e){return this.documentTransform.transformDocument(e)},n.prototype.getDocumentInfo=function(e){var t=this.transformCache;if(!t.has(e)){var r={hasClientExports:E(e),hasForcedResolvers:this.localState.shouldForceResolvers(e),hasNonreactiveDirective:A(["nonreactive"],e),clientQuery:this.localState.clientQuery(e),serverQuery:Tt([{name:"client",remove:!0},{name:"connection"},{name:"nonreactive"}],e),defaultVars:(0,ge.O4)((0,ge.$H)(e)),asQuery:(0,c.pi)((0,c.pi)({},e),{definitions:e.definitions.map(function(i){return"OperationDefinition"===i.kind&&"query"!==i.operation?(0,c.pi)((0,c.pi)({},i),{operation:"query"}):i})})};t.set(e,r)}return t.get(e)},n.prototype.getVariables=function(e,t){return(0,c.pi)((0,c.pi)({},this.getDocumentInfo(e).defaultVars),t)},n.prototype.watchQuery=function(e){var t=this.transform(e.query);typeof(e=(0,c.pi)((0,c.pi)({},e),{variables:this.getVariables(t,e.variables)})).notifyOnNetworkStatusChange>"u"&&(e.notifyOnNetworkStatusChange=!1);var r=new Bt(this),i=new gt({queryManager:this,queryInfo:r,options:e});return i.lastQuery=t,this.queries.set(i.queryId,r),r.init({document:t,observableQuery:i,variables:i.variables}),i},n.prototype.query=function(e,t){var r=this;return void 0===t&&(t=this.generateQueryId()),(0,D.kG)(e.query,29),(0,D.kG)("Document"===e.query.kind,30),(0,D.kG)(!e.returnPartialData,31),(0,D.kG)(!e.pollInterval,32),this.fetchQuery(t,(0,c.pi)((0,c.pi)({},e),{query:this.transform(e.query)})).finally(function(){return r.stopQuery(t)})},n.prototype.generateQueryId=function(){return String(this.queryIdCounter++)},n.prototype.generateRequestId=function(){return this.requestIdCounter++},n.prototype.generateMutationId=function(){return String(this.mutationIdCounter++)},n.prototype.stopQueryInStore=function(e){this.stopQueryInStoreNoBroadcast(e),this.broadcastQueries()},n.prototype.stopQueryInStoreNoBroadcast=function(e){var t=this.queries.get(e);t&&t.stop()},n.prototype.clearStore=function(e){return void 0===e&&(e={discardWatches:!0}),this.cancelPendingFetches((0,D._K)(33)),this.queries.forEach(function(t){t.observableQuery?t.networkStatus=pe.loading:t.stop()}),this.mutationStore&&(this.mutationStore=Object.create(null)),this.cache.reset(e)},n.prototype.getObservableQueries=function(e){var t=this;void 0===e&&(e="active");var r=new Map,i=new Map,a=new Set;return Array.isArray(e)&&e.forEach(function(o){"string"==typeof o?i.set(o,!1):(0,K.JW)(o)?i.set(t.transform(o),!1):(0,m.s)(o)&&o.query&&a.add(o)}),this.queries.forEach(function(o,s){var u=o.observableQuery,f=o.document;if(u){if("all"===e)return void r.set(s,u);var l=u.queryName;if("standby"===u.options.fetchPolicy||"active"===e&&!u.hasObservers())return;("active"===e||l&&i.has(l)||f&&i.has(f))&&(r.set(s,u),l&&i.set(l,!0),f&&i.set(f,!0))}}),a.size&&a.forEach(function(o){var s=(0,$r.X)("legacyOneTimeQuery"),u=t.getQuery(s).init({document:o.query,variables:o.variables}),f=new gt({queryManager:t,queryInfo:u,options:(0,c.pi)((0,c.pi)({},o),{fetchPolicy:"network-only"})});(0,D.kG)(f.queryId===s),u.setObservableQuery(f),r.set(s,f)}),!1!==globalThis.__DEV__&&i.size&&i.forEach(function(o,s){o||!1!==globalThis.__DEV__&&D.kG.warn("string"==typeof s?34:35,s)}),r},n.prototype.reFetchObservableQueries=function(e){var t=this;void 0===e&&(e=!1);var r=[];return this.getObservableQueries(e?"all":"active").forEach(function(i,a){var o=i.options.fetchPolicy;i.resetLastResults(),(e||"standby"!==o&&"cache-only"!==o)&&r.push(i.refetch()),t.getQuery(a).setDiff(null)}),this.broadcastQueries(),Promise.all(r)},n.prototype.setObservableQuery=function(e){this.getQuery(e.queryId).setObservableQuery(e)},n.prototype.startGraphQLSubscription=function(e){var t=this,r=e.query,i=e.fetchPolicy,a=e.errorPolicy,o=void 0===a?"none":a,s=e.variables,u=e.context,f=void 0===u?{}:u,l=e.extensions,p=void 0===l?{}:l;r=this.transform(r),s=this.getVariables(r,s);var h=function(w){return t.getObservableFromLink(r,f,w,p).map(function(_){"no-cache"!==i&&(Gt(_,o)&&t.cache.write({query:r,result:_.data,dataId:"ROOT_SUBSCRIPTION",variables:w}),t.broadcastQueries());var k=yt(_),b=function M(n){return!!n.extensions&&Array.isArray(n.extensions[O])}(_);if(k||b){var x={};if(k&&(x.graphQLErrors=_.errors),b&&(x.protocolErrors=_.extensions[O]),"none"===o||b)throw new he(x)}return"ignore"===o&&delete _.errors,_})};if(this.getDocumentInfo(r).hasClientExports){var v=this.localState.addExportedVariables(r,s,f).then(h);return new j.y(function(w){var _=null;return v.then(function(k){return _=k.subscribe(w)},w.error),function(){return _&&_.unsubscribe()}})}return h(s)},n.prototype.stopQuery=function(e){this.stopQueryNoBroadcast(e),this.broadcastQueries()},n.prototype.stopQueryNoBroadcast=function(e){this.stopQueryInStoreNoBroadcast(e),this.removeQuery(e)},n.prototype.removeQuery=function(e){this.fetchCancelFns.delete(e),this.queries.has(e)&&(this.getQuery(e).stop(),this.queries.delete(e))},n.prototype.broadcastQueries=function(){this.onBroadcast&&this.onBroadcast(),this.queries.forEach(function(e){return e.notify()})},n.prototype.getLocalState=function(){return this.localState},n.prototype.getObservableFromLink=function(e,t,r,i,a){var s,o=this;void 0===a&&(a=null!==(s=t?.queryDeduplication)&&void 0!==s?s:this.queryDeduplication);var u,f=this.getDocumentInfo(e),l=f.serverQuery,p=f.clientQuery;if(l){var v=this.inFlightLinkObservables,w=this.link,_={query:l,variables:r,operationName:(0,ge.rY)(l)||void 0,context:this.prepareContext((0,c.pi)((0,c.pi)({},t),{forceFetch:!a})),extensions:i};if(t=_.context,a){var k=pt(l),b=(0,Be.B)(r),x=v.lookup(k,b);if(!(u=x.observable)){var S=new Ue([H(w,_)]);u=x.observable=S,S.beforeNext(function(){v.remove(k,b)})}}else u=new Ue([H(w,_)])}else u=new Ue([j.y.of({data:{}})]),t=this.prepareContext(t);return p&&(u=Lt(u,function(G){return o.localState.runResolvers({document:p,remoteResult:G,context:t,variables:r})})),u},n.prototype.getResultsFromLink=function(e,t,r){var i=e.lastRequestId=this.generateRequestId(),a=this.cache.transformForLink(r.query);return Lt(this.getObservableFromLink(a,r.context,r.variables),function(o){var s=Vt(o),u=s.length>0,f=r.errorPolicy;if(i>=e.lastRequestId){if(u&&"none"===f)throw e.markError(new he({graphQLErrors:s}));e.markResult(o,a,r,t),e.markReady()}var l={data:o.data,loading:!1,networkStatus:pe.ready};return u&&"none"===f&&(l.data=void 0),u&&"ignore"!==f&&(l.errors=s,l.networkStatus=pe.error),l},function(o){var s=R(o)?o:new he({networkError:o});throw i>=e.lastRequestId&&e.markError(s),s})},n.prototype.fetchConcastWithInfo=function(e,t,r,i){var a=this;void 0===r&&(r=pe.loading),void 0===i&&(i=t.query);var Z,ce,o=this.getVariables(i,t.variables),s=this.getQuery(e),u=this.defaultOptions.watchQuery,f=t.fetchPolicy,p=t.errorPolicy,v=t.returnPartialData,_=t.notifyOnNetworkStatusChange,b=t.context,S=Object.assign({},t,{query:i,variables:o,fetchPolicy:void 0===f?u&&u.fetchPolicy||"cache-first":f,errorPolicy:void 0===p?u&&u.errorPolicy||"none":p,returnPartialData:void 0!==v&&v,notifyOnNetworkStatusChange:void 0!==_&&_,context:void 0===b?{}:b}),G=function(ve){S.variables=ve;var Oe=a.fetchQueryByPolicy(s,S,r);return"standby"!==S.fetchPolicy&&Oe.sources.length>0&&s.observableQuery&&s.observableQuery.applyNextFetchPolicy("after-fetch",t),Oe},z=function(){return a.fetchCancelFns.delete(e)};if(this.fetchCancelFns.set(e,function(ve){z(),setTimeout(function(){return Z.cancel(ve)})}),this.getDocumentInfo(S.query).hasClientExports)Z=new Ue(this.localState.addExportedVariables(S.query,S.variables,S.context).then(G).then(function(ve){return ve.sources})),ce=!0;else{var se=G(S.variables);ce=se.fromLink,Z=new Ue(se.sources)}return Z.promise.then(z,z),{concast:Z,fromLink:ce}},n.prototype.refetchQueries=function(e){var t=this,r=e.updateCache,i=e.include,a=e.optimistic,o=void 0!==a&&a,s=e.removeOptimistic,u=void 0===s?o?(0,$r.X)("refetchQueries"):void 0:s,f=e.onQueryUpdated,l=new Map;i&&this.getObservableQueries(i).forEach(function(h,v){l.set(v,{oq:h,lastDiff:t.getQuery(v).getDiff()})});var p=new Map;return r&&this.cache.batch({update:r,optimistic:o&&u||!1,removeOptimistic:u,onWatchUpdated:function(h,v,w){var _=h.watcher instanceof Bt&&h.watcher.observableQuery;if(_){if(f){l.delete(_.queryId);var k=f(_,v,w);return!0===k&&(k=_.refetch()),!1!==k&&p.set(_,k),k}null!==f&&l.set(_.queryId,{oq:_,lastDiff:w,diff:v})}}}),l.size&&l.forEach(function(h,v){var b,w=h.oq,_=h.lastDiff,k=h.diff;if(f){if(!k){var x=w.queryInfo;x.reset(),k=x.getDiff()}b=f(w,k,_)}(!f||!0===b)&&(b=w.refetch()),!1!==b&&p.set(w,b),v.indexOf("legacyOneTimeQuery")>=0&&t.stopQueryNoBroadcast(v)}),u&&this.cache.removeOptimistic(u),p},n.prototype.fetchQueryByPolicy=function(e,t,r){var i=this,a=t.query,o=t.variables,s=t.fetchPolicy,u=t.refetchWritePolicy,f=t.errorPolicy,l=t.returnPartialData,p=t.context,h=t.notifyOnNetworkStatusChange,v=e.networkStatus;e.init({document:a,variables:o,networkStatus:r});var w=function(){return e.getDiff()},_=function(G,z){void 0===z&&(z=e.networkStatus||pe.loading);var Z=G.result;!1!==globalThis.__DEV__&&!l&&!be(Z,{})&&on(G.missing);var ce=function(se){return j.y.of((0,c.pi)({data:se,loading:ot(z),networkStatus:z},G.complete?null:{partial:!0}))};return Z&&i.getDocumentInfo(a).hasForcedResolvers?i.localState.runResolvers({document:a,remoteResult:{data:Z},context:p,variables:o,onlyRunForcedResolvers:!0}).then(function(se){return ce(se.data||void 0)}):"none"===f&&z===pe.refetch&&Array.isArray(G.missing)?ce(void 0):ce(Z)},k="no-cache"===s?0:r===pe.refetch&&"merge"!==u?1:2,b=function(){return i.getResultsFromLink(e,k,{query:a,variables:o,context:p,fetchPolicy:s,errorPolicy:f})},x=h&&"number"==typeof v&&v!==r&&ot(r);switch(s){default:case"cache-first":return(S=w()).complete?{fromLink:!1,sources:[_(S,e.markReady())]}:l||x?{fromLink:!0,sources:[_(S),b()]}:{fromLink:!0,sources:[b()]};case"cache-and-network":var S;return(S=w()).complete||l||x?{fromLink:!0,sources:[_(S),b()]}:{fromLink:!0,sources:[b()]};case"cache-only":return{fromLink:!1,sources:[_(w(),e.markReady())]};case"network-only":return x?{fromLink:!0,sources:[_(w()),b()]}:{fromLink:!0,sources:[b()]};case"no-cache":return x?{fromLink:!0,sources:[_(e.getDiff()),b()]}:{fromLink:!0,sources:[b()]};case"standby":return{fromLink:!1,sources:[]}}},n.prototype.getQuery=function(e){return e&&!this.queries.has(e)&&this.queries.set(e,new Bt(this,e)),this.queries.get(e)},n.prototype.prepareContext=function(e){void 0===e&&(e={});var t=this.localState.prepareContext(e);return(0,c.pi)((0,c.pi)((0,c.pi)({},this.defaultContext),t),{clientAwareness:this.clientAwareness})},n}(),Ut=new It,cn=new WeakMap;function st(n){var e=cn.get(n);return e||cn.set(n,e={vars:new Set,dep:Hr()}),e}function fn(n){st(n).vars.forEach(function(e){return e.forgetCache(n)})}function ln(n){var e=new Set,t=new Set,r=function(a){if(arguments.length>0){if(n!==a){n=a,e.forEach(function(u){st(u).dep.dirty(r),function Ui(n){n.broadcastWatches&&n.broadcastWatches()}(u)});var o=Array.from(t);t.clear(),o.forEach(function(u){return u(n)})}}else{var s=Ut.getValue();s&&(i(s),st(s).dep(r))}return n};r.onNextChange=function(a){return t.add(a),function(){t.delete(a)}};var i=r.attachCache=function(a){return e.add(a),st(a).vars.add(r),r};return r.forgetCache=function(a){return e.delete(a)},r}var Ki=function(){function n(e){var t=e.cache,r=e.client,i=e.resolvers,a=e.fragmentMatcher;this.selectionsToResolveCache=new WeakMap,this.cache=t,r&&(this.client=r),i&&this.addResolvers(i),a&&this.setFragmentMatcher(a)}return n.prototype.addResolvers=function(e){var t=this;this.resolvers=this.resolvers||{},Array.isArray(e)?e.forEach(function(r){t.resolvers=Se(t.resolvers,r)}):this.resolvers=Se(this.resolvers,e)},n.prototype.setResolvers=function(e){this.resolvers={},this.addResolvers(e)},n.prototype.getResolvers=function(){return this.resolvers||{}},n.prototype.runResolvers=function(e){return(0,c.mG)(this,arguments,void 0,function(t){var r=t.document,i=t.remoteResult,a=t.context,o=t.variables,s=t.onlyRunForcedResolvers,u=void 0!==s&&s;return(0,c.Jh)(this,function(f){return r?[2,this.resolveDocument(r,i.data,a,o,this.fragmentMatcher,u).then(function(l){return(0,c.pi)((0,c.pi)({},i),{data:l.result})})]:[2,i]})})},n.prototype.setFragmentMatcher=function(e){this.fragmentMatcher=e},n.prototype.getFragmentMatcher=function(){return this.fragmentMatcher},n.prototype.clientQuery=function(e){return A(["client"],e)&&this.resolvers?e:null},n.prototype.serverQuery=function(e){return Or(e)},n.prototype.prepareContext=function(e){var t=this.cache;return(0,c.pi)((0,c.pi)({},e),{cache:t,getCacheKey:function(r){return t.identify(r)}})},n.prototype.addExportedVariables=function(e){return(0,c.mG)(this,arguments,void 0,function(t,r,i){return void 0===r&&(r={}),void 0===i&&(i={}),(0,c.Jh)(this,function(a){return t?[2,this.resolveDocument(t,this.buildRootValueFromCache(t,r)||{},this.prepareContext(i),r).then(function(o){return(0,c.pi)((0,c.pi)({},r),o.exportedVariables)})]:[2,(0,c.pi)({},r)]})})},n.prototype.shouldForceResolvers=function(e){var t=!1;return(0,Q.visit)(e,{Directive:{enter:function(r){if("client"===r.name.value&&r.arguments&&(t=r.arguments.some(function(i){return"always"===i.name.value&&"BooleanValue"===i.value.kind&&!0===i.value.value})))return Q.BREAK}}}),t},n.prototype.buildRootValueFromCache=function(e,t){return this.cache.diff({query:fi(e),variables:t,returnPartialData:!0,optimistic:!1}).result},n.prototype.resolveDocument=function(e,t){return(0,c.mG)(this,arguments,void 0,function(r,i,a,o,s,u){var f,l,p,h,v,w,_,k,b,x;return void 0===a&&(a={}),void 0===o&&(o={}),void 0===s&&(s=function(){return!0}),void 0===u&&(u=!1),(0,c.Jh)(this,function(G){return f=(0,ge.p$)(r),l=(0,ge.kU)(r),p=(0,Ae.F)(l),h=this.collectSelectionsToResolve(f,p),w=(v=f.operation)?v.charAt(0).toUpperCase()+v.slice(1):"Query",k=(_=this).cache,b=_.client,x={fragmentMap:p,context:(0,c.pi)((0,c.pi)({},a),{cache:k,client:b}),variables:o,fragmentMatcher:s,defaultOperationType:w,exportedVariables:{},selectionsToResolve:h,onlyRunForcedResolvers:u},[2,this.resolveSelectionSet(f.selectionSet,!1,i,x).then(function(z){return{result:z,exportedVariables:x.exportedVariables}})]})})},n.prototype.resolveSelectionSet=function(e,t,r,i){return(0,c.mG)(this,void 0,void 0,function(){var a,o,s,u,l=this;return(0,c.Jh)(this,function(p){return a=i.fragmentMap,o=i.context,s=i.variables,u=[r],[2,Promise.all(e.selections.map(function(h){return(0,c.mG)(l,void 0,void 0,function(){var v;return(0,c.Jh)(this,function(_){return(t||i.selectionsToResolve.has(h))&&$(h,s)?(0,K.My)(h)?[2,this.resolveField(h,t,r,i).then(function(k){var b;typeof k<"u"&&u.push(((b={})[(0,K.u2)(h)]=k,b))})]:((0,K.Ao)(h)?v=h:(0,D.kG)(v=a[h.name.value],18,h.name.value),v&&v.typeCondition&&i.fragmentMatcher(r,v.typeCondition.name.value,o)?[2,this.resolveSelectionSet(v.selectionSet,t,r,i).then(function(k){u.push(k)})]:[2]):[2]})})})).then(function(){return je(u)})]})})},n.prototype.resolveField=function(e,t,r,i){return(0,c.mG)(this,void 0,void 0,function(){var a,o,s,u,f,l,p,h,v,w=this;return(0,c.Jh)(this,function(_){return r?(a=i.variables,o=e.name.value,s=(0,K.u2)(e),u=o!==s,f=r[s]||r[o],l=Promise.resolve(f),(!i.onlyRunForcedResolvers||this.shouldForceResolvers(e))&&(p=r.__typename||i.defaultOperationType,(h=this.resolvers&&this.resolvers[p])&&(v=h[u?o:s])&&(l=Promise.resolve(Ut.withValue(this.cache,v,[r,(0,K.NC)(e,a),i.context,{field:e,fragmentMap:i.fragmentMap}])))),[2,l.then(function(k){var b,x;if(void 0===k&&(k=f),e.directives&&e.directives.forEach(function(G){"export"===G.name.value&&G.arguments&&G.arguments.forEach(function(z){"as"===z.name.value&&"StringValue"===z.value.kind&&(i.exportedVariables[z.value.value]=k)})}),!e.selectionSet||null==k)return k;var S=null!==(x=null===(b=e.directives)||void 0===b?void 0:b.some(function(G){return"client"===G.name.value}))&&void 0!==x&&x;return Array.isArray(k)?w.resolveSubSelectedArray(e,t||S,k,i):e.selectionSet?w.resolveSelectionSet(e.selectionSet,t||S,k,i):void 0})]):[2,null]})})},n.prototype.resolveSubSelectedArray=function(e,t,r,i){var a=this;return Promise.all(r.map(function(o){return null===o?null:Array.isArray(o)?a.resolveSubSelectedArray(e,t,o,i):e.selectionSet?a.resolveSelectionSet(e.selectionSet,t,o,i):void 0}))},n.prototype.collectSelectionsToResolve=function(e,t){var r=function(o){return!Array.isArray(o)},i=this.selectionsToResolveCache;return function a(o){if(!i.has(o)){var s=new Set;i.set(o,s),(0,Q.visit)(o,{Directive:function(u,f,l,p,h){"client"===u.name.value&&h.forEach(function(v){r(v)&&(0,Q.isSelectionNode)(v)&&s.add(v)})},FragmentSpread:function(u,f,l,p,h){var v=t[u.name.value];(0,D.kG)(v,19,u.name.value);var w=a(v);w.size>0&&(h.forEach(function(_){r(_)&&(0,Q.isSelectionNode)(_)&&s.add(_)}),s.add(u),w.forEach(function(_){s.add(_)}))}})}return i.get(o)}(e)},n}();function bt(n,e){return Ke(n,e,e.variables&&{variables:Ke((0,c.pi)((0,c.pi)({},n&&n.variables),e.variables))})}var Kt,hn=!1,pn=function(){function n(e){var t=this;if(this.resetStoreCallbacks=[],this.clearStoreCallbacks=[],!e.cache)throw(0,D._K)(15);var r=e.uri,o=e.cache,s=e.documentTransform,u=e.ssrMode,f=void 0!==u&&u,l=e.ssrForceFetchDelay,p=void 0===l?0:l,h=e.connectToDevTools,v=e.queryDeduplication,w=void 0===v||v,_=e.defaultOptions,k=e.defaultContext,b=e.assumeImmutableResults,x=void 0===b?o.assumeImmutableResults:b,S=e.resolvers,G=e.typeDefs,z=e.fragmentMatcher,Z=e.name,ce=e.version,se=e.devtools,ve=e.link;ve||(ve=r?new kr({uri:r,credentials:e.credentials,headers:e.headers}):V.i.empty()),this.link=ve,this.cache=o,this.disableNetworkFetches=f||p>0,this.queryDeduplication=w,this.defaultOptions=_||Object.create(null),this.typeDefs=G,this.devtoolsConfig=(0,c.pi)((0,c.pi)({},se),{enabled:se?.enabled||h}),void 0===this.devtoolsConfig.enabled&&(this.devtoolsConfig.enabled=!1!==globalThis.__DEV__),p&&setTimeout(function(){return t.disableNetworkFetches=!1},p),this.watchQuery=this.watchQuery.bind(this),this.query=this.query.bind(this),this.mutate=this.mutate.bind(this),this.watchFragment=this.watchFragment.bind(this),this.resetStore=this.resetStore.bind(this),this.reFetchObservableQueries=this.reFetchObservableQueries.bind(this),this.version=W.i,this.localState=new Ki({cache:o,client:this,resolvers:S,fragmentMatcher:z}),this.queryManager=new Bi({cache:this.cache,link:this.link,defaultOptions:this.defaultOptions,defaultContext:k,documentTransform:s,queryDeduplication:w,ssrMode:f,clientAwareness:{name:Z,version:ce},localState:this.localState,assumeImmutableResults:x,onBroadcast:this.devtoolsConfig.enabled?function(){t.devToolsHookCb&&t.devToolsHookCb({action:{},state:{queries:t.queryManager.getQueryStore(),mutations:t.queryManager.mutationStore||{}},dataWithOptimisticResults:t.cache.extract(!0)})}:void 0}),this.devtoolsConfig.enabled&&this.connectToDevTools()}return n.prototype.connectToDevTools=function(){if(!(typeof window>"u")){var e=window,t=Symbol.for("apollo.devtools");(e[t]=e[t]||[]).push(this),e.__APOLLO_CLIENT__=this,!hn&&!1!==globalThis.__DEV__&&(hn=!0,window.document&&window.top===window.self&&/^(https?|file):$/.test(window.location.protocol)&&setTimeout(function(){if(!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__){var r=window.navigator,i=r&&r.userAgent,a=void 0;"string"==typeof i&&(i.indexOf("Chrome/")>-1?a="https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm":i.indexOf("Firefox/")>-1&&(a="https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/")),a&&!1!==globalThis.__DEV__&&D.kG.log("Download the Apollo DevTools for a better development experience: %s",a)}},1e4))}},Object.defineProperty(n.prototype,"documentTransform",{get:function(){return this.queryManager.documentTransform},enumerable:!1,configurable:!0}),n.prototype.stop=function(){this.queryManager.stop()},n.prototype.watchQuery=function(e){return this.defaultOptions.watchQuery&&(e=bt(this.defaultOptions.watchQuery,e)),this.disableNetworkFetches&&("network-only"===e.fetchPolicy||"cache-and-network"===e.fetchPolicy)&&(e=(0,c.pi)((0,c.pi)({},e),{fetchPolicy:"cache-first"})),this.queryManager.watchQuery(e)},n.prototype.query=function(e){return this.defaultOptions.query&&(e=bt(this.defaultOptions.query,e)),(0,D.kG)("cache-and-network"!==e.fetchPolicy,16),this.disableNetworkFetches&&"network-only"===e.fetchPolicy&&(e=(0,c.pi)((0,c.pi)({},e),{fetchPolicy:"cache-first"})),this.queryManager.query(e)},n.prototype.mutate=function(e){return this.defaultOptions.mutate&&(e=bt(this.defaultOptions.mutate,e)),this.queryManager.mutate(e)},n.prototype.subscribe=function(e){return this.queryManager.startGraphQLSubscription(e)},n.prototype.readQuery=function(e,t){return void 0===t&&(t=!1),this.cache.readQuery(e,t)},n.prototype.watchFragment=function(e){return this.cache.watchFragment(e)},n.prototype.readFragment=function(e,t){return void 0===t&&(t=!1),this.cache.readFragment(e,t)},n.prototype.writeQuery=function(e){var t=this.cache.writeQuery(e);return!1!==e.broadcast&&this.queryManager.broadcastQueries(),t},n.prototype.writeFragment=function(e){var t=this.cache.writeFragment(e);return!1!==e.broadcast&&this.queryManager.broadcastQueries(),t},n.prototype.__actionHookForDevTools=function(e){this.devToolsHookCb=e},n.prototype.__requestRaw=function(e){return H(this.link,e)},n.prototype.resetStore=function(){var e=this;return Promise.resolve().then(function(){return e.queryManager.clearStore({discardWatches:!1})}).then(function(){return Promise.all(e.resetStoreCallbacks.map(function(t){return t()}))}).then(function(){return e.reFetchObservableQueries()})},n.prototype.clearStore=function(){var e=this;return Promise.resolve().then(function(){return e.queryManager.clearStore({discardWatches:!0})}).then(function(){return Promise.all(e.clearStoreCallbacks.map(function(t){return t()}))})},n.prototype.onResetStore=function(e){var t=this;return this.resetStoreCallbacks.push(e),function(){t.resetStoreCallbacks=t.resetStoreCallbacks.filter(function(r){return r!==e})}},n.prototype.onClearStore=function(e){var t=this;return this.clearStoreCallbacks.push(e),function(){t.clearStoreCallbacks=t.clearStoreCallbacks.filter(function(r){return r!==e})}},n.prototype.reFetchObservableQueries=function(e){return this.queryManager.reFetchObservableQueries(e)},n.prototype.refetchQueries=function(e){var t=this.queryManager.refetchQueries(e),r=[],i=[];t.forEach(function(o,s){r.push(s),i.push(o)});var a=Promise.all(i);return a.queries=r,a.results=i,a.catch(function(o){!1!==globalThis.__DEV__&&D.kG.debug(17,o)}),a},n.prototype.getObservableQueries=function(e){return void 0===e&&(e="active"),this.queryManager.getObservableQueries(e)},n.prototype.extract=function(e){return this.cache.extract(e)},n.prototype.restore=function(e){return this.cache.restore(e)},n.prototype.addResolvers=function(e){this.localState.addResolvers(e)},n.prototype.setResolvers=function(e){this.localState.setResolvers(e)},n.prototype.getResolvers=function(){return this.localState.getResolvers()},n.prototype.setLocalStateFragmentMatcher=function(e){this.localState.setFragmentMatcher(e)},n.prototype.setLink=function(e){this.link=this.queryManager.link=e},Object.defineProperty(n.prototype,"defaultContext",{get:function(){return this.queryManager.defaultContext},enumerable:!1,configurable:!0}),n}();!1!==globalThis.__DEV__&&(pn.prototype.getMemoryInternals=ht.su),Kt||(Kt={});var Yt=function(){function n(){this.assumeImmutableResults=!1,this.getFragmentDoc=it(Ae.Yk,{max:We.Q["cache.fragmentQueryDocuments"]||1e3,cache:Cr.k})}return n.prototype.batch=function(e){var i,t=this;return this.performTransaction(function(){return i=e.update(t)},"string"==typeof e.optimistic?e.optimistic:!1===e.optimistic?null:void 0),i},n.prototype.recordOptimisticTransaction=function(e,t){this.performTransaction(e,t)},n.prototype.transformDocument=function(e){return e},n.prototype.transformForLink=function(e){return e},n.prototype.identify=function(e){},n.prototype.gc=function(){return[]},n.prototype.modify=function(e){return!1},n.prototype.readQuery=function(e,t){return void 0===t&&(t=!!e.optimistic),this.read((0,c.pi)((0,c.pi)({},e),{rootId:e.id||"ROOT_QUERY",optimistic:t}))},n.prototype.watchFragment=function(e){var p,t=this,r=e.fragment,i=e.fragmentName,a=e.from,o=e.optimistic,s=void 0===o||o,u=(0,c._T)(e,["fragment","fragmentName","from","optimistic"]),f=this.getFragmentDoc(r,i),l=(0,c.pi)((0,c.pi)({},u),{returnPartialData:!0,id:typeof a>"u"||"string"==typeof a?a:this.identify(a),query:f,optimistic:s});return new j.y(function(h){return t.watch((0,c.pi)((0,c.pi)({},l),{immediate:!0,callback:function(v){if(!p||!tn(f,{data:p?.result},{data:v.result})){var w={data:v.result,complete:!!v.complete};v.missing&&(w.missing=je(v.missing.map(function(_){return _.missing}))),p=v,h.next(w)}}}))})},n.prototype.readFragment=function(e,t){return void 0===t&&(t=!!e.optimistic),this.read((0,c.pi)((0,c.pi)({},e),{query:this.getFragmentDoc(e.fragment,e.fragmentName),rootId:e.id,optimistic:t}))},n.prototype.writeQuery=function(e){var t=e.id,r=e.data,i=(0,c._T)(e,["id","data"]);return this.write(Object.assign(i,{dataId:t||"ROOT_QUERY",result:r}))},n.prototype.writeFragment=function(e){var t=e.id,r=e.data,i=e.fragment,a=e.fragmentName,o=(0,c._T)(e,["id","data","fragment","fragmentName"]);return this.write(Object.assign(o,{query:this.getFragmentDoc(i,a),dataId:t,result:r}))},n.prototype.updateQuery=function(e,t){return this.batch({update:function(r){var i=r.readQuery(e),a=t(i);return null==a?i:(r.writeQuery((0,c.pi)((0,c.pi)({},e),{data:a})),a)}})},n.prototype.updateFragment=function(e,t){return this.batch({update:function(r){var i=r.readFragment(e),a=t(i);return null==a?i:(r.writeFragment((0,c.pi)((0,c.pi)({},e),{data:a})),a)}})},n}();!1!==globalThis.__DEV__&&(Yt.prototype.getMemoryInternals=ht.Kb);var Ht=function(n){function e(t,r,i,a){var o,s=n.call(this,t)||this;if(s.message=t,s.path=r,s.query=i,s.variables=a,Array.isArray(s.path)){s.missing=s.message;for(var u=s.path.length-1;u>=0;--u)s.missing=((o={})[s.path[u]]=s.missing,o)}else s.missing=s.path;return s.__proto__=e.prototype,s}return(0,c.ZT)(e,n),e}(Error);function Jt(n){return!1!==globalThis.__DEV__&&function Yi(n){var e=new Set([n]);return e.forEach(function(t){(0,m.s)(t)&&function Hi(n){if(!1!==globalThis.__DEV__&&!Object.isFrozen(n))try{Object.freeze(n)}catch(e){if(e instanceof TypeError)return null;throw e}return n}(t)===t&&Object.getOwnPropertyNames(t).forEach(function(r){(0,m.s)(t[r])&&e.add(t[r])})}),n}(n),n}var Te=Object.prototype.hasOwnProperty;function ut(n){return null==n}function $t(n,e){var t=n.__typename,r=n.id,i=n._id;if("string"==typeof t&&(e&&(e.keyObject=ut(r)?ut(i)?void 0:{_id:i}:{id:r}),ut(r)&&!ut(i)&&(r=i),!ut(r)))return"".concat(t,":").concat("number"==typeof r||"string"==typeof r?r:JSON.stringify(r))}var vn={dataIdFromObject:$t,addTypename:!0,resultCaching:!0,canonizeResults:!1};function dn(n){var e=n.canonizeResults;return void 0===e?vn.canonizeResults:e}var yn=/^[_a-z][_0-9a-z]*/i;function Ve(n){var e=n.match(yn);return e?e[0]:n}function Zt(n,e,t){return!!(0,m.s)(e)&&(le(e)?e.every(function(r){return Zt(n,r,t)}):n.selections.every(function(r){if((0,K.My)(r)&&$(r,t)){var i=(0,K.u2)(r);return Te.call(e,i)&&(!r.selectionSet||Zt(r.selectionSet,e[i],t))}return!0}))}function He(n){return(0,m.s)(n)&&!(0,K.Yk)(n)&&!le(n)}function mn(n,e){var t=(0,Ae.F)((0,ge.kU)(n));return{fragmentMap:t,lookupFragment:function(r){var i=t[r];return!i&&e&&(i=e.lookup(r)),i||null}}}var n,e,_t=Object.create(null),Xt=function(){return _t},gn=Object.create(null),ct=function(){function n(e,t){var r=this;this.policies=e,this.group=t,this.data=Object.create(null),this.rootIds=Object.create(null),this.refs=Object.create(null),this.getFieldValue=function(i,a){return Jt((0,K.Yk)(i)?r.get(i.__ref,a):i&&i[a])},this.canRead=function(i){return(0,K.Yk)(i)?r.has(i.__ref):"object"==typeof i},this.toReference=function(i,a){if("string"==typeof i)return(0,K.kQ)(i);if((0,K.Yk)(i))return i;var o=r.policies.identify(i)[0];if(o){var s=(0,K.kQ)(o);return a&&r.merge(o,i),s}}}return n.prototype.toObject=function(){return(0,c.pi)({},this.data)},n.prototype.has=function(e){return void 0!==this.lookup(e,!0)},n.prototype.get=function(e,t){if(this.group.depend(e,t),Te.call(this.data,e)){var r=this.data[e];if(r&&Te.call(r,t))return r[t]}return"__typename"===t&&Te.call(this.policies.rootTypenamesById,e)?this.policies.rootTypenamesById[e]:this instanceof Ne?this.parent.get(e,t):void 0},n.prototype.lookup=function(e,t){return t&&this.group.depend(e,"__exists"),Te.call(this.data,e)?this.data[e]:this instanceof Ne?this.parent.lookup(e,t):this.policies.rootTypenamesById[e]?Object.create(null):void 0},n.prototype.merge=function(e,t){var i,r=this;(0,K.Yk)(e)&&(e=e.__ref),(0,K.Yk)(t)&&(t=t.__ref);var a="string"==typeof e?this.lookup(i=e):e,o="string"==typeof t?this.lookup(i=t):t;if(o){(0,D.kG)("string"==typeof i,1);var s=new we(ea).merge(a,o);if(this.data[i]=s,s!==a&&(delete this.refs[i],this.group.caching)){var u=Object.create(null);a||(u.__exists=1),Object.keys(o).forEach(function(f){if(!a||a[f]!==s[f]){u[f]=1;var l=Ve(f);l!==f&&!r.policies.hasKeyArgs(s.__typename,l)&&(u[l]=1),void 0===s[f]&&!(r instanceof Ne)&&delete s[f]}}),u.__typename&&!(a&&a.__typename)&&this.policies.rootTypenamesById[i]===s.__typename&&delete u.__typename,Object.keys(u).forEach(function(f){return r.group.dirty(i,f)})}}},n.prototype.modify=function(e,t){var r=this,i=this.lookup(e);if(i){var a=Object.create(null),o=!1,s=!0,u={DELETE:_t,INVALIDATE:gn,isReference:K.Yk,toReference:this.toReference,canRead:this.canRead,readField:function(f,l){return r.policies.readField("string"==typeof f?{fieldName:f,from:l||(0,K.kQ)(e)}:f,{store:r})}};if(Object.keys(i).forEach(function(f){var l=Ve(f),p=i[f];if(void 0!==p){var h="function"==typeof t?t:t[f]||t[l];if(h){var v=h===Xt?_t:h(Jt(p),(0,c.pi)((0,c.pi)({},u),{fieldName:l,storeFieldName:f,storage:r.getStorage(e,f)}));if(v===gn)r.group.dirty(e,f);else if(v===_t&&(v=void 0),v!==p&&(a[f]=v,o=!0,p=v,!1!==globalThis.__DEV__)){var w=function(z){if(void 0===r.lookup(z.__ref))return!1!==globalThis.__DEV__&&D.kG.warn(2,z),!0};if((0,K.Yk)(v))w(v);else if(Array.isArray(v))for(var _=!1,k=void 0,b=0,x=v;b<x.length;b++){var S=x[b];if((0,K.Yk)(S)){if(_=!0,w(S))break}else"object"==typeof S&&S&&r.policies.identify(S)[0]&&(k=S);if(_&&void 0!==k){!1!==globalThis.__DEV__&&D.kG.warn(3,k);break}}}}void 0!==p&&(s=!1)}}),o)return this.merge(e,a),s&&(this instanceof Ne?this.data[e]=void 0:delete this.data[e],this.group.dirty(e,"__exists")),!0}return!1},n.prototype.delete=function(e,t,r){var i,a=this.lookup(e);if(a){var o=this.getFieldValue(a,"__typename"),s=t&&r?this.policies.getStoreFieldName({typename:o,fieldName:t,args:r}):t;return this.modify(e,s?((i={})[s]=Xt,i):Xt)}return!1},n.prototype.evict=function(e,t){var r=!1;return e.id&&(Te.call(this.data,e.id)&&(r=this.delete(e.id,e.fieldName,e.args)),this instanceof Ne&&this!==t&&(r=this.parent.evict(e,t)||r),(e.fieldName||r)&&this.group.dirty(e.id,e.fieldName||"__exists")),r},n.prototype.clear=function(){this.replace(null)},n.prototype.extract=function(){var e=this,t=this.toObject(),r=[];return this.getRootIdSet().forEach(function(i){Te.call(e.policies.rootTypenamesById,i)||r.push(i)}),r.length&&(t.__META={extraRootIds:r.sort()}),t},n.prototype.replace=function(e){var t=this;if(Object.keys(this.data).forEach(function(a){e&&Te.call(e,a)||t.delete(a)}),e){var r=e.__META,i=(0,c._T)(e,["__META"]);Object.keys(i).forEach(function(a){t.merge(a,i[a])}),r&&r.extraRootIds.forEach(this.retain,this)}},n.prototype.retain=function(e){return this.rootIds[e]=(this.rootIds[e]||0)+1},n.prototype.release=function(e){if(this.rootIds[e]>0){var t=--this.rootIds[e];return t||delete this.rootIds[e],t}return 0},n.prototype.getRootIdSet=function(e){return void 0===e&&(e=new Set),Object.keys(this.rootIds).forEach(e.add,e),this instanceof Ne?this.parent.getRootIdSet(e):Object.keys(this.policies.rootTypenamesById).forEach(e.add,e),e},n.prototype.gc=function(){var e=this,t=this.getRootIdSet(),r=this.toObject();t.forEach(function(o){Te.call(r,o)&&(Object.keys(e.findChildRefIds(o)).forEach(t.add,t),delete r[o])});var i=Object.keys(r);if(i.length){for(var a=this;a instanceof Ne;)a=a.parent;i.forEach(function(o){return a.delete(o)})}return i},n.prototype.findChildRefIds=function(e){if(!Te.call(this.refs,e)){var t=this.refs[e]=Object.create(null),r=this.data[e];if(!r)return t;var i=new Set([r]);i.forEach(function(a){(0,K.Yk)(a)&&(t[a.__ref]=!0),(0,m.s)(a)&&Object.keys(a).forEach(function(o){var s=a[o];(0,m.s)(s)&&i.add(s)})})}return this.refs[e]},n.prototype.makeCacheKey=function(){return this.group.keyMaker.lookupArray(arguments)},n}(),bn=function(){function n(e,t){void 0===t&&(t=null),this.caching=e,this.parent=t,this.d=null,this.resetCaching()}return n.prototype.resetCaching=function(){this.d=this.caching?Hr():null,this.keyMaker=new qe(ue)},n.prototype.depend=function(e,t){if(this.d){this.d(er(e,t));var r=Ve(t);r!==t&&this.d(er(e,r)),this.parent&&this.parent.depend(e,t)}},n.prototype.dirty=function(e,t){this.d&&this.d.dirty(er(e,t),"__exists"===t?"forget":"setDirty")},n}();function er(n,e){return e+"#"+n}function _n(n,e){ft(n)&&n.group.depend(e,"__exists")}e=function(t){function r(i){var o=i.resultCaching,u=i.seed,f=t.call(this,i.policies,new bn(void 0===o||o))||this;return f.stump=new Xi(f),f.storageTrie=new qe(ue),u&&f.replace(u),f}return(0,c.ZT)(r,t),r.prototype.addLayer=function(i,a){return this.stump.addLayer(i,a)},r.prototype.removeLayer=function(){return this},r.prototype.getStorage=function(){return this.storageTrie.lookupArray(arguments)},r}(n=ct||(ct={})),n.Root=e;var Ne=function(n){function e(t,r,i,a){var o=n.call(this,r.policies,a)||this;return o.id=t,o.parent=r,o.replay=i,o.group=a,i(o),o}return(0,c.ZT)(e,n),e.prototype.addLayer=function(t,r){return new e(t,this,r,this.group)},e.prototype.removeLayer=function(t){var r=this,i=this.parent.removeLayer(t);return t===this.id?(this.group.caching&&Object.keys(this.data).forEach(function(a){var o=r.data[a],s=i.lookup(a);s?o?o!==s&&Object.keys(o).forEach(function(u){be(o[u],s[u])||r.group.dirty(a,u)}):(r.group.dirty(a,"__exists"),Object.keys(s).forEach(function(u){r.group.dirty(a,u)})):r.delete(a)}),i):i===this.parent?this:i.addLayer(this.id,this.replay)},e.prototype.toObject=function(){return(0,c.pi)((0,c.pi)({},this.parent.toObject()),this.data)},e.prototype.findChildRefIds=function(t){var r=this.parent.findChildRefIds(t);return Te.call(this.data,t)?(0,c.pi)((0,c.pi)({},r),n.prototype.findChildRefIds.call(this,t)):r},e.prototype.getStorage=function(){for(var t=this.parent;t.parent;)t=t.parent;return t.getStorage.apply(t,arguments)},e}(ct),Xi=function(n){function e(t){return n.call(this,"EntityStore.Stump",t,function(){},new bn(t.group.caching,t.group))||this}return(0,c.ZT)(e,n),e.prototype.removeLayer=function(){return this},e.prototype.merge=function(t,r){return this.parent.merge(t,r)},e}(Ne);function ea(n,e,t){var r=n[t],i=e[t];return be(r,i)?r:i}function ft(n){return!!(n instanceof ct&&n.group.caching)}var wn=function(){function n(){this.known=new(de?WeakSet:Set),this.pool=new qe(ue),this.passes=new WeakMap,this.keysByJSON=new Map,this.empty=this.admit({})}return n.prototype.isKnown=function(e){return(0,m.s)(e)&&this.known.has(e)},n.prototype.pass=function(e){if((0,m.s)(e)){var t=function ta(n){return(0,m.s)(n)?le(n)?n.slice(0):(0,c.pi)({__proto__:Object.getPrototypeOf(n)},n):n}(e);return this.passes.set(t,e),t}return e},n.prototype.admit=function(e){var t=this;if((0,m.s)(e)){var r=this.passes.get(e);if(r)return r;switch(Object.getPrototypeOf(e)){case Array.prototype:if(this.known.has(e))return e;var a=e.map(this.admit,this);return(o=this.pool.lookupArray(a)).array||(this.known.add(o.array=a),!1!==globalThis.__DEV__&&Object.freeze(a)),o.array;case null:case Object.prototype:if(this.known.has(e))return e;var s=Object.getPrototypeOf(e),u=[s],f=this.sortedKeys(e);u.push(f.json);var o,l=u.length;if(f.sorted.forEach(function(v){u.push(t.admit(e[v]))}),!(o=this.pool.lookupArray(u)).object){var p=o.object=Object.create(s);this.known.add(p),f.sorted.forEach(function(v,w){p[v]=u[l+w]}),!1!==globalThis.__DEV__&&Object.freeze(p)}return o.object}}return e},n.prototype.sortedKeys=function(e){var t=Object.keys(e),r=this.pool.lookupArray(t);if(!r.keys){t.sort();var i=JSON.stringify(t);(r.keys=this.keysByJSON.get(i))||this.keysByJSON.set(i,r.keys={sorted:t,json:i})}return r.keys},n}();function On(n){return[n.selectionSet,n.objectOrReference,n.context,n.context.canonizeResults]}var ra=function(){function n(e){var t=this;this.knownResults=new(ue?WeakMap:Map),this.config=Ke(e,{addTypename:!1!==e.addTypename,canonizeResults:dn(e)}),this.canon=e.canon||new wn,this.executeSelectionSet=it(function(r){var i,a=r.context.canonizeResults,o=On(r);o[3]=!a;var s=(i=t.executeSelectionSet).peek.apply(i,o);return s?a?(0,c.pi)((0,c.pi)({},s),{result:t.canon.admit(s.result)}):s:(_n(r.context.store,r.enclosingRef.__ref),t.execSelectionSetImpl(r))},{max:this.config.resultCacheMaxSize||We.Q["inMemoryCache.executeSelectionSet"]||5e4,keyArgs:On,makeCacheKey:function(r,i,a,o){if(ft(a.store))return a.store.makeCacheKey(r,(0,K.Yk)(i)?i.__ref:i,a.varString,o)}}),this.executeSubSelectedArray=it(function(r){return _n(r.context.store,r.enclosingRef.__ref),t.execSubSelectedArrayImpl(r)},{max:this.config.resultCacheMaxSize||We.Q["inMemoryCache.executeSubSelectedArray"]||1e4,makeCacheKey:function(r){var i=r.field,a=r.array,o=r.context;if(ft(o.store))return o.store.makeCacheKey(i,a,o.varString)}})}return n.prototype.resetCanon=function(){this.canon=new wn},n.prototype.diffQueryAgainstStore=function(e){var t=e.store,r=e.query,i=e.rootId,a=void 0===i?"ROOT_QUERY":i,o=e.variables,s=e.returnPartialData,u=void 0===s||s,f=e.canonizeResults,l=void 0===f?this.config.canonizeResults:f,p=this.config.cache.policies;o=(0,c.pi)((0,c.pi)({},(0,ge.O4)((0,ge.iW)(r))),o);var w,h=(0,K.kQ)(a),v=this.executeSelectionSet({selectionSet:(0,ge.p$)(r).selectionSet,objectOrReference:h,enclosingRef:h,context:(0,c.pi)({store:t,query:r,policies:p,variables:o,varString:(0,Be.B)(o),canonizeResults:l},mn(r,this.config.fragments))});if(v.missing&&(w=[new Ht(na(v.missing),v.missing,r,o)],!u))throw w[0];return{result:v.result,complete:!w,missing:w}},n.prototype.isFresh=function(e,t,r,i){if(ft(i.store)&&this.knownResults.get(e)===r){var a=this.executeSelectionSet.peek(r,t,i,this.canon.isKnown(e));if(a&&e===a.result)return!0}return!1},n.prototype.execSelectionSetImpl=function(e){var t=this,r=e.selectionSet,i=e.objectOrReference,a=e.enclosingRef,o=e.context;if((0,K.Yk)(i)&&!o.policies.rootTypenamesById[i.__ref]&&!o.store.has(i.__ref))return{result:this.canon.empty,missing:"Dangling reference to missing ".concat(i.__ref," object")};var h,s=o.variables,u=o.policies,l=o.store.getFieldValue(i,"__typename"),p=[],v=new we;function w(S,G){var z;return S.missing&&(h=v.merge(h,((z={})[G]=S.missing,z))),S.result}this.config.addTypename&&"string"==typeof l&&!u.rootIdsByTypename[l]&&p.push({__typename:l});var _=new Set(r.selections);_.forEach(function(S){var G,z;if($(S,s))if((0,K.My)(S)){var Z=u.readField({fieldName:S.name.value,field:S,variables:o.variables,from:i},o),ce=(0,K.u2)(S);void 0===Z?Ft.added(S)||(h=v.merge(h,((G={})[ce]="Can't find field '".concat(S.name.value,"' on ").concat((0,K.Yk)(i)?i.__ref+" object":"object "+JSON.stringify(i,null,2)),G))):le(Z)?Z.length>0&&(Z=w(t.executeSubSelectedArray({field:S,array:Z,enclosingRef:a,context:o}),ce)):S.selectionSet?null!=Z&&(Z=w(t.executeSelectionSet({selectionSet:S.selectionSet,objectOrReference:Z,enclosingRef:(0,K.Yk)(Z)?Z:a,context:o}),ce)):o.canonizeResults&&(Z=t.canon.pass(Z)),void 0!==Z&&p.push(((z={})[ce]=Z,z))}else{var se=(0,Ae.hi)(S,o.lookupFragment);if(!se&&S.kind===Q.Kind.FRAGMENT_SPREAD)throw(0,D._K)(9,S.name.value);se&&u.fragmentMatches(se,l)&&se.selectionSet.selections.forEach(_.add,_)}});var b={result:je(p),missing:h},x=o.canonizeResults?this.canon.admit(b):Jt(b);return x.result&&this.knownResults.set(x.result,r),x},n.prototype.execSubSelectedArrayImpl=function(e){var s,t=this,r=e.field,i=e.array,a=e.enclosingRef,o=e.context,u=new we;function f(l,p){var h;return l.missing&&(s=u.merge(s,((h={})[p]=l.missing,h))),l.result}return r.selectionSet&&(i=i.filter(o.store.canRead)),i=i.map(function(l,p){return null===l?null:le(l)?f(t.executeSubSelectedArray({field:r,array:l,enclosingRef:a,context:o}),p):r.selectionSet?f(t.executeSelectionSet({selectionSet:r.selectionSet,objectOrReference:l,enclosingRef:(0,K.Yk)(l)?l:a,context:o}),p):(!1!==globalThis.__DEV__&&function ia(n,e,t){if(!e.selectionSet){var r=new Set([t]);r.forEach(function(i){(0,m.s)(i)&&((0,D.kG)(!(0,K.Yk)(i),10,function $i(n,e){return(0,K.Yk)(e)?n.get(e.__ref,"__typename"):e&&e.__typename}(n,i),e.name.value),Object.values(i).forEach(r.add,r))})}}(o.store,r,l),l)}),{result:o.canonizeResults?this.canon.admit(i):i,missing:s}},n}();function na(n){try{JSON.stringify(n,function(e,t){if("string"==typeof t)throw t;return t})}catch(e){return e}}var aa=I(69753),En=Object.create(null);function tr(n){var e=JSON.stringify(n);return En[e]||(En[e]=Object.create(null))}function Sn(n){var e=tr(n);return e.keyFieldsFn||(e.keyFieldsFn=function(t,r){var i=function(o,s){return r.readField(s,o)},a=r.keyObject=rr(n,function(o){var s=Je(r.storeObject,o,i);return void 0===s&&t!==r.storeObject&&Te.call(t,o[0])&&(s=Je(t,o,Tn)),(0,D.kG)(void 0!==s,4,o.join("."),t),s});return"".concat(r.typename,":").concat(JSON.stringify(a))})}function kn(n){var e=tr(n);return e.keyArgsFn||(e.keyArgsFn=function(t,r){var i=r.field,a=r.variables,o=r.fieldName,s=rr(n,function(f){var l=f[0],p=l.charAt(0);if("@"!==p)if("$"!==p){if(t)return Je(t,f)}else{var _=l.slice(1);if(a&&Te.call(a,_)){var k=f.slice(0);return k[0]=_,Je(a,k)}}else if(i&&te(i.directives)){var h=l.slice(1),v=i.directives.find(function(b){return b.name.value===h}),w=v&&(0,K.NC)(v,a);return w&&Je(w,f.slice(1))}}),u=JSON.stringify(s);return(t||"{}"!==u)&&(o+=":"+u),o})}function rr(n,e){var t=new we;return Dn(n).reduce(function(r,i){var a,o=e(i);if(void 0!==o){for(var s=i.length-1;s>=0;--s)(a={})[i[s]]=o,o=a;r=t.merge(r,o)}return r},Object.create(null))}function Dn(n){var e=tr(n);if(!e.paths){var t=e.paths=[],r=[];n.forEach(function(i,a){le(i)?(Dn(i).forEach(function(o){return t.push(r.concat(o))}),r.length=0):(r.push(i),le(n[a+1])||(t.push(r.slice(0)),r.length=0))})}return e.paths}function Tn(n,e){return n[e]}function Je(n,e,t){return t=t||Tn,Fn(e.reduce(function r(i,a){return le(i)?i.map(function(o){return r(o,a)}):i&&t(i,a)},n))}function Fn(n){return(0,m.s)(n)?le(n)?n.map(Fn):rr(Object.keys(n).sort(),function(e){return Je(n,e)}):n}function nr(n){return void 0!==n.args?n.args:n.field?(0,K.NC)(n.field,n.variables):null}var oa=function(){},Pn=function(n,e){return e.fieldName},Rn=function(n,e,t){return(0,t.mergeObjects)(n,e)},Mn=function(n,e){return e},sa=function(){function n(e){this.config=e,this.typePolicies=Object.create(null),this.toBeAdded=Object.create(null),this.supertypeMap=new Map,this.fuzzySubtypes=new Map,this.rootIdsByTypename=Object.create(null),this.rootTypenamesById=Object.create(null),this.usingPossibleTypes=!1,this.config=(0,c.pi)({dataIdFromObject:$t},e),this.cache=this.config.cache,this.setRootTypename("Query"),this.setRootTypename("Mutation"),this.setRootTypename("Subscription"),e.possibleTypes&&this.addPossibleTypes(e.possibleTypes),e.typePolicies&&this.addTypePolicies(e.typePolicies)}return n.prototype.identify=function(e,t){var r,i=this,a=t&&(t.typename||(null===(r=t.storeObject)||void 0===r?void 0:r.__typename))||e.__typename;if(a===this.rootTypenamesById.ROOT_QUERY)return["ROOT_QUERY"];for(var u,o=t&&t.storeObject||e,s=(0,c.pi)((0,c.pi)({},t),{typename:a,storeObject:o,readField:t&&t.readField||function(){var h=ir(arguments,o);return i.readField(h,{store:i.cache.data,variables:h.variables})}}),f=a&&this.getTypePolicy(a),l=f&&f.keyFn||this.config.dataIdFromObject;l;){var p=l((0,c.pi)((0,c.pi)({},e),o),s);if(!le(p)){u=p;break}l=Sn(p)}return u=u?String(u):void 0,s.keyObject?[u,s.keyObject]:[u]},n.prototype.addTypePolicies=function(e){var t=this;Object.keys(e).forEach(function(r){var i=e[r],a=i.queryType,o=i.mutationType,s=i.subscriptionType,u=(0,c._T)(i,["queryType","mutationType","subscriptionType"]);a&&t.setRootTypename("Query",r),o&&t.setRootTypename("Mutation",r),s&&t.setRootTypename("Subscription",r),Te.call(t.toBeAdded,r)?t.toBeAdded[r].push(u):t.toBeAdded[r]=[u]})},n.prototype.updateTypePolicy=function(e,t){var r=this,i=this.getTypePolicy(e),a=t.keyFields,o=t.fields;function s(u,f){u.merge="function"==typeof f?f:!0===f?Rn:!1===f?Mn:u.merge}s(i,t.merge),i.keyFn=!1===a?oa:le(a)?Sn(a):"function"==typeof a?a:i.keyFn,o&&Object.keys(o).forEach(function(u){var f=r.getFieldPolicy(e,u,!0),l=o[u];if("function"==typeof l)f.read=l;else{var p=l.keyArgs,h=l.read,v=l.merge;f.keyFn=!1===p?Pn:le(p)?kn(p):"function"==typeof p?p:f.keyFn,"function"==typeof h&&(f.read=h),s(f,v)}f.read&&f.merge&&(f.keyFn=f.keyFn||Pn)})},n.prototype.setRootTypename=function(e,t){void 0===t&&(t=e);var r="ROOT_"+e.toUpperCase(),i=this.rootTypenamesById[r];t!==i&&((0,D.kG)(!i||i===e,5,e),i&&delete this.rootIdsByTypename[i],this.rootIdsByTypename[t]=r,this.rootTypenamesById[r]=t)},n.prototype.addPossibleTypes=function(e){var t=this;this.usingPossibleTypes=!0,Object.keys(e).forEach(function(r){t.getSupertypeSet(r,!0),e[r].forEach(function(i){t.getSupertypeSet(i,!0).add(r);var a=i.match(yn);(!a||a[0]!==i)&&t.fuzzySubtypes.set(i,new RegExp(i))})})},n.prototype.getTypePolicy=function(e){var t=this;if(!Te.call(this.typePolicies,e)){var r=this.typePolicies[e]=Object.create(null);r.fields=Object.create(null);var i=this.supertypeMap.get(e);!i&&this.fuzzySubtypes.size&&(i=this.getSupertypeSet(e,!0),this.fuzzySubtypes.forEach(function(o,s){if(o.test(e)){var u=t.supertypeMap.get(s);u&&u.forEach(function(f){return i.add(f)})}})),i&&i.size&&i.forEach(function(o){var s=t.getTypePolicy(o),u=s.fields,f=(0,c._T)(s,["fields"]);Object.assign(r,f),Object.assign(r.fields,u)})}var a=this.toBeAdded[e];return a&&a.length&&a.splice(0).forEach(function(o){t.updateTypePolicy(e,o)}),this.typePolicies[e]},n.prototype.getFieldPolicy=function(e,t,r){if(e){var i=this.getTypePolicy(e).fields;return i[t]||r&&(i[t]=Object.create(null))}},n.prototype.getSupertypeSet=function(e,t){var r=this.supertypeMap.get(e);return!r&&t&&this.supertypeMap.set(e,r=new Set),r},n.prototype.fragmentMatches=function(e,t,r,i){var a=this;if(!e.typeCondition)return!0;if(!t)return!1;var o=e.typeCondition.name.value;if(t===o)return!0;if(this.usingPossibleTypes&&this.supertypeMap.has(o))for(var s=this.getSupertypeSet(t,!0),u=[s],f=function(w){var _=a.getSupertypeSet(w,!1);_&&_.size&&u.indexOf(_)<0&&u.push(_)},l=!(!r||!this.fuzzySubtypes.size),p=!1,h=0;h<u.length;++h){var v=u[h];if(v.has(o))return s.has(o)||(p&&!1!==globalThis.__DEV__&&D.kG.warn(6,t,o),s.add(o)),!0;v.forEach(f),l&&h===u.length-1&&Zt(e.selectionSet,r,i)&&(l=!1,p=!0,this.fuzzySubtypes.forEach(function(w,_){var k=t.match(w);k&&k[0]===t&&f(_)}))}return!1},n.prototype.hasKeyArgs=function(e,t){var r=this.getFieldPolicy(e,t,!1);return!(!r||!r.keyFn)},n.prototype.getStoreFieldName=function(e){var a,t=e.typename,r=e.fieldName,i=this.getFieldPolicy(t,r,!1),o=i&&i.keyFn;if(o&&t)for(var s={typename:t,fieldName:r,field:e.field||null,variables:e.variables},u=nr(e);o;){var f=o(u,s);if(!le(f)){a=f||r;break}o=kn(f)}return void 0===a&&(a=e.field?(0,K.vf)(e.field,e.variables):(0,K.PT)(r,nr(e))),!1===a?r:r===Ve(a)?a:r+":"+a},n.prototype.readField=function(e,t){var r=e.from;if(r&&(e.field||e.fieldName)){if(void 0===e.typename){var a=t.store.getFieldValue(r,"__typename");a&&(e.typename=a)}var o=this.getStoreFieldName(e),s=Ve(o),u=t.store.getFieldValue(r,o),f=this.getFieldPolicy(e.typename,s,!1),l=f&&f.read;if(l){var p=Cn(this,r,e,t,t.store.getStorage((0,K.Yk)(r)?r.__ref:r,o));return Ut.withValue(this.cache,l,[u,p])}return u}},n.prototype.getReadFunction=function(e,t){var r=this.getFieldPolicy(e,t,!1);return r&&r.read},n.prototype.getMergeFunction=function(e,t,r){var i=this.getFieldPolicy(e,t,!1),a=i&&i.merge;return!a&&r&&(a=(i=this.getTypePolicy(r))&&i.merge),a},n.prototype.runMergeFunction=function(e,t,r,i,a){var o=r.field,s=r.typename,u=r.merge;return u===Rn?In(i.store)(e,t):u===Mn?t:(i.overwrite&&(e=void 0),u(e,t,Cn(this,void 0,{typename:s,fieldName:o.name.value,field:o,variables:i.variables},i,a||Object.create(null))))},n}();function Cn(n,e,t,r,i){var a=n.getStoreFieldName(t),o=Ve(a),s=t.variables||r.variables,u=r.store,f=u.toReference,l=u.canRead;return{args:nr(t),field:t.field||null,fieldName:o,storeFieldName:a,variables:s,isReference:K.Yk,toReference:f,storage:i,cache:n.cache,canRead:l,readField:function(){return n.readField(ir(arguments,e,s),r)},mergeObjects:In(r.store)}}function ir(n,e,t){var o,r=n[0];return"string"==typeof r?o={fieldName:r,from:n.length>1?n[1]:e}:(o=(0,c.pi)({},r),Te.call(o,"from")||(o.from=e)),!1!==globalThis.__DEV__&&void 0===o.from&&!1!==globalThis.__DEV__&&D.kG.warn(7,(0,aa.v)(Array.from(n))),void 0===o.variables&&(o.variables=t),o}function In(n){return function(t,r){if(le(t)||le(r))throw(0,D._K)(8);if((0,m.s)(t)&&(0,m.s)(r)){var i=n.getFieldValue(t,"__typename"),a=n.getFieldValue(r,"__typename");if(i&&a&&i!==a)return r;if((0,K.Yk)(t)&&He(r))return n.merge(t.__ref,r),t;if(He(t)&&(0,K.Yk)(r))return n.merge(t,r.__ref),r;if(He(t)&&He(r))return(0,c.pi)((0,c.pi)({},t),r)}return r}}function ar(n,e,t){var r="".concat(e).concat(t),i=n.flavors.get(r);return i||n.flavors.set(r,i=n.clientOnly===e&&n.deferred===t?n:(0,c.pi)((0,c.pi)({},n),{clientOnly:e,deferred:t})),i}var ua=function(){function n(e,t,r){this.cache=e,this.reader=t,this.fragments=r}return n.prototype.writeToStore=function(e,t){var r=this,i=t.query,a=t.result,o=t.dataId,s=t.variables,u=t.overwrite,f=(0,ge.$H)(i),l=function Zi(){return new we}();s=(0,c.pi)((0,c.pi)({},(0,ge.O4)(f)),s);var p=(0,c.pi)((0,c.pi)({store:e,written:Object.create(null),merge:function(v,w){return l.merge(v,w)},variables:s,varString:(0,Be.B)(s)},mn(i,this.fragments)),{overwrite:!!u,incomingById:new Map,clientOnly:!1,deferred:!1,flavors:new Map}),h=this.processSelectionSet({result:a||Object.create(null),dataId:o,selectionSet:f.selectionSet,mergeTree:{map:new Map},context:p});if(!(0,K.Yk)(h))throw(0,D._K)(11,a);return p.incomingById.forEach(function(v,w){var _=v.storeObject,k=v.mergeTree,b=v.fieldNodeSet,x=(0,K.kQ)(w);if(k&&k.map.size){var S=r.applyMerges(k,x,_,p);if((0,K.Yk)(S))return;_=S}if(!1!==globalThis.__DEV__&&!p.overwrite){var G=Object.create(null);b.forEach(function(ce){ce.selectionSet&&(G[ce.name.value]=!0)}),Object.keys(_).forEach(function(ce){(function(ce){return!0===G[Ve(ce)]})(ce)&&!function(ce){var se=k&&k.map.get(ce);return Boolean(se&&se.info&&se.info.merge)}(ce)&&function ca(n,e,t,r){var i=function(p){var h=r.getFieldValue(p,t);return"object"==typeof h&&h},a=i(n);if(a){var o=i(e);if(o&&!(0,K.Yk)(a)&&!be(a,o)&&!Object.keys(a).every(function(p){return void 0!==r.getFieldValue(o,p)})){var s=r.getFieldValue(n,"__typename")||r.getFieldValue(e,"__typename"),u=Ve(t),f="".concat(s,".").concat(u);if(!An.has(f)){An.add(f);var l=[];!le(a)&&!le(o)&&[a,o].forEach(function(p){var h=r.getFieldValue(p,"__typename");"string"==typeof h&&!l.includes(h)&&l.push(h)}),!1!==globalThis.__DEV__&&D.kG.warn(14,u,s,l.length?"either ensure all objects of type "+l.join(" and ")+" have an ID or a custom merge function, or ":"",f,(0,c.pi)({},a),(0,c.pi)({},o))}}}}(x,_,ce,p.store)})}e.merge(w,_)}),e.retain(h.__ref),h},n.prototype.processSelectionSet=function(e){var t=this,r=e.dataId,i=e.result,a=e.selectionSet,o=e.context,s=e.mergeTree,u=this.cache.policies,f=Object.create(null),l=r&&u.rootTypenamesById[r]||(0,K.qw)(i,a,o.fragmentMap)||r&&o.store.get(r,"__typename");"string"==typeof l&&(f.__typename=l);var p=function(){var S=ir(arguments,f,o.variables);if((0,K.Yk)(S.from)){var G=o.incomingById.get(S.from.__ref);if(G){var z=u.readField((0,c.pi)((0,c.pi)({},S),{from:G.storeObject}),o);if(void 0!==z)return z}}return u.readField(S,o)},h=new Set;this.flattenFields(a,i,o,l).forEach(function(S,G){var z,Z=(0,K.u2)(G),ce=i[Z];if(h.add(G),void 0!==ce){var se=u.getStoreFieldName({typename:l,fieldName:G.name.value,field:G,variables:S.variables}),ve=xn(s,se),Oe=t.processFieldValue(ce,G,G.selectionSet?ar(S,!1,!1):S,ve),Ze=void 0;G.selectionSet&&((0,K.Yk)(Oe)||He(Oe))&&(Ze=p("__typename",Oe));var Xe=u.getMergeFunction(l,G.name.value,Ze);Xe?ve.info={field:G,typename:l,merge:Xe}:Qn(s,se),f=S.merge(f,((z={})[se]=Oe,z))}else!1!==globalThis.__DEV__&&!S.clientOnly&&!S.deferred&&!Ft.added(G)&&!u.getReadFunction(l,G.name.value)&&!1!==globalThis.__DEV__&&D.kG.error(12,(0,K.u2)(G),i)});try{var v=u.identify(i,{typename:l,selectionSet:a,fragmentMap:o.fragmentMap,storeObject:f,readField:p}),_=v[1];r=r||v[0],_&&(f=o.merge(f,_))}catch(S){if(!r)throw S}if("string"==typeof r){var k=(0,K.kQ)(r),b=o.written[r]||(o.written[r]=[]);if(b.indexOf(a)>=0||(b.push(a),this.reader&&this.reader.isFresh(i,k,a,o)))return k;var x=o.incomingById.get(r);return x?(x.storeObject=o.merge(x.storeObject,f),x.mergeTree=or(x.mergeTree,s),h.forEach(function(S){return x.fieldNodeSet.add(S)})):o.incomingById.set(r,{storeObject:f,mergeTree:wt(s)?void 0:s,fieldNodeSet:h}),k}return f},n.prototype.processFieldValue=function(e,t,r,i){var a=this;return t.selectionSet&&null!==e?le(e)?e.map(function(o,s){var u=a.processFieldValue(o,t,r,xn(i,s));return Qn(i,s),u}):this.processSelectionSet({result:e,selectionSet:t.selectionSet,context:r,mergeTree:i}):!1!==globalThis.__DEV__?en(e):e},n.prototype.flattenFields=function(e,t,r,i){void 0===i&&(i=(0,K.qw)(t,e,r.fragmentMap));var a=new Map,o=this.cache.policies,s=new qe(!1);return function u(f,l){var p=s.lookup(f,l.clientOnly,l.deferred);p.visited||(p.visited=!0,f.selections.forEach(function(h){if($(h,r.variables)){var v=l.clientOnly,w=l.deferred;if(!(v&&w)&&te(h.directives)&&h.directives.forEach(function(b){var x=b.name.value;if("client"===x&&(v=!0),"defer"===x){var S=(0,K.NC)(b,r.variables);(!S||!1!==S.if)&&(w=!0)}}),(0,K.My)(h)){var _=a.get(h);_&&(v=v&&_.clientOnly,w=w&&_.deferred),a.set(h,ar(r,v,w))}else{var k=(0,Ae.hi)(h,r.lookupFragment);if(!k&&h.kind===Q.Kind.FRAGMENT_SPREAD)throw(0,D._K)(13,h.name.value);k&&o.fragmentMatches(k,i,t,r.variables)&&u(k.selectionSet,ar(r,v,w))}}}))}(e,r),a},n.prototype.applyMerges=function(e,t,r,i,a){var o,s=this;if(e.map.size&&!(0,K.Yk)(r)){var u=le(r)||!(0,K.Yk)(t)&&!He(t)?void 0:t,f=r;u&&!a&&(a=[(0,K.Yk)(u)?u.__ref:u]);var l,p=function(h,v){return le(h)?"number"==typeof v?h[v]:void 0:i.store.getFieldValue(h,String(v))};e.map.forEach(function(h,v){var w=p(u,v),_=p(f,v);if(void 0!==_){a&&a.push(v);var k=s.applyMerges(h,w,_,i,a);k!==_&&(l=l||new Map).set(v,k),a&&(0,D.kG)(a.pop()===v)}}),l&&(r=le(f)?f.slice(0):(0,c.pi)({},f),l.forEach(function(h,v){r[v]=h}))}return e.info?this.cache.policies.runMergeFunction(t,r,e.info,i,a&&(o=i.store).getStorage.apply(o,a)):r},n}(),jn=[];function xn(n,e){var t=n.map;return t.has(e)||t.set(e,jn.pop()||{map:new Map}),t.get(e)}function or(n,e){if(n===e||!e||wt(e))return n;if(!n||wt(n))return e;var t=n.info&&e.info?(0,c.pi)((0,c.pi)({},n.info),e.info):n.info||e.info,r=n.map.size&&e.map.size,a={info:t,map:r?new Map:n.map.size?n.map:e.map};if(r){var o=new Set(e.map.keys());n.map.forEach(function(s,u){a.map.set(u,or(s,e.map.get(u))),o.delete(u)}),o.forEach(function(s){a.map.set(s,or(e.map.get(s),n.map.get(s)))})}return a}function wt(n){return!n||!(n.info||n.map.size)}function Qn(n,e){var t=n.map,r=t.get(e);r&&wt(r)&&(jn.push(r),t.delete(e))}var An=new Set,qn=function(n){function e(t){void 0===t&&(t={});var r=n.call(this)||this;return r.watches=new Set,r.addTypenameTransform=new qt(Ft),r.assumeImmutableResults=!0,r.makeVar=ln,r.txCount=0,r.config=function Ji(n){return Ke(vn,n)}(t),r.addTypename=!!r.config.addTypename,r.policies=new sa({cache:r,dataIdFromObject:r.config.dataIdFromObject,possibleTypes:r.config.possibleTypes,typePolicies:r.config.typePolicies}),r.init(),r}return(0,c.ZT)(e,n),e.prototype.init=function(){var t=this.data=new ct.Root({policies:this.policies,resultCaching:this.config.resultCaching});this.optimisticData=t.stump,this.resetResultCache()},e.prototype.resetResultCache=function(t){var r=this,i=this.storeReader,a=this.config.fragments;this.storeWriter=new ua(this,this.storeReader=new ra({cache:this,addTypename:this.addTypename,resultCacheMaxSize:this.config.resultCacheMaxSize,canonizeResults:dn(this.config),canon:t?void 0:i&&i.canon,fragments:a}),a),this.maybeBroadcastWatch=it(function(o,s){return r.broadcastWatch(o,s)},{max:this.config.resultCacheMaxSize||We.Q["inMemoryCache.maybeBroadcastWatch"]||5e3,makeCacheKey:function(o){var s=o.optimistic?r.optimisticData:r.data;if(ft(s))return s.makeCacheKey(o.query,o.callback,(0,Be.B)({optimistic:o.optimistic,id:o.id,variables:o.variables}))}}),new Set([this.data.group,this.optimisticData.group]).forEach(function(o){return o.resetCaching()})},e.prototype.restore=function(t){return this.init(),t&&this.data.replace(t),this},e.prototype.extract=function(t){return void 0===t&&(t=!1),(t?this.optimisticData:this.data).extract()},e.prototype.read=function(t){var r=t.returnPartialData,i=void 0!==r&&r;try{return this.storeReader.diffQueryAgainstStore((0,c.pi)((0,c.pi)({},t),{store:t.optimistic?this.optimisticData:this.data,config:this.config,returnPartialData:i})).result||null}catch(a){if(a instanceof Ht)return null;throw a}},e.prototype.write=function(t){try{return++this.txCount,this.storeWriter.writeToStore(this.data,t)}finally{!--this.txCount&&!1!==t.broadcast&&this.broadcastWatches()}},e.prototype.modify=function(t){if(Te.call(t,"id")&&!t.id)return!1;var r=t.optimistic?this.optimisticData:this.data;try{return++this.txCount,r.modify(t.id||"ROOT_QUERY",t.fields)}finally{!--this.txCount&&!1!==t.broadcast&&this.broadcastWatches()}},e.prototype.diff=function(t){return this.storeReader.diffQueryAgainstStore((0,c.pi)((0,c.pi)({},t),{store:t.optimistic?this.optimisticData:this.data,rootId:t.id||"ROOT_QUERY",config:this.config}))},e.prototype.watch=function(t){var r=this;return this.watches.size||function Gi(n){st(n).vars.forEach(function(e){return e.attachCache(n)})}(this),this.watches.add(t),t.immediate&&this.maybeBroadcastWatch(t),function(){r.watches.delete(t)&&!r.watches.size&&fn(r),r.maybeBroadcastWatch.forget(t)}},e.prototype.gc=function(t){var r;Be.B.reset(),pt.reset(),this.addTypenameTransform.resetCache(),null===(r=this.config.fragments)||void 0===r||r.resetCaches();var i=this.optimisticData.gc();return t&&!this.txCount&&(t.resetResultCache?this.resetResultCache(t.resetResultIdentities):t.resetResultIdentities&&this.storeReader.resetCanon()),i},e.prototype.retain=function(t,r){return(r?this.optimisticData:this.data).retain(t)},e.prototype.release=function(t,r){return(r?this.optimisticData:this.data).release(t)},e.prototype.identify=function(t){if((0,K.Yk)(t))return t.__ref;try{return this.policies.identify(t)[0]}catch(r){!1!==globalThis.__DEV__&&D.kG.warn(r)}},e.prototype.evict=function(t){if(!t.id){if(Te.call(t,"id"))return!1;t=(0,c.pi)((0,c.pi)({},t),{id:"ROOT_QUERY"})}try{return++this.txCount,this.optimisticData.evict(t,this.data)}finally{!--this.txCount&&!1!==t.broadcast&&this.broadcastWatches()}},e.prototype.reset=function(t){var r=this;return this.init(),Be.B.reset(),t&&t.discardWatches?(this.watches.forEach(function(i){return r.maybeBroadcastWatch.forget(i)}),this.watches.clear(),fn(this)):this.broadcastWatches(),Promise.resolve()},e.prototype.removeOptimistic=function(t){var r=this.optimisticData.removeLayer(t);r!==this.optimisticData&&(this.optimisticData=r,this.broadcastWatches())},e.prototype.batch=function(t){var f,r=this,i=t.update,a=t.optimistic,o=void 0===a||a,s=t.removeOptimistic,u=t.onWatchUpdated,l=function(h){var w=r.data,_=r.optimisticData;++r.txCount,h&&(r.data=r.optimisticData=h);try{return f=i(r)}finally{--r.txCount,r.data=w,r.optimisticData=_}},p=new Set;return u&&!this.txCount&&this.broadcastWatches((0,c.pi)((0,c.pi)({},t),{onWatchUpdated:function(h){return p.add(h),!1}})),"string"==typeof o?this.optimisticData=this.optimisticData.addLayer(o,l):!1===o?l(this.data):l(),"string"==typeof s&&(this.optimisticData=this.optimisticData.removeLayer(s)),u&&p.size?(this.broadcastWatches((0,c.pi)((0,c.pi)({},t),{onWatchUpdated:function(h,v){var w=u.call(this,h,v);return!1!==w&&p.delete(h),w}})),p.size&&p.forEach(function(h){return r.maybeBroadcastWatch.dirty(h)})):this.broadcastWatches(t),f},e.prototype.performTransaction=function(t,r){return this.batch({update:t,optimistic:r||null!==r})},e.prototype.transformDocument=function(t){return this.addTypenameToDocument(this.addFragmentsToDocument(t))},e.prototype.broadcastWatches=function(t){var r=this;this.txCount||this.watches.forEach(function(i){return r.maybeBroadcastWatch(i,t)})},e.prototype.addFragmentsToDocument=function(t){var r=this.config.fragments;return r?r.transform(t):t},e.prototype.addTypenameToDocument=function(t){return this.addTypename?this.addTypenameTransform.transformDocument(t):t},e.prototype.broadcastWatch=function(t,r){var i=t.lastDiff,a=this.diff(t);r&&(t.optimistic&&"string"==typeof r.optimistic&&(a.fromOptimisticTransaction=!0),r.onWatchUpdated&&!1===r.onWatchUpdated.call(this,t,a,i))||(!i||!be(i.result,a.result))&&t.callback(t.lastDiff=a,i)},e}(Yt);!1!==globalThis.__DEV__&&(qn.prototype.getMemoryInternals=ht.q4);var fa=V.i.empty,la=V.i.from,ha=V.i.split,pa=V.i.concat,va=function(){if(typeof AbortController>"u")return{controller:!1,signal:!1};var n=new AbortController;return{controller:n,signal:n.signal}};function da(n){var e=!1;return new Promise(function(t,r){n.subscribe({next:function(i){e?!1!==globalThis.__DEV__&&D.kG.warn(43):(e=!0,t(i))},error:r})})}function ya(n){return new j.y(function(e){n.then(function(t){e.next(t),e.complete()}).catch(e.error.bind(e))})}var Ln=I(79292),Ot=new Map,sr=new Map,Vn=!0,Et=!1;function Nn(n){return n.replace(/[\s,]+/g," ").trim()}function _a(n){var e=Nn(n);if(!Ot.has(e)){var t=(0,Q.parse)(n,{experimentalFragmentVariables:Et,allowLegacyFragmentVariables:Et});if(!t||"Document"!==t.kind)throw new Error("Not a valid GraphQL document.");Ot.set(e,function ba(n){var e=new Set(n.definitions);e.forEach(function(r){r.loc&&delete r.loc,Object.keys(r).forEach(function(i){var a=r[i];a&&"object"==typeof a&&e.add(a)})});var t=n.loc;return t&&(delete t.startToken,delete t.endToken),n}(function ga(n){var e=new Set,t=[];return n.definitions.forEach(function(r){if("FragmentDefinition"===r.kind){var i=r.name.value,a=function ma(n){return Nn(n.source.body.substring(n.start,n.end))}(r.loc),o=sr.get(i);o&&!o.has(a)?Vn&&console.warn("Warning: fragment with name "+i+" already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names"):o||sr.set(i,o=new Set),o.add(a),e.has(a)||(e.add(a),t.push(r))}else t.push(r)}),(0,c.pi)((0,c.pi)({},n),{definitions:t})}(t)))}return Ot.get(e)}function $e(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];"string"==typeof n&&(n=[n]);var r=n[0];return e.forEach(function(i,a){r+=i&&"Document"===i.kind?i.loc.source.body:i,r+=n[a+1]}),_a(r)}function zn(){Ot.clear(),sr.clear()}function Wn(){Vn=!1}function Bn(){Et=!0}function Gn(){Et=!1}var lt_gql=$e,lt_resetCaches=zn,lt_disableFragmentWarnings=Wn,lt_enableExperimentalFragmentVariables=Bn,lt_disableExperimentalFragmentVariables=Gn;(function(n){n.gql=lt_gql,n.resetCaches=lt_resetCaches,n.disableFragmentWarnings=lt_disableFragmentWarnings,n.enableExperimentalFragmentVariables=lt_enableExperimentalFragmentVariables,n.disableExperimentalFragmentVariables=lt_disableExperimentalFragmentVariables})($e||($e={})),$e.default=$e,(0,Ln.U6)(!1!==globalThis.__DEV__?"log":"silent")},40484:(ke,fe,I)=>{I.d(fe,{i:()=>A});var c=I(11253),D=I(64302),V=I(97582),W=I(70591);function ie(E,F){return F?F(E):D.y.of()}function C(E){return"function"==typeof E?new A(E):E}function J(E){return E.request.length<=1}var A=function(){function E(F){F&&(this.request=F)}return E.empty=function(){return new E(function(){return D.y.of()})},E.from=function(F){return 0===F.length?E.empty():F.map(C).reduce(function(N,j){return N.concat(j)})},E.split=function(F,N,j){var L,X=C(N),ae=C(j||new E(ie));return L=J(X)&&J(ae)?new E(function(ue){return F(ue)?X.request(ue)||D.y.of():ae.request(ue)||D.y.of()}):new E(function(ue,de){return F(ue)?X.request(ue,de)||D.y.of():ae.request(ue,de)||D.y.of()}),Object.assign(L,{left:X,right:ae})},E.execute=function(F,N){return F.request(function H(E,F){var N=(0,V.pi)({},E);return Object.defineProperty(F,"setContext",{enumerable:!1,value:function(ae){N=(0,V.pi)((0,V.pi)({},N),"function"==typeof ae?ae(N):ae)}}),Object.defineProperty(F,"getContext",{enumerable:!1,value:function(){return(0,V.pi)({},N)}}),F}(N.context,function Q(E){var F={variables:E.variables||{},extensions:E.extensions||{},operationName:E.operationName,query:E.query};return F.operationName||(F.operationName="string"!=typeof F.query?(0,W.rY)(F.query)||void 0:""),F}(function $(E){for(var F=["query","operationName","variables","extensions","context"],N=0,j=Object.keys(E);N<j.length;N++){var X=j[N];if(F.indexOf(X)<0)throw(0,c._K)(44,X)}return E}(N))))||D.y.of()},E.concat=function(F,N){var j=C(F);if(J(j))return!1!==globalThis.__DEV__&&c.kG.warn(36,j),j;var ae,X=C(N);return ae=J(X)?new E(function(L){return j.request(L,function(ue){return X.request(ue)||D.y.of()})||D.y.of()}):new E(function(L,ue){return j.request(L,function(de){return X.request(de,ue)||D.y.of()})||D.y.of()}),Object.assign(ae,{left:j,right:X})},E.prototype.split=function(F,N,j){return this.concat(E.split(F,N,j||new E(ie)))},E.prototype.concat=function(F){return E.concat(this,F)},E.prototype.request=function(F,N){throw(0,c._K)(37)},E.prototype.onError=function(F,N){if(N&&N.error)return N.error(F),!1;throw F},E.prototype.setOnError=function(F){return this.onError=F,this},E}()},19162:(ke,fe,I)=>{I.d(fe,{L:()=>Q,s:()=>W});var c=I(5058),D=I(38678),V=new WeakSet;function H($){$.size<=($.max||-1)||V.has($)||(V.add($),setTimeout(function(){$.clean(),V.delete($)},100))}var W=function($,ie){var C=new c.k($,ie);return C.set=function(J,A){var E=c.k.prototype.set.call(this,J,A);return H(this),E},C},Q=function($,ie){var C=new D.e($,ie);return C.set=function(J,A){var E=D.e.prototype.set.call(this,J,A);return H(this),E},C}},72905:(ke,fe,I)=>{I.d(fe,{Kb:()=>$,q4:()=>Q,su:()=>W,zP:()=>H});var c=I(97582),D=I(64171),V={};function H(L,ue){V[L]=ue}var W=!1!==globalThis.__DEV__?function C(){var L,ue,de,Ee,Re;if(!1===globalThis.__DEV__)throw new Error("only supported in development mode");return{limits:Object.fromEntries(Object.entries({parser:1e3,canonicalStringify:1e3,print:2e3,"documentTransform.cache":2e3,"queryManager.getDocumentInfo":2e3,"PersistedQueryLink.persistedQueryHashes":2e3,"fragmentRegistry.transform":2e3,"fragmentRegistry.lookup":1e3,"fragmentRegistry.findFragmentSpreads":4e3,"cache.fragmentQueryDocuments":1e3,"removeTypenameFromVariables.getVariableDefinitions":2e3,"inMemoryCache.maybeBroadcastWatch":5e3,"inMemoryCache.executeSelectionSet":5e4,"inMemoryCache.executeSubSelectedArray":1e4}).map(function(ue){var de=ue[0];return[de,D.Q[de]||ue[1]]})),sizes:(0,c.pi)({print:null===(L=V.print)||void 0===L?void 0:L.call(V),parser:null===(ue=V.parser)||void 0===ue?void 0:ue.call(V),canonicalStringify:null===(de=V.canonicalStringify)||void 0===de?void 0:de.call(V),links:ae(this.link),queryManager:{getDocumentInfo:this.queryManager.transformCache.size,documentTransforms:j(this.queryManager.documentTransform)}},null===(Re=(Ee=this.cache).getMemoryInternals)||void 0===Re?void 0:Re.call(Ee))}}:void 0,Q=!1!==globalThis.__DEV__?function A(){var L=this.config.fragments;return(0,c.pi)((0,c.pi)({},J.apply(this)),{addTypenameDocumentTransform:j(this.addTypenameTransform),inMemoryCache:{executeSelectionSet:F(this.storeReader.executeSelectionSet),executeSubSelectedArray:F(this.storeReader.executeSubSelectedArray),maybeBroadcastWatch:F(this.maybeBroadcastWatch)},fragmentRegistry:{findFragmentSpreads:F(L?.findFragmentSpreads),lookup:F(L?.lookup),transform:F(L?.transform)}})}:void 0,$=!1!==globalThis.__DEV__?J:void 0;function J(){return{cache:{fragmentQueryDocuments:F(this.getFragmentDoc)}}}function F(L){return function E(L){return!!L&&"dirtyKey"in L}(L)?L.size:void 0}function N(L){return null!=L}function j(L){return X(L).map(function(ue){return{cache:ue}})}function X(L){return L?(0,c.ev)((0,c.ev)([F(L?.performWork)],X(L?.left),!0),X(L?.right),!0).filter(N):[]}function ae(L){var ue;return L?(0,c.ev)((0,c.ev)([null===(ue=L?.getMemoryInternals)||void 0===ue?void 0:ue.call(L)],ae(L?.left),!0),ae(L?.right),!0).filter(N):[]}},64171:(ke,fe,I)=>{I.d(fe,{Q:()=>H});var c=I(97582),D=I(11253),V=Symbol.for("apollo.cacheSize"),H=(0,c.pi)({},D.CO[V])},13395:(ke,fe,I)=>{I.d(fe,{B:()=>H});var W,c=I(19162),D=I(64171),V=I(72905),H=Object.assign(function(C){return JSON.stringify(C,Q)},{reset:function(){W=new c.L(D.Q.canonicalStringify||1e3)}});function Q(ie,C){if(C&&"object"==typeof C){var J=Object.getPrototypeOf(C);if(J===Object.prototype||null===J){var A=Object.keys(C);if(A.every($))return C;var E=JSON.stringify(A),F=W.get(E);if(!F){A.sort();var N=JSON.stringify(A);F=W.get(N)||A,W.set(E,F),W.set(N,F)}var j=Object.create(J);return F.forEach(function(X){j[X]=C[X]}),j}}return C}function $(ie,C,J){return 0===C||J[C-1]<=ie}!1!==globalThis.__DEV__&&(0,V.zP)("canonicalStringify",function(){return W.size}),H.reset()},48561:(ke,fe,I)=>{I.d(fe,{X:()=>D});var c=new Map;function D(V){var H=c.get(V)||1;return c.set(V,H+1),"".concat(V,":").concat(H,":").concat(Math.random().toString(36).slice(2))}},27062:(ke,fe,I)=>{function c(V){return null!==V&&"object"==typeof V}I.d(fe,{s:()=>c})},69753:(ke,fe,I)=>{I.d(fe,{v:()=>D});var c=I(48561);function D(V,H){void 0===H&&(H=0);var W=(0,c.X)("stringifyForDisplay");return JSON.stringify(V,function(Q,$){return void 0===$?W:$},H).split(JSON.stringify(W)).join("<undefined>")}},11253:(ke,fe,I)=>{I.d(fe,{CO:()=>H,kG:()=>$,wY:()=>V,_K:()=>ie});var c=I(79292),D=I(56497);function V(N){try{return N()}catch{}}const H=V(function(){return globalThis})||V(function(){return window})||V(function(){return self})||V(function(){return global})||V(function(){return V.constructor("return this")()});var W=I(69753);function Q(N){return function(j){for(var X=[],ae=1;ae<arguments.length;ae++)X[ae-1]=arguments[ae];if("number"==typeof j){var L=j;(j=A(L))||(j=E(L,X),X=[])}N.apply(void 0,[j].concat(X))}}var $=Object.assign(function(j,X){for(var ae=[],L=2;L<arguments.length;L++)ae[L-2]=arguments[L];j||(0,c.kG)(j,A(X,ae)||E(X,ae))},{debug:Q(c.kG.debug),log:Q(c.kG.log),warn:Q(c.kG.warn),error:Q(c.kG.error)});function ie(N){for(var j=[],X=1;X<arguments.length;X++)j[X-1]=arguments[X];return new c.ej(A(N,j)||E(N,j))}var C=Symbol.for("ApolloErrorMessageHandler_"+D.i);function J(N){if("string"==typeof N)return N;try{return(0,W.v)(N,2).slice(0,1e3)}catch{return"<non-serializable>"}}function A(N,j){if(void 0===j&&(j=[]),N)return H[C]&&H[C](N,j.map(J))}function E(N,j){if(void 0===j&&(j=[]),N)return"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({version:D.i,message:N,args:j.map(J)})))}globalThis},89661:(ke,fe,I)=>{I.d(fe,{F:()=>H,Yk:()=>V,hi:()=>W});var c=I(97582),D=I(11253);function V(Q,$){var ie=$,C=[];return Q.definitions.forEach(function(A){if("OperationDefinition"===A.kind)throw(0,D._K)(74,A.operation,A.name?" named '".concat(A.name.value,"'"):"");"FragmentDefinition"===A.kind&&C.push(A)}),typeof ie>"u"&&((0,D.kG)(1===C.length,75,C.length),ie=C[0].name.value),(0,c.pi)((0,c.pi)({},Q),{definitions:(0,c.ev)([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:ie}}]}}],Q.definitions,!0)})}function H(Q){void 0===Q&&(Q=[]);var $={};return Q.forEach(function(ie){$[ie.name.value]=ie}),$}function W(Q,$){switch(Q.kind){case"InlineFragment":return Q;case"FragmentSpread":var ie=Q.name.value;if("function"==typeof $)return $(ie);var C=$&&$[ie];return(0,D.kG)(C,76,ie),C||null;default:return null}}},70591:(ke,fe,I)=>{I.d(fe,{$H:()=>H,A$:()=>V,O4:()=>J,iW:()=>$,kU:()=>Q,p$:()=>C,pD:()=>ie,rY:()=>W});var c=I(11253),D=I(97634);function V(A){(0,c.kG)(A&&"Document"===A.kind,77);var E=A.definitions.filter(function(F){return"FragmentDefinition"!==F.kind}).map(function(F){if("OperationDefinition"!==F.kind)throw(0,c._K)(78,F.kind);return F});return(0,c.kG)(E.length<=1,79,E.length),A}function H(A){return V(A),A.definitions.filter(function(E){return"OperationDefinition"===E.kind})[0]}function W(A){return A.definitions.filter(function(E){return"OperationDefinition"===E.kind&&!!E.name}).map(function(E){return E.name.value})[0]||null}function Q(A){return A.definitions.filter(function(E){return"FragmentDefinition"===E.kind})}function $(A){var E=H(A);return(0,c.kG)(E&&"query"===E.operation,80),E}function ie(A){(0,c.kG)("Document"===A.kind,81),(0,c.kG)(A.definitions.length<=1,82);var E=A.definitions[0];return(0,c.kG)("FragmentDefinition"===E.kind,83),E}function C(A){V(A);for(var E,F=0,N=A.definitions;F<N.length;F++){var j=N[F];if("OperationDefinition"===j.kind){var X=j.operation;if("query"===X||"mutation"===X||"subscription"===X)return j}"FragmentDefinition"===j.kind&&!E&&(E=j)}if(E)return E;throw(0,c._K)(84)}function J(A){var E=Object.create(null),F=A&&A.variableDefinitions;return F&&F.length&&F.forEach(function(N){N.defaultValue&&(0,D.vb)(E,N.variable.name,N.defaultValue)}),E}},97634:(ke,fe,I)=>{I.d(fe,{Ao:()=>B,JW:()=>$,My:()=>T,NC:()=>Re,PT:()=>Ee,Yk:()=>Q,kQ:()=>W,qw:()=>Me,u2:()=>Qe,vb:()=>ae,vf:()=>L});var c=I(11253),D=I(27062),V=I(89661),H=I(13395);function W(g){return{__ref:String(g)}}function Q(g){return Boolean(g&&"object"==typeof g&&"string"==typeof g.__ref)}function $(g){return(0,D.s)(g)&&"Document"===g.kind&&Array.isArray(g.definitions)}function ae(g,P,q,ee){if(function J(g){return"IntValue"===g.kind}(q)||function A(g){return"FloatValue"===g.kind}(q))g[P.value]=Number(q.value);else if(function C(g){return"BooleanValue"===g.kind}(q)||function ie(g){return"StringValue"===g.kind}(q))g[P.value]=q.value;else if(function F(g){return"ObjectValue"===g.kind}(q)){var Y={};q.fields.map(function(ne){return ae(Y,ne.name,ne.value,ee)}),g[P.value]=Y}else if(function E(g){return"Variable"===g.kind}(q))g[P.value]=(ee||{})[q.name.value];else if(function N(g){return"ListValue"===g.kind}(q))g[P.value]=q.values.map(function(ne){var oe={};return ae(oe,P,ne,ee),oe[P.value]});else if(function j(g){return"EnumValue"===g.kind}(q))g[P.value]=q.value;else{if(!function X(g){return"NullValue"===g.kind}(q))throw(0,c._K)(85,P.value,q.kind);g[P.value]=null}}function L(g,P){var q=null;g.directives&&(q={},g.directives.forEach(function(Y){q[Y.name.value]={},Y.arguments&&Y.arguments.forEach(function(U){return ae(q[Y.name.value],U.name,U.value,P)})}));var ee=null;return g.arguments&&g.arguments.length&&(ee={},g.arguments.forEach(function(Y){return ae(ee,Y.name,Y.value,P)})),Ee(g.name.value,ee,q)}var ue=["connection","include","skip","client","rest","export","nonreactive"],de=H.B,Ee=Object.assign(function(g,P,q){if(P&&q&&q.connection&&q.connection.key){if(q.connection.filter&&q.connection.filter.length>0){var ee=q.connection.filter?q.connection.filter:[];ee.sort();var Y={};return ee.forEach(function(oe){Y[oe]=P[oe]}),"".concat(q.connection.key,"(").concat(de(Y),")")}return q.connection.key}var U=g;if(P){var ne=de(P);U+="(".concat(ne,")")}return q&&Object.keys(q).forEach(function(oe){-1===ue.indexOf(oe)&&(q[oe]&&Object.keys(q[oe]).length?U+="@".concat(oe,"(").concat(de(q[oe]),")"):U+="@".concat(oe))}),U},{setStringify:function(g){var P=de;return de=g,P}});function Re(g,P){if(g.arguments&&g.arguments.length){var q={};return g.arguments.forEach(function(ee){return ae(q,ee.name,ee.value,P)}),q}return null}function Qe(g){return g.alias?g.alias.value:g.name.value}function Me(g,P,q){for(var ee,Y=0,U=P.selections;Y<U.length;Y++)if(T(ne=U[Y])){if("__typename"===ne.name.value)return g[Qe(ne)]}else ee?ee.push(ne):ee=[ne];if("string"==typeof g.__typename)return g.__typename;if(ee)for(var oe=0,ye=ee;oe<ye.length;oe++){var ne,d=Me(g,(0,V.hi)(ne=ye[oe],q).selectionSet,q);if("string"==typeof d)return d}}function T(g){return"Field"===g.kind}function B(g){return"InlineFragment"===g.kind}},56497:(ke,fe,I)=>{I.d(fe,{i:()=>c});var c="3.11.8"},38678:(ke,fe,I)=>{function c(){}I.d(fe,{e:()=>D});class D{constructor(H=1/0,W=c){this.max=H,this.dispose=W,this.map=new Map,this.newest=null,this.oldest=null}has(H){return this.map.has(H)}get(H){const W=this.getNode(H);return W&&W.value}get size(){return this.map.size}getNode(H){const W=this.map.get(H);if(W&&W!==this.newest){const{older:Q,newer:$}=W;$&&($.older=Q),Q&&(Q.newer=$),W.older=this.newest,W.older.newer=W,W.newer=null,this.newest=W,W===this.oldest&&(this.oldest=$)}return W}set(H,W){let Q=this.getNode(H);return Q?Q.value=W:(Q={key:H,value:W,newer:null,older:this.newest},this.newest&&(this.newest.newer=Q),this.newest=Q,this.oldest=this.oldest||Q,this.map.set(H,Q),Q.value)}clean(){for(;this.oldest&&this.map.size>this.max;)this.delete(this.oldest.key)}delete(H){const W=this.map.get(H);return!!W&&(W===this.newest&&(this.newest=W.older),W===this.oldest&&(this.oldest=W.newer),W.newer&&(W.newer.older=W.older),W.older&&(W.older.newer=W.newer),this.map.delete(H),this.dispose(W.value,H),!0)}}},5058:(ke,fe,I)=>{function c(){}I.d(fe,{k:()=>$});const D=c,V=typeof WeakRef<"u"?WeakRef:function(ie){return{deref:()=>ie}},H=typeof WeakMap<"u"?WeakMap:Map,W=typeof FinalizationRegistry<"u"?FinalizationRegistry:function(){return{register:c,unregister:c}};class ${constructor(C=1/0,J=D){this.max=C,this.dispose=J,this.map=new H,this.newest=null,this.oldest=null,this.unfinalizedNodes=new Set,this.finalizationScheduled=!1,this.size=0,this.finalize=()=>{const A=this.unfinalizedNodes.values();for(let E=0;E<10024;E++){const F=A.next().value;if(!F)break;this.unfinalizedNodes.delete(F);const N=F.key;delete F.key,F.keyRef=new V(N),this.registry.register(N,F,F)}this.unfinalizedNodes.size>0?queueMicrotask(this.finalize):this.finalizationScheduled=!1},this.registry=new W(this.deleteNode.bind(this))}has(C){return this.map.has(C)}get(C){const J=this.getNode(C);return J&&J.value}getNode(C){const J=this.map.get(C);if(J&&J!==this.newest){const{older:A,newer:E}=J;E&&(E.older=A),A&&(A.newer=E),J.older=this.newest,J.older.newer=J,J.newer=null,this.newest=J,J===this.oldest&&(this.oldest=E)}return J}set(C,J){let A=this.getNode(C);return A?A.value=J:(A={key:C,value:J,newer:null,older:this.newest},this.newest&&(this.newest.newer=A),this.newest=A,this.oldest=this.oldest||A,this.scheduleFinalization(A),this.map.set(C,A),this.size++,A.value)}clean(){for(;this.oldest&&this.size>this.max;)this.deleteNode(this.oldest)}deleteNode(C){C===this.newest&&(this.newest=C.older),C===this.oldest&&(this.oldest=C.newer),C.newer&&(C.newer.older=C.older),C.older&&(C.older.newer=C.newer),this.size--;const J=C.key||C.keyRef&&C.keyRef.deref();this.dispose(C.value,J),C.keyRef?this.registry.unregister(C):this.unfinalizedNodes.delete(C),J&&this.map.delete(J)}delete(C){const J=this.map.get(C);return!!J&&(this.deleteNode(J),!0)}scheduleFinalization(C){this.unfinalizedNodes.add(C),this.finalizationScheduled||(this.finalizationScheduled=!0,queueMicrotask(this.finalize))}}},79292:(ke,fe,I)=>{I.d(fe,{U6:()=>J,ej:()=>W,kG:()=>Q});var c=I(97582),D="Invariant Violation",V=Object.setPrototypeOf,H=void 0===V?function(E,F){return E.__proto__=F,E}:V,W=function(E){function F(N){void 0===N&&(N=D);var j=E.call(this,"number"==typeof N?D+": "+N+" (see https://github.com/apollographql/invariant-packages)":N)||this;return j.framesToPop=1,j.name=D,H(j,F.prototype),j}return(0,c.ZT)(F,E),F}(Error);function Q(E,F){if(!E)throw new W(F)}var E,$=["debug","log","warn","error","silent"],ie=$.indexOf("log");function C(E){return function(){if($.indexOf(E)>=ie)return(console[E]||console.log).apply(console,arguments)}}function J(E){var F=$[ie];return ie=Math.max(0,$.indexOf(E)),F}(E=Q||(Q={})).debug=C("debug"),E.log=C("log"),E.warn=C("warn"),E.error=C("error")},64302:(ke,fe,I)=>{function V(T,B){(null==B||B>T.length)&&(B=T.length);for(var g=0,P=new Array(B);g<B;g++)P[g]=T[g];return P}function H(T,B){for(var g=0;g<B.length;g++){var P=B[g];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(T,P.key,P)}}function W(T,B,g){return B&&H(T.prototype,B),g&&H(T,g),Object.defineProperty(T,"prototype",{writable:!1}),T}I.d(fe,{y:()=>Me});var Q=function(){return"function"==typeof Symbol},$=function(T){return Q()&&Boolean(Symbol[T])},ie=function(T){return $(T)?Symbol[T]:"@@"+T};Q()&&!$("observable")&&(Symbol.observable=Symbol("observable"));var C=ie("iterator"),J=ie("observable"),A=ie("species");function E(T,B){var g=T[B];if(null!=g){if("function"!=typeof g)throw new TypeError(g+" is not a function");return g}}function F(T){var B=T.constructor;return void 0!==B&&null===(B=B[A])&&(B=void 0),void 0!==B?B:Me}function j(T){j.log?j.log(T):setTimeout(function(){throw T})}function X(T){Promise.resolve().then(function(){try{T()}catch(B){j(B)}})}function ae(T){var B=T._cleanup;if(void 0!==B&&(T._cleanup=void 0,B))try{if("function"==typeof B)B();else{var g=E(B,"unsubscribe");g&&g.call(B)}}catch(P){j(P)}}function L(T){T._observer=void 0,T._queue=void 0,T._state="closed"}function de(T,B,g){T._state="running";var P=T._observer;try{var q=E(P,B);switch(B){case"next":q&&q.call(P,g);break;case"error":if(L(T),!q)throw g;q.call(P,g);break;case"complete":L(T),q&&q.call(P)}}catch(ee){j(ee)}"closed"===T._state?ae(T):"running"===T._state&&(T._state="ready")}function Ee(T,B,g){if("closed"!==T._state){if("buffering"===T._state)return void T._queue.push({type:B,value:g});if("ready"!==T._state)return T._state="buffering",T._queue=[{type:B,value:g}],void X(function(){return function ue(T){var B=T._queue;if(B){T._queue=void 0,T._state="ready";for(var g=0;g<B.length&&(de(T,B[g].type,B[g].value),"closed"!==T._state);++g);}}(T)});de(T,B,g)}}var Re=function(){function T(g,P){this._cleanup=void 0,this._observer=g,this._queue=void 0,this._state="initializing";var q=new Qe(this);try{this._cleanup=P.call(void 0,q)}catch(ee){q.error(ee)}"initializing"===this._state&&(this._state="ready")}return T.prototype.unsubscribe=function(){"closed"!==this._state&&(L(this),ae(this))},W(T,[{key:"closed",get:function(){return"closed"===this._state}}]),T}(),Qe=function(){function T(g){this._subscription=g}var B=T.prototype;return B.next=function(P){Ee(this._subscription,"next",P)},B.error=function(P){Ee(this._subscription,"error",P)},B.complete=function(){Ee(this._subscription,"complete")},W(T,[{key:"closed",get:function(){return"closed"===this._subscription._state}}]),T}(),Me=function(){function T(g){if(!(this instanceof T))throw new TypeError("Observable cannot be called as a function");if("function"!=typeof g)throw new TypeError("Observable initializer must be a function");this._subscriber=g}var B=T.prototype;return B.subscribe=function(P){return("object"!=typeof P||null===P)&&(P={next:P,error:arguments[1],complete:arguments[2]}),new Re(P,this._subscriber)},B.forEach=function(P){var q=this;return new Promise(function(ee,Y){if("function"==typeof P)var ne=q.subscribe({next:function(oe){try{P(oe,U)}catch(ye){Y(ye),ne.unsubscribe()}},error:Y,complete:ee});else Y(new TypeError(P+" is not a function"));function U(){ne.unsubscribe(),ee()}})},B.map=function(P){var q=this;if("function"!=typeof P)throw new TypeError(P+" is not a function");return new(F(this))(function(Y){return q.subscribe({next:function(U){try{U=P(U)}catch(ne){return Y.error(ne)}Y.next(U)},error:function(U){Y.error(U)},complete:function(){Y.complete()}})})},B.filter=function(P){var q=this;if("function"!=typeof P)throw new TypeError(P+" is not a function");return new(F(this))(function(Y){return q.subscribe({next:function(U){try{if(!P(U))return}catch(ne){return Y.error(ne)}Y.next(U)},error:function(U){Y.error(U)},complete:function(){Y.complete()}})})},B.reduce=function(P){var q=this;if("function"!=typeof P)throw new TypeError(P+" is not a function");var ee=F(this),Y=arguments.length>1,U=!1,oe=arguments[1];return new ee(function(ye){return q.subscribe({next:function(d){var y=!U;if(U=!0,!y||Y)try{oe=P(oe,d)}catch(m){return ye.error(m)}else oe=d},error:function(d){ye.error(d)},complete:function(){if(!U&&!Y)return ye.error(new TypeError("Cannot reduce an empty sequence"));ye.next(oe),ye.complete()}})})},B.concat=function(){for(var P=this,q=arguments.length,ee=new Array(q),Y=0;Y<q;Y++)ee[Y]=arguments[Y];var U=F(this);return new U(function(ne){var oe,ye=0;return function d(y){oe=y.subscribe({next:function(m){ne.next(m)},error:function(m){ne.error(m)},complete:function(){ye===ee.length?(oe=void 0,ne.complete()):d(U.from(ee[ye++]))}})}(P),function(){oe&&(oe.unsubscribe(),oe=void 0)}})},B.flatMap=function(P){var q=this;if("function"!=typeof P)throw new TypeError(P+" is not a function");var ee=F(this);return new ee(function(Y){var U=[],ne=q.subscribe({next:function(ye){if(P)try{ye=P(ye)}catch(y){return Y.error(y)}var d=ee.from(ye).subscribe({next:function(y){Y.next(y)},error:function(y){Y.error(y)},complete:function(){var y=U.indexOf(d);y>=0&&U.splice(y,1),oe()}});U.push(d)},error:function(ye){Y.error(ye)},complete:function(){oe()}});function oe(){ne.closed&&0===U.length&&Y.complete()}return function(){U.forEach(function(ye){return ye.unsubscribe()}),ne.unsubscribe()}})},B[J]=function(){return this},T.from=function(P){var q="function"==typeof this?this:T;if(null==P)throw new TypeError(P+" is not an object");var ee=E(P,J);if(ee){var Y=ee.call(P);if(Object(Y)!==Y)throw new TypeError(Y+" is not an object");return function N(T){return T instanceof Me}(Y)&&Y.constructor===q?Y:new q(function(U){return Y.subscribe(U)})}if($("iterator")&&(ee=E(P,C)))return new q(function(U){X(function(){if(!U.closed){for(var oe,ne=function c(T,B){var g=typeof Symbol<"u"&&T[Symbol.iterator]||T["@@iterator"];if(g)return(g=g.call(T)).next.bind(g);if(Array.isArray(T)||(g=function D(T,B){if(T){if("string"==typeof T)return V(T,B);var g=Object.prototype.toString.call(T).slice(8,-1);if("Object"===g&&T.constructor&&(g=T.constructor.name),"Map"===g||"Set"===g)return Array.from(T);if("Arguments"===g||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(g))return V(T,B)}}(T))||B&&T&&"number"==typeof T.length){g&&(T=g);var P=0;return function(){return P>=T.length?{done:!0}:{done:!1,value:T[P++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(ee.call(P));!(oe=ne()).done;)if(U.next(oe.value),U.closed)return;U.complete()}})});if(Array.isArray(P))return new q(function(U){X(function(){if(!U.closed){for(var ne=0;ne<P.length;++ne)if(U.next(P[ne]),U.closed)return;U.complete()}})});throw new TypeError(P+" is not observable")},T.of=function(){for(var P=arguments.length,q=new Array(P),ee=0;ee<P;ee++)q[ee]=arguments[ee];return new("function"==typeof this?this:T)(function(U){X(function(){if(!U.closed){for(var ne=0;ne<q.length;++ne)if(U.next(q[ne]),U.closed)return;U.complete()}})})},W(T,null,[{key:A,get:function(){return this}}]),T}();Q()&&Object.defineProperty(Me,Symbol("extensions"),{value:{symbol:J,hostReportError:j},configurable:!0})},97582:(ke,fe,I)=>{I.d(fe,{FC:()=>Re,Jh:()=>F,KL:()=>Me,ZT:()=>D,_T:()=>H,cy:()=>T,ev:()=>de,gn:()=>W,mG:()=>E,pi:()=>V,pr:()=>ue,qq:()=>Ee});var c=function(d,y){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(m,O){m.__proto__=O}||function(m,O){for(var M in O)Object.prototype.hasOwnProperty.call(O,M)&&(m[M]=O[M])})(d,y)};function D(d,y){if("function"!=typeof y&&null!==y)throw new TypeError("Class extends value "+String(y)+" is not a constructor or null");function m(){this.constructor=d}c(d,y),d.prototype=null===y?Object.create(y):(m.prototype=y.prototype,new m)}var V=function(){return V=Object.assign||function(y){for(var m,O=1,M=arguments.length;O<M;O++)for(var R in m=arguments[O])Object.prototype.hasOwnProperty.call(m,R)&&(y[R]=m[R]);return y},V.apply(this,arguments)};function H(d,y){var m={};for(var O in d)Object.prototype.hasOwnProperty.call(d,O)&&y.indexOf(O)<0&&(m[O]=d[O]);if(null!=d&&"function"==typeof Object.getOwnPropertySymbols){var M=0;for(O=Object.getOwnPropertySymbols(d);M<O.length;M++)y.indexOf(O[M])<0&&Object.prototype.propertyIsEnumerable.call(d,O[M])&&(m[O[M]]=d[O[M]])}return m}function W(d,y,m,O){var re,M=arguments.length,R=M<3?y:null===O?O=Object.getOwnPropertyDescriptor(y,m):O;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)R=Reflect.decorate(d,y,m,O);else for(var he=d.length-1;he>=0;he--)(re=d[he])&&(R=(M<3?re(R):M>3?re(y,m,R):re(y,m))||R);return M>3&&R&&Object.defineProperty(y,m,R),R}function E(d,y,m,O){return new(m||(m=Promise))(function(R,re){function he(_e){try{te(O.next(_e))}catch(Se){re(Se)}}function le(_e){try{te(O.throw(_e))}catch(Se){re(Se)}}function te(_e){_e.done?R(_e.value):function M(R){return R instanceof m?R:new m(function(re){re(R)})}(_e.value).then(he,le)}te((O=O.apply(d,y||[])).next())})}function F(d,y){var O,M,R,m={label:0,sent:function(){if(1&R[0])throw R[1];return R[1]},trys:[],ops:[]},re=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return re.next=he(0),re.throw=he(1),re.return=he(2),"function"==typeof Symbol&&(re[Symbol.iterator]=function(){return this}),re;function he(te){return function(_e){return function le(te){if(O)throw new TypeError("Generator is already executing.");for(;re&&(re=0,te[0]&&(m=0)),m;)try{if(O=1,M&&(R=2&te[0]?M.return:te[0]?M.throw||((R=M.return)&&R.call(M),0):M.next)&&!(R=R.call(M,te[1])).done)return R;switch(M=0,R&&(te=[2&te[0],R.value]),te[0]){case 0:case 1:R=te;break;case 4:return m.label++,{value:te[1],done:!1};case 5:m.label++,M=te[1],te=[0];continue;case 7:te=m.ops.pop(),m.trys.pop();continue;default:if(!(R=(R=m.trys).length>0&&R[R.length-1])&&(6===te[0]||2===te[0])){m=0;continue}if(3===te[0]&&(!R||te[1]>R[0]&&te[1]<R[3])){m.label=te[1];break}if(6===te[0]&&m.label<R[1]){m.label=R[1],R=te;break}if(R&&m.label<R[2]){m.label=R[2],m.ops.push(te);break}R[2]&&m.ops.pop(),m.trys.pop();continue}te=y.call(d,m)}catch(_e){te=[6,_e],M=0}finally{O=R=0}if(5&te[0])throw te[1];return{value:te[0]?te[1]:void 0,done:!0}}([te,_e])}}}function ue(){for(var d=0,y=0,m=arguments.length;y<m;y++)d+=arguments[y].length;var O=Array(d),M=0;for(y=0;y<m;y++)for(var R=arguments[y],re=0,he=R.length;re<he;re++,M++)O[M]=R[re];return O}function de(d,y,m){if(m||2===arguments.length)for(var R,O=0,M=y.length;O<M;O++)(R||!(O in y))&&(R||(R=Array.prototype.slice.call(y,0,O)),R[O]=y[O]);return d.concat(R||Array.prototype.slice.call(y))}function Ee(d){return this instanceof Ee?(this.v=d,this):new Ee(d)}function Re(d,y,m){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var M,O=m.apply(d,y||[]),R=[];return M=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),he("next"),he("throw"),he("return",function re(me){return function(we){return Promise.resolve(we).then(me,Se)}}),M[Symbol.asyncIterator]=function(){return this},M;function he(me,we){O[me]&&(M[me]=function(De){return new Promise(function(Ie,xe){R.push([me,De,Ie,xe])>1||le(me,De)})},we&&(M[me]=we(M[me])))}function le(me,we){try{!function te(me){me.value instanceof Ee?Promise.resolve(me.value.v).then(_e,Se):je(R[0][2],me)}(O[me](we))}catch(De){je(R[0][3],De)}}function _e(me){le("next",me)}function Se(me){le("throw",me)}function je(me,we){me(we),R.shift(),R.length&&le(R[0][0],R[0][1])}}function Me(d){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var m,y=d[Symbol.asyncIterator];return y?y.call(d):(d=function X(d){var y="function"==typeof Symbol&&Symbol.iterator,m=y&&d[y],O=0;if(m)return m.call(d);if(d&&"number"==typeof d.length)return{next:function(){return d&&O>=d.length&&(d=void 0),{value:d&&d[O++],done:!d}}};throw new TypeError(y?"Object is not iterable.":"Symbol.iterator is not defined.")}(d),m={},O("next"),O("throw"),O("return"),m[Symbol.asyncIterator]=function(){return this},m);function O(R){m[R]=d[R]&&function(re){return new Promise(function(he,le){!function M(R,re,he,le){Promise.resolve(le).then(function(te){R({value:te,done:he})},re)}(he,le,(re=d[R](re)).done,re.value)})}}}function T(d,y){return Object.defineProperty?Object.defineProperty(d,"raw",{value:y}):d.raw=y,d}"function"==typeof SuppressedError&&SuppressedError}}]);