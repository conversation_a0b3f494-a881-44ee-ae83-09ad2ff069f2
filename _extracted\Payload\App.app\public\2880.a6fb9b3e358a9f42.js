(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2880],{92880:(A,s,n)=>{n.r(s),n.d(s,{MboTransfiyaHomePageModule:()=>H});var l=n(17007),d=n(78007),m=n(30263),f=n(65715),g=n(15861),c=n(39904),y=n(95437),h=n(70658),b=n(42789),p=n(17698),e=n(99877),v=n(48774),u=n(23436),T=n(77691);const{APPROVED:x,ASK:M,CONTACTS:P,PENDING:S,TRANSFER:E}=c.Z6.TRANSFERS.TRANSFIYA;let z=(()=>{class o{constructor(t,a){this.mboProvider=t,this.requestConfiguration=a,this.canTransfiya=h.N.navigatorEnabled,this.backAction={id:"btn_transfiya-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(c.Z6.TRANSFERS.HOME)}}}ngOnInit(){this.initializatedConfiguration()}onTransfer(){this.mboProvider.navigation.next(E.SOURCE)}onMoney(){this.mboProvider.navigation.next(x.HOME)}onRequest(){this.mboProvider.navigation.next(M.DESTINATION)}onPending(){this.mboProvider.navigation.next(S.HOME)}onContacts(){this.mboProvider.navigation.next(P)}initializatedConfiguration(){var t=this;return(0,g.Z)(function*(){(yield t.requestConfiguration.home()).when({success:({history$:a})=>{a.then(i=>{t.history=i}).catch(()=>{t.history=b.bq.empty()})}})})()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(p.tE))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-home-page"]],decls:23,vars:3,consts:[[1,"mbo-transfiya-home-page__content"],[1,"mbo-transfiya-home-page__header"],["title","Transfiya",3,"leftAction"],[1,"mbo-transfiya-home-page__body"],[1,"mbo-transfiya-home-page__title"],["id","btn_transfiya-home_transfer","icon","mobile-transfer",3,"hidden","click"],["id","btn_transfiya-home_approved","icon","money-hand",3,"click"],["id","btn_transfiya-home_request","icon","money-request",3,"click"],["id","btn_transfiya-home_pending","icon","exchange-data",3,"click"],["id","btn_transfiya-home_phones","icon","user-profile",3,"click"],[1,"mbo-transfiya-home-page__footer"],[3,"history"]],template:function(t,a){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," Opciones transfiya "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-card-category",5),e.\u0275\u0275listener("click",function(){return a.onTransfer()}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Transferir dinero"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"bocc-card-category",6),e.\u0275\u0275listener("click",function(){return a.onMoney()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Aceptar dinero"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"bocc-card-category",7),e.\u0275\u0275listener("click",function(){return a.onRequest()}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Solicitar dinero"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"bocc-card-category",8),e.\u0275\u0275listener("click",function(){return a.onPending()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Solicitudes recibidas"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(18,"bocc-card-category",9),e.\u0275\u0275listener("click",function(){return a.onContacts()}),e.\u0275\u0275elementStart(19,"span"),e.\u0275\u0275text(20,"Celulares de confianza"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(21,"div",10),e.\u0275\u0275element(22,"mbo-transfiya-history-list",11),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",a.backAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!a.canTransfiya),e.\u0275\u0275advance(16),e.\u0275\u0275property("history",a.history))},dependencies:[v.J,u.D,T.L],styles:["/*!\n * MBO TransfiyaHome Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 14/Jun/2022\n * Updated: 05/Feb/2024\n*/mbo-transfiya-home-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;row-gap:var(--sizing-x12);overflow:auto}mbo-transfiya-home-page .mbo-transfiya-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-home-page .mbo-transfiya-home-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-home-page .mbo-transfiya-home-page__logo{padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-transfiya-home-page .mbo-transfiya-home-page__logo img{width:auto;height:var(--sizing-x24)}mbo-transfiya-home-page .mbo-transfiya-home-page__footer{position:relative;width:100%;padding:var(--sizing-safe-form-x8);box-sizing:border-box;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;box-shadow:var(--z-top-lighter-4);background:var(--color-carbon-lighter-50)}mbo-transfiya-home-page .mbo-transfiya-home-page__footer mbo-transfiya-history-list{margin-top:var(--sizing-x4)}mbo-transfiya-home-page .mbo-transfiya-home-page__footer mbo-transfiya-history-list .mbo-transfiya-history-list__content{padding:0rem}\n"],encapsulation:2}),o})(),H=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[l.CommonModule,d.RouterModule.forChild([{path:"",component:z}]),m.Jx,m.D0,f.vl]}),o})()}}]);