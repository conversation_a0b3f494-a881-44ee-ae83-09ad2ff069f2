(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6935],{36935:(R,i,o)=>{o.r(i),o.d(i,{MboCelToCelSendResultPageModule:()=>M});var d=o(17007),m=o(78007),s=o(79798),p=o(15861),e=o(99877),r=o(39904),g=o(95437),u=o(77478),C=o(17758),v=o(10464),h=o(78021),b=o(16442);function f(n,a){if(1&n&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275element(1,"mbo-header-result",5),e.\u0275\u0275elementEnd()),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("rightActions",t.rightActions)}}let T=(()=>{class n{constructor(t,l,c){this.ref=t,this.mboProvider=l,this.managerCelToCel=c,this.requesting=!0,this.template=r.$d,this.rightActions=[{id:"btn_cel-to-cel-send-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_cel-to-cel-send-result-page_template"),this.initializatedTransaction()}onAction(t){this.mboProvider.navigation.next("finish"===t?r.Z6.CUSTOMER.PRODUCTS.HOME:r.Z6.TRANSFERS.CELTOCEL.SEND.SOURCE)}initializatedTransaction(){var t=this;return(0,p.Z)(function*(){(yield t.managerCelToCel.send()).when({success:l=>{t.template=(0,u.R)(l)}},()=>{t.requesting=!1,t.managerCelToCel.reset()})})()}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(g.ZL),e.\u0275\u0275directiveInject(C.Ey))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-cel-to-cel-send-result-page"]],decls:5,vars:2,consts:[[1,"mbo-cel-to-cel-send-result-page__content","mbo-page__scroller"],["class","mbo-cel-to-cel-send-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-cel-to-cel-send-result-page__body"],["id","crd_cel-to-cel-send-result-page_template",3,"template","action"],[1,"mbo-cel-to-cel-send-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(t,l){1&t&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),e.\u0275\u0275template(2,f,2,1,"div",1),e.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),e.\u0275\u0275listener("action",function(S){return l.onAction(S)}),e.\u0275\u0275elementEnd()()()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!l.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("template",l.template))},dependencies:[d.NgIf,v.K,h.c,b.u],styles:["/*!\n * MBO CelToCelSendResult Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 08/Nov/2022\n * Updated: 06/Feb/2024\n*/mbo-cel-to-cel-send-result-page{position:relative;display:block;width:100%;height:100%}mbo-cel-to-cel-send-result-page .mbo-cel-to-cel-send-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-cel-to-cel-send-result-page .mbo-cel-to-cel-send-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),n})(),M=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,m.RouterModule.forChild([{path:"",component:T}]),s.KI,s.cN,s.tu]}),n})()}}]);