(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8240],{78240:(M,v,i)=>{i.r(v),i.d(v,{MboManagerTokensPage:()=>T});var d=i(17007),h=i(78007),m=i(30263),u=i(39904),f=i(95437),g=i(15861),p=i(87956),k=i(53113),s=i(98699),e=i(99877);function b(a){return`No pudimos ${a?"suspender":"reactivar"} el dispositivo, por favor vuelve a intentarlo`}let x=(()=>{class a{constructor(t,o){this.customerProducts=t,this.tokenWalletService=o}configuration(t){var o=this;return(0,g.Z)(function*(){try{const n=yield o.customerProducts.requestProductForId(t),r=yield o.tokenWalletService.getTokens(n);return s.Either.success({product:n,tokens:r})}catch({message:n}){return Promise.resolve(s.Either.failure({message:n}))}})()}toggleStatus(t,o){var n=this;return(0,g.Z)(function*(){try{return(yield o.active?n.tokenWalletService.suspendToken(t,o):n.tokenWalletService.resumeToken(t,o))?s.Either.success(new k.Wr(o.id,o.name,o.deviceName,!o.active)):s.Either.failure({message:b(o.active)})}catch{return s.Either.failure({message:b(o.active)})}})()}remove(t,o){var n=this;return(0,g.Z)(function*(){try{return(yield n.tokenWalletService.deleteToken(t,o))?s.Either.success():s.Either.failure({message:"No pudimos eliminar el dispositivo, por favor vuelve a intentarlo"})}catch{return s.Either.failure({message:"No pudimos eliminar el dispositivo, por favor vuelve a intentarlo"})}})()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275inject(p.hM),e.\u0275\u0275inject(p.N$))},a.\u0275prov=e.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();function y(a,c){if(1&a&&e.\u0275\u0275element(0,"bocc-card-product-summary",10),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",null==t.product?null:t.product.color)("icon",null==t.product?null:t.product.logo)("title",null==t.product?null:t.product.nickname)("number",null==t.product?null:t.product.publicNumber)}}function S(a,c){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",11)(1,"bocc-switch",12),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onToggleStatus(r))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"div",13)(3,"label",14),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"span",15),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"button",16),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onRemove(r))}),e.\u0275\u0275elementEnd()()}if(2&a){const t=c.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("checked",t.active),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.name," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.deviceName," ")}}let T=(()=>{class a{constructor(t,o,n,r){this.activateRoute=t,this.modalConfirmationService=o,this.mboProvider=n,this.managerTokenService=r,this.tokens=[],this.backAction={id:"btn_payment-creditcard-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{const{productId:l}=this.activateRoute.snapshot.queryParams;l?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:l}):this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.HOME)}}}ngOnInit(){const{productId:t}=this.activateRoute.snapshot.queryParams;t?this.managerTokenService.configuration(t).then(o=>{o.when({success:({product:n,tokens:r})=>{this.tokens=r,this.product=n},failure:()=>{}})}):this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.HOME)}onToggleStatus(t){this.mboProvider.loader.open("Actualizando dispositivo, por favor espere..."),this.managerTokenService.toggleStatus(this.product,t).then(o=>{o.when({success:n=>{this.tokens=this.tokens.map(r=>r.id===n.id?n:r),this.mboProvider.toast.success("El dispositivo fue actualizado correctamente","Cambio exitoso")},failure:({message:n})=>{this.mboProvider.toast.error(n,"Cambio fallido")}})}).finally(()=>{this.mboProvider.loader.close()})}onRemove(t){this.modalConfirmationService.execute({message:"Tendr\xe1s que realizar el proceso de vinculaci\xf3n nuevamente si quieres utilizarlo.",title:"Eliminar dispositivo",accept:{label:"Eliminar",theme:"danger",click:()=>{this.confirmRemove(t)}},decline:{label:"Cancelar"}})}confirmRemove(t){this.mboProvider.loader.open("Eliminando dispositivo, por favor espere..."),this.managerTokenService.remove(this.product,t).then(o=>{o.when({success:()=>{this.tokens=this.tokens.filter(({id:n})=>n!==t.id),this.mboProvider.toast.success("El dispositivo fue eliminado correctamente")},failure:({message:n})=>{this.mboProvider.toast.error(n,"Eliminaci\xf3n fallida")}})}).finally(()=>{this.mboProvider.loader.close()})}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(h.ActivatedRoute),e.\u0275\u0275directiveInject(m.$e),e.\u0275\u0275directiveInject(f.ZL),e.\u0275\u0275directiveInject(x))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-manager-token-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:13,vars:3,consts:[[1,"mbo-manager-token-page__content"],[1,"mbo-manager-token-page__header"],[3,"leftAction"],[1,"mbo-manager-token-page__body"],[1,"subtitle2-regular"],[1,"body2-medium"],[3,"color","icon","title","number",4,"ngIf"],[1,"caption-semibold"],[1,"mbo-manager-token-page__list"],["class","mbo-manager-token-page__card",4,"ngFor","ngForOf"],[3,"color","icon","title","number"],[1,"mbo-manager-token-page__card"],[3,"checked","click"],[1,"mbo-manager-token-page__card__ballot"],[1,"smalltext-medium","truncate"],[1,"caption-medium","truncate"],["bocc-button-action","remove",3,"click"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," Dispositivos vinculados a tu tarjeta "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"p",5),e.\u0275\u0275text(7," Activa o desactiva tu tarjeta en los dispositivos. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(8,y,1,4,"bocc-card-product-summary",6),e.\u0275\u0275elementStart(9,"span",7),e.\u0275\u0275text(10,"MIS DISPOSITIVOS"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8),e.\u0275\u0275template(12,S,8,3,"div",9),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",o.backAction),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngIf",o.product),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngForOf",o.tokens))},dependencies:[d.CommonModule,d.NgForOf,d.NgIf,m.u1,m.jN,m.D1,m.Jx],styles:["/*!\n * MBO ManagerToken Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 09/May/2025\n * Updated: 09/May/2025\n*/mbo-manager-token-page{position:relative;display:block;width:100%;height:100vh;overflow:hidden}mbo-manager-token-page .mbo-manager-token-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-manager-token-page .mbo-manager-token-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-manager-token-page .mbo-manager-token-page__body>p{color:var(--color-carbon-lighter-700)}mbo-manager-token-page .mbo-manager-token-page__body>span{color:var(--color-carbon-lighter-400);padding:var(--sizing-x2) 0rem}mbo-manager-token-page .mbo-manager-token-page__list{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}mbo-manager-token-page .mbo-manager-token-page__card{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x6);border:var(--border-1-lighter-300)}mbo-manager-token-page .mbo-manager-token-page__card__ballot{display:flex;width:calc(100% - 60rem);flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-manager-token-page .mbo-manager-token-page__card__ballot>span{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),a})()}}]);