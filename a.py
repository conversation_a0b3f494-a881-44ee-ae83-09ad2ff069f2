#!/usr/bin/env python3
"""
analiza_public_js.py  –  Escaneo rápido de bundles JS

Uso:
    python analiza_public_js.py /ruta/a/_extracted/Payload/App.app/public
    # si omites la ruta, toma la carpeta actual

Salida:
    - Muestra una barra de progreso (tqdm) mientras analiza.
    - Genera 'reporte_vuln.txt' con los resultados deduplicados y ordenados.
"""

import re, sys, os, pathlib, itertools
try:
    from tqdm import tqdm  # barra de progreso bonita
except ImportError:        # fallback si no está instalado
    tqdm = lambda x, **k: x

# 1. Directorio de entrada -----------------------------------------------------
root = pathlib.Path(sys.argv[1]) if len(sys.argv) > 1 else pathlib.Path.cwd()
if not root.is_dir():
    sys.exit(f"ERROR: {root} no es un directorio válido")

# 2. Patrones de búsqueda ------------------------------------------------------
regexen = {
    "codes"  : re.compile(r'\bcode\s*:\s*"([^"]+)"'),
    "names"  : re.compile(r'\bname\s*:\s*"([^"]+)"'),
    "phones" : re.compile(r'\+57\d{10}'),
    "tokens" : re.compile(r'[A-Za-z0-9+/]{20,}')
}
solo_digitos = re.compile(r'^[0-9+/]{20,}$')

# 3. Contenedores de resultados (sets -> sin duplicados) -----------------------
hallazgos = {k: set() for k in regexen}

# 4. Enumerar todos los JS y analizarlos --------------------------------------
js_files = sorted(root.rglob("*.js"), key=os.path.getsize)  # por tamaño
for js_path in tqdm(js_files, desc="Analizando .js"):
    try:
        contenido = js_path.read_text(encoding="utf-8", errors="ignore")
    except Exception as e:
        print(f"[!] No pude leer {js_path}: {e}")
        continue

    for clave, patron in regexen.items():
        for match in patron.findall(contenido):
            if clave == "tokens" and solo_digitos.match(match):
                continue            # descartamos cadenas sólo numéricas
            hallazgos[clave].add(match)

# 5. Generar reporte -----------------------------------------------------------
reporte = pathlib.Path("reporte_vuln.txt").open("w", encoding="utf-8")
reporte.write("=== REPORTE DE DATOS SENSIBLES ===\n\n")

def escribir_seccion(titulo, clave):
    reporte.write(f"{titulo}\n")
    for item in sorted(hallazgos[clave]):
        reporte.write(f"  - {item}\n")
    reporte.write("\n")

escribir_seccion("1. Códigos QR (campo code):",            "codes")
escribir_seccion("2. Nombres / Razón social:",             "names")
escribir_seccion("3. Teléfonos (+57xxxxxxxxxx):",          "phones")
escribir_seccion("4. Tokens / cadenas base64 (>20 chars):","tokens")

reporte.close()
print("\n✅  Análisis completado. Revisa 'reporte_vuln.txt'.")

