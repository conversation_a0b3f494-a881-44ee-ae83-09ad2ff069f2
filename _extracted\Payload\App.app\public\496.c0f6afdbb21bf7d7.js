(self.webpackChunkapp=self.webpackChunkapp||[]).push([[496],{10954:(N,I,t)=>{t.d(I,{V:()=>M,Ws:()=>d,YH:()=>f,d6:()=>A,uJ:()=>C});var e=t(39904),v=t(87903),r=t(53113),i=t(66067);class d extends i.T2{constructor(g,u,_,n,s,E,y,p,R,z,l,O,S){super(g,u,_,n,s,y,p,R,z,O,S),this.colorValue=E,this.franchise=l,this.bank.isOccidente&&R&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,v.mm)(this,l),this.currenciesValue=l?.currencies||[e.y1],this.digitalValue="DIGITAL"===E}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class C{constructor(g,u,_){this.code=g,this.amount=u,this.amountCurrency=_||0}}class f{constructor(g,u,_,n,s){this.label=g,this.mode=u,this.copTotal=n?.value||0,this.usdTotal=s?.value||0,this.copUsdTotal=_?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new C("COP",this.copTotal,this.copTotal)}usdValue(){return new C("USD",this.copUsdTotal,this.usdTotal)}}class h{constructor(g,u,_){this.destination=g,this.source=u,this.currency=_}}class M{constructor(g,u,_,n,s,E){this.destination=g,this.source=u,this.isManual=_,this.trm=n,this.cop=s,this.usd=E,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new h(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new h(this.destination,this.source,this.usd):void 0}}class A extends r.LN{constructor(g,u,_){super(g,u),this.currency=_}}},96381:(N,I,t)=>{t.d(I,{T:()=>S,P:()=>Q});var e=t(15861),v=t(77279),r=t(81536),i=t(87956),d=t(98699),C=t(10954),f=t(39904),h=t(29306),M=t(7464),A=t(87903),x=t(53113),g=t(1131);function n(a,L){return new C.V(a.destination,a.source,a.mode===g.o.MANUAL,L,a.cop,a.usd)}var E=t(71776),y=t(42168),p=t(99877);let R=(()=>{class a{constructor(o,c){this.http=o,c.subscribes(f.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,y.firstValueFrom)(this.http.get(f.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,y.map)(({content:o})=>o.map(c=>function _(a){return new C.Ws(a.id,a.acctType,a.acctTypeName,"DIGITAL"===a.color?f.CG:a.loanName,a.acctId,a.isOwner&&a.color||"NONE",(0,M.RO)(a.bankId,a.bankName),a.isAval,a.dynamo||!1,a.isOwner,a.creditCardType?function u(a){return new h.dD(a?.code,a?.description,0,0)}(a.creditCardType):void 0,a.isOwner?void 0:a.owner,a.isOwner?void 0:new x.dp((0,A.nX)(a.ownerIdType),a.ownerId))}(c))),(0,y.tap)(o=>{this.creditCards=o})))}}return a.\u0275fac=function(o){return new(o||a)(p.\u0275\u0275inject(E.HttpClient),p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),z=(()=>{class a{constructor(o){this.http=o}send(o){return(0,A.EC)([o.getPaymentCop(),o.getPaymentUsd()].filter(c=>!!c).map(c=>()=>this.sendCurrency(c)))}sendCurrency(o){return(0,y.firstValueFrom)(this.http.post(f.bV.PAYMENTS.CREDIT_CARD,function s(a){return{acctIdFrom:a.source.id,acctNickNameFrom:a.source.nickname,bankIdFrom:a.source.bank.id,acctIdTo:a.destination.id,acctNameTo:a.destination.nickname,bankIdTo:a.destination.bank.id,bankNameTo:a.destination.bank.name,amt:Math.ceil(a.currency.amount),curCode:a.currency.code,paymentDesc:""}}(o)).pipe((0,y.map)(c=>{const b=(0,A.l1)(c,"SUCCESS"),{type:D,message:T}=b;return new C.d6(D,T,o.currency)}),(0,y.catchError)(c=>{const{message:b}=(0,A.rU)(c);return(0,y.of)(new C.d6("ERROR",b,o.currency))})))}}return a.\u0275fac=function(o){return new(o||a)(p.\u0275\u0275inject(E.HttpClient))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var l=t(20691);let O=(()=>{class a extends l.Store{constructor(o){super({confirmation:!1,fromCustomer:!1,mode:g.o.PAY_MIN}),this.eventBusService=o,this.eventBusService.subscribes(f.PU,()=>{this.reset()})}setDestination(o,c=!1){this.reduce(b=>({...b,destination:o,fromCustomer:c}))}getDestination(){return this.select(({destination:o})=>o)}itIsFromCustomer(){return this.select(({fromCustomer:o})=>o)}setSource(o){this.reduce(c=>({...c,source:o}))}getSource(){return this.select(({source:o})=>o)}setAmount(o){const{cop:c,mode:b,usd:D}=o;this.reduce(T=>({...T,cop:c,mode:b,usd:D,confirmation:!0}))}getAmount(){return this.select(({mode:o,cop:c,usd:b})=>({cop:c,mode:o,usd:b}))}setCurrencyCode(o){this.reduce(c=>({...c,currencyCode:o}))}itIsConfirmation(){return this.select(({confirmation:o})=>o)}}return a.\u0275fac=function(o){return new(o||a)(p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),S=(()=>{class a{constructor(o,c,b,D,T){this.financials=o,this.productService=c,this.repository=b,this.store=D,this.eventBusService=T}setDestination(o){var c=this;return(0,e.Z)(function*(){try{return o.isRequiredInformation&&(yield c.productService.requestInformation(o)),d.Either.success(c.store.setDestination(o))}catch{return d.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(o){try{return d.Either.success(this.store.setSource(o))}catch({message:c}){return d.Either.failure({message:c})}}setAmount(o){try{return d.Either.success(this.store.setAmount(o))}catch({message:c}){return d.Either.failure({message:c})}}setCurrencyCode(o){try{return d.Either.success(this.store.setCurrencyCode(o))}catch({message:c}){return d.Either.failure({message:c})}}reset(){try{const o=this.store.itIsFromCustomer(),c=this.store.getDestination();return this.store.reset(),d.Either.success({fromCustomer:o,destination:c})}catch({message:o}){return d.Either.failure({message:o})}}send(){var o=this;return(0,e.Z)(function*(){const c=n(o.store.currentState,yield o.requestTrmUsd()),b=yield o.execute(c),D=b.reduce((T,{isError:B})=>T&&!B,!0);return o.eventBusService.emit(D?v.q.TransactionSuccess:v.q.TransactionFailed),d.Either.success({creditCard:c,status:b})})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([o])=>o))}execute(o){try{return this.repository.send(o)}catch({message:c}){return Promise.resolve([new C.d6("ERROR",c)])}}}return a.\u0275fac=function(o){return new(o||a)(p.\u0275\u0275inject(r.rm),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(z),p.\u0275\u0275inject(O),p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var K=t(89148);const{MANUAL:U,PAY_ALTERNATIVE:F,PAY_MIN:P,PAY_TOTAL:m}=g.o,{CcaDatePayMinCop:j,CcaDatePayMinUsd:W,CcaPayAltMinCop:V,CcaPayAltMinUsd:Y,CcaPayMinCop:G,CcaPayMinUsd:H,CcaPayTotalCop:Z,CcaPayTotalUsd:k}=K.Av;let Q=(()=>{class a{constructor(o,c,b,D,T){this.products=o,this.productService=c,this.financials=b,this.repository=D,this.store=T}destination(){var o=this;return(0,e.Z)(function*(){try{return d.Either.success((yield o.repository.request()).reduce((c,b)=>{const{others:D,principals:T}=c;return(b.bank.isOccidente?T:D).push(b),c},{others:[],principals:[]}))}catch({message:c}){return d.Either.failure({message:c})}})()}source(o){var c=this;return(0,e.Z)(function*(){try{const b=yield c.products.requestAccountsForTransfer(),D=c.store.itIsConfirmation(),T=yield c.requestCreditCard(o);return d.Either.success({confirmation:D,destination:T,products:b})}catch({message:b}){return d.Either.failure({message:b})}})()}information(){var o=this;return(0,e.Z)(function*(){try{const c=o.store.getDestination(),b=yield o.productService.requestInformation(c),D=yield o.requestTrmUsd(),T=b?.getSection(j),B=b?.getSection(W);return d.Either.success({destination:c,min:new C.YH("VALOR M\xcdNIMO A PAGAR",P,D,b?.getSection(G),b?.getSection(H)),alternative:new C.YH("VALOR M\xcdNIMO ALTERNO",F,D,b?.getSection(V),b?.getSection(Y)),total:new C.YH("SALDO ACTUAL",m,D,b?.getSection(Z),b?.getSection(k)),dateCop:T?.valueFormat,dateUsd:B?.valueFormat})}catch({message:c}){return d.Either.failure({message:c})}})()}selectAmount(){var o=this;return(0,e.Z)(function*(){try{const c=o.store.itIsConfirmation(),b=o.store.getAmount(),D=o.store.getSource(),T=o.store.getDestination(),B=yield o.productService.requestInformation(T),w=yield o.requestTrmUsd();return d.Either.success({destination:T,amount:b,confirmation:c,trm:w,source:D,min:new C.YH("Pago m\xednimo",P,w,B?.getSection(G),B?.getSection(H)),alternative:new C.YH("Pago m\xednimo alterno",F,w,B?.getSection(V),B?.getSection(Y)),total:new C.YH("Saldo actual",m,w,B?.getSection(Z),B?.getSection(k)),manual:new C.YH("Otro valor",U)})}catch({message:c}){return d.Either.failure({message:c})}})()}amount(){try{const o=this.store.itIsConfirmation(),c=this.store.getSource(),b=this.store.getDestination(),{cop:D}=this.store.getAmount();return d.Either.success({amount:D?.amount||0,confirmation:o,destination:b,source:c})}catch({message:o}){return d.Either.failure({message:o})}}confirmation(){var o=this;return(0,e.Z)(function*(){try{const c=n(o.store.currentState,yield o.requestTrmUsd());return d.Either.success({payment:c})}catch({message:c}){return d.Either.failure({message:c})}})()}requestCreditCard(o){var c=this;return(0,e.Z)(function*(){let b=c.store.getDestination();return!b&&o&&(b=(yield c.products.requestCreditCards()).find(({id:D})=>D===o),c.store.setDestination(b,!0)),b})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([o])=>o))}}return a.\u0275fac=function(o){return new(o||a)(p.\u0275\u0275inject(i.hM),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(r.rm),p.\u0275\u0275inject(R),p.\u0275\u0275inject(O))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},55351:(N,I,t)=>{t.d(I,{t:()=>f});var e=t(30263),v=t(39904),r=t(95437),i=t(96381),d=t(99877);let f=(()=>{class h{constructor(A,x,g){this.modalConfirmation=A,this.mboProvider=x,this.managerCreditCard=g}execute(A=!0){A?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:A,destination:x})=>{A?this.mboProvider.navigation.back(v.Z6.CUSTOMER.PRODUCTS.INFO,{productId:x.id}):this.mboProvider.navigation.back(v.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(v.Z6.PAYMENTS.HOME)}})}}return h.\u0275fac=function(A){return new(A||h)(d.\u0275\u0275inject(e.$e),d.\u0275\u0275inject(r.ZL),d.\u0275\u0275inject(i.T))},h.\u0275prov=d.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h})()},1131:(N,I,t)=>{t.d(I,{o:()=>e});var e=(()=>{return(v=e||(e={}))[v.PAY_MIN=0]="PAY_MIN",v[v.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",v[v.PAY_TOTAL=2]="PAY_TOTAL",v[v.MANUAL=3]="MANUAL",e;var v})()},63111:(N,I,t)=>{t.d(I,{m:()=>i});var e=t(99877),r=t(3235);let i=(()=>{class d{constructor(){this.copAmount=0,this.copUsdAmount=0,this.usdAmount=0}}return d.\u0275fac=function(f){return new(f||d)},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-card-currency-amount"]],inputs:{copAmount:"copAmount",copUsdAmount:"copUsdAmount",usdAmount:"usdAmount",theme:"theme"},decls:18,vars:15,consts:[[1,"mbo-card-currency-amount__content"],[1,"mbo-card-currency-amount__currency"],[1,"mbo-card-currency-amount__currency__logo"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"mbo-card-currency-amount__currency__content"],[1,"mbo-card-currency-amount__currency__detail","caption-medium"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"mbo-card-currency-amount__currency__value","caption-medium"]],template:function(f,h){1&f&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),e.\u0275\u0275element(3,"img",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",4)(5,"span",5),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"boccCurrency"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(8,"div",1)(9,"div",2),e.\u0275\u0275element(10,"img",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",4)(12,"span",5),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"boccCurrency"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"span",7),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"boccCurrency"),e.\u0275\u0275elementEnd()()()()),2&f&&(e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(7,3,null==h.copAmount?null:h.copAmount.toString(),"$",!1)," "),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(14,7,null==h.copUsdAmount?null:h.copUsdAmount.toString(),"$",!1)," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(17,11,null==h.usdAmount?null:h.usdAmount.toString(),"USD",!0)," "))},dependencies:[r.T],styles:["/*!\n * MBO CardCurrencyAmount Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 28/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-card-currency-amount{position:relative;width:100%;display:block}mbo-card-currency-amount .mbo-card-currency-amount__content{position:relative;display:flex;width:100%;padding:var(--sizing-x6);box-sizing:border-box}mbo-card-currency-amount .mbo-card-currency-amount__currency{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo{max-width:var(--sizing-x8);max-height:var(--sizing-x8)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo img{width:100%;height:100%}mbo-card-currency-amount .mbo-card-currency-amount__currency__content{display:flex;width:calc(100% - 14rem);flex-direction:column;row-gap:var(--sizing-x1)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail{position:relative;width:100%;color:var(--color-carbon-lighter-700)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail:before{margin-right:var(--sizing-x1);text-align:left;width:var(--sizing-x4)}mbo-card-currency-amount .mbo-card-currency-amount__currency__value{position:relative;width:100%;color:var(--color-amathyst-700)}\n"],encapsulation:2}),d})()},2297:(N,I,t)=>{t.d(I,{p:()=>g});var e=t(30263),r=(t(10954),t(99877)),d=t(17007),f=t(2460),h=t(45542),M=t(3235),A=t(16450);function x(u,_){if(1&u&&(r.\u0275\u0275elementStart(0,"div",7)(1,"div",8),r.\u0275\u0275element(2,"img",15),r.\u0275\u0275elementStart(3,"span",10),r.\u0275\u0275text(4,"Deuda en d\xf3lares"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(5,"div",11)(6,"span",10),r.\u0275\u0275text(7),r.\u0275\u0275pipe(8,"boccCurrencyCop"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(9,"span",16),r.\u0275\u0275text(10),r.\u0275\u0275pipe(11,"boccCurrency"),r.\u0275\u0275elementEnd()()()),2&u){const n=r.\u0275\u0275nextContext();r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(8,2,null==n.currency?null:n.currency.copUsdTotal.toString(),!1)," "),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind3(11,5,null==n.currency?null:n.currency.usdTotal.toString(),"USD",!1)," ")}}let g=(()=>{class u{constructor(n){this.modalConfirmation=n,this.canUsd=!0,this.condense=!0}onHeader(){this.condense=!this.condense}onInformation(){this.modalConfirmation.execute({message:this.message,title:this.title,accept:{label:"Aceptar"}})}}return u.\u0275fac=function(n){return new(n||u)(r.\u0275\u0275directiveInject(e.$e))},u.\u0275cmp=r.\u0275\u0275defineComponent({type:u,selectors:[["mbo-creditcard-information"]],inputs:{currency:"currency",title:"title",message:"message",canUsd:"canUsd"},decls:24,vars:13,consts:[[1,"mbo-creditcard-information__content"],[1,"mbo-creditcard-information__header",3,"click"],[1,"overline-medium"],[1,"mbo-creditcard-information__header__amount"],[1,"body1-medium"],[3,"icon"],[1,"mbo-creditcard-information__body",3,"hidden"],[1,"mbo-creditcard-information__currency"],[1,"mbo-creditcard-information__currency__label"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"caption-medium"],[1,"mbo-creditcard-information__currency__amount"],["class","mbo-creditcard-information__currency",4,"ngIf"],["bocc-button","flat","prefixIcon","chat-info",3,"click"],[1,"bocc-divider"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"usd","caption-medium"]],template:function(n,s){1&n&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275listener("click",function(){return s.onHeader()}),r.\u0275\u0275elementStart(2,"label",2),r.\u0275\u0275text(3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",3)(5,"span",4),r.\u0275\u0275text(6),r.\u0275\u0275pipe(7,"boccCurrencyCop"),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(8,"bocc-icon",5),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(9,"div",6)(10,"div",7)(11,"div",8),r.\u0275\u0275element(12,"img",9),r.\u0275\u0275elementStart(13,"span",10),r.\u0275\u0275text(14,"Deuda en pesos"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(15,"div",11)(16,"span",10),r.\u0275\u0275text(17),r.\u0275\u0275pipe(18,"boccCurrencyCop"),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275template(19,x,12,9,"div",12),r.\u0275\u0275elementStart(20,"button",13),r.\u0275\u0275listener("click",function(){return s.onInformation()}),r.\u0275\u0275elementStart(21,"span"),r.\u0275\u0275text(22),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(23,"div",14),r.\u0275\u0275elementEnd()()),2&n&&(r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate(null==s.currency?null:s.currency.label),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(7,7,null==s.currency?null:s.currency.amountTotal.toString(),!1)," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",s.condense?"list-open":"list-close"),r.\u0275\u0275advance(1),r.\u0275\u0275property("hidden",s.condense),r.\u0275\u0275advance(8),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(18,10,null==s.currency?null:s.currency.copTotal.toString(),!1)," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngIf",s.canUsd),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate(s.title))},dependencies:[d.NgIf,f.Z,h.P,M.T,A.f],styles:["/*!\n * MBO CreditCardInformation Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Mar/2023\n * Updated: 08/Jul/2024\n*/mbo-creditcard-information{position:relative;width:100%;display:block}mbo-creditcard-information .mbo-creditcard-information__content{position:relative;width:100%}mbo-creditcard-information .mbo-creditcard-information__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header>label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__header__amount{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header__amount>span{width:100%;text-align:right}mbo-creditcard-information .mbo-creditcard-information__header__amount>bocc-icon{color:var(--color-blue-700)}mbo-creditcard-information .mbo-creditcard-information__body{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;display:flex;width:100%;margin-top:var(--sizing-x6);flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button{width:100%}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label img{margin-right:var(--sizing-x4);width:var(--sizing-x8);height:var(--sizing-x8)}mbo-creditcard-information .mbo-creditcard-information__currency__label span{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency__amount{padding-right:var(--sizing-x6);box-sizing:border-box;margin:auto 0rem;display:flex;flex-direction:column}mbo-creditcard-information .mbo-creditcard-information__currency__amount span{color:var(--color-carbon-lighter-700);text-align:right}mbo-creditcard-information .mbo-creditcard-information__currency__amount span.usd{color:var(--color-amathyst-700)}\n"],encapsulation:2}),u})()},33022:(N,I,t)=>{t.d(I,{H:()=>n});var e=t(99877),r=t(39904),d=(t(10954),t(17007)),f=t(13462),M=t(45542),A=t(92275),x=t(55944),g=t(19102);function u(s,E){if(1&s){const y=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"div",15)(2,"label",16),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"span",17),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div")(7,"bocc-badge"),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(9,"div",18)(10,"button",19),e.\u0275\u0275listener("click",function(){const z=e.\u0275\u0275restoreView(y).$implicit,l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onSelect(z))}),e.\u0275\u0275text(11," Ver recibo "),e.\u0275\u0275elementEnd()()()}if(2&s){const y=E.$implicit,p=E.index,R=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("PAGO ",p+1,""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(R.getPayLabel(y)),e.\u0275\u0275advance(2),e.\u0275\u0275attribute("bocc-theme",R.getPayColor(y)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",R.getPayStatus(y)," ")}}var _=(()=>{return(s=_||(_={}))[s.FAILURE=0]="FAILURE",s[s.INCOMPLETE=1]="INCOMPLETE",s[s.SUCCESS=2]="SUCCESS",_;var s})();let n=(()=>{class s{constructor(){this.status=[],this.select=new e.EventEmitter}get state(){return this.status.reduce((y,{isError:p})=>p?y:y+1,0)}get title(){switch(this.state){case _.SUCCESS:return"\xa1Pagos exitosos!";case _.INCOMPLETE:return"\xa1Pagos parcialmente exitosos!";default:return"\xa1Pagos fallidos!"}}get animation(){switch(this.state){case _.SUCCESS:case _.INCOMPLETE:return r.F6;default:return r.cj}}getPayLabel(y){return"USD"===y.currency?.code?"Pago en d\xf3lares":"Pago en pesos"}getPayColor(y){return y.isError?"danger":"success"}getPayStatus(y){return y.isError?"Fallido":"Exitoso"}onSelect(y){this.select.emit(y)}}return s.\u0275fac=function(y){return new(y||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-creditcard-payments-card"]],inputs:{payment:"payment",status:"status"},outputs:{select:"select"},decls:17,vars:5,consts:[[1,"mbo-creditcard-payments-card__content"],[1,"mbo-creditcard-payments-card__header"],[3,"result"],[1,"mbo-creditcard-payments-card__status"],[3,"options"],[1,"mbo-creditcard-payments-card__subheader"],[1,"subtitle2-medium"],[1,"body2-medium"],[1,"bocc-divider"],[1,"mbo-creditcard-payments-card__body"],["class","mbo-creditcard-payments-card__payment",4,"ngFor","ngForOf"],[1,"mbo-creditcard-payments-card__amount"],[1,"smalltext-bold"],[3,"amount"],[1,"mbo-creditcard-payments-card__payment"],[1,"mbo-creditcard-payments-card__payment__content"],[1,"caption-medium"],[1,"smalltext-medium"],[1,"mbo-creditcard-payments-card__payment__action"],["bocc-button","flat","prefixIcon","extract-receive",3,"click"]],template:function(y,p){1&y&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"mbo-bank-logo",2),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275element(4,"ng-lottie",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"label",6),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"span",7),e.\u0275\u0275text(9,"Revis\xe1 los detalles de los pagos."),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(10,"div",8),e.\u0275\u0275elementStart(11,"div",9),e.\u0275\u0275template(12,u,12,4,"div",10),e.\u0275\u0275elementStart(13,"div",11)(14,"label",12),e.\u0275\u0275text(15,"TOTAL PAGADO"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(16,"bocc-amount",13),e.\u0275\u0275elementEnd()()()),2&y&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("result",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("options",p.animation),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(p.title),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",p.status),e.\u0275\u0275advance(4),e.\u0275\u0275property("amount",null==p.payment?null:p.payment.totalAmount))},dependencies:[d.NgForOf,f.LottieComponent,M.P,A.O,x.Q,g.r],styles:["/*!\n * MBO CreditCardPaymentsCard Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 09/Feb/2024\n * Updated: 08/Jul/2024\n*/mbo-creditcard-payments-card{position:relative;display:block;padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x8);box-shadow:var(--z-bottom-lighter-8);background:var(--color-carbon-lighter-50)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__header{position:relative;display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>label{color:var(--color-carbon-darker-1000);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment{position:relative;display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>label{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>span{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__action{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount label{color:var(--color-carbon-lighter-400);text-align:right}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount bocc-amount{text-align:right}\n"],encapsulation:2}),s})()},50773:(N,I,t)=>{t.d(I,{n:()=>g}),t(57544),t(10954);var i=t(99877),C=t(17007),h=t(80349),M=t(63111),A=t(3235);function x(u,_){if(1&u&&(i.\u0275\u0275elementStart(0,"div",6),i.\u0275\u0275element(1,"mbo-card-currency-amount",7),i.\u0275\u0275elementEnd()),2&u){const n=i.\u0275\u0275nextContext();i.\u0275\u0275classProp("mbo-creditcard-radiobutton__footer--disabled",n.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("copAmount",null==n.value?null:n.value.copTotal)("copUsdAmount",null==n.value?null:n.value.copUsdTotal)("usdAmount",null==n.value?null:n.value.usdTotal)}}let g=(()=>{class u{constructor(){this.disabled=!1,this.hasFooter=!1,this.skeleton=!1}get checked(){return this.radioControl&&this.radioControl.value===this.value}onComponent(){this.disabled||this.radioControl?.setValue(this.value)}}return u.\u0275fac=function(n){return new(n||u)},u.\u0275cmp=i.\u0275\u0275defineComponent({type:u,selectors:[["mbo-creditcard-radiobutton"]],inputs:{radioControl:"radioControl",value:"value",theme:"theme",disabled:"disabled",hasFooter:"hasFooter",skeleton:"skeleton"},decls:9,vars:10,consts:[[1,"mbo-creditcard-radiobutton__content",3,"click"],[3,"checked"],[1,"mbo-creditcard-radiobutton__body"],[1,"mbo-creditcard-radiobutton__label","body2-medium"],[1,"mbo-creditcard-radiobutton__amount","subtitle1-medium"],["class","mbo-creditcard-radiobutton__footer",3,"mbo-creditcard-radiobutton__footer--disabled",4,"ngIf"],[1,"mbo-creditcard-radiobutton__footer"],[3,"copAmount","copUsdAmount","usdAmount"]],template:function(n,s){1&n&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275listener("click",function(){return s.onComponent()}),i.\u0275\u0275element(1,"bocc-radiobutton",1),i.\u0275\u0275elementStart(2,"div",2)(3,"span",3),i.\u0275\u0275text(4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"span",4),i.\u0275\u0275text(6),i.\u0275\u0275pipe(7,"boccCurrency"),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275template(8,x,2,5,"div",5)),2&n&&(i.\u0275\u0275classProp("mbo-creditcard-radiobutton__content--disabled",s.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("checked",s.checked),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",null==s.value?null:s.value.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind3(7,6,null==s.value?null:s.value.amountTotal.toString(),"$",!1)," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!s.hasFooter))},dependencies:[C.NgIf,h.V,M.m,A.T],styles:["/*!\n * MBO CreditCardRadiobutton Component\n * v2.2.0\n * Author: MB Frontend Developers\n * Created: 17/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x8);--pvt-body-padding: var(--sizing-x8);--pvt-amount-padding: 0rem var(--sizing-x2);position:relative;display:block;width:100%;box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content--disabled{opacity:.5;pointer-events:none}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer{position:relative;display:flex;width:100%;padding:var(--pvt-body-padding);box-sizing:border-box;border-top:var(--border-1-lighter-300);background:var(--overlay-lgrey-40)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer--disabled{pointer-events:none;opacity:.5}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer .mbo-card-currency-amount__content{padding:var(--pvt-amount-padding)}@media screen and (max-width: 320px){mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x6);--pvt-body-padding: var(--sizing-x6)}}\n"],encapsulation:2}),u})()},11747:(N,I,t)=>{t.d(I,{PF:()=>d,Hf:()=>_,xm:()=>u,kz:()=>s,Cq:()=>z}),t(63111);var v=t(17007),r=t(83651),i=t(99877);let d=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,r.P6]}),l})();t(2297),t(33022);var h=t(79798),M=t(30263),A=t(44487),x=t.n(A),g=t(13462);let u=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,g.LottieModule.forRoot({player:()=>x()}),M.P8,M.Oh,M.Qg,h.rw]}),l})(),_=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,M.Zl,M.P8,M.oc,r.P6]}),l})();t(50773);let s=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,M.Dj,M.V6,M.Qg,r.P6,d]}),l})();t(57544);let z=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule]}),l})()},40496:(N,I,t)=>{t.r(I),t.d(I,{MboPaymentCreditCardInformationPageModule:()=>K});var e=t(17007),v=t(78007),r=t(79798),i=t(30263),d=t(83651),C=t(11747),f=t(15861),h=t(78506),M=t(39904),A=t(29306),x=t(95437),g=t(57544),u=t(96381),_=t(55351),n=t(99877),s=t(48774),E=t(83413),y=t(68819),p=t(45542),R=t(2297);function z(U,F){if(1&U&&(n.\u0275\u0275elementStart(0,"div",14)(1,"label"),n.\u0275\u0275element(2,"img",15),n.\u0275\u0275elementStart(3,"span",16),n.\u0275\u0275text(4,"FECHA L\xcdMITE DE PAGO"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(5,"span",17),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&U){const P=n.\u0275\u0275nextContext();n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate(P.dateCop)}}function l(U,F){if(1&U&&(n.\u0275\u0275elementStart(0,"div",14)(1,"label"),n.\u0275\u0275element(2,"img",18),n.\u0275\u0275elementStart(3,"span",16),n.\u0275\u0275text(4,"FECHA L\xcdMITE DE PAGO"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(5,"span",17),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&U){const P=n.\u0275\u0275nextContext();n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate(P.dateUsd)}}const O=M.Z6.PAYMENTS.CREDIT_CARD;let S=(()=>{class U{constructor(P,m,j,W){this.mboProvider=P,this.requestConfiguration=m,this.managerInformation=j,this.paymentCancel=W,this.requestingInfo=!0,this.requesting=!0,this.sections=[],this.canUsd=!1,this.backAction={id:"btn_payment-creditcard-information_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(O.SOURCE)}},this.cancelAction={id:"btn_payment-creditcard-information_cancel",label:"Cancelar",click:()=>{this.paymentCancel.execute()}},this.minMessage="Es la menor cantidad de dinero que debes abonar a tu cuenta en el mes, recuerda que este producto diferencia tus compras realizadas en pesos y dolares, las cuales deben ser saldadas por completo.",this.altMessage="Al pagar este valor el plazo elegido inicialmente para el pago de tu deuda se modificar\xe1, estableciendo uno nuevo de 48 meses. La tasa inicial de tus compras se mantendr\xe1.",this.totalMessage="Es el monto que debes abonar para saldar tu deuda.",this.currencyControl=new g.FormControl(M.y1)}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(O.SOURCE)}initializatedConfiguration(){var P=this;return(0,f.Z)(function*(){(yield P.requestConfiguration.information()).when({success:m=>{P.destination=m.destination,P.min=m.min,P.alternative=m.alternative,P.total=m.total,P.dateCop=m.dateCop,P.dateUsd=m.dateUsd,P.canUsd=m.destination.hasCurrency(M.qB),P.requestInfo(m.destination)}},()=>{P.requesting=!1})})()}requestInfo(P){var m=this;return(0,f.Z)(function*(){(yield m.managerInformation.request({product:P,ignoreds:["PAYMENT"]})).when({success:({movements$:j,sections:W})=>{j.then(V=>{m.movements=V}).catch(()=>{m.movements=A.kq.empty()}),m.sections=W}},()=>{m.requestingInfo=!1})})()}}return U.\u0275fac=function(P){return new(P||U)(n.\u0275\u0275directiveInject(x.ZL),n.\u0275\u0275directiveInject(u.P),n.\u0275\u0275directiveInject(h.vu),n.\u0275\u0275directiveInject(_.t))},U.\u0275cmp=n.\u0275\u0275defineComponent({type:U,selectors:[["mbo-payment-creditcard-information-page"]],decls:17,vars:29,consts:[[1,"mbo-payment-creditcard-information-page__content"],[1,"mbo-payment-creditcard-information-page__header"],["title","Producto","progress","50%",3,"leftAction","rightAction"],[1,"mbo-payment-creditcard-information-page__body"],[3,"color","icon","title","number","detail","statusColor","statusLabel","skeleton"],[1,"mbo-payment-creditcard-information-page__currencies",3,"hidden"],["title","\xbfQu\xe9 es el valor m\xednimo a pagar?",3,"currency","canUsd","message"],["title","\xbfQu\xe9 es el valor m\xednimo alterno?",3,"currency","canUsd","message"],["title","\xbfQue es el saldo actual?",3,"currency","canUsd","message"],["class","mbo-payment-creditcard-information-page__date",4,"ngIf"],[1,"mbo-payment-creditcard-information-page__footer"],[3,"requesting","condense","product","sections","movements","header","currencyControl"],[1,"mbo-payment-creditcard-information-page__footer__actions"],["id","btn_payment-creditcard-information_submit","bocc-button","raised",3,"click"],[1,"mbo-payment-creditcard-information-page__date"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"overline-medium"],[1,"smalltext-medium"],["src","assets/shared/logos/currencies/usd-enabled.svg"]],template:function(P,m){1&P&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3),n.\u0275\u0275element(4,"bocc-card-product-summary",4),n.\u0275\u0275elementStart(5,"div",5),n.\u0275\u0275element(6,"mbo-creditcard-information",6)(7,"mbo-creditcard-information",7)(8,"mbo-creditcard-information",8),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(9,z,7,1,"div",9),n.\u0275\u0275template(10,l,7,1,"div",9),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(11,"div",10),n.\u0275\u0275element(12,"mbo-product-info-body",11),n.\u0275\u0275elementStart(13,"div",12)(14,"button",13),n.\u0275\u0275listener("click",function(){return m.onSubmit()}),n.\u0275\u0275elementStart(15,"span"),n.\u0275\u0275text(16,"Continuar"),n.\u0275\u0275elementEnd()()()()),2&P&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",m.backAction)("rightAction",m.cancelAction),n.\u0275\u0275advance(2),n.\u0275\u0275property("color",null==m.destination?null:m.destination.color)("icon",null==m.destination?null:m.destination.logo)("title",null==m.destination?null:m.destination.nickname)("number",null==m.destination?null:m.destination.publicNumber)("detail",null==m.destination?null:m.destination.bank.name)("statusColor",null==m.destination||null==m.destination.status?null:m.destination.status.color)("statusLabel",null==m.destination||null==m.destination.status?null:m.destination.status.label)("skeleton",m.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("hidden",m.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("currency",m.min)("canUsd",m.canUsd)("message",m.minMessage),n.\u0275\u0275advance(1),n.\u0275\u0275property("currency",m.alternative)("canUsd",m.canUsd)("message",m.altMessage),n.\u0275\u0275advance(1),n.\u0275\u0275property("currency",m.total)("canUsd",m.canUsd)("message",m.totalMessage),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",m.dateCop),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",m.dateUsd&&m.canUsd),n.\u0275\u0275advance(2),n.\u0275\u0275property("requesting",m.requestingInfo)("condense",!1)("product",m.destination)("sections",m.sections)("movements",m.movements)("header",!1)("currencyControl",m.currencyControl))},dependencies:[e.NgIf,s.J,E.D,y.w,p.P,R.p],styles:["/*!\n * MBO PaymentCreditCardInformation Page\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Mar/2023\n * Updated: 16/Jun/2024\n*/mbo-payment-creditcard-information-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x12);justify-content:space-between}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__body bocc-card-product-avatar{background:var(--color-carbon-lighter-300);overflow:hidden;border-radius:var(--sizing-x2)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__body bocc-card-product-avatar .bocc-card-product-avatar__title{color:var(--color-carbon-lighter-700)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__currencies{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__date{position:relative;width:100%;display:flex;justify-content:space-between;padding-right:var(--sizing-x12);box-sizing:border-box}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__date label{display:flex;color:var(--color-carbon-lighter-700);margin:auto 0rem}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__date label img{width:var(--sizing-x8);height:var(--sizing-x8);margin-right:var(--sizing-x4)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__date span{margin:auto 0rem}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__footer{position:relative;width:100%;padding-top:var(--sizing-x8);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__footer__actions{position:sticky;bottom:0rem;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__footer__actions button{width:100%}mbo-payment-creditcard-information-page .mbo-payment-creditcard-information-page__footer mbo-product-info-body{background:transparent;box-shadow:none;margin-bottom:0rem}\n"],encapsulation:2}),U})(),K=(()=>{class U{}return U.\u0275fac=function(P){return new(P||U)},U.\u0275mod=n.\u0275\u0275defineNgModule({type:U}),U.\u0275inj=n.\u0275\u0275defineInjector({imports:[e.CommonModule,v.RouterModule.forChild([{path:"",component:S}]),i.dH,i.Jx,i.D1,r.wu,i.P8,d.P6,C.Hf]}),U})()},63674:(N,I,t)=>{t.d(I,{Eg:()=>h,Lo:()=>i,Wl:()=>d,ZC:()=>C,_f:()=>v,br:()=>f,tl:()=>r});var e=t(29306);const v={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},r=new e.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),i={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},C={color:"danger",key:"expired",label:"Vencida"},f={color:"info",key:"recurring",label:"Pago recurrente"},h={color:"info",key:"programmed",label:"Programado"}},66067:(N,I,t)=>{t.d(I,{S6:()=>M,T2:()=>f,UQ:()=>A,mZ:()=>h});var e=t(39904),v=t(6472),i=t(63674),d=t(31707);class f{constructor(g,u,_,n,s,E,y,p,R,z,l){this.id=g,this.type=u,this.name=_,this.nickname=n,this.number=s,this.bank=E,this.isAval=y,this.isProtected=p,this.isOwner=R,this.ownerName=z,this.ownerDocument=l,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[e.y1],this.initialsName=(0,v.initials)(n),this.shortNumber=s.substring(s.length-4),this.descriptionNumber=`${_} ${s}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:E.logo,light:E.logo,standard:E.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(g){this.informationValue||(this.informationValue=g)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(g){return this.currenciesValue.includes(g)}}class h{constructor(g,u){this.id=g,this.type=u}}class M{constructor(g,u,_,n,s,E,y,p,R,z,l,O){this.uuid=g,this.number=u,this.nie=_,this.nickname=n,this.companyId=s,this.companyName=E,this.amount=y,this.registerDate=p,this.expirationDate=R,this.paid=z,this.statusCode=l,this.references=O,this.recurring=O.length>0,this.status=function C(x){switch(x){case d.U.EXPIRED:return i.ZC;case d.U.PENDING:return i.Wl;case d.U.PROGRAMMED:return i.Eg;case d.U.RECURRING:return i.br;default:return i.Lo}}(l)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class A{constructor(g,u,_,n,s,E,y,p){this.uuid=g,this.number=u,this.nickname=_,this.companyId=n,this.companyName=s,this.city=E,this.amount=y,this.isBiller=p}}},31707:(N,I,t)=>{t.d(I,{U:()=>e,f:()=>v});var e=(()=>{return(r=e||(e={})).RECURRING="1",r.EXPIRED="2",r.PENDING="3",r.PROGRAMMED="4",e;var r})(),v=(()=>{return(r=v||(v={})).BILLER="Servicio",r.NON_BILLER="Servicio",r.PSE="Servicio",r.TAX="Impuesto",r.LOAN="Obligaci\xf3n financiera",r.CREDIT_CARD="Obligaci\xf3n financiera",v;var r})()}}]);