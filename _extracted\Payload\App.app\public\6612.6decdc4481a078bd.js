(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6612],{76612:(C,n,o)=>{o.r(n),o.d(n,{MboCustomerTuplusModule:()=>d});var M=o(17007),t=o(78007),l=o(99877);const a=[{path:"",loadChildren:()=>o.e(571).then(o.bind(o,40571)).then(u=>u.MboCustomerTuplusHomePageModule)}];let d=(()=>{class u{}return u.\u0275fac=function(E){return new(E||u)},u.\u0275mod=l.\u0275\u0275defineNgModule({type:u}),u.\u0275inj=l.\u0275\u0275defineInjector({imports:[M.CommonModule,t.RouterModule.forChild(a)]}),u})()}}]);