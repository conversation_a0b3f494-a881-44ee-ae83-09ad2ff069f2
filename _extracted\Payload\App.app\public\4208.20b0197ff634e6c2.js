(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4208],{44477:(R,y,t)=>{t.d(y,{p:()=>p,c:()=>l});var f=t(15861),u=t(87956),m=t(53113),r=t(98699);class d{constructor(s,e){this.invoice=s,this.source=e}}function b(n){return new d(n.invoice,n.source)}var g=t(71776),P=t(39904),I=t(87903),o=t(42168),v=t(84757),c=t(99877);let E=(()=>{class n{constructor(e){this.http=e}send(e){return(0,o.firstValueFrom)(this.http.post(P.bV.PAYMENTS.INVOICE,function D(n){return[{acctIdFrom:n.source.id,acctNickname:n.source.nickname,acctTypeFrom:n.source.type,amt:n.invoice.amount.toString(),nie:n.invoice.nie,invoiceNum:n.invoice.number,pmtCodServ:n.invoice.companyId,toEntity:n.invoice.companyName,toNickname:n.invoice.nickname,expDt:n.invoice.expirationDate.toISOString()}]}(e)).pipe((0,v.map)(([a])=>(0,I.l1)(a,"SUCCESS")))).catch(a=>(0,I.rU)(a))}}return n.\u0275fac=function(e){return new(e||n)(c.\u0275\u0275inject(g.HttpClient))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var C=t(20691);let i=(()=>{class n extends C.Store{constructor(e){super({confirmation:!1}),e.subscribes(P.PU,()=>{this.reset()})}setInvoice(e){this.reduce(a=>({...a,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(a=>({...a,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return n.\u0275fac=function(e){return new(e||n)(c.\u0275\u0275inject(u.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),p=(()=>{class n{constructor(e,a,M){this.repository=e,this.store=a,this.eventBusService=M}setInvoice(e){try{return r.Either.success(this.store.setInvoice(e))}catch({message:a}){return r.Either.failure({message:a})}}setSource(e){try{return r.Either.success(this.store.setSource(e))}catch({message:a}){return r.Either.failure({message:a})}}reset(){try{return r.Either.success(this.store.reset())}catch({message:e}){return r.Either.failure({message:e})}}send(){var e=this;return(0,f.Z)(function*(){const a=b(e.store.currentState),M=yield e.execute(a);return e.eventBusService.emit(M.channel),r.Either.success({invoice:a,status:M})})()}execute(e){try{return this.repository.send(e)}catch({message:a}){return Promise.resolve(m.LN.error(a))}}}return n.\u0275fac=function(e){return new(e||n)(c.\u0275\u0275inject(E),c.\u0275\u0275inject(i),c.\u0275\u0275inject(u.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var h=t(19799);let l=(()=>{class n{constructor(e,a,M){this.products=e,this.invoices=a,this.store=M}source(e){var a=this;return(0,f.Z)(function*(){try{const M=yield a.products.requestAccountsForTransfer();let O=a.store.getInvoice();return!O&&e&&(O=(yield a.invoices.request()).find(({uuid:N})=>e===N),a.store.setInvoice(O)),r.Either.success({invoice:O,products:M})}catch({message:M}){return r.Either.failure({message:M})}})()}confirmation(){try{const e=b(this.store.currentState);return r.Either.success({payment:e})}catch({message:e}){return r.Either.failure({message:e})}}}return n.\u0275fac=function(e){return new(e||n)(c.\u0275\u0275inject(u.hM),c.\u0275\u0275inject(h.W),c.\u0275\u0275inject(i))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},40349:(R,y,t)=>{t.d(y,{f:()=>b});var f=t(39904),u=t(95437),m=t(30263),r=t(44477),d=t(99877);let b=(()=>{class g{constructor(I,o,v){this.modalConfirmation=I,this.mboProvider=o,this.managerInvoice=v}execute(I=!0){I?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(f.Z6.PAYMENTS.HOME)}}return g.\u0275fac=function(I){return new(I||g)(d.\u0275\u0275inject(m.$e),d.\u0275\u0275inject(u.ZL),d.\u0275\u0275inject(r.p))},g.\u0275prov=d.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},14208:(R,y,t)=>{t.r(y),t.d(y,{MboPaymentInvoiceConfirmationPageModule:()=>h});var f=t(17007),u=t(78007),m=t(79798),r=t(30263),d=t(83651),D=t(15861),b=t(39904),g=t(95437),P=t(44477),I=t(40349),o=t(99877),v=t(10464),c=t(48774),E=t(17941),C=t(45542);const i=b.Z6.PAYMENTS.SERVICES;let p=(()=>{class l{constructor(s,e,a){this.mboProvider=s,this.requestConfiguration=e,this.cancelProvider=a,this.backAction={id:"btn_payment-invoice-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(i.INVOICE.SOURCE)}},this.cancelAction={id:"btn_payment-invoice-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_payment-invoice-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(i.HOME)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(i.INVOICE.RESULT)}initializatedConfiguration(){var s=this;return(0,D.Z)(function*(){(yield s.requestConfiguration.confirmation()).when({success:({payment:e})=>{s.payment=e}})})()}}return l.\u0275fac=function(s){return new(s||l)(o.\u0275\u0275directiveInject(g.ZL),o.\u0275\u0275directiveInject(P.c),o.\u0275\u0275directiveInject(I.f))},l.\u0275cmp=o.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-invoice-confirmation-page"]],decls:16,vars:9,consts:[[1,"mbo-payment-invoice-confirmation-page__content"],[1,"mbo-payment-invoice-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount"],["header","DESDE",3,"title","subtitle"],[1,"mbo-payment-invoice-confirmation-page__footer"],["id","btn_payment-invoice-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"click"]],template:function(s,e){1&s&&(o.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),o.\u0275\u0275element(3,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),o.\u0275\u0275text(7," \xbfDeseas pagar la factura? "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(8,"div",6),o.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(12,"div",10)(13,"button",11),o.\u0275\u0275listener("click",function(){return e.onSubmit()}),o.\u0275\u0275elementStart(14,"span"),o.\u0275\u0275text(15,"Realizar pago"),o.\u0275\u0275elementEnd()()()()),2&s&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("leftAction",e.backAction)("rightAction",e.cancelAction),o.\u0275\u0275advance(6),o.\u0275\u0275property("title",null==e.payment||null==e.payment.invoice?null:e.payment.invoice.nickname)("subtitle",null==e.payment||null==e.payment.invoice?null:e.payment.invoice.number)("detail",null==e.payment||null==e.payment.invoice?null:e.payment.invoice.companyName)("actions",e.destinationActions),o.\u0275\u0275advance(1),o.\u0275\u0275property("amount",null==e.payment||null==e.payment.invoice?null:e.payment.invoice.amount),o.\u0275\u0275advance(1),o.\u0275\u0275property("title",null==e.payment||null==e.payment.source?null:e.payment.source.nickname)("subtitle",null==e.payment||null==e.payment.source?null:e.payment.source.number))},dependencies:[v.K,c.J,E.D,C.P],styles:["/*!\n * BOCC PaymentInvoiceConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 31/Jul/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-invoice-confirmation-page .mbo-payment-invoice-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-invoice-confirmation-page .mbo-payment-invoice-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-invoice-confirmation-page .mbo-payment-invoice-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-payment-invoice-confirmation-page .mbo-payment-invoice-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),l})(),h=(()=>{class l{}return l.\u0275fac=function(s){return new(s||l)},l.\u0275mod=o.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=o.\u0275\u0275defineInjector({imports:[f.CommonModule,u.RouterModule.forChild([{path:"",component:p}]),m.KI,r.Jx,r.DM,r.B4,r.Dj,d.P6,r.P8]}),l})()},63674:(R,y,t)=>{t.d(y,{Eg:()=>g,Lo:()=>r,Wl:()=>d,ZC:()=>D,_f:()=>u,br:()=>b,tl:()=>m});var f=t(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},m=new f.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),r={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},D={color:"danger",key:"expired",label:"Vencida"},b={color:"info",key:"recurring",label:"Pago recurrente"},g={color:"info",key:"programmed",label:"Programado"}},66067:(R,y,t)=>{t.d(y,{S6:()=>P,T2:()=>b,UQ:()=>I,mZ:()=>g});var f=t(39904),u=t(6472),r=t(63674),d=t(31707);class b{constructor(v,c,E,C,i,p,h,l,n,s,e){this.id=v,this.type=c,this.name=E,this.nickname=C,this.number=i,this.bank=p,this.isAval=h,this.isProtected=l,this.isOwner=n,this.ownerName=s,this.ownerDocument=e,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[f.y1],this.initialsName=(0,u.initials)(C),this.shortNumber=i.substring(i.length-4),this.descriptionNumber=`${E} ${i}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:p.logo,light:p.logo,standard:p.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(v){this.informationValue||(this.informationValue=v)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(v){return this.currenciesValue.includes(v)}}class g{constructor(v,c){this.id=v,this.type=c}}class P{constructor(v,c,E,C,i,p,h,l,n,s,e,a){this.uuid=v,this.number=c,this.nie=E,this.nickname=C,this.companyId=i,this.companyName=p,this.amount=h,this.registerDate=l,this.expirationDate=n,this.paid=s,this.statusCode=e,this.references=a,this.recurring=a.length>0,this.status=function D(o){switch(o){case d.U.EXPIRED:return r.ZC;case d.U.PENDING:return r.Wl;case d.U.PROGRAMMED:return r.Eg;case d.U.RECURRING:return r.br;default:return r.Lo}}(e)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class I{constructor(v,c,E,C,i,p,h,l){this.uuid=v,this.number=c,this.nickname=E,this.companyId=C,this.companyName=i,this.city=p,this.amount=h,this.isBiller=l}}},19799:(R,y,t)=>{t.d(y,{e:()=>E,W:()=>C});var f=t(71776),u=t(39904),m=t(87956),r=t(98699),d=t(42168),D=t(84757),b=t(53113),g=t(33876),P=t(66067);var c=t(99877);let E=(()=>{class i{constructor(h,l){this.http=h,l.subscribes(u.PU,()=>{this.destroy()}),this.billers$=(0,r.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,D.map)(({content:n})=>n.map(s=>function v(i){return new P.UQ((0,g.v4)(),i.nie,i.nickname,i.orgIdNum,i.orgName,i.city,+i.amt,(0,r.parseBoolean)(i.biller))}(s))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return i.\u0275fac=function(h){return new(h||i)(c.\u0275\u0275inject(f.HttpClient),c.\u0275\u0275inject(m.Yd))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),C=(()=>{class i{constructor(h,l){this.http=h,l.subscribes(u.PU,()=>{this.destroy()}),this.invoices$=(0,r.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,D.map)(({content:n})=>n.map(s=>function o(i){const p=i.refInfo.map(h=>function I(i){return new P.mZ(i.refId,i.refType)}(h));return new P.S6((0,g.v4)(),i.invoiceNum,i.nie,i.nickName,i.orgIdNum,i.orgName,+i.totalCurAmt,new b.ou(i.effDt),new b.ou(i.expDt),(0,r.parseBoolean)(i.payDone),i.state,p)}(s))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return i.\u0275fac=function(h){return new(h||i)(c.\u0275\u0275inject(f.HttpClient),c.\u0275\u0275inject(m.Yd))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},31707:(R,y,t)=>{t.d(y,{U:()=>f,f:()=>u});var f=(()=>{return(m=f||(f={})).RECURRING="1",m.EXPIRED="2",m.PENDING="3",m.PROGRAMMED="4",f;var m})(),u=(()=>{return(m=u||(u={})).BILLER="Servicio",m.NON_BILLER="Servicio",m.PSE="Servicio",m.TAX="Impuesto",m.LOAN="Obligaci\xf3n financiera",m.CREDIT_CARD="Obligaci\xf3n financiera",u;var m})()}}]);