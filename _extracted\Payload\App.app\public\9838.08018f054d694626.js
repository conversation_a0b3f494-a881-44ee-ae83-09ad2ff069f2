(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9838],{10954:(_,M,n)=>{n.d(M,{V:()=>U,Ws:()=>o,YH:()=>T,d6:()=>s,uJ:()=>g});var m=n(39904),u=n(87903),C=n(53113),p=n(66067);class o extends p.T2{constructor(d,f,h,b,I,l,y,i,c,N,O,D,V){super(d,f,h,b,I,y,i,c,N,D,V),this.colorValue=l,this.franchise=O,this.bank.isOccidente&&c&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,u.mm)(this,O),this.currenciesValue=O?.currencies||[m.y1],this.digitalValue="DIGITAL"===l}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class g{constructor(d,f,h){this.code=d,this.amount=f,this.amountCurrency=h||0}}class T{constructor(d,f,h,b,I){this.label=d,this.mode=f,this.copTotal=b?.value||0,this.usdTotal=I?.value||0,this.copUsdTotal=h?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new g("COP",this.copTotal,this.copTotal)}usdValue(){return new g("USD",this.copUsdTotal,this.usdTotal)}}class A{constructor(d,f,h){this.destination=d,this.source=f,this.currency=h}}class U{constructor(d,f,h,b,I,l){this.destination=d,this.source=f,this.isManual=h,this.trm=b,this.cop=I,this.usd=l,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new A(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new A(this.destination,this.source,this.usd):void 0}}class s extends C.LN{constructor(d,f,h){super(d,f),this.currency=h}}},96381:(_,M,n)=>{n.d(M,{T:()=>V,P:()=>x});var m=n(15861),u=n(77279),C=n(81536),p=n(87956),o=n(98699),g=n(10954),T=n(39904),A=n(29306),U=n(7464),s=n(87903),P=n(53113),d=n(1131);function b(e,S){return new g.V(e.destination,e.source,e.mode===d.o.MANUAL,S,e.cop,e.usd)}var l=n(71776),y=n(42168),i=n(99877);let c=(()=>{class e{constructor(t,r){this.http=t,r.subscribes(T.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,y.firstValueFrom)(this.http.get(T.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,y.map)(({content:t})=>t.map(r=>function h(e){return new g.Ws(e.id,e.acctType,e.acctTypeName,"DIGITAL"===e.color?T.CG:e.loanName,e.acctId,e.isOwner&&e.color||"NONE",(0,U.RO)(e.bankId,e.bankName),e.isAval,e.dynamo||!1,e.isOwner,e.creditCardType?function f(e){return new A.dD(e?.code,e?.description,0,0)}(e.creditCardType):void 0,e.isOwner?void 0:e.owner,e.isOwner?void 0:new P.dp((0,s.nX)(e.ownerIdType),e.ownerId))}(r))),(0,y.tap)(t=>{this.creditCards=t})))}}return e.\u0275fac=function(t){return new(t||e)(i.\u0275\u0275inject(l.HttpClient),i.\u0275\u0275inject(p.Yd))},e.\u0275prov=i.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),N=(()=>{class e{constructor(t){this.http=t}send(t){return(0,s.EC)([t.getPaymentCop(),t.getPaymentUsd()].filter(r=>!!r).map(r=>()=>this.sendCurrency(r)))}sendCurrency(t){return(0,y.firstValueFrom)(this.http.post(T.bV.PAYMENTS.CREDIT_CARD,function I(e){return{acctIdFrom:e.source.id,acctNickNameFrom:e.source.nickname,bankIdFrom:e.source.bank.id,acctIdTo:e.destination.id,acctNameTo:e.destination.nickname,bankIdTo:e.destination.bank.id,bankNameTo:e.destination.bank.name,amt:Math.ceil(e.currency.amount),curCode:e.currency.code,paymentDesc:""}}(t)).pipe((0,y.map)(r=>{const a=(0,s.l1)(r,"SUCCESS"),{type:v,message:E}=a;return new g.d6(v,E,t.currency)}),(0,y.catchError)(r=>{const{message:a}=(0,s.rU)(r);return(0,y.of)(new g.d6("ERROR",a,t.currency))})))}}return e.\u0275fac=function(t){return new(t||e)(i.\u0275\u0275inject(l.HttpClient))},e.\u0275prov=i.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var O=n(20691);let D=(()=>{class e extends O.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,mode:d.o.PAY_MIN}),this.eventBusService=t,this.eventBusService.subscribes(T.PU,()=>{this.reset()})}setDestination(t,r=!1){this.reduce(a=>({...a,destination:t,fromCustomer:r}))}getDestination(){return this.select(({destination:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(r=>({...r,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount(t){const{cop:r,mode:a,usd:v}=t;this.reduce(E=>({...E,cop:r,mode:a,usd:v,confirmation:!0}))}getAmount(){return this.select(({mode:t,cop:r,usd:a})=>({cop:r,mode:t,usd:a}))}setCurrencyCode(t){this.reduce(r=>({...r,currencyCode:t}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return e.\u0275fac=function(t){return new(t||e)(i.\u0275\u0275inject(p.Yd))},e.\u0275prov=i.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),V=(()=>{class e{constructor(t,r,a,v,E){this.financials=t,this.productService=r,this.repository=a,this.store=v,this.eventBusService=E}setDestination(t){var r=this;return(0,m.Z)(function*(){try{return t.isRequiredInformation&&(yield r.productService.requestInformation(t)),o.Either.success(r.store.setDestination(t))}catch{return o.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return o.Either.success(this.store.setSource(t))}catch({message:r}){return o.Either.failure({message:r})}}setAmount(t){try{return o.Either.success(this.store.setAmount(t))}catch({message:r}){return o.Either.failure({message:r})}}setCurrencyCode(t){try{return o.Either.success(this.store.setCurrencyCode(t))}catch({message:r}){return o.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),r=this.store.getDestination();return this.store.reset(),o.Either.success({fromCustomer:t,destination:r})}catch({message:t}){return o.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const r=b(t.store.currentState,yield t.requestTrmUsd()),a=yield t.execute(r),v=a.reduce((E,{isError:R})=>E&&!R,!0);return t.eventBusService.emit(v?u.q.TransactionSuccess:u.q.TransactionFailed),o.Either.success({creditCard:r,status:a})})()}requestTrmUsd(){return(0,o.catchPromise)(this.financials.request().then(([t])=>t))}execute(t){try{return this.repository.send(t)}catch({message:r}){return Promise.resolve([new g.d6("ERROR",r)])}}}return e.\u0275fac=function(t){return new(t||e)(i.\u0275\u0275inject(C.rm),i.\u0275\u0275inject(p.M5),i.\u0275\u0275inject(N),i.\u0275\u0275inject(D),i.\u0275\u0275inject(p.Yd))},e.\u0275prov=i.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var k=n(89148);const{MANUAL:w,PAY_ALTERNATIVE:j,PAY_MIN:F,PAY_TOTAL:B}=d.o,{CcaDatePayMinCop:z,CcaDatePayMinUsd:X,CcaPayAltMinCop:Y,CcaPayAltMinUsd:W,CcaPayMinCop:G,CcaPayMinUsd:Z,CcaPayTotalCop:K,CcaPayTotalUsd:H}=k.Av;let x=(()=>{class e{constructor(t,r,a,v,E){this.products=t,this.productService=r,this.financials=a,this.repository=v,this.store=E}destination(){var t=this;return(0,m.Z)(function*(){try{return o.Either.success((yield t.repository.request()).reduce((r,a)=>{const{others:v,principals:E}=r;return(a.bank.isOccidente?E:v).push(a),r},{others:[],principals:[]}))}catch({message:r}){return o.Either.failure({message:r})}})()}source(t){var r=this;return(0,m.Z)(function*(){try{const a=yield r.products.requestAccountsForTransfer(),v=r.store.itIsConfirmation(),E=yield r.requestCreditCard(t);return o.Either.success({confirmation:v,destination:E,products:a})}catch({message:a}){return o.Either.failure({message:a})}})()}information(){var t=this;return(0,m.Z)(function*(){try{const r=t.store.getDestination(),a=yield t.productService.requestInformation(r),v=yield t.requestTrmUsd(),E=a?.getSection(z),R=a?.getSection(X);return o.Either.success({destination:r,min:new g.YH("VALOR M\xcdNIMO A PAGAR",F,v,a?.getSection(G),a?.getSection(Z)),alternative:new g.YH("VALOR M\xcdNIMO ALTERNO",j,v,a?.getSection(Y),a?.getSection(W)),total:new g.YH("SALDO ACTUAL",B,v,a?.getSection(K),a?.getSection(H)),dateCop:E?.valueFormat,dateUsd:R?.valueFormat})}catch({message:r}){return o.Either.failure({message:r})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const r=t.store.itIsConfirmation(),a=t.store.getAmount(),v=t.store.getSource(),E=t.store.getDestination(),R=yield t.productService.requestInformation(E),L=yield t.requestTrmUsd();return o.Either.success({destination:E,amount:a,confirmation:r,trm:L,source:v,min:new g.YH("Pago m\xednimo",F,L,R?.getSection(G),R?.getSection(Z)),alternative:new g.YH("Pago m\xednimo alterno",j,L,R?.getSection(Y),R?.getSection(W)),total:new g.YH("Saldo actual",B,L,R?.getSection(K),R?.getSection(H)),manual:new g.YH("Otro valor",w)})}catch({message:r}){return o.Either.failure({message:r})}})()}amount(){try{const t=this.store.itIsConfirmation(),r=this.store.getSource(),a=this.store.getDestination(),{cop:v}=this.store.getAmount();return o.Either.success({amount:v?.amount||0,confirmation:t,destination:a,source:r})}catch({message:t}){return o.Either.failure({message:t})}}confirmation(){var t=this;return(0,m.Z)(function*(){try{const r=b(t.store.currentState,yield t.requestTrmUsd());return o.Either.success({payment:r})}catch({message:r}){return o.Either.failure({message:r})}})()}requestCreditCard(t){var r=this;return(0,m.Z)(function*(){let a=r.store.getDestination();return!a&&t&&(a=(yield r.products.requestCreditCards()).find(({id:v})=>v===t),r.store.setDestination(a,!0)),a})()}requestTrmUsd(){return(0,o.catchPromise)(this.financials.request().then(([t])=>t))}}return e.\u0275fac=function(t){return new(t||e)(i.\u0275\u0275inject(p.hM),i.\u0275\u0275inject(p.M5),i.\u0275\u0275inject(C.rm),i.\u0275\u0275inject(c),i.\u0275\u0275inject(D))},e.\u0275prov=i.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},55351:(_,M,n)=>{n.d(M,{t:()=>T});var m=n(30263),u=n(39904),C=n(95437),p=n(96381),o=n(99877);let T=(()=>{class A{constructor(s,P,d){this.modalConfirmation=s,this.mboProvider=P,this.managerCreditCard=d}execute(s=!0){s?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:s,destination:P})=>{s?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:P.id}):this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)}})}}return A.\u0275fac=function(s){return new(s||A)(o.\u0275\u0275inject(m.$e),o.\u0275\u0275inject(C.ZL),o.\u0275\u0275inject(p.T))},A.\u0275prov=o.\u0275\u0275defineInjectable({token:A,factory:A.\u0275fac,providedIn:"root"}),A})()},1131:(_,M,n)=>{n.d(M,{o:()=>m});var m=(()=>{return(u=m||(m={}))[u.PAY_MIN=0]="PAY_MIN",u[u.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",u[u.PAY_TOTAL=2]="PAY_TOTAL",u[u.MANUAL=3]="MANUAL",m;var u})()},39838:(_,M,n)=>{n.r(M),n.d(M,{MboPaymentCreditCardSourcePageModule:()=>I});var m=n(17007),u=n(78007),C=n(79798),p=n(30263),o=n(15861),g=n(39904),T=n(95437),A=n(96381),U=n(55351),s=n(99877),P=n(83413),d=n(48774),f=n(4663);const h=g.Z6.PAYMENTS.CREDIT_CARD;let b=(()=>{class l{constructor(i,c,N,O,D){this.activateRoute=i,this.mboProvider=c,this.requestConfiguration=N,this.managerCreditCard=O,this.cancelProvider=D,this.confirmation=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_payment-creditcard-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(h.DESTINATION)}},this.cancelAction={id:"btn_payment-creditcard-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onInformation(){this.destination.isRequiredInformation&&this.mboProvider.navigation.next(h.INFORMATION)}onProduct(i){this.managerCreditCard.setSource(i).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?h.CONFIRMATION:!this.destination.isRequiredInformation||this.destination.isProtected?h.AMOUNT:h.SELECT_AMOUNT)}})}initializatedConfiguration(){var i=this;return(0,o.Z)(function*(){const{productId:c}=i.activateRoute.snapshot.queryParams;(yield i.requestConfiguration.source(c)).when({success:({confirmation:N,destination:O,products:D})=>{if(!O)return i.mboProvider.navigation.back(h.DESTINATION);i.confirmation=N,i.products=D,i.destination=O}},()=>{i.requesting=!1})})()}}return l.\u0275fac=function(i){return new(i||l)(s.\u0275\u0275directiveInject(u.ActivatedRoute),s.\u0275\u0275directiveInject(T.ZL),s.\u0275\u0275directiveInject(A.P),s.\u0275\u0275directiveInject(A.T),s.\u0275\u0275directiveInject(U.t))},l.\u0275cmp=s.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-creditcard-source-page"]],decls:7,vars:12,consts:[[1,"mbo-payment-creditcard-source-page__content"],[1,"mbo-payment-creditcard-source-page__header"],["title","Origen","progress","50%",3,"leftAction","rightAction"],[1,"mbo-payment-creditcard-source-page__body"],[3,"color","icon","title","number","detail","statusColor","statusLabel","hidden","click"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","select"]],template:function(i,c){1&i&&(s.\u0275\u0275elementStart(0,"div",0)(1,"div",1),s.\u0275\u0275element(2,"bocc-header-form",2),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(3,"div",3)(4,"bocc-card-product-summary",4),s.\u0275\u0275listener("click",function(){return c.onInformation()}),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"mbo-product-source-selector",5),s.\u0275\u0275listener("select",function(O){return c.onProduct(O)}),s.\u0275\u0275text(6," \xbfDesde d\xf3nde deseas pagar? "),s.\u0275\u0275elementEnd()()()),2&i&&(s.\u0275\u0275advance(2),s.\u0275\u0275property("leftAction",c.backAction)("rightAction",c.cancelAction),s.\u0275\u0275advance(2),s.\u0275\u0275property("color",null==c.destination?null:c.destination.color)("icon",null==c.destination?null:c.destination.logo)("title",null==c.destination?null:c.destination.nickname)("number",null==c.destination?null:c.destination.publicNumber)("detail",null==c.destination?null:c.destination.bank.name)("statusColor",null==c.destination||null==c.destination.status?null:c.destination.status.color)("statusLabel",null==c.destination||null==c.destination.status?null:c.destination.status.label)("hidden",!c.destination),s.\u0275\u0275advance(1),s.\u0275\u0275property("skeleton",c.requesting)("products",c.products))},dependencies:[P.D,d.J,f.c],styles:["/*!\n * MBO PaymentCreditCardSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 22/Jul/2022\n * Updated: 12/Ene/2024\n*/mbo-payment-creditcard-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-creditcard-source-page .mbo-payment-creditcard-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-creditcard-source-page .mbo-payment-creditcard-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}\n"],encapsulation:2}),l})(),I=(()=>{class l{}return l.\u0275fac=function(i){return new(i||l)},l.\u0275mod=s.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=s.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:b}]),p.D1,p.Jx,C.cV]}),l})()},63674:(_,M,n)=>{n.d(M,{Eg:()=>A,Lo:()=>p,Wl:()=>o,ZC:()=>g,_f:()=>u,br:()=>T,tl:()=>C});var m=n(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},C=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),p={color:"success",key:"paid",label:"Pagada"},o={color:"alert",key:"pending",label:"Por pagar"},g={color:"danger",key:"expired",label:"Vencida"},T={color:"info",key:"recurring",label:"Pago recurrente"},A={color:"info",key:"programmed",label:"Programado"}},66067:(_,M,n)=>{n.d(M,{S6:()=>U,T2:()=>T,UQ:()=>s,mZ:()=>A});var m=n(39904),u=n(6472),p=n(63674),o=n(31707);class T{constructor(d,f,h,b,I,l,y,i,c,N,O){this.id=d,this.type=f,this.name=h,this.nickname=b,this.number=I,this.bank=l,this.isAval=y,this.isProtected=i,this.isOwner=c,this.ownerName=N,this.ownerDocument=O,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,u.initials)(b),this.shortNumber=I.substring(I.length-4),this.descriptionNumber=`${h} ${I}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:l.logo,light:l.logo,standard:l.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(d){this.informationValue||(this.informationValue=d)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(d){return this.currenciesValue.includes(d)}}class A{constructor(d,f){this.id=d,this.type=f}}class U{constructor(d,f,h,b,I,l,y,i,c,N,O,D){this.uuid=d,this.number=f,this.nie=h,this.nickname=b,this.companyId=I,this.companyName=l,this.amount=y,this.registerDate=i,this.expirationDate=c,this.paid=N,this.statusCode=O,this.references=D,this.recurring=D.length>0,this.status=function g(P){switch(P){case o.U.EXPIRED:return p.ZC;case o.U.PENDING:return p.Wl;case o.U.PROGRAMMED:return p.Eg;case o.U.RECURRING:return p.br;default:return p.Lo}}(O)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class s{constructor(d,f,h,b,I,l,y,i){this.uuid=d,this.number=f,this.nickname=h,this.companyId=b,this.companyName=I,this.city=l,this.amount=y,this.isBiller=i}}},31707:(_,M,n)=>{n.d(M,{U:()=>m,f:()=>u});var m=(()=>{return(C=m||(m={})).RECURRING="1",C.EXPIRED="2",C.PENDING="3",C.PROGRAMMED="4",m;var C})(),u=(()=>{return(C=u||(u={})).BILLER="Servicio",C.NON_BILLER="Servicio",C.PSE="Servicio",C.TAX="Impuesto",C.LOAN="Obligaci\xf3n financiera",C.CREDIT_CARD="Obligaci\xf3n financiera",u;var C})()}}]);