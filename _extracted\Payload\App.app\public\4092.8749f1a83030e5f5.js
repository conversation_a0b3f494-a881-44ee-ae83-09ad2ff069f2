(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4092],{10954:(F,I,e)=>{e.d(I,{V:()=>A,Ws:()=>d,YH:()=>c,d6:()=>M,uJ:()=>h});var t=e(39904),v=e(87903),n=e(53113),i=e(66067);class d extends i.T2{constructor(C,m,b,u,s,f,y,p,z,L,l,T,S){super(C,m,b,u,s,y,p,z,L,T,S),this.colorValue=f,this.franchise=l,this.bank.isOccidente&&z&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,v.mm)(this,l),this.currenciesValue=l?.currencies||[t.y1],this.digitalValue="DIGITAL"===f}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class h{constructor(C,m,b){this.code=C,this.amount=m,this.amountCurrency=b||0}}class c{constructor(C,m,b,u,s){this.label=C,this.mode=m,this.copTotal=u?.value||0,this.usdTotal=s?.value||0,this.copUsdTotal=b?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new h("COP",this.copTotal,this.copTotal)}usdValue(){return new h("USD",this.copUsdTotal,this.usdTotal)}}class P{constructor(C,m,b){this.destination=C,this.source=m,this.currency=b}}class A{constructor(C,m,b,u,s,f){this.destination=C,this.source=m,this.isManual=b,this.trm=u,this.cop=s,this.usd=f,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new P(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new P(this.destination,this.source,this.usd):void 0}}class M extends n.LN{constructor(C,m,b){super(C,m),this.currency=b}}},96381:(F,I,e)=>{e.d(I,{T:()=>S,P:()=>Q});var t=e(15861),v=e(77279),n=e(81536),i=e(87956),d=e(98699),h=e(10954),c=e(39904),P=e(29306),A=e(7464),M=e(87903),O=e(53113),C=e(1131);function u(o,B){return new h.V(o.destination,o.source,o.mode===C.o.MANUAL,B,o.cop,o.usd)}var f=e(71776),y=e(42168),p=e(99877);let z=(()=>{class o{constructor(r,a){this.http=r,a.subscribes(c.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,y.firstValueFrom)(this.http.get(c.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,y.map)(({content:r})=>r.map(a=>function b(o){return new h.Ws(o.id,o.acctType,o.acctTypeName,"DIGITAL"===o.color?c.CG:o.loanName,o.acctId,o.isOwner&&o.color||"NONE",(0,A.RO)(o.bankId,o.bankName),o.isAval,o.dynamo||!1,o.isOwner,o.creditCardType?function m(o){return new P.dD(o?.code,o?.description,0,0)}(o.creditCardType):void 0,o.isOwner?void 0:o.owner,o.isOwner?void 0:new O.dp((0,M.nX)(o.ownerIdType),o.ownerId))}(a))),(0,y.tap)(r=>{this.creditCards=r})))}}return o.\u0275fac=function(r){return new(r||o)(p.\u0275\u0275inject(f.HttpClient),p.\u0275\u0275inject(i.Yd))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),L=(()=>{class o{constructor(r){this.http=r}send(r){return(0,M.EC)([r.getPaymentCop(),r.getPaymentUsd()].filter(a=>!!a).map(a=>()=>this.sendCurrency(a)))}sendCurrency(r){return(0,y.firstValueFrom)(this.http.post(c.bV.PAYMENTS.CREDIT_CARD,function s(o){return{acctIdFrom:o.source.id,acctNickNameFrom:o.source.nickname,bankIdFrom:o.source.bank.id,acctIdTo:o.destination.id,acctNameTo:o.destination.nickname,bankIdTo:o.destination.bank.id,bankNameTo:o.destination.bank.name,amt:Math.ceil(o.currency.amount),curCode:o.currency.code,paymentDesc:""}}(r)).pipe((0,y.map)(a=>{const _=(0,M.l1)(a,"SUCCESS"),{type:D,message:R}=_;return new h.d6(D,R,r.currency)}),(0,y.catchError)(a=>{const{message:_}=(0,M.rU)(a);return(0,y.of)(new h.d6("ERROR",_,r.currency))})))}}return o.\u0275fac=function(r){return new(r||o)(p.\u0275\u0275inject(f.HttpClient))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var l=e(20691);let T=(()=>{class o extends l.Store{constructor(r){super({confirmation:!1,fromCustomer:!1,mode:C.o.PAY_MIN}),this.eventBusService=r,this.eventBusService.subscribes(c.PU,()=>{this.reset()})}setDestination(r,a=!1){this.reduce(_=>({..._,destination:r,fromCustomer:a}))}getDestination(){return this.select(({destination:r})=>r)}itIsFromCustomer(){return this.select(({fromCustomer:r})=>r)}setSource(r){this.reduce(a=>({...a,source:r}))}getSource(){return this.select(({source:r})=>r)}setAmount(r){const{cop:a,mode:_,usd:D}=r;this.reduce(R=>({...R,cop:a,mode:_,usd:D,confirmation:!0}))}getAmount(){return this.select(({mode:r,cop:a,usd:_})=>({cop:a,mode:r,usd:_}))}setCurrencyCode(r){this.reduce(a=>({...a,currencyCode:r}))}itIsConfirmation(){return this.select(({confirmation:r})=>r)}}return o.\u0275fac=function(r){return new(r||o)(p.\u0275\u0275inject(i.Yd))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),S=(()=>{class o{constructor(r,a,_,D,R){this.financials=r,this.productService=a,this.repository=_,this.store=D,this.eventBusService=R}setDestination(r){var a=this;return(0,t.Z)(function*(){try{return r.isRequiredInformation&&(yield a.productService.requestInformation(r)),d.Either.success(a.store.setDestination(r))}catch{return d.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(r){try{return d.Either.success(this.store.setSource(r))}catch({message:a}){return d.Either.failure({message:a})}}setAmount(r){try{return d.Either.success(this.store.setAmount(r))}catch({message:a}){return d.Either.failure({message:a})}}setCurrencyCode(r){try{return d.Either.success(this.store.setCurrencyCode(r))}catch({message:a}){return d.Either.failure({message:a})}}reset(){try{const r=this.store.itIsFromCustomer(),a=this.store.getDestination();return this.store.reset(),d.Either.success({fromCustomer:r,destination:a})}catch({message:r}){return d.Either.failure({message:r})}}send(){var r=this;return(0,t.Z)(function*(){const a=u(r.store.currentState,yield r.requestTrmUsd()),_=yield r.execute(a),D=_.reduce((R,{isError:j})=>R&&!j,!0);return r.eventBusService.emit(D?v.q.TransactionSuccess:v.q.TransactionFailed),d.Either.success({creditCard:a,status:_})})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([r])=>r))}execute(r){try{return this.repository.send(r)}catch({message:a}){return Promise.resolve([new h.d6("ERROR",a)])}}}return o.\u0275fac=function(r){return new(r||o)(p.\u0275\u0275inject(n.rm),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(L),p.\u0275\u0275inject(T),p.\u0275\u0275inject(i.Yd))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var E=e(89148);const{MANUAL:U,PAY_ALTERNATIVE:g,PAY_MIN:x,PAY_TOTAL:N}=C.o,{CcaDatePayMinCop:V,CcaDatePayMinUsd:$,CcaPayAltMinCop:K,CcaPayAltMinUsd:w,CcaPayMinCop:Y,CcaPayMinUsd:G,CcaPayTotalCop:H,CcaPayTotalUsd:Z}=E.Av;let Q=(()=>{class o{constructor(r,a,_,D,R){this.products=r,this.productService=a,this.financials=_,this.repository=D,this.store=R}destination(){var r=this;return(0,t.Z)(function*(){try{return d.Either.success((yield r.repository.request()).reduce((a,_)=>{const{others:D,principals:R}=a;return(_.bank.isOccidente?R:D).push(_),a},{others:[],principals:[]}))}catch({message:a}){return d.Either.failure({message:a})}})()}source(r){var a=this;return(0,t.Z)(function*(){try{const _=yield a.products.requestAccountsForTransfer(),D=a.store.itIsConfirmation(),R=yield a.requestCreditCard(r);return d.Either.success({confirmation:D,destination:R,products:_})}catch({message:_}){return d.Either.failure({message:_})}})()}information(){var r=this;return(0,t.Z)(function*(){try{const a=r.store.getDestination(),_=yield r.productService.requestInformation(a),D=yield r.requestTrmUsd(),R=_?.getSection(V),j=_?.getSection($);return d.Either.success({destination:a,min:new h.YH("VALOR M\xcdNIMO A PAGAR",x,D,_?.getSection(Y),_?.getSection(G)),alternative:new h.YH("VALOR M\xcdNIMO ALTERNO",g,D,_?.getSection(K),_?.getSection(w)),total:new h.YH("SALDO ACTUAL",N,D,_?.getSection(H),_?.getSection(Z)),dateCop:R?.valueFormat,dateUsd:j?.valueFormat})}catch({message:a}){return d.Either.failure({message:a})}})()}selectAmount(){var r=this;return(0,t.Z)(function*(){try{const a=r.store.itIsConfirmation(),_=r.store.getAmount(),D=r.store.getSource(),R=r.store.getDestination(),j=yield r.productService.requestInformation(R),W=yield r.requestTrmUsd();return d.Either.success({destination:R,amount:_,confirmation:a,trm:W,source:D,min:new h.YH("Pago m\xednimo",x,W,j?.getSection(Y),j?.getSection(G)),alternative:new h.YH("Pago m\xednimo alterno",g,W,j?.getSection(K),j?.getSection(w)),total:new h.YH("Saldo actual",N,W,j?.getSection(H),j?.getSection(Z)),manual:new h.YH("Otro valor",U)})}catch({message:a}){return d.Either.failure({message:a})}})()}amount(){try{const r=this.store.itIsConfirmation(),a=this.store.getSource(),_=this.store.getDestination(),{cop:D}=this.store.getAmount();return d.Either.success({amount:D?.amount||0,confirmation:r,destination:_,source:a})}catch({message:r}){return d.Either.failure({message:r})}}confirmation(){var r=this;return(0,t.Z)(function*(){try{const a=u(r.store.currentState,yield r.requestTrmUsd());return d.Either.success({payment:a})}catch({message:a}){return d.Either.failure({message:a})}})()}requestCreditCard(r){var a=this;return(0,t.Z)(function*(){let _=a.store.getDestination();return!_&&r&&(_=(yield a.products.requestCreditCards()).find(({id:D})=>D===r),a.store.setDestination(_,!0)),_})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([r])=>r))}}return o.\u0275fac=function(r){return new(r||o)(p.\u0275\u0275inject(i.hM),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(n.rm),p.\u0275\u0275inject(z),p.\u0275\u0275inject(T))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},1131:(F,I,e)=>{e.d(I,{o:()=>t});var t=(()=>{return(v=t||(t={}))[v.PAY_MIN=0]="PAY_MIN",v[v.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",v[v.PAY_TOTAL=2]="PAY_TOTAL",v[v.MANUAL=3]="MANUAL",t;var v})()},63111:(F,I,e)=>{e.d(I,{m:()=>i});var t=e(99877),n=e(3235);let i=(()=>{class d{constructor(){this.copAmount=0,this.copUsdAmount=0,this.usdAmount=0}}return d.\u0275fac=function(c){return new(c||d)},d.\u0275cmp=t.\u0275\u0275defineComponent({type:d,selectors:[["mbo-card-currency-amount"]],inputs:{copAmount:"copAmount",copUsdAmount:"copUsdAmount",usdAmount:"usdAmount",theme:"theme"},decls:18,vars:15,consts:[[1,"mbo-card-currency-amount__content"],[1,"mbo-card-currency-amount__currency"],[1,"mbo-card-currency-amount__currency__logo"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"mbo-card-currency-amount__currency__content"],[1,"mbo-card-currency-amount__currency__detail","caption-medium"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"mbo-card-currency-amount__currency__value","caption-medium"]],template:function(c,P){1&c&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),t.\u0275\u0275element(3,"img",3),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"div",4)(5,"span",5),t.\u0275\u0275text(6),t.\u0275\u0275pipe(7,"boccCurrency"),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(8,"div",1)(9,"div",2),t.\u0275\u0275element(10,"img",6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"div",4)(12,"span",5),t.\u0275\u0275text(13),t.\u0275\u0275pipe(14,"boccCurrency"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(15,"span",7),t.\u0275\u0275text(16),t.\u0275\u0275pipe(17,"boccCurrency"),t.\u0275\u0275elementEnd()()()()),2&c&&(t.\u0275\u0275advance(6),t.\u0275\u0275textInterpolate1(" ",t.\u0275\u0275pipeBind3(7,3,null==P.copAmount?null:P.copAmount.toString(),"$",!1)," "),t.\u0275\u0275advance(7),t.\u0275\u0275textInterpolate1(" ",t.\u0275\u0275pipeBind3(14,7,null==P.copUsdAmount?null:P.copUsdAmount.toString(),"$",!1)," "),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1(" ",t.\u0275\u0275pipeBind3(17,11,null==P.usdAmount?null:P.usdAmount.toString(),"USD",!0)," "))},dependencies:[n.T],styles:["/*!\n * MBO CardCurrencyAmount Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 28/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-card-currency-amount{position:relative;width:100%;display:block}mbo-card-currency-amount .mbo-card-currency-amount__content{position:relative;display:flex;width:100%;padding:var(--sizing-x6);box-sizing:border-box}mbo-card-currency-amount .mbo-card-currency-amount__currency{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo{max-width:var(--sizing-x8);max-height:var(--sizing-x8)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo img{width:100%;height:100%}mbo-card-currency-amount .mbo-card-currency-amount__currency__content{display:flex;width:calc(100% - 14rem);flex-direction:column;row-gap:var(--sizing-x1)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail{position:relative;width:100%;color:var(--color-carbon-lighter-700)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail:before{margin-right:var(--sizing-x1);text-align:left;width:var(--sizing-x4)}mbo-card-currency-amount .mbo-card-currency-amount__currency__value{position:relative;width:100%;color:var(--color-amathyst-700)}\n"],encapsulation:2}),d})()},2297:(F,I,e)=>{e.d(I,{p:()=>C});var t=e(30263),n=(e(10954),e(99877)),d=e(17007),c=e(2460),P=e(45542),A=e(3235),M=e(16450);function O(m,b){if(1&m&&(n.\u0275\u0275elementStart(0,"div",7)(1,"div",8),n.\u0275\u0275element(2,"img",15),n.\u0275\u0275elementStart(3,"span",10),n.\u0275\u0275text(4,"Deuda en d\xf3lares"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(5,"div",11)(6,"span",10),n.\u0275\u0275text(7),n.\u0275\u0275pipe(8,"boccCurrencyCop"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"span",16),n.\u0275\u0275text(10),n.\u0275\u0275pipe(11,"boccCurrency"),n.\u0275\u0275elementEnd()()()),2&m){const u=n.\u0275\u0275nextContext();n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(8,2,null==u.currency?null:u.currency.copUsdTotal.toString(),!1)," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind3(11,5,null==u.currency?null:u.currency.usdTotal.toString(),"USD",!1)," ")}}let C=(()=>{class m{constructor(u){this.modalConfirmation=u,this.canUsd=!0,this.condense=!0}onHeader(){this.condense=!this.condense}onInformation(){this.modalConfirmation.execute({message:this.message,title:this.title,accept:{label:"Aceptar"}})}}return m.\u0275fac=function(u){return new(u||m)(n.\u0275\u0275directiveInject(t.$e))},m.\u0275cmp=n.\u0275\u0275defineComponent({type:m,selectors:[["mbo-creditcard-information"]],inputs:{currency:"currency",title:"title",message:"message",canUsd:"canUsd"},decls:24,vars:13,consts:[[1,"mbo-creditcard-information__content"],[1,"mbo-creditcard-information__header",3,"click"],[1,"overline-medium"],[1,"mbo-creditcard-information__header__amount"],[1,"body1-medium"],[3,"icon"],[1,"mbo-creditcard-information__body",3,"hidden"],[1,"mbo-creditcard-information__currency"],[1,"mbo-creditcard-information__currency__label"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"caption-medium"],[1,"mbo-creditcard-information__currency__amount"],["class","mbo-creditcard-information__currency",4,"ngIf"],["bocc-button","flat","prefixIcon","chat-info",3,"click"],[1,"bocc-divider"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"usd","caption-medium"]],template:function(u,s){1&u&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275listener("click",function(){return s.onHeader()}),n.\u0275\u0275elementStart(2,"label",2),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"span",4),n.\u0275\u0275text(6),n.\u0275\u0275pipe(7,"boccCurrencyCop"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(8,"bocc-icon",5),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(9,"div",6)(10,"div",7)(11,"div",8),n.\u0275\u0275element(12,"img",9),n.\u0275\u0275elementStart(13,"span",10),n.\u0275\u0275text(14,"Deuda en pesos"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(15,"div",11)(16,"span",10),n.\u0275\u0275text(17),n.\u0275\u0275pipe(18,"boccCurrencyCop"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275template(19,O,12,9,"div",12),n.\u0275\u0275elementStart(20,"button",13),n.\u0275\u0275listener("click",function(){return s.onInformation()}),n.\u0275\u0275elementStart(21,"span"),n.\u0275\u0275text(22),n.\u0275\u0275elementEnd()(),n.\u0275\u0275element(23,"div",14),n.\u0275\u0275elementEnd()()),2&u&&(n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate(null==s.currency?null:s.currency.label),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(7,7,null==s.currency?null:s.currency.amountTotal.toString(),!1)," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("icon",s.condense?"list-open":"list-close"),n.\u0275\u0275advance(1),n.\u0275\u0275property("hidden",s.condense),n.\u0275\u0275advance(8),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(18,10,null==s.currency?null:s.currency.copTotal.toString(),!1)," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",s.canUsd),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate(s.title))},dependencies:[d.NgIf,c.Z,P.P,A.T,M.f],styles:["/*!\n * MBO CreditCardInformation Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Mar/2023\n * Updated: 08/Jul/2024\n*/mbo-creditcard-information{position:relative;width:100%;display:block}mbo-creditcard-information .mbo-creditcard-information__content{position:relative;width:100%}mbo-creditcard-information .mbo-creditcard-information__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header>label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__header__amount{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header__amount>span{width:100%;text-align:right}mbo-creditcard-information .mbo-creditcard-information__header__amount>bocc-icon{color:var(--color-blue-700)}mbo-creditcard-information .mbo-creditcard-information__body{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;display:flex;width:100%;margin-top:var(--sizing-x6);flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button{width:100%}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label img{margin-right:var(--sizing-x4);width:var(--sizing-x8);height:var(--sizing-x8)}mbo-creditcard-information .mbo-creditcard-information__currency__label span{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency__amount{padding-right:var(--sizing-x6);box-sizing:border-box;margin:auto 0rem;display:flex;flex-direction:column}mbo-creditcard-information .mbo-creditcard-information__currency__amount span{color:var(--color-carbon-lighter-700);text-align:right}mbo-creditcard-information .mbo-creditcard-information__currency__amount span.usd{color:var(--color-amathyst-700)}\n"],encapsulation:2}),m})()},33022:(F,I,e)=>{e.d(I,{H:()=>u});var t=e(99877),n=e(39904),d=(e(10954),e(17007)),c=e(13462),A=e(45542),M=e(92275),O=e(55944),C=e(19102);function m(s,f){if(1&s){const y=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",14)(1,"div",15)(2,"label",16),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"span",17),t.\u0275\u0275text(5),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"div")(7,"bocc-badge"),t.\u0275\u0275text(8),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(9,"div",18)(10,"button",19),t.\u0275\u0275listener("click",function(){const L=t.\u0275\u0275restoreView(y).$implicit,l=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(l.onSelect(L))}),t.\u0275\u0275text(11," Ver recibo "),t.\u0275\u0275elementEnd()()()}if(2&s){const y=f.$implicit,p=f.index,z=t.\u0275\u0275nextContext();t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1("PAGO ",p+1,""),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(z.getPayLabel(y)),t.\u0275\u0275advance(2),t.\u0275\u0275attribute("bocc-theme",z.getPayColor(y)),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",z.getPayStatus(y)," ")}}var b=(()=>{return(s=b||(b={}))[s.FAILURE=0]="FAILURE",s[s.INCOMPLETE=1]="INCOMPLETE",s[s.SUCCESS=2]="SUCCESS",b;var s})();let u=(()=>{class s{constructor(){this.status=[],this.select=new t.EventEmitter}get state(){return this.status.reduce((y,{isError:p})=>p?y:y+1,0)}get title(){switch(this.state){case b.SUCCESS:return"\xa1Pagos exitosos!";case b.INCOMPLETE:return"\xa1Pagos parcialmente exitosos!";default:return"\xa1Pagos fallidos!"}}get animation(){switch(this.state){case b.SUCCESS:case b.INCOMPLETE:return n.F6;default:return n.cj}}getPayLabel(y){return"USD"===y.currency?.code?"Pago en d\xf3lares":"Pago en pesos"}getPayColor(y){return y.isError?"danger":"success"}getPayStatus(y){return y.isError?"Fallido":"Exitoso"}onSelect(y){this.select.emit(y)}}return s.\u0275fac=function(y){return new(y||s)},s.\u0275cmp=t.\u0275\u0275defineComponent({type:s,selectors:[["mbo-creditcard-payments-card"]],inputs:{payment:"payment",status:"status"},outputs:{select:"select"},decls:17,vars:5,consts:[[1,"mbo-creditcard-payments-card__content"],[1,"mbo-creditcard-payments-card__header"],[3,"result"],[1,"mbo-creditcard-payments-card__status"],[3,"options"],[1,"mbo-creditcard-payments-card__subheader"],[1,"subtitle2-medium"],[1,"body2-medium"],[1,"bocc-divider"],[1,"mbo-creditcard-payments-card__body"],["class","mbo-creditcard-payments-card__payment",4,"ngFor","ngForOf"],[1,"mbo-creditcard-payments-card__amount"],[1,"smalltext-bold"],[3,"amount"],[1,"mbo-creditcard-payments-card__payment"],[1,"mbo-creditcard-payments-card__payment__content"],[1,"caption-medium"],[1,"smalltext-medium"],[1,"mbo-creditcard-payments-card__payment__action"],["bocc-button","flat","prefixIcon","extract-receive",3,"click"]],template:function(y,p){1&y&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"mbo-bank-logo",2),t.\u0275\u0275elementStart(3,"div",3),t.\u0275\u0275element(4,"ng-lottie",4),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(5,"div",5)(6,"label",6),t.\u0275\u0275text(7),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(8,"span",7),t.\u0275\u0275text(9,"Revis\xe1 los detalles de los pagos."),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275element(10,"div",8),t.\u0275\u0275elementStart(11,"div",9),t.\u0275\u0275template(12,m,12,4,"div",10),t.\u0275\u0275elementStart(13,"div",11)(14,"label",12),t.\u0275\u0275text(15,"TOTAL PAGADO"),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(16,"bocc-amount",13),t.\u0275\u0275elementEnd()()()),2&y&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("result",!0),t.\u0275\u0275advance(2),t.\u0275\u0275property("options",p.animation),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(p.title),t.\u0275\u0275advance(5),t.\u0275\u0275property("ngForOf",p.status),t.\u0275\u0275advance(4),t.\u0275\u0275property("amount",null==p.payment?null:p.payment.totalAmount))},dependencies:[d.NgForOf,c.LottieComponent,A.P,M.O,O.Q,C.r],styles:["/*!\n * MBO CreditCardPaymentsCard Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 09/Feb/2024\n * Updated: 08/Jul/2024\n*/mbo-creditcard-payments-card{position:relative;display:block;padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x8);box-shadow:var(--z-bottom-lighter-8);background:var(--color-carbon-lighter-50)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__header{position:relative;display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>label{color:var(--color-carbon-darker-1000);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment{position:relative;display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>label{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>span{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__action{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount label{color:var(--color-carbon-lighter-400);text-align:right}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount bocc-amount{text-align:right}\n"],encapsulation:2}),s})()},50773:(F,I,e)=>{e.d(I,{n:()=>C}),e(57544),e(10954);var i=e(99877),h=e(17007),P=e(80349),A=e(63111),M=e(3235);function O(m,b){if(1&m&&(i.\u0275\u0275elementStart(0,"div",6),i.\u0275\u0275element(1,"mbo-card-currency-amount",7),i.\u0275\u0275elementEnd()),2&m){const u=i.\u0275\u0275nextContext();i.\u0275\u0275classProp("mbo-creditcard-radiobutton__footer--disabled",u.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("copAmount",null==u.value?null:u.value.copTotal)("copUsdAmount",null==u.value?null:u.value.copUsdTotal)("usdAmount",null==u.value?null:u.value.usdTotal)}}let C=(()=>{class m{constructor(){this.disabled=!1,this.hasFooter=!1,this.skeleton=!1}get checked(){return this.radioControl&&this.radioControl.value===this.value}onComponent(){this.disabled||this.radioControl?.setValue(this.value)}}return m.\u0275fac=function(u){return new(u||m)},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-creditcard-radiobutton"]],inputs:{radioControl:"radioControl",value:"value",theme:"theme",disabled:"disabled",hasFooter:"hasFooter",skeleton:"skeleton"},decls:9,vars:10,consts:[[1,"mbo-creditcard-radiobutton__content",3,"click"],[3,"checked"],[1,"mbo-creditcard-radiobutton__body"],[1,"mbo-creditcard-radiobutton__label","body2-medium"],[1,"mbo-creditcard-radiobutton__amount","subtitle1-medium"],["class","mbo-creditcard-radiobutton__footer",3,"mbo-creditcard-radiobutton__footer--disabled",4,"ngIf"],[1,"mbo-creditcard-radiobutton__footer"],[3,"copAmount","copUsdAmount","usdAmount"]],template:function(u,s){1&u&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275listener("click",function(){return s.onComponent()}),i.\u0275\u0275element(1,"bocc-radiobutton",1),i.\u0275\u0275elementStart(2,"div",2)(3,"span",3),i.\u0275\u0275text(4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"span",4),i.\u0275\u0275text(6),i.\u0275\u0275pipe(7,"boccCurrency"),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275template(8,O,2,5,"div",5)),2&u&&(i.\u0275\u0275classProp("mbo-creditcard-radiobutton__content--disabled",s.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("checked",s.checked),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",null==s.value?null:s.value.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind3(7,6,null==s.value?null:s.value.amountTotal.toString(),"$",!1)," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!s.hasFooter))},dependencies:[h.NgIf,P.V,A.m,M.T],styles:["/*!\n * MBO CreditCardRadiobutton Component\n * v2.2.0\n * Author: MB Frontend Developers\n * Created: 17/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x8);--pvt-body-padding: var(--sizing-x8);--pvt-amount-padding: 0rem var(--sizing-x2);position:relative;display:block;width:100%;box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content--disabled{opacity:.5;pointer-events:none}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer{position:relative;display:flex;width:100%;padding:var(--pvt-body-padding);box-sizing:border-box;border-top:var(--border-1-lighter-300);background:var(--overlay-lgrey-40)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer--disabled{pointer-events:none;opacity:.5}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer .mbo-card-currency-amount__content{padding:var(--pvt-amount-padding)}@media screen and (max-width: 320px){mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x6);--pvt-body-padding: var(--sizing-x6)}}\n"],encapsulation:2}),m})()},11747:(F,I,e)=>{e.d(I,{PF:()=>d,Hf:()=>b,xm:()=>m,kz:()=>s,Cq:()=>L}),e(63111);var v=e(17007),n=e(83651),i=e(99877);let d=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,n.P6]}),l})();e(2297),e(33022);var P=e(79798),A=e(30263),M=e(44487),O=e.n(M),C=e(13462);let m=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,C.LottieModule.forRoot({player:()=>O()}),A.P8,A.Oh,A.Qg,P.rw]}),l})(),b=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,A.Zl,A.P8,A.oc,n.P6]}),l})();e(50773);let s=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,A.Dj,A.V6,A.Qg,n.P6,d]}),l})();e(57544);let L=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule]}),l})()},74092:(F,I,e)=>{e.r(I),e.d(I,{MboPaymentCreditCardResultPageModule:()=>S});var t=e(17007),v=e(78007),n=e(79798),i=e(30263),d=e(11747),h=e(15861),c=e(99877),P=e(39904),A=e(95437),M=e(87903),O=e(53113);function C(E){const{isError:U,message:g}=E;return{animation:(0,M.jY)(E),title:U?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:g}}function m({isError:E}){return E?[(0,M.wT)("Finalizar","finish","outline"),(0,M.wT)("Volver a intentar","retry")]:[(0,M.wT)("Hacer otro pago","retry","outline"),(0,M.wT)("Finalizar","finish")]}function b(E){const{dateFormat:U,timeFormat:g}=new O.ou,{status:{currency:x},creditCard:N}=E,V=[];return V.push((0,M.SP)("DESTINO",N.destination.nickname,N.destination.number)),V.push((0,M._f)("VALOR",x.amount)),"USD"===x.code&&V.push((0,M._f)("EQUIVALENTES EN D\xd3LARES",x.amountCurrency,"USD")),V.push((0,M.cZ)(U,g)),V}var s=e(96381),f=e(10464),y=e(78021),p=e(45542),z=e(33022),L=e(16442);function l(E,U){if(1&E&&(c.\u0275\u0275elementStart(0,"div",9),c.\u0275\u0275element(1,"mbo-header-result",10),c.\u0275\u0275elementEnd()),2&E){const g=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("leftAction",g.leftAction)("rightActions",g.rightActions)}}let T=(()=>{class E{constructor(g,x,N){this.ref=g,this.mboProvider=x,this.managerCreditCard=N,this.requesting=!0,this.templates=[],this.template=P.$d,this.status=[],this.singlePayment=!0,this.rightActions=[]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-creditcard-result-page_template"),this.initializatedTransaction()}onAction(g){this.mboProvider.navigation.next("finish"===g?P.Z6.CUSTOMER.PRODUCTS.HOME:P.Z6.PAYMENTS.CREDIT_CARD.DESTINATION)}onPayment(g){this.openReceiveCreditCard(this.payment,g)}initializatedTransaction(){var g=this;return(0,h.Z)(function*(){(yield g.managerCreditCard.send()).when({success:({creditCard:x,status:N})=>{g.payment=x,g.status=N,1===g.status.length?g.openReceiveCreditCard(x,g.status[0]):g.singlePayment=!1}},()=>{g.requesting=!1,g.managerCreditCard.reset()})})()}openReceiveCreditCard(g,x){this.template=function u(E){const{status:U}=E;return{actions:m(U),error:U.isError,header:C(U),informations:b(E),skeleton:!1}}({creditCard:g,status:x}),this.leftAction={id:"btn_payment-creditcard-result-page_back",prefixIcon:"arrow-left",hidden:this.status.length<=1,click:()=>{this.singlePayment=!1}},this.rightActions=[{id:"btn_payment-creditcard-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}],this.singlePayment=!0}}return E.\u0275fac=function(g){return new(g||E)(c.\u0275\u0275directiveInject(c.ElementRef),c.\u0275\u0275directiveInject(A.ZL),c.\u0275\u0275directiveInject(s.T))},E.\u0275cmp=c.\u0275\u0275defineComponent({type:E,selectors:[["mbo-payment-creditcard-result-page"]],decls:14,vars:7,consts:[[1,"mbo-payment-creditcard-result-page__content","mbo-page__scroller"],["class","mbo-payment-creditcard-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-creditcard-result-page__body"],[1,"mbo-payment-creditcard-result-page__card",3,"hidden"],[3,"payment","status","select"],[1,"mbo-payment-creditcard-result-page__card__footer"],["bocc-button","outline",3,"click"],["bocc-button","raised",3,"click"],["id","crd_payment-creditcard-result-page_template",3,"template","disabled","hidden","action"],[1,"mbo-payment-creditcard-result-page__header","mbo-page__header"],[3,"leftAction","rightActions"]],template:function(g,x){1&g&&(c.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),c.\u0275\u0275template(2,l,2,2,"div",1),c.\u0275\u0275elementStart(3,"div",2)(4,"div",3)(5,"mbo-creditcard-payments-card",4),c.\u0275\u0275listener("select",function(V){return x.onPayment(V)}),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(6,"div",5)(7,"button",6),c.\u0275\u0275listener("click",function(){return x.onAction("retry")}),c.\u0275\u0275elementStart(8,"span"),c.\u0275\u0275text(9,"Hacer otro pago"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(10,"button",7),c.\u0275\u0275listener("click",function(){return x.onAction("finish")}),c.\u0275\u0275elementStart(11,"span"),c.\u0275\u0275text(12,"Finalizar"),c.\u0275\u0275elementEnd()()()(),c.\u0275\u0275elementStart(13,"mbo-card-transaction-template",8),c.\u0275\u0275listener("action",function(V){return x.onAction(V)}),c.\u0275\u0275elementEnd()()()()),2&g&&(c.\u0275\u0275advance(2),c.\u0275\u0275property("ngIf",!x.requesting&&x.singlePayment),c.\u0275\u0275advance(2),c.\u0275\u0275property("hidden",x.singlePayment),c.\u0275\u0275advance(1),c.\u0275\u0275property("payment",x.payment)("status",x.status),c.\u0275\u0275advance(8),c.\u0275\u0275property("template",x.template)("disabled",x.status.length>1)("hidden",!x.singlePayment))},dependencies:[t.NgIf,f.K,y.c,p.P,z.H,L.u],styles:["/*!\n * MBO PaymentCreditCardResult Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 26/Jul/2022\n * Updated: 10/Feb/2024\n*/mbo-payment-creditcard-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-creditcard-result-page .mbo-payment-creditcard-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-creditcard-result-page .mbo-payment-creditcard-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-payment-creditcard-result-page .mbo-payment-creditcard-result-page__card{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}mbo-payment-creditcard-result-page .mbo-payment-creditcard-result-page__card__footer{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}\n"],encapsulation:2}),E})(),S=(()=>{class E{}return E.\u0275fac=function(g){return new(g||E)},E.\u0275mod=c.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=c.\u0275\u0275defineInjector({imports:[t.CommonModule,v.RouterModule.forChild([{path:"",component:T}]),n.KI,n.cN,i.P8,d.xm,n.tu]}),E})()},63674:(F,I,e)=>{e.d(I,{Eg:()=>P,Lo:()=>i,Wl:()=>d,ZC:()=>h,_f:()=>v,br:()=>c,tl:()=>n});var t=e(29306);const v={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},n=new t.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),i={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},h={color:"danger",key:"expired",label:"Vencida"},c={color:"info",key:"recurring",label:"Pago recurrente"},P={color:"info",key:"programmed",label:"Programado"}},66067:(F,I,e)=>{e.d(I,{S6:()=>A,T2:()=>c,UQ:()=>M,mZ:()=>P});var t=e(39904),v=e(6472),i=e(63674),d=e(31707);class c{constructor(C,m,b,u,s,f,y,p,z,L,l){this.id=C,this.type=m,this.name=b,this.nickname=u,this.number=s,this.bank=f,this.isAval=y,this.isProtected=p,this.isOwner=z,this.ownerName=L,this.ownerDocument=l,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[t.y1],this.initialsName=(0,v.initials)(u),this.shortNumber=s.substring(s.length-4),this.descriptionNumber=`${b} ${s}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:f.logo,light:f.logo,standard:f.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(C){this.informationValue||(this.informationValue=C)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(C){return this.currenciesValue.includes(C)}}class P{constructor(C,m){this.id=C,this.type=m}}class A{constructor(C,m,b,u,s,f,y,p,z,L,l,T){this.uuid=C,this.number=m,this.nie=b,this.nickname=u,this.companyId=s,this.companyName=f,this.amount=y,this.registerDate=p,this.expirationDate=z,this.paid=L,this.statusCode=l,this.references=T,this.recurring=T.length>0,this.status=function h(O){switch(O){case d.U.EXPIRED:return i.ZC;case d.U.PENDING:return i.Wl;case d.U.PROGRAMMED:return i.Eg;case d.U.RECURRING:return i.br;default:return i.Lo}}(l)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class M{constructor(C,m,b,u,s,f,y,p){this.uuid=C,this.number=m,this.nickname=b,this.companyId=u,this.companyName=s,this.city=f,this.amount=y,this.isBiller=p}}},31707:(F,I,e)=>{e.d(I,{U:()=>t,f:()=>v});var t=(()=>{return(n=t||(t={})).RECURRING="1",n.EXPIRED="2",n.PENDING="3",n.PROGRAMMED="4",t;var n})(),v=(()=>{return(n=v||(v={})).BILLER="Servicio",n.NON_BILLER="Servicio",n.PSE="Servicio",n.TAX="Impuesto",n.LOAN="Obligaci\xf3n financiera",n.CREDIT_CARD="Obligaci\xf3n financiera",v;var n})()}}]);