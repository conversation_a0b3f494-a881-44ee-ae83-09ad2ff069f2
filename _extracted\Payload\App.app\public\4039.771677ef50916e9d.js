(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4039],{44039:(C,l,t)=>{t.r(l),t.d(l,{MboAuthenticationModule:()=>I});var M=t(17007),u=t(78007),g=t(39904),d=t(99877);const{LOGIN:s,MANDATORY_UPDATE:i,ONBOARDING:r,SIGNOUT:{INACTIVITY:c,TIMEOUT:f}}=g.Z6.AUTHENTICATION;let h=(()=>{class o{canDeactivate(n,v,A,e){return![i,r,c,f].includes(e.url)}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275prov=d.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();const m=[{path:"",redirectTo:"manager-update",pathMatch:"full"},{path:"manager-update",loadChildren:()=>t.e(9822).then(t.bind(t,29822)).then(o=>o.MboManagerUpdatePageModule)},{path:"onboarding",loadComponent:()=>t.e(8332).then(t.bind(t,58332)).then(o=>o.MboOnboardingPage)},{path:"login",loadChildren:()=>t.e(3947).then(t.bind(t,83947)).then(o=>o.MboLoginPageModule),canDeactivate:[h]},{path:"enrollment",loadChildren:()=>t.e(111).then(t.bind(t,80111)).then(o=>o.MboAuthenticationEnrollmentModule)},{path:"forgot-password",loadChildren:()=>t.e(1914).then(t.bind(t,1914)).then(o=>o.MboForgotPasswordModule)},{path:"errors",loadChildren:()=>t.e(300).then(t.bind(t,10300)).then(o=>o.MboAuthenticationErrorsModule),canDeactivate:[h]},{path:"signout",loadChildren:()=>t.e(8285).then(t.bind(t,8285)).then(o=>o.MboSignoutModule),canDeactivate:[(()=>{class o{canDeactivate(n,v,A,e){return e.url===s}}return o.\u0275fac=function(n){return new(n||o)},o.\u0275prov=d.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()]}];let I=(()=>{class o{}return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=d.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=d.\u0275\u0275defineInjector({imports:[M.CommonModule,u.RouterModule.forChild(m)]}),o})()}}]);