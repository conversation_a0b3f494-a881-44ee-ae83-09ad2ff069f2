(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1765],{77493:(j,p,o)=>{o.d(p,{P:()=>v,G:()=>x});var m=o(15861),u=o(87956),s=o(53113),i=o(98699),h=o(38074),N=o(29306),M=o(87903),y=o(66067);class T{constructor(L,t,n,a){this.destination=L,this.source=t,this.amount=n,this.manual=a}}function g(e){return new T(e.destination,e.source,e.amount,e.manual)}var r=o(71776),C=o(39904),A=o(42168),b=o(84757),c=o(99877);let I=(()=>{class e{constructor(t,n){this.http=t,n.subscribes(C.PU,()=>{this.loans=void 0})}request(){return this.loans?Promise.resolve(this.loans):(0,A.firstValueFrom)(this.http.get(C.bV.PAYMENTS.DEBTS.CATALOG,{params:{exclude:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,b.map)(({content:t})=>t.map(n=>function E(e){return new y.T2(e.id,e.acctType,e.acctTypeName,e.loanName,e.acctId,new N.Br(e.bankId,e.bankName),e.isAval,e.dynamo||!1,e.isOwner,e.isOwner?null:e.owner,e.isOwner?null:new s.dp((0,M.nX)(e.ownerIdType),e.ownerId))}(n))),(0,b.tap)(t=>{this.loans=t})))}send(t){return(0,A.firstValueFrom)(this.http.post(C.bV.PAYMENTS.LOAN,function P(e){return{acctIdFrom:e.source.id,acctNickNameFrom:e.source.nickname,bankIdFrom:e.source.bank.id,acctIdTo:e.destination.id,acctNameTo:e.destination.nickname,bankIdTo:e.destination.bank.id,bankNameTo:e.destination.bank.name,amt:Math.ceil(e.amount),curCode:"COP",paymentDesc:""}}(t)).pipe((0,b.map)(n=>(0,M.l1)(n,"SUCCESS")))).catch(n=>(0,M.rU)(n))}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(r.HttpClient),c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var D=o(20691);let R=(()=>{class e extends D.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,manual:!1}),this.eventBusService=t,this.eventBusService.subscribes(C.PU,()=>{this.reset()})}setDestination(t,n=!1){this.reduce(a=>({...a,destination:t,fromCustomer:n}))}getDestination(){return this.select(({destination:t})=>t)}setProduct(t){this.reduce(n=>({...n,product:t}))}getProduct(){return this.select(({product:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(n=>({...n,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount({amount:t,manual:n}){this.reduce(a=>({...a,manual:n,amount:t}))}selectForAmount(){return this.select(({amount:t,confirmation:n,destination:a,source:f})=>({amount:t,confirmation:n,destination:a,source:f}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),v=(()=>{class e{constructor(t,n,a,f){this.productService=t,this.repository=n,this.store=a,this.eventBusService=f}setDestination(t){var n=this;return(0,m.Z)(function*(){try{return(0,h.p)(t)&&(yield n.productService.requestInformation(t)),i.Either.success(n.store.setDestination(t))}catch{return i.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return i.Either.success(this.store.setSource(t))}catch({message:n}){return i.Either.failure({message:n})}}setAmount(t,n=!1){try{return i.Either.success(this.store.setAmount({amount:t,manual:n}))}catch({message:a}){return i.Either.failure({message:a})}}reset(){try{const t=this.store.itIsFromCustomer(),n=this.store.getProduct();return this.store.reset(),i.Either.success({fromCustomer:t,product:n})}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const n=g(t.store.currentState),a=yield t.execute(n);return t.eventBusService.emit(a.channel),i.Either.success({loan:n,status:a})})()}execute(t){try{return this.repository.send(t)}catch({message:n}){return Promise.resolve(s.LN.error(n))}}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(I),c.\u0275\u0275inject(R),c.\u0275\u0275inject(u.Yd))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var O=o(89148);const{DlaPayMin:d,DlaPayTotal:l,LoanPayMin:U,LoanPayTotal:V}=O.Av;let x=(()=>{class e{constructor(t,n,a,f){this.products=t,this.productService=n,this.repository=a,this.store=f}destination(){var t=this;return(0,m.Z)(function*(){try{return i.Either.success(yield t.requestLoans())}catch({message:n}){return i.Either.failure({message:n})}})()}source(t){var n=this;return(0,m.Z)(function*(){try{const a=yield n.products.requestAccountsForTransfer(),f=n.store.itIsConfirmation(),S=yield n.requestLoan(t);return i.Either.success({confirmation:f,destination:S,products:a})}catch({message:a}){return i.Either.failure({message:a})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const n=t.store.itIsConfirmation(),a=t.store.getSource(),f=t.store.getDestination(),S=yield t.productService.requestInformation(f),F=t.getMinPayment(S),B=t.getTotalPayment(S);return i.Either.success({confirmation:n,cop:{min:F,total:B},source:a})}catch({message:n}){return i.Either.failure({message:n})}})()}amount(){var t=this;return(0,m.Z)(function*(){try{const n=t.store.getDestination(),a=(0,h.p)(n)?yield t.productService.requestInformation(t.store.getDestination()):void 0,f=a&&t.getTotalPayment(a);return i.Either.success({...t.store.selectForAmount(),total:f})}catch({message:n}){return i.Either.failure({message:n})}})()}confirmation(){try{const t=g(this.store.currentState);return i.Either.success({payment:t})}catch({message:t}){return i.Either.failure({message:t})}}requestLoans(){return this.repository.request().then(t=>t.reduce((n,a)=>{const{others:f,principals:S}=n;return(a.bank.isOccidente?S:f).push(a),n},{others:[],principals:[]}))}requestLoan(t){var n=this;return(0,m.Z)(function*(){let a=n.store.getDestination();if(!a&&t){const f=yield n.products.requestProductForId(t);if(n.store.setProduct(f),f){const{principals:S}=yield n.requestLoans(),F=S.find(({number:B})=>B===f.number);F&&(yield n.productService.requestInformation(F)),a=F||f,n.store.setDestination(a,!0)}}return a})()}getMinPayment(t){return t?.getSection(U)||t?.getSection(d)}getTotalPayment(t){return t?.getSection(V)||t?.getSection(l)}}return e.\u0275fac=function(t){return new(t||e)(c.\u0275\u0275inject(u.hM),c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(I),c.\u0275\u0275inject(R))},e.\u0275prov=c.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},20225:(j,p,o)=>{o.d(p,{w:()=>M});var m=o(30263),u=o(39904),s=o(95437),i=o(77493),h=o(99877);let M=(()=>{class y{constructor(E,P,g){this.modalConfirmation=E,this.mboProvider=P,this.managerLoan=g}execute(E=!0){E?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de cr\xe9dito actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerLoan.reset().when({success:({fromCustomer:E,product:P})=>{E?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:P.id}):this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)}})}}return y.\u0275fac=function(E){return new(E||y)(h.\u0275\u0275inject(m.$e),h.\u0275\u0275inject(s.ZL),h.\u0275\u0275inject(i.P))},y.\u0275prov=h.\u0275\u0275defineInjectable({token:y,factory:y.\u0275fac,providedIn:"root"}),y})()},41765:(j,p,o)=>{o.r(p),o.d(p,{MboPaymentLoanAmountPageModule:()=>R});var m=o(17007),u=o(78007),s=o(30263),i=o(15861),h=o(24495),N=o(39904),M=o(87903),y=o(95437),T=o(57544),E=o(38074),P=o(77493),g=o(20225),r=o(99877),C=o(35641),A=o(48774),b=o(83413),c=o(45542);const I=N.Z6.PAYMENTS.LOAN;let D=(()=>{class v{constructor(d,l,U,V){this.mboProvider=d,this.requestConfiguration=l,this.managerLoan=U,this.cancelProvider=V,this.confirmation=!1,this.requesting=!0,this.backAction={id:"btn_payment-loan-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back((0,E.p)(this.destination)?I.SELECT_AMOUNT:I.SOURCE)}},this.cancelAction={id:"btn_payment-loan-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new T.FormControl}ngOnInit(){this.initializatedConfiguration()}get disabled(){return this.amountControl.invalid||this.requesting}onSubmit(){this.managerLoan.setAmount(this.amountControl.value,!0).when({success:()=>{this.mboProvider.navigation.next(I.CONFIRMATION)}})}initializatedConfiguration(){var d=this;return(0,i.Z)(function*(){(yield d.requestConfiguration.amount()).when({success:({amount:l,confirmation:U,destination:V,source:x})=>{d.destination=V,d.source=x,d.confirmation=U,l&&d.amountControl.setValue(l);const e=[h.C1,h.LU,h.PO];(0,M.VN)(x)&&e.push((0,h.vB)(x.amount)),d.amountControl.setValidators(e)}},()=>{d.requesting=!1})})()}}return v.\u0275fac=function(d){return new(d||v)(r.\u0275\u0275directiveInject(y.ZL),r.\u0275\u0275directiveInject(P.G),r.\u0275\u0275directiveInject(P.P),r.\u0275\u0275directiveInject(g.w))},v.\u0275cmp=r.\u0275\u0275defineComponent({type:v,selectors:[["mbo-payment-loan-amount-page"]],decls:13,vars:12,consts:[[1,"mbo-payment-loan-amount-page__content"],[1,"mbo-payment-loan-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-payment-loan-amount-page__body"],[1,"mbo-payment-loan-amount-page__message","subtitle2-medium"],["elementId","txt_payment-loan-amount_value","label","Valor a pagar","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount","hidden"],[3,"skeleton","hidden"],[1,"mbo-payment-loan-amount-page__footer"],["id","btn_payment-loan-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(d,l){1&d&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"p",4),r.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas pagar? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6)(8,"bocc-card-product-summary",7),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(9,"div",8)(10,"button",9),r.\u0275\u0275listener("click",function(){return l.onSubmit()}),r.\u0275\u0275elementStart(11,"span"),r.\u0275\u0275text(12,"Continuar"),r.\u0275\u0275elementEnd()()()),2&d&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",l.backAction)("rightAction",l.cancelAction),r.\u0275\u0275advance(4),r.\u0275\u0275property("formControl",l.amountControl),r.\u0275\u0275advance(1),r.\u0275\u0275property("color",null==l.source?null:l.source.color)("icon",null==l.source?null:l.source.logo)("title",null==l.source?null:l.source.nickname)("number",null==l.source?null:l.source.shortNumber)("amount",null==l.source?null:l.source.amount)("hidden",l.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("skeleton",!0)("hidden",!l.requesting),r.\u0275\u0275advance(2),r.\u0275\u0275property("disabled",l.disabled))},dependencies:[C.d,A.J,b.D,c.P],styles:["/*!\n * MBO PaymentLoanAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 27/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-payment-loan-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-loan-amount-page .mbo-payment-loan-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-payment-loan-amount-page .mbo-payment-loan-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-payment-loan-amount-page .mbo-payment-loan-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-payment-loan-amount-page .mbo-payment-loan-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-loan-amount-page .mbo-payment-loan-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-payment-loan-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20)}}\n"],encapsulation:2}),v})(),R=(()=>{class v{}return v.\u0275fac=function(d){return new(d||v)},v.\u0275mod=r.\u0275\u0275defineNgModule({type:v}),v.\u0275inj=r.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:D}]),s.dH,s.Jx,s.D1,s.P8]}),v})()},63674:(j,p,o)=>{o.d(p,{Eg:()=>y,Lo:()=>i,Wl:()=>h,ZC:()=>N,_f:()=>u,br:()=>M,tl:()=>s});var m=o(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},s=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),i={color:"success",key:"paid",label:"Pagada"},h={color:"alert",key:"pending",label:"Por pagar"},N={color:"danger",key:"expired",label:"Vencida"},M={color:"info",key:"recurring",label:"Pago recurrente"},y={color:"info",key:"programmed",label:"Programado"}},66067:(j,p,o)=>{o.d(p,{S6:()=>T,T2:()=>M,UQ:()=>E,mZ:()=>y});var m=o(39904),u=o(6472),i=o(63674),h=o(31707);class M{constructor(g,r,C,A,b,c,I,D,R,v,O){this.id=g,this.type=r,this.name=C,this.nickname=A,this.number=b,this.bank=c,this.isAval=I,this.isProtected=D,this.isOwner=R,this.ownerName=v,this.ownerDocument=O,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,u.initials)(A),this.shortNumber=b.substring(b.length-4),this.descriptionNumber=`${C} ${b}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(g){this.informationValue||(this.informationValue=g)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(g){return this.currenciesValue.includes(g)}}class y{constructor(g,r){this.id=g,this.type=r}}class T{constructor(g,r,C,A,b,c,I,D,R,v,O,d){this.uuid=g,this.number=r,this.nie=C,this.nickname=A,this.companyId=b,this.companyName=c,this.amount=I,this.registerDate=D,this.expirationDate=R,this.paid=v,this.statusCode=O,this.references=d,this.recurring=d.length>0,this.status=function N(P){switch(P){case h.U.EXPIRED:return i.ZC;case h.U.PENDING:return i.Wl;case h.U.PROGRAMMED:return i.Eg;case h.U.RECURRING:return i.br;default:return i.Lo}}(O)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class E{constructor(g,r,C,A,b,c,I,D){this.uuid=g,this.number=r,this.nickname=C,this.companyId=A,this.companyName=b,this.city=c,this.amount=I,this.isBiller=D}}},38074:(j,p,o)=>{o.d(p,{p:()=>u});var m=o(29306);function u(s){return s instanceof m.xs||s.isRequiredInformation}},31707:(j,p,o)=>{o.d(p,{U:()=>m,f:()=>u});var m=(()=>{return(s=m||(m={})).RECURRING="1",s.EXPIRED="2",s.PENDING="3",s.PROGRAMMED="4",m;var s})(),u=(()=>{return(s=u||(u={})).BILLER="Servicio",s.NON_BILLER="Servicio",s.PSE="Servicio",s.TAX="Impuesto",s.LOAN="Obligaci\xf3n financiera",s.CREDIT_CARD="Obligaci\xf3n financiera",u;var s})()}}]);