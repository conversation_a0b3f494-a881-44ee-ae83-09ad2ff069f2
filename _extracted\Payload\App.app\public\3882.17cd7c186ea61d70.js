(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3882],{53644:(E,y,t)=>{t.d(y,{MK:()=>b,NM:()=>h,Rm:()=>d,VS:()=>i,ay:()=>f});var v=t(90806);class f{constructor(c){this.tagAval=c}}class h{constructor(c,o){this.subtitle=c,this.title=o}}class i{constructor(c,o,n){this.fullName=c,this.documentType=o,this.documentNumber=n,this.maskName=(0,v.Z)(c)}}class d{constructor(c,o,n,a,s,A){this.keyType=c,this.tagAval=o,this.accountReceptor=n,this.type=a,this.bank=s,this.customer=A}get customerMaskName(){return this.customer.maskName}}class b{constructor(c,o,n,a,s,A,e){this.source=c,this.account=o,this.contact=n,this.customerName=a,this.ipAddress=s,this.amount=A,this.note=e}}},30786:(E,y,t)=>{t.d(y,{$:()=>b,Ry:()=>c,iK:()=>p});var v=t(29306),f=t(64892),h=t(87903),i=t(53644);const d={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function b(o){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:n,CustIdentType:a,GovIssueIdent:s,PersonName:{FirstName:A}},DepAcctId:{AcctId:e,AcctType:I,BankInfo:T}},RefInfo:x}=o,r=s?.GovIssueIdentType||a,u=s?.IdentSerialNum||n,{RefId:m,RefType:_}=x[0];return new i.Rm(_,m,e,I,new v.Br(T.BankId,T.Name,T.BankId===f.qE.Occidente),new i.VS(A,(0,h.nX)(d[r]),u))}function p(o){return{fromDepAcctId:o.source.id,fromDepAcctName:o.source.name,fromDepAcctType:o.source.type,fromNickName:o.source.nickname,toDepAcctBankId:o.account.bank.id,toDepAcctType:o.account.type,toDepAcctId:o.account.accountReceptor,toDepAcctName:o.account.customer.fullName,toNickName:"",toUserIdNumber:o.account.customer.documentNumber,toUserIdType:o.account.customer.documentType.code,keyInfo:{key:o.account.tagAval,type:o.account.keyType},personInfoTo:{fullName:o.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:o.account.customer.documentType.code,identSerialNum:o.account.customer.documentNumber}},personInfoFrom:{firstName:o.customerName.clientFirstName,lastName:o.customerName.clientLastName,legalName:`${o.customerName.clientFirstName} ${o.customerName.clientLastName}`},curAmt:o.amount.toString(),refId:o.note?.reference||"",memo:o.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function c(o){return new i.MK(o.source,o.account,new i.NM(o.destination.tagAval,o.account.customer.fullName),o.customerName,o.ipAddress,o.amount,o.note)}},90806:(E,y,t)=>{t.d(y,{D:()=>d,Z:()=>b});var v=t(87903),f=t(53113);function h(p){const{isError:c,message:o}=p;return{animation:(0,v.jY)(p),title:c?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:o}}function i({isError:p}){return p?[(0,v.wT)("Finalizar","finish","outline"),(0,v.wT)("Volver a intentar","retry")]:[(0,v.wT)("Hacer otra transferencia","retry","outline"),(0,v.wT)("Finalizar","finish")]}function d(p){const{dateFormat:c,timeFormat:o}=new f.ou,{status:n,tagAval:a}=p,s=[(0,v.SP)("DESTINO",a.account.customer.maskName,a.account.tagAval,a.account.bank.name),(0,v._f)("SUMA DE",a.amount)];return a.note&&s.push((0,v.SP)("DESCRIPCI\xd3N",a.note.description,"",a.note.reference)),s.push((0,v.cZ)(c,o)),{actions:i(n),error:n.isError,header:h(n),informations:s,skeleton:!1}}function b(p){const c=p.split(" "),[o]=c,n=c[c.length-1],a=n.length,A=a>3?3:2;return`${o.substring(0,o.length>4?4:2)}*****${n.substring(a-A,a)}`}},90596:(E,y,t)=>{t.d(y,{$:()=>v.$,N:()=>c});var v=t(50142),f=t(15861),h=t(87956),i=t(98699),d=t(30786),b=t(23604),p=t(99877);let c=(()=>{class o{constructor(a,s){this.store=a,this.products=s}source(){var a=this;return(0,f.Z)(function*(){try{const s=yield a.products.requestAccountsForTransfer(),A=a.store.itIsConfirmation();return i.Either.success({confirmation:A,products:s})}catch({message:s}){return i.Either.failure({message:s})}})()}destination(){var a=this;return(0,f.Z)(function*(){try{const s=yield a.products.requestAccountsForTransfer(),A=a.store.itIsConfirmation();return i.Either.success({confirmation:A,hasOneSource:s.length<2,destination:a.store.getTagAval()})}catch({message:s}){return i.Either.failure({message:s})}})()}amount(){try{const a=this.store.itIsConfirmation(),s=this.store.getSource(),A=this.store.getAmount(),e=this.store.getAccount();return i.Either.success({account:e,amount:A,confirmation:a,source:s})}catch({message:a}){return i.Either.failure({message:a})}}confirmation(){try{const a=(0,d.Ry)(this.store.currentState);return i.Either.success({transfer:a})}catch({message:a}){return i.Either.failure({message:a})}}}return o.\u0275fac=function(a){return new(a||o)(p.\u0275\u0275inject(b.B),p.\u0275\u0275inject(h.hM))},o.\u0275prov=p.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},50142:(E,y,t)=>{t.d(y,{$:()=>I});var v=t(15861),f=t(87956),h=t(53113),i=t(98699),d=t(30786),b=t(71776),p=t(39904),c=t(87903),o=t(42168),n=t(84757),a=t(99877);let s=(()=>{class T{constructor(r){this.http=r}requestVerifyAccount(r){var u=this;return(0,v.Z)(function*(){return(0,o.firstValueFrom)(u.http.post(p.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:r},pilot:!0}).pipe((0,n.map)(m=>m.map(_=>(0,d.$)(_))),(0,n.catchError)(m=>{if("206"===m.error?.MsgRsHdr?.Status?.StatusCode)return(0,o.of)([]);throw m})))})()}send(r){return(0,o.firstValueFrom)(this.http.post(p.bV.TRANSFERS.TAG_AVAL,(0,d.iK)(r),{headers:{"X-Customer-Ip":r.ipAddress}}).pipe((0,n.map)(u=>(0,c.l1)(u,"SUCCESS")))).catch(u=>(0,c.rU)(u))}}return T.\u0275fac=function(r){return new(r||T)(a.\u0275\u0275inject(b.HttpClient))},T.\u0275prov=a.\u0275\u0275defineInjectable({token:T,factory:T.\u0275fac,providedIn:"root"}),T})();var A=t(23604),e=t(74520);let I=(()=>{class T{constructor(r,u,m,_){this.repository=r,this.store=u,this.eventBusService=m,this.customerStore=_}setSource(r,u=!1){try{return i.Either.success(this.store.setSource(r,u))}catch({message:m}){return i.Either.failure({message:m})}}verfiyAccount(r){var u=this;return(0,v.Z)(function*(){try{const m=yield u.requestAccount(r);return u.store.setTagAval(r),i.Either.success(!!m)}catch({message:m}){return i.Either.failure({message:m})}})()}setAmount(r){try{return i.Either.success(this.store.setAmount(r))}catch({message:u}){return i.Either.failure({message:u})}}reset(){try{const r=this.store.itIsFromCustomer(),u=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:r,source:u})}catch({message:r}){return i.Either.failure({message:r})}}send(){var r=this;return(0,v.Z)(function*(){const u=r.customerStore.currentState,{session:{ip:m,customer:{clientFirstName:_,clientLastName:l}}}=u;r.store.setIpAddress(m),r.store.setCustomerName({clientFirstName:_,clientLastName:l});const g=(0,d.Ry)(r.store.currentState),C=yield r.execute(g);return r.eventBusService.emit(C.channel),i.Either.success({tagAval:g,status:C})})()}execute(r){try{return this.repository.send(r)}catch({message:u}){return Promise.resolve(h.LN.error(u))}}requestAccount(r){var u=this;return(0,v.Z)(function*(){const m=u.store.getTagAval();let _=u.store.getAccount();const{tagAval:l}=r;return(m?.tagAval!==l||!_)&&([_]=yield u.repository.requestVerifyAccount(l),u.store.setAccount(_)),_})()}setNote(r){try{return i.Either.success(this.store.setNote(r))}catch({message:u}){return i.Either.failure({message:u})}}removeNote(){try{return i.Either.success(this.store.removeNote())}catch({message:r}){return i.Either.failure({message:r})}}}return T.\u0275fac=function(r){return new(r||T)(a.\u0275\u0275inject(s),a.\u0275\u0275inject(A.B),a.\u0275\u0275inject(f.Yd),a.\u0275\u0275inject(e.f))},T.\u0275prov=a.\u0275\u0275defineInjectable({token:T,factory:T.\u0275fac,providedIn:"root"}),T})()},32435:(E,y,t)=>{t.d(y,{Z:()=>c});var v=t(30263),f=t(39904),h=t(95437),i=t(90596),d=t(99877);const p=f.Z6.TRANSFERS.GENERIC;let c=(()=>{class o{constructor(a,s,A){this.modalConfirmation=a,this.mboProvider=s,this.managerTagAval=A}execute(a=!0){a?this.confirmation():this.cancelConfirmed(!0)}backCustomer(a=!0){a?this.mboProvider.navigation.back(f.Z6.TRANSFERS.TAG_AVAL.SOURCE):this.backConfirmed(!0)}confirmation(a=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre Tags Aval actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(a)}},decline:{label:"Continuar"}}).then(s=>"accept"===s)}cancelConfirmed(a){const s=this.managerTagAval.reset();a&&s.when({success:({fromCustomer:A,source:e})=>{A?this.mboProvider.navigation.back(f.Z6.CUSTOMER.PRODUCTS.INFO,{productId:e.id}):this.mboProvider.navigation.back(f.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(f.Z6.TRANSFERS.HOME)}})}backConfirmed(a){const s=this.managerTagAval.reset();a&&s.when({success:({fromCustomer:A,source:e})=>{A?this.mboProvider.navigation.back(p.DESTINATION,{productId:e.id}):this.mboProvider.navigation.back(f.Z6.TRANSFERS.TAG_AVAL.SOURCE)},failure:()=>{this.mboProvider.navigation.back(f.Z6.TRANSFERS.HOME)}})}}return o.\u0275fac=function(a){return new(a||o)(d.\u0275\u0275inject(v.$e),d.\u0275\u0275inject(h.ZL),d.\u0275\u0275inject(i.$))},o.\u0275prov=d.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},23604:(E,y,t)=>{t.d(y,{B:()=>p});var v=t(39904),f=t(87956),h=t(20691),d=t(99877);let p=(()=>{class c extends h.Store{constructor(n){super({confirmation:!1,fromCustomer:!1}),n.subscribes(v.PU,()=>{this.reset()})}setIpAddress(n){this.reduce(a=>({...a,ipAddress:n}))}setCustomerName(n){this.reduce(a=>({...a,customerName:n}))}itIsFromCustomer(){return this.select(({fromCustomer:n})=>n)}setSource(n,a=!1){this.reduce(s=>({...s,source:n,fromCustomer:a}))}getSource(){return this.select(({source:n})=>n)}setTagAval(n){this.reduce(a=>({...a,destination:n}))}getTagAval(){return this.select(({destination:n})=>n)}setAccount(n){this.reduce(a=>({...a,account:n}))}getAccount(){return this.select(({account:n})=>n)}setAmount(n){this.reduce(a=>({...a,amount:n,confirmation:!0}))}getAmount(){return this.select(({amount:n})=>n)}itIsConfirmation(){return this.select(({confirmation:n})=>n)}setNote(n){this.reduce(a=>({...a,note:n}))}removeNote(){this.reduce(n=>({...n,note:void 0}))}}return c.\u0275fac=function(n){return new(n||c)(d.\u0275\u0275inject(f.Yd))},c.\u0275prov=d.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},43882:(E,y,t)=>{t.r(y),t.d(y,{MboTransferTagAvalDestinationPage:()=>u});var v=t(15861),f=t(17007),h=t(30263),i=t(38198),d=t(24495),b=t(95437),p=t(57544),c=t(52484),o=t(53644),n=t(90596),a=t(32435),s=t(39904),A=t(23604),e=t(99877);function I(m,_){1&m&&e.\u0275\u0275element(0,"img",17)}function T(m,_){1&m&&e.\u0275\u0275element(0,"img",18)}const x=s.Z6.TRANSFERS.TAG_AVAL;let r=(()=>{class m{constructor(l,g){this.store=l,this.mboProvider=g}ngOnInit(){this.account=this.store.getAccount()}ngBoccPortal(l){this.portal=l}get isAval(){return!!this.account?.bank.isAval}onSubmit(){var l=this;return(0,v.Z)(function*(){l.portal?.close(),l.mboProvider.navigation.next(x.AMOUNT)})()}onCancel(){this.portal?.close()}}return m.\u0275fac=function(l){return new(l||m)(e.\u0275\u0275directiveInject(A.B),e.\u0275\u0275directiveInject(b.ZL))},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-tag-aval-verify-modal"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:26,vars:7,consts:[[1,"mbo-tag-aval-verify-modal__body"],[1,"mbo-tag-aval-verify-modal__body__logo"],["src","assets/transfers/logos/tag-aval-verify.svg",4,"ngIf"],["src","assets/transfers/logos/tag-key-verify.svg",4,"ngIf"],[1,"mbo-tag-aval-verify-modal__title","smalltext-medium"],[1,"mbo-tag-aval-verify-modal__description","overline-semibold"],[1,"mbo-tag-aval-verify-modal__box-receptor"],[1,"mbo-tag-aval-verify-modal__box-receptor__img"],[3,"src"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--name","overline-default"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--bank-name","overline-default"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container"],[1,"overline-semibold"],[1,"mbo-tag-aval-verify-modal__footer"],["id","btn_tag-aval-verify-modal_submit","bocc-button","raised","suffixIcon","success",3,"click"],["id","btn_tag-aval-verify-modal_cancel","bocc-button","outline",3,"click"],["src","assets/transfers/logos/tag-aval-verify.svg"],["src","assets/transfers/logos/tag-key-verify.svg"]],template:function(l,g){1&l&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,I,1,0,"img",2),e.\u0275\u0275template(3,T,1,0,"img",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"label",4),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"label",5),e.\u0275\u0275text(7," DESTINO "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",6)(9,"div",7),e.\u0275\u0275element(10,"img",8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",9)(12,"label",10),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"label",11),e.\u0275\u0275text(15),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"div",12)(17,"span",13),e.\u0275\u0275text(18),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",14)(20,"button",15),e.\u0275\u0275listener("click",function(){return g.onSubmit()}),e.\u0275\u0275elementStart(21,"span"),e.\u0275\u0275text(22,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"button",16),e.\u0275\u0275listener("click",function(){return g.onCancel()}),e.\u0275\u0275elementStart(24,"span"),e.\u0275\u0275text(25,"Corregir"),e.\u0275\u0275elementEnd()()()),2&l&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",g.isAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!g.isAval),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",g.isAval?"ESTE TAG AVAL PERTENECE A":"ESTA LLAVE PERTENECE A"," "),e.\u0275\u0275advance(5),e.\u0275\u0275property("src",null==g.account?null:g.account.bank.logo,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==g.account?null:g.account.customerMaskName," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",null==g.account?null:g.account.bank.name," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==g.account?null:g.account.tagAval))},dependencies:[f.CommonModule,f.NgIf,h.P8],styles:["mbo-tag-aval-verify-modal{position:relative;display:flex;flex-direction:column;min-width:140rem;box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__body__logo{text-align:center}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__title{position:relative;width:100%;text-align:center;margin:var(--sizing-x6) 0rem;color:var(--color-carbon-darker-1000)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__img{width:18rem;border-radius:var(--sizing-x4);padding:var(--sizing-x1);box-sizing:border-box;border:var(--border-1-lighter-300)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__img img{width:100%}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor{display:flex;flex-direction:column;width:calc(100% - 22rem);row-gap:var(--sizing-x2)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--bank-name{color:var(--color-carbon-lighter-700);width:100%}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden;white-space:nowrap}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container>span{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key{color:var(--color-carbon-lighter-700)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__description{color:var(--color-carbon-lighter-400)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x8);box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__footer button{width:100%}\n"],encapsulation:2}),m})(),u=(()=>{class m{constructor(l,g,C,P,M){this.mboProvider=l,this.requestConfiguration=g,this.managerTagAval=C,this.cancelProvider=P,this.modalService=M,this.confirmation=!1,this.backInvalid=!1,this.requesting=!0,this.requestingContact=!1,this.backAction={id:"btn_transfer-tag-aval-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting||this.confirmation||this.backInvalid,click:()=>{this.cancelProvider.backCustomer(this.confirmation)}},this.cancelAction={id:"btn_transfer-tag-aval-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}},this.tagAvalControl=new p.FormControl("",[d.C1,(0,d.ec)(5,i.QM),c.b])}ngOnInit(){this.initializatedConfiguration()}get accountInvalid(){return!this.destination}get disabled(){return this.destination?this.accountInvalid:this.tagAvalControl.invalid}onSubmit(){var l=this;return(0,v.Z)(function*(){l.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield l.managerTagAval.verfiyAccount(l.getTagAval())).when({success:g=>{g?l.modalService.create(r).open():l.mboProvider.toast.warning("El Tag Aval que ingresaste no es v\xe1lido, recuerda que no es un n\xfamero de cuenta.","Verifica el Tag ingresado")},failure:()=>{l.mboProvider.toast.error("Ocurri\xf3 un error al tratar de consultar la cuenta relacionada al Tag Aval")}},()=>{l.mboProvider.loader.close()})})()}initializatedConfiguration(){var l=this;return(0,v.Z)(function*(){(yield l.requestConfiguration.destination()).when({success:({confirmation:g,hasOneSource:C,destination:P})=>{l.backInvalid=C,l.destination=P,l.confirmation=g,l.tagAvalControl.setValue(P?.tagAval)}},()=>{l.requesting=!1})})()}getTagAval(){return new o.ay(this.tagAvalControl.value)}}return m.\u0275fac=function(l){return new(l||m)(e.\u0275\u0275directiveInject(b.ZL),e.\u0275\u0275directiveInject(n.N),e.\u0275\u0275directiveInject(n.$),e.\u0275\u0275directiveInject(a.Z),e.\u0275\u0275directiveInject(h.iM))},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-transfer-tag-aval-destination-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:23,vars:5,consts:[[1,"mbo-transfer-tag-aval-destination-page__content"],[1,"mbo-transfer-tag-aval-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfer-tag-aval-destination-page__body"],[1,"mbo-transfer-tag-aval-destination-page__selection"],[1,"subtitle2-medium"],["id","txt_transfer-tag-aval-destination_key","type","keyAval","label","Ingresa la llave de destino","placeholder","Ej. @ABCDE123",3,"formControl"],[1,"mbo-transfer-tag-aval-destination-page__messages"],[1,"mbo-transfer-tag-aval-destination-page__message"],["icon","information"],[1,"caption-regular"],["icon","bell",3,"visible"],[1,"mbo-transfer-tag-aval-destination-page__footer"],["id","btn_transfer-tag-aval-destination_submit","bocc-button","raised",3,"disabled","click"]],template:function(l,g){1&l&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"p",5),e.\u0275\u0275text(6,"\xbfA qui\xe9n deseas transferirle dinero hoy?"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(7,"bocc-growing-box",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"div",8),e.\u0275\u0275element(10,"bocc-icon",9),e.\u0275\u0275elementStart(11,"label",10),e.\u0275\u0275text(12," Puedes ingresar la llaves tipo Tag Aval, C\xe9dula, Correo, Celular o Llave alfanum\xe9rica. "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(13,"div",8),e.\u0275\u0275element(14,"bocc-icon",9),e.\u0275\u0275elementStart(15,"label",10),e.\u0275\u0275text(16," Inicia con @ si la Llave es un Tag Aval o alfanum\xe9rica. "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"bocc-alert",11),e.\u0275\u0275text(18," Podr\xe1s confirmar el titular y entidad de la llave en el pr\xf3ximo paso. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(19,"div",12)(20,"button",13),e.\u0275\u0275listener("click",function(){return g.onSubmit()}),e.\u0275\u0275elementStart(21,"span"),e.\u0275\u0275text(22,"Continuar"),e.\u0275\u0275elementEnd()()()),2&l&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",g.backAction)("rightAction",g.cancelAction),e.\u0275\u0275advance(5),e.\u0275\u0275property("formControl",g.tagAvalControl),e.\u0275\u0275advance(10),e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",g.disabled))},dependencies:[f.CommonModule,h.Zl,h.Jx,h.dH,h.P8,h.B4],styles:["/*!\n * MBO TransferTagAvalDestination Page\n * v2.1.4\n * Author: MB Frontend Developers\n * Created: 13/Aug/2024\n * Updated: 23/Ene/2025\n*/mbo-transfer-tag-aval-destination-page{--bocc-growing-field-font-size: var(--body1-size);--bocc-growing-field-letter-spacing: var(--body1-letter-spacing);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x24);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__selection{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__selection p{text-align:center}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__messages{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__message{--bocc-icon-dimension: var(--sizing-x8);display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__message bocc-icon{color:var(--color-blue-700)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__message label{color:var(--color-blue-700)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__error{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__error bocc-icon{font-size:var(--sizing-x8);width:var(--sizing-x8);height:var(--sizing-x8);color:var(--color-semantic-danger-700)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__error span{color:var(--color-carbon-lighter-700)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__message{color:var(--color-carbon-darker-1000)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__info{position:relative;color:var(--color-carbon-darker-1000)}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-tag-aval-destination-page .mbo-transfer-tag-aval-destination-page__footer button{width:100%}\n"],encapsulation:2}),m})()},20827:(E,y,t)=>{function f(d){return"3"===d.charAt(0)}t.d(y,{lE:()=>f}),t(87903)},52484:(E,y,t)=>{t.d(y,{O:()=>i,b:()=>d});var v=t(20827);const f=/^[a-z|A-Z|0-9|@|.]*$/,h=/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,i=b=>b&&!(0,v.lE)(b)?{id:"firstCharPhone",message:"Campo debe iniciar con n\xfamero 3"}:null,d=b=>!b||h.test(b)||f.test(b)?null:{id:"tagAvalOrKey",message:"Campo no cumple con los criterios para ser una llave"}}}]);