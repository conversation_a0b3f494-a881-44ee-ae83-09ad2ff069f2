(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7581],{53644:(C,E,e)=>{e.d(E,{MK:()=>p,NM:()=>_,Rm:()=>v,VS:()=>s,ay:()=>d});var m=e(90806);class d{constructor(i){this.tagAval=i}}class _{constructor(i,o){this.subtitle=i,this.title=o}}class s{constructor(i,o,a){this.fullName=i,this.documentType=o,this.documentNumber=a,this.maskName=(0,m.Z)(i)}}class v{constructor(i,o,a,t,c,g){this.keyType=i,this.tagAval=o,this.accountReceptor=a,this.type=t,this.bank=c,this.customer=g}get customerMaskName(){return this.customer.maskName}}class p{constructor(i,o,a,t,c,g,n){this.source=i,this.account=o,this.contact=a,this.customerName=t,this.ipAddress=c,this.amount=g,this.note=n}}},30786:(C,E,e)=>{e.d(E,{$:()=>p,Ry:()=>i,iK:()=>l});var m=e(29306),d=e(64892),_=e(87903),s=e(53644);const v={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function p(o){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:a,CustIdentType:t,GovIssueIdent:c,PersonName:{FirstName:g}},DepAcctId:{AcctId:n,AcctType:b,BankInfo:f}},RefInfo:M}=o,r=c?.GovIssueIdentType||t,u=c?.IdentSerialNum||a,{RefId:h,RefType:T}=M[0];return new s.Rm(T,h,n,b,new m.Br(f.BankId,f.Name,f.BankId===d.qE.Occidente),new s.VS(g,(0,_.nX)(v[r]),u))}function l(o){return{fromDepAcctId:o.source.id,fromDepAcctName:o.source.name,fromDepAcctType:o.source.type,fromNickName:o.source.nickname,toDepAcctBankId:o.account.bank.id,toDepAcctType:o.account.type,toDepAcctId:o.account.accountReceptor,toDepAcctName:o.account.customer.fullName,toNickName:"",toUserIdNumber:o.account.customer.documentNumber,toUserIdType:o.account.customer.documentType.code,keyInfo:{key:o.account.tagAval,type:o.account.keyType},personInfoTo:{fullName:o.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:o.account.customer.documentType.code,identSerialNum:o.account.customer.documentNumber}},personInfoFrom:{firstName:o.customerName.clientFirstName,lastName:o.customerName.clientLastName,legalName:`${o.customerName.clientFirstName} ${o.customerName.clientLastName}`},curAmt:o.amount.toString(),refId:o.note?.reference||"",memo:o.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function i(o){return new s.MK(o.source,o.account,new s.NM(o.destination.tagAval,o.account.customer.fullName),o.customerName,o.ipAddress,o.amount,o.note)}},90806:(C,E,e)=>{e.d(E,{D:()=>v,Z:()=>p});var m=e(87903),d=e(53113);function _(l){const{isError:i,message:o}=l;return{animation:(0,m.jY)(l),title:i?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:o}}function s({isError:l}){return l?[(0,m.wT)("Finalizar","finish","outline"),(0,m.wT)("Volver a intentar","retry")]:[(0,m.wT)("Hacer otra transferencia","retry","outline"),(0,m.wT)("Finalizar","finish")]}function v(l){const{dateFormat:i,timeFormat:o}=new d.ou,{status:a,tagAval:t}=l,c=[(0,m.SP)("DESTINO",t.account.customer.maskName,t.account.tagAval,t.account.bank.name),(0,m._f)("SUMA DE",t.amount)];return t.note&&c.push((0,m.SP)("DESCRIPCI\xd3N",t.note.description,"",t.note.reference)),c.push((0,m.cZ)(i,o)),{actions:s(a),error:a.isError,header:_(a),informations:c,skeleton:!1}}function p(l){const i=l.split(" "),[o]=i,a=i[i.length-1],t=a.length,g=t>3?3:2;return`${o.substring(0,o.length>4?4:2)}*****${a.substring(t-g,t)}`}},90596:(C,E,e)=>{e.d(E,{$:()=>m.$,N:()=>i});var m=e(50142),d=e(15861),_=e(87956),s=e(98699),v=e(30786),p=e(23604),l=e(99877);let i=(()=>{class o{constructor(t,c){this.store=t,this.products=c}source(){var t=this;return(0,d.Z)(function*(){try{const c=yield t.products.requestAccountsForTransfer(),g=t.store.itIsConfirmation();return s.Either.success({confirmation:g,products:c})}catch({message:c}){return s.Either.failure({message:c})}})()}destination(){var t=this;return(0,d.Z)(function*(){try{const c=yield t.products.requestAccountsForTransfer(),g=t.store.itIsConfirmation();return s.Either.success({confirmation:g,hasOneSource:c.length<2,destination:t.store.getTagAval()})}catch({message:c}){return s.Either.failure({message:c})}})()}amount(){try{const t=this.store.itIsConfirmation(),c=this.store.getSource(),g=this.store.getAmount(),n=this.store.getAccount();return s.Either.success({account:n,amount:g,confirmation:t,source:c})}catch({message:t}){return s.Either.failure({message:t})}}confirmation(){try{const t=(0,v.Ry)(this.store.currentState);return s.Either.success({transfer:t})}catch({message:t}){return s.Either.failure({message:t})}}}return o.\u0275fac=function(t){return new(t||o)(l.\u0275\u0275inject(p.B),l.\u0275\u0275inject(_.hM))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},50142:(C,E,e)=>{e.d(E,{$:()=>b});var m=e(15861),d=e(87956),_=e(53113),s=e(98699),v=e(30786),p=e(71776),l=e(39904),i=e(87903),o=e(42168),a=e(84757),t=e(99877);let c=(()=>{class f{constructor(r){this.http=r}requestVerifyAccount(r){var u=this;return(0,m.Z)(function*(){return(0,o.firstValueFrom)(u.http.post(l.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:r},pilot:!0}).pipe((0,a.map)(h=>h.map(T=>(0,v.$)(T))),(0,a.catchError)(h=>{if("206"===h.error?.MsgRsHdr?.Status?.StatusCode)return(0,o.of)([]);throw h})))})()}send(r){return(0,o.firstValueFrom)(this.http.post(l.bV.TRANSFERS.TAG_AVAL,(0,v.iK)(r),{headers:{"X-Customer-Ip":r.ipAddress}}).pipe((0,a.map)(u=>(0,i.l1)(u,"SUCCESS")))).catch(u=>(0,i.rU)(u))}}return f.\u0275fac=function(r){return new(r||f)(t.\u0275\u0275inject(p.HttpClient))},f.\u0275prov=t.\u0275\u0275defineInjectable({token:f,factory:f.\u0275fac,providedIn:"root"}),f})();var g=e(23604),n=e(74520);let b=(()=>{class f{constructor(r,u,h,T){this.repository=r,this.store=u,this.eventBusService=h,this.customerStore=T}setSource(r,u=!1){try{return s.Either.success(this.store.setSource(r,u))}catch({message:h}){return s.Either.failure({message:h})}}verfiyAccount(r){var u=this;return(0,m.Z)(function*(){try{const h=yield u.requestAccount(r);return u.store.setTagAval(r),s.Either.success(!!h)}catch({message:h}){return s.Either.failure({message:h})}})()}setAmount(r){try{return s.Either.success(this.store.setAmount(r))}catch({message:u}){return s.Either.failure({message:u})}}reset(){try{const r=this.store.itIsFromCustomer(),u=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:r,source:u})}catch({message:r}){return s.Either.failure({message:r})}}send(){var r=this;return(0,m.Z)(function*(){const u=r.customerStore.currentState,{session:{ip:h,customer:{clientFirstName:T,clientLastName:P}}}=u;r.store.setIpAddress(h),r.store.setCustomerName({clientFirstName:T,clientLastName:P});const I=(0,v.Ry)(r.store.currentState),A=yield r.execute(I);return r.eventBusService.emit(A.channel),s.Either.success({tagAval:I,status:A})})()}execute(r){try{return this.repository.send(r)}catch({message:u}){return Promise.resolve(_.LN.error(u))}}requestAccount(r){var u=this;return(0,m.Z)(function*(){const h=u.store.getTagAval();let T=u.store.getAccount();const{tagAval:P}=r;return(h?.tagAval!==P||!T)&&([T]=yield u.repository.requestVerifyAccount(P),u.store.setAccount(T)),T})()}setNote(r){try{return s.Either.success(this.store.setNote(r))}catch({message:u}){return s.Either.failure({message:u})}}removeNote(){try{return s.Either.success(this.store.removeNote())}catch({message:r}){return s.Either.failure({message:r})}}}return f.\u0275fac=function(r){return new(r||f)(t.\u0275\u0275inject(c),t.\u0275\u0275inject(g.B),t.\u0275\u0275inject(d.Yd),t.\u0275\u0275inject(n.f))},f.\u0275prov=t.\u0275\u0275defineInjectable({token:f,factory:f.\u0275fac,providedIn:"root"}),f})()},32435:(C,E,e)=>{e.d(E,{Z:()=>i});var m=e(30263),d=e(39904),_=e(95437),s=e(90596),v=e(99877);const l=d.Z6.TRANSFERS.GENERIC;let i=(()=>{class o{constructor(t,c,g){this.modalConfirmation=t,this.mboProvider=c,this.managerTagAval=g}execute(t=!0){t?this.confirmation():this.cancelConfirmed(!0)}backCustomer(t=!0){t?this.mboProvider.navigation.back(d.Z6.TRANSFERS.TAG_AVAL.SOURCE):this.backConfirmed(!0)}confirmation(t=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre Tags Aval actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(t)}},decline:{label:"Continuar"}}).then(c=>"accept"===c)}cancelConfirmed(t){const c=this.managerTagAval.reset();t&&c.when({success:({fromCustomer:g,source:n})=>{g?this.mboProvider.navigation.back(d.Z6.CUSTOMER.PRODUCTS.INFO,{productId:n.id}):this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)}})}backConfirmed(t){const c=this.managerTagAval.reset();t&&c.when({success:({fromCustomer:g,source:n})=>{g?this.mboProvider.navigation.back(l.DESTINATION,{productId:n.id}):this.mboProvider.navigation.back(d.Z6.TRANSFERS.TAG_AVAL.SOURCE)},failure:()=>{this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)}})}}return o.\u0275fac=function(t){return new(t||o)(v.\u0275\u0275inject(m.$e),v.\u0275\u0275inject(_.ZL),v.\u0275\u0275inject(s.$))},o.\u0275prov=v.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},23604:(C,E,e)=>{e.d(E,{B:()=>l});var m=e(39904),d=e(87956),_=e(20691),v=e(99877);let l=(()=>{class i extends _.Store{constructor(a){super({confirmation:!1,fromCustomer:!1}),a.subscribes(m.PU,()=>{this.reset()})}setIpAddress(a){this.reduce(t=>({...t,ipAddress:a}))}setCustomerName(a){this.reduce(t=>({...t,customerName:a}))}itIsFromCustomer(){return this.select(({fromCustomer:a})=>a)}setSource(a,t=!1){this.reduce(c=>({...c,source:a,fromCustomer:t}))}getSource(){return this.select(({source:a})=>a)}setTagAval(a){this.reduce(t=>({...t,destination:a}))}getTagAval(){return this.select(({destination:a})=>a)}setAccount(a){this.reduce(t=>({...t,account:a}))}getAccount(){return this.select(({account:a})=>a)}setAmount(a){this.reduce(t=>({...t,amount:a,confirmation:!0}))}getAmount(){return this.select(({amount:a})=>a)}itIsConfirmation(){return this.select(({confirmation:a})=>a)}setNote(a){this.reduce(t=>({...t,note:a}))}removeNote(){this.reduce(a=>({...a,note:void 0}))}}return i.\u0275fac=function(a){return new(a||i)(v.\u0275\u0275inject(d.Yd))},i.\u0275prov=v.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},7581:(C,E,e)=>{e.r(E),e.d(E,{MboTransferTagAvalAmountPage:()=>h});var m=e(17007),_=e(30263),s=e(24495),v=e(39904),p=e(64892),l=e(87903),i=e(95437),o=e(57544),t=e(40914),c=e(90596),g=e(32435),n=e(99877);const{MAX_TAG_AVAL:f,MIN_TAG_AVAL:M,MIN_DALE:r}=t.R,u=v.Z6.TRANSFERS.TAG_AVAL;let h=(()=>{class T{constructor(I,A,N,D){this.mboProvider=I,this.requestConfiguration=A,this.managerTagAval=N,this.cancelProvider=D,this.confirmation=!1,this.backAction={id:"btn_transfer-tag-aval-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(u.DESTINATION)}},this.cancelAction={id:"btn_transfer-tag-aval-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new o.FormControl({validators:[s.C1]})}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerTagAval.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(u.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({confirmation:I,account:A,amount:N,source:D})=>{this.confirmation=I,this.source=D,N&&this.amountControl.setValue(N);const R=[s.C1,s.LU,(0,s.Go)(A?.bank.id===p.qE.Dale?r:M),(0,s.VV)(f)];(0,l.VN)(D)&&R.push((0,s.vB)(D.amount)),this.amountControl.setValidators(R)}})}}return T.\u0275fac=function(I){return new(I||T)(n.\u0275\u0275directiveInject(i.ZL),n.\u0275\u0275directiveInject(c.N),n.\u0275\u0275directiveInject(c.$),n.\u0275\u0275directiveInject(g.Z))},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-transfer-tag-aval-amount-page"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:12,vars:9,consts:[[1,"mbo-transfer-tag-aval-amount-page__content"],[1,"mbo-transfer-tag-aval-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfer-tag-aval-amount-page__body"],[1,"mbo-transfer-tag-aval-amount-page__message","subtitle2-medium"],["elementId","txt_transfer-tag-aval-amount_value","label","Valor a transferir","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-transfer-tag-aval-amount-page__footer"],["id","btn_aval-tag-send-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(I,A){1&I&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"p",4),n.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas transferir? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(8,"div",7)(9,"button",8),n.\u0275\u0275listener("click",function(){return A.onSubmit()}),n.\u0275\u0275elementStart(10,"span"),n.\u0275\u0275text(11,"Continuar"),n.\u0275\u0275elementEnd()()()),2&I&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",A.backAction)("rightAction",A.cancelAction),n.\u0275\u0275advance(4),n.\u0275\u0275property("formControl",A.amountControl),n.\u0275\u0275advance(1),n.\u0275\u0275property("color",null==A.source?null:A.source.color)("icon",null==A.source?null:A.source.logo)("title",null==A.source?null:A.source.nickname)("number",null==A.source?null:A.source.publicNumber)("amount",null==A.source?null:A.source.amount),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",A.amountControl.invalid))},dependencies:[m.CommonModule,_.Jx,_.D1,_.dH,_.P8],styles:["/*!\n * MBO TransferTagAvalAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 13/Aug/2024\n * Updated: 13/Aug/2024\n*/mbo-transfer-tag-aval-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);--pvt-checkbox-margin-top: var(--sizing-x20);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__body bocc-checkbox-label{margin-top:var(--pvt-checkbox-margin-top)}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-tag-aval-amount-page .mbo-transfer-tag-aval-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfer-tag-aval-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20);--pvt-checkbox-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),T})()},40914:(C,E,e)=>{e.d(E,{R:()=>m,r:()=>d});const m={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},d={MIN_TRUSTFUND:2e5}}}]);