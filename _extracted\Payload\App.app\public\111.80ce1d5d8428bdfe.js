(self.webpackChunkapp=self.webpackChunkapp||[]).push([[111],{80111:(s,l,n)=>{n.r(l),n.d(l,{MboAuthenticationEnrollmentModule:()=>E});var d=n(17007),h=n(78007),M=n(30263),t=n(99877);const a=[{path:"",redirectTo:"welcome",pathMatch:"full"},{path:"welcome",loadChildren:()=>n.e(8848).then(n.bind(n,48848)).then(o=>o.MboEnrollmentWelcomePageModule)},{path:"document-verification",loadChildren:()=>n.e(6631).then(n.bind(n,26631)).then(o=>o.MboEnrollmentDocumentPageModule)},{path:"otp-verification",loadChildren:()=>n.e(1051).then(n.bind(n,21051)).then(o=>o.MboEnrollmentOTPPageModule)},{path:"product-verification",loadChildren:()=>n.e(525).then(n.bind(n,10525)).then(o=>o.MboEnrollmentProductPageModule)},{path:"password-assignment",loadChildren:()=>n.e(3035).then(n.bind(n,93035)).then(o=>o.MboEnrollmentPasswordPageModule)},{path:"device-signup",loadChildren:()=>n.e(8722).then(n.bind(n,18722)).then(o=>o.MboEnrollmentDevicePageModule)},{path:"biometric",loadChildren:()=>n.e(4810).then(n.bind(n,24810)).then(o=>o.MboEnrollmentBiometricPageModule)},{path:"success-signup",loadChildren:()=>n.e(6273).then(n.bind(n,16273)).then(o=>o.MboEnrollmentSuccessPageModule)},{path:"facial-biometrics",loadChildren:()=>n.e(3271).then(n.bind(n,83271)).then(o=>o.BoccFacialBiometricsPageModule)}];let E=(()=>{class o{}return o.\u0275fac=function(P){return new(P||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({providers:[M.oc],imports:[d.CommonModule,h.RouterModule.forChild(a)]}),o})()}}]);