(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8054],{98054:(Sn,At,ze)=>{ze.r(At),ze.d(At,{BarcodeScannerWeb:()=>Eu});var ue=ze(15861),jt=ze(17737);var r,ht=(r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])})(t,n)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),tt=function(r){function e(t,n){var i=this.constructor,a=r.call(this,t,n)||this;return Object.defineProperty(a,"name",{value:i.name,enumerable:!1,configurable:!0}),function ct(r,e){var t=Object.setPrototypeOf;t?t(r,e):r.__proto__=e}(a,i.prototype),function et(r,e){void 0===e&&(e=r.constructor);var t=Error.captureStackTrace;t&&t(r,e)}(a),a}return ht(e,r),e}(Error),Ze=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),je=function(r){function e(t){void 0===t&&(t=void 0);var n=r.call(this,t)||this;return n.message=t,n}return Ze(e,r),e.prototype.getKind=function(){return this.constructor.kind},e.kind="Exception",e}(tt);const re=je;var Yt=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),In=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Yt(e,r),e.kind="ArgumentException",e}(re);const ne=In;var On=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Tn=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return On(e,r),e.kind="IllegalArgumentException",e}(re);const R=Tn;var Dn=function(){function r(e){if(this.binarizer=e,null===e)throw new R("Binarizer must be non-null.")}return r.prototype.getWidth=function(){return this.binarizer.getWidth()},r.prototype.getHeight=function(){return this.binarizer.getHeight()},r.prototype.getBlackRow=function(e,t){return this.binarizer.getBlackRow(e,t)},r.prototype.getBlackMatrix=function(){return null==this.matrix&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},r.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},r.prototype.crop=function(e,t,n,i){var a=this.binarizer.getLuminanceSource().crop(e,t,n,i);return new r(this.binarizer.createBinarizer(a))},r.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},r.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new r(this.binarizer.createBinarizer(e))},r.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new r(this.binarizer.createBinarizer(e))},r.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch{return""}},r}();const Er=Dn;var Rn=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),bn=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Rn(e,r),e.getChecksumInstance=function(){return new e},e.kind="ChecksumException",e}(re);const ae=bn;var Nn=function(){function r(e){this.source=e}return r.prototype.getLuminanceSource=function(){return this.source},r.prototype.getWidth=function(){return this.source.getWidth()},r.prototype.getHeight=function(){return this.source.getHeight()},r}();const Pn=Nn;var Mn=function(){function r(){}return r.arraycopy=function(e,t,n,i,a){for(;a--;)n[i++]=e[t++]},r.currentTimeMillis=function(){return Date.now()},r}();const q=Mn;var Bn=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Fn=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Bn(e,r),e.kind="IndexOutOfBoundsException",e}(re);const Kt=Fn;var Ln=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),kn=function(r){function e(t,n){void 0===t&&(t=void 0),void 0===n&&(n=void 0);var i=r.call(this,n)||this;return i.index=t,i.message=n,i}return Ln(e,r),e.kind="ArrayIndexOutOfBoundsException",e}(Kt);const mr=kn;var Vn=function(){function r(){}return r.fill=function(e,t){for(var n=0,i=e.length;n<i;n++)e[n]=t},r.fillWithin=function(e,t,n,i){r.rangeCheck(e.length,t,n);for(var a=t;a<n;a++)e[a]=i},r.rangeCheck=function(e,t,n){if(t>n)throw new R("fromIndex("+t+") > toIndex("+n+")");if(t<0)throw new mr(t);if(n>e)throw new mr(n)},r.asList=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e},r.create=function(e,t,n){return Array.from({length:e}).map(function(a){return Array.from({length:t}).fill(n)})},r.createInt32Array=function(e,t,n){return Array.from({length:e}).map(function(a){return Int32Array.from({length:t}).fill(n)})},r.equals=function(e,t){if(!(e&&t&&e.length&&t.length&&e.length===t.length))return!1;for(var n=0,i=e.length;n<i;n++)if(e[n]!==t[n])return!1;return!0},r.hashCode=function(e){var t,n;if(null===e)return 0;var i=1;try{for(var a=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),o=a.next();!o.done;o=a.next())i=31*i+o.value}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.fillUint8Array=function(e,t){for(var n=0;n!==e.length;n++)e[n]=t},r.copyOf=function(e,t){return e.slice(0,t)},r.copyOfUint8Array=function(e,t){if(e.length<=t){var n=new Uint8Array(t);return n.set(e),n}return e.slice(0,t)},r.copyOfRange=function(e,t,n){var i=n-t,a=new Int32Array(i);return q.arraycopy(e,t,a,0,i),a},r.binarySearch=function(e,t,n){void 0===n&&(n=r.numberComparator);for(var i=0,a=e.length-1;i<=a;){var o=a+i>>1,s=n(t,e[o]);if(s>0)i=o+1;else{if(!(s<0))return o;a=o-1}}return-i-1},r.numberComparator=function(e,t){return e-t},r}();const oe=Vn;var Hn=function(){function r(){}return r.numberOfTrailingZeros=function(e){var t;if(0===e)return 32;var n=31;return 0!=(t=e<<16)&&(n-=16,e=t),0!=(t=e<<8)&&(n-=8,e=t),0!=(t=e<<4)&&(n-=4,e=t),0!=(t=e<<2)&&(n-=2,e=t),n-(e<<1>>>31)},r.numberOfLeadingZeros=function(e){if(0===e)return 32;var t=1;return e>>>16||(t+=16,e<<=16),e>>>24||(t+=8,e<<=8),e>>>28||(t+=4,e<<=4),e>>>30||(t+=2,e<<=2),t-(e>>>31)},r.toHexString=function(e){return e.toString(16)},r.toBinaryString=function(e){return String(parseInt(String(e),2))},r.bitCount=function(e){return e=(e=(858993459&(e-=e>>>1&1431655765))+(e>>>2&858993459))+(e>>>4)&252645135,63&(e+=e>>>8)+(e>>>16)},r.truncDivision=function(e,t){return Math.trunc(e/t)},r.parseInt=function(e,t){return void 0===t&&(t=void 0),parseInt(e,t)},r.MIN_VALUE_32_BITS=-2147483648,r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r}();const B=Hn;var Gn=function(){function r(e,t){void 0===e?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,this.bits=null==t?r.makeArray(e):t)}return r.prototype.getSize=function(){return this.size},r.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},r.prototype.ensureCapacity=function(e){if(e>32*this.bits.length){var t=r.makeArray(e);q.arraycopy(this.bits,0,t,0,this.bits.length),this.bits=t}},r.prototype.get=function(e){return 0!=(this.bits[Math.floor(e/32)]&1<<(31&e))},r.prototype.set=function(e){this.bits[Math.floor(e/32)]|=1<<(31&e)},r.prototype.flip=function(e){this.bits[Math.floor(e/32)]^=1<<(31&e)},r.prototype.getNextSet=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=n[i];a&=~((1<<(31&e))-1);for(var o=n.length;0===a;){if(++i===o)return t;a=n[i]}var s=32*i+B.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.getNextUnset=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=~n[i];a&=~((1<<(31&e))-1);for(var o=n.length;0===a;){if(++i===o)return t;a=~n[i]}var s=32*i+B.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.setBulk=function(e,t){this.bits[Math.floor(e/32)]=t},r.prototype.setRange=function(e,t){if(t<e||e<0||t>this.size)throw new R;if(t!==e){t--;for(var n=Math.floor(e/32),i=Math.floor(t/32),a=this.bits,o=n;o<=i;o++)a[o]|=(2<<(o<i?31:31&t))-(1<<(o>n?0:31&e))}},r.prototype.clear=function(){for(var e=this.bits.length,t=this.bits,n=0;n<e;n++)t[n]=0},r.prototype.isRange=function(e,t,n){if(t<e||e<0||t>this.size)throw new R;if(t===e)return!0;t--;for(var i=Math.floor(e/32),a=Math.floor(t/32),o=this.bits,s=i;s<=a;s++){var c=(2<<(s<a?31:31&t))-(1<<(s>i?0:31&e))&4294967295;if((o[s]&c)!==(n?c:0))return!1}return!0},r.prototype.appendBit=function(e){this.ensureCapacity(this.size+1),e&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++},r.prototype.appendBits=function(e,t){if(t<0||t>32)throw new R("Num bits must be between 0 and 32");this.ensureCapacity(this.size+t);for(var n=t;n>0;n--)this.appendBit(1==(e>>n-1&1))},r.prototype.appendBitArray=function(e){var t=e.size;this.ensureCapacity(this.size+t);for(var n=0;n<t;n++)this.appendBit(e.get(n))},r.prototype.xor=function(e){if(this.size!==e.size)throw new R("Sizes don't match");for(var t=this.bits,n=0,i=t.length;n<i;n++)t[n]^=e.bits[n]},r.prototype.toBytes=function(e,t,n,i){for(var a=0;a<i;a++){for(var o=0,s=0;s<8;s++)this.get(e)&&(o|=1<<7-s),e++;t[n+a]=o}},r.prototype.getBitArray=function(){return this.bits},r.prototype.reverse=function(){for(var e=new Int32Array(this.bits.length),t=Math.floor((this.size-1)/32),n=t+1,i=this.bits,a=0;a<n;a++){var o=i[a];e[t-a]=o=(o=(o=(o=(o=o>>1&1431655765|(1431655765&o)<<1)>>2&858993459|(858993459&o)<<2)>>4&252645135|(252645135&o)<<4)>>8&16711935|(16711935&o)<<8)>>16&65535|(65535&o)<<16}if(this.size!==32*n){var s=32*n-this.size,u=e[0]>>>s;for(a=1;a<n;a++){var f=e[a];e[a-1]=u|=f<<32-s,u=f>>>s}e[n-1]=u}this.bits=e},r.makeArray=function(e){return new Int32Array(Math.floor((e+31)/32))},r.prototype.equals=function(e){return e instanceof r&&(this.size===e.size&&oe.equals(this.bits,e.bits))},r.prototype.hashCode=function(){return 31*this.size+oe.hashCode(this.bits)},r.prototype.toString=function(){for(var e="",t=0,n=this.size;t<n;t++)7&t||(e+=" "),e+=this.get(t)?"X":".";return e},r.prototype.clone=function(){return new r(this.size,this.bits.slice())},r.prototype.toArray=function(){for(var e=[],t=0,n=this.size;t<n;t++)e.push(this.get(t));return e},r}();const ve=Gn;var Ct=(()=>(function(r){r[r.OTHER=0]="OTHER",r[r.PURE_BARCODE=1]="PURE_BARCODE",r[r.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",r[r.TRY_HARDER=3]="TRY_HARDER",r[r.CHARACTER_SET=4]="CHARACTER_SET",r[r.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",r[r.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",r[r.ENABLE_CODE_39_EXTENDED_MODE=7]="ENABLE_CODE_39_EXTENDED_MODE",r[r.ASSUME_GS1=8]="ASSUME_GS1",r[r.RETURN_CODABAR_START_END=9]="RETURN_CODABAR_START_END",r[r.NEED_RESULT_POINT_CALLBACK=10]="NEED_RESULT_POINT_CALLBACK",r[r.ALLOWED_EAN_EXTENSIONS=11]="ALLOWED_EAN_EXTENSIONS"}(Ct||(Ct={})),Ct))();const K=Ct;var Wn=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Xn=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Wn(e,r),e.getFormatInstance=function(){return new e},e.kind="FormatException",e}(re);const O=Xn;var zn=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},z=(()=>(function(r){r[r.Cp437=0]="Cp437",r[r.ISO8859_1=1]="ISO8859_1",r[r.ISO8859_2=2]="ISO8859_2",r[r.ISO8859_3=3]="ISO8859_3",r[r.ISO8859_4=4]="ISO8859_4",r[r.ISO8859_5=5]="ISO8859_5",r[r.ISO8859_6=6]="ISO8859_6",r[r.ISO8859_7=7]="ISO8859_7",r[r.ISO8859_8=8]="ISO8859_8",r[r.ISO8859_9=9]="ISO8859_9",r[r.ISO8859_10=10]="ISO8859_10",r[r.ISO8859_11=11]="ISO8859_11",r[r.ISO8859_13=12]="ISO8859_13",r[r.ISO8859_14=13]="ISO8859_14",r[r.ISO8859_15=14]="ISO8859_15",r[r.ISO8859_16=15]="ISO8859_16",r[r.SJIS=16]="SJIS",r[r.Cp1250=17]="Cp1250",r[r.Cp1251=18]="Cp1251",r[r.Cp1252=19]="Cp1252",r[r.Cp1256=20]="Cp1256",r[r.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",r[r.UTF8=22]="UTF8",r[r.ASCII=23]="ASCII",r[r.Big5=24]="Big5",r[r.GB18030=25]="GB18030",r[r.EUC_KR=26]="EUC_KR"}(z||(z={})),z))(),Zn=function(){function r(e,t,n){for(var i,a,o=[],s=3;s<arguments.length;s++)o[s-3]=arguments[s];this.valueIdentifier=e,this.name=n,this.values="number"==typeof t?Int32Array.from([t]):t,this.otherEncodingNames=o,r.VALUE_IDENTIFIER_TO_ECI.set(e,this),r.NAME_TO_ECI.set(n,this);for(var u=this.values,f=0,c=u.length;f!==c;f++)r.VALUES_TO_ECI.set(u[f],this);try{for(var d=zn(o),l=d.next();!l.done;l=d.next())r.NAME_TO_ECI.set(l.value,this)}catch(p){i={error:p}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}}return r.prototype.getValueIdentifier=function(){return this.valueIdentifier},r.prototype.getName=function(){return this.name},r.prototype.getValue=function(){return this.values[0]},r.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new O("incorect value");var t=r.VALUES_TO_ECI.get(e);if(void 0===t)throw new O("incorect value");return t},r.getCharacterSetECIByName=function(e){var t=r.NAME_TO_ECI.get(e);if(void 0===t)throw new O("incorect value");return t},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.getName()===t.getName()},r.VALUE_IDENTIFIER_TO_ECI=new Map,r.VALUES_TO_ECI=new Map,r.NAME_TO_ECI=new Map,r.Cp437=new r(z.Cp437,Int32Array.from([0,2]),"Cp437"),r.ISO8859_1=new r(z.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),r.ISO8859_2=new r(z.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),r.ISO8859_3=new r(z.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),r.ISO8859_4=new r(z.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),r.ISO8859_5=new r(z.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),r.ISO8859_6=new r(z.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),r.ISO8859_7=new r(z.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),r.ISO8859_8=new r(z.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),r.ISO8859_9=new r(z.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),r.ISO8859_10=new r(z.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),r.ISO8859_11=new r(z.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),r.ISO8859_13=new r(z.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),r.ISO8859_14=new r(z.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),r.ISO8859_15=new r(z.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),r.ISO8859_16=new r(z.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),r.SJIS=new r(z.SJIS,20,"SJIS","Shift_JIS"),r.Cp1250=new r(z.Cp1250,21,"Cp1250","windows-1250"),r.Cp1251=new r(z.Cp1251,22,"Cp1251","windows-1251"),r.Cp1252=new r(z.Cp1252,23,"Cp1252","windows-1252"),r.Cp1256=new r(z.Cp1256,24,"Cp1256","windows-1256"),r.UnicodeBigUnmarked=new r(z.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),r.UTF8=new r(z.UTF8,26,"UTF8","UTF-8"),r.ASCII=new r(z.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),r.Big5=new r(z.Big5,28,"Big5"),r.GB18030=new r(z.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),r.EUC_KR=new r(z.EUC_KR,30,"EUC_KR","EUC-KR"),r}();const fe=Zn;var jn=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Yn=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return jn(e,r),e.kind="UnsupportedOperationException",e}(re);const Et=Yn;var Kn=function(){function r(){}return r.decode=function(e,t){var n=this.encodingName(t);return this.customDecoder?this.customDecoder(e,n):typeof TextDecoder>"u"||this.shouldDecodeOnFallback(n)?this.decodeFallback(e,n):new TextDecoder(n).decode(e)},r.shouldDecodeOnFallback=function(e){return!r.isBrowser()&&"ISO-8859-1"===e},r.encode=function(e,t){var n=this.encodingName(t);return this.customEncoder?this.customEncoder(e,n):typeof TextEncoder>"u"?this.encodeFallback(e):(new TextEncoder).encode(e)},r.isBrowser=function(){return typeof window<"u"&&"[object Window]"==={}.toString.call(window)},r.encodingName=function(e){return"string"==typeof e?e:e.getName()},r.encodingCharacterSet=function(e){return e instanceof fe?e:fe.getCharacterSetECIByName(e)},r.decodeFallback=function(e,t){var n=this.encodingCharacterSet(t);if(r.isDecodeFallbackSupported(n)){for(var i="",a=0,o=e.length;a<o;a++){var s=e[a].toString(16);s.length<2&&(s="0"+s),i+="%"+s}return decodeURIComponent(i)}if(n.equals(fe.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new Et("Encoding "+this.encodingName(t)+" not supported by fallback.")},r.isDecodeFallbackSupported=function(e){return e.equals(fe.UTF8)||e.equals(fe.ISO8859_1)||e.equals(fe.ASCII)},r.encodeFallback=function(e){for(var n=btoa(unescape(encodeURIComponent(e))).split(""),i=[],a=0;a<n.length;a++)i.push(n[a].charCodeAt(0));return new Uint8Array(i)},r}();const De=Kn;var qn=function(){function r(){}return r.castAsNonUtf8Char=function(e,t){void 0===t&&(t=null);var n=t?t.getName():this.ISO88591;return De.decode(new Uint8Array([e]),n)},r.guessEncoding=function(e,t){if(null!=t&&void 0!==t.get(K.CHARACTER_SET))return t.get(K.CHARACTER_SET).toString();for(var n=e.length,i=!0,a=!0,o=!0,s=0,u=0,f=0,c=0,h=0,d=0,l=0,v=0,p=0,x=0,w=0,y=e.length>3&&239===e[0]&&187===e[1]&&191===e[2],_=0;_<n&&(i||a||o);_++){var C=255&e[_];o&&(s>0?128&C?s--:o=!1:128&C&&(64&C?(s++,32&C?(s++,16&C?(s++,8&C?o=!1:c++):f++):u++):o=!1)),i&&(C>127&&C<160?i=!1:C>159&&(C<192||215===C||247===C)&&w++),a&&(h>0?C<64||127===C||C>252?a=!1:h--:128===C||160===C||C>239?a=!1:C>160&&C<224?(d++,v=0,++l>p&&(p=l)):C>127?(h++,l=0,++v>x&&(x=v)):(l=0,v=0))}return o&&s>0&&(o=!1),a&&h>0&&(a=!1),o&&(y||u+f+c>0)?r.UTF8:a&&(r.ASSUME_SHIFT_JIS||p>=3||x>=3)?r.SHIFT_JIS:i&&a?2===p&&2===d||10*w>=n?r.SHIFT_JIS:r.ISO88591:i?r.ISO88591:a?r.SHIFT_JIS:o?r.UTF8:r.PLATFORM_DEFAULT_ENCODING},r.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=-1;return e.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function a(s,u,f,c,h,d){if("%%"===s)return"%";if(void 0!==t[++i]){s=c?parseInt(c.substr(1)):void 0;var v,l=h?parseInt(h.substr(1)):void 0;switch(d){case"s":v=t[i];break;case"c":v=t[i][0];break;case"f":v=parseFloat(t[i]).toFixed(s);break;case"p":v=parseFloat(t[i]).toPrecision(s);break;case"e":v=parseFloat(t[i]).toExponential(s);break;case"x":v=parseInt(t[i]).toString(l||16);break;case"d":v=parseFloat(parseInt(t[i],l||10).toPrecision(s)).toFixed(0)}v="object"==typeof v?JSON.stringify(v):(+v).toString(l);for(var p=parseInt(f),x=f&&f[0]+""=="0"?"0":" ";v.length<p;)v=void 0!==u?v+x:x+v;return v}})},r.getBytes=function(e,t){return De.encode(e,t)},r.getCharCode=function(e,t){return void 0===t&&(t=0),e.charCodeAt(t)},r.getCharAt=function(e){return String.fromCharCode(e)},r.SHIFT_JIS=fe.SJIS.getName(),r.GB2312="GB2312",r.ISO88591=fe.ISO8859_1.getName(),r.EUC_JP="EUC_JP",r.UTF8=fe.UTF8.getName(),r.PLATFORM_DEFAULT_ENCODING=r.UTF8,r.ASSUME_SHIFT_JIS=!1,r}();const U=qn;var Qn=function(){function r(e){void 0===e&&(e=""),this.value=e}return r.prototype.enableDecoding=function(e){return this.encoding=e,this},r.prototype.append=function(e){return this.value+="string"==typeof e?e.toString():this.encoding?U.castAsNonUtf8Char(e,this.encoding):String.fromCharCode(e),this},r.prototype.appendChars=function(e,t,n){for(var i=t;t<t+n;i++)this.append(e[i]);return this},r.prototype.length=function(){return this.value.length},r.prototype.charAt=function(e){return this.value.charAt(e)},r.prototype.deleteCharAt=function(e){this.value=this.value.substr(0,e)+this.value.substring(e+1)},r.prototype.setCharAt=function(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+1)},r.prototype.substring=function(e,t){return this.value.substring(e,t)},r.prototype.setLengthToZero=function(){this.value=""},r.prototype.toString=function(){return this.value},r.prototype.insert=function(e,t){this.value=this.value.substring(0,e)+t+this.value.substring(e)},r}();const F=Qn;var Jn=function(){function r(e,t,n,i){if(this.width=e,this.height=t,this.rowSize=n,this.bits=i,null==t&&(t=e),this.height=t,e<1||t<1)throw new R("Both dimensions must be greater than 0");null==n&&(n=Math.floor((e+31)/32)),this.rowSize=n,null==i&&(this.bits=new Int32Array(this.rowSize*this.height))}return r.parseFromBooleanArray=function(e){for(var t=e.length,n=e[0].length,i=new r(n,t),a=0;a<t;a++)for(var o=e[a],s=0;s<n;s++)o[s]&&i.set(s,a);return i},r.parseFromString=function(e,t,n){if(null===e)throw new R("stringRepresentation cannot be null");for(var i=new Array(e.length),a=0,o=0,s=-1,u=0,f=0;f<e.length;)if("\n"===e.charAt(f)||"\r"===e.charAt(f)){if(a>o){if(-1===s)s=a-o;else if(a-o!==s)throw new R("row lengths do not match");o=a,u++}f++}else if(e.substring(f,f+t.length)===t)f+=t.length,i[a]=!0,a++;else{if(e.substring(f,f+n.length)!==n)throw new R("illegal character encountered: "+e.substring(f));f+=n.length,i[a]=!1,a++}if(a>o){if(-1===s)s=a-o;else if(a-o!==s)throw new R("row lengths do not match");u++}for(var c=new r(s,u),h=0;h<a;h++)i[h]&&c.set(Math.floor(h%s),Math.floor(h/s));return c},r.prototype.get=function(e,t){var n=t*this.rowSize+Math.floor(e/32);return 0!=(this.bits[n]>>>(31&e)&1)},r.prototype.set=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]|=1<<(31&e)&4294967295},r.prototype.unset=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]&=~(1<<(31&e)&4294967295)},r.prototype.flip=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]^=1<<(31&e)&4294967295},r.prototype.xor=function(e){if(this.width!==e.getWidth()||this.height!==e.getHeight()||this.rowSize!==e.getRowSize())throw new R("input matrix dimensions do not match");for(var t=new ve(Math.floor(this.width/32)+1),n=this.rowSize,i=this.bits,a=0,o=this.height;a<o;a++)for(var s=a*n,u=e.getRow(a,t).getBitArray(),f=0;f<n;f++)i[s+f]^=u[f]},r.prototype.clear=function(){for(var e=this.bits,t=e.length,n=0;n<t;n++)e[n]=0},r.prototype.setRegion=function(e,t,n,i){if(t<0||e<0)throw new R("Left and top must be nonnegative");if(i<1||n<1)throw new R("Height and width must be at least 1");var a=e+n,o=t+i;if(o>this.height||a>this.width)throw new R("The region must fit inside the matrix");for(var s=this.rowSize,u=this.bits,f=t;f<o;f++)for(var c=f*s,h=e;h<a;h++)u[c+Math.floor(h/32)]|=1<<(31&h)&4294967295},r.prototype.getRow=function(e,t){null==t||t.getSize()<this.width?t=new ve(this.width):t.clear();for(var n=this.rowSize,i=this.bits,a=e*n,o=0;o<n;o++)t.setBulk(32*o,i[a+o]);return t},r.prototype.setRow=function(e,t){q.arraycopy(t.getBitArray(),0,this.bits,e*this.rowSize,this.rowSize)},r.prototype.rotate180=function(){for(var e=this.getWidth(),t=this.getHeight(),n=new ve(e),i=new ve(e),a=0,o=Math.floor((t+1)/2);a<o;a++)n=this.getRow(a,n),i=this.getRow(t-1-a,i),n.reverse(),i.reverse(),this.setRow(a,i),this.setRow(t-1-a,n)},r.prototype.getEnclosingRectangle=function(){for(var t=this.height,n=this.rowSize,i=this.bits,a=this.width,o=t,s=-1,u=-1,f=0;f<t;f++)for(var c=0;c<n;c++){var h=i[f*n+c];if(0!==h){if(f<o&&(o=f),f>u&&(u=f),32*c<a){for(var d=0;!(h<<31-d&4294967295);)d++;32*c+d<a&&(a=32*c+d)}if(32*c+31>s){for(d=31;!(h>>>d);)d--;32*c+d>s&&(s=32*c+d)}}}return s<a||u<o?null:Int32Array.from([a,o,s-a+1,u-o+1])},r.prototype.getTopLeftOnBit=function(){for(var e=this.rowSize,t=this.bits,n=0;n<t.length&&0===t[n];)n++;if(n===t.length)return null;for(var i=n/e,a=n%e*32,o=t[n],s=0;!(o<<31-s&4294967295);)s++;return Int32Array.from([a+=s,i])},r.prototype.getBottomRightOnBit=function(){for(var e=this.rowSize,t=this.bits,n=t.length-1;n>=0&&0===t[n];)n--;if(n<0)return null;for(var i=Math.floor(n/e),a=32*Math.floor(n%e),o=t[n],s=31;!(o>>>s);)s--;return Int32Array.from([a+=s,i])},r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.getRowSize=function(){return this.rowSize},r.prototype.equals=function(e){return e instanceof r&&(this.width===e.width&&this.height===e.height&&this.rowSize===e.rowSize&&oe.equals(this.bits,e.bits))},r.prototype.hashCode=function(){var e=this.width;return 31*(e=31*(e=31*(e=31*e+this.width)+this.height)+this.rowSize)+oe.hashCode(this.bits)},r.prototype.toString=function(e,t,n){return void 0===e&&(e="X "),void 0===t&&(t="  "),void 0===n&&(n="\n"),this.buildToString(e,t,n)},r.prototype.buildToString=function(e,t,n){for(var i=new F,a=0,o=this.height;a<o;a++){for(var s=0,u=this.width;s<u;s++)i.append(this.get(s,a)?e:t);i.append(n)}return i.toString()},r.prototype.clone=function(){return new r(this.width,this.height,this.rowSize,this.bits.slice())},r}();const me=Jn;var $n=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ei=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return $n(e,r),e.getNotFoundInstance=function(){return new e},e.kind="NotFoundException",e}(re);const E=ei;var ti=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ri=function(r){function e(t){var n=r.call(this,t)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return ti(e,r),e.prototype.getBlackRow=function(t,n){var i=this.getLuminanceSource(),a=i.getWidth();null==n||n.getSize()<a?n=new ve(a):n.clear(),this.initArrays(a);for(var o=i.getRow(t,this.luminances),s=this.buckets,u=0;u<a;u++)s[(255&o[u])>>e.LUMINANCE_SHIFT]++;var f=e.estimateBlackPoint(s);if(a<3)for(u=0;u<a;u++)(255&o[u])<f&&n.set(u);else{var c=255&o[0],h=255&o[1];for(u=1;u<a-1;u++){var d=255&o[u+1];(4*h-c-d)/2<f&&n.set(u),c=h,h=d}}return n},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight(),a=new me(n,i);this.initArrays(n);for(var o=this.buckets,s=1;s<5;s++)for(var u=Math.floor(i*s/5),f=t.getRow(u,this.luminances),c=Math.floor(4*n/5),h=Math.floor(n/5);h<c;h++)o[(255&f[h])>>e.LUMINANCE_SHIFT]++;var l=e.estimateBlackPoint(o),v=t.getMatrix();for(s=0;s<i;s++){var p=s*n;for(h=0;h<n;h++)(255&v[p+h])<l&&a.set(h,s)}return a},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var n=this.buckets,i=0;i<e.LUMINANCE_BUCKETS;i++)n[i]=0},e.estimateBlackPoint=function(t){for(var n=t.length,i=0,a=0,o=0,s=0;s<n;s++)t[s]>o&&(a=s,o=t[s]),t[s]>i&&(i=t[s]);var u=0,f=0;for(s=0;s<n;s++){var c=s-a;(h=t[s]*c*c)>f&&(u=s,f=h)}if(a>u){var d=a;a=u,u=d}if(u-a<=n/16)throw new E;var l=u-1,v=-1;for(s=u-1;s>a;s--){var h,p=s-a;(h=p*p*(u-s)*(i-t[s]))>v&&(l=s,v=h)}return l<<e.LUMINANCE_SHIFT},e.LUMINANCE_SHIFT=8-(e.LUMINANCE_BITS=5),e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e}(Pn);const ni=ri;var ii=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ai=function(r){function e(t){var n=r.call(this,t)||this;return n.matrix=null,n}return ii(e,r),e.prototype.getBlackMatrix=function(){if(null!==this.matrix)return this.matrix;var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight();if(n>=e.MINIMUM_DIMENSION&&i>=e.MINIMUM_DIMENSION){var a=t.getMatrix(),o=n>>e.BLOCK_SIZE_POWER;n&e.BLOCK_SIZE_MASK&&o++;var s=i>>e.BLOCK_SIZE_POWER;i&e.BLOCK_SIZE_MASK&&s++;var u=e.calculateBlackPoints(a,o,s,n,i),f=new me(n,i);e.calculateThresholdForBlock(a,o,s,n,i,u,f),this.matrix=f}else this.matrix=r.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,n,i,a,o,s,u){for(var f=o-e.BLOCK_SIZE,c=a-e.BLOCK_SIZE,h=0;h<i;h++){var d=h<<e.BLOCK_SIZE_POWER;d>f&&(d=f);for(var l=e.cap(h,2,i-3),v=0;v<n;v++){var p=v<<e.BLOCK_SIZE_POWER;p>c&&(p=c);for(var x=e.cap(v,2,n-3),w=0,y=-2;y<=2;y++){var _=s[l+y];w+=_[x-2]+_[x-1]+_[x]+_[x+1]+_[x+2]}e.thresholdBlock(t,p,d,w/25,a,u)}}},e.cap=function(t,n,i){return t<n?n:t>i?i:t},e.thresholdBlock=function(t,n,i,a,o,s){for(var u=0,f=i*o+n;u<e.BLOCK_SIZE;u++,f+=o)for(var c=0;c<e.BLOCK_SIZE;c++)(255&t[f+c])<=a&&s.set(n+c,i+u)},e.calculateBlackPoints=function(t,n,i,a,o){for(var s=o-e.BLOCK_SIZE,u=a-e.BLOCK_SIZE,f=new Array(i),c=0;c<i;c++){f[c]=new Int32Array(n);var h=c<<e.BLOCK_SIZE_POWER;h>s&&(h=s);for(var d=0;d<n;d++){var l=d<<e.BLOCK_SIZE_POWER;l>u&&(l=u);for(var v=0,p=255,x=0,w=0,y=h*a+l;w<e.BLOCK_SIZE;w++,y+=a){for(var _=0;_<e.BLOCK_SIZE;_++){var C=255&t[y+_];v+=C,C<p&&(p=C),C>x&&(x=C)}if(x-p>e.MIN_DYNAMIC_RANGE)for(w++,y+=a;w<e.BLOCK_SIZE;w++,y+=a)for(_=0;_<e.BLOCK_SIZE;_++)v+=255&t[y+_]}var m=v>>2*e.BLOCK_SIZE_POWER;if(x-p<=e.MIN_DYNAMIC_RANGE&&(m=p/2,c>0&&d>0)){var S=(f[c-1][d]+2*f[c][d-1]+f[c-1][d-1])/4;p<S&&(m=S)}f[c][d]=m}}return f},e.BLOCK_SIZE_MASK=(e.BLOCK_SIZE=1<<(e.BLOCK_SIZE_POWER=3))-1,e.MINIMUM_DIMENSION=5*e.BLOCK_SIZE,e.MIN_DYNAMIC_RANGE=24,e}(ni);const Sr=ai;var oi=function(){function r(e,t){this.width=e,this.height=t}return r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.isCropSupported=function(){return!1},r.prototype.crop=function(e,t,n,i){throw new Et("This luminance source does not support cropping.")},r.prototype.isRotateSupported=function(){return!1},r.prototype.rotateCounterClockwise=function(){throw new Et("This luminance source does not support rotation by 90 degrees.")},r.prototype.rotateCounterClockwise45=function(){throw new Et("This luminance source does not support rotation by 45 degrees.")},r.prototype.toString=function(){for(var e=new Uint8ClampedArray(this.width),t=new F,n=0;n<this.height;n++){for(var i=this.getRow(n,e),a=0;a<this.width;a++){var o=255&i[a];t.append(o<64?"#":o<128?"+":o<192?".":" ")}t.append("\n")}return t.toString()},r}();const lt=oi;var si=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ui=function(r){function e(t){var n=r.call(this,t.getWidth(),t.getHeight())||this;return n.delegate=t,n}return si(e,r),e.prototype.getRow=function(t,n){for(var i=this.delegate.getRow(t,n),a=this.getWidth(),o=0;o<a;o++)i[o]=255-(255&i[o]);return i},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),n=this.getWidth()*this.getHeight(),i=new Uint8ClampedArray(n),a=0;a<n;a++)i[a]=255-(255&t[a]);return i},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,n,i,a){return new e(this.delegate.crop(t,n,i,a))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e}(lt);const mt=ui;var fi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ci=function(r){function e(t,n){void 0===n&&(n=!1);var i=r.call(this,t.width,t.height)||this;return i.canvas=t,i.tempCanvasElement=null,i.buffer=e.makeBufferFromCanvasImageData(t,n),i}return fi(e,r),e.makeBufferFromCanvasImageData=function(t,n){void 0===n&&(n=!1);var i=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(i.data,t.width,t.height,n)},e.toGrayscaleBuffer=function(t,n,i,a){void 0===a&&(a=!1);var o=new Uint8ClampedArray(n*i);if((e.FRAME_INDEX=!e.FRAME_INDEX)||!a)for(var s=0,u=0,f=t.length;s<f;s+=4,u++){o[u]=0===t[s+3]?255:306*t[s]+601*t[s+1]+117*t[s+2]+512>>10}else{s=0,u=0;for(var p=t.length;s<p;s+=4,u++)void 0,o[u]=255-(0===t[s+3]?255:306*t[s]+601*t[s+1]+117*t[s+2]+512>>10)}return o},e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new R("Requested row is outside the image: "+t);var i=this.getWidth(),a=t*i;return null===n?n=this.buffer.slice(a,a+i):(n.length<i&&(n=new Uint8ClampedArray(i)),n.set(this.buffer.slice(a,a+i))),n},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return r.prototype.crop.call(this,t,n,i,a),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement(),i=n.getContext("2d"),a=t*e.DEGREE_TO_RADIANS,o=this.canvas.width,s=this.canvas.height,u=Math.ceil(Math.abs(Math.cos(a))*o+Math.abs(Math.sin(a))*s),f=Math.ceil(Math.abs(Math.sin(a))*o+Math.abs(Math.cos(a))*s);return n.width=u,n.height=f,i.translate(u/2,f/2),i.rotate(a),i.drawImage(this.canvas,o/-2,s/-2),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.prototype.invert=function(){return new mt(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e}(lt),hi=function(){function r(e,t,n){this.deviceId=e,this.label=t,this.kind="videoinput",this.groupId=n||void 0}return r.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},r}(),Ae=function(r,e,t,n){return new(t||(t=Promise))(function(a,o){function s(c){try{f(n.next(c))}catch(h){o(h)}}function u(c){try{f(n.throw(c))}catch(h){o(h)}}function f(c){c.done?a(c.value):function i(a){return a instanceof t?a:new t(function(o){o(a)})}(c.value).then(s,u)}f((n=n.apply(r,e||[])).next())})},Ce=function(r,e){var n,i,a,o,t={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(f){return function(c){return function u(f){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=2&f[0]?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[2&f[0],a.value]),f[0]){case 0:case 1:a=f;break;case 4:return t.label++,{value:f[1],done:!1};case 5:t.label++,i=f[1],f=[0];continue;case 7:f=t.ops.pop(),t.trys.pop();continue;default:if(!(a=(a=t.trys).length>0&&a[a.length-1])&&(6===f[0]||2===f[0])){t=0;continue}if(3===f[0]&&(!a||f[1]>a[0]&&f[1]<a[3])){t.label=f[1];break}if(6===f[0]&&t.label<a[1]){t.label=a[1],a=f;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(f);break}a[2]&&t.ops.pop(),t.trys.pop();continue}f=e.call(r,t)}catch(c){f=[6,c],i=0}finally{n=a=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,c])}}},nt=function(){function r(e,t,n){void 0===t&&(t=500),this.reader=e,this.timeBetweenScansMillis=t,this._hints=n,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(r.prototype,"hasNavigator",{get:function(){return typeof navigator<"u"},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"canEnumerateDevices",{get:function(){return!(!this.isMediaDevicesSuported||!navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(e){this._timeBetweenDecodingAttempts=e<0?0:e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"hints",{get:function(){return this._hints},set:function(e){this._hints=e||null},enumerable:!1,configurable:!0}),r.prototype.listVideoInputDevices=function(){return Ae(this,void 0,void 0,function(){var e,t,n,i,a,o,h,d;return Ce(this,function(l){switch(l.label){case 0:if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=l.sent(),t=[];try{for(n=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),i=n.next();!i.done;i=n.next())"videoinput"===(o="video"===(a=i.value).kind?"videoinput":a.kind)&&t.push({deviceId:a.deviceId||a.id,label:a.label||"Video device "+(t.length+1),kind:o,groupId:a.groupId})}catch(v){h={error:v}}finally{try{i&&!i.done&&(d=n.return)&&d.call(n)}finally{if(h)throw h.error}}return[2,t]}})})},r.prototype.getVideoInputDevices=function(){return Ae(this,void 0,void 0,function(){return Ce(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return[2,t.sent().map(function(n){return new hi(n.deviceId,n.label)})]}})})},r.prototype.findDeviceById=function(e){return Ae(this,void 0,void 0,function(){var t;return Ce(this,function(n){switch(n.label){case 0:return[4,this.listVideoInputDevices()];case 1:return(t=n.sent())?[2,t.find(function(i){return i.deviceId===e})]:[2,null]}})})},r.prototype.decodeFromInputVideoDevice=function(e,t){return Ae(this,void 0,void 0,function(){return Ce(this,function(n){switch(n.label){case 0:return[4,this.decodeOnceFromVideoDevice(e,t)];case 1:return[2,n.sent()]}})})},r.prototype.decodeOnceFromVideoDevice=function(e,t){return Ae(this,void 0,void 0,function(){return Ce(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.decodeOnceFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t)];case 1:return[2,a.sent()]}})})},r.prototype.decodeOnceFromConstraints=function(e,t){return Ae(this,void 0,void 0,function(){var n;return Ce(this,function(i){switch(i.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return n=i.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromStream=function(e,t){return Ae(this,void 0,void 0,function(){var n;return Ce(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return n=a.sent(),[4,this.decodeOnce(n)];case 2:return[2,a.sent()]}})})},r.prototype.decodeFromInputVideoDeviceContinuously=function(e,t,n){return Ae(this,void 0,void 0,function(){return Ce(this,function(i){switch(i.label){case 0:return[4,this.decodeFromVideoDevice(e,t,n)];case 1:return[2,i.sent()]}})})},r.prototype.decodeFromVideoDevice=function(e,t,n){return Ae(this,void 0,void 0,function(){return Ce(this,function(o){switch(o.label){case 0:return[4,this.decodeFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t,n)];case 1:return[2,o.sent()]}})})},r.prototype.decodeFromConstraints=function(e,t,n){return Ae(this,void 0,void 0,function(){var i;return Ce(this,function(a){switch(a.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return i=a.sent(),[4,this.decodeFromStream(i,t,n)];case 2:return[2,a.sent()]}})})},r.prototype.decodeFromStream=function(e,t,n){return Ae(this,void 0,void 0,function(){var i;return Ce(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return i=a.sent(),[4,this.decodeContinuously(i,n)];case 2:return[2,a.sent()]}})})},r.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},r.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},r.prototype.attachStreamToVideo=function(e,t){return Ae(this,void 0,void 0,function(){var n;return Ce(this,function(i){switch(i.label){case 0:return n=this.prepareVideoElement(t),this.addVideoSource(n,e),this.videoElement=n,this.stream=e,[4,this.playVideoOnLoadAsync(n)];case 1:return i.sent(),[2,n]}})})},r.prototype.playVideoOnLoadAsync=function(e){var t=this;return new Promise(function(n,i){return t.playVideoOnLoad(e,function(){return n()})})},r.prototype.playVideoOnLoad=function(e,t){var n=this;this.videoEndedListener=function(){return n.stopStreams()},this.videoCanPlayListener=function(){return n.tryPlayVideo(e)},e.addEventListener("ended",this.videoEndedListener),e.addEventListener("canplay",this.videoCanPlayListener),e.addEventListener("playing",t),this.tryPlayVideo(e)},r.prototype.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&!e.ended&&e.readyState>2},r.prototype.tryPlayVideo=function(e){return Ae(this,void 0,void 0,function(){return Ce(this,function(n){switch(n.label){case 0:if(this.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,e.play()];case 2:return n.sent(),[3,4];case 3:return n.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},r.prototype.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new ne("element with id '"+e+"' not found");if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new ne("element with id '"+e+"' must be an "+t+" element");return n},r.prototype.decodeFromImage=function(e,t){if(!e&&!t)throw new ne("either imageElement with a src set or an url must be provided");return t&&!e?this.decodeFromImageUrl(t):this.decodeFromImageElement(e)},r.prototype.decodeFromVideo=function(e,t){if(!e&&!t)throw new ne("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrl(t):this.decodeFromVideoElement(e)},r.prototype.decodeFromVideoContinuously=function(e,t,n){if(void 0===e&&void 0===t)throw new ne("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrlContinuously(t,n):this.decodeFromVideoElementContinuously(e,n)},r.prototype.decodeFromImageElement=function(e){if(!e)throw new ne("An image element must be provided.");this.reset();var t=this.prepareImageElement(e);return this.imageElement=t,this.isImageLoaded(t)?this.decodeOnce(t,!1,!0):this._decodeOnLoadImage(t)},r.prototype.decodeFromVideoElement=function(e){var t=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideo(t)},r.prototype.decodeFromVideoElementContinuously=function(e,t){var n=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideoContinuously(n,t)},r.prototype._decodeFromVideoElementSetup=function(e){if(!e)throw new ne("A video element must be provided.");this.reset();var t=this.prepareVideoElement(e);return this.videoElement=t,t},r.prototype.decodeFromImageUrl=function(e){if(!e)throw new ne("An URL must be provided.");this.reset();var t=this.prepareImageElement();this.imageElement=t;var n=this._decodeOnLoadImage(t);return t.src=e,n},r.prototype.decodeFromVideoUrl=function(e){if(!e)throw new ne("An URL must be provided.");this.reset();var t=this.prepareVideoElement(),n=this.decodeFromVideoElement(t);return t.src=e,n},r.prototype.decodeFromVideoUrlContinuously=function(e,t){if(!e)throw new ne("An URL must be provided.");this.reset();var n=this.prepareVideoElement(),i=this.decodeFromVideoElementContinuously(n,t);return n.src=e,i},r.prototype._decodeOnLoadImage=function(e){var t=this;return new Promise(function(n,i){t.imageLoadedListener=function(){return t.decodeOnce(e,!1,!0).then(n,i)},e.addEventListener("load",t.imageLoadedListener)})},r.prototype._decodeOnLoadVideo=function(e){return Ae(this,void 0,void 0,function(){return Ce(this,function(t){switch(t.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return t.sent(),[4,this.decodeOnce(e)];case 2:return[2,t.sent()]}})})},r.prototype._decodeOnLoadVideoContinuously=function(e,t){return Ae(this,void 0,void 0,function(){return Ce(this,function(n){switch(n.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return n.sent(),this.decodeContinuously(e,t),[2]}})})},r.prototype.isImageLoaded=function(e){return!(!e.complete||0===e.naturalWidth)},r.prototype.prepareImageElement=function(e){var t;return typeof e>"u"&&((t=document.createElement("img")).width=200,t.height=200),"string"==typeof e&&(t=this.getMediaElement(e,"img")),e instanceof HTMLImageElement&&(t=e),t},r.prototype.prepareVideoElement=function(e){var t;return!e&&typeof document<"u"&&((t=document.createElement("video")).width=200,t.height=200),"string"==typeof e&&(t=this.getMediaElement(e,"video")),e instanceof HTMLVideoElement&&(t=e),t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t},r.prototype.decodeOnce=function(e,t,n){var i=this;void 0===t&&(t=!0),void 0===n&&(n=!0),this._stopAsyncDecode=!1;var a=function(o,s){if(i._stopAsyncDecode)return s(new E("Video stream has ended before any code could be detected.")),void(i._stopAsyncDecode=void 0);try{o(i.decode(e))}catch(d){if(t&&d instanceof E||(d instanceof ae||d instanceof O)&&n)return setTimeout(a,i._timeBetweenDecodingAttempts,o,s);s(d)}};return new Promise(function(o,s){return a(o,s)})},r.prototype.decodeContinuously=function(e,t){var n=this;this._stopContinuousDecode=!1;var i=function(){if(n._stopContinuousDecode)n._stopContinuousDecode=void 0;else try{var a=n.decode(e);t(a,null),setTimeout(i,n.timeBetweenScansMillis)}catch(u){t(null,u),(u instanceof ae||u instanceof O||u instanceof E)&&setTimeout(i,n._timeBetweenDecodingAttempts)}};i()},r.prototype.decode=function(e){var t=this.createBinaryBitmap(e);return this.decodeBitmap(t)},r.prototype.createBinaryBitmap=function(e){this.getCaptureCanvasContext(e);var n=!1;e instanceof HTMLVideoElement?(this.drawFrameOnCanvas(e),n=!0):this.drawImageOnCanvas(e);var i=this.getCaptureCanvas(e),a=new ci(i,n),o=new Sr(a);return new Er(o)},r.prototype.getCaptureCanvasContext=function(e){if(!this.captureCanvasContext){var t=this.getCaptureCanvas(e),n=void 0;try{n=t.getContext("2d",{willReadFrequently:!0})}catch{n=t.getContext("2d")}this.captureCanvasContext=n}return this.captureCanvasContext},r.prototype.getCaptureCanvas=function(e){if(!this.captureCanvas){var t=this.createCaptureCanvas(e);this.captureCanvas=t}return this.captureCanvas},r.prototype.drawFrameOnCanvas=function(e,t,n){void 0===t&&(t={sx:0,sy:0,sWidth:e.videoWidth,sHeight:e.videoHeight,dx:0,dy:0,dWidth:e.videoWidth,dHeight:e.videoHeight}),void 0===n&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.drawImageOnCanvas=function(e,t,n){void 0===t&&(t={sx:0,sy:0,sWidth:e.naturalWidth,sHeight:e.naturalHeight,dx:0,dy:0,dWidth:e.naturalWidth,dHeight:e.naturalHeight}),void 0===n&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.decodeBitmap=function(e){return this.reader.decode(e,this._hints)},r.prototype.createCaptureCanvas=function(e){if(typeof document>"u")return this._destroyCaptureCanvas(),null;var n,i,t=document.createElement("canvas");return typeof e<"u"&&(e instanceof HTMLVideoElement?(n=e.videoWidth,i=e.videoHeight):e instanceof HTMLImageElement&&(n=e.naturalWidth||e.width,i=e.naturalHeight||e.height)),t.style.width=n+"px",t.style.height=i+"px",t.width=n,t.height=i,t},r.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(e){return e.stop()}),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()},r.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},r.prototype._destroyVideoElement=function(){this.videoElement&&(typeof this.videoEndedListener<"u"&&this.videoElement.removeEventListener("ended",this.videoEndedListener),typeof this.videoPlayingEventListener<"u"&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),typeof this.videoCanPlayListener<"u"&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},r.prototype._destroyImageElement=function(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},r.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},r.prototype.addVideoSource=function(e,t){try{e.srcObject=t}catch{e.src=URL.createObjectURL(t)}},r.prototype.cleanVideoSource=function(e){try{e.srcObject=null}catch{e.src=""}this.videoElement.removeAttribute("src")},r}(),li=function(){function r(e,t,n,i,a,o){void 0===n&&(n=null==t?0:8*t.length),void 0===o&&(o=q.currentTimeMillis()),this.text=e,this.rawBytes=t,this.numBits=n,this.resultPoints=i,this.format=a,this.timestamp=o,this.text=e,this.rawBytes=t,this.numBits=null==n?null==t?0:8*t.length:n,this.resultPoints=i,this.format=a,this.resultMetadata=null,this.timestamp=null==o?q.currentTimeMillis():o}return r.prototype.getText=function(){return this.text},r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.getBarcodeFormat=function(){return this.format},r.prototype.getResultMetadata=function(){return this.resultMetadata},r.prototype.putMetadata=function(e,t){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(e,t)},r.prototype.putAllMetadata=function(e){null!==e&&(this.resultMetadata=null===this.resultMetadata?e:new Map(e))},r.prototype.addResultPoints=function(e){var t=this.resultPoints;if(null===t)this.resultPoints=e;else if(null!==e&&e.length>0){var n=new Array(t.length+e.length);q.arraycopy(t,0,n,0,t.length),q.arraycopy(e,0,n,t.length,e.length),this.resultPoints=n}},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.toString=function(){return this.text},r}();const we=li;var St=(()=>(function(r){r[r.AZTEC=0]="AZTEC",r[r.CODABAR=1]="CODABAR",r[r.CODE_39=2]="CODE_39",r[r.CODE_93=3]="CODE_93",r[r.CODE_128=4]="CODE_128",r[r.DATA_MATRIX=5]="DATA_MATRIX",r[r.EAN_8=6]="EAN_8",r[r.EAN_13=7]="EAN_13",r[r.ITF=8]="ITF",r[r.MAXICODE=9]="MAXICODE",r[r.PDF_417=10]="PDF_417",r[r.QR_CODE=11]="QR_CODE",r[r.RSS_14=12]="RSS_14",r[r.RSS_EXPANDED=13]="RSS_EXPANDED",r[r.UPC_A=14]="UPC_A",r[r.UPC_E=15]="UPC_E",r[r.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"}(St||(St={})),St))();const N=St;var It=(()=>(function(r){r[r.OTHER=0]="OTHER",r[r.ORIENTATION=1]="ORIENTATION",r[r.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",r[r.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",r[r.ISSUE_NUMBER=4]="ISSUE_NUMBER",r[r.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",r[r.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",r[r.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",r[r.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",r[r.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",r[r.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"}(It||(It={})),It))();const xe=It;var vi=function(){function r(e,t,n,i,a,o){void 0===a&&(a=-1),void 0===o&&(o=-1),this.rawBytes=e,this.text=t,this.byteSegments=n,this.ecLevel=i,this.structuredAppendSequenceNumber=a,this.structuredAppendParity=o,this.numBits=null==e?0:8*e.length}return r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.setNumBits=function(e){this.numBits=e},r.prototype.getText=function(){return this.text},r.prototype.getByteSegments=function(){return this.byteSegments},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getErrorsCorrected=function(){return this.errorsCorrected},r.prototype.setErrorsCorrected=function(e){this.errorsCorrected=e},r.prototype.getErasures=function(){return this.erasures},r.prototype.setErasures=function(e){this.erasures=e},r.prototype.getOther=function(){return this.other},r.prototype.setOther=function(e){this.other=e},r.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},r.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},r.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},r}();const Ot=vi;var pi=function(){function r(){}return r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(0===e)throw new R;return this.logTable[e]},r.addOrSubtract=function(e,t){return e^t},r}();const vt=pi;var gi=function(){function r(e,t){if(0===t.length)throw new R;this.field=e;var n=t.length;if(n>1&&0===t[0]){for(var i=1;i<n&&0===t[i];)i++;i===n?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(n-i),q.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return 0===this.coefficients[0]},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){if(0===e)return this.getCoefficient(0);var n,t=this.coefficients;if(1===e){n=0;for(var i=0,a=t.length;i!==a;i++)n=vt.addOrSubtract(n,t[i]);return n}n=t[0];var s=t.length,u=this.field;for(i=1;i<s;i++)n=vt.addOrSubtract(u.multiply(e,n),t[i]);return n},r.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new R("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;q.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=vt.addOrSubtract(t[s-o],n[s]);return new r(this.field,a)},r.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new R("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=this.field,u=0;u<n;u++)for(var f=t[u],c=0;c<a;c++)o[u+c]=vt.addOrSubtract(o[u+c],s.multiply(f,i[c]));return new r(s,o)},r.prototype.multiplyScalar=function(e){if(0===e)return this.field.getZero();if(1===e)return this;for(var t=this.coefficients.length,n=this.field,i=new Int32Array(t),a=this.coefficients,o=0;o<t;o++)i[o]=n.multiply(a[o],e);return new r(n,i)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new R;if(0===t)return this.field.getZero();for(var n=this.coefficients,i=n.length,a=new Int32Array(i+e),o=this.field,s=0;s<i;s++)a[s]=o.multiply(n[s],t);return new r(o,a)},r.prototype.divide=function(e){if(!this.field.equals(e.field))throw new R("GenericGFPolys do not have same GenericGF field");if(e.isZero())throw new R("Divide by 0");for(var t=this.field,n=t.getZero(),i=this,a=e.getCoefficient(e.getDegree()),o=t.inverse(a);i.getDegree()>=e.getDegree()&&!i.isZero();){var s=i.getDegree()-e.getDegree(),u=t.multiply(i.getCoefficient(i.getDegree()),o),f=e.multiplyByMonomial(s,u),c=t.buildMonomial(s,u);n=n.addOrSubtract(c),i=i.addOrSubtract(f)}return[n,i]},r.prototype.toString=function(){for(var e="",t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);if(0!==n){if(n<0?(e+=" - ",n=-n):e.length>0&&(e+=" + "),0===t||1!==n){var i=this.field.log(n);0===i?e+="1":1===i?e+="a":(e+="a^",e+=i)}0!==t&&(1===t?e+="x":(e+="x^",e+=t))}}return e},r}();const Ue=gi;var xi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),yi=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return xi(e,r),e.kind="ArithmeticException",e}(re);const Ir=yi;var wi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),_i=function(r){function e(t,n,i){var a=r.call(this)||this;a.primitive=t,a.size=n,a.generatorBase=i;for(var o=new Int32Array(n),s=1,u=0;u<n;u++)o[u]=s,(s*=2)>=n&&(s^=t,s&=n-1);a.expTable=o;var f=new Int32Array(n);for(u=0;u<n-1;u++)f[o[u]]=u;return a.logTable=f,a.zero=new Ue(a,Int32Array.from([0])),a.one=new Ue(a,Int32Array.from([1])),a}return wi(e,r),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new R;if(0===n)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new Ue(this,i)},e.prototype.inverse=function(t){if(0===t)throw new Ir;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,n){return 0===t||0===n?0:this.expTable[(this.logTable[t]+this.logTable[n])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+B.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(vt);const Se=_i;var Ai=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ci=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Ai(e,r),e.kind="ReedSolomonException",e}(re);const Tt=Ci;var Ei=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),mi=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Ei(e,r),e.kind="IllegalStateException",e}(re);const Be=mi;var Si=function(){function r(e){this.field=e}return r.prototype.decode=function(e,t){for(var n=this.field,i=new Ue(n,e),a=new Int32Array(t),o=!0,s=0;s<t;s++){var u=i.evaluateAt(n.exp(s+n.getGeneratorBase()));a[a.length-1-s]=u,0!==u&&(o=!1)}if(!o){var f=new Ue(n,a),c=this.runEuclideanAlgorithm(n.buildMonomial(t,1),f,t),d=c[1],l=this.findErrorLocations(c[0]),v=this.findErrorMagnitudes(d,l);for(s=0;s<l.length;s++){var p=e.length-1-n.log(l[s]);if(p<0)throw new Tt("Bad error location");e[p]=Se.addOrSubtract(e[p],v[s])}}},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=this.field,o=e,s=t,u=a.getZero(),f=a.getOne();s.getDegree()>=(n/2|0);){var c=o,h=u;if(u=f,(o=s).isZero())throw new Tt("r_{i-1} was zero");s=c;for(var d=a.getZero(),l=o.getCoefficient(o.getDegree()),v=a.inverse(l);s.getDegree()>=o.getDegree()&&!s.isZero();){var p=s.getDegree()-o.getDegree(),x=a.multiply(s.getCoefficient(s.getDegree()),v);d=d.addOrSubtract(a.buildMonomial(p,x)),s=s.addOrSubtract(o.multiplyByMonomial(p,x))}if(f=d.multiply(u).addOrSubtract(h),s.getDegree()>=o.getDegree())throw new Be("Division algorithm failed to reduce polynomial?")}var w=f.getCoefficient(0);if(0===w)throw new Tt("sigmaTilde(0) was zero");var y=a.inverse(w);return[f.multiplyScalar(y),s.multiplyScalar(y)]},r.prototype.findErrorLocations=function(e){var t=e.getDegree();if(1===t)return Int32Array.from([e.getCoefficient(1)]);for(var n=new Int32Array(t),i=0,a=this.field,o=1;o<a.getSize()&&i<t;o++)0===e.evaluateAt(o)&&(n[i]=a.inverse(o),i++);if(i!==t)throw new Tt("Error locator degree does not match number of roots");return n},r.prototype.findErrorMagnitudes=function(e,t){for(var n=t.length,i=new Int32Array(n),a=this.field,o=0;o<n;o++){for(var s=a.inverse(t[o]),u=1,f=0;f<n;f++)if(o!==f){var c=a.multiply(t[f],s);u=a.multiply(u,1&c?-2&c:1|c)}i[o]=a.multiply(e.evaluateAt(s),a.inverse(u)),0!==a.getGeneratorBase()&&(i[o]=a.multiply(i[o],s))}return i},r}();const Dt=Si;var pe=(()=>(function(r){r[r.UPPER=0]="UPPER",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.DIGIT=3]="DIGIT",r[r.PUNCT=4]="PUNCT",r[r.BINARY=5]="BINARY"}(pe||(pe={})),pe))(),Ii=function(){function r(){}return r.prototype.decode=function(e){this.ddata=e;var t=e.getBits(),n=this.extractBits(t),i=this.correctBits(n),a=r.convertBoolArrayToByteArray(i),o=r.getEncodedData(i),s=new Ot(a,o,null,null);return s.setNumBits(i.length),s},r.highLevelDecode=function(e){return this.getEncodedData(e)},r.getEncodedData=function(e){for(var t=e.length,n=pe.UPPER,i=pe.UPPER,a="",o=0;o<t;)if(i===pe.BINARY){if(t-o<5)break;var s=r.readCode(e,o,5);if(o+=5,0===s){if(t-o<11)break;s=r.readCode(e,o,11)+31,o+=11}for(var u=0;u<s;u++){if(t-o<8){o=t;break}var f=r.readCode(e,o,8);a+=U.castAsNonUtf8Char(f),o+=8}i=n}else{var c=i===pe.DIGIT?4:5;if(t-o<c)break;f=r.readCode(e,o,c),o+=c;var h=r.getCharacter(i,f);h.startsWith("CTRL_")?(n=i,i=r.getTable(h.charAt(5)),"L"===h.charAt(6)&&(n=i)):(a+=h,i=n)}return a},r.getTable=function(e){switch(e){case"L":return pe.LOWER;case"P":return pe.PUNCT;case"M":return pe.MIXED;case"D":return pe.DIGIT;case"B":return pe.BINARY;default:return pe.UPPER}},r.getCharacter=function(e,t){switch(e){case pe.UPPER:return r.UPPER_TABLE[t];case pe.LOWER:return r.LOWER_TABLE[t];case pe.MIXED:return r.MIXED_TABLE[t];case pe.PUNCT:return r.PUNCT_TABLE[t];case pe.DIGIT:return r.DIGIT_TABLE[t];default:throw new Be("Bad table")}},r.prototype.correctBits=function(e){var t,n;this.ddata.getNbLayers()<=2?(n=6,t=Se.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,t=Se.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,t=Se.AZTEC_DATA_10):(n=12,t=Se.AZTEC_DATA_12);var i=this.ddata.getNbDatablocks(),a=e.length/n;if(a<i)throw new O;for(var o=e.length%n,s=new Int32Array(a),u=0;u<a;u++,o+=n)s[u]=r.readCode(e,o,n);try{new Dt(t).decode(s,a-i)}catch(x){throw new O(x)}var c=(1<<n)-1,h=0;for(u=0;u<i;u++){if(0===(d=s[u])||d===c)throw new O;(1===d||d===c-1)&&h++}var l=new Array(i*n-h),v=0;for(u=0;u<i;u++){var d;if(1===(d=s[u])||d===c-1)l.fill(d>1,v,v+n-1),v+=n-1;else for(var p=n-1;p>=0;--p)l[v++]=0!=(d&1<<p)}return l},r.prototype.extractBits=function(e){var t=this.ddata.isCompact(),n=this.ddata.getNbLayers(),i=(t?11:14)+4*n,a=new Int32Array(i),o=new Array(this.totalBitsInLayer(n,t));if(t)for(var s=0;s<a.length;s++)a[s]=s;else{var u=i+1+2*B.truncDivision(B.truncDivision(i,2)-1,15),f=i/2,c=B.truncDivision(u,2);for(s=0;s<f;s++){var h=s+B.truncDivision(s,15);a[f-s-1]=c-h-1,a[f+s]=c+h+1}}s=0;for(var d=0;s<n;s++){for(var l=4*(n-s)+(t?9:12),v=2*s,p=i-1-v,x=0;x<l;x++)for(var w=2*x,y=0;y<2;y++)o[d+w+y]=e.get(a[v+y],a[v+x]),o[d+2*l+w+y]=e.get(a[v+x],a[p-y]),o[d+4*l+w+y]=e.get(a[p-y],a[p-x]),o[d+6*l+w+y]=e.get(a[p-x],a[v+y]);d+=8*l}return o},r.readCode=function(e,t,n){for(var i=0,a=t;a<t+n;a++)i<<=1,e[a]&&(i|=1);return i},r.readByte=function(e,t){var n=e.length-t;return n>=8?r.readCode(e,t,8):r.readCode(e,t,n)<<8-n},r.convertBoolArrayToByteArray=function(e){for(var t=new Uint8Array((e.length+7)/8),n=0;n<t.length;n++)t[n]=r.readByte(e,8*n);return t},r.prototype.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],r.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],r.MIXED_TABLE=["CTRL_PS"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","\t","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~","\x7f","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],r.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],r.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],r}();const Or=Ii;var Oi=function(){function r(){}return r.round=function(e){return isNaN(e)?0:e<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:e>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:e+(e<0?-.5:.5)|0},r.distance=function(e,t,n,i){var a=e-n,o=t-i;return Math.sqrt(a*a+o*o)},r.sum=function(e){for(var t=0,n=0,i=e.length;n!==i;n++)t+=e[n];return t},r}();const G=Oi;var Ti=function(){function r(){}return r.floatToIntBits=function(e){return e},r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r}();const qt=Ti;var Di=function(){function r(e,t){this.x=e,this.y=t}return r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.equals=function(e){return e instanceof r&&(this.x===e.x&&this.y===e.y)},r.prototype.hashCode=function(){return 31*qt.floatToIntBits(this.x)+qt.floatToIntBits(this.y)},r.prototype.toString=function(){return"("+this.x+","+this.y+")"},r.orderBestPatterns=function(e){var a,o,s,t=this.distance(e[0],e[1]),n=this.distance(e[1],e[2]),i=this.distance(e[0],e[2]);if(n>=t&&n>=i?(o=e[0],a=e[1],s=e[2]):i>=n&&i>=t?(o=e[1],a=e[0],s=e[2]):(o=e[2],a=e[0],s=e[1]),this.crossProductZ(a,o,s)<0){var u=a;a=s,s=u}e[0]=a,e[1]=o,e[2]=s},r.distance=function(e,t){return G.distance(e.x,e.y,t.x,t.y)},r.crossProductZ=function(e,t,n){var i=t.x,a=t.y;return(n.x-i)*(e.y-a)-(n.y-a)*(e.x-i)},r}();const b=Di;var Ri=function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r}();const Qt=Ri;var bi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ni=function(r){function e(t,n,i,a,o){var s=r.call(this,t,n)||this;return s.compact=i,s.nbDatablocks=a,s.nbLayers=o,s}return bi(e,r),e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(Qt);const Pi=Ni;var Mi=function(){function r(e,t,n,i){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),null==t&&(t=r.INIT_SIZE),null==n&&(n=e.getWidth()/2|0),null==i&&(i=e.getHeight()/2|0);var a=t/2|0;if(this.leftInit=n-a,this.rightInit=n+a,this.upInit=i-a,this.downInit=i+a,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new E}return r.prototype.detect=function(){for(var e=this.leftInit,t=this.rightInit,n=this.upInit,i=this.downInit,a=!1,o=!0,s=!1,u=!1,f=!1,c=!1,h=!1,d=this.width,l=this.height;o;){o=!1;for(var v=!0;(v||!u)&&t<d;)(v=this.containsBlackPoint(n,i,t,!1))?(t++,o=!0,u=!0):u||t++;if(t>=d){a=!0;break}for(var p=!0;(p||!f)&&i<l;)(p=this.containsBlackPoint(e,t,i,!0))?(i++,o=!0,f=!0):f||i++;if(i>=l){a=!0;break}for(var x=!0;(x||!c)&&e>=0;)(x=this.containsBlackPoint(n,i,e,!1))?(e--,o=!0,c=!0):c||e--;if(e<0){a=!0;break}for(var w=!0;(w||!h)&&n>=0;)(w=this.containsBlackPoint(e,t,n,!0))?(n--,o=!0,h=!0):h||n--;if(n<0){a=!0;break}o&&(s=!0)}if(!a&&s){for(var y=t-e,_=null,C=1;null===_&&C<y;C++)_=this.getBlackPointOnSegment(e,i-C,e+C,i);if(null==_)throw new E;var m=null;for(C=1;null===m&&C<y;C++)m=this.getBlackPointOnSegment(e,n+C,e+C,n);if(null==m)throw new E;var S=null;for(C=1;null===S&&C<y;C++)S=this.getBlackPointOnSegment(t,n+C,t-C,n);if(null==S)throw new E;var I=null;for(C=1;null===I&&C<y;C++)I=this.getBlackPointOnSegment(t,i-C,t-C,i);if(null==I)throw new E;return this.centerEdges(I,_,S,m)}throw new E},r.prototype.getBlackPointOnSegment=function(e,t,n,i){for(var a=G.round(G.distance(e,t,n,i)),o=(n-e)/a,s=(i-t)/a,u=this.image,f=0;f<a;f++){var c=G.round(e+f*o),h=G.round(t+f*s);if(u.get(c,h))return new b(c,h)}return null},r.prototype.centerEdges=function(e,t,n,i){var a=e.getX(),o=e.getY(),s=t.getX(),u=t.getY(),f=n.getX(),c=n.getY(),h=i.getX(),d=i.getY(),l=r.CORR;return a<this.width/2?[new b(h-l,d+l),new b(s+l,u+l),new b(f-l,c-l),new b(a+l,o-l)]:[new b(h+l,d+l),new b(s+l,u-l),new b(f-l,c+l),new b(a-l,o-l)]},r.prototype.containsBlackPoint=function(e,t,n,i){var a=this.image;if(i){for(var o=e;o<=t;o++)if(a.get(o,n))return!0}else for(var s=e;s<=t;s++)if(a.get(n,s))return!0;return!1},r.INIT_SIZE=10,r.CORR=1,r}();const Jt=Mi;var Bi=function(){function r(){}return r.checkAndNudgePoints=function(e,t){for(var n=e.getWidth(),i=e.getHeight(),a=!0,o=0;o<t.length&&a;o+=2){var s=Math.floor(t[o]),u=Math.floor(t[o+1]);if(s<-1||s>n||u<-1||u>i)throw new E;a=!1,-1===s?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),-1===u?(t[o+1]=0,a=!0):u===i&&(t[o+1]=i-1,a=!0)}for(a=!0,o=t.length-2;o>=0&&a;o-=2){if(s=Math.floor(t[o]),u=Math.floor(t[o+1]),s<-1||s>n||u<-1||u>i)throw new E;a=!1,-1===s?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),-1===u?(t[o+1]=0,a=!0):u===i&&(t[o+1]=i-1,a=!0)}},r}();const Tr=Bi;var Fi=function(){function r(e,t,n,i,a,o,s,u,f){this.a11=e,this.a21=t,this.a31=n,this.a12=i,this.a22=a,this.a32=o,this.a13=s,this.a23=u,this.a33=f}return r.quadrilateralToQuadrilateral=function(e,t,n,i,a,o,s,u,f,c,h,d,l,v,p,x){var w=r.quadrilateralToSquare(e,t,n,i,a,o,s,u);return r.squareToQuadrilateral(f,c,h,d,l,v,p,x).times(w)},r.prototype.transformPoints=function(e){for(var t=e.length,n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,u=this.a23,f=this.a31,c=this.a32,h=this.a33,d=0;d<t;d+=2){var l=e[d],v=e[d+1],p=a*l+u*v+h;e[d]=(n*l+o*v+f)/p,e[d+1]=(i*l+s*v+c)/p}},r.prototype.transformPointsWithValues=function(e,t){for(var n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,u=this.a23,f=this.a31,c=this.a32,h=this.a33,d=e.length,l=0;l<d;l++){var v=e[l],p=t[l],x=a*v+u*p+h;e[l]=(n*v+o*p+f)/x,t[l]=(i*v+s*p+c)/x}},r.squareToQuadrilateral=function(e,t,n,i,a,o,s,u){var f=e-n+a-s,c=t-i+o-u;if(0===f&&0===c)return new r(n-e,a-n,e,i-t,o-i,t,0,0,1);var h=n-a,d=s-a,l=i-o,v=u-o,p=h*v-d*l,x=(f*v-d*c)/p,w=(h*c-f*l)/p;return new r(n-e+x*n,s-e+w*s,e,i-t+x*i,u-t+w*u,t,x,w,1)},r.quadrilateralToSquare=function(e,t,n,i,a,o,s,u){return r.squareToQuadrilateral(e,t,n,i,a,o,s,u).buildAdjoint()},r.prototype.buildAdjoint=function(){return new r(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},r.prototype.times=function(e){return new r(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},r}();const Dr=Fi;var Li=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ki=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Li(e,r),e.prototype.sampleGrid=function(t,n,i,a,o,s,u,f,c,h,d,l,v,p,x,w,y,_,C){var m=Dr.quadrilateralToQuadrilateral(a,o,s,u,f,c,h,d,l,v,p,x,w,y,_,C);return this.sampleGridWithTransform(t,n,i,m)},e.prototype.sampleGridWithTransform=function(t,n,i,a){if(n<=0||i<=0)throw new E;for(var o=new me(n,i),s=new Float32Array(2*n),u=0;u<i;u++){for(var f=s.length,c=u+.5,h=0;h<f;h+=2)s[h]=h/2+.5,s[h+1]=c;a.transformPoints(s),Tr.checkAndNudgePoints(t,s);try{for(h=0;h<f;h+=2)t.get(Math.floor(s[h]),Math.floor(s[h+1]))&&o.set(h/2,u)}catch{throw new E}}return o},e}(Tr);const Ui=ki;var Vi=function(){function r(){}return r.setGridSampler=function(e){r.gridSampler=e},r.getInstance=function(){return r.gridSampler},r.gridSampler=new Ui,r}();const $t=Vi;var Ie=function(){function r(e,t){this.x=e,this.y=t}return r.prototype.toResultPoint=function(){return new b(this.getX(),this.getY())},r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r}(),Hi=function(){function r(e){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=e}return r.prototype.detect=function(){return this.detectMirror(!1)},r.prototype.detectMirror=function(e){var t=this.getMatrixCenter(),n=this.getBullsEyeCorners(t);if(e){var i=n[0];n[0]=n[2],n[2]=i}this.extractParameters(n);var a=this.sampleGrid(this.image,n[this.shift%4],n[(this.shift+1)%4],n[(this.shift+2)%4],n[(this.shift+3)%4]),o=this.getMatrixCornerPoints(n);return new Pi(a,o,this.compact,this.nbDataBlocks,this.nbLayers)},r.prototype.extractParameters=function(e){if(!(this.isValidPoint(e[0])&&this.isValidPoint(e[1])&&this.isValidPoint(e[2])&&this.isValidPoint(e[3])))throw new E;var t=2*this.nbCenterLayers,n=new Int32Array([this.sampleLine(e[0],e[1],t),this.sampleLine(e[1],e[2],t),this.sampleLine(e[2],e[3],t),this.sampleLine(e[3],e[0],t)]);this.shift=this.getRotation(n,t);for(var i=0,a=0;a<4;a++){var o=n[(this.shift+a)%4];this.compact?(i<<=7,i+=o>>1&127):(i<<=10,i+=(o>>2&992)+(o>>1&31))}var s=this.getCorrectedParameterData(i,this.compact);this.compact?(this.nbLayers=1+(s>>6),this.nbDataBlocks=1+(63&s)):(this.nbLayers=1+(s>>11),this.nbDataBlocks=1+(2047&s))},r.prototype.getRotation=function(e,t){var n=0;e.forEach(function(a,o,s){n=(a>>t-2<<1)+(1&a)+(n<<3)}),n=((1&n)<<11)+(n>>1);for(var i=0;i<4;i++)if(B.bitCount(n^this.EXPECTED_CORNER_BITS[i])<=2)return i;throw new E},r.prototype.getCorrectedParameterData=function(e,t){var n,i;t?(n=7,i=2):(n=10,i=4);for(var a=n-i,o=new Int32Array(n),s=n-1;s>=0;--s)o[s]=15&e,e>>=4;try{new Dt(Se.AZTEC_PARAM).decode(o,a)}catch{throw new E}var f=0;for(s=0;s<i;s++)f=(f<<4)+o[s];return f},r.prototype.getBullsEyeCorners=function(e){var t=e,n=e,i=e,a=e,o=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var s=this.getFirstDifferent(t,o,1,-1),u=this.getFirstDifferent(n,o,1,1),f=this.getFirstDifferent(i,o,-1,1),c=this.getFirstDifferent(a,o,-1,-1);if(this.nbCenterLayers>2){var h=this.distancePoint(c,s)*this.nbCenterLayers/(this.distancePoint(a,t)*(this.nbCenterLayers+2));if(h<.75||h>1.25||!this.isWhiteOrBlackRectangle(s,u,f,c))break}t=s,n=u,i=f,a=c,o=!o}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new E;this.compact=5===this.nbCenterLayers;var d=new b(t.getX()+.5,t.getY()-.5),l=new b(n.getX()+.5,n.getY()+.5),v=new b(i.getX()-.5,i.getY()+.5),p=new b(a.getX()-.5,a.getY()-.5);return this.expandSquare([d,l,v,p],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},r.prototype.getMatrixCenter=function(){var e,t,n,i;try{e=(a=new Jt(this.image).detect())[0],t=a[1],n=a[2],i=a[3]}catch{var o=this.image.getWidth()/2,s=this.image.getHeight()/2;e=this.getFirstDifferent(new Ie(o+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new Ie(o+7,s+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new Ie(o-7,s+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new Ie(o-7,s-7),!1,-1,-1).toResultPoint()}var u=G.round((e.getX()+i.getX()+t.getX()+n.getX())/4),f=G.round((e.getY()+i.getY()+t.getY()+n.getY())/4);try{var a;e=(a=new Jt(this.image,15,u,f).detect())[0],t=a[1],n=a[2],i=a[3]}catch{e=this.getFirstDifferent(new Ie(u+7,f-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new Ie(u+7,f+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new Ie(u-7,f+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new Ie(u-7,f-7),!1,-1,-1).toResultPoint()}return u=G.round((e.getX()+i.getX()+t.getX()+n.getX())/4),f=G.round((e.getY()+i.getY()+t.getY()+n.getY())/4),new Ie(u,f)},r.prototype.getMatrixCornerPoints=function(e){return this.expandSquare(e,2*this.nbCenterLayers,this.getDimension())},r.prototype.sampleGrid=function(e,t,n,i,a){var o=$t.getInstance(),s=this.getDimension(),u=s/2-this.nbCenterLayers,f=s/2+this.nbCenterLayers;return o.sampleGrid(e,s,s,u,u,f,u,f,f,u,f,t.getX(),t.getY(),n.getX(),n.getY(),i.getX(),i.getY(),a.getX(),a.getY())},r.prototype.sampleLine=function(e,t,n){for(var i=0,a=this.distanceResultPoint(e,t),o=a/n,s=e.getX(),u=e.getY(),f=o*(t.getX()-e.getX())/a,c=o*(t.getY()-e.getY())/a,h=0;h<n;h++)this.image.get(G.round(s+h*f),G.round(u+h*c))&&(i|=1<<n-h-1);return i},r.prototype.isWhiteOrBlackRectangle=function(e,t,n,i){e=new Ie(e.getX()-3,e.getY()+3),t=new Ie(t.getX()-3,t.getY()-3),n=new Ie(n.getX()+3,n.getY()-3),i=new Ie(i.getX()+3,i.getY()+3);var o=this.getColor(i,e);if(0===o)return!1;var s=this.getColor(e,t);return s===o&&(s=this.getColor(t,n))===o&&(s=this.getColor(n,i))===o},r.prototype.getColor=function(e,t){for(var n=this.distancePoint(e,t),i=(t.getX()-e.getX())/n,a=(t.getY()-e.getY())/n,o=0,s=e.getX(),u=e.getY(),f=this.image.get(e.getX(),e.getY()),c=Math.ceil(n),h=0;h<c;h++)u+=a,this.image.get(G.round(s+=i),G.round(u))!==f&&o++;var d=o/n;return d>.1&&d<.9?0:d<=.1===f?1:-1},r.prototype.getFirstDifferent=function(e,t,n,i){for(var a=e.getX()+n,o=e.getY()+i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n,o+=i;for(a-=n,o-=i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n;for(a-=n;this.isValid(a,o)&&this.image.get(a,o)===t;)o+=i;return new Ie(a,o-=i)},r.prototype.expandSquare=function(e,t,n){var i=n/(2*t),a=e[0].getX()-e[2].getX(),o=e[0].getY()-e[2].getY(),s=(e[0].getX()+e[2].getX())/2,u=(e[0].getY()+e[2].getY())/2,f=new b(s+i*a,u+i*o),c=new b(s-i*a,u-i*o);return a=e[1].getX()-e[3].getX(),o=e[1].getY()-e[3].getY(),s=(e[1].getX()+e[3].getX())/2,u=(e[1].getY()+e[3].getY())/2,[f,new b(s+i*a,u+i*o),c,new b(s-i*a,u-i*o)]},r.prototype.isValid=function(e,t){return e>=0&&e<this.image.getWidth()&&t>0&&t<this.image.getHeight()},r.prototype.isValidPoint=function(e){var t=G.round(e.getX()),n=G.round(e.getY());return this.isValid(t,n)},r.prototype.distancePoint=function(e,t){return G.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.distanceResultPoint=function(e,t){return G.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(B.truncDivision(this.nbLayers-4,8)+1)+15},r}();const Gi=Hi;var Wi=function(){function r(){}return r.prototype.decode=function(e,t){void 0===t&&(t=null);var n=null,i=new Gi(e.getBlackMatrix()),a=null,o=null;try{a=(s=i.detectMirror(!1)).getPoints(),this.reportFoundResultPoints(t,a),o=(new Or).decode(s)}catch(h){n=h}if(null==o)try{var s;a=(s=i.detectMirror(!0)).getPoints(),this.reportFoundResultPoints(t,a),o=(new Or).decode(s)}catch(h){throw n??h}var u=new we(o.getText(),o.getRawBytes(),o.getNumBits(),a,N.AZTEC,q.currentTimeMillis()),f=o.getByteSegments();null!=f&&u.putMetadata(xe.BYTE_SEGMENTS,f);var c=o.getECLevel();return null!=c&&u.putMetadata(xe.ERROR_CORRECTION_LEVEL,c),u},r.prototype.reportFoundResultPoints=function(e,t){if(null!=e){var n=e.get(K.NEED_RESULT_POINT_CALLBACK);null!=n&&t.forEach(function(i,a,o){n.foundPossibleResultPoint(i)})}},r.prototype.reset=function(){},r}();const Rt=Wi;var Xi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),zi=(function(r){Xi(function e(t){return void 0===t&&(t=500),r.call(this,new Rt,t)||this},r)}(nt),function(){function r(){}return r.prototype.decode=function(e,t){try{return this.doDecode(e,t)}catch{if(t&&!0===t.get(K.TRY_HARDER)&&e.isRotateSupported()){var i=e.rotateCounterClockwise(),a=this.doDecode(i,t),o=a.getResultMetadata(),s=270;null!==o&&!0===o.get(xe.ORIENTATION)&&(s+=o.get(xe.ORIENTATION)%360),a.putMetadata(xe.ORIENTATION,s);var u=a.getResultPoints();if(null!==u)for(var f=i.getHeight(),c=0;c<u.length;c++)u[c]=new b(f-u[c].getY()-1,u[c].getX());return a}throw new E}},r.prototype.reset=function(){},r.prototype.doDecode=function(e,t){var u,n=e.getWidth(),i=e.getHeight(),a=new ve(n),o=t&&!0===t.get(K.TRY_HARDER),s=Math.max(1,i>>(o?8:5));u=o?i:15;for(var f=Math.trunc(i/2),c=0;c<u;c++){var h=Math.trunc((c+1)/2),l=f+s*(0==(1&c)?h:-h);if(l<0||l>=i)break;try{a=e.getBlackRow(l,a)}catch{continue}for(var v=function(y){if(1===y&&(a.reverse(),t&&!0===t.get(K.NEED_RESULT_POINT_CALLBACK))){var _=new Map;t.forEach(function(S,I){return _.set(I,S)}),_.delete(K.NEED_RESULT_POINT_CALLBACK),t=_}try{var C=p.decodeRow(l,a,t);if(1===y){C.putMetadata(xe.ORIENTATION,180);var m=C.getResultPoints();null!==m&&(m[0]=new b(n-m[0].getX()-1,m[0].getY()),m[1]=new b(n-m[1].getX()-1,m[1].getY()))}return{value:C}}catch{}},p=this,x=0;x<2;x++){var w=v(x);if("object"==typeof w)return w.value}}throw new E},r.recordPattern=function(e,t,n){for(var i=n.length,a=0;a<i;a++)n[a]=0;var o=e.getSize();if(t>=o)throw new E;for(var s=!e.get(t),u=0,f=t;f<o;){if(e.get(f)!==s)n[u]++;else{if(++u===i)break;n[u]=1,s=!s}f++}if(u!==i&&(u!==i-1||f!==o))throw new E},r.recordPatternInReverse=function(e,t,n){for(var i=n.length,a=e.get(t);t>0&&i>=0;)e.get(--t)!==a&&(i--,a=!a);if(i>=0)throw new E;r.recordPattern(e,t+1,n)},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return Number.POSITIVE_INFINITY;var u=a/o;n*=u;for(var f=0,c=0;c<i;c++){var h=e[c],d=t[c]*u,l=h>d?h-d:d-h;if(l>n)return Number.POSITIVE_INFINITY;f+=l}return f/a},r}());const ce=zi;var Zi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ji=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Zi(e,r),e.findStartPattern=function(t){for(var n=t.getSize(),i=t.getNextSet(0),a=0,o=Int32Array.from([0,0,0,0,0,0]),s=i,u=!1,c=i;c<n;c++)if(t.get(c)!==u)o[a]++;else{if(5===a){for(var h=e.MAX_AVG_VARIANCE,d=-1,l=e.CODE_START_A;l<=e.CODE_START_C;l++){var v=ce.patternMatchVariance(o,e.CODE_PATTERNS[l],e.MAX_INDIVIDUAL_VARIANCE);v<h&&(h=v,d=l)}if(d>=0&&t.isRange(Math.max(0,s-(c-s)/2),s,!1))return Int32Array.from([s,c,d]);s+=o[0]+o[1],(o=o.slice(2,o.length))[a-1]=0,o[a]=0,a--}else a++;o[a]=1,u=!u}throw new E},e.decodeCode=function(t,n,i){ce.recordPattern(t,i,n);for(var a=e.MAX_AVG_VARIANCE,o=-1,s=0;s<e.CODE_PATTERNS.length;s++){var f=this.patternMatchVariance(n,e.CODE_PATTERNS[s],e.MAX_INDIVIDUAL_VARIANCE);f<a&&(a=f,o=s)}if(o>=0)return o;throw new E},e.prototype.decodeRow=function(t,n,i){var c,a=i&&!0===i.get(K.ASSUME_GS1),o=e.findStartPattern(n),s=o[2],u=0,f=new Uint8Array(20);switch(f[u++]=s,s){case e.CODE_START_A:c=e.CODE_CODE_A;break;case e.CODE_START_B:c=e.CODE_CODE_B;break;case e.CODE_START_C:c=e.CODE_CODE_C;break;default:throw new O}for(var h=!1,d=!1,l="",v=o[0],p=o[1],x=Int32Array.from([0,0,0,0,0,0]),w=0,y=0,_=s,C=0,m=!0,S=!1,I=!1;!h;){var D=d;switch(d=!1,w=y,y=e.decodeCode(n,x,p),f[u++]=y,y!==e.CODE_STOP&&(m=!0),y!==e.CODE_STOP&&(_+=++C*y),v=p,p+=x.reduce(function(Ar,Cr){return Ar+Cr},0),y){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new O}switch(c){case e.CODE_CODE_A:if(y<64)l+=String.fromCharCode(I===S?" ".charCodeAt(0)+y:" ".charCodeAt(0)+y+128),I=!1;else if(y<96)l+=String.fromCharCode(I===S?y-64:y+64),I=!1;else switch(y!==e.CODE_STOP&&(m=!1),y){case e.CODE_FNC_1:a&&(l+=0===l.length?"]C1":String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!S&&I?(S=!0,I=!1):S&&I?(S=!1,I=!1):I=!0;break;case e.CODE_SHIFT:d=!0,c=e.CODE_CODE_B;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:h=!0}break;case e.CODE_CODE_B:if(y<96)l+=String.fromCharCode(I===S?" ".charCodeAt(0)+y:" ".charCodeAt(0)+y+128),I=!1;else switch(y!==e.CODE_STOP&&(m=!1),y){case e.CODE_FNC_1:a&&(l+=0===l.length?"]C1":String.fromCharCode(29));break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!S&&I?(S=!0,I=!1):S&&I?(S=!1,I=!1):I=!0;break;case e.CODE_SHIFT:d=!0,c=e.CODE_CODE_A;break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:h=!0}break;case e.CODE_CODE_C:if(y<100)y<10&&(l+="0"),l+=y;else switch(y!==e.CODE_STOP&&(m=!1),y){case e.CODE_FNC_1:a&&(l+=0===l.length?"]C1":String.fromCharCode(29));break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_STOP:h=!0}}D&&(c=c===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var M=p-v;if(p=n.getNextUnset(p),!n.isRange(p,Math.min(n.getSize(),p+(p-v)/2),!1))throw new E;if((_-=C*w)%103!==w)throw new ae;var P=l.length;if(0===P)throw new E;P>0&&m&&(l=l.substring(0,c===e.CODE_CODE_C?P-2:P-1));for(var ie=(o[1]+o[0])/2,H=v+M/2,te=f.length,Ne=new Uint8Array(te),Xe=0;Xe<te;Xe++)Ne[Xe]=f[Xe];var _r=[new b(ie,t),new b(H,t)];return new we(l,Ne,0,_r,N.CODE_128,(new Date).getTime())},e.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e}(ce);const Rr=ji;var Yi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),er=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ki=function(r){function e(t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=r.call(this)||this;return i.usingCheckDigit=t,i.extendedMode=n,i.decodeRowResult="",i.counters=new Int32Array(9),i}return Yi(e,r),e.prototype.decodeRow=function(t,n,i){var a,o,s,u,f=this.counters;f.fill(0),this.decodeRowResult="";var l,v,c=e.findAsteriskPattern(n,f),h=n.getNextSet(c[1]),d=n.getSize();do{e.recordPattern(n,h,f);var p=e.toNarrowWidePattern(f);if(p<0)throw new E;l=e.patternToChar(p),this.decodeRowResult+=l,v=h;try{for(var x=(a=void 0,er(f)),w=x.next();!w.done;w=x.next())h+=w.value}catch(te){a={error:te}}finally{try{w&&!w.done&&(o=x.return)&&o.call(x)}finally{if(a)throw a.error}}h=n.getNextSet(h)}while("*"!==l);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var P,_=0;try{for(var C=er(f),m=C.next();!m.done;m=C.next())_+=m.value}catch(te){s={error:te}}finally{try{m&&!m.done&&(u=C.return)&&u.call(C)}finally{if(s)throw s.error}}if(h!==d&&2*(h-v-_)<_)throw new E;if(this.usingCheckDigit){for(var I=this.decodeRowResult.length-1,D=0,M=0;M<I;M++)D+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(M));if(this.decodeRowResult.charAt(I)!==e.ALPHABET_STRING.charAt(D%43))throw new ae;this.decodeRowResult=this.decodeRowResult.substring(0,I)}if(0===this.decodeRowResult.length)throw new E;P=this.extendedMode?e.decodeExtended(this.decodeRowResult):this.decodeRowResult;var H=v+_/2;return new we(P,null,0,[new b((c[1]+c[0])/2,t),new b(H,t)],N.CODE_39,(new Date).getTime())},e.findAsteriskPattern=function(t,n){for(var i=t.getSize(),a=t.getNextSet(0),o=0,s=a,u=!1,f=n.length,c=a;c<i;c++)if(t.get(c)!==u)n[o]++;else{if(o===f-1){if(this.toNarrowWidePattern(n)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,s-Math.floor((c-s)/2)),s,!1))return[s,c];s+=n[0]+n[1],n.copyWithin(0,2,2+o-1),n[o-1]=0,n[o]=0,o--}else o++;n[o]=1,u=!u}throw new E},e.toNarrowWidePattern=function(t){var n,i,s,a=t.length,o=0;do{var u=2147483647;try{for(var f=(n=void 0,er(t)),c=f.next();!c.done;c=f.next())(h=c.value)<u&&h>o&&(u=h)}catch(p){n={error:p}}finally{try{c&&!c.done&&(i=f.return)&&i.call(f)}finally{if(n)throw n.error}}o=u,s=0;for(var d=0,l=0,v=0;v<a;v++)(h=t[v])>o&&(l|=1<<a-1-v,s++,d+=h);if(3===s){for(v=0;v<a&&s>0;v++){var h;if((h=t[v])>o&&(s--,2*h>=d))return-1}return l}}while(s>3);return-1},e.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);if(t===e.ASTERISK_ENCODING)return"*";throw new E},e.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if("+"===o||"$"===o||"%"===o||"/"===o){var s=t.charAt(a+1),u="\0";switch(o){case"+":if(!(s>="A"&&s<="Z"))throw new O;u=String.fromCharCode(s.charCodeAt(0)+32);break;case"$":if(!(s>="A"&&s<="Z"))throw new O;u=String.fromCharCode(s.charCodeAt(0)-64);break;case"%":if(s>="A"&&s<="E")u=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")u=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")u=String.fromCharCode(s.charCodeAt(0)+43);else if("U"===s)u="\0";else if("V"===s)u="@";else if("W"===s)u="`";else{if("X"!==s&&"Y"!==s&&"Z"!==s)throw new O;u="\x7f"}break;case"/":if(s>="A"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)-32);else{if("Z"!==s)throw new O;u=":"}}i+=u,a++}else i+=o}return i},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e}(ce);const br=Ki;var qi=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),tr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Qi=function(r){function e(){var t=r.call(this)||this;return t.decodeRowResult="",t.counters=new Int32Array(6),t}return qi(e,r),e.prototype.decodeRow=function(t,n,i){var a,o,s,u,l,v,f=this.findAsteriskPattern(n),c=n.getNextSet(f[1]),h=n.getSize(),d=this.counters;d.fill(0),this.decodeRowResult="";do{e.recordPattern(n,c,d);var p=this.toPattern(d);if(p<0)throw new E;l=this.patternToChar(p),this.decodeRowResult+=l,v=c;try{for(var x=(a=void 0,tr(d)),w=x.next();!w.done;w=x.next())c+=w.value}catch(M){a={error:M}}finally{try{w&&!w.done&&(o=x.return)&&o.call(x)}finally{if(a)throw a.error}}c=n.getNextSet(c)}while("*"!==l);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var _=0;try{for(var C=tr(d),m=C.next();!m.done;m=C.next())_+=m.value}catch(M){s={error:M}}finally{try{m&&!m.done&&(u=C.return)&&u.call(C)}finally{if(s)throw s.error}}if(c===h||!n.get(c))throw new E;if(this.decodeRowResult.length<2)throw new E;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var S=this.decodeExtended(this.decodeRowResult),D=v+_/2;return new we(S,null,0,[new b((f[1]+f[0])/2,t),new b(D,t)],N.CODE_93,(new Date).getTime())},e.prototype.findAsteriskPattern=function(t){var n=t.getSize(),i=t.getNextSet(0);this.counters.fill(0);for(var a=this.counters,o=i,s=!1,u=a.length,f=0,c=i;c<n;c++)if(t.get(c)!==s)a[f]++;else{if(f===u-1){if(this.toPattern(a)===e.ASTERISK_ENCODING)return new Int32Array([o,c]);o+=a[0]+a[1],a.copyWithin(0,2,2+f-1),a[f-1]=0,a[f]=0,f--}else f++;a[f]=1,s=!s}throw new E},e.prototype.toPattern=function(t){var n,i,a=0;try{for(var o=tr(t),s=o.next();!s.done;s=o.next())a+=s.value}catch(v){n={error:v}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}for(var f=0,c=t.length,h=0;h<c;h++){var d=Math.round(9*t[h]/a);if(d<1||d>4)return-1;if(1&h)f<<=d;else for(var l=0;l<d;l++)f=f<<1|1}return f},e.prototype.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);throw new E},e.prototype.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if(o>="a"&&o<="d"){if(a>=n-1)throw new O;var s=t.charAt(a+1),u="\0";switch(o){case"d":if(!(s>="A"&&s<="Z"))throw new O;u=String.fromCharCode(s.charCodeAt(0)+32);break;case"a":if(!(s>="A"&&s<="Z"))throw new O;u=String.fromCharCode(s.charCodeAt(0)-64);break;case"b":if(s>="A"&&s<="E")u=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")u=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")u=String.fromCharCode(s.charCodeAt(0)+43);else if("U"===s)u="\0";else if("V"===s)u="@";else if("W"===s)u="`";else{if(!(s>="X"&&s<="Z"))throw new O;u=String.fromCharCode(127)}break;case"c":if(s>="A"&&s<="O")u=String.fromCharCode(s.charCodeAt(0)-32);else{if("Z"!==s)throw new O;u=":"}}i+=u,a++}else i+=o}return i},e.prototype.checkChecksums=function(t){var n=t.length;this.checkOneChecksum(t,n-2,20),this.checkOneChecksum(t,n-1,15)},e.prototype.checkOneChecksum=function(t,n,i){for(var a=1,o=0,s=n-1;s>=0;s--)o+=a*e.ALPHABET_STRING.indexOf(t.charAt(s)),++a>i&&(a=1);if(t.charAt(n)!==e.ALPHABET_STRING[o%47])throw new ae},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",e.ASTERISK_ENCODING=(e.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350])[47],e}(ce);const Nr=Qi;var Ji=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ea=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.narrowLineWidth=-1,t}return Ji(e,r),e.prototype.decodeRow=function(t,n,i){var a,o,s=this.decodeStart(n),u=this.decodeEnd(n),f=new F;e.decodeMiddle(n,s[1],u[0],f);var c=f.toString(),h=null;null!=i&&(h=i.get(K.ALLOWED_LENGTHS)),null==h&&(h=e.DEFAULT_ALLOWED_LENGTHS);var d=c.length,l=!1,v=0;try{for(var p=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(h),x=p.next();!x.done;x=p.next()){var w=x.value;if(d===w){l=!0;break}w>v&&(v=w)}}catch(C){a={error:C}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}if(!l&&d>v&&(l=!0),!l)throw new O;var y=[new b(s[1],t),new b(u[0],t)];return new we(c,null,0,y,N.ITF,(new Date).getTime())},e.decodeMiddle=function(t,n,i,a){var o=new Int32Array(10),s=new Int32Array(5),u=new Int32Array(5);for(o.fill(0),s.fill(0),u.fill(0);n<i;){ce.recordPattern(t,n,o);for(var f=0;f<5;f++){var c=2*f;s[f]=o[c],u[f]=o[c+1]}var h=e.decodeDigit(s);a.append(h.toString()),h=this.decodeDigit(u),a.append(h.toString()),o.forEach(function(d){n+=d})}},e.prototype.decodeStart=function(t){var n=e.skipWhiteSpace(t),i=e.findGuardPattern(t,n,e.START_PATTERN);return this.narrowLineWidth=(i[1]-i[0])/4,this.validateQuietZone(t,i[0]),i},e.prototype.validateQuietZone=function(t,n){var i=10*this.narrowLineWidth;i=i<n?i:n;for(var a=n-1;i>0&&a>=0&&!t.get(a);a--)i--;if(0!==i)throw new E},e.skipWhiteSpace=function(t){var n=t.getSize(),i=t.getNextSet(0);if(i===n)throw new E;return i},e.prototype.decodeEnd=function(t){t.reverse();try{var n=e.skipWhiteSpace(t),i=void 0;try{i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[0])}catch(o){o instanceof E&&(i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[1]))}this.validateQuietZone(t,i[0]);var a=i[0];return i[0]=t.getSize()-i[1],i[1]=t.getSize()-a,i}finally{t.reverse()}},e.findGuardPattern=function(t,n,i){var a=i.length,o=new Int32Array(a),s=t.getSize(),u=!1,f=0,c=n;o.fill(0);for(var h=n;h<s;h++)if(t.get(h)!==u)o[f]++;else{if(f===a-1){if(ce.patternMatchVariance(o,i,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[c,h];c+=o[0]+o[1],q.arraycopy(o,2,o,0,f-1),o[f-1]=0,o[f]=0,f--}else f++;o[f]=1,u=!u}throw new E},e.decodeDigit=function(t){for(var n=e.MAX_AVG_VARIANCE,i=-1,a=e.PATTERNS.length,o=0;o<a;o++){var u=ce.patternMatchVariance(t,e.PATTERNS[o],e.MAX_INDIVIDUAL_VARIANCE);u<n?(n=u,i=o):u===n&&(i=-1)}if(i>=0)return i%10;throw new E},e.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=Int32Array.from([1,1,1,1]),e.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],e}(ce);const Pr=ea;var ta=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ra=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.decodeRowStringBuffer="",t}return ta(e,r),e.findStartGuardPattern=function(t){for(var i,n=!1,a=0,o=Int32Array.from([0,0,0]);!n;){o=Int32Array.from([0,0,0]);var s=(i=e.findGuardPattern(t,a,!1,this.START_END_PATTERN,o))[0],u=s-((a=i[1])-s);u>=0&&(n=t.isRange(u,s,!1))}return i},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(0===n)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){if((o=t.charAt(a).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new O;i+=o}for(i*=3,a=n-2;a>=0;a-=2){var o;if((o=t.charAt(a).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new O;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,n,i,a){return this.findGuardPattern(t,n,i,a,new Int32Array(a.length))},e.findGuardPattern=function(t,n,i,a,o){for(var s=t.getSize(),u=0,f=n=i?t.getNextUnset(n):t.getNextSet(n),c=a.length,h=i,d=n;d<s;d++)if(t.get(d)!==h)o[u]++;else{if(u===c-1){if(ce.patternMatchVariance(o,a,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([f,d]);f+=o[0]+o[1];for(var l=o.slice(2,o.length),v=0;v<u-1;v++)o[v]=l[v];o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,h=!h}throw new E},e.decodeDigit=function(t,n,i,a){this.recordPattern(t,i,n);for(var o=this.MAX_AVG_VARIANCE,s=-1,u=a.length,f=0;f<u;f++){var h=ce.patternMatchVariance(n,a[f],e.MAX_INDIVIDUAL_VARIANCE);h<o&&(o=h,s=f)}if(s>=0)return s;throw new E},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e}(ce);const it=ra;var na=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ia=function(){function r(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),u=[new b((n[0]+n[1])/2,e),new b(a,e)],f=new we(o,null,0,u,N.UPC_EAN_EXTENSION,(new Date).getTime());return null!=s&&f.putAllMetadata(s),f},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),u=t[1],f=0,c=0;c<5&&u<s;c++){var h=it.decodeDigit(e,o,u,it.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+h%10);try{for(var d=(i=void 0,na(o)),l=d.next();!l.done;l=d.next())u+=l.value}catch(x){i={error:x}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}h>=10&&(f|=1<<4-c),4!==c&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(5!==n.length)throw new E;var p=this.determineCheckDigit(f);if(r.extensionChecksum(n.toString())!==p)throw new E;return u},r.extensionChecksum=function(e){for(var t=e.length,n=0,i=t-2;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-"0".charCodeAt(0);for(n*=3,i=t-1;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-"0".charCodeAt(0);return(n*=3)%10},r.prototype.determineCheckDigit=function(e){for(var t=0;t<10;t++)if(e===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new E},r.parseExtensionString=function(e){if(5!==e.length)return null;var t=r.parseExtension5String(e);return null==t?null:new Map([[xe.SUGGESTED_PRICE,t]])},r.parseExtension5String=function(e){var t;switch(e.charAt(0)){case"0":t="\xa3";break;case"5":t="$";break;case"9":switch(e){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t=""}var n=parseInt(e.substring(1)),a=n%100;return t+(n/100).toString()+"."+(a<10?"0"+a:a.toString())},r}();const aa=ia;var oa=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},sa=function(){function r(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),u=[new b((n[0]+n[1])/2,e),new b(a,e)],f=new we(o,null,0,u,N.UPC_EAN_EXTENSION,(new Date).getTime());return null!=s&&f.putAllMetadata(s),f},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),u=t[1],f=0,c=0;c<2&&u<s;c++){var h=it.decodeDigit(e,o,u,it.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+h%10);try{for(var d=(i=void 0,oa(o)),l=d.next();!l.done;l=d.next())u+=l.value}catch(p){i={error:p}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}h>=10&&(f|=1<<1-c),1!==c&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(2!==n.length)throw new E;if(parseInt(n.toString())%4!==f)throw new E;return u},r.parseExtensionString=function(e){return 2!==e.length?null:new Map([[xe.ISSUE_NUMBER,parseInt(e)]])},r}();const ua=sa;var fa=function(){function r(){}return r.decodeRow=function(e,t,n){var i=it.findGuardPattern(t,n,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return(new aa).decodeRow(e,t,i)}catch{return(new ua).decodeRow(e,t,i)}},r.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),r}();const ca=fa;var ha=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),da=function(r){function e(){var t=r.call(this)||this;t.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map(function(s){return Int32Array.from(s)});for(var n=10;n<20;n++){for(var i=e.L_PATTERNS[n-10],a=new Int32Array(i.length),o=0;o<i.length;o++)a[o]=i[i.length-o-1];e.L_AND_G_PATTERNS[n]=a}return t}return ha(e,r),e.prototype.decodeRow=function(t,n,i){var a=e.findStartGuardPattern(n),o=null==i?null:i.get(K.NEED_RESULT_POINT_CALLBACK);if(null!=o){var s=new b((a[0]+a[1])/2,t);o.foundPossibleResultPoint(s)}var u=this.decodeMiddle(n,a,this.decodeRowStringBuffer),f=u.rowOffset,c=u.resultString;if(null!=o){var h=new b(f,t);o.foundPossibleResultPoint(h)}var d=e.decodeEnd(n,f);if(null!=o){var l=new b((d[0]+d[1])/2,t);o.foundPossibleResultPoint(l)}var v=d[1],p=v+(v-d[0]);if(p>=n.getSize()||!n.isRange(v,p,!1))throw new E;var x=c.toString();if(x.length<8)throw new O;if(!e.checkChecksum(x))throw new ae;var w=(a[1]+a[0])/2,y=(d[1]+d[0])/2,_=this.getBarcodeFormat(),C=[new b(w,t),new b(y,t)],m=new we(x,null,0,C,_,(new Date).getTime()),S=0;try{var I=ca.decodeRow(t,n,d[1]);m.putMetadata(xe.UPC_EAN_EXTENSION,I.getText()),m.putAllMetadata(I.getResultMetadata()),m.addResultPoints(I.getResultPoints()),S=I.getText().length}catch{}var D=null==i?null:i.get(K.ALLOWED_EAN_EXTENSIONS);if(null!=D){var M=!1;for(var P in D)if(S.toString()===P){M=!0;break}if(!M)throw new E}return m},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(0===n)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){if((o=t.charAt(a).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new O;i+=o}for(i*=3,a=n-2;a>=0;a-=2){var o;if((o=t.charAt(a).charCodeAt(0)-"0".charCodeAt(0))<0||o>9)throw new O;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e}(it);const he=da;var la=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Mr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},va=function(r){function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return la(e,r),e.prototype.decodeMiddle=function(t,n,i){var a,o,s,u,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var c=t.getSize(),h=n[1],d=0,l=0;l<6&&h<c;l++){var v=he.decodeDigit(t,f,h,he.L_AND_G_PATTERNS);i+=String.fromCharCode("0".charCodeAt(0)+v%10);try{for(var p=(a=void 0,Mr(f)),x=p.next();!x.done;x=p.next())h+=x.value}catch(m){a={error:m}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}v>=10&&(d|=1<<5-l)}for(i=e.determineFirstDigit(i,d),h=he.findGuardPattern(t,h,!0,he.MIDDLE_PATTERN,new Int32Array(he.MIDDLE_PATTERN.length).fill(0))[1],l=0;l<6&&h<c;l++){v=he.decodeDigit(t,f,h,he.L_PATTERNS),i+=String.fromCharCode("0".charCodeAt(0)+v);try{for(var _=(s=void 0,Mr(f)),C=_.next();!C.done;C=_.next())h+=C.value}catch(I){s={error:I}}finally{try{C&&!C.done&&(u=_.return)&&u.call(_)}finally{if(s)throw s.error}}}return{rowOffset:h,resultString:i}},e.prototype.getBarcodeFormat=function(){return N.EAN_13},e.determineFirstDigit=function(t,n){for(var i=0;i<10;i++)if(n===this.FIRST_DIGIT_ENCODINGS[i])return String.fromCharCode("0".charCodeAt(0)+i)+t;throw new E},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e}(he);const rr=va;var pa=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Br=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ga=function(r){function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return pa(e,r),e.prototype.decodeMiddle=function(t,n,i){var a,o,s,u,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var c=t.getSize(),h=n[1],d=0;d<4&&h<c;d++){var l=he.decodeDigit(t,f,h,he.L_PATTERNS);i+=String.fromCharCode("0".charCodeAt(0)+l);try{for(var v=(a=void 0,Br(f)),p=v.next();!p.done;p=v.next())h+=p.value}catch(C){a={error:C}}finally{try{p&&!p.done&&(o=v.return)&&o.call(v)}finally{if(a)throw a.error}}}for(h=he.findGuardPattern(t,h,!0,he.MIDDLE_PATTERN,new Int32Array(he.MIDDLE_PATTERN.length).fill(0))[1],d=0;d<4&&h<c;d++){l=he.decodeDigit(t,f,h,he.L_PATTERNS),i+=String.fromCharCode("0".charCodeAt(0)+l);try{for(var y=(s=void 0,Br(f)),_=y.next();!_.done;_=y.next())h+=_.value}catch(S){s={error:S}}finally{try{_&&!_.done&&(u=y.return)&&u.call(y)}finally{if(s)throw s.error}}}return{rowOffset:h,resultString:i}},e.prototype.getBarcodeFormat=function(){return N.EAN_8},e}(he);const Fr=ga;var xa=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ya=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.ean13Reader=new rr,t}return xa(e,r),e.prototype.getBarcodeFormat=function(){return N.UPC_A},e.prototype.decode=function(t,n){return this.maybeReturnResult(this.ean13Reader.decode(t))},e.prototype.decodeRow=function(t,n,i){return this.maybeReturnResult(this.ean13Reader.decodeRow(t,n,i))},e.prototype.decodeMiddle=function(t,n,i){return this.ean13Reader.decodeMiddle(t,n,i)},e.prototype.maybeReturnResult=function(t){var n=t.getText();if("0"===n.charAt(0)){var i=new we(n.substring(1),null,null,t.getResultPoints(),N.UPC_A);return null!=t.getResultMetadata()&&i.putAllMetadata(t.getResultMetadata()),i}throw new E},e.prototype.reset=function(){this.ean13Reader.reset()},e}(he);const Lr=ya;var wa=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),_a=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Aa=function(r){function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=new Int32Array(4),t}return wa(e,r),e.prototype.decodeMiddle=function(t,n,i){var a,o,s=this.decodeMiddleCounters.map(function(x){return x});s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var u=t.getSize(),f=n[1],c=0,h=0;h<6&&f<u;h++){var d=e.decodeDigit(t,s,f,e.L_AND_G_PATTERNS);i+=String.fromCharCode("0".charCodeAt(0)+d%10);try{for(var l=(a=void 0,_a(s)),v=l.next();!v.done;v=l.next())f+=v.value}catch(x){a={error:x}}finally{try{v&&!v.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}d>=10&&(c|=1<<5-h)}return e.determineNumSysAndCheckDigit(new F(i),c),f},e.prototype.decodeEnd=function(t,n){return e.findGuardPatternWithoutCounters(t,n,!0,e.MIDDLE_END_PATTERN)},e.prototype.checkChecksum=function(t){return he.checkChecksum(e.convertUPCEtoUPCA(t))},e.determineNumSysAndCheckDigit=function(t,n){for(var i=0;i<=1;i++)for(var a=0;a<10;a++)if(n===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[i][a])return t.insert(0,"0"+i),void t.append("0"+a);throw E.getNotFoundInstance()},e.prototype.getBarcodeFormat=function(){return N.UPC_E},e.convertUPCEtoUPCA=function(t){var n=t.slice(1,7).split("").map(function(o){return o.charCodeAt(0)}),i=new F;i.append(t.charAt(0));var a=n[5];switch(a){case 0:case 1:case 2:i.appendChars(n,0,2),i.append(a),i.append("0000"),i.appendChars(n,2,3);break;case 3:i.appendChars(n,0,3),i.append("00000"),i.appendChars(n,3,2);break;case 4:i.appendChars(n,0,4),i.append("00000"),i.append(n[4]);break;default:i.appendChars(n,0,5),i.append("0000"),i.append(a)}return t.length>=8&&i.append(t.charAt(7)),i.toString()},e.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],e}(he);const kr=Aa;var Ca=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ur=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ea=function(r){function e(t){var n=r.call(this)||this,i=null==t?null:t.get(K.POSSIBLE_FORMATS),a=[];return null!=i&&(i.indexOf(N.EAN_13)>-1&&a.push(new rr),i.indexOf(N.UPC_A)>-1&&a.push(new Lr),i.indexOf(N.EAN_8)>-1&&a.push(new Fr),i.indexOf(N.UPC_E)>-1&&a.push(new kr)),0===a.length&&(a.push(new rr),a.push(new Lr),a.push(new Fr),a.push(new kr)),n.readers=a,n}return Ca(e,r),e.prototype.decodeRow=function(t,n,i){var a,o;try{for(var s=Ur(this.readers),u=s.next();!u.done;u=s.next()){var f=u.value;try{var c=f.decodeRow(t,n,i),h=c.getBarcodeFormat()===N.EAN_13&&"0"===c.getText().charAt(0),d=null==i?null:i.get(K.POSSIBLE_FORMATS),l=null==d||d.includes(N.UPC_A);if(h&&l){var v=c.getRawBytes(),p=new we(c.getText().substring(1),v,v?v.length:null,c.getResultPoints(),N.UPC_A);return p.putAllMetadata(c.getResultMetadata()),p}return c}catch{}}}catch(x){a={error:x}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}throw new E},e.prototype.reset=function(){var t,n;try{for(var i=Ur(this.readers),a=i.next();!a.done;a=i.next())a.value.reset()}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e}(ce);const nr=Ea;var ma=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Sa=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},t}return ma(e,r),e.prototype.decodeRow=function(t,n,i){var a=this.getValidRowData(n);if(!a)throw new E;var o=this.codaBarDecodeRow(a.row);if(!o)throw new E;return new we(o,null,0,[new b(a.left,t),new b(a.right,t)],N.CODABAR,(new Date).getTime())},e.prototype.getValidRowData=function(t){var n=t.toArray(),i=n.indexOf(!0);if(-1===i)return null;var a=n.lastIndexOf(!0);if(a<=i)return null;for(var o=[],s=(n=n.slice(i,a+1))[0],u=1,f=1;f<n.length;f++)n[f]===s?u++:(s=n[f],o.push(u),u=1);return o.push(u),o.length<23&&(o.length+1)%8!=0?null:{row:o,left:i,right:a}},e.prototype.codaBarDecodeRow=function(t){for(var n=[],i=Math.ceil(t.reduce(function(u,f){return(u+f)/2},0));t.length>0;){var o=t.splice(0,8).splice(0,7).map(function(u){return u<i?"n":"w"}).join("");if(void 0===this.CODA_BAR_CHAR_SET[o])return null;n.push(this.CODA_BAR_CHAR_SET[o])}var s=n.join("");return this.validCodaBarString(s)?s:null},e.prototype.validCodaBarString=function(t){return/^[A-D].{1,}[A-D]$/.test(t)},e}(ce);const Ia=Sa;var Oa=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Da=function(r){function e(){var t=r.call(this)||this;return t.decodeFinderCounters=new Int32Array(4),t.dataCharacterCounters=new Int32Array(8),t.oddRoundingErrors=new Array(4),t.evenRoundingErrors=new Array(4),t.oddCounts=new Array(t.dataCharacterCounters.length/2),t.evenCounts=new Array(t.dataCharacterCounters.length/2),t}return Oa(e,r),e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,n){for(var i=0;i<n.length;i++)if(ce.patternMatchVariance(t,n[i],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return i;throw new E},e.count=function(t){return G.sum(new Int32Array(t))},e.increment=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]>a&&(a=n[o],i=o);t[i]++},e.decrement=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]<a&&(a=n[o],i=o);t[i]--},e.isFinderPattern=function(t){var n,i,a=t[0]+t[1],s=a/(a+t[2]+t[3]);if(s>=e.MIN_FINDER_PATTERN_RATIO&&s<=e.MAX_FINDER_PATTERN_RATIO){var u=Number.MAX_SAFE_INTEGER,f=Number.MIN_SAFE_INTEGER;try{for(var c=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(t),h=c.next();!h.done;h=c.next()){var d=h.value;d>f&&(f=d),d<u&&(u=d)}}catch(l){n={error:l}}finally{try{h&&!h.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}return f<10*u}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e}(ce);const Ye=Da;var Ra=function(){function r(e,t){this.value=e,this.checksumPortion=t}return r.prototype.getValue=function(){return this.value},r.prototype.getChecksumPortion=function(){return this.checksumPortion},r.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},r.prototype.equals=function(e){return e instanceof r&&(this.value===e.value&&this.checksumPortion===e.checksumPortion)},r.prototype.hashCode=function(){return this.value^this.checksumPortion},r}();const bt=Ra;var ba=function(){function r(e,t,n,i,a){this.value=e,this.startEnd=t,this.value=e,this.startEnd=t,this.resultPoints=new Array,this.resultPoints.push(new b(n,a)),this.resultPoints.push(new b(i,a))}return r.prototype.getValue=function(){return this.value},r.prototype.getStartEnd=function(){return this.startEnd},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.equals=function(e){return e instanceof r&&this.value===e.value},r.prototype.hashCode=function(){return this.value},r}();const Vr=ba;var Pa=function(){function r(){}return r.getRSSvalue=function(e,t,n){var i,a,o=0;try{for(var s=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),u=s.next();!u.done;u=s.next())o+=u.value}catch(y){i={error:y}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}for(var c=0,h=0,d=e.length,l=0;l<d-1;l++){var v=void 0;for(v=1,h|=1<<l;v<e[l];v++,h&=~(1<<l)){var p=r.combins(o-v-1,d-l-2);if(n&&0===h&&o-v-(d-l-1)>=d-l-1&&(p-=r.combins(o-v-(d-l),d-l-2)),d-l-1>1){for(var x=0,w=o-v-(d-l-2);w>t;w--)x+=r.combins(o-v-w-1,d-l-3);p-=x*(d-1-l)}else o-v>t&&p--;c+=p}o-=v}return c},r.combins=function(e,t){var n,i;e-t>t?(i=t,n=e-t):(i=e-t,n=t);for(var a=1,o=1,s=e;s>n;s--)a*=s,o<=i&&(a/=o,o++);for(;o<=i;)a/=o,o++;return a},r}();const at=Pa;var Ma=function(){function r(){}return r.buildBitArray=function(e){var t=2*e.length-1;null==e[e.length-1].getRightChar()&&(t-=1);for(var i=new ve(12*t),a=0,s=e[0].getRightChar().getValue(),u=11;u>=0;--u)s&1<<u&&i.set(a),a++;for(u=1;u<e.length;++u){for(var f=e[u],c=f.getLeftChar().getValue(),h=11;h>=0;--h)c&1<<h&&i.set(a),a++;if(null!==f.getRightChar()){var d=f.getRightChar().getValue();for(h=11;h>=0;--h)d&1<<h&&i.set(a),a++}}return i},r}();const Ba=Ma;var Fa=function(){function r(e,t){t?this.decodedInformation=null:(this.finished=e,this.decodedInformation=t)}return r.prototype.getDecodedInformation=function(){return this.decodedInformation},r.prototype.isFinished=function(){return this.finished},r}();const Ke=Fa;var La=function(){function r(e){this.newPosition=e}return r.prototype.getNewPosition=function(){return this.newPosition},r}();const ir=La;var ka=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ua=function(r){function e(t,n){var i=r.call(this,t)||this;return i.value=n,i}return ka(e,r),e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e}(ir);const Pe=Ua;var Va=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ha=function(r){function e(t,n,i){var a=r.call(this,t)||this;return i?(a.remaining=!0,a.remainingValue=a.remainingValue):(a.remaining=!1,a.remainingValue=0),a.newString=n,a}return Va(e,r),e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e}(ir);const qe=Ha;var Ga=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Wa=function(r){function e(t,n,i){var a=r.call(this,t)||this;if(n<0||n>10||i<0||i>10)throw new O;return a.firstDigit=n,a.secondDigit=i,a}return Ga(e,r),e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return 10*this.firstDigit+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit===e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit===e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e}(ir);const ot=Wa;var Nt=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Xa=function(){function r(){}return r.parseFieldsInGeneralPurpose=function(e){var t,n,i,a,o,s,u,f;if(!e)return null;if(e.length<2)throw new E;var c=e.substring(0,2);try{for(var h=Nt(r.TWO_DIGIT_DATA_LENGTH),d=h.next();!d.done;d=h.next())if((l=d.value)[0]===c)return l[1]===r.VARIABLE_LENGTH?r.processVariableAI(2,l[2],e):r.processFixedAI(2,l[1],e)}catch(S){t={error:S}}finally{try{d&&!d.done&&(n=h.return)&&n.call(h)}finally{if(t)throw t.error}}if(e.length<3)throw new E;var v=e.substring(0,3);try{for(var p=Nt(r.THREE_DIGIT_DATA_LENGTH),x=p.next();!x.done;x=p.next())if((l=x.value)[0]===v)return l[1]===r.VARIABLE_LENGTH?r.processVariableAI(3,l[2],e):r.processFixedAI(3,l[1],e)}catch(S){i={error:S}}finally{try{x&&!x.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}try{for(var w=Nt(r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),y=w.next();!y.done;y=w.next())if((l=y.value)[0]===v)return l[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,l[2],e):r.processFixedAI(4,l[1],e)}catch(S){o={error:S}}finally{try{y&&!y.done&&(s=w.return)&&s.call(w)}finally{if(o)throw o.error}}if(e.length<4)throw new E;var _=e.substring(0,4);try{for(var C=Nt(r.FOUR_DIGIT_DATA_LENGTH),m=C.next();!m.done;m=C.next()){var l;if((l=m.value)[0]===_)return l[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,l[2],e):r.processFixedAI(4,l[1],e)}}catch(S){u={error:S}}finally{try{m&&!m.done&&(f=C.return)&&f.call(C)}finally{if(u)throw u.error}}throw new E},r.processFixedAI=function(e,t,n){if(n.length<e)throw new E;var i=n.substring(0,e);if(n.length<e+t)throw new E;var a=n.substring(e,e+t),o=n.substring(e+t),s="("+i+")"+a,u=r.parseFieldsInGeneralPurpose(o);return null==u?s:s+u},r.processVariableAI=function(e,t,n){var a,i=n.substring(0,e),o=n.substring(e,a=n.length<e+t?n.length:e+t),s=n.substring(a),u="("+i+")"+o,f=r.parseFieldsInGeneralPurpose(s);return null==f?u:u+f},r.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",r.VARIABLE_LENGTH=[],20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",r.VARIABLE_LENGTH,20],["22",r.VARIABLE_LENGTH,29],["30",r.VARIABLE_LENGTH,8],["37",r.VARIABLE_LENGTH,8],["90",r.VARIABLE_LENGTH,30],["91",r.VARIABLE_LENGTH,30],["92",r.VARIABLE_LENGTH,30],["93",r.VARIABLE_LENGTH,30],["94",r.VARIABLE_LENGTH,30],["95",r.VARIABLE_LENGTH,30],["96",r.VARIABLE_LENGTH,30],["97",r.VARIABLE_LENGTH,3],["98",r.VARIABLE_LENGTH,30],["99",r.VARIABLE_LENGTH,30]],r.THREE_DIGIT_DATA_LENGTH=[["240",r.VARIABLE_LENGTH,30],["241",r.VARIABLE_LENGTH,30],["242",r.VARIABLE_LENGTH,6],["250",r.VARIABLE_LENGTH,30],["251",r.VARIABLE_LENGTH,30],["253",r.VARIABLE_LENGTH,17],["254",r.VARIABLE_LENGTH,20],["400",r.VARIABLE_LENGTH,30],["401",r.VARIABLE_LENGTH,30],["402",17],["403",r.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",r.VARIABLE_LENGTH,20],["421",r.VARIABLE_LENGTH,15],["422",3],["423",r.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",r.VARIABLE_LENGTH,15],["391",r.VARIABLE_LENGTH,18],["392",r.VARIABLE_LENGTH,15],["393",r.VARIABLE_LENGTH,18],["703",r.VARIABLE_LENGTH,30]],r.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",r.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",r.VARIABLE_LENGTH,20],["8003",r.VARIABLE_LENGTH,30],["8004",r.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",r.VARIABLE_LENGTH,30],["8008",r.VARIABLE_LENGTH,12],["8018",18],["8020",r.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",r.VARIABLE_LENGTH,70],["8200",r.VARIABLE_LENGTH,70]],r}();const za=Xa;var Za=function(){function r(e){this.buffer=new F,this.information=e}return r.prototype.decodeAllCodes=function(e,t){for(var n=t,i=null;;){var a=this.decodeGeneralPurposeField(n,i),o=za.parseFieldsInGeneralPurpose(a.getNewString());if(null!=o&&e.append(o),i=a.isRemaining()?""+a.getRemainingValue():null,n===a.getNewPosition())break;n=a.getNewPosition()}return e.toString()},r.prototype.isStillNumeric=function(e){if(e+7>this.information.getSize())return e+4<=this.information.getSize();for(var t=e;t<e+3;++t)if(this.information.get(t))return!0;return this.information.get(e+3)},r.prototype.decodeNumeric=function(e){if(e+7>this.information.getSize()){var t=this.extractNumericValueFromBitArray(e,4);return new ot(this.information.getSize(),0===t?ot.FNC1:t-1,ot.FNC1)}var n=this.extractNumericValueFromBitArray(e,7);return new ot(e+7,(n-8)/11,(n-8)%11)},r.prototype.extractNumericValueFromBitArray=function(e,t){return r.extractNumericValueFromBitArray(this.information,e,t)},r.extractNumericValueFromBitArray=function(e,t,n){for(var i=0,a=0;a<n;++a)e.get(t+a)&&(i|=1<<n-a-1);return i},r.prototype.decodeGeneralPurposeField=function(e,t){this.buffer.setLengthToZero(),null!=t&&this.buffer.append(t),this.current.setPosition(e);var n=this.parseBlocks();return null!=n&&n.isRemaining()?new qe(this.current.getPosition(),this.buffer.toString(),n.getRemainingValue()):new qe(this.current.getPosition(),this.buffer.toString())},r.prototype.parseBlocks=function(){var e,t;do{var n=this.current.getPosition();if(e=this.current.isAlpha()?(t=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(t=this.parseIsoIec646Block()).isFinished():(t=this.parseNumericBlock()).isFinished(),n===this.current.getPosition()&&!e)break}while(!e);return t.getDecodedInformation()},r.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var e=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFirstDigitFNC1()){var t=void 0;return t=e.isSecondDigitFNC1()?new qe(this.current.getPosition(),this.buffer.toString()):new qe(this.current.getPosition(),this.buffer.toString(),e.getSecondDigit()),new Ke(!0,t)}if(this.buffer.append(e.getFirstDigit()),e.isSecondDigitFNC1())return t=new qe(this.current.getPosition(),this.buffer.toString()),new Ke(!0,t);this.buffer.append(e.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new Ke(!1)},r.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var e=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new qe(this.current.getPosition(),this.buffer.toString());return new Ke(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new Ke(!1)},r.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var e=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new qe(this.current.getPosition(),this.buffer.toString());return new Ke(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new Ke(!1)},r.prototype.isStillIsoIec646=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+7>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<116)return!0;if(e+8>this.information.getSize())return!1;var i=this.extractNumericValueFromBitArray(e,8);return i>=232&&i<253},r.prototype.decodeIsoIec646=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(15===t)return new Pe(e+5,Pe.FNC1);if(t>=5&&t<15)return new Pe(e+5,"0"+(t-5));var a,n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<90)return new Pe(e+7,""+(n+1));if(n>=90&&n<116)return new Pe(e+7,""+(n+7));switch(this.extractNumericValueFromBitArray(e,8)){case 232:a="!";break;case 233:a='"';break;case 234:a="%";break;case 235:a="&";break;case 236:a="'";break;case 237:a="(";break;case 238:a=")";break;case 239:a="*";break;case 240:a="+";break;case 241:a=",";break;case 242:a="-";break;case 243:a=".";break;case 244:a="/";break;case 245:a=":";break;case 246:a=";";break;case 247:a="<";break;case 248:a="=";break;case 249:a=">";break;case 250:a="?";break;case 251:a="_";break;case 252:a=" ";break;default:throw new O}return new Pe(e+8,a)},r.prototype.isStillAlpha=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+6>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,6);return n>=16&&n<63},r.prototype.decodeAlphanumeric=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(15===t)return new Pe(e+5,Pe.FNC1);if(t>=5&&t<15)return new Pe(e+5,"0"+(t-5));var i,n=this.extractNumericValueFromBitArray(e,6);if(n>=32&&n<58)return new Pe(e+6,""+(n+33));switch(n){case 58:i="*";break;case 59:i=",";break;case 60:i="-";break;case 61:i=".";break;case 62:i="/";break;default:throw new Be("Decoding invalid alphanumeric value: "+n)}return new Pe(e+6,i)},r.prototype.isAlphaTo646ToAlphaLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<5&&t+e<this.information.getSize();++t)if(2===t){if(!this.information.get(e+2))return!1}else if(this.information.get(e+t))return!1;return!0},r.prototype.isAlphaOr646ToNumericLatch=function(e){if(e+3>this.information.getSize())return!1;for(var t=e;t<e+3;++t)if(this.information.get(t))return!1;return!0},r.prototype.isNumericToAlphaNumericLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<4&&t+e<this.information.getSize();++t)if(this.information.get(e+t))return!1;return!0},r}();const Pt=Za;var ja=function(){function r(e){this.information=e,this.generalDecoder=new Pt(e)}return r.prototype.getInformation=function(){return this.information},r.prototype.getGeneralDecoder=function(){return this.generalDecoder},r}();const Hr=ja;var Ya=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ka=function(r){function e(t){return r.call(this,t)||this}return Ya(e,r),e.prototype.encodeCompressedGtin=function(t,n){t.append("(01)");var i=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,n,i)},e.prototype.encodeCompressedGtinWithoutAI=function(t,n,i){for(var a=0;a<4;++a){var o=this.getGeneralDecoder().extractNumericValueFromBitArray(n+10*a,10);o/100==0&&t.append("0"),o/10==0&&t.append("0"),t.append(o)}e.appendCheckDigit(t,i)},e.appendCheckDigit=function(t,n){for(var i=0,a=0;a<13;a++){var o=t.charAt(a+n).charCodeAt(0)-"0".charCodeAt(0);i+=1&a?o:3*o}10==(i=10-i%10)&&(i=0),t.append(i)},e.GTIN_SIZE=40,e}(Hr);const Me=Ka;var qa=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Qa=function(r){function e(t){return r.call(this,t)||this}return qa(e,r),e.prototype.parseInformation=function(){var t=new F;t.append("(01)");var n=t.length(),i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(i),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,n),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e}(Me);const Ja=Qa;var $a=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),eo=function(r){function e(t){return r.call(this,t)||this}return $a(e,r),e.prototype.parseInformation=function(){var t=new F;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e}(Hr);const to=eo;var ro=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),no=function(r){function e(t){return r.call(this,t)||this}return ro(e,r),e.prototype.encodeCompressedWeight=function(t,n,i){var a=this.getGeneralDecoder().extractNumericValueFromBitArray(n,i);this.addWeightCode(t,a);for(var o=this.checkWeight(a),s=1e5,u=0;u<5;++u)o/s==0&&t.append("0"),s/=10;t.append(o)},e}(Me);const Mt=no;var io=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ao=function(r){function e(t){return r.call(this,t)||this}return io(e,r),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+Mt.GTIN_SIZE+e.WEIGHT_SIZE)throw new E;var t=new F;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+Mt.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e}(Mt);const Gr=ao;var oo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),so=function(r){function e(t){return r.call(this,t)||this}return oo(e,r),e.prototype.addWeightCode=function(t,n){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e}(Gr);const uo=so;var fo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),co=function(r){function e(t){return r.call(this,t)||this}return fo(e,r),e.prototype.addWeightCode=function(t,n){t.append(n<1e4?"(3202)":"(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e}(Gr);const ho=co;var lo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),vo=function(r){function e(t){return r.call(this,t)||this}return lo(e,r),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Me.GTIN_SIZE)throw new E;var t=new F;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Me.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(n),t.append(")");var i=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Me.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(i.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e}(Me);const po=vo;var go=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),xo=function(r){function e(t){return r.call(this,t)||this}return go(e,r),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Me.GTIN_SIZE)throw new E;var t=new F;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Me.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(n),t.append(")");var i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Me.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);i/100==0&&t.append("0"),i/10==0&&t.append("0"),t.append(i);var a=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Me.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(a.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e}(Me);const yo=xo;var wo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),_o=function(r){function e(t,n,i){var a=r.call(this,t)||this;return a.dateCode=i,a.firstAIdigits=n,a}return wo(e,r),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new E;var t=new F;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,n){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(n,e.DATE_SIZE);if(38400!==i){t.append("("),t.append(this.dateCode),t.append(")");var a=i%32,o=(i/=32)%12+1,s=i/=12;s/10==0&&t.append("0"),t.append(s),o/10==0&&t.append("0"),t.append(o),a/10==0&&t.append("0"),t.append(a)}},e.prototype.addWeightCode=function(t,n){t.append("("),t.append(this.firstAIdigits),t.append(n/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e}(Mt);const Ve=_o;var Co=function(){function r(e,t,n,i){this.leftchar=e,this.rightchar=t,this.finderpattern=n,this.maybeLast=i}return r.prototype.mayBeLast=function(){return this.maybeLast},r.prototype.getLeftChar=function(){return this.leftchar},r.prototype.getRightChar=function(){return this.rightchar},r.prototype.getFinderPattern=function(){return this.finderpattern},r.prototype.mustBeLast=function(){return null==this.rightchar},r.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"},r.equals=function(e,t){return e instanceof r&&r.equalsOrNull(e.leftchar,t.leftchar)&&r.equalsOrNull(e.rightchar,t.rightchar)&&r.equalsOrNull(e.finderpattern,t.finderpattern)},r.equalsOrNull=function(e,t){return null===e?null===t:r.equals(e,t)},r.prototype.hashCode=function(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()},r}();const Wr=Co;var Eo=function(){function r(e,t,n){this.pairs=e,this.rowNumber=t,this.wasReversed=n}return r.prototype.getPairs=function(){return this.pairs},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.isReversed=function(){return this.wasReversed},r.prototype.isEquivalent=function(e){return this.checkEqualitity(this,e)},r.prototype.toString=function(){return"{ "+this.pairs+" }"},r.prototype.equals=function(e,t){return e instanceof r&&this.checkEqualitity(e,t)&&e.wasReversed===t.wasReversed},r.prototype.checkEqualitity=function(e,t){var n;if(e&&t)return e.forEach(function(i,a){t.forEach(function(o){i.getLeftChar().getValue()===o.getLeftChar().getValue()&&i.getRightChar().getValue()===o.getRightChar().getValue()&&i.getFinderPatter().getValue()===o.getFinderPatter().getValue()&&(n=!0)})}),n},r}();const mo=Eo;var So=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),He=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Io=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.pairs=new Array(e.MAX_PAIRS),t.rows=new Array,t.startEnd=[2],t}return So(e,r),e.prototype.decodeRow=function(t,n,i){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,n))}catch{}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,n))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,n){for(var i=!1;!i;)try{this.pairs.push(this.retrieveNextPair(n,this.pairs,t))}catch(s){if(s instanceof E){if(!this.pairs.length)throw new E;i=!0}}if(this.checkChecksum())return this.pairs;var a;if(a=!!this.rows.length,this.storeRow(t,!1),a){var o=this.checkRowsBoolean(!1);if(null!=o||null!=(o=this.checkRowsBoolean(!0)))return o}throw new E},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var n=null;try{n=this.checkRows(new Array,0)}catch(i){console.log(i)}return t&&(this.rows=this.rows.reverse()),n},e.prototype.checkRows=function(t,n){for(var i,a,o=n;o<this.rows.length;o++){var s=this.rows[o];this.pairs.length=0;try{for(var u=(i=void 0,He(t)),f=u.next();!f.done;f=u.next())this.pairs.push(f.value.getPairs())}catch(d){i={error:d}}finally{try{f&&!f.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}if(this.pairs.push(s.getPairs()),e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var h=new Array(t);h.push(s);try{return this.checkRows(h,o+1)}catch(d){console.log(d)}}}throw new E},e.isValidSequence=function(t){var n,i;try{for(var a=He(e.FINDER_PATTERN_SEQUENCES),o=a.next();!o.done;o=a.next()){var s=o.value;if(!(t.length>s.length)){for(var u=!0,f=0;f<t.length;f++)if(t[f].getFinderPattern().getValue()!==s[f]){u=!1;break}if(u)return!0}}}catch(c){n={error:c}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return!1},e.prototype.storeRow=function(t,n){for(var i=0,a=!1,o=!1;i<this.rows.length;){var s=this.rows[i];if(s.getRowNumber()>t){o=s.isEquivalent(this.pairs);break}a=s.isEquivalent(this.pairs),i++}o||a||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(i,new mo(this.pairs,t,n)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,n){var i,a,o,s,u,f;try{for(var c=He(n),h=c.next();!h.done;h=c.next()){var d=h.value;if(d.getPairs().length!==t.length)try{for(var v=(o=void 0,He(d.getPairs())),p=v.next();!p.done;p=v.next()){var x=p.value;try{for(var y=(u=void 0,He(t)),_=y.next();!_.done;_=y.next())if(Wr.equals(x,_.value)){!0;break}}catch(m){u={error:m}}finally{try{_&&!_.done&&(f=y.return)&&f.call(y)}finally{if(u)throw u.error}}}}catch(m){o={error:m}}finally{try{p&&!p.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}}}catch(m){i={error:m}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}},e.isPartialRow=function(t,n){var i,a,o,s,u,f;try{for(var c=He(n),h=c.next();!h.done;h=c.next()){var d=h.value,l=!0;try{for(var v=(o=void 0,He(t)),p=v.next();!p.done;p=v.next()){var x=p.value,w=!1;try{for(var y=(u=void 0,He(d.getPairs())),_=y.next();!_.done;_=y.next())if(x.equals(_.value)){w=!0;break}}catch(m){u={error:m}}finally{try{_&&!_.done&&(f=y.return)&&f.call(y)}finally{if(u)throw u.error}}if(!w){l=!1;break}}}catch(m){o={error:m}}finally{try{p&&!p.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}if(l)return!0}}catch(m){i={error:m}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var i=function Ao(r){try{if(r.get(1))return new Ja(r);if(!r.get(2))return new to(r);switch(Pt.extractNumericValueFromBitArray(r,1,4)){case 4:return new uo(r);case 5:return new ho(r)}switch(Pt.extractNumericValueFromBitArray(r,1,5)){case 12:return new po(r);case 13:return new yo(r)}switch(Pt.extractNumericValueFromBitArray(r,1,7)){case 56:return new Ve(r,"310","11");case 57:return new Ve(r,"320","11");case 58:return new Ve(r,"310","13");case 59:return new Ve(r,"320","13");case 60:return new Ve(r,"310","15");case 61:return new Ve(r,"320","15");case 62:return new Ve(r,"310","17");case 63:return new Ve(r,"320","17")}}catch(i){throw console.log(i),new Be("unknown decoder: "+r)}}(Ba.buildBitArray(t)),a=i.parseInformation(),o=t[0].getFinderPattern().getResultPoints(),s=t[t.length-1].getFinderPattern().getResultPoints();return new we(a,null,null,[o[0],o[1],s[0],s[1]],N.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),n=t.getLeftChar(),i=t.getRightChar();if(null===i)return!1;for(var a=i.getChecksumPortion(),o=2,s=1;s<this.pairs.size();++s){var u=this.pairs.get(s);a+=u.getLeftChar().getChecksumPortion(),o++;var f=u.getRightChar();null!=f&&(a+=f.getChecksumPortion(),o++)}return 211*(o-4)+(a%=211)===n.getValue()},e.getNextSecondBar=function(t,n){var i;return t.get(n)?(i=t.getNextUnset(n),i=t.getNextSet(i)):(i=t.getNextSet(n),i=t.getNextUnset(i)),i},e.prototype.retrieveNextPair=function(t,n,i){var a=n.length%2==0;this.startFromEven&&(a=!a);var o,s=!0,u=-1;do{this.findNextPair(t,n,u),null===(o=this.parseFoundFinderPattern(t,i,a))?u=e.getNextSecondBar(t,this.startEnd[0]):s=!1}while(s);var c,f=this.decodeDataCharacter(t,o,a,!0);if(!this.isEmptyPair(n)&&n[n.length-1].mustBeLast())throw new E;try{c=this.decodeDataCharacter(t,o,a,!1)}catch(h){c=null,console.log(h)}return new Wr(f,c,o,!0)},e.prototype.isEmptyPair=function(t){return 0===t.length},e.prototype.findNextPair=function(t,n,i){var a=this.getDecodeFinderCounters();a[0]=0,a[1]=0,a[2]=0,a[3]=0;var s,o=t.getSize();s=i>=0?i:this.isEmptyPair(n)?0:n[n.length-1].getFinderPattern().getStartEnd()[1];var f=n.length%2!=0;this.startFromEven&&(f=!f);for(var c=!1;s<o&&(c=!t.get(s));)s++;for(var h=0,d=s,l=s;l<o;l++)if(t.get(l)!==c)a[h]++;else{if(3===h){if(f&&e.reverseCounters(a),e.isFinderPattern(a))return this.startEnd[0]=d,void(this.startEnd[1]=l);f&&e.reverseCounters(a),d+=a[0]+a[1],a[0]=a[2],a[1]=a[3],a[2]=0,a[3]=0,h--}else h++;a[h]=1,c=!c}throw new E},e.reverseCounters=function(t){for(var n=t.length,i=0;i<n/2;++i){var a=t[i];t[i]=t[n-i-1],t[n-i-1]=a}},e.prototype.parseFoundFinderPattern=function(t,n,i){var a,o,s;if(i){for(var u=this.startEnd[0]-1;u>=0&&!t.get(u);)u--;u++,a=this.startEnd[0]-u,o=u,s=this.startEnd[1]}else o=this.startEnd[0],a=(s=t.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];var c,f=this.getDecodeFinderCounters();q.arraycopy(f,0,f,1,f.length-1),f[0]=a;try{c=this.parseFinderValue(f,e.FINDER_PATTERNS)}catch{return null}return new Vr(c,[o,s],o,s,n)},e.prototype.decodeDataCharacter=function(t,n,i,a){for(var o=this.getDataCharacterCounters(),s=0;s<o.length;s++)o[s]=0;if(a)e.recordPatternInReverse(t,n.getStartEnd()[0],o);else{e.recordPattern(t,n.getStartEnd()[1],o);for(var u=0,f=o.length-1;u<f;u++,f--){var c=o[u];o[u]=o[f],o[f]=c}}var d=G.sum(new Int32Array(o))/17,l=(n.getStartEnd()[1]-n.getStartEnd()[0])/15;if(Math.abs(d-l)/l>.3)throw new E;var v=this.getOddCounts(),p=this.getEvenCounts(),x=this.getOddRoundingErrors(),w=this.getEvenRoundingErrors();for(u=0;u<o.length;u++){var y=1*o[u]/d,_=y+.5;if(_<1){if(y<.3)throw new E;_=1}else if(_>8){if(y>8.7)throw new E;_=8}var C=u/2;1&u?(p[C]=_,w[C]=y-_):(v[C]=_,x[C]=y-_)}this.adjustOddEvenCounts(17);var m=4*n.getValue()+(i?0:2)+(a?0:1)-1,S=0,I=0;for(u=v.length-1;u>=0;u--)e.isNotA1left(n,i,a)&&(I+=v[u]*e.WEIGHTS[m][2*u]),S+=v[u];var M=0;for(u=p.length-1;u>=0;u--)e.isNotA1left(n,i,a)&&(M+=p[u]*e.WEIGHTS[m][2*u+1]);var P=I+M;if(1&S||S>13||S<4)throw new E;var ie=(13-S)/2,H=e.SYMBOL_WIDEST[ie],te=9-H,Ne=at.getRSSvalue(v,H,!0),Xe=at.getRSSvalue(p,te,!1);return new bt(Ne*e.EVEN_TOTAL_SUBSET[ie]+Xe+e.GSUM[ie],P)},e.isNotA1left=function(t,n,i){return!(0===t.getValue()&&n&&i)},e.prototype.adjustOddEvenCounts=function(t){var n=G.sum(new Int32Array(this.getOddCounts())),i=G.sum(new Int32Array(this.getEvenCounts())),a=!1,o=!1;n>13?o=!0:n<4&&(a=!0);var s=!1,u=!1;i>13?u=!0:i<4&&(s=!0);var f=n+i-t,c=1==(1&n),h=0==(1&i);if(1===f)if(c){if(h)throw new E;o=!0}else{if(!h)throw new E;u=!0}else if(-1===f)if(c){if(h)throw new E;a=!0}else{if(!h)throw new E;s=!0}else{if(0!==f)throw new E;if(c){if(!h)throw new E;n<i?(a=!0,u=!0):(o=!0,s=!0)}else if(h)throw new E}if(a){if(o)throw new E;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(o&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(u)throw new E;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}u&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A=0,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B=1,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C=2,e.FINDER_PAT_B,e.FINDER_PAT_D=3],[e.FINDER_PAT_A,e.FINDER_PAT_E=4,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F=5],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e}(Ye);const Oo=Io;var To=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Do=function(r){function e(t,n,i){var a=r.call(this,t,n)||this;return a.count=0,a.finderPattern=i,a}return To(e,r),e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e}(bt);const Ro=Do;var bo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ar=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},No=function(r){function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.possibleLeftPairs=[],t.possibleRightPairs=[],t}return bo(e,r),e.prototype.decodeRow=function(t,n,i){var a,o,s,u,f=this.decodePair(n,!1,t,i);e.addOrTally(this.possibleLeftPairs,f),n.reverse();var c=this.decodePair(n,!0,t,i);e.addOrTally(this.possibleRightPairs,c),n.reverse();try{for(var h=ar(this.possibleLeftPairs),d=h.next();!d.done;d=h.next()){var l=d.value;if(l.getCount()>1)try{for(var v=(s=void 0,ar(this.possibleRightPairs)),p=v.next();!p.done;p=v.next()){var x=p.value;if(x.getCount()>1&&e.checkChecksum(l,x))return e.constructResult(l,x)}}catch(w){s={error:w}}finally{try{p&&!p.done&&(u=v.return)&&u.call(v)}finally{if(s)throw s.error}}}}catch(w){a={error:w}}finally{try{d&&!d.done&&(o=h.return)&&o.call(h)}finally{if(a)throw a.error}}throw new E},e.addOrTally=function(t,n){var i,a;if(null!=n){var o=!1;try{for(var s=ar(t),u=s.next();!u.done;u=s.next()){var f=u.value;if(f.getValue()===n.getValue()){f.incrementCount(),o=!0;break}}}catch(c){i={error:c}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}o||t.push(n)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,n){for(var i=4537077*t.getValue()+n.getValue(),a=new String(i).toString(),o=new F,s=13-a.length;s>0;s--)o.append("0");o.append(a);var u=0;for(s=0;s<13;s++){var f=o.charAt(s).charCodeAt(0)-"0".charCodeAt(0);u+=1&s?f:3*f}10==(u=10-u%10)&&(u=0),o.append(u.toString());var c=t.getFinderPattern().getResultPoints(),h=n.getFinderPattern().getResultPoints();return new we(o.toString(),null,0,[c[0],c[1],h[0],h[1]],N.RSS_14,(new Date).getTime())},e.checkChecksum=function(t,n){var i=(t.getChecksumPortion()+16*n.getChecksumPortion())%79,a=9*t.getFinderPattern().getValue()+n.getFinderPattern().getValue();return a>72&&a--,a>8&&a--,i===a},e.prototype.decodePair=function(t,n,i,a){try{var o=this.findFinderPattern(t,n),s=this.parseFoundFinderPattern(t,i,n,o),u=null==a?null:a.get(K.NEED_RESULT_POINT_CALLBACK);if(null!=u){var f=(o[0]+o[1])/2;n&&(f=t.getSize()-1-f),u.foundPossibleResultPoint(new b(f,i))}var c=this.decodeDataCharacter(t,s,!0),h=this.decodeDataCharacter(t,s,!1);return new Ro(1597*c.getValue()+h.getValue(),c.getChecksumPortion()+4*h.getChecksumPortion(),s)}catch{return null}},e.prototype.decodeDataCharacter=function(t,n,i){for(var a=this.getDataCharacterCounters(),o=0;o<a.length;o++)a[o]=0;if(i)ce.recordPatternInReverse(t,n.getStartEnd()[0],a);else{ce.recordPattern(t,n.getStartEnd()[1]+1,a);for(var s=0,u=a.length-1;s<u;s++,u--){var f=a[s];a[s]=a[u],a[u]=f}}var c=i?16:15,h=G.sum(new Int32Array(a))/c,d=this.getOddCounts(),l=this.getEvenCounts(),v=this.getOddRoundingErrors(),p=this.getEvenRoundingErrors();for(s=0;s<a.length;s++){var x=a[s]/h,w=Math.floor(x+.5);w<1?w=1:w>8&&(w=8);var y=Math.floor(s/2);1&s?(l[y]=w,p[y]=x-w):(d[y]=w,v[y]=x-w)}this.adjustOddEvenCounts(i,c);var _=0,C=0;for(s=d.length-1;s>=0;s--)C*=9,C+=d[s],_+=d[s];var m=0,S=0;for(s=l.length-1;s>=0;s--)m*=9,m+=l[s],S+=l[s];var D,M,I=C+3*m;if(i){if(1&_||_>12||_<4)throw new E;var P=9-(M=e.OUTSIDE_ODD_WIDEST[D=(12-_)/2]),ie=at.getRSSvalue(d,M,!1),H=at.getRSSvalue(l,P,!0);return new bt(ie*e.OUTSIDE_EVEN_TOTAL_SUBSET[D]+H+e.OUTSIDE_GSUM[D],I)}if(1&S||S>10||S<4)throw new E;return P=9-(M=e.INSIDE_ODD_WIDEST[D=(10-S)/2]),ie=at.getRSSvalue(d,M,!0),H=at.getRSSvalue(l,P,!1),new bt(H*e.INSIDE_ODD_TOTAL_SUBSET[D]+ie+e.INSIDE_GSUM[D],I)},e.prototype.findFinderPattern=function(t,n){var i=this.getDecodeFinderCounters();i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var a=t.getSize(),o=!1,s=0;s<a&&n!==(o=!t.get(s));)s++;for(var u=0,f=s,c=s;c<a;c++)if(t.get(c)!==o)i[u]++;else{if(3===u){if(Ye.isFinderPattern(i))return[f,c];f+=i[0]+i[1],i[0]=i[2],i[1]=i[3],i[2]=0,i[3]=0,u--}else u++;i[u]=1,o=!o}throw new E},e.prototype.parseFoundFinderPattern=function(t,n,i,a){for(var o=t.get(a[0]),s=a[0]-1;s>=0&&o!==t.get(s);)s--;s++;var u=a[0]-s,f=this.getDecodeFinderCounters(),c=new Int32Array(f.length);q.arraycopy(f,0,c,1,f.length-1),c[0]=u;var h=this.parseFinderValue(c,e.FINDER_PATTERNS),d=s,l=a[1];return i&&(d=t.getSize()-1-d,l=t.getSize()-1-l),new Vr(h,[s,a[1]],d,l,n)},e.prototype.adjustOddEvenCounts=function(t,n){var i=G.sum(new Int32Array(this.getOddCounts())),a=G.sum(new Int32Array(this.getEvenCounts())),o=!1,s=!1,u=!1,f=!1;t?(i>12?s=!0:i<4&&(o=!0),a>12?f=!0:a<4&&(u=!0)):(i>11?s=!0:i<5&&(o=!0),a>10?f=!0:a<4&&(u=!0));var c=i+a-n,h=(1&i)==(t?1:0),d=1==(1&a);if(1===c)if(h){if(d)throw new E;s=!0}else{if(!d)throw new E;f=!0}else if(-1===c)if(h){if(d)throw new E;o=!0}else{if(!d)throw new E;u=!0}else{if(0!==c)throw new E;if(h){if(!d)throw new E;i<a?(o=!0,f=!0):(s=!0,u=!0)}else if(d)throw new E}if(o){if(s)throw new E;Ye.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&Ye.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(f)throw new E;Ye.increment(this.getEvenCounts(),this.getOddRoundingErrors())}f&&Ye.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],e}(Ye);const Xr=No;var Po=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Mo=function(r){function e(t){var n=r.call(this)||this;n.readers=[];var i=t?t.get(K.POSSIBLE_FORMATS):null,a=t&&void 0!==t.get(K.ASSUME_CODE_39_CHECK_DIGIT),o=t&&void 0!==t.get(K.ENABLE_CODE_39_EXTENDED_MODE);return i&&((i.includes(N.EAN_13)||i.includes(N.UPC_A)||i.includes(N.EAN_8)||i.includes(N.UPC_E))&&n.readers.push(new nr(t)),i.includes(N.CODE_39)&&n.readers.push(new br(a,o)),i.includes(N.CODE_93)&&n.readers.push(new Nr),i.includes(N.CODE_128)&&n.readers.push(new Rr),i.includes(N.ITF)&&n.readers.push(new Pr),i.includes(N.CODABAR)&&n.readers.push(new Ia),i.includes(N.RSS_14)&&n.readers.push(new Xr),i.includes(N.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),n.readers.push(new Oo))),0===n.readers.length&&(n.readers.push(new nr(t)),n.readers.push(new br),n.readers.push(new Nr),n.readers.push(new nr(t)),n.readers.push(new Rr),n.readers.push(new Pr),n.readers.push(new Xr)),n}return Po(e,r),e.prototype.decodeRow=function(t,n,i){for(var a=0;a<this.readers.length;a++)try{return this.readers[a].decodeRow(t,n,i)}catch{}throw new E},e.prototype.reset=function(){this.readers.forEach(function(t){return t.reset()})},e}(ce);const st=Mo;var Bo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),zr=(function(r){Bo(function e(t,n){return void 0===t&&(t=500),r.call(this,new st(n),t,n)||this},r)}(nt),function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}),Z=function(){function r(e,t,n){this.ecCodewords=e,this.ecBlocks=[t],n&&this.ecBlocks.push(n)}return r.prototype.getECCodewords=function(){return this.ecCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r}(),W=function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r}(),Fo=function(){function r(e,t,n,i,a,o){var s,u;this.versionNumber=e,this.symbolSizeRows=t,this.symbolSizeColumns=n,this.dataRegionSizeRows=i,this.dataRegionSizeColumns=a,this.ecBlocks=o;var f=0,c=o.getECCodewords(),h=o.getECBlocks();try{for(var d=zr(h),l=d.next();!l.done;l=d.next()){var v=l.value;f+=v.getCount()*(v.getDataCodewords()+c)}}catch(p){s={error:p}}finally{try{l&&!l.done&&(u=d.return)&&u.call(d)}finally{if(s)throw s.error}}this.totalCodewords=f}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},r.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},r.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},r.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r.getVersionForDimensions=function(e,t){var n,i;if(1&e||1&t)throw new O;try{for(var a=zr(r.VERSIONS),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===t)return s}}catch(u){n={error:u}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}throw new O},r.prototype.toString=function(){return""+this.versionNumber},r.buildVersions=function(){return[new r(1,10,10,8,8,new Z(5,new W(1,3))),new r(2,12,12,10,10,new Z(7,new W(1,5))),new r(3,14,14,12,12,new Z(10,new W(1,8))),new r(4,16,16,14,14,new Z(12,new W(1,12))),new r(5,18,18,16,16,new Z(14,new W(1,18))),new r(6,20,20,18,18,new Z(18,new W(1,22))),new r(7,22,22,20,20,new Z(20,new W(1,30))),new r(8,24,24,22,22,new Z(24,new W(1,36))),new r(9,26,26,24,24,new Z(28,new W(1,44))),new r(10,32,32,14,14,new Z(36,new W(1,62))),new r(11,36,36,16,16,new Z(42,new W(1,86))),new r(12,40,40,18,18,new Z(48,new W(1,114))),new r(13,44,44,20,20,new Z(56,new W(1,144))),new r(14,48,48,22,22,new Z(68,new W(1,174))),new r(15,52,52,24,24,new Z(42,new W(2,102))),new r(16,64,64,14,14,new Z(56,new W(2,140))),new r(17,72,72,16,16,new Z(36,new W(4,92))),new r(18,80,80,18,18,new Z(48,new W(4,114))),new r(19,88,88,20,20,new Z(56,new W(4,144))),new r(20,96,96,22,22,new Z(68,new W(4,174))),new r(21,104,104,24,24,new Z(56,new W(6,136))),new r(22,120,120,18,18,new Z(68,new W(6,175))),new r(23,132,132,20,20,new Z(62,new W(8,163))),new r(24,144,144,22,22,new Z(62,new W(8,156),new W(2,155))),new r(25,8,18,6,16,new Z(7,new W(1,5))),new r(26,8,32,6,14,new Z(11,new W(1,10))),new r(27,12,26,10,24,new Z(14,new W(1,16))),new r(28,12,36,10,16,new Z(18,new W(1,22))),new r(29,16,36,14,16,new Z(24,new W(1,32))),new r(30,16,48,14,22,new Z(28,new W(1,49)))]},r.VERSIONS=r.buildVersions(),r}();const Lo=Fo;var ko=function(){function r(e){var t=e.getHeight();if(t<8||t>144||1&t)throw new O;this.version=r.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new me(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return r.prototype.getVersion=function(){return this.version},r.readVersion=function(e){var t=e.getHeight(),n=e.getWidth();return Lo.getVersionForDimensions(t,n)},r.prototype.readCodewords=function(){var e=new Int8Array(this.version.getTotalCodewords()),t=0,n=4,i=0,a=this.mappingBitMatrix.getHeight(),o=this.mappingBitMatrix.getWidth(),s=!1,u=!1,f=!1,c=!1;do{if(n!==a||0!==i||s)if(n===a-2&&0===i&&3&o&&!u)e[t++]=255&this.readCorner2(a,o),n-=2,i+=2,u=!0;else if(n!==a+4||2!==i||7&o||f)if(n!==a-2||0!==i||4!=(7&o)||c){do{n<a&&i>=0&&!this.readMappingMatrix.get(i,n)&&(e[t++]=255&this.readUtah(n,i,a,o)),n-=2,i+=2}while(n>=0&&i<o);n+=1,i+=3;do{n>=0&&i<o&&!this.readMappingMatrix.get(i,n)&&(e[t++]=255&this.readUtah(n,i,a,o)),n+=2,i-=2}while(n<a&&i>=0);n+=3,i+=1}else e[t++]=255&this.readCorner4(a,o),n-=2,i+=2,c=!0;else e[t++]=255&this.readCorner3(a,o),n-=2,i+=2,f=!0;else e[t++]=255&this.readCorner1(a,o),n-=2,i+=2,s=!0}while(n<a||i<o);if(t!==this.version.getTotalCodewords())throw new O;return e},r.prototype.readModule=function(e,t,n,i){return e<0&&(e+=n,t+=4-(n+4&7)),t<0&&(t+=i,e+=4-(i+4&7)),this.readMappingMatrix.set(t,e),this.mappingBitMatrix.get(t,e)},r.prototype.readUtah=function(e,t,n,i){var a=0;return this.readModule(e-2,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-2,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t,n,i)&&(a|=1),a<<=1,this.readModule(e,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e,t,n,i)&&(a|=1),a},r.prototype.readCorner1=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,1,e,t)&&(n|=1),n<<=1,this.readModule(e-1,2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.readCorner2=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-4,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner3=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-3,e,t)&&(n|=1),n<<=1,this.readModule(1,t-2,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner4=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.extractDataRegion=function(e){var t=this.version.getSymbolSizeRows(),n=this.version.getSymbolSizeColumns();if(e.getHeight()!==t)throw new R("Dimension of bitMatrix must match the version size");for(var i=this.version.getDataRegionSizeRows(),a=this.version.getDataRegionSizeColumns(),o=t/i|0,s=n/a|0,c=new me(s*a,o*i),h=0;h<o;++h)for(var d=h*i,l=0;l<s;++l)for(var v=l*a,p=0;p<i;++p)for(var x=h*(i+2)+1+p,w=d+p,y=0;y<a;++y)e.get(l*(a+2)+1+y,x)&&c.set(v+y,w);return c},r}();const Uo=ko;var Zr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Vo=function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t){var n,i,a,o,s=t.getECBlocks(),u=0,f=s.getECBlocks();try{for(var c=Zr(f),h=c.next();!h.done;h=c.next())u+=(d=h.value).getCount()}catch(Ne){n={error:Ne}}finally{try{h&&!h.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}var l=new Array(u),v=0;try{for(var p=Zr(f),x=p.next();!x.done;x=p.next())for(var d=x.value,w=0;w<d.getCount();w++){var y=d.getDataCodewords(),_=s.getECCodewords()+y;l[v++]=new r(y,new Uint8Array(_))}}catch(Ne){a={error:Ne}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}var m=l[0].codewords.length-s.getECCodewords(),S=m-1,I=0;for(w=0;w<S;w++)for(var D=0;D<v;D++)l[D].codewords[w]=e[I++];var M=24===t.getVersionNumber(),P=M?8:v;for(D=0;D<P;D++)l[D].codewords[m-1]=e[I++];var ie=l[0].codewords.length;for(w=m;w<ie;w++)for(D=0;D<v;D++){var H=M?(D+8)%v:D;l[H].codewords[M&&H>7?w-1:w]=e[I++]}if(I!==e.length)throw new R;return l},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r}();const Ho=Vo;var Go=function(){function r(e){this.bytes=e,this.byteOffset=0,this.bitOffset=0}return r.prototype.getBitOffset=function(){return this.bitOffset},r.prototype.getByteOffset=function(){return this.byteOffset},r.prototype.readBits=function(e){if(e<1||e>32||e>this.available())throw new R(""+e);var t=0,n=this.bitOffset,i=this.byteOffset,a=this.bytes;if(n>0){var o=8-n,s=e<o?e:o;t=(a[i]&255>>8-s<<(u=o-s))>>u,e-=s,8===(n+=s)&&(n=0,i++)}if(e>0){for(;e>=8;)t=t<<8|255&a[i],i++,e-=8;var u;e>0&&(t=t<<e|(a[i]&255>>(u=8-e)<<u)>>u,n+=e)}return this.bitOffset=n,this.byteOffset=i,t},r.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},r}();const jr=Go;var se=(()=>(function(r){r[r.PAD_ENCODE=0]="PAD_ENCODE",r[r.ASCII_ENCODE=1]="ASCII_ENCODE",r[r.C40_ENCODE=2]="C40_ENCODE",r[r.TEXT_ENCODE=3]="TEXT_ENCODE",r[r.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",r[r.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",r[r.BASE256_ENCODE=6]="BASE256_ENCODE"}(se||(se={})),se))(),Wo=function(){function r(){}return r.decode=function(e){var t=new jr(e),n=new F,i=new F,a=new Array,o=se.ASCII_ENCODE;do{if(o===se.ASCII_ENCODE)o=this.decodeAsciiSegment(t,n,i);else{switch(o){case se.C40_ENCODE:this.decodeC40Segment(t,n);break;case se.TEXT_ENCODE:this.decodeTextSegment(t,n);break;case se.ANSIX12_ENCODE:this.decodeAnsiX12Segment(t,n);break;case se.EDIFACT_ENCODE:this.decodeEdifactSegment(t,n);break;case se.BASE256_ENCODE:this.decodeBase256Segment(t,n,a);break;default:throw new O}o=se.ASCII_ENCODE}}while(o!==se.PAD_ENCODE&&t.available()>0);return i.length()>0&&n.append(i.toString()),new Ot(e,n.toString(),0===a.length?null:a,null)},r.decodeAsciiSegment=function(e,t,n){var i=!1;do{var a=e.readBits(8);if(0===a)throw new O;if(a<=128)return i&&(a+=128),t.append(String.fromCharCode(a-1)),se.ASCII_ENCODE;if(129===a)return se.PAD_ENCODE;if(a<=229){var o=a-130;o<10&&t.append("0"),t.append(""+o)}else switch(a){case 230:return se.C40_ENCODE;case 231:return se.BASE256_ENCODE;case 232:t.append(String.fromCharCode(29));break;case 233:case 234:case 241:break;case 235:i=!0;break;case 236:t.append("[)>\x1e05\x1d"),n.insert(0,"\x1e\x04");break;case 237:t.append("[)>\x1e06\x1d"),n.insert(0,"\x1e\x04");break;case 238:return se.ANSIX12_ENCODE;case 239:return se.TEXT_ENCODE;case 240:return se.EDIFACT_ENCODE;default:if(254!==a||0!==e.available())throw new O}}while(e.available()>0);return se.ASCII_ENCODE},r.decodeC40Segment=function(e,t){var n=!1,i=[],a=0;do{if(8===e.available())return;var o=e.readBits(8);if(254===o)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var u=i[s];switch(a){case 0:if(u<3)a=u+1;else{if(!(u<this.C40_BASIC_SET_CHARS.length))throw new O;var f=this.C40_BASIC_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}break;case 1:n?(t.append(String.fromCharCode(u+128)),n=!1):t.append(String.fromCharCode(u)),a=0;break;case 2:if(u<this.C40_SHIFT2_SET_CHARS.length)f=this.C40_SHIFT2_SET_CHARS[u],n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f);else switch(u){case 27:t.append(String.fromCharCode(29));break;case 30:n=!0;break;default:throw new O}a=0;break;case 3:n?(t.append(String.fromCharCode(u+224)),n=!1):t.append(String.fromCharCode(u+96)),a=0;break;default:throw new O}}}while(e.available()>0)},r.decodeTextSegment=function(e,t){var n=!1,i=[],a=0;do{if(8===e.available())return;var o=e.readBits(8);if(254===o)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var u=i[s];switch(a){case 0:if(u<3)a=u+1;else{if(!(u<this.TEXT_BASIC_SET_CHARS.length))throw new O;var f=this.TEXT_BASIC_SET_CHARS[u];n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f)}break;case 1:n?(t.append(String.fromCharCode(u+128)),n=!1):t.append(String.fromCharCode(u)),a=0;break;case 2:if(u<this.TEXT_SHIFT2_SET_CHARS.length)f=this.TEXT_SHIFT2_SET_CHARS[u],n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f);else switch(u){case 27:t.append(String.fromCharCode(29));break;case 30:n=!0;break;default:throw new O}a=0;break;case 3:if(!(u<this.TEXT_SHIFT3_SET_CHARS.length))throw new O;f=this.TEXT_SHIFT3_SET_CHARS[u],n?(t.append(String.fromCharCode(f.charCodeAt(0)+128)),n=!1):t.append(f),a=0;break;default:throw new O}}}while(e.available()>0)},r.decodeAnsiX12Segment=function(e,t){var n=[];do{if(8===e.available())return;var i=e.readBits(8);if(254===i)return;this.parseTwoBytes(i,e.readBits(8),n);for(var a=0;a<3;a++){var o=n[a];switch(o){case 0:t.append("\r");break;case 1:t.append("*");break;case 2:t.append(">");break;case 3:t.append(" ");break;default:if(o<14)t.append(String.fromCharCode(o+44));else{if(!(o<40))throw new O;t.append(String.fromCharCode(o+51))}}}}while(e.available()>0)},r.parseTwoBytes=function(e,t,n){var i=(e<<8)+t-1,a=Math.floor(i/1600);n[0]=a,i-=1600*a,a=Math.floor(i/40),n[1]=a,n[2]=i-40*a},r.decodeEdifactSegment=function(e,t){do{if(e.available()<=16)return;for(var n=0;n<4;n++){var i=e.readBits(6);if(31===i){var a=8-e.getBitOffset();return void(8!==a&&e.readBits(a))}32&i||(i|=64),t.append(String.fromCharCode(i))}}while(e.available()>0)},r.decodeBase256Segment=function(e,t,n){var o,i=1+e.getByteOffset(),a=this.unrandomize255State(e.readBits(8),i++);if((o=0===a?e.available()/8|0:a<250?a:250*(a-249)+this.unrandomize255State(e.readBits(8),i++))<0)throw new O;for(var s=new Uint8Array(o),u=0;u<o;u++){if(e.available()<8)throw new O;s[u]=this.unrandomize255State(e.readBits(8),i++)}n.push(s);try{t.append(De.decode(s,U.ISO88591))}catch(f){throw new Be("Platform does not support required encoding: "+f.message)}},r.unrandomize255State=function(e,t){var i=e-(149*t%255+1);return i>=0?i:i+256},r.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],r.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],r.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],r.TEXT_SHIFT2_SET_CHARS=r.C40_SHIFT2_SET_CHARS,r.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",String.fromCharCode(127)],r}();const Xo=Wo;var Zo=function(){function r(){this.rsDecoder=new Dt(Se.DATA_MATRIX_FIELD_256)}return r.prototype.decode=function(e){var t,n,i=new Uo(e),a=i.getVersion(),o=i.readCodewords(),s=Ho.getDataBlocks(o,a),u=0;try{for(var f=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(s),c=f.next();!c.done;c=f.next())u+=c.value.getNumDataCodewords()}catch(_){t={error:_}}finally{try{c&&!c.done&&(n=f.return)&&n.call(f)}finally{if(t)throw t.error}}for(var d=new Uint8Array(u),l=s.length,v=0;v<l;v++){var p=s[v],x=p.getCodewords(),w=p.getNumDataCodewords();this.correctErrors(x,w);for(var y=0;y<w;y++)d[y*l+v]=x[y]}return Xo.decode(d)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new ae}for(var i=0;i<t;i++)e[i]=n[i]},r}();const jo=Zo;var Yo=function(){function r(e){this.image=e,this.rectangleDetector=new Jt(this.image)}return r.prototype.detect=function(){var e=this.rectangleDetector.detect(),t=this.detectSolid1(e);if((t=this.detectSolid2(t))[3]=this.correctTopRight(t),!t[3])throw new E;var n=(t=this.shiftToModuleCenter(t))[0],i=t[1],a=t[2],o=t[3],s=this.transitionsBetween(n,o)+1,u=this.transitionsBetween(a,o)+1;1==(1&s)&&(s+=1),1==(1&u)&&(u+=1),4*s<7*u&&4*u<7*s&&(s=u=Math.max(s,u));var f=r.sampleGrid(this.image,n,i,a,o,s,u);return new Qt(f,[n,i,a,o])},r.shiftPoint=function(e,t,n){var i=(t.getX()-e.getX())/(n+1),a=(t.getY()-e.getY())/(n+1);return new b(e.getX()+i,e.getY()+a)},r.moveAway=function(e,t,n){var i=e.getX(),a=e.getY();return i<t?i-=1:i+=1,a<n?a-=1:a+=1,new b(i,a)},r.prototype.detectSolid1=function(e){var t=e[0],n=e[1],i=e[3],a=e[2],o=this.transitionsBetween(t,n),s=this.transitionsBetween(n,i),u=this.transitionsBetween(i,a),f=this.transitionsBetween(a,t),c=o,h=[a,t,n,i];return c>s&&(c=s,h[0]=t,h[1]=n,h[2]=i,h[3]=a),c>u&&(c=u,h[0]=n,h[1]=i,h[2]=a,h[3]=t),c>f&&(h[0]=i,h[1]=a,h[2]=t,h[3]=n),h},r.prototype.detectSolid2=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=r.shiftPoint(n,i,4*(o+1)),u=r.shiftPoint(i,n,4*(o+1));return this.transitionsBetween(s,t)<this.transitionsBetween(u,a)?(e[0]=t,e[1]=n,e[2]=i,e[3]=a):(e[0]=n,e[1]=i,e[2]=a,e[3]=t),e},r.prototype.correctTopRight=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=this.transitionsBetween(n,a),u=r.shiftPoint(t,n,4*(s+1)),f=r.shiftPoint(i,n,4*(o+1));o=this.transitionsBetween(u,a),s=this.transitionsBetween(f,a);var c=new b(a.getX()+(i.getX()-n.getX())/(o+1),a.getY()+(i.getY()-n.getY())/(o+1)),h=new b(a.getX()+(t.getX()-n.getX())/(s+1),a.getY()+(t.getY()-n.getY())/(s+1));return this.isValid(c)?this.isValid(h)?this.transitionsBetween(u,c)+this.transitionsBetween(f,c)>this.transitionsBetween(u,h)+this.transitionsBetween(f,h)?c:h:c:this.isValid(h)?h:null},r.prototype.shiftToModuleCenter=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a)+1,s=this.transitionsBetween(i,a)+1,u=r.shiftPoint(t,n,4*s),f=r.shiftPoint(i,n,4*o);1==(1&(o=this.transitionsBetween(u,a)+1))&&(o+=1),1==(1&(s=this.transitionsBetween(f,a)+1))&&(s+=1);var d,l,c=(t.getX()+n.getX()+i.getX()+a.getX())/4,h=(t.getY()+n.getY()+i.getY()+a.getY())/4;return t=r.moveAway(t,c,h),n=r.moveAway(n,c,h),i=r.moveAway(i,c,h),a=r.moveAway(a,c,h),u=r.shiftPoint(t,n,4*s),u=r.shiftPoint(u,a,4*o),d=r.shiftPoint(n,t,4*s),d=r.shiftPoint(d,i,4*o),f=r.shiftPoint(i,a,4*s),f=r.shiftPoint(f,n,4*o),l=r.shiftPoint(a,i,4*s),[u,d,f,l=r.shiftPoint(l,t,4*o)]},r.prototype.isValid=function(e){return e.getX()>=0&&e.getX()<this.image.getWidth()&&e.getY()>0&&e.getY()<this.image.getHeight()},r.sampleGrid=function(e,t,n,i,a,o,s){return $t.getInstance().sampleGrid(e,o,s,.5,.5,o-.5,.5,o-.5,s-.5,.5,s-.5,t.getX(),t.getY(),a.getX(),a.getY(),i.getX(),i.getY(),n.getX(),n.getY())},r.prototype.transitionsBetween=function(e,t){var n=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.trunc(t.getX()),o=Math.trunc(t.getY()),s=Math.abs(o-i)>Math.abs(a-n);if(s){var u=n;n=i,i=u,u=a,a=o,o=u}for(var f=Math.abs(a-n),c=Math.abs(o-i),h=-f/2,d=i<o?1:-1,l=n<a?1:-1,v=0,p=this.image.get(s?i:n,s?n:i),x=n,w=i;x!==a;x+=l){var y=this.image.get(s?w:x,s?x:w);if(y!==p&&(v++,p=y),(h+=c)>0){if(w===o)break;w+=d,h-=f}}return v},r}();const Ko=Yo;var qo=function(){function r(){this.decoder=new jo}return r.prototype.decode=function(e,t){var n,i;if(void 0===t&&(t=null),null!=t&&t.has(K.PURE_BARCODE)){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(a),i=r.NO_POINTS}else{var o=new Ko(e.getBlackMatrix()).detect();n=this.decoder.decode(o.getBits()),i=o.getPoints()}var s=n.getRawBytes(),u=new we(n.getText(),s,8*s.length,i,N.DATA_MATRIX,q.currentTimeMillis()),f=n.getByteSegments();null!=f&&u.putMetadata(xe.BYTE_SEGMENTS,f);var c=n.getECLevel();return null!=c&&u.putMetadata(xe.ERROR_CORRECTION_LEVEL,c),u},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(null==t||null==n)throw new E;var i=this.moduleSize(t,e),a=t[1],s=t[0],f=(n[0]-s+1)/i,c=(n[1]-a+1)/i;if(f<=0||c<=0)throw new E;var h=i/2;a+=h,s+=h;for(var d=new me(f,c),l=0;l<c;l++)for(var v=a+l*i,p=0;p<f;p++)e.get(s+p*i,v)&&d.set(p,l);return d},r.moduleSize=function(e,t){for(var n=t.getWidth(),i=e[0],a=e[1];i<n&&t.get(i,a);)i++;if(i===n)throw new E;var o=i-e[0];if(0===o)throw new E;return o},r.NO_POINTS=[],r}();const Bt=qo;var Qo=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Qe=(function(r){Qo(function e(t){return void 0===t&&(t=500),r.call(this,new Bt,t)||this},r)}(nt),(()=>(function(r){r[r.L=0]="L",r[r.M=1]="M",r[r.Q=2]="Q",r[r.H=3]="H"}(Qe||(Qe={})),Qe))()),Jo=function(){function r(e,t,n){this.value=e,this.stringValue=t,this.bits=n,r.FOR_BITS.set(n,this),r.FOR_VALUE.set(e,this)}return r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.fromString=function(e){switch(e){case"L":return r.L;case"M":return r.M;case"Q":return r.Q;case"H":return r.H;default:throw new ne(e+"not available")}},r.prototype.toString=function(){return this.stringValue},r.prototype.equals=function(e){return e instanceof r&&this.value===e.value},r.forBits=function(e){if(e<0||e>=r.FOR_BITS.size)throw new R;return r.FOR_BITS.get(e)},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.L=new r(Qe.L,"L",1),r.M=new r(Qe.M,"M",0),r.Q=new r(Qe.Q,"Q",3),r.H=new r(Qe.H,"H",2),r}();const Le=Jo;var e1=function(){function r(e){this.errorCorrectionLevel=Le.forBits(e>>3&3),this.dataMask=7&e}return r.numBitsDiffering=function(e,t){return B.bitCount(e^t)},r.decodeFormatInformation=function(e,t){var n=r.doDecodeFormatInformation(e,t);return null!==n?n:r.doDecodeFormatInformation(e^r.FORMAT_INFO_MASK_QR,t^r.FORMAT_INFO_MASK_QR)},r.doDecodeFormatInformation=function(e,t){var n,i,a=Number.MAX_SAFE_INTEGER,o=0;try{for(var s=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(r.FORMAT_INFO_DECODE_LOOKUP),u=s.next();!u.done;u=s.next()){var f=u.value,c=f[0];if(c===e||c===t)return new r(f[1]);var h=r.numBitsDiffering(e,c);h<a&&(o=f[1],a=h),e!==t&&(h=r.numBitsDiffering(t,c))<a&&(o=f[1],a=h)}}catch(d){n={error:d}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(n)throw n.error}}return a<=3?new r(o):null},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getDataMask=function(){return this.dataMask},r.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},r.prototype.equals=function(e){return e instanceof r&&(this.errorCorrectionLevel===e.errorCorrectionLevel&&this.dataMask===e.dataMask)},r.FORMAT_INFO_MASK_QR=21522,r.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],r}();const Yr=e1;var r1=function(){function r(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.ecCodewordsPerBlock=e,this.ecBlocks=t}return r.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},r.prototype.getNumBlocks=function(){var e,t,n=0,i=this.ecBlocks;try{for(var a=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(i),o=a.next();!o.done;o=a.next())n+=o.value.getCount()}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n},r.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},r.prototype.getECBlocks=function(){return this.ecBlocks},r}();const A=r1;var n1=function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r}();const g=n1;var i1=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a1=function(){function r(e,t){for(var n,i,a=[],o=2;o<arguments.length;o++)a[o-2]=arguments[o];this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=a;var s=0,u=a[0].getECCodewordsPerBlock(),f=a[0].getECBlocks();try{for(var c=i1(f),h=c.next();!h.done;h=c.next()){var d=h.value;s+=d.getCount()*(d.getDataCodewords()+u)}}catch(l){n={error:l}}finally{try{h&&!h.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}this.totalCodewords=s}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},r.prototype.getECBlocksForLevel=function(e){return this.ecBlocks[e.getValue()]},r.getProvisionalVersionForDimension=function(e){if(e%4!=1)throw new O;try{return this.getVersionForNumber((e-17)/4)}catch{throw new O}},r.getVersionForNumber=function(e){if(e<1||e>40)throw new R;return r.VERSIONS[e-1]},r.decodeVersionInformation=function(e){for(var t=Number.MAX_SAFE_INTEGER,n=0,i=0;i<r.VERSION_DECODE_INFO.length;i++){var a=r.VERSION_DECODE_INFO[i];if(a===e)return r.getVersionForNumber(i+7);var o=Yr.numBitsDiffering(e,a);o<t&&(n=i+7,t=o)}return t<=3?r.getVersionForNumber(n):null},r.prototype.buildFunctionPattern=function(){var e=this.getDimensionForVersion(),t=new me(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);for(var n=this.alignmentPatternCenters.length,i=0;i<n;i++)for(var a=this.alignmentPatternCenters[i]-2,o=0;o<n;o++)0===i&&(0===o||o===n-1)||i===n-1&&0===o||t.setRegion(this.alignmentPatternCenters[o]-2,a,5,5);return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t},r.prototype.toString=function(){return""+this.versionNumber},r.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),r.VERSIONS=[new r(1,new Int32Array(0),new A(7,new g(1,19)),new A(10,new g(1,16)),new A(13,new g(1,13)),new A(17,new g(1,9))),new r(2,Int32Array.from([6,18]),new A(10,new g(1,34)),new A(16,new g(1,28)),new A(22,new g(1,22)),new A(28,new g(1,16))),new r(3,Int32Array.from([6,22]),new A(15,new g(1,55)),new A(26,new g(1,44)),new A(18,new g(2,17)),new A(22,new g(2,13))),new r(4,Int32Array.from([6,26]),new A(20,new g(1,80)),new A(18,new g(2,32)),new A(26,new g(2,24)),new A(16,new g(4,9))),new r(5,Int32Array.from([6,30]),new A(26,new g(1,108)),new A(24,new g(2,43)),new A(18,new g(2,15),new g(2,16)),new A(22,new g(2,11),new g(2,12))),new r(6,Int32Array.from([6,34]),new A(18,new g(2,68)),new A(16,new g(4,27)),new A(24,new g(4,19)),new A(28,new g(4,15))),new r(7,Int32Array.from([6,22,38]),new A(20,new g(2,78)),new A(18,new g(4,31)),new A(18,new g(2,14),new g(4,15)),new A(26,new g(4,13),new g(1,14))),new r(8,Int32Array.from([6,24,42]),new A(24,new g(2,97)),new A(22,new g(2,38),new g(2,39)),new A(22,new g(4,18),new g(2,19)),new A(26,new g(4,14),new g(2,15))),new r(9,Int32Array.from([6,26,46]),new A(30,new g(2,116)),new A(22,new g(3,36),new g(2,37)),new A(20,new g(4,16),new g(4,17)),new A(24,new g(4,12),new g(4,13))),new r(10,Int32Array.from([6,28,50]),new A(18,new g(2,68),new g(2,69)),new A(26,new g(4,43),new g(1,44)),new A(24,new g(6,19),new g(2,20)),new A(28,new g(6,15),new g(2,16))),new r(11,Int32Array.from([6,30,54]),new A(20,new g(4,81)),new A(30,new g(1,50),new g(4,51)),new A(28,new g(4,22),new g(4,23)),new A(24,new g(3,12),new g(8,13))),new r(12,Int32Array.from([6,32,58]),new A(24,new g(2,92),new g(2,93)),new A(22,new g(6,36),new g(2,37)),new A(26,new g(4,20),new g(6,21)),new A(28,new g(7,14),new g(4,15))),new r(13,Int32Array.from([6,34,62]),new A(26,new g(4,107)),new A(22,new g(8,37),new g(1,38)),new A(24,new g(8,20),new g(4,21)),new A(22,new g(12,11),new g(4,12))),new r(14,Int32Array.from([6,26,46,66]),new A(30,new g(3,115),new g(1,116)),new A(24,new g(4,40),new g(5,41)),new A(20,new g(11,16),new g(5,17)),new A(24,new g(11,12),new g(5,13))),new r(15,Int32Array.from([6,26,48,70]),new A(22,new g(5,87),new g(1,88)),new A(24,new g(5,41),new g(5,42)),new A(30,new g(5,24),new g(7,25)),new A(24,new g(11,12),new g(7,13))),new r(16,Int32Array.from([6,26,50,74]),new A(24,new g(5,98),new g(1,99)),new A(28,new g(7,45),new g(3,46)),new A(24,new g(15,19),new g(2,20)),new A(30,new g(3,15),new g(13,16))),new r(17,Int32Array.from([6,30,54,78]),new A(28,new g(1,107),new g(5,108)),new A(28,new g(10,46),new g(1,47)),new A(28,new g(1,22),new g(15,23)),new A(28,new g(2,14),new g(17,15))),new r(18,Int32Array.from([6,30,56,82]),new A(30,new g(5,120),new g(1,121)),new A(26,new g(9,43),new g(4,44)),new A(28,new g(17,22),new g(1,23)),new A(28,new g(2,14),new g(19,15))),new r(19,Int32Array.from([6,30,58,86]),new A(28,new g(3,113),new g(4,114)),new A(26,new g(3,44),new g(11,45)),new A(26,new g(17,21),new g(4,22)),new A(26,new g(9,13),new g(16,14))),new r(20,Int32Array.from([6,34,62,90]),new A(28,new g(3,107),new g(5,108)),new A(26,new g(3,41),new g(13,42)),new A(30,new g(15,24),new g(5,25)),new A(28,new g(15,15),new g(10,16))),new r(21,Int32Array.from([6,28,50,72,94]),new A(28,new g(4,116),new g(4,117)),new A(26,new g(17,42)),new A(28,new g(17,22),new g(6,23)),new A(30,new g(19,16),new g(6,17))),new r(22,Int32Array.from([6,26,50,74,98]),new A(28,new g(2,111),new g(7,112)),new A(28,new g(17,46)),new A(30,new g(7,24),new g(16,25)),new A(24,new g(34,13))),new r(23,Int32Array.from([6,30,54,78,102]),new A(30,new g(4,121),new g(5,122)),new A(28,new g(4,47),new g(14,48)),new A(30,new g(11,24),new g(14,25)),new A(30,new g(16,15),new g(14,16))),new r(24,Int32Array.from([6,28,54,80,106]),new A(30,new g(6,117),new g(4,118)),new A(28,new g(6,45),new g(14,46)),new A(30,new g(11,24),new g(16,25)),new A(30,new g(30,16),new g(2,17))),new r(25,Int32Array.from([6,32,58,84,110]),new A(26,new g(8,106),new g(4,107)),new A(28,new g(8,47),new g(13,48)),new A(30,new g(7,24),new g(22,25)),new A(30,new g(22,15),new g(13,16))),new r(26,Int32Array.from([6,30,58,86,114]),new A(28,new g(10,114),new g(2,115)),new A(28,new g(19,46),new g(4,47)),new A(28,new g(28,22),new g(6,23)),new A(30,new g(33,16),new g(4,17))),new r(27,Int32Array.from([6,34,62,90,118]),new A(30,new g(8,122),new g(4,123)),new A(28,new g(22,45),new g(3,46)),new A(30,new g(8,23),new g(26,24)),new A(30,new g(12,15),new g(28,16))),new r(28,Int32Array.from([6,26,50,74,98,122]),new A(30,new g(3,117),new g(10,118)),new A(28,new g(3,45),new g(23,46)),new A(30,new g(4,24),new g(31,25)),new A(30,new g(11,15),new g(31,16))),new r(29,Int32Array.from([6,30,54,78,102,126]),new A(30,new g(7,116),new g(7,117)),new A(28,new g(21,45),new g(7,46)),new A(30,new g(1,23),new g(37,24)),new A(30,new g(19,15),new g(26,16))),new r(30,Int32Array.from([6,26,52,78,104,130]),new A(30,new g(5,115),new g(10,116)),new A(28,new g(19,47),new g(10,48)),new A(30,new g(15,24),new g(25,25)),new A(30,new g(23,15),new g(25,16))),new r(31,Int32Array.from([6,30,56,82,108,134]),new A(30,new g(13,115),new g(3,116)),new A(28,new g(2,46),new g(29,47)),new A(30,new g(42,24),new g(1,25)),new A(30,new g(23,15),new g(28,16))),new r(32,Int32Array.from([6,34,60,86,112,138]),new A(30,new g(17,115)),new A(28,new g(10,46),new g(23,47)),new A(30,new g(10,24),new g(35,25)),new A(30,new g(19,15),new g(35,16))),new r(33,Int32Array.from([6,30,58,86,114,142]),new A(30,new g(17,115),new g(1,116)),new A(28,new g(14,46),new g(21,47)),new A(30,new g(29,24),new g(19,25)),new A(30,new g(11,15),new g(46,16))),new r(34,Int32Array.from([6,34,62,90,118,146]),new A(30,new g(13,115),new g(6,116)),new A(28,new g(14,46),new g(23,47)),new A(30,new g(44,24),new g(7,25)),new A(30,new g(59,16),new g(1,17))),new r(35,Int32Array.from([6,30,54,78,102,126,150]),new A(30,new g(12,121),new g(7,122)),new A(28,new g(12,47),new g(26,48)),new A(30,new g(39,24),new g(14,25)),new A(30,new g(22,15),new g(41,16))),new r(36,Int32Array.from([6,24,50,76,102,128,154]),new A(30,new g(6,121),new g(14,122)),new A(28,new g(6,47),new g(34,48)),new A(30,new g(46,24),new g(10,25)),new A(30,new g(2,15),new g(64,16))),new r(37,Int32Array.from([6,28,54,80,106,132,158]),new A(30,new g(17,122),new g(4,123)),new A(28,new g(29,46),new g(14,47)),new A(30,new g(49,24),new g(10,25)),new A(30,new g(24,15),new g(46,16))),new r(38,Int32Array.from([6,32,58,84,110,136,162]),new A(30,new g(4,122),new g(18,123)),new A(28,new g(13,46),new g(32,47)),new A(30,new g(48,24),new g(14,25)),new A(30,new g(42,15),new g(32,16))),new r(39,Int32Array.from([6,26,54,82,110,138,166]),new A(30,new g(20,117),new g(4,118)),new A(28,new g(40,47),new g(7,48)),new A(30,new g(43,24),new g(22,25)),new A(30,new g(10,15),new g(67,16))),new r(40,Int32Array.from([6,30,58,86,114,142,170]),new A(30,new g(19,118),new g(6,119)),new A(28,new g(18,47),new g(31,48)),new A(30,new g(34,24),new g(34,25)),new A(30,new g(20,15),new g(61,16)))],r}();const Je=a1;var de=(()=>(function(r){r[r.DATA_MASK_000=0]="DATA_MASK_000",r[r.DATA_MASK_001=1]="DATA_MASK_001",r[r.DATA_MASK_010=2]="DATA_MASK_010",r[r.DATA_MASK_011=3]="DATA_MASK_011",r[r.DATA_MASK_100=4]="DATA_MASK_100",r[r.DATA_MASK_101=5]="DATA_MASK_101",r[r.DATA_MASK_110=6]="DATA_MASK_110",r[r.DATA_MASK_111=7]="DATA_MASK_111"}(de||(de={})),de))(),o1=function(){function r(e,t){this.value=e,this.isMasked=t}return r.prototype.unmaskBitMatrix=function(e,t){for(var n=0;n<t;n++)for(var i=0;i<t;i++)this.isMasked(n,i)&&e.flip(i,n)},r.values=new Map([[de.DATA_MASK_000,new r(de.DATA_MASK_000,function(e,t){return 0==(e+t&1)})],[de.DATA_MASK_001,new r(de.DATA_MASK_001,function(e,t){return 0==(1&e)})],[de.DATA_MASK_010,new r(de.DATA_MASK_010,function(e,t){return t%3==0})],[de.DATA_MASK_011,new r(de.DATA_MASK_011,function(e,t){return(e+t)%3==0})],[de.DATA_MASK_100,new r(de.DATA_MASK_100,function(e,t){return 0==(Math.floor(e/2)+Math.floor(t/3)&1)})],[de.DATA_MASK_101,new r(de.DATA_MASK_101,function(e,t){return e*t%6==0})],[de.DATA_MASK_110,new r(de.DATA_MASK_110,function(e,t){return e*t%6<3})],[de.DATA_MASK_111,new r(de.DATA_MASK_111,function(e,t){return 0==(e+t+e*t%3&1)})]]),r}();const Kr=o1;var s1=function(){function r(e){var t=e.getHeight();if(t<21||1!=(3&t))throw new O;this.bitMatrix=e}return r.prototype.readFormatInformation=function(){if(null!=this.parsedFormatInfo)return this.parsedFormatInfo;for(var e=0,t=0;t<6;t++)e=this.copyBit(t,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(var n=5;n>=0;n--)e=this.copyBit(8,n,e);var i=this.bitMatrix.getHeight(),a=0,o=i-7;for(n=i-1;n>=o;n--)a=this.copyBit(8,n,a);for(t=i-8;t<i;t++)a=this.copyBit(t,8,a);if(this.parsedFormatInfo=Yr.decodeFormatInformation(e,a),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new O},r.prototype.readVersion=function(){if(null!=this.parsedVersion)return this.parsedVersion;var e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return Je.getVersionForNumber(t);for(var n=0,i=e-11,a=5;a>=0;a--)for(var o=e-9;o>=i;o--)n=this.copyBit(o,a,n);var s=Je.decodeVersionInformation(n);if(null!==s&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;for(n=0,o=5;o>=0;o--)for(a=e-9;a>=i;a--)n=this.copyBit(o,a,n);if(null!==(s=Je.decodeVersionInformation(n))&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;throw new O},r.prototype.copyBit=function(e,t,n){return(this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t))?n<<1|1:n<<1},r.prototype.readCodewords=function(){var e=this.readFormatInformation(),t=this.readVersion(),n=Kr.values.get(e.getDataMask()),i=this.bitMatrix.getHeight();n.unmaskBitMatrix(this.bitMatrix,i);for(var a=t.buildFunctionPattern(),o=!0,s=new Uint8Array(t.getTotalCodewords()),u=0,f=0,c=0,h=i-1;h>0;h-=2){6===h&&h--;for(var d=0;d<i;d++)for(var l=o?i-1-d:d,v=0;v<2;v++)a.get(h-v,l)||(c++,f<<=1,this.bitMatrix.get(h-v,l)&&(f|=1),8===c&&(s[u++]=f,c=0,f=0));o=!o}if(u!==t.getTotalCodewords())throw new O;return s},r.prototype.remask=function(){if(null!==this.parsedFormatInfo){var e=Kr.values.get(this.parsedFormatInfo.getDataMask()),t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}},r.prototype.setMirror=function(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e},r.prototype.mirror=function(){for(var e=this.bitMatrix,t=0,n=e.getWidth();t<n;t++)for(var i=t+1,a=e.getHeight();i<a;i++)e.get(t,i)!==e.get(i,t)&&(e.flip(i,t),e.flip(t,i))},r}();const u1=s1;var qr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},f1=function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t,n){var i,a,o,s;if(e.length!==t.getTotalCodewords())throw new R;var u=t.getECBlocksForLevel(n),f=0,c=u.getECBlocks();try{for(var h=qr(c),d=h.next();!d.done;d=h.next())f+=(l=d.value).getCount()}catch(te){i={error:te}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}var v=new Array(f),p=0;try{for(var x=qr(c),w=x.next();!w.done;w=x.next())for(var l=w.value,y=0;y<l.getCount();y++){var _=l.getDataCodewords(),C=u.getECCodewordsPerBlock()+_;v[p++]=new r(_,new Uint8Array(C))}}catch(te){o={error:te}}finally{try{w&&!w.done&&(s=x.return)&&s.call(x)}finally{if(o)throw o.error}}for(var m=v[0].codewords.length,S=v.length-1;S>=0&&v[S].codewords.length!==m;)S--;S++;var D=m-u.getECCodewordsPerBlock(),M=0;for(y=0;y<D;y++)for(var P=0;P<p;P++)v[P].codewords[y]=e[M++];for(P=S;P<p;P++)v[P].codewords[D]=e[M++];var ie=v[0].codewords.length;for(y=D;y<ie;y++)for(P=0;P<p;P++)v[P].codewords[P<S?y:y+1]=e[M++];return v},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r}();const c1=f1;var Te=(()=>(function(r){r[r.TERMINATOR=0]="TERMINATOR",r[r.NUMERIC=1]="NUMERIC",r[r.ALPHANUMERIC=2]="ALPHANUMERIC",r[r.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",r[r.BYTE=4]="BYTE",r[r.ECI=5]="ECI",r[r.KANJI=6]="KANJI",r[r.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",r[r.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",r[r.HANZI=9]="HANZI"}(Te||(Te={})),Te))(),h1=function(){function r(e,t,n,i){this.value=e,this.stringValue=t,this.characterCountBitsForVersions=n,this.bits=i,r.FOR_BITS.set(i,this),r.FOR_VALUE.set(e,this)}return r.forBits=function(e){var t=r.FOR_BITS.get(e);if(void 0===t)throw new R;return t},r.prototype.getCharacterCountBits=function(e){var t=e.getVersionNumber();return this.characterCountBitsForVersions[t<=9?0:t<=26?1:2]},r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.prototype.equals=function(e){return e instanceof r&&this.value===e.value},r.prototype.toString=function(){return this.stringValue},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.TERMINATOR=new r(Te.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),r.NUMERIC=new r(Te.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),r.ALPHANUMERIC=new r(Te.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),r.STRUCTURED_APPEND=new r(Te.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),r.BYTE=new r(Te.BYTE,"BYTE",Int32Array.from([8,16,16]),4),r.ECI=new r(Te.ECI,"ECI",Int32Array.from([0,0,0]),7),r.KANJI=new r(Te.KANJI,"KANJI",Int32Array.from([8,10,12]),8),r.FNC1_FIRST_POSITION=new r(Te.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),r.FNC1_SECOND_POSITION=new r(Te.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),r.HANZI=new r(Te.HANZI,"HANZI",Int32Array.from([8,10,12]),13),r}();const Q=h1;var d1=function(){function r(){}return r.decode=function(e,t,n,i){var a=new jr(e),o=new F,s=new Array,u=-1,f=-1;try{var c=null,h=!1,d=void 0;do{if(a.available()<4)d=Q.TERMINATOR;else{var l=a.readBits(4);d=Q.forBits(l)}switch(d){case Q.TERMINATOR:break;case Q.FNC1_FIRST_POSITION:case Q.FNC1_SECOND_POSITION:h=!0;break;case Q.STRUCTURED_APPEND:if(a.available()<16)throw new O;u=a.readBits(8),f=a.readBits(8);break;case Q.ECI:var v=r.parseECIValue(a);if(null===(c=fe.getCharacterSetECIByValue(v)))throw new O;break;case Q.HANZI:var p=a.readBits(4),x=a.readBits(d.getCharacterCountBits(t));p===r.GB2312_SUBSET&&r.decodeHanziSegment(a,o,x);break;default:var w=a.readBits(d.getCharacterCountBits(t));switch(d){case Q.NUMERIC:r.decodeNumericSegment(a,o,w);break;case Q.ALPHANUMERIC:r.decodeAlphanumericSegment(a,o,w,h);break;case Q.BYTE:r.decodeByteSegment(a,o,w,c,s,i);break;case Q.KANJI:r.decodeKanjiSegment(a,o,w);break;default:throw new O}}}while(d!==Q.TERMINATOR)}catch{throw new O}return new Ot(e,o.toString(),0===s.length?null:s,null===n?null:n.toString(),u,f)},r.decodeHanziSegment=function(e,t,n){if(13*n>e.available())throw new O;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/96<<8&4294967295|o%96;i[a]=(s+=s<959?41377:42657)>>8&255,i[a+1]=255&s,a+=2,n--}try{t.append(De.decode(i,U.GB2312))}catch(u){throw new O(u)}},r.decodeKanjiSegment=function(e,t,n){if(13*n>e.available())throw new O;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/192<<8&4294967295|o%192;i[a]=(s+=s<7936?33088:49472)>>8,i[a+1]=s,a+=2,n--}try{t.append(De.decode(i,U.SHIFT_JIS))}catch(u){throw new O(u)}},r.decodeByteSegment=function(e,t,n,i,a,o){if(8*n>e.available())throw new O;for(var s=new Uint8Array(n),u=0;u<n;u++)s[u]=e.readBits(8);var f;f=null===i?U.guessEncoding(s,o):i.getName();try{t.append(De.decode(s,f))}catch(c){throw new O(c)}a.push(s)},r.toAlphaNumericChar=function(e){if(e>=r.ALPHANUMERIC_CHARS.length)throw new O;return r.ALPHANUMERIC_CHARS[e]},r.decodeAlphanumericSegment=function(e,t,n,i){for(var a=t.length();n>1;){if(e.available()<11)throw new O;var o=e.readBits(11);t.append(r.toAlphaNumericChar(Math.floor(o/45))),t.append(r.toAlphaNumericChar(o%45)),n-=2}if(1===n){if(e.available()<6)throw new O;t.append(r.toAlphaNumericChar(e.readBits(6)))}if(i)for(var s=a;s<t.length();s++)"%"===t.charAt(s)&&(s<t.length()-1&&"%"===t.charAt(s+1)?t.deleteCharAt(s+1):t.setCharAt(s,String.fromCharCode(29)))},r.decodeNumericSegment=function(e,t,n){for(;n>=3;){if(e.available()<10)throw new O;var i=e.readBits(10);if(i>=1e3)throw new O;t.append(r.toAlphaNumericChar(Math.floor(i/100))),t.append(r.toAlphaNumericChar(Math.floor(i/10)%10)),t.append(r.toAlphaNumericChar(i%10)),n-=3}if(2===n){if(e.available()<7)throw new O;var a=e.readBits(7);if(a>=100)throw new O;t.append(r.toAlphaNumericChar(Math.floor(a/10))),t.append(r.toAlphaNumericChar(a%10))}else if(1===n){if(e.available()<4)throw new O;var o=e.readBits(4);if(o>=10)throw new O;t.append(r.toAlphaNumericChar(o))}},r.parseECIValue=function(e){var t=e.readBits(8);if(!(128&t))return 127&t;if(128==(192&t))return(63&t)<<8&4294967295|e.readBits(8);if(192==(224&t))return(31&t)<<16&4294967295|e.readBits(16);throw new O},r.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",r.GB2312_SUBSET=1,r}();const l1=d1;var v1=function(){function r(e){this.mirrored=e}return r.prototype.isMirrored=function(){return this.mirrored},r.prototype.applyMirroredCorrection=function(e){if(this.mirrored&&null!==e&&!(e.length<3)){var t=e[0];e[0]=e[2],e[2]=t}},r}();const Qr=v1;var Jr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},p1=function(){function r(){this.rsDecoder=new Dt(Se.QR_CODE_FIELD_256)}return r.prototype.decodeBooleanArray=function(e,t){return this.decodeBitMatrix(me.parseFromBooleanArray(e),t)},r.prototype.decodeBitMatrix=function(e,t){var n=new u1(e),i=null;try{return this.decodeBitMatrixParser(n,t)}catch(o){i=o}try{n.remask(),n.setMirror(!0),n.readVersion(),n.readFormatInformation(),n.mirror();var a=this.decodeBitMatrixParser(n,t);return a.setOther(new Qr(!0)),a}catch(o){throw null!==i?i:o}},r.prototype.decodeBitMatrixParser=function(e,t){var n,i,a,o,s=e.readVersion(),u=e.readFormatInformation().getErrorCorrectionLevel(),f=e.readCodewords(),c=c1.getDataBlocks(f,s,u),h=0;try{for(var d=Jr(c),l=d.next();!l.done;l=d.next())h+=(v=l.value).getNumDataCodewords()}catch(S){n={error:S}}finally{try{l&&!l.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}var p=new Uint8Array(h),x=0;try{for(var w=Jr(c),y=w.next();!y.done;y=w.next()){var v,_=(v=y.value).getCodewords(),C=v.getNumDataCodewords();this.correctErrors(_,C);for(var m=0;m<C;m++)p[x++]=_[m]}}catch(S){a={error:S}}finally{try{y&&!y.done&&(o=w.return)&&o.call(w)}finally{if(a)throw a.error}}return l1.decode(p,s,u,t)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new ae}for(var i=0;i<t;i++)e[i]=n[i]},r}();const g1=p1;var x1=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),y1=function(r){function e(t,n,i){var a=r.call(this,t,n)||this;return a.estimatedModuleSize=i,a}return x1(e,r),e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){return new e((this.getX()+n)/2,(this.getY()+t)/2,(this.estimatedModuleSize+i)/2)},e}(b);const w1=y1;var A1=function(){function r(e,t,n,i,a,o,s){this.image=e,this.startX=t,this.startY=n,this.width=i,this.height=a,this.moduleSize=o,this.resultPointCallback=s,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return r.prototype.find=function(){for(var e=this.startX,t=this.height,i=e+this.width,a=this.startY+t/2,o=new Int32Array(3),s=this.image,u=0;u<t;u++){var f=a+(1&u?-Math.floor((u+1)/2):Math.floor((u+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var c=e;c<i&&!s.get(c,f);)c++;for(var h=0;c<i;){if(s.get(c,f))if(1===h)o[1]++;else if(2===h){var d;if(this.foundPatternCross(o)&&null!==(d=this.handlePossibleCenter(o,f,c)))return d;o[0]=o[2],o[1]=1,o[2]=0,h=1}else o[++h]++;else 1===h&&h++,o[h]++;c++}if(this.foundPatternCross(o)&&null!==(d=this.handlePossibleCenter(o,f,i)))return d}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new E},r.centerFromEnd=function(e,t){return t-e[2]-e[1]/2},r.prototype.foundPatternCross=function(e){for(var t=this.moduleSize,n=t/2,i=0;i<3;i++)if(Math.abs(t-e[i])>=n)return!1;return!0},r.prototype.crossCheckVertical=function(e,t,n,i){var a=this.image,o=a.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var u=e;u>=0&&a.get(t,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&!a.get(t,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(t,u)&&s[1]<=n;)s[1]++,u++;if(u===o||s[1]>n)return NaN;for(;u<o&&!a.get(t,u)&&s[2]<=n;)s[2]++,u++;return s[2]>n||5*Math.abs(s[0]+s[1]+s[2]-i)>=2*i?NaN:this.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.handlePossibleCenter=function(e,t,n){var i,a,o=e[0]+e[1]+e[2],s=r.centerFromEnd(e,n),u=this.crossCheckVertical(t,s,2*e[1],o);if(!isNaN(u)){var f=(e[0]+e[1]+e[2])/3;try{for(var c=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.possibleCenters),h=c.next();!h.done;h=c.next()){var d=h.value;if(d.aboutEquals(f,u,s))return d.combineEstimate(u,s,f)}}catch(v){i={error:v}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}var l=new w1(s,u,f);this.possibleCenters.push(l),null!=this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(l)}return null},r}();const C1=A1;var E1=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),m1=function(r){function e(t,n,i,a){var o=r.call(this,t,n)||this;return o.estimatedModuleSize=i,o.count=a,void 0===a&&(o.count=1),o}return E1(e,r),e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){var a=this.count+1;return new e((this.count*this.getX()+n)/a,(this.count*this.getY()+t)/a,(this.count*this.estimatedModuleSize+i)/a,a)},e}(b);const S1=m1;var I1=function(){function r(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}return r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r}();const O1=I1;var pt=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},T1=function(){function r(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}return r.prototype.getImage=function(){return this.image},r.prototype.getPossibleCenters=function(){return this.possibleCenters},r.prototype.find=function(e){var t=null!=e&&void 0!==e.get(K.TRY_HARDER),n=null!=e&&void 0!==e.get(K.PURE_BARCODE),i=this.image,a=i.getHeight(),o=i.getWidth(),s=Math.floor(3*a/(4*r.MAX_MODULES));(s<r.MIN_SKIP||t)&&(s=r.MIN_SKIP);for(var u=!1,f=new Int32Array(5),c=s-1;c<a&&!u;c+=s){f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0;for(var h=0,d=0;d<o;d++)if(i.get(d,c))1==(1&h)&&h++,f[h]++;else if(1&h)f[h]++;else if(4===h)if(r.foundPatternCross(f)){if(!0!==this.handlePossibleCenter(f,c,d,n)){f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,h=3;continue}if(s=2,!0===this.hasSkipped)u=this.haveMultiplyConfirmedCenters();else{var v=this.findRowSkip();v>f[2]&&(c+=v-f[2]-s,d=o-1)}h=0,f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0}else f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,h=3;else f[++h]++;r.foundPatternCross(f)&&!0===this.handlePossibleCenter(f,c,o,n)&&(s=f[0],this.hasSkipped&&(u=this.haveMultiplyConfirmedCenters()))}var p=this.selectBestPatterns();return b.orderBestPatterns(p),new O1(p)},r.centerFromEnd=function(e,t){return t-e[4]-e[3]-e[2]/2},r.foundPatternCross=function(e){for(var t=0,n=0;n<5;n++){var i=e[n];if(0===i)return!1;t+=i}if(t<7)return!1;var a=t/7,o=a/2;return Math.abs(a-e[0])<o&&Math.abs(a-e[1])<o&&Math.abs(3*a-e[2])<3*o&&Math.abs(a-e[3])<o&&Math.abs(a-e[4])<o},r.prototype.getCrossCheckStateCount=function(){var e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e},r.prototype.crossCheckDiagonal=function(e,t,n,i){for(var a=this.getCrossCheckStateCount(),o=0,s=this.image;e>=o&&t>=o&&s.get(t-o,e-o);)a[2]++,o++;if(e<o||t<o)return!1;for(;e>=o&&t>=o&&!s.get(t-o,e-o)&&a[1]<=n;)a[1]++,o++;if(e<o||t<o||a[1]>n)return!1;for(;e>=o&&t>=o&&s.get(t-o,e-o)&&a[0]<=n;)a[0]++,o++;if(a[0]>n)return!1;var u=s.getHeight(),f=s.getWidth();for(o=1;e+o<u&&t+o<f&&s.get(t+o,e+o);)a[2]++,o++;if(e+o>=u||t+o>=f)return!1;for(;e+o<u&&t+o<f&&!s.get(t+o,e+o)&&a[3]<n;)a[3]++,o++;if(e+o>=u||t+o>=f||a[3]>=n)return!1;for(;e+o<u&&t+o<f&&s.get(t+o,e+o)&&a[4]<n;)a[4]++,o++;return!(a[4]>=n)&&Math.abs(a[0]+a[1]+a[2]+a[3]+a[4]-i)<2*i&&r.foundPatternCross(a)},r.prototype.crossCheckVertical=function(e,t,n,i){for(var a=this.image,o=a.getHeight(),s=this.getCrossCheckStateCount(),u=e;u>=0&&a.get(t,u);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!a.get(t,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&a.get(t,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(t,u);)s[2]++,u++;if(u===o)return NaN;for(;u<o&&!a.get(t,u)&&s[3]<n;)s[3]++,u++;if(u===o||s[3]>=n)return NaN;for(;u<o&&a.get(t,u)&&s[4]<n;)s[4]++,u++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-i)>=2*i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.crossCheckHorizontal=function(e,t,n,i){for(var a=this.image,o=a.getWidth(),s=this.getCrossCheckStateCount(),u=e;u>=0&&a.get(u,t);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!a.get(u,t)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&a.get(u,t)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<o&&a.get(u,t);)s[2]++,u++;if(u===o)return NaN;for(;u<o&&!a.get(u,t)&&s[3]<n;)s[3]++,u++;if(u===o||s[3]>=n)return NaN;for(;u<o&&a.get(u,t)&&s[4]<n;)s[4]++,u++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-i)>=i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,u):NaN},r.prototype.handlePossibleCenter=function(e,t,n,i){var a=e[0]+e[1]+e[2]+e[3]+e[4],o=r.centerFromEnd(e,n),s=this.crossCheckVertical(t,Math.floor(o),e[2],a);if(!isNaN(s)&&(o=this.crossCheckHorizontal(Math.floor(o),Math.floor(s),e[2],a),!isNaN(o)&&(!i||this.crossCheckDiagonal(Math.floor(s),Math.floor(o),e[2],a)))){for(var u=a/7,f=!1,c=this.possibleCenters,h=0,d=c.length;h<d;h++){var l=c[h];if(l.aboutEquals(u,s,o)){c[h]=l.combineEstimate(s,o,u),f=!0;break}}if(!f){var v=new S1(o,s,u);c.push(v),null!=this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(v)}return!0}return!1},r.prototype.findRowSkip=function(){var e,t;if(this.possibleCenters.length<=1)return 0;var i=null;try{for(var a=pt(this.possibleCenters),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.getCount()>=r.CENTER_QUORUM){if(null!=i)return this.hasSkipped=!0,Math.floor((Math.abs(i.getX()-s.getX())-Math.abs(i.getY()-s.getY()))/2);i=s}}}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return 0},r.prototype.haveMultiplyConfirmedCenters=function(){var e,t,n,i,a=0,o=0,s=this.possibleCenters.length;try{for(var u=pt(this.possibleCenters),f=u.next();!f.done;f=u.next())(c=f.value).getCount()>=r.CENTER_QUORUM&&(a++,o+=c.getEstimatedModuleSize())}catch(p){e={error:p}}finally{try{f&&!f.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}if(a<3)return!1;var h=o/s,d=0;try{for(var l=pt(this.possibleCenters),v=l.next();!v.done;v=l.next()){var c=v.value;d+=Math.abs(c.getEstimatedModuleSize()-h)}}catch(p){n={error:p}}finally{try{v&&!v.done&&(i=l.return)&&i.call(l)}finally{if(n)throw n.error}}return d<=.05*o},r.prototype.selectBestPatterns=function(){var e,t,n,i,a=this.possibleCenters.length;if(a<3)throw new E;var s,o=this.possibleCenters;if(a>3){var u=0,f=0;try{for(var c=pt(this.possibleCenters),h=c.next();!h.done;h=c.next()){var l=h.value.getEstimatedModuleSize();u+=l,f+=l*l}}catch(m){e={error:m}}finally{try{h&&!h.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}s=u/a;var v=Math.sqrt(f/a-s*s);o.sort(function(m,S){var I=Math.abs(S.getEstimatedModuleSize()-s),D=Math.abs(m.getEstimatedModuleSize()-s);return I<D?-1:I>D?1:0});for(var p=Math.max(.2*s,v),x=0;x<o.length&&o.length>3;x++)Math.abs(o[x].getEstimatedModuleSize()-s)>p&&(o.splice(x,1),x--)}if(o.length>3){u=0;try{for(var y=pt(o),_=y.next();!_.done;_=y.next())u+=_.value.getEstimatedModuleSize()}catch(S){n={error:S}}finally{try{_&&!_.done&&(i=y.return)&&i.call(y)}finally{if(n)throw n.error}}s=u/o.length,o.sort(function(S,I){if(I.getCount()===S.getCount()){var D=Math.abs(I.getEstimatedModuleSize()-s),M=Math.abs(S.getEstimatedModuleSize()-s);return D<M?1:D>M?-1:0}return I.getCount()-S.getCount()}),o.splice(3)}return[o[0],o[1],o[2]]},r.CENTER_QUORUM=2,r.MIN_SKIP=3,r.MAX_MODULES=57,r}();const D1=T1;var R1=function(){function r(e){this.image=e}return r.prototype.getImage=function(){return this.image},r.prototype.getResultPointCallback=function(){return this.resultPointCallback},r.prototype.detect=function(e){this.resultPointCallback=null==e?null:e.get(K.NEED_RESULT_POINT_CALLBACK);var n=new D1(this.image,this.resultPointCallback).find(e);return this.processFinderPatternInfo(n)},r.prototype.processFinderPatternInfo=function(e){var t=e.getTopLeft(),n=e.getTopRight(),i=e.getBottomLeft(),a=this.calculateModuleSize(t,n,i);if(a<1)throw new E("No pattern found in proccess finder.");var o=r.computeDimension(t,n,i,a),s=Je.getProvisionalVersionForDimension(o),u=s.getDimensionForVersion()-7,f=null;if(s.getAlignmentPatternCenters().length>0)for(var c=n.getX()-t.getX()+i.getX(),h=n.getY()-t.getY()+i.getY(),d=1-3/u,l=Math.floor(t.getX()+d*(c-t.getX())),v=Math.floor(t.getY()+d*(h-t.getY())),p=4;p<=16;p<<=1)try{f=this.findAlignmentInRegion(a,l,v,p);break}catch(_){if(!(_ instanceof E))throw _}var x=r.createTransform(t,n,i,f,o),w=r.sampleGrid(this.image,x,o);return new Qt(w,null===f?[i,t,n]:[i,t,n,f])},r.createTransform=function(e,t,n,i,a){var s,u,f,c,o=a-3.5;return null!==i?(s=i.getX(),u=i.getY(),c=f=o-3):(s=t.getX()-e.getX()+n.getX(),u=t.getY()-e.getY()+n.getY(),f=o,c=o),Dr.quadrilateralToQuadrilateral(3.5,3.5,o,3.5,f,c,3.5,o,e.getX(),e.getY(),t.getX(),t.getY(),s,u,n.getX(),n.getY())},r.sampleGrid=function(e,t,n){return $t.getInstance().sampleGridWithTransform(e,n,n,t)},r.computeDimension=function(e,t,n,i){var a=G.round(b.distance(e,t)/i),o=G.round(b.distance(e,n)/i),s=Math.floor((a+o)/2)+7;switch(3&s){case 0:s++;break;case 2:s--;break;case 3:throw new E("Dimensions could be not found.")}return s},r.prototype.calculateModuleSize=function(e,t,n){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,n))/2},r.prototype.calculateModuleSizeOneWay=function(e,t){var n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),i=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(n)?i/7:isNaN(i)?n/7:(n+i)/14},r.prototype.sizeOfBlackWhiteBlackRunBothWays=function(e,t,n,i){var a=this.sizeOfBlackWhiteBlackRun(e,t,n,i),o=1,s=e-(n-e);s<0?(o=e/(e-s),s=0):s>=this.image.getWidth()&&(o=(this.image.getWidth()-1-e)/(s-e),s=this.image.getWidth()-1);var u=Math.floor(t-(i-t)*o);return o=1,u<0?(o=t/(t-u),u=0):u>=this.image.getHeight()&&(o=(this.image.getHeight()-1-t)/(u-t),u=this.image.getHeight()-1),s=Math.floor(e+(s-e)*o),(a+=this.sizeOfBlackWhiteBlackRun(e,t,s,u))-1},r.prototype.sizeOfBlackWhiteBlackRun=function(e,t,n,i){var a=Math.abs(i-t)>Math.abs(n-e);if(a){var o=e;e=t,t=o,o=n,n=i,i=o}for(var s=Math.abs(n-e),u=Math.abs(i-t),f=-s/2,c=e<n?1:-1,h=t<i?1:-1,d=0,l=n+c,v=e,p=t;v!==l;v+=c){if(1===d===this.image.get(a?p:v,a?v:p)){if(2===d)return G.distance(v,p,e,t);d++}if((f+=u)>0){if(p===i)break;p+=h,f-=s}}return 2===d?G.distance(n+c,i,e,t):NaN},r.prototype.findAlignmentInRegion=function(e,t,n,i){var a=Math.floor(i*e),o=Math.max(0,t-a),s=Math.min(this.image.getWidth()-1,t+a);if(s-o<3*e)throw new E("Alignment top exceeds estimated module size.");var u=Math.max(0,n-a),f=Math.min(this.image.getHeight()-1,n+a);if(f-u<3*e)throw new E("Alignment bottom exceeds estimated module size.");return new C1(this.image,o,u,s-o,f-u,e,this.resultPointCallback).find()},r}();const b1=R1;var N1=function(){function r(){this.decoder=new g1}return r.prototype.getDecoder=function(){return this.decoder},r.prototype.decode=function(e,t){var n,i;if(null!=t&&void 0!==t.get(K.PURE_BARCODE)){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decodeBitMatrix(a,t),i=r.NO_POINTS}else{var o=new b1(e.getBlackMatrix()).detect(t);n=this.decoder.decodeBitMatrix(o.getBits(),t),i=o.getPoints()}n.getOther()instanceof Qr&&n.getOther().applyMirroredCorrection(i);var s=new we(n.getText(),n.getRawBytes(),void 0,i,N.QR_CODE,void 0),u=n.getByteSegments();null!==u&&s.putMetadata(xe.BYTE_SEGMENTS,u);var f=n.getECLevel();return null!==f&&s.putMetadata(xe.ERROR_CORRECTION_LEVEL,f),n.hasStructuredAppend()&&(s.putMetadata(xe.STRUCTURED_APPEND_SEQUENCE,n.getStructuredAppendSequenceNumber()),s.putMetadata(xe.STRUCTURED_APPEND_PARITY,n.getStructuredAppendParity())),s},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(null===t||null===n)throw new E;var i=this.moduleSize(t,e),a=t[1],o=n[1],s=t[0],u=n[0];if(s>=u||a>=o)throw new E;if(o-a!=u-s&&(u=s+(o-a))>=e.getWidth())throw new E;var f=Math.round((u-s+1)/i),c=Math.round((o-a+1)/i);if(f<=0||c<=0)throw new E;if(c!==f)throw new E;var h=Math.floor(i/2);a+=h;var d=(s+=h)+Math.floor((f-1)*i)-u;if(d>0){if(d>h)throw new E;s-=d}var l=a+Math.floor((c-1)*i)-o;if(l>0){if(l>h)throw new E;a-=l}for(var v=new me(f,c),p=0;p<c;p++)for(var x=a+Math.floor(p*i),w=0;w<f;w++)e.get(s+Math.floor(w*i),x)&&v.set(w,p);return v},r.moduleSize=function(e,t){for(var n=t.getHeight(),i=t.getWidth(),a=e[0],o=e[1],s=!0,u=0;a<i&&o<n;){if(s!==t.get(a,o)){if(5==++u)break;s=!s}a++,o++}if(a===i||o===n)throw new E;return(a-e[0])/7},r.NO_POINTS=new Array,r}();const Ft=N1;var M1=function(){function r(){}return r.prototype.PDF417Common=function(){},r.getBitCountSum=function(e){return G.sum(e)},r.toIntArray=function(e){var t,n;if(null==e||!e.length)return r.EMPTY_INT_ARRAY;var i=new Int32Array(e.length),a=0;try{for(var o=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),s=o.next();!s.done;s=o.next())i[a++]=s.value}catch(f){t={error:f}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return i},r.getCodeword=function(e){var t=oe.binarySearch(r.SYMBOL_TABLE,262143&e);return t<0?-1:(r.CODEWORD_TABLE[t]-1)%r.NUMBER_OF_CODEWORDS},r.MAX_CODEWORDS_IN_BARCODE=(r.NUMBER_OF_CODEWORDS=929)-1,r.MIN_ROWS_IN_BARCODE=3,r.MAX_ROWS_IN_BARCODE=90,r.MODULES_IN_CODEWORD=17,r.MODULES_IN_STOP_PATTERN=18,r.BARS_IN_MODULE=8,r.EMPTY_INT_ARRAY=new Int32Array([]),r.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),r.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),r}();const V=M1;var B1=function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r}();const F1=B1;var L1=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},k1=function(){function r(){}return r.detectMultiple=function(e,t,n){var i=e.getBlackMatrix(),a=r.detect(n,i);return a.length||((i=i.clone()).rotate180(),a=r.detect(n,i)),new F1(i,a)},r.detect=function(e,t){for(var n,i,a=new Array,o=0,s=0,u=!1;o<t.getHeight();){var f=r.findVertices(t,o,s);if(null!=f[0]||null!=f[3]){if(u=!0,a.push(f),!e)break;null!=f[2]?(s=Math.trunc(f[2].getX()),o=Math.trunc(f[2].getY())):(s=Math.trunc(f[4].getX()),o=Math.trunc(f[4].getY()))}else{if(!u)break;u=!1,s=0;try{for(var c=(n=void 0,L1(a)),h=c.next();!h.done;h=c.next()){var d=h.value;null!=d[1]&&(o=Math.trunc(Math.max(o,d[1].getY()))),null!=d[3]&&(o=Math.max(o,Math.trunc(d[3].getY())))}}catch(l){n={error:l}}finally{try{h&&!h.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}o+=r.ROW_STEP}}return a},r.findVertices=function(e,t,n){var i=e.getHeight(),a=e.getWidth(),o=new Array(8);return r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.START_PATTERN),r.INDEXES_START_PATTERN),null!=o[4]&&(n=Math.trunc(o[4].getX()),t=Math.trunc(o[4].getY())),r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.STOP_PATTERN),r.INDEXES_STOP_PATTERN),o},r.copyToResult=function(e,t,n){for(var i=0;i<n.length;i++)e[n[i]]=t[i]},r.findRowsWithPattern=function(e,t,n,i,a,o){for(var s=new Array(4),u=!1,f=new Int32Array(o.length);i<t;i+=r.ROW_STEP)if(null!=(c=r.findGuardPattern(e,a,i,n,!1,o,f))){for(;i>0;){if(null==(h=r.findGuardPattern(e,a,--i,n,!1,o,f))){i++;break}c=h}s[0]=new b(c[0],i),s[1]=new b(c[1],i),u=!0;break}var d=i+1;if(u){for(var l=0,h=Int32Array.from([Math.trunc(s[0].getX()),Math.trunc(s[1].getX())]);d<t;d++){var c;if(null!=(c=r.findGuardPattern(e,h[0],d,n,!1,o,f))&&Math.abs(h[0]-c[0])<r.MAX_PATTERN_DRIFT&&Math.abs(h[1]-c[1])<r.MAX_PATTERN_DRIFT)h=c,l=0;else{if(l>r.SKIPPED_ROW_COUNT_MAX)break;l++}}s[2]=new b(h[0],d-=l+1),s[3]=new b(h[1],d)}return d-i<r.BARCODE_MIN_HEIGHT&&oe.fill(s,null),s},r.findGuardPattern=function(e,t,n,i,a,o,s){oe.fillWithin(s,0,s.length,0);for(var u=t,f=0;e.get(u,n)&&u>0&&f++<r.MAX_PIXEL_DRIFT;)u--;for(var c=u,h=0,d=o.length,l=a;c<i;c++)if(e.get(c,n)!==l)s[h]++;else{if(h===d-1){if(r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE)return new Int32Array([u,c]);u+=s[0]+s[1],q.arraycopy(s,2,s,0,h-1),s[h-1]=0,s[h]=0,h--}else h++;s[h]=1,l=!l}return h===d-1&&r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE?new Int32Array([u,c-1]):null},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return 1/0;var u=a/o;n*=u;for(var f=0,c=0;c<i;c++){var h=e[c],d=t[c]*u,l=h>d?h-d:d-h;if(l>n)return 1/0;f+=l}return f/a},r.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),r.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),r.MAX_AVG_VARIANCE=.42,r.MAX_INDIVIDUAL_VARIANCE=.8,r.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),r.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),r.MAX_PIXEL_DRIFT=3,r.MAX_PATTERN_DRIFT=5,r.SKIPPED_ROW_COUNT_MAX=25,r.ROW_STEP=5,r.BARCODE_MIN_HEIGHT=10,r}();const U1=k1;var H1=function(){function r(e,t){if(0===t.length)throw new R;this.field=e;var n=t.length;if(n>1&&0===t[0]){for(var i=1;i<n&&0===t[i];)i++;i===n?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(n-i),q.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return 0===this.coefficients[0]},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){var t,n;if(0===e)return this.getCoefficient(0);if(1===e){var i=0;try{for(var a=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.coefficients),o=a.next();!o.done;o=a.next())i=this.field.add(i,o.value)}catch(h){t={error:h}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i}for(var u=this.coefficients[0],f=this.coefficients.length,c=1;c<f;c++)u=this.field.add(this.field.multiply(e,u),this.coefficients[c]);return u},r.prototype.add=function(e){if(!this.field.equals(e.field))throw new R("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;q.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=this.field.add(t[s-o],n[s]);return new r(this.field,a)},r.prototype.subtract=function(e){if(!this.field.equals(e.field))throw new R("ModulusPolys do not have same ModulusGF field");return e.isZero()?this:this.add(e.negative())},r.prototype.multiply=function(e){return e instanceof r?this.multiplyOther(e):this.multiplyScalar(e)},r.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new R("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new r(this.field,new Int32Array([0]));for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=0;s<n;s++)for(var u=t[s],f=0;f<a;f++)o[s+f]=this.field.add(o[s+f],this.field.multiply(u,i[f]));return new r(this.field,o)},r.prototype.negative=function(){for(var e=this.coefficients.length,t=new Int32Array(e),n=0;n<e;n++)t[n]=this.field.subtract(0,this.coefficients[n]);return new r(this.field,t)},r.prototype.multiplyScalar=function(e){if(0===e)return new r(this.field,new Int32Array([0]));if(1===e)return this;for(var t=this.coefficients.length,n=new Int32Array(t),i=0;i<t;i++)n[i]=this.field.multiply(this.coefficients[i],e);return new r(this.field,n)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new R;if(0===t)return new r(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),a=0;a<n;a++)i[a]=this.field.multiply(this.coefficients[a],t);return new r(this.field,i)},r.prototype.toString=function(){for(var e=new F,t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);0!==n&&(n<0?(e.append(" - "),n=-n):e.length()>0&&e.append(" + "),(0===t||1!==n)&&e.append(n),0!==t&&(1===t?e.append("x"):(e.append("x^"),e.append(t))))}return e.toString()},r}();const $e=H1;var G1=function(){function r(){}return r.prototype.add=function(e,t){return(e+t)%this.modulus},r.prototype.subtract=function(e,t){return(this.modulus+e-t)%this.modulus},r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(0===e)throw new R;return this.logTable[e]},r.prototype.inverse=function(e){if(0===e)throw new Ir;return this.expTable[this.modulus-this.logTable[e]-1]},r.prototype.multiply=function(e,t){return 0===e||0===t?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.modulus-1)]},r.prototype.getSize=function(){return this.modulus},r.prototype.equals=function(e){return e===this},r}();const W1=G1;var X1=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),z1=function(r){function e(t,n){var i=r.call(this)||this;i.modulus=t,i.expTable=new Int32Array(t),i.logTable=new Int32Array(t);for(var a=1,o=0;o<t;o++)i.expTable[o]=a,a=a*n%t;for(o=0;o<t-1;o++)i.logTable[i.expTable[o]]=o;return i.zero=new $e(i,new Int32Array([0])),i.one=new $e(i,new Int32Array([1])),i}return X1(e,r),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new R;if(0===n)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new $e(this,i)},e.PDF417_GF=new e(V.NUMBER_OF_CODEWORDS,3),e}(W1);const Z1=z1;var Y1=function(){function r(){this.field=Z1.PDF417_GF}return r.prototype.decode=function(e,t,n){for(var i,a,o=new $e(this.field,e),s=new Int32Array(t),u=!1,f=t;f>0;f--){var c=o.evaluateAt(this.field.exp(f));s[t-f]=c,0!==c&&(u=!0)}if(!u)return 0;var h=this.field.getOne();if(null!=n)try{for(var d=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(n),l=d.next();!l.done;l=d.next()){var p=this.field.exp(e.length-1-l.value),x=new $e(this.field,new Int32Array([this.field.subtract(0,p),1]));h=h.multiply(x)}}catch(D){i={error:D}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}var w=new $e(this.field,s),y=this.runEuclideanAlgorithm(this.field.buildMonomial(t,1),w,t),_=y[0],C=y[1],m=this.findErrorLocations(_),S=this.findErrorMagnitudes(C,_,m);for(f=0;f<m.length;f++){var I=e.length-1-this.field.log(m[f]);if(I<0)throw ae.getChecksumInstance();e[I]=this.field.subtract(e[I],S[f])}return m.length},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=e,o=t,s=this.field.getZero(),u=this.field.getOne();o.getDegree()>=Math.round(n/2);){var f=a,c=s;if(s=u,(a=o).isZero())throw ae.getChecksumInstance();o=f;for(var h=this.field.getZero(),d=a.getCoefficient(a.getDegree()),l=this.field.inverse(d);o.getDegree()>=a.getDegree()&&!o.isZero();){var v=o.getDegree()-a.getDegree(),p=this.field.multiply(o.getCoefficient(o.getDegree()),l);h=h.add(this.field.buildMonomial(v,p)),o=o.subtract(a.multiplyByMonomial(v,p))}u=h.multiply(s).subtract(c).negative()}var x=u.getCoefficient(0);if(0===x)throw ae.getChecksumInstance();var w=this.field.inverse(x);return[u.multiply(w),o.multiply(w)]},r.prototype.findErrorLocations=function(e){for(var t=e.getDegree(),n=new Int32Array(t),i=0,a=1;a<this.field.getSize()&&i<t;a++)0===e.evaluateAt(a)&&(n[i]=this.field.inverse(a),i++);if(i!==t)throw ae.getChecksumInstance();return n},r.prototype.findErrorMagnitudes=function(e,t,n){for(var i=t.getDegree(),a=new Int32Array(i),o=1;o<=i;o++)a[i-o]=this.field.multiply(o,t.getCoefficient(o));var s=new $e(this.field,a),u=n.length,f=new Int32Array(u);for(o=0;o<u;o++){var c=this.field.inverse(n[o]),h=this.field.subtract(0,e.evaluateAt(c)),d=this.field.inverse(s.evaluateAt(c));f[o]=this.field.multiply(h,d)}return f},r}();const K1=Y1;var q1=function(){function r(e,t,n,i,a){e instanceof r?this.constructor_2(e):this.constructor_1(e,t,n,i,a)}return r.prototype.constructor_1=function(e,t,n,i,a){var o=null==t||null==n,s=null==i||null==a;if(o&&s)throw new E;o?(t=new b(0,i.getY()),n=new b(0,a.getY())):s&&(i=new b(e.getWidth()-1,t.getY()),a=new b(e.getWidth()-1,n.getY())),this.image=e,this.topLeft=t,this.bottomLeft=n,this.topRight=i,this.bottomRight=a,this.minX=Math.trunc(Math.min(t.getX(),n.getX())),this.maxX=Math.trunc(Math.max(i.getX(),a.getX())),this.minY=Math.trunc(Math.min(t.getY(),i.getY())),this.maxY=Math.trunc(Math.max(n.getY(),a.getY()))},r.prototype.constructor_2=function(e){this.image=e.image,this.topLeft=e.getTopLeft(),this.bottomLeft=e.getBottomLeft(),this.topRight=e.getTopRight(),this.bottomRight=e.getBottomRight(),this.minX=e.getMinX(),this.maxX=e.getMaxX(),this.minY=e.getMinY(),this.maxY=e.getMaxY()},r.merge=function(e,t){return null==e?t:null==t?e:new r(e.image,e.topLeft,e.bottomLeft,t.topRight,t.bottomRight)},r.prototype.addMissingRows=function(e,t,n){var i=this.topLeft,a=this.bottomLeft,o=this.topRight,s=this.bottomRight;if(e>0){var u=n?this.topLeft:this.topRight,f=Math.trunc(u.getY()-e);f<0&&(f=0);var c=new b(u.getX(),f);n?i=c:o=c}if(t>0){var h=n?this.bottomLeft:this.bottomRight,d=Math.trunc(h.getY()+t);d>=this.image.getHeight()&&(d=this.image.getHeight()-1);var l=new b(h.getX(),d);n?a=l:s=l}return new r(this.image,i,a,o,s)},r.prototype.getMinX=function(){return this.minX},r.prototype.getMaxX=function(){return this.maxX},r.prototype.getMinY=function(){return this.minY},r.prototype.getMaxY=function(){return this.maxY},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getBottomRight=function(){return this.bottomRight},r}();const or=q1;var Q1=function(){function r(e,t,n,i){this.columnCount=e,this.errorCorrectionLevel=i,this.rowCountUpperPart=t,this.rowCountLowerPart=n,this.rowCount=t+n}return r.prototype.getColumnCount=function(){return this.columnCount},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getRowCount=function(){return this.rowCount},r.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},r.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},r}();const J1=Q1;var $1=function(){function r(){this.buffer=""}return r.form=function(e,t){var n=-1;return e.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function i(o,s,u,f,c,h){if("%%"===o)return"%";if(void 0!==t[++n]){o=f?parseInt(f.substr(1)):void 0;var l,d=c?parseInt(c.substr(1)):void 0;switch(h){case"s":l=t[n];break;case"c":l=t[n][0];break;case"f":l=parseFloat(t[n]).toFixed(o);break;case"p":l=parseFloat(t[n]).toPrecision(o);break;case"e":l=parseFloat(t[n]).toExponential(o);break;case"x":l=parseInt(t[n]).toString(d||16);break;case"d":l=parseFloat(parseInt(t[n],d||10).toPrecision(o)).toFixed(0)}l="object"==typeof l?JSON.stringify(l):(+l).toString(d);for(var v=parseInt(u),p=u&&u[0]+""=="0"?"0":" ";l.length<v;)l=void 0!==s?l+p:p+l;return l}})},r.prototype.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.buffer+=r.form(e,t)},r.prototype.toString=function(){return this.buffer},r}();const sr=$1;var t0=function(){function r(e){this.boundingBox=new or(e),this.codewords=new Array(e.getMaxY()-e.getMinY()+1)}return r.prototype.getCodewordNearby=function(e){var t=this.getCodeword(e);if(null!=t)return t;for(var n=1;n<r.MAX_NEARBY_DISTANCE;n++){var i=this.imageRowToCodewordIndex(e)-n;if(i>=0&&null!=(t=this.codewords[i])||(i=this.imageRowToCodewordIndex(e)+n)<this.codewords.length&&null!=(t=this.codewords[i]))return t}return null},r.prototype.imageRowToCodewordIndex=function(e){return e-this.boundingBox.getMinY()},r.prototype.setCodeword=function(e,t){this.codewords[this.imageRowToCodewordIndex(e)]=t},r.prototype.getCodeword=function(e){return this.codewords[this.imageRowToCodewordIndex(e)]},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.getCodewords=function(){return this.codewords},r.prototype.toString=function(){var e,t,n=new sr,i=0;try{for(var a=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.codewords),o=a.next();!o.done;o=a.next()){var s=o.value;null!=s?n.format("%3d: %3d|%3d%n",i++,s.getRowNumber(),s.getValue()):n.format("%3d:    |   %n",i++)}}catch(u){e={error:u}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n.toString()},r.MAX_NEARBY_DISTANCE=5,r}();const $r=t0;var n0=function(r,e){var t="function"==typeof Symbol&&r[Symbol.iterator];if(!t)return r;var i,o,n=t.call(r),a=[];try{for(;(void 0===e||e-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(o)throw o.error}}return a},i0=function(){function r(){this.values=new Map}return r.prototype.setValue=function(e){e=Math.trunc(e);var t=this.values.get(e);null==t&&(t=0),t++,this.values.set(e,t)},r.prototype.getValue=function(){var e,t,n=-1,i=new Array,a=function(h,d){var l_getKey=function(){return h},l_getValue=function(){return d};l_getValue()>n?(n=l_getValue(),(i=[]).push(l_getKey())):l_getValue()===n&&i.push(l_getKey())};try{for(var o=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.values.entries()),s=o.next();!s.done;s=o.next()){var u=n0(s.value,2);a(u[0],u[1])}}catch(h){e={error:h}}finally{try{s&&!s.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return V.toIntArray(i)},r.prototype.getConfidence=function(e){return this.values.get(e)},r}();const gt=i0;var a0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ur=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},o0=function(r){function e(t,n){var i=r.call(this,t)||this;return i._isLeft=n,i}return a0(e,r),e.prototype.setRowNumbers=function(){var t,n;try{for(var i=ur(this.getCodewords()),a=i.next();!a.done;a=i.next())a.value?.setRowNumberAsRowIndicatorColumn()}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var n=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(n,t);for(var i=this.getBoundingBox(),a=this._isLeft?i.getTopLeft():i.getTopRight(),o=this._isLeft?i.getBottomLeft():i.getBottomRight(),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),u=this.imageRowToCodewordIndex(Math.trunc(o.getY())),f=-1,c=1,h=0,d=s;d<u;d++)if(null!=n[d]){var l=n[d],v=l.getRowNumber()-f;if(0===v)h++;else if(1===v)c=Math.max(c,h),h=1,f=l.getRowNumber();else if(v<0||l.getRowNumber()>=t.getRowCount()||v>d)n[d]=null;else{for(var p,x=(p=c>2?(c-2)*v:v)>=d,w=1;w<=p&&!x;w++)x=null!=n[d-w];x?n[d]=null:(f=l.getRowNumber(),h=1)}}},e.prototype.getRowHeights=function(){var t,n,i=this.getBarcodeMetadata();if(null==i)return null;this.adjustIncompleteIndicatorColumnRowNumbers(i);var a=new Int32Array(i.getRowCount());try{for(var o=ur(this.getCodewords()),s=o.next();!s.done;s=o.next()){var u=s.value;if(null!=u){var f=u.getRowNumber();if(f>=a.length)continue;a[f]++}}}catch(c){t={error:c}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return a},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var n=this.getBoundingBox(),i=this._isLeft?n.getTopLeft():n.getTopRight(),a=this._isLeft?n.getBottomLeft():n.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(i.getY())),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),u=this.getCodewords(),f=-1,c=1,h=0,d=o;d<s;d++)if(null!=u[d]){var l=u[d];l.setRowNumberAsRowIndicatorColumn();var v=l.getRowNumber()-f;0===v?h++:1===v?(c=Math.max(c,h),h=1,f=l.getRowNumber()):l.getRowNumber()>=t.getRowCount()?u[d]=null:(f=l.getRowNumber(),h=1)}},e.prototype.getBarcodeMetadata=function(){var t,n,i=this.getCodewords(),a=new gt,o=new gt,s=new gt,u=new gt;try{for(var f=ur(i),c=f.next();!c.done;c=f.next()){var h=c.value;if(null!=h){h.setRowNumberAsRowIndicatorColumn();var d=h.getValue()%30,l=h.getRowNumber();switch(this._isLeft||(l+=2),l%3){case 0:o.setValue(3*d+1);break;case 1:u.setValue(d/3),s.setValue(d%3);break;case 2:a.setValue(d+1)}}}}catch(p){t={error:p}}finally{try{c&&!c.done&&(n=f.return)&&n.call(f)}finally{if(t)throw t.error}}if(0===a.getValue().length||0===o.getValue().length||0===s.getValue().length||0===u.getValue().length||a.getValue()[0]<1||o.getValue()[0]+s.getValue()[0]<V.MIN_ROWS_IN_BARCODE||o.getValue()[0]+s.getValue()[0]>V.MAX_ROWS_IN_BARCODE)return null;var v=new J1(a.getValue()[0],o.getValue()[0],s.getValue()[0],u.getValue()[0]);return this.removeIncorrectCodewords(i,v),v},e.prototype.removeIncorrectCodewords=function(t,n){for(var i=0;i<t.length;i++){var a=t[i];if(null!=t[i]){var o=a.getValue()%30,s=a.getRowNumber();if(s>n.getRowCount()){t[i]=null;continue}switch(this._isLeft||(s+=2),s%3){case 0:3*o+1!==n.getRowCountUpperPart()&&(t[i]=null);break;case 1:(Math.trunc(o/3)!==n.getErrorCorrectionLevel()||o%3!==n.getRowCountLowerPart())&&(t[i]=null);break;case 2:o+1!==n.getColumnCount()&&(t[i]=null)}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+"\n"+r.prototype.toString.call(this)},e}($r);const en=o0;var u0=function(){function r(e,t){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=e,this.barcodeColumnCount=e.getColumnCount(),this.boundingBox=t,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}return r.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var t,e=V.MAX_CODEWORDS_IN_BARCODE;do{t=e,e=this.adjustRowNumbersAndGetCount()}while(e>0&&e<t);return this.detectionResultColumns},r.prototype.adjustIndicatorColumnRowNumbers=function(e){e?.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},r.prototype.adjustRowNumbersAndGetCount=function(){var e=this.adjustRowNumbersByRow();if(0===e)return 0;for(var t=1;t<this.barcodeColumnCount+1;t++)for(var n=this.detectionResultColumns[t].getCodewords(),i=0;i<n.length;i++)null!=n[i]&&(n[i].hasValidRowNumber()||this.adjustRowNumbers(t,i,n));return e},r.prototype.adjustRowNumbersByRow=function(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()},r.prototype.adjustRowNumbersFromBothRI=function(){if(null!=this.detectionResultColumns[0]&&null!=this.detectionResultColumns[this.barcodeColumnCount+1])for(var e=this.detectionResultColumns[0].getCodewords(),t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<e.length;n++)if(null!=e[n]&&null!=t[n]&&e[n].getRowNumber()===t[n].getRowNumber())for(var i=1;i<=this.barcodeColumnCount;i++){var a=this.detectionResultColumns[i].getCodewords()[n];null!=a&&(a.setRowNumber(e[n].getRowNumber()),a.hasValidRowNumber()||(this.detectionResultColumns[i].getCodewords()[n]=null))}},r.prototype.adjustRowNumbersFromRRI=function(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;for(var e=0,t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<t.length;n++)if(null!=t[n])for(var i=t[n].getRowNumber(),a=0,o=this.barcodeColumnCount+1;o>0&&a<this.ADJUST_ROW_NUMBER_SKIP;o--){var s=this.detectionResultColumns[o].getCodewords()[n];null!=s&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.prototype.adjustRowNumbersFromLRI=function(){if(null==this.detectionResultColumns[0])return 0;for(var e=0,t=this.detectionResultColumns[0].getCodewords(),n=0;n<t.length;n++)if(null!=t[n])for(var i=t[n].getRowNumber(),a=0,o=1;o<this.barcodeColumnCount+1&&a<this.ADJUST_ROW_NUMBER_SKIP;o++){var s=this.detectionResultColumns[o].getCodewords()[n];null!=s&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.adjustRowNumberIfValid=function(e,t,n){return null==n||n.hasValidRowNumber()||(n.isValidRowNumber(e)?(n.setRowNumber(e),t=0):++t),t},r.prototype.adjustRowNumbers=function(e,t,n){var i,a;if(null!=this.detectionResultColumns[e-1]){var o=n[t],s=this.detectionResultColumns[e-1].getCodewords(),u=s;null!=this.detectionResultColumns[e+1]&&(u=this.detectionResultColumns[e+1].getCodewords());var f=new Array(14);f[2]=s[t],f[3]=u[t],t>0&&(f[0]=n[t-1],f[4]=s[t-1],f[5]=u[t-1]),t>1&&(f[8]=n[t-2],f[10]=s[t-2],f[11]=u[t-2]),t<n.length-1&&(f[1]=n[t+1],f[6]=s[t+1],f[7]=u[t+1]),t<n.length-2&&(f[9]=n[t+2],f[12]=s[t+2],f[13]=u[t+2]);try{for(var c=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(f),h=c.next();!h.done;h=c.next())if(r.adjustRowNumber(o,h.value))return}catch(l){i={error:l}}finally{try{h&&!h.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}}},r.adjustRowNumber=function(e,t){return!(null==t||!t.hasValidRowNumber()||t.getBucket()!==e.getBucket()||(e.setRowNumber(t.getRowNumber()),0))},r.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},r.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},r.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},r.prototype.setBoundingBox=function(e){this.boundingBox=e},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.setDetectionResultColumn=function(e,t){this.detectionResultColumns[e]=t},r.prototype.getDetectionResultColumn=function(e){return this.detectionResultColumns[e]},r.prototype.toString=function(){var e=this.detectionResultColumns[0];null==e&&(e=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var t=new sr,n=0;n<e.getCodewords().length;n++){t.format("CW %3d:",n);for(var i=0;i<this.barcodeColumnCount+2;i++)if(null!=this.detectionResultColumns[i]){var a=this.detectionResultColumns[i].getCodewords()[n];null!=a?t.format(" %3d|%3d",a.getRowNumber(),a.getValue()):t.format("    |   ")}else t.format("    |   ");t.format("%n")}return t.toString()},r}();const f0=u0;var c0=function(){function r(e,t,n,i){this.rowNumber=r.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(t),this.bucket=Math.trunc(n),this.value=Math.trunc(i)}return r.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},r.prototype.isValidRowNumber=function(e){return e!==r.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},r.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))},r.prototype.getWidth=function(){return this.endX-this.startX},r.prototype.getStartX=function(){return this.startX},r.prototype.getEndX=function(){return this.endX},r.prototype.getBucket=function(){return this.bucket},r.prototype.getValue=function(){return this.value},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.setRowNumber=function(e){this.rowNumber=e},r.prototype.toString=function(){return this.rowNumber+"|"+this.value},r.BARCODE_ROW_UNKNOWN=-1,r}();const h0=c0;var d0=function(){function r(){}return r.initialize=function(){for(var e=0;e<V.SYMBOL_TABLE.length;e++)for(var t=V.SYMBOL_TABLE[e],n=1&t,i=0;i<V.BARS_IN_MODULE;i++){for(var a=0;(1&t)===n;)a+=1,t>>=1;n=1&t,r.RATIOS_TABLE[e]||(r.RATIOS_TABLE[e]=new Array(V.BARS_IN_MODULE)),r.RATIOS_TABLE[e][V.BARS_IN_MODULE-i-1]=Math.fround(a/V.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},r.getDecodedValue=function(e){var t=r.getDecodedCodewordValue(r.sampleBitCounts(e));return-1!==t?t:r.getClosestDecodedValue(e)},r.sampleBitCounts=function(e){for(var t=G.sum(e),n=new Int32Array(V.BARS_IN_MODULE),i=0,a=0,o=0;o<V.MODULES_IN_CODEWORD;o++)a+e[i]<=t/(2*V.MODULES_IN_CODEWORD)+o*t/V.MODULES_IN_CODEWORD&&(a+=e[i],i++),n[i]++;return n},r.getDecodedCodewordValue=function(e){var t=r.getBitValue(e);return-1===V.getCodeword(t)?-1:t},r.getBitValue=function(e){for(var t=0,n=0;n<e.length;n++)for(var i=0;i<e[n];i++)t=t<<1|(n%2==0?1:0);return Math.trunc(t)},r.getClosestDecodedValue=function(e){var t=G.sum(e),n=new Array(V.BARS_IN_MODULE);if(t>1)for(var i=0;i<n.length;i++)n[i]=Math.fround(e[i]/t);var a=qt.MAX_VALUE,o=-1;this.bSymbolTableReady||r.initialize();for(var s=0;s<r.RATIOS_TABLE.length;s++){for(var u=0,f=r.RATIOS_TABLE[s],c=0;c<V.BARS_IN_MODULE;c++){var h=Math.fround(f[c]-n[c]);if((u+=Math.fround(h*h))>=a)break}u<a&&(a=u,o=V.SYMBOL_TABLE[s])}return o},r.bSymbolTableReady=!1,r.RATIOS_TABLE=new Array(V.SYMBOL_TABLE.length).map(function(e){return new Array(V.BARS_IN_MODULE)}),r}();const l0=d0;var v0=function(){function r(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return r.prototype.getSegmentIndex=function(){return this.segmentIndex},r.prototype.setSegmentIndex=function(e){this.segmentIndex=e},r.prototype.getFileId=function(){return this.fileId},r.prototype.setFileId=function(e){this.fileId=e},r.prototype.getOptionalData=function(){return this.optionalData},r.prototype.setOptionalData=function(e){this.optionalData=e},r.prototype.isLastSegment=function(){return this.lastSegment},r.prototype.setLastSegment=function(e){this.lastSegment=e},r.prototype.getSegmentCount=function(){return this.segmentCount},r.prototype.setSegmentCount=function(e){this.segmentCount=e},r.prototype.getSender=function(){return this.sender||null},r.prototype.setSender=function(e){this.sender=e},r.prototype.getAddressee=function(){return this.addressee||null},r.prototype.setAddressee=function(e){this.addressee=e},r.prototype.getFileName=function(){return this.fileName},r.prototype.setFileName=function(e){this.fileName=e},r.prototype.getFileSize=function(){return this.fileSize},r.prototype.setFileSize=function(e){this.fileSize=e},r.prototype.getChecksum=function(){return this.checksum},r.prototype.setChecksum=function(e){this.checksum=e},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.setTimestamp=function(e){this.timestamp=e},r}();const p0=v0;var g0=function(){function r(){}return r.parseLong=function(e,t){return void 0===t&&(t=void 0),parseInt(e,t)},r}();const tn=g0;var x0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),y0=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return x0(e,r),e.kind="NullPointerException",e}(re);const w0=y0;var _0=function(){function r(){}return r.prototype.writeBytes=function(e){this.writeBytesOffset(e,0,e.length)},r.prototype.writeBytesOffset=function(e,t,n){if(null==e)throw new w0;if(t<0||t>e.length||n<0||t+n>e.length||t+n<0)throw new Kt;if(0!==n)for(var i=0;i<n;i++)this.write(e[t+i])},r.prototype.flush=function(){},r.prototype.close=function(){},r}();const A0=_0;var C0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),E0=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C0(e,r),e}(re);const m0=E0;var S0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),I0=function(r){function e(t){void 0===t&&(t=32);var n=r.call(this)||this;if(n.count=0,t<0)throw new R("Negative initial size: "+t);return n.buf=new Uint8Array(t),n}return S0(e,r),e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var i=this.buf.length<<1;if(i-t<0&&(i=t),i<0){if(t<0)throw new m0;i=B.MAX_VALUE}this.buf=oe.copyOfUint8Array(this.buf,i)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,n,i){if(n<0||n>t.length||i<0||n+i-t.length>0)throw new Kt;this.ensureCapacity(this.count+i),q.arraycopy(t,n,this.buf,this.count,i),this.count+=i},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return oe.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?"string"==typeof t?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e}(A0);const O0=I0;var Lt,j=(()=>(function(r){r[r.ALPHA=0]="ALPHA",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.PUNCT=3]="PUNCT",r[r.ALPHA_SHIFT=4]="ALPHA_SHIFT",r[r.PUNCT_SHIFT=5]="PUNCT_SHIFT"}(j||(j={})),j))();function rn(){if(typeof window<"u")return window.BigInt||null;if(typeof global<"u")return global.BigInt||null;if(typeof self<"u")return self.BigInt||null;throw new Error("Can't search globals for BigInt!")}function Ge(r){if(typeof Lt>"u"&&(Lt=rn()),null===Lt)throw new Error("BigInt is not supported!");return Lt(r)}var D0=function(){function r(){}return r.decode=function(e,t){var n=new F(""),i=fe.ISO8859_1;n.enableDecoding(i);for(var a=1,o=e[a++],s=new p0;a<e[0];){switch(o){case r.TEXT_COMPACTION_MODE_LATCH:a=r.textCompaction(e,a,n);break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:a=r.byteCompaction(o,e,i,a,n);break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[a++]);break;case r.NUMERIC_COMPACTION_MODE_LATCH:a=r.numericCompaction(e,a,n);break;case r.ECI_CHARSET:fe.getCharacterSetECIByValue(e[a++]);break;case r.ECI_GENERAL_PURPOSE:a+=2;break;case r.ECI_USER_DEFINED:a++;break;case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:a=r.decodeMacroBlock(e,a,s);break;case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:throw new O;default:a--,a=r.textCompaction(e,a,n)}if(!(a<e.length))throw O.getFormatInstance();o=e[a++]}if(0===n.length())throw O.getFormatInstance();var f=new Ot(null,n.toString(),null,t);return f.setOther(s),f},r.decodeMacroBlock=function(e,t,n){if(t+r.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw O.getFormatInstance();for(var i=new Int32Array(r.NUMBER_OF_SEQUENCE_CODEWORDS),a=0;a<r.NUMBER_OF_SEQUENCE_CODEWORDS;a++,t++)i[a]=e[t];n.setSegmentIndex(B.parseInt(r.decodeBase900toBase10(i,r.NUMBER_OF_SEQUENCE_CODEWORDS)));var o=new F;t=r.textCompaction(e,t,o),n.setFileId(o.toString());var s=-1;for(e[t]===r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=t+1);t<e[0];)switch(e[t]){case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++t]){case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var u=new F;t=r.textCompaction(e,t+1,u),n.setFileName(u.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var f=new F;t=r.textCompaction(e,t+1,f),n.setSender(f.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var c=new F;t=r.textCompaction(e,t+1,c),n.setAddressee(c.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var h=new F;t=r.numericCompaction(e,t+1,h),n.setSegmentCount(B.parseInt(h.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var d=new F;t=r.numericCompaction(e,t+1,d),n.setTimestamp(tn.parseLong(d.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var l=new F;t=r.numericCompaction(e,t+1,l),n.setChecksum(B.parseInt(l.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var v=new F;t=r.numericCompaction(e,t+1,v),n.setFileSize(tn.parseLong(v.toString()));break;default:throw O.getFormatInstance()}break;case r.MACRO_PDF417_TERMINATOR:t++,n.setLastSegment(!0);break;default:throw O.getFormatInstance()}if(-1!==s){var p=t-s;n.isLastSegment()&&p--,n.setOptionalData(oe.copyOfRange(e,s,s+p))}return t},r.textCompaction=function(e,t,n){for(var i=new Int32Array(2*(e[0]-t)),a=new Int32Array(2*(e[0]-t)),o=0,s=!1;t<e[0]&&!s;){var u=e[t++];if(u<r.TEXT_COMPACTION_MODE_LATCH)i[o]=u/30,i[o+1]=u%30,o+=2;else switch(u){case r.TEXT_COMPACTION_MODE_LATCH:i[o++]=r.TEXT_COMPACTION_MODE_LATCH;break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,s=!0;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i[o]=r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,u=e[t++],a[o]=u,o++}}return r.decodeTextCompaction(i,a,o,n),t},r.decodeTextCompaction=function(e,t,n,i){for(var a=j.ALPHA,o=j.ALPHA,s=0;s<n;){var u=e[s],f="";switch(a){case j.ALPHA:if(u<26)f=String.fromCharCode(65+u);else switch(u){case 26:f=" ";break;case r.LL:a=j.LOWER;break;case r.ML:a=j.MIXED;break;case r.PS:o=a,a=j.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}break;case j.LOWER:if(u<26)f=String.fromCharCode(97+u);else switch(u){case 26:f=" ";break;case r.AS:o=a,a=j.ALPHA_SHIFT;break;case r.ML:a=j.MIXED;break;case r.PS:o=a,a=j.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}break;case j.MIXED:if(u<r.PL)f=r.MIXED_CHARS[u];else switch(u){case r.PL:a=j.PUNCT;break;case 26:f=" ";break;case r.LL:a=j.LOWER;break;case r.AL:a=j.ALPHA;break;case r.PS:o=a,a=j.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}break;case j.PUNCT:if(u<r.PAL)f=r.PUNCT_CHARS[u];else switch(u){case r.PAL:a=j.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}break;case j.ALPHA_SHIFT:if(a=o,u<26)f=String.fromCharCode(65+u);else switch(u){case 26:f=" ";break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}break;case j.PUNCT_SHIFT:if(a=o,u<r.PAL)f=r.PUNCT_CHARS[u];else switch(u){case r.PAL:a=j.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=j.ALPHA}}""!==f&&i.append(f),s++}},r.byteCompaction=function(e,t,n,i,a){var o=new O0,s=0,u=0,f=!1;switch(e){case r.BYTE_COMPACTION_MODE_LATCH:for(var c=new Int32Array(6),h=t[i++];i<t[0]&&!f;)switch(c[s++]=h,u=900*u+h,h=t[i++],h){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,f=!0;break;default:if(s%5==0&&s>0){for(var d=0;d<6;++d)o.write(Number(Ge(u)>>Ge(8*(5-d))));u=0,s=0}}i===t[0]&&h<r.TEXT_COMPACTION_MODE_LATCH&&(c[s++]=h);for(var l=0;l<s;l++)o.write(c[l]);break;case r.BYTE_COMPACTION_MODE_LATCH_6:for(;i<t[0]&&!f;){var v=t[i++];if(v<r.TEXT_COMPACTION_MODE_LATCH)s++,u=900*u+v;else switch(v){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,f=!0}if(s%5==0&&s>0){for(d=0;d<6;++d)o.write(Number(Ge(u)>>Ge(8*(5-d))));u=0,s=0}}}return a.append(De.decode(o.toByteArray(),n)),i},r.numericCompaction=function(e,t,n){for(var i=0,a=!1,o=new Int32Array(r.MAX_NUMERIC_CODEWORDS);t<e[0]&&!a;){var s=e[t++];if(t===e[0]&&(a=!0),s<r.TEXT_COMPACTION_MODE_LATCH)o[i]=s,i++;else switch(s){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,a=!0}(i%r.MAX_NUMERIC_CODEWORDS==0||s===r.NUMERIC_COMPACTION_MODE_LATCH||a)&&i>0&&(n.append(r.decodeBase900toBase10(o,i)),i=0)}return t},r.decodeBase900toBase10=function(e,t){for(var n=Ge(0),i=0;i<t;i++)n+=r.EXP900[t-i-1]*Ge(e[i]);var a=n.toString();if("1"!==a.charAt(0))throw new O;return a.substring(1)},r.TEXT_COMPACTION_MODE_LATCH=900,r.BYTE_COMPACTION_MODE_LATCH=901,r.NUMERIC_COMPACTION_MODE_LATCH=902,r.BYTE_COMPACTION_MODE_LATCH_6=924,r.ECI_USER_DEFINED=925,r.ECI_GENERAL_PURPOSE=926,r.ECI_CHARSET=927,r.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,r.MACRO_PDF417_TERMINATOR=922,r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,r.MAX_NUMERIC_CODEWORDS=15,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,r.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,r.PL=25,r.LL=27,r.AS=27,r.ML=28,r.AL=28,r.PS=29,r.PAL=29,r.PUNCT_CHARS=";<>@[\\]_`~!\r\t,:\n-.$/\"|*()?{}'",r.MIXED_CHARS="0123456789&\r\t,:#-.$/+%*=^",r.EXP900=rn()?function T0(){var r=[];r[0]=Ge(1);var e=Ge(900);r[1]=e;for(var t=2;t<16;t++)r[t]=r[t-1]*e;return r}():[],r.NUMBER_OF_SEQUENCE_CODEWORDS=2,r}();const R0=D0;var xt=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},b0=function(){function r(){}return r.decode=function(e,t,n,i,a,o,s){for(var h,u=new or(e,t,n,i,a),f=null,c=null,d=!0;;d=!1){if(null!=t&&(f=r.getRowIndicatorColumn(e,u,t,!0,o,s)),null!=i&&(c=r.getRowIndicatorColumn(e,u,i,!1,o,s)),null==(h=r.merge(f,c)))throw E.getNotFoundInstance();var l=h.getBoundingBox();if(!d||null==l||!(l.getMinY()<u.getMinY()||l.getMaxY()>u.getMaxY()))break;u=l}h.setBoundingBox(u);var v=h.getBarcodeColumnCount()+1;h.setDetectionResultColumn(0,f),h.setDetectionResultColumn(v,c);for(var p=null!=f,x=1;x<=v;x++){var w=p?x:v-x;if(void 0===h.getDetectionResultColumn(w)){var y=void 0;y=0===w||w===v?new en(u,0===w):new $r(u),h.setDetectionResultColumn(w,y);for(var _=-1,C=_,m=u.getMinY();m<=u.getMaxY();m++){if((_=r.getStartColumn(h,w,m,p))<0||_>u.getMaxX()){if(-1===C)continue;_=C}var S=r.detectCodeword(e,u.getMinX(),u.getMaxX(),p,_,m,o,s);null!=S&&(y.setCodeword(m,S),C=_,o=Math.min(o,S.getWidth()),s=Math.max(s,S.getWidth()))}}}return r.createDecoderResult(h)},r.merge=function(e,t){if(null==e&&null==t)return null;var n=r.getBarcodeMetadata(e,t);if(null==n)return null;var i=or.merge(r.adjustBoundingBox(e),r.adjustBoundingBox(t));return new f0(n,i)},r.adjustBoundingBox=function(e){var t,n;if(null==e)return null;var i=e.getRowHeights();if(null==i)return null;var a=r.getMax(i),o=0;try{for(var s=xt(i),u=s.next();!u.done;u=s.next()){var f=u.value;if(o+=a-f,f>0)break}}catch(l){t={error:l}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}for(var c=e.getCodewords(),h=0;o>0&&null==c[h];h++)o--;var d=0;for(h=i.length-1;h>=0&&(d+=a-i[h],!(i[h]>0));h--);for(h=c.length-1;d>0&&null==c[h];h--)d--;return e.getBoundingBox().addMissingRows(o,d,e.isLeft())},r.getMax=function(e){var t,n,i=-1;try{for(var a=xt(e),o=a.next();!o.done;o=a.next())i=Math.max(i,o.value)}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.getBarcodeMetadata=function(e,t){var n,i;return null==e||null==(n=e.getBarcodeMetadata())?null==t?null:t.getBarcodeMetadata():null==t||null==(i=t.getBarcodeMetadata())?n:n.getColumnCount()!==i.getColumnCount()&&n.getErrorCorrectionLevel()!==i.getErrorCorrectionLevel()&&n.getRowCount()!==i.getRowCount()?null:n},r.getRowIndicatorColumn=function(e,t,n,i,a,o){for(var s=new en(t,i),u=0;u<2;u++)for(var f=0===u?1:-1,c=Math.trunc(Math.trunc(n.getX())),h=Math.trunc(Math.trunc(n.getY()));h<=t.getMaxY()&&h>=t.getMinY();h+=f){var d=r.detectCodeword(e,0,e.getWidth(),i,c,h,a,o);null!=d&&(s.setCodeword(h,d),c=i?d.getStartX():d.getEndX())}return s},r.adjustCodewordCount=function(e,t){var n=t[0][1],i=n.getValue(),a=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-r.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===i.length){if(a<1||a>V.MAX_CODEWORDS_IN_BARCODE)throw E.getNotFoundInstance();n.setValue(a)}else i[0]!==a&&n.setValue(a)},r.createDecoderResult=function(e){var t=r.createBarcodeMatrix(e);r.adjustCodewordCount(e,t);for(var n=new Array,i=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),a=[],o=new Array,s=0;s<e.getBarcodeRowCount();s++)for(var u=0;u<e.getBarcodeColumnCount();u++){var f=t[s][u+1].getValue(),c=s*e.getBarcodeColumnCount()+u;0===f.length?n.push(c):1===f.length?i[c]=f[0]:(o.push(c),a.push(f))}for(var h=new Array(a.length),d=0;d<h.length;d++)h[d]=a[d];return r.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),i,V.toIntArray(n),V.toIntArray(o),h)},r.createDecoderResultFromAmbiguousValues=function(e,t,n,i,a){for(var o=new Int32Array(i.length),s=100;s-- >0;){for(var u=0;u<o.length;u++)t[i[u]]=a[u][o[u]];try{return r.decodeCodewords(t,e,n)}catch(c){if(!(c instanceof ae))throw c}if(0===o.length)throw ae.getChecksumInstance();for(u=0;u<o.length;u++){if(o[u]<a[u].length-1){o[u]++;break}if(o[u]=0,u===o.length-1)throw ae.getChecksumInstance()}}throw ae.getChecksumInstance()},r.createBarcodeMatrix=function(e){for(var t,n,i,a,o=Array.from({length:e.getBarcodeRowCount()},function(){return new Array(e.getBarcodeColumnCount()+2)}),s=0;s<o.length;s++)for(var u=0;u<o[s].length;u++)o[s][u]=new gt;var f=0;try{for(var c=xt(e.getDetectionResultColumns()),h=c.next();!h.done;h=c.next()){var d=h.value;if(null!=d)try{for(var l=(i=void 0,xt(d.getCodewords())),v=l.next();!v.done;v=l.next()){var p=v.value;if(null!=p){var x=p.getRowNumber();if(x>=0){if(x>=o.length)continue;o[x][f].setValue(p.getValue())}}}}catch(w){i={error:w}}finally{try{v&&!v.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}f++}}catch(w){t={error:w}}finally{try{h&&!h.done&&(n=c.return)&&n.call(c)}finally{if(t)throw t.error}}return o},r.isValidBarcodeColumn=function(e,t){return t>=0&&t<=e.getBarcodeColumnCount()+1},r.getStartColumn=function(e,t,n,i){var a,o,s=i?1:-1,u=null;if(r.isValidBarcodeColumn(e,t-s)&&(u=e.getDetectionResultColumn(t-s).getCodeword(n)),null!=u)return i?u.getEndX():u.getStartX();if(null!=(u=e.getDetectionResultColumn(t).getCodewordNearby(n)))return i?u.getStartX():u.getEndX();if(r.isValidBarcodeColumn(e,t-s)&&(u=e.getDetectionResultColumn(t-s).getCodewordNearby(n)),null!=u)return i?u.getEndX():u.getStartX();for(var f=0;r.isValidBarcodeColumn(e,t-s);){t-=s;try{for(var c=(a=void 0,xt(e.getDetectionResultColumn(t).getCodewords())),h=c.next();!h.done;h=c.next()){var d=h.value;if(null!=d)return(i?d.getEndX():d.getStartX())+s*f*(d.getEndX()-d.getStartX())}}catch(l){a={error:l}}finally{try{h&&!h.done&&(o=c.return)&&o.call(c)}finally{if(a)throw a.error}}f++}return i?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},r.detectCodeword=function(e,t,n,i,a,o,s,u){a=r.adjustCodewordStartColumn(e,t,n,i,a,o);var f=r.getModuleBitCount(e,t,n,i,a,o);if(null==f)return null;var c,h=G.sum(f);if(i)c=a+h;else{for(var d=0;d<f.length/2;d++){var l=f[d];f[d]=f[f.length-1-d],f[f.length-1-d]=l}a=(c=a)-h}if(!r.checkCodewordSkew(h,s,u))return null;var v=l0.getDecodedValue(f),p=V.getCodeword(v);return-1===p?null:new h0(a,c,r.getCodewordBucketNumber(v),p)},r.getModuleBitCount=function(e,t,n,i,a,o){for(var s=a,u=new Int32Array(8),f=0,c=i?1:-1,h=i;(i?s<n:s>=t)&&f<u.length;)e.get(s,o)===h?(u[f]++,s+=c):(f++,h=!h);return f===u.length||s===(i?n:t)&&f===u.length-1?u:null},r.getNumberOfECCodeWords=function(e){return 2<<e},r.adjustCodewordStartColumn=function(e,t,n,i,a,o){for(var s=a,u=i?-1:1,f=0;f<2;f++){for(;(i?s>=t:s<n)&&i===e.get(s,o);){if(Math.abs(a-s)>r.CODEWORD_SKEW_SIZE)return a;s+=u}u=-u,i=!i}return s},r.checkCodewordSkew=function(e,t,n){return t-r.CODEWORD_SKEW_SIZE<=e&&e<=n+r.CODEWORD_SKEW_SIZE},r.decodeCodewords=function(e,t,n){if(0===e.length)throw O.getFormatInstance();var i=1<<t+1,a=r.correctErrors(e,n,i);r.verifyCodewordCount(e,i);var o=R0.decode(e,""+t);return o.setErrorsCorrected(a),o.setErasures(n.length),o},r.correctErrors=function(e,t,n){if(null!=t&&t.length>n/2+r.MAX_ERRORS||n<0||n>r.MAX_EC_CODEWORDS)throw ae.getChecksumInstance();return r.errorCorrection.decode(e,n,t)},r.verifyCodewordCount=function(e,t){if(e.length<4)throw O.getFormatInstance();var n=e[0];if(n>e.length)throw O.getFormatInstance();if(0===n){if(!(t<e.length))throw O.getFormatInstance();e[0]=e.length-t}},r.getBitCountForCodeword=function(e){for(var t=new Int32Array(8),n=0,i=t.length-1;!((1&e)!==n&&(n=1&e,i--,i<0));)t[i]++,e>>=1;return t},r.getCodewordBucketNumber=function(e){return e instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(e):this.getCodewordBucketNumber_number(e)},r.getCodewordBucketNumber_number=function(e){return r.getCodewordBucketNumber(r.getBitCountForCodeword(e))},r.getCodewordBucketNumber_Int32Array=function(e){return(e[0]-e[2]+e[4]-e[6]+9)%9},r.toString=function(e){for(var t=new sr,n=0;n<e.length;n++){t.format("Row %2d: ",n);for(var i=0;i<e[n].length;i++){var a=e[n][i];0===a.getValue().length?t.format("        ",null):t.format("%4d(%2d)",a.getValue()[0],a.getConfidence(a.getValue()[0]))}t.format("%n")}return t.toString()},r.CODEWORD_SKEW_SIZE=2,r.MAX_ERRORS=3,r.MAX_EC_CODEWORDS=512,r.errorCorrection=new K1,r}();const N0=b0;var M0=function(){function r(){}return r.prototype.decode=function(e,t){void 0===t&&(t=null);var n=r.decode(e,t,!1);if(null==n||0===n.length||null==n[0])throw E.getNotFoundInstance();return n[0]},r.prototype.decodeMultiple=function(e,t){void 0===t&&(t=null);try{return r.decode(e,t,!0)}catch(n){throw n instanceof O||n instanceof ae?E.getNotFoundInstance():n}},r.decode=function(e,t,n){var i,a,o=new Array,s=U1.detectMultiple(e,t,n);try{for(var u=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(s.getPoints()),f=u.next();!f.done;f=u.next()){var c=f.value,h=N0.decode(s.getBits(),c[4],c[5],c[6],c[7],r.getMinCodewordWidth(c),r.getMaxCodewordWidth(c)),d=new we(h.getText(),h.getRawBytes(),void 0,c,N.PDF_417);d.putMetadata(xe.ERROR_CORRECTION_LEVEL,h.getECLevel());var l=h.getOther();null!=l&&d.putMetadata(xe.PDF417_EXTRA_METADATA,l),o.push(d)}}catch(v){i={error:v}}finally{try{f&&!f.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}return o.map(function(v){return v})},r.getMaxWidth=function(e,t){return null==e||null==t?0:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMinWidth=function(e,t){return null==e||null==t?B.MAX_VALUE:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(r.getMaxWidth(e[0],e[4]),r.getMaxWidth(e[6],e[2])*V.MODULES_IN_CODEWORD/V.MODULES_IN_STOP_PATTERN),Math.max(r.getMaxWidth(e[1],e[5]),r.getMaxWidth(e[7],e[3])*V.MODULES_IN_CODEWORD/V.MODULES_IN_STOP_PATTERN)))},r.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(r.getMinWidth(e[0],e[4]),r.getMinWidth(e[6],e[2])*V.MODULES_IN_CODEWORD/V.MODULES_IN_STOP_PATTERN),Math.min(r.getMinWidth(e[1],e[5]),r.getMinWidth(e[7],e[3])*V.MODULES_IN_CODEWORD/V.MODULES_IN_STOP_PATTERN)))},r.prototype.reset=function(){},r}();const kt=M0;var B0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),F0=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return B0(e,r),e.kind="ReaderException",e}(re);const nn=F0;var an=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},L0=function(){function r(){}return r.prototype.decode=function(e,t){return this.setHints(t),this.decodeInternal(e)},r.prototype.decodeWithState=function(e){return null==this.readers&&this.setHints(null),this.decodeInternal(e)},r.prototype.setHints=function(e){this.hints=e;var t=null!=e&&void 0!==e.get(K.TRY_HARDER),n=null==e?null:e.get(K.POSSIBLE_FORMATS),i=new Array;if(null!=n){var a=n.some(function(o){return o===N.UPC_A||o===N.UPC_E||o===N.EAN_13||o===N.EAN_8||o===N.CODABAR||o===N.CODE_39||o===N.CODE_93||o===N.CODE_128||o===N.ITF||o===N.RSS_14||o===N.RSS_EXPANDED});a&&!t&&i.push(new st(e)),n.includes(N.QR_CODE)&&i.push(new Ft),n.includes(N.DATA_MATRIX)&&i.push(new Bt),n.includes(N.AZTEC)&&i.push(new Rt),n.includes(N.PDF_417)&&i.push(new kt),a&&t&&i.push(new st(e))}0===i.length&&(t||i.push(new st(e)),i.push(new Ft),i.push(new Bt),i.push(new Rt),i.push(new kt),t&&i.push(new st(e))),this.readers=i},r.prototype.reset=function(){var e,t;if(null!==this.readers)try{for(var n=an(this.readers),i=n.next();!i.done;i=n.next())i.value.reset()}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},r.prototype.decodeInternal=function(e){var t,n;if(null===this.readers)throw new nn("No readers where selected, nothing can be read.");try{for(var i=an(this.readers),a=i.next();!a.done;a=i.next()){var o=a.value;try{return o.decode(e,this.hints)}catch(s){if(s instanceof nn)continue}}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}throw new E("No MultiFormat Readers were able to detect the code.")},r}();const on=L0;var k0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),U0=(function(r){function e(t,n){void 0===t&&(t=null),void 0===n&&(n=500);var a=new on;return a.setHints(t),r.call(this,a,n)||this}k0(e,r),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)}}(nt),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),V0=(function(r){U0(function e(t){return void 0===t&&(t=500),r.call(this,new kt,t)||this},r)}(nt),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),Ut=(function(r){V0(function e(t){return void 0===t&&(t=500),r.call(this,new Ft,t)||this},r)}(nt),(()=>(function(r){r[r.ERROR_CORRECTION=0]="ERROR_CORRECTION",r[r.CHARACTER_SET=1]="CHARACTER_SET",r[r.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",r[r.DATA_MATRIX_COMPACT=3]="DATA_MATRIX_COMPACT",r[r.MIN_SIZE=4]="MIN_SIZE",r[r.MAX_SIZE=5]="MAX_SIZE",r[r.MARGIN=6]="MARGIN",r[r.PDF417_COMPACT=7]="PDF417_COMPACT",r[r.PDF417_COMPACTION=8]="PDF417_COMPACTION",r[r.PDF417_DIMENSIONS=9]="PDF417_DIMENSIONS",r[r.AZTEC_LAYERS=10]="AZTEC_LAYERS",r[r.QR_VERSION=11]="QR_VERSION",r[r.GS1_FORMAT=12]="GS1_FORMAT",r[r.FORCE_C40=13]="FORCE_C40"}(Ut||(Ut={})),Ut))());const L=Ut;var H0=function(){function r(e){this.field=e,this.cachedGenerators=[],this.cachedGenerators.push(new Ue(e,Int32Array.from([1])))}return r.prototype.buildGenerator=function(e){var t=this.cachedGenerators;if(e>=t.length)for(var n=t[t.length-1],i=this.field,a=t.length;a<=e;a++){var o=n.multiply(new Ue(i,Int32Array.from([1,i.exp(a-1+i.getGeneratorBase())])));t.push(o),n=o}return t[e]},r.prototype.encode=function(e,t){if(0===t)throw new R("No error correction bytes");var n=e.length-t;if(n<=0)throw new R("No data bytes provided");var i=this.buildGenerator(t),a=new Int32Array(n);q.arraycopy(e,0,a,0,n);for(var o=new Ue(this.field,a),u=(o=o.multiplyByMonomial(t,1)).divide(i)[1].getCoefficients(),f=t-u.length,c=0;c<f;c++)e[n+c]=0;q.arraycopy(u,0,e,n+f,u.length)},r}();const sn=H0;var G0=function(){function r(){}return r.applyMaskPenaltyRule1=function(e){return r.applyMaskPenaltyRule1Internal(e,!0)+r.applyMaskPenaltyRule1Internal(e,!1)},r.applyMaskPenaltyRule2=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a-1;o++)for(var s=n[o],u=0;u<i-1;u++){var f=s[u];f===s[u+1]&&f===n[o+1][u]&&f===n[o+1][u+1]&&t++}return r.N2*t},r.applyMaskPenaltyRule3=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=0;s<i;s++){var u=n[o];s+6<i&&1===u[s]&&0===u[s+1]&&1===u[s+2]&&1===u[s+3]&&1===u[s+4]&&0===u[s+5]&&1===u[s+6]&&(r.isWhiteHorizontal(u,s-4,s)||r.isWhiteHorizontal(u,s+7,s+11))&&t++,o+6<a&&1===n[o][s]&&0===n[o+1][s]&&1===n[o+2][s]&&1===n[o+3][s]&&1===n[o+4][s]&&0===n[o+5][s]&&1===n[o+6][s]&&(r.isWhiteVertical(n,s,o-4,o)||r.isWhiteVertical(n,s,o+7,o+11))&&t++}return t*r.N3},r.isWhiteHorizontal=function(e,t,n){t=Math.max(t,0),n=Math.min(n,e.length);for(var i=t;i<n;i++)if(1===e[i])return!1;return!0},r.isWhiteVertical=function(e,t,n,i){n=Math.max(n,0),i=Math.min(i,e.length);for(var a=n;a<i;a++)if(1===e[a][t])return!1;return!0},r.applyMaskPenaltyRule4=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=n[o],u=0;u<i;u++)1===s[u]&&t++;var f=e.getHeight()*e.getWidth();return Math.floor(10*Math.abs(2*t-f)/f)*r.N4},r.getDataMaskBit=function(e,t,n){var i,a;switch(e){case 0:i=n+t&1;break;case 1:i=1&n;break;case 2:i=t%3;break;case 3:i=(n+t)%3;break;case 4:i=Math.floor(n/2)+Math.floor(t/3)&1;break;case 5:i=(1&(a=n*t))+a%3;break;case 6:i=(1&(a=n*t))+a%3&1;break;case 7:i=(a=n*t)%3+(n+t&1)&1;break;default:throw new R("Invalid mask pattern: "+e)}return 0===i},r.applyMaskPenaltyRule1Internal=function(e,t){for(var n=0,i=t?e.getHeight():e.getWidth(),a=t?e.getWidth():e.getHeight(),o=e.getArray(),s=0;s<i;s++){for(var u=0,f=-1,c=0;c<a;c++){var h=t?o[s][c]:o[c][s];h===f?u++:(u>=5&&(n+=r.N1+(u-5)),u=1,f=h)}u>=5&&(n+=r.N1+(u-5))}return n},r.N1=3,r.N2=3,r.N3=40,r.N4=10,r}();const yt=G0;var X0=function(){function r(e,t){this.width=e,this.height=t;for(var n=new Array(t),i=0;i!==t;i++)n[i]=new Uint8Array(e);this.bytes=n}return r.prototype.getHeight=function(){return this.height},r.prototype.getWidth=function(){return this.width},r.prototype.get=function(e,t){return this.bytes[t][e]},r.prototype.getArray=function(){return this.bytes},r.prototype.setNumber=function(e,t,n){this.bytes[t][e]=n},r.prototype.setBoolean=function(e,t,n){this.bytes[t][e]=n?1:0},r.prototype.clear=function(e){var t,n;try{for(var i=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.bytes),a=i.next();!a.done;a=i.next())oe.fill(a.value,e)}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;if(this.width!==t.width||this.height!==t.height)return!1;for(var n=0,i=this.height;n<i;++n)for(var a=this.bytes[n],o=t.bytes[n],s=0,u=this.width;s<u;++s)if(a[s]!==o[s])return!1;return!0},r.prototype.toString=function(){for(var e=new F,t=0,n=this.height;t<n;++t){for(var i=this.bytes[t],a=0,o=this.width;a<o;++a)switch(i[a]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ")}e.append("\n")}return e.toString()},r}();const un=X0;var z0=function(){function r(){this.maskPattern=-1}return r.prototype.getMode=function(){return this.mode},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getVersion=function(){return this.version},r.prototype.getMaskPattern=function(){return this.maskPattern},r.prototype.getMatrix=function(){return this.matrix},r.prototype.toString=function(){var e=new F;return e.append("<<\n"),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append("\n ecLevel: "),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append("\n version: "),e.append(this.version?this.version.toString():"null"),e.append("\n maskPattern: "),e.append(this.maskPattern.toString()),this.matrix?(e.append("\n matrix:\n"),e.append(this.matrix.toString())):e.append("\n matrix: null\n"),e.append(">>\n"),e.toString()},r.prototype.setMode=function(e){this.mode=e},r.prototype.setECLevel=function(e){this.ecLevel=e},r.prototype.setVersion=function(e){this.version=e},r.prototype.setMaskPattern=function(e){this.maskPattern=e},r.prototype.setMatrix=function(e){this.matrix=e},r.isValidMaskPattern=function(e){return e>=0&&e<r.NUM_MASK_PATTERNS},r.NUM_MASK_PATTERNS=8,r}();const fr=z0;var Z0=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),j0=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Z0(e,r),e.kind="WriterException",e}(re);const J=j0;var Y0=function(){function r(){}return r.clearMatrix=function(e){e.clear(255)},r.buildMatrix=function(e,t,n,i,a){r.clearMatrix(a),r.embedBasicPatterns(n,a),r.embedTypeInfo(t,i,a),r.maybeEmbedVersionInfo(n,a),r.embedDataBits(e,i,a)},r.embedBasicPatterns=function(e,t){r.embedPositionDetectionPatternsAndSeparators(t),r.embedDarkDotAtLeftBottomCorner(t),r.maybeEmbedPositionAdjustmentPatterns(e,t),r.embedTimingPatterns(t)},r.embedTypeInfo=function(e,t,n){var i=new ve;r.makeTypeInfoBits(e,t,i);for(var a=0,o=i.getSize();a<o;++a){var s=i.get(i.getSize()-1-a),u=r.TYPE_INFO_COORDINATES[a];if(n.setBoolean(u[0],u[1],s),a<8){var h=n.getWidth()-a-1;n.setBoolean(h,d=8,s)}else{h=8;var d=n.getHeight()-7+(a-8);n.setBoolean(h,d,s)}}},r.maybeEmbedVersionInfo=function(e,t){if(!(e.getVersionNumber()<7)){var n=new ve;r.makeVersionInfoBits(e,n);for(var i=17,a=0;a<6;++a)for(var o=0;o<3;++o){var s=n.get(i);i--,t.setBoolean(a,t.getHeight()-11+o,s),t.setBoolean(t.getHeight()-11+o,a,s)}}},r.embedDataBits=function(e,t,n){for(var i=0,a=-1,o=n.getWidth()-1,s=n.getHeight()-1;o>0;){for(6===o&&(o-=1);s>=0&&s<n.getHeight();){for(var u=0;u<2;++u){var f=o-u;if(r.isEmpty(n.get(f,s))){var c=void 0;i<e.getSize()?(c=e.get(i),++i):c=!1,255!==t&&yt.getDataMaskBit(t,f,s)&&(c=!c),n.setBoolean(f,s,c)}}s+=a}s+=a=-a,o-=2}if(i!==e.getSize())throw new J("Not all bits consumed: "+i+"/"+e.getSize())},r.findMSBSet=function(e){return 32-B.numberOfLeadingZeros(e)},r.calculateBCHCode=function(e,t){if(0===t)throw new R("0 polynomial");var n=r.findMSBSet(t);for(e<<=n-1;r.findMSBSet(e)>=n;)e^=t<<r.findMSBSet(e)-n;return e},r.makeTypeInfoBits=function(e,t,n){if(!fr.isValidMaskPattern(t))throw new J("Invalid mask pattern");var i=e.getBits()<<3|t;n.appendBits(i,5);var a=r.calculateBCHCode(i,r.TYPE_INFO_POLY);n.appendBits(a,10);var o=new ve;if(o.appendBits(r.TYPE_INFO_MASK_PATTERN,15),n.xor(o),15!==n.getSize())throw new J("should not happen but we got: "+n.getSize())},r.makeVersionInfoBits=function(e,t){t.appendBits(e.getVersionNumber(),6);var n=r.calculateBCHCode(e.getVersionNumber(),r.VERSION_INFO_POLY);if(t.appendBits(n,12),18!==t.getSize())throw new J("should not happen but we got: "+t.getSize())},r.isEmpty=function(e){return 255===e},r.embedTimingPatterns=function(e){for(var t=8;t<e.getWidth()-8;++t){var n=(t+1)%2;r.isEmpty(e.get(t,6))&&e.setNumber(t,6,n),r.isEmpty(e.get(6,t))&&e.setNumber(6,t,n)}},r.embedDarkDotAtLeftBottomCorner=function(e){if(0===e.get(8,e.getHeight()-8))throw new J;e.setNumber(8,e.getHeight()-8,1)},r.embedHorizontalSeparationPattern=function(e,t,n){for(var i=0;i<8;++i){if(!r.isEmpty(n.get(e+i,t)))throw new J;n.setNumber(e+i,t,0)}},r.embedVerticalSeparationPattern=function(e,t,n){for(var i=0;i<7;++i){if(!r.isEmpty(n.get(e,t+i)))throw new J;n.setNumber(e,t+i,0)}},r.embedPositionAdjustmentPattern=function(e,t,n){for(var i=0;i<5;++i)for(var a=r.POSITION_ADJUSTMENT_PATTERN[i],o=0;o<5;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPattern=function(e,t,n){for(var i=0;i<7;++i)for(var a=r.POSITION_DETECTION_PATTERN[i],o=0;o<7;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPatternsAndSeparators=function(e){var t=r.POSITION_DETECTION_PATTERN[0].length;r.embedPositionDetectionPattern(0,0,e),r.embedPositionDetectionPattern(e.getWidth()-t,0,e),r.embedPositionDetectionPattern(0,e.getWidth()-t,e),r.embedHorizontalSeparationPattern(0,7,e),r.embedHorizontalSeparationPattern(e.getWidth()-8,7,e),r.embedHorizontalSeparationPattern(0,e.getWidth()-8,e),r.embedVerticalSeparationPattern(7,0,e),r.embedVerticalSeparationPattern(e.getHeight()-7-1,0,e),r.embedVerticalSeparationPattern(7,e.getHeight()-7,e)},r.maybeEmbedPositionAdjustmentPatterns=function(e,t){if(!(e.getVersionNumber()<2))for(var n=e.getVersionNumber()-1,i=r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],a=0,o=i.length;a!==o;a++){var s=i[a];if(s>=0)for(var u=0;u!==o;u++){var f=i[u];f>=0&&r.isEmpty(t.get(f,s))&&r.embedPositionAdjustmentPattern(f-2,s-2,t)}}},r.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),r.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),r.VERSION_INFO_POLY=7973,r.TYPE_INFO_POLY=1335,r.TYPE_INFO_MASK_PATTERN=21522,r}();const fn=Y0;var K0=function(){function r(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}return r.prototype.getDataBytes=function(){return this.dataBytes},r.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},r}();const q0=K0;var cn=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Q0=function(){function r(){}return r.calculateMaskPenalty=function(e){return yt.applyMaskPenaltyRule1(e)+yt.applyMaskPenaltyRule2(e)+yt.applyMaskPenaltyRule3(e)+yt.applyMaskPenaltyRule4(e)},r.encode=function(e,t,n){void 0===n&&(n=null);var i=r.DEFAULT_BYTE_MODE_ENCODING,a=null!==n&&void 0!==n.get(L.CHARACTER_SET);a&&(i=n.get(L.CHARACTER_SET).toString());var o=this.chooseMode(e,i),s=new ve;if(o===Q.BYTE&&(a||r.DEFAULT_BYTE_MODE_ENCODING!==i)){var u=fe.getCharacterSetECIByName(i);void 0!==u&&this.appendECI(u,s)}this.appendModeInfo(o,s);var c,f=new ve;if(this.appendBytes(e,o,f,i),null!==n&&void 0!==n.get(L.QR_VERSION)){var h=Number.parseInt(n.get(L.QR_VERSION).toString(),10);c=Je.getVersionForNumber(h);var d=this.calculateBitsNeeded(o,s,f,c);if(!this.willFit(d,c,t))throw new J("Data too big for requested version")}else c=this.recommendVersion(t,o,s,f);var l=new ve;l.appendBitArray(s);var v=o===Q.BYTE?f.getSizeInBytes():e.length;this.appendLengthInfo(v,c,o,l),l.appendBitArray(f);var p=c.getECBlocksForLevel(t),x=c.getTotalCodewords()-p.getTotalECCodewords();this.terminateBits(x,l);var w=this.interleaveWithECBytes(l,c.getTotalCodewords(),x,p.getNumBlocks()),y=new fr;y.setECLevel(t),y.setMode(o),y.setVersion(c);var _=c.getDimensionForVersion(),C=new un(_,_),m=this.chooseMaskPattern(w,t,c,C);return y.setMaskPattern(m),fn.buildMatrix(w,t,c,m,C),y.setMatrix(C),y},r.recommendVersion=function(e,t,n,i){var a=this.calculateBitsNeeded(t,n,i,Je.getVersionForNumber(1)),o=this.chooseVersion(a,e),s=this.calculateBitsNeeded(t,n,i,o);return this.chooseVersion(s,e)},r.calculateBitsNeeded=function(e,t,n,i){return t.getSize()+e.getCharacterCountBits(i)+n.getSize()},r.getAlphanumericCode=function(e){return e<r.ALPHANUMERIC_TABLE.length?r.ALPHANUMERIC_TABLE[e]:-1},r.chooseMode=function(e,t){if(void 0===t&&(t=null),fe.SJIS.getName()===t&&this.isOnlyDoubleByteKanji(e))return Q.KANJI;for(var n=!1,i=!1,a=0,o=e.length;a<o;++a){var s=e.charAt(a);if(r.isDigit(s))n=!0;else{if(-1===this.getAlphanumericCode(s.charCodeAt(0)))return Q.BYTE;i=!0}}return i?Q.ALPHANUMERIC:n?Q.NUMERIC:Q.BYTE},r.isOnlyDoubleByteKanji=function(e){var t;try{t=De.encode(e,fe.SJIS)}catch{return!1}var n=t.length;if(n%2!=0)return!1;for(var i=0;i<n;i+=2){var a=255&t[i];if((a<129||a>159)&&(a<224||a>235))return!1}return!0},r.chooseMaskPattern=function(e,t,n,i){for(var a=Number.MAX_SAFE_INTEGER,o=-1,s=0;s<fr.NUM_MASK_PATTERNS;s++){fn.buildMatrix(e,t,n,s,i);var u=this.calculateMaskPenalty(i);u<a&&(a=u,o=s)}return o},r.chooseVersion=function(e,t){for(var n=1;n<=40;n++){var i=Je.getVersionForNumber(n);if(r.willFit(e,i,t))return i}throw new J("Data too big")},r.willFit=function(e,t,n){return t.getTotalCodewords()-t.getECBlocksForLevel(n).getTotalECCodewords()>=(e+7)/8},r.terminateBits=function(e,t){var n=8*e;if(t.getSize()>n)throw new J("data bits cannot fit in the QR Code"+t.getSize()+" > "+n);for(var i=0;i<4&&t.getSize()<n;++i)t.appendBit(!1);var a=7&t.getSize();if(a>0)for(i=a;i<8;i++)t.appendBit(!1);var o=e-t.getSizeInBytes();for(i=0;i<o;++i)t.appendBits(1&i?17:236,8);if(t.getSize()!==n)throw new J("Bits size does not equal capacity")},r.getNumDataBytesAndNumECBytesForBlockID=function(e,t,n,i,a,o){if(i>=n)throw new J("Block ID too large");var s=e%n,u=n-s,f=Math.floor(e/n),c=f+1,h=Math.floor(t/n),d=h+1,l=f-h,v=c-d;if(l!==v)throw new J("EC bytes mismatch");if(n!==u+s)throw new J("RS blocks mismatch");if(e!==(h+l)*u+(d+v)*s)throw new J("Total bytes mismatch");i<u?(a[0]=h,o[0]=l):(a[0]=d,o[0]=v)},r.interleaveWithECBytes=function(e,t,n,i){var a,o,s,u;if(e.getSizeInBytes()!==n)throw new J("Number of bits and data bytes does not match");for(var f=0,c=0,h=0,d=new Array,l=0;l<i;++l){var v=new Int32Array(1),p=new Int32Array(1);r.getNumDataBytesAndNumECBytesForBlockID(t,n,i,l,v,p);var x=v[0],w=new Uint8Array(x);e.toBytes(8*f,w,0,x);var y=r.generateECBytes(w,p[0]);d.push(new q0(w,y)),c=Math.max(c,x),h=Math.max(h,y.length),f+=v[0]}if(n!==f)throw new J("Data bytes does not match offset");var _=new ve;for(l=0;l<c;++l)try{for(var C=(a=void 0,cn(d)),m=C.next();!m.done;m=C.next())l<(w=m.value.getDataBytes()).length&&_.appendBits(w[l],8)}catch(P){a={error:P}}finally{try{m&&!m.done&&(o=C.return)&&o.call(C)}finally{if(a)throw a.error}}for(l=0;l<h;++l)try{for(var I=(s=void 0,cn(d)),D=I.next();!D.done;D=I.next())l<(y=D.value.getErrorCorrectionBytes()).length&&_.appendBits(y[l],8)}catch(P){s={error:P}}finally{try{D&&!D.done&&(u=I.return)&&u.call(I)}finally{if(s)throw s.error}}if(t!==_.getSizeInBytes())throw new J("Interleaving error: "+t+" and "+_.getSizeInBytes()+" differ.");return _},r.generateECBytes=function(e,t){for(var n=e.length,i=new Int32Array(n+t),a=0;a<n;a++)i[a]=255&e[a];new sn(Se.QR_CODE_FIELD_256).encode(i,t);var o=new Uint8Array(t);for(a=0;a<t;a++)o[a]=i[n+a];return o},r.appendModeInfo=function(e,t){t.appendBits(e.getBits(),4)},r.appendLengthInfo=function(e,t,n,i){var a=n.getCharacterCountBits(t);if(e>=1<<a)throw new J(e+" is bigger than "+((1<<a)-1));i.appendBits(e,a)},r.appendBytes=function(e,t,n,i){switch(t){case Q.NUMERIC:r.appendNumericBytes(e,n);break;case Q.ALPHANUMERIC:r.appendAlphanumericBytes(e,n);break;case Q.BYTE:r.append8BitBytes(e,n,i);break;case Q.KANJI:r.appendKanjiBytes(e,n);break;default:throw new J("Invalid mode: "+t)}},r.getDigit=function(e){return e.charCodeAt(0)-48},r.isDigit=function(e){var t=r.getDigit(e);return t>=0&&t<=9},r.appendNumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getDigit(e.charAt(i));if(i+2<n){var o=r.getDigit(e.charAt(i+1)),s=r.getDigit(e.charAt(i+2));t.appendBits(100*a+10*o+s,10),i+=3}else i+1<n?(o=r.getDigit(e.charAt(i+1)),t.appendBits(10*a+o,7),i+=2):(t.appendBits(a,4),i++)}},r.appendAlphanumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getAlphanumericCode(e.charCodeAt(i));if(-1===a)throw new J;if(i+1<n){var o=r.getAlphanumericCode(e.charCodeAt(i+1));if(-1===o)throw new J;t.appendBits(45*a+o,11),i+=2}else t.appendBits(a,6),i++}},r.append8BitBytes=function(e,t,n){var i;try{i=De.encode(e,n)}catch(u){throw new J(u)}for(var a=0,o=i.length;a!==o;a++)t.appendBits(i[a],8)},r.appendKanjiBytes=function(e,t){var n;try{n=De.encode(e,fe.SJIS)}catch(h){throw new J(h)}for(var i=n.length,a=0;a<i;a+=2){var u=(255&n[a])<<8&4294967295|255&n[a+1],f=-1;if(u>=33088&&u<=40956?f=u-33088:u>=57408&&u<=60351&&(f=u-49472),-1===f)throw new J("Invalid byte sequence");t.appendBits(192*(f>>8)+(255&f),13)}},r.appendECI=function(e,t){t.appendBits(Q.ECI.getBits(),4),t.appendBits(e.getValue(),8)},r.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),r.DEFAULT_BYTE_MODE_ENCODING=fe.UTF8.getName(),r}();const Vt=Q0;!function(){function r(){}r.prototype.write=function(e,t,n,i){if(void 0===i&&(i=null),0===e.length)throw new R("Found empty contents");if(t<0||n<0)throw new R("Requested dimensions are too small: "+t+"x"+n);var a=Le.L,o=r.QUIET_ZONE_SIZE;null!==i&&(void 0!==i.get(L.ERROR_CORRECTION)&&(a=Le.fromString(i.get(L.ERROR_CORRECTION).toString())),void 0!==i.get(L.MARGIN)&&(o=Number.parseInt(i.get(L.MARGIN).toString(),10)));var s=Vt.encode(e,a,i);return this.renderResult(s,t,n,o)},r.prototype.writeToDom=function(e,t,n,i,a){void 0===a&&(a=null),"string"==typeof e&&(e=document.querySelector(e));var o=this.write(t,n,i,a);e&&e.appendChild(o)},r.prototype.renderResult=function(e,t,n,i){var a=e.getMatrix();if(null===a)throw new Be;for(var o=a.getWidth(),s=a.getHeight(),u=o+2*i,f=s+2*i,c=Math.max(t,u),h=Math.max(n,f),d=Math.min(Math.floor(c/u),Math.floor(h/f)),l=Math.floor((c-o*d)/2),v=Math.floor((h-s*d)/2),p=this.createSVGElement(c,h),x=0,w=v;x<s;x++,w+=d)for(var y=0,_=l;y<o;y++,_+=d)if(1===a.get(y,x)){var C=this.createSvgRectElement(_,w,d,d);p.appendChild(C)}return p},r.prototype.createSVGElement=function(e,t){var n=document.createElementNS(r.SVG_NS,"svg");return n.setAttributeNS(null,"height",e.toString()),n.setAttributeNS(null,"width",t.toString()),n},r.prototype.createSvgRectElement=function(e,t,n,i){var a=document.createElementNS(r.SVG_NS,"rect");return a.setAttributeNS(null,"x",e.toString()),a.setAttributeNS(null,"y",t.toString()),a.setAttributeNS(null,"height",n.toString()),a.setAttributeNS(null,"width",i.toString()),a.setAttributeNS(null,"fill","#000000"),a},r.QUIET_ZONE_SIZE=4,r.SVG_NS="http://www.w3.org/2000/svg"}();!function(){function r(){}r.prototype.encode=function(e,t,n,i,a){if(0===e.length)throw new R("Found empty contents");if(t!==N.QR_CODE)throw new R("Can only encode QR_CODE, but got "+t);if(n<0||i<0)throw new R("Requested dimensions are too small: "+n+"x"+i);var o=Le.L,s=r.QUIET_ZONE_SIZE;null!==a&&(void 0!==a.get(L.ERROR_CORRECTION)&&(o=Le.fromString(a.get(L.ERROR_CORRECTION).toString())),void 0!==a.get(L.MARGIN)&&(s=Number.parseInt(a.get(L.MARGIN).toString(),10)));var u=Vt.encode(e,o,a);return r.renderResult(u,n,i,s)},r.renderResult=function(e,t,n,i){var a=e.getMatrix();if(null===a)throw new Be;for(var o=a.getWidth(),s=a.getHeight(),u=o+2*i,f=s+2*i,c=Math.max(t,u),h=Math.max(n,f),d=Math.min(Math.floor(c/u),Math.floor(h/f)),l=Math.floor((c-o*d)/2),v=Math.floor((h-s*d)/2),p=new me(c,h),x=0,w=v;x<s;x++,w+=d)for(var y=0,_=l;y<o;y++,_+=d)1===a.get(y,x)&&p.setRegion(_,w,d,d);return p},r.QUIET_ZONE_SIZE=4}();var es=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}();!function(r){function e(t,n,i,a,o,s,u,f){var c=r.call(this,s,u)||this;if(c.yuvData=t,c.dataWidth=n,c.dataHeight=i,c.left=a,c.top=o,a+s>n||o+u>i)throw new R("Crop rectangle does not fit within image data.");return f&&c.reverseHorizontal(s,u),c}es(e,r),e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new R("Requested row is outside the image: "+t);var i=this.getWidth();return(null==n||n.length<i)&&(n=new Uint8ClampedArray(i)),q.arraycopy(this.yuvData,(t+this.top)*this.dataWidth+this.left,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.yuvData;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return q.arraycopy(this.yuvData,o,a,0,i),a;for(var s=0;s<n;s++)q.arraycopy(this.yuvData,o,a,s*t,t),o+=this.dataWidth;return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+n,i,a,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,n=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,i=new Int32Array(t*n),a=this.yuvData,o=this.top*this.dataWidth+this.left,s=0;s<n;s++){for(var u=s*t,f=0;f<t;f++)i[u+f]=4278190080|65793*(255&a[o+f*e.THUMBNAIL_SCALE_FACTOR]);o+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return i},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,n){for(var i=this.yuvData,a=0,o=this.top*this.dataWidth+this.left;a<n;a++,o+=this.dataWidth)for(var s=o+t/2,u=o,f=o+t-1;u<s;u++,f--){var c=i[u];i[u]=i[f],i[f]=c}},e.prototype.invert=function(){return new mt(this)},e.THUMBNAIL_SCALE_FACTOR=2}(lt);var ts=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}();!function(r){function e(t,n,i,a,o,s,u){var f=r.call(this,n,i)||this;if(f.dataWidth=a,f.dataHeight=o,f.left=s,f.top=u,4===t.BYTES_PER_ELEMENT){for(var c=n*i,h=new Uint8ClampedArray(c),d=0;d<c;d++){var l=t[d];h[d]=((l>>16&255)+(l>>7&510)+(255&l))/4&255}f.luminances=h}else f.luminances=t;if(void 0===a&&(f.dataWidth=n),void 0===o&&(f.dataHeight=i),void 0===s&&(f.left=0),void 0===u&&(f.top=0),f.left+n>f.dataWidth||f.top+i>f.dataHeight)throw new R("Crop rectangle does not fit within image data.");return f}ts(e,r),e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new R("Requested row is outside the image: "+t);var i=this.getWidth();return(null==n||n.length<i)&&(n=new Uint8ClampedArray(i)),q.arraycopy(this.luminances,(t+this.top)*this.dataWidth+this.left,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.luminances;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return q.arraycopy(this.luminances,o,a,0,i),a;for(var s=0;s<n;s++)q.arraycopy(this.luminances,o,a,s*t,t),o+=this.dataWidth;return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.luminances,i,a,this.dataWidth,this.dataHeight,this.left+t,this.top+n)},e.prototype.invert=function(){return new mt(this)}}(lt);var rs=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),ns=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return rs(e,r),e.forName=function(t){return this.getCharacterSetECIByName(t)},e}(fe);const cr=ns;var is=function(){function r(){}return r.ISO_8859_1=fe.ISO8859_1,r}();const hn=is;var as=function(){function r(e,t,n){this.codewords=e,this.numcols=t,this.numrows=n,this.bits=new Uint8Array(t*n),oe.fill(this.bits,2)}return r.prototype.getNumrows=function(){return this.numrows},r.prototype.getNumcols=function(){return this.numcols},r.prototype.getBits=function(){return this.bits},r.prototype.getBit=function(e,t){return 1===this.bits[t*this.numcols+e]},r.prototype.setBit=function(e,t,n){this.bits[t*this.numcols+e]=n?1:0},r.prototype.noBit=function(e,t){return 2===this.bits[t*this.numcols+e]},r.prototype.place=function(){var e=0,t=4,n=0;do{t===this.numrows&&0===n&&this.corner1(e++),t===this.numrows-2&&0===n&&this.numcols%4!=0&&this.corner2(e++),t===this.numrows-2&&0===n&&this.numcols%8==4&&this.corner3(e++),t===this.numrows+4&&2===n&&this.numcols%8==0&&this.corner4(e++);do{t<this.numrows&&n>=0&&this.noBit(n,t)&&this.utah(t,n,e++),t-=2,n+=2}while(t>=0&&n<this.numcols);t++,n+=3;do{t>=0&&n<this.numcols&&this.noBit(n,t)&&this.utah(t,n,e++),t+=2,n-=2}while(t<this.numrows&&n>=0);t+=3,n++}while(t<this.numrows||n<this.numcols);this.noBit(this.numcols-1,this.numrows-1)&&(this.setBit(this.numcols-1,this.numrows-1,!0),this.setBit(this.numcols-2,this.numrows-2,!0))},r.prototype.module=function(e,t,n,i){e<0&&(e+=this.numrows,t+=4-(this.numrows+4)%8),t<0&&(t+=this.numcols,e+=4-(this.numcols+4)%8);var a=this.codewords.charCodeAt(n);this.setBit(t,e,0!=(a&=1<<8-i))},r.prototype.utah=function(e,t,n){this.module(e-2,t-2,n,1),this.module(e-2,t-1,n,2),this.module(e-1,t-2,n,3),this.module(e-1,t-1,n,4),this.module(e-1,t,n,5),this.module(e,t-2,n,6),this.module(e,t-1,n,7),this.module(e,t,n,8)},r.prototype.corner1=function(e){this.module(this.numrows-1,0,e,1),this.module(this.numrows-1,1,e,2),this.module(this.numrows-1,2,e,3),this.module(0,this.numcols-2,e,4),this.module(0,this.numcols-1,e,5),this.module(1,this.numcols-1,e,6),this.module(2,this.numcols-1,e,7),this.module(3,this.numcols-1,e,8)},r.prototype.corner2=function(e){this.module(this.numrows-3,0,e,1),this.module(this.numrows-2,0,e,2),this.module(this.numrows-1,0,e,3),this.module(0,this.numcols-4,e,4),this.module(0,this.numcols-3,e,5),this.module(0,this.numcols-2,e,6),this.module(0,this.numcols-1,e,7),this.module(1,this.numcols-1,e,8)},r.prototype.corner3=function(e){this.module(this.numrows-3,0,e,1),this.module(this.numrows-2,0,e,2),this.module(this.numrows-1,0,e,3),this.module(0,this.numcols-2,e,4),this.module(0,this.numcols-1,e,5),this.module(1,this.numcols-1,e,6),this.module(2,this.numcols-1,e,7),this.module(3,this.numcols-1,e,8)},r.prototype.corner4=function(e){this.module(this.numrows-1,0,e,1),this.module(this.numrows-1,this.numcols-1,e,2),this.module(0,this.numcols-3,e,3),this.module(0,this.numcols-2,e,4),this.module(0,this.numcols-1,e,5),this.module(1,this.numcols-3,e,6),this.module(1,this.numcols-2,e,7),this.module(1,this.numcols-1,e,8)},r}();const os=as;var hr,dn=[5,7,10,11,12,14,18,20,24,28,36,42,48,56,62,68],ss=[[228,48,15,111,62],[23,68,144,134,240,92,254],[28,24,185,166,223,248,116,255,110,61],[175,138,205,12,194,168,39,245,60,97,120],[41,153,158,91,61,42,142,213,97,178,100,242],[156,97,192,252,95,9,157,119,138,45,18,186,83,185],[83,195,100,39,188,75,66,61,241,213,109,129,94,254,225,48,90,188],[15,195,244,9,233,71,168,2,188,160,153,145,253,79,108,82,27,174,186,172],[52,190,88,205,109,39,176,21,155,197,251,223,155,21,5,172,254,124,12,181,184,96,50,193],[211,231,43,97,71,96,103,174,37,151,170,53,75,34,249,121,17,138,110,213,141,136,120,151,233,168,93,255],[245,127,242,218,130,250,162,181,102,120,84,179,220,251,80,182,229,18,2,4,68,33,101,137,95,119,115,44,175,184,59,25,225,98,81,112],[77,193,137,31,19,38,22,153,247,105,122,2,245,133,242,8,175,95,100,9,167,105,214,111,57,121,21,1,253,57,54,101,248,202,69,50,150,177,226,5,9,5],[245,132,172,223,96,32,117,22,238,133,238,231,205,188,237,87,191,106,16,147,118,23,37,90,170,205,131,88,120,100,66,138,186,240,82,44,176,87,187,147,160,175,69,213,92,253,225,19],[175,9,223,238,12,17,220,208,100,29,175,170,230,192,215,235,150,159,36,223,38,200,132,54,228,146,218,234,117,203,29,232,144,238,22,150,201,117,62,207,164,13,137,245,127,67,247,28,155,43,203,107,233,53,143,46],[242,93,169,50,144,210,39,118,202,188,201,189,143,108,196,37,185,112,134,230,245,63,197,190,250,106,185,221,175,64,114,71,161,44,147,6,27,218,51,63,87,10,40,130,188,17,163,31,176,170,4,107,232,7,94,166,224,124,86,47,11,204],[220,228,173,89,251,149,159,56,89,33,147,244,154,36,73,127,213,136,248,180,234,197,158,177,68,122,93,213,15,160,227,236,66,139,153,185,202,167,179,25,220,232,96,210,231,136,223,239,181,241,59,52,172,25,49,232,211,189,64,54,108,153,132,63,96,103,82,186]],Ht=(hr=function(r,e){for(var t=1,n=0;n<255;n++)e[n]=t,r[t]=n,(t*=2)>=256&&(t^=301);return{LOG:r,ALOG:e}}([],[]),hr.LOG),ln=hr.ALOG,Gt="[)>\x1e05\x1d",Wt="[)>\x1e06\x1d",ys=function(){function r(){}return r.encodeECC200=function(e,t){if(e.length!==t.getDataCapacity())throw new Error("The number of codewords does not match the selected symbol");var n=new F;n.append(e);var i=t.getInterleavedBlockCount();if(1===i){var a=this.createECCBlock(e,t.getErrorCodewords());n.append(a)}else{for(var o=[],s=[],u=0;u<i;u++)o[u]=t.getDataLengthForInterleavedBlock(u+1),s[u]=t.getErrorLengthForInterleavedBlock(u+1);for(var f=0;f<i;f++){for(var c=new F,h=f;h<t.getDataCapacity();h+=i)c.append(e.charAt(h));a=this.createECCBlock(c.toString(),s[f]);for(var d=0,l=f;l<s[f]*i;l+=i)n.setCharAt(t.getDataCapacity()+l,a.charAt(d++))}}return n.toString()},r.createECCBlock=function(e,t){for(var n=-1,i=0;i<dn.length;i++)if(dn[i]===t){n=i;break}if(n<0)throw new Error("Illegal number of error correction codewords specified: "+t);var a=ss[n],o=[];for(i=0;i<t;i++)o[i]=0;for(i=0;i<e.length;i++){for(var s=o[t-1]^e.charAt(i).charCodeAt(0),u=t-1;u>0;u--)o[u]=0!==s&&0!==a[u]?o[u-1]^ln[(Ht[s]+Ht[a[u]])%255]:o[u-1];o[0]=0!==s&&0!==a[0]?ln[(Ht[s]+Ht[a[0]])%255]:0}var f=[];for(i=0;i<t;i++)f[i]=o[t-i-1];return f.map(function(c){return String.fromCharCode(c)}).join("")},r}();const ws=ys;var _s=function(){function r(){}return r.prototype.getEncodingMode=function(){return 0},r.prototype.encode=function(e){if(k.determineConsecutiveDigitCount(e.getMessage(),e.pos)>=2)e.writeCodeword(this.encodeASCIIDigits(e.getMessage().charCodeAt(e.pos),e.getMessage().charCodeAt(e.pos+1))),e.pos+=2;else{var n=e.getCurrentChar(),i=k.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(i!==this.getEncodingMode())switch(i){case 5:return e.writeCodeword(231),void e.signalEncoderChange(5);case 1:return e.writeCodeword(230),void e.signalEncoderChange(1);case 3:e.writeCodeword(238),e.signalEncoderChange(3);break;case 2:e.writeCodeword(239),e.signalEncoderChange(2);break;case 4:e.writeCodeword(240),e.signalEncoderChange(4);break;default:throw new Error("Illegal mode: "+i)}else k.isExtendedASCII(n)?(e.writeCodeword(235),e.writeCodeword(n-128+1),e.pos++):(e.writeCodeword(n+1),e.pos++)}},r.prototype.encodeASCIIDigits=function(e,t){if(k.isDigit(e)&&k.isDigit(t))return 10*(e-48)+(t-48)+130;throw new Error("not digits: "+e+t)},r}(),As=function(){function r(){}return r.prototype.getEncodingMode=function(){return 5},r.prototype.encode=function(e){var t=new F;for(t.append(0);e.hasMoreCharacters();){var n=e.getCurrentChar();if(t.append(n),e.pos++,k.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode())!==this.getEncodingMode()){e.signalEncoderChange(0);break}}var a=t.length()-1,s=e.getCodewordCount()+a+1;e.updateSymbolInfo(s);var u=e.getSymbolInfo().getDataCapacity()-s>0;if(e.hasMoreCharacters()||u)if(a<=249)t.setCharAt(0,U.getCharAt(a));else{if(!(a<=1555))throw new Error("Message length not in valid ranges: "+a);t.setCharAt(0,U.getCharAt(Math.floor(a/250)+249)),t.insert(1,U.getCharAt(a%250))}var f=0;for(n=t.length();f<n;f++)e.writeCodeword(this.randomize255State(t.charAt(f).charCodeAt(0),e.getCodewordCount()+1))},r.prototype.randomize255State=function(e,t){var i=e+(149*t%255+1);return i<=255?i:i-256},r}(),lr=function(){function r(){}return r.prototype.getEncodingMode=function(){return 1},r.prototype.encodeMaximal=function(e){for(var t=new F,n=0,i=e.pos,a=0;e.hasMoreCharacters();){var o=e.getCurrentChar();e.pos++,n=this.encodeChar(o,t),t.length()%3==0&&(i=e.pos,a=t.length())}if(a!==t.length()){var s=Math.floor(t.length()/3*2),u=Math.floor(e.getCodewordCount()+s+1);e.updateSymbolInfo(u);var f=e.getSymbolInfo().getDataCapacity()-u,c=Math.floor(t.length()%3);(2===c&&2!==f||1===c&&(n>3||1!==f))&&(e.pos=i)}t.length()>0&&e.writeCodeword(230),this.handleEOD(e,t)},r.prototype.encode=function(e){for(var t=new F;e.hasMoreCharacters();){var n=e.getCurrentChar();e.pos++;var i=this.encodeChar(n,t),a=2*Math.floor(t.length()/3),o=e.getCodewordCount()+a;e.updateSymbolInfo(o);var s=e.getSymbolInfo().getDataCapacity()-o;if(!e.hasMoreCharacters()){var u=new F;for(t.length()%3==2&&2!==s&&(i=this.backtrackOneCharacter(e,t,u,i));t.length()%3==1&&(i>3||1!==s);)i=this.backtrackOneCharacter(e,t,u,i);break}if(t.length()%3==0&&k.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode())!==this.getEncodingMode()){e.signalEncoderChange(0);break}}this.handleEOD(e,t)},r.prototype.backtrackOneCharacter=function(e,t,n,i){var a=t.length(),o=t.toString().substring(0,a-i);t.setLengthToZero(),t.append(o),e.pos--;var s=e.getCurrentChar();return i=this.encodeChar(s,n),e.resetSymbolInfo(),i},r.prototype.writeNextTriplet=function(e,t){e.writeCodewords(this.encodeToCodewords(t.toString()));var n=t.toString().substring(3);t.setLengthToZero(),t.append(n)},r.prototype.handleEOD=function(e,t){var n=Math.floor(t.length()/3*2),i=t.length()%3,a=e.getCodewordCount()+n;e.updateSymbolInfo(a);var o=e.getSymbolInfo().getDataCapacity()-a;if(2===i){for(t.append("\0");t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(254)}else if(1===o&&1===i){for(;t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(254),e.pos--}else{if(0!==i)throw new Error("Unexpected case. Please report!");for(;t.length()>=3;)this.writeNextTriplet(e,t);(o>0||e.hasMoreCharacters())&&e.writeCodeword(254)}e.signalEncoderChange(0)},r.prototype.encodeChar=function(e,t){return e===" ".charCodeAt(0)?(t.append(3),1):e>="0".charCodeAt(0)&&e<="9".charCodeAt(0)?(t.append(e-48+4),1):e>="A".charCodeAt(0)&&e<="Z".charCodeAt(0)?(t.append(e-65+14),1):e<" ".charCodeAt(0)?(t.append(0),t.append(e),2):e<="/".charCodeAt(0)?(t.append(1),t.append(e-33),2):e<="@".charCodeAt(0)?(t.append(1),t.append(e-58+15),2):e<="_".charCodeAt(0)?(t.append(1),t.append(e-91+22),2):e<=127?(t.append(2),t.append(e-96),2):(t.append("1\x1e"),2+this.encodeChar(e-128,t))},r.prototype.encodeToCodewords=function(e){var t=1600*e.charCodeAt(0)+40*e.charCodeAt(1)+e.charCodeAt(2)+1,n=t/256,i=t%256,a=new F;return a.append(n),a.append(i),a.toString()},r}(),Cs=function(){function r(){}return r.prototype.getEncodingMode=function(){return 4},r.prototype.encode=function(e){for(var t=new F;e.hasMoreCharacters();){var n=e.getCurrentChar();if(this.encodeChar(n,t),e.pos++,t.length()>=4){e.writeCodewords(this.encodeToCodewords(t.toString()));var a=t.toString().substring(4);if(t.setLengthToZero(),t.append(a),k.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode())!==this.getEncodingMode()){e.signalEncoderChange(0);break}}}t.append(U.getCharAt(31)),this.handleEOD(e,t)},r.prototype.handleEOD=function(e,t){try{var n=t.length();if(0===n)return;if(1===n){e.updateSymbolInfo();var i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount(),a=e.getRemainingCharacters();if(a>i&&(e.updateSymbolInfo(e.getCodewordCount()+1),i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount()),a<=i&&i<=2)return}if(n>4)throw new Error("Count must not exceed 4");var o=n-1,s=this.encodeToCodewords(t.toString()),f=!e.hasMoreCharacters()&&o<=2;o<=2&&(e.updateSymbolInfo(e.getCodewordCount()+o),(i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount())>=3&&(f=!1,e.updateSymbolInfo(e.getCodewordCount()+s.length))),f?(e.resetSymbolInfo(),e.pos-=o):e.writeCodewords(s)}finally{e.signalEncoderChange(0)}},r.prototype.encodeChar=function(e,t){e>=" ".charCodeAt(0)&&e<="?".charCodeAt(0)?t.append(e):e>="@".charCodeAt(0)&&e<="^".charCodeAt(0)?t.append(U.getCharAt(e-64)):k.illegalCharacter(U.getCharAt(e))},r.prototype.encodeToCodewords=function(e){var t=e.length;if(0===t)throw new Error("StringBuilder must not be empty");var s=(e.charAt(0).charCodeAt(0)<<18)+((t>=2?e.charAt(1).charCodeAt(0):0)<<12)+((t>=3?e.charAt(2).charCodeAt(0):0)<<6)+(t>=4?e.charAt(3).charCodeAt(0):0),u=s>>16&255,f=s>>8&255,c=255&s,h=new F;return h.append(u),t>=2&&h.append(f),t>=3&&h.append(c),h.toString()},r}(),Es=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),X=function(){function r(e,t,n,i,a,o,s,u){void 0===s&&(s=0),void 0===u&&(u=0),this.rectangular=e,this.dataCapacity=t,this.errorCodewords=n,this.matrixWidth=i,this.matrixHeight=a,this.dataRegions=o,this.rsBlockData=s,this.rsBlockError=u}return r.lookup=function(e,t,n,i,a){var o,s;void 0===t&&(t=0),void 0===n&&(n=null),void 0===i&&(i=null),void 0===a&&(a=!0);try{for(var u=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(Is),f=u.next();!f.done;f=u.next()){var c=f.value;if((1!==t||!c.rectangular)&&(2!==t||c.rectangular)&&(null==n||!(c.getSymbolWidth()<n.getWidth()||c.getSymbolHeight()<n.getHeight()))&&(null==i||!(c.getSymbolWidth()>i.getWidth()||c.getSymbolHeight()>i.getHeight()))&&e<=c.dataCapacity)return c}}catch(h){o={error:h}}finally{try{f&&!f.done&&(s=u.return)&&s.call(u)}finally{if(o)throw o.error}}if(a)throw new Error("Can't find a symbol arrangement that matches the message. Data codewords: "+e);return null},r.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},r.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},r.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+2*this.getHorizontalDataRegions()},r.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+2*this.getVerticalDataRegions()},r.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},r.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},r.prototype.getDataCapacity=function(){return this.dataCapacity},r.prototype.getErrorCodewords=function(){return this.errorCodewords},r.prototype.getDataLengthForInterleavedBlock=function(e){return this.rsBlockData},r.prototype.getErrorLengthForInterleavedBlock=function(e){return this.rsBlockError},r}();const gn=X;var Ss=function(r){function e(){return r.call(this,!1,1558,620,22,22,36,-1,62)||this}return Es(e,r),e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e}(X),Is=[new X(!1,3,5,8,8,1),new X(!1,5,7,10,10,1),new X(!0,5,7,16,6,1),new X(!1,8,10,12,12,1),new X(!0,10,11,14,6,2),new X(!1,12,12,14,14,1),new X(!0,16,14,24,10,1),new X(!1,18,14,16,16,1),new X(!1,22,18,18,18,1),new X(!0,22,18,16,10,2),new X(!1,30,20,20,20,1),new X(!0,32,24,16,14,2),new X(!1,36,24,22,22,1),new X(!1,44,28,24,24,1),new X(!0,49,28,22,14,2),new X(!1,62,36,14,14,4),new X(!1,86,42,16,16,4),new X(!1,114,48,18,18,4),new X(!1,144,56,20,20,4),new X(!1,174,68,22,22,4),new X(!1,204,84,24,24,4,102,42),new X(!1,280,112,14,14,16,140,56),new X(!1,368,144,16,16,16,92,36),new X(!1,456,192,18,18,16,114,48),new X(!1,576,224,20,20,16,144,56),new X(!1,696,272,22,22,16,174,68),new X(!1,816,336,24,24,16,136,56),new X(!1,1050,408,18,18,36,175,68),new X(!1,1304,496,20,20,36,163,62),new Ss],Os=function(){function r(e){this.msg=e,this.pos=0,this.skipAtEnd=0;for(var t=e.split("").map(function(s){return s.charCodeAt(0)}),n=new F,i=0,a=t.length;i<a;i++){var o=String.fromCharCode(255&t[i]);if("?"===o&&"?"!==e.charAt(i))throw new Error("Message contains characters outside ISO-8859-1 encoding.");n.append(o)}this.msg=n.toString(),this.shape=0,this.codewords=new F,this.newEncoding=-1}return r.prototype.setSymbolShape=function(e){this.shape=e},r.prototype.setSizeConstraints=function(e,t){this.minSize=e,this.maxSize=t},r.prototype.getMessage=function(){return this.msg},r.prototype.setSkipAtEnd=function(e){this.skipAtEnd=e},r.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCodewords=function(){return this.codewords},r.prototype.writeCodewords=function(e){this.codewords.append(e)},r.prototype.writeCodeword=function(e){this.codewords.append(e)},r.prototype.getCodewordCount=function(){return this.codewords.length()},r.prototype.getNewEncoding=function(){return this.newEncoding},r.prototype.signalEncoderChange=function(e){this.newEncoding=e},r.prototype.resetEncoderSignal=function(){this.newEncoding=-1},r.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},r.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},r.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},r.prototype.getSymbolInfo=function(){return this.symbolInfo},r.prototype.updateSymbolInfo=function(e){void 0===e&&(e=this.getCodewordCount()),(null==this.symbolInfo||e>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=gn.lookup(e,this.shape,this.minSize,this.maxSize,!0))},r.prototype.resetSymbolInfo=function(){this.symbolInfo=null},r}(),Ts=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ds=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Ts(e,r),e.prototype.getEncodingMode=function(){return 3},e.prototype.encode=function(t){for(var n=new F;t.hasMoreCharacters();){var i=t.getCurrentChar();if(t.pos++,this.encodeChar(i,n),n.length()%3==0&&(this.writeNextTriplet(t,n),k.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode())){t.signalEncoderChange(0);break}}this.handleEOD(t,n)},e.prototype.encodeChar=function(t,n){switch(t){case 13:n.append(0);break;case"*".charCodeAt(0):n.append(1);break;case">".charCodeAt(0):n.append(2);break;case" ".charCodeAt(0):n.append(3);break;default:t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)?n.append(t-48+4):t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)?n.append(t-65+14):k.illegalCharacter(U.getCharAt(t))}return 1},e.prototype.handleEOD=function(t,n){t.updateSymbolInfo();var i=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),a=n.length();t.pos-=a,(t.getRemainingCharacters()>1||i>1||t.getRemainingCharacters()!==i)&&t.writeCodeword(254),t.getNewEncoding()<0&&t.signalEncoderChange(0)},e}(lr),Rs=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),bs=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return Rs(e,r),e.prototype.getEncodingMode=function(){return 2},e.prototype.encodeChar=function(t,n){return t===" ".charCodeAt(0)?(n.append(3),1):t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)?(n.append(t-48+4),1):t>="a".charCodeAt(0)&&t<="z".charCodeAt(0)?(n.append(t-97+14),1):t<" ".charCodeAt(0)?(n.append(0),n.append(t),2):t<="/".charCodeAt(0)?(n.append(1),n.append(t-33),2):t<="@".charCodeAt(0)?(n.append(1),n.append(t-58+15),2):t>="[".charCodeAt(0)&&t<="_".charCodeAt(0)?(n.append(1),n.append(t-91+22),2):t==="`".charCodeAt(0)?(n.append(2),n.append(0),2):t<="Z".charCodeAt(0)?(n.append(2),n.append(t-65+1),2):t<=127?(n.append(2),n.append(t-123+27),2):(n.append("1\x1e"),2+this.encodeChar(t-128,n))},e}(lr),Ns=function(){function r(){}return r.randomize253State=function(e){var n=149*e%253+1+129;return n<=254?n:n-254},r.encodeHighLevel=function(e,t,n,i,a){void 0===t&&(t=0),void 0===n&&(n=null),void 0===i&&(i=null),void 0===a&&(a=!1);var o=new lr,s=[new _s,o,new bs,new Ds,new Cs,new As],u=new Os(e);u.setSymbolShape(t),u.setSizeConstraints(n,i),e.startsWith(Gt)&&e.endsWith("\x1e\x04")?(u.writeCodeword(236),u.setSkipAtEnd(2),u.pos+=Gt.length):e.startsWith(Wt)&&e.endsWith("\x1e\x04")&&(u.writeCodeword(237),u.setSkipAtEnd(2),u.pos+=Wt.length);var f=0;for(a&&(o.encodeMaximal(u),f=u.getNewEncoding(),u.resetEncoderSignal());u.hasMoreCharacters();)s[f].encode(u),u.getNewEncoding()>=0&&(f=u.getNewEncoding(),u.resetEncoderSignal());var c=u.getCodewordCount();u.updateSymbolInfo();var h=u.getSymbolInfo().getDataCapacity();c<h&&0!==f&&5!==f&&4!==f&&u.writeCodeword("\xfe");var d=u.getCodewords();for(d.length()<h&&d.append(129);d.length()<h;)d.append(this.randomize253State(d.length()+1));return u.getCodewords().toString()},r.lookAheadTest=function(e,t,n){var i=this.lookAheadTestIntern(e,t,n);if(3===n&&3===i){for(var a=Math.min(t+3,e.length),o=t;o<a;o++)if(!this.isNativeX12(e.charCodeAt(o)))return 0}else if(4===n&&4===i)for(a=Math.min(t+4,e.length),o=t;o<a;o++)if(!this.isNativeEDIFACT(e.charCodeAt(o)))return 0;return i},r.lookAheadTestIntern=function(e,t,n){if(t>=e.length)return n;var i;0===n?i=[0,1,1,1,1,1.25]:(i=[1,2,2,2,2,2.25])[n]=0;for(var a=0,o=new Uint8Array(6),s=[];;){if(t+a===e.length){oe.fill(o,0),oe.fill(s,0);var u=this.findMinimums(i,s,B.MAX_VALUE,o),f=this.getMinimumCount(o);if(s[0]===u)return 0;if(1===f){if(o[5]>0)return 5;if(o[4]>0)return 4;if(o[2]>0)return 2;if(o[3]>0)return 3}return 1}var c=e.charCodeAt(t+a);if(a++,this.isDigit(c)?i[0]+=.5:this.isExtendedASCII(c)?(i[0]=Math.ceil(i[0]),i[0]+=2):(i[0]=Math.ceil(i[0]),i[0]++),this.isNativeC40(c)?i[1]+=2/3:this.isExtendedASCII(c)?i[1]+=8/3:i[1]+=4/3,this.isNativeText(c)?i[2]+=2/3:this.isExtendedASCII(c)?i[2]+=8/3:i[2]+=4/3,this.isNativeX12(c)?i[3]+=2/3:this.isExtendedASCII(c)?i[3]+=13/3:i[3]+=10/3,this.isNativeEDIFACT(c)?i[4]+=3/4:this.isExtendedASCII(c)?i[4]+=4.25:i[4]+=3.25,this.isSpecialB256(c)?i[5]+=4:i[5]++,a>=4){if(oe.fill(o,0),oe.fill(s,0),this.findMinimums(i,s,B.MAX_VALUE,o),s[0]<this.min(s[5],s[1],s[2],s[3],s[4]))return 0;if(s[5]<s[0]||s[5]+1<this.min(s[1],s[2],s[3],s[4]))return 5;if(s[4]+1<this.min(s[5],s[1],s[2],s[3],s[0]))return 4;if(s[2]+1<this.min(s[5],s[1],s[4],s[3],s[0]))return 2;if(s[3]+1<this.min(s[5],s[1],s[4],s[2],s[0]))return 3;if(s[1]+1<this.min(s[0],s[5],s[4],s[2])){if(s[1]<s[3])return 1;if(s[1]===s[3]){for(var h=t+a+1;h<e.length;){var d=e.charCodeAt(h);if(this.isX12TermSep(d))return 3;if(!this.isNativeX12(d))break;h++}return 1}}}}},r.min=function(e,t,n,i,a){var o=Math.min(e,Math.min(t,Math.min(n,i)));return void 0===a?o:Math.min(o,a)},r.findMinimums=function(e,t,n,i){for(var a=0;a<6;a++){var o=t[a]=Math.ceil(e[a]);n>o&&(n=o,oe.fill(i,0)),n===o&&(i[a]=i[a]+1)}return n},r.getMinimumCount=function(e){for(var t=0,n=0;n<6;n++)t+=e[n];return t||0},r.isDigit=function(e){return e>="0".charCodeAt(0)&&e<="9".charCodeAt(0)},r.isExtendedASCII=function(e){return e>=128&&e<=255},r.isNativeC40=function(e){return e===" ".charCodeAt(0)||e>="0".charCodeAt(0)&&e<="9".charCodeAt(0)||e>="A".charCodeAt(0)&&e<="Z".charCodeAt(0)},r.isNativeText=function(e){return e===" ".charCodeAt(0)||e>="0".charCodeAt(0)&&e<="9".charCodeAt(0)||e>="a".charCodeAt(0)&&e<="z".charCodeAt(0)},r.isNativeX12=function(e){return this.isX12TermSep(e)||e===" ".charCodeAt(0)||e>="0".charCodeAt(0)&&e<="9".charCodeAt(0)||e>="A".charCodeAt(0)&&e<="Z".charCodeAt(0)},r.isX12TermSep=function(e){return 13===e||e==="*".charCodeAt(0)||e===">".charCodeAt(0)},r.isNativeEDIFACT=function(e){return e>=" ".charCodeAt(0)&&e<="^".charCodeAt(0)},r.isSpecialB256=function(e){return!1},r.determineConsecutiveDigitCount=function(e,t){void 0===t&&(t=0);for(var n=e.length,i=t;i<n&&this.isDigit(e.charCodeAt(i));)i++;return i-t},r.illegalCharacter=function(e){var t=B.toHexString(e.charCodeAt(0));throw t="0000".substring(0,4-t.length)+t,new Error("Illegal character: "+e+" (0x"+t+")")},r}();const k=Ns;var vr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},xn=function(){function r(e){this.charset=e,this.name=e.name}return r.prototype.canEncode=function(e){try{return null!=De.encode(e,this.charset)}catch{return!1}},r}(),Ps=function(){function r(e,t,n){var i,a,o,s,u,f;this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(M){return new xn(cr.forName(M))}),this.encoders=[];var c=[];c.push(new xn(hn.ISO_8859_1));for(var h=null!=t&&t.name.startsWith("UTF"),d=0;d<e.length;d++){var l=!1;try{for(var v=(i=void 0,vr(c)),p=v.next();!p.done;p=v.next()){var x=p.value,w=e.charAt(d);if(w.charCodeAt(0)===n||x.canEncode(w)){l=!0;break}}}catch(M){i={error:M}}finally{try{p&&!p.done&&(a=v.return)&&a.call(v)}finally{if(i)throw i.error}}if(!l)try{for(var _=(o=void 0,vr(this.ENCODERS)),C=_.next();!C.done;C=_.next())if((x=C.value).canEncode(e.charAt(d))){c.push(x),l=!0;break}}catch(M){o={error:M}}finally{try{C&&!C.done&&(s=_.return)&&s.call(_)}finally{if(o)throw o.error}}l||(h=!0)}if(1!==c.length||h){this.encoders=[];var m=0;try{for(var S=vr(c),I=S.next();!I.done;I=S.next())this.encoders[m++]=x=I.value}catch(M){u={error:M}}finally{try{I&&!I.done&&(f=S.return)&&f.call(S)}finally{if(u)throw u.error}}}else this.encoders=[c[0]];var D=-1;if(null!=t)for(d=0;d<this.encoders.length;d++)if(null!=this.encoders[d]&&t.name===this.encoders[d].name){D=d;break}this.priorityEncoderIndex=D}return r.prototype.length=function(){return this.encoders.length},r.prototype.getCharsetName=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].name},r.prototype.getCharset=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].charset},r.prototype.getECIValue=function(e){return this.encoders[e].charset.getValueIdentifier()},r.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},r.prototype.canEncode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return!0},r.prototype.encode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return De.encode(U.getCharAt(e),this.encoders[t].name)},r}(),Ms=3,Bs=function(){function r(e,t,n){this.fnc1=n;var i=new Ps(e,t,n);if(1===i.length())for(var a=0;a<this.bytes.length;a++){var o=e.charAt(a).charCodeAt(0);this.bytes[a]=o===n?1e3:o}else this.bytes=this.encodeMinimally(e,i,n)}return r.prototype.getFNC1Character=function(){return this.fnc1},r.prototype.length=function(){return this.bytes.length},r.prototype.haveNCharacters=function(e,t){if(e+t-1>=this.bytes.length)return!1;for(var n=0;n<t;n++)if(this.isECI(e+n))return!1;return!0},r.prototype.charAt=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(this.isECI(e))throw new Error("value at "+e+" is not a character but an ECI");return this.isFNC1(e)?this.fnc1:this.bytes[e]},r.prototype.subSequence=function(e,t){if(e<0||e>t||t>this.length())throw new Error(""+e);for(var n=new F,i=e;i<t;i++){if(this.isECI(i))throw new Error("value at "+i+" is not a character but an ECI");n.append(this.charAt(i))}return n.toString()},r.prototype.isECI=function(e){if(e<0||e>=this.length())throw new Error(""+e);return this.bytes[e]>255&&this.bytes[e]<=999},r.prototype.isFNC1=function(e){if(e<0||e>=this.length())throw new Error(""+e);return 1e3===this.bytes[e]},r.prototype.getECIValue=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(!this.isECI(e))throw new Error("value at "+e+" is not an ECI but a character");return this.bytes[e]-256},r.prototype.addEdge=function(e,t,n){(null==e[t][n.encoderIndex]||e[t][n.encoderIndex].cachedTotalSize>n.cachedTotalSize)&&(e[t][n.encoderIndex]=n)},r.prototype.addEdges=function(e,t,n,i,a,o){var s=e.charAt(i).charCodeAt(0),u=0,f=t.length();t.getPriorityEncoderIndex()>=0&&(s===o||t.canEncode(s,t.getPriorityEncoderIndex()))&&(f=(u=t.getPriorityEncoderIndex())+1);for(var c=u;c<f;c++)(s===o||t.canEncode(s,c))&&this.addEdge(n,i+1,new yn(s,t,c,a,o))},r.prototype.encodeMinimally=function(e,t,n){var i=e.length,a=new(yn[i+1][t.length()]);this.addEdges(e,t,a,0,null,n);for(var o=1;o<=i;o++){for(var s=0;s<t.length();s++)null!=a[o][s]&&o<i&&this.addEdges(e,t,a,o,a[o][s],n);for(s=0;s<t.length();s++)a[o-1][s]=null}var u=-1,f=B.MAX_VALUE;for(s=0;s<t.length();s++)if(null!=a[i][s]){var c=a[i][s];c.cachedTotalSize<f&&(f=c.cachedTotalSize,u=s)}if(u<0)throw new Error('Failed to encode "'+e+'"');for(var h=[],d=a[i][u];null!=d;){if(d.isFNC1())h.unshift(1e3);else{var l=t.encode(d.c,d.encoderIndex);for(o=l.length-1;o>=0;o--)h.unshift(255&l[o])}(null===d.previous?0:d.previous.encoderIndex)!==d.encoderIndex&&h.unshift(256+t.getECIValue(d.encoderIndex)),d=d.previous}var p=[];for(o=0;o<p.length;o++)p[o]=h[o];return p},r}(),yn=function(){function r(e,t,n,i,a){this.c=e,this.encoderSet=t,this.encoderIndex=n,this.previous=i,this.fnc1=a,this.c=e===a?1e3:e;var o=this.isFNC1()?1:t.encode(e,n).length;(null===i?0:i.encoderIndex)!==n&&(o+=Ms),null!=i&&(o+=i.cachedTotalSize),this.cachedTotalSize=o}return r.prototype.isFNC1=function(){return 1e3===this.c},r}(),Fs=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),wt=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ls=function(r,e){var t="function"==typeof Symbol&&r[Symbol.iterator];if(!t)return r;var i,o,n=t.call(r),a=[];try{for(;(void 0===e||e-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(o)throw o.error}}return a},T=(()=>(function(r){r[r.ASCII=0]="ASCII",r[r.C40=1]="C40",r[r.TEXT=2]="TEXT",r[r.X12=3]="X12",r[r.EDF=4]="EDF",r[r.B256=5]="B256"}(T||(T={})),T))(),Us=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],Oe=function(){function r(){}return r.isExtendedASCII=function(e,t){return e!==t&&e>=128&&e<=255},r.isInC40Shift1Set=function(e){return e<=31},r.isInC40Shift2Set=function(e,t){var n,i;try{for(var a=wt(Us),o=a.next();!o.done;o=a.next())if(o.value.charCodeAt(0)===e)return!0}catch(u){n={error:u}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return e===t},r.isInTextShift1Set=function(e){return this.isInC40Shift1Set(e)},r.isInTextShift2Set=function(e,t){return this.isInC40Shift2Set(e,t)},r.encodeHighLevel=function(e,t,n,i){void 0===t&&(t=null),void 0===n&&(n=-1),void 0===i&&(i=0);var a=0;return e.startsWith(Gt)&&e.endsWith("\x1e\x04")?(a=5,e=e.substring(Gt.length,e.length-2)):e.startsWith(Wt)&&e.endsWith("\x1e\x04")&&(a=6,e=e.substring(Wt.length,e.length-2)),decodeURIComponent(escape(String.fromCharCode.apply(String,function(){for(var r=[],e=0;e<arguments.length;e++)r=r.concat(Ls(arguments[e]));return r}(this.encode(e,t,n,i,a)))))},r.encode=function(e,t,n,i,a){return this.encodeMinimally(new Hs(e,t,n,i,a)).getBytes()},r.addEdge=function(e,t){var n=t.fromPosition+t.characterLength;(null===e[n][t.getEndMode()]||e[n][t.getEndMode()].cachedTotalSize>t.cachedTotalSize)&&(e[n][t.getEndMode()]=t)},r.getNumberOfC40Words=function(e,t,n,i){for(var a=0,o=t;o<e.length();o++){if(e.isECI(o))return i[0]=0,0;var s=e.charAt(o);if(n&&k.isNativeC40(s)||!n&&k.isNativeText(s))a++;else if(r.isExtendedASCII(s,e.getFNC1Character())){var u=255&s;u>=128&&(n&&k.isNativeC40(u-128)||!n&&k.isNativeText(u-128))?a+=3:a+=4}else a+=2;if(a%3==0||(a-2)%3==0&&o+1===e.length())return i[0]=o-t+1,Math.ceil(a/3)}return i[0]=0,0},r.addEdges=function(e,t,n,i){var a,o;if(e.isECI(n))this.addEdge(t,new Re(e,T.ASCII,n,1,i));else{var l,s=e.charAt(n);if(null===i||i.getEndMode()!==T.EDF){k.isDigit(s)&&e.haveNCharacters(n,2)&&k.isDigit(e.charAt(n+1))?this.addEdge(t,new Re(e,T.ASCII,n,2,i)):this.addEdge(t,new Re(e,T.ASCII,n,1,i));var u=[T.C40,T.TEXT];try{for(var f=wt(u),c=f.next();!c.done;c=f.next()){var h=c.value,d=[];r.getNumberOfC40Words(e,n,h===T.C40,d)>0&&this.addEdge(t,new Re(e,h,n,d[0],i))}}catch(p){a={error:p}}finally{try{c&&!c.done&&(o=f.return)&&o.call(f)}finally{if(a)throw a.error}}e.haveNCharacters(n,3)&&k.isNativeX12(e.charAt(n))&&k.isNativeX12(e.charAt(n+1))&&k.isNativeX12(e.charAt(n+2))&&this.addEdge(t,new Re(e,T.X12,n,3,i)),this.addEdge(t,new Re(e,T.B256,n,1,i))}for(l=0;l<3;l++){var v=n+l;if(!e.haveNCharacters(v,1)||!k.isNativeEDIFACT(e.charAt(v)))break;this.addEdge(t,new Re(e,T.EDF,n,l+1,i))}3===l&&e.haveNCharacters(n,4)&&k.isNativeEDIFACT(e.charAt(n+3))&&this.addEdge(t,new Re(e,T.EDF,n,4,i))}},r.encodeMinimally=function(e){var t=e.length(),n=Array(t+1).fill(null).map(function(){return Array(6).fill(0)});this.addEdges(e,n,0,null);for(var i=1;i<=t;i++){for(var a=0;a<6;a++)null!==n[i][a]&&i<t&&this.addEdges(e,n,i,n[i][a]);for(a=0;a<6;a++)n[i-1][a]=null}var o=-1,s=B.MAX_VALUE;for(a=0;a<6;a++)if(null!==n[t][a]){var u=n[t][a],f=a>=1&&a<=3?u.cachedTotalSize+1:u.cachedTotalSize;f<s&&(s=f,o=a)}if(o<0)throw new Error('Failed to encode "'+e+'"');return new Vs(n[t][o])},r}(),Vs=function(){function r(e){var t=e.input,n=0,i=[],a=[],o=[];(e.mode===T.C40||e.mode===T.TEXT||e.mode===T.X12)&&e.getEndMode()!==T.ASCII&&(n+=this.prepend(Re.getBytes(254),i));for(var s=e;null!==s;)n+=this.prepend(s.getDataBytes(),i),(null===s.previous||s.getPreviousStartMode()!==s.getMode())&&(s.getMode()===T.B256&&(n<=249?(i.unshift(n),n++):(i.unshift(n%250),i.unshift(n/250+249),n+=2),a.push(i.length),o.push(n)),this.prepend(s.getLatchBytes(),i),n=0),s=s.previous;5===t.getMacroId()?n+=this.prepend(Re.getBytes(236),i):6===t.getMacroId()&&(n+=this.prepend(Re.getBytes(237),i)),t.getFNC1Character()>0&&(n+=this.prepend(Re.getBytes(232),i));for(var u=0;u<a.length;u++)this.applyRandomPattern(i,i.length-a[u],o[u]);var f=e.getMinSymbolSize(i.length);for(i.length<f&&i.push(129);i.length<f;)i.push(this.randomize253State(i.length+1));for(this.bytes=new Uint8Array(i.length),u=0;u<this.bytes.length;u++)this.bytes[u]=i[u]}return r.prototype.prepend=function(e,t){for(var n=e.length-1;n>=0;n--)t.unshift(e[n]);return e.length},r.prototype.randomize253State=function(e){var n=149*e%253+1+129;return n<=254?n:n-254},r.prototype.applyRandomPattern=function(e,t,n){for(var i=0;i<n;i++){var a=t+i,u=149*(a+1)%255+1+(255&e[a]);e[a]=u<=255?u:u-256}},r.prototype.getBytes=function(){return this.bytes},r}(),Re=function(){function r(e,t,n,i,a){if(this.input=e,this.mode=t,this.fromPosition=n,this.characterLength=i,this.previous=a,this.allCodewordCapacities=[3,5,8,10,12,16,18,22,30,32,36,44,49,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.squareCodewordCapacities=[3,5,8,12,18,22,30,36,44,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.rectangularCodewordCapacities=[5,10,16,33,32,49],!(n+i<=e.length()))throw new Error("Invalid edge");var o=null!==a?a.cachedTotalSize:0,s=this.getPreviousMode();switch(t){case T.ASCII:o++,(e.isECI(n)||Oe.isExtendedASCII(e.charAt(n),e.getFNC1Character()))&&o++,(s===T.C40||s===T.TEXT||s===T.X12)&&o++;break;case T.B256:o++,(s!==T.B256||250===this.getB256Size())&&o++,s===T.ASCII?o++:(s===T.C40||s===T.TEXT||s===T.X12)&&(o+=2);break;case T.C40:case T.TEXT:case T.X12:o+=t===T.X12?2:2*Oe.getNumberOfC40Words(e,n,t===T.C40,[]),s===T.ASCII||s===T.B256?o++:s!==t&&(s===T.C40||s===T.TEXT||s===T.X12)&&(o+=2);break;case T.EDF:o+=3,s===T.ASCII||s===T.B256?o++:(s===T.C40||s===T.TEXT||s===T.X12)&&(o+=2)}this.cachedTotalSize=o}return r.prototype.getB256Size=function(){for(var e=0,t=this;null!==t&&t.mode===T.B256&&e<=250;)e++,t=t.previous;return e},r.prototype.getPreviousStartMode=function(){return null===this.previous?T.ASCII:this.previous.mode},r.prototype.getPreviousMode=function(){return null===this.previous?T.ASCII:this.previous.getEndMode()},r.prototype.getEndMode=function(){if(this.mode===T.EDF){if(this.characterLength<4)return T.ASCII;if((e=this.getLastASCII())>0&&this.getCodewordsRemaining(this.cachedTotalSize+e)<=2-e)return T.ASCII}if(this.mode===T.C40||this.mode===T.TEXT||this.mode===T.X12){if(this.fromPosition+this.characterLength>=this.input.length()&&0===this.getCodewordsRemaining(this.cachedTotalSize))return T.ASCII;var e;if(1===(e=this.getLastASCII())&&0===this.getCodewordsRemaining(this.cachedTotalSize+1))return T.ASCII}return this.mode},r.prototype.getMode=function(){return this.mode},r.prototype.getLastASCII=function(){var e=this.input.length(),t=this.fromPosition+this.characterLength;return e-t>4||t>=e?0:e-t==1?Oe.isExtendedASCII(this.input.charAt(t),this.input.getFNC1Character())?0:1:e-t==2?Oe.isExtendedASCII(this.input.charAt(t),this.input.getFNC1Character())||Oe.isExtendedASCII(this.input.charAt(t+1),this.input.getFNC1Character())?0:k.isDigit(this.input.charAt(t))&&k.isDigit(this.input.charAt(t+1))?1:2:e-t==3?k.isDigit(this.input.charAt(t))&&k.isDigit(this.input.charAt(t+1))&&!Oe.isExtendedASCII(this.input.charAt(t+2),this.input.getFNC1Character())||k.isDigit(this.input.charAt(t+1))&&k.isDigit(this.input.charAt(t+2))&&!Oe.isExtendedASCII(this.input.charAt(t),this.input.getFNC1Character())?2:0:k.isDigit(this.input.charAt(t))&&k.isDigit(this.input.charAt(t+1))&&k.isDigit(this.input.charAt(t+2))&&k.isDigit(this.input.charAt(t+3))?2:0},r.prototype.getMinSymbolSize=function(e){var t,n,i,a,o,s;switch(this.input.getShapeHint()){case 1:try{for(var u=wt(this.squareCodewordCapacities),f=u.next();!f.done;f=u.next())if((c=f.value)>=e)return c}catch(p){t={error:p}}finally{try{f&&!f.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}break;case 2:try{for(var h=wt(this.rectangularCodewordCapacities),d=h.next();!d.done;d=h.next())if((c=d.value)>=e)return c}catch(p){i={error:p}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}}try{for(var l=wt(this.allCodewordCapacities),v=l.next();!v.done;v=l.next()){var c;if((c=v.value)>=e)return c}}catch(p){o={error:p}}finally{try{v&&!v.done&&(s=l.return)&&s.call(l)}finally{if(o)throw o.error}}return this.allCodewordCapacities[this.allCodewordCapacities.length-1]},r.prototype.getCodewordsRemaining=function(e){return this.getMinSymbolSize(e)-e},r.getBytes=function(e,t){var n=new Uint8Array(t?2:1);return n[0]=e,t&&(n[1]=t),n},r.prototype.setC40Word=function(e,t,n,i,a){var o=1600*(255&n)+40*(255&i)+(255&a)+1;e[t]=o/256,e[t+1]=o%256},r.prototype.getX12Value=function(e){return 13===e?0:42===e?1:62===e?2:32===e?3:e>=48&&e<=57?e-44:e>=65&&e<=90?e-51:e},r.prototype.getX12Words=function(){if(this.characterLength%3!=0)throw new Error("X12 words must be a multiple of 3");for(var e=new Uint8Array(this.characterLength/3*2),t=0;t<e.length;t+=2)this.setC40Word(e,t,this.getX12Value(this.input.charAt(this.fromPosition+t/2*3)),this.getX12Value(this.input.charAt(this.fromPosition+t/2*3+1)),this.getX12Value(this.input.charAt(this.fromPosition+t/2*3+2)));return e},r.prototype.getShiftValue=function(e,t,n){return t&&Oe.isInC40Shift1Set(e)||!t&&Oe.isInTextShift1Set(e)?0:t&&Oe.isInC40Shift2Set(e,n)||!t&&Oe.isInTextShift2Set(e,n)?1:2},r.prototype.getC40Value=function(e,t,n,i){if(n===i){if(2!==t)throw new Error("FNC1 cannot be used in C40 shift 2");return 27}return e?n<=31?n:32===n?3:n<=47?n-33:n<=57?n-44:n<=64?n-43:n<=90?n-51:n<=95?n-69:n<=127?n-96:n:0===n?0:0===t&&n<=3?n-1:1===t&&n<=31?n:32===n?3:n>=33&&n<=47?n-33:n>=48&&n<=57?n-44:n>=58&&n<=64?n-43:n>=65&&n<=90?n-64:n>=91&&n<=95?n-69:96===n?0:n>=97&&n<=122?n-83:n>=123&&n<=127?n-96:n},r.prototype.getC40Words=function(e,t){for(var n=[],i=0;i<this.characterLength;i++){var a=this.input.charAt(this.fromPosition+i);if(e&&k.isNativeC40(a)||!e&&k.isNativeText(a))n.push(this.getC40Value(e,0,a,t));else if(Oe.isExtendedASCII(a,t)){var s=(255&a)-128;if(e&&k.isNativeC40(s)||!e&&k.isNativeText(s))n.push(1),n.push(30),n.push(this.getC40Value(e,0,s,t));else{n.push(1),n.push(30);var o=this.getShiftValue(s,e,t);n.push(o),n.push(this.getC40Value(e,o,s,t))}}else o=this.getShiftValue(a,e,t),n.push(o),n.push(this.getC40Value(e,o,a,t))}if(n.length%3!=0){if((n.length-2)%3!=0||this.fromPosition+this.characterLength!==this.input.length())throw new Error("C40 words must be a multiple of 3");n.push(0)}var u=new Uint8Array(n.length/3*2),f=0;for(i=0;i<n.length;i+=3)this.setC40Word(u,f,255&n[i],255&n[i+1],255&n[i+2]),f+=2;return u},r.prototype.getEDFBytes=function(){for(var e=Math.ceil(this.characterLength/4),t=new Uint8Array(3*e),n=this.fromPosition,i=Math.min(this.fromPosition+this.characterLength-1,this.input.length()-1),a=0;a<e;a+=3){for(var o=[],s=0;s<4;s++)o[s]=n<=i?63&this.input.charAt(n++):n===i+1?31:0;var u=o[0]<<18;u|=o[1]<<12,u|=o[2]<<6,t[a]=(u|=o[3])>>16&255,t[a+1]=u>>8&255,t[a+2]=255&u}return t},r.prototype.getLatchBytes=function(){switch(this.getPreviousMode()){case T.ASCII:case T.B256:switch(this.mode){case T.B256:return r.getBytes(231);case T.C40:return r.getBytes(230);case T.TEXT:return r.getBytes(239);case T.X12:return r.getBytes(238);case T.EDF:return r.getBytes(240)}break;case T.C40:case T.TEXT:case T.X12:if(this.mode!==this.getPreviousMode())switch(this.mode){case T.ASCII:return r.getBytes(254);case T.B256:return r.getBytes(254,231);case T.C40:return r.getBytes(254,230);case T.TEXT:return r.getBytes(254,239);case T.X12:return r.getBytes(254,238);case T.EDF:return r.getBytes(254,240)}break;case T.EDF:if(this.mode!==T.EDF)throw new Error("Cannot switch from EDF to "+this.mode)}return new Uint8Array(0)},r.prototype.getDataBytes=function(){switch(this.mode){case T.ASCII:return this.input.isECI(this.fromPosition)?r.getBytes(241,this.input.getECIValue(this.fromPosition)+1):Oe.isExtendedASCII(this.input.charAt(this.fromPosition),this.input.getFNC1Character())?r.getBytes(235,this.input.charAt(this.fromPosition)-127):2===this.characterLength?r.getBytes(10*this.input.charAt(this.fromPosition)+this.input.charAt(this.fromPosition+1)+130):this.input.isFNC1(this.fromPosition)?r.getBytes(232):r.getBytes(this.input.charAt(this.fromPosition)+1);case T.B256:return r.getBytes(this.input.charAt(this.fromPosition));case T.C40:return this.getC40Words(!0,this.input.getFNC1Character());case T.TEXT:return this.getC40Words(!1,this.input.getFNC1Character());case T.X12:return this.getX12Words();case T.EDF:return this.getEDFBytes()}},r}(),Hs=function(r){function e(t,n,i,a,o){var s=r.call(this,t,n,i)||this;return s.shape=a,s.macroId=o,s}return Fs(e,r),e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape},e}(Bs);!function(){function r(){}r.prototype.encode=function(e,t,n,i,a){if(void 0===a&&(a=null),""===e.trim())throw new Error("Found empty contents");if(t!==N.DATA_MATRIX)throw new Error("Can only encode DATA_MATRIX, but got "+t);if(n<0||i<0)throw new Error("Requested dimensions can't be negative: "+n+"x"+i);var d,o=0,s=null,u=null;if(null!=a){var f=a.get(L.DATA_MATRIX_SHAPE);null!=f&&(o=f);var c=a.get(L.MIN_SIZE);null!=c&&(s=c);var h=a.get(L.MAX_SIZE);null!=h&&(u=h)}if(null!=a&&a.has(L.DATA_MATRIX_COMPACT)&&Boolean(a.get(L.DATA_MATRIX_COMPACT).toString())){var v=a.has(L.GS1_FORMAT)&&Boolean(a.get(L.GS1_FORMAT).toString()),p=null;a.has(L.CHARACTER_SET)&&(p=cr.forName(a.get(L.CHARACTER_SET).toString())),d=Oe.encodeHighLevel(e,p,v?29:-1,o)}else{var w=null!=a&&a.has(L.FORCE_C40)&&Boolean(a.get(L.FORCE_C40).toString());d=k.encodeHighLevel(e,o,s,u,w)}var y=gn.lookup(d.length,o,s,u,!0),_=ws.encodeECC200(d,y),C=new os(_,y.getSymbolDataWidth(),y.getSymbolDataHeight());return C.place(),this.encodeLowLevel(C,y,n,i)},r.prototype.encodeLowLevel=function(e,t,n,i){for(var a=t.getSymbolDataWidth(),o=t.getSymbolDataHeight(),s=new un(t.getSymbolWidth(),t.getSymbolHeight()),u=0,f=0;f<o;f++){var c=void 0;if(f%t.matrixHeight==0){c=0;for(var h=0;h<t.getSymbolWidth();h++)s.setBoolean(c,u,h%2==0),c++;u++}for(c=0,h=0;h<a;h++)h%t.matrixWidth==0&&(s.setBoolean(c,u,!0),c++),s.setBoolean(c,u,e.getBit(h,f)),c++,h%t.matrixWidth==t.matrixWidth-1&&(s.setBoolean(c,u,f%2==0),c++);if(u++,f%t.matrixHeight==t.matrixHeight-1){for(c=0,h=0;h<t.getSymbolWidth();h++)s.setBoolean(c,u,!0),c++;u++}}return this.convertByteMatrixToBitMatrix(s,n,i)},r.prototype.convertByteMatrixToBitMatrix=function(e,t,n){var h,i=e.getWidth(),a=e.getHeight(),o=Math.max(t,i),s=Math.max(n,a),u=Math.min(o/i,s/a),f=(o-i*u)/2,c=(s-a*u)/2;n<a||t<i?(f=0,c=0,h=new me(i,a)):h=new me(t,n),h.clear();for(var d=0,l=c;d<a;d++,l+=u)for(var v=0,p=f;v<i;v++,p+=u)1===e.get(v,d)&&h.setRegion(p,l,u,u);return h}}();var Gs=function(){function r(){}return r.prototype.isCompact=function(){return this.compact},r.prototype.setCompact=function(e){this.compact=e},r.prototype.getSize=function(){return this.size},r.prototype.setSize=function(e){this.size=e},r.prototype.getLayers=function(){return this.layers},r.prototype.setLayers=function(e){this.layers=e},r.prototype.getCodeWords=function(){return this.codeWords},r.prototype.setCodeWords=function(e){this.codeWords=e},r.prototype.getMatrix=function(){return this.matrix},r.prototype.setMatrix=function(e){this.matrix=e},r}();const Ws=Gs;var Xs=function(){function r(){}return r.singletonList=function(e){return[e]},r.min=function(e,t){return e.sort(t)[0]},r}();const wn=Xs;var zs=function(){function r(e){this.previous=e}return r.prototype.getPrevious=function(){return this.previous},r}();const Zs=zs;var js=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ys=function(r){function e(t,n,i){var a=r.call(this,t)||this;return a.value=n,a.bitCount=i,a}return js(e,r),e.prototype.appendTo=function(t,n){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,n){return new e(this,t,n)},e.prototype.addBinaryShift=function(t,n){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,n)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return"<"+B.toBinaryString((t|=1<<this.bitCount)|1<<this.bitCount).substring(1)+">"},e}(Zs);const pr=Ys;var Ks=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])})(e,t)};return function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),qs=function(r){function e(t,n,i){var a=r.call(this,t,0,0)||this;return a.binaryShiftStart=n,a.binaryShiftByteCount=i,a}return Ks(e,r),e.prototype.appendTo=function(t,n){for(var i=0;i<this.binaryShiftByteCount;i++)(0===i||31===i&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):t.appendBits(0===i?Math.min(this.binaryShiftByteCount,31):this.binaryShiftByteCount-31,5)),t.appendBits(n[this.binaryShiftStart+i],8)},e.prototype.addBinaryShift=function(t,n){return new e(this,t,n)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e}(pr);const Qs=qs;function _t(r,e,t){return new pr(r,e,t)}var $s=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],eu=new pr(null,0,0),gr=[Int32Array.from([0,327708,327710,327709,656318]),Int32Array.from([590318,0,327710,327709,656318]),Int32Array.from([262158,590300,0,590301,932798]),Int32Array.from([327709,327708,656318,0,327710]),Int32Array.from([327711,656380,656382,656381,0])],An=function ru(r){var e,t;try{for(var n=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(r),i=n.next();!i.done;i=n.next())oe.fill(i.value,-1)}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return r[0][4]=0,r[1][4]=0,r[1][0]=28,r[3][4]=0,r[2][4]=0,r[2][0]=15,r}(oe.createInt32Array(6,6)),iu=function(){function r(e,t,n,i){this.token=e,this.mode=t,this.binaryShiftByteCount=n,this.bitCount=i}return r.prototype.getMode=function(){return this.mode},r.prototype.getToken=function(){return this.token},r.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},r.prototype.getBitCount=function(){return this.bitCount},r.prototype.latchAndAppend=function(e,t){var n=this.bitCount,i=this.token;if(e!==this.mode){var a=gr[this.mode][e];i=_t(i,65535&a,a>>16),n+=a>>16}var o=2===e?4:5;return new r(i=_t(i,t,o),e,0,n+o)},r.prototype.shiftAndAppend=function(e,t){var n=this.token,i=2===this.mode?4:5;return n=_t(n,An[this.mode][e],i),new r(n=_t(n,t,5),this.mode,0,this.bitCount+i+5)},r.prototype.addBinaryShiftChar=function(e){var t=this.token,n=this.mode,i=this.bitCount;if(4===this.mode||2===this.mode){var a=gr[n][0];t=_t(t,65535&a,a>>16),i+=a>>16,n=0}var s=new r(t,n,this.binaryShiftByteCount+1,i+(0===this.binaryShiftByteCount||31===this.binaryShiftByteCount?18:62===this.binaryShiftByteCount?9:8));return 2078===s.binaryShiftByteCount&&(s=s.endBinaryShift(e+1)),s},r.prototype.endBinaryShift=function(e){if(0===this.binaryShiftByteCount)return this;var t=this.token;return t=function Js(r,e,t){return new Qs(r,e,t)}(t,e-this.binaryShiftByteCount,this.binaryShiftByteCount),new r(t,this.mode,0,this.bitCount)},r.prototype.isBetterThanOrEqualTo=function(e){var t=this.bitCount+(gr[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?t+=r.calculateBinaryShiftCost(e)-r.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(t+=10),t<=e.bitCount},r.prototype.toBitArray=function(e){for(var t,n,i=[],a=this.endBinaryShift(e.length).token;null!==a;a=a.getPrevious())i.unshift(a);var o=new ve;try{for(var s=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(i),u=s.next();!u.done;u=s.next())u.value.appendTo(o,e)}catch(c){t={error:c}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r.prototype.toString=function(){return U.format("%s bits=%d bytes=%d",$s[this.mode],this.bitCount,this.binaryShiftByteCount)},r.calculateBinaryShiftCost=function(e){return e.binaryShiftByteCount>62?21:e.binaryShiftByteCount>31?20:e.binaryShiftByteCount>0?10:0},r.INITIAL_STATE=new r(eu,0,0,0),r}();const au=iu;var xr=function ou(r){var e=U.getCharCode(" "),t=U.getCharCode("."),n=U.getCharCode(",");r[0][e]=1;for(var i=U.getCharCode("Z"),a=U.getCharCode("A"),o=a;o<=i;o++)r[0][o]=o-a+2;r[1][e]=1;var s=U.getCharCode("z"),u=U.getCharCode("a");for(o=u;o<=s;o++)r[1][o]=o-u+2;r[2][e]=1;var f=U.getCharCode("9"),c=U.getCharCode("0");for(o=c;o<=f;o++)r[2][o]=o-c+2;r[2][n]=12,r[2][t]=13;for(var h=["\0"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","\t","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~","\x7f"],d=0;d<h.length;d++)r[3][U.getCharCode(h[d])]=d;var l=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"];for(d=0;d<l.length;d++)U.getCharCode(l[d])>0&&(r[4][U.getCharCode(l[d])]=d);return r}(oe.createInt32Array(5,256)),Zt=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},su=function(){function r(e){this.text=e}return r.prototype.encode=function(){for(var e=U.getCharCode(" "),t=U.getCharCode("\n"),n=wn.singletonList(au.INITIAL_STATE),i=0;i<this.text.length;i++){var a=void 0,o=i+1<this.text.length?this.text[i+1]:0;switch(this.text[i]){case U.getCharCode("\r"):a=o===t?2:0;break;case U.getCharCode("."):a=o===e?3:0;break;case U.getCharCode(","):a=o===e?4:0;break;case U.getCharCode(":"):a=o===e?5:0;break;default:a=0}a>0?(n=r.updateStateListForPair(n,i,a),i++):n=this.updateStateListForChar(n,i)}return wn.min(n,function(u,f){return u.getBitCount()-f.getBitCount()}).toBitArray(this.text)},r.prototype.updateStateListForChar=function(e,t){var n,i,a=[];try{for(var o=Zt(e),s=o.next();!s.done;s=o.next())this.updateStateForChar(s.value,t,a)}catch(f){n={error:f}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}return r.simplifyStates(a)},r.prototype.updateStateForChar=function(e,t,n){for(var i=255&this.text[t],a=xr[e.getMode()][i]>0,o=null,s=0;s<=4;s++){var u=xr[s][i];if(u>0){if(null==o&&(o=e.endBinaryShift(t)),!a||s===e.getMode()||2===s){var f=o.latchAndAppend(s,u);n.push(f)}if(!a&&An[e.getMode()][s]>=0){var c=o.shiftAndAppend(s,u);n.push(c)}}}if(e.getBinaryShiftByteCount()>0||0===xr[e.getMode()][i]){var h=e.addBinaryShiftChar(t);n.push(h)}},r.updateStateListForPair=function(e,t,n){var i,a,o=[];try{for(var s=Zt(e),u=s.next();!u.done;u=s.next())this.updateStateForPair(u.value,t,n,o)}catch(c){i={error:c}}finally{try{u&&!u.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}return this.simplifyStates(o)},r.updateStateForPair=function(e,t,n,i){var a=e.endBinaryShift(t);if(i.push(a.latchAndAppend(4,n)),4!==e.getMode()&&i.push(a.shiftAndAppend(4,n)),3===n||4===n){var o=a.latchAndAppend(2,16-n).latchAndAppend(2,1);i.push(o)}if(e.getBinaryShiftByteCount()>0){var s=e.addBinaryShiftChar(t).addBinaryShiftChar(t+1);i.push(s)}},r.simplifyStates=function(e){var t,n,i,a,o=[];try{for(var s=Zt(e),u=s.next();!u.done;u=s.next()){var f=u.value,c=!0,h=function(x){if(x.isBetterThanOrEqualTo(f))return c=!1,"break";f.isBetterThanOrEqualTo(x)&&(o=o.filter(function(w){return w!==x}))};try{for(var d=(i=void 0,Zt(o)),l=d.next();!l.done&&"break"!==h(l.value);l=d.next());}catch(x){i={error:x}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}c&&o.push(f)}}catch(x){t={error:x}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r}();const uu=su;var cu=function(){function r(){}return r.encodeBytes=function(e){return r.encode(e,r.DEFAULT_EC_PERCENT,r.DEFAULT_AZTEC_LAYERS)},r.encode=function(e,t,n){var s,u,f,c,h,i=new uu(e).encode(),a=B.truncDivision(i.getSize()*t,100)+11,o=i.getSize()+a;if(n!==r.DEFAULT_AZTEC_LAYERS){if(s=n<0,(u=Math.abs(n))>(s?r.MAX_NB_BITS_COMPACT:r.MAX_NB_BITS))throw new R(U.format("Illegal value %s for layers",n));var d=(f=r.totalBitsInLayer(u,s))-f%(c=r.WORD_SIZE[u]);if((h=r.stuffBits(i,c)).getSize()+a>d)throw new R("Data to large for user specified layer");if(s&&h.getSize()>64*c)throw new R("Data to large for user specified layer")}else{c=0,h=null;for(var l=0;;l++){if(l>r.MAX_NB_BITS)throw new R("Data too large for an Aztec code");if(!(o>(f=r.totalBitsInLayer(u=(s=l<=3)?l+1:l,s)))&&((null==h||c!==r.WORD_SIZE[u])&&(h=r.stuffBits(i,c=r.WORD_SIZE[u])),d=f-f%c,!(s&&h.getSize()>64*c)&&h.getSize()+a<=d))break}}var _,v=r.generateCheckWords(h,f,c),p=h.getSize()/c,x=r.generateModeMessage(s,u,p),w=(s?11:14)+4*u,y=new Int32Array(w);if(s)for(_=w,l=0;l<y.length;l++)y[l]=l;else{_=w+1+2*B.truncDivision(B.truncDivision(w,2)-1,15);var C=B.truncDivision(w,2),m=B.truncDivision(_,2);for(l=0;l<C;l++){var S=l+B.truncDivision(l,15);y[C-l-1]=m-S-1,y[C+l]=m+S+1}}for(var I=new me(_),D=(l=0,0);l<u;l++){for(var M=4*(u-l)+(s?9:12),P=0;P<M;P++)for(var ie=2*P,H=0;H<2;H++)v.get(D+ie+H)&&I.set(y[2*l+H],y[2*l+P]),v.get(D+2*M+ie+H)&&I.set(y[2*l+P],y[w-1-2*l-H]),v.get(D+4*M+ie+H)&&I.set(y[w-1-2*l-H],y[w-1-2*l-P]),v.get(D+6*M+ie+H)&&I.set(y[w-1-2*l-P],y[2*l+H]);D+=8*M}if(r.drawModeMessage(I,s,_,x),s)r.drawBullsEye(I,B.truncDivision(_,2),5);else for(r.drawBullsEye(I,B.truncDivision(_,2),7),l=0,P=0;l<B.truncDivision(w,2)-1;l+=15,P+=16)for(H=1&B.truncDivision(_,2);H<_;H+=2)I.set(B.truncDivision(_,2)-P,H),I.set(B.truncDivision(_,2)+P,H),I.set(H,B.truncDivision(_,2)-P),I.set(H,B.truncDivision(_,2)+P);var te=new Ws;return te.setCompact(s),te.setSize(_),te.setLayers(u),te.setCodeWords(p),te.setMatrix(I),te},r.drawBullsEye=function(e,t,n){for(var i=0;i<n;i+=2)for(var a=t-i;a<=t+i;a++)e.set(a,t-i),e.set(a,t+i),e.set(t-i,a),e.set(t+i,a);e.set(t-n,t-n),e.set(t-n+1,t-n),e.set(t-n,t-n+1),e.set(t+n,t-n),e.set(t+n,t-n+1),e.set(t+n,t+n-1)},r.generateModeMessage=function(e,t,n){var i=new ve;return e?(i.appendBits(t-1,2),i.appendBits(n-1,6),i=r.generateCheckWords(i,28,4)):(i.appendBits(t-1,5),i.appendBits(n-1,11),i=r.generateCheckWords(i,40,4)),i},r.drawModeMessage=function(e,t,n,i){var a=B.truncDivision(n,2);if(t)for(var o=0;o<7;o++){var s=a-3+o;i.get(o)&&e.set(s,a-5),i.get(o+7)&&e.set(a+5,s),i.get(20-o)&&e.set(s,a+5),i.get(27-o)&&e.set(a-5,s)}else for(o=0;o<10;o++)s=a-5+o+B.truncDivision(o,5),i.get(o)&&e.set(s,a-7),i.get(o+10)&&e.set(a+7,s),i.get(29-o)&&e.set(s,a+7),i.get(39-o)&&e.set(a-7,s)},r.generateCheckWords=function(e,t,n){var i,a,o=e.getSize()/n,s=new sn(r.getGF(n)),u=B.truncDivision(t,n),f=r.bitsToWords(e,n,u);s.encode(f,u-o);var c=t%n,h=new ve;h.appendBits(0,c);try{for(var d=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(Array.from(f)),l=d.next();!l.done;l=d.next())h.appendBits(l.value,n)}catch(p){i={error:p}}finally{try{l&&!l.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}return h},r.bitsToWords=function(e,t,n){var a,o,i=new Int32Array(n);for(a=0,o=e.getSize()/t;a<o;a++){for(var s=0,u=0;u<t;u++)s|=e.get(a*t+u)?1<<t-u-1:0;i[a]=s}return i},r.getGF=function(e){switch(e){case 4:return Se.AZTEC_PARAM;case 6:return Se.AZTEC_DATA_6;case 8:return Se.AZTEC_DATA_8;case 10:return Se.AZTEC_DATA_10;case 12:return Se.AZTEC_DATA_12;default:throw new R("Unsupported word size "+e)}},r.stuffBits=function(e,t){for(var n=new ve,i=e.getSize(),a=(1<<t)-2,o=0;o<i;o+=t){for(var s=0,u=0;u<t;u++)(o+u>=i||e.get(o+u))&&(s|=1<<t-1-u);(s&a)===a?(n.appendBits(s&a,t),o--):s&a?n.appendBits(s,t):(n.appendBits(1|s,t),o--)}return n},r.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.DEFAULT_EC_PERCENT=33,r.DEFAULT_AZTEC_LAYERS=0,r.MAX_NB_BITS=32,r.MAX_NB_BITS_COMPACT=4,r.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]),r}();const yr=cu;!function(){function r(){}r.prototype.encode=function(e,t,n,i){return this.encodeWithHints(e,t,n,i,null)},r.prototype.encodeWithHints=function(e,t,n,i,a){var o=hn.ISO_8859_1,s=yr.DEFAULT_EC_PERCENT,u=yr.DEFAULT_AZTEC_LAYERS;return null!=a&&(a.has(L.CHARACTER_SET)&&(o=cr.forName(a.get(L.CHARACTER_SET).toString())),a.has(L.ERROR_CORRECTION)&&(s=B.parseInt(a.get(L.ERROR_CORRECTION).toString())),a.has(L.AZTEC_LAYERS)&&(u=B.parseInt(a.get(L.AZTEC_LAYERS).toString()))),r.encodeLayers(e,t,n,i,o,s,u)},r.encodeLayers=function(e,t,n,i,a,o,s){if(t!==N.AZTEC)throw new R("Can only encode AZTEC, but got "+t);var u=yr.encode(U.getBytes(e,a),o,s);return r.renderResult(u,n,i)},r.renderResult=function(e,t,n){var i=e.getMatrix();if(null==i)throw new Be;for(var a=i.getWidth(),o=i.getHeight(),s=Math.max(t,a),u=Math.max(n,o),f=Math.min(s/a,u/o),c=(s-a*f)/2,h=(u-o*f)/2,d=new me(s,u),l=0,v=h;l<o;l++,v+=f)for(var p=0,x=c;p<a;p++,x+=f)i.get(p,l)&&d.setRegion(x,v,f,f);return d}}();var hu=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),du=function(r){function e(t){var n=r.call(this,t.width,t.height)||this;return n.canvas=t,n.tempCanvasElement=null,n.buffer=e.makeBufferFromCanvasImageData(t),n}return hu(e,r),e.makeBufferFromCanvasImageData=function(t){var n;try{n=t.getContext("2d",{willReadFrequently:!0})}catch{n=t.getContext("2d")}if(!n)throw new Error("Couldn't get canvas context.");var i=n.getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(i.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,n,i){for(var a=new Uint8ClampedArray(n*i),o=0,s=0,u=t.length;o<u;o+=4,s++){a[s]=0===t[o+3]?255:306*t[o]+601*t[o+1]+117*t[o+2]+512>>10}return a},e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new R("Requested row is outside the image: "+t);var i=this.getWidth(),a=t*i;return null===n?n=this.buffer.slice(a,a+i):(n.length<i&&(n=new Uint8ClampedArray(i)),n.set(this.buffer.slice(a,a+i))),n},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return r.prototype.crop.call(this,t,n,i,a),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.invert=function(){return new mt(this)},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement();if(!n)throw new Error("Could not create a Canvas element.");var i=t*e.DEGREE_TO_RADIANS,a=this.canvas.width,o=this.canvas.height,s=Math.ceil(Math.abs(Math.cos(i))*a+Math.abs(Math.sin(i))*o),u=Math.ceil(Math.abs(Math.sin(i))*a+Math.abs(Math.cos(i))*o);n.width=s,n.height=u;var f=n.getContext("2d");if(!f)throw new Error("Could not create a Canvas Context element.");return f.translate(s/2,u/2),f.rotate(i),f.drawImage(this.canvas,a/-2,o/-2),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.DEGREE_TO_RADIANS=Math.PI/180,e}(lt);function Cn(){return typeof navigator<"u"}var ut=function(){return ut=Object.assign||function(r){for(var e,t=1,n=arguments.length;t<n;t++)for(var i in e=arguments[t])Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i]);return r},ut.apply(this,arguments)},$=function(r,e,t,n){return new(t||(t=Promise))(function(a,o){function s(c){try{f(n.next(c))}catch(h){o(h)}}function u(c){try{f(n.throw(c))}catch(h){o(h)}}function f(c){c.done?a(c.value):function i(a){return a instanceof t?a:new t(function(o){o(a)})}(c.value).then(s,u)}f((n=n.apply(r,e||[])).next())})},ee=function(r,e){var n,i,a,o,t={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(f){return function(c){return function u(f){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=2&f[0]?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[2&f[0],a.value]),f[0]){case 0:case 1:a=f;break;case 4:return t.label++,{value:f[1],done:!1};case 5:t.label++,i=f[1],f=[0];continue;case 7:f=t.ops.pop(),t.trys.pop();continue;default:if(!(a=(a=t.trys).length>0&&a[a.length-1])&&(6===f[0]||2===f[0])){t=0;continue}if(3===f[0]&&(!a||f[1]>a[0]&&f[1]<a[3])){t.label=f[1];break}if(6===f[0]&&t.label<a[1]){t.label=a[1],a=f;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(f);break}a[2]&&t.ops.pop(),t.trys.pop();continue}f=e.call(r,t)}catch(c){f=[6,c],i=0}finally{n=a=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,c])}}},wr=function(r){var e="function"==typeof Symbol&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},pu={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},ft=function(){function r(e,t,n){void 0===t&&(t=new Map),void 0===n&&(n={}),this.reader=e,this.hints=t,this.options=ut(ut({},pu),n)}return Object.defineProperty(r.prototype,"possibleFormats",{set:function(e){this.hints.set(K.POSSIBLE_FORMATS,e)},enumerable:!1,configurable:!0}),r.addVideoSource=function(e,t){try{e.srcObject=t}catch{console.error("got interrupted by new loading request")}},r.mediaStreamSetTorch=function(e,t){return $(this,void 0,void 0,function(){return ee(this,function(n){switch(n.label){case 0:return[4,e.applyConstraints({advanced:[{fillLightMode:t?"flash":"off",torch:!!t}]})];case 1:return n.sent(),[2]}})})},r.mediaStreamIsTorchCompatible=function(e){var t,n,i=e.getVideoTracks();try{for(var a=wr(i),o=a.next();!o.done;o=a.next())if(r.mediaStreamIsTorchCompatibleTrack(o.value))return!0}catch(u){t={error:u}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return!1},r.mediaStreamIsTorchCompatibleTrack=function(e){try{return"torch"in e.getCapabilities()}catch(n){return console.error(n),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},r.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&e.readyState>2},r.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new ne("element with id '".concat(e,"' not found"));if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new ne("element with id '".concat(e,"' must be an ").concat(t," element"));return n},r.createVideoElement=function(e){if(e instanceof HTMLVideoElement)return e;if("string"==typeof e)return r.getMediaElement(e,"video");if(!e&&typeof document<"u"){var t=document.createElement("video");return t.width=200,t.height=200,t}throw new Error("Couldn't get videoElement from videoSource!")},r.prepareImageElement=function(e){if(e instanceof HTMLImageElement)return e;if("string"==typeof e)return r.getMediaElement(e,"img");if(typeof e>"u"){var t=document.createElement("img");return t.width=200,t.height=200,t}throw new Error("Couldn't get imageElement from imageSource!")},r.prepareVideoElement=function(e){var t=r.createVideoElement(e);return t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t},r.isImageLoaded=function(e){return!(!e.complete||0===e.naturalWidth)},r.createBinaryBitmapFromCanvas=function(e){var t=new du(e),n=new Sr(t);return new Er(n)},r.drawImageOnCanvas=function(e,t){e.drawImage(t,0,0)},r.getMediaElementDimensions=function(e){if(e instanceof HTMLVideoElement)return{height:e.videoHeight,width:e.videoWidth};if(e instanceof HTMLImageElement)return{height:e.naturalHeight||e.height,width:e.naturalWidth||e.width};throw new Error("Couldn't find the Source's dimensions!")},r.createCaptureCanvas=function(e){if(!e)throw new ne("Cannot create a capture canvas without a media element.");if(typeof document>"u")throw new Error('The page "Document" is undefined, make sure you\'re running in a browser.');var t=document.createElement("canvas"),n=r.getMediaElementDimensions(e),i=n.width,a=n.height;return t.style.width=i+"px",t.style.height=a+"px",t.width=i,t.height=a,t},r.tryPlayVideo=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(n){switch(n.label){case 0:if(e?.ended)return console.error("Trying to play video that has ended."),[2,!1];if(r.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2,!0];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,e.play()];case 2:return n.sent(),[2,!0];case 3:return t=n.sent(),console.warn("It was not possible to play the video.",t),[2,!1];case 4:return[2]}})})},r.createCanvasFromMediaElement=function(e){var t=r.createCaptureCanvas(e),n=t.getContext("2d");if(!n)throw new Error("Couldn't find Canvas 2D Context.");return r.drawImageOnCanvas(n,e),t},r.createBinaryBitmapFromMediaElem=function(e){var t=r.createCanvasFromMediaElement(e);return r.createBinaryBitmapFromCanvas(t)},r.destroyImageElement=function(e){e.src="",e.removeAttribute("src"),e=void 0},r.listVideoInputDevices=function(){return $(this,void 0,void 0,function(){var e,t,n,i,a,o,s,u,h,d;return ee(this,function(l){switch(l.label){case 0:if(!Cn())throw new Error("Can't enumerate devices, navigator is not present.");if(!function vu(){return!(!function lu(){return Cn()&&!!navigator.mediaDevices}()||!navigator.mediaDevices.enumerateDevices)}())throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=l.sent(),t=[];try{for(n=wr(e),i=n.next();!i.done;i=n.next())"videoinput"===(o="video"===(a=i.value).kind?"videoinput":a.kind)&&(s=a.deviceId||a.id,u=a.label||"Video device ".concat(t.length+1),t.push({deviceId:s,label:u,kind:o,groupId:a.groupId}))}catch(v){h={error:v}}finally{try{i&&!i.done&&(d=n.return)&&d.call(n)}finally{if(h)throw h.error}}return[2,t]}})})},r.findDeviceById=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(n){switch(n.label){case 0:return[4,r.listVideoInputDevices()];case 1:return(t=n.sent())?[2,t.find(function(i){return i.deviceId===e})]:[2]}})})},r.cleanVideoSource=function(e){if(e){try{e.srcObject=null}catch{e.src=""}e&&e.removeAttribute("src")}},r.releaseAllStreams=function(){0!==r.streamTracker.length&&r.streamTracker.forEach(function(e){e.getTracks().forEach(function(t){return t.stop()})}),r.streamTracker=[]},r.playVideoOnLoadAsync=function(e,t){return $(this,void 0,void 0,function(){return ee(this,function(i){switch(i.label){case 0:return[4,r.tryPlayVideo(e)];case 1:return i.sent()?[2,!0]:[2,new Promise(function(a,o){var s=setTimeout(function(){r.isVideoPlaying(e)||(o(!1),e.removeEventListener("canplay",u))},t),u=function(){r.tryPlayVideo(e).then(function(f){clearTimeout(s),e.removeEventListener("canplay",u),a(f)})};e.addEventListener("canplay",u)})]}})})},r.attachStreamToVideo=function(e,t,n){return void 0===n&&(n=5e3),$(this,void 0,void 0,function(){var i;return ee(this,function(a){switch(a.label){case 0:return i=r.prepareVideoElement(t),r.addVideoSource(i,e),[4,r.playVideoOnLoadAsync(i,n)];case 1:return a.sent(),[2,i]}})})},r._waitImageLoad=function(e){return new Promise(function(t,n){var a=setTimeout(function(){r.isImageLoaded(e)||(e.removeEventListener("load",o),n())},1e4),o=function(){clearTimeout(a),e.removeEventListener("load",o),t()};e.addEventListener("load",o)})},r.checkCallbackFnOrThrow=function(e){if(!e)throw new ne("`callbackFn` is a required parameter, you cannot capture results without it.")},r.disposeMediaStream=function(e){e.getVideoTracks().forEach(function(t){return t.stop()}),e=void 0},r.prototype.decode=function(e){var t=r.createCanvasFromMediaElement(e);return this.decodeFromCanvas(t)},r.prototype.decodeBitmap=function(e){return this.reader.decode(e,this.hints)},r.prototype.decodeFromCanvas=function(e){var t=r.createBinaryBitmapFromCanvas(e);return this.decodeBitmap(t)},r.prototype.decodeFromImageElement=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(n){switch(n.label){case 0:if(!e)throw new ne("An image element must be provided.");return t=r.prepareImageElement(e),[4,this._decodeOnLoadImage(t)];case 1:return[2,n.sent()]}})})},r.prototype.decodeFromImageUrl=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(n){switch(n.label){case 0:if(!e)throw new ne("An URL must be provided.");(t=r.prepareImageElement()).src=e,n.label=1;case 1:return n.trys.push([1,,3,4]),[4,this.decodeFromImageElement(t)];case 2:return[2,n.sent()];case 3:return r.destroyImageElement(t),[7];case 4:return[2]}})})},r.prototype.decodeFromConstraints=function(e,t,n){return $(this,void 0,void 0,function(){var i,a;return ee(this,function(o){switch(o.label){case 0:return r.checkCallbackFnOrThrow(n),[4,this.getUserMedia(e)];case 1:i=o.sent(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this.decodeFromStream(i,t,n)];case 3:return[2,o.sent()];case 4:throw a=o.sent(),r.disposeMediaStream(i),a;case 5:return[2]}})})},r.prototype.decodeFromStream=function(e,t,n){return $(this,void 0,void 0,function(){var a,s,u,f,h,d,l=this;return ee(this,function(v){switch(v.label){case 0:return r.checkCallbackFnOrThrow(n),[4,r.attachStreamToVideo(e,t,this.options.tryPlayVideoTimeout)];case 1:return a=v.sent(),s=this.scan(a,n,function(){r.disposeMediaStream(e),r.cleanVideoSource(a)}),u=e.getVideoTracks(),f=ut(ut({},s),{stop:function(){s.stop()},streamVideoConstraintsApply:function(p,x){return $(this,void 0,void 0,function(){var w,y,_,m,S,I;return ee(this,function(D){switch(D.label){case 0:w=x?u.filter(x):u,D.label=1;case 1:D.trys.push([1,6,7,8]),y=wr(w),_=y.next(),D.label=2;case 2:return _.done?[3,5]:[4,_.value.applyConstraints(p)];case 3:D.sent(),D.label=4;case 4:return _=y.next(),[3,2];case 5:return[3,8];case 6:return m=D.sent(),S={error:m},[3,8];case 7:try{_&&!_.done&&(I=y.return)&&I.call(y)}finally{if(S)throw S.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(p){return u.find(p).getConstraints()},streamVideoSettingsGet:function(p){return u.find(p).getSettings()},streamVideoCapabilitiesGet:function(p){return u.find(p).getCapabilities()}}),r.mediaStreamIsTorchCompatible(e)&&(h=u?.find(function(p){return r.mediaStreamIsTorchCompatibleTrack(p)}),f.switchTorch=d=function(p){return $(l,void 0,void 0,function(){return ee(this,function(x){switch(x.label){case 0:return[4,r.mediaStreamSetTorch(h,p)];case 1:return x.sent(),[2]}})})},f.stop=function(){return $(l,void 0,void 0,function(){return ee(this,function(p){switch(p.label){case 0:return s.stop(),[4,d(!1)];case 1:return p.sent(),[2]}})})}),[2,f]}})})},r.prototype.decodeFromVideoDevice=function(e,t,n){return $(this,void 0,void 0,function(){return ee(this,function(o){switch(o.label){case 0:return r.checkCallbackFnOrThrow(n),[4,this.decodeFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t,n)];case 1:return[2,o.sent()]}})})},r.prototype.decodeFromVideoElement=function(e,t){return $(this,void 0,void 0,function(){var n;return ee(this,function(a){switch(a.label){case 0:if(r.checkCallbackFnOrThrow(t),!e)throw new ne("A video element must be provided.");return n=r.prepareVideoElement(e),[4,r.playVideoOnLoadAsync(n,this.options.tryPlayVideoTimeout)];case 1:return a.sent(),[2,this.scan(n,t)]}})})},r.prototype.decodeFromVideoUrl=function(e,t){return $(this,void 0,void 0,function(){var n,i;return ee(this,function(s){switch(s.label){case 0:if(r.checkCallbackFnOrThrow(t),!e)throw new ne("An URL must be provided.");return(n=r.prepareVideoElement()).src=e,i=function(){r.cleanVideoSource(n)},[4,r.playVideoOnLoadAsync(n,this.options.tryPlayVideoTimeout)];case 1:return s.sent(),[2,this.scan(n,t,i)]}})})},r.prototype.decodeOnceFromConstraints=function(e,t){return $(this,void 0,void 0,function(){var n;return ee(this,function(i){switch(i.label){case 0:return[4,this.getUserMedia(e)];case 1:return n=i.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromStream=function(e,t){return $(this,void 0,void 0,function(){var n,i;return ee(this,function(o){switch(o.label){case 0:return n=Boolean(t),[4,r.attachStreamToVideo(e,t)];case 1:i=o.sent(),o.label=2;case 2:return o.trys.push([2,,4,5]),[4,this.scanOneResult(i)];case 3:return[2,o.sent()];case 4:return n||r.cleanVideoSource(i),[7];case 5:return[2]}})})},r.prototype.decodeOnceFromVideoDevice=function(e,t){return $(this,void 0,void 0,function(){return ee(this,function(a){switch(a.label){case 0:return[4,this.decodeOnceFromConstraints({video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},t)];case 1:return[2,a.sent()]}})})},r.prototype.decodeOnceFromVideoElement=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(i){switch(i.label){case 0:if(!e)throw new ne("A video element must be provided.");return t=r.prepareVideoElement(e),[4,r.playVideoOnLoadAsync(t,this.options.tryPlayVideoTimeout)];case 1:return i.sent(),[4,this.scanOneResult(t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromVideoUrl=function(e){return $(this,void 0,void 0,function(){var t,n;return ee(this,function(i){switch(i.label){case 0:if(!e)throw new ne("An URL must be provided.");(t=r.prepareVideoElement()).src=e,n=this.decodeOnceFromVideoElement(t),i.label=1;case 1:return i.trys.push([1,,3,4]),[4,n];case 2:return[2,i.sent()];case 3:return r.cleanVideoSource(t),[7];case 4:return[2]}})})},r.prototype.scanOneResult=function(e,t,n,i){var a=this;return void 0===t&&(t=!0),void 0===n&&(n=!0),void 0===i&&(i=!0),new Promise(function(o,s){a.scan(e,function(u,f,c){if(u)return o(u),void c.stop();if(f){if(f instanceof E&&t||f instanceof ae&&n||f instanceof O&&i)return;c.stop(),s(f)}})})},r.prototype.scan=function(e,t,n){var i=this;r.checkCallbackFnOrThrow(t);var o,a=r.createCaptureCanvas(e);try{o=a.getContext("2d",{willReadFrequently:!0})}catch{o=a.getContext("2d")}if(!o)throw new Error("Couldn't create canvas for visual element scan.");var f,s=function(){o=void 0,a=void 0},u=!1,h={stop:function(){u=!0,clearTimeout(f),s(),n&&n()}},d=function(){if(!u)try{r.drawImageOnCanvas(o,e);var l=i.decodeFromCanvas(a);t(l,void 0,h),f=setTimeout(d,i.options.delayBetweenScanSuccess)}catch(w){if(t(void 0,w,h),w instanceof ae||w instanceof O||w instanceof E)return void(f=setTimeout(d,i.options.delayBetweenScanAttempts));s(),n&&n(w)}};return d(),h},r.prototype._decodeOnLoadImage=function(e){return $(this,void 0,void 0,function(){return ee(this,function(n){switch(n.label){case 0:return r.isImageLoaded(e)?[3,2]:[4,r._waitImageLoad(e)];case 1:n.sent(),n.label=2;case 2:return[2,this.decode(e)]}})})},r.prototype.getUserMedia=function(e){return $(this,void 0,void 0,function(){var t;return ee(this,function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return t=n.sent(),r.streamTracker.push(t),[2,t]}})})},r.streamTracker=[],r}(),gu=function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}(),xu=(function(r){gu(function e(t,n){return r.call(this,new Rt,t,n)||this},r)}(ft),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),yu=(function(r){xu(function e(t,n){return r.call(this,new st(t),t,n)||this},r)}(ft),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),wu=(function(r){yu(function e(t,n){return r.call(this,new Bt,t,n)||this},r)}(ft),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),_u=(function(r){function e(t,n){var i=this,a=new on;return a.setHints(t),(i=r.call(this,a,t,n)||this).reader=a,i}wu(e,r),Object.defineProperty(e.prototype,"possibleFormats",{set:function(t){this.hints.set(K.POSSIBLE_FORMATS,t),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e.prototype.setHints=function(t){this.hints=t,this.reader.setHints(this.hints)}}(ft),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),Au=(function(r){_u(function e(t,n){return r.call(this,new kt,t,n)||this},r)}(ft),function(){var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])})(e,t)};return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}}()),Cu=function(r){function e(t,n){return r.call(this,new Ft,t,n)||this}return Au(e,r),e}(ft),ke="http://www.w3.org/2000/svg",En=(function(){function r(e){if("string"==typeof e){var t=document.getElementById(e);if(!t)throw new Error("Could not find a Container element with '".concat(e,"'."));this.containerElement=t}else this.containerElement=e}r.prototype.write=function(e,t,n,i){if(0===e.length)throw new R("Found empty contents");if(t<0||n<0)throw new R("Requested dimensions are too small: "+t+"x"+n);var a=i&&void 0!==i.get(L.MARGIN)?Number.parseInt(i.get(L.MARGIN).toString(),10):r.QUIET_ZONE_SIZE,o=this.encode(i,e);return this.renderResult(o,t,n,a)},r.prototype.createSVGElement=function(e,t){var n=document.createElementNS(r.SVG_NS,"svg");return n.setAttributeNS(ke,"width",t.toString()),n.setAttributeNS(ke,"height",e.toString()),n},r.prototype.createSvgPathPlaceholderElement=function(e,t){var n=document.createElementNS(r.SVG_NS,"path");return n.setAttributeNS(ke,"d","M0 0h".concat(e,"v").concat(t,"H0z")),n.setAttributeNS(ke,"fill","none"),n},r.prototype.createSvgRectElement=function(e,t,n,i){var a=document.createElementNS(r.SVG_NS,"rect");return a.setAttributeNS(ke,"x",e.toString()),a.setAttributeNS(ke,"y",t.toString()),a.setAttributeNS(ke,"height",n.toString()),a.setAttributeNS(ke,"width",i.toString()),a.setAttributeNS(ke,"fill","#000000"),a},r.prototype.encode=function(e,t){var n=Le.L;if(e&&void 0!==e.get(L.ERROR_CORRECTION)){var i=e.get(L.ERROR_CORRECTION).toString();n=Le.fromString(i)}return Vt.encode(t,n,e)},r.prototype.renderResult=function(e,t,n,i){var a=e.getMatrix();if(null===a)throw new Be;var o=a.getWidth(),s=a.getHeight(),u=o+2*i,f=s+2*i,c=Math.max(t,u),h=Math.max(n,f),d=Math.min(Math.floor(c/u),Math.floor(h/f)),l=Math.floor((c-o*d)/2),v=Math.floor((h-s*d)/2),p=this.createSVGElement(c,h),x=this.createSvgPathPlaceholderElement(t,n);p.appendChild(x),this.containerElement.appendChild(p);for(var w=0,y=v;w<s;w++,y+=d)for(var _=0,C=l;_<o;_++,C+=d)if(1===a.get(_,w)){var m=this.createSvgRectElement(C,y,d,d);p.appendChild(m)}return p},r.QUIET_ZONE_SIZE=4,r.SVG_NS="http://www.w3.org/2000/svg"}(),"http://www.w3.org/2000/svg"),mn=(function(){function r(){}r.prototype.write=function(e,t,n,i){if(0===e.length)throw new R("Found empty contents");if(t<0||n<0)throw new R("Requested dimensions are too small: "+t+"x"+n);var a=Le.L,o=r.QUIET_ZONE_SIZE;if(i){if(void 0!==i.get(L.ERROR_CORRECTION)){var s=i.get(L.ERROR_CORRECTION).toString();a=Le.fromString(s)}void 0!==i.get(L.MARGIN)&&(o=Number.parseInt(i.get(L.MARGIN).toString(),10))}var u=Vt.encode(e,a,i);return this.renderResult(u,t,n,o)},r.prototype.writeToDom=function(e,t,n,i,a){if("string"==typeof e){var o=document.querySelector(e);if(!o)throw new Error("Could no find the target HTML element.");e=o}var s=this.write(t,n,i,a);e instanceof HTMLElement&&e.appendChild(s)},r.prototype.renderResult=function(e,t,n,i){var a=e.getMatrix();if(null===a)throw new Be;for(var o=a.getWidth(),s=a.getHeight(),u=o+2*i,f=s+2*i,c=Math.max(t,u),h=Math.max(n,f),d=Math.min(Math.floor(c/u),Math.floor(h/f)),l=Math.floor((c-o*d)/2),v=Math.floor((h-s*d)/2),p=this.createSVGElement(c,h),x=0,w=v;x<s;x++,w+=d)for(var y=0,_=l;y<o;y++,_+=d)if(1===a.get(y,x)){var C=this.createSvgRectElement(_,w,d,d);p.appendChild(C)}return p},r.prototype.createSVGElement=function(e,t){var n=document.createElementNS(En,"svg"),i=e.toString(),a=t.toString();return n.setAttribute("height",a),n.setAttribute("width",i),n.setAttribute("viewBox","0 0 "+i+" "+a),n},r.prototype.createSvgRectElement=function(e,t,n,i){var a=document.createElementNS(En,"rect");return a.setAttribute("x",e.toString()),a.setAttribute("y",t.toString()),a.setAttribute("height",n.toString()),a.setAttribute("width",i.toString()),a.setAttribute("fill","#000000"),a},r.QUIET_ZONE_SIZE=4}(),ze(34528));let Eu=(()=>{class r extends jt.WebPlugin{constructor(){super(...arguments),this.formats=[],this.controls=null,this.torchState=!1,this.video=null,this.options=null,this.backgroundColor=null,this.facingMode=r.BACK}prepare(){var t=this;return(0,ue.Z)(function*(){yield t.getVideoElement()})()}hideBackground(){return this.backgroundColor=document.documentElement.style.backgroundColor,document.documentElement.style.backgroundColor="transparent",Promise.resolve()}showBackground(){return document.documentElement.style.backgroundColor=this.backgroundColor||"",Promise.resolve()}startScan(t){var n=this;return(0,ue.Z)(function*(){var i;if(n.options=t,n.formats=[],null===(i=t?.targetedFormats)||void 0===i||i.forEach(o=>{Object.keys(N).indexOf(o)>=0?n.formats.push(0):console.error(o,"is not supported on web")}),t?.cameraDirection&&(n.facingMode=t.cameraDirection===mn.G.BACK?r.BACK:r.FORWARD),yield n.getVideoElement())return n.getFirstResultFromReader();throw n.unavailable("Missing video element")})()}startScanning(t,n){throw this.unimplemented("Not implemented on web.")}pauseScanning(){return this.controls&&(this.controls.stop(),this.controls=null),Promise.resolve()}resumeScanning(){var t=this;return(0,ue.Z)(function*(){t.getFirstResultFromReader()})()}stopScan(t){var n=this;return(0,ue.Z)(function*(){yield n.stop(),n.controls&&(n.controls.stop(),n.controls=null)})()}checkPermission(t){var n=this;return(0,ue.Z)(function*(){if(typeof navigator>"u"||!navigator.permissions)throw n.unavailable("Permissions API not available in this browser");try{return{granted:"granted"===(yield window.navigator.permissions.query({name:"camera"})).state}}catch{throw n.unavailable("Camera permissions are not available in this browser")}})()}openAppSettings(){var t=this;return(0,ue.Z)(function*(){throw t.unavailable("App settings are not available in this browser")})()}disableTorch(){var t=this;return(0,ue.Z)(function*(){t.controls&&t.controls.switchTorch&&(t.controls.switchTorch(!1),t.torchState=!1)})()}enableTorch(){var t=this;return(0,ue.Z)(function*(){t.controls&&t.controls.switchTorch&&(t.controls.switchTorch(!0),t.torchState=!0)})()}toggleTorch(){var t=this;return(0,ue.Z)(function*(){t.controls&&t.controls.switchTorch&&t.controls.switchTorch(!0)})()}getTorchState(){var t=this;return(0,ue.Z)(function*(){return{isEnabled:t.torchState}})()}getVideoElement(){var t=this;return(0,ue.Z)(function*(){return t.video||(yield t.startVideo()),t.video})()}getFirstResultFromReader(){var t=this;return(0,ue.Z)(function*(){const n=yield t.getVideoElement();return new Promise(function(){var i=(0,ue.Z)(function*(a){if(n){let o;t.formats.length&&(o=new Map,o.set(K.POSSIBLE_FORMATS,t.formats));const s=new Cu(o);t.controls=yield s.decodeFromVideoElement(n,(u,f,c)=>{!f&&u&&u.getText()&&(a({hasContent:!0,content:u.getText(),format:u.getBarcodeFormat().toString()}),c.stop(),t.controls=null,t.stop()),f&&f.message&&console.error(f.message)})}});return function(a){return i.apply(this,arguments)}}())})()}startVideo(){var t=this;return(0,ue.Z)(function*(){return new Promise(function(){var n=(0,ue.Z)(function*(i,a){var o;yield navigator.mediaDevices.getUserMedia({audio:!1,video:!0}).then(f=>{f.getTracks().forEach(c=>c.stop())}).catch(f=>{a(f)});const s=document.body;if(document.getElementById("video"))a({message:"camera already started"});else{const f=document.createElement("div");f.setAttribute("style","position:absolute; top: 0; left: 0; width:100%; height: 100%; background-color: black;"),t.video=document.createElement("video"),t.video.id="video",(null===(o=t.options)||void 0===o?void 0:o.cameraDirection)!==mn.G.BACK?t.video.setAttribute("style","-webkit-transform: scaleX(-1); transform: scaleX(-1); width:100%; height: 100%;"):t.video.setAttribute("style","width:100%; height: 100%;");const c=navigator.userAgent.toLowerCase();c.includes("safari")&&!c.includes("chrome")&&(t.video.setAttribute("autoplay","true"),t.video.setAttribute("muted","true"),t.video.setAttribute("playsinline","true")),f.appendChild(t.video),s.appendChild(f),navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&navigator.mediaDevices.getUserMedia({video:t.facingMode}).then(l=>{t.video&&(t.video.srcObject=l,t.video.play()),i({})},l=>{a(l)})}});return function(i,a){return n.apply(this,arguments)}}())})()}stop(){var t=this;return(0,ue.Z)(function*(){var n;if(t.video){t.video.pause();const s=t.video.srcObject.getTracks();for(var i=0;i<s.length;i++)s[i].stop();null===(n=t.video.parentElement)||void 0===n||n.remove(),t.video=null}})()}}return r.FORWARD={facingMode:"user"},r.BACK={facingMode:"environment"},r})()},15861:(Sn,At,ze)=>{function ue(ct,et,ht,tt,rt,dt,Ze){try{var je=ct[dt](Ze),re=je.value}catch(Yt){return void ht(Yt)}je.done?et(re):Promise.resolve(re).then(tt,rt)}function jt(ct){return function(){var et=this,ht=arguments;return new Promise(function(tt,rt){var dt=ct.apply(et,ht);function Ze(re){ue(dt,tt,rt,Ze,je,"next",re)}function je(re){ue(dt,tt,rt,Ze,je,"throw",re)}Ze(void 0)})}}ze.d(At,{Z:()=>jt})}}]);