(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8477],{9736:(T,A,r)=>{r.d(A,{Hu:()=>P,wG:()=>x,rQ:()=>B,_G:()=>L});var v=r(15861),p=r(87956),C=r(53113),i=r(98699);class h{constructor(s,e,n,c,d){this.id=s,this.nit=e,this.name=n,this.city=c,this.exampleUrl=d}}class O{constructor(s,e,n,c){this.number=s,this.amount=e,this.expirationDate=n,this.companyId=c}get expirationFormat(){return this.expirationDate.dateFormat}}class S{constructor(s,e,n){this.agreement=s,this.invoice=e,this.source=n}}function m(t){return new S(t.agreement,t.invoice,t.source)}function b(t){return new h(t.orgIdNum,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var f=r(71776),y=r(39904),N=r(87903),I=r(42168),E=r(84757),o=r(99877);let M=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,I.firstValueFrom)(this.http.get(y.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,E.map)(({content:n})=>n.map(c=>b(c)))))}requestCompanyId(e){return(0,I.firstValueFrom)(this.http.get(y.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,E.map)(({content:n})=>n.length?b(n[0]):null)))}requestInvoice(e,n){return(0,I.firstValueFrom)(this.http.get(y.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,E.map)(c=>function a(t){return new O(t.nie||t.invoiceNum,+t.amt,new C.ou(t.expDt),t.orgIdNum)}(c))))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(f.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),l=(()=>{class t{constructor(e){this.http=e}send(e){return(0,I.firstValueFrom)(this.http.post(y.bV.PAYMENTS.INVOICE_MANUAL,function j(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,E.map)(([n])=>(0,N.l1)(n,"SUCCESS")))).catch(n=>(0,N.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(f.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var u=r(20691);let g=(()=>{class t extends u.Store{constructor(e){super({confirmation:!1}),e.subscribes(y.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(p.Yd))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),P=(()=>{class t{constructor(e,n,c){this.repository=e,this.store=n,this.eventBusService=c}setAgreement(e){try{return i.Either.success(this.store.setAgreement(e))}catch({message:n}){return i.Either.failure({message:n})}}setInvoice(e){try{return i.Either.success(this.store.setInvoice(e))}catch({message:n}){return i.Either.failure({message:n})}}setSource(e){try{return i.Either.success(this.store.setSource(e))}catch({message:n}){return i.Either.failure({message:n})}}reset(){try{return i.Either.success(this.store.reset())}catch({message:e}){return i.Either.failure({message:e})}}send(){var e=this;return(0,v.Z)(function*(){const n=m(e.store.currentState),c=yield e.execute(n);return e.eventBusService.emit(c.channel),i.Either.success({invoice:n,status:c})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(C.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(l),o.\u0275\u0275inject(g),o.\u0275\u0275inject(p.Yd))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),x=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,v.Z)(function*(){try{return i.Either.success(yield n.repository.requestAll(e))}catch({message:c,status:d}){return 400===d?i.Either.success([]):i.Either.failure({message:c})}})()}invoice(e,{id:n}){var c=this;return(0,v.Z)(function*(){try{return i.Either.success(yield c.repository.requestInvoice(e,n))}catch({message:d,status:F}){return i.Either.failure({value:400===F,message:d})}})()}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(M))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),B=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return i.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return i.Either.failure({message:e})}}source(){var e=this;return(0,v.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),c=e.store.getAgreement(),d=e.store.getInvoice();return i.Either.success({agreement:c,invoice:d,products:n})}catch({message:n}){return i.Either.failure({message:n})}})()}confirmation(){try{const e=m(this.store.currentState);return i.Either.success({payment:e})}catch({message:e}){return i.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(p.hM),o.\u0275\u0275inject(g))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const V=/^415(\d+)8020(\d+)$/;let U=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,v.Z)(function*(){const c=e.replace(/\D/g,"").match(V);if(!c)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const d=yield n.requestNuraCodes(),F=c[1],R=d.find(({ean_code:Y})=>Y===F);if(!R)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:c[2].slice(0,+R.length),companyId:R.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,I.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,E.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(f.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),L=(()=>{class t{constructor(e,n,c){this.repository=e,this.store=n,this.barcodeService=c}execute(e){var n=this;return(0,v.Z)(function*(){try{const{reference:c,companyId:d}=yield n.barcodeService.execute(e),F=yield n.repository.requestCompanyId(d),R=yield n.repository.requestInvoice(c,d);return n.store.setAgreement(F),n.store.setInvoice(R),i.Either.success()}catch{return i.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(M),o.\u0275\u0275inject(g),o.\u0275\u0275inject(U))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},74242:(T,A,r)=>{r.d(A,{s:()=>S});var v=r(39904),p=r(95437),C=r(30263),i=r(9736),h=r(99877);let S=(()=>{class m{constructor(a,j,f){this.modalConfirmation=a,this.mboProvider=j,this.managerInvoice=f}execute(a=!0){a?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(v.Z6.PAYMENTS.HOME)}}return m.\u0275fac=function(a){return new(a||m)(h.\u0275\u0275inject(C.$e),h.\u0275\u0275inject(p.ZL),h.\u0275\u0275inject(i.Hu))},m.\u0275prov=h.\u0275\u0275defineInjectable({token:m,factory:m.\u0275fac,providedIn:"root"}),m})()},88477:(T,A,r)=>{r.r(A),r.d(A,{MboPaymentInvoiceManualSourcePageModule:()=>E});var v=r(17007),p=r(78007),C=r(79798),i=r(30263),h=r(15861),O=r(39904),S=r(95437),m=r(9736),b=r(74242),a=r(99877),j=r(6661),f=r(4663),y=r(48774);const N=O.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL;let I=(()=>{class o{constructor(l,u,g,P){this.mboProvider=l,this.requestConfiguration=u,this.managerInvoice=g,this.cancelProvider=P,this.confirmation=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_payment-invoice-manual-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(N.REFERENCE)}},this.cancelAction={id:"btn_payment-invoice-manual-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(l){this.managerInvoice.setSource(l).when({success:()=>{this.mboProvider.navigation.next(N.CONFIRMATION)}})}initializatedConfiguration(){var l=this;return(0,h.Z)(function*(){(yield l.requestConfiguration.source()).when({success:({agreement:u,invoice:g,products:P})=>{l.agreement=u,l.invoice=g,l.products=P}},()=>{l.requesting=!1})})()}}return o.\u0275fac=function(l){return new(l||o)(a.\u0275\u0275directiveInject(S.ZL),a.\u0275\u0275directiveInject(m.rQ),a.\u0275\u0275directiveInject(m.Hu),a.\u0275\u0275directiveInject(b.s))},o.\u0275cmp=a.\u0275\u0275defineComponent({type:o,selectors:[["mbo-payment-invoice-manual-source-page"]],decls:7,vars:9,consts:[[1,"mbo-payment-invoice-manual-source-page__content"],[1,"mbo-payment-invoice-manual-source-page__header"],["title","Origen","progress","80%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-manual-source-page__body"],[3,"header","title","number","amount","hidden"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","select"]],template:function(l,u){1&l&&(a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275element(2,"bocc-header-form",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",3),a.\u0275\u0275element(4,"bocc-card-service",4),a.\u0275\u0275elementStart(5,"mbo-product-source-selector",5),a.\u0275\u0275listener("select",function(P){return u.onProduct(P)}),a.\u0275\u0275text(6," \xbfDesde d\xf3nde deseas pagar? "),a.\u0275\u0275elementEnd()()()),2&l&&(a.\u0275\u0275advance(2),a.\u0275\u0275property("leftAction",u.backAction)("rightAction",u.cancelAction),a.\u0275\u0275advance(2),a.\u0275\u0275propertyInterpolate1("header","Vence ",null==u.invoice?null:u.invoice.expirationFormat,""),a.\u0275\u0275property("title",null==u.agreement?null:u.agreement.name)("number",null==u.invoice?null:u.invoice.number)("amount",null==u.invoice?null:u.invoice.amount)("hidden",u.requesting),a.\u0275\u0275advance(1),a.\u0275\u0275property("skeleton",u.requesting)("products",u.products))},dependencies:[j.S,f.c,y.J],styles:["/*!\n * MBO PaymentInvoiceManualSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-invoice-manual-source-page .mbo-payment-invoice-manual-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x8)}mbo-payment-invoice-manual-source-page .mbo-payment-invoice-manual-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-invoice-manual-source-page .mbo-payment-invoice-manual-source-page__body .bocc-card-service__content{background:var(--color-carbon-lighter-200);border:none;box-shadow:none}\n"],encapsulation:2}),o})(),E=(()=>{class o{}return o.\u0275fac=function(l){return new(l||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[v.CommonModule,p.RouterModule.forChild([{path:"",component:I}]),i.S8,C.cV,i.Jx]}),o})()}}]);