#!/usr/bin/env python3
"""
analiza_public_js.py  –  Escaneo de bundles JS con salida JSON

Uso:
    python analiza_public_js.py /ruta/a/_extracted/Payload/App.app/public
    # Si omites la ruta, toma el directorio actual.

Salida:
    reporte_vuln.json  →  {
        "<archivo>.js": {
            "codes":  [...],
            "names":  [...],
            "phones": [...],
            "tokens": [...]
        },
        ...
    }
"""

import json, re, sys, os, pathlib
try:
    from tqdm import tqdm                        # barra de progreso
except ImportError:
    tqdm = lambda x, **k: x                      # fallback silencioso

# ---------------------------------------------------------------------------
# 1. Carpeta de entrada
root = pathlib.Path(sys.argv[1]) if len(sys.argv) > 1 else pathlib.Path.cwd()
if not root.is_dir():
    sys.exit(f"ERROR: {root} no es un directorio válido")

# ---------------------------------------------------------------------------
# 2. Patrones de búsqueda
regexen = {
    "codes" : re.compile(r'"code"\s*:\s*"([^"]+)"'),
    # nombre exacto del campo "name":  evita falsos positivos como "filename"
    "names" : re.compile(r'"name"\s*:\s*"([^"]+)"'),
    "phones": re.compile(r'\+57\d{10}'),
    "tokens": re.compile(r'[A-Za-z0-9+/]{20,}')
}
solo_digitos = re.compile(r'^[0-9+/]{20,}$')     # descartar cadenas numéricas

# ---------------------------------------------------------------------------
# 3. Recorrer todos los .js
resultado = {}                                   # { archivo: {cat: [items]} }
js_files = sorted(root.rglob("*.js"), key=os.path.getsize)
for js_path in tqdm(js_files, desc="Analizando .js"):
    try:
        text = js_path.read_text(encoding="utf-8", errors="ignore")
    except Exception as e:
        print(f"[!] No pude leer {js_path}: {e}")
        continue

    for categoria, patron in regexen.items():
        hallados = []
        for m in patron.finditer(text):
            valor = m.group(1) if m.groups() else m.group(0)
            if categoria == "tokens" and solo_digitos.match(valor):
                continue
            hallados.append(valor)

        if hallados:
            file_dict = resultado.setdefault(js_path.name, {"codes": [],
                                                            "names": [],
                                                            "phones": [],
                                                            "tokens": []})
            file_dict[categoria].extend(hallados)

# ---------------------------------------------------------------------------
# 4. Deduplicar y ordenar cada lista
for info in resultado.values():
    for cat in info:
        info[cat] = sorted(set(info[cat]))

# 5. Guardar JSON
with open("reporte_vuln.json", "w", encoding="utf-8") as fp:
    json.dump(resultado, fp, ensure_ascii=False, indent=2)

print("\n✅  Listo: revisa 'reporte_vuln.json'")
