(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9319],{99319:(E,l,a)=>{a.r(l),a.d(l,{MboTransfiyaFavoriteAccountPage:()=>y});var d=a(15861),i=a(17007),r=a(30263),u=a(79798),f=a(39904),v=a(95437),g=a(17698),_=a(65715),e=a(99877);function p(n,c){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-transfiya-account-selector",9),e.\u0275\u0275listener("click",function(){const s=e.\u0275\u0275restoreView(t).$implicit,h=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(h.onAccount(s))}),e.\u0275\u0275elementEnd()}if(2&n){const t=c.$implicit,o=e.\u0275\u0275nextContext();e.\u0275\u0275property("product",t)("checked",o.itIsAccountChecked(t))}}function b(n,c){1&n&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",10),e.\u0275\u0275text(1," No tienes ninguna cuenta "),e.\u0275\u0275elementEnd())}let y=(()=>{class n{constructor(t,o,m,s){this.modalConfirmation=t,this.mboProvider=o,this.modalService=m,this.managerDefaultAccount=s,this.accounts=[],this.backAction={id:"btn_transfiya-favorite-account_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(f.Z6.TRANSFERS.CELTOCEL.HOME)}}}ngOnInit(){setTimeout(()=>{this.initializedConfiguration()},120)}itIsAccountChecked(t){return"DDA"===t.type}onAccount(t){this.itIsAccountChecked(t)?this.uncheckAccount(t):this.checkAccount(t)}initializedConfiguration(){var t=this;return(0,d.Z)(function*(){t.mboProvider.loader.open("Solicitando productos, por favor espere..."),(yield t.managerDefaultAccount.configuration()).when({success:({accounts:o})=>{t.accounts=o}},()=>{t.mboProvider.loader.close()})})()}checkAccount(t){this.modalService.create(_.tM,{containerProps:{autoclose:!1},componentProps:{account:t}}).open()}uncheckAccount(t){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/transfiya-account-unchecked.svg",title:"Desmarcar cuenta favorita",message:"Si decides deseleccionar la cuenta, deber\xe1s autorizar cada vez que te env\xeden dinero por Transfiya.",accept:{label:"Cancelar"},decline:{label:"Desmarcar",icon:"favorite-minus",theme:"danger"}})}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(r.$e),e.\u0275\u0275directiveInject(v.ZL),e.\u0275\u0275directiveInject(r.iM),e.\u0275\u0275directiveInject(g._y))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-transfiya-favorite-account-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:11,vars:4,consts:[[1,"mbo-transfiya-favorite-account-page__content"],[1,"mbo-transfiya-favorite-account-page__header"],["title","Cuenta favorita",3,"leftAction"],[1,"mbo-transfiya-favorite-account-page__body"],[1,"mbo-transfiya-favorite-account-page__title","subtitle2-medium"],["icon","bell",3,"visible"],[1,"mbo-transfiya-favorite-account-page__list"],[3,"product","checked","click",4,"ngFor","ngForOf"],["logo","assets/shared/logos/modals/transfiya-account-none.svg",4,"ngIf"],[3,"product","checked","click"],["logo","assets/shared/logos/modals/transfiya-account-none.svg"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," Selecciona la cuenta d\xf3nde deseas recibir el dinero de Transfiya "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-alert",5),e.\u0275\u0275text(7," Puedes cambiar esta cuenta siempre que quieras. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",6),e.\u0275\u0275template(9,p,1,2,"mbo-transfiya-account-selector",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,b,2,0,"mbo-message-empty",8),e.\u0275\u0275elementEnd()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",o.backAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("visible",o.accounts.length),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",o.accounts),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.accounts.length))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,r.Jx,r.B4,u.Aj,_.$g],styles:["/*!\n * MBO TransfiyaFavoriteAccount Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 09/Sep/2024\n * Updated: 09/Sep/2024\n*/mbo-transfiya-favorite-account-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfiya-favorite-account-page .mbo-transfiya-favorite-account-page__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x4);box-sizing:border-box;margin-bottom:var(--sizing-x12)}mbo-transfiya-favorite-account-page .mbo-transfiya-favorite-account-page__title{color:var(--color-carbon-darker-1000);text-align:center}mbo-transfiya-favorite-account-page .mbo-transfiya-favorite-account-page__list{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),n})()}}]);