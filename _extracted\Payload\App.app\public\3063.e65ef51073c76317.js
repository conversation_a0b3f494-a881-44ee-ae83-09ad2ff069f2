(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3063],{33063:(E,t,o)=>{o.r(t),o.d(t,{MboQuickWithdrawalModule:()=>M});var a=o(17007),d=o(78007),l=o(99877);const h=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>o.e(6200).then(o.bind(o,46200)).then(n=>n.MboQuickWithdrawalSourcePageModule)},{path:"beneficiary",loadChildren:()=>o.e(5395).then(o.bind(o,85395)).then(n=>n.MboQuickWithdrawalBeneficiaryPageModule)},{path:"amount",loadChildren:()=>o.e(1775).then(o.bind(o,51775)).then(n=>n.MboQuickWithdrawalAmountPageModule)},{path:"confirmation",loadChildren:()=>o.e(2043).then(o.bind(o,12043)).then(n=>n.MboQuickWithdrawalConfirmationPageModule)},{path:"result",loadChildren:()=>o.e(9484).then(o.bind(o,29484)).then(n=>n.MboQuickWithdrawalResultPageModule)}];let M=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=l.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=l.\u0275\u0275defineInjector({imports:[a.CommonModule,d.RouterModule.forChild(h)]}),n})()}}]);