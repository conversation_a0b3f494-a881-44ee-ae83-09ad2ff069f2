(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2745],{52745:(n,a,s)=>{s.r(a),s.d(a,{OneSpanSecureStoragePluginWeb:()=>i});var o=s(17737);class i extends o.WebPlugin{constructor(){super(...arguments),this.storageName="",this.data=[]}isProtectedBySecureHardware(){return Promise.resolve({protected:!1})}connect(e){this.storageName=e.storageName;const r=sessionStorage.getItem(this.storageName);return r&&(this.data=JSON.parse(r)),Promise.resolve({created:!0})}getAll(){const e={};return this.data.forEach(({key:r,value:t})=>{e[r]=t}),Promise.resolve({data:e})}getString({forKey:e}){const r=this.data.find(({key:t})=>t===e);if(!r)throw Error("Failed to get a string from Secure Storage, Storage does not contains requested key");return Promise.resolve({value:r.value})}putString(e){const r=this.data.findIndex(({key:t})=>t===e.forKey);return r<0?this.data.push({key:e.forKey,value:e.forValue}):this.data[r].value=e.forValue,Promise.resolve({put:!0})}remove({forKey:e}){const r=this.data.findIndex(({key:t})=>t===e);return-1!==r?(this.data.splice(r,1),Promise.resolve({remove:!0})):Promise.resolve({remove:!1})}write(){return sessionStorage.setItem(this.storageName,JSON.stringify(this.data)),Promise.resolve({write:!0})}clear(){return sessionStorage.removeItem(this.storageName),this.data=[],Promise.resolve({clear:!0})}contains({forKey:e}){return Promise.resolve({contains:!!this.data.find(({key:r})=>r===e)})}}}}]);