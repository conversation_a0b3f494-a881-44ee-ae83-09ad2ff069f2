(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3168],{12188:(R,b,t)=>{t.d(b,{I:()=>h,T:()=>E});var v=t(15861),m=t(87956),u=t(53113),s=t(98699);class d{constructor(l,e,n){this.biller=l,this.source=e,this.amount=n}}function y(r){return new d(r.biller,r.source,r.amount)}var p=t(71776),I=t(39904),f=t(87903),P=t(42168),i=t(84757),c=t(99877);let C=(()=>{class r{constructor(e){this.http=e}send(e){return(0,P.firstValueFrom)(this.http.post(I.bV.PAYMENTS.BILLER,function A(r){return[{acctIdFrom:r.source.id,acctNickname:r.source.nickname,acctTypeFrom:r.source.type,amt:String(r.amount),nie:r.biller.number,pmtCodServ:r.biller.companyId,toEntity:r.biller.companyName,toNickname:r.biller.nickname}]}(e)).pipe((0,i.map)(([n])=>(0,f.l1)(n,"SUCCESS")))).catch(n=>(0,f.rU)(n))}}return r.\u0275fac=function(e){return new(e||r)(c.\u0275\u0275inject(p.HttpClient))},r.\u0275prov=c.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var B=t(20691);let o=(()=>{class r extends B.Store{constructor(e){super({confirmation:!1}),e.subscribes(I.PU,()=>{this.reset()})}setBiller(e){this.reduce(n=>({...n,biller:e}))}getBiller(){return this.select(({biller:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}setAmount(e){this.reduce(n=>({...n,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return r.\u0275fac=function(e){return new(e||r)(c.\u0275\u0275inject(m.Yd))},r.\u0275prov=c.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),h=(()=>{class r{constructor(e,n,a){this.repository=e,this.store=n,this.eventBusService=a}setBiller(e){try{return s.Either.success(this.store.setBiller(e))}catch({message:n}){return s.Either.failure({message:n})}}setSource(e){try{return s.Either.success(this.store.setSource(e))}catch({message:n}){return s.Either.failure({message:n})}}setAmount(e){try{return s.Either.success(this.store.setAmount(e))}catch({message:n}){return s.Either.failure({message:n})}}reset(){try{return s.Either.success(this.store.reset())}catch({message:e}){return s.Either.failure({message:e})}}send(){var e=this;return(0,v.Z)(function*(){const n=y(e.store.currentState),a=yield e.execute(n);return e.eventBusService.emit(a.channel),s.Either.success({biller:n,status:a})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(u.LN.error(n))}}}return r.\u0275fac=function(e){return new(e||r)(c.\u0275\u0275inject(C),c.\u0275\u0275inject(o),c.\u0275\u0275inject(m.Yd))},r.\u0275prov=c.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var g=t(19799);let E=(()=>{class r{constructor(e,n,a){this.products=e,this.billers=n,this.store=a}source(e){var n=this;return(0,v.Z)(function*(){try{const a=yield n.products.requestAccountsForTransfer();let D=n.store.getBiller();return!D&&e&&(D=(yield n.billers.request()).find(({uuid:M})=>e===M),n.store.setBiller(D)),s.Either.success({biller:D,products:a})}catch({message:a}){return s.Either.failure({message:a})}})()}amount(){try{const e=this.store.getSource(),n=this.store.getBiller(),a=this.store.getAmount();return s.Either.success({amount:a,biller:n,source:e})}catch({message:e}){return s.Either.failure({message:e})}}confirmation(){try{const e=y(this.store.currentState);return s.Either.success({payment:e})}catch({message:e}){return s.Either.failure({message:e})}}}return r.\u0275fac=function(e){return new(e||r)(c.\u0275\u0275inject(m.hM),c.\u0275\u0275inject(g.e),c.\u0275\u0275inject(o))},r.\u0275prov=c.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},61566:(R,b,t)=>{t.d(b,{w:()=>y});var v=t(39904),m=t(95437),u=t(30263),s=t(12188),d=t(99877);let y=(()=>{class p{constructor(f,P,i){this.modalConfirmation=f,this.mboProvider=P,this.managerBiller=i}execute(f=!0){f?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerBiller.reset(),this.mboProvider.navigation.back(v.Z6.PAYMENTS.HOME)}}return p.\u0275fac=function(f){return new(f||p)(d.\u0275\u0275inject(u.$e),d.\u0275\u0275inject(m.ZL),d.\u0275\u0275inject(s.I))},p.\u0275prov=d.\u0275\u0275defineInjectable({token:p,factory:p.\u0275fac,providedIn:"root"}),p})()},63168:(R,b,t)=>{t.r(b),t.d(b,{MboPaymentBillerAmountPageModule:()=>r});var v=t(17007),m=t(78007),u=t(30263),s=t(15861),d=t(24495),A=t(39904),y=t(87903),p=t(95437),I=t(57544),f=t(12188),P=t(61566),i=t(99877),c=t(35641),C=t(48774),B=t(83413),o=t(45542);const h=A.Z6.PAYMENTS.SERVICES.BILLER;function g(l){return"00004200"===l?2e4:1}let E=(()=>{class l{constructor(n,a,D,M){this.mboProvider=n,this.requestConfiguration=a,this.managerBiller=D,this.cancelProvider=M,this.requesting=!0,this.backAction={id:"btn_payment-biller-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(h.SOURCE)}},this.cancelAction={id:"btn_payment-biller-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new I.FormControl}ngOnInit(){this.initializatedConfiguration()}get disabled(){return this.amountControl.invalid||this.requesting}onSubmit(){this.managerBiller.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(h.CONFIRMATION)}})}initializatedConfiguration(){var n=this;return(0,s.Z)(function*(){(yield n.requestConfiguration.amount()).when({success:({amount:a,biller:D,source:M})=>{a&&n.amountControl.setValue(a),n.source=M;const O=[d.C1,d.LU,(0,d.Go)(g(D.companyId))];(0,y.VN)(M)&&O.push((0,d.VV)(M.amount)),n.amountControl.setValidators(O)}},()=>{n.requesting=!1})})()}}return l.\u0275fac=function(n){return new(n||l)(i.\u0275\u0275directiveInject(p.ZL),i.\u0275\u0275directiveInject(f.T),i.\u0275\u0275directiveInject(f.I),i.\u0275\u0275directiveInject(P.w))},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-biller-amount-page"]],decls:13,vars:12,consts:[[1,"mbo-payment-biller-amount-page__content"],[1,"mbo-payment-biller-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-payment-biller-amount-page__body"],[1,"mbo-payment-biller-amount-page__message","subtitle2-medium"],["elementId","txt_payment-biller-amount_value","label","Valor a pagar","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount","hidden"],[3,"skeleton","hidden"],[1,"mbo-payment-biller-amount-page__footer"],["id","btn_payment-biller-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(n,a){1&n&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3)(4,"p",4),i.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas pagar? "),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6)(8,"bocc-card-product-summary",7),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(9,"div",8)(10,"button",9),i.\u0275\u0275listener("click",function(){return a.onSubmit()}),i.\u0275\u0275elementStart(11,"span"),i.\u0275\u0275text(12,"Continuar"),i.\u0275\u0275elementEnd()()()),2&n&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("leftAction",a.backAction)("rightAction",a.cancelAction),i.\u0275\u0275advance(4),i.\u0275\u0275property("formControl",a.amountControl),i.\u0275\u0275advance(1),i.\u0275\u0275property("color",null==a.source?null:a.source.color)("icon",null==a.source?null:a.source.logo)("title",null==a.source?null:a.source.nickname)("number",null==a.source?null:a.source.shortNumber)("amount",null==a.source?null:a.source.amount)("hidden",a.requesting),i.\u0275\u0275advance(1),i.\u0275\u0275property("skeleton",!0)("hidden",!a.requesting),i.\u0275\u0275advance(2),i.\u0275\u0275property("disabled",a.disabled))},dependencies:[c.d,C.J,B.D,o.P],styles:["/*!\n * MBO PaymentBillerAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 01/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-biller-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-biller-amount-page .mbo-payment-biller-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-payment-biller-amount-page .mbo-payment-biller-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-payment-biller-amount-page .mbo-payment-biller-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-payment-biller-amount-page .mbo-payment-biller-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-biller-amount-page .mbo-payment-biller-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-payment-loan-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20)}}\n"],encapsulation:2}),l})(),r=(()=>{class l{}return l.\u0275fac=function(n){return new(n||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[v.CommonModule,m.RouterModule.forChild([{path:"",component:E}]),u.dH,u.Jx,u.D1,u.P8]}),l})()},63674:(R,b,t)=>{t.d(b,{Eg:()=>p,Lo:()=>s,Wl:()=>d,ZC:()=>A,_f:()=>m,br:()=>y,tl:()=>u});var v=t(29306);const m={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},u=new v.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),s={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},A={color:"danger",key:"expired",label:"Vencida"},y={color:"info",key:"recurring",label:"Pago recurrente"},p={color:"info",key:"programmed",label:"Programado"}},66067:(R,b,t)=>{t.d(b,{S6:()=>I,T2:()=>y,UQ:()=>f,mZ:()=>p});var v=t(39904),m=t(6472),s=t(63674),d=t(31707);class y{constructor(i,c,C,B,o,h,g,E,r,l,e){this.id=i,this.type=c,this.name=C,this.nickname=B,this.number=o,this.bank=h,this.isAval=g,this.isProtected=E,this.isOwner=r,this.ownerName=l,this.ownerDocument=e,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[v.y1],this.initialsName=(0,m.initials)(B),this.shortNumber=o.substring(o.length-4),this.descriptionNumber=`${C} ${o}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:h.logo,light:h.logo,standard:h.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(i){this.informationValue||(this.informationValue=i)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(i){return this.currenciesValue.includes(i)}}class p{constructor(i,c){this.id=i,this.type=c}}class I{constructor(i,c,C,B,o,h,g,E,r,l,e,n){this.uuid=i,this.number=c,this.nie=C,this.nickname=B,this.companyId=o,this.companyName=h,this.amount=g,this.registerDate=E,this.expirationDate=r,this.paid=l,this.statusCode=e,this.references=n,this.recurring=n.length>0,this.status=function A(P){switch(P){case d.U.EXPIRED:return s.ZC;case d.U.PENDING:return s.Wl;case d.U.PROGRAMMED:return s.Eg;case d.U.RECURRING:return s.br;default:return s.Lo}}(e)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class f{constructor(i,c,C,B,o,h,g,E){this.uuid=i,this.number=c,this.nickname=C,this.companyId=B,this.companyName=o,this.city=h,this.amount=g,this.isBiller=E}}},19799:(R,b,t)=>{t.d(b,{e:()=>C,W:()=>B});var v=t(71776),m=t(39904),u=t(87956),s=t(98699),d=t(42168),A=t(84757),y=t(53113),p=t(33876),I=t(66067);var c=t(99877);let C=(()=>{class o{constructor(g,E){this.http=g,E.subscribes(m.PU,()=>{this.destroy()}),this.billers$=(0,s.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(m.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,A.map)(({content:r})=>r.map(l=>function i(o){return new I.UQ((0,p.v4)(),o.nie,o.nickname,o.orgIdNum,o.orgName,o.city,+o.amt,(0,s.parseBoolean)(o.biller))}(l))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return o.\u0275fac=function(g){return new(g||o)(c.\u0275\u0275inject(v.HttpClient),c.\u0275\u0275inject(u.Yd))},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),B=(()=>{class o{constructor(g,E){this.http=g,E.subscribes(m.PU,()=>{this.destroy()}),this.invoices$=(0,s.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(m.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,A.map)(({content:r})=>r.map(l=>function P(o){const h=o.refInfo.map(g=>function f(o){return new I.mZ(o.refId,o.refType)}(g));return new I.S6((0,p.v4)(),o.invoiceNum,o.nie,o.nickName,o.orgIdNum,o.orgName,+o.totalCurAmt,new y.ou(o.effDt),new y.ou(o.expDt),(0,s.parseBoolean)(o.payDone),o.state,h)}(l))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return o.\u0275fac=function(g){return new(g||o)(c.\u0275\u0275inject(v.HttpClient),c.\u0275\u0275inject(u.Yd))},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},31707:(R,b,t)=>{t.d(b,{U:()=>v,f:()=>m});var v=(()=>{return(u=v||(v={})).RECURRING="1",u.EXPIRED="2",u.PENDING="3",u.PROGRAMMED="4",v;var u})(),m=(()=>{return(u=m||(m={})).BILLER="Servicio",u.NON_BILLER="Servicio",u.PSE="Servicio",u.TAX="Impuesto",u.LOAN="Obligaci\xf3n financiera",u.CREDIT_CARD="Obligaci\xf3n financiera",m;var u})()}}]);