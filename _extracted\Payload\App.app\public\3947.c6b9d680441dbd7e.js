(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3947],{83947:(B,T,o)=>{o.r(T),o.d(T,{MboLoginPageModule:()=>ft});var s=o(17007),a=o(78007),e=o(30263),n=o(33395),d=o(79798),c=o(39904),_=o(95437),t=o(99877),p=o(25317),f=o(2460);let E=(()=>{class l{constructor(r,m,h){this.contactsInfoService=r,this.bottomSheetService=m,this.mboProvider=h}ngOnInit(){this.contactsInfoService.subscribe(r=>{if("string"!=typeof r){if("PHONES"===r.action)return this.contactsSheet?.open(),void this.contactsInfoService.close();if("WHATSAPP"===r.action)return this.whatsappSheet?.open(),void this.contactsInfoService.close()}}),this.contactsSheet=this.bottomSheetService.create(d.GI),this.whatsappSheet=this.bottomSheetService.create(d.Uy)}ngOnDestroy(){this.contactsSheet?.destroy(),this.whatsappSheet?.destroy()}onContacts(){this.contactsInfoService.open()}onOffices(){this.mboProvider.openUrl(c.BA.GEOLOCATION)}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275directiveInject(d.w7),t.\u0275\u0275directiveInject(e.fG),t.\u0275\u0275directiveInject(_.ZL))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-login-footer"]],decls:10,vars:0,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_login-footer_location","boccUtagComponent","click",1,"bocc-footer-form__element",3,"click"],["icon","location-home"],[1,"bocc-footer-form__label"],["id","btn_login-footer_contacts","boccUtagComponent","click",1,"bocc-footer-form__element",3,"click"],["icon","chat-info"]],template:function(r,m){1&r&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),t.\u0275\u0275listener("click",function(){return m.onOffices()}),t.\u0275\u0275element(3,"bocc-icon",3),t.\u0275\u0275elementStart(4,"label",4),t.\u0275\u0275text(5," Oficinas "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(6,"div",5),t.\u0275\u0275listener("click",function(){return m.onContacts()}),t.\u0275\u0275element(7,"bocc-icon",6),t.\u0275\u0275elementStart(8,"label",4),t.\u0275\u0275text(9," Cont\xe1ctanos "),t.\u0275\u0275elementEnd()()()())},dependencies:[p.I,f.Z],styles:["mbo-login-footer{--pvt-footer-element-font-size: var(--caption-size);--pvt-footer-element-letter-spacing: var(--caption-letter-spacing);position:relative;display:block;width:100%;padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-login-footer .bocc-footer-form__element label{font-size:var(--pvt-footer-element-font-size);letter-spacing:var(--pvt-footer-element-letter-spacing)}@media screen and (max-width: 320px){mbo-login-footer{--pvt-footer-element-font-size: var(--caption-size);--pvt-footer-element-letter-spacing: var(--caption-letter-spacing)}}\n"],encapsulation:2}),l})(),i=(()=>{class l{}return l.\u0275fac=function(r){return new(r||l)},l.\u0275mod=t.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=t.\u0275\u0275defineInjector({imports:[s.CommonModule,n.kW,e.Zl,e.b6,d.To]}),l})();var u=o(25329),x=o(3372),b=o(87956),v=o(31711),C=o(53113),g=o(84041),y=o.n(g),P=(()=>{return(l=P||(P={})).CUSTOMER_FAILED="MIG02",l.CUSTOMER_VALIDATION_FAILED="MIG03",l.SECURE_DATA_FAILED="MIG04",l.START_SUCCESS="MIG05",l.VERIFICATION_SUCCESS="MIG08",P;var l})();class k{constructor(S,r,m,h,M,D){this.token=S,this.lastAuthDate=r,this.currentDate=m,this.lastIPAddress=h,this.success=M,this.migrate=D}get expiration(){return 1e3*y()(this.token).exp}}class z{constructor(S,r,m,h,M){this.success=S,this.type=r,this.processId=m,this.errorMessage=h,this.additionalErrorMessage=M}get isVerificated(){return this.isCorrect(P.VERIFICATION_SUCCESS)}errorFormat(S){return`${this.type}: ${this.errorMessage||S}`}isCorrect(S){return this.success&&this.type===S}}class L{constructor(S,r,m,h){this.password=m,this.deviceSerial=h,this.document=new C.dp(S,r)}get documentType(){return this.document.type}get documentNumber(){return this.document.number}}class G{constructor(S,r,m,h,M,D){this.biometricMode=S,this._isBiometricLinked=r,this.firstLogin=m,this.compilation=h,this.platform=M,this.customer=D}get isBiometricLinked(){return this._isBiometricLinked}get isActiveBiometric(){return this.biometricMode!==v._.None}get canAuthenticationBiometric(){return this.isActiveBiometric&&this._isBiometricLinked}get isEnableBiometric(){return this.isActiveBiometric&&!this._isBiometricLinked}reset(){this._isBiometricLinked=!1,this.customer=void 0}}class oe{constructor(S,r,m){this.value=S,this.shortAccount=r,this.typeAccount=m}}function j(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"bocc-icon",10),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(r);const h=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(h.onOpenTags())}),t.\u0275\u0275elementEnd()}}function U(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"div",3)(2,"div",4),t.\u0275\u0275listener("click",function(h){t.\u0275\u0275restoreView(r);const M=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(M.onBoarding(h))}),t.\u0275\u0275element(3,"img",5),t.\u0275\u0275elementStart(4,"label",6),t.\u0275\u0275text(5,"\xbfC\xf3mo funciona?"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275element(6,"bocc-tag-aval",7),t.\u0275\u0275template(7,j,1,0,"bocc-icon",8),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(8,"mbo-tag-aval-copy",9),t.\u0275\u0275elementEnd()}if(2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(6),t.\u0275\u0275property("value",r.currentTag.value),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",r.tags.length>1),t.\u0275\u0275advance(1),t.\u0275\u0275property("value",r.currentTag.value)}}function H(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",17)(1,"div",18),t.\u0275\u0275listener("click",function(){const M=t.\u0275\u0275restoreView(r).$implicit,D=t.\u0275\u0275nextContext(3);return t.\u0275\u0275resetView(D.onSelect(M))}),t.\u0275\u0275element(2,"bocc-icon",19),t.\u0275\u0275elementStart(3,"span",20),t.\u0275\u0275text(4),t.\u0275\u0275elementEnd()(),t.\u0275\u0275element(5,"mbo-tag-aval",7),t.\u0275\u0275elementEnd()}if(2&l){const r=S.$implicit;t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate(r.shortAccount),t.\u0275\u0275advance(1),t.\u0275\u0275property("value",r.value)}}function X(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"div",14)(1,"label",15),t.\u0275\u0275text(2,"CUENTAS DE AHORRO"),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(3,H,6,2,"div",16),t.\u0275\u0275elementEnd()),2&l){const r=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(3),t.\u0275\u0275property("ngForOf",r.savingTags)}}function re(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",17)(1,"div",21),t.\u0275\u0275listener("click",function(){const M=t.\u0275\u0275restoreView(r).$implicit,D=t.\u0275\u0275nextContext(3);return t.\u0275\u0275resetView(D.onSelect(M))}),t.\u0275\u0275element(2,"bocc-icon",22),t.\u0275\u0275elementStart(3,"span",20),t.\u0275\u0275text(4),t.\u0275\u0275elementEnd()(),t.\u0275\u0275element(5,"mbo-tag-aval",7),t.\u0275\u0275elementEnd()}if(2&l){const r=S.$implicit;t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate(r.shortAccount),t.\u0275\u0275advance(1),t.\u0275\u0275property("value",r.value)}}function ue(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"div",14)(1,"label",15),t.\u0275\u0275text(2,"CUENTAS CORRIENTE"),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(3,re,6,2,"div",16),t.\u0275\u0275elementEnd()),2&l){const r=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(3),t.\u0275\u0275property("ngForOf",r.checkingTags)}}function q(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"bocc-bottom-sheet",11),t.\u0275\u0275listener("visibleChange",function(h){t.\u0275\u0275restoreView(r);const M=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(M.tagsListVisible=h)}),t.\u0275\u0275elementStart(1,"div",12),t.\u0275\u0275template(2,X,4,1,"div",13),t.\u0275\u0275template(3,ue,4,1,"div",13),t.\u0275\u0275elementEnd()()}if(2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("visible",r.tagsListVisible),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",r.savingTags.length),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",r.checkingTags.length)}}let R=(()=>{class l{constructor(r,m,h){this.secureStorageService=r,this.preferencesService=m,this.onboardingScreenService=h,this.visibility=!0,this.tags=[],this.savingTags=[],this.checkingTags=[],this.tagsListVisible=!1,this.visibleTagAval=!0}ngOnInit(){this.initializatedConfiguration()}get tagsAvailable(){return this.visibleTagAval&&this.visibility&&!!this.tags.length}onOpenTags(){this.tagsListVisible=!0}onSelect(r){this.secureStorageService.put(u.c.TagAval,r.value).then(()=>{this.currentTag=r,this.tagsListVisible=!1})}initializatedConfiguration(){Promise.all([this.secureStorageService.get(u.c.TagsAval),this.secureStorageService.get(u.c.TagAval),this.preferencesService.requestBoolean(x.M.VisibleTagAvalLogin)]).then(([r,m,h])=>{this.visibleTagAval=h,r&&(this.tags=JSON.parse(r).map(({shortNumber:M,type:D,avalKey:Z,tagAval:J})=>{const Y=new oe(J||Z,M,D);return"SDA"===D?this.savingTags.push(Y):this.checkingTags.push(Y),Y}),this.currentTag=m&&this.tags.find(({value:M})=>M===m)||this.tags[0])})}onBoarding(r){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(d.ZS)),this.tagAvalonboarding.open(),r.stopPropagation()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275directiveInject(b.Is),t.\u0275\u0275directiveInject(b.yW),t.\u0275\u0275directiveInject(d.x6))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-select-tag-aval"]],inputs:{visibility:"visibility"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["class","mbo-select-tag-aval__card",4,"ngIf"],[3,"visible","visibleChange",4,"ngIf"],[1,"mbo-select-tag-aval__card"],[1,"mbo-select-tag-aval__component"],[1,"mbo-select-tag-aval__description",3,"click"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png"],[1,"overline-medium","truncate"],[3,"value"],["icon","list-open",3,"click",4,"ngIf"],["elementId","icon_login_copy-tag",3,"value"],["icon","list-open",3,"click"],[3,"visible","visibleChange"],[1,"mbo-select-tag-aval__sheet"],["class","mbo-select-tag-aval__sheet__tags",4,"ngIf"],[1,"mbo-select-tag-aval__sheet__tags"],[1,"smalltext-semibold"],["class","mbo-select-tag-aval__sheet__tag",4,"ngFor","ngForOf"],[1,"mbo-select-tag-aval__sheet__tag"],["bocc-theme","success",1,"mbo-select-tag-aval__sheet__tag__account",3,"click"],["icon","coins"],[1,"smalltext-medium"],["bocc-theme","amathyst",1,"mbo-select-tag-aval__sheet__tag__account",3,"click"],["icon","wallet-percent"]],template:function(r,m){1&r&&(t.\u0275\u0275template(0,U,9,3,"div",0),t.\u0275\u0275template(1,q,4,3,"bocc-bottom-sheet",1)),2&r&&(t.\u0275\u0275property("ngIf",m.tagsAvailable),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",m.tagsAvailable))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,e.Zl,e.b6,e.qd,d.t5,d.$O],styles:['mbo-select-tag-aval{--bocc-tag-aval-font-size: var(--overline-size);--pvt-logo-height: var(--sizing-x8);position:relative;display:block;width:100%;overflow:hidden}mbo-select-tag-aval .mbo-select-tag-aval__card{--bocc-icon-dimension: var(--sizing-x9);position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x4) var(--sizing-x8);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-300);background:var(--overlay-lgrey-40)}mbo-select-tag-aval .mbo-select-tag-aval__card mbo-tag-aval-copy{margin-left:var(--sizing-x2)}mbo-select-tag-aval .mbo-select-tag-aval__component{position:relative;display:flex;width:calc(100% - var(--sizing-x16));-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1);align-items:center;padding-right:var(--sizing-x4);overflow:hidden}mbo-select-tag-aval .mbo-select-tag-aval__component:after{content:"";position:absolute;right:0;top:50%;height:50%;width:1px;background-color:var(--color-carbon-lighter-300);transform:translateY(-50%)}mbo-select-tag-aval .mbo-select-tag-aval__component bocc-tag-aval{margin-left:var(--sizing-x6);overflow:hidden}mbo-select-tag-aval .mbo-select-tag-aval__component>bocc-icon{color:var(--color-blue-700)}mbo-select-tag-aval .mbo-select-tag-aval__description{position:relative;display:flex;flex-direction:column;align-items:flex-start;row-gap:var(--sizing-x2);padding-right:var(--sizing-x6)}mbo-select-tag-aval .mbo-select-tag-aval__description:after{content:"";position:absolute;right:0;top:50%;height:50%;width:1px;background-color:var(--color-carbon-lighter-300);transform:translateY(-50%)}mbo-select-tag-aval .mbo-select-tag-aval__description img{height:var(--pvt-logo-height);width:auto;max-width:100%}mbo-select-tag-aval .mbo-select-tag-aval__description label{color:var(--color-blue-700)}mbo-select-tag-aval .mbo-select-tag-aval__sheet{display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x6);box-sizing:border-box}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tags{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tags label{padding:var(--sizing-x4);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tag{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4);box-sizing:border-box}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tag__account{--bocc-icon-dimension: var(--sizing-x12);display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tag__account bocc-icon{padding:var(--sizing-x2);border-radius:50%;background:var(--color-bocc-200);color:var(--color-bocc-700)}mbo-select-tag-aval .mbo-select-tag-aval__sheet__tag__account span{color:var(--color-amathyst-700)}@media screen and (max-width: 360px){mbo-select-tag-aval{--pvt-logo-height: var(--sizing-x6)}}\n'],encapsulation:2}),l})();var F=o(15861),N=o(88844),V=o(29306),ie=o(27236),ne=(()=>{return(l=ne||(ne={})).AuthenticationMode="authentication-mode",l.CustomerIP="customer-ip",l.DeviceModel="device-model",l.DocumentNumber="document-number",l.DocumentType="document-type",ne;var l})(),le=o(5164),fe=o(7464),Me=o(74520),ee=o(70658),be=o(83328),he=o(12263),me=o(85911),ve=o(65518);function Se(l){return new k(l.authToken||l.token,l.lastAuthDate,l.currentDate,l.lastIPAddress,l.success,l.migrate)}function Ce(l){return new z(l.success,l.step,l.processId,l.errorMessage,l.additionalErrorMessage)}var _e=o(71776),ce=o(87903),te=o(42168),Pe=o(81781);let Oe=(()=>{class l{constructor(r,m,h){this.migrationService=r,this.deviceService=m,this.cryptoService=h}requestAll(){return this.migrationService.requestAll()}request(r){return this.migrationService.request(r)}updateAll(r){return this.migrationService.updateAll(r)}update(r){return this.migrationService.update(r)}fingerprint(r){var m=this;return(0,F.Z)(function*(){const h=m.deviceService.controller.itIsIos?yield m.createRawIOS(r):yield m.createRawDevice(r);return m.cryptoService.encodeSha512(h.toLowerCase())})()}createRawIOS(r){var m=this;return(0,F.Z)(function*(){const{operatingSystem:h}=yield m.deviceService.getInfo(),{number:M,type:{code:D}}=r;return`${h}:SPPNAN3.1.0:${D}:${M}:MB:${m.reverseNumber(M)}`})()}createRawDevice(r){var m=this;return(0,F.Z)(function*(){const{operatingSystem:h,model:M}=yield m.deviceService.getInfo(),{number:D,type:{code:Z}}=r;return`${h}:SPPNAN3.1.0:${Z}:${D}:MB:${M}:${M}:${m.reverseNumber(D)}`})()}reverseNumber(r){return r.split("").reverse().join("")}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(b.ar),t.\u0275\u0275inject(b.U8),t.\u0275\u0275inject(b.$I))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),ye=(()=>{class l{constructor(r,m){this.deviceService=r,this.service=m}document(r){return Promise.all([this.deviceService.getInfo(),this.deviceService.getFingerprint()]).then(([m,h])=>(this.service.reset(),this.service.execute({idType:r.type.code,id:r.number,deviceName:m.name?m.name:m.model,deviceSerial:h,deviceOS:m.operatingSystem,serial:h,deviceOperatingSystem:m.operatingSystem,deviceUuid:m.uuid,login:!0,companyId:ee.N.bankId})))}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(b.U8),t.\u0275\u0275inject(Pe.s))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),w=(()=>{class l{constructor(r){this.migrationService=r}save(r){var m=this;return(0,F.Z)(function*(){const{document:h}=r,M=yield m.migrationService.fingerprint(h);r.fingerprint=M;const D=yield m.requestAll();return D.push(r),yield m.updateAll(D),D})()}requestAll(){return this.migrationService.requestAll()}request(r){return this.migrationService.request(r)}refresh(r){var m=this;return(0,F.Z)(function*(){try{const h=yield m.request(r.document);if(h){const M=h.clone({fullName:r.clientName,segment:r.segment.code}),D=(yield m.requestAll()).map(Z=>Z.uuid===M.uuid?M:Z);yield Promise.all([m.migrationService.update(M),m.updateAll(D)])}}catch{return}})()}updateAll(r){return this.migrationService.updateAll(r)}remove(r){var m=this;return(0,F.Z)(function*(){const h=yield m.requestAll(),M=h.indexOf(r);return M>-1&&(h.splice(M,1),yield m.updateAll(h)),h})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(Oe))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),K=(()=>{class l{constructor(r,m){this.http=r,this.deviceService=m}start(r){return this.createPayload(r).then(m=>(0,te.firstValueFrom)(this.http.post(c.bV.SILENT_MIGRATION,m).pipe((0,te.map)(h=>Ce(h)))))}verificate(r,m){return(0,te.firstValueFrom)(this.http.post(c.bV.SILENT_MIGRATION,{processId:r.processId,content:{currentPassword:m}}).pipe((0,te.map)(h=>Ce(h))))}createPayload(r){const{deviceService:m}=this;return Promise.all([m.getInfo(),m.getFingerprint()]).then(([h,M])=>({content:{companyId:ee.N.bankId,deviceAppBuild:h.appCompilation,deviceAppVersion:h.appVersion,deviceName:h.name,deviceManufacturer:h.manufacturer,deviceModel:h.model,deviceOperatingSystem:h.operatingSystem,deviceOsVersion:h.osVersion,devicePlatform:m.controller.platform,deviceSerial:M,deviceUuid:r.uuid,id:r.documentNumber,idType:r.documentType.code,isComplementaryServices:r.complimentaryServicesEnabled}}))}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(_e.HttpClient),t.\u0275\u0275inject(b.U8))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),A=(()=>{class l{constructor(r,m,h){this.http=r,this.tokenStorageService=m,this.deviceFingerprintService=h}requestToken(r){var m=this;const h=function we(l){return{deviceSerial:l.deviceSerial,documentType:l.documentType.code,documentNumber:l.documentNumber,password:l.password}}(r);return(0,te.firstValueFrom)(this.http.post(c.bV.LOGIN,h,{headers:new _e.HttpHeaders({Authorization:`Bearer ${ee.N.api.token}`})}).pipe((0,te.map)(M=>Se(M)),(0,te.tap)(function(){var M=(0,F.Z)(function*(D){m.deviceFingerprintService.setIpAddress(D.lastIPAddress),yield m.tokenStorageService.setValue(D.token),yield m.tokenStorageService.setExpiration(D.expiration)});return function(D){return M.apply(this,arguments)}}()))).catch(M=>{throw new le.Kr(M,M.error?.description||"Ocurri\xf3 un error inesperado. No pudimos realizar la autenticaci\xf3n de tus credenciales")})}requestSecurity(r){var m=this;return(0,F.Z)(function*(){const h=function Te(l){return{deviceFingerPrint:l.deviceSerial,documentType:l.documentType.code,documentNumber:l.documentNumber,ipAddress:"127.0.0.1",password:l.password,"x-device-serial":l.deviceSerial}}(r);return(0,te.firstValueFrom)(m.http.post(c.bV.LOGIN_SECURITY,h).pipe((0,te.map)(M=>{if(M.Service)throw new le.Kr(M.Service,M.ErrorMessage);return[Se(M.authToken),new V.O8(r.documentType,r.documentNumber,M.getUserData.clientName.trim(),M.getUserData.clientFirstName?.trim(),M.getUserData.clientLastName?.trim(),M.getUserData.hasComplementaryServicesActivited,(0,ce.vY)(M.getUserData.currentSegment))]}),(0,te.tap)(function(){var M=(0,F.Z)(function*([D]){m.deviceFingerprintService.setIpAddress(D.lastIPAddress),yield m.tokenStorageService.setValue(D.token),yield m.tokenStorageService.setExpiration(D.expiration)});return function(D){return M.apply(this,arguments)}}()),(0,te.catchError)(M=>{throw M instanceof _e.HttpErrorResponse&&M.error?.Service?new le.Kr(M.error.Service,M.error.ErrorMessage):M})))})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(_e.HttpClient),t.\u0275\u0275inject(b.id),t.\u0275\u0275inject(b.ew))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})();var W=o(81092),$=o(98699);class Q extends $.PartialSealed{static enrollment(S){return new Q("enrollment",S)}static failure(S){return new Q("failure",S)}static resetBiometric(){return new Q("resetBiometric")}static success(S){return new Q("success",S)}}let Ae=(()=>{class l{constructor(r,m,h,M){this.migrationRepository=r,this.enrollmentStore=m,this.loginStore=h,this.enrollmentRepository=M}execute(r,m){var h=this;return(0,F.Z)(function*(){try{return(yield h.verifyMigration(r))?h.resolveSuccess():h.enrollmentRepository.document(r).then(D=>D.value===be.l.FILL_UNIVERSAL_PASSWORD?h.resolveSuccess():(D.value===be.l.LOGIN_VALIDATION_ERROR&&h.enrollmentStore.setDocument(r.type,r.number,m),[!1,(0,he.F)(D)]))}catch{return[!1,me.d.error("Ocurrio un error al tratar de verificar datos de identificaci\xf3n")]}})()}resolveSuccess(){return this.loginStore.setEnrollmentConfirmed(!0),[!0]}verifyMigration(r){var m=this;return(0,F.Z)(function*(){return ee.N.silentMigrationEnabled&&!!(yield m.migrationRepository.request(r))})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(w),t.\u0275\u0275inject(ve.c),t.\u0275\u0275inject(W.P),t.\u0275\u0275inject(ye))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})();class xe extends Error{constructor(S){super(void 0),this.value=S}}const Ee="Lo sentimos, ocurrio un error al tratar de autenticarte. Por favor intente m\xe1s tarde",Ve={CHANNELISBLOCKED_BASICDATA:c.Z6.AUTHENTICATION.ERRORS.CHANNEL_BLOCKED,ENROLLMENT_DEVICE_VALIDATION:c.Z6.AUTHENTICATION.ENROLLMENT.WELCOME,GET_USER_DATA_ON_SDS:c.Z6.AUTHENTICATION.ERRORS.SERVICE_FAILURE,USERDOESNOTEXIST_BASICDATA:c.Z6.AUTHENTICATION.ERRORS.SERVICE_FAILURE},We=["AUTHENTICATION"];let Ge=(()=>{class l{constructor(r,m,h,M,D,Z,J,Y,ae,pe,ge,ze,ht,Ct,yt,xt,Et){this.deviceService=r,this.cryptoService=m,this.analyticsInfoService=h,this.secureStorageService=M,this.storageService=D,this.preferencesService=Z,this.authenticationRepository=J,this.biometricService=Y,this.customerService=ae,this.sessionService=pe,this.logoutService=ge,this.migrationRepository=ze,this.enrollmentRepository=ht,this.enrollmentStore=Ct,this.loginStore=yt,this.sessionStore=xt,this.verifyEnrollmentLogin=Et}credentials(r){var m=this;return(0,F.Z)(function*(){try{const h=yield m.requestSession(r,r.remember,"credentials");return yield m.secureStorageService.put(u.c.TemporalPassword,r.password),yield m.biometricService.save(function Ke(l){return new V.J1(l.documentType,l.documentNumber,l.password)}(r),!1),h}catch(h){return Q.failure(me.d.error(`${h.message||Ee} (ATH02)`))}})()}biometric(r){var m=this;return(0,F.Z)(function*(){try{return m.requestSession(yield m.biometricService.request(),r,"biometric")}catch(h){return Q.failure(me.d.error(`${h.message||Ee} (ATH03)`))}})()}getAuthenticationCredentials(r){return Promise.all([this.cryptoService.encode(r.password),this.deviceService.getFingerprint()]).then(([m,h])=>new L(r.documentType,r.documentNumber,m,h))}requestToken(r){return this.getAuthenticationCredentials(r).then(m=>this.authenticationRepository.requestToken(m))}verifyEnrollment(r,m,h){var M=this;return(0,F.Z)(function*(){const D=new C.dp(r,m),[Z,J]=yield M.verifyEnrollmentLogin.execute(D,h);if(!Z)return Promise.resolve({errorServer:!1,granted:Z,enrollment:J});const Y=M.loginStore.isEnrollmentConfirmed();return ee.N.navigatorEnabled||Y?Promise.resolve({granted:!0,errorServer:!1}):M.enrollmentRepository.document(D).then(ae=>{const pe=ae.value!==be.l.LOGIN_VALIDATION_ERROR,ge=ae.value===be.l.FILL_UNIVERSAL_PASSWORD;return M.loginStore.setEnrollmentConfirmed(ge),{enrollment:(0,he.F)(ae),errorServer:pe,granted:ge}}).catch(({message:ae})=>({errorServer:!0,granted:!1,enrollment:me.d.error(`${ae??Ee} (ATH01)`)}))})()}requestCustomerFromSteps(r,m){var h=this;return(0,F.Z)(function*(){const{documentNumber:M,documentType:D}=r,Z=yield h.verifyEnrollment(D,M,m);if(!Z.granted)throw new xe(Z);const J=yield h.requestToken(r),Y=yield h.customerService.request(function je(l){return new C.dp(l.documentType,l.documentNumber)}(r));return[J,Y]})()}requestCustomerFromSecurity(r){return this.getAuthenticationCredentials(r).then(m=>this.authenticationRepository.requestSecurity(m))}requestCustomer(r,m){return this.preferencesService.requestBoolean(x.M.NewLogin).then(h=>h?this.requestCustomerFromSecurity(r):this.requestCustomerFromSteps(r,m))}requestSilentMigration(){var r=this;return(0,F.Z)(function*(){if(!ee.N.silentMigrationEnabled)return;const m=yield r.storageService.get(ie.Z.CustomerSilentMigration);return m&&(0,fe._$)(m)})()}requestSession(r,m,h){var M=this;return(0,F.Z)(function*(){try{const[D,Z]=yield M.requestCustomer(r,m);M.currentToken=D,M.migrationRepository.refresh(Z);const Y=function Ie(l){return new V.Xs(l.customer,l.token.lastIPAddress,new C.ou(l.token.currentDate),new C.ou(l.token.lastAuthDate),l.customer.hasComplementaryServicesActivited??l.silentMigration.complimentaryServicesEnabled)}({customer:Z,silentMigration:yield M.requestSilentMigration(),token:D});return yield M.sessionService.save(Y,m),M.sessionStore.setSession(Y),M.saveAnalyticsInfo(Y,h),M.enrollmentStore.reset(),Q.success({requiredActiveToken:M.loginStore.isRequiredTokenActivation(),session:Y})}catch(D){if(M.currentToken&&(M.logoutService.execute(M.currentToken.token),M.currentToken=void 0),D instanceof le.Kr){const Z=Ve[D.value];return"ENROLLMENT_DEVICE_VALIDATION"===D.value&&M.enrollmentStore.setDocument(r.documentType,r.documentNumber,m),Z?Q.failure(me.d.back(Z)):We.includes[D.value]?Q.failure(me.d.error(D.message)):"biometric"===h&&"1611"===D.value.error?.statusCode?Q.resetBiometric():Q.failure(me.d.error(D.message))}return D instanceof xe?D.value.errorServer?Q.failure(D.value.enrollment):Q.enrollment(D.value.enrollment):Q.failure(me.d.error(`${Ee}, ${D.message} (ATH04)`))}})()}saveAnalyticsInfo(r,m){var h=this;return(0,F.Z)(function*(){const{model:M,name:D}=yield h.deviceService.getInfo(),{documentNumber:Z,documentType:J}=r.customer;h.analyticsInfoService.append(ne.AuthenticationMode,m),h.analyticsInfoService.append(ne.DocumentType,J.code),h.analyticsInfoService.append(ne.DocumentNumber,Z),h.analyticsInfoService.append(ne.CustomerIP,r.ip),h.analyticsInfoService.append(ne.DeviceModel,D||M)})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(b.U8),t.\u0275\u0275inject(b.$I),t.\u0275\u0275inject(b.El),t.\u0275\u0275inject(b.Is),t.\u0275\u0275inject(b.V1),t.\u0275\u0275inject(b.yW),t.\u0275\u0275inject(A),t.\u0275\u0275inject(b.oy),t.\u0275\u0275inject(b.vZ),t.\u0275\u0275inject(b.NY),t.\u0275\u0275inject(b.dA),t.\u0275\u0275inject(w),t.\u0275\u0275inject(ye),t.\u0275\u0275inject(ve.c),t.\u0275\u0275inject(W.P),t.\u0275\u0275inject(Me.f),t.\u0275\u0275inject(Ae))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})();var Ze=o(81536);let $e=(()=>{class l{constructor(r,m,h,M,D){this.customerRepository=r,this.publicKeyRepository=m,this.migrationRepository=h,this.cryptoService=M,this.loginStore=D}execute(r){var m=this;return(0,F.Z)(function*(){try{if(!ee.N.debugKonyEnabled)return $.Either.success();const h=yield m.verifyCustomer(r);if(!h)return $.Either.success();ee.N.production||m.loginStore.setEnrollmentConfirmed(!0);const{success:M,message:D}=yield m.verifySilentMigration(h,r);return M?$.Either.success():$.Either.failure({message:D})}catch{return $.Either.success()}})()}verifyCustomer(r){const{documentNumber:m,documentType:h}=r;return ee.N.silentMigrationEnabled?this.customerRepository.request(new C.dp(h,m)):Promise.resolve(void 0)}verifySilentMigration(r,m){var h=this;return(0,F.Z)(function*(){const M=yield h.migrationRepository.start(r);if(!M.success)return{success:!1,message:"No pudimos autenticarte. Por favor, vuelve a intentarlo."};const D=yield h.getPassword(m),Z=yield h.migrationRepository.verificate(M,D),{isVerificated:J,type:Y}=Z;return{success:J,message:Z.errorFormat(h.getMsgDefault(Y))}})()}getPassword({password:r}){return this.publicKeyRepository.requestEnrollment().then(m=>this.cryptoService.encodeRSA(m,r))}getMsgDefault(r){return r===P.CUSTOMER_FAILED?"No pudimos autenticarte. Por favor, dir\xedjase a la oficina m\xe1s cercana":"No pudimos autenticarte. Por favor, vuelve a intentarlo."}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(w),t.\u0275\u0275inject(Ze.aH),t.\u0275\u0275inject(K),t.\u0275\u0275inject(b.$I),t.\u0275\u0275inject(W.P))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),Ne=(()=>{class l{constructor(r,m,h,M,D,Z,J,Y,ae,pe){this.silentMigrationrepository=r,this.deviceService=m,this.customerBiometricService=h,this.customerService=M,this.logoutService=D,this.storageService=Z,this.secureStorageService=J,this.biometricService=Y,this.customerPreferencesService=ae,this.tokenService=pe,this.firstLogin=!0}login(){return Promise.all([this.deviceService.getInfo(),this.customerService.request(),this.biometricService.getMode(),this.storageService.get(ie.Z.BiometricLinked),this.tokenService.value()]).then(([r,m,h,M,D])=>{D&&(0,$.catchPromise)(this.logoutService.execute(D));const Z=new G(h,M,this.firstLogin,r.appCompilation,r.platform,m);return this.firstLogin=!1,$.Either.success(Z)}).catch(({message:r})=>$.Either.failure({message:r}))}removeCustomer(){try{return Promise.all([this.customerService.destroy(),this.customerBiometricService.remove(),ee.N.production?this.customerPreferencesService.destroy():Promise.resolve(!0),this.storageService.remove(ie.Z.CustomerFirstLogin),this.secureStorageService.remove(u.c.TagsAval),this.secureStorageService.remove(u.c.TagAval)]).then(([r,m,h])=>$.Either.success(r&&m&&h))}catch({message:r}){return Promise.resolve($.Either.failure({message:r}))}}silentMigration(){return this.silentMigrationrepository.requestAll().then(r=>$.Either.success(r)).catch(({message:r})=>$.Either.failure({message:r}))}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(w),t.\u0275\u0275inject(b.U8),t.\u0275\u0275inject(b.oy),t.\u0275\u0275inject(b.vZ),t.\u0275\u0275inject(b.dA),t.\u0275\u0275inject(b.V1),t.\u0275\u0275inject(b.Is),t.\u0275\u0275inject(b.x1),t.\u0275\u0275inject(b.fT),t.\u0275\u0275inject(b.id))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),He=(()=>{class l{constructor(r){this.repository=r}save(r){var m=this;return(0,F.Z)(function*(){try{return $.Either.success(yield m.repository.save(r))}catch({message:h}){return $.Either.failure({message:h})}})()}remove(r){var m=this;return(0,F.Z)(function*(){try{return $.Either.success(yield m.repository.remove(r))}catch({message:h}){return $.Either.failure({message:h})}})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(w))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),Fe=(()=>{class l{constructor(r){this.storageService=r,this.subject=new te.Subject}select(r){this.storageService.set(ie.Z.CustomerSilentMigration,(0,fe.p2)(r)),this.subject.next(r)}subscribe(r){const m=this.subject.asObservable().subscribe(r);return()=>{m.unsubscribe()}}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275inject(b.V1))},l.\u0275prov=t.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})();var de=o(24495),se=o(57544),Ye=o(33876);class Xe extends se.FormGroup{constructor(){const S=new se.FormControl(c.Gd,[de.C1]),r=new se.FormControl(void 0,[de.C1,de.X1]),m=new se.FormControl(void 0,[de.C1]),h=new se.FormControl((0,Ye.v4)(),[de.C1]),M=new se.FormControl(!0),D=new se.FormControl(!0);super({controls:{documentType:S,documentNumber:r,reference:m,uuid:h,complementaryServices:M,remember:D}}),this.documentType=S,this.documentNumber=r,this.reference=m,this.uuid=h,this.complementaryServices=M,this.remember=D}get documentValid(){return this.documentType.valid&&this.documentNumber.valid}createSilentMigration(){return new V.JL(this.documentType.value,this.documentNumber.value,"",this.reference.value,this.uuid.value,this.complementaryServices.value,this.remember.value,"MIGRATION")}}var De=o(45542),ke=o(60817),Je=o(64181),Qe=o(48774),Be=o(65887);function qe(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"div",29),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&l){const r=t.\u0275\u0275nextContext().$implicit;t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",r.fullName," ")}}function et(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",18)(1,"div",19),t.\u0275\u0275element(2,"bocc-icon",20),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",21),t.\u0275\u0275listener("click",function(){const M=t.\u0275\u0275restoreView(r).$implicit,D=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(D.onSelect(M))}),t.\u0275\u0275template(4,qe,2,1,"div",22),t.\u0275\u0275elementStart(5,"div",23),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"div",24),t.\u0275\u0275text(8),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(9,"div",25),t.\u0275\u0275text(10),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"div",26),t.\u0275\u0275text(12),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(13,"div",27)(14,"bocc-icon",28),t.\u0275\u0275listener("click",function(){const M=t.\u0275\u0275restoreView(r).$implicit,D=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(D.onRemove(M))}),t.\u0275\u0275elementEnd()()()}if(2&l){const r=S.$implicit;t.\u0275\u0275advance(1),t.\u0275\u0275classProp("mbo-silent-migration-sheet__element__avatar--active",r.complimentaryServicesEnabled),t.\u0275\u0275advance(3),t.\u0275\u0275property("ngIf",r.fullName),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",r.reference," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",r.documentType.label," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",r.documentNumber," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",r.uuid," ")}}function tt(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",30),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(r);const h=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(h.onRegister())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2,"Nuevo usuario"),t.\u0275\u0275elementEnd()()}}function ot(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",31),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(r);const h=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(h.onSubmit())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2,"Registrar usuario"),t.\u0275\u0275elementEnd()()}if(2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("disabled",r.migrationControls.invalid)}}let nt=(()=>{class l{constructor(r,m,h){this.silentSelectorProvider=r,this.managerConfiguration=m,this.managerSilentMigration=h,this.documents=N.iR,this.customers=[],this.registering=!1,this.migrationControls=new Xe,this.backAction={id:"btn_silent-migration_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.registering,click:()=>{this.portal.close()}},this.cancelAction={id:"btn_silent-migration_cancel",label:"Cancelar",hidden:()=>!this.registering,click:()=>{this.registering=!1}}}ngOnInit(){this.requestConfiguration()}ngBoccPortal(r){this.portal=r}onSelect(r){this.silentSelectorProvider.select(r),this.portal.close()}onRemove(r){var m=this;return(0,F.Z)(function*(){(yield m.managerSilentMigration.remove(r)).when({success:h=>{m.customers=h}})})()}onRegister(){this.registering=!0}onSubmit(){var r=this;return(0,F.Z)(function*(){const m=r.migrationControls.createSilentMigration();(yield r.managerSilentMigration.save(m)).when({success:h=>{r.customers=h,r.registering=!1,r.migrationControls.reset()}})})()}requestConfiguration(){var r=this;return(0,F.Z)(function*(){(yield r.managerConfiguration.silentMigration()).when({success:m=>{r.customers=m}})})()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275directiveInject(Fe),t.\u0275\u0275directiveInject(Ne),t.\u0275\u0275directiveInject(He))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-silent-migration-sheet"]],decls:22,vars:16,consts:[[1,"mbo-silent-migration-sheet"],[1,"mbo-silent-migration-sheet__header"],["title","Migraci\xf3n",3,"leftAction","rightAction"],[1,"mbo-silent-migration-sheet__catalog",3,"hidden"],[1,"mbo-silent-migration-sheet__collection",3,"hidden"],["class","mbo-silent-migration-sheet__element",4,"ngFor","ngForOf"],[1,"mbo-silent-migration-sheet__empty",3,"hidden"],["src","assets/shared/logos/modals/search-result-none.svg"],[1,"body2-medium"],[1,"mbo-silent-migration-sheet__body",3,"hidden"],["pageId","silent-migration",1,"bocc-small__col-1-4",3,"documents","documentNumber","documentType"],["elementId","txt_silent-migration_reference","label","Referencia","placeholder","Ingresar una referencia",3,"formControl"],["elementId","txt_silent-migration_uuid","label","UUID","placeholder","Ingresar valor UUID",3,"formControl"],["elementId","chck_silent-migration_services",3,"formControl"],["elementId","chck_silent-migration_remember",3,"formControl"],[1,"mbo-silent-migration-sheet__footer"],["id","btn_silent-migration_new","bocc-button","raised","prefixIcon","add-page",3,"click",4,"ngIf"],["id","btn_silent-migration_submit","bocc-button","raised",3,"disabled","click",4,"ngIf"],[1,"mbo-silent-migration-sheet__element"],[1,"mbo-silent-migration-sheet__element__avatar"],["icon","user-manager"],[1,"mbo-silent-migration-sheet__element__content",3,"click"],["class","mbo-silent-migration-sheet__element__full-name smalltext-medium truncate",4,"ngIf"],[1,"mbo-silent-migration-sheet__element__reference","smalltext-medium","truncate"],[1,"mbo-silent-migration-sheet__element__type","smalltext-medium"],[1,"mbo-silent-migration-sheet__element__number","caption-medium"],[1,"mbo-silent-migration-sheet__element__uuid","overline-medium"],[1,"mbo-silent-migration-sheet__element__action"],["icon","remove",3,"click"],[1,"mbo-silent-migration-sheet__element__full-name","smalltext-medium","truncate"],["id","btn_silent-migration_new","bocc-button","raised","prefixIcon","add-page",3,"click"],["id","btn_silent-migration_submit","bocc-button","raised",3,"disabled","click"]],template:function(r,m){1&r&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4),t.\u0275\u0275template(5,et,15,7,"div",5),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"div",6)(7,"picture"),t.\u0275\u0275element(8,"img",7),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(9,"p",8),t.\u0275\u0275text(10," Actualmente no existen usuarios registrados para migraci\xf3n silenciosa en este dispositivo "),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(11,"div",9),t.\u0275\u0275element(12,"mbo-document-customer",10)(13,"bocc-input-box",11)(14,"bocc-input-box",12),t.\u0275\u0275elementStart(15,"bocc-checkbox-label",13),t.\u0275\u0275text(16," Habilitar servicios complementarios "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(17,"bocc-checkbox-label",14),t.\u0275\u0275text(18," Recordar datos del documento "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(19,"div",15),t.\u0275\u0275template(20,tt,3,0,"button",16),t.\u0275\u0275template(21,ot,3,1,"button",17),t.\u0275\u0275elementEnd()()),2&r&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",m.backAction)("rightAction",m.cancelAction),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",m.registering),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",!m.customers.length),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",m.customers),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",m.customers.length),t.\u0275\u0275advance(5),t.\u0275\u0275property("hidden",!m.registering),t.\u0275\u0275advance(1),t.\u0275\u0275property("documents",m.documents)("documentNumber",m.migrationControls.documentNumber)("documentType",m.migrationControls.documentType),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",m.migrationControls.reference),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",m.migrationControls.uuid),t.\u0275\u0275advance(1),t.\u0275\u0275property("formControl",m.migrationControls.complementaryServices),t.\u0275\u0275advance(2),t.\u0275\u0275property("formControl",m.migrationControls.remember),t.\u0275\u0275advance(3),t.\u0275\u0275property("ngIf",!m.registering),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",m.registering))},dependencies:[s.NgForOf,s.NgIf,De.P,ke.a,f.Z,Je.D,Qe.J,Be.X],styles:["mbo-silent-migration-sheet{position:relative;width:100%;display:block}mbo-silent-migration-sheet .mbo-silent-migration-sheet{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__header{position:sticky;top:0;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__catalog{display:flex;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-silent-migration-sheet .mbo-silent-migration-sheet__collection{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__avatar{padding:var(--sizing-x2);border-radius:var(--sizing-x2);background:var(--color-semantic-danger-200);color:var(--color-semantic-danger-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__avatar--active{background:var(--color-semantic-success-200);color:var(--color-semantic-success-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__content{width:100%;overflow:hidden}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__full-name{color:var(--color-amathyst-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__reference{color:var(--color-blue-400)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__number{color:var(--color-blue-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__uuid{color:var(--color-carbon-lighter-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__element__action{color:var(--color-carbon-lighter-400)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__empty{position:relative;display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x12)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__empty>picture{position:relative}mbo-silent-migration-sheet .mbo-silent-migration-sheet__empty>picture>img{width:44rem;height:44rem}mbo-silent-migration-sheet .mbo-silent-migration-sheet__empty>p{position:relative;text-align:center;color:var(--color-carbon-lighter-700)}mbo-silent-migration-sheet .mbo-silent-migration-sheet__body{display:flex;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-silent-migration-sheet .mbo-silent-migration-sheet__footer{position:sticky;display:flex;bottom:0;flex-direction:column;row-gap:var(--sizing-x6);background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-silent-migration-sheet .mbo-silent-migration-sheet__footer button{width:100%}\n"],encapsulation:2}),l})(),rt=(()=>{class l{}return l.\u0275fac=function(r){return new(r||l)},l.\u0275mod=t.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=t.\u0275\u0275defineInjector({imports:[s.CommonModule,e.P8,e.aR,e.Zl,e.DT,e.Jx,d.XH]}),l})();class it extends se.FormGroup{constructor(){const S=new se.FormControl(c.Gd,[de.C1]),r=new se.FormControl(void 0,[de.C1,de.X1]),m=new se.FormControl(void 0,[de.C1,(0,de.Je)(8)]),h=new se.FormControl(!1);super({controls:{documentType:S,documentNumber:r,password:m,remember:h}}),this.documentType=S,this.documentNumber=r,this.password=m,this.remember=h}get documentValid(){return this.documentType.valid&&this.documentNumber.valid}get documentCustomer(){return new C.dp(this.documentType.value,this.documentNumber.value)}get credentials(){return{documentType:this.documentType.value,documentNumber:this.documentNumber.value,password:this.password.value,remember:this.remember.value}}setCustomer(S){this.documentType.setValue(S.documentType),this.documentNumber.setValue(S.documentNumber)}}var at=o(92275),ct=o(895),st=o(19102),Re=o(84757);let lt=(()=>{class l{constructor(r,m){this.ref=r,this.bottomSheetService=m,this.mboSilentMigrationCode=!1,this.click$=new te.Subject}ngOnInit(){this.migrationSheet=this.bottomSheetService.create(nt),this.ref.nativeElement.addEventListener("click",this.onClick.bind(this)),this.click$.pipe((0,Re.bufferToggle)(this.click$.pipe((0,Re.throttleTime)(500)),()=>(0,te.timer)(500)),(0,Re.filter)(({length:r})=>r>=3)).subscribe(()=>{this.mboSilentMigrationCode&&this.showSilentMigrationSheet()})}ngOnDestroy(){this.migrationSheet?.destroy()}onClick(){this.click$.next(0)}showSilentMigrationSheet(){this.migrationSheet.open()}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275directiveInject(t.ElementRef),t.\u0275\u0275directiveInject(e.fG))},l.\u0275dir=t.\u0275\u0275defineDirective({type:l,selectors:[["","mboSilentMigrationCode",""]],inputs:{mboSilentMigrationCode:"mboSilentMigrationCode"}}),l})();function mt(l,S){if(1&l&&t.\u0275\u0275element(0,"bocc-button-standard",23),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("button",r.registerAction)}}function dt(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"div",24)(1,"h5",25),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"span",26),t.\u0275\u0275text(4," \xa1Bienvenido a tu Banca M\xf3vil! "),t.\u0275\u0275elementEnd()()),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" Hola ",r.customer.shortName," ")}}function ut(l,S){if(1&l&&t.\u0275\u0275element(0,"mbo-document-customer",27),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("condense",!0)("documents",r.documents)("documentNumber",r.loginControls.documentNumber)("documentType",r.loginControls.documentType)("disabled",r.authenticating)}}function pt(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"bocc-checkbox-label",28),t.\u0275\u0275text(1," Recordar mis datos "),t.\u0275\u0275elementEnd()),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("formControl",r.loginControls.remember)("disabled",r.authenticating)}}function bt(l,S){if(1&l){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",29),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(r);const h=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(h.onRemoveCustomer())}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2,"Olvidar usuario"),t.\u0275\u0275elementEnd()()}}function _t(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"span"),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" (",r.configuration.compilation,") ")}}function gt(l,S){if(1&l&&(t.\u0275\u0275elementStart(0,"div",30)(1,"bocc-badge")(2,"b"),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd()()()),2&l){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1("Actividad ",r.appCodeActivity,"")}}let vt=(()=>{class l{constructor(r,m,h,M,D,Z,J,Y,ae){this.ref=r,this.modalConfirmation=m,this.biometricService=h,this.mboProvider=M,this.silentSelectorProvider=D,this.enrollmentStore=Z,this.managerConfiguration=J,this.authenticateMigration=Y,this.authenticateCustomer=ae,this.authenticating=!1,this.enrollmenting=!1,this.submitIcon="lock-password",this.documents=N.tc;const{appVersion:pe,bankName:ge}=ee.N,ze=(new Date).getFullYear();this.version=`\xa9 ${ze} ${ge} - v${pe}`,this.loginControls=new it,this.registerAction={id:"btn_login_register",label:"Reg\xedstrate",disabled:()=>this.enrollmenting,click:()=>{this.enrollmentStore.reset(),this.mboProvider.navigation.next(c.Z6.AUTHENTICATION.ENROLLMENT.WELCOME)}}}ngOnInit(){this.unsubscription=this.silentSelectorProvider.subscribe(({documentNumber:r,documentType:m,remember:h})=>{this.loginControls.documentType.setValue(m),this.loginControls.documentNumber.setValue(r),this.loginControls.remember.setValue(h)}),this.initializatedConfiguration()}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get isDevelopment(){return!ee.N.production&&!ee.N.navigatorEnabled}get appCodeActivity(){return ee.N.appCodeActivity}get customer(){return this.configuration?.customer}get canSilentMigration(){return ee.N.silentMigrationEnabled&&!this.customer}get isSubmitEnabled(){return!!this.customer&&this.configuration.isBiometricLinked||this.loginControls.valid}onRecover(){this.mboProvider.navigation.next(c.Z6.AUTHENTICATION.FORGOT_PASSWORD.WELCOME)}onSubmit(){var r=this;return(0,F.Z)(function*(){return r.loginControls.password.valid?r.startAuthenticationCredentials():r.customer&&r.configuration.isBiometricLinked?r.startAuthenticationBiometric():void 0})()}onRemoveCustomer(){this.modalConfirmation.execute({title:"Olvidar usuario",message:"\xbfEstas seguro que deseas dejar de recordar este usuario de Banca M\xf3vil en este dispositivo?",accept:{label:"Olvidar",theme:"danger",click:()=>{this.managerConfiguration.removeCustomer().then(()=>{this.resetCustomer()})}},decline:{label:"Cancelar"}})}initializatedConfiguration(){var r=this;return(0,F.Z)(function*(){(yield r.managerConfiguration.login()).when({success:m=>{const{biometricMode:h,isBiometricLinked:M,customer:D,firstLogin:Z}=m;if(r.configuration=m,D)r.loginControls.remember.setValue(!0),r.loginControls.setCustomer(D),r.submitIcon=M?(0,ce.tR)(h):"lock-password",M&&Z&&r.startAuthenticationBiometric();else{const{documentNumber:J,documentRemember:Y,documentType:ae}=r.enrollmentStore.getDocument();r.loginControls.documentType.setValue(ae||c.Gd),r.loginControls.documentNumber.setValue(J),r.loginControls.remember.setValue(Y)}}})})()}startAuthentication(r){var m=this;return(0,F.Z)(function*(){m.mboProvider.loader.open("Autenticando, por favor espere..."),m.authenticating=!0,(yield m.authenticateMigration.execute(m.loginControls.credentials)).when({failure:({message:h})=>{!ee.N.production&&console.log(h)}},r)})()}startAuthenticationBiometric(){var r=this;return(0,F.Z)(function*(){(yield r.biometricService.authentication())&&r.startAuthentication(()=>{r.authenticateCustomer.biometric(!!r.customer||r.loginControls.remember.value).then(m=>{r.resolveAuthetication(m)})})})()}startAuthenticationCredentials(){var r=this;return(0,F.Z)(function*(){r.startAuthentication(()=>{r.authenticateCustomer.credentials(r.loginControls.credentials).then(m=>{r.resolveAuthetication(m)})})})()}redirectEnrollment(r){r===c.Z6.AUTHENTICATION.ENROLLMENT.WELCOME?this.managerConfiguration.removeCustomer().finally(()=>{this.resetCustomer(),this.mboProvider.navigation.back(r)}):this.mboProvider.navigation.back(r)}resolveEnrollment(r){r.when({next:m=>{this.redirectEnrollment(m)},back:m=>{this.redirectEnrollment(m)},error:m=>{this.mboProvider.toast.error(m)},warning:m=>{this.mboProvider.toast.warning(m)}},()=>{this.enrollmenting=!1})}resetPasswordForBiometric(){this.modalConfirmation.execute({logo:"assets/authentication/logos/authentication-biometric-failed.svg",title:"Biom\xe9trico desvinculado",message:"Tu contrase\xf1a actual no coincide con la vinculada en el biom\xe9trico del dispositivo, debes ingresar por contrase\xf1a para reactivarlo nuevamente en Banca M\xf3vil",accept:{label:"Ingresar por contrase\xf1a",click:()=>{this.ref.nativeElement.querySelector("bocc-password-box input").focus()}}})}resolveAuthetication(r){r.when({success:({requiredActiveToken:m})=>{this.mboProvider.navigation.next(m?c.Z6.CUSTOMER.TOKEN_ACTIVATION:c.Z6.CUSTOMER.PRODUCTS.HOME)},enrollment:m=>{this.resolveEnrollment(m)},resetBiometric:()=>{this.resetPasswordForBiometric()},failure:m=>{this.resolveEnrollment(m)}},()=>{this.authenticating=!1,this.mboProvider.loader.close()})}resetCustomer(){this.configuration.reset(),this.loginControls.remember.setValue(!1),this.submitIcon="lock-password"}}return l.\u0275fac=function(r){return new(r||l)(t.\u0275\u0275directiveInject(t.ElementRef),t.\u0275\u0275directiveInject(e.$e),t.\u0275\u0275directiveInject(b.x1),t.\u0275\u0275directiveInject(_.ZL),t.\u0275\u0275directiveInject(Fe),t.\u0275\u0275directiveInject(ve.c),t.\u0275\u0275directiveInject(Ne),t.\u0275\u0275directiveInject($e),t.\u0275\u0275directiveInject(Ge))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-login-page"]],decls:29,vars:17,consts:[[1,"mbo-login-page"],[1,"mbo-login-page__content"],[1,"mbo-login-page__header"],[3,"visibility"],[3,"button",4,"ngIf"],[3,"mboSilentMigrationCode"],[1,"mbo-login-page__body"],[1,"mbo-login-page__form"],["class","mbo-login-page__customer",4,"ngIf"],["pageId","login",3,"condense","documents","documentNumber","documentType","disabled",4,"ngIf"],[1,"mbo-login-page__customer__password"],["elementId","pwd_login_password","placeholder","Ingresa su contrase\xf1a","label","Contrase\xf1a",3,"formControl"],["id","btn_login_recover","bocc-button","flat","boccUtagComponent","click",3,"click"],["elementId","chck_login_remember",3,"formControl","disabled",4,"ngIf"],[1,"mbo-login-page__footer"],[1,"mbo-login-page__footer__content"],[1,"mbo-login-page__footer__actions"],["id","btn_login_submit-authenticate","bocc-button","raised","boccUtagComponent","click",3,"prefixIcon","disabled","click"],["id","btn_login_remove-customer","bocc-button","flat","boccUtagComponent","click",3,"click",4,"ngIf"],[1,"mbo-login-page__footer__version"],[4,"ngIf"],["class","mbo-login-page__footer__qa",4,"ngIf"],[1,"mbo-login-page__footer__menu"],[3,"button"],[1,"mbo-login-page__customer"],[1,"mbo-login-page__customer--title","h5-regular"],[1,"mbo-login-page__customer--subtitle","subtitle2-medium"],["pageId","login",3,"condense","documents","documentNumber","documentType","disabled"],["elementId","chck_login_remember",3,"formControl","disabled"],["id","btn_login_remove-customer","bocc-button","flat","boccUtagComponent","click",3,"click"],[1,"mbo-login-page__footer__qa"]],template:function(r,m){1&r&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),t.\u0275\u0275element(3,"mbo-select-tag-aval",3),t.\u0275\u0275template(4,mt,1,1,"bocc-button-standard",4),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(5,"mbo-bank-logo",5),t.\u0275\u0275elementStart(6,"div",6)(7,"div",7),t.\u0275\u0275template(8,dt,5,1,"div",8),t.\u0275\u0275template(9,ut,1,5,"mbo-document-customer",9),t.\u0275\u0275elementStart(10,"div",10),t.\u0275\u0275element(11,"bocc-password-box",11),t.\u0275\u0275elementStart(12,"button",12),t.\u0275\u0275listener("click",function(){return m.onRecover()}),t.\u0275\u0275elementStart(13,"span"),t.\u0275\u0275text(14,"Olvid\xe9 mi contrase\xf1a"),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275template(15,pt,2,2,"bocc-checkbox-label",13),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(16,"div",14)(17,"div",15)(18,"div",16)(19,"button",17),t.\u0275\u0275listener("click",function(){return m.onSubmit()}),t.\u0275\u0275elementStart(20,"span"),t.\u0275\u0275text(21,"Iniciar sesi\xf3n"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275template(22,bt,3,0,"button",18),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(23,"div",19),t.\u0275\u0275text(24),t.\u0275\u0275template(25,_t,2,1,"span",20),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(26,gt,4,1,"div",21),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(27,"div",22),t.\u0275\u0275element(28,"mbo-login-footer"),t.\u0275\u0275elementEnd()()()),2&r&&(t.\u0275\u0275classProp("mbo-login-page--visible",m.configuration),t.\u0275\u0275advance(2),t.\u0275\u0275classProp("mbo-login-page__header--center",!!m.customer),t.\u0275\u0275advance(1),t.\u0275\u0275property("visibility",!!m.customer),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!m.customer),t.\u0275\u0275advance(1),t.\u0275\u0275property("mboSilentMigrationCode",m.canSilentMigration),t.\u0275\u0275advance(3),t.\u0275\u0275property("ngIf",m.customer),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!m.customer),t.\u0275\u0275advance(2),t.\u0275\u0275property("formControl",m.loginControls.password),t.\u0275\u0275advance(4),t.\u0275\u0275property("ngIf",!m.customer),t.\u0275\u0275advance(4),t.\u0275\u0275property("prefixIcon",m.submitIcon)("disabled",!m.isSubmitEnabled),t.\u0275\u0275advance(3),t.\u0275\u0275property("ngIf",m.customer),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",m.version," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",null==m.configuration?null:m.configuration.compilation),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",m.isDevelopment))},dependencies:[s.NgIf,p.I,at.O,De.P,De.v,ct.s,ke.a,st.r,Be.X,R,E,lt],styles:["mbo-login-page{--mbo-bank-logo-height: 26rem;--pvt-content-row-gap: 26rem;--pvt-header-padding: var(--sizing-x6);position:relative;display:block;width:100%;height:100%;overflow:hidden;background:var(--color-carbon-lighter-50)}mbo-login-page .mbo-login-page{--pvt-content-opacity: 0;position:relative;display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between;opacity:var(--pvt-content-opacity)}mbo-login-page .mbo-login-page--visible{--pvt-content-opacity: 1;animation:.36s mbo-login-page-animation cubic-bezier(.68,-.55,.27,1.55)}mbo-login-page .mbo-login-page__content{position:relative;display:flex;width:100%;padding-top:var(--sizing-safe-top, 0rem);background:var(--color-carbon-lighter-50);flex-direction:column;row-gap:var(--pvt-content-row-gap)}mbo-login-page .mbo-login-page__content bocc-header-form{background:var(--color-carbon-lighter-50)}mbo-login-page .mbo-login-page__content .bocc-header-form__title{width:20%}mbo-login-page .mbo-login-page__content .bocc-header-form__action{width:auto}mbo-login-page .mbo-login-page__header{--bocc-button-padding: 0rem var(--sizing-x2);--bocc-button-height: var(--sizing-x16);position:relative;display:flex;justify-content:space-between;align-items:flex-start;padding:var(--pvt-header-padding);box-sizing:border-box}mbo-login-page .mbo-login-page__header--invert{flex-direction:row-reverse}mbo-login-page .mbo-login-page__body{position:relative;display:flex;width:100%;transition:.5s height cubic-bezier(.68,-.55,.27,1.55)}mbo-login-page .mbo-login-page__body .bocc-tab-form__content{display:flex;align-items:center;min-height:100rem}mbo-login-page .mbo-login-page__form{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin:auto 0rem}mbo-login-page .mbo-login-page__customer{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}mbo-login-page .mbo-login-page__customer--title{color:var(--color-blue-700);text-align:center}mbo-login-page .mbo-login-page__customer--subtitle{text-align:center}mbo-login-page .mbo-login-page__customer__body{--bocc-chip-avatar-background: var(--bocc-segment-background-color);--bocc-chip-avatar-font-color: var(--bocc-segment-font-color)}mbo-login-page .mbo-login-page__customer__password{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-login-page .mbo-login-page__customer__password>button{align-self:flex-end}mbo-login-page .mbo-login-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);z-index:var(--z-index-4);padding:var(--sizing-x8) 0rem var(--sizing-safe-bottom, 0rem) 0rem;background:var(--overlay-white-80)}mbo-login-page .mbo-login-page__footer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-login-page .mbo-login-page__footer__menu{border-top:var(--border-1-lighter-300);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-login-page .mbo-login-page__footer__actions{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-login-page .mbo-login-page__footer__version{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-400);font-size:var(--smalltext-size);font-weight:var(--font-weight-medium);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing)}mbo-login-page .mbo-login-page__footer__qa{--color-bocc-900: var(--color-amathyst-700);--color-bocc-200: var(--color-amathyst-200);display:flex;justify-content:center}.mbo-login-page__header--center{justify-content:center!important}@media screen and (max-height: 800px){mbo-login-page{--mbo-bank-logo-height: var(--sizing-x24);--pvt-content-row-gap: var(--sizing-x24)}}@media screen and (max-height: 700px){mbo-login-page{--mbo-bank-logo-height: var(--sizing-x22);--pvt-content-row-gap: var(--sizing-x22)}}@media screen and (max-height: 600px){mbo-login-page{--mbo-bank-logo-height: var(--sizing-x20);--pvt-content-row-gap: var(--sizing-x20);--pvt-header-padding: var(--sizing-x4) var(--sizing-x3)}}@keyframes mbo-login-page-animation{0%{opacity:0}75%{opacity:0}to{opacity:1}}\n"],encapsulation:2}),l})(),ft=(()=>{class l{}return l.\u0275fac=function(r){return new(r||l)},l.\u0275mod=t.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=t.\u0275\u0275defineInjector({imports:[s.CommonModule,a.RouterModule.forChild([{path:"",component:vt}]),n.kW,e.gh,e.Oh,e.u1,e.P8,e.vl,e.Zl,e.DT,e.sC,e.tv,e.aR,e.qw,d.rw,d.XH,d.J5,R,i,rt]}),l})()},83328:(B,T,o)=>{o.d(T,{U:()=>a,l:()=>s});var s=(()=>{return(e=s||(s={})).CANNOT_REGISTER_DEVICE="CANNOT REGISTER DEVICE",e.CHANNEL_IS_BLOCKED="CHANNEL IS BLOCKED",e.COMPLETED="COMPLETED",e.DEVICE_ALREADY_REGISTERED="DEVICE ALREADY REGISTERED",e.ERROR_SIM_INVALID="ERROR SIM INVALID",e.FILL_CURRENT_CHANNEL_PASSWORD="FILL CURRENT CHANNEL PASSWORD",e.FILL_DEVICE_NAME="FILL DEVICE NAME",e.FILL_NEW_UNIVERSAL_PASSWORD="FILL NEW UNIVERSAL PASSWORD",e.FILL_OTP_DATA="FILL OTP DATA",e.FILL_SECURE_DATA="FILL SECURE DATA",e.FILL_UNIVERSAL_PASSWORD="FILL UNIVERSAL PASSWORD",e.INIT="INIT",e.LOGIN_VALIDATION_ERROR="LOGIN VALIDATION ERROR",e.ONESPAN_ACTIVATE_LICENSE="ENR20",e.ONESPAN_ACTIVATE_INSTANCE="ENR19",e.REGISTER_DEVICE_ERROR="REGISTER DEVICE ERROR",e.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION="RETRIES LIMIT EXCEED ON OTP GENERATION",e.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION="RETRIES LIMIT EXCEED ON SECURE DATA GENERATION",e.SERVICE_ERROR="SERVICE ERROR",e.USER_DOES_NOT_EXISTS="USER DOES NOT EXISTS",e.ENROLLMENT_ERROR_CODE="ENROLLMENT ERROR CODE",s;var e})(),a=(()=>{return(e=a||(a={})).LOGIN_ERROR="1",e.SECURE_DATA="121",e.VALIDATION_PRODUCT="1842",e.VALIDATION_SIM="112",e.PORTABILITY="186",a;var e})()},12263:(B,T,o)=>{o.d(T,{F:()=>f});var s=o(39904),a=o(83328),e=o(85911);const{AUTHENTICATION:{ENROLLMENT:n,ERRORS:d}}=s.Z6,c=[a.l.COMPLETED,a.l.DEVICE_ALREADY_REGISTERED],_=[a.l.LOGIN_VALIDATION_ERROR,a.l.ERROR_SIM_INVALID,a.l.CHANNEL_IS_BLOCKED,a.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION,a.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION,a.l.SERVICE_ERROR,a.l.USER_DOES_NOT_EXISTS,a.l.CANNOT_REGISTER_DEVICE],t=[a.U.PORTABILITY,a.U.SECURE_DATA,a.U.VALIDATION_PRODUCT,a.U.VALIDATION_SIM];function p(E){switch(E){case a.l.LOGIN_VALIDATION_ERROR:return n.WELCOME;case a.l.FILL_OTP_DATA:return n.OTP_VERIFICATION;case a.l.FILL_SECURE_DATA:return n.PRODUCT_VERIFICATION;case a.l.FILL_NEW_UNIVERSAL_PASSWORD:return n.PASSWORD_ASSIGNMENT;case a.l.FILL_DEVICE_NAME:return n.DEVICE_SIGNUP;case a.l.ERROR_SIM_INVALID:return d.SIM_INVALID;case a.l.CHANNEL_IS_BLOCKED:return d.CHANNEL_BLOCKED;case a.l.RETRIES_LIMIT_EXCEED_ON_OTP_GENERATION:return d.EXCEED_ATTEMPTS;case a.l.RETRIES_LIMIT_EXCEED_ON_SECURE_DATA_GENERATION:return d.DEFAULT_MESSAGE;case a.l.CANNOT_REGISTER_DEVICE:return d.MAX_DEVICES;case a.l.SERVICE_ERROR:return d.SERVICE_FAILURE;default:return d.DEFAULT_MESSAGE}}function f(E){const{error:i,errorCode:u,message:x,success:b,value:v}=E;return b?c.includes(v)?e.d.finish():e.d.next(p(v)):v===a.l.ENROLLMENT_ERROR_CODE&&u&&t.includes(u)?e.d.back(d.DEFAULT_MESSAGE):_.includes(v)?e.d.back(p(v)):i?e.d.error(x):e.d.warning(x)}},85911:(B,T,o)=>{o.d(T,{d:()=>e});var s=o(98699);class e extends s.PartialSealed{static back(d){return new e("back",d)}static error(d){return new e("error",d)}static finish(d=""){return new e("finish",d)}static next(d){return new e("next",d)}static warning(d){return new e("warning",d)}}},81781:(B,T,o)=>{o.d(T,{s:()=>x});var s=o(71776),a=o(39904),e=o(42168);class c{constructor(v,C,g,y,P){this.accountType=v,this.productType=C,this.length=g,this.question=y,this.questionType=P}}class _{constructor(v,C,g,y,P,O,I,k,z){this.success=v,this.error=C,this.value=g,this.enrollmentKey=y,this.processId=P,this.secureData=O,this.errorMessage=I,this.errorCode=k,this.additionalErrorMessage=z}get message(){const v=this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR01).";return this.additionalErrorMessage?`${v}, ${this.additionalErrorMessage}`:v}static error(){return new _(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de enrolamiento, por favor intente m\xe1s tarde (ENR02).")}}var t=o(83328);function f(b,v=!1){return new _(!!b.success,v,b.step,b.enrollmentKey,b.processId,b.secureDataBriefQuestion&&function p(b){return new c(b.accountType,b.productType,b.length,b.question,b.questionType)}(b.secureDataBriefQuestion),b.errorMessage,b.errorCode,b.additionalErrorMessage)}var E=o(65518),i=o(99877);let x=(()=>{class b{constructor(C,g){this.http=C,this.enrollmentStore=g,this.processId=""}execute(C){return(0,e.firstValueFrom)(this.http.post(a.bV.ENROLLMENT,{processId:this.processId,content:C,ipAddress:""}).pipe((0,e.map)(g=>f(g)),(0,e.tap)(({errorCode:g,processId:y,secureData:P})=>{this.enrollmentStore.setErrorCode(function u(b){switch(b){case t.U.PORTABILITY:return"PT87";case t.U.SECURE_DATA:return"43DS";case t.U.VALIDATION_PRODUCT:return"606ENP";case t.U.VALIDATION_SIM:return"VS61";default:return""}}(g)),this.processId=y,P&&this.enrollmentStore.setSecureData(P)}))).catch(({error:g})=>g?f(g,!0):_.error())}reset(){this.processId=""}}return b.\u0275fac=function(C){return new(C||b)(i.\u0275\u0275inject(s.HttpClient),i.\u0275\u0275inject(E.c))},b.\u0275prov=i.\u0275\u0275defineInjectable({token:b,factory:b.\u0275fac,providedIn:"root"}),b})()},65518:(B,T,o)=>{o.d(T,{c:()=>d});var s=o(20691),e=o(99877);let d=(()=>{class c extends s.Store{constructor(){super({})}getDocument(){return this.select(t=>({documentType:t.documentType,documentNumber:t.documentNumber,documentRemember:t.documentRemember}))}setDocument(t,p,f){this.reduce(E=>({...E,documentType:t,documentNumber:p,documentRemember:f}))}setEnrollmentKey(t){this.reduce(p=>({...p,enrollmentKey:t}))}setPassword(t){this.reduce(p=>({...p,password:t}))}setSecureData(t){this.reduce(p=>({...p,secureData:t}))}getSecureData(){return this.select(({secureData:t})=>t)}setErrorCode(t){this.reduce(p=>({...p,errorCode:t}))}getErrorCode(){return this.select(({errorCode:t})=>t)}}return c.\u0275fac=function(t){return new(t||c)},c.\u0275prov=e.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},19102:(B,T,o)=>{o.d(T,{r:()=>d});var s=o(17007),e=o(99877);let d=(()=>{class c{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return c.\u0275fac=function(t){return new(t||c)},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(t,p){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&t&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",p.src,e.\u0275\u0275sanitizeUrl))},dependencies:[s.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),c})()},52701:(B,T,o)=>{o.d(T,{q:()=>c});var s=o(17007),e=o(30263),n=o(99877);let c=(()=>{class _{}return _.\u0275fac=function(p){return new(p||_)},_.\u0275cmp=n.\u0275\u0275defineComponent({type:_,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(p,f){1&p&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"bocc-icon",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"label",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()()),2&p&&(n.\u0275\u0275classMap(f.classTheme),n.\u0275\u0275advance(3),n.\u0275\u0275property("icon",f.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",f.label," "))},dependencies:[s.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),_})()},55648:(B,T,o)=>{o.d(T,{u:()=>f});var s=o(15861),a=o(17007),n=o(30263),d=o(78506),c=o(99877);function t(E,i){if(1&E){const u=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",2),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(u);const b=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(b.onClick())}),c.\u0275\u0275elementStart(1,"span"),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd()()}if(2&E){const u=c.\u0275\u0275nextContext();c.\u0275\u0275property("prefixIcon",u.icon)("disabled",u.disabled),c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate1(" ",u.label," ")}}function p(E,i){if(1&E){const u=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",3),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(u);const b=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(b.onClick())}),c.\u0275\u0275elementEnd()}if(2&E){const u=c.\u0275\u0275nextContext();c.\u0275\u0275property("bocc-button-action",u.icon)("disabled",u.disabled)}}let f=(()=>{class E{constructor(u){this.preferences=u,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:u})=>{this.isIncognito=u||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var u=this;return(0,s.Z)(function*(){yield u.preferences.toggleIncognito()})()}}return E.\u0275fac=function(u){return new(u||E)(c.\u0275\u0275directiveInject(d.Bx))},E.\u0275cmp=c.\u0275\u0275defineComponent({type:E,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(u,x){1&u&&(c.\u0275\u0275template(0,t,3,3,"button",0),c.\u0275\u0275template(1,p,1,2,"button",1)),2&u&&(c.\u0275\u0275property("ngIf",!x.actionMode),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",x.actionMode))},dependencies:[a.CommonModule,a.NgIf,n.P8,n.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),E})()},72765:(B,T,o)=>{o.d(T,{rw:()=>s.r,qr:()=>a.q,uf:()=>e.u,Z:()=>t,t5:()=>b,$O:()=>x});var s=o(19102),a=o(52701),e=o(55648),n=o(17007),d=o(30263),c=o(99877);const _=["*"];let t=(()=>{class v{constructor(){this.disabled=!1}}return v.\u0275fac=function(g){return new(g||v)},v.\u0275cmp=c.\u0275\u0275defineComponent({type:v,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],ngContentSelectors:_,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(g,y){1&g&&(c.\u0275\u0275projectionDef(),c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275element(2,"bocc-icon",2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",3),c.\u0275\u0275projection(4),c.\u0275\u0275elementEnd()()),2&g&&(c.\u0275\u0275classProp("mbo-poster__content--disabled",y.disabled),c.\u0275\u0275advance(2),c.\u0275\u0275property("icon",y.icon))},dependencies:[n.CommonModule,d.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),v})();var p=o(33395),f=o(77279),E=o(87903),i=o(87956),u=o(25317);let x=(()=>{class v{constructor(g){this.eventBusService=g}onCopy(){this.value&&((0,E.Bn)(this.value),this.eventBusService.emit(f.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return v.\u0275fac=function(g){return new(g||v)(c.\u0275\u0275directiveInject(i.Yd))},v.\u0275cmp=c.\u0275\u0275defineComponent({type:v,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(g,y){1&g&&(c.\u0275\u0275elementStart(0,"bocc-icon",0),c.\u0275\u0275listener("click",function(){return y.onCopy()}),c.\u0275\u0275elementEnd()),2&g&&c.\u0275\u0275property("id",y.elementId)},dependencies:[n.CommonModule,d.Zl,p.kW,u.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),v})(),b=(()=>{class v{}return v.\u0275fac=function(g){return new(g||v)},v.\u0275cmp=c.\u0275\u0275defineComponent({type:v,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(g,y){1&g&&c.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&g&&(c.\u0275\u0275property("value",y.value),c.\u0275\u0275advance(1),c.\u0275\u0275property("value",y.value))},dependencies:[n.CommonModule,d.qd,x],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),v})()},79798:(B,T,o)=>{o.d(T,{Vc:()=>a.Vc,rw:()=>s.rw,k4:()=>a.k4,qr:()=>s.qr,uf:()=>s.uf,xO:()=>n.x,A6:()=>e.A,tu:()=>u,Tj:()=>x,GI:()=>R,Uy:()=>F,To:()=>re,w7:()=>q,o2:()=>a.o2,B_:()=>a.B_,fi:()=>a.fi,XH:()=>a.XH,cN:()=>a.cN,Aj:()=>a.Aj,J5:()=>a.J5,DB:()=>le.D,NH:()=>N.N,ES:()=>ne.E,Nu:()=>a.Nu,x6:()=>ie.x,KI:()=>fe.K,iF:()=>a.iF,u8:()=>Me.u,eM:()=>be.e,ZF:()=>he.Z,wu:()=>me.w,$n:()=>ve.$,KN:()=>Ie.K,cV:()=>Te.c,t5:()=>s.t5,$O:()=>s.$O,ZS:()=>Ce.Z,sO:()=>_e.s,bL:()=>ye,zO:()=>ee.z});var s=o(72765),a=o(27302),e=o(1027),n=o(7427),c=(o(16442),o(17007)),_=o(30263),t=o(44487),p=o.n(t),f=o(13462),E=o(21498),i=o(99877);let u=(()=>{class w{}return w.\u0275fac=function(A){return new(A||w)},w.\u0275mod=i.\u0275\u0275defineNgModule({type:w}),w.\u0275inj=i.\u0275\u0275defineInjector({imports:[c.CommonModule,f.LottieModule.forRoot({player:()=>p()}),s.rw,_.P8,_.Dj,E.P]}),w})(),x=(()=>{class w{ngBoccPortal(A){this.portal=A}onSubmit(){this.portal?.close()}}return w.\u0275fac=function(A){return new(A||w)},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-icon",2),i.\u0275\u0275elementStart(3,"label"),i.\u0275\u0275text(4," \xa1Atenci\xf3n! "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p"),i.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),i.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"li",4),i.\u0275\u0275text(11,"Transacciones a celulares."),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(12,"li",4),i.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(14,"li",4),i.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(16,"p",5),i.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(18,"div",6)(19,"button",7),i.\u0275\u0275listener("click",function(){return W.onSubmit()}),i.\u0275\u0275elementStart(20,"span"),i.\u0275\u0275text(21,"Continuar"),i.\u0275\u0275elementEnd()()())},dependencies:[c.CommonModule,_.Zl,_.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),w})();var b=o(7603),v=o(87956),C=o(74520),g=o(39904),y=o(87903);function O(w,K){if(1&w){const A=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"div",6)(1,"label",7),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"span",8),i.\u0275\u0275text(4,"Tu gerente asignado (a)"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p",8),i.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(7,"button",9),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(A);const $=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView($.onEmail($.manager.email))}),i.\u0275\u0275elementStart(8,"span",10),i.\u0275\u0275text(9),i.\u0275\u0275elementEnd()()()}if(2&w){const A=i.\u0275\u0275nextContext(2);i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",A.manager.name," "),i.\u0275\u0275advance(7),i.\u0275\u0275textInterpolate1(" ",A.manager.email," ")}}function I(w,K){if(1&w){const A=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),i.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"button",12),i.\u0275\u0275listener("click",function($){i.\u0275\u0275restoreView(A);const Q=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(Q.onRetryManager($))}),i.\u0275\u0275elementStart(4,"span"),i.\u0275\u0275text(5,"Recargar"),i.\u0275\u0275elementEnd()()()}}function k(w,K){if(1&w&&(i.\u0275\u0275elementStart(0,"div",3),i.\u0275\u0275template(1,O,10,2,"div",4),i.\u0275\u0275template(2,I,6,0,"div",5),i.\u0275\u0275elementEnd()),2&w){const A=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",A.manager),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!A.manager)}}function z(w,K){1&w&&(i.\u0275\u0275elementStart(0,"div",13),i.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),i.\u0275\u0275elementEnd()),2&w&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0))}o(29306);let L=(()=>{class w{constructor(A){this.customerService=A,this.requesting=!1}onRetryManager(A){this.customerService.requestManager(),A.stopPropagation()}onEmail(A){(0,y.Gw)(`mailto:${A}`)}onWhatsapp(){(0,y.Gw)(g.BA.WHATSAPP)}}return w.\u0275fac=function(A){return new(A||w)(i.\u0275\u0275directiveInject(v.vZ))},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,k,3,2,"div",1),i.\u0275\u0275template(2,z,5,4,"div",2),i.\u0275\u0275elementEnd()),2&A&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!W.requesting),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",W.requesting))},dependencies:[c.CommonModule,c.NgIf,_.P8,_.Dj,a.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),w})();const G={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},oe={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},j={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function H(w,K){if(1&w&&(i.\u0275\u0275elementStart(0,"div",7),i.\u0275\u0275element(1,"mbo-contact-manager",8),i.\u0275\u0275elementEnd()),2&w){const A=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("manager",A.manager)("requesting",A.requesting)}}function X(w,K){if(1&w){const A=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"li",9)(1,"div",10),i.\u0275\u0275listener("click",function($){const Ae=i.\u0275\u0275restoreView(A).$implicit,xe=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(xe.onOption(Ae,$))}),i.\u0275\u0275elementStart(2,"label",11),i.\u0275\u0275text(3),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"div",12)(5,"div",13),i.\u0275\u0275element(6,"bocc-icon",14),i.\u0275\u0275elementEnd()()()()}if(2&w){const A=K.$implicit;i.\u0275\u0275property("id",A.id),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",A.label," "),i.\u0275\u0275advance(1),i.\u0275\u0275attribute("bocc-theme",A.boccTheme),i.\u0275\u0275advance(2),i.\u0275\u0275property("icon",A.icon)}}let re=(()=>{class w{constructor(A,W,$){this.utagService=A,this.customerStore=W,this.customerService=$,this.isManagerEnabled=!1,this.requesting=!1,this.options=[G,oe,j]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:A})=>{this.isManagerEnabled=A?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(W=>{this.manager=W.manager,this.requesting=W.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(A){this.portal=A}onOption(A,W){this.utagService.link("click",A.id),this.portal?.send({action:"option",value:A}),W.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return w.\u0275fac=function(A){return new(A||w)(i.\u0275\u0275directiveInject(b.D),i.\u0275\u0275directiveInject(C.f),i.\u0275\u0275directiveInject(v.vZ))},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-contact-information"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275listener("click",function(){return W.onClose()}),i.\u0275\u0275template(1,H,2,2,"div",1),i.\u0275\u0275elementStart(2,"ul",2),i.\u0275\u0275template(3,X,7,4,"li",3),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),i.\u0275\u0275listener("click",function(){return W.onClose()}),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(6,"div",6),i.\u0275\u0275listener("click",function(){return W.onClose()}),i.\u0275\u0275elementEnd()),2&A&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",W.isManagerEnabled),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngForOf",W.options))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,_.Zl,L],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),w})();var ue=o(95437);let q=(()=>{class w{constructor(A,W){this.floatingService=A,this.mboProvider=W,this.contactsFloating=this.floatingService.create(re),this.contactsFloating?.subscribe(({action:$,value:Q})=>{"option"===$?this.dispatchOption(Q):this.close()})}subscribe(A){this.subscriber=A}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(A){"PQRS"===A.action?this.mboProvider.openUrl(g.BA.PQRS):this.subscriber&&this.subscriber(A)}}return w.\u0275fac=function(A){return new(A||w)(i.\u0275\u0275inject(_.B7),i.\u0275\u0275inject(ue.ZL))},w.\u0275prov=i.\u0275\u0275defineInjectable({token:w,factory:w.\u0275fac,providedIn:"root"}),w})(),R=(()=>{class w{constructor(){this.defenderLineNumber=g._L.DEFENDER_LINE,this.defenderLinePhone=g.WB.DEFENDER_LINE}ngBoccPortal(A){}onEmail(){(0,y.Gw)("mailto:<EMAIL>")}}return w.\u0275fac=function(A){return new(A||w)},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-contact-phones"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275element(1,"mbo-attention-lines-form"),i.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),i.\u0275\u0275element(5,"bocc-icon",4),i.\u0275\u0275elementStart(6,"span",5),i.\u0275\u0275text(7,"Defensor del consumidor financiero"),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),i.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(12,"label",8)(13,"span"),i.\u0275\u0275text(14,"Lorena Cerchar Rosado"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(15,"bocc-badge",9),i.\u0275\u0275text(16," Suplente "),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(17,"div",10),i.\u0275\u0275element(18,"bocc-icon",11),i.\u0275\u0275elementStart(19,"div",12)(20,"span",13),i.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(22,"div",10),i.\u0275\u0275element(23,"bocc-icon",14),i.\u0275\u0275elementStart(24,"div",12)(25,"a",15),i.\u0275\u0275text(26),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(27,"span",13),i.\u0275\u0275text(28," Ext. 15318 - 15311 "),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(29,"div",10),i.\u0275\u0275element(30,"bocc-icon",16),i.\u0275\u0275elementStart(31,"div",12)(32,"span",17),i.\u0275\u0275listener("click",function(){return W.onEmail()}),i.\u0275\u0275text(33," <EMAIL> "),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275elementStart(34,"div",10),i.\u0275\u0275element(35,"bocc-icon",18),i.\u0275\u0275elementStart(36,"div",12)(37,"span",13),i.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),i.\u0275\u0275elementEnd()()()()()()),2&A&&(i.\u0275\u0275advance(25),i.\u0275\u0275property("href",W.defenderLinePhone,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",W.defenderLineNumber," "))},dependencies:[c.CommonModule,_.Zl,_.Oh,a.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),w})(),F=(()=>{class w{constructor(){this.whatsappNumber=g._L.WHATSAPP}ngBoccPortal(A){}onClick(){(0,y.Gw)(g.BA.WHATSAPP)}}return w.\u0275fac=function(A){return new(A||w)},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",2)(4,"button",3),i.\u0275\u0275listener("click",function(){return W.onClick()}),i.\u0275\u0275elementStart(5,"span"),i.\u0275\u0275text(6),i.\u0275\u0275elementEnd()()()()),2&A&&(i.\u0275\u0275advance(6),i.\u0275\u0275textInterpolate(W.whatsappNumber))},dependencies:[c.CommonModule,_.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),w})();var N=o(10119),ie=(o(87677),o(68789)),ne=o(10455),le=o(91642),fe=o(10464),Me=o(75221),ee=o(88649),be=o(13043),he=o(38116),me=o(68819),ve=o(19310),Ie=o(94614),Te=(o(70957),o(91248),o(4663)),Ce=o(13961),_e=o(66709),ce=o(24495),te=o(57544),Pe=o(53113);class Oe extends te.FormGroup{constructor(){const K=new te.FormControl("",[ce.zf,ce.O_,ce.Y2,(0,ce.Mv)(24)]),A=new te.FormControl("",[ce.C1,ce.zf,ce.O_,ce.Y2,(0,ce.Mv)(24)]);super({controls:{description:A,reference:K}}),this.description=A,this.reference=K}setNote(K){this.description.setValue(K?.description),this.reference.setValue(K?.reference)}getNote(){return new Pe.$H(this.description.value,this.reference.value)}}function Le(w,K){if(1&w&&i.\u0275\u0275element(0,"bocc-input-box",7),2&w){const A=i.\u0275\u0275nextContext();i.\u0275\u0275property("formControl",A.formControls.reference)}}let ye=(()=>{class w{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new Oe}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(A){this.portal=A}}return w.\u0275fac=function(A){return new(A||w)},w.\u0275cmp=i.\u0275\u0275defineComponent({type:w,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(A,W){1&A&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3)(4,"div",4),i.\u0275\u0275text(5),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(6,"bocc-input-box",5),i.\u0275\u0275template(7,Le,1,1,"bocc-input-box",6),i.\u0275\u0275elementEnd()()),2&A&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("leftAction",W.cancelAction)("rightAction",W.saveAction),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",W.title," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("formControl",W.formControls.description),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",W.requiredReference))},dependencies:[c.CommonModule,c.NgIf,_.Jx,_.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),w})()},35324:(B,T,o)=>{o.d(T,{V:()=>p});var s=o(17007),e=o(30263),n=o(39904),d=o(87903),c=o(99877);function t(f,E){if(1&f){const i=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"a",9),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(i);const x=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(x.onWhatsapp())}),c.\u0275\u0275elementStart(1,"div",3),c.\u0275\u0275element(2,"bocc-icon",10),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",5)(4,"label",6),c.\u0275\u0275text(5," Whatsapp "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(6,"label",7),c.\u0275\u0275text(7),c.\u0275\u0275elementEnd()()()}if(2&f){const i=c.\u0275\u0275nextContext();c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",i.whatsappNumber," ")}}let p=(()=>{class f{constructor(){this.whatsapp=!1,this.whatsappNumber=n._L.WHATSAPP,this.nationalLineNumber=n._L.NATIONAL_LINE,this.bogotaLineNumber=n._L.BOGOTA_LINE,this.nationalLinePhone=n.WB.NATIONAL_LINE,this.bogotaLinePhone=n.WB.BOGOTA_LINE}onWhatsapp(){(0,d.Gw)(n.BA.WHATSAPP)}}return f.\u0275fac=function(i){return new(i||f)},f.\u0275cmp=c.\u0275\u0275defineComponent({type:f,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(i,u){1&i&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275template(1,t,8,1,"a",1),c.\u0275\u0275elementStart(2,"a",2)(3,"div",3),c.\u0275\u0275element(4,"bocc-icon",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"div",5)(6,"label",6),c.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(8,"label",7),c.\u0275\u0275text(9),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(10,"a",8)(11,"div",3),c.\u0275\u0275element(12,"bocc-icon",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(13,"div",5)(14,"label",6),c.\u0275\u0275text(15," Bogot\xe1 "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(16,"label",7),c.\u0275\u0275text(17),c.\u0275\u0275elementEnd()()()()),2&i&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",u.whatsapp),c.\u0275\u0275advance(1),c.\u0275\u0275property("href",u.nationalLinePhone,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",u.nationalLineNumber," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("href",u.bogotaLinePhone,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",u.bogotaLineNumber," "))},dependencies:[s.CommonModule,s.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),f})()},9593:(B,T,o)=>{o.d(T,{k:()=>t});var s=o(17007),e=o(30263),n=o(39904),d=o(95437),c=o(99877);let t=(()=>{class p{constructor(E){this.mboProvider=E,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(n.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(n.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(n.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return p.\u0275fac=function(E){return new(E||p)(c.\u0275\u0275directiveInject(d.ZL))},p.\u0275cmp=c.\u0275\u0275defineComponent({type:p,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(E,i){1&E&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),c.\u0275\u0275listener("click",function(){return i.onProducts()}),c.\u0275\u0275element(3,"bocc-icon",3),c.\u0275\u0275elementStart(4,"label",4),c.\u0275\u0275text(5," Productos "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(6,"div",5),c.\u0275\u0275listener("click",function(){return i.onTransfers()}),c.\u0275\u0275element(7,"bocc-icon",6),c.\u0275\u0275elementStart(8,"label",4),c.\u0275\u0275text(9," Transferir "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(10,"div",7),c.\u0275\u0275listener("click",function(){return i.onPaymentQR()}),c.\u0275\u0275elementStart(11,"div",8)(12,"div",9),c.\u0275\u0275element(13,"bocc-icon",10),c.\u0275\u0275elementEnd()(),c.\u0275\u0275element(14,"bocc-icon",11),c.\u0275\u0275elementStart(15,"label",4),c.\u0275\u0275text(16," Pago QR "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(17,"div",12),c.\u0275\u0275listener("click",function(){return i.onPayments()}),c.\u0275\u0275element(18,"bocc-icon",13),c.\u0275\u0275elementStart(19,"label",4),c.\u0275\u0275text(20," Pagar "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(21,"div",14),c.\u0275\u0275listener("click",function(){return i.onToken()}),c.\u0275\u0275element(22,"bocc-icon",15),c.\u0275\u0275elementStart(23,"label",4),c.\u0275\u0275text(24," Token "),c.\u0275\u0275elementEnd()()()()),2&E&&(c.\u0275\u0275advance(2),c.\u0275\u0275classProp("bocc-footer-form__element--active",i.isProducts),c.\u0275\u0275advance(4),c.\u0275\u0275classProp("bocc-footer-form__element--active",i.isTransfers),c.\u0275\u0275advance(11),c.\u0275\u0275classProp("bocc-footer-form__element--active",i.isPayments),c.\u0275\u0275advance(4),c.\u0275\u0275classProp("bocc-footer-form__element--active",i.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[s.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),p})()},83867:(B,T,o)=>{o.d(T,{o:()=>v});var s=o(17007),e=o(30263),n=o(8834),d=o(98699),p=(o(57544),o(99877));function E(C,g){if(1&C&&(p.\u0275\u0275elementStart(0,"label",11),p.\u0275\u0275text(1),p.\u0275\u0275elementEnd()),2&C){const y=p.\u0275\u0275nextContext();p.\u0275\u0275classProp("mbo-currency-box__rate--active",y.hasValue),p.\u0275\u0275advance(1),p.\u0275\u0275textInterpolate2(" ",y.valueFormat," ",y.rateCode," ")}}function i(C,g){if(1&C&&(p.\u0275\u0275elementStart(0,"div",12),p.\u0275\u0275element(1,"img",13),p.\u0275\u0275elementEnd()),2&C){const y=p.\u0275\u0275nextContext();p.\u0275\u0275advance(1),p.\u0275\u0275property("src",y.icon,p.\u0275\u0275sanitizeUrl)}}function u(C,g){if(1&C&&(p.\u0275\u0275elementStart(0,"div",14),p.\u0275\u0275text(1),p.\u0275\u0275elementEnd()),2&C){const y=p.\u0275\u0275nextContext();p.\u0275\u0275advance(1),p.\u0275\u0275textInterpolate1(" ",y.currencyCode," ")}}function x(C,g){if(1&C&&(p.\u0275\u0275elementStart(0,"div",15),p.\u0275\u0275element(1,"bocc-icon",16),p.\u0275\u0275elementStart(2,"span",17),p.\u0275\u0275text(3),p.\u0275\u0275elementEnd()()),2&C){const y=p.\u0275\u0275nextContext();p.\u0275\u0275advance(3),p.\u0275\u0275textInterpolate1(" ",null==y.formControl.error?null:y.formControl.error.message," ")}}function b(C,g){if(1&C&&(p.\u0275\u0275elementStart(0,"div",18),p.\u0275\u0275element(1,"bocc-icon",19),p.\u0275\u0275elementStart(2,"span",17),p.\u0275\u0275text(3),p.\u0275\u0275elementEnd()()),2&C){const y=p.\u0275\u0275nextContext();p.\u0275\u0275advance(3),p.\u0275\u0275textInterpolate1(" ",y.helperInfo," ")}}let v=(()=>{class C{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,d.itIsDefined)(this.rate)}get value(){const y=+this.formControl?.value;return isNaN(y)?0:this.hasRate?y/this.rate:0}get valueFormat(){return(0,n.b)({value:this.value,symbol:"$",decimals:!0})}}return C.\u0275fac=function(y){return new(y||C)},C.\u0275cmp=p.\u0275\u0275defineComponent({type:C,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(y,P){1&y&&(p.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),p.\u0275\u0275text(3),p.\u0275\u0275elementEnd(),p.\u0275\u0275template(4,E,2,4,"label",3),p.\u0275\u0275elementEnd(),p.\u0275\u0275elementStart(5,"div",4)(6,"div",5),p.\u0275\u0275template(7,i,2,1,"div",6),p.\u0275\u0275element(8,"bocc-currency-field",7),p.\u0275\u0275template(9,u,2,1,"div",8),p.\u0275\u0275elementEnd()(),p.\u0275\u0275template(10,x,4,1,"div",9),p.\u0275\u0275template(11,b,4,1,"div",10),p.\u0275\u0275elementEnd()),2&y&&(p.\u0275\u0275classProp("mbo-currency-box--focused",P.formControl.focused)("mbo-currency-box--error",P.formControl.invalid&&P.formControl.touched)("mbo-currency-box--disabled",P.formControl.disabled||P.disabled),p.\u0275\u0275advance(2),p.\u0275\u0275property("for",P.elementId),p.\u0275\u0275advance(1),p.\u0275\u0275textInterpolate1(" ",P.label," "),p.\u0275\u0275advance(1),p.\u0275\u0275property("ngIf",P.hasRate),p.\u0275\u0275advance(3),p.\u0275\u0275property("ngIf",P.icon),p.\u0275\u0275advance(1),p.\u0275\u0275property("elementId",P.elementId)("placeholder",P.placeholder)("disabled",P.disabled)("formControl",P.formControl),p.\u0275\u0275advance(1),p.\u0275\u0275property("ngIf",P.currencyCode),p.\u0275\u0275advance(1),p.\u0275\u0275property("ngIf",P.formControl.invalid&&P.formControl.touched),p.\u0275\u0275advance(1),p.\u0275\u0275property("ngIf",P.helperInfo&&!(P.formControl.invalid&&P.formControl.touched)))},dependencies:[s.CommonModule,s.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),C})()},85070:(B,T,o)=>{o.d(T,{f:()=>_});var s=o(17007),e=o(78506),n=o(99877);const c=["*"];let _=(()=>{class t{constructor(f){this.session=f}ngOnInit(){this.session.customer().then(f=>this.customer=f)}}return t.\u0275fac=function(f){return new(f||t)(n.\u0275\u0275directiveInject(e._I))},t.\u0275cmp=n.\u0275\u0275defineComponent({type:t,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(f,E){1&f&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"label",1),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",2),n.\u0275\u0275projection(4),n.\u0275\u0275elementEnd()()),2&f&&(n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==E.customer?null:E.customer.shortName))},dependencies:[s.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),t})()},65887:(B,T,o)=>{o.d(T,{X:()=>f});var s=o(17007),e=o(99877),d=o(30263),c=o(24495);function p(E,i){1&E&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}o(57544);let f=(()=>{class E{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[c.C1]:[]),this.unsubscription=this.documentType.subscribe(u=>{u&&(this.updateNumber(u,this.required),this.inputType=this.getInputType(u))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(u){if(u.required){const x=u.required.currentValue;this.documentType.setValidators(x?[c.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,x)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(u){return"PA"===u.code?"text":"number"}updateNumber(u,x){const b=this.validatorsForNumber(u,x);this.documentNumber.setValidators(b),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(u,x){return this.validatorsFromType(u).concat(x?[c.C1]:[])}maxLength(u){return x=>x&&x.length>u?{id:"maxLength",message:`Debe tener m\xe1ximo ${u} caracteres`}:null}validatorsFromType(u){switch(u.code){case"PA":return[c.JF];case"NIT":return[c.X1,this.maxLength(15)];default:return[c.X1]}}}return E.\u0275fac=function(u){return new(u||E)},E.\u0275cmp=e.\u0275\u0275defineComponent({type:E,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(u,x){1&u&&(e.\u0275\u0275template(0,p,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&u&&(e.\u0275\u0275property("ngIf",x.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",x.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",x.elementSelectId)("label",x.labelType)("suggestions",x.documents)("disabled",x.disabled)("formControl",x.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",x.elementInputId)("label",x.labelNumber)("type",x.inputType)("disabled",x.disabled)("formControl",x.documentNumber))},dependencies:[s.CommonModule,s.NgIf,d.DT,d.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),E})()},78021:(B,T,o)=>{o.d(T,{c:()=>E});var s=o(17007),e=o(30263),n=o(7603),d=o(98699),_=o(99877);function p(i,u){if(1&i){const x=_.\u0275\u0275getCurrentView();_.\u0275\u0275elementStart(0,"button",5),_.\u0275\u0275listener("click",function(){_.\u0275\u0275restoreView(x);const v=_.\u0275\u0275nextContext();return _.\u0275\u0275resetView(v.onAction(v.leftAction))}),_.\u0275\u0275elementStart(1,"span"),_.\u0275\u0275text(2),_.\u0275\u0275elementEnd()()}if(2&i){const x=_.\u0275\u0275nextContext();_.\u0275\u0275property("id",x.leftAction.id)("bocc-button",x.leftAction.type||"flat")("prefixIcon",x.leftAction.prefixIcon)("disabled",x.itIsDisabled(x.leftAction))("hidden",x.itIsHidden(x.leftAction)),_.\u0275\u0275advance(2),_.\u0275\u0275textInterpolate(x.leftAction.label)}}function f(i,u){if(1&i){const x=_.\u0275\u0275getCurrentView();_.\u0275\u0275elementStart(0,"button",6),_.\u0275\u0275listener("click",function(){const C=_.\u0275\u0275restoreView(x).$implicit,g=_.\u0275\u0275nextContext();return _.\u0275\u0275resetView(g.onAction(C))}),_.\u0275\u0275elementEnd()}if(2&i){const x=u.$implicit,b=_.\u0275\u0275nextContext();_.\u0275\u0275property("id",x.id)("type",x.type||"flat")("bocc-button-action",x.icon)("disabled",b.itIsDisabled(x))("hidden",b.itIsHidden(x))}}let E=(()=>{class i{constructor(x){this.utagService=x,this.rightActions=[]}itIsDisabled({disabled:x}){return(0,d.evalValueOrFunction)(x)}itIsHidden({hidden:x}){return(0,d.evalValueOrFunction)(x)}onAction(x){const{id:b}=x;b&&this.utagService.link("click",b),x.click()}}return i.\u0275fac=function(x){return new(x||i)(_.\u0275\u0275directiveInject(n.D))},i.\u0275cmp=_.\u0275\u0275defineComponent({type:i,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[_.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(x,b){1&x&&(_.\u0275\u0275elementStart(0,"div",0)(1,"div",1),_.\u0275\u0275template(2,p,3,6,"button",2),_.\u0275\u0275elementEnd(),_.\u0275\u0275elementStart(3,"div",3),_.\u0275\u0275template(4,f,1,5,"button",4),_.\u0275\u0275elementEnd()()),2&x&&(_.\u0275\u0275advance(2),_.\u0275\u0275property("ngIf",b.leftAction),_.\u0275\u0275advance(2),_.\u0275\u0275property("ngForOf",b.rightActions))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),i})()},27302:(B,T,o)=>{o.d(T,{Vc:()=>s.V,k4:()=>a.k,o2:()=>e.o,B_:()=>t,fi:()=>p.f,XH:()=>f.X,cN:()=>x.c,Aj:()=>b.A,J5:()=>k.J,Nu:()=>G,iF:()=>q});var s=o(35324),a=o(9593),e=o(83867),n=o(17007),d=o(99877);function _(R,F){if(1&R){const N=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"div",2),d.\u0275\u0275listener("click",function(){const ne=d.\u0275\u0275restoreView(N).$implicit,le=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(le.onClickCurrency(ne))}),d.\u0275\u0275elementStart(1,"div",3),d.\u0275\u0275element(2,"img",4)(3,"img",5),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"label",6),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()()}if(2&R){const N=F.$implicit,V=d.\u0275\u0275nextContext();d.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",V.isEnabled(N)),d.\u0275\u0275advance(2),d.\u0275\u0275property("src",N.enabledIcon,d.\u0275\u0275sanitizeUrl),d.\u0275\u0275advance(1),d.\u0275\u0275property("src",N.disabledIcon,d.\u0275\u0275sanitizeUrl),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate1(" ",N.label," ")}}o(57544);let t=(()=>{class R{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[N]=this.currencies;this.formControl.setValue(N)}}ngOnChanges(N){const{currencies:V}=N;if(V){const[ie]=V.currentValue;this.formControl&&this.formControl.setValue(ie)}}isEnabled(N){return N===this.formControl?.value}onClickCurrency(N){this.formControl&&!this.disabled&&this.formControl.setValue(N)}changeCurriencies(N){if(N.currencies){const V=N.currencies.currentValue,[ie]=V;this.formControl&&this.formControl.setValue(ie)}}}return R.\u0275fac=function(N){return new(N||R)},R.\u0275cmp=d.\u0275\u0275defineComponent({type:R,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[d.\u0275\u0275NgOnChangesFeature,d.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(N,V){1&N&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275template(1,_,6,5,"div",1),d.\u0275\u0275elementEnd()),2&N&&(d.\u0275\u0275classProp("mbo-currency-toggle--disabled",V.disabled),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngForOf",V.currencies))},dependencies:[n.CommonModule,n.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),R})();var p=o(85070),f=o(65887),E=o(30263),x=o(78021),b=o(50689),g=(o(7603),o(98699),o(72765)),k=o(88014);function z(R,F){if(1&R&&(d.\u0275\u0275elementStart(0,"div",4),d.\u0275\u0275element(1,"img",5),d.\u0275\u0275elementEnd()),2&R){const N=d.\u0275\u0275nextContext();d.\u0275\u0275advance(1),d.\u0275\u0275property("src",N.src,d.\u0275\u0275sanitizeUrl)}}const L=["*"];let G=(()=>{class R{}return R.\u0275fac=function(N){return new(N||R)},R.\u0275cmp=d.\u0275\u0275defineComponent({type:R,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],ngContentSelectors:L,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(N,V){1&N&&(d.\u0275\u0275projectionDef(),d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275template(1,z,2,1,"div",1),d.\u0275\u0275elementStart(2,"div",2)(3,"div",3),d.\u0275\u0275projection(4),d.\u0275\u0275elementEnd()()()),2&N&&(d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",V.src))},dependencies:[n.CommonModule,n.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),R})();var oe=o(24495);const j=/[A-Z]/,U=/[a-z]/,H=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,X=R=>R&&!j.test(R)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,re=R=>R&&!U.test(R)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,ue=R=>R&&!H.test(R)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let q=(()=>{class R{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([oe.C1,re,X,ue,(0,oe.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const N=this.formControl.errors.reduce((ie,{id:ne})=>[...ie,ne],[]),V=N.includes("required");this.smallInvalid=N.includes("smallCase")||V,this.capitalInvalid=N.includes("capitalCase")||V,this.specialCharInvalid=N.includes("specialChar")||V,this.minLengthInvalid=N.includes("minlength")||V})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return R.\u0275fac=function(N){return new(N||R)},R.\u0275cmp=d.\u0275\u0275defineComponent({type:R,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(N,V){1&N&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275element(1,"bocc-password-box",1),d.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),d.\u0275\u0275text(4," Min\xfascula "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(5,"mbo-poster",4),d.\u0275\u0275text(6," May\xfascula "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(7,"mbo-poster",5),d.\u0275\u0275text(8," Especial "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(9,"mbo-poster",6),d.\u0275\u0275text(10," Caracteres "),d.\u0275\u0275elementEnd()()()),2&N&&(d.\u0275\u0275advance(1),d.\u0275\u0275property("elementId",V.elementId)("disabled",V.disabled)("formControl",V.formControl),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",V.smallInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",V.capitalInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",V.specialCharInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",V.minLengthInvalid))},dependencies:[n.CommonModule,E.sC,g.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),R})()},50689:(B,T,o)=>{o.d(T,{A:()=>c});var s=o(17007),e=o(99877);const d=["*"];let c=(()=>{class _{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return _.\u0275fac=function(p){return new(p||_)},_.\u0275cmp=e.\u0275\u0275defineComponent({type:_,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(p,f){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&p&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",f.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[s.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),_})()},88014:(B,T,o)=>{o.d(T,{J:()=>d});var s=o(17007),e=o(99877);let d=(()=>{class c{}return c.\u0275fac=function(t){return new(t||c)},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(t,p){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[s.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),c})()},21498:(B,T,o)=>{o.d(T,{P:()=>i});var s=o(17007),e=o(30263),n=o(99877);function c(u,x){if(1&u&&n.\u0275\u0275element(0,"bocc-card-product-summary",7),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",b.information.product.color)("icon",b.information.product.icon)("number",b.information.product.number)("title",b.information.product.title)("subtitle",b.information.product.subtitle)}}function _(u,x){if(1&u&&n.\u0275\u0275element(0,"bocc-card-summary",8),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",b.information.standard.header)("title",b.information.standard.title)("subtitle",b.information.standard.subtitle)("detail",b.information.standard.detail)}}function t(u,x){if(1&u&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",b.information.amount.header)("amount",b.information.amount.value)("symbol",b.information.amount.symbol)("amountSmall",b.information.amount.small)}}function p(u,x){if(1&u&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",b.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(b.information.text.content)}}function f(u,x){if(1&u&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",12),n.\u0275\u0275element(1,"bocc-icon",13),n.\u0275\u0275elementStart(2,"span",14),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",15),n.\u0275\u0275elementStart(5,"span",14),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",b.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",b.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",b.information.datetime.time," ")}}function E(u,x){if(1&u&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&u){const b=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",b.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",b.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",b.information.badge.label," ")}}let i=(()=>{class u{}return u.\u0275fac=function(b){return new(b||u)},u.\u0275cmp=n.\u0275\u0275defineComponent({type:u,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(b,v){1&b&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,c,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,_,1,4,"bocc-card-summary",2),n.\u0275\u0275template(3,t,1,4,"bocc-card-summary",3),n.\u0275\u0275template(4,p,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,f,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,E,3,4,"bocc-card-summary",6),n.\u0275\u0275elementEnd()),2&b&&(n.\u0275\u0275property("ngSwitch",v.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[s.CommonModule,s.NgSwitch,s.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),u})()},7427:(B,T,o)=>{o.d(T,{x:()=>i});var s=o(17007),e=o(30263),n=o(87903),c=(o(29306),o(77279)),_=o(87956),t=o(68789),p=o(13961),f=o(99877);let i=(()=>{class u{constructor(b,v){this.eventBusService=b,this.onboardingScreenService=v,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,n.Bn)(this.product.tagAval),this.eventBusService.emit(c.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(p.Z)),this.tagAvalonboarding.open()}}return u.\u0275fac=function(b){return new(b||u)(f.\u0275\u0275directiveInject(_.Yd),f.\u0275\u0275directiveInject(t.x))},u.\u0275cmp=f.\u0275\u0275defineComponent({type:u,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[f.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(b,v){1&b&&(f.\u0275\u0275elementStart(0,"bocc-card-product",0),f.\u0275\u0275listener("key",function(){return v.onTagAval()})("onboarding",function(){return v.onBoarding()}),f.\u0275\u0275elementEnd()),2&b&&(f.\u0275\u0275classMap(v.product.bank.className),f.\u0275\u0275property("iconTitle",v.iconTitle)("title",v.product.nickname||v.product.name)("icon",v.product.logo)("tagAval",v.product.tagAvalFormat)("actions",v.actions)("color",v.product.color)("code",v.product.shortNumber)("label",v.product.label)("amount",v.product.amount)("incognito",v.incognito)("displayCard",!0)("statusLabel",null==v.product.status?null:v.product.status.label)("statusColor",null==v.product.status?null:v.product.status.color)("cromaline",!0)("msgError",v.msgError))},dependencies:[s.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),u})()},1027:(B,T,o)=>{o.d(T,{A:()=>b});var s=o(17007),a=o(72765),e=o(30263),n=o(99877);function d(v,C){if(1&v&&n.\u0275\u0275element(0,"bocc-card-product-summary",8),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",g.information.product.color)("icon",g.information.product.icon)("number",g.information.product.number)("title",g.information.product.title)("subtitle",g.information.product.subtitle)}}function c(v,C){if(1&v&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.standard.header)("title",g.information.standard.title)("subtitle",g.information.standard.subtitle)}}function _(v,C){if(1&v&&n.\u0275\u0275element(0,"bocc-card-summary",10),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.amount.header)("amount",g.information.amount.value)("symbol",g.information.amount.symbol)}}function t(v,C){if(1&v&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(g.information.text.content)}}function p(v,C){if(1&v&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",13),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",16),n.\u0275\u0275elementStart(5,"span",15),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",g.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",g.information.datetime.time," ")}}function f(v,C){if(1&v&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",17),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd()()),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.date.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",g.information.date.date," ")}}function E(v,C){if(1&v&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&v){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",g.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",g.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",g.information.badge.label," ")}}let i=(()=>{class v{}return v.\u0275fac=function(g){return new(g||v)},v.\u0275cmp=n.\u0275\u0275defineComponent({type:v,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(g,y){1&g&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,d,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,c,1,3,"bocc-card-summary",2),n.\u0275\u0275template(3,_,1,3,"bocc-card-summary",3),n.\u0275\u0275template(4,t,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,p,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,f,4,3,"bocc-card-summary",6),n.\u0275\u0275template(7,E,3,4,"bocc-card-summary",7),n.\u0275\u0275elementEnd()),2&g&&(n.\u0275\u0275property("ngSwitch",y.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","date"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[s.CommonModule,s.NgSwitch,s.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),v})();function u(v,C){1&v&&n.\u0275\u0275element(0,"mbo-card-information-element",8),2&v&&n.\u0275\u0275property("information",C.$implicit)}const x=["*"];let b=(()=>{class v{constructor(){this.skeleton=!1,this.informations=[]}}return v.\u0275fac=function(g){return new(g||v)},v.\u0275cmp=n.\u0275\u0275defineComponent({type:v,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:x,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(g,y){1&g&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"mbo-bank-logo",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"div",4),n.\u0275\u0275element(5,"div",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275projection(7),n.\u0275\u0275template(8,u,1,1,"mbo-card-information-element",7),n.\u0275\u0275elementEnd()()),2&g&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("result",!0),n.\u0275\u0275advance(5),n.\u0275\u0275property("ngForOf",y.informations))},dependencies:[s.CommonModule,s.NgForOf,a.rw,i],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),v})()},16442:(B,T,o)=>{o.d(T,{u:()=>g});var s=o(99877),e=o(17007),d=o(13462),_=o(19102),t=o(45542),p=o(65467),f=o(21498);function E(y,P){if(1&y&&(s.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),s.\u0275\u0275text(1),s.\u0275\u0275elementEnd()),2&y){const O=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",O.template.skeleton),s.\u0275\u0275property("secondary",!0)("active",O.template.skeleton),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",O.template.header.subtitle," ")}}function i(y,P){1&y&&s.\u0275\u0275element(0,"mbo-card-information",16),2&y&&s.\u0275\u0275property("information",P.$implicit)}function u(y,P){if(1&y&&(s.\u0275\u0275elementStart(0,"div",14),s.\u0275\u0275projection(1),s.\u0275\u0275template(2,i,1,1,"mbo-card-information",15),s.\u0275\u0275elementEnd()),2&y){const O=s.\u0275\u0275nextContext();s.\u0275\u0275advance(2),s.\u0275\u0275property("ngForOf",O.template.informations)}}function x(y,P){1&y&&(s.\u0275\u0275elementStart(0,"div",17),s.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),s.\u0275\u0275elementEnd()),2&y&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("active",!0),s.\u0275\u0275advance(1),s.\u0275\u0275property("active",!0)("secondary",!0),s.\u0275\u0275advance(1),s.\u0275\u0275property("active",!0)("secondary",!0))}function b(y,P){if(1&y){const O=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"button",23),s.\u0275\u0275listener("click",function(){const z=s.\u0275\u0275restoreView(O).$implicit,L=s.\u0275\u0275nextContext(2);return s.\u0275\u0275resetView(L.onAction(z))}),s.\u0275\u0275elementStart(1,"span"),s.\u0275\u0275text(2),s.\u0275\u0275elementEnd()()}if(2&y){const O=P.$implicit;s.\u0275\u0275property("bocc-button",O.type)("prefixIcon",O.prefixIcon),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate(O.label)}}function v(y,P){if(1&y&&(s.\u0275\u0275elementStart(0,"div",21),s.\u0275\u0275template(1,b,3,3,"button",22),s.\u0275\u0275elementEnd()),2&y){const O=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("ngForOf",O.template.actions)}}const C=["*"];let g=(()=>{class y{constructor(){this.disabled=!1,this.action=new s.EventEmitter}onAction({event:O}){this.action.emit(O)}}return y.\u0275fac=function(O){return new(O||y)},y.\u0275cmp=s.\u0275\u0275defineComponent({type:y,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:C,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(O,I){1&O&&(s.\u0275\u0275projectionDef(),s.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),s.\u0275\u0275element(3,"mbo-bank-logo",3),s.\u0275\u0275elementStart(4,"div",4),s.\u0275\u0275element(5,"ng-lottie",5),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),s.\u0275\u0275text(7),s.\u0275\u0275elementEnd(),s.\u0275\u0275template(8,E,2,5,"bocc-skeleton-text",7),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(9,"div",8),s.\u0275\u0275element(10,"div",9),s.\u0275\u0275elementEnd(),s.\u0275\u0275template(11,u,3,1,"div",10),s.\u0275\u0275template(12,x,4,5,"div",11),s.\u0275\u0275elementEnd(),s.\u0275\u0275template(13,v,2,1,"div",12)),2&O&&(s.\u0275\u0275classProp("animation",!I.template.skeleton),s.\u0275\u0275advance(3),s.\u0275\u0275property("result",!0),s.\u0275\u0275advance(2),s.\u0275\u0275property("options",I.template.header.animation),s.\u0275\u0275advance(1),s.\u0275\u0275property("active",I.template.skeleton),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",I.template.header.title," "),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.template.skeleton||I.template.header.subtitle),s.\u0275\u0275advance(3),s.\u0275\u0275property("ngIf",!I.template.skeleton),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.template.skeleton),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",I.template.actions.length&&!I.disabled))},dependencies:[e.NgForOf,e.NgIf,d.LottieComponent,_.r,t.P,p.D,f.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),y})()},10119:(B,T,o)=>{o.d(T,{N:()=>x});var s=o(17007),e=o(99877),d=o(30263),c=o(7603),_=o(98699);function p(b,v){if(1&b&&e.\u0275\u0275element(0,"bocc-diamond",14),2&b){const C=v.$implicit,g=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",g.itIsSelected(C))}}function f(b,v){if(1&b){const C=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(C);const y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onAction(y.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&b){const C=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",C.footerActionLeft.id)("bocc-button",C.footerActionLeft.type)("prefixIcon",C.footerActionLeft.prefixIcon)("disabled",C.itIsDisabled(C.footerActionLeft))("hidden",C.itIsHidden(C.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(C.footerActionLeft.label)}}function E(b,v){if(1&b){const C=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(C);const y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onAction(y.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&b){const C=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",C.footerActionRight.id)("bocc-button",C.footerActionRight.type)("prefixIcon",C.footerActionRight.prefixIcon)("disabled",C.itIsDisabled(C.footerActionRight))("hidden",C.itIsHidden(C.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(C.footerActionRight.label)}}const i=["*"];let x=(()=>{class b{constructor(C,g){this.ref=C,this.utagService=g,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((C,g)=>g),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(C){return C===this.currentPosition}itIsDisabled({disabled:C}){return(0,_.evalValueOrFunction)(C)}itIsHidden({hidden:C}){return(0,_.evalValueOrFunction)(C)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(C){const{id:g}=C;g&&this.utagService.link("click",g),C.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(C){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(C),this.automatic=!1,this.setTranslatePosition(C)}setTranslatePosition(C){this.translateX=C*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(C){this.transformContent=`translateX(${C}px)`}emitPosition(C){this.finished||(this.finished=C+1===this.elements.length),this.position.emit({position:C,finished:this.finished})}getPositionSlide(C){return C>=this.elements.length?this.elements.length-1:C<0?0:C}setTouchHandler(C){let g=0,y=0;C.addEventListener("touchstart",P=>{if(P.changedTouches.length){const{clientX:O}=P.changedTouches.item(0);g=0,this.touched=!0,y=O}}),C.addEventListener("touchmove",P=>{if(P.changedTouches.length){const O=P.changedTouches.item(0),I=O.clientX-y;y=O.clientX,this.translateX+=I,g+=I,this.setTranslateContent(this.translateX)}}),C.addEventListener("touchend",P=>{this.touched=!1,P.changedTouches.length&&(Math.abs(g)/this.widthBody*100>=40&&(g>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return b.\u0275fac=function(C){return new(C||b)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(c.D))},b.\u0275cmp=e.\u0275\u0275defineComponent({type:b,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:i,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(C,g){1&C&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return g.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return g.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,p,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return g.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,f,3,6,"button",13),e.\u0275\u0275template(16,E,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&C&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",g.headerActionLeft)("rightAction",g.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",g.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",g.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",g.widthContent)("transform",g.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",g.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!g.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",g.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!g.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",g.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.footerActionRight))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,d.P8,d.u1,d.ou,d.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),b})()},68789:(B,T,o)=>{o.d(T,{x:()=>c});var s=o(7603),a=o(10455),e=o(87677),n=o(99877);let c=(()=>{class _{constructor(p){this.portalService=p}information(){this.portal||(this.portal=this.portalService.container({component:a.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(p,f){return this.portalService.container({component:p,container:e.C,props:{container:f?.containerProps,component:f?.componentProps}})}}return _.\u0275fac=function(p){return new(p||_)(n.\u0275\u0275inject(s.v))},_.\u0275prov=n.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},87677:(B,T,o)=>{o.d(T,{C:()=>e});var s=o(99877);let e=(()=>{class n{constructor(c){this.ref=c,this.visible=!1,this.visibleChange=new s.EventEmitter}open(c=0){setTimeout(()=>{this.changeVisible(!0)},c)}close(c=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},c)}append(c){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(c)}ngBoccPortal(c){this.portal=c}changeVisible(c){this.visible=c,this.visibleChange.emit(c)}}return n.\u0275fac=function(c){return new(c||n)(s.\u0275\u0275directiveInject(s.ElementRef))},n.\u0275cmp=s.\u0275\u0275defineComponent({type:n,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(c,_){1&c&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275element(1,"div",1),s.\u0275\u0275elementEnd()),2&c&&s.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",_.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),n})()},10455:(B,T,o)=>{o.d(T,{E:()=>_});var s=o(17007),e=o(99877),d=o(27302),c=o(10119);let _=(()=>{class t{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(f){this.portal=f}onPosition({finished:f}){this.finished=f,f&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return t.\u0275fac=function(f){return new(f||t)},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(f,E){1&f&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(u){return E.onPosition(u)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&f&&e.\u0275\u0275property("footerActionLeft",E.footerLeft)("footerActionRight",E.footerRight)("gradient",!0)},dependencies:[s.CommonModule,d.Nu,c.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),t})()},91642:(B,T,o)=>{o.d(T,{D:()=>g});var s=o(17007),e=o(99877),d=o(30263),c=o(87542),_=o(70658),t=o(3372),p=o(87956),f=o(72765);function E(y,P){1&y&&e.\u0275\u0275element(0,"mbo-bank-logo")}function i(y,P){1&y&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function u(y,P){if(1&y&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&y){const O=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",O.verifying)}}function x(y,P){1&y&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function b(y,P){if(1&y){const O=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(O);const k=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(k.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&y){const O=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",O.verifying)}}const v=["*"],{OtpInputSuperuser:C}=t.M;let g=(()=>{class y{constructor(O,I,k,z){this.ref=O,this.otpService=I,this.deviceService=k,this.preferencesService=z,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=c.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new c.yV}ngOnInit(){this.otpService.onCode(O=>{this.otpControls.setCode(O),this.otpControls.valid&&this.onAutocomplete(O)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(O){const{documentNumber:I}=O;I&&this.preferencesService.applyFunctionality(C,I.currentValue).then(k=>{this.itIsDocumentSuperuser=k})}get otpVisible(){return!_.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return _.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&_.N.otpReadonlyMobile}onAutocomplete(O){this.code.emit(O)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return y.\u0275fac=function(O){return new(O||y)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(p.no),e.\u0275\u0275directiveInject(p.U8),e.\u0275\u0275directiveInject(p.yW))},y.\u0275cmp=e.\u0275\u0275defineComponent({type:y,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:v,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(O,I){1&O&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,E,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,i,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(z){return I.onAutocomplete(z)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,u,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,x,7,0,"div",8),e.\u0275\u0275template(13,b,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&O&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",I.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",I.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",I.otpVisible),e.\u0275\u0275property("formControls",I.otpControls)("readonly",I.otpReadonly)("mobile",I.otpMobile)("disabled",I.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",I.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",I.isIos))},dependencies:[s.CommonModule,s.NgIf,d.P8,d.Yx,f.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),y})()},10464:(B,T,o)=>{o.d(T,{K:()=>_});var s=o(17007),e=o(99877),d=o(22816);const c=["*"];let _=(()=>{class t{constructor(f){this.ref=f,this.scroller=new d.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(f){this.scroller.reset(f.target)}}return t.\u0275fac=function(f){return new(f||t)(e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(f,E){1&f&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(u){return E.onScroll(u)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&f&&e.\u0275\u0275classProp("mbo-page__content--start",E.scrollStart)("mbo-page__content--end",E.scrollEnd)},dependencies:[s.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),t})()},75221:(B,T,o)=>{o.d(T,{u:()=>t});var s=o(17007),e=o(30263),n=o(27302),c=(o(88649),o(99877));let t=(()=>{class p{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return p.\u0275fac=function(E){return new(E||p)},p.\u0275cmp=c.\u0275\u0275defineComponent({type:p,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(E,i){1&E&&c.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&E&&(c.\u0275\u0275property("formControl",i.passwordControl.controls.password)("disabled",i.disabled)("elementId",i.elementPasswordId),c.\u0275\u0275advance(1),c.\u0275\u0275property("elementId",i.elementConfirmId)("disabled",i.disabled)("formControl",i.passwordControl.controls.repeatPassword))},dependencies:[s.CommonModule,e.sC,n.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),p})()},88649:(B,T,o)=>{o.d(T,{z:()=>n});var s=o(57544),a=o(24495);class n extends s.FormGroup{constructor(){const c=new s.FormControl(""),_=new s.FormControl("",[a.C1,(d=c,c=>c&&c!==d.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var d;super({controls:{password:c,repeatPassword:_}})}get password(){return this.controls.password.value}}},13043:(B,T,o)=>{o.d(T,{e:()=>b});var s=o(17007),e=o(99877),d=o(30263),t=(o(57544),o(27302));function p(v,C){1&v&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function f(v,C){if(1&v&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&v){const g=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.title," ")}}function E(v,C){if(1&v){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const O=e.\u0275\u0275restoreView(g).$implicit,I=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(I.onProduct(O))}),e.\u0275\u0275elementEnd()}if(2&v){const g=C.$implicit,y=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",g.color)("icon",g.logo)("title",g.nickname)("number",g.publicNumber)("detail",g.bank.name)("ghost",y.ghost)}}function i(v,C){if(1&v&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,f,2,1,"div",8),e.\u0275\u0275template(3,E,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&v){const g=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",g.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",g.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!g.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.msgError," ")}}function u(v,C){1&v&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&v&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const x=["*"];let b=(()=>{class v{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(g){return this.productControl?.value?.id===g.id}onProduct(g){this.select.emit(g),this.productControl?.setValue(g)}}return v.\u0275fac=function(g){return new(g||v)},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:x,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(g,y){1&g&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,p,3,0,"div",1),e.\u0275\u0275template(2,i,6,5,"div",2),e.\u0275\u0275template(3,u,3,2,"div",3),e.\u0275\u0275elementEnd()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",y.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!y.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",y.skeleton))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,d.w_,t.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),v})()},38116:(B,T,o)=>{o.d(T,{Z:()=>_});var s=o(17007),e=o(99877),d=o(30263);function c(t,p){if(1&t){const f=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const u=e.\u0275\u0275restoreView(f).$implicit,x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onAction(u))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&t){const f=p.$implicit,E=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(E.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",E.itIsDisabled(f)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(E.theme(f)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",f.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",f.label," ")}}let _=(()=>{class t{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(f){return f.requiredInformation&&f.errorInformation}theme(f){return this.itIsDisabled(f)?"none":f.theme}onAction(f){!this.itIsDisabled(f)&&this.action.emit(f.type)}}return t.\u0275fac=function(f){return new(f||t)},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(f,E){1&f&&e.\u0275\u0275template(0,c,6,8,"div",0),2&f&&e.\u0275\u0275property("ngForOf",E.actions)},dependencies:[s.CommonModule,s.NgForOf,d.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),t})()},68819:(B,T,o)=>{o.d(T,{w:()=>L});var s=o(17007),e=o(99877),d=o(30263),c=o(39904),p=(o(57544),o(78506)),E=(o(29306),o(87903)),i=o(95437),u=o(27302),x=o(70957),b=o(91248),v=o(13961),C=o(68789),g=o(33395),y=o(25317);function P(G,oe){if(1&G){const j=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(H){e.\u0275\u0275restoreView(j);const X=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(X.onBoarding(H))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(j);const H=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(H.onCopyKey(H.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(j);const H=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(H.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&G){const j=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",j.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",j.product.tagAval)}}function O(G,oe){if(1&G&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&G){const j=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",j.digitalSection)("currencyCode",null==j.currencyControl.value?null:j.currencyControl.value.code)("hidden",j.itIsVisibleMovements)}}function I(G,oe){if(1&G&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&G){const j=oe.$implicit,U=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",j)("currencyCode",null==U.currencyControl.value?null:U.currencyControl.value.code)}}const k=[[["","header",""]],"*"],z=["[header]","*"];let L=(()=>{class G{constructor(j,U,H){this.mboProvider=j,this.managerInformation=U,this.onboardingScreenService=H,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(j){const{movements:U,sections:H}=j;U&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!U.currentValue),H&&this.product&&this.refreshComponent(this.product,H.currentValue),this.managerInformation.requestInfoBody().then(X=>{X.when({success:({canEditTagAval:re})=>{this.canEditTagAval=re}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(j){(0,E.Bn)(j),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(c.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(j,U){const H=(0,E.A2)(j);if(this.sectionPosition=0,U?.length){const X=U.map(({title:re},ue)=>({label:re,value:ue}));H&&(this.headerMovements.value=this.sections.length,X.push(this.headerMovements)),this.headers=X}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const X=[{label:"Error",value:1}];H&&X.unshift(this.headerMovements),this.headers=X}}onBoarding(j){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(v.Z)),this.tagAvalonboarding.open(),j.stopPropagation()}}return G.\u0275fac=function(j){return new(j||G)(e.\u0275\u0275directiveInject(i.ZL),e.\u0275\u0275directiveInject(p.vu),e.\u0275\u0275directiveInject(C.x))},G.\u0275cmp=e.\u0275\u0275defineComponent({type:G,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:z,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(j,U){1&j&&(e.\u0275\u0275projectionDef(k),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(X){return U.sectionPosition=X}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,P,15,2,"div",4),e.\u0275\u0275template(6,O,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,I,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&j&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",U.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",U.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",U.headers)("value",U.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",U.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==U.product?null:U.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",U.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",U.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",U.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",U.movements)("header",U.header)("product",U.product)("currencyCode",null==U.currencyControl.value?null:U.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",U.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!U.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,d.Gf,d.qw,d.P8,d.Dj,d.qd,x.K,b.I,u.Aj,g.kW,y.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),G})()},19310:(B,T,o)=>{o.d(T,{$:()=>O});var s=o(17007),a=o(99877),e=o(30263),n=o(87903);let d=(()=>{class I{transform(z,L,G=" "){return(0,n.rd)(z,L,G)}}return I.\u0275fac=function(z){return new(z||I)},I.\u0275pipe=a.\u0275\u0275definePipe({name:"codeSplit",type:I,pure:!0}),I})(),c=(()=>{class I{}return I.\u0275fac=function(z){return new(z||I)},I.\u0275mod=a.\u0275\u0275defineNgModule({type:I}),I.\u0275inj=a.\u0275\u0275defineInjector({imports:[s.CommonModule]}),I})();o(57544);var t=o(70658),p=o(78506),E=(o(29306),o(87956)),i=o(72765),u=o(27302);function x(I,k){if(1&I){const z=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",18),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(z);const G=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(G.onDigital())}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275property("prefixIcon",z.digitalIcon),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate(z.digitalIncognito?"Ver datos":"Ocultar datos")}}function b(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"span"),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate(null==z.product?null:z.product.publicNumber)}}function v(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"span",19),a.\u0275\u0275text(1),a.\u0275\u0275pipe(2,"codeSplit"),a.\u0275\u0275elementEnd()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",a.\u0275\u0275pipeBind2(2,1,z.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":z.digitalNumber,4)," ")}}function C(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"bocc-badge",20),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275attribute("bocc-theme",null==z.product||null==z.product.status?null:z.product.status.color),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",null==z.product||null==z.product.status?null:z.product.status.label," ")}}function g(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"div",21),a.\u0275\u0275element(1,"bocc-progress-bar",22),a.\u0275\u0275elementEnd()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("theme",z.progressBarTheme)("width",z.progressBarStatus)}}function y(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"div",23)(1,"label",24),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),a.\u0275\u0275element(5,"bocc-amount",26),a.\u0275\u0275elementEnd(),a.\u0275\u0275element(6,"mbo-button-incognito-mode",27),a.\u0275\u0275elementEnd()()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate1(" ",null==z.product?null:z.product.label," "),a.\u0275\u0275advance(2),a.\u0275\u0275property("active",!z.product),a.\u0275\u0275advance(1),a.\u0275\u0275property("amount",null==z.product?null:z.product.amount)("incognito",z.incognito),a.\u0275\u0275advance(1),a.\u0275\u0275property("actionMode",!0)("hidden",!z.product)}}function P(I,k){if(1&I&&(a.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),a.\u0275\u0275text(3,"Vence"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(4,"span",19),a.\u0275\u0275text(5),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(6,"div",29)(7,"label",20),a.\u0275\u0275text(8,"CVC"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(9,"span",19),a.\u0275\u0275text(10),a.\u0275\u0275elementEnd()()()),2&I){const z=a.\u0275\u0275nextContext();a.\u0275\u0275advance(5),a.\u0275\u0275textInterpolate1(" ",z.digitalIncognito?"\u2022\u2022 | \u2022\u2022":z.digitalExpAt," "),a.\u0275\u0275advance(5),a.\u0275\u0275textInterpolate1(" ",z.digitalIncognito?"\u2022\u2022\u2022":z.digitalCVC," ")}}let O=(()=>{class I{constructor(z,L){this.managerPreferences=z,this.digitalService=L,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new a.EventEmitter,this.digital=new a.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:z})=>{this.incognito=z})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:z,value:L})=>{this.product&&this.product.id===z&&this.refreshDigitalState(L)}))}ngOnChanges(z){const{product:L}=z;if(L&&L.currentValue){const G=L.currentValue;this.refreshDigitalState(this.digitalService.request(G.id)),this.activateDigitalCountdown(G)}}ngOnDestroy(){this.unsubscriptions.forEach(z=>z())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(z){const{incognito:L,requiredRequest:G,cvc:oe,expirationAt:j,number:U}=z;this.digitalIncognito=L,this.digitalExpAt=j,this.digitalCVC=oe,this.digitalNumber=U,this.digitalIcon=L?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=G}activateDigitalCountdown(z){const{countdown$:L}=this.digitalService.request(z.id);L?(this.progressBarRequired=!0,this.progressBarPercent=100,L.subscribe(G=>{this.progressBarRequired=!(G>=t.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-G/t.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return I.\u0275fac=function(z){return new(z||I)(a.\u0275\u0275directiveInject(p.Bx),a.\u0275\u0275directiveInject(E.ZP))},I.\u0275cmp=a.\u0275\u0275defineComponent({type:I,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[a.\u0275\u0275NgOnChangesFeature,a.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(z,L){1&z&&(a.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),a.\u0275\u0275listener("click",function(){return L.onClose()}),a.\u0275\u0275elementStart(3,"span"),a.\u0275\u0275text(4,"Atr\xe1s"),a.\u0275\u0275elementEnd()(),a.\u0275\u0275element(5,"mbo-currency-toggle",3),a.\u0275\u0275template(6,x,3,2,"button",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(7,"div",5)(8,"div",6),a.\u0275\u0275element(9,"img",7),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),a.\u0275\u0275text(12),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),a.\u0275\u0275template(15,b,2,1,"span",12),a.\u0275\u0275template(16,v,3,4,"span",13),a.\u0275\u0275template(17,C,2,2,"bocc-badge",14),a.\u0275\u0275elementEnd()(),a.\u0275\u0275template(18,g,2,2,"div",15),a.\u0275\u0275elementEnd()(),a.\u0275\u0275template(19,y,7,6,"div",16),a.\u0275\u0275template(20,P,11,2,"div",17),a.\u0275\u0275elementEnd()),2&z&&(a.\u0275\u0275classMap(null==L.product?null:L.product.bank.className),a.\u0275\u0275property("color",null==L.product?null:L.product.color),a.\u0275\u0275advance(5),a.\u0275\u0275property("formControl",L.currencyControl)("currencies",L.currencies)("hidden",!(null!=L.product&&L.product.bank.isOccidente)||L.currencies.length<2),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital),a.\u0275\u0275advance(3),a.\u0275\u0275property("src",null==L.product?null:L.product.logo,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(2),a.\u0275\u0275property("active",!L.product),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",(null==L.product?null:L.product.nickname)||(null==L.product?null:L.product.name)," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!L.product),a.\u0275\u0275advance(1),a.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!L.product),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",!(null!=L.product&&L.product.isDigital)),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",(null==L.product||null==L.product.status?null:L.product.status.label)&&L.digitalIncognito),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",L.progressBarRequired),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",!(null!=L.product&&L.product.isDigital)),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",null==L.product?null:L.product.isDigital))},dependencies:[s.CommonModule,s.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,c,d,i.uf,u.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),I})()},94614:(B,T,o)=>{o.d(T,{K:()=>p});var s=o(17007),e=o(30263),n=o(39904),c=(o(29306),o(95437)),_=o(99877);let p=(()=>{class f{constructor(i){this.mboProvider=i,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:i,id:u,parentProduct:x}=this.product;"covered"===i&&x?this.goToPage(x.id,u):this.goToPage(u)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(i,u){this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:i,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:u})}}return f.\u0275fac=function(i){return new(i||f)(_.\u0275\u0275directiveInject(c.ZL))},f.\u0275cmp=_.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[_.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(i,u){1&i&&(_.\u0275\u0275elementStart(0,"div",0),_.\u0275\u0275listener("click",function(){return u.onComponent()}),_.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),_.\u0275\u0275text(3),_.\u0275\u0275elementEnd(),_.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),_.\u0275\u0275text(5),_.\u0275\u0275elementEnd()(),_.\u0275\u0275elementStart(6,"div",4),_.\u0275\u0275element(7,"bocc-amount",5),_.\u0275\u0275elementEnd()()),2&i&&(_.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",u.skeleton),_.\u0275\u0275advance(2),_.\u0275\u0275property("active",u.skeleton),_.\u0275\u0275advance(1),_.\u0275\u0275textInterpolate1(" ",null==u.movement?null:u.movement.dateFormat," "),_.\u0275\u0275advance(1),_.\u0275\u0275property("active",u.skeleton),_.\u0275\u0275advance(1),_.\u0275\u0275textInterpolate1(" ",null==u.movement?null:u.movement.description," "),_.\u0275\u0275advance(1),_.\u0275\u0275property("hidden",u.skeleton),_.\u0275\u0275advance(1),_.\u0275\u0275property("amount",null==u.movement?null:u.movement.value)("currencyCode",null==u.movement?null:u.movement.currencyCode)("theme",!0))},dependencies:[s.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),f})()},70957:(B,T,o)=>{o.d(T,{K:()=>g});var s=o(15861),a=o(17007),n=o(99877),c=o(30263),_=o(78506),t=o(39904),f=(o(29306),o(87903)),E=o(95437),i=o(27302),u=o(94614);function x(y,P){if(1&y){const O=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",8),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(O);const k=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(k.onRedirectAll())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Ver todos"),n.\u0275\u0275elementEnd()()}}function b(y,P){if(1&y&&(n.\u0275\u0275elementStart(0,"div",5)(1,"label",6),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(3,x,3,0,"button",7),n.\u0275\u0275elementEnd()),2&y){const O=n.\u0275\u0275nextContext();n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",(null==O.productMovements||null==O.productMovements.range?null:O.productMovements.range.label)||"Sin resultados"," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==O.productMovements?null:O.productMovements.range)}}function v(y,P){if(1&y&&n.\u0275\u0275element(0,"mbo-product-info-movement",9),2&y){const O=P.$implicit,I=n.\u0275\u0275nextContext();n.\u0275\u0275property("movement",O)("product",I.product)}}function C(y,P){if(1&y&&(n.\u0275\u0275elementStart(0,"mbo-message-empty",10),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&y){const O=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",O.msgError," ")}}let g=(()=>{class y{constructor(O,I){this.mboProvider=O,this.managerProductMovements=I,this.header=!0,this.requesting=!1}ngOnChanges(O){const{currencyCode:I,product:k}=O;if(!this.movements&&(k||I)){const z=I?.currentValue||this.currencyCode,L=k?.currentValue||this.product;this.currentMovements=void 0,(0,f.A2)(L)&&this.requestFirstPage(L,z)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:O,id:I,parentProduct:k}=this.product;this.mboProvider.navigation.next(t.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===O?{productId:k?.id,coveredCardId:I}:{productId:I},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(O,I){var k=this;return(0,s.Z)(function*(){k.requesting=!0,(yield k.managerProductMovements.requestForProduct({product:O,currencyCode:I})).when({success:z=>{k.currentMovements=z},failure:()=>{k.currentMovements=void 0}},()=>{k.requesting=!1})})()}}return y.\u0275fac=function(O){return new(O||y)(n.\u0275\u0275directiveInject(E.ZL),n.\u0275\u0275directiveInject(_.sy))},y.\u0275cmp=n.\u0275\u0275defineComponent({type:y,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[n.\u0275\u0275NgOnChangesFeature,n.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(O,I){1&O&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,b,4,2,"div",1),n.\u0275\u0275elementStart(2,"div",2),n.\u0275\u0275template(3,v,1,2,"mbo-product-info-movement",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(4,C,2,1,"mbo-message-empty",4),n.\u0275\u0275elementEnd()),2&O&&(n.\u0275\u0275property("hidden",I.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",I.header),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",null==I.productMovements?null:I.productMovements.firstPage),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==I.productMovements?null:I.productMovements.isEmpty))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,c.P8,u.K,i.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),y})()},91248:(B,T,o)=>{o.d(T,{I:()=>f});var s=o(17007),e=o(30263),n=o(99877);function c(E,i){if(1&E&&n.\u0275\u0275element(0,"bocc-amount",10),2&E){const u=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("amount",u.value)("currencyCode",u.currencyCode)}}function _(E,i){if(1&E&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&E){const u=n.\u0275\u0275nextContext().$implicit,x=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",x.incognito||x.section.incognito?u.mask:u.value," ")}}function t(E,i){if(1&E){const u=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",11),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(u);const b=n.\u0275\u0275nextContext().$implicit;return n.\u0275\u0275resetView(b.action.click())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()}if(2&E){const u=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("suffixIcon",u.action.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(u.action.label)}}function p(E,i){if(1&E&&(n.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"label",6),n.\u0275\u0275template(5,c,1,2,"bocc-amount",7),n.\u0275\u0275template(6,_,2,1,"span",8),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(7,t,3,2,"button",9),n.\u0275\u0275elementEnd()),2&E){const u=i.$implicit,x=n.\u0275\u0275nextContext();n.\u0275\u0275property("hidden",(null==u?null:u.currencyCode)!==x.currencyCode),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",u.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",u.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!u.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",u.action&&!(x.incognito||x.section.incognito))}}let f=(()=>{class E{constructor(){this.currencyCode="COP"}}return E.\u0275fac=function(u){return new(u||E)},E.\u0275cmp=n.\u0275\u0275defineComponent({type:E,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(u,x){1&u&&(n.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),n.\u0275\u0275template(2,p,8,5,"li",2),n.\u0275\u0275elementEnd()()),2&u&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",x.section.datas))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),E})()},4663:(B,T,o)=>{o.d(T,{c:()=>i});var s=o(17007),e=o(99877),d=o(30263),c=o(27302);function _(u,x){1&u&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function t(u,x){if(1&u&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&u){const b=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",b.title," ")}}function p(u,x){if(1&u){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const g=e.\u0275\u0275restoreView(b).$implicit,y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onProduct(g))}),e.\u0275\u0275elementEnd()}if(2&u){const b=x.$implicit,v=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",b.color)("icon",b.logo)("title",b.nickname)("number",b.publicNumber)("ghost",v.ghost)("amount",b.amount)("tagAval",b.tagAvalFormat),e.\u0275\u0275attribute("amount-status",v.amountColorProduct(b))}}function f(u,x){1&u&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const E=["*"];let i=(()=>{class u{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(b){return b.amount>0?"success":b.amount<0?"danger":"empty"}onProduct(b){this.select.emit(b)}}return u.\u0275fac=function(b){return new(b||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:E,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(b,v){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,_,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,t,2,1,"div",5),e.\u0275\u0275template(8,p,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,f,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&b&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",v.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",v.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!v.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!v.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,d.w_,d.P8,c.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),u})()},13961:(B,T,o)=>{o.d(T,{Z:()=>_});var s=o(17007),e=o(27302),n=o(10119),d=o(99877);let _=(()=>{class t{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(f){this.portal=f}}return t.\u0275fac=function(f){return new(f||t)},t.\u0275cmp=d.\u0275\u0275defineComponent({type:t,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(f,E){1&f&&(d.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),d.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"p"),d.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),d.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(9,"p"),d.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),d.\u0275\u0275elementEnd()()()),2&f&&d.\u0275\u0275property("headerActionRight",E.headerAction)("gradient",!0)},dependencies:[s.CommonModule,e.Nu,n.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),t})()},66709:(B,T,o)=>{o.d(T,{s:()=>_});var s=o(17007),a=o(99877),e=o(30263),n=o(87542);let d=(()=>{class t{ngBoccPortal(f){}}return t.\u0275fac=function(f){return new(f||t)},t.\u0275cmp=a.\u0275\u0275defineComponent({type:t,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(f,E){1&f&&(a.\u0275\u0275elementStart(0,"div",0)(1,"label",1),a.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),a.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),a.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(11,"p",4),a.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),a.\u0275\u0275element(13,"br"),a.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),a.\u0275\u0275element(15,"br"),a.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),a.\u0275\u0275elementStart(17,"span"),a.\u0275\u0275text(18,">"),a.\u0275\u0275elementEnd(),a.\u0275\u0275text(19," Configuraci\xf3n "),a.\u0275\u0275elementStart(20,"span"),a.\u0275\u0275text(21,">"),a.\u0275\u0275elementEnd(),a.\u0275\u0275text(22," Seguridad "),a.\u0275\u0275elementStart(23,"span"),a.\u0275\u0275text(24,">"),a.\u0275\u0275elementEnd(),a.\u0275\u0275text(25," Activar Token Mobile."),a.\u0275\u0275element(26,"br"),a.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),a.\u0275\u0275elementEnd()()()())},dependencies:[s.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),t})();const c=["*"];let _=(()=>{class t{constructor(f,E){this.ref=f,this.bottomSheetService=E,this.verifying=!1,this.tokenLength=n.Xi,this.code=new a.EventEmitter,this.tokenControls=new n.b2}ngOnInit(){const f=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(d),setTimeout(()=>{f?.focus()},120)}onAutocomplete(f){this.code.emit(f)}onInfo(){this.infoSheet?.open()}}return t.\u0275fac=function(f){return new(f||t)(a.\u0275\u0275directiveInject(a.ElementRef),a.\u0275\u0275directiveInject(e.fG))},t.\u0275cmp=a.\u0275\u0275defineComponent({type:t,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(f,E){1&f&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275projection(2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"p",2),a.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(5,"p",2),a.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),a.\u0275\u0275elementStart(7,"a"),a.\u0275\u0275text(8),a.\u0275\u0275elementEnd(),a.\u0275\u0275text(9,". "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(10,"div",3),a.\u0275\u0275element(11,"img",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),a.\u0275\u0275listener("autocomplete",function(u){return E.onAutocomplete(u)}),a.\u0275\u0275text(13," Ingresa tu clave "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(14,"button",6),a.\u0275\u0275listener("click",function(){return E.onInfo()}),a.\u0275\u0275elementStart(15,"span"),a.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),a.\u0275\u0275elementEnd()()()),2&f&&(a.\u0275\u0275advance(8),a.\u0275\u0275textInterpolate1("",E.tokenLength," d\xedgitos"),a.\u0275\u0275advance(4),a.\u0275\u0275property("disabled",E.verifying)("formControls",E.tokenControls),a.\u0275\u0275advance(2),a.\u0275\u0275property("disabled",E.verifying))},dependencies:[s.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),t})()},88844:(B,T,o)=>{o.d(T,{YI:()=>t,tc:()=>v,iR:()=>b,jq:()=>f,Hv:()=>_,S6:()=>E,E2:()=>u,V4:()=>x,wp:()=>ue,CE:()=>O,YQ:()=>e,ND:()=>d,t1:()=>g});var s=o(6472);class a{constructor(R){this.value=R}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:R}){return this.value.id===R}filtrable(R){return(0,s.hasPattern)(this.value.name,R)}}function e(q){return q.map(R=>new a(R))}class n{constructor(R){this.currency=R}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(R){return this.currency.code===R?.code}filtrable(R){return!0}}function d(q){return q.map(R=>new n(R))}var c=o(39904);class _{constructor(R){this.value=R}get title(){return this.value.label}get description(){return this.value.label}compareTo(R){return this.value.reference===R.reference}filtrable(R){return!0}}const t=c.Bf.map(q=>new _(q));class p{constructor(R,F){this.value=R,this.title=this.value.label,this.description=F?this.value.code:this.value.label}compareTo(R){return this.value===R}filtrable(R){return!0}}const f=new p(c.Gd),E=new p(c.XU),i=new p(c.t$),u=new p(c.j1),x=new p(c.k7),b=[f,E,i,u],v=[new p(c.Gd,!0),new p(c.XU,!0),new p(c.t$,!0),new p(c.j1,!0)];class C{constructor(R){this.product=R}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(R){return this.product.id===R?.id}filtrable(R){return!0}}function g(q){return q.map(R=>new C(R))}var y=o(89148);class P{constructor(R){this.filter=R}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(R){return this.value===R}filtrable(R){return!0}}const O=new P({label:"Todos los productos",short:"Todos",value:y.Gt.None}),I=new P({label:"Cuentas de ahorro",short:"Ahorros",value:y.Gt.SavingAccount}),k=new P({label:"Cuentas corriente",short:"Corrientes",value:y.Gt.CheckingAccount}),z=new P({label:"Depositos electr\xf3nicos",short:"Depositos",value:y.Gt.ElectronicDeposit}),L=new P({label:"Cuentas AFC",short:"AFC",value:y.Gt.AfcAccount}),G=new P({label:"Tarjetas de cr\xe9dito",short:"TC",value:y.Gt.CreditCard}),oe=new P({label:"Inversiones",short:"Inversiones",value:y.Gt.CdtAccount}),j=new P({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:y.Gt.Loan}),U=new P({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:y.Gt.ResolvingCredit}),H=new P({label:"Productos Aval",short:"Aval",value:y.Gt.Aval}),X=new P({label:"Productos fiduciarios",short:"Fiducias",value:y.Gt.Trustfund}),re=new P({label:"Otros productos",short:"Otros",value:y.Gt.None}),ue={SDA:I,DDA:k,EDA:z,AFC:L,CCA:G,CDA:oe,DLA:j,LOC:U,AVAL:H,80:X,MDA:re,NONE:re,SBA:re,VDA:re}},22816:(B,T,o)=>{o.d(T,{S:()=>s});class s{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);