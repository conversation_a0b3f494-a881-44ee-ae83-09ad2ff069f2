(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3899],{57929:(G,p,a)=>{a.d(p,{Be:()=>B,q3:()=>M,qQ:()=>A});const M=36,A=0,B=[{code:"0002015502010102125802CO5921DIEGO ANDRES CORREDOR49250103RBM0014CO.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124O2dhtQbToI1IP7xYOHkR1SUm0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099360041740013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064310002ES0121DIEGO ANDRES CORREDOR54061200006304C1DE",name:"DIEGO ANDRES CORREDOR",type:"onlyAccount"},{code:"000201550202010211560105802CO5922ALMACEN Y SASTRERIA *******************.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124bAK0LiWIA7a7GGPS3ZTGmmCv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601315238 DUITAMA8223010100014CO.COM.RBM.IVA503001099100104110013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573154033178070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122ALMACEN Y SASTRERIA IN6304EA05",name:"ALMACEN Y SASTRERIA IN",type:"onlyAccount"},{code:"000201550202010211560105802CO5917SACOS AZULES BOCC49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124cJbE3StP8jyiME/aY+8d/Qo80014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601505664 SAN PEDRO8223010100014CO.COM.RBM.IVA503001099353177830013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573108133170070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064270002ES0117SACOS AZULES BOCC6304C439",name:"SACOS AZULES BOCC",type:"onlyAccount"},{code:"00020101021249250014CO.COM.CRB.RED0103CRB50300013CO.COM.CRB.CU01090164845945204581453031705405150005802CO5914Cci*boxBurguer6011BOGOTA D.C.622501031530708000DE40808020080270016CO.COM.CRB.CANAL0103POS81250015CO.COM.CRB.CIVA01020282230014CO.COM.CRB.IVA0101083240015CO.COM.CRB.BASE0101084250015CO.COM.CRB.CINC01020285230014CO.COM.CRB.INC0101090300016CO.COM.CRB.TRXID010600015491260014CO.COM.CRB.SEC0104627c6304df69",name:"CCI BOX BURGER",type:"onlyCards"},{code:"00020155020201021256076750.005802CO5918PRUEBAS QR REDEBAN49250103RBM0014CO.COM.RBM.RED902701035360016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124oC4KvIdTb9ouPsgVLNsLPwj00014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.*********************.000015CO.COM.RBM.BASE62180708SRB0008208020084250102020015CO.COM.RBM.CINC52040004852601040.000014CO.COM.RBM.INC530317064280002ES0118PRUEBAS QR REDEBAN5405450006304FAD5",name:"PRUEBAS QR REDEBAN",type:"cardsAndAccounts"},{code:"00020155020201021256040.005802CO5912COMERCIO POS49250103RBM0014CO.COM.RBM.RED9028010432620016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124HYImT9C9mng/eqME88+mrObw0014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.********************.000015CO.COM.RBM.BASE5125010454220013CO.COM.RBM.CA62180708SRB0068308020084250102020015CO.COM.RBM.CINC52040000852601040.000014CO.COM.RBM.INC530317064220002ES0112COMERCIO POS5405112206304B678",name:"COMERCIO POS",type:"cardsAndAccounts"},{code:"0002010102115802CO5915BOGOTA BICYCLES49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80260102IM0016CO.COM.RBM.CANAL91270105477470014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA60131100100 BOGOT8226010419.00014CO.COM.RBM.IVA50290108165934100013CO.COM.RBM.*****************.COM.RBM.BASE62180708BL01222608020084250102030015CO.COM.RBM.CINC520459418523010100014CO.COM.RBM.INC530317064250002ES0115BOGOTA BICYCLES6304E56D",name:"BOGOTA BICYCLES",type:"cardsAndAccounts"},{code:"000201550202010211560105802CO5922Hierros de occidente m49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124eH1E5X5opSOneQRXjtmvYMIX0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601227099 BOJAYA8223010100014CO.COM.RBM.IVA503001099353192840013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444454070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122Hierros de occidente m63049CC9",name:"HIERROS OCCIDENTE M",type:"onlyAccount"},{code:"000201550202010211560105802CO5914CAMISAS BOGOTA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL914601244nOdGGoa7JhbdMHsdz6/ZTfw0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601550001 VILLAVICE8223010100014CO.COM.RBM.IVA503001099353189710013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444450070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064240002ES0114CAMISAS BOGOTA63049B14",name:"CAMISAS BOGOTA",type:"onlyAccount"},{code:"000201550202010211560105802CO5911LA PLAZUELA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124kLbT32m0FcJ/Ws+o6IsRzz/C0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099236850430013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573215009881070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064210002ES0111LA PLAZUELA63041E06",name:"LA PLAZUELA",type:"onlyAccount"},{code:"000201550202010211560105802CO5912FLORES JUANA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124sz0Z69d6TSQ0H2oXwdNV1JzE0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099233753060013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573124012500070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064220002ES0112FLORES JUANA6304CD95",name:"FLORES JUANA",type:"onlyAccount"},{code:"0002015502010102115802CO5922COMERCIO DE SIEMPRE *******************.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124YOCYxVWUOwVMrGNJJvp2u8Uv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601366001 PEREIRA8223010100014CO.COM.RBM.IVA503001099102030400013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520405118523010100014CO.COM.RBM.INC530317064320002ES0122COMERCIO DE SIEMPRE BC63040D54",name:"COMERCIO DE SIEMPRE BC",type:"onlyAccount"}]},69595:(G,p,a)=>{a.d(p,{tG:()=>Y,bE:()=>q,PY:()=>X});var M=a(15861),A=a(87956),B=a(53113),i=a(98699),d=a(89148),y=a(57929),E=(()=>{return(r=E||(E={})).Static="11",r.Dinamic="12",E;var r})();const{CcaBuyAvailableCop:h}=d.Av;class g{constructor(o){this.name=o}}class P{constructor(o,t,e,n,s,u,R,f,S,F,Z,w){this.reference=o,this.codeQr=t,this.type=e,this.merchant=n,this.baseAmount=s,this.ivaAmount=u,this.incAmount=R,this.tipAmount=f,this.totalAmount=S,this.betweenAccounts=F,this.belongCommerce=Z,this.approvalId=w}get codeDinamic(){return this.type===E.Dinamic}get codeStatic(){return this.type===E.Static}}class v{constructor(o,t,e,n,s,u,R,f,S,F,Z){this.product=o,this.id=t,this.name=e,this.number=n,this.amountValue=s,this.logo=u,this.icon=R,this.color=f,this.requiredQuotas=S,this.numberQuotas=F,this.type=Z,this.shortNumber=n.substring(n.length-4)}get amount(){return this.amountValue}isAvailableAmount(o){return this.amountValue>=o}}class Q extends v{constructor(o,t){super(o,t.id,t.name,t.number,o.amount,t.franchise?t.franchise.getLogoContrast(t.color):o.bank.logo,t.franchise?{dark:t.franchise.logo.dark,light:t.franchise.logo.light,standard:t.franchise.getLogoContrast(t.color)}:{dark:o.bank.logo,light:o.bank.logo,standard:o.bank.logo},t.color,!1,y.qQ,"debit")}}class D extends v{constructor(o,t){if(super(o,o.id,o.name,o.publicNumber,o.amount,o.logo,o.icon,o.color,!0,y.q3,"credit"),t){const e=t.getSection(h);this.amountValue=+e?.value||o.amount}}}class N{constructor(o,t,e,n,s){this.invoice=o,this.type=t,this.amount=e,this.source=n,this.quotas=s}}var U=a(71776),c=a(39904),m=a(87903),O=a(42168),l=a(84757),C=a(99877);let I=(()=>{class r{constructor(t,e){this.http=t,this.fingerprintService=e}read(t){var e=this;return(0,O.firstValueFrom)(this.http.post(c.bV.PAYMENTS.QR.READ,{metadata:t}).pipe((0,l.tap)(n=>{if(Object.keys(n).every(u=>!n[u]))throw new Error("Invalid QR")}),(0,l.switchMap)(function(){var n=(0,M.Z)(function*(s){const{acquirerCode:u,merchantCode:R}=s,f=("Redeban"===u||"RBM"===u)&&9===R.length&&"9"===R.charAt(0),S=!f&&(yield e.verifyCommerce(t));return function b(r,o,t,e){return new P(o.billingNumber||o.trnConsecutiveCode,r,o.qrType,new g(o.merchantName),+o.netTrxAmount,+o.ivaValue,+o.incValue,+o.tipValue,+o.totalTrxAmount,t,e,o.approvalId)}(t,s,f,S)});return function(s){return n.apply(this,arguments)}}())))}verifyCommerce(t){return(0,O.firstValueFrom)(this.http.post(c.bV.PAYMENTS.QR.COMMERCE,{metadata:t}).pipe((0,l.map)(e=>"320"===e?.msgRsHdr?.status?.serverStatusCode)))}send(t){return"card"===t.type?this.sendForQr(t):this.sendForAccount(t)}cancel(t){return(0,O.firstValueFrom)(this.http.post(c.bV.PAYMENTS.QR.CANCEL,{code:t.invoice.codeQr}).pipe((0,l.map)(e=>(0,m.l1)(e,"SUCCESS")))).catch(e=>(0,m.rU)(e))}sendForQr(t){return(0,O.firstValueFrom)(this.http.post(c.bV.PAYMENTS.QR.PAY,function T(r){return{code:r.invoice.codeQr,installments:r.quotas,origin:r.source.id}}(t)).pipe((0,l.map)(e=>(0,m.l1)(e,"SUCCESS")))).catch(e=>(0,m.rU)(e))}sendForAccount(t){var e=this;return(0,M.Z)(function*(){const n=yield e.fingerprintService.getInfo();return(0,O.firstValueFrom)(e.http.post(c.bV.PAYMENTS.QR.PAY_ACCOUNT,function V(r,o){return{code:r.invoice.codeQr,curAmt:{amt:r.amount,curCode:"COP"},deviceAdmin:o,origin:r.source.id}}(t,n)).pipe((0,l.map)(s=>[201,"201"].includes(s.msgRsHdr?.status?.statusCode)?new B.LN("SUCCESS",`Ref: ${s.approvalId}`):"397"===s.msgRsHdr?.status?.additionalStatus?.statusCode?new B.LN("INFO",s.msgRsHdr.status.additionalStatus.statusDesc):(0,m.l1)(s,"SUCCESS")))).catch(s=>(0,m.rU)(s))})()}}return r.\u0275fac=function(t){return new(t||r)(C.\u0275\u0275inject(U.HttpClient),C.\u0275\u0275inject(A.ew))},r.\u0275prov=C.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var x=a(20691);let j=(()=>{class r extends x.Store{constructor(t){super({amount:0,creditCards:[],debitCards:[],products:[],pay:!0,requiredQuotas:!0,type:"card"}),t.subscribes(c.PU,()=>{this.reset()})}setProducts(t){this.reduce(e=>({...e,products:t}))}getProducts(){return this.select(({products:t})=>t)}setCreditCards(t){this.reduce(e=>({...e,creditCards:t}))}addCreditCard(t){const e=this.getCreditCards();e.filter(({id:s})=>s===t.id).length||this.reduce(s=>({...s,creditCards:[...e,t]}))}getCreditCards(){return this.select(({creditCards:t})=>t)}setDebitCards(t){this.reduce(e=>({...e,debitCards:t}))}addDebitCard(t){const e=this.getDebitCards();e.filter(({id:s})=>s===t.id).length||this.reduce(s=>({...s,debitCards:[...e,t]}))}getDebitCards(){return this.select(({debitCards:t})=>t)}setInvoice(t,e=!0){this.reduce(n=>({...n,invoice:t,pay:e}))}getInvoice(){return this.select(({invoice:t})=>t)}setSourceCard(t){this.reduce(e=>({...e,source:t,requiredQuotas:t.requiredQuotas,type:"card"}))}setSourceProduct(t){this.reduce(e=>({...e,source:t,quotas:0,requiredQuotas:!1,type:"account"}))}getSource(){return this.select(({source:t})=>t)}selectForSource(){return this.select(({amount:t,creditCards:e,debitCards:n,invoice:s,products:u,source:R})=>({amount:s.codeDinamic?s.totalAmount:t,creditCards:e,codeStatic:s.codeStatic,debitCards:n,invoice:s,products:u,source:R}))}setQuotas(t){this.reduce(e=>({...e,quotas:t}))}getQuotas(){return this.select(({quotas:t})=>t)}setAmount(t){this.reduce(e=>({...e,amount:t}))}getAmount(){return this.select(({amount:t})=>t)}getPay(){return this.select(({pay:t})=>t)}selectForConfirmation(){return this.select(({amount:t,invoice:e,quotas:n,requiredQuotas:s,source:u})=>({amount:t,invoice:e,quotas:n,requiredQuotas:s,source:u}))}}return r.\u0275fac=function(t){return new(t||r)(C.\u0275\u0275inject(A.Yd))},r.\u0275prov=C.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),Y=(()=>{class r{constructor(t,e,n){this.repository=t,this.store=e,this.eventBusService=n}setSourceCard(t){try{const e=this.store.getSource();return e&&e.type!==t.type&&this.store.setQuotas(t.numberQuotas),i.Either.success(this.store.setSourceCard(t))}catch{return i.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setSourceProduct(t){try{return i.Either.success(this.store.setSourceProduct(t))}catch{return i.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setQuotas(t){try{return i.Either.success(this.store.setQuotas(t))}catch({message:e}){return i.Either.failure({message:e})}}setAmount(t){try{return i.Either.success(this.store.setAmount(t))}catch({message:e}){return i.Either.failure({message:e})}}reset(){try{return i.Either.success(this.store.reset())}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,M.Z)(function*(){const e=function L(r){return new N(r.invoice,r.type,r.amount,r.source,r.quotas)}(t.store.currentState),n=yield t.execute(e);return t.eventBusService.emit(n.channel),i.Either.success({paymentQr:e,status:n})})()}execute(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(B.LN.error(e))}}}return r.\u0275fac=function(t){return new(t||r)(C.\u0275\u0275inject(I),C.\u0275\u0275inject(j),C.\u0275\u0275inject(A.Yd))},r.\u0275prov=C.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),q=(()=>{class r{constructor(t){this.store=t}source(){var t=this;return(0,M.Z)(function*(){try{return i.Either.success(t.store.selectForSource())}catch({message:e}){return i.Either.failure({message:e})}})()}cancel(){var t=this;return(0,M.Z)(function*(){try{const e=t.store.getInvoice();return i.Either.success({invoice:e})}catch({message:e}){return i.Either.failure({message:e})}})()}confirmation(){var t=this;return(0,M.Z)(function*(){try{return i.Either.success(t.store.selectForConfirmation())}catch({message:e}){return i.Either.failure({message:e})}})()}}return r.\u0275fac=function(t){return new(t||r)(C.\u0275\u0275inject(j))},r.\u0275prov=C.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var z=a(70658);let X=(()=>{class r{constructor(t,e,n,s,u){this.products=t,this.productService=e,this.repository=n,this.store=s,this.scannerService=u}execute(){var t=this;return(0,M.Z)(function*(){try{const{code:e,cancelled:n,granted:s}=yield t.scanQrCode();return s||z.N.navigatorEnabled?n?i.Either.failure({value:!1,message:"Escaneo del c\xf3digo QR cancelado para realizar pago"}):i.Either.success(e):i.Either.failure({value:!1,message:"Banca m\xf3vil no cuenta con permisos para escanear de c\xf3digo QR, por favor habilite los permisos y vuelva a intentarlo"})}catch({message:e}){return i.Either.failure({value:!0,message:e})}})()}payment(t){var e=this;return(0,M.Z)(function*(){try{return e.verifyInvoice(yield e.repository.read(t))}catch({message:n}){return e.store.reset(),i.Either.failure({message:n})}})()}cancel(t){var e=this;return(0,M.Z)(function*(){try{const n=yield e.repository.read(t);return n.approvalId?i.Either.success(e.store.setInvoice(n,!1)):i.Either.failure({message:"C\xf3digo escaneado para pago QR es inv\xe1lido"})}catch({message:n}){return e.store.reset(),i.Either.failure({message:n})}})()}scanQrCode(){var t=this;return(0,M.Z)(function*(){return t.scannerService.qrCode({orientation:"portrait"})})()}verifyInvoice(t){var e=this;return(0,M.Z)(function*(){try{if(t.betweenAccounts){const s=yield e.verifyAccount(!0);return s&&e.store.setSourceProduct(s),i.Either.success(e.store.setInvoice(t))}const n=yield e.verifyCards();if(!t.belongCommerce&&!n)throw Error("Actualmente no cuentas con tarjetas activas para realizar pago QR");if(n&&(e.store.setSourceCard(n),e.store.setQuotas(n.numberQuotas)),t.belongCommerce){const s=yield e.verifyAccount(!1);if(!s&&!n)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");s&&e.store.setSourceProduct(s)}else e.store.setProducts([]);return i.Either.success(e.store.setInvoice(t))}catch({message:n}){return i.Either.failure({message:n})}})()}verifyAccount(t){var e=this;return(0,M.Z)(function*(){const n=(yield e.products.requestAccountsForTransfer()).sort((u,R)=>u.amount>R.amount?-1:1);e.store.setProducts(n);const[s]=n;if(!s&&t)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");return s})()}verifyCards(){var t=this;return(0,M.Z)(function*(){const[[e],[n]]=yield Promise.all([t.requestCreditCards(),t.requestDebitCards()]);return e||n})()}requestCreditCards(){var t=this;return(0,M.Z)(function*(){const e=yield t.products.requestCreditCards(),s=(yield Promise.all(e.map(u=>t.requestCreditCardInformation(u)))).filter(u=>(0,i.itIsDefined)(u)).sort((u,R)=>u.amount>R.amount?-1:1);return t.store.setCreditCards(s),s})()}requestCreditCardInformation(t){return this.productService.requestInformation(t).then(e=>e&&new D(t,e)).catch(()=>{})}requestDebitCards(){var t=this;return(0,M.Z)(function*(){const e=yield t.products.requestAccountsForTransfer(),s=(yield Promise.all(e.map(u=>t.requestDebitCardsInformation(u)))).reduce((u,R)=>u.concat(R),[]).sort((u,R)=>u.amount>R.amount?-1:1);return t.store.setDebitCards(s),s})()}requestDebitCardsInformation(t){return this.productService.requestDebitCards(t).then(e=>e.map(n=>new Q(t,n))).catch(()=>[])}}return r.\u0275fac=function(t){return new(t||r)(C.\u0275\u0275inject(A.hM),C.\u0275\u0275inject(A.M5),C.\u0275\u0275inject(I),C.\u0275\u0275inject(j),C.\u0275\u0275inject(A.LQ))},r.\u0275prov=C.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},83899:(G,p,a)=>{a.r(p),a.d(p,{MboPaymentQrResultPageModule:()=>U});var M=a(17007),A=a(78007),B=a(79798),i=a(15861),d=a(99877),y=a(39904),E=a(95437),h=a(87903),g=a(53113);function P(c){const{isError:m,message:O,type:l}=c;return{animation:(0,h.jY)(c),title:"INFO"===l?"\xa1Verifica tu transacci\xf3n!":m?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:O}}function v({isError:c}){return c?[(0,h.wT)("Finalizar","finish","outline"),(0,h.wT)("Volver a intentar","retry")]:[(0,h.wT)("Hacer otro pago","retry","outline"),(0,h.wT)("Finalizar","finish")]}var D=a(69595),N=a(10464),b=a(78021),T=a(16442);function V(c,m){if(1&c&&(d.\u0275\u0275elementStart(0,"div",4),d.\u0275\u0275element(1,"mbo-header-result",5),d.\u0275\u0275elementEnd()),2&c){const O=d.\u0275\u0275nextContext();d.\u0275\u0275advance(1),d.\u0275\u0275property("rightActions",O.rightActions)}}let L=(()=>{class c{constructor(O,l,C){this.ref=O,this.mboProvider=l,this.managerPaymentQr=C,this.requesting=!0,this.template=y.$d,this.rightActions=[{id:"btn_payment-qr-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-qr-result-page_template"),this.initializatedTransaction()}onAction(O){this.mboProvider.navigation.next("finish"===O?y.Z6.CUSTOMER.PRODUCTS.HOME:y.Z6.PAYMENTS.QR.SCAN)}initializatedTransaction(){var O=this;return(0,i.Z)(function*(){(yield O.managerPaymentQr.send()).when({success:l=>{O.template=function Q(c){const{dateFormat:m,timeFormat:O}=new g.ou,{status:l,paymentQr:C}=c;return{actions:v(l),error:l.isError,header:P(l),informations:[(0,h.SP)("COMERCIO",C.invoice.merchant.name,C.invoice.codeDinamic?C.invoice.reference:""),(0,h.SP)("DESDE",C.source.name,C.source.number,"Banco de occidente"),(0,h._f)("SUMA DE",C.invoice.codeDinamic?C.invoice.totalAmount:C.amount),(0,h.cZ)(m,O)],skeleton:!1}}(l)}},()=>{O.requesting=!1,O.managerPaymentQr.reset()})})()}}return c.\u0275fac=function(O){return new(O||c)(d.\u0275\u0275directiveInject(d.ElementRef),d.\u0275\u0275directiveInject(E.ZL),d.\u0275\u0275directiveInject(D.tG))},c.\u0275cmp=d.\u0275\u0275defineComponent({type:c,selectors:[["mbo-payment-qr-result-page"]],decls:5,vars:2,consts:[[1,"mbo-payment-qr-result-page__content","mbo-page__scroller"],["class","mbo-payment-qr-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-qr-result-page__body"],["id","crd_payment-qr-result-page_template",3,"template","action"],[1,"mbo-payment-qr-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(O,l){1&O&&(d.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),d.\u0275\u0275template(2,V,2,1,"div",1),d.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),d.\u0275\u0275listener("action",function(I){return l.onAction(I)}),d.\u0275\u0275elementEnd()()()()),2&O&&(d.\u0275\u0275advance(2),d.\u0275\u0275property("ngIf",!l.requesting),d.\u0275\u0275advance(2),d.\u0275\u0275property("template",l.template))},dependencies:[M.NgIf,N.K,b.c,T.u],styles:["/*!\n * MBO PaymentQrResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 27/Jul/2022\n * Updated: 10/Ene/2024\n*/mbo-payment-qr-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-qr-result-page .mbo-payment-qr-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-qr-result-page .mbo-payment-qr-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),c})(),U=(()=>{class c{}return c.\u0275fac=function(O){return new(O||c)},c.\u0275mod=d.\u0275\u0275defineNgModule({type:c}),c.\u0275inj=d.\u0275\u0275defineInjector({imports:[M.CommonModule,A.RouterModule.forChild([{path:"",component:L}]),B.KI,B.cN,B.tu]}),c})()}}]);