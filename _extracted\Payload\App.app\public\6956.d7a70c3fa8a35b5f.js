(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6956],{46956:(P,c,n)=>{n.r(c),n.d(c,{MboTagAvalEditPage:()=>C});var p=n(15861),d=n(17007),u=n(78007),l=n(30263),v=n(24495),s=n(39904),b=n(95437);const h=/^[0-9|a-z|A-Z]*$/,f=o=>o&&!h.test(o.substring(1))?{id:"tagAval",message:"Campo no debe tener puntos, caracteres especiales, espacios ni tildes"}:null;var A=n(57544),m=n(54747),M=n(74561),y=n(87777),e=n(99877);function S(o,i){if(1&o&&e.\u0275\u0275element(0,"bocc-card-product-summary",14),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("icon",null==t.product?null:t.product.logo)("title",null==t.product?null:t.product.nickname)("number",null==t.product?null:t.product.shortNumber)("tagAval",null==t.product?null:t.product.tagAval)("color",null==t.product?null:t.product.color)}}let C=(()=>{class o{constructor(t,a,r,g,x,T){this.activateRoute=t,this.bottomSheetService=a,this.mboProvider=r,this.requestConfiguration=g,this.managerTagAval=x,this.modalService=T,this.requesting=!1,this.backAction={id:"btn_tag-aval-edit_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(s.Z6.CUSTOMER.PRODUCTS.INFO,{productId:this.product?.id})}},this.tagAvalControl=new A.FormControl("",[v.C1,f,(0,v.ec)(6,16)]),this.tagAvalMsg=this.getMsgStatus(!1),this.tagLengthMsg=this.getMsgStatus(!1)}ngOnInit(){this.unsubscription=this.tagAvalControl.subscribe(()=>{const a=this.tagAvalControl.errors.reduce((r,{id:g})=>[...r,g],[]);this.tagAvalMsg=this.getMsgStatus(a.includes("tagAval")),this.tagLengthMsg=this.getMsgStatus(a.includes("betweenLength"))}),this.tagAvalSheet=this.bottomSheetService.create(M.QH),this.tagAvalSheet?.open();const{productId:t}=this.activateRoute.snapshot.queryParams;this.requestConfiguration.requestProductAnyForId(t).then(a=>{a?this.product=a:this.mboProvider.navigation.back(s.Z6.CUSTOMER.PRODUCTS.HOME)})}ngOnDestroy(){this.unsubscription&&this.unsubscription(),this.unsubscriptionModal&&this.unsubscriptionModal(),this.tagAvalSheet?.destroy()}onHelper(){this.tagAvalSheet?.open()}onContinue(){if(this.product.tagAval===this.tagAvalControl.value)return void this.mboProvider.toast.warning("El nuevo Tag Aval debe ser diferente al actual");const t=this.modalService.create(y.a,{componentProps:{product:this.product,tagAval:this.tagAvalControl.value}});t.open(),this.unsubscriptionModal=t.subscribe(a=>{a&&this.onSubmit()})}onSubmit(){var t=this;return(0,p.Z)(function*(){t.requesting=!0,t.mboProvider.loader.open("Por favor espere..."),(yield t.managerTagAval.editInAccount(t.product,t.tagAvalControl.value)).when({success:()=>{t.mboProvider.toast.success(`Tu nuevo Tag Aval es: ${t.tagAvalControl.value}`,"Personalizaci\xf3n exitosa"),t.mboProvider.navigation.back(s.Z6.CUSTOMER.PRODUCTS.INFO,{productId:t.product?.id})},failure:({message:a})=>{t.mboProvider.toast.error(a,"Personalizaci\xf3n no exitosa"),t.requesting=!1}},()=>{t.requesting=!1,t.mboProvider.loader.close()})})()}getMsgStatus(t){return this.tagAvalControl.pristine?{icon:"information",status:"info"}:t?{icon:"warning",status:"error"}:{icon:"success",status:"success"}}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(u.ActivatedRoute),e.\u0275\u0275directiveInject(l.fG),e.\u0275\u0275directiveInject(b.ZL),e.\u0275\u0275directiveInject(m.mb),e.\u0275\u0275directiveInject(m.$Z),e.\u0275\u0275directiveInject(l.iM))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-tag-aval-edit-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:26,vars:17,consts:[[1,"mbo-tag-aval-edit-page__content"],[1,"mbo-tag-aval-edit-page__header"],[3,"leftAction"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png",1,"mbo-tag-aval-edit-page__logo"],[1,"mbo-tag-aval-edit-page__body"],[1,"mbo-tag-aval-edit-page__title","subtitle2-medium"],[3,"icon","title","number","tagAval","color",4,"ngIf"],["type","tagAval","label","Ingresa el nuevo Tag Aval","prefixIcon","tag-aval","placeholder","TuTagAval",3,"formControl","helpInLabel","msgErrorVisible","disabled","helper"],[1,"mbo-tag-aval-edit-page__messages"],[1,"mbo-tag-aval-edit-page__message"],[3,"icon"],[1,"caption-regular"],[1,"mbo-tag-aval-edit-page__footer"],["id","btn_tag-aval-edit_submit","bocc-button","raised",3,"spinner","disabled","click"],[3,"icon","title","number","tagAval","color"]],template:function(t,a){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-header-form",2),e.\u0275\u0275element(3,"img",3),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"div",4)(5,"label",5),e.\u0275\u0275text(6," Personaliza tu Tag Aval "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(7,S,1,5,"bocc-card-product-summary",6),e.\u0275\u0275elementStart(8,"bocc-growing-box",7),e.\u0275\u0275listener("helper",function(){return a.onHelper()}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",8)(10,"div",9),e.\u0275\u0275element(11,"bocc-icon",10),e.\u0275\u0275elementStart(12,"label",11),e.\u0275\u0275text(13," No incluyas puntos ni caracteres especiales "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"div",9),e.\u0275\u0275element(15,"bocc-icon",10),e.\u0275\u0275elementStart(16,"label",11),e.\u0275\u0275text(17," Entre 5 y 15 caracteres "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(18,"div",9),e.\u0275\u0275element(19,"bocc-icon",10),e.\u0275\u0275elementStart(20,"label",11),e.\u0275\u0275text(21," No incluyas espacios ni tildes "),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(22,"div",12)(23,"button",13),e.\u0275\u0275listener("click",function(){return a.onContinue()}),e.\u0275\u0275elementStart(24,"span"),e.\u0275\u0275text(25,"Continuar"),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",a.backAction),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",a.product),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",a.tagAvalControl)("helpInLabel",!0)("msgErrorVisible",!1)("disabled",a.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275classMap(a.tagAvalMsg.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("icon",a.tagAvalMsg.icon),e.\u0275\u0275advance(3),e.\u0275\u0275classMap(a.tagLengthMsg.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("icon",a.tagLengthMsg.icon),e.\u0275\u0275advance(3),e.\u0275\u0275classMap(a.tagAvalMsg.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("icon",a.tagAvalMsg.icon),e.\u0275\u0275advance(4),e.\u0275\u0275property("spinner",a.requesting)("disabled",a.tagAvalControl.invalid||a.requesting))},dependencies:[d.CommonModule,d.NgIf,l.Jx,l.dH,l.Zl,l.P8,l.D1],styles:["/*!\n * MBO TagAvalEdit Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 20/Nov/2024\n * Updated: 20/Nov/2024\n*/mbo-tag-aval-edit-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between;row-gap:var(--sizing-x2);overflow:hidden}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__content{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__logo{height:var(--sizing-x8);margin:var(--sizing-x4) 0rem}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__title{text-align:center}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__messages{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message{--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-lighter-700);--bocc-icon-dimension: var(--sizing-x8);display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message.info{--pvt-label-color: var(--color-blue-700)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message.error{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message bocc-icon{color:var(--pvt-icon-color)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__message label{color:var(--pvt-label-color)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__actions{display:flex;flex-direction:column;row-gap:var(--sizing-x2);margin-top:var(--sizing-x8)}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-tag-aval-edit-page .mbo-tag-aval-edit-page__footer button{width:100%}\n"],encapsulation:2}),o})()}}]);