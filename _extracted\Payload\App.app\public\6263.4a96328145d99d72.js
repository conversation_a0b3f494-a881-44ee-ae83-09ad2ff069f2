(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6263],{44477:(M,p,n)=>{n.d(p,{p:()=>d,c:()=>I});var h=n(15861),l=n(87956),u=n(53113),c=n(98699);class r{constructor(a,e){this.invoice=a,this.source=e}}function b(t){return new r(t.invoice,t.source)}var g=n(71776),C=n(39904),D=n(87903),R=n(42168),f=n(84757),s=n(99877);let P=(()=>{class t{constructor(e){this.http=e}send(e){return(0,R.firstValueFrom)(this.http.post(C.bV.PAYMENTS.INVOICE,function y(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.nie,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.invoice.companyName,toNickname:t.invoice.nickname,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,f.map)(([o])=>(0,D.l1)(o,"SUCCESS")))).catch(o=>(0,D.rU)(o))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(g.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var E=n(20691);let i=(()=>{class t extends E.Store{constructor(e){super({confirmation:!1}),e.subscribes(C.PU,()=>{this.reset()})}setInvoice(e){this.reduce(o=>({...o,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(o=>({...o,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(l.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),d=(()=>{class t{constructor(e,o,v){this.repository=e,this.store=o,this.eventBusService=v}setInvoice(e){try{return c.Either.success(this.store.setInvoice(e))}catch({message:o}){return c.Either.failure({message:o})}}setSource(e){try{return c.Either.success(this.store.setSource(e))}catch({message:o}){return c.Either.failure({message:o})}}reset(){try{return c.Either.success(this.store.reset())}catch({message:e}){return c.Either.failure({message:e})}}send(){var e=this;return(0,h.Z)(function*(){const o=b(e.store.currentState),v=yield e.execute(o);return e.eventBusService.emit(v.channel),c.Either.success({invoice:o,status:v})})()}execute(e){try{return this.repository.send(e)}catch({message:o}){return Promise.resolve(u.LN.error(o))}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(P),s.\u0275\u0275inject(i),s.\u0275\u0275inject(l.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var m=n(19799);let I=(()=>{class t{constructor(e,o,v){this.products=e,this.invoices=o,this.store=v}source(e){var o=this;return(0,h.Z)(function*(){try{const v=yield o.products.requestAccountsForTransfer();let N=o.store.getInvoice();return!N&&e&&(N=(yield o.invoices.request()).find(({uuid:S})=>e===S),o.store.setInvoice(N)),c.Either.success({invoice:N,products:v})}catch({message:v}){return c.Either.failure({message:v})}})()}confirmation(){try{const e=b(this.store.currentState);return c.Either.success({payment:e})}catch({message:e}){return c.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(l.hM),s.\u0275\u0275inject(m.W),s.\u0275\u0275inject(i))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},45507:(M,p,n)=>{n.r(p),n.d(p,{MboPaymentInvoiceResultPageModule:()=>I});var h=n(17007),l=n(78007),u=n(79798),c=n(15861),r=n(99877),y=n(39904),b=n(95437),g=n(87903),C=n(53113);function D(t){const{isError:a,message:e}=t;return{animation:(0,g.jY)(t),title:a?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:e}}function R({isError:t}){return t?[(0,g.wT)("Finalizar","finish","outline"),(0,g.wT)("Volver a intentar","retry")]:[(0,g.wT)("Hacer otro pago","retry","outline"),(0,g.wT)("Finalizar","finish")]}var s=n(44477),P=n(10464),E=n(78021),i=n(16442);function d(t,a){if(1&t&&(r.\u0275\u0275elementStart(0,"div",4),r.\u0275\u0275element(1,"mbo-header-result",5),r.\u0275\u0275elementEnd()),2&t){const e=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("rightActions",e.rightActions)}}let m=(()=>{class t{constructor(e,o,v){this.ref=e,this.mboProvider=o,this.managerInvoice=v,this.requesting=!0,this.template=y.$d,this.rightActions=[{id:"btn_payment-invoice-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-invoice-result-page_template"),this.initializatedTransaction()}onAction(e){this.mboProvider.navigation.next("finish"===e?y.Z6.CUSTOMER.PRODUCTS.HOME:y.Z6.PAYMENTS.SERVICES.INVOICE.SOURCE)}initializatedTransaction(){var e=this;return(0,c.Z)(function*(){(yield e.managerInvoice.send()).when({success:o=>{e.template=function f(t){const{dateFormat:a,timeFormat:e}=new C.ou,{status:o,invoice:v}=t;return{actions:R(o),error:o.isError,header:D(o),informations:[(0,g.SP)("DESTINO",v.invoice.nickname,v.invoice.number,v.invoice.companyName),(0,g._f)("SUMA DE",v.invoice.amount),(0,g.cZ)(a,e)],skeleton:!1}}(o)}},()=>{e.requesting=!1,e.managerInvoice.reset()})})()}}return t.\u0275fac=function(e){return new(e||t)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(b.ZL),r.\u0275\u0275directiveInject(s.p))},t.\u0275cmp=r.\u0275\u0275defineComponent({type:t,selectors:[["mbo-payment-invoice-result-page"]],decls:5,vars:2,consts:[[1,"mbo-payment-invoice-result-page__content","mbo-page__scroller"],["class","mbo-payment-invoice-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-invoice-result-page__body"],["id","crd_payment-invoice-result-page_template",3,"template","action"],[1,"mbo-payment-invoice-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(e,o){1&e&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),r.\u0275\u0275template(2,d,2,1,"div",1),r.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),r.\u0275\u0275listener("action",function(N){return o.onAction(N)}),r.\u0275\u0275elementEnd()()()()),2&e&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("ngIf",!o.requesting),r.\u0275\u0275advance(2),r.\u0275\u0275property("template",o.template))},dependencies:[h.NgIf,P.K,E.c,i.u],styles:["/*!\n * BOCC PaymentInvoiceResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 31/Jul/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-invoice-result-page .mbo-payment-invoice-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-invoice-result-page .mbo-payment-invoice-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),t})(),I=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=r.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=r.\u0275\u0275defineInjector({imports:[h.CommonModule,l.RouterModule.forChild([{path:"",component:m}]),u.KI,u.cN,u.tu]}),t})()},63674:(M,p,n)=>{n.d(p,{Eg:()=>g,Lo:()=>c,Wl:()=>r,ZC:()=>y,_f:()=>l,br:()=>b,tl:()=>u});var h=n(29306);const l={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},u=new h.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),c={color:"success",key:"paid",label:"Pagada"},r={color:"alert",key:"pending",label:"Por pagar"},y={color:"danger",key:"expired",label:"Vencida"},b={color:"info",key:"recurring",label:"Pago recurrente"},g={color:"info",key:"programmed",label:"Programado"}},66067:(M,p,n)=>{n.d(p,{S6:()=>C,T2:()=>b,UQ:()=>D,mZ:()=>g});var h=n(39904),l=n(6472),c=n(63674),r=n(31707);class b{constructor(f,s,P,E,i,d,m,I,t,a,e){this.id=f,this.type=s,this.name=P,this.nickname=E,this.number=i,this.bank=d,this.isAval=m,this.isProtected=I,this.isOwner=t,this.ownerName=a,this.ownerDocument=e,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[h.y1],this.initialsName=(0,l.initials)(E),this.shortNumber=i.substring(i.length-4),this.descriptionNumber=`${P} ${i}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:d.logo,light:d.logo,standard:d.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(f){this.informationValue||(this.informationValue=f)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(f){return this.currenciesValue.includes(f)}}class g{constructor(f,s){this.id=f,this.type=s}}class C{constructor(f,s,P,E,i,d,m,I,t,a,e,o){this.uuid=f,this.number=s,this.nie=P,this.nickname=E,this.companyId=i,this.companyName=d,this.amount=m,this.registerDate=I,this.expirationDate=t,this.paid=a,this.statusCode=e,this.references=o,this.recurring=o.length>0,this.status=function y(R){switch(R){case r.U.EXPIRED:return c.ZC;case r.U.PENDING:return c.Wl;case r.U.PROGRAMMED:return c.Eg;case r.U.RECURRING:return c.br;default:return c.Lo}}(e)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class D{constructor(f,s,P,E,i,d,m,I){this.uuid=f,this.number=s,this.nickname=P,this.companyId=E,this.companyName=i,this.city=d,this.amount=m,this.isBiller=I}}},19799:(M,p,n)=>{n.d(p,{e:()=>P,W:()=>E});var h=n(71776),l=n(39904),u=n(87956),c=n(98699),r=n(42168),y=n(84757),b=n(53113),g=n(33876),C=n(66067);var s=n(99877);let P=(()=>{class i{constructor(m,I){this.http=m,I.subscribes(l.PU,()=>{this.destroy()}),this.billers$=(0,c.securePromise)(()=>(0,r.firstValueFrom)(this.http.get(l.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,y.map)(({content:t})=>t.map(a=>function f(i){return new C.UQ((0,g.v4)(),i.nie,i.nickname,i.orgIdNum,i.orgName,i.city,+i.amt,(0,c.parseBoolean)(i.biller))}(a))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return i.\u0275fac=function(m){return new(m||i)(s.\u0275\u0275inject(h.HttpClient),s.\u0275\u0275inject(u.Yd))},i.\u0275prov=s.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),E=(()=>{class i{constructor(m,I){this.http=m,I.subscribes(l.PU,()=>{this.destroy()}),this.invoices$=(0,c.securePromise)(()=>(0,r.firstValueFrom)(this.http.get(l.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,y.map)(({content:t})=>t.map(a=>function R(i){const d=i.refInfo.map(m=>function D(i){return new C.mZ(i.refId,i.refType)}(m));return new C.S6((0,g.v4)(),i.invoiceNum,i.nie,i.nickName,i.orgIdNum,i.orgName,+i.totalCurAmt,new b.ou(i.effDt),new b.ou(i.expDt),(0,c.parseBoolean)(i.payDone),i.state,d)}(a))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return i.\u0275fac=function(m){return new(m||i)(s.\u0275\u0275inject(h.HttpClient),s.\u0275\u0275inject(u.Yd))},i.\u0275prov=s.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},31707:(M,p,n)=>{n.d(p,{U:()=>h,f:()=>l});var h=(()=>{return(u=h||(h={})).RECURRING="1",u.EXPIRED="2",u.PENDING="3",u.PROGRAMMED="4",h;var u})(),l=(()=>{return(u=l||(l={})).BILLER="Servicio",u.NON_BILLER="Servicio",u.PSE="Servicio",u.TAX="Impuesto",u.LOAN="Obligaci\xf3n financiera",u.CREDIT_CARD="Obligaci\xf3n financiera",l;var u})()}}]);