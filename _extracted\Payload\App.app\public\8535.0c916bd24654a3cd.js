(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8535],{108:(P,T,o)=>{o.d(T,{a:()=>b,z:()=>y});var e=o(87903),d=o(53113);function p(t){const{isError:n,message:i,type:m}=t;return{animation:(0,e.jY)(t),title:n?"\xa1Transferencia fallida!":"PENDING"===m?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:i}}function g({isError:t}){return t?[(0,e.wT)("Finalizar","finish","outline"),(0,e.wT)("Volver a intentar","retry")]:[(0,e.wT)("Hacer otra transferencia","retry","outline"),(0,e.wT)("Finalizar","finish")]}function v(t){const{approved:n,pending:i}=t;if(n)return function r(t,n){const{dateFormat:i,timeFormat:m}=new d.ou,c=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),c.push((0,e.cZ)(i,m)),c}(t,n);if(i)return function h(t,n){const{dateFormat:i,timeFormat:m}=new d.ou,c=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),c.push((0,e.cZ)(i,m)),c}(t,i);const{dateFormat:m,timeFormat:c}=new d.ou,u=[(0,e.SP)("TRANSFER"===t.type?"ENVIADO A":"SOLICITADO A",t.contact?.name,t.contact?.number),(0,e._f)("SUMA DE",t.amount)];return t.description&&u.push((0,e.SP)("DESCRIPCI\xd3N","","",t.description)),u.push((0,e.cZ)(m,c)),u}function y(t){const{status:n,transfiya:i}=t;return{actions:g(n),error:n.isError,header:p(n),informations:v(i),skeleton:!1}}function b(t){const n=[],{amount:i,category:m,color:c,date:{dateFormat:u,timeFormat:s},description:f,phoneFormat:a,reference:l}=t;return n.push((0,e.SP)("REFERENCIA",l)),n.push((0,e.fW)("TIPO DE TRANSACCI\xd3N",c,m)),n.push((0,e.SP)("CONTACTO",a)),n.push((0,e._f)("LA SUMA DE",i)),f&&n.push((0,e.Kt)("DESCRIPCI\xd3N",f)),n.push((0,e.cZ)(u,s)),n}},18535:(P,T,o)=>{o.r(T),o.d(T,{MboTransfiyaTransferResultPageModule:()=>u});var e=o(17007),d=o(78007),p=o(79798),g=o(15861),r=o(99877),h=o(39904),v=o(95437),y=o(108),b=o(17698),t=o(10464),n=o(78021),i=o(16442);function m(s,f){if(1&s&&(r.\u0275\u0275elementStart(0,"div",4),r.\u0275\u0275element(1,"mbo-header-result",5),r.\u0275\u0275elementEnd()),2&s){const a=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("rightActions",a.rightActions)}}let c=(()=>{class s{constructor(a,l,E){this.ref=a,this.mboProvider=l,this.managerTransfiya=E,this.requesting=!0,this.template=h.$d,this.rightActions=[{id:"btn_transfiya-transfer-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfiya-transfer-result-page_template"),this.initializatedTransaction()}onAction(a){this.mboProvider.navigation.next("finish"===a?h.Z6.CUSTOMER.PRODUCTS.HOME:h.Z6.TRANSFERS.TRANSFIYA.TRANSFER.SOURCE)}initializatedTransaction(){var a=this;return(0,g.Z)(function*(){(yield a.managerTransfiya.confirmTransfer()).when({success:l=>{a.template=(0,y.z)(l)}},()=>{a.requesting=!1,a.managerTransfiya.reset()})})()}}return s.\u0275fac=function(a){return new(a||s)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(v.ZL),r.\u0275\u0275directiveInject(b.Pm))},s.\u0275cmp=r.\u0275\u0275defineComponent({type:s,selectors:[["mbo-transfiya-transfer-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfiya-transfer-result-page__content","mbo-page__scroller"],["class","mbo-transfiya-transfer-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfiya-transfer-result-page__body"],["id","crd_transfiya-transfer-result-page_template",3,"template","action"],[1,"mbo-transfiya-transfer-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(a,l){1&a&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),r.\u0275\u0275template(2,m,2,1,"div",1),r.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),r.\u0275\u0275listener("action",function(S){return l.onAction(S)}),r.\u0275\u0275elementEnd()()()()),2&a&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("ngIf",!l.requesting),r.\u0275\u0275advance(2),r.\u0275\u0275property("template",l.template))},dependencies:[e.NgIf,t.K,n.c,i.u],styles:["/*!\n * MBO TransfiyaTransferResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 29/Jul/2022\n * Updated: 09/Feb/2024\n*/mbo-transfiya-transfer-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-transfer-result-page .mbo-transfiya-transfer-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-transfer-result-page .mbo-transfiya-transfer-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),s})(),u=(()=>{class s{}return s.\u0275fac=function(a){return new(a||s)},s.\u0275mod=r.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=r.\u0275\u0275defineInjector({imports:[e.CommonModule,d.RouterModule.forChild([{path:"",component:c}]),p.KI,p.cN,p.tu]}),s})()}}]);