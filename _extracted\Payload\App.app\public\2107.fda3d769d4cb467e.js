(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2107],{10954:(R,b,n)=>{n.d(b,{V:()=>N,Ws:()=>o,YH:()=>O,d6:()=>i,uJ:()=>C});var m=n(39904),u=n(87903),h=n(53113),y=n(66067);class o extends y.T2{constructor(d,g,f,I,l,v,c,s,D,M,S,U,j){super(d,g,f,I,l,c,s,D,M,U,j),this.colorValue=v,this.franchise=S,this.bank.isOccidente&&D&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,u.mm)(this,S),this.currenciesValue=S?.currencies||[m.y1],this.digitalValue="DIGITAL"===v}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class C{constructor(d,g,f){this.code=d,this.amount=g,this.amountCurrency=f||0}}class O{constructor(d,g,f,I,l){this.label=d,this.mode=g,this.copTotal=I?.value||0,this.usdTotal=l?.value||0,this.copUsdTotal=f?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new C("COP",this.copTotal,this.copTotal)}usdValue(){return new C("USD",this.copUsdTotal,this.usdTotal)}}class E{constructor(d,g,f){this.destination=d,this.source=g,this.currency=f}}class N{constructor(d,g,f,I,l,v){this.destination=d,this.source=g,this.isManual=f,this.trm=I,this.cop=l,this.usd=v,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new E(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new E(this.destination,this.source,this.usd):void 0}}class i extends h.LN{constructor(d,g,f){super(d,g),this.currency=f}}},96381:(R,b,n)=>{n.d(b,{T:()=>j,P:()=>J});var m=n(15861),u=n(77279),h=n(81536),y=n(87956),o=n(98699),C=n(10954),O=n(39904),E=n(29306),N=n(7464),i=n(87903),P=n(53113),d=n(1131);function I(e,T){return new C.V(e.destination,e.source,e.mode===d.o.MANUAL,T,e.cop,e.usd)}var v=n(71776),c=n(42168),s=n(99877);let D=(()=>{class e{constructor(t,r){this.http=t,r.subscribes(O.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,c.firstValueFrom)(this.http.get(O.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,c.map)(({content:t})=>t.map(r=>function f(e){return new C.Ws(e.id,e.acctType,e.acctTypeName,"DIGITAL"===e.color?O.CG:e.loanName,e.acctId,e.isOwner&&e.color||"NONE",(0,N.RO)(e.bankId,e.bankName),e.isAval,e.dynamo||!1,e.isOwner,e.creditCardType?function g(e){return new E.dD(e?.code,e?.description,0,0)}(e.creditCardType):void 0,e.isOwner?void 0:e.owner,e.isOwner?void 0:new P.dp((0,i.nX)(e.ownerIdType),e.ownerId))}(r))),(0,c.tap)(t=>{this.creditCards=t})))}}return e.\u0275fac=function(t){return new(t||e)(s.\u0275\u0275inject(v.HttpClient),s.\u0275\u0275inject(y.Yd))},e.\u0275prov=s.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),M=(()=>{class e{constructor(t){this.http=t}send(t){return(0,i.EC)([t.getPaymentCop(),t.getPaymentUsd()].filter(r=>!!r).map(r=>()=>this.sendCurrency(r)))}sendCurrency(t){return(0,c.firstValueFrom)(this.http.post(O.bV.PAYMENTS.CREDIT_CARD,function l(e){return{acctIdFrom:e.source.id,acctNickNameFrom:e.source.nickname,bankIdFrom:e.source.bank.id,acctIdTo:e.destination.id,acctNameTo:e.destination.nickname,bankIdTo:e.destination.bank.id,bankNameTo:e.destination.bank.name,amt:Math.ceil(e.currency.amount),curCode:e.currency.code,paymentDesc:""}}(t)).pipe((0,c.map)(r=>{const a=(0,i.l1)(r,"SUCCESS"),{type:p,message:A}=a;return new C.d6(p,A,t.currency)}),(0,c.catchError)(r=>{const{message:a}=(0,i.rU)(r);return(0,c.of)(new C.d6("ERROR",a,t.currency))})))}}return e.\u0275fac=function(t){return new(t||e)(s.\u0275\u0275inject(v.HttpClient))},e.\u0275prov=s.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var S=n(20691);let U=(()=>{class e extends S.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,mode:d.o.PAY_MIN}),this.eventBusService=t,this.eventBusService.subscribes(O.PU,()=>{this.reset()})}setDestination(t,r=!1){this.reduce(a=>({...a,destination:t,fromCustomer:r}))}getDestination(){return this.select(({destination:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(r=>({...r,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount(t){const{cop:r,mode:a,usd:p}=t;this.reduce(A=>({...A,cop:r,mode:a,usd:p,confirmation:!0}))}getAmount(){return this.select(({mode:t,cop:r,usd:a})=>({cop:r,mode:t,usd:a}))}setCurrencyCode(t){this.reduce(r=>({...r,currencyCode:t}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return e.\u0275fac=function(t){return new(t||e)(s.\u0275\u0275inject(y.Yd))},e.\u0275prov=s.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),j=(()=>{class e{constructor(t,r,a,p,A){this.financials=t,this.productService=r,this.repository=a,this.store=p,this.eventBusService=A}setDestination(t){var r=this;return(0,m.Z)(function*(){try{return t.isRequiredInformation&&(yield r.productService.requestInformation(t)),o.Either.success(r.store.setDestination(t))}catch{return o.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return o.Either.success(this.store.setSource(t))}catch({message:r}){return o.Either.failure({message:r})}}setAmount(t){try{return o.Either.success(this.store.setAmount(t))}catch({message:r}){return o.Either.failure({message:r})}}setCurrencyCode(t){try{return o.Either.success(this.store.setCurrencyCode(t))}catch({message:r}){return o.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),r=this.store.getDestination();return this.store.reset(),o.Either.success({fromCustomer:t,destination:r})}catch({message:t}){return o.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const r=I(t.store.currentState,yield t.requestTrmUsd()),a=yield t.execute(r),p=a.reduce((A,{isError:_})=>A&&!_,!0);return t.eventBusService.emit(p?u.q.TransactionSuccess:u.q.TransactionFailed),o.Either.success({creditCard:r,status:a})})()}requestTrmUsd(){return(0,o.catchPromise)(this.financials.request().then(([t])=>t))}execute(t){try{return this.repository.send(t)}catch({message:r}){return Promise.resolve([new C.d6("ERROR",r)])}}}return e.\u0275fac=function(t){return new(t||e)(s.\u0275\u0275inject(h.rm),s.\u0275\u0275inject(y.M5),s.\u0275\u0275inject(M),s.\u0275\u0275inject(U),s.\u0275\u0275inject(y.Yd))},e.\u0275prov=s.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var w=n(89148);const{MANUAL:H,PAY_ALTERNATIVE:V,PAY_MIN:F,PAY_TOTAL:B}=d.o,{CcaDatePayMinCop:z,CcaDatePayMinUsd:X,CcaPayAltMinCop:Y,CcaPayAltMinUsd:W,CcaPayMinCop:x,CcaPayMinUsd:G,CcaPayTotalCop:Z,CcaPayTotalUsd:K}=w.Av;let J=(()=>{class e{constructor(t,r,a,p,A){this.products=t,this.productService=r,this.financials=a,this.repository=p,this.store=A}destination(){var t=this;return(0,m.Z)(function*(){try{return o.Either.success((yield t.repository.request()).reduce((r,a)=>{const{others:p,principals:A}=r;return(a.bank.isOccidente?A:p).push(a),r},{others:[],principals:[]}))}catch({message:r}){return o.Either.failure({message:r})}})()}source(t){var r=this;return(0,m.Z)(function*(){try{const a=yield r.products.requestAccountsForTransfer(),p=r.store.itIsConfirmation(),A=yield r.requestCreditCard(t);return o.Either.success({confirmation:p,destination:A,products:a})}catch({message:a}){return o.Either.failure({message:a})}})()}information(){var t=this;return(0,m.Z)(function*(){try{const r=t.store.getDestination(),a=yield t.productService.requestInformation(r),p=yield t.requestTrmUsd(),A=a?.getSection(z),_=a?.getSection(X);return o.Either.success({destination:r,min:new C.YH("VALOR M\xcdNIMO A PAGAR",F,p,a?.getSection(x),a?.getSection(G)),alternative:new C.YH("VALOR M\xcdNIMO ALTERNO",V,p,a?.getSection(Y),a?.getSection(W)),total:new C.YH("SALDO ACTUAL",B,p,a?.getSection(Z),a?.getSection(K)),dateCop:A?.valueFormat,dateUsd:_?.valueFormat})}catch({message:r}){return o.Either.failure({message:r})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const r=t.store.itIsConfirmation(),a=t.store.getAmount(),p=t.store.getSource(),A=t.store.getDestination(),_=yield t.productService.requestInformation(A),L=yield t.requestTrmUsd();return o.Either.success({destination:A,amount:a,confirmation:r,trm:L,source:p,min:new C.YH("Pago m\xednimo",F,L,_?.getSection(x),_?.getSection(G)),alternative:new C.YH("Pago m\xednimo alterno",V,L,_?.getSection(Y),_?.getSection(W)),total:new C.YH("Saldo actual",B,L,_?.getSection(Z),_?.getSection(K)),manual:new C.YH("Otro valor",H)})}catch({message:r}){return o.Either.failure({message:r})}})()}amount(){try{const t=this.store.itIsConfirmation(),r=this.store.getSource(),a=this.store.getDestination(),{cop:p}=this.store.getAmount();return o.Either.success({amount:p?.amount||0,confirmation:t,destination:a,source:r})}catch({message:t}){return o.Either.failure({message:t})}}confirmation(){var t=this;return(0,m.Z)(function*(){try{const r=I(t.store.currentState,yield t.requestTrmUsd());return o.Either.success({payment:r})}catch({message:r}){return o.Either.failure({message:r})}})()}requestCreditCard(t){var r=this;return(0,m.Z)(function*(){let a=r.store.getDestination();return!a&&t&&(a=(yield r.products.requestCreditCards()).find(({id:p})=>p===t),r.store.setDestination(a,!0)),a})()}requestTrmUsd(){return(0,o.catchPromise)(this.financials.request().then(([t])=>t))}}return e.\u0275fac=function(t){return new(t||e)(s.\u0275\u0275inject(y.hM),s.\u0275\u0275inject(y.M5),s.\u0275\u0275inject(h.rm),s.\u0275\u0275inject(D),s.\u0275\u0275inject(U))},e.\u0275prov=s.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},55351:(R,b,n)=>{n.d(b,{t:()=>O});var m=n(30263),u=n(39904),h=n(95437),y=n(96381),o=n(99877);let O=(()=>{class E{constructor(i,P,d){this.modalConfirmation=i,this.mboProvider=P,this.managerCreditCard=d}execute(i=!0){i?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:i,destination:P})=>{i?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:P.id}):this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)}})}}return E.\u0275fac=function(i){return new(i||E)(o.\u0275\u0275inject(m.$e),o.\u0275\u0275inject(h.ZL),o.\u0275\u0275inject(y.T))},E.\u0275prov=o.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})()},1131:(R,b,n)=>{n.d(b,{o:()=>m});var m=(()=>{return(u=m||(m={}))[u.PAY_MIN=0]="PAY_MIN",u[u.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",u[u.PAY_TOTAL=2]="PAY_TOTAL",u[u.MANUAL=3]="MANUAL",m;var u})()},52107:(R,b,n)=>{n.r(b),n.d(b,{MboPaymentCreditCardDestinationPageModule:()=>I});var m=n(17007),u=n(78007),h=n(79798),y=n(30263),o=n(15861),C=n(39904),O=n(95437),E=n(96381),N=n(55351),i=n(99877),P=n(13043),d=n(48774),g=n(50689);let f=(()=>{class l{constructor(c,s,D,M){this.mboProvider=c,this.requestConfiguration=s,this.managerCreditCard=D,this.cancelProvider=M,this.confirmation=!1,this.hasError=!1,this.requesting=!0,this.principals=[],this.others=[],this.cancelAction={id:"btn_payment-creditcard-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initalizatedConfiguration()}get hasPrincipals(){return this.principals.length>0}get hasOthers(){return this.others.length>0}get isEmpty(){return!this.requesting&&0===this.principals.length&&0===this.others.length}get msgEmpty(){return this.hasError?"Ocurrio un error inesperado mientras consultamos tus tarjetas por pagar, por favor intentalo de nuevo en unos minutos.":"Actualmente no cuentas con tarjetas de cr\xe9ditos pendientes por pagar"}onProduct(c){var s=this;return(0,o.Z)(function*(){c.isAvalGroup&&s.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield s.managerCreditCard.setDestination(c)).when({success:()=>{s.mboProvider.navigation.next(C.Z6.PAYMENTS.CREDIT_CARD.SOURCE)},failure:({message:D})=>{s.mboProvider.toast.error(D)}},()=>{s.mboProvider.loader.close()})})()}initalizatedConfiguration(){var c=this;return(0,o.Z)(function*(){(yield c.requestConfiguration.destination()).when({success:({others:s,principals:D})=>{c.others=s,c.principals=D},failure:()=>{c.hasError=!0}},()=>{c.requesting=!1})})()}}return l.\u0275fac=function(c){return new(c||l)(i.\u0275\u0275directiveInject(O.ZL),i.\u0275\u0275directiveInject(E.P),i.\u0275\u0275directiveInject(E.T),i.\u0275\u0275directiveInject(N.t))},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-creditcard-destination-page"]],decls:11,vars:11,consts:[[1,"mbo-payment-creditcard-destination-page__content"],[1,"mbo-payment-creditcard-destination-page__header"],["title","Tarjeta","progress","25%",3,"rightAction"],[1,"mbo-payment-creditcard-destination-page__body"],[1,"mbo-payment-creditcard-destination-page__body__title","subtitle2-medium"],[1,"mbo-payment-creditcard-destination-page__body__content",3,"hidden"],["title","TARJETAS BANCO DE OCCIDENTE",3,"header","skeleton","products","hidden","select"],["title","TARJETAS DE OTRAS ENTIDADES",3,"header","products","hidden","select"],[1,"mbo-payment-creditcard-destination-page__empty",3,"hidden"]],template:function(c,s){1&c&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3)(4,"div",4),i.\u0275\u0275text(5," \xbfCual tarjeta desear pagar hoy? "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"div",5)(7,"mbo-product-destination-selector",6),i.\u0275\u0275listener("select",function(M){return s.onProduct(M)}),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(8,"mbo-product-destination-selector",7),i.\u0275\u0275listener("select",function(M){return s.onProduct(M)}),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(9,"mbo-message-empty",8),i.\u0275\u0275text(10),i.\u0275\u0275elementEnd()()()),2&c&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("rightAction",s.cancelAction),i.\u0275\u0275advance(4),i.\u0275\u0275property("hidden",s.isEmpty),i.\u0275\u0275advance(1),i.\u0275\u0275property("header",!1)("skeleton",s.requesting)("products",s.principals)("hidden",!s.requesting&&!s.hasPrincipals),i.\u0275\u0275advance(1),i.\u0275\u0275property("header",!1)("products",s.others)("hidden",s.requesting||!s.hasOthers),i.\u0275\u0275advance(1),i.\u0275\u0275property("hidden",!s.isEmpty),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",s.msgEmpty," "))},dependencies:[P.e,d.J,g.A],styles:["/*!\n * MBO PaymentCreditCardDestination Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 18/Jul/2022\n * Updated: 12/Ene/2024\n*/mbo-payment-creditcard-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-creditcard-destination-page .mbo-payment-creditcard-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-destination-page .mbo-payment-creditcard-destination-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-creditcard-destination-page .mbo-payment-creditcard-destination-page__body__title{color:var(--color-carbon-darker-1000)}mbo-payment-creditcard-destination-page .mbo-payment-creditcard-destination-page__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}\n"],encapsulation:2}),l})(),I=(()=>{class l{}return l.\u0275fac=function(c){return new(c||l)},l.\u0275mod=i.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=i.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:f}]),h.eM,y.Jx,h.Aj]}),l})()},63674:(R,b,n)=>{n.d(b,{Eg:()=>E,Lo:()=>y,Wl:()=>o,ZC:()=>C,_f:()=>u,br:()=>O,tl:()=>h});var m=n(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},h=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),y={color:"success",key:"paid",label:"Pagada"},o={color:"alert",key:"pending",label:"Por pagar"},C={color:"danger",key:"expired",label:"Vencida"},O={color:"info",key:"recurring",label:"Pago recurrente"},E={color:"info",key:"programmed",label:"Programado"}},66067:(R,b,n)=>{n.d(b,{S6:()=>N,T2:()=>O,UQ:()=>i,mZ:()=>E});var m=n(39904),u=n(6472),y=n(63674),o=n(31707);class O{constructor(d,g,f,I,l,v,c,s,D,M,S){this.id=d,this.type=g,this.name=f,this.nickname=I,this.number=l,this.bank=v,this.isAval=c,this.isProtected=s,this.isOwner=D,this.ownerName=M,this.ownerDocument=S,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,u.initials)(I),this.shortNumber=l.substring(l.length-4),this.descriptionNumber=`${f} ${l}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:v.logo,light:v.logo,standard:v.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(d){this.informationValue||(this.informationValue=d)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(d){return this.currenciesValue.includes(d)}}class E{constructor(d,g){this.id=d,this.type=g}}class N{constructor(d,g,f,I,l,v,c,s,D,M,S,U){this.uuid=d,this.number=g,this.nie=f,this.nickname=I,this.companyId=l,this.companyName=v,this.amount=c,this.registerDate=s,this.expirationDate=D,this.paid=M,this.statusCode=S,this.references=U,this.recurring=U.length>0,this.status=function C(P){switch(P){case o.U.EXPIRED:return y.ZC;case o.U.PENDING:return y.Wl;case o.U.PROGRAMMED:return y.Eg;case o.U.RECURRING:return y.br;default:return y.Lo}}(S)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class i{constructor(d,g,f,I,l,v,c,s){this.uuid=d,this.number=g,this.nickname=f,this.companyId=I,this.companyName=l,this.city=v,this.amount=c,this.isBiller=s}}},31707:(R,b,n)=>{n.d(b,{U:()=>m,f:()=>u});var m=(()=>{return(h=m||(m={})).RECURRING="1",h.EXPIRED="2",h.PENDING="3",h.PROGRAMMED="4",m;var h})(),u=(()=>{return(h=u||(u={})).BILLER="Servicio",h.NON_BILLER="Servicio",h.PSE="Servicio",h.TAX="Impuesto",h.LOAN="Obligaci\xf3n financiera",h.CREDIT_CARD="Obligaci\xf3n financiera",u;var h})()}}]);