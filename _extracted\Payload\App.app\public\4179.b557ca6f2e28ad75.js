(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4179],{108:(E,g,r)=>{r.d(g,{a:()=>y,z:()=>A});var e=r(87903),p=r(53113);function f(t){const{isError:n,message:i,type:m}=t;return{animation:(0,e.jY)(t),title:n?"\xa1Transferencia fallida!":"PENDING"===m?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:i}}function T({isError:t}){return t?[(0,e.wT)("Finalizar","finish","outline"),(0,e.wT)("Volver a intentar","retry")]:[(0,e.wT)("Hacer otra transferencia","retry","outline"),(0,e.wT)("Finalizar","finish")]}function v(t){const{approved:n,pending:i}=t;if(n)return function a(t,n){const{dateFormat:i,timeFormat:m}=new p.ou,c=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),c.push((0,e.cZ)(i,m)),c}(t,n);if(i)return function h(t,n){const{dateFormat:i,timeFormat:m}=new p.ou,c=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),c.push((0,e.cZ)(i,m)),c}(t,i);const{dateFormat:m,timeFormat:c}=new p.ou,d=[(0,e.SP)("TRANSFER"===t.type?"ENVIADO A":"SOLICITADO A",t.contact?.name,t.contact?.number),(0,e._f)("SUMA DE",t.amount)];return t.description&&d.push((0,e.SP)("DESCRIPCI\xd3N","","",t.description)),d.push((0,e.cZ)(m,c)),d}function A(t){const{status:n,transfiya:i}=t;return{actions:T(n),error:n.isError,header:f(n),informations:v(i),skeleton:!1}}function y(t){const n=[],{amount:i,category:m,color:c,date:{dateFormat:d,timeFormat:s},description:u,phoneFormat:o,reference:l}=t;return n.push((0,e.SP)("REFERENCIA",l)),n.push((0,e.fW)("TIPO DE TRANSACCI\xd3N",c,m)),n.push((0,e.SP)("CONTACTO",o)),n.push((0,e._f)("LA SUMA DE",i)),u&&n.push((0,e.Kt)("DESCRIPCI\xd3N",u)),n.push((0,e.cZ)(d,s)),n}},84179:(E,g,r)=>{r.r(g),r.d(g,{MboTransfiyaAskResultPageModule:()=>d});var e=r(17007),p=r(78007),f=r(79798),T=r(15861),a=r(99877),h=r(39904),v=r(95437),A=r(108),y=r(17698),t=r(10464),n=r(78021),i=r(16442);function m(s,u){if(1&s&&(a.\u0275\u0275elementStart(0,"div",4),a.\u0275\u0275element(1,"mbo-header-result",5),a.\u0275\u0275elementEnd()),2&s){const o=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("rightActions",o.rightActions)}}let c=(()=>{class s{constructor(o,l,b){this.ref=o,this.mboProvider=l,this.managerTransfiya=b,this.requesting=!0,this.template=h.$d,this.rightActions=[{id:"btn_transfiya-ask-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfiya-ask-result-page_template"),this.initializatedTransaction()}onAction(o){this.mboProvider.navigation.next("finish"===o?h.Z6.CUSTOMER.PRODUCTS.HOME:h.Z6.TRANSFERS.TRANSFIYA.ASK.DESTINATION)}initializatedTransaction(){var o=this;return(0,T.Z)(function*(){(yield o.managerTransfiya.confirmAsk()).when({success:l=>{o.template=(0,A.z)(l)}},()=>{o.requesting=!1,o.managerTransfiya.reset()})})()}}return s.\u0275fac=function(o){return new(o||s)(a.\u0275\u0275directiveInject(a.ElementRef),a.\u0275\u0275directiveInject(v.ZL),a.\u0275\u0275directiveInject(y.Pm))},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["mbo-transfiya-ask-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfiya-ask-result-page__content","mbo-page__scroller"],["class","mbo-transfiya-ask-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfiya-ask-result-page__body"],["id","crd_transfiya-ask-result-page_template",3,"template","action"],[1,"mbo-transfiya-ask-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(o,l){1&o&&(a.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),a.\u0275\u0275template(2,m,2,1,"div",1),a.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),a.\u0275\u0275listener("action",function(P){return l.onAction(P)}),a.\u0275\u0275elementEnd()()()()),2&o&&(a.\u0275\u0275advance(2),a.\u0275\u0275property("ngIf",!l.requesting),a.\u0275\u0275advance(2),a.\u0275\u0275property("template",l.template))},dependencies:[e.NgIf,t.K,n.c,i.u],styles:["/*!\n * MBO TransfiyaAskResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 08/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-ask-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-ask-result-page .mbo-transfiya-ask-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-ask-result-page .mbo-transfiya-ask-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),s})(),d=(()=>{class s{}return s.\u0275fac=function(o){return new(o||s)},s.\u0275mod=a.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=a.\u0275\u0275defineInjector({imports:[e.CommonModule,p.RouterModule.forChild([{path:"",component:c}]),f.KI,f.cN,f.tu]}),s})()}}]);