(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9999],{9999:(O,m,o)=>{o.r(m),o.d(m,{MboCelToCelSendAmountPageModule:()=>j});var p=o(17007),v=o(78007),c=o(30263),l=o(24495),b=o(39904),C=o(64892),h=o(87903),f=o(95437),A=o(57544),T=o(40914),d=o(17758),M=o(98487),e=o(99877),y=o(48774),S=o(83413),x=o(35641),E=o(45542);const{MAX_CEL_TO_CEL:P,MIN_CEL_TO_CEL:I,MIN_DALE:z}=T.R,u=b.Z6.TRANSFERS.CELTOCEL.SEND;let N=(()=>{class t{constructor(a,n,i,r){this.mboProvider=a,this.requestConfiguration=n,this.managerCelToCel=i,this.cancelProvider=r,this.confirmation=!1,this.backAction={id:"btn_cel-to-cel-send-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(u.DESTINATION)}},this.cancelAction={id:"btn_cel-to-cel-send-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new A.FormControl(void 0,[l.C1])}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerCelToCel.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(u.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({confirmation:a,account:n,amount:i,source:r})=>{this.confirmation=a,this.source=r,i&&this.amountControl.setValue(i);const g=[l.C1,l.LU,(0,l.Go)(n?.bank.id===C.qE.Dale?z:I),(0,l.VV)(P)];(0,h.VN)(r)&&g.push((0,l.vB)(r.amount)),this.amountControl.setValidators(g)}})}}return t.\u0275fac=function(a){return new(a||t)(e.\u0275\u0275directiveInject(f.ZL),e.\u0275\u0275directiveInject(d.ZW),e.\u0275\u0275directiveInject(d.Ey),e.\u0275\u0275directiveInject(M.T))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-cel-to-cel-send-amount-page"]],decls:12,vars:9,consts:[[1,"mbo-cel-to-cel-send-amount-page__content"],[1,"mbo-cel-to-cel-send-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-cel-to-cel-send-amount-page__body"],[1,"mbo-cel-to-cel-send-amount-page__message","subtitle2-medium"],["elementId","txt_cel-to-cel-send-amount_value","label","Valor a transferir","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-cel-to-cel-send-amount-page__footer"],["id","btn_cel-to-cel-send-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(a,n){1&a&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas transferir? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return n.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Continuar"),e.\u0275\u0275elementEnd()()()),2&a&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",n.backAction)("rightAction",n.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("formControl",n.amountControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("color",null==n.source?null:n.source.color)("icon",null==n.source?null:n.source.logo)("title",null==n.source?null:n.source.nickname)("number",null==n.source?null:n.source.publicNumber)("amount",null==n.source?null:n.source.amount),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",n.amountControl.invalid))},dependencies:[y.J,S.D,x.d,E.P],styles:["/*!\n * MBO CelToCelSendAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 08/Nov/2022\n * Updated: 06/Feb/2024\n*/mbo-cel-to-cel-send-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);--pvt-checkbox-margin-top: var(--sizing-x20);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__body bocc-checkbox-label{margin-top:var(--pvt-checkbox-margin-top)}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-cel-to-cel-send-amount-page .mbo-cel-to-cel-send-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-cel-to-cel-send-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20);--pvt-checkbox-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),t})(),j=(()=>{class t{}return t.\u0275fac=function(a){return new(a||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[p.CommonModule,v.RouterModule.forChild([{path:"",component:N}]),c.Jx,c.D1,c.dH,c.P8]}),t})()}}]);