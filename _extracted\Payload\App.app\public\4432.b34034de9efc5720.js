(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4432],{44477:(O,p,t)=>{t.d(p,{p:()=>f,c:()=>I});var m=t(15861),l=t(87956),d=t(53113),c=t(98699);class v{constructor(u,n){this.invoice=u,this.source=n}}function P(e){return new v(e.invoice,e.source)}var g=t(71776),b=t(39904),i=t(87903),E=t(42168),h=t(84757),a=t(99877);let C=(()=>{class e{constructor(n){this.http=n}send(n){return(0,E.firstValueFrom)(this.http.post(b.bV.PAYMENTS.INVOICE,function M(e){return[{acctIdFrom:e.source.id,acctNickname:e.source.nickname,acctTypeFrom:e.source.type,amt:e.invoice.amount.toString(),nie:e.invoice.nie,invoiceNum:e.invoice.number,pmtCodServ:e.invoice.companyId,toEntity:e.invoice.companyName,toNickname:e.invoice.nickname,expDt:e.invoice.expirationDate.toISOString()}]}(n)).pipe((0,h.map)(([r])=>(0,i.l1)(r,"SUCCESS")))).catch(r=>(0,i.rU)(r))}}return e.\u0275fac=function(n){return new(n||e)(a.\u0275\u0275inject(g.HttpClient))},e.\u0275prov=a.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var y=t(20691);let o=(()=>{class e extends y.Store{constructor(n){super({confirmation:!1}),n.subscribes(b.PU,()=>{this.reset()})}setInvoice(n){this.reduce(r=>({...r,invoice:n}))}getInvoice(){return this.select(({invoice:n})=>n)}setSource(n){this.reduce(r=>({...r,source:n}))}getSource(){return this.select(({source:n})=>n)}getConfirmation(){return this.select(({confirmation:n})=>n)}}return e.\u0275fac=function(n){return new(n||e)(a.\u0275\u0275inject(l.Yd))},e.\u0275prov=a.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),f=(()=>{class e{constructor(n,r,D){this.repository=n,this.store=r,this.eventBusService=D}setInvoice(n){try{return c.Either.success(this.store.setInvoice(n))}catch({message:r}){return c.Either.failure({message:r})}}setSource(n){try{return c.Either.success(this.store.setSource(n))}catch({message:r}){return c.Either.failure({message:r})}}reset(){try{return c.Either.success(this.store.reset())}catch({message:n}){return c.Either.failure({message:n})}}send(){var n=this;return(0,m.Z)(function*(){const r=P(n.store.currentState),D=yield n.execute(r);return n.eventBusService.emit(D.channel),c.Either.success({invoice:r,status:D})})()}execute(n){try{return this.repository.send(n)}catch({message:r}){return Promise.resolve(d.LN.error(r))}}}return e.\u0275fac=function(n){return new(n||e)(a.\u0275\u0275inject(C),a.\u0275\u0275inject(o),a.\u0275\u0275inject(l.Yd))},e.\u0275prov=a.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var s=t(19799);let I=(()=>{class e{constructor(n,r,D){this.products=n,this.invoices=r,this.store=D}source(n){var r=this;return(0,m.Z)(function*(){try{const D=yield r.products.requestAccountsForTransfer();let R=r.store.getInvoice();return!R&&n&&(R=(yield r.invoices.request()).find(({uuid:N})=>n===N),r.store.setInvoice(R)),c.Either.success({invoice:R,products:D})}catch({message:D}){return c.Either.failure({message:D})}})()}confirmation(){try{const n=P(this.store.currentState);return c.Either.success({payment:n})}catch({message:n}){return c.Either.failure({message:n})}}}return e.\u0275fac=function(n){return new(n||e)(a.\u0275\u0275inject(l.hM),a.\u0275\u0275inject(s.W),a.\u0275\u0275inject(o))},e.\u0275prov=a.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},40349:(O,p,t)=>{t.d(p,{f:()=>P});var m=t(39904),l=t(95437),d=t(30263),c=t(44477),v=t(99877);let P=(()=>{class g{constructor(i,E,h){this.modalConfirmation=i,this.mboProvider=E,this.managerInvoice=h}execute(i=!0){i?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(m.Z6.PAYMENTS.HOME)}}return g.\u0275fac=function(i){return new(i||g)(v.\u0275\u0275inject(d.$e),v.\u0275\u0275inject(l.ZL),v.\u0275\u0275inject(c.p))},g.\u0275prov=v.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},84432:(O,p,t)=>{t.r(p),t.d(p,{MboPaymentInvoiceSourcePageModule:()=>f});var m=t(17007),l=t(78007),d=t(79798),c=t(30263),v=t(15861),M=t(39904),P=t(95437),g=t(44477),b=t(40349),i=t(99877),E=t(6661),h=t(4663),a=t(48774);function C(s,I){if(1&s&&i.\u0275\u0275element(0,"bocc-card-service",6),2&s){const e=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",null==e.invoice?null:e.invoice.registerFormat)("status",null==e.invoice?null:e.invoice.status.label)("statusColor",null==e.invoice?null:e.invoice.status.color)("title",null==e.invoice?null:e.invoice.nickname)("number",null==e.invoice?null:e.invoice.number)("subtitle",null==e.invoice?null:e.invoice.companyName)("amount",null==e.invoice?null:e.invoice.amount)}}const y=M.Z6.PAYMENTS.SERVICES;let o=(()=>{class s{constructor(e,u,n,r,D){this.activateRoute=e,this.mboProvider=u,this.requestConfiguration=n,this.managerInvoice=r,this.cancelProvider=D,this.confirmation=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_payment-invoice-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(y.HOME)}},this.cancelAction={id:"btn_payment-invoice-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(e){this.managerInvoice.setSource(e).when({success:()=>{this.mboProvider.navigation.next(y.INVOICE.CONFIRMATION)}})}initializatedConfiguration(){var e=this;return(0,v.Z)(function*(){const{invoiceUuid:u}=e.activateRoute.snapshot.queryParams;(yield e.requestConfiguration.source(u)).when({success:({invoice:n,products:r})=>{if(!n)return e.mboProvider.navigation.back(y.HOME);e.invoice=n,e.products=r}},()=>{e.requesting=!1})})()}}return s.\u0275fac=function(e){return new(e||s)(i.\u0275\u0275directiveInject(l.ActivatedRoute),i.\u0275\u0275directiveInject(P.ZL),i.\u0275\u0275directiveInject(g.c),i.\u0275\u0275directiveInject(g.p),i.\u0275\u0275directiveInject(b.f))},s.\u0275cmp=i.\u0275\u0275defineComponent({type:s,selectors:[["mbo-payment-invoice-source-page"]],decls:7,vars:5,consts:[[1,"mbo-payment-invoice-source-page__content"],[1,"mbo-payment-invoice-source-page__header"],["title","Origen","progress","50%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-source-page__body"],[3,"header","status","statusColor","title","number","subtitle","amount",4,"ngIf"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","select"],[3,"header","status","statusColor","title","number","subtitle","amount"]],template:function(e,u){1&e&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275element(2,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",3),i.\u0275\u0275template(4,C,1,7,"bocc-card-service",4),i.\u0275\u0275elementStart(5,"mbo-product-source-selector",5),i.\u0275\u0275listener("select",function(r){return u.onProduct(r)}),i.\u0275\u0275text(6," \xbfDesde d\xf3nde deseas pagar? "),i.\u0275\u0275elementEnd()()()),2&e&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("leftAction",u.backAction)("rightAction",u.cancelAction),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",u.invoice),i.\u0275\u0275advance(1),i.\u0275\u0275property("skeleton",u.requesting)("products",u.products))},dependencies:[m.NgIf,E.S,h.c,a.J],styles:["/*!\n * BOCC PaymentInvoiceSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 31/Jul/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-invoice-source-page .mbo-payment-invoice-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-invoice-source-page .mbo-payment-invoice-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}\n"],encapsulation:2}),s})(),f=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=i.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=i.\u0275\u0275defineInjector({imports:[m.CommonModule,l.RouterModule.forChild([{path:"",component:o}]),c.S8,d.cV,c.Jx]}),s})()},63674:(O,p,t)=>{t.d(p,{Eg:()=>g,Lo:()=>c,Wl:()=>v,ZC:()=>M,_f:()=>l,br:()=>P,tl:()=>d});var m=t(29306);const l={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},d=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),c={color:"success",key:"paid",label:"Pagada"},v={color:"alert",key:"pending",label:"Por pagar"},M={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},g={color:"info",key:"programmed",label:"Programado"}},66067:(O,p,t)=>{t.d(p,{S6:()=>b,T2:()=>P,UQ:()=>i,mZ:()=>g});var m=t(39904),l=t(6472),c=t(63674),v=t(31707);class P{constructor(h,a,C,y,o,f,s,I,e,u,n){this.id=h,this.type=a,this.name=C,this.nickname=y,this.number=o,this.bank=f,this.isAval=s,this.isProtected=I,this.isOwner=e,this.ownerName=u,this.ownerDocument=n,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,l.initials)(y),this.shortNumber=o.substring(o.length-4),this.descriptionNumber=`${C} ${o}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:f.logo,light:f.logo,standard:f.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(h){this.informationValue||(this.informationValue=h)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(h){return this.currenciesValue.includes(h)}}class g{constructor(h,a){this.id=h,this.type=a}}class b{constructor(h,a,C,y,o,f,s,I,e,u,n,r){this.uuid=h,this.number=a,this.nie=C,this.nickname=y,this.companyId=o,this.companyName=f,this.amount=s,this.registerDate=I,this.expirationDate=e,this.paid=u,this.statusCode=n,this.references=r,this.recurring=r.length>0,this.status=function M(E){switch(E){case v.U.EXPIRED:return c.ZC;case v.U.PENDING:return c.Wl;case v.U.PROGRAMMED:return c.Eg;case v.U.RECURRING:return c.br;default:return c.Lo}}(n)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class i{constructor(h,a,C,y,o,f,s,I){this.uuid=h,this.number=a,this.nickname=C,this.companyId=y,this.companyName=o,this.city=f,this.amount=s,this.isBiller=I}}},19799:(O,p,t)=>{t.d(p,{e:()=>C,W:()=>y});var m=t(71776),l=t(39904),d=t(87956),c=t(98699),v=t(42168),M=t(84757),P=t(53113),g=t(33876),b=t(66067);var a=t(99877);let C=(()=>{class o{constructor(s,I){this.http=s,I.subscribes(l.PU,()=>{this.destroy()}),this.billers$=(0,c.securePromise)(()=>(0,v.firstValueFrom)(this.http.get(l.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,M.map)(({content:e})=>e.map(u=>function h(o){return new b.UQ((0,g.v4)(),o.nie,o.nickname,o.orgIdNum,o.orgName,o.city,+o.amt,(0,c.parseBoolean)(o.biller))}(u))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return o.\u0275fac=function(s){return new(s||o)(a.\u0275\u0275inject(m.HttpClient),a.\u0275\u0275inject(d.Yd))},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),y=(()=>{class o{constructor(s,I){this.http=s,I.subscribes(l.PU,()=>{this.destroy()}),this.invoices$=(0,c.securePromise)(()=>(0,v.firstValueFrom)(this.http.get(l.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,M.map)(({content:e})=>e.map(u=>function E(o){const f=o.refInfo.map(s=>function i(o){return new b.mZ(o.refId,o.refType)}(s));return new b.S6((0,g.v4)(),o.invoiceNum,o.nie,o.nickName,o.orgIdNum,o.orgName,+o.totalCurAmt,new P.ou(o.effDt),new P.ou(o.expDt),(0,c.parseBoolean)(o.payDone),o.state,f)}(u))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return o.\u0275fac=function(s){return new(s||o)(a.\u0275\u0275inject(m.HttpClient),a.\u0275\u0275inject(d.Yd))},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},31707:(O,p,t)=>{t.d(p,{U:()=>m,f:()=>l});var m=(()=>{return(d=m||(m={})).RECURRING="1",d.EXPIRED="2",d.PENDING="3",d.PROGRAMMED="4",m;var d})(),l=(()=>{return(d=l||(l={})).BILLER="Servicio",d.NON_BILLER="Servicio",d.PSE="Servicio",d.TAX="Impuesto",d.LOAN="Obligaci\xf3n financiera",d.CREDIT_CARD="Obligaci\xf3n financiera",l;var d})()}}]);