(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8184],{98184:(pt,M,y)=>{y.r(M),y.d(M,{BlockScrollStrategy:()=>T,CdkConnectedOverlay:()=>ct,CdkOverlayOrigin:()=>q,CdkScrollable:()=>C.CdkScrollable,CloseScrollStrategy:()=>Y,ConnectedOverlayPositionChange:()=>F,ConnectionPositionPair:()=>et,FlexibleConnectedPositionStrategy:()=>Z,FullscreenOverlayContainer:()=>ut,GlobalPositionStrategy:()=>G,NoopScrollStrategy:()=>D,Overlay:()=>P,OverlayConfig:()=>B,OverlayContainer:()=>w,OverlayKeyboardDispatcher:()=>H,OverlayModule:()=>ft,OverlayOutsideClickDispatcher:()=>N,OverlayPositionBuilder:()=>J,OverlayRef:()=>W,RepositionScrollStrategy:()=>X,STANDARD_DROPDOWN_ADJACENT_POSITIONS:()=>lt,STANDARD_DROPDOWN_BELOW_POSITIONS:()=>rt,ScrollDispatcher:()=>C.ScrollDispatcher,ScrollStrategyOptions:()=>V,ScrollingVisibility:()=>it,ViewportRuler:()=>C.ViewportRuler,validateHorizontalPosition:()=>ot,validateVerticalPosition:()=>st});var C=y(94304),m=y(17007),l=y(99877),_=y(32565),b=y(66354),E=y(84757),k=y(32044),x=y(8504),p=y(42168),A=y(86058);const L=(0,b.supportsScrollBehavior)();class T{constructor(t,e){this._viewportRuler=t,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=e}attach(){}enable(){if(this._canBeEnabled()){const t=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=t.style.left||"",this._previousHTMLStyles.top=t.style.top||"",t.style.left=(0,_.coerceCssPixelValue)(-this._previousScrollPosition.left),t.style.top=(0,_.coerceCssPixelValue)(-this._previousScrollPosition.top),t.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){const t=this._document.documentElement,i=t.style,s=this._document.body.style,n=i.scrollBehavior||"",r=s.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,t.classList.remove("cdk-global-scrollblock"),L&&(i.scrollBehavior=s.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),L&&(i.scrollBehavior=n,s.scrollBehavior=r)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;const e=this._document.body,i=this._viewportRuler.getViewportSize();return e.scrollHeight>i.height||e.scrollWidth>i.width}}class Y{constructor(t,e,i,s){this._scrollDispatcher=t,this._ngZone=e,this._viewportRuler=i,this._config=s,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(t){this._overlayRef=t}enable(){if(this._scrollSubscription)return;const t=this._scrollDispatcher.scrolled(0).pipe((0,E.filter)(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=t.subscribe(()=>{const e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=t.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}class D{enable(){}disable(){}attach(){}}function R(o,t){return t.some(e=>o.bottom<e.top||o.top>e.bottom||o.right<e.left||o.left>e.right)}function I(o,t){return t.some(e=>o.top<e.top||o.bottom>e.bottom||o.left<e.left||o.right>e.right)}class X{constructor(t,e,i,s){this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=i,this._config=s,this._scrollSubscription=null}attach(t){this._overlayRef=t}enable(){this._scrollSubscription||(this._scrollSubscription=this._scrollDispatcher.scrolled(this._config?this._config.scrollThrottle:0).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){const e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:s}=this._viewportRuler.getViewportSize();R(e,[{width:i,height:s,bottom:s,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}}))}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}let V=(()=>{class o{constructor(e,i,s,n){this._scrollDispatcher=e,this._viewportRuler=i,this._ngZone=s,this.noop=()=>new D,this.close=r=>new Y(this._scrollDispatcher,this._ngZone,this._viewportRuler,r),this.block=()=>new T(this._viewportRuler,this._document),this.reposition=r=>new X(this._scrollDispatcher,this._viewportRuler,this._ngZone,r),this._document=n}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(C.ScrollDispatcher),l.\u0275\u0275inject(C.ViewportRuler),l.\u0275\u0275inject(l.NgZone),l.\u0275\u0275inject(m.DOCUMENT))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();class B{constructor(t){if(this.scrollStrategy=new D,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,t){const e=Object.keys(t);for(const i of e)void 0!==t[i]&&(this[i]=t[i])}}}class et{constructor(t,e,i,s,n){this.offsetX=i,this.offsetY=s,this.panelClass=n,this.originX=t.originX,this.originY=t.originY,this.overlayX=e.overlayX,this.overlayY=e.overlayY}}class it{}class F{constructor(t,e){this.connectionPair=t,this.scrollableViewProperties=e}}function st(o,t){if("top"!==t&&"bottom"!==t&&"center"!==t)throw Error(`ConnectedPosition: Invalid ${o} "${t}". Expected "top", "bottom" or "center".`)}function ot(o,t){if("start"!==t&&"end"!==t&&"center"!==t)throw Error(`ConnectedPosition: Invalid ${o} "${t}". Expected "start", "end" or "center".`)}let j=(()=>{class o{constructor(e){this._attachedOverlays=[],this._document=e}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){const i=this._attachedOverlays.indexOf(e);i>-1&&this._attachedOverlays.splice(i,1),0===this._attachedOverlays.length&&this.detach()}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(m.DOCUMENT))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),H=(()=>{class o extends j{constructor(e,i){super(e),this._ngZone=i,this._keydownListener=s=>{const n=this._attachedOverlays;for(let r=n.length-1;r>-1;r--)if(n[r]._keydownEvents.observers.length>0){const a=n[r]._keydownEvents;this._ngZone?this._ngZone.run(()=>a.next(s)):a.next(s);break}}}add(e){super.add(e),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(l.NgZone,8))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),N=(()=>{class o extends j{constructor(e,i,s){super(e),this._platform=i,this._ngZone=s,this._cursorStyleIsSet=!1,this._pointerDownListener=n=>{this._pointerDownEventTarget=(0,b._getEventTarget)(n)},this._clickListener=n=>{const r=(0,b._getEventTarget)(n),a="click"===n.type&&this._pointerDownEventTarget?this._pointerDownEventTarget:r;this._pointerDownEventTarget=null;const c=this._attachedOverlays.slice();for(let d=c.length-1;d>-1;d--){const h=c[d];if(h._outsidePointerEvents.observers.length<1||!h.hasAttached())continue;if(h.overlayElement.contains(r)||h.overlayElement.contains(a))break;const f=h._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>f.next(n)):f.next(n)}}}add(e){if(super.add(e),!this._isAttached){const i=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(i)):this._addEventListeners(i),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){const e=this._document.body;e.removeEventListener("pointerdown",this._pointerDownListener,!0),e.removeEventListener("click",this._clickListener,!0),e.removeEventListener("auxclick",this._clickListener,!0),e.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(e.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(e){e.addEventListener("pointerdown",this._pointerDownListener,!0),e.addEventListener("click",this._clickListener,!0),e.addEventListener("auxclick",this._clickListener,!0),e.addEventListener("contextmenu",this._clickListener,!0)}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(b.Platform),l.\u0275\u0275inject(l.NgZone,8))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),w=(()=>{class o{constructor(e,i){this._platform=i,this._document=e}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const e="cdk-overlay-container";if(this._platform.isBrowser||(0,b._isTestEnvironment)()){const s=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let n=0;n<s.length;n++)s[n].remove()}const i=this._document.createElement("div");i.classList.add(e),(0,b._isTestEnvironment)()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(b.Platform))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();class W{constructor(t,e,i,s,n,r,a,c,d,h=!1){this._portalOutlet=t,this._host=e,this._pane=i,this._config=s,this._ngZone=n,this._keyboardDispatcher=r,this._document=a,this._location=c,this._outsideClickDispatcher=d,this._animationsDisabled=h,this._backdropElement=null,this._backdropClick=new p.Subject,this._attachments=new p.Subject,this._detachments=new p.Subject,this._locationChanges=p.Subscription.EMPTY,this._backdropClickHandler=f=>this._backdropClick.next(f),this._backdropTransitionendHandler=f=>{this._disposeBackdrop(f.target)},this._keydownEvents=new p.Subject,this._outsidePointerEvents=new p.Subject,s.scrollStrategy&&(this._scrollStrategy=s.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=s.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(t){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);const e=this._portalOutlet.attach(t);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._ngZone.onStable.pipe((0,E.take)(1)).subscribe(()=>{this.hasAttached()&&this.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),"function"==typeof e?.onDestroy&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();const t=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenStable(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),t}dispose(){const t=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._previousHostParent=this._pane=this._host=null,t&&this._detachments.next(),this._detachments.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(t){t!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=t,this.hasAttached()&&(t.attach(this),this.updatePosition()))}updateSize(t){this._config={...this._config,...t},this._updateElementSize()}setDirection(t){this._config={...this._config,direction:t},this._updateElementDirection()}addPanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!0)}removePanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!1)}getDirection(){const t=this._config.direction;return t?"string"==typeof t?t:t.value:"ltr"}updateScrollStrategy(t){t!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=t,this.hasAttached()&&(t.attach(this),t.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;const t=this._pane.style;t.width=(0,_.coerceCssPixelValue)(this._config.width),t.height=(0,_.coerceCssPixelValue)(this._config.height),t.minWidth=(0,_.coerceCssPixelValue)(this._config.minWidth),t.minHeight=(0,_.coerceCssPixelValue)(this._config.minHeight),t.maxWidth=(0,_.coerceCssPixelValue)(this._config.maxWidth),t.maxHeight=(0,_.coerceCssPixelValue)(this._config.maxHeight)}_togglePointerEvents(t){this._pane.style.pointerEvents=t?"":"none"}_attachBackdrop(){const t="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(t)})}):this._backdropElement.classList.add(t)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){const t=this._backdropElement;if(t){if(this._animationsDisabled)return void this._disposeBackdrop(t);t.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{t.addEventListener("transitionend",this._backdropTransitionendHandler)}),t.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(t)},500))}}_toggleClasses(t,e,i){const s=(0,_.coerceArray)(e||[]).filter(n=>!!n);s.length&&(i?t.classList.add(...s):t.classList.remove(...s))}_detachContentWhenStable(){this._ngZone.runOutsideAngular(()=>{const t=this._ngZone.onStable.pipe((0,E.takeUntil)((0,p.merge)(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||0===this._pane.children.length)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),t.unsubscribe())})})}_disposeScrollStrategy(){const t=this._scrollStrategy;t&&(t.disable(),t.detach&&t.detach())}_disposeBackdrop(t){t&&(t.removeEventListener("click",this._backdropClickHandler),t.removeEventListener("transitionend",this._backdropTransitionendHandler),t.remove(),this._backdropElement===t&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}}const z="cdk-overlay-connected-position-bounding-box",nt=/([A-Za-z%]+)$/;class Z{get positions(){return this._preferredPositions}constructor(t,e,i,s,n){this._viewportRuler=e,this._document=i,this._platform=s,this._overlayContainer=n,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new p.Subject,this._resizeSubscription=p.Subscription.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(t)}attach(t){this._validatePositions(),t.hostElement.classList.add(z),this._overlayRef=t,this._boundingBox=t.hostElement,this._pane=t.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition)return void this.reapplyLastPosition();this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const t=this._originRect,e=this._overlayRect,i=this._viewportRect,s=this._containerRect,n=[];let r;for(let a of this._preferredPositions){let c=this._getOriginPoint(t,s,a),d=this._getOverlayPoint(c,e,a),h=this._getOverlayFit(d,e,i,a);if(h.isCompletelyWithinViewport)return this._isPushed=!1,void this._applyPosition(a,c);this._canFitWithFlexibleDimensions(h,d,i)?n.push({position:a,origin:c,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(c,a)}):(!r||r.overlayFit.visibleArea<h.visibleArea)&&(r={overlayFit:h,overlayPoint:d,originPoint:c,position:a,overlayRect:e})}if(n.length){let a=null,c=-1;for(const d of n){const h=d.boundingBoxRect.width*d.boundingBoxRect.height*(d.position.weight||1);h>c&&(c=h,a=d)}return this._isPushed=!1,void this._applyPosition(a.position,a.origin)}if(this._canPush)return this._isPushed=!0,void this._applyPosition(r.position,r.originPoint);this._applyPosition(r.position,r.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&S(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(z),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;const t=this._lastPosition;if(t){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const e=this._getOriginPoint(this._originRect,this._containerRect,t);this._applyPosition(t,e)}else this.apply()}withScrollableContainers(t){return this._scrollables=t,this}withPositions(t){return this._preferredPositions=t,-1===t.indexOf(this._lastPosition)&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(t){return this._viewportMargin=t,this}withFlexibleDimensions(t=!0){return this._hasFlexibleDimensions=t,this}withGrowAfterOpen(t=!0){return this._growAfterOpen=t,this}withPush(t=!0){return this._canPush=t,this}withLockedPosition(t=!0){return this._positionLocked=t,this}setOrigin(t){return this._origin=t,this}withDefaultOffsetX(t){return this._offsetX=t,this}withDefaultOffsetY(t){return this._offsetY=t,this}withTransformOriginOn(t){return this._transformOriginSelector=t,this}_getOriginPoint(t,e,i){let s,n;if("center"==i.originX)s=t.left+t.width/2;else{const r=this._isRtl()?t.right:t.left,a=this._isRtl()?t.left:t.right;s="start"==i.originX?r:a}return e.left<0&&(s-=e.left),n="center"==i.originY?t.top+t.height/2:"top"==i.originY?t.top:t.bottom,e.top<0&&(n-=e.top),{x:s,y:n}}_getOverlayPoint(t,e,i){let s,n;return s="center"==i.overlayX?-e.width/2:"start"===i.overlayX?this._isRtl()?-e.width:0:this._isRtl()?0:-e.width,n="center"==i.overlayY?-e.height/2:"top"==i.overlayY?0:-e.height,{x:t.x+s,y:t.y+n}}_getOverlayFit(t,e,i,s){const n=U(e);let{x:r,y:a}=t,c=this._getOffset(s,"x"),d=this._getOffset(s,"y");c&&(r+=c),d&&(a+=d);let O=0-a,u=a+n.height-i.height,g=this._subtractOverflows(n.width,0-r,r+n.width-i.width),v=this._subtractOverflows(n.height,O,u),tt=g*v;return{visibleArea:tt,isCompletelyWithinViewport:n.width*n.height===tt,fitsInViewportVertically:v===n.height,fitsInViewportHorizontally:g==n.width}}_canFitWithFlexibleDimensions(t,e,i){if(this._hasFlexibleDimensions){const s=i.bottom-e.y,n=i.right-e.x,r=K(this._overlayRef.getConfig().minHeight),a=K(this._overlayRef.getConfig().minWidth);return(t.fitsInViewportVertically||null!=r&&r<=s)&&(t.fitsInViewportHorizontally||null!=a&&a<=n)}return!1}_pushOverlayOnScreen(t,e,i){if(this._previousPushAmount&&this._positionLocked)return{x:t.x+this._previousPushAmount.x,y:t.y+this._previousPushAmount.y};const s=U(e),n=this._viewportRect,r=Math.max(t.x+s.width-n.width,0),a=Math.max(t.y+s.height-n.height,0),c=Math.max(n.top-i.top-t.y,0),d=Math.max(n.left-i.left-t.x,0);let h=0,f=0;return h=s.width<=n.width?d||-r:t.x<this._viewportMargin?n.left-i.left-t.x:0,f=s.height<=n.height?c||-a:t.y<this._viewportMargin?n.top-i.top-t.y:0,this._previousPushAmount={x:h,y:f},{x:t.x+h,y:t.y+f}}_applyPosition(t,e){if(this._setTransformOrigin(t),this._setOverlayElementStyles(e,t),this._setBoundingBoxStyles(e,t),t.panelClass&&this._addPanelClasses(t.panelClass),this._lastPosition=t,this._positionChanges.observers.length){const i=this._getScrollVisibility(),s=new F(t,i);this._positionChanges.next(s)}this._isInitialRender=!1}_setTransformOrigin(t){if(!this._transformOriginSelector)return;const e=this._boundingBox.querySelectorAll(this._transformOriginSelector);let i,s=t.overlayY;i="center"===t.overlayX?"center":this._isRtl()?"start"===t.overlayX?"right":"left":"start"===t.overlayX?"left":"right";for(let n=0;n<e.length;n++)e[n].style.transformOrigin=`${i} ${s}`}_calculateBoundingBoxRect(t,e){const i=this._viewportRect,s=this._isRtl();let n,r,a,h,f,O;if("top"===e.overlayY)r=t.y,n=i.height-r+this._viewportMargin;else if("bottom"===e.overlayY)a=i.height-t.y+2*this._viewportMargin,n=i.height-a+this._viewportMargin;else{const u=Math.min(i.bottom-t.y+i.top,t.y),g=this._lastBoundingBoxSize.height;n=2*u,r=t.y-u,n>g&&!this._isInitialRender&&!this._growAfterOpen&&(r=t.y-g/2)}if("end"===e.overlayX&&!s||"start"===e.overlayX&&s)O=i.width-t.x+this._viewportMargin,h=t.x-this._viewportMargin;else if("start"===e.overlayX&&!s||"end"===e.overlayX&&s)f=t.x,h=i.right-t.x;else{const u=Math.min(i.right-t.x+i.left,t.x),g=this._lastBoundingBoxSize.width;h=2*u,f=t.x-u,h>g&&!this._isInitialRender&&!this._growAfterOpen&&(f=t.x-g/2)}return{top:r,left:f,bottom:a,right:O,width:h,height:n}}_setBoundingBoxStyles(t,e){const i=this._calculateBoundingBoxRect(t,e);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));const s={};if(this._hasExactPosition())s.top=s.left="0",s.bottom=s.right=s.maxHeight=s.maxWidth="",s.width=s.height="100%";else{const n=this._overlayRef.getConfig().maxHeight,r=this._overlayRef.getConfig().maxWidth;s.height=(0,_.coerceCssPixelValue)(i.height),s.top=(0,_.coerceCssPixelValue)(i.top),s.bottom=(0,_.coerceCssPixelValue)(i.bottom),s.width=(0,_.coerceCssPixelValue)(i.width),s.left=(0,_.coerceCssPixelValue)(i.left),s.right=(0,_.coerceCssPixelValue)(i.right),s.alignItems="center"===e.overlayX?"center":"end"===e.overlayX?"flex-end":"flex-start",s.justifyContent="center"===e.overlayY?"center":"bottom"===e.overlayY?"flex-end":"flex-start",n&&(s.maxHeight=(0,_.coerceCssPixelValue)(n)),r&&(s.maxWidth=(0,_.coerceCssPixelValue)(r))}this._lastBoundingBoxSize=i,S(this._boundingBox.style,s)}_resetBoundingBoxStyles(){S(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){S(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(t,e){const i={},s=this._hasExactPosition(),n=this._hasFlexibleDimensions,r=this._overlayRef.getConfig();if(s){const h=this._viewportRuler.getViewportScrollPosition();S(i,this._getExactOverlayY(e,t,h)),S(i,this._getExactOverlayX(e,t,h))}else i.position="static";let a="",c=this._getOffset(e,"x"),d=this._getOffset(e,"y");c&&(a+=`translateX(${c}px) `),d&&(a+=`translateY(${d}px)`),i.transform=a.trim(),r.maxHeight&&(s?i.maxHeight=(0,_.coerceCssPixelValue)(r.maxHeight):n&&(i.maxHeight="")),r.maxWidth&&(s?i.maxWidth=(0,_.coerceCssPixelValue)(r.maxWidth):n&&(i.maxWidth="")),S(this._pane.style,i)}_getExactOverlayY(t,e,i){let s={top:"",bottom:""},n=this._getOverlayPoint(e,this._overlayRect,t);return this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i)),"bottom"===t.overlayY?s.bottom=this._document.documentElement.clientHeight-(n.y+this._overlayRect.height)+"px":s.top=(0,_.coerceCssPixelValue)(n.y),s}_getExactOverlayX(t,e,i){let r,s={left:"",right:""},n=this._getOverlayPoint(e,this._overlayRect,t);return this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i)),r=this._isRtl()?"end"===t.overlayX?"left":"right":"end"===t.overlayX?"right":"left","right"===r?s.right=this._document.documentElement.clientWidth-(n.x+this._overlayRect.width)+"px":s.left=(0,_.coerceCssPixelValue)(n.x),s}_getScrollVisibility(){const t=this._getOriginRect(),e=this._pane.getBoundingClientRect(),i=this._scrollables.map(s=>s.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:I(t,i),isOriginOutsideView:R(t,i),isOverlayClipped:I(e,i),isOverlayOutsideView:R(e,i)}}_subtractOverflows(t,...e){return e.reduce((i,s)=>i-Math.max(s,0),t)}_getNarrowedViewportRect(){const t=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+t-this._viewportMargin,bottom:i.top+e-this._viewportMargin,width:t-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return"rtl"===this._overlayRef.getDirection()}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(t,e){return"x"===e?null==t.offsetX?this._offsetX:t.offsetX:null==t.offsetY?this._offsetY:t.offsetY}_validatePositions(){}_addPanelClasses(t){this._pane&&(0,_.coerceArray)(t).forEach(e=>{""!==e&&-1===this._appliedPanelClasses.indexOf(e)&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(t=>{this._pane.classList.remove(t)}),this._appliedPanelClasses=[])}_getOriginRect(){const t=this._origin;if(t instanceof l.ElementRef)return t.nativeElement.getBoundingClientRect();if(t instanceof Element)return t.getBoundingClientRect();const e=t.width||0,i=t.height||0;return{top:t.y,bottom:t.y+i,left:t.x,right:t.x+e,height:i,width:e}}}function S(o,t){for(let e in t)t.hasOwnProperty(e)&&(o[e]=t[e]);return o}function K(o){if("number"!=typeof o&&null!=o){const[t,e]=o.split(nt);return e&&"px"!==e?null:parseFloat(t)}return o||null}function U(o){return{top:Math.floor(o.top),right:Math.floor(o.right),bottom:Math.floor(o.bottom),left:Math.floor(o.left),width:Math.floor(o.width),height:Math.floor(o.height)}}const rt=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"}],lt=[{originX:"end",originY:"top",overlayX:"start",overlayY:"top"},{originX:"end",originY:"bottom",overlayX:"start",overlayY:"bottom"},{originX:"start",originY:"top",overlayX:"end",overlayY:"top"},{originX:"start",originY:"bottom",overlayX:"end",overlayY:"bottom"}],$="cdk-global-overlay-wrapper";class G{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(t){const e=t.getConfig();this._overlayRef=t,this._width&&!e.width&&t.updateSize({width:this._width}),this._height&&!e.height&&t.updateSize({height:this._height}),t.hostElement.classList.add($),this._isDisposed=!1}top(t=""){return this._bottomOffset="",this._topOffset=t,this._alignItems="flex-start",this}left(t=""){return this._xOffset=t,this._xPosition="left",this}bottom(t=""){return this._topOffset="",this._bottomOffset=t,this._alignItems="flex-end",this}right(t=""){return this._xOffset=t,this._xPosition="right",this}start(t=""){return this._xOffset=t,this._xPosition="start",this}end(t=""){return this._xOffset=t,this._xPosition="end",this}width(t=""){return this._overlayRef?this._overlayRef.updateSize({width:t}):this._width=t,this}height(t=""){return this._overlayRef?this._overlayRef.updateSize({height:t}):this._height=t,this}centerHorizontally(t=""){return this.left(t),this._xPosition="center",this}centerVertically(t=""){return this.top(t),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;const t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:s,height:n,maxWidth:r,maxHeight:a}=i,c=!("100%"!==s&&"100vw"!==s||r&&"100%"!==r&&"100vw"!==r),d=!("100%"!==n&&"100vh"!==n||a&&"100%"!==a&&"100vh"!==a),h=this._xPosition,f=this._xOffset,O="rtl"===this._overlayRef.getConfig().direction;let u="",g="",v="";c?v="flex-start":"center"===h?(v="center",O?g=f:u=f):O?"left"===h||"end"===h?(v="flex-end",u=f):("right"===h||"start"===h)&&(v="flex-start",g=f):"left"===h||"start"===h?(v="flex-start",u=f):("right"===h||"end"===h)&&(v="flex-end",g=f),t.position=this._cssPosition,t.marginLeft=c?"0":u,t.marginTop=d?"0":this._topOffset,t.marginBottom=this._bottomOffset,t.marginRight=c?"0":g,e.justifyContent=v,e.alignItems=d?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;const t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,i=e.style;e.classList.remove($),i.justifyContent=i.alignItems=t.marginTop=t.marginBottom=t.marginLeft=t.marginRight=t.position="",this._overlayRef=null,this._isDisposed=!0}}let J=(()=>{class o{constructor(e,i,s,n){this._viewportRuler=e,this._document=i,this._platform=s,this._overlayContainer=n}global(){return new G}flexibleConnectedTo(e){return new Z(e,this._viewportRuler,this._document,this._platform,this._overlayContainer)}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(C.ViewportRuler),l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(b.Platform),l.\u0275\u0275inject(w))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),at=0,P=(()=>{class o{constructor(e,i,s,n,r,a,c,d,h,f,O,u){this.scrollStrategies=e,this._overlayContainer=i,this._componentFactoryResolver=s,this._positionBuilder=n,this._keyboardDispatcher=r,this._injector=a,this._ngZone=c,this._document=d,this._directionality=h,this._location=f,this._outsideClickDispatcher=O,this._animationsModuleType=u}create(e){const i=this._createHostElement(),s=this._createPaneElement(i),n=this._createPortalOutlet(s),r=new B(e);return r.direction=r.direction||this._directionality.value,new W(n,i,s,r,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,"NoopAnimations"===this._animationsModuleType)}position(){return this._positionBuilder}_createPaneElement(e){const i=this._document.createElement("div");return i.id="cdk-overlay-"+at++,i.classList.add("cdk-overlay-pane"),e.appendChild(i),i}_createHostElement(){const e=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(e),e}_createPortalOutlet(e){return this._appRef||(this._appRef=this._injector.get(l.ApplicationRef)),new x.DomPortalOutlet(e,this._componentFactoryResolver,this._appRef,this._injector,this._document)}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(V),l.\u0275\u0275inject(w),l.\u0275\u0275inject(l.ComponentFactoryResolver),l.\u0275\u0275inject(J),l.\u0275\u0275inject(H),l.\u0275\u0275inject(l.Injector),l.\u0275\u0275inject(l.NgZone),l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(k.Directionality),l.\u0275\u0275inject(m.Location),l.\u0275\u0275inject(N),l.\u0275\u0275inject(l.ANIMATION_MODULE_TYPE,8))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();const ht=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],Q=new l.InjectionToken("cdk-connected-overlay-scroll-strategy");let q=(()=>{class o{constructor(e){this.elementRef=e}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275directiveInject(l.ElementRef))},o.\u0275dir=l.\u0275\u0275defineDirective({type:o,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"],standalone:!0}),o})(),ct=(()=>{class o{get offsetX(){return this._offsetX}set offsetX(e){this._offsetX=e,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(e){this._offsetY=e,this._position&&this._updatePositionStrategy(this._position)}get hasBackdrop(){return this._hasBackdrop}set hasBackdrop(e){this._hasBackdrop=(0,_.coerceBooleanProperty)(e)}get lockPosition(){return this._lockPosition}set lockPosition(e){this._lockPosition=(0,_.coerceBooleanProperty)(e)}get flexibleDimensions(){return this._flexibleDimensions}set flexibleDimensions(e){this._flexibleDimensions=(0,_.coerceBooleanProperty)(e)}get growAfterOpen(){return this._growAfterOpen}set growAfterOpen(e){this._growAfterOpen=(0,_.coerceBooleanProperty)(e)}get push(){return this._push}set push(e){this._push=(0,_.coerceBooleanProperty)(e)}constructor(e,i,s,n,r){this._overlay=e,this._dir=r,this._hasBackdrop=!1,this._lockPosition=!1,this._growAfterOpen=!1,this._flexibleDimensions=!1,this._push=!1,this._backdropSubscription=p.Subscription.EMPTY,this._attachSubscription=p.Subscription.EMPTY,this._detachSubscription=p.Subscription.EMPTY,this._positionSubscription=p.Subscription.EMPTY,this.viewportMargin=0,this.open=!1,this.disableClose=!1,this.backdropClick=new l.EventEmitter,this.positionChange=new l.EventEmitter,this.attach=new l.EventEmitter,this.detach=new l.EventEmitter,this.overlayKeydown=new l.EventEmitter,this.overlayOutsideClick=new l.EventEmitter,this._templatePortal=new x.TemplatePortal(i,s),this._scrollStrategyFactory=n,this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef&&this._overlayRef.dispose()}ngOnChanges(e){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),e.origin&&this.open&&this._position.apply()),e.open&&(this.open?this._attachOverlay():this._detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=ht);const e=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=e.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=e.detachments().subscribe(()=>this.detach.emit()),e.keydownEvents().subscribe(i=>{this.overlayKeydown.next(i),i.keyCode===A.ESCAPE&&!this.disableClose&&!(0,A.hasModifierKey)(i)&&(i.preventDefault(),this._detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(i=>{this.overlayOutsideClick.next(i)})}_buildConfig(){const e=this._position=this.positionStrategy||this._createPositionStrategy(),i=new B({direction:this._dir,positionStrategy:e,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop});return(this.width||0===this.width)&&(i.width=this.width),(this.height||0===this.height)&&(i.height=this.height),(this.minWidth||0===this.minWidth)&&(i.minWidth=this.minWidth),(this.minHeight||0===this.minHeight)&&(i.minHeight=this.minHeight),this.backdropClass&&(i.backdropClass=this.backdropClass),this.panelClass&&(i.panelClass=this.panelClass),i}_updatePositionStrategy(e){const i=this.positions.map(s=>({originX:s.originX,originY:s.originY,overlayX:s.overlayX,overlayY:s.overlayY,offsetX:s.offsetX||this.offsetX,offsetY:s.offsetY||this.offsetY,panelClass:s.panelClass||void 0}));return e.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(i).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){const e=this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());return this._updatePositionStrategy(e),e}_getFlexibleConnectedPositionStrategyOrigin(){return this.origin instanceof q?this.origin.elementRef:this.origin}_attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(e=>{this.backdropClick.emit(e)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe((0,E.takeWhile)(()=>this.positionChange.observers.length>0)).subscribe(e=>{this.positionChange.emit(e),0===this.positionChange.observers.length&&this._positionSubscription.unsubscribe()}))}_detachOverlay(){this._overlayRef&&this._overlayRef.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe()}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275directiveInject(P),l.\u0275\u0275directiveInject(l.TemplateRef),l.\u0275\u0275directiveInject(l.ViewContainerRef),l.\u0275\u0275directiveInject(Q),l.\u0275\u0275directiveInject(k.Directionality,8))},o.\u0275dir=l.\u0275\u0275defineDirective({type:o,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:["cdkConnectedOverlayOrigin","origin"],positions:["cdkConnectedOverlayPositions","positions"],positionStrategy:["cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:["cdkConnectedOverlayOffsetX","offsetX"],offsetY:["cdkConnectedOverlayOffsetY","offsetY"],width:["cdkConnectedOverlayWidth","width"],height:["cdkConnectedOverlayHeight","height"],minWidth:["cdkConnectedOverlayMinWidth","minWidth"],minHeight:["cdkConnectedOverlayMinHeight","minHeight"],backdropClass:["cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:["cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:["cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:["cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:["cdkConnectedOverlayOpen","open"],disableClose:["cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:["cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:["cdkConnectedOverlayHasBackdrop","hasBackdrop"],lockPosition:["cdkConnectedOverlayLockPosition","lockPosition"],flexibleDimensions:["cdkConnectedOverlayFlexibleDimensions","flexibleDimensions"],growAfterOpen:["cdkConnectedOverlayGrowAfterOpen","growAfterOpen"],push:["cdkConnectedOverlayPush","push"]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],standalone:!0,features:[l.\u0275\u0275NgOnChangesFeature]}),o})();const _t={provide:Q,deps:[P],useFactory:function dt(o){return()=>o.scrollStrategies.reposition()}};let ft=(()=>{class o{}return o.\u0275fac=function(e){return new(e||o)},o.\u0275mod=l.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=l.\u0275\u0275defineInjector({providers:[P,_t],imports:[k.BidiModule,x.PortalModule,C.ScrollingModule,C.ScrollingModule]}),o})(),ut=(()=>{class o extends w{constructor(e,i){super(e,i)}ngOnDestroy(){super.ngOnDestroy(),this._fullScreenEventName&&this._fullScreenListener&&this._document.removeEventListener(this._fullScreenEventName,this._fullScreenListener)}_createContainer(){super._createContainer(),this._adjustParentForFullscreenChange(),this._addFullscreenChangeListener(()=>this._adjustParentForFullscreenChange())}_adjustParentForFullscreenChange(){this._containerElement&&(this.getFullscreenElement()||this._document.body).appendChild(this._containerElement)}_addFullscreenChangeListener(e){const i=this._getEventName();i&&(this._fullScreenListener&&this._document.removeEventListener(i,this._fullScreenListener),this._document.addEventListener(i,e),this._fullScreenListener=e)}_getEventName(){if(!this._fullScreenEventName){const e=this._document;e.fullscreenEnabled?this._fullScreenEventName="fullscreenchange":e.webkitFullscreenEnabled?this._fullScreenEventName="webkitfullscreenchange":e.mozFullScreenEnabled?this._fullScreenEventName="mozfullscreenchange":e.msFullscreenEnabled&&(this._fullScreenEventName="MSFullscreenChange")}return this._fullScreenEventName}getFullscreenElement(){const e=this._document;return e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement||null}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(m.DOCUMENT),l.\u0275\u0275inject(b.Platform))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()}}]);