(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4852,1281],{21281:(A,c,e)=>{e.r(c),e.d(c,{_isNumberValue:()=>o,coerceArray:()=>f,coerceBooleanProperty:()=>i,coerceCssPixelValue:()=>l,coerceElement:()=>p,coerceNumberProperty:()=>a,coerceStringArray:()=>y});var u=e(99877);function i(r){return null!=r&&"false"!=`${r}`}function a(r,n=0){return o(r)?Number(r):n}function o(r){return!isNaN(parseFloat(r))&&!isNaN(Number(r))}function f(r){return Array.isArray(r)?r:[r]}function l(r){return null==r?"":"string"==typeof r?r:`${r}px`}function p(r){return r instanceof u.ElementRef?r.nativeElement:r}function y(r,n=/\s+/){const t=[];if(null!=r){const m=Array.isArray(r)?r:`${r}`.split(n);for(const N of m){const s=`${N}`.trim();s&&t.push(s)}}return t}}}]);