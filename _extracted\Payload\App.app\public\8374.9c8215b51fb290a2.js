(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8374],{10954:(j,O,n)=>{n.d(O,{V:()=>_,Ws:()=>i,YH:()=>T,d6:()=>v,uJ:()=>l});var u=n(39904),c=n(87903),m=n(53113),f=n(66067);class i extends f.T2{constructor(a,o,p,E,P,A,M,d,U,L,h,S,D){super(a,o,p,E,P,M,d,U,L,S,D),this.colorValue=A,this.franchise=h,this.bank.isOccidente&&U&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,c.mm)(this,h),this.currenciesValue=h?.currencies||[u.y1],this.digitalValue="DIGITAL"===A}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class l{constructor(a,o,p){this.code=a,this.amount=o,this.amountCurrency=p||0}}class T{constructor(a,o,p,E,P){this.label=a,this.mode=o,this.copTotal=E?.value||0,this.usdTotal=P?.value||0,this.copUsdTotal=p?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new l("COP",this.copTotal,this.copTotal)}usdValue(){return new l("USD",this.copUsdTotal,this.usdTotal)}}class I{constructor(a,o,p){this.destination=a,this.source=o,this.currency=p}}class _{constructor(a,o,p,E,P,A){this.destination=a,this.source=o,this.isManual=p,this.trm=E,this.cop=P,this.usd=A,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new I(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new I(this.destination,this.source,this.usd):void 0}}class v extends m.LN{constructor(a,o,p){super(a,o),this.currency=p}}},96381:(j,O,n)=>{n.d(O,{T:()=>D,P:()=>k});var u=n(15861),c=n(77279),m=n(81536),f=n(87956),i=n(98699),l=n(10954),T=n(39904),I=n(29306),_=n(7464),v=n(87903),y=n(53113),a=n(1131);function E(e,N){return new l.V(e.destination,e.source,e.mode===a.o.MANUAL,N,e.cop,e.usd)}var A=n(71776),M=n(42168),d=n(99877);let U=(()=>{class e{constructor(t,r){this.http=t,r.subscribes(T.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,M.firstValueFrom)(this.http.get(T.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,M.map)(({content:t})=>t.map(r=>function p(e){return new l.Ws(e.id,e.acctType,e.acctTypeName,"DIGITAL"===e.color?T.CG:e.loanName,e.acctId,e.isOwner&&e.color||"NONE",(0,_.RO)(e.bankId,e.bankName),e.isAval,e.dynamo||!1,e.isOwner,e.creditCardType?function o(e){return new I.dD(e?.code,e?.description,0,0)}(e.creditCardType):void 0,e.isOwner?void 0:e.owner,e.isOwner?void 0:new y.dp((0,v.nX)(e.ownerIdType),e.ownerId))}(r))),(0,M.tap)(t=>{this.creditCards=t})))}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(A.HttpClient),d.\u0275\u0275inject(f.Yd))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),L=(()=>{class e{constructor(t){this.http=t}send(t){return(0,v.EC)([t.getPaymentCop(),t.getPaymentUsd()].filter(r=>!!r).map(r=>()=>this.sendCurrency(r)))}sendCurrency(t){return(0,M.firstValueFrom)(this.http.post(T.bV.PAYMENTS.CREDIT_CARD,function P(e){return{acctIdFrom:e.source.id,acctNickNameFrom:e.source.nickname,bankIdFrom:e.source.bank.id,acctIdTo:e.destination.id,acctNameTo:e.destination.nickname,bankIdTo:e.destination.bank.id,bankNameTo:e.destination.bank.name,amt:Math.ceil(e.currency.amount),curCode:e.currency.code,paymentDesc:""}}(t)).pipe((0,M.map)(r=>{const s=(0,v.l1)(r,"SUCCESS"),{type:g,message:b}=s;return new l.d6(g,b,t.currency)}),(0,M.catchError)(r=>{const{message:s}=(0,v.rU)(r);return(0,M.of)(new l.d6("ERROR",s,t.currency))})))}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(A.HttpClient))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var h=n(20691);let S=(()=>{class e extends h.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,mode:a.o.PAY_MIN}),this.eventBusService=t,this.eventBusService.subscribes(T.PU,()=>{this.reset()})}setDestination(t,r=!1){this.reduce(s=>({...s,destination:t,fromCustomer:r}))}getDestination(){return this.select(({destination:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(r=>({...r,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount(t){const{cop:r,mode:s,usd:g}=t;this.reduce(b=>({...b,cop:r,mode:s,usd:g,confirmation:!0}))}getAmount(){return this.select(({mode:t,cop:r,usd:s})=>({cop:r,mode:t,usd:s}))}setCurrencyCode(t){this.reduce(r=>({...r,currencyCode:t}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(f.Yd))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),D=(()=>{class e{constructor(t,r,s,g,b){this.financials=t,this.productService=r,this.repository=s,this.store=g,this.eventBusService=b}setDestination(t){var r=this;return(0,u.Z)(function*(){try{return t.isRequiredInformation&&(yield r.productService.requestInformation(t)),i.Either.success(r.store.setDestination(t))}catch{return i.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return i.Either.success(this.store.setSource(t))}catch({message:r}){return i.Either.failure({message:r})}}setAmount(t){try{return i.Either.success(this.store.setAmount(t))}catch({message:r}){return i.Either.failure({message:r})}}setCurrencyCode(t){try{return i.Either.success(this.store.setCurrencyCode(t))}catch({message:r}){return i.Either.failure({message:r})}}reset(){try{const t=this.store.itIsFromCustomer(),r=this.store.getDestination();return this.store.reset(),i.Either.success({fromCustomer:t,destination:r})}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,u.Z)(function*(){const r=E(t.store.currentState,yield t.requestTrmUsd()),s=yield t.execute(r),g=s.reduce((b,{isError:R})=>b&&!R,!0);return t.eventBusService.emit(g?c.q.TransactionSuccess:c.q.TransactionFailed),i.Either.success({creditCard:r,status:s})})()}requestTrmUsd(){return(0,i.catchPromise)(this.financials.request().then(([t])=>t))}execute(t){try{return this.repository.send(t)}catch({message:r}){return Promise.resolve([new l.d6("ERROR",r)])}}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(m.rm),d.\u0275\u0275inject(f.M5),d.\u0275\u0275inject(L),d.\u0275\u0275inject(S),d.\u0275\u0275inject(f.Yd))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var C=n(89148);const{MANUAL:F,PAY_ALTERNATIVE:V,PAY_MIN:B,PAY_TOTAL:W}=a.o,{CcaDatePayMinCop:z,CcaDatePayMinUsd:X,CcaPayAltMinCop:x,CcaPayAltMinUsd:G,CcaPayMinCop:Z,CcaPayMinUsd:K,CcaPayTotalCop:H,CcaPayTotalUsd:w}=C.Av;let k=(()=>{class e{constructor(t,r,s,g,b){this.products=t,this.productService=r,this.financials=s,this.repository=g,this.store=b}destination(){var t=this;return(0,u.Z)(function*(){try{return i.Either.success((yield t.repository.request()).reduce((r,s)=>{const{others:g,principals:b}=r;return(s.bank.isOccidente?b:g).push(s),r},{others:[],principals:[]}))}catch({message:r}){return i.Either.failure({message:r})}})()}source(t){var r=this;return(0,u.Z)(function*(){try{const s=yield r.products.requestAccountsForTransfer(),g=r.store.itIsConfirmation(),b=yield r.requestCreditCard(t);return i.Either.success({confirmation:g,destination:b,products:s})}catch({message:s}){return i.Either.failure({message:s})}})()}information(){var t=this;return(0,u.Z)(function*(){try{const r=t.store.getDestination(),s=yield t.productService.requestInformation(r),g=yield t.requestTrmUsd(),b=s?.getSection(z),R=s?.getSection(X);return i.Either.success({destination:r,min:new l.YH("VALOR M\xcdNIMO A PAGAR",B,g,s?.getSection(Z),s?.getSection(K)),alternative:new l.YH("VALOR M\xcdNIMO ALTERNO",V,g,s?.getSection(x),s?.getSection(G)),total:new l.YH("SALDO ACTUAL",W,g,s?.getSection(H),s?.getSection(w)),dateCop:b?.valueFormat,dateUsd:R?.valueFormat})}catch({message:r}){return i.Either.failure({message:r})}})()}selectAmount(){var t=this;return(0,u.Z)(function*(){try{const r=t.store.itIsConfirmation(),s=t.store.getAmount(),g=t.store.getSource(),b=t.store.getDestination(),R=yield t.productService.requestInformation(b),Y=yield t.requestTrmUsd();return i.Either.success({destination:b,amount:s,confirmation:r,trm:Y,source:g,min:new l.YH("Pago m\xednimo",B,Y,R?.getSection(Z),R?.getSection(K)),alternative:new l.YH("Pago m\xednimo alterno",V,Y,R?.getSection(x),R?.getSection(G)),total:new l.YH("Saldo actual",W,Y,R?.getSection(H),R?.getSection(w)),manual:new l.YH("Otro valor",F)})}catch({message:r}){return i.Either.failure({message:r})}})()}amount(){try{const t=this.store.itIsConfirmation(),r=this.store.getSource(),s=this.store.getDestination(),{cop:g}=this.store.getAmount();return i.Either.success({amount:g?.amount||0,confirmation:t,destination:s,source:r})}catch({message:t}){return i.Either.failure({message:t})}}confirmation(){var t=this;return(0,u.Z)(function*(){try{const r=E(t.store.currentState,yield t.requestTrmUsd());return i.Either.success({payment:r})}catch({message:r}){return i.Either.failure({message:r})}})()}requestCreditCard(t){var r=this;return(0,u.Z)(function*(){let s=r.store.getDestination();return!s&&t&&(s=(yield r.products.requestCreditCards()).find(({id:g})=>g===t),r.store.setDestination(s,!0)),s})()}requestTrmUsd(){return(0,i.catchPromise)(this.financials.request().then(([t])=>t))}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(f.hM),d.\u0275\u0275inject(f.M5),d.\u0275\u0275inject(m.rm),d.\u0275\u0275inject(U),d.\u0275\u0275inject(S))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},55351:(j,O,n)=>{n.d(O,{t:()=>T});var u=n(30263),c=n(39904),m=n(95437),f=n(96381),i=n(99877);let T=(()=>{class I{constructor(v,y,a){this.modalConfirmation=v,this.mboProvider=y,this.managerCreditCard=a}execute(v=!0){v?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:v,destination:y})=>{v?this.mboProvider.navigation.back(c.Z6.CUSTOMER.PRODUCTS.INFO,{productId:y.id}):this.mboProvider.navigation.back(c.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(c.Z6.PAYMENTS.HOME)}})}}return I.\u0275fac=function(v){return new(v||I)(i.\u0275\u0275inject(u.$e),i.\u0275\u0275inject(m.ZL),i.\u0275\u0275inject(f.T))},I.\u0275prov=i.\u0275\u0275defineInjectable({token:I,factory:I.\u0275fac,providedIn:"root"}),I})()},1131:(j,O,n)=>{n.d(O,{o:()=>u});var u=(()=>{return(c=u||(u={}))[c.PAY_MIN=0]="PAY_MIN",c[c.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",c[c.PAY_TOTAL=2]="PAY_TOTAL",c[c.MANUAL=3]="MANUAL",u;var c})()},38374:(j,O,n)=>{n.r(O),n.d(O,{MboPaymentCreditCardAmountPageModule:()=>L});var u=n(17007),c=n(78007),m=n(30263),f=n(24495),i=n(39904),l=n(87903),T=n(95437),I=n(57544),_=n(10954),v=n(96381),y=n(55351),a=n(1131),o=n(99877),p=n(35641),E=n(48774),P=n(83413),A=n(45542);const{SOURCE:M,CONFIRMATION:d}=i.Z6.PAYMENTS.CREDIT_CARD;let U=(()=>{class h{constructor(D,C,F,V){this.mboProvider=D,this.requestConfiguration=C,this.managerPayment=F,this.cancelProvider=V,this.confirmation=!1,this.requesting=!0,this.backAction={id:"btn_payment-creditcard-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(M)}},this.cancelAction={id:"btn_payment-creditcard-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new I.FormControl}ngOnInit(){this.initializatedConfiguration()}get disabled(){return this.amountControl.invalid||this.requesting}onSubmit(){this.managerPayment.setAmount({mode:a.o.MANUAL,cop:new _.uJ("COP",this.amountControl.value)}).when({success:()=>{this.mboProvider.navigation.next(d)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({amount:D,confirmation:C,destination:F,source:V})=>{this.destination=F,this.source=V,this.confirmation=C,D&&this.amountControl.setValue(D);const B=[f.C1,f.LU];(0,l.VN)(V)&&B.push((0,f.vB)(V.amount)),this.amountControl.setValidators(B)}},()=>{this.requesting=!1})}}return h.\u0275fac=function(D){return new(D||h)(o.\u0275\u0275directiveInject(T.ZL),o.\u0275\u0275directiveInject(v.P),o.\u0275\u0275directiveInject(v.T),o.\u0275\u0275directiveInject(y.t))},h.\u0275cmp=o.\u0275\u0275defineComponent({type:h,selectors:[["mbo-payment-creditcard-amount-page"]],decls:13,vars:12,consts:[[1,"mbo-payment-creditcard-amount-page__content"],[1,"mbo-payment-creditcard-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-payment-creditcard-amount-page__body"],[1,"mbo-payment-creditcard-amount-page__message","subtitle2-medium"],["elementId","txt_transfer-generic-amount_value","label","Valor a pagar","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount","hidden"],[3,"skeleton","hidden"],[1,"mbo-payment-creditcard-amount-page__footer"],["id","btn_payment-creditcard-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(D,C){1&D&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"div",3)(4,"p",4),o.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas pagar? "),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6)(8,"bocc-card-product-summary",7),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(9,"div",8)(10,"button",9),o.\u0275\u0275listener("click",function(){return C.onSubmit()}),o.\u0275\u0275elementStart(11,"span"),o.\u0275\u0275text(12,"Continuar"),o.\u0275\u0275elementEnd()()()),2&D&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("leftAction",C.backAction)("rightAction",C.cancelAction),o.\u0275\u0275advance(4),o.\u0275\u0275property("formControl",C.amountControl),o.\u0275\u0275advance(1),o.\u0275\u0275property("color",null==C.source?null:C.source.color)("icon",null==C.source?null:C.source.logo)("title",null==C.source?null:C.source.nickname)("number",null==C.source?null:C.source.shortNumber)("amount",null==C.source?null:C.source.amount)("hidden",C.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("skeleton",!0)("hidden",!C.requesting),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",C.disabled))},dependencies:[p.d,E.J,P.D,A.P],styles:["/*!\n * MBO PaymentCreditCardAmount Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 25/Jul/2022\n * Updated: 02/Feb/2024\n*/mbo-payment-creditcard-amount-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__content{position:relative;width:100%}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__body{position:relative;float:left;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__body bocc-card-product-summary{margin-top:var(--sizing-x28)}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__message{color:var(--color-carbon-darker-1000);margin-bottom:var(--sizing-x28)}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-creditcard-amount-page .mbo-payment-creditcard-amount-page__footer button{width:100%}\n"],encapsulation:2}),h})(),L=(()=>{class h{}return h.\u0275fac=function(D){return new(D||h)},h.\u0275mod=o.\u0275\u0275defineNgModule({type:h}),h.\u0275inj=o.\u0275\u0275defineInjector({imports:[u.CommonModule,c.RouterModule.forChild([{path:"",component:U}]),m.dH,m.Jx,m.D1,m.P8]}),h})()},63674:(j,O,n)=>{n.d(O,{Eg:()=>I,Lo:()=>f,Wl:()=>i,ZC:()=>l,_f:()=>c,br:()=>T,tl:()=>m});var u=n(29306);const c={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},m=new u.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),f={color:"success",key:"paid",label:"Pagada"},i={color:"alert",key:"pending",label:"Por pagar"},l={color:"danger",key:"expired",label:"Vencida"},T={color:"info",key:"recurring",label:"Pago recurrente"},I={color:"info",key:"programmed",label:"Programado"}},66067:(j,O,n)=>{n.d(O,{S6:()=>_,T2:()=>T,UQ:()=>v,mZ:()=>I});var u=n(39904),c=n(6472),f=n(63674),i=n(31707);class T{constructor(a,o,p,E,P,A,M,d,U,L,h){this.id=a,this.type=o,this.name=p,this.nickname=E,this.number=P,this.bank=A,this.isAval=M,this.isProtected=d,this.isOwner=U,this.ownerName=L,this.ownerDocument=h,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[u.y1],this.initialsName=(0,c.initials)(E),this.shortNumber=P.substring(P.length-4),this.descriptionNumber=`${p} ${P}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:A.logo,light:A.logo,standard:A.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(a){this.informationValue||(this.informationValue=a)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(a){return this.currenciesValue.includes(a)}}class I{constructor(a,o){this.id=a,this.type=o}}class _{constructor(a,o,p,E,P,A,M,d,U,L,h,S){this.uuid=a,this.number=o,this.nie=p,this.nickname=E,this.companyId=P,this.companyName=A,this.amount=M,this.registerDate=d,this.expirationDate=U,this.paid=L,this.statusCode=h,this.references=S,this.recurring=S.length>0,this.status=function l(y){switch(y){case i.U.EXPIRED:return f.ZC;case i.U.PENDING:return f.Wl;case i.U.PROGRAMMED:return f.Eg;case i.U.RECURRING:return f.br;default:return f.Lo}}(h)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class v{constructor(a,o,p,E,P,A,M,d){this.uuid=a,this.number=o,this.nickname=p,this.companyId=E,this.companyName=P,this.city=A,this.amount=M,this.isBiller=d}}},31707:(j,O,n)=>{n.d(O,{U:()=>u,f:()=>c});var u=(()=>{return(m=u||(u={})).RECURRING="1",m.EXPIRED="2",m.PENDING="3",m.PROGRAMMED="4",u;var m})(),c=(()=>{return(m=c||(c={})).BILLER="Servicio",m.NON_BILLER="Servicio",m.PSE="Servicio",m.TAX="Impuesto",m.LOAN="Obligaci\xf3n financiera",m.CREDIT_CARD="Obligaci\xf3n financiera",c;var m})()}}]);