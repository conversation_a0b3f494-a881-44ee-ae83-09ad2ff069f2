(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4572,539],{30539:(x,b,o)=>{o.r(b),o.d(b,{FormArray:()=>y,FormArrayControl:()=>A,FormArrayGroup:()=>m,FormControl:()=>d,FormGroup:()=>p,formArray:()=>w,formArrayControl:()=>T,formArrayGroup:()=>k,formControl:()=>S,formGroup:()=>I});var u=o(98699);function a(...e){const[r,t]=e;return r&&!t&&function E(e){return"object"==typeof e&&("value"in e||"validators"in e)}(r)?r:{value:r,validators:t}}function c(...e){const[r,t]=e;return!t&&function F(e){return"object"==typeof e&&"controls"in e}(r)?r:{controls:r,validators:t}}function f(...e){const[r,t]=e;return r&&!t&&function C(e){return"object"==typeof e&&("groups"in e||"validators"in e)}(r)?r:{groups:r,validators:t}}const l=(e,r)=>Object.values(e).reduce((t,s)=>s.disabled?t:t&&(0,u.parseBoolean)(s[r]),!0),g=(e,r)=>Object.values(e).reduce((t,s)=>s.disabled?t:t||(0,u.parseBoolean)(s[r]),!1);function h(e,r){return e.reduce((t,s)=>t&&(0,u.parseBoolean)(s[r]),!0)}function v(e,r){return e.reduce((t,s)=>t||(0,u.parseBoolean)(s[r]),!1)}class y{constructor(r,t){this.currentGroups=[],this.currentValid=!0,this.currentDisabled=!1,this.currentErrors=[];const{groups:s,validators:i}=f(r,t);this.unsusbcriptions=new Map,this.initialState=s,this.validators=i,this.refresh(this.initialState),this.observable=(0,u.observable)(this.value),s?.forEach(n=>{this.subscription(n)})}get groups(){return this.currentGroups}get controls(){return this.groups.map(({controls:r})=>r)}get touched(){return v(this.groups,"touched")}get touchedAll(){return h(this.groups,"touchedAll")}get untouched(){return!this.touched}get untouchedAll(){return!this.touchedAll}get dirty(){return v(this.groups,"dirty")}get dirtyAll(){return h(this.groups,"dirtyAll")}get pristine(){return!this.dirty}get pristineAll(){return!this.dirtyAll}get disabled(){return this.currentDisabled}get enabled(){return!this.currentDisabled}get valid(){return this.currentValid&&h(this.groups,"valid")}get invalid(){return!this.currentValid}get value(){return this.groups.map(({value:r})=>r)}get error(){return this.currentError}get errors(){return this.currentErrors}get wrong(){return this.touched&&this.invalid}reset(){this.refresh(this.initialState)}disable(){this.currentDisabled=!0}enable(){this.currentDisabled=!1}push(r){this.subscription(r),this.refresh([...this.groups,r])}merge(r){r.forEach(t=>{this.subscription(t)}),this.refresh([...this.groups,...r])}set(r){this.currentGroups.forEach(({uuid:t})=>{this.unsusbcriptions.delete(t)}),r.forEach(t=>{this.subscription(t)}),this.refresh(r)}remove({uuid:r}){this.refresh(this.groups.filter(t=>t.uuid!==r))}setValidators(r){this.validators=r,this.updateValidityStatus(this.groups,r)}subscribe(r){return this.observable.subscribe(r)}subscription(r){const t=r.subscribe(()=>{this.updateValidityStatus(this.groups,this.validators)});this.unsusbcriptions.set(r.uuid,t)}updateValidityStatus(r,t){if(t){const s=(({groups:e,validators:r})=>r.reduce((t,s)=>{const i=s(e);return i&&t.push(i),t},[]))({groups:r,validators:t});this.currentErrors=s,this.currentError=s[0],this.currentValid=0===s.length}else this.currentValid=!0,this.currentErrors=[],this.currentError=void 0}refresh(r){const t=r||[];this.currentGroups=t,this.updateValidityStatus(t,this.validators)}}function w(e,r){return new y(f(e,r))}var V=o(33326);class d{constructor(r,t){this.currentFocused=!1,this.currentTouched=!1,this.currentDirty=!1,this.currentDisabled=!1,this.currentValid=!0,this.currentErrors=[];const{value:s,validators:i}=a(r,t);this.observable=(0,u.observable)(s),this.initialValue=s,this.validators=i,this.currentValue=s,this.updateValueAndValidity(s,i)}get focused(){return this.currentFocused}get unfocused(){return!this.currentFocused}get touched(){return this.currentTouched}get untouched(){return!this.currentTouched}get dirty(){return this.currentDirty}get pristine(){return!this.currentDirty}get disabled(){return this.currentDisabled}get enabled(){return!this.currentDisabled}get valid(){return this.currentValid}get invalid(){return!this.currentValid}get value(){return this.currentValue}get errors(){return this.currentErrors}get error(){return this.currentError}get wrong(){return this.touched&&this.invalid}reset(){this.setValue(this.initialValue),this.currentDirty=!1,this.currentTouched=!1}focus(){this.currentFocused=!0}blur(){this.currentFocused=!1,this.currentTouched=!0}disable(){this.currentDisabled=!0}enable(){this.currentDisabled=!1}touch(){this.currentTouched=!0}setValue(r){this.enabled&&(this.currentValue=r,this.currentDirty=!0,this.updateValueAndValidity(r,this.validators),this.observable.next(r))}setValidators(r=[]){this.validators=r,this.updateValueAndValidity(this.value,r)}subscribe(r){return this.observable.subscribe(r)}updateValueAndValidity(r,t){if(t){const s=(({value:e,validators:r})=>r.reduce((t,s)=>{const i=s(e);return i&&t.push(i),t},[]))({value:r,validators:t});this.currentError=s[0],this.currentErrors=s,this.currentValid=0===s.length}else this.currentValid=!0,this.currentError=void 0,this.currentErrors=[]}}function S(e,r){return new d(a(e,r))}class A extends d{constructor(r,t){const{value:s,validators:i}=a(r,t);super(s,i),this.uuid=(0,V.v4)()}}function T(e,r){return new A(a(e,r))}class p{constructor(r,t){this.currentErrors=[],this.currentValid=!0;const{controls:s,validators:i}=c(r,t);this.currentControls=s,this.validators=i,this.updateValueAndValidity(s,i),this.observable=(0,u.observable)(this.value),Object.values(s).forEach(n=>{n.subscribe(()=>{this.updateValueAndValidity(this.controls,this.validators),this.observable.next(this.value)})})}get controls(){return this.currentControls}get touched(){return g(this.controls,"touched")}get touchedAll(){return l(this.controls,"touched")}get untouched(){return!this.touched}get untouchedAll(){return!this.touchedAll}get dirty(){return g(this.controls,"dirty")}get dirtyAll(){return l(this.controls,"dirty")}get pristine(){return!this.dirty}get pristineAll(){return this.dirtyAll}get valid(){return this.currentValid&&l(this.controls,"valid")}get invalid(){return!this.valid}get value(){return Object.entries(this.controls).reduce((r,[t,{value:s}])=>(r[t]=s,r),{})}get errors(){return this.currentErrors}get error(){return this.currentError}get wrong(){return this.touched&&this.invalid}reset(){Object.values(this.controls).forEach(r=>{r.reset()})}setValidators(r){this.validators=r,this.updateValueAndValidity(this.controls,r)}subscribe(r){return this.observable.subscribe(r)}updateValueAndValidity(r,t){if(t){const s=(({controls:e,validators:r})=>r.reduce((t,s)=>{const i=s(e);return i&&t.push(i),t},[]))({controls:r,validators:t});this.currentErrors=s,this.currentError=s[0],this.currentValid=0===s.length}else this.currentErrors=[],this.currentError=void 0,this.currentValid=!0}}function I(e,r){return new p(c(e,r))}class m extends p{constructor(r,t){const{controls:s,resource:i,validators:n}=c(r,t);super(s,n),this.uuid=(0,V.v4)(),this.resource=i}}function k(e,r){return new m(c(e,r))}}}]);