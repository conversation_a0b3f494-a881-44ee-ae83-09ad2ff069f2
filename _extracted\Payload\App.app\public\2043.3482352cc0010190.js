(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2043],{93252:(E,p,n)=>{n.d(p,{$x:()=>h,KF:()=>u,Xh:()=>a,pb:()=>d});var v=n(53113);class h{constructor(l,s,f){this.documentCustomer=l,this.channel=s,this.owner=f}}class a{constructor(l,s,f,r){this.source=l,this.amount=s,this.currencyCode=f,this.beneficiary=r}}class u{constructor(l){this.value=l}}class d extends v.LN{constructor(l,s,f){super(l,s),this.otp=f}}},99224:(E,p,n)=>{n.d(p,{M:()=>w,i:()=>I});var v=n(15861),h=n(87956),a=n(98699),u=n(93252);function d(i){return new u.Xh(i.source,i.amount,i.currencyCode,i.beneficiary)}var b=n(71776),l=n(39904),s=n(87903),f=n(42168),r=n(84757),c=n(99877);let A=(()=>{class i{constructor(t){this.http=t}send(t){const e=function y(i){return{accId:i.source.id,amt:i.amount,curCode:i.currencyCode,channel:i.beneficiary.channel.type,documentNumber:i.beneficiary.documentCustomer.number,documentType:i.beneficiary.documentCustomer.type.code}}(t);return(0,f.firstValueFrom)(this.http.post(l.bV.TRANSACTIONS.QUICK_WITHDRAWAL,e).pipe((0,r.map)(o=>{const{type:g}=(0,s.l1)(o,"SUCCESS");return new u.pb(g,o.otpInfo?.validity.validityPeriodInfo.desc||"",new u.KF(o.otpInfo?.otpValue))}))).catch(o=>{const{message:g,type:M}=(0,s.rU)(o);return new u.pb(M,g)})}}return i.\u0275fac=function(t){return new(t||i)(c.\u0275\u0275inject(b.HttpClient))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})();var k=n(20691);let C=(()=>{class i extends k.Store{constructor(t){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=t,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(t,e=!1){this.reduce(o=>({...o,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setBeneficiary(t){this.reduce(e=>({...e,beneficiary:t}))}selectForBeneficiary(){return this.select(({beneficiary:t,confirmation:e})=>({beneficiary:t,confirmation:e}))}setAmount(t){this.reduce(e=>({...e,amount:t,confirmation:!0,currencyCode:"COP"}))}selectForAmount(){return this.select(({amount:t,confirmation:e,source:o})=>({amount:t,confirmation:e,source:o}))}getCurrencyCode(){return this.select(({currencyCode:t})=>t)}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return i.\u0275fac=function(t){return new(t||i)(c.\u0275\u0275inject(h.Yd))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),w=(()=>{class i{constructor(t,e,o){this.eventBusService=t,this.repository=e,this.store=o}setSource(t){try{return a.Either.success(this.store.setSource(t))}catch({message:e}){return a.Either.failure({message:e})}}setBeneficiary(t){try{return a.Either.success(this.store.setBeneficiary(t))}catch({message:e}){return a.Either.failure({message:e})}}setAmount(t){try{return a.Either.success(this.store.setAmount(t))}catch({message:e}){return a.Either.failure({message:e})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getSource();return this.store.reset(),a.Either.success({fromCustomer:t,source:e})}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,v.Z)(function*(){const e=d(t.store.currentState),o=yield t.execute(e);return t.eventBusService.emit(o.channel),a.Either.success({withdrawal:e,status:o})})()}execute(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(u.pb.error(e))}}}return i.\u0275fac=function(t){return new(t||i)(c.\u0275\u0275inject(h.Yd),c.\u0275\u0275inject(A),c.\u0275\u0275inject(C))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})();var P=n(78506);let I=(()=>{class i{constructor(t,e,o){this.products=t,this.store=e,this.session=o}source(){var t=this;return(0,v.Z)(function*(){try{const e=t.store.itIsConfirmation(),o=yield t.requestAccounts();return a.Either.success({confirmation:e,products:o})}catch({message:e}){return a.Either.failure({message:e})}})()}beneficiary(t){var e=this;return(0,v.Z)(function*(){try{const o=yield e.session.customer(),g=yield e.requestAccounts(),M=e.requestAccount(g,t),S=e.store.selectForBeneficiary(),W=e.store.itIsConfirmation();return a.Either.success({...S,confirmation:W,customer:o,products:g,source:M})}catch({message:o}){return a.Either.failure({message:o})}})()}amount(){try{return a.Either.success(this.store.selectForAmount())}catch({message:t}){return a.Either.failure({message:t})}}confirmation(){try{const t=d(this.store.currentState);return a.Either.success({withdrawal:t})}catch({message:t}){return a.Either.failure({message:t})}}requestAccounts(){return this.products.requestAccountsForTransfer()}requestAccount(t,e){let o=this.store.getSource();return!o&&e&&(o=t.find(({id:g})=>e===g),this.store.setSource(o,!0)),o}}return i.\u0275fac=function(t){return new(t||i)(c.\u0275\u0275inject(h.hM),c.\u0275\u0275inject(C),c.\u0275\u0275inject(P._I))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},20534:(E,p,n)=>{n.d(p,{v:()=>l});var v=n(30263),h=n(39904),a=n(95437),u=n(99224),d=n(99877);let l=(()=>{class s{constructor(r,c,y){this.modalConfirmation=r,this.mboProvider=c,this.managerWithdrawal=y}execute(r=!0){r?this.modalConfirmation.execute({title:"Cancelar retiro",message:"\xbfEstas seguro que deseas cancelar el retiro actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerWithdrawal.reset().when({success:({fromCustomer:r,source:c})=>{r?this.mboProvider.navigation.back(h.Z6.CUSTOMER.PRODUCTS.INFO,{productId:c.id}):this.mboProvider.navigation.back(h.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(h.Z6.TRANSFERS.HOME)}})}}return s.\u0275fac=function(r){return new(r||s)(d.\u0275\u0275inject(v.$e),d.\u0275\u0275inject(a.ZL),d.\u0275\u0275inject(u.M))},s.\u0275prov=d.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},12043:(E,p,n)=>{n.r(p),n.d(p,{MboQuickWithdrawalConfirmationPageModule:()=>I});var v=n(17007),h=n(78007),a=n(79798),u=n(30263),d=n(83651),b=n(39904),l=n(95437),s=n(99224),f=n(20534),r=n(99877),c=n(10464),y=n(48774),A=n(17941),k=n(66613),C=n(45542);const w=b.Z6.TRANSACTIONS.QUICK_WITHDRAWAL;let P=(()=>{class i{constructor(t,e,o){this.mboProvider=t,this.requestConfiguration=e,this.cancelProvider=o,this.backAction={id:"btn_quick-withdrawal-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(w.AMOUNT)}},this.cancelAction={id:"btn_quick-withdrawal-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.sourceActions=[{id:"btn_quick-withdrawal-confirmation_source",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(w.SOURCE)}}],this.amountActions=[{id:"btn_quick-withdrawal-confirmation_amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(w.AMOUNT)}}],this.beneficiaryActions=[{id:"btn_quick-withdrawal-confirmation_beneficiary",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(w.BENEFICIARY)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(w.RESULT)}initializatedConfiguration(){this.requestConfiguration.confirmation().when({success:({withdrawal:t})=>{this.withdrawal=t}})}}return i.\u0275fac=function(t){return new(t||i)(r.\u0275\u0275directiveInject(l.ZL),r.\u0275\u0275directiveInject(s.i),r.\u0275\u0275directiveInject(f.v))},i.\u0275cmp=r.\u0275\u0275defineComponent({type:i,selectors:[["mbo-quick-withdrawal-confirmation-page"]],decls:18,vars:10,consts:[[1,"mbo-quick-withdrawal-confirmation-page__content","mbo-page__scroller"],[1,"mbo-quick-withdrawal-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-quick-withdrawal-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],["header","DESDE",3,"title","subtitle","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","LUGAR",3,"title","actions"],["icon","bell",3,"visible"],[1,"mbo-quick-withdrawal-confirmation-page__footer"],["id","btn_quick-withdrawal-confirmation_submit","bocc-button","raised","prefixIcon","atm-withdraw",3,"click"]],template:function(t,e){1&t&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),r.\u0275\u0275element(3,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),r.\u0275\u0275text(7," \xbfQuieres realizar un retiro f\xe1cil? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"div",6),r.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),r.\u0275\u0275elementStart(12,"bocc-alert",10),r.\u0275\u0275text(13," Costo de la transacci\xf3n $ 0 pesos. "),r.\u0275\u0275elementEnd()()()()(),r.\u0275\u0275elementStart(14,"div",11)(15,"button",12),r.\u0275\u0275listener("click",function(){return e.onSubmit()}),r.\u0275\u0275elementStart(16,"span"),r.\u0275\u0275text(17,"Retirar"),r.\u0275\u0275elementEnd()()()()),2&t&&(r.\u0275\u0275advance(3),r.\u0275\u0275property("leftAction",e.backAction)("rightAction",e.cancelAction),r.\u0275\u0275advance(6),r.\u0275\u0275property("title",null==e.withdrawal||null==e.withdrawal.source?null:e.withdrawal.source.nickname)("subtitle",null==e.withdrawal||null==e.withdrawal.source?null:e.withdrawal.source.number)("actions",e.sourceActions),r.\u0275\u0275advance(1),r.\u0275\u0275property("amount",null==e.withdrawal?null:e.withdrawal.amount)("actions",e.amountActions),r.\u0275\u0275advance(1),r.\u0275\u0275property("title",null==e.withdrawal||null==e.withdrawal.beneficiary?null:e.withdrawal.beneficiary.channel.label)("actions",e.beneficiaryActions),r.\u0275\u0275advance(1),r.\u0275\u0275property("visible",!0))},dependencies:[c.K,y.J,A.D,k.B,C.P],styles:["/*!\n * MBO QuickWithdrawalConfirmation Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 02/Ene/2023\n * Updated: 16/Jun/2024\n*/mbo-quick-withdrawal-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-quick-withdrawal-confirmation-page .mbo-page__content{row-gap:var(--sizing-x4)}mbo-quick-withdrawal-confirmation-page .mbo-quick-withdrawal-confirmation-page__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-quick-withdrawal-confirmation-page .mbo-quick-withdrawal-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-quick-withdrawal-confirmation-page .mbo-quick-withdrawal-confirmation-page__body bocc-alert{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-quick-withdrawal-confirmation-page .mbo-quick-withdrawal-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-quick-withdrawal-confirmation-page .mbo-quick-withdrawal-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),i})(),I=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=r.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=r.\u0275\u0275defineInjector({imports:[v.CommonModule,h.RouterModule.forChild([{path:"",component:P}]),a.KI,u.Jx,u.DM,u.B4,u.Dj,d.P6,u.P8]}),i})()}}]);