(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8976],{68976:(u,t,o)=>{o.r(t),o.d(t,{LocalNotifications:()=>i,Weekday:()=>s});var a=o(17737),s=(()=>{return(n=s||(s={}))[n.Sunday=1]="Sunday",n[n.Monday=2]="Monday",n[n.Tuesday=3]="Tuesday",n[n.Wednesday=4]="Wednesday",n[n.Thursday=5]="Thursday",n[n.Friday=6]="Friday",n[n.Saturday=7]="Saturday",s;var n})();const i=(0,a.registerPlugin)("LocalNotifications",{web:()=>o.e(7434).then(o.bind(o,67434)).then(n=>new n.LocalNotificationsWeb)})}}]);