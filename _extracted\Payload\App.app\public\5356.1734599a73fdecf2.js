(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5356],{15356:(y,f,l)=>{l.r(f),l.d(f,{DeviceWeb:()=>d});var s=l(15861),g=l(17737);class d extends g.WebPlugin{getId(){var e=this;return(0,s.Z)(function*(){return{identifier:e.getUid()}})()}getInfo(){var e=this;return(0,s.Z)(function*(){if(typeof navigator>"u"||!navigator.userAgent)throw e.unavailable("Device API not available in this browser");const t=navigator.userAgent,n=e.parseUa(t);return{model:n.model,platform:"web",operatingSystem:n.operatingSystem,osVersion:n.osVersion,manufacturer:navigator.vendor,isVirtual:!1,webViewVersion:n.browserVersion}})()}getBatteryInfo(){var e=this;return(0,s.Z)(function*(){if(typeof navigator>"u"||!navigator.getBattery)throw e.unavailable("Device API not available in this browser");let t={};try{t=yield navigator.getBattery()}catch{}return{batteryLevel:t.level,isCharging:t.charging}})()}getLanguageCode(){return(0,s.Z)(function*(){return{value:navigator.language.split("-")[0].toLowerCase()}})()}getLanguageTag(){return(0,s.Z)(function*(){return{value:navigator.language}})()}parseUa(e){const t={},n=e.indexOf("(")+1;let a=e.indexOf(") AppleWebKit");-1!==e.indexOf(") Gecko")&&(a=e.indexOf(") Gecko"));const r=e.substring(n,a);if(-1!==e.indexOf("Android")){const i=r.replace("; wv","").split("; ").pop();i&&(t.model=i.split(" Build")[0]),t.osVersion=r.split("; ")[1]}else if(t.model=r.split("; ")[0],typeof navigator<"u"&&navigator.oscpu)t.osVersion=navigator.oscpu;else if(-1!==e.indexOf("Windows"))t.osVersion=r;else{const i=r.split("; ").pop();if(i){const c=i.replace(" like Mac OS X","").split(" ");t.osVersion=c[c.length-1].replace(/_/g,".")}}t.operatingSystem=/android/i.test(e)?"android":/iPad|iPhone|iPod/.test(e)&&!window.MSStream?"ios":/Win/.test(e)?"windows":/Mac/i.test(e)?"mac":"unknown";const o=!!window.ApplePaySession,v=!!window.chrome,S=/Firefox/.test(e),_=/Edg/.test(e),w=/FxiOS/.test(e),m=/CriOS/.test(e),h=/EdgiOS/.test(e);if(o||v&&!_||w||m||h){let i;i=w?"FxiOS":m?"CriOS":h?"EdgiOS":o?"Version":"Chrome";const c=e.split(" ");for(const x of c)if(x.includes(i)){const b=x.split("/")[1];t.browserVersion=b}}else if(S||_){const x=e.split("").reverse().join("").split("/")[0].split("").reverse().join("");t.browserVersion=x}return t}getUid(){if(typeof window<"u"&&window.localStorage){let e=window.localStorage.getItem("_capuid");return e||(e=this.uuid4(),window.localStorage.setItem("_capuid",e),e)}return this.uuid4()}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}}},15861:(y,f,l)=>{function s(u,d,p,e,t,n,a){try{var r=u[n](a),o=r.value}catch(v){return void p(v)}r.done?d(o):Promise.resolve(o).then(e,t)}function g(u){return function(){var d=this,p=arguments;return new Promise(function(e,t){var n=u.apply(d,p);function a(o){s(n,e,t,a,r,"next",o)}function r(o){s(n,e,t,a,r,"throw",o)}a(void 0)})}}l.d(f,{Z:()=>g})}}]);