(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9026],{34528:(n,D,E)=>{E.d(D,{G:()=>C,w:()=>_});const _={UPC_A:"UPC_A",UPC_E:"UPC_E",UPC_EAN_EXTENSION:"UPC_EAN_EXTENSION",EAN_8:"EAN_8",EAN_13:"EAN_13",CODE_39:"CODE_39",CODE_39_MOD_43:"CODE_39_MOD_43",CODE_93:"CODE_93",CODE_128:"CODE_128",CODABAR:"CODABAR",ITF:"ITF",ITF_14:"ITF_14",AZTEC:"AZTEC",DATA_MATRIX:"DATA_MATRIX",MAXICODE:"MAXICODE",PDF_417:"PDF_417",QR_CODE:"QR_CODE",RSS_14:"RSS_14",RSS_EXPANDED:"RSS_EXPANDED"},C={FRONT:"front",BACK:"back"}},9026:(n,D,E)=>{E.r(D),E.d(D,{BarcodeFormat:()=>A.w,BarcodeScanner:()=>O,CameraDirection:()=>A.G});var _=E(17737),A=E(34528);const O=(0,_.registerPlugin)("BarcodeScanner",{web:()=>E.e(8054).then(E.bind(E,98054)).then(a=>new a.BarcodeScannerWeb)})}}]);