(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4210],{34210:(s,d,o)=>{o.r(d),o.d(d,{MboCreditUseQuotaModule:()=>u});var l=o(17007),a=o(78007),n=o(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>o.e(3580).then(o.bind(o,73580)).then(t=>t.MboCreditUseQuotaSourcePageModule)},{path:"destination",loadChildren:()=>o.e(1455).then(o.bind(o,31455)).then(t=>t.MboCreditUseQuotaDestinationPageModule)},{path:"amount",loadChildren:()=>o.e(2236).then(o.bind(o,2236)).then(t=>t.MboCreditUseQuotaAmountPageModule)},{path:"confirmation",loadChildren:()=>o.e(5138).then(o.bind(o,85138)).then(t=>t.MboCreditUseQuotaConfirmationPageModule)},{path:"result",loadChildren:()=>o.e(3284).then(o.bind(o,73284)).then(t=>t.MboCreditUseQuotaResultPageModule)}];let u=(()=>{class t{}return t.\u0275fac=function(C){return new(C||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,a.RouterModule.forChild(M)]}),t})()}}]);