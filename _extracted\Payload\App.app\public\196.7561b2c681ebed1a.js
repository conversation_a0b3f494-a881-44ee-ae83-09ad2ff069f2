(self.webpackChunkapp=self.webpackChunkapp||[]).push([[196,8190],{23192:(b,A,i)=>{i.d(A,{SQ:()=>r,Xb:()=>n});var o=i(55502),l=i(92261),d=i(91935);const r=typeof Symbol<"u"?Symbol("amplify_default"):"@@amplify_default",g=new o.k("Hub");class p{constructor(S){this.listeners=new Map,this.protectedChannels=["core","auth","api","analytics","interactions","pubsub","storage","ui","xr"],this.name=S}_remove(S,u){const I=this.listeners.get(S);I?this.listeners.set(S,[...I.filter(({callback:_})=>_!==u)]):g.warn(`No listeners for ${S}`)}dispatch(S,u,I,_){"string"==typeof S&&this.protectedChannels.indexOf(S)>-1&&(_===r||g.warn(`WARNING: ${S} is protected and dispatching on it can have unintended consequences`));const v={channel:S,payload:{...u},source:I,patternInfo:[]};try{this._toListeners(v)}catch(c){g.error(c)}}listen(S,u,I="noname"){let _;if("function"!=typeof u)throw new d._({name:l.z2,message:"No callback supplied to Hub"});_=u;let v=this.listeners.get(S);return v||(v=[],this.listeners.set(S,v)),v.push({name:I,callback:_}),()=>{this._remove(S,_)}}_toListeners(S){const{channel:u,payload:I}=S,_=this.listeners.get(u);_&&_.forEach(v=>{g.debug(`Dispatching to ${u} with `,I);try{v.callback(S)}catch(c){g.error(c)}})}}const n=new p("__default__");new p("internal-hub")},55502:(b,A,i)=>{i.d(A,{k:()=>r});var o=i(92261),l=(()=>{return(g=l||(l={})).DEBUG="DEBUG",g.ERROR="ERROR",g.INFO="INFO",g.WARN="WARN",g.VERBOSE="VERBOSE",g.NONE="NONE",l;var g})();const d={VERBOSE:1,DEBUG:2,INFO:3,WARN:4,ERROR:5,NONE:6};let r=(()=>{class g{constructor(n,h=l.WARN){this.name=n,this.level=h,this._pluggables=[]}_padding(n){return n<10?"0"+n:""+n}_ts(){const n=new Date;return[this._padding(n.getMinutes()),this._padding(n.getSeconds())].join(":")+"."+n.getMilliseconds()}configure(n){return n?(this._config=n,this._config):this._config}_log(n,...h){let m=this.level;if(g.LOG_LEVEL&&(m=g.LOG_LEVEL),typeof window<"u"&&window.LOG_LEVEL&&(m=window.LOG_LEVEL),!(d[n]>=d[m]))return;let I=console.log.bind(console);n===l.ERROR&&console.error&&(I=console.error.bind(console)),n===l.WARN&&console.warn&&(I=console.warn.bind(console)),g.BIND_ALL_LOG_LEVELS&&(n===l.INFO&&console.info&&(I=console.info.bind(console)),n===l.DEBUG&&console.debug&&(I=console.debug.bind(console)));const _=`[${n}] ${this._ts()} ${this.name}`;let v="";if(1===h.length&&"string"==typeof h[0])v=`${_} - ${h[0]}`,I(v);else if(1===h.length)v=`${_} ${h[0]}`,I(_,h[0]);else if("string"==typeof h[0]){let c=h.slice(1);1===c.length&&(c=c[0]),v=`${_} - ${h[0]} ${c}`,I(`${_} - ${h[0]}`,c)}else v=`${_} ${h}`,I(_,h);for(const c of this._pluggables){const T={message:v,timestamp:Date.now()};c.pushLogs([T])}}log(...n){this._log(l.INFO,...n)}info(...n){this._log(l.INFO,...n)}warn(...n){this._log(l.WARN,...n)}error(...n){this._log(l.ERROR,...n)}debug(...n){this._log(l.DEBUG,...n)}verbose(...n){this._log(l.VERBOSE,...n)}addPluggable(n){n&&n.getCategoryName()===o.YG&&(this._pluggables.push(n),n.configure(this._config))}listPluggables(){return this._pluggables}}return g.LOG_LEVEL=null,g.BIND_ALL_LOG_LEVELS=!1,g})()},91396:(b,A,i)=>{i.d(A,{Cj:()=>L,QW:()=>B});var o=i(81220);const l=()=>typeof global<"u",r=()=>typeof window<"u",g=()=>typeof document<"u",p=()=>typeof process<"u",n=(k,F)=>!!Object.keys(k).find(K=>K.startsWith(F)),oe=[{platform:o.gQ.Expo,detectionMethod:function J(){return l()&&typeof global.expo<"u"}},{platform:o.gQ.ReactNative,detectionMethod:function H(){return typeof navigator<"u"&&typeof navigator.product<"u"&&"ReactNative"===navigator.product}},{platform:o.gQ.NextJs,detectionMethod:function v(){return r()&&window.next&&"object"==typeof window.next}},{platform:o.gQ.Nuxt,detectionMethod:function T(){return r()&&(void 0!==window.__NUXT__||void 0!==window.$nuxt)}},{platform:o.gQ.Angular,detectionMethod:function Z(){const k=Boolean(g()&&document.querySelector("[ng-version]")),F=Boolean(r()&&typeof window.ng<"u");return k||F}},{platform:o.gQ.React,detectionMethod:function h(){const k=j=>j.startsWith("_react")||j.startsWith("__react");return g()&&Array.from(document.querySelectorAll("[id]")).some(j=>Object.keys(j).find(k))}},{platform:o.gQ.VueJs,detectionMethod:function S(){return r()&&n(window,"__VUE")}},{platform:o.gQ.Svelte,detectionMethod:function I(){return r()&&n(window,"__SVELTE")}},{platform:o.gQ.WebUnknown,detectionMethod:function le(){return r()}},{platform:o.gQ.NextJsSSR,detectionMethod:function c(){return l()&&(n(global,"__next")||n(global,"__NEXT"))}},{platform:o.gQ.NuxtSSR,detectionMethod:function P(){return l()&&typeof global.__NUXT_PATHS__<"u"}},{platform:o.gQ.ReactSSR,detectionMethod:function m(){return p()&&typeof process.env<"u"&&!!Object.keys(process.env).find(k=>k.includes("react"))}},{platform:o.gQ.VueJsSSR,detectionMethod:function u(){return l()&&n(global,"__VUE")}},{platform:o.gQ.AngularSSR,detectionMethod:function W(){return p()&&"object"==typeof process.env&&process.env.npm_lifecycle_script?.startsWith("ng ")||!1}},{platform:o.gQ.SvelteSSR,detectionMethod:function _(){return p()&&typeof process.env<"u"&&!!Object.keys(process.env).find(k=>k.includes("svelte"))}}];let f;const E=[];let w=!1;const U=1e3,L=()=>{if(!f){if(f=function ge(){return oe.find(k=>k.detectionMethod())?.platform||o.gQ.ServerSideUnknown}(),w)for(;E.length;)E.pop()?.();else E.forEach(k=>{k()});V(o.gQ.ServerSideUnknown,10),V(o.gQ.WebUnknown,10)}return f},B=k=>{w||E.push(k)};function V(k,F){f===k&&!w&&setTimeout(()=>{(function x(){f=void 0})(),w=!0,setTimeout(L,U)},F)}},5919:(b,A,i)=>{i.d(A,{Zm:()=>I});var o=i(81220);const l="6.13.1";var d=i(91396);const r={},n="aws-amplify",h=_=>_.replace(/\+.*/,"");new class m{constructor(){this.userAgent=`${n}/${h(l)}`}get framework(){return(0,d.Cj)()}get isReactNative(){return this.framework===o.gQ.ReactNative||this.framework===o.gQ.Expo}observeFrameworkChanges(v){(0,d.QW)(v)}};const I=_=>(({category:_,action:v}={})=>{const c=[[n,h(l)]];if(_&&c.push([_,v]),c.push(["framework",(0,d.Cj)()]),_&&v){const T=((_,v)=>r[_]?.[v]?.additionalDetails)(_,v);T&&T.forEach(P=>{c.push(P)})}return c})(_).map(([T,P])=>T&&P?`${T}/${P}`:T).join(" ")},81220:(b,A,i)=>{i.d(A,{WD:()=>l,gQ:()=>o,gq:()=>g});var o=(()=>{return(c=o||(o={})).WebUnknown="0",c.React="1",c.NextJs="2",c.Angular="3",c.VueJs="4",c.Nuxt="5",c.Svelte="6",c.ServerSideUnknown="100",c.ReactSSR="101",c.NextJsSSR="102",c.AngularSSR="103",c.VueJsSSR="104",c.NuxtSSR="105",c.SvelteSSR="106",c.ReactNative="201",c.Expo="202",o;var c})(),l=(()=>{return(c=l||(l={})).AI="ai",c.API="api",c.Auth="auth",c.Analytics="analytics",c.DataStore="datastore",c.Geo="geo",c.InAppMessaging="inappmessaging",c.Interactions="interactions",c.Predictions="predictions",c.PubSub="pubsub",c.PushNotification="pushnotification",c.Storage="storage",l;var c})(),g=(()=>{return(c=g||(g={})).GraphQl="1",c.Get="2",c.Post="3",c.Put="4",c.Patch="5",c.Del="6",c.Head="7",g;var c})()},40454:(b,A,i)=>{i.d(A,{b:()=>d});const o={id:"aws",outputs:{dnsSuffix:"amazonaws.com"},regionRegex:"^(us|eu|ap|sa|ca|me|af)\\-\\w+\\-\\d+$",regions:["aws-global"]},l={partitions:[o,{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn"},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:["aws-cn-global"]}]},d=r=>{const{partitions:g}=l;for(const{regions:p,outputs:n,regionRegex:h}of g){const m=new RegExp(h);if(p.includes(r)||m.test(r))return n.dnsSuffix}return o.outputs.dnsSuffix}},6990:(b,A,i)=>{i.d(A,{S:()=>p});var o=i(15861),l=i(91935),d=i(87199);const r=n=>{let h;return()=>(h||(h=n()),h)},g=n=>!["HEAD","GET","DELETE"].includes(n.toUpperCase()),p=function(){var n=(0,o.Z)(function*({url:h,method:m,headers:S,body:u},{abortSignal:I,cache:_,withCrossDomainCredentials:v}){let c;try{c=yield fetch(h,{method:m,headers:S,body:g(m)?u:void 0,signal:I,cache:_,credentials:v?"include":"same-origin"})}catch(W){throw W instanceof TypeError?new l._({name:d.Z.NetworkError,message:"A network error has occurred.",underlyingError:W}):W}const T={};return c.headers?.forEach((W,H)=>{T[H.toLowerCase()]=W}),{statusCode:c.status,headers:T,body:null,body:Object.assign(c.body??{},{text:r(()=>c.text()),blob:r(()=>c.blob()),json:r(()=>c.json())})}});return function(m,S){return n.apply(this,arguments)}}()},54473:(b,A,i)=>{i.d(A,{y:()=>g});var o=i(3454),l=i(46570),d=i(4079),r=i(6990);const g=(0,d.V)(r.S,[l.n,o.d])},62942:(b,A,i)=>{i.d(A,{z:()=>l});var o=i(15861);const l=(d,r,g,p)=>function(){var n=(0,o.Z)(function*(h,m){const S={...p,...h},u=yield S.endpointResolver(S,m),I=yield r(m,u),_=yield d(I,{...S});return g(_)});return function(h,m){return n.apply(this,arguments)}}()},4079:(b,A,i)=>{i.d(A,{V:()=>o});const o=(l,d)=>(r,g)=>{const p={};let n=h=>l(h,g);for(let h=d.length-1;h>=0;h--)n=(0,d[h])(g)(n,p);return n(r)}},67834:(b,A,i)=>{i.d(A,{j:()=>g});var o=i(15861),l=i(87199);const d=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch","BadRequestException"],r=u=>!!u&&d.includes(u),g=u=>function(){var I=(0,o.Z)(function*(_,v){const c=v??(yield u(_))??void 0,T=c?.code||c?.name,P=_?.statusCode;return{retryable:m(v)||h(P,T)||r(T)||S(P,T)}});return function(_,v){return I.apply(this,arguments)}}(),p=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException"],n=["TimeoutError","RequestTimeout","RequestTimeoutException"],h=(u,I)=>429===u||!!I&&p.includes(I),m=u=>[l.Z.NetworkError,"ERR_NETWORK"].includes(u?.name),S=(u,I)=>!!u&&[500,502,503,504].includes(u)||!!I&&n.includes(I)},79987:(b,A,i)=>{i.d(A,{k:()=>d});var o=i(75599);const l=3e5,d=r=>{const p=(0,o.k)(l)(r);return!1===p?l:p}},3454:(b,A,i)=>{i.d(A,{d:()=>d});var o=i(15861);const d=({maxAttempts:p=3,retryDecider:n,computeDelay:h,abortSignal:m})=>{if(p<1)throw new Error("maxAttempts must be greater than 0");return(S,u)=>function(){var I=(0,o.Z)(function*(v){let c,P,T=u.attemptsCount??0;const Z=()=>{if(P)return g(P,T),P;throw g(c,T),c};for(;!m?.aborted&&T<p;){try{P=yield S(v),c=void 0}catch(J){c=J,P=void 0}T=(u.attemptsCount??0)>T?u.attemptsCount??0:T+1,u.attemptsCount=T;const{isCredentialsExpiredError:W,retryable:H}=yield n(P,c,u);if(!H)return Z();if(u.isCredentialsExpired=!!W,!m?.aborted&&T<p){const J=h(T);yield r(J,m)}}if(m?.aborted)throw new Error("Request aborted.");return Z()});return function _(v){return I.apply(this,arguments)}}()},r=(p,n)=>{if(n?.aborted)return Promise.resolve();let h,m;const S=new Promise(u=>{m=u,h=setTimeout(u,p)});return n?.addEventListener("abort",function u(I){clearTimeout(h),n?.removeEventListener("abort",u),m()}),S},g=(p,n)=>{"[object Object]"===Object.prototype.toString.call(p)&&(p.$metadata={...p.$metadata??{},attempts:n})}},46570:(b,A,i)=>{i.d(A,{n:()=>l});var o=i(15861);const l=({userAgentHeader:d="x-amz-user-agent",userAgentValue:r=""})=>g=>function(){var p=(0,o.Z)(function*(h){if(0===r.trim().length)return yield g(h);{const m=d.toLowerCase();return h.headers[m]=h.headers[m]?`${h.headers[m]} ${r}`:r,yield g(h)}});return function n(h){return p.apply(this,arguments)}}()},54974:(b,A,i)=>{i.d(A,{e:()=>r,f:()=>d});var o=i(15861),l=i(97282);const d=function(){var g=(0,o.Z)(function*(p){if(!p||p.statusCode<300)return;const n=yield r(p),m=(I=>{const[_]=I.toString().split(/[,:]+/);return _.includes("#")?_.split("#")[1]:_})(p.headers["x-amzn-errortype"]??n.code??n.__type??"UnknownError"),u=new Error(n.message??n.Message??"Unknown error");return Object.assign(u,{name:m,$metadata:(0,l.B)(p)})});return function(n){return g.apply(this,arguments)}}(),r=function(){var g=(0,o.Z)(function*(p){if(!p.body)throw new Error("Missing response payload");const n=yield p.body.json();return Object.assign(n,{$metadata:(0,l.B)(p)})});return function(n){return g.apply(this,arguments)}}()},97282:(b,A,i)=>{i.d(A,{B:()=>o});const o=d=>{const{headers:r,statusCode:g}=d;return{...l(d)?d.$metadata:{},httpStatusCode:g,requestId:r["x-amzn-requestid"]??r["x-amzn-request-id"]??r["x-amz-request-id"],extendedRequestId:r["x-amz-id-2"],cfId:r["x-amz-cf-id"]}},l=d=>"object"==typeof d?.$metadata},92261:(b,A,i)=>{i.d(A,{Mt:()=>l,YG:()=>o,z2:()=>d});const o="Logging",l="x-amz-user-agent",d="NoHubcallbackProvidedException"},91935:(b,A,i)=>{i.d(A,{_:()=>o});class o extends Error{constructor({message:d,name:r,recoverySuggestion:g,underlyingError:p}){super(d),this.name=r,this.underlyingError=p,this.recoverySuggestion=g,this.constructor=o,Object.setPrototypeOf(this,o.prototype)}}},82847:(b,A,i)=>{i.d(A,{$:()=>l});var o=i(91935);const l=(d,r=o._)=>(g,p,n)=>{const{message:h,recoverySuggestion:m}=d[p];if(!g)throw new r({name:p,message:n?`${h} ${n}`:h,recoverySuggestion:m})}},74173:(b,A,i)=>{i.d(A,{YE:()=>m,FG:()=>n,xp:()=>S});var o=i(10180);const l={convert(u,I){let _=u;return I?.urlSafe&&(_=_.replace(/-/g,"+").replace(/_/g,"/")),(0,o.tl)()(_)}};var d=i(82847),r=(()=>{return(u=r||(r={})).AuthTokenConfigException="AuthTokenConfigException",u.AuthUserPoolAndIdentityPoolException="AuthUserPoolAndIdentityPoolException",u.AuthUserPoolException="AuthUserPoolException",u.InvalidIdentityPoolIdException="InvalidIdentityPoolIdException",u.OAuthNotConfigureException="OAuthNotConfigureException",r;var u})();const p=(0,d.$)({[r.AuthTokenConfigException]:{message:"Auth Token Provider not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app."},[r.AuthUserPoolAndIdentityPoolException]:{message:"Auth UserPool or IdentityPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with UserPoolId and IdentityPoolId."},[r.AuthUserPoolException]:{message:"Auth UserPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with userPoolId and userPoolClientId."},[r.InvalidIdentityPoolIdException]:{message:"Invalid identity pool id provided.",recoverySuggestion:"Make sure a valid identityPoolId is given in the config."},[r.OAuthNotConfigureException]:{message:"oauth param not configured.",recoverySuggestion:"Make sure to call Amplify.configure with oauth parameter in your app."}});function n(u){let I=!0;I=!!u&&!!u.userPoolId&&!!u.userPoolClientId,p(I,r.AuthUserPoolException)}function m(u){p(!!u?.identityPoolId,r.InvalidIdentityPoolIdException)}function S(u){const I=u.split(".");if(3!==I.length)throw new Error("Invalid token");try{const v=I[1].replace(/-/g,"+").replace(/_/g,"/"),c=decodeURIComponent(l.convert(v).split("").map(P=>`%${`00${P.charCodeAt(0).toString(16)}`.slice(-2)}`).join(""));return{toString:()=>u,payload:JSON.parse(c)}}catch{throw new Error("Invalid token payload")}}},87199:(b,A,i)=>{i.d(A,{Z:()=>o});var o=(()=>{return(l=o||(o={})).NoEndpointId="NoEndpointId",l.PlatformNotSupported="PlatformNotSupported",l.Unknown="Unknown",l.NetworkError="NetworkError",o;var l})()},99120:(b,A,i)=>{i.d(A,{a:()=>o,z:()=>l});const o=URL,l=URLSearchParams},10180:(b,A,i)=>{i.d(A,{Ds:()=>d,tl:()=>r});var o=i(91935);const d=()=>{if(typeof window<"u"&&"function"==typeof window.btoa)return window.btoa;if("function"==typeof btoa)return btoa;throw new o._({name:"Base64EncoderError",message:"Cannot resolve the `btoa` function from the environment."})},r=()=>{if(typeof window<"u"&&"function"==typeof window.atob)return window.atob;if("function"==typeof atob)return atob;throw new o._({name:"Base64EncoderError",message:"Cannot resolve the `atob` function from the environment."})}},96594:(b,A,i)=>{i.d(A,{j:()=>o});const o=()=>typeof window<"u"&&typeof window.document<"u"},52458:(b,A,i)=>{i.d(A,{h:()=>ge});var o=i(55502),l=i(91935);const d=new o.k("parseAWSExports"),r={API_KEY:"apiKey",AWS_IAM:"iam",AMAZON_COGNITO_USER_POOLS:"userPool",OPENID_CONNECT:"oidc",NONE:"none",AWS_LAMBDA:"lambda",LAMBDA:"lambda"},p=f=>f?.split(",")??[],n=({domain:f,scope:E,redirectSignIn:w,redirectSignOut:R,responseType:D})=>({domain:f,scopes:E,redirectSignIn:p(w),redirectSignOut:p(R),responseType:D}),h=f=>f.map(E=>{const w=E.toLowerCase();return w.charAt(0).toUpperCase()+w.slice(1)});const Z={AMAZON_COGNITO_USER_POOLS:"userPool",API_KEY:"apiKey",AWS_IAM:"iam",AWS_LAMBDA:"lambda",OPENID_CONNECT:"oidc"};function W(f){return Z[f]}const H={GOOGLE:"Google",LOGIN_WITH_AMAZON:"Amazon",FACEBOOK:"Facebook",SIGN_IN_WITH_APPLE:"Apple"};function J(f=[]){return f.reduce((E,w)=>(void 0!==H[w]&&E.push(H[w]),E),[])}function le(f){return"OPTIONAL"===f?"optional":"REQUIRED"===f?"on":"off"}function oe(f){const E={};return f.forEach(({name:w,bucket_name:R,aws_region:D,paths:U})=>{if(w in E)throw new Error(`Duplicate friendly name found: ${w}. Name must be unique.`);const L=U?Object.entries(U).reduce((B,[x,V])=>(void 0!==V&&(B[x]=V),B),{}):void 0;E[w]={bucketName:R,region:D,paths:L}}),E}const ge=f=>Object.keys(f).some(E=>E.startsWith("aws_"))?((f={})=>{if(!Object.prototype.hasOwnProperty.call(f,"aws_project_region"))throw new l._({name:"InvalidParameterException",message:"Invalid config parameter.",recoverySuggestion:"Ensure passing the config object imported from  `amplifyconfiguration.json`."});const{aws_appsync_apiKey:E,aws_appsync_authenticationType:w,aws_appsync_graphqlEndpoint:R,aws_appsync_region:D,aws_bots_config:U,aws_cognito_identity_pool_id:L,aws_cognito_sign_up_verification_method:B,aws_cognito_mfa_configuration:x,aws_cognito_mfa_types:V,aws_cognito_password_protection_settings:k,aws_cognito_verification_mechanisms:F,aws_cognito_signup_attributes:K,aws_cognito_social_providers:j,aws_cognito_username_attributes:te,aws_mandatory_sign_in:ie,aws_mobile_analytics_app_id:re,aws_mobile_analytics_app_region:fe,aws_user_files_s3_bucket:he,aws_user_files_s3_bucket_region:Ce,aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing:Te,aws_user_pools_id:_e,aws_user_pools_web_client_id:we,geo:pe,oauth:se,predictions:Y,aws_cloud_logic_custom:ae,Notifications:Pe,modelIntrospection:ye}=f,M={};re&&(M.Analytics={Pinpoint:{appId:re,region:fe}});const{InAppMessaging:ce,Push:de}=Pe??{};if(ce?.AWSPinpoint||de?.AWSPinpoint){if(ce?.AWSPinpoint){const{appId:O,region:X}=ce.AWSPinpoint;M.Notifications={InAppMessaging:{Pinpoint:{appId:O,region:X}}}}if(de?.AWSPinpoint){const{appId:O,region:X}=de.AWSPinpoint;M.Notifications={...M.Notifications,PushNotification:{Pinpoint:{appId:O,region:X}}}}}if(Array.isArray(U)&&(M.Interactions={LexV1:Object.fromEntries(U.map(O=>[O.name,O]))}),R){const O=r[w];O||d.debug(`Invalid authentication type ${w}. Falling back to IAM.`),M.API={GraphQL:{endpoint:R,apiKey:E,region:D,defaultAuthMode:O??"iam"}},ye&&(M.API.GraphQL.modelIntrospection=ye)}const ke=x?{status:x&&x.toLowerCase(),totpEnabled:V?.includes("TOTP")??!1,smsEnabled:V?.includes("SMS")??!1}:void 0,be=k?{minLength:k.passwordPolicyMinLength,requireLowercase:k.passwordPolicyCharacters?.includes("REQUIRES_LOWERCASE")??!1,requireUppercase:k.passwordPolicyCharacters?.includes("REQUIRES_UPPERCASE")??!1,requireNumbers:k.passwordPolicyCharacters?.includes("REQUIRES_NUMBERS")??!1,requireSpecialCharacters:k.passwordPolicyCharacters?.includes("REQUIRES_SYMBOLS")??!1}:void 0,Re=Array.from(new Set([...F??[],...K??[]])).reduce((O,X)=>({...O,[X.toLowerCase()]:{required:!0}}),{}),De=te?.includes("EMAIL")??!1,Ae=te?.includes("PHONE_NUMBER")??!1;(L||_e)&&(M.Auth={Cognito:{identityPoolId:L,allowGuestAccess:"enable"!==ie,signUpVerificationMethod:B,userAttributes:Re,userPoolClientId:we,userPoolId:_e,mfa:ke,passwordFormat:be,loginWith:{username:!(De||Ae),email:De,phone:Ae}}});const Oe=!!se&&Object.keys(se).length>0,ee=!!j&&j.length>0;if(M.Auth&&Oe&&(M.Auth.Cognito.loginWith={...M.Auth.Cognito.loginWith,oauth:{...n(se),...ee&&{providers:h(j)}}}),he&&(M.Storage={S3:{bucket:he,region:Ce,dangerouslyConnectToHttpEndpointForTesting:Te}}),pe){const{amazon_location_service:O}=pe;M.Geo={LocationService:{maps:O.maps,geofenceCollections:O.geofenceCollections,searchIndices:O.search_indices,region:O.region}}}if(ae&&(M.API={...M.API,REST:ae.reduce((O,X)=>{const{name:Ie,endpoint:Me,region:me,service:ne}=X;return{...O,[Ie]:{endpoint:Me,...ne?{service:ne}:void 0,...me?{region:me}:void 0}}},{})}),Y){const{VoiceId:O}=Y?.convert?.speechGenerator?.defaults??{};M.Predictions=O?{...Y,convert:{...Y.convert,speechGenerator:{...Y.convert.speechGenerator,defaults:{voiceId:O}}}}:Y}return M})(f):function m(f){const{version:E}=f;return!!E&&E.startsWith("1")}(f)?function P(f){const E={};if(f.storage&&(E.Storage=function S(f){if(!f)return;const{bucket_name:E,aws_region:w,buckets:R}=f;return{S3:{bucket:E,region:w,buckets:R&&oe(R)}}}(f.storage)),f.auth&&(E.Auth=function u(f){if(!f)return;const{user_pool_id:E,user_pool_client_id:w,identity_pool_id:R,password_policy:D,mfa_configuration:U,mfa_methods:L,unauthenticated_identities_enabled:B,oauth:x,username_attributes:V,standard_required_attributes:k,groups:F}=f,K={Cognito:{userPoolId:E,userPoolClientId:w,groups:F}};return R&&(K.Cognito={...K.Cognito,identityPoolId:R}),D&&(K.Cognito.passwordFormat={requireLowercase:D.require_lowercase,requireNumbers:D.require_numbers,requireUppercase:D.require_uppercase,requireSpecialCharacters:D.require_symbols,minLength:D.min_length??6}),U&&(K.Cognito.mfa={status:le(U),smsEnabled:L?.includes("SMS"),totpEnabled:L?.includes("TOTP")}),B&&(K.Cognito.allowGuestAccess=B),x&&(K.Cognito.loginWith={oauth:{domain:x.domain,redirectSignIn:x.redirect_sign_in_uri,redirectSignOut:x.redirect_sign_out_uri,responseType:"token"===x.response_type?"token":"code",scopes:x.scopes,providers:J(x.identity_providers)}}),V&&(K.Cognito.loginWith={...K.Cognito.loginWith,email:V.includes("email"),phone:V.includes("phone_number"),username:V.includes("username")}),k&&(K.Cognito.userAttributes=k.reduce((j,te)=>({...j,[te]:{required:!0}}),{})),K}(f.auth)),f.analytics&&(E.Analytics=function I(f){if(!f?.amazon_pinpoint)return;const{amazon_pinpoint:E}=f;return{Pinpoint:{appId:E.app_id,region:E.aws_region}}}(f.analytics)),f.geo&&(E.Geo=function _(f){if(!f)return;const{aws_region:E,geofence_collections:w,maps:R,search_indices:D}=f;return{LocationService:{region:E,searchIndices:D,geofenceCollections:w,maps:R}}}(f.geo)),f.data&&(E.API=function v(f){if(!f)return;const{aws_region:E,default_authorization_type:w,url:R,api_key:D,model_introspection:U}=f;return{GraphQL:{endpoint:R,defaultAuthMode:W(w),region:E,apiKey:D,modelIntrospection:U}}}(f.data)),f.custom){const w=function c(f){if(!f?.events)return;const{url:E,aws_region:w,api_key:R,default_authorization_type:D}=f.events;return{Events:{endpoint:E,defaultAuthMode:W(D),region:w,apiKey:R}}}(f.custom);w&&"Events"in w&&(E.API={...E.API,...w})}return f.notifications&&(E.Notifications=function T(f){if(!f)return;const{aws_region:E,channels:w,amazon_pinpoint_app_id:R}=f,D=w.includes("IN_APP_MESSAGING"),U=w.includes("APNS")||w.includes("FCM");if(!D&&!U)return;const L={};return D&&(L.InAppMessaging={Pinpoint:{appId:R,region:E}}),U&&(L.PushNotification={Pinpoint:{appId:R,region:E}}),L}(f.notifications)),E}(f):f},90206:(b,A,i)=>{i.d(A,{t:()=>o});const o=3e5},75599:(b,A,i)=>{i.d(A,{k:()=>l});var o=i(90206);function l(d=o.t){return p=>{const n=2**p*100+100*Math.random();return!(n>d)&&n}}},78190:(b,A,i)=>{i.r(A),i.d(A,{Amplify:()=>at});var o=i(98778),l=i(52458),d=i(15861),r=i(74173),p=i(91935);class n extends p._{constructor(e){super(e),this.constructor=n,Object.setPrototypeOf(this,n.prototype)}}function m(s){if(!s||!s.includes(":"))throw new n({name:"InvalidIdentityPoolIdException",message:"Invalid identity pool id provided.",recoverySuggestion:"Make sure a valid identityPoolId is given in the config."});return s.split(":")[0]}const S="UserUnAuthenticatedException";new n({name:"InvalidRedirectException",message:"signInRedirect or signOutRedirect had an invalid format or was not found.",recoverySuggestion:"Please make sure the signIn/Out redirect in your oauth config is valid."}),new n({name:"InvalidAppSchemeException",message:"A valid non-http app scheme was not found in the config.",recoverySuggestion:"Please make sure a valid custom app scheme is present in the config."}),new n({name:"InvalidPreferredRedirectUrlException",message:"The given preferredRedirectUrl does not match any items in the redirectSignOutUrls array from the config.",recoverySuggestion:"Please make sure a matching preferredRedirectUrl is provided."}),new n({name:"InvalidOriginException",message:"redirect is coming from a different origin. The oauth flow needs to be initiated from the same origin",recoverySuggestion:"Please call signInWithRedirect from the same origin."});const R=new n({name:"TokenRefreshException",message:"Token refresh is not supported when authenticated with the 'implicit grant' (token) oauth flow. \n\tPlease change your oauth configuration to use 'code grant' flow.",recoverySuggestion:"Please logout and change your Amplify configuration to use \"code grant\" flow. \n\tE.g { responseType: 'code' }"}),D=new n({name:S,message:"User needs to be authenticated to call this API.",recoverySuggestion:"Sign in before calling this API again."});const B={inflightOAuth:"inflightOAuth",oauthSignIn:"oauthSignIn",oauthPKCE:"oauthPKCE",oauthState:"oauthState"};function x(s){return s?.accessToken||s?.idToken}var F=i(62942);var ie=i(54974),re=i(87199);function fe(s){if(!s||"Error"===s.name||s instanceof TypeError)throw new n({name:re.Z.Unknown,message:"An unknown error has occurred.",underlyingError:s})}const he=()=>function(){var s=(0,d.Z)(function*(e){if(e.statusCode>=300){const t=yield(0,ie.f)(e);throw fe(t),new n({name:t.name,message:t.message})}return(0,ie.e)(e)});return function(e){return s.apply(this,arguments)}}();var Ce=i(4079),Te=i(54473);const we=(0,Ce.V)(Te.y,[()=>(s,e)=>function(){var t=(0,d.Z)(function*(y){return y.headers["cache-control"]="no-store",s(y)});return function a(y){return t.apply(this,arguments)}}()]);var pe=i(67834),se=i(79987),Y=i(5919);const ae="cognito-idp",Pe={service:ae,retryDecider:(0,pe.j)(ie.f),computeDelay:se.k,userAgentValue:(0,Y.Zm)(),cache:"no-store"};var M=i(99120),ce=i(40454);const ke=({endpointOverride:s})=>e=>s?{url:new M.a(s)}:(({region:s})=>({url:new M.a(`https://${ae}.${s}.${(0,ce.b)(s)}`)}))(e),Re=(s=>{let e;return(0,d.Z)(function*(...t){return e||(e=new Promise((a,y)=>{s(...t).then(C=>{a(C)}).catch(C=>{y(C)}).finally(()=>{e=void 0})}),e)})})(function(){var s=(0,d.Z)(function*({tokens:e,authConfig:t,username:a}){(0,r.FG)(t?.Cognito);const{userPoolId:y,userPoolClientId:C,userPoolEndpoint:G}=t.Cognito,N=function h(s){const e=s?.split("_")[0];if(!s||s.indexOf("_")<0||!e||"string"!=typeof e)throw new n({name:"InvalidUserPoolId",message:"Invalid user pool id provided."});return e}(y);!function U(s){if(function k(s){return x(s)&&!s?.refreshToken}(s))throw R;if(!function V(s){return x(s)&&s?.refreshToken}(s))throw D}(e);const $=e.refreshToken,q={REFRESH_TOKEN:$};e.deviceMetadata?.deviceKey&&(q.DEVICE_KEY=e.deviceMetadata.deviceKey);const Ee=function be({username:s,userPoolId:e,userPoolClientId:t}){if(typeof window>"u")return;const a=window.AmazonCognitoAdvancedSecurityData;if(typeof a>"u")return;const y=a.getData(s,e,t);return y?{EncodedData:y}:{}}({username:a,userPoolId:y,userPoolClientId:C}),ve=(s=>(0,F.z)(we,(e,t)=>(({url:s},e,t)=>({headers:e,url:s,body:t,method:"POST"}))(t,{"content-type":"application/x-amz-json-1.1","x-amz-target":"AWSCognitoIdentityProviderService.InitiateAuth"},JSON.stringify(e)),he(),{...Pe,...s}))({endpointResolver:ke({endpointOverride:G})}),{AuthenticationResult:Ke}=yield ve({region:N},{ClientId:C,AuthFlow:"REFRESH_TOKEN_AUTH",AuthParameters:q,UserContextData:Ee}),$e=(0,r.xp)(Ke?.AccessToken??""),ct=Ke?.IdToken?(0,r.xp)(Ke.IdToken):void 0,{iat:Ze}=$e.payload;if(!Ze)throw new n({name:"iatNotFoundException",message:"iat not found in access token"});return{accessToken:$e,idToken:ct,clockDrift:1e3*Ze-(new Date).getTime(),refreshToken:$,username:a}});return function(t){return s.apply(this,arguments)}}()),Ae={accessToken:"accessToken",idToken:"idToken",oidcProvider:"oidcProvider",clockDrift:"clockDrift",refreshToken:"refreshToken",deviceKey:"deviceKey",randomPasswordKey:"randomPasswordKey",deviceGroupKey:"deviceGroupKey",signInDetails:"signInDetails",oauthMetadata:"oauthMetadata"};var Oe=i(82847),ee=(()=>((ee||(ee={})).InvalidAuthTokens="InvalidAuthTokens",ee))();const X=(0,Oe.$)({[ee.InvalidAuthTokens]:{message:"Invalid tokens.",recoverySuggestion:"Make sure the tokens are valid."}}),Ie="CognitoIdentityServiceProvider";class Me{getKeyValueStorage(){if(!this.keyValueStorage)throw new n({name:"KeyValueStorageNotFoundException",message:"KeyValueStorage was not found in TokenStore"});return this.keyValueStorage}setKeyValueStorage(e){this.keyValueStorage=e}setAuthConfig(e){this.authConfig=e}loadTokens(){var e=this;return(0,d.Z)(function*(){try{const t=yield e.getAuthKeys(),a=yield e.getKeyValueStorage().getItem(t.accessToken);if(!a)throw new n({name:"NoSessionFoundException",message:"Auth session was not found. Make sure to call signIn."});const y=(0,r.xp)(a),C=yield e.getKeyValueStorage().getItem(t.idToken),G=C?(0,r.xp)(C):void 0,N=(yield e.getKeyValueStorage().getItem(t.refreshToken))??void 0,$=(yield e.getKeyValueStorage().getItem(t.clockDrift))??"0",q=Number.parseInt($),Ee=yield e.getKeyValueStorage().getItem(t.signInDetails),ve={accessToken:y,idToken:G,refreshToken:N,deviceMetadata:(yield e.getDeviceMetadata())??void 0,clockDrift:q,username:yield e.getLastAuthUser()};return Ee&&(ve.signInDetails=JSON.parse(Ee)),ve}catch{return null}})()}storeTokens(e){var t=this;return(0,d.Z)(function*(){X(void 0!==e,ee.InvalidAuthTokens);const a=e.username;yield t.getKeyValueStorage().setItem(t.getLastAuthUserKey(),a);const y=yield t.getAuthKeys();yield t.getKeyValueStorage().setItem(y.accessToken,e.accessToken.toString()),e.idToken?yield t.getKeyValueStorage().setItem(y.idToken,e.idToken.toString()):yield t.getKeyValueStorage().removeItem(y.idToken),e.refreshToken?yield t.getKeyValueStorage().setItem(y.refreshToken,e.refreshToken):yield t.getKeyValueStorage().removeItem(y.refreshToken),e.deviceMetadata&&(e.deviceMetadata.deviceKey&&(yield t.getKeyValueStorage().setItem(y.deviceKey,e.deviceMetadata.deviceKey)),e.deviceMetadata.deviceGroupKey&&(yield t.getKeyValueStorage().setItem(y.deviceGroupKey,e.deviceMetadata.deviceGroupKey)),yield t.getKeyValueStorage().setItem(y.randomPasswordKey,e.deviceMetadata.randomPassword)),e.signInDetails?yield t.getKeyValueStorage().setItem(y.signInDetails,JSON.stringify(e.signInDetails)):yield t.getKeyValueStorage().removeItem(y.signInDetails),yield t.getKeyValueStorage().setItem(y.clockDrift,`${e.clockDrift}`)})()}clearTokens(){var e=this;return(0,d.Z)(function*(){const t=yield e.getAuthKeys();yield Promise.all([e.getKeyValueStorage().removeItem(t.accessToken),e.getKeyValueStorage().removeItem(t.idToken),e.getKeyValueStorage().removeItem(t.clockDrift),e.getKeyValueStorage().removeItem(t.refreshToken),e.getKeyValueStorage().removeItem(t.signInDetails),e.getKeyValueStorage().removeItem(e.getLastAuthUserKey()),e.getKeyValueStorage().removeItem(t.oauthMetadata)])})()}getDeviceMetadata(e){var t=this;return(0,d.Z)(function*(){const a=yield t.getAuthKeys(e),y=yield t.getKeyValueStorage().getItem(a.deviceKey),C=yield t.getKeyValueStorage().getItem(a.deviceGroupKey),G=yield t.getKeyValueStorage().getItem(a.randomPasswordKey);return G&&C&&y?{deviceKey:y,deviceGroupKey:C,randomPassword:G}:null})()}clearDeviceMetadata(e){var t=this;return(0,d.Z)(function*(){const a=yield t.getAuthKeys(e);yield Promise.all([t.getKeyValueStorage().removeItem(a.deviceKey),t.getKeyValueStorage().removeItem(a.deviceGroupKey),t.getKeyValueStorage().removeItem(a.randomPasswordKey)])})()}getAuthKeys(e){var t=this;return(0,d.Z)(function*(){(0,r.FG)(t.authConfig?.Cognito);const a=e??(yield t.getLastAuthUser());return me(Ie,`${t.authConfig.Cognito.userPoolClientId}.${a}`)})()}getLastAuthUserKey(){return(0,r.FG)(this.authConfig?.Cognito),`${Ie}.${this.authConfig.Cognito.userPoolClientId}.LastAuthUser`}getLastAuthUser(){var e=this;return(0,d.Z)(function*(){return(yield e.getKeyValueStorage().getItem(e.getLastAuthUserKey()))??"username"})()}setOAuthMetadata(e){var t=this;return(0,d.Z)(function*(){const{oauthMetadata:a}=yield t.getAuthKeys();yield t.getKeyValueStorage().setItem(a,JSON.stringify(e))})()}getOAuthMetadata(){var e=this;return(0,d.Z)(function*(){const{oauthMetadata:t}=yield e.getAuthKeys(),a=yield e.getKeyValueStorage().getItem(t);return a&&JSON.parse(a)})()}}const me=(s,e)=>ne(Ae)(`${s}`,e);function ne(s){const e=Object.values({...s});return(t,a)=>e.reduce((y,C)=>({...y,[C]:`${t}.${a}.${C}`}),{})}var ze=i(96594);function Le({expiresAt:s,clockDrift:e,tolerance:t=5e3}){return Date.now()+e+t>s}var Ge=i(23192);const Ve="amplify-signin-with-hostedUI",z="CognitoIdentityServiceProvider",Q=(s,e)=>ne(B)(s,e),We=new class Qe{constructor(e){this.keyValueStorage=e}clearOAuthInflightData(){var e=this;return(0,d.Z)(function*(){(0,r.FG)(e.cognitoConfig);const t=Q(z,e.cognitoConfig.userPoolClientId);yield Promise.all([e.keyValueStorage.removeItem(t.inflightOAuth),e.keyValueStorage.removeItem(t.oauthPKCE),e.keyValueStorage.removeItem(t.oauthState)])})()}clearOAuthData(){var e=this;return(0,d.Z)(function*(){(0,r.FG)(e.cognitoConfig);const t=Q(z,e.cognitoConfig.userPoolClientId);return yield e.clearOAuthInflightData(),yield e.keyValueStorage.removeItem(Ve),e.keyValueStorage.removeItem(t.oauthSignIn)})()}loadOAuthState(){(0,r.FG)(this.cognitoConfig);const e=Q(z,this.cognitoConfig.userPoolClientId);return this.keyValueStorage.getItem(e.oauthState)}storeOAuthState(e){(0,r.FG)(this.cognitoConfig);const t=Q(z,this.cognitoConfig.userPoolClientId);return this.keyValueStorage.setItem(t.oauthState,e)}loadPKCE(){(0,r.FG)(this.cognitoConfig);const e=Q(z,this.cognitoConfig.userPoolClientId);return this.keyValueStorage.getItem(e.oauthPKCE)}storePKCE(e){(0,r.FG)(this.cognitoConfig);const t=Q(z,this.cognitoConfig.userPoolClientId);return this.keyValueStorage.setItem(t.oauthPKCE,e)}setAuthConfig(e){this.cognitoConfig=e}loadOAuthInFlight(){var e=this;return(0,d.Z)(function*(){(0,r.FG)(e.cognitoConfig);const t=Q(z,e.cognitoConfig.userPoolClientId);return"true"===(yield e.keyValueStorage.getItem(t.inflightOAuth))})()}storeOAuthInFlight(e){var t=this;return(0,d.Z)(function*(){(0,r.FG)(t.cognitoConfig);const a=Q(z,t.cognitoConfig.userPoolClientId);yield t.keyValueStorage.setItem(a.inflightOAuth,`${e}`)})()}loadOAuthSignIn(){var e=this;return(0,d.Z)(function*(){(0,r.FG)(e.cognitoConfig);const t=Q(z,e.cognitoConfig.userPoolClientId),a=yield e.keyValueStorage.getItem(Ve),[y,C]=(yield e.keyValueStorage.getItem(t.oauthSignIn))?.split(",")??[];return{isOAuthSignIn:"true"===y||"true"===a,preferPrivateSession:"true"===C}})()}storeOAuthSignIn(e,t=!1){var a=this;return(0,d.Z)(function*(){(0,r.FG)(a.cognitoConfig);const y=Q(z,a.cognitoConfig.userPoolClientId);yield a.keyValueStorage.setItem(y.oauthSignIn,`${e},${t}`)})()}}(o.defaultStorage),Ne=[];class Je{constructor(){var e=this;this.waitForInflightOAuth=(0,ze.j)()?(0,d.Z)(function*(){if(yield We.loadOAuthInFlight())return e.inflightPromise||(e.inflightPromise=new Promise((t,a)=>{Ne.push(t)})),e.inflightPromise}):(0,d.Z)(function*(){})}setAuthConfig(e){We.setAuthConfig(e.Cognito),this.authConfig=e}setTokenRefresher(e){this.tokenRefresher=e}setAuthTokenStore(e){this.tokenStore=e}getTokenStore(){if(!this.tokenStore)throw new n({name:"EmptyTokenStoreException",message:"TokenStore not set"});return this.tokenStore}getTokenRefresher(){if(!this.tokenRefresher)throw new n({name:"EmptyTokenRefresherException",message:"TokenRefresher not set"});return this.tokenRefresher}getTokens(e){var t=this;return(0,d.Z)(function*(){let a;try{(0,r.FG)(t.authConfig?.Cognito)}catch{return null}yield t.waitForInflightOAuth(),t.inflightPromise=void 0,a=yield t.getTokenStore().loadTokens();const y=yield t.getTokenStore().getLastAuthUser();if(null===a)return null;const C=!!a?.idToken&&Le({expiresAt:1e3*(a.idToken?.payload?.exp??0),clockDrift:a.clockDrift??0}),G=Le({expiresAt:1e3*(a.accessToken?.payload?.exp??0),clockDrift:a.clockDrift??0});return(e?.forceRefresh||C||G)&&(a=yield t.refreshTokens({tokens:a,username:y}),null===a)?null:{accessToken:a?.accessToken,idToken:a?.idToken,signInDetails:a?.signInDetails}})()}refreshTokens({tokens:e,username:t}){var a=this;return(0,d.Z)(function*(){try{const{signInDetails:y}=e,C=yield a.getTokenRefresher()({tokens:e,authConfig:a.authConfig,username:t});return C.signInDetails=y,yield a.setTokens({tokens:C}),o.Hub.dispatch("auth",{event:"tokenRefresh"},"Auth",Ge.SQ),C}catch(y){return a.handleErrors(y)}})()}handleErrors(e){if(fe(e),e.name!==re.Z.NetworkError&&this.clearTokens(),o.Hub.dispatch("auth",{event:"tokenRefresh_failure",data:{error:e}},"Auth",Ge.SQ),e.name.startsWith("NotAuthorizedException"))return null;throw e}setTokens({tokens:e}){var t=this;return(0,d.Z)(function*(){return t.getTokenStore().storeTokens(e)})()}clearTokens(){var e=this;return(0,d.Z)(function*(){return e.getTokenStore().clearTokens()})()}getDeviceMetadata(e){return this.getTokenStore().getDeviceMetadata(e)}clearDeviceMetadata(e){return this.getTokenStore().clearDeviceMetadata(e)}setOAuthMetadata(e){return this.getTokenStore().setOAuthMetadata(e)}getOAuthMetadata(){return this.getTokenStore().getOAuthMetadata()}}const ue=new class Ye{constructor(){this.authTokenStore=new Me,this.authTokenStore.setKeyValueStorage(o.defaultStorage),this.tokenOrchestrator=new Je,this.tokenOrchestrator.setAuthTokenStore(this.authTokenStore),this.tokenOrchestrator.setTokenRefresher(Re)}getTokens({forceRefresh:e}={forceRefresh:!1}){return this.tokenOrchestrator.getTokens({forceRefresh:e})}setKeyValueStorage(e){this.authTokenStore.setKeyValueStorage(e)}setAuthConfig(e){this.authTokenStore.setAuthConfig(e),this.tokenOrchestrator.setAuthConfig(e)}},qe={identityId:"identityId"},et=new o.ConsoleLogger("DefaultIdentityIdStore");function Fe(s){const e=(0,r.xp)(s).payload.iss,t={};if(!e)throw new n({name:"InvalidIdTokenException",message:"Invalid Idtoken."});return t[e.replace(/(^\w+:|^)\/\//,"")]=s,t}const ot=new o.ConsoleLogger("CognitoIdentityIdProvider");function Ue(){return(Ue=(0,d.Z)(function*({tokens:s,authConfig:e,identityIdStore:t}){t.setAuthConfig({Cognito:e});let a=yield t.loadIdentityId();if(s){if(a&&"primary"===a.type)return a.id;{const y=s.idToken?Fe(s.idToken.toString()):{},C=yield je(y,e);a&&a.id===C&&ot.debug(`The guest identity ${a.id} has become the primary identity.`),a={id:C,type:"primary"}}}else{if(a&&"guest"===a.type)return a.id;a={id:yield je({},e),type:"guest"}}return t.storeIdentityId(a),a.id})).apply(this,arguments)}function je(s,e){return xe.apply(this,arguments)}function xe(){return(xe=(0,d.Z)(function*(s,e){const t=e?.identityPoolId,a=m(t),y=(yield(0,o.getId)({region:a},{IdentityPoolId:t,Logins:s})).IdentityId;if(!y)throw new n({name:"GetIdResponseException",message:"Received undefined response from getId operation",recoverySuggestion:"Make sure to pass a valid identityPoolId in the configuration."});return y})).apply(this,arguments)}const Se=new o.ConsoleLogger("CognitoCredentialsProvider"),st=new class rt{constructor(e){this._nextCredentialsRefresh=0,this._identityIdStore=e}clearCredentialsAndIdentityId(){var e=this;return(0,d.Z)(function*(){Se.debug("Clearing out credentials and identityId"),e._credentialsAndIdentityId=void 0,yield e._identityIdStore.clearIdentityId()})()}clearCredentials(){var e=this;return(0,d.Z)(function*(){Se.debug("Clearing out in-memory credentials"),e._credentialsAndIdentityId=void 0})()}getCredentialsAndIdentityId(e){var t=this;return(0,d.Z)(function*(){const a=e.authenticated,{tokens:y}=e,{authConfig:C}=e;try{(0,r.YE)(C?.Cognito)}catch{return}if(!a&&!C.Cognito.allowGuestAccess)return;const{forceRefresh:G}=e,N=t.hasTokenChanged(y),$=yield function it(s){return Ue.apply(this,arguments)}({tokens:y,authConfig:C.Cognito,identityIdStore:t._identityIdStore});return(G||N)&&t.clearCredentials(),a?(function w(s){if(!s||!s.idToken)throw new n({name:S,message:"User needs to be authenticated to call this API.",recoverySuggestion:"Sign in before calling this API again."})}(y),t.credsForOIDCTokens(C.Cognito,y,$)):t.getGuestCredentials($,C.Cognito)})()}getGuestCredentials(e,t){var a=this;return(0,d.Z)(function*(){if(a._credentialsAndIdentityId&&!a.isPastTTL()&&!1===a._credentialsAndIdentityId.isAuthenticatedCreds)return Se.info("returning stored credentials as they neither past TTL nor expired."),a._credentialsAndIdentityId;a.clearCredentials();const y=m(t.identityPoolId),C=yield(0,o.getCredentialsForIdentity)({region:y},{IdentityId:e});if(C.Credentials&&C.Credentials.AccessKeyId&&C.Credentials.SecretKey){a._nextCredentialsRefresh=(new Date).getTime()+3e6;const G={credentials:{accessKeyId:C.Credentials.AccessKeyId,secretAccessKey:C.Credentials.SecretKey,sessionToken:C.Credentials.SessionToken,expiration:C.Credentials.Expiration},identityId:e},N=C.IdentityId;return N&&(G.identityId=N,a._identityIdStore.storeIdentityId({id:N,type:"guest"})),a._credentialsAndIdentityId={...G,isAuthenticatedCreds:!1},G}throw new n({name:"CredentialsNotFoundException",message:"Cognito did not respond with either Credentials, AccessKeyId or SecretKey."})})()}credsForOIDCTokens(e,t,a){var y=this;return(0,d.Z)(function*(){if(y._credentialsAndIdentityId&&!y.isPastTTL()&&!0===y._credentialsAndIdentityId.isAuthenticatedCreds)return Se.debug("returning stored credentials as they neither past TTL nor expired."),y._credentialsAndIdentityId;y.clearCredentials();const C=t.idToken?Fe(t.idToken.toString()):{},G=m(e.identityPoolId),N=yield(0,o.getCredentialsForIdentity)({region:G},{IdentityId:a,Logins:C});if(N.Credentials&&N.Credentials.AccessKeyId&&N.Credentials.SecretKey){const $={credentials:{accessKeyId:N.Credentials.AccessKeyId,secretAccessKey:N.Credentials.SecretKey,sessionToken:N.Credentials.SessionToken,expiration:N.Credentials.Expiration},identityId:a};y._credentialsAndIdentityId={...$,isAuthenticatedCreds:!0,associatedIdToken:t.idToken?.toString()},y._nextCredentialsRefresh=(new Date).getTime()+3e6;const q=N.IdentityId;return q&&($.identityId=q,y._identityIdStore.storeIdentityId({id:q,type:"primary"})),$}throw new n({name:"CredentialsException",message:"Cognito did not respond with either Credentials, AccessKeyId or SecretKey."})})()}isPastTTL(){return void 0===this._nextCredentialsRefresh||this._nextCredentialsRefresh<=Date.now()}hasTokenChanged(e){return!!e&&!!this._credentialsAndIdentityId?.associatedIdToken&&e.idToken?.toString()!==this._credentialsAndIdentityId.associatedIdToken}}(new class tt{setAuthConfig(e){(0,r.YE)(e.Cognito),this.authConfig=e,this._authKeys=((s,e)=>ne(qe)(`com.amplify.${s}`,e))("Cognito",e.Cognito.identityPoolId)}constructor(e){this._authKeys={},this._hasGuestIdentityId=!1,this.keyValueStorage=e}loadIdentityId(){var e=this;return(0,d.Z)(function*(){(0,r.YE)(e.authConfig?.Cognito);try{if(e._primaryIdentityId)return{id:e._primaryIdentityId,type:"primary"};{const t=yield e.keyValueStorage.getItem(e._authKeys.identityId);return t?(e._hasGuestIdentityId=!0,{id:t,type:"guest"}):null}}catch(t){return et.log("Error getting stored IdentityId.",t),null}})()}storeIdentityId(e){var t=this;return(0,d.Z)(function*(){(0,r.YE)(t.authConfig?.Cognito),"guest"===e.type?(t.keyValueStorage.setItem(t._authKeys.identityId,e.id),t._primaryIdentityId=void 0,t._hasGuestIdentityId=!0):(t._primaryIdentityId=e.id,t._hasGuestIdentityId&&(t.keyValueStorage.removeItem(t._authKeys.identityId),t._hasGuestIdentityId=!1))})()}clearIdentityId(){var e=this;return(0,d.Z)(function*(){e._primaryIdentityId=void 0,yield e.keyValueStorage.removeItem(e._authKeys.identityId)})()}}(o.defaultStorage)),at={configure(s,e){const t=(0,l.h)(s);if(t.Auth){if(!e?.Auth)return o.Amplify.libraryOptions.Auth?e?(void 0!==e.ssr&&ue.setKeyValueStorage(e.ssr?new o.CookieStorage({sameSite:"lax"}):o.defaultStorage),void o.Amplify.configure(t,{Auth:o.Amplify.libraryOptions.Auth,...e})):void o.Amplify.configure(t):(ue.setAuthConfig(t.Auth),ue.setKeyValueStorage(e?.ssr?new o.CookieStorage({sameSite:"lax"}):o.defaultStorage),void o.Amplify.configure(t,{...e,Auth:{tokenProvider:ue,credentialsProvider:st}}));o.Amplify.configure(t,e)}else o.Amplify.configure(t,e)},getConfig:()=>o.Amplify.getConfig()}},15861:(b,A,i)=>{function o(d,r,g,p,n,h,m){try{var S=d[h](m),u=S.value}catch(I){return void g(I)}S.done?r(u):Promise.resolve(u).then(p,n)}function l(d){return function(){var r=this,g=arguments;return new Promise(function(p,n){var h=d.apply(r,g);function m(u){o(h,p,n,m,S,"next",u)}function S(u){o(h,p,n,m,S,"throw",u)}m(void 0)})}}i.d(A,{Z:()=>l})}}]);