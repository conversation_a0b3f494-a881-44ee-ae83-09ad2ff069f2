(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9181],{59181:(R,g,o)=>{o.r(g),o.d(g,{MboSecurityTravelerRegisterPageModule:()=>w});var c=o(17007),m=o(78007),l=o(30263),e=o(99877),p=o(39904),y=o(95437),u=o(24495),s=o(57544);class v extends s.FormArrayGroup{constructor(t=new Date){const r=new s.FormArrayControl(null,[u.C1]),i=new s.FormArrayControl(t,[u.C1]),a=new s.FormArrayControl(t,[u.C1]);super({controls:{country:r,entryDate:i,departureDate:a}}),this.limitDate=t,this.country=r,this.entryDate=i,this.departureDate=a}}class b extends s.FormGroup{constructor(){const t=new s.FormControl,r=new s.FormControl;super({controls:{name:t,phone:r}}),this.name=t,this.phone=r}}class f{constructor(){this.firstCountry=new v,this.countries=new s.FormArray({groups:[this.firstCountry]}),this.contact=new b}requestLastCountry(){return this.countries.groups[this.countries.groups.length-1]}itIsLastCountry(t){return t===this.requestLastCountry()}canRemoveCountry(t){return this.firstCountry!==t&&this.itIsLastCountry(t)}addCountry(t){this.countries.push(new v(new Date(t.getTime())))}removeCountry(t){this.firstCountry!==t&&this.countries.remove(t)}requestLimitDate(){const t=this.requestLastCountry();return{valid:t.controls.departureDate.valid,value:t.controls.departureDate.value}}}var h=o(48774),C=o(64181),x=o(42865),D=o(45542),S=o(66613);function _(n,t){if(1&n){const r=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",19),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(r);const a=e.\u0275\u0275nextContext().$implicit,E=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(E.removeCountry(a))}),e.\u0275\u0275text(1," Eliminar "),e.\u0275\u0275elementEnd()}}function T(n,t){if(1&n&&(e.\u0275\u0275elementStart(0,"div",13)(1,"div",14)(2,"span"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,_,2,0,"button",15),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(5,"bocc-input-box",16),e.\u0275\u0275elementStart(6,"bocc-date-field",17),e.\u0275\u0275text(7," Fecha de entrada "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"bocc-date-field",18),e.\u0275\u0275text(9," Fecha de salida "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(10,"div",10),e.\u0275\u0275elementEnd()),2&n){const r=t.$implicit,i=t.index,a=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" DESTINO N\xdaMERO ",i+1," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.travelerControls.canRemoveCountry(r)),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",r.country),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",r.entryDate)("minDate",r.limitDate),e.\u0275\u0275advance(2),e.\u0275\u0275property("formControl",r.departureDate)("minDate",r.entryDate.state)("disabled",r.entryDate.invalid)}}const{HOME:d}=p.Z6.CUSTOMER.SECURITY.TRAVELER;let M=(()=>{class n{constructor(r,i,a){this.changeDetector=r,this.mboProvider=i,this.confirmationService=a,this.minDate=new Date,this.travelerControls=new f,this.backAction={id:"btn_traveler-register_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(d)}},this.cancelAction={id:"btn_traveler-register_cancel",label:"Cancelar",click:()=>{this.onCancel()}}}ngAfterViewInit(){this.changeDetector.detectChanges()}addCountry(){const{valid:r,value:i}=this.travelerControls.requestLimitDate();r&&(this.travelerControls.addCountry(i),this.changeDetector.detectChanges())}removeCountry(r){this.travelerControls.removeCountry(r)}onSubmit(){console.log(this.travelerControls)}onCancel(){this.confirmationService.execute({title:"Cancelar registro de viaje",message:"\xbfRealmente desea cancelar el registro de un nuevo viaje?",accept:{label:"Cancelar registro",click:()=>{this.mboProvider.navigation.back(d)}},decline:{label:"Continuar"}})}}return n.\u0275fac=function(r){return new(r||n)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(l.$e))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-security-traveler-register-page"]],decls:18,vars:4,consts:[[1,"mbo-security-traveler-register-page__content"],[1,"mbo-security-traveler-register-page__header"],["title","Nuevo viaje",3,"leftAction","rightAction"],[1,"mbo-security-traveler-register-page__body"],["icon","bell",3,"visible"],[1,"mbo-security-traveler-register-page__title"],[1,"mbo-security-traveler-register-page__countries"],["class","mbo-security-traveler-register-page__country",4,"ngFor","ngForOf"],[1,"mbo-security-traveler-register-page__action"],["bocc-button","flat","prefixIcon","plus",3,"click"],[1,"bocc-divider"],[1,"mbo-security-traveler-register-page__footer"],["id","btn_security-traveler-register_submit","bocc-button","raised",3,"click"],[1,"mbo-security-traveler-register-page__country"],[1,"mbo-security-traveler-register-page__country__title"],["bocc-button","flat","prefixIcon","remove",3,"click",4,"ngIf"],["label","Pa\xeds de destino",3,"formControl"],[3,"formControl","minDate"],[3,"formControl","minDate","disabled"],["bocc-button","flat","prefixIcon","remove",3,"click"]],template:function(r,i){1&r&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"bocc-alert",4),e.\u0275\u0275text(5," Inf\xf3rmanos sobre tu viaje y evita bloqueos en tus productos cuando los uses en el exterior. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",5),e.\u0275\u0275text(7," INFORMACI\xd3N DE TU VIAJE "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",6),e.\u0275\u0275template(9,T,11,8,"div",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8)(11,"button",9),e.\u0275\u0275listener("click",function(){return i.addCountry()}),e.\u0275\u0275text(12," Agregar otro pa\xeds "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(13,"div",10),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"div",11)(15,"button",12),e.\u0275\u0275listener("click",function(){return i.onSubmit()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Continuar"),e.\u0275\u0275elementEnd()()()),2&r&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",i.backAction)("rightAction",i.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",i.travelerControls.countries.groups))},dependencies:[c.NgForOf,c.NgIf,h.J,C.D,x.Y,D.P,S.B],styles:["/*!\n * MBO SecurityTravelerRegister Page\n * v2.1.1\n * Author: MB Frontend Developers\n * Created: 03/Oct/2023\n * Updated: 08/Jul/2024\n*/mbo-security-traveler-register-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;row-gap:var(--sizing-x4);flex-direction:column;justify-content:space-between}mbo-security-traveler-register-page .mbo-security-traveler-register-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-security-traveler-register-page .mbo-security-traveler-register-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-security-traveler-register-page .mbo-security-traveler-register-page__title{font-size:var(--smalltext-size);letter-spacing:var(--smalltext-letter-spacing);line-height:var(--smalltext-line-height);font-weight:var(--font-weight-medium);text-align:center}mbo-security-traveler-register-page .mbo-security-traveler-register-page__countries{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-security-traveler-register-page .mbo-security-traveler-register-page__country{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-security-traveler-register-page .mbo-security-traveler-register-page__country__title{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center}mbo-security-traveler-register-page .mbo-security-traveler-register-page__country__title>span{padding-left:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);font-weight:var(--font-weight-semibold)}mbo-security-traveler-register-page .mbo-security-traveler-register-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-safe-footer-x8);box-sizing:border-box}\n"],encapsulation:2}),n})(),w=(()=>{class n{}return n.\u0275fac=function(r){return new(r||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[c.CommonModule,m.RouterModule.forChild([{path:"",component:M}]),l.Jx,l.DT,l.YC,l.P8,l.B4]}),n})()}}]);