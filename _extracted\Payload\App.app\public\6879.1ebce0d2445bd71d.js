(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6879],{46879:(e,t,n)=>{n.r(t),n.d(t,{MboPaymentBillerModule:()=>P});var a=n(17007),d=n(78007),o=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(6962).then(n.bind(n,86962)).then(l=>l.MboPaymentBillerSourcePageModule)},{path:"amount",loadChildren:()=>n.e(3168).then(n.bind(n,63168)).then(l=>l.MboPaymentBillerAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(1992).then(n.bind(n,11992)).then(l=>l.MboPaymentBillerConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(6292).then(n.bind(n,6292)).then(l=>l.MboPaymentBillerResultPageModule)}];let P=(()=>{class l{}return l.\u0275fac=function(u){return new(u||l)},l.\u0275mod=o.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=o.\u0275\u0275defineInjector({imports:[a.CommonModule,d.RouterModule.forChild(M)]}),l})()}}]);