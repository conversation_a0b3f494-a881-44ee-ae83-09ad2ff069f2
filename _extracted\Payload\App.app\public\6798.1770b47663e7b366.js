(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6798],{77493:(T,y,o)=>{o.d(y,{P:()=>v,G:()=>V});var m=o(15861),u=o(87956),l=o(53113),a=o(98699),p=o(38074),L=o(29306),C=o(87903),P=o(66067);class S{constructor(O,t,e,s){this.destination=O,this.source=t,this.amount=e,this.manual=s}}function f(n){return new S(n.destination,n.source,n.amount,n.manual)}var I=o(71776),E=o(39904),M=o(42168),h=o(84757),c=o(99877);let A=(()=>{class n{constructor(t,e){this.http=t,e.subscribes(E.PU,()=>{this.loans=void 0})}request(){return this.loans?Promise.resolve(this.loans):(0,M.firstValueFrom)(this.http.get(E.bV.PAYMENTS.DEBTS.CATALOG,{params:{exclude:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,h.map)(({content:t})=>t.map(e=>function b(n){return new P.T2(n.id,n.acctType,n.acctTypeName,n.loanName,n.acctId,new L.Br(n.bankId,n.bankName),n.isAval,n.dynamo||!1,n.isOwner,n.isOwner?null:n.owner,n.isOwner?null:new l.dp((0,C.nX)(n.ownerIdType),n.ownerId))}(e))),(0,h.tap)(t=>{this.loans=t})))}send(t){return(0,M.firstValueFrom)(this.http.post(E.bV.PAYMENTS.LOAN,function i(n){return{acctIdFrom:n.source.id,acctNickNameFrom:n.source.nickname,bankIdFrom:n.source.bank.id,acctIdTo:n.destination.id,acctNameTo:n.destination.nickname,bankIdTo:n.destination.bank.id,bankNameTo:n.destination.bank.name,amt:Math.ceil(n.amount),curCode:"COP",paymentDesc:""}}(t)).pipe((0,h.map)(e=>(0,C.l1)(e,"SUCCESS")))).catch(e=>(0,C.rU)(e))}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(I.HttpClient),c.\u0275\u0275inject(u.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var d=o(20691);let D=(()=>{class n extends d.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,manual:!1}),this.eventBusService=t,this.eventBusService.subscribes(E.PU,()=>{this.reset()})}setDestination(t,e=!1){this.reduce(s=>({...s,destination:t,fromCustomer:e}))}getDestination(){return this.select(({destination:t})=>t)}setProduct(t){this.reduce(e=>({...e,product:t}))}getProduct(){return this.select(({product:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(e=>({...e,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount({amount:t,manual:e}){this.reduce(s=>({...s,manual:e,amount:t}))}selectForAmount(){return this.select(({amount:t,confirmation:e,destination:s,source:g})=>({amount:t,confirmation:e,destination:s,source:g}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(u.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),v=(()=>{class n{constructor(t,e,s,g){this.productService=t,this.repository=e,this.store=s,this.eventBusService=g}setDestination(t){var e=this;return(0,m.Z)(function*(){try{return(0,p.p)(t)&&(yield e.productService.requestInformation(t)),a.Either.success(e.store.setDestination(t))}catch{return a.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return a.Either.success(this.store.setSource(t))}catch({message:e}){return a.Either.failure({message:e})}}setAmount(t,e=!1){try{return a.Either.success(this.store.setAmount({amount:t,manual:e}))}catch({message:s}){return a.Either.failure({message:s})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getProduct();return this.store.reset(),a.Either.success({fromCustomer:t,product:e})}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const e=f(t.store.currentState),s=yield t.execute(e);return t.eventBusService.emit(s.channel),a.Either.success({loan:e,status:s})})()}execute(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(l.LN.error(e))}}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(A),c.\u0275\u0275inject(D),c.\u0275\u0275inject(u.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var r=o(89148);const{DlaPayMin:R,DlaPayTotal:F,LoanPayMin:_,LoanPayTotal:B}=r.Av;let V=(()=>{class n{constructor(t,e,s,g){this.products=t,this.productService=e,this.repository=s,this.store=g}destination(){var t=this;return(0,m.Z)(function*(){try{return a.Either.success(yield t.requestLoans())}catch({message:e}){return a.Either.failure({message:e})}})()}source(t){var e=this;return(0,m.Z)(function*(){try{const s=yield e.products.requestAccountsForTransfer(),g=e.store.itIsConfirmation(),N=yield e.requestLoan(t);return a.Either.success({confirmation:g,destination:N,products:s})}catch({message:s}){return a.Either.failure({message:s})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const e=t.store.itIsConfirmation(),s=t.store.getSource(),g=t.store.getDestination(),N=yield t.productService.requestInformation(g),U=t.getMinPayment(N),j=t.getTotalPayment(N);return a.Either.success({confirmation:e,cop:{min:U,total:j},source:s})}catch({message:e}){return a.Either.failure({message:e})}})()}amount(){var t=this;return(0,m.Z)(function*(){try{const e=t.store.getDestination(),s=(0,p.p)(e)?yield t.productService.requestInformation(t.store.getDestination()):void 0,g=s&&t.getTotalPayment(s);return a.Either.success({...t.store.selectForAmount(),total:g})}catch({message:e}){return a.Either.failure({message:e})}})()}confirmation(){try{const t=f(this.store.currentState);return a.Either.success({payment:t})}catch({message:t}){return a.Either.failure({message:t})}}requestLoans(){return this.repository.request().then(t=>t.reduce((e,s)=>{const{others:g,principals:N}=e;return(s.bank.isOccidente?N:g).push(s),e},{others:[],principals:[]}))}requestLoan(t){var e=this;return(0,m.Z)(function*(){let s=e.store.getDestination();if(!s&&t){const g=yield e.products.requestProductForId(t);if(e.store.setProduct(g),g){const{principals:N}=yield e.requestLoans(),U=N.find(({number:j})=>j===g.number);U&&(yield e.productService.requestInformation(U)),s=U||g,e.store.setDestination(s,!0)}}return s})()}getMinPayment(t){return t?.getSection(_)||t?.getSection(R)}getTotalPayment(t){return t?.getSection(B)||t?.getSection(F)}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(u.hM),c.\u0275\u0275inject(u.M5),c.\u0275\u0275inject(A),c.\u0275\u0275inject(D))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},20225:(T,y,o)=>{o.d(y,{w:()=>C});var m=o(30263),u=o(39904),l=o(95437),a=o(77493),p=o(99877);let C=(()=>{class P{constructor(b,i,f){this.modalConfirmation=b,this.mboProvider=i,this.managerLoan=f}execute(b=!0){b?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de cr\xe9dito actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerLoan.reset().when({success:({fromCustomer:b,product:i})=>{b?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:i.id}):this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.PAYMENTS.HOME)}})}}return P.\u0275fac=function(b){return new(b||P)(p.\u0275\u0275inject(m.$e),p.\u0275\u0275inject(l.ZL),p.\u0275\u0275inject(a.P))},P.\u0275prov=p.\u0275\u0275defineInjectable({token:P,factory:P.\u0275fac,providedIn:"root"}),P})()},66798:(T,y,o)=>{o.r(y),o.d(y,{MboPaymentLoanConfirmationPageModule:()=>A});var m=o(17007),u=o(78007),l=o(79798),a=o(30263),p=o(83651),L=o(15861),C=o(39904),P=o(95437),S=o(77493),b=o(20225),i=o(99877),f=o(10464),I=o(48774),E=o(45542),M=o(17941);const h=C.Z6.PAYMENTS.LOAN;let c=(()=>{class d{constructor(v,r,R){this.mboProvider=v,this.requestConfiguration=r,this.cancelProvider=R,this.backAction={id:"btn_payment-loan-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(this.payment.manual?h.AMOUNT:h.SELECT_AMOUNT)}},this.cancelAction={id:"btn_payment-loan-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_payment-loan-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(h.DESTINATION)}}],this.amountActions=[{id:"btn_payment-loan-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(this.payment.manual?h.AMOUNT:h.SELECT_AMOUNT)}}]}ngOnInit(){this.intializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(h.RESULT)}intializatedConfiguration(){var v=this;return(0,L.Z)(function*(){(yield v.requestConfiguration.confirmation()).when({success:({payment:r})=>{v.payment=r}})})()}}return d.\u0275fac=function(v){return new(v||d)(i.\u0275\u0275directiveInject(P.ZL),i.\u0275\u0275directiveInject(S.G),i.\u0275\u0275directiveInject(b.w))},d.\u0275cmp=i.\u0275\u0275defineComponent({type:d,selectors:[["mbo-payment-loan-confirmation-page"]],decls:16,vars:10,consts:[[1,"mbo-payment-loan-confirmation-page__content","mbo-page__scroller"],[1,"mbo-payment-loan-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-payment-loan-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],[1,"mbo-payment-loan-confirmation-page__footer"],["id","btn_payment-loan-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"click"]],template:function(v,r){1&v&&(i.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),i.\u0275\u0275element(3,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),i.\u0275\u0275text(7," \xbfDeseas pagar el cr\xe9dito? "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(8,"div",6),i.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),i.\u0275\u0275elementEnd()()()(),i.\u0275\u0275elementStart(12,"div",10)(13,"button",11),i.\u0275\u0275listener("click",function(){return r.onSubmit()}),i.\u0275\u0275elementStart(14,"span"),i.\u0275\u0275text(15,"Pagar"),i.\u0275\u0275elementEnd()()()()),2&v&&(i.\u0275\u0275advance(3),i.\u0275\u0275property("leftAction",r.backAction)("rightAction",r.cancelAction),i.\u0275\u0275advance(6),i.\u0275\u0275property("title",null==r.payment||null==r.payment.destination?null:r.payment.destination.nickname)("subtitle",null==r.payment||null==r.payment.destination?null:r.payment.destination.publicNumber)("detail",null==r.payment||null==r.payment.destination?null:r.payment.destination.name)("actions",r.destinationActions),i.\u0275\u0275advance(1),i.\u0275\u0275property("amount",null==r.payment?null:r.payment.amount)("actions",r.amountActions),i.\u0275\u0275advance(1),i.\u0275\u0275property("title",null==r.payment||null==r.payment.source?null:r.payment.source.nickname)("subtitle",null==r.payment||null==r.payment.source?null:r.payment.source.number))},dependencies:[f.K,I.J,E.P,M.D],styles:["/*!\n * MBO PaymentLoanConfirmation Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 27/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-payment-loan-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-loan-confirmation-page .mbo-payment-loan-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-loan-confirmation-page .mbo-payment-loan-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-loan-confirmation-page .mbo-payment-loan-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-payment-loan-confirmation-page .mbo-payment-loan-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),d})(),A=(()=>{class d{}return d.\u0275fac=function(v){return new(v||d)},d.\u0275mod=i.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=i.\u0275\u0275defineInjector({imports:[m.CommonModule,u.RouterModule.forChild([{path:"",component:c}]),l.KI,a.Jx,a.P8,a.DM,a.B4,a.Dj,p.P6]}),d})()},63674:(T,y,o)=>{o.d(y,{Eg:()=>P,Lo:()=>a,Wl:()=>p,ZC:()=>L,_f:()=>u,br:()=>C,tl:()=>l});var m=o(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},l=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),a={color:"success",key:"paid",label:"Pagada"},p={color:"alert",key:"pending",label:"Por pagar"},L={color:"danger",key:"expired",label:"Vencida"},C={color:"info",key:"recurring",label:"Pago recurrente"},P={color:"info",key:"programmed",label:"Programado"}},66067:(T,y,o)=>{o.d(y,{S6:()=>S,T2:()=>C,UQ:()=>b,mZ:()=>P});var m=o(39904),u=o(6472),a=o(63674),p=o(31707);class C{constructor(f,I,E,M,h,c,A,d,D,v,r){this.id=f,this.type=I,this.name=E,this.nickname=M,this.number=h,this.bank=c,this.isAval=A,this.isProtected=d,this.isOwner=D,this.ownerName=v,this.ownerDocument=r,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,u.initials)(M),this.shortNumber=h.substring(h.length-4),this.descriptionNumber=`${E} ${h}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(f){this.informationValue||(this.informationValue=f)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(f){return this.currenciesValue.includes(f)}}class P{constructor(f,I){this.id=f,this.type=I}}class S{constructor(f,I,E,M,h,c,A,d,D,v,r,R){this.uuid=f,this.number=I,this.nie=E,this.nickname=M,this.companyId=h,this.companyName=c,this.amount=A,this.registerDate=d,this.expirationDate=D,this.paid=v,this.statusCode=r,this.references=R,this.recurring=R.length>0,this.status=function L(i){switch(i){case p.U.EXPIRED:return a.ZC;case p.U.PENDING:return a.Wl;case p.U.PROGRAMMED:return a.Eg;case p.U.RECURRING:return a.br;default:return a.Lo}}(r)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class b{constructor(f,I,E,M,h,c,A,d){this.uuid=f,this.number=I,this.nickname=E,this.companyId=M,this.companyName=h,this.city=c,this.amount=A,this.isBiller=d}}},38074:(T,y,o)=>{o.d(y,{p:()=>u});var m=o(29306);function u(l){return l instanceof m.xs||l.isRequiredInformation}},31707:(T,y,o)=>{o.d(y,{U:()=>m,f:()=>u});var m=(()=>{return(l=m||(m={})).RECURRING="1",l.EXPIRED="2",l.PENDING="3",l.PROGRAMMED="4",m;var l})(),u=(()=>{return(l=u||(u={})).BILLER="Servicio",l.NON_BILLER="Servicio",l.PSE="Servicio",l.TAX="Impuesto",l.LOAN="Obligaci\xf3n financiera",l.CREDIT_CARD="Obligaci\xf3n financiera",u;var l})()}}]);