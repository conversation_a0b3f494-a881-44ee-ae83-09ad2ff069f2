(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4386],{9736:(F,b,r)=>{r.d(b,{Hu:()=>T,wG:()=>B,rQ:()=>D,_G:()=>L});var d=r(15861),f=r(87956),y=r(53113),c=r(98699);class v{constructor(s,e,n,i,l){this.id=s,this.nit=e,this.name=n,this.city=i,this.exampleUrl=l}}class N{constructor(s,e,n,i){this.number=s,this.amount=e,this.expirationDate=n,this.companyId=i}get expirationFormat(){return this.expirationDate.dateFormat}}class C{constructor(s,e,n){this.agreement=s,this.invoice=e,this.source=n}}function m(t){return new C(t.agreement,t.invoice,t.source)}function S(t){return new v(t.orgIdNum,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var I=r(71776),p=r(39904),R=r(87903),E=r(42168),M=r(84757),o=r(99877);let P=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,E.firstValueFrom)(this.http.get(p.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,M.map)(({content:n})=>n.map(i=>S(i)))))}requestCompanyId(e){return(0,E.firstValueFrom)(this.http.get(p.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,M.map)(({content:n})=>n.length?S(n[0]):null)))}requestInvoice(e,n){return(0,E.firstValueFrom)(this.http.get(p.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,M.map)(i=>function a(t){return new N(t.nie||t.invoiceNum,+t.amt,new y.ou(t.expDt),t.orgIdNum)}(i))))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),u=(()=>{class t{constructor(e){this.http=e}send(e){return(0,E.firstValueFrom)(this.http.post(p.bV.PAYMENTS.INVOICE_MANUAL,function A(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,M.map)(([n])=>(0,R.l1)(n,"SUCCESS")))).catch(n=>(0,R.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var h=r(20691);let g=(()=>{class t extends h.Store{constructor(e){super({confirmation:!1}),e.subscribes(p.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(f.Yd))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),T=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.eventBusService=i}setAgreement(e){try{return c.Either.success(this.store.setAgreement(e))}catch({message:n}){return c.Either.failure({message:n})}}setInvoice(e){try{return c.Either.success(this.store.setInvoice(e))}catch({message:n}){return c.Either.failure({message:n})}}setSource(e){try{return c.Either.success(this.store.setSource(e))}catch({message:n}){return c.Either.failure({message:n})}}reset(){try{return c.Either.success(this.store.reset())}catch({message:e}){return c.Either.failure({message:e})}}send(){var e=this;return(0,d.Z)(function*(){const n=m(e.store.currentState),i=yield e.execute(n);return e.eventBusService.emit(i.channel),c.Either.success({invoice:n,status:i})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(y.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(u),o.\u0275\u0275inject(g),o.\u0275\u0275inject(f.Yd))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),B=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,d.Z)(function*(){try{return c.Either.success(yield n.repository.requestAll(e))}catch({message:i,status:l}){return 400===l?c.Either.success([]):c.Either.failure({message:i})}})()}invoice(e,{id:n}){var i=this;return(0,d.Z)(function*(){try{return c.Either.success(yield i.repository.requestInvoice(e,n))}catch({message:l,status:j}){return c.Either.failure({value:400===j,message:l})}})()}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(P))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),D=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return c.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return c.Either.failure({message:e})}}source(){var e=this;return(0,d.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),i=e.store.getAgreement(),l=e.store.getInvoice();return c.Either.success({agreement:i,invoice:l,products:n})}catch({message:n}){return c.Either.failure({message:n})}})()}confirmation(){try{const e=m(this.store.currentState);return c.Either.success({payment:e})}catch({message:e}){return c.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(f.hM),o.\u0275\u0275inject(g))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const V=/^415(\d+)8020(\d+)$/;let U=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,d.Z)(function*(){const i=e.replace(/\D/g,"").match(V);if(!i)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const l=yield n.requestNuraCodes(),j=i[1],x=l.find(({ean_code:z})=>z===j);if(!x)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:i[2].slice(0,+x.length),companyId:x.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,E.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,M.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(I.HttpClient))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),L=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.barcodeService=i}execute(e){var n=this;return(0,d.Z)(function*(){try{const{reference:i,companyId:l}=yield n.barcodeService.execute(e),j=yield n.repository.requestCompanyId(l),x=yield n.repository.requestInvoice(i,l);return n.store.setAgreement(j),n.store.setInvoice(x),c.Either.success()}catch{return c.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(o.\u0275\u0275inject(P),o.\u0275\u0275inject(g),o.\u0275\u0275inject(U))},t.\u0275prov=o.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},74242:(F,b,r)=>{r.d(b,{s:()=>C});var d=r(39904),f=r(95437),y=r(30263),c=r(9736),v=r(99877);let C=(()=>{class m{constructor(a,A,I){this.modalConfirmation=a,this.mboProvider=A,this.managerInvoice=I}execute(a=!0){a?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(d.Z6.PAYMENTS.HOME)}}return m.\u0275fac=function(a){return new(a||m)(v.\u0275\u0275inject(y.$e),v.\u0275\u0275inject(f.ZL),v.\u0275\u0275inject(c.Hu))},m.\u0275prov=v.\u0275\u0275defineInjectable({token:m,factory:m.\u0275fac,providedIn:"root"}),m})()},84386:(F,b,r)=>{r.r(b),r.d(b,{MboPaymentInvoiceManualSelectPageModule:()=>M});var d=r(17007),f=r(78007),y=r(30263),c=r(15861),v=r(39904),N=r(95437),C=r(87956),m=r(9736),S=r(74242),a=r(99877),A=r(48774),I=r(18443);const p=v.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL;let E=(()=>{class o{constructor(u,h,g,T){this.mboProvider=u,this.scannerService=h,this.verifyInvoice=g,this.cancelProvider=T,this.cancelAction={id:"btn_payment-invoice-manual_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}}}onBarcode(){var u=this;return(0,c.Z)(function*(){try{const{code:h}=yield u.scannerService.qrCode({orientation:"landscape"});u.mboProvider.loader.open("Verificando, por favor espere..."),(yield u.verifyInvoice.execute(h)).when({success:()=>{u.mboProvider.navigation.next(p.SOURCE)},failure:({message:g})=>{u.mboProvider.toast.error(g)}},()=>{u.mboProvider.loader.close()})}catch{u.mboProvider.loader.close()}})()}onManual(){this.mboProvider.navigation.next(p.AGREEMENT)}}return o.\u0275fac=function(u){return new(u||o)(a.\u0275\u0275directiveInject(N.ZL),a.\u0275\u0275directiveInject(C.LQ),a.\u0275\u0275directiveInject(m._G),a.\u0275\u0275directiveInject(S.s))},o.\u0275cmp=a.\u0275\u0275defineComponent({type:o,selectors:[["mbo-payment-invoice-manual-select-page"]],decls:10,vars:1,consts:[[1,"mbo-payment-invoice-manual-select-page__header"],["title","Servicio","progress","20%",3,"rightAction"],[1,"mbo-payment-invoice-manual-select-page__content"],[1,"mbo-payment-invoice-manual-select-page__title","subtitle2-medium"],[1,"mbo-payment-invoice-manual-select-page__body"],["id","btn_payment-invoice-manual_barcode","icon","invoice-barcode",3,"click"],["id","btn_payment-invoice-manual_submit","icon","invoice-manual",3,"click"]],template:function(u,h){1&u&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"bocc-header-form",1),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(2,"div",2)(3,"div",3),a.\u0275\u0275text(4," Escoge como quieres pagar el servicio "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(5,"div",4)(6,"bocc-button-toggle",5),a.\u0275\u0275listener("click",function(){return h.onBarcode()}),a.\u0275\u0275text(7," Leer c\xf3digo de barras de la factura "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(8,"bocc-button-toggle",6),a.\u0275\u0275listener("click",function(){return h.onManual()}),a.\u0275\u0275text(9," Ingresar manualmente el servicio "),a.\u0275\u0275elementEnd()()()),2&u&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("rightAction",h.cancelAction))},dependencies:[A.J,I.u],styles:["/*!\n * MBO PaymentInvoiceManualSelect Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 01/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-select-page{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-invoice-manual-select-page .mbo-payment-invoice-manual-select-page__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-invoice-manual-select-page .mbo-payment-invoice-manual-select-page__body{position:relative;width:100%;display:flex;-moz-column-gap:var(--sizing-x8);column-gap:var(--sizing-x8)}mbo-payment-invoice-manual-select-page .mbo-payment-invoice-manual-select-page__body bocc-button-toggle{width:calc(50% - var(--sizing-x4))}mbo-payment-invoice-manual-select-page .mbo-payment-invoice-manual-select-page__body bocc-button-toggle .bocc-button-toggle__content{width:100%}\n"],encapsulation:2}),o})(),M=(()=>{class o{}return o.\u0275fac=function(u){return new(u||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[d.CommonModule,f.RouterModule.forChild([{path:"",component:E}]),y.Jx,y.us]}),o})()}}]);