(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2243],{7479:(U,O,o)=>{o.d(O,{d:()=>V});var b=o(15861),m=o(71776),e=o(39904),u=o(27236),F=o(77279),v=o(87956),y=o(74520),k=o(98017),E=o(42168),z=o(84757),T=o(70658),D=o(53113);class _{constructor(S,a,s,c,d){this.id=S,this.title=a,this.message=s,this.date=c,this.read=d}reading(){return new _(this.id,this.title,this.message,this.date,!0)}}var x=o(99877);const{CustomerNotifications:w}=u.Z;let V=(()=>{class p{constructor(a,s,c,d,g){this.http=a,this.deviceService=s,this.storageService=d,this.customerStore=g,c.subscribes([F.q.Logout],()=>{this.observable$.next([]),this.collection$=void 0,this.notifications$=void 0}),this.observable$=new E.Subject}request(a=!1){var s=this;return(0,b.Z)(function*(){return(!s.notifications$||a)&&(s.notifications$=s.remote()),s.notifications$})()}requestForId(a){return this.notifications$?this.notifications$.then(s=>s.find(c=>c.id===a)):Promise.resolve(void 0)}read(a){var s=this;return(0,b.Z)(function*(){return s.refreshNotification(a,{read:!0})})()}remove(a){var s=this;return(0,b.Z)(function*(){return s.refreshNotification(a,{remove:!0})})()}subscribe(a){const s=this.observable$.subscribe(a);return()=>{s.unsubscribe()}}remote(){var a=this;return(0,b.Z)(function*(){const{documentNumber:s,documentType:c}=a.customerStore.getCustomer(),{end:d,start:g}=e.Xy.getValue(),M=yield a.deviceService.getFingerprint(),C=yield a.requestCollection();return(0,E.firstValueFrom)(a.http.post(e.bV.NOTIFICATIONS.MESSAGES,{count:50,custPermId:`${T.N.bankId}${c.code}${s}`,deviceSerial:M,reference:"banco_de_occidente_movil",startDt:(0,k.dateFormatTemplate)(g,"{aa}-{mm}-{dd}T{hh}:{ii}:{ss}"),endDt:(0,k.dateFormatTemplate)(d,"{aa}-{mm}-{dd}T{hh}:{ii}:{ss}")}).pipe((0,z.map)(({messages:f})=>f.reduce((j,P)=>{const H=C.get(P.id);return H?.remove||j.push(function $(p,S){return new _(p.id,p.subject,p.content,new D.ou(p.upDt),S?.read||!1)}(P,H)),j},[])),(0,z.catchError)(f=>{if(f instanceof m.HttpErrorResponse&&"1120"===f.error.status?.statusCode)return(0,E.of)([]);throw f}),(0,z.tap)(f=>a.observable$.next(f))))})()}requestCollection(){return this.collection$||(this.collection$=this.storageService.get(w).then(a=>{const s=new Date((new Date).getTime()-7*k.Miliseconds.Day),c=a?.filter(({expDt:d})=>new Date(d).getTime()>s.getTime())||[];return this.storageService.set(w,c),c.reduce((d,g)=>d.set(g.id,g),new Map)})),this.collection$}requestNotificationForId(a){var s=this;return(0,b.Z)(function*(){const c=yield s.requestCollection();let d=c.get(a.id);return d||(d={expDt:a.date.toISOString(),id:a.id,read:!1,remove:!1},c.set(a.id,d),s.refreshCollection(c)),d})()}refreshCollection(a){const s=Array.from(a.values());this.storageService.set(w,s),this.collection$=Promise.resolve(a)}refreshNotification(a,s){var c=this;return(0,b.Z)(function*(){const d=yield c.requestCollection(),g=yield c.requestNotificationForId(a);if(d.set(a.id,{...g,...s}),c.refreshCollection(d),c.notifications$){const M=s.read?(yield c.notifications$).map(C=>C.id===a.id?a.reading():C):(yield c.notifications$).filter(C=>C.id!==a.id);c.notifications$=Promise.resolve(M),c.observable$.next(M)}})()}}return p.\u0275fac=function(a){return new(a||p)(x.\u0275\u0275inject(m.HttpClient),x.\u0275\u0275inject(v.U8),x.\u0275\u0275inject(v.Yd),x.\u0275\u0275inject(v.V1),x.\u0275\u0275inject(y.f))},p.\u0275prov=x.\u0275\u0275defineInjectable({token:p,factory:p.\u0275fac,providedIn:"root"}),p})()},12243:(U,O,o)=>{o.r(O),o.d(O,{MboApplicationHomePageModule:()=>X});var b=o(78007),m=o(17007),e=o(99877),u=o(30263),F=o(7479),v=o(39904),y=o(95437),k=o(74520);function E(t,l){1&t&&e.\u0275\u0275element(0,"div",9)}let z=(()=>{class t{constructor(n,i,r){this.mboProvider=n,this.notifications=i,this.customerStore=r,this.unsubscriptions=[],this.classSegment="preferential",this.pendings=!1,this.listener=new e.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.notifications.subscribe(n=>{this.pendings=n.reduce((i,{read:r})=>i||!r,!1)})),this.customerStore.subscribe(({session:n})=>{this.classSegment=n?.customer?.segment?.className||"preferential"})}ngOnDestroy(){this.unsubscriptions.forEach(n=>{n()})}goToHome(){this.mboProvider.navigation.back(v.Z6.CUSTOMER.PRODUCTS.HOME)}onCustomer(){this.listener.emit("customer")}onNotifications(){this.listener.emit("notifications")}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(F.d),e.\u0275\u0275directiveInject(k.f))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-application-header"]],outputs:{listener:"listener"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:10,vars:3,consts:[[1,"mbo-application-header__content"],[1,"mbo-application-header__menu"],["icon","menu-burger",3,"click"],[1,"mbo-application-header__title"],[3,"click"],["src","assets/shared/logos/occidente-white.svg","alt","mbo-application-header-bank-img"],[1,"mbo-application-header__notifications"],["src","assets/shared/icons/notifications.svg","alt","mbo-application-header-notifications-img"],["class","mbo-application-header__notifications--active",4,"ngIf"],[1,"mbo-application-header__notifications--active"]],template:function(n,i){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-icon",2),e.\u0275\u0275listener("click",function(){return i.onCustomer()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(3,"div",3)(4,"figure",4),e.\u0275\u0275listener("click",function(){return i.goToHome()}),e.\u0275\u0275element(5,"img",5),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"div",6)(7,"button",4),e.\u0275\u0275listener("click",function(){return i.onNotifications()}),e.\u0275\u0275element(8,"img",7),e.\u0275\u0275template(9,E,1,0,"div",8),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275classMap(i.classSegment),e.\u0275\u0275advance(9),e.\u0275\u0275property("ngIf",i.pendings))},dependencies:[m.CommonModule,m.NgIf,u.Zl],styles:["mbo-application-header{position:relative;width:100%;display:block;color:var(--color-carbon-lighter-50)}mbo-application-header .mbo-application-header__content{--component-color-background: var(--gradient-blue-left-700);position:relative;display:flex;width:100%;justify-content:space-between;align-items:center;padding:calc(var(--sizing-x6) + var(--sizing-safe-top)) var(--sizing-x8) var(--sizing-x6) var(--sizing-x8);box-sizing:border-box;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8);background:var(--component-color-background)}mbo-application-header .mbo-application-header__content.elite{--component-color-background: linear-gradient( 180deg, #00244f 0%, #001227 100% )}mbo-application-header .mbo-application-header__content.elite-plus{--component-color-background: radial-gradient( 132.98% 166.25% at 81.54% 7.45%, #1d1513 0%, #1d1513 100% )}mbo-application-header .mbo-application-header__content.preferential{--component-color-background: linear-gradient( 180deg, #163467 0%, #0a1d3d 100% )}mbo-application-header .mbo-application-header__content.selecto,mbo-application-header .mbo-application-header__content.aval{--component-color-background: linear-gradient( 180deg, #002449 0%, #0055b3 100% )}mbo-application-header .mbo-application-header__content.migration{--component-color-background: linear-gradient( 180deg, #e24c4c 0, #981b1f 100% )}mbo-application-header .mbo-application-header__content.developer{--component-color-background: linear-gradient( 180deg, #53575d 0, #12151c 100% )}mbo-application-header .mbo-application-header__menu{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-application-header .mbo-application-header__title figure{width:var(--sizing-x16);height:var(--sizing-x16);margin:0rem}mbo-application-header .mbo-application-header__title figure img{width:inherit;height:inherit}mbo-application-header .mbo-application-header__notifications{position:relative;width:var(--sizing-x12);height:var(--sizing-x12)}mbo-application-header .mbo-application-header__notifications--active{position:absolute;width:var(--sizing-x2);height:var(--sizing-x2);border-radius:50%;top:3.3rem;right:2.2rem;background:var(--color-semantic-danger-700)}mbo-application-header .mbo-application-header__notifications button{background:none;border:none;border-radius:0rem;padding:0rem;width:var(--sizing-x12);height:var(--sizing-x12)}mbo-application-header .mbo-application-header__notifications button img{width:inherit;height:inherit}\n"],encapsulation:2}),t})();var T=o(15861),D=o(78506),_=o(70658);const $=["*"];let x=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-segment"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:$,decls:2,vars:0,consts:[[1,"mbo-segment__content"]],template:function(n,i){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())},dependencies:[m.CommonModule],styles:[":root{--segment-color-font: var(--color-carbon-lighter-50);--segment-border: 1px solid var(--color-blue-400);--segment-gradient-background: linear-gradient( 1turn, var(--color-blue-900) 0, var(--color-blue-700) 25% );--segment-gradient-background-source: linear-gradient( to bottom, var(--color-blue-400), var(--color-blue-900) );--segment-gradient-background-shape: linear-gradient( -8deg, var(--color-blue-900) 0, var(--color-blue-700) 25% )}mbo-segment{position:relative;display:inline-grid}mbo-segment .mbo-segment__content{position:relative;display:flex;align-items:center;padding:0rem var(--sizing-x2);box-sizing:border-box;border-radius:var(--sizing-x2);height:var(--sizing-x12);color:var(--segment-color-font);background:var(--segment-gradient-background);z-index:var(--z-index-4);overflow:hidden;white-space:nowrap}mbo-segment .mbo-segment__content span{position:relative;display:block;height:var(--sizing-x12);line-height:var(--sizing-x12);padding:0rem var(--sizing-x1)}\n"],encapsulation:2}),t})();function w(t,l){1&t&&e.\u0275\u0275element(0,"bocc-icon",4)}function V(t,l){1&t&&(e.\u0275\u0275elementStart(0,"bocc-badge",5),e.\u0275\u0275text(1,"Nuevo"),e.\u0275\u0275elementEnd()),2&t&&e.\u0275\u0275property("news",!0)}const p=["*"];let S=(()=>{class t{constructor(){this.versionNews=""}get visibleNews(){return this.versionNews===_.N.appVersion}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-sidenav-option"]],inputs:{icon:"icon",versionNews:"versionNews"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:p,decls:6,vars:3,consts:[[1,"mbo-sidenav-option__avatar"],[3,"icon"],["icon","next-page",4,"ngIf"],[3,"news",4,"ngIf"],["icon","next-page"],[3,"news"]],template:function(n,i){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"bocc-icon",1),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"label"),e.\u0275\u0275projection(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,w,1,0,"bocc-icon",2),e.\u0275\u0275template(5,V,2,1,"bocc-badge",3)),2&n&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("icon",i.icon),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!i.visibleNews),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.visibleNews))},dependencies:[m.CommonModule,m.NgIf,u.Oh,u.Zl],encapsulation:2}),t})();var a=o(87956),s=o(98699),c=o(3372);let d=(()=>{class t{constructor(n,i,r){this.deviceService=n,this.customerStore=i,this.preferencesService=r,this.capsStatement=!1}request(){var n=this;return(0,T.Z)(function*(){try{const{appCompilation:i,name:r,model:h}=yield n.deviceService.getInfo(),N=yield n.preferencesService.requestBoolean(c.M.Caps);return s.Either.success({compilation:i,device:r??h,session$:A=>n.customerStore.subscribe(A),capsState:N})}catch({message:i}){return s.Either.failure({message:i})}})()}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275inject(a.U8),e.\u0275\u0275inject(k.f),e.\u0275\u0275inject(a.yW))},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function g(t,l){if(1&t&&(e.\u0275\u0275elementStart(0,"span",28),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",null==n.session||null==n.session.customer?null:n.session.customer.segment.label," ")}}function M(t,l){1&t&&e.\u0275\u0275element(0,"img",29)}function C(t,l){if(1&t&&(e.\u0275\u0275elementStart(0,"mbo-segment"),e.\u0275\u0275template(1,g,2,1,"span",26),e.\u0275\u0275template(2,M,1,0,"img",27),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","AVAL"!==(null==n.session||null==n.session.customer?null:n.session.customer.segment.code)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","AVAL"===(null==n.session||null==n.session.customer?null:n.session.customer.segment.code))}}function f(t,l){if(1&t&&(e.\u0275\u0275elementStart(0,"div",30)(1,"bocc-badge")(2,"b"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()()()),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Actividad ",n.appCodeActivity,"")}}function j(t,l){if(1&t){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-sidenav-option",31),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onCaps())}),e.\u0275\u0275text(1," Topes "),e.\u0275\u0275elementEnd()}}function P(t,l){if(1&t){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-sidenav-option",32),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onProducts())}),e.\u0275\u0275text(1," Activar productos "),e.\u0275\u0275elementEnd()}}function H(t,l){if(1&t){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-sidenav-option",33),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onHelp())}),e.\u0275\u0275text(1," Ayuda "),e.\u0275\u0275elementEnd()}}const{CERTIFICATIONS:L,HELP:B,NOTIFICATIONS:G,SECURITY:R,CAPS:Y}=v.Z6.CUSTOMER;let Z=(()=>{class t{constructor(n,i,r,h,N,A){this.ref=n,this.modalConfirmation=i,this.mboProvider=r,this.inactivityProvider=h,this.managerSidenav=N,this.signoutCustomer=A,this.visible=!0,this.start=!1,this.classSegment="advance",this.version=_.N.appVersion,this.compilation="0.0.0",this.capsState=!1,this.visibleChange=new e.EventEmitter}ngOnInit(){this.initializatedConfiguration()}ngOnChanges(n){const{visible:i}=n;if(i){const{classList:r}=this.ref.nativeElement;i.currentValue&&!this.start&&(this.start=!0,r.add("start")),i.currentValue?r.add("visible"):r.remove("visible")}}get envDevelopment(){return!_.N.production}get appCodeActivity(){return _.N.appCodeActivity}onClose(){this.closeComponent()}onCertifications(){this.closeComponent(L.HOME)}onCaps(){this.closeComponent(Y.HOME)}onNotifications(){this.closeComponent(G.HOME)}onSecurity(){this.closeComponent(R.HOME)}onProducts(){this.closeComponent(R.ACTIVATE_PRODUCTS.CREDIT_CARD.SOURCE)}onHelp(){this.closeComponent(B.HOME)}onOffices(){this.mboProvider.openUrl(v.BA.GEOLOCATION)}onAvalPay(){this.mboProvider.openUrl(v.BA.AVAL_PAY_CENTER)}onLogout(){this.modalConfirmation.execute({title:"SALIDA SEGURA",message:"\xbfEstas seguro que deseas cerrar sesi\xf3n en la aplicaci\xf3n?",accept:{label:"Aceptar",click:()=>{this.logout()}},decline:{label:"Cancelar"}})}initializatedConfiguration(){var n=this;return(0,T.Z)(function*(){(yield n.managerSidenav.request()).when({success:({compilation:i,device:r,session$:h,capsState:N})=>{n.compilation=i,n.device=r,n.capsState=N,h(({session:A})=>{n.classSegment=A?.customer.segment.className,n.session=A})}})})()}logout(){var n=this;return(0,T.Z)(function*(){yield n.signoutCustomer.execute(),n.mboProvider.navigation.back(v.Z6.AUTHENTICATION.LOGIN),n.closeComponent(),n.inactivityProvider.off()})()}closeComponent(n){this.ref.nativeElement.classList.remove("visible"),this.visible=!1,this.visibleChange.emit(!1),n&&setTimeout(()=>{this.mboProvider.navigation.next(n)},280)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(u.$e),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(y._5),e.\u0275\u0275directiveInject(d),e.\u0275\u0275directiveInject(D.xE))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-application-sidenav"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:47,vars:15,consts:[[1,"mbo-application-sidenav__component"],[1,"mbo-application-sidenav__content"],[1,"mbo-application-sidenav__header"],[1,"mbo-application-sidenav__header__carnet"],[1,"mbo-application-sidenav__header__avatar"],[1,"mbo-application-sidenav__header__info"],[1,"mbo-application-sidenav__header__title","body2-medium"],[1,"mbo-application-sidenav__header__segment"],[4,"ngIf"],["class","mbo-application-sidenav__badge",4,"ngIf"],[1,"mbo-application-sidenav__body"],[1,"mbo-application-sidenav__menu"],["id","btn_app-sidenav_certifications","icon","certificate-medal",3,"click"],["id","btn_app-sidenav_caps","icon","arrow-trm",3,"click",4,"ngIf"],["id","btn_app-sidenav_notifications","icon","bell",3,"click"],["id","btn_app-sidenav_security","icon","protection",3,"click"],["id","btn_app-sidenav_products","icon","products-active",3,"click",4,"ngIf"],["id","btn_app-sidenav_help","icon","question","versionNews","5.6.0",3,"click",4,"ngIf"],["id","btn_app-sidenav_location","icon","location",3,"click"],["id","btn_app-sidenav_aval-pay","icon","aval-products",3,"click"],[1,"mbo-application-sidenav__info"],[1,"caption-regular"],[1,"mbo-application-sidenav__footer"],["id","btn_app-sidenav_logout","bocc-button","raised","prefixIcon","log-out",3,"click"],["src","assets/shared/logos/occidente-sidenav.svg","alt","mbo-application-sidenav-bank-img",1,"mbo-application-sidenav__logo"],[1,"mbo-application-sidenav__backdrop",3,"click"],["class","overline-semibold truncate",4,"ngIf"],["alt","mbo_customer_segment","src","assets/shared/logos/aval-segment.svg",4,"ngIf"],[1,"overline-semibold","truncate"],["alt","mbo_customer_segment","src","assets/shared/logos/aval-segment.svg"],[1,"mbo-application-sidenav__badge"],["id","btn_app-sidenav_caps","icon","arrow-trm",3,"click"],["id","btn_app-sidenav_products","icon","products-active",3,"click"],["id","btn_app-sidenav_help","icon","question","versionNews","5.6.0",3,"click"]],template:function(n,i){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",5)(7,"label",6),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",7),e.\u0275\u0275template(10,C,3,2,"mbo-segment",8),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(11,f,4,1,"div",9),e.\u0275\u0275elementStart(12,"div",10)(13,"div",11)(14,"mbo-sidenav-option",12),e.\u0275\u0275listener("click",function(){return i.onCertifications()}),e.\u0275\u0275text(15," Documentos "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(16,j,2,0,"mbo-sidenav-option",13),e.\u0275\u0275elementStart(17,"mbo-sidenav-option",14),e.\u0275\u0275listener("click",function(){return i.onNotifications()}),e.\u0275\u0275text(18," Notificaciones "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"mbo-sidenav-option",15),e.\u0275\u0275listener("click",function(){return i.onSecurity()}),e.\u0275\u0275text(20," Seguridad "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(21,P,2,0,"mbo-sidenav-option",16),e.\u0275\u0275template(22,H,2,0,"mbo-sidenav-option",17),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"div",11)(24,"mbo-sidenav-option",18),e.\u0275\u0275listener("click",function(){return i.onOffices()}),e.\u0275\u0275text(25," Ubica tu oficina "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"mbo-sidenav-option",19),e.\u0275\u0275listener("click",function(){return i.onAvalPay()}),e.\u0275\u0275text(27," Aval Pay Center "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",20)(29,"label",21),e.\u0275\u0275text(30," Ultimo ingreso "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"label",21),e.\u0275\u0275text(32),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(33,"label",21),e.\u0275\u0275text(34),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(35,"label",21),e.\u0275\u0275text(36),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(37,"label",21),e.\u0275\u0275text(38),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(39,"label",21),e.\u0275\u0275text(40),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(41,"div",22)(42,"button",23),e.\u0275\u0275listener("click",function(){return i.onLogout()}),e.\u0275\u0275elementStart(43,"span"),e.\u0275\u0275text(44,"Salida segura"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(45,"img",24),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(46,"div",25),e.\u0275\u0275listener("click",function(){return i.onClose()}),e.\u0275\u0275elementEnd()),2&n&&(e.\u0275\u0275classMap(i.classSegment),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",null==i.session||null==i.session.customer?null:i.session.customer.initialsName," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==i.session||null==i.session.customer?null:i.session.customer.clientName," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==i.session||null==i.session.customer?null:i.session.customer.segment.visible),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",i.capsState),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate1(" ",null==i.session||null==i.session.lastAuthDate?null:i.session.lastAuthDate.fullFormat," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" Dispositivo ",i.device," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" IP ",null==i.session?null:i.session.ip," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" Fecha ",null==i.session||null==i.session.currentDate?null:i.session.currentDate.fullFormat," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2(" Versi\xf3n ",i.version," (",i.compilation,") "))},dependencies:[m.CommonModule,m.NgIf,u.Oh,u.P8,x,S],styles:["mbo-application-sidenav{--pvt-component-color-font: var(--color-carbon-lighter-50);--pvt-opacity-backdrop: 0;--pvt-carnet-gap: var(--sizing-x6);--pvt-avatar-sizing: var(--sizing-x28);--pvt-button-color: var(--color-carbon-lighter-50);--pvt-button-background: var(--gradient-blue-bottom-700);--pvt-menu-background: var(--overlay-blue-20);position:fixed;display:block;top:0rem;left:0rem;width:100%;height:100%;z-index:var(--z-index-32);transform:translate(-100%)}mbo-application-sidenav.start{transition:transform .24s var(--standard-curve)}mbo-application-sidenav.visible{--pvt-opacity-backdrop: 1;transform:translate(0)}mbo-application-sidenav mbo-sidenav-option{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);color:var(--pvt-component-color-font);padding:var(--sizing-x3) 0rem var(--sizing-x3) var(--sizing-x3);box-sizing:border-box}mbo-application-sidenav mbo-sidenav-option>label{width:calc(100% - var(--sizing-x24));height:var(--sizing-x16);line-height:var(--sizing-x16);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-weight:var(--font-weight-medium);font-size:var(--smalltext-size);letter-spacing:var(--smalltext-letter-spacing)}mbo-application-sidenav mbo-sidenav-option bocc-badge{margin-right:var(--sizing-x4)}mbo-application-sidenav .mbo-application-sidenav__component{--avatar-color-background: var(--color-blue-700);--component-color-background: linear-gradient( 180deg, #0056cb 0%, #00246f 100% );position:relative;display:flex;justify-content:space-between;flex-direction:column;width:calc(100% - var(--sizing-x28));height:100%;overflow:auto;z-index:var(--z-index-24);padding-top:var(--sizing-safe-top);box-sizing:border-box;background:var(--component-color-background);border-radius:0rem var(--sizing-x8) var(--sizing-x8) 0rem}mbo-application-sidenav .mbo-application-sidenav__component.elite{--avatar-color-background: var(--color-navy-700);--component-color-background: linear-gradient( 180deg, #00244f 0%, #001227 100% );--segment-border: 1px solid var(--color-ocher-900);--segment-gradient-background: linear-gradient( 1turn, #dca85e 0, #7e5a23 100% );--segment-gradient-background-source: linear-gradient( to bottom, var(--color-ocher-900), #dca85e );--segment-gradient-background-shape: linear-gradient( -8deg, #dca85e 0, #7e5a23 100% )}mbo-application-sidenav .mbo-application-sidenav__component.elite-plus{--pvt-menu-background: var(--overlay-dgrey-20);--avatar-color-background: rgba(4, 4, 4, .4);--component-color-background: var(--gradient-segments-elite-plus);--segment-border: 1px solid #3a3c3f;--segment-gradient-background: linear-gradient( 1turn, #4b4e53 0, #3a3c3f 100% );--segment-gradient-background-source: linear-gradient( to bottom, #3a3c3f, #4b4e53 );--segment-gradient-background-shape: linear-gradient( -8deg, #4b4e53 0, #3a3c3f 100% )}mbo-application-sidenav .mbo-application-sidenav__component.preferential{--avatar-color-background: var(--color-navy-700);--component-color-background: linear-gradient( 180deg, #163467 0%, #0a1d3d 100% );--segment-color-font: var(--color-carbon-lighter-900);--segment-border: 1px solid var(--color-carbon-lighter-900);--segment-gradient-background: linear-gradient( 1turn, #f0f0f0 0, #989898 100% );--segment-gradient-background-source: linear-gradient( to bottom, var(--color-carbon-lighter-900), #f0f0f0 );--segment-gradient-background-shape: linear-gradient( -8deg, #f0f0f0 0, #989898 100% )}mbo-application-sidenav .mbo-application-sidenav__component.selecto{--pvt-button-color: var(--color-blue-700);--pvt-button-background: linear-gradient( 180deg, #ffffff 0%, #dfe5f9 100% );--avatar-color-background: var(--color-navy-700);--component-color-background: linear-gradient( 180deg, #002449 0%, #0055b3 100% );--segment-color-font: var(--color-carbon-lighter-50);--segment-border: 1px solid #0055b3;--segment-gradient-background: linear-gradient( 180deg, #002449 0%, #0055b3 100% );--segment-gradient-background-source: linear-gradient( to bottom, #002449, #0055b3 );--segment-gradient-background-shape: linear-gradient( -8deg, #0055b3 0, #002449 100% )}mbo-application-sidenav .mbo-application-sidenav__component.aval{--pvt-button-color: var(--color-blue-700);--pvt-button-background: linear-gradient( 180deg, #ffffff 0%, #dfe5f9 100% );--avatar-color-background: var(--color-navy-700);--component-color-background: linear-gradient( 180deg, #002449 0%, #0055b3 100% );--segment-color-font: var(--color-carbon-lighter-50);--segment-border: 1px solid #0055b3;--segment-gradient-background: var(--color-carbon-lighter-50);--segment-gradient-background-source: var(--color-carbon-lighter-50);--segment-gradient-background-shape: var(--color-carbon-lighter-50)}mbo-application-sidenav .mbo-application-sidenav__component.migration{--pvt-button-background: var(--gradient-danger-bottom-900);--pvt-menu-background: var(--overlay-dgrey-20);--pvt-button-background: linear-gradient( 180deg, #e24c4c 0%, #981b1f 100% );--avatar-color-background: #e24c4c;--component-color-background: linear-gradient( 180deg, #902424 0%, #410e0e 100% );--segment-border: 1px solid #981b1f;--segment-gradient-background: linear-gradient( 1turn, #e24c4c 0, #981b1f 100% );--segment-gradient-background-source: linear-gradient( to bottom, #981b1f, #e24c4c );--segment-gradient-background-shape: linear-gradient( -8deg, #e24c4c 0, #981b1f 100% )}mbo-application-sidenav .mbo-application-sidenav__component.developer{--pvt-menu-background: var(--overlay-dgrey-20);--pvt-button-background: var(--gradient-developer-bottom-900);--avatar-color-background: #4b4e53;--component-color-background: linear-gradient( 180deg, #3a3c3f 0%, #242528 100% );--segment-border: 1px solid #3a3c3f;--segment-gradient-background: linear-gradient( 1turn, #4b4e53 0, #3a3c3f 100% );--segment-gradient-background-source: linear-gradient( to bottom, #3a3c3f, #4b4e53 );--segment-gradient-background-shape: linear-gradient( -8deg, #4b4e53 0, #3a3c3f 100% )}mbo-application-sidenav .mbo-application-sidenav__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box;z-index:var(--z-index-2)}mbo-application-sidenav .mbo-application-sidenav__header__carnet{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--pvt-carnet-gap);column-gap:var(--pvt-carnet-gap)}mbo-application-sidenav .mbo-application-sidenav__header__avatar{background:var(--avatar-color-background);width:var(--pvt-avatar-sizing);height:var(--pvt-avatar-sizing);line-height:var(--pvt-avatar-sizing);text-align:center;border-radius:50%;font-size:var(--sizing-x8);color:var(--color-carbon-lighter-50)}mbo-application-sidenav .mbo-application-sidenav__header__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--pvt-avatar-sizing) - var(--pvt-carnet-gap));overflow:hidden}mbo-application-sidenav .mbo-application-sidenav__header__title{position:relative;width:100%;color:var(--pvt-component-color-font);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}mbo-application-sidenav .mbo-application-sidenav__header__subtitle{position:relative;width:100%;opacity:.5;color:var(--color-blue-700)}mbo-application-sidenav .mbo-application-sidenav__badge{display:flex;justify-content:center;padding-bottom:var(--sizing-x4)}mbo-application-sidenav .mbo-application-sidenav__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-application-sidenav .mbo-application-sidenav__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-application-sidenav .mbo-application-sidenav__menu{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x2);box-sizing:border-box;background:var(--pvt-menu-background);border-radius:var(--sizing-x4)}mbo-application-sidenav .mbo-application-sidenav__info{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x8) var(--sizing-x2);box-sizing:border-box}mbo-application-sidenav .mbo-application-sidenav__info>label{position:relative;width:100%;color:var(--color-carbon-lighter-50);padding-left:var(--sizing-x2);box-sizing:border-box}mbo-application-sidenav .mbo-application-sidenav__logo{position:absolute;bottom:0;z-index:-1}mbo-application-sidenav .mbo-application-sidenav__footer{position:sticky;bottom:0rem;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-application-sidenav .mbo-application-sidenav__footer button{color:var(--pvt-button-color);background:var(--pvt-button-background);width:100%}mbo-application-sidenav .mbo-application-sidenav__backdrop{position:absolute;top:0rem;width:100%;height:100%;opacity:var(--pvt-opacity-backdrop);will-change:opacity;background:var(--overlay-blue-80);transition:opacity .24s 0ms var(--deceleration-curve);transition-delay:.16s;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}@media screen and (max-width: 320px){mbo-application-sidenav{--pvt-carnet-gap: var(--sizing-x4);--pvt-avatar-sizing: var(--sizing-x24)}}\n"],encapsulation:2}),t})();var I=o(82470);const W=[{path:"",component:(()=>{class t{constructor(n,i,r){this.modalConfirmationService=n,this.mboProvider=i,this.deviceService=r,this.visible=!1}ngOnInit(){this.deviceService.controller.itIsMobile&&this.deviceService.controller.itIsIos&&(I.StatusBar.setStyle({style:I.Style.Dark}),this.modalConfirmationService.setOpening(()=>{I.StatusBar.setStyle({style:I.Style.Dark})}),this.modalConfirmationService.setClosing(()=>{I.StatusBar.setStyle({style:I.Style.Dark})})),this.mboProvider.navigation.setAnimationElement(document.querySelector(".mbo-application__page__body"))}onHeader(n){switch(n){case"notifications":this.mboProvider.navigation.next(v.Z6.CUSTOMER.NOTIFICATIONS.HOME);break;case"customer":this.visible=!0}}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(u.$e),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(a.U8))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-application-home"]],decls:5,vars:1,consts:[[1,"mbo-application__page__header"],[3,"listener"],[1,"mbo-application__page__body"],[3,"visible","visibleChange"]],template:function(n,i){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"mbo-application-header",1),e.\u0275\u0275listener("listener",function(h){return i.onHeader(h)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(2,"div",2)(3,"mbo-application-sidenav",3),e.\u0275\u0275listener("visibleChange",function(h){return i.visible=h}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(4,"router-outlet"),e.\u0275\u0275elementEnd()),2&n&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("visible",i.visible))},dependencies:[b.RouterOutlet,z,Z],styles:["mbo-application-home{--mbo-application-body-safe-spacing: calc( var(--sizing-x28) + var(--sizing-safe-top, 0rem) )}\n"],encapsulation:2}),t})(),children:[{path:"customer",loadChildren:()=>o.e(9698).then(o.bind(o,69698)).then(t=>t.MboCustomerModule)},{path:"transfers",loadChildren:()=>o.e(8181).then(o.bind(o,48181)).then(t=>t.MboTransfersModule)},{path:"payments",loadChildren:()=>o.e(6550).then(o.bind(o,56550)).then(t=>t.MboPaymentsModule)},{path:"transactions",loadChildren:()=>o.e(9406).then(o.bind(o,39406)).then(t=>t.MboTransactionsModule)},{path:"two-factor",loadChildren:()=>o.e(7800).then(o.bind(o,47800)).then(t=>t.MboTwoFactorAuthenticationModule)}]}];let X=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[b.RouterModule.forChild(W),z,Z]}),t})()}}]);