(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5091],{35091:(jc,Vr,Pr)=>{Pr.r(Vr),Pr.d(Vr,{BREAK:()=>un,BreakingChangeType:()=>ie,DEFAULT_DEPRECATION_REASON:()=>er,DangerousChangeType:()=>Ge,DirectiveLocation:()=>L,ExecutableDefinitionsRule:()=>Na,FieldsOnCorrectTypeRule:()=>La,FragmentsOnCompositeTypesRule:()=>wa,GraphQLBoolean:()=>Te,GraphQLDeprecatedDirective:()=>vt,GraphQLDirective:()=>Le,GraphQLEnumType:()=>xe,GraphQLError:()=>E,GraphQLFloat:()=>da,GraphQLID:()=>Kt,GraphQLIncludeDirective:()=>Zt,GraphQLInputObjectType:()=>en,GraphQLInt:()=>va,GraphQLInterfaceType:()=>Ze,GraphQLList:()=>te,GraphQLNonNull:()=>k,GraphQLObjectType:()=>Ee,GraphQLScalarType:()=>Re,GraphQLSchema:()=>mn,GraphQLSkipDirective:()=>$t,GraphQLSpecifiedByDirective:()=>nr,GraphQLString:()=>Z,GraphQLUnionType:()=>$e,Kind:()=>v,KnownArgumentNamesRule:()=>Ga,KnownDirectivesRule:()=>hr,KnownFragmentNamesRule:()=>ka,KnownTypeNamesRule:()=>dr,Lexer:()=>_t,Location:()=>Rt,LoneAnonymousOperationRule:()=>ba,LoneSchemaDefinitionRule:()=>$a,NoDeprecatedCustomRule:()=>Pi,NoFragmentCyclesRule:()=>Pa,NoSchemaIntrospectionCustomRule:()=>Gu,NoUndefinedVariablesRule:()=>Ca,NoUnusedFragmentsRule:()=>_a,NoUnusedVariablesRule:()=>Ua,OverlappingFieldsCanBeMergedRule:()=>za,PossibleFragmentSpreadsRule:()=>Va,PossibleTypeExtensionsRule:()=>ii,ProvidedRequiredArgumentsRule:()=>Ka,ScalarLeafsRule:()=>Ra,SchemaMetaFieldDef:()=>Rn,SingleFieldSubscriptionsRule:()=>Da,Source:()=>zn,Token:()=>ee,TokenKind:()=>m,TypeInfo:()=>cr,TypeKind:()=>J,TypeMetaFieldDef:()=>Ln,TypeNameMetaFieldDef:()=>Fn,UniqueArgumentNamesRule:()=>yr,UniqueDirectiveNamesRule:()=>ai,UniqueDirectivesPerLocationRule:()=>mr,UniqueEnumValueNamesRule:()=>ti,UniqueFieldDefinitionNamesRule:()=>ri,UniqueFragmentNamesRule:()=>Fa,UniqueInputFieldNamesRule:()=>Ir,UniqueOperationNamesRule:()=>Oa,UniqueOperationTypesRule:()=>ei,UniqueTypeNamesRule:()=>ni,UniqueVariableNamesRule:()=>Ma,ValidationContext:()=>ci,ValuesOfCorrectTypeRule:()=>Qa,VariablesAreInputTypesRule:()=>Aa,VariablesInAllowedPositionRule:()=>Xa,__Directive:()=>Jt,__DirectiveLocation:()=>Xt,__EnumValue:()=>zt,__Field:()=>Ht,__InputValue:()=>An,__Schema:()=>ft,__Type:()=>Ne,__TypeKind:()=>Wt,assertAbstractType:()=>ps,assertCompositeType:()=>fs,assertDirective:()=>ks,assertEnumType:()=>as,assertInputObjectType:()=>is,assertInputType:()=>us,assertInterfaceType:()=>oa,assertLeafType:()=>ls,assertListType:()=>os,assertNamedType:()=>ds,assertNonNullType:()=>ss,assertNullableType:()=>jt,assertObjectType:()=>ia,assertOutputType:()=>cs,assertScalarType:()=>ts,assertSchema:()=>rr,assertType:()=>aa,assertUnionType:()=>rs,assertValidName:()=>Xo,assertValidSchema:()=>ir,assertWrappingType:()=>vs,astFromValue:()=>Pe,buildASTSchema:()=>Qi,buildClientSchema:()=>Hu,buildSchema:()=>$u,coerceInputValue:()=>vi,concatAST:()=>mc,createSourceEventStream:()=>Vi,defaultFieldResolver:()=>wi,defaultTypeResolver:()=>Si,doTypesOverlap:()=>Bt,execute:()=>Dr,executeSync:()=>hi,extendSchema:()=>Wu,findBreakingChanges:()=>Ic,findDangerousChanges:()=>Nc,findDeprecatedUsages:()=>Rc,formatError:()=>Bu,getDescription:()=>on,getDirectiveValues:()=>Pn,getIntrospectionQuery:()=>Ci,getLocation:()=>Yn,getNamedType:()=>ce,getNullableType:()=>Gt,getOperationAST:()=>qu,getOperationRootType:()=>Or,getVisitFn:()=>cn,graphql:()=>Pu,graphqlSync:()=>Mu,introspectionFromSchema:()=>Xu,introspectionTypes:()=>kn,isAbstractType:()=>Ve,isCompositeType:()=>_e,isDefinitionNode:()=>qs,isDirective:()=>pt,isEnumType:()=>re,isEqualType:()=>st,isExecutableDefinitionNode:()=>fr,isInputObjectType:()=>z,isInputType:()=>pe,isInterfaceType:()=>j,isIntrospectionType:()=>tn,isLeafType:()=>ke,isListType:()=>W,isNamedType:()=>vn,isNonNullType:()=>_,isNullableType:()=>sa,isObjectType:()=>U,isOutputType:()=>Je,isRequiredArgument:()=>Xe,isRequiredInputField:()=>ot,isScalarType:()=>ye,isSchema:()=>Ea,isSelectionNode:()=>Ks,isSpecifiedDirective:()=>tr,isSpecifiedScalarType:()=>lt,isType:()=>rt,isTypeDefinitionNode:()=>En,isTypeExtensionNode:()=>dt,isTypeNode:()=>Xs,isTypeSubTypeOf:()=>nn,isTypeSystemDefinitionNode:()=>pr,isTypeSystemExtensionNode:()=>vr,isUnionType:()=>se,isValidNameError:()=>Ut,isValueNode:()=>Js,isWrappingType:()=>bn,lexicographicSortSchema:()=>nc,locatedError:()=>Ye,parse:()=>Zn,parseType:()=>Qo,parseValue:()=>Zr,print:()=>H,printError:()=>Yr,printIntrospectionSchema:()=>rc,printLocation:()=>Cr,printSchema:()=>tc,printSourceLocation:()=>St,printType:()=>Ki,responsePathAsArray:()=>de,separateOperations:()=>yc,specifiedDirectives:()=>je,specifiedRules:()=>oi,specifiedScalarTypes:()=>wn,stripIgnoredCharacters:()=>Ec,subscribe:()=>xu,syntaxError:()=>fe,typeFromAST:()=>ve,validate:()=>Nr,validateSchema:()=>ar,valueFromAST:()=>Me,valueFromASTUntyped:()=>tt,version:()=>mo,versionInfo:()=>yo,visit:()=>We,visitInParallel:()=>Ct,visitWithTypeInfo:()=>lr});var mo="15.8.0",yo=Object.freeze({major:15,minor:8,patch:0,preReleaseTag:null});function le(e){return"function"==typeof e?.then}function Qn(e){return(Qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function he(e){return"object"==Qn(e)&&null!==e}var Mr="function"==typeof Symbol&&null!=Symbol.iterator?Symbol.iterator:"@@iterator",Dt="function"==typeof Symbol&&null!=Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator",Ie="function"==typeof Symbol&&null!=Symbol.toStringTag?Symbol.toStringTag:"@@toStringTag";function Yn(e,n){for(var i,t=/\r\n|[\n\r]/g,r=1,a=n+1;(i=t.exec(e.body))&&i.index<n;)r+=1,a=n+1-(i.index+i[0].length);return{line:r,column:a}}function Cr(e){return St(e.source,Yn(e.source,e.start))}function St(e,n){var t=e.locationOffset.column-1,r=qn(t)+e.body,a=n.line-1,o=n.line+(e.locationOffset.line-1),u=n.column+(1===n.line?t:0),c="".concat(e.name,":").concat(o,":").concat(u,"\n"),l=r.split(/\r\n|[\n\r]/g),f=l[a];if(f.length>120){for(var p=Math.floor(u/80),d=u%80,h=[],y=0;y<f.length;y+=80)h.push(f.slice(y,y+80));return c+Ur([["".concat(o),h[0]]].concat(h.slice(1,p+1).map(function(I){return["",I]}),[[" ",qn(d-1)+"^"],["",h[p+1]]]))}return c+Ur([["".concat(o-1),l[a-1]],["".concat(o),f],["",qn(u-1)+"^"],["".concat(o+1),l[a+1]]])}function Ur(e){var n=e.filter(function(r){return void 0!==r[1]}),t=Math.max.apply(Math,n.map(function(r){return r[0].length}));return n.map(function(r){var i=r[1];return function Eo(e,n){return qn(e-n.length)+n}(t,r[0])+(i?" | "+i:" |")}).join("\n")}function qn(e){return Array(e+1).join(" ")}function Kn(e){return(Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function xr(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function go(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function jr(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Gr(e,n){return!n||"object"!==Kn(n)&&"function"!=typeof n?In(e):n}function In(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function wt(e){var n="function"==typeof Map?new Map:void 0;return wt=function(r){if(null===r||!function Do(e){return-1!==Function.toString.call(e).indexOf("[native code]")}(r))return r;if("function"!=typeof r)throw new TypeError("Super expression must either be null or a function");if(typeof n<"u"){if(n.has(r))return n.get(r);n.set(r,a)}function a(){return Jn(r,arguments,On(this).constructor)}return a.prototype=Object.create(r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),Nn(a,r)},wt(e)}function Jn(e,n,t){return(Jn=Br()?Reflect.construct:function(a,i,o){var s=[null];s.push.apply(s,i);var c=new(Function.bind.apply(a,s));return o&&Nn(c,o.prototype),c}).apply(null,arguments)}function Br(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Nn(e,n){return(Nn=Object.setPrototypeOf||function(r,a){return r.__proto__=a,r})(e,n)}function On(e){return(On=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}var E=function(e){!function Oo(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&Nn(e,n)}(t,e);var n=function bo(e){var n=Br();return function(){var a,r=On(e);if(n){var i=On(this).constructor;a=Reflect.construct(r,arguments,i)}else a=r.apply(this,arguments);return Gr(this,a)}}(t);function t(r,a,i,o,s,u,c){var l,f,p,d;(function Io(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")})(this,t),(d=n.call(this,r)).name="GraphQLError",d.originalError=u??void 0,d.nodes=Qr(Array.isArray(a)?a:a?[a]:void 0);for(var h=[],y=0,I=null!==(N=d.nodes)&&void 0!==N?N:[];y<I.length;y++){var N,F=I[y].loc;null!=F&&h.push(F)}h=Qr(h),d.source=i??(null===(l=h)||void 0===l?void 0:l[0].source),d.positions=o??(null===(f=h)||void 0===f?void 0:f.map(function(R){return R.start})),d.locations=o&&i?o.map(function(R){return Yn(i,R)}):null===(p=h)||void 0===p?void 0:p.map(function(R){return Yn(R.source,R.start)}),d.path=s??void 0;var V=u?.extensions;return d.extensions=null==c&&he(V)?function To(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?xr(Object(t),!0).forEach(function(r){go(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):xr(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}({},V):c??{},Object.defineProperties(In(d),{message:{enumerable:!0},locations:{enumerable:null!=d.locations},path:{enumerable:null!=d.path},extensions:{enumerable:null!=d.extensions&&Object.keys(d.extensions).length>0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),null!=u&&u.stack?(Object.defineProperty(In(d),"stack",{value:u.stack,writable:!0,configurable:!0}),Gr(d)):(Error.captureStackTrace?Error.captureStackTrace(In(d),t):Object.defineProperty(In(d),"stack",{value:Error().stack,writable:!0,configurable:!0}),d)}return function No(e,n,t){n&&jr(e.prototype,n),t&&jr(e,t)}(t,[{key:"toString",value:function(){return Yr(this)}},{key:Ie,get:function(){return"Object"}}]),t}(wt(Error));function Qr(e){return void 0===e||0===e.length?void 0:e}function Yr(e){var n=e.message;if(e.nodes)for(var t=0,r=e.nodes;t<r.length;t++){var a=r[t];a.loc&&(n+="\n\n"+Cr(a.loc))}else if(e.source&&e.locations)for(var i=0,o=e.locations;i<o.length;i++)n+="\n\n"+St(e.source,o[i]);return n}function fe(e,n,t){return new E("Syntax Error: ".concat(t),void 0,e,[n])}var v=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"});function $(e,n){if(!Boolean(e))throw new Error(n??"Unexpected invariant triggered.")}const At="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):void 0;function be(e){var n=e.prototype.toJSON;"function"==typeof n||$(0),e.prototype.inspect=n,At&&(e.prototype[At]=n)}var Rt=function(){function e(t,r,a){this.start=t.start,this.end=r.end,this.startToken=t,this.endToken=r,this.source=a}return e.prototype.toJSON=function(){return{start:this.start,end:this.end}},e}();be(Rt);var ee=function(){function e(t,r,a,i,o,s,u){this.kind=t,this.start=r,this.end=a,this.line=i,this.column=o,this.value=u,this.prev=s,this.next=null}return e.prototype.toJSON=function(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}},e}();function Lt(e){return null!=e&&"string"==typeof e.kind}be(ee);var m=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"});function Xn(e){return(Xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}var wo=10,qr=2;function T(e){return Hn(e,[])}function Hn(e,n){switch(Xn(e)){case"string":return JSON.stringify(e);case"function":return e.name?"[function ".concat(e.name,"]"):"[function]";case"object":return null===e?"null":function Ao(e,n){if(-1!==n.indexOf(e))return"[Circular]";var t=[].concat(n,[e]),r=function Fo(e){var n=e[String(At)];return"function"==typeof n?n:"function"==typeof e.inspect?e.inspect:void 0}(e);if(void 0!==r){var a=r.call(e);if(a!==e)return"string"==typeof a?a:Hn(a,t)}else if(Array.isArray(e))return function Lo(e,n){if(0===e.length)return"[]";if(n.length>qr)return"[Array]";for(var t=Math.min(wo,e.length),r=e.length-t,a=[],i=0;i<t;++i)a.push(Hn(e[i],n));return 1===r?a.push("... 1 more item"):r>1&&a.push("... ".concat(r," more items")),"["+a.join(", ")+"]"}(e,t);return function Ro(e,n){var t=Object.keys(e);if(0===t.length)return"{}";if(n.length>qr)return"["+function ko(e){var n=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if("Object"===n&&"function"==typeof e.constructor){var t=e.constructor.name;if("string"==typeof t&&""!==t)return t}return n}(e)+"]";var r=t.map(function(a){return a+": "+Hn(e[a],n)});return"{ "+r.join(", ")+" }"}(e,t)}(e,n);default:return String(e)}}function P(e,n){if(!Boolean(e))throw new Error(n)}const De=function(n,t){return n instanceof t};function Kr(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var zn=function(){function e(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GraphQL request",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{line:1,column:1};"string"==typeof n||P(0,"Body must be a string. Received: ".concat(T(n),".")),this.body=n,this.name=t,this.locationOffset=r,this.locationOffset.line>0||P(0,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||P(0,"column in locationOffset is 1-indexed and must be positive.")}return function _o(e,n,t){n&&Kr(e.prototype,n),t&&Kr(e,t)}(e,[{key:Ie,get:function(){return"Source"}}]),e}();function Jr(e){return De(e,zn)}var L=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"});function kt(e){var n=e.split(/\r\n|[\n\r]/g),t=Hr(e);if(0!==t)for(var r=1;r<n.length;r++)n[r]=n[r].slice(t);for(var a=0;a<n.length&&Xr(n[a]);)++a;for(var i=n.length;i>a&&Xr(n[i-1]);)--i;return n.slice(a,i).join("\n")}function Xr(e){for(var n=0;n<e.length;++n)if(" "!==e[n]&&"\t"!==e[n])return!1;return!0}function Hr(e){for(var n,t=!0,r=!0,a=0,i=null,o=0;o<e.length;++o)switch(e.charCodeAt(o)){case 13:10===e.charCodeAt(o+1)&&++o;case 10:t=!1,r=!0,a=0;break;case 9:case 32:++a;break;default:r&&!t&&(null===i||a<i)&&(i=a),r=!1}return null!==(n=i)&&void 0!==n?n:0}function zr(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=-1===e.indexOf("\n"),s=!r||'"'===e[e.length-1]||"\\"===e[e.length-1]||t,u="";return s&&!(r&&(" "===e[0]||"\t"===e[0]))&&(u+="\n"+n),u+=n?e.replace(/\n/g,"\n"+n):e,s&&(u+="\n"),'"""'+u.replace(/"""/g,'\\"""')+'"""'}var _t=function(){function e(t){var r=new ee(m.SOF,0,0,0,0,null);this.source=t,this.lastToken=r,this.token=r,this.line=1,this.lineStart=0}var n=e.prototype;return n.advance=function(){return this.lastToken=this.token,this.token=this.lookahead()},n.lookahead=function(){var r=this.token;if(r.kind!==m.EOF)do{var a;r=null!==(a=r.next)&&void 0!==a?a:r.next=Vo(this,r)}while(r.kind===m.COMMENT);return r},e}();function Wr(e){return e===m.BANG||e===m.DOLLAR||e===m.AMP||e===m.PAREN_L||e===m.PAREN_R||e===m.SPREAD||e===m.COLON||e===m.EQUALS||e===m.AT||e===m.BRACKET_L||e===m.BRACKET_R||e===m.BRACE_L||e===m.PIPE||e===m.BRACE_R}function ze(e){return isNaN(e)?m.EOF:e<127?JSON.stringify(String.fromCharCode(e)):'"\\u'.concat(("00"+e.toString(16).toUpperCase()).slice(-4),'"')}function Vo(e,n){for(var t=e.source,r=t.body,a=r.length,i=n.end;i<a;){var o=r.charCodeAt(i),s=e.line,u=1+i-e.lineStart;switch(o){case 65279:case 9:case 32:case 44:++i;continue;case 10:++i,++e.line,e.lineStart=i;continue;case 13:10===r.charCodeAt(i+1)?i+=2:++i,++e.line,e.lineStart=i;continue;case 33:return new ee(m.BANG,i,i+1,s,u,n);case 35:return Mo(t,i,s,u,n);case 36:return new ee(m.DOLLAR,i,i+1,s,u,n);case 38:return new ee(m.AMP,i,i+1,s,u,n);case 40:return new ee(m.PAREN_L,i,i+1,s,u,n);case 41:return new ee(m.PAREN_R,i,i+1,s,u,n);case 46:if(46===r.charCodeAt(i+1)&&46===r.charCodeAt(i+2))return new ee(m.SPREAD,i,i+3,s,u,n);break;case 58:return new ee(m.COLON,i,i+1,s,u,n);case 61:return new ee(m.EQUALS,i,i+1,s,u,n);case 64:return new ee(m.AT,i,i+1,s,u,n);case 91:return new ee(m.BRACKET_L,i,i+1,s,u,n);case 93:return new ee(m.BRACKET_R,i,i+1,s,u,n);case 123:return new ee(m.BRACE_L,i,i+1,s,u,n);case 124:return new ee(m.PIPE,i,i+1,s,u,n);case 125:return new ee(m.BRACE_R,i,i+1,s,u,n);case 34:return 34===r.charCodeAt(i+1)&&34===r.charCodeAt(i+2)?xo(t,i,s,u,n,e):Uo(t,i,s,u,n);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return Co(t,i,o,s,u,n);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return Go(t,i,s,u,n)}throw fe(t,i,Po(o))}return new ee(m.EOF,a,a,e.line,1+i-e.lineStart,n)}function Po(e){return e<32&&9!==e&&10!==e&&13!==e?"Cannot contain the invalid character ".concat(ze(e),"."):39===e?"Unexpected single quote character ('), did you mean to use a double quote (\")?":"Cannot parse the unexpected character ".concat(ze(e),".")}function Mo(e,n,t,r,a){var o,i=e.body,s=n;do{o=i.charCodeAt(++s)}while(!isNaN(o)&&(o>31||9===o));return new ee(m.COMMENT,n,s,t,r,a,i.slice(n+1,s))}function Co(e,n,t,r,a,i){var o=e.body,s=t,u=n,c=!1;if(45===s&&(s=o.charCodeAt(++u)),48===s){if((s=o.charCodeAt(++u))>=48&&s<=57)throw fe(e,u,"Invalid number, unexpected digit after 0: ".concat(ze(s),"."))}else u=Vt(e,u,s),s=o.charCodeAt(u);if(46===s&&(c=!0,s=o.charCodeAt(++u),u=Vt(e,u,s),s=o.charCodeAt(u)),(69===s||101===s)&&(c=!0,(43===(s=o.charCodeAt(++u))||45===s)&&(s=o.charCodeAt(++u)),u=Vt(e,u,s),s=o.charCodeAt(u)),46===s||function Bo(e){return 95===e||e>=65&&e<=90||e>=97&&e<=122}(s))throw fe(e,u,"Invalid number, expected digit but got: ".concat(ze(s),"."));return new ee(c?m.FLOAT:m.INT,n,u,r,a,i,o.slice(n,u))}function Vt(e,n,t){var r=e.body,a=n,i=t;if(i>=48&&i<=57){do{i=r.charCodeAt(++a)}while(i>=48&&i<=57);return a}throw fe(e,a,"Invalid number, expected digit but got: ".concat(ze(i),"."))}function Uo(e,n,t,r,a){for(var i=e.body,o=n+1,s=o,u=0,c="";o<i.length&&!isNaN(u=i.charCodeAt(o))&&10!==u&&13!==u;){if(34===u)return c+=i.slice(s,o),new ee(m.STRING,n,o+1,t,r,a,c);if(u<32&&9!==u)throw fe(e,o,"Invalid character within String: ".concat(ze(u),"."));if(++o,92===u){switch(c+=i.slice(s,o-1),u=i.charCodeAt(o)){case 34:c+='"';break;case 47:c+="/";break;case 92:c+="\\";break;case 98:c+="\b";break;case 102:c+="\f";break;case 110:c+="\n";break;case 114:c+="\r";break;case 116:c+="\t";break;case 117:var l=jo(i.charCodeAt(o+1),i.charCodeAt(o+2),i.charCodeAt(o+3),i.charCodeAt(o+4));if(l<0){var f=i.slice(o+1,o+5);throw fe(e,o,"Invalid character escape sequence: \\u".concat(f,"."))}c+=String.fromCharCode(l),o+=4;break;default:throw fe(e,o,"Invalid character escape sequence: \\".concat(String.fromCharCode(u),"."))}s=++o}}throw fe(e,o,"Unterminated string.")}function xo(e,n,t,r,a,i){for(var o=e.body,s=n+3,u=s,c=0,l="";s<o.length&&!isNaN(c=o.charCodeAt(s));){if(34===c&&34===o.charCodeAt(s+1)&&34===o.charCodeAt(s+2))return l+=o.slice(u,s),new ee(m.BLOCK_STRING,n,s+3,t,r,a,kt(l));if(c<32&&9!==c&&10!==c&&13!==c)throw fe(e,s,"Invalid character within String: ".concat(ze(c),"."));10===c?(++s,++i.line,i.lineStart=s):13===c?(10===o.charCodeAt(s+1)?s+=2:++s,++i.line,i.lineStart=s):92===c&&34===o.charCodeAt(s+1)&&34===o.charCodeAt(s+2)&&34===o.charCodeAt(s+3)?(l+=o.slice(u,s)+'"""',u=s+=4):++s}throw fe(e,s,"Unterminated string.")}function jo(e,n,t,r){return Wn(e)<<12|Wn(n)<<8|Wn(t)<<4|Wn(r)}function Wn(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function Go(e,n,t,r,a){for(var i=e.body,o=i.length,s=n+1,u=0;s!==o&&!isNaN(u=i.charCodeAt(s))&&(95===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122);)++s;return new ee(m.NAME,n,s,t,r,a,i.slice(n,s))}function Zn(e,n){return new Pt(e,n).parseDocument()}function Zr(e,n){var t=new Pt(e,n);t.expectToken(m.SOF);var r=t.parseValueLiteral(!1);return t.expectToken(m.EOF),r}function Qo(e,n){var t=new Pt(e,n);t.expectToken(m.SOF);var r=t.parseTypeReference();return t.expectToken(m.EOF),r}var Pt=function(){function e(t,r){var a=Jr(t)?t:new zn(t);this._lexer=new _t(a),this._options=r}var n=e.prototype;return n.parseName=function(){var r=this.expectToken(m.NAME);return{kind:v.NAME,value:r.value,loc:this.loc(r)}},n.parseDocument=function(){var r=this._lexer.token;return{kind:v.DOCUMENT,definitions:this.many(m.SOF,this.parseDefinition,m.EOF),loc:this.loc(r)}},n.parseDefinition=function(){if(this.peek(m.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(m.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},n.parseOperationDefinition=function(){var r=this._lexer.token;if(this.peek(m.BRACE_L))return{kind:v.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(r)};var i,a=this.parseOperationType();return this.peek(m.NAME)&&(i=this.parseName()),{kind:v.OPERATION_DEFINITION,operation:a,name:i,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(r)}},n.parseOperationType=function(){var r=this.expectToken(m.NAME);switch(r.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(r)},n.parseVariableDefinitions=function(){return this.optionalMany(m.PAREN_L,this.parseVariableDefinition,m.PAREN_R)},n.parseVariableDefinition=function(){var r=this._lexer.token;return{kind:v.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(m.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(m.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(r)}},n.parseVariable=function(){var r=this._lexer.token;return this.expectToken(m.DOLLAR),{kind:v.VARIABLE,name:this.parseName(),loc:this.loc(r)}},n.parseSelectionSet=function(){var r=this._lexer.token;return{kind:v.SELECTION_SET,selections:this.many(m.BRACE_L,this.parseSelection,m.BRACE_R),loc:this.loc(r)}},n.parseSelection=function(){return this.peek(m.SPREAD)?this.parseFragment():this.parseField()},n.parseField=function(){var i,o,r=this._lexer.token,a=this.parseName();return this.expectOptionalToken(m.COLON)?(i=a,o=this.parseName()):o=a,{kind:v.FIELD,alias:i,name:o,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(m.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(r)}},n.parseArguments=function(r){return this.optionalMany(m.PAREN_L,r?this.parseConstArgument:this.parseArgument,m.PAREN_R)},n.parseArgument=function(){var r=this._lexer.token,a=this.parseName();return this.expectToken(m.COLON),{kind:v.ARGUMENT,name:a,value:this.parseValueLiteral(!1),loc:this.loc(r)}},n.parseConstArgument=function(){var r=this._lexer.token;return{kind:v.ARGUMENT,name:this.parseName(),value:(this.expectToken(m.COLON),this.parseValueLiteral(!0)),loc:this.loc(r)}},n.parseFragment=function(){var r=this._lexer.token;this.expectToken(m.SPREAD);var a=this.expectOptionalKeyword("on");return!a&&this.peek(m.NAME)?{kind:v.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(r)}:{kind:v.INLINE_FRAGMENT,typeCondition:a?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(r)}},n.parseFragmentDefinition=function(){var r,a=this._lexer.token;return this.expectKeyword("fragment"),!0===(null===(r=this._options)||void 0===r?void 0:r.experimentalFragmentVariables)?{kind:v.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(a)}:{kind:v.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(a)}},n.parseFragmentName=function(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()},n.parseValueLiteral=function(r){var a=this._lexer.token;switch(a.kind){case m.BRACKET_L:return this.parseList(r);case m.BRACE_L:return this.parseObject(r);case m.INT:return this._lexer.advance(),{kind:v.INT,value:a.value,loc:this.loc(a)};case m.FLOAT:return this._lexer.advance(),{kind:v.FLOAT,value:a.value,loc:this.loc(a)};case m.STRING:case m.BLOCK_STRING:return this.parseStringLiteral();case m.NAME:switch(this._lexer.advance(),a.value){case"true":return{kind:v.BOOLEAN,value:!0,loc:this.loc(a)};case"false":return{kind:v.BOOLEAN,value:!1,loc:this.loc(a)};case"null":return{kind:v.NULL,loc:this.loc(a)};default:return{kind:v.ENUM,value:a.value,loc:this.loc(a)}}case m.DOLLAR:if(!r)return this.parseVariable()}throw this.unexpected()},n.parseStringLiteral=function(){var r=this._lexer.token;return this._lexer.advance(),{kind:v.STRING,value:r.value,block:r.kind===m.BLOCK_STRING,loc:this.loc(r)}},n.parseList=function(r){var a=this,i=this._lexer.token;return{kind:v.LIST,values:this.any(m.BRACKET_L,function(){return a.parseValueLiteral(r)},m.BRACKET_R),loc:this.loc(i)}},n.parseObject=function(r){var a=this,i=this._lexer.token;return{kind:v.OBJECT,fields:this.any(m.BRACE_L,function(){return a.parseObjectField(r)},m.BRACE_R),loc:this.loc(i)}},n.parseObjectField=function(r){var a=this._lexer.token,i=this.parseName();return this.expectToken(m.COLON),{kind:v.OBJECT_FIELD,name:i,value:this.parseValueLiteral(r),loc:this.loc(a)}},n.parseDirectives=function(r){for(var a=[];this.peek(m.AT);)a.push(this.parseDirective(r));return a},n.parseDirective=function(r){var a=this._lexer.token;return this.expectToken(m.AT),{kind:v.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(r),loc:this.loc(a)}},n.parseTypeReference=function(){var a,r=this._lexer.token;return this.expectOptionalToken(m.BRACKET_L)?(a=this.parseTypeReference(),this.expectToken(m.BRACKET_R),a={kind:v.LIST_TYPE,type:a,loc:this.loc(r)}):a=this.parseNamedType(),this.expectOptionalToken(m.BANG)?{kind:v.NON_NULL_TYPE,type:a,loc:this.loc(r)}:a},n.parseNamedType=function(){var r=this._lexer.token;return{kind:v.NAMED_TYPE,name:this.parseName(),loc:this.loc(r)}},n.parseTypeSystemDefinition=function(){var r=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(r.kind===m.NAME)switch(r.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(r)},n.peekDescription=function(){return this.peek(m.STRING)||this.peek(m.BLOCK_STRING)},n.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},n.parseSchemaDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("schema");var i=this.parseDirectives(!0),o=this.many(m.BRACE_L,this.parseOperationTypeDefinition,m.BRACE_R);return{kind:v.SCHEMA_DEFINITION,description:a,directives:i,operationTypes:o,loc:this.loc(r)}},n.parseOperationTypeDefinition=function(){var r=this._lexer.token,a=this.parseOperationType();this.expectToken(m.COLON);var i=this.parseNamedType();return{kind:v.OPERATION_TYPE_DEFINITION,operation:a,type:i,loc:this.loc(r)}},n.parseScalarTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("scalar");var i=this.parseName(),o=this.parseDirectives(!0);return{kind:v.SCALAR_TYPE_DEFINITION,description:a,name:i,directives:o,loc:this.loc(r)}},n.parseObjectTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("type");var i=this.parseName(),o=this.parseImplementsInterfaces(),s=this.parseDirectives(!0),u=this.parseFieldsDefinition();return{kind:v.OBJECT_TYPE_DEFINITION,description:a,name:i,interfaces:o,directives:s,fields:u,loc:this.loc(r)}},n.parseImplementsInterfaces=function(){var r;if(!this.expectOptionalKeyword("implements"))return[];if(!0===(null===(r=this._options)||void 0===r?void 0:r.allowLegacySDLImplementsInterfaces)){var a=[];this.expectOptionalToken(m.AMP);do{a.push(this.parseNamedType())}while(this.expectOptionalToken(m.AMP)||this.peek(m.NAME));return a}return this.delimitedMany(m.AMP,this.parseNamedType)},n.parseFieldsDefinition=function(){var r;return!0===(null===(r=this._options)||void 0===r?void 0:r.allowLegacySDLEmptyFields)&&this.peek(m.BRACE_L)&&this._lexer.lookahead().kind===m.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(m.BRACE_L,this.parseFieldDefinition,m.BRACE_R)},n.parseFieldDefinition=function(){var r=this._lexer.token,a=this.parseDescription(),i=this.parseName(),o=this.parseArgumentDefs();this.expectToken(m.COLON);var s=this.parseTypeReference(),u=this.parseDirectives(!0);return{kind:v.FIELD_DEFINITION,description:a,name:i,arguments:o,type:s,directives:u,loc:this.loc(r)}},n.parseArgumentDefs=function(){return this.optionalMany(m.PAREN_L,this.parseInputValueDef,m.PAREN_R)},n.parseInputValueDef=function(){var r=this._lexer.token,a=this.parseDescription(),i=this.parseName();this.expectToken(m.COLON);var s,o=this.parseTypeReference();this.expectOptionalToken(m.EQUALS)&&(s=this.parseValueLiteral(!0));var u=this.parseDirectives(!0);return{kind:v.INPUT_VALUE_DEFINITION,description:a,name:i,type:o,defaultValue:s,directives:u,loc:this.loc(r)}},n.parseInterfaceTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("interface");var i=this.parseName(),o=this.parseImplementsInterfaces(),s=this.parseDirectives(!0),u=this.parseFieldsDefinition();return{kind:v.INTERFACE_TYPE_DEFINITION,description:a,name:i,interfaces:o,directives:s,fields:u,loc:this.loc(r)}},n.parseUnionTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("union");var i=this.parseName(),o=this.parseDirectives(!0),s=this.parseUnionMemberTypes();return{kind:v.UNION_TYPE_DEFINITION,description:a,name:i,directives:o,types:s,loc:this.loc(r)}},n.parseUnionMemberTypes=function(){return this.expectOptionalToken(m.EQUALS)?this.delimitedMany(m.PIPE,this.parseNamedType):[]},n.parseEnumTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("enum");var i=this.parseName(),o=this.parseDirectives(!0),s=this.parseEnumValuesDefinition();return{kind:v.ENUM_TYPE_DEFINITION,description:a,name:i,directives:o,values:s,loc:this.loc(r)}},n.parseEnumValuesDefinition=function(){return this.optionalMany(m.BRACE_L,this.parseEnumValueDefinition,m.BRACE_R)},n.parseEnumValueDefinition=function(){var r=this._lexer.token,a=this.parseDescription(),i=this.parseName(),o=this.parseDirectives(!0);return{kind:v.ENUM_VALUE_DEFINITION,description:a,name:i,directives:o,loc:this.loc(r)}},n.parseInputObjectTypeDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("input");var i=this.parseName(),o=this.parseDirectives(!0),s=this.parseInputFieldsDefinition();return{kind:v.INPUT_OBJECT_TYPE_DEFINITION,description:a,name:i,directives:o,fields:s,loc:this.loc(r)}},n.parseInputFieldsDefinition=function(){return this.optionalMany(m.BRACE_L,this.parseInputValueDef,m.BRACE_R)},n.parseTypeSystemExtension=function(){var r=this._lexer.lookahead();if(r.kind===m.NAME)switch(r.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(r)},n.parseSchemaExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var a=this.parseDirectives(!0),i=this.optionalMany(m.BRACE_L,this.parseOperationTypeDefinition,m.BRACE_R);if(0===a.length&&0===i.length)throw this.unexpected();return{kind:v.SCHEMA_EXTENSION,directives:a,operationTypes:i,loc:this.loc(r)}},n.parseScalarTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var a=this.parseName(),i=this.parseDirectives(!0);if(0===i.length)throw this.unexpected();return{kind:v.SCALAR_TYPE_EXTENSION,name:a,directives:i,loc:this.loc(r)}},n.parseObjectTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var a=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseDirectives(!0),s=this.parseFieldsDefinition();if(0===i.length&&0===o.length&&0===s.length)throw this.unexpected();return{kind:v.OBJECT_TYPE_EXTENSION,name:a,interfaces:i,directives:o,fields:s,loc:this.loc(r)}},n.parseInterfaceTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var a=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseDirectives(!0),s=this.parseFieldsDefinition();if(0===i.length&&0===o.length&&0===s.length)throw this.unexpected();return{kind:v.INTERFACE_TYPE_EXTENSION,name:a,interfaces:i,directives:o,fields:s,loc:this.loc(r)}},n.parseUnionTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var a=this.parseName(),i=this.parseDirectives(!0),o=this.parseUnionMemberTypes();if(0===i.length&&0===o.length)throw this.unexpected();return{kind:v.UNION_TYPE_EXTENSION,name:a,directives:i,types:o,loc:this.loc(r)}},n.parseEnumTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var a=this.parseName(),i=this.parseDirectives(!0),o=this.parseEnumValuesDefinition();if(0===i.length&&0===o.length)throw this.unexpected();return{kind:v.ENUM_TYPE_EXTENSION,name:a,directives:i,values:o,loc:this.loc(r)}},n.parseInputObjectTypeExtension=function(){var r=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var a=this.parseName(),i=this.parseDirectives(!0),o=this.parseInputFieldsDefinition();if(0===i.length&&0===o.length)throw this.unexpected();return{kind:v.INPUT_OBJECT_TYPE_EXTENSION,name:a,directives:i,fields:o,loc:this.loc(r)}},n.parseDirectiveDefinition=function(){var r=this._lexer.token,a=this.parseDescription();this.expectKeyword("directive"),this.expectToken(m.AT);var i=this.parseName(),o=this.parseArgumentDefs(),s=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var u=this.parseDirectiveLocations();return{kind:v.DIRECTIVE_DEFINITION,description:a,name:i,arguments:o,repeatable:s,locations:u,loc:this.loc(r)}},n.parseDirectiveLocations=function(){return this.delimitedMany(m.PIPE,this.parseDirectiveLocation)},n.parseDirectiveLocation=function(){var r=this._lexer.token,a=this.parseName();if(void 0!==L[a.value])return a;throw this.unexpected(r)},n.loc=function(r){var a;if(!0!==(null===(a=this._options)||void 0===a?void 0:a.noLocation))return new Rt(r,this._lexer.lastToken,this._lexer.source)},n.peek=function(r){return this._lexer.token.kind===r},n.expectToken=function(r){var a=this._lexer.token;if(a.kind===r)return this._lexer.advance(),a;throw fe(this._lexer.source,a.start,"Expected ".concat($r(r),", found ").concat(Mt(a),"."))},n.expectOptionalToken=function(r){var a=this._lexer.token;if(a.kind===r)return this._lexer.advance(),a},n.expectKeyword=function(r){var a=this._lexer.token;if(a.kind!==m.NAME||a.value!==r)throw fe(this._lexer.source,a.start,'Expected "'.concat(r,'", found ').concat(Mt(a),"."));this._lexer.advance()},n.expectOptionalKeyword=function(r){var a=this._lexer.token;return a.kind===m.NAME&&a.value===r&&(this._lexer.advance(),!0)},n.unexpected=function(r){var a=r??this._lexer.token;return fe(this._lexer.source,a.start,"Unexpected ".concat(Mt(a),"."))},n.any=function(r,a,i){this.expectToken(r);for(var o=[];!this.expectOptionalToken(i);)o.push(a.call(this));return o},n.optionalMany=function(r,a,i){if(this.expectOptionalToken(r)){var o=[];do{o.push(a.call(this))}while(!this.expectOptionalToken(i));return o}return[]},n.many=function(r,a,i){this.expectToken(r);var o=[];do{o.push(a.call(this))}while(!this.expectOptionalToken(i));return o},n.delimitedMany=function(r,a){this.expectOptionalToken(r);var i=[];do{i.push(a.call(this))}while(this.expectOptionalToken(r));return i},e}();function Mt(e){var n=e.value;return $r(e.kind)+(null!=n?' "'.concat(n,'"'):"")}function $r(e){return Wr(e)?'"'.concat(e,'"'):e}var Yo={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]},un=Object.freeze({});function We(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Yo,r=void 0,a=Array.isArray(e),i=[e],o=-1,s=[],u=void 0,c=void 0,l=void 0,f=[],p=[],d=e;do{var h=++o===i.length,y=h&&0!==s.length;if(h){if(c=0===p.length?void 0:f[f.length-1],u=l,l=p.pop(),y){if(a)u=u.slice();else{for(var I={},N=0,b=Object.keys(u);N<b.length;N++){var F=b[N];I[F]=u[F]}u=I}for(var V=0,R=0;R<s.length;R++){var Y=s[R][0],X=s[R][1];a&&(Y-=V),a&&null===X?(u.splice(Y,1),V++):u[Y]=X}}o=r.index,i=r.keys,s=r.edits,a=r.inArray,r=r.prev}else{if(c=l?a?o:i[o]:void 0,null==(u=l?l[c]:d))continue;l&&f.push(c)}var ge,K=void 0;if(!Array.isArray(u)){if(!Lt(u))throw new Error("Invalid AST Node: ".concat(T(u),"."));var ue=cn(n,u.kind,h);if(ue){if((K=ue.call(n,u,c,l,f,p))===un)break;if(!1===K){if(!h){f.pop();continue}}else if(void 0!==K&&(s.push([c,K]),!h)){if(!Lt(K)){f.pop();continue}u=K}}}void 0===K&&y&&s.push([c,u]),h?f.pop():(r={inArray:a,index:o,keys:i,edits:s,prev:r},i=(a=Array.isArray(u))?u:null!==(ge=t[u.kind])&&void 0!==ge?ge:[],o=-1,s=[],l&&p.push(l),l=u)}while(void 0!==r);return 0!==s.length&&(d=s[s.length-1][1]),d}function Ct(e){var n=new Array(e.length);return{enter:function(r){for(var a=0;a<e.length;a++)if(null==n[a]){var i=cn(e[a],r.kind,!1);if(i){var o=i.apply(e[a],arguments);if(!1===o)n[a]=r;else if(o===un)n[a]=un;else if(void 0!==o)return o}}},leave:function(r){for(var a=0;a<e.length;a++)if(null==n[a]){var i=cn(e[a],r.kind,!0);if(i){var o=i.apply(e[a],arguments);if(o===un)n[a]=un;else if(void 0!==o&&!1!==o)return o}}else n[a]===r&&(n[a]=null)}}}function cn(e,n,t){var r=e[n];if(r){if(!t&&"function"==typeof r)return r;var a=t?r.leave:r.enter;if("function"==typeof a)return a}else{var i=t?e.leave:e.enter;if(i){if("function"==typeof i)return i;var o=i[n];if("function"==typeof o)return o}}}const ln=Array.prototype.find?function(e,n){return Array.prototype.find.call(e,n)}:function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(n(r))return r}},q=Object.values||function(e){return Object.keys(e).map(function(n){return e[n]})};function Ye(e,n,t){var r,a=e instanceof Error?e:new Error("Unexpected error value: "+T(e));return Array.isArray(a.path)?a:new E(a.message,null!==(r=a.nodes)&&void 0!==r?r:n,a.source,a.positions,t,a)}var Jo=/^[_a-zA-Z][_a-zA-Z0-9]*$/;function Xo(e){var n=Ut(e);if(n)throw n;return e}function Ut(e){return"string"==typeof e||P(0,"Expected name to be a string."),e.length>1&&"_"===e[0]&&"_"===e[1]?new E('Name "'.concat(e,'" must not begin with "__", which is reserved by GraphQL introspection.')):Jo.test(e)?void 0:new E('Names must match /^[_a-zA-Z][_a-zA-Z0-9]*$/ but "'.concat(e,'" does not.'))}const fn=Object.entries||function(e){return Object.keys(e).map(function(n){return[n,e[n]]})};function Se(e,n){return e.reduce(function(t,r){return t[n(r)]=r,t},Object.create(null))}function Ce(e,n){for(var t=Object.create(null),r=0,a=fn(e);r<a.length;r++){var i=a[r],o=i[0];t[o]=n(i[1],o)}return t}function me(e){if(null===Object.getPrototypeOf(e))return e;for(var n=Object.create(null),t=0,r=fn(e);t<r.length;t++){var a=r[t];n[a[0]]=a[1]}return n}function qe(e,n,t){return e.reduce(function(r,a){return r[n(a)]=t(a),r},Object.create(null))}var zo=5;function Ue(e,n){var t="string"==typeof e?[e,n]:[void 0,e],r=t[0],i=" Did you mean ";r&&(i+=r+" ");var o=t[1].map(function(c){return'"'.concat(c,'"')});switch(o.length){case 0:return"";case 1:return i+o[0]+"?";case 2:return i+o[0]+" or "+o[1]+"?"}var s=o.slice(0,zo),u=s.pop();return i+s.join(", ")+", or "+u+"?"}function ea(e){return e}function $n(e,n){for(var t=0,r=0;t<e.length&&r<n.length;){var a=e.charCodeAt(t),i=n.charCodeAt(r);if(et(a)&&et(i)){var o=0;do{++t,o=10*o+a-xt,a=e.charCodeAt(t)}while(et(a)&&o>0);var s=0;do{++r,s=10*s+i-xt,i=n.charCodeAt(r)}while(et(i)&&s>0);if(o<s)return-1;if(o>s)return 1}else{if(a<i)return-1;if(a>i)return 1;++t,++r}}return e.length-n.length}var xt=48,Wo=57;function et(e){return!isNaN(e)&&xt<=e&&e<=Wo}function Ke(e,n){for(var t=Object.create(null),r=new Zo(e),a=Math.floor(.4*e.length)+1,i=0;i<n.length;i++){var o=n[i],s=r.measure(o,a);void 0!==s&&(t[o]=s)}return Object.keys(t).sort(function(u,c){var l=t[u]-t[c];return 0!==l?l:$n(u,c)})}var Zo=function(){function e(t){this._input=t,this._inputLowerCase=t.toLowerCase(),this._inputArray=na(this._inputLowerCase),this._rows=[new Array(t.length+1).fill(0),new Array(t.length+1).fill(0),new Array(t.length+1).fill(0)]}return e.prototype.measure=function(r,a){if(this._input===r)return 0;var i=r.toLowerCase();if(this._inputLowerCase===i)return 1;var o=na(i),s=this._inputArray;if(o.length<s.length){var u=o;o=s,s=u}var c=o.length,l=s.length;if(!(c-l>a)){for(var f=this._rows,p=0;p<=l;p++)f[0][p]=p;for(var d=1;d<=c;d++){for(var h=f[(d-1)%3],y=f[d%3],I=y[0]=d,N=1;N<=l;N++){var F=Math.min(h[N]+1,y[N-1]+1,h[N-1]+(o[d-1]===s[N-1]?0:1));d>1&&N>1&&o[d-1]===s[N-2]&&o[d-2]===s[N-1]&&(F=Math.min(F,f[(d-2)%3][N-2]+1)),F<I&&(I=F),y[N]=F}if(I>a)return}var R=f[c%3][l];return R<=a?R:void 0}},e}();function na(e){for(var n=e.length,t=new Array(n),r=0;r<n;++r)t[r]=e.charCodeAt(r);return t}function H(e){return We(e,{leave:es})}var es={Name:function(n){return n.value},Variable:function(n){return"$"+n.name},Document:function(n){return S(n.definitions,"\n\n")+"\n"},OperationDefinition:function(n){var t=n.operation,r=n.name,a=ne("(",S(n.variableDefinitions,", "),")"),i=S(n.directives," "),o=n.selectionSet;return r||i||a||"query"!==t?S([t,S([r,a]),i,o]," "):o},VariableDefinition:function(n){var i=n.directives;return n.variable+": "+n.type+ne(" = ",n.defaultValue)+ne(" ",S(i," "))},SelectionSet:function(n){return Ae(n.selections)},Field:function(n){var r=n.name,a=n.arguments,i=n.directives,o=n.selectionSet,s=ne("",n.alias,": ")+r,u=s+ne("(",S(a,", "),")");return u.length>80&&(u=s+ne("(\n",nt(S(a,"\n")),"\n)")),S([u,S(i," "),o]," ")},Argument:function(n){return n.name+": "+n.value},FragmentSpread:function(n){return"..."+n.name+ne(" ",S(n.directives," "))},InlineFragment:function(n){var r=n.directives,a=n.selectionSet;return S(["...",ne("on ",n.typeCondition),S(r," "),a]," ")},FragmentDefinition:function(n){var r=n.typeCondition,a=n.variableDefinitions,i=n.directives,o=n.selectionSet;return"fragment ".concat(n.name).concat(ne("(",S(a,", "),")")," ")+"on ".concat(r," ").concat(ne("",S(i," ")," "))+o},IntValue:function(n){return n.value},FloatValue:function(n){return n.value},StringValue:function(n,t){var r=n.value;return n.block?zr(r,"description"===t?"":"  "):JSON.stringify(r)},BooleanValue:function(n){return n.value?"true":"false"},NullValue:function(){return"null"},EnumValue:function(n){return n.value},ListValue:function(n){return"["+S(n.values,", ")+"]"},ObjectValue:function(n){return"{"+S(n.fields,", ")+"}"},ObjectField:function(n){return n.name+": "+n.value},Directive:function(n){return"@"+n.name+ne("(",S(n.arguments,", "),")")},NamedType:function(n){return n.name},ListType:function(n){return"["+n.type+"]"},NonNullType:function(n){return n.type+"!"},SchemaDefinition:we(function(e){var t=e.operationTypes;return S(["schema",S(e.directives," "),Ae(t)]," ")}),OperationTypeDefinition:function(n){return n.operation+": "+n.type},ScalarTypeDefinition:we(function(e){return S(["scalar",e.name,S(e.directives," ")]," ")}),ObjectTypeDefinition:we(function(e){var r=e.directives,a=e.fields;return S(["type",e.name,ne("implements ",S(e.interfaces," & ")),S(r," "),Ae(a)]," ")}),FieldDefinition:we(function(e){var t=e.arguments,r=e.type,a=e.directives;return e.name+(ta(t)?ne("(\n",nt(S(t,"\n")),"\n)"):ne("(",S(t,", "),")"))+": "+r+ne(" ",S(a," "))}),InputValueDefinition:we(function(e){var a=e.directives;return S([e.name+": "+e.type,ne("= ",e.defaultValue),S(a," ")]," ")}),InterfaceTypeDefinition:we(function(e){var r=e.directives,a=e.fields;return S(["interface",e.name,ne("implements ",S(e.interfaces," & ")),S(r," "),Ae(a)]," ")}),UnionTypeDefinition:we(function(e){var r=e.types;return S(["union",e.name,S(e.directives," "),r&&0!==r.length?"= "+S(r," | "):""]," ")}),EnumTypeDefinition:we(function(e){var r=e.values;return S(["enum",e.name,S(e.directives," "),Ae(r)]," ")}),EnumValueDefinition:we(function(e){return S([e.name,S(e.directives," ")]," ")}),InputObjectTypeDefinition:we(function(e){var r=e.fields;return S(["input",e.name,S(e.directives," "),Ae(r)]," ")}),DirectiveDefinition:we(function(e){var t=e.arguments,r=e.repeatable,a=e.locations;return"directive @"+e.name+(ta(t)?ne("(\n",nt(S(t,"\n")),"\n)"):ne("(",S(t,", "),")"))+(r?" repeatable":"")+" on "+S(a," | ")}),SchemaExtension:function(n){var r=n.operationTypes;return S(["extend schema",S(n.directives," "),Ae(r)]," ")},ScalarTypeExtension:function(n){return S(["extend scalar",n.name,S(n.directives," ")]," ")},ObjectTypeExtension:function(n){var a=n.directives,i=n.fields;return S(["extend type",n.name,ne("implements ",S(n.interfaces," & ")),S(a," "),Ae(i)]," ")},InterfaceTypeExtension:function(n){var a=n.directives,i=n.fields;return S(["extend interface",n.name,ne("implements ",S(n.interfaces," & ")),S(a," "),Ae(i)]," ")},UnionTypeExtension:function(n){var a=n.types;return S(["extend union",n.name,S(n.directives," "),a&&0!==a.length?"= "+S(a," | "):""]," ")},EnumTypeExtension:function(n){var a=n.values;return S(["extend enum",n.name,S(n.directives," "),Ae(a)]," ")},InputObjectTypeExtension:function(n){var a=n.fields;return S(["extend input",n.name,S(n.directives," "),Ae(a)]," ")}};function we(e){return function(n){return S([n.description,e(n)],"\n")}}function S(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return null!==(n=e?.filter(function(r){return r}).join(t))&&void 0!==n?n:""}function Ae(e){return ne("{\n",nt(S(e,"\n")),"\n}")}function ne(e,n){return null!=n&&""!==n?e+n+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:""):""}function nt(e){return ne("  ",e.replace(/\n/g,"\n  "))}function ns(e){return-1!==e.indexOf("\n")}function ta(e){return null!=e&&e.some(ns)}function tt(e,n){switch(e.kind){case v.NULL:return null;case v.INT:return parseInt(e.value,10);case v.FLOAT:return parseFloat(e.value);case v.STRING:case v.ENUM:case v.BOOLEAN:return e.value;case v.LIST:return e.values.map(function(t){return tt(t,n)});case v.OBJECT:return qe(e.fields,function(t){return t.name.value},function(t){return tt(t.value,n)});case v.VARIABLE:return n?.[e.name.value]}$(0,"Unexpected value node: "+T(e))}function ra(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function pn(e,n,t){return n&&ra(e.prototype,n),t&&ra(e,t),e}function rt(e){return ye(e)||U(e)||j(e)||se(e)||re(e)||z(e)||W(e)||_(e)}function aa(e){if(!rt(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL type."));return e}function ye(e){return De(e,Re)}function ts(e){if(!ye(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Scalar type."));return e}function U(e){return De(e,Ee)}function ia(e){if(!U(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Object type."));return e}function j(e){return De(e,Ze)}function oa(e){if(!j(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Interface type."));return e}function se(e){return De(e,$e)}function rs(e){if(!se(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Union type."));return e}function re(e){return De(e,xe)}function as(e){if(!re(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Enum type."));return e}function z(e){return De(e,en)}function is(e){if(!z(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Input Object type."));return e}function W(e){return De(e,te)}function os(e){if(!W(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL List type."));return e}function _(e){return De(e,k)}function ss(e){if(!_(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL Non-Null type."));return e}function pe(e){return ye(e)||re(e)||z(e)||bn(e)&&pe(e.ofType)}function us(e){if(!pe(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL input type."));return e}function Je(e){return ye(e)||U(e)||j(e)||se(e)||re(e)||bn(e)&&Je(e.ofType)}function cs(e){if(!Je(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL output type."));return e}function ke(e){return ye(e)||re(e)}function ls(e){if(!ke(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL leaf type."));return e}function _e(e){return U(e)||j(e)||se(e)}function fs(e){if(!_e(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL composite type."));return e}function Ve(e){return j(e)||se(e)}function ps(e){if(!Ve(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL abstract type."));return e}function te(e){if(!(this instanceof te))return new te(e);this.ofType=aa(e)}function k(e){if(!(this instanceof k))return new k(e);this.ofType=jt(e)}function bn(e){return W(e)||_(e)}function vs(e){if(!bn(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL wrapping type."));return e}function sa(e){return rt(e)&&!_(e)}function jt(e){if(!sa(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL nullable type."));return e}function Gt(e){if(e)return _(e)?e.ofType:e}function vn(e){return ye(e)||U(e)||j(e)||se(e)||re(e)||z(e)}function ds(e){if(!vn(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL named type."));return e}function ce(e){if(e){for(var n=e;bn(n);)n=n.ofType;return n}}function at(e){return"function"==typeof e?e():e}function dn(e){return e&&e.length>0?e:void 0}te.prototype.toString=function(){return"["+String(this.ofType)+"]"},te.prototype.toJSON=function(){return this.toString()},Object.defineProperty(te.prototype,Ie,{get:function(){return"GraphQLList"}}),be(te),k.prototype.toString=function(){return String(this.ofType)+"!"},k.prototype.toJSON=function(){return this.toString()},Object.defineProperty(k.prototype,Ie,{get:function(){return"GraphQLNonNull"}}),be(k);var Re=function(){function e(t){var r,a,i,o=null!==(r=t.parseValue)&&void 0!==r?r:ea;this.name=t.name,this.description=t.description,this.specifiedByUrl=t.specifiedByUrl,this.serialize=null!==(a=t.serialize)&&void 0!==a?a:ea,this.parseValue=o,this.parseLiteral=null!==(i=t.parseLiteral)&&void 0!==i?i:function(s,u){return o(tt(s,u))},this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),"string"==typeof t.name||P(0,"Must provide name."),null==t.specifiedByUrl||"string"==typeof t.specifiedByUrl||P(0,"".concat(this.name,' must provide "specifiedByUrl" as a string, ')+"but got: ".concat(T(t.specifiedByUrl),".")),null==t.serialize||"function"==typeof t.serialize||P(0,"".concat(this.name,' must provide "serialize" function. If this custom Scalar is also used as an input type, ensure "parseValue" and "parseLiteral" functions are also provided.')),t.parseLiteral&&("function"==typeof t.parseValue&&"function"==typeof t.parseLiteral||P(0,"".concat(this.name,' must provide both "parseValue" and "parseLiteral" functions.')))}var n=e.prototype;return n.toConfig=function(){var r;return{name:this.name,description:this.description,specifiedByUrl:this.specifiedByUrl,serialize:this.serialize,parseValue:this.parseValue,parseLiteral:this.parseLiteral,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLScalarType"}}]),e}();be(Re);var Ee=function(){function e(t){this.name=t.name,this.description=t.description,this.isTypeOf=t.isTypeOf,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),this._fields=ca.bind(void 0,t),this._interfaces=ua.bind(void 0,t),"string"==typeof t.name||P(0,"Must provide name."),null==t.isTypeOf||"function"==typeof t.isTypeOf||P(0,"".concat(this.name,' must provide "isTypeOf" as a function, ')+"but got: ".concat(T(t.isTypeOf),"."))}var n=e.prototype;return n.getFields=function(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields},n.getInterfaces=function(){return"function"==typeof this._interfaces&&(this._interfaces=this._interfaces()),this._interfaces},n.toConfig=function(){return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:la(this.getFields()),isTypeOf:this.isTypeOf,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes||[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLObjectType"}}]),e}();function ua(e){var n,t=null!==(n=at(e.interfaces))&&void 0!==n?n:[];return Array.isArray(t)||P(0,"".concat(e.name," interfaces must be an Array or a function which returns an Array.")),t}function ca(e){var n=at(e.fields);return hn(n)||P(0,"".concat(e.name," fields must be an object with field names as keys or a function which returns such an object.")),Ce(n,function(t,r){var a;hn(t)||P(0,"".concat(e.name,".").concat(r," field config must be an object.")),!("isDeprecated"in t)||P(0,"".concat(e.name,".").concat(r,' should provide "deprecationReason" instead of "isDeprecated".')),null==t.resolve||"function"==typeof t.resolve||P(0,"".concat(e.name,".").concat(r," field resolver must be a function if ")+"provided, but got: ".concat(T(t.resolve),"."));var i=null!==(a=t.args)&&void 0!==a?a:{};hn(i)||P(0,"".concat(e.name,".").concat(r," args must be an object with argument names as keys."));var o=fn(i).map(function(s){var c=s[1];return{name:s[0],description:c.description,type:c.type,defaultValue:c.defaultValue,deprecationReason:c.deprecationReason,extensions:c.extensions&&me(c.extensions),astNode:c.astNode}});return{name:r,description:t.description,type:t.type,args:o,resolve:t.resolve,subscribe:t.subscribe,isDeprecated:null!=t.deprecationReason,deprecationReason:t.deprecationReason,extensions:t.extensions&&me(t.extensions),astNode:t.astNode}})}function hn(e){return he(e)&&!Array.isArray(e)}function la(e){return Ce(e,function(n){return{description:n.description,type:n.type,args:fa(n.args),resolve:n.resolve,subscribe:n.subscribe,deprecationReason:n.deprecationReason,extensions:n.extensions,astNode:n.astNode}})}function fa(e){return qe(e,function(n){return n.name},function(n){return{description:n.description,type:n.type,defaultValue:n.defaultValue,deprecationReason:n.deprecationReason,extensions:n.extensions,astNode:n.astNode}})}function Xe(e){return _(e.type)&&void 0===e.defaultValue}be(Ee);var Ze=function(){function e(t){this.name=t.name,this.description=t.description,this.resolveType=t.resolveType,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),this._fields=ca.bind(void 0,t),this._interfaces=ua.bind(void 0,t),"string"==typeof t.name||P(0,"Must provide name."),null==t.resolveType||"function"==typeof t.resolveType||P(0,"".concat(this.name,' must provide "resolveType" as a function, ')+"but got: ".concat(T(t.resolveType),"."))}var n=e.prototype;return n.getFields=function(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields},n.getInterfaces=function(){return"function"==typeof this._interfaces&&(this._interfaces=this._interfaces()),this._interfaces},n.toConfig=function(){var r;return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:la(this.getFields()),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLInterfaceType"}}]),e}();be(Ze);var $e=function(){function e(t){this.name=t.name,this.description=t.description,this.resolveType=t.resolveType,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),this._types=hs.bind(void 0,t),"string"==typeof t.name||P(0,"Must provide name."),null==t.resolveType||"function"==typeof t.resolveType||P(0,"".concat(this.name,' must provide "resolveType" as a function, ')+"but got: ".concat(T(t.resolveType),"."))}var n=e.prototype;return n.getTypes=function(){return"function"==typeof this._types&&(this._types=this._types()),this._types},n.toConfig=function(){var r;return{name:this.name,description:this.description,types:this.getTypes(),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLUnionType"}}]),e}();function hs(e){var n=at(e.types);return Array.isArray(n)||P(0,"Must provide Array of types or a function which returns such an array for Union ".concat(e.name,".")),n}be($e);var xe=function(){function e(t){this.name=t.name,this.description=t.description,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),this._values=function ms(e,n){return hn(n)||P(0,"".concat(e," values must be an object with value names as keys.")),fn(n).map(function(t){var r=t[0],a=t[1];return hn(a)||P(0,"".concat(e,".").concat(r,' must refer to an object with a "value" key ')+"representing an internal value but got: ".concat(T(a),".")),!("isDeprecated"in a)||P(0,"".concat(e,".").concat(r,' should provide "deprecationReason" instead of "isDeprecated".')),{name:r,description:a.description,value:void 0!==a.value?a.value:r,isDeprecated:null!=a.deprecationReason,deprecationReason:a.deprecationReason,extensions:a.extensions&&me(a.extensions),astNode:a.astNode}})}(this.name,t.values),this._valueLookup=new Map(this._values.map(function(r){return[r.value,r]})),this._nameLookup=Se(this._values,function(r){return r.name}),"string"==typeof t.name||P(0,"Must provide name.")}var n=e.prototype;return n.getValues=function(){return this._values},n.getValue=function(r){return this._nameLookup[r]},n.serialize=function(r){var a=this._valueLookup.get(r);if(void 0===a)throw new E('Enum "'.concat(this.name,'" cannot represent value: ').concat(T(r)));return a.name},n.parseValue=function(r){if("string"!=typeof r){var a=T(r);throw new E('Enum "'.concat(this.name,'" cannot represent non-string value: ').concat(a,".")+it(this,a))}var i=this.getValue(r);if(null==i)throw new E('Value "'.concat(r,'" does not exist in "').concat(this.name,'" enum.')+it(this,r));return i.value},n.parseLiteral=function(r,a){if(r.kind!==v.ENUM){var i=H(r);throw new E('Enum "'.concat(this.name,'" cannot represent non-enum value: ').concat(i,".")+it(this,i),r)}var o=this.getValue(r.value);if(null==o){var s=H(r);throw new E('Value "'.concat(s,'" does not exist in "').concat(this.name,'" enum.')+it(this,s),r)}return o.value},n.toConfig=function(){var r,a=qe(this.getValues(),function(i){return i.name},function(i){return{description:i.description,value:i.value,deprecationReason:i.deprecationReason,extensions:i.extensions,astNode:i.astNode}});return{name:this.name,description:this.description,values:a,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLEnumType"}}]),e}();function it(e,n){return Ue("the enum value",Ke(n,e.getValues().map(function(a){return a.name})))}be(xe);var en=function(){function e(t){this.name=t.name,this.description=t.description,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=dn(t.extensionASTNodes),this._fields=ys.bind(void 0,t),"string"==typeof t.name||P(0,"Must provide name.")}var n=e.prototype;return n.getFields=function(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields},n.toConfig=function(){var r,a=Ce(this.getFields(),function(i){return{description:i.description,type:i.type,defaultValue:i.defaultValue,deprecationReason:i.deprecationReason,extensions:i.extensions,astNode:i.astNode}});return{name:this.name,description:this.description,fields:a,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[]}},n.toString=function(){return this.name},n.toJSON=function(){return this.toString()},pn(e,[{key:Ie,get:function(){return"GraphQLInputObjectType"}}]),e}();function ys(e){var n=at(e.fields);return hn(n)||P(0,"".concat(e.name," fields must be an object with field names as keys or a function which returns such an object.")),Ce(n,function(t,r){return!("resolve"in t)||P(0,"".concat(e.name,".").concat(r," field has a resolve property, but Input Types cannot define resolvers.")),{name:r,description:t.description,type:t.type,defaultValue:t.defaultValue,deprecationReason:t.deprecationReason,extensions:t.extensions&&me(t.extensions),astNode:t.astNode}})}function ot(e){return _(e.type)&&void 0===e.defaultValue}function st(e,n){return e===n||!!(_(e)&&_(n)||W(e)&&W(n))&&st(e.ofType,n.ofType)}function nn(e,n,t){return n===t||(_(t)?!!_(n)&&nn(e,n.ofType,t.ofType):_(n)?nn(e,n.ofType,t):W(t)?!!W(n)&&nn(e,n.ofType,t.ofType):!W(n)&&Ve(t)&&(j(n)||U(n))&&e.isSubType(t,n))}function Bt(e,n,t){return n===t||(Ve(n)?Ve(t)?e.getPossibleTypes(n).some(function(r){return e.isSubType(t,r)}):e.isSubType(n,t):!!Ve(t)&&e.isSubType(t,n))}be(en);const pa=Array.from||function(e,n,t){if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");var r=e[Mr];if("function"==typeof r){for(var o,a=r.call(e),i=[],s=0;!(o=a.next()).done;++s)if(i.push(n.call(t,o.value,s)),s>9999999)throw new TypeError("Near-infinite iteration.");return i}var u=e.length;if("number"==typeof u&&u>=0&&u%1==0){for(var c=[],l=0;l<u;++l)Object.prototype.hasOwnProperty.call(e,l)&&c.push(n.call(t,e[l],l));return c}return[]},Dn=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)};function ut(e){return(ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Qt(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(l){return l};if(null==e||"object"!==ut(e))return null;if(Array.isArray(e))return e.map(n);var t=e[Mr];if("function"==typeof t){for(var i,r=t.call(e),a=[],o=0;!(i=r.next()).done;++o)a.push(n(i.value,o));return a}var s=e.length;if("number"==typeof s&&s>=0&&s%1==0){for(var u=[],c=0;c<s;++c){if(!Object.prototype.hasOwnProperty.call(e,c))return null;u.push(n(e[String(c)],c))}return u}return null}const ct=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e};var Yt=2147483647,qt=-2147483648,va=new Re({name:"Int",description:"The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.",serialize:function Is(e){var n=Sn(e);if("boolean"==typeof n)return n?1:0;var t=n;if("string"==typeof n&&""!==n&&(t=Number(n)),!ct(t))throw new E("Int cannot represent non-integer value: ".concat(T(n)));if(t>Yt||t<qt)throw new E("Int cannot represent non 32-bit signed integer value: "+T(n));return t},parseValue:function Ns(e){if(!ct(e))throw new E("Int cannot represent non-integer value: ".concat(T(e)));if(e>Yt||e<qt)throw new E("Int cannot represent non 32-bit signed integer value: ".concat(e));return e},parseLiteral:function(n){if(n.kind!==v.INT)throw new E("Int cannot represent non-integer value: ".concat(H(n)),n);var t=parseInt(n.value,10);if(t>Yt||t<qt)throw new E("Int cannot represent non 32-bit signed integer value: ".concat(n.value),n);return t}}),da=new Re({name:"Float",description:"The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).",serialize:function Os(e){var n=Sn(e);if("boolean"==typeof n)return n?1:0;var t=n;if("string"==typeof n&&""!==n&&(t=Number(n)),!Dn(t))throw new E("Float cannot represent non numeric value: ".concat(T(n)));return t},parseValue:function bs(e){if(!Dn(e))throw new E("Float cannot represent non numeric value: ".concat(T(e)));return e},parseLiteral:function(n){if(n.kind!==v.FLOAT&&n.kind!==v.INT)throw new E("Float cannot represent non numeric value: ".concat(H(n)),n);return parseFloat(n.value)}});function Sn(e){if(he(e)){if("function"==typeof e.valueOf){var n=e.valueOf();if(!he(n))return n}if("function"==typeof e.toJSON)return e.toJSON()}return e}var Z=new Re({name:"String",description:"The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.",serialize:function Ds(e){var n=Sn(e);if("string"==typeof n)return n;if("boolean"==typeof n)return n?"true":"false";if(Dn(n))return n.toString();throw new E("String cannot represent value: ".concat(T(e)))},parseValue:function Ss(e){if("string"!=typeof e)throw new E("String cannot represent a non string value: ".concat(T(e)));return e},parseLiteral:function(n){if(n.kind!==v.STRING)throw new E("String cannot represent a non string value: ".concat(H(n)),n);return n.value}}),Te=new Re({name:"Boolean",description:"The `Boolean` scalar type represents `true` or `false`.",serialize:function ws(e){var n=Sn(e);if("boolean"==typeof n)return n;if(Dn(n))return 0!==n;throw new E("Boolean cannot represent a non boolean value: ".concat(T(n)))},parseValue:function As(e){if("boolean"!=typeof e)throw new E("Boolean cannot represent a non boolean value: ".concat(T(e)));return e},parseLiteral:function(n){if(n.kind!==v.BOOLEAN)throw new E("Boolean cannot represent a non boolean value: ".concat(H(n)),n);return n.value}}),Kt=new Re({name:"ID",description:'The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.',serialize:function Rs(e){var n=Sn(e);if("string"==typeof n)return n;if(ct(n))return String(n);throw new E("ID cannot represent value: ".concat(T(e)))},parseValue:function Ls(e){if("string"==typeof e)return e;if(ct(e))return e.toString();throw new E("ID cannot represent value: ".concat(T(e)))},parseLiteral:function(n){if(n.kind!==v.STRING&&n.kind!==v.INT)throw new E("ID cannot represent a non-string and non-integer value: "+H(n),n);return n.value}}),wn=Object.freeze([Z,va,da,Te,Kt]);function lt(e){return wn.some(function(n){return e.name===n.name})}function Pe(e,n){if(_(n)){var t=Pe(e,n.ofType);return t?.kind===v.NULL?null:t}if(null===e)return{kind:v.NULL};if(void 0===e)return null;if(W(n)){var r=n.ofType,a=Qt(e);if(null!=a){for(var i=[],o=0;o<a.length;o++){var u=Pe(a[o],r);null!=u&&i.push(u)}return{kind:v.LIST,values:i}}return Pe(e,r)}if(z(n)){if(!he(e))return null;for(var c=[],l=0,f=q(n.getFields());l<f.length;l++){var p=f[l],d=Pe(e[p.name],p.type);d&&c.push({kind:v.OBJECT_FIELD,name:{kind:v.NAME,value:p.name},value:d})}return{kind:v.OBJECT,fields:c}}if(ke(n)){var h=n.serialize(e);if(null==h)return null;if("boolean"==typeof h)return{kind:v.BOOLEAN,value:h};if("number"==typeof h&&Dn(h)){var y=String(h);return ha.test(y)?{kind:v.INT,value:y}:{kind:v.FLOAT,value:y}}if("string"==typeof h)return re(n)?{kind:v.ENUM,value:h}:n===Kt&&ha.test(h)?{kind:v.INT,value:h}:{kind:v.STRING,value:h};throw new TypeError("Cannot convert value to AST: ".concat(T(h),"."))}$(0,"Unexpected input type: "+T(n))}var ha=/^-?(?:0|[1-9][0-9]*)$/,ft=new Ee({name:"__Schema",description:"A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.",fields:function(){return{description:{type:Z,resolve:function(t){return t.description}},types:{description:"A list of all types supported by this server.",type:new k(new te(new k(Ne))),resolve:function(t){return q(t.getTypeMap())}},queryType:{description:"The type that query operations will be rooted at.",type:new k(Ne),resolve:function(t){return t.getQueryType()}},mutationType:{description:"If this server supports mutation, the type that mutation operations will be rooted at.",type:Ne,resolve:function(t){return t.getMutationType()}},subscriptionType:{description:"If this server support subscription, the type that subscription operations will be rooted at.",type:Ne,resolve:function(t){return t.getSubscriptionType()}},directives:{description:"A list of all directives supported by this server.",type:new k(new te(new k(Jt))),resolve:function(t){return t.getDirectives()}}}}}),Jt=new Ee({name:"__Directive",description:"A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.",fields:function(){return{name:{type:new k(Z),resolve:function(t){return t.name}},description:{type:Z,resolve:function(t){return t.description}},isRepeatable:{type:new k(Te),resolve:function(t){return t.isRepeatable}},locations:{type:new k(new te(new k(Xt))),resolve:function(t){return t.locations}},args:{type:new k(new te(new k(An))),args:{includeDeprecated:{type:Te,defaultValue:!1}},resolve:function(t,r){return r.includeDeprecated?t.args:t.args.filter(function(i){return null==i.deprecationReason})}}}}}),Xt=new xe({name:"__DirectiveLocation",description:"A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.",values:{QUERY:{value:L.QUERY,description:"Location adjacent to a query operation."},MUTATION:{value:L.MUTATION,description:"Location adjacent to a mutation operation."},SUBSCRIPTION:{value:L.SUBSCRIPTION,description:"Location adjacent to a subscription operation."},FIELD:{value:L.FIELD,description:"Location adjacent to a field."},FRAGMENT_DEFINITION:{value:L.FRAGMENT_DEFINITION,description:"Location adjacent to a fragment definition."},FRAGMENT_SPREAD:{value:L.FRAGMENT_SPREAD,description:"Location adjacent to a fragment spread."},INLINE_FRAGMENT:{value:L.INLINE_FRAGMENT,description:"Location adjacent to an inline fragment."},VARIABLE_DEFINITION:{value:L.VARIABLE_DEFINITION,description:"Location adjacent to a variable definition."},SCHEMA:{value:L.SCHEMA,description:"Location adjacent to a schema definition."},SCALAR:{value:L.SCALAR,description:"Location adjacent to a scalar definition."},OBJECT:{value:L.OBJECT,description:"Location adjacent to an object type definition."},FIELD_DEFINITION:{value:L.FIELD_DEFINITION,description:"Location adjacent to a field definition."},ARGUMENT_DEFINITION:{value:L.ARGUMENT_DEFINITION,description:"Location adjacent to an argument definition."},INTERFACE:{value:L.INTERFACE,description:"Location adjacent to an interface definition."},UNION:{value:L.UNION,description:"Location adjacent to a union definition."},ENUM:{value:L.ENUM,description:"Location adjacent to an enum definition."},ENUM_VALUE:{value:L.ENUM_VALUE,description:"Location adjacent to an enum value definition."},INPUT_OBJECT:{value:L.INPUT_OBJECT,description:"Location adjacent to an input object type definition."},INPUT_FIELD_DEFINITION:{value:L.INPUT_FIELD_DEFINITION,description:"Location adjacent to an input object field definition."}}}),Ne=new Ee({name:"__Type",description:"The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByUrl`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.",fields:function(){return{kind:{type:new k(Wt),resolve:function(t){return ye(t)?J.SCALAR:U(t)?J.OBJECT:j(t)?J.INTERFACE:se(t)?J.UNION:re(t)?J.ENUM:z(t)?J.INPUT_OBJECT:W(t)?J.LIST:_(t)?J.NON_NULL:void $(0,'Unexpected type: "'.concat(T(t),'".'))}},name:{type:Z,resolve:function(t){return void 0!==t.name?t.name:void 0}},description:{type:Z,resolve:function(t){return void 0!==t.description?t.description:void 0}},specifiedByUrl:{type:Z,resolve:function(t){return void 0!==t.specifiedByUrl?t.specifiedByUrl:void 0}},fields:{type:new te(new k(Ht)),args:{includeDeprecated:{type:Te,defaultValue:!1}},resolve:function(t,r){var a=r.includeDeprecated;if(U(t)||j(t)){var i=q(t.getFields());return a?i:i.filter(function(o){return null==o.deprecationReason})}}},interfaces:{type:new te(new k(Ne)),resolve:function(t){if(U(t)||j(t))return t.getInterfaces()}},possibleTypes:{type:new te(new k(Ne)),resolve:function(t,r,a,i){var o=i.schema;if(Ve(t))return o.getPossibleTypes(t)}},enumValues:{type:new te(new k(zt)),args:{includeDeprecated:{type:Te,defaultValue:!1}},resolve:function(t,r){var a=r.includeDeprecated;if(re(t)){var i=t.getValues();return a?i:i.filter(function(o){return null==o.deprecationReason})}}},inputFields:{type:new te(new k(An)),args:{includeDeprecated:{type:Te,defaultValue:!1}},resolve:function(t,r){var a=r.includeDeprecated;if(z(t)){var i=q(t.getFields());return a?i:i.filter(function(o){return null==o.deprecationReason})}}},ofType:{type:Ne,resolve:function(t){return void 0!==t.ofType?t.ofType:void 0}}}}}),Ht=new Ee({name:"__Field",description:"Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.",fields:function(){return{name:{type:new k(Z),resolve:function(t){return t.name}},description:{type:Z,resolve:function(t){return t.description}},args:{type:new k(new te(new k(An))),args:{includeDeprecated:{type:Te,defaultValue:!1}},resolve:function(t,r){return r.includeDeprecated?t.args:t.args.filter(function(i){return null==i.deprecationReason})}},type:{type:new k(Ne),resolve:function(t){return t.type}},isDeprecated:{type:new k(Te),resolve:function(t){return null!=t.deprecationReason}},deprecationReason:{type:Z,resolve:function(t){return t.deprecationReason}}}}}),An=new Ee({name:"__InputValue",description:"Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.",fields:function(){return{name:{type:new k(Z),resolve:function(t){return t.name}},description:{type:Z,resolve:function(t){return t.description}},type:{type:new k(Ne),resolve:function(t){return t.type}},defaultValue:{type:Z,description:"A GraphQL-formatted string representing the default value for this input value.",resolve:function(t){var i=Pe(t.defaultValue,t.type);return i?H(i):null}},isDeprecated:{type:new k(Te),resolve:function(t){return null!=t.deprecationReason}},deprecationReason:{type:Z,resolve:function(t){return t.deprecationReason}}}}}),zt=new Ee({name:"__EnumValue",description:"One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.",fields:function(){return{name:{type:new k(Z),resolve:function(t){return t.name}},description:{type:Z,resolve:function(t){return t.description}},isDeprecated:{type:new k(Te),resolve:function(t){return null!=t.deprecationReason}},deprecationReason:{type:Z,resolve:function(t){return t.deprecationReason}}}}}),J=Object.freeze({SCALAR:"SCALAR",OBJECT:"OBJECT",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",INPUT_OBJECT:"INPUT_OBJECT",LIST:"LIST",NON_NULL:"NON_NULL"}),Wt=new xe({name:"__TypeKind",description:"An enum describing what kind of type a given `__Type` is.",values:{SCALAR:{value:J.SCALAR,description:"Indicates this type is a scalar."},OBJECT:{value:J.OBJECT,description:"Indicates this type is an object. `fields` and `interfaces` are valid fields."},INTERFACE:{value:J.INTERFACE,description:"Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields."},UNION:{value:J.UNION,description:"Indicates this type is a union. `possibleTypes` is a valid field."},ENUM:{value:J.ENUM,description:"Indicates this type is an enum. `enumValues` is a valid field."},INPUT_OBJECT:{value:J.INPUT_OBJECT,description:"Indicates this type is an input object. `inputFields` is a valid field."},LIST:{value:J.LIST,description:"Indicates this type is a list. `ofType` is a valid field."},NON_NULL:{value:J.NON_NULL,description:"Indicates this type is a non-null. `ofType` is a valid field."}}}),Rn={name:"__schema",type:new k(ft),description:"Access the current type schema of this server.",args:[],resolve:function(n,t,r,a){return a.schema},isDeprecated:!1,deprecationReason:void 0,extensions:void 0,astNode:void 0},Ln={name:"__type",type:Ne,description:"Request the type information of a single type.",args:[{name:"name",description:void 0,type:new k(Z),defaultValue:void 0,deprecationReason:void 0,extensions:void 0,astNode:void 0}],resolve:function(n,t,r,a){return a.schema.getType(t.name)},isDeprecated:!1,deprecationReason:void 0,extensions:void 0,astNode:void 0},Fn={name:"__typename",type:new k(Z),description:"The name of the current Object type at runtime.",args:[],resolve:function(n,t,r,a){return a.parentType.name},isDeprecated:!1,deprecationReason:void 0,extensions:void 0,astNode:void 0},kn=Object.freeze([ft,Jt,Xt,Ne,Ht,An,zt,Wt]);function tn(e){return kn.some(function(n){return e.name===n.name})}function ma(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function pt(e){return De(e,Le)}function ks(e){if(!pt(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL directive."));return e}var Le=function(){function e(t){var r,a;this.name=t.name,this.description=t.description,this.locations=t.locations,this.isRepeatable=null!==(r=t.isRepeatable)&&void 0!==r&&r,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,t.name||P(0,"Directive must be named."),Array.isArray(t.locations)||P(0,"@".concat(t.name," locations must be an Array."));var i=null!==(a=t.args)&&void 0!==a?a:{};he(i)&&!Array.isArray(i)||P(0,"@".concat(t.name," args must be an object with argument names as keys.")),this.args=fn(i).map(function(o){var u=o[1];return{name:o[0],description:u.description,type:u.type,defaultValue:u.defaultValue,deprecationReason:u.deprecationReason,extensions:u.extensions&&me(u.extensions),astNode:u.astNode}})}var n=e.prototype;return n.toConfig=function(){return{name:this.name,description:this.description,locations:this.locations,args:fa(this.args),isRepeatable:this.isRepeatable,extensions:this.extensions,astNode:this.astNode}},n.toString=function(){return"@"+this.name},n.toJSON=function(){return this.toString()},function Fs(e,n,t){n&&ma(e.prototype,n),t&&ma(e,t)}(e,[{key:Ie,get:function(){return"GraphQLDirective"}}]),e}();be(Le);var Zt=new Le({name:"include",description:"Directs the executor to include this field or fragment only when the `if` argument is true.",locations:[L.FIELD,L.FRAGMENT_SPREAD,L.INLINE_FRAGMENT],args:{if:{type:new k(Te),description:"Included when true."}}}),$t=new Le({name:"skip",description:"Directs the executor to skip this field or fragment when the `if` argument is true.",locations:[L.FIELD,L.FRAGMENT_SPREAD,L.INLINE_FRAGMENT],args:{if:{type:new k(Te),description:"Skipped when true."}}}),er="No longer supported",vt=new Le({name:"deprecated",description:"Marks an element of a GraphQL schema as no longer supported.",locations:[L.FIELD_DEFINITION,L.ARGUMENT_DEFINITION,L.INPUT_FIELD_DEFINITION,L.ENUM_VALUE],args:{reason:{type:Z,description:"Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).",defaultValue:er}}}),nr=new Le({name:"specifiedBy",description:"Exposes a URL that specifies the behaviour of this scalar.",locations:[L.SCALAR],args:{url:{type:new k(Z),description:"The URL that specifies the behaviour of this scalar."}}}),je=Object.freeze([Zt,$t,vt,nr]);function tr(e){return je.some(function(n){return n.name===e.name})}function ya(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ea(e){return De(e,mn)}function rr(e){if(!Ea(e))throw new Error("Expected ".concat(T(e)," to be a GraphQL schema."));return e}var mn=function(){function e(t){var r;this.__validationErrors=!0===t.assumeValid?[]:void 0,he(t)||P(0,"Must provide configuration object."),!t.types||Array.isArray(t.types)||P(0,'"types" must be Array if provided but got: '.concat(T(t.types),".")),!t.directives||Array.isArray(t.directives)||P(0,'"directives" must be Array if provided but got: '+"".concat(T(t.directives),".")),this.description=t.description,this.extensions=t.extensions&&me(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=t.extensionASTNodes,this._queryType=t.query,this._mutationType=t.mutation,this._subscriptionType=t.subscription,this._directives=null!==(r=t.directives)&&void 0!==r?r:je;var a=new Set(t.types);if(null!=t.types)for(var i=0,o=t.types;i<o.length;i++){var s=o[i];a.delete(s),Fe(s,a)}null!=this._queryType&&Fe(this._queryType,a),null!=this._mutationType&&Fe(this._mutationType,a),null!=this._subscriptionType&&Fe(this._subscriptionType,a);for(var u=0,c=this._directives;u<c.length;u++){var l=c[u];if(pt(l))for(var f=0,p=l.args;f<p.length;f++)Fe(p[f].type,a)}Fe(ft,a),this._typeMap=Object.create(null),this._subTypeMap=Object.create(null),this._implementationsMap=Object.create(null);for(var h=0,y=pa(a);h<y.length;h++){var I=y[h];if(null!=I){var N=I.name;if(N||P(0,"One of the provided types for building the Schema is missing a name."),void 0!==this._typeMap[N])throw new Error('Schema must contain uniquely named types but contains multiple types named "'.concat(N,'".'));if(this._typeMap[N]=I,j(I))for(var b=0,F=I.getInterfaces();b<F.length;b++){var V=F[b];if(j(V)){var R=this._implementationsMap[V.name];void 0===R&&(R=this._implementationsMap[V.name]={objects:[],interfaces:[]}),R.interfaces.push(I)}}else if(U(I))for(var Y=0,X=I.getInterfaces();Y<X.length;Y++){var K=X[Y];if(j(K)){var ue=this._implementationsMap[K.name];void 0===ue&&(ue=this._implementationsMap[K.name]={objects:[],interfaces:[]}),ue.objects.push(I)}}}}}var n=e.prototype;return n.getQueryType=function(){return this._queryType},n.getMutationType=function(){return this._mutationType},n.getSubscriptionType=function(){return this._subscriptionType},n.getTypeMap=function(){return this._typeMap},n.getType=function(r){return this.getTypeMap()[r]},n.getPossibleTypes=function(r){return se(r)?r.getTypes():this.getImplementations(r).objects},n.getImplementations=function(r){return this._implementationsMap[r.name]??{objects:[],interfaces:[]}},n.isPossibleType=function(r,a){return this.isSubType(r,a)},n.isSubType=function(r,a){var i=this._subTypeMap[r.name];if(void 0===i){if(i=Object.create(null),se(r))for(var o=0,s=r.getTypes();o<s.length;o++)i[s[o].name]=!0;else{for(var c=this.getImplementations(r),l=0,f=c.objects;l<f.length;l++)i[f[l].name]=!0;for(var d=0,h=c.interfaces;d<h.length;d++)i[h[d].name]=!0}this._subTypeMap[r.name]=i}return void 0!==i[a.name]},n.getDirectives=function(){return this._directives},n.getDirective=function(r){return ln(this.getDirectives(),function(a){return a.name===r})},n.toConfig=function(){var r;return{description:this.description,query:this.getQueryType(),mutation:this.getMutationType(),subscription:this.getSubscriptionType(),types:q(this.getTypeMap()),directives:this.getDirectives().slice(),extensions:this.extensions,astNode:this.astNode,extensionASTNodes:null!==(r=this.extensionASTNodes)&&void 0!==r?r:[],assumeValid:void 0!==this.__validationErrors}},function _s(e,n,t){n&&ya(e.prototype,n),t&&ya(e,t)}(e,[{key:Ie,get:function(){return"GraphQLSchema"}}]),e}();function Fe(e,n){var t=ce(e);if(!n.has(t))if(n.add(t),se(t))for(var r=0,a=t.getTypes();r<a.length;r++)Fe(a[r],n);else if(U(t)||j(t)){for(var o=0,s=t.getInterfaces();o<s.length;o++)Fe(s[o],n);for(var c=0,l=q(t.getFields());c<l.length;c++){var f=l[c];Fe(f.type,n);for(var p=0,d=f.args;p<d.length;p++)Fe(d[p].type,n)}}else if(z(t))for(var y=0,I=q(t.getFields());y<I.length;y++)Fe(I[y].type,n);return n}function ar(e){if(rr(e),e.__validationErrors)return e.__validationErrors;var n=new Vs(e);(function Ps(e){var n=e.schema,t=n.getQueryType();if(t){if(!U(t)){var r;e.reportError("Query root type must be Object type, it cannot be ".concat(T(t),"."),null!==(r=or(n,"query"))&&void 0!==r?r:t.astNode)}}else e.reportError("Query root type must be provided.",n.astNode);var i,a=n.getMutationType();a&&!U(a)&&e.reportError("Mutation root type must be Object type if provided, it cannot be "+"".concat(T(a),"."),null!==(i=or(n,"mutation"))&&void 0!==i?i:a.astNode);var s,o=n.getSubscriptionType();o&&!U(o)&&e.reportError("Subscription root type must be Object type if provided, it cannot be "+"".concat(T(o),"."),null!==(s=or(n,"subscription"))&&void 0!==s?s:o.astNode)})(n),function Ms(e){for(var n=0,t=e.schema.getDirectives();n<t.length;n++){var r=t[n];if(pt(r)){rn(e,r);for(var a=0,i=r.args;a<i.length;a++){var s,o=i[a];rn(e,o),pe(o.type)||e.reportError("The type of @".concat(r.name,"(").concat(o.name,":) must be Input Type ")+"but got: ".concat(T(o.type),"."),o.astNode),Xe(o)&&null!=o.deprecationReason&&e.reportError("Required argument @".concat(r.name,"(").concat(o.name,":) cannot be deprecated."),[ur(o.astNode),null===(s=o.astNode)||void 0===s?void 0:s.type])}}else e.reportError("Expected directive but got: ".concat(T(r),"."),r?.astNode)}}(n),function Cs(e){for(var n=function Qs(e){var n=Object.create(null),t=[],r=Object.create(null);return function a(i){if(!n[i.name]){n[i.name]=!0,r[i.name]=t.length;for(var o=q(i.getFields()),s=0;s<o.length;s++){var u=o[s];if(_(u.type)&&z(u.type.ofType)){var c=u.type.ofType,l=r[c.name];if(t.push(u),void 0===l)a(c);else{var f=t.slice(l),p=f.map(function(d){return d.name}).join(".");e.reportError('Cannot reference Input Object "'.concat(c.name,'" within itself through a series of non-null fields: "').concat(p,'".'),f.map(function(d){return d.astNode}))}t.pop()}}r[i.name]=void 0}}}(e),t=e.schema.getTypeMap(),r=0,a=q(t);r<a.length;r++){var i=a[r];vn(i)?(tn(i)||rn(e,i),U(i)||j(i)?(Ta(e,i),ga(e,i)):se(i)?js(e,i):re(i)?Gs(e,i):z(i)&&(Bs(e,i),n(i))):e.reportError("Expected GraphQL named type but got: ".concat(T(i),"."),i.astNode)}}(n);var t=n.getErrors();return e.__validationErrors=t,t}function ir(e){var n=ar(e);if(0!==n.length)throw new Error(n.map(function(t){return t.message}).join("\n\n"))}var Vs=function(){function e(t){this._errors=[],this.schema=t}var n=e.prototype;return n.reportError=function(r,a){var i=Array.isArray(a)?a.filter(Boolean):a;this.addError(new E(r,i))},n.addError=function(r){this._errors.push(r)},n.getErrors=function(){return this._errors},e}();function or(e,n){for(var t=sr(e,function(i){return i.operationTypes}),r=0;r<t.length;r++){var a=t[r];if(a.operation===n)return a.type}}function rn(e,n){var t=Ut(n.name);t&&e.addError(Ye(t,n.astNode))}function Ta(e,n){var t=q(n.getFields());0===t.length&&e.reportError("Type ".concat(n.name," must define one or more fields."),yn(n));for(var r=0;r<t.length;r++){var i,a=t[r];rn(e,a),Je(a.type)||e.reportError("The type of ".concat(n.name,".").concat(a.name," must be Output Type ")+"but got: ".concat(T(a.type),"."),null===(i=a.astNode)||void 0===i?void 0:i.type);for(var o=0,s=a.args;o<s.length;o++){var l,f,u=s[o],c=u.name;rn(e,u),pe(u.type)||e.reportError("The type of ".concat(n.name,".").concat(a.name,"(").concat(c,":) must be Input ")+"Type but got: ".concat(T(u.type),"."),null===(l=u.astNode)||void 0===l?void 0:l.type),Xe(u)&&null!=u.deprecationReason&&e.reportError("Required argument ".concat(n.name,".").concat(a.name,"(").concat(c,":) cannot be deprecated."),[ur(u.astNode),null===(f=u.astNode)||void 0===f?void 0:f.type])}}}function ga(e,n){for(var t=Object.create(null),r=0,a=n.getInterfaces();r<a.length;r++){var i=a[r];j(i)?n!==i?t[i.name]?e.reportError("Type ".concat(n.name," can only implement ").concat(i.name," once."),_n(n,i)):(t[i.name]=!0,xs(e,n,i),Us(e,n,i)):e.reportError("Type ".concat(n.name," cannot implement itself because it would create a circular reference."),_n(n,i)):e.reportError("Type ".concat(T(n)," must only implement Interface types, ")+"it cannot implement ".concat(T(i),"."),_n(n,i))}}function Us(e,n,t){for(var r=n.getFields(),a=0,i=q(t.getFields());a<i.length;a++){var o=i[a],s=o.name,u=r[s];if(u){var c,l;nn(e.schema,u.type,o.type)||e.reportError("Interface field ".concat(t.name,".").concat(s," expects type ")+"".concat(T(o.type)," but ").concat(n.name,".").concat(s," ")+"is type ".concat(T(u.type),"."),[null===(c=o.astNode)||void 0===c?void 0:c.type,null===(l=u.astNode)||void 0===l?void 0:l.type]);for(var f=function(F,V){var K,ue,R=V[F],Y=R.name,X=ln(u.args,function(ge){return ge.name===Y});if(!X)return e.reportError("Interface field argument ".concat(t.name,".").concat(s,"(").concat(Y,":) expected but ").concat(n.name,".").concat(s," does not provide it."),[R.astNode,u.astNode]),"continue";st(R.type,X.type)||e.reportError("Interface field argument ".concat(t.name,".").concat(s,"(").concat(Y,":) ")+"expects type ".concat(T(R.type)," but ")+"".concat(n.name,".").concat(s,"(").concat(Y,":) is type ")+"".concat(T(X.type),"."),[null===(K=R.astNode)||void 0===K?void 0:K.type,null===(ue=X.astNode)||void 0===ue?void 0:ue.type])},p=0,d=o.args;p<d.length;p++)f(p,d);for(var y=function(F,V){var R=V[F],Y=R.name;!ln(o.args,function(K){return K.name===Y})&&Xe(R)&&e.reportError("Object field ".concat(n.name,".").concat(s," includes required argument ").concat(Y," that is missing from the Interface field ").concat(t.name,".").concat(s,"."),[R.astNode,o.astNode])},I=0,N=u.args;I<N.length;I++)y(I,N)}else e.reportError("Interface field ".concat(t.name,".").concat(s," expected but ").concat(n.name," does not provide it."),[o.astNode].concat(yn(n)))}}function xs(e,n,t){for(var r=n.getInterfaces(),a=0,i=t.getInterfaces();a<i.length;a++){var o=i[a];-1===r.indexOf(o)&&e.reportError(o===n?"Type ".concat(n.name," cannot implement ").concat(t.name," because it would create a circular reference."):"Type ".concat(n.name," must implement ").concat(o.name," because it is implemented by ").concat(t.name,"."),[].concat(_n(t,o),_n(n,t)))}}function js(e,n){var t=n.getTypes();0===t.length&&e.reportError("Union type ".concat(n.name," must define one or more member types."),yn(n));for(var r=Object.create(null),a=0;a<t.length;a++){var i=t[a];r[i.name]?e.reportError("Union type ".concat(n.name," can only include type ").concat(i.name," once."),Ia(n,i.name)):(r[i.name]=!0,U(i)||e.reportError("Union type ".concat(n.name," can only include Object types, ")+"it cannot include ".concat(T(i),"."),Ia(n,String(i))))}}function Gs(e,n){var t=n.getValues();0===t.length&&e.reportError("Enum type ".concat(n.name," must define one or more values."),yn(n));for(var r=0;r<t.length;r++){var a=t[r],i=a.name;rn(e,a),("true"===i||"false"===i||"null"===i)&&e.reportError("Enum type ".concat(n.name," cannot include value: ").concat(i,"."),a.astNode)}}function Bs(e,n){var t=q(n.getFields());0===t.length&&e.reportError("Input Object type ".concat(n.name," must define one or more fields."),yn(n));for(var r=0;r<t.length;r++){var i,o,a=t[r];rn(e,a),pe(a.type)||e.reportError("The type of ".concat(n.name,".").concat(a.name," must be Input Type ")+"but got: ".concat(T(a.type),"."),null===(i=a.astNode)||void 0===i?void 0:i.type),ot(a)&&null!=a.deprecationReason&&e.reportError("Required input field ".concat(n.name,".").concat(a.name," cannot be deprecated."),[ur(a.astNode),null===(o=a.astNode)||void 0===o?void 0:o.type])}}function yn(e){var n=e.astNode,t=e.extensionASTNodes;return n?t?[n].concat(t):[n]:t??[]}function sr(e,n){for(var t=[],r=0,a=yn(e);r<a.length;r++){var i;t=t.concat(null!==(i=n(a[r]))&&void 0!==i?i:[])}return t}function _n(e,n){return sr(e,function(t){return t.interfaces}).filter(function(t){return t.name.value===n.name})}function Ia(e,n){return sr(e,function(t){return t.types}).filter(function(t){return t.name.value===n})}function ur(e){var n;return null==e||null===(n=e.directives)||void 0===n?void 0:n.find(function(t){return t.name.value===vt.name})}function ve(e,n){var t;return n.kind===v.LIST_TYPE?(t=ve(e,n.type))&&new te(t):n.kind===v.NON_NULL_TYPE?(t=ve(e,n.type))&&new k(t):n.kind===v.NAMED_TYPE?e.getType(n.name.value):void $(0,"Unexpected type node: "+T(n))}var cr=function(){function e(t,r,a){this._schema=t,this._typeStack=[],this._parentTypeStack=[],this._inputTypeStack=[],this._fieldDefStack=[],this._defaultValueStack=[],this._directive=null,this._argument=null,this._enumValue=null,this._getFieldDef=r??Ys,a&&(pe(a)&&this._inputTypeStack.push(a),_e(a)&&this._parentTypeStack.push(a),Je(a)&&this._typeStack.push(a))}var n=e.prototype;return n.getType=function(){if(this._typeStack.length>0)return this._typeStack[this._typeStack.length-1]},n.getParentType=function(){if(this._parentTypeStack.length>0)return this._parentTypeStack[this._parentTypeStack.length-1]},n.getInputType=function(){if(this._inputTypeStack.length>0)return this._inputTypeStack[this._inputTypeStack.length-1]},n.getParentInputType=function(){if(this._inputTypeStack.length>1)return this._inputTypeStack[this._inputTypeStack.length-2]},n.getFieldDef=function(){if(this._fieldDefStack.length>0)return this._fieldDefStack[this._fieldDefStack.length-1]},n.getDefaultValue=function(){if(this._defaultValueStack.length>0)return this._defaultValueStack[this._defaultValueStack.length-1]},n.getDirective=function(){return this._directive},n.getArgument=function(){return this._argument},n.getEnumValue=function(){return this._enumValue},n.enter=function(r){var a=this._schema;switch(r.kind){case v.SELECTION_SET:var i=ce(this.getType());this._parentTypeStack.push(_e(i)?i:void 0);break;case v.FIELD:var s,u,o=this.getParentType();o&&(s=this._getFieldDef(a,o,r))&&(u=s.type),this._fieldDefStack.push(s),this._typeStack.push(Je(u)?u:void 0);break;case v.DIRECTIVE:this._directive=a.getDirective(r.name.value);break;case v.OPERATION_DEFINITION:var c;switch(r.operation){case"query":c=a.getQueryType();break;case"mutation":c=a.getMutationType();break;case"subscription":c=a.getSubscriptionType()}this._typeStack.push(U(c)?c:void 0);break;case v.INLINE_FRAGMENT:case v.FRAGMENT_DEFINITION:var l=r.typeCondition,f=l?ve(a,l):ce(this.getType());this._typeStack.push(Je(f)?f:void 0);break;case v.VARIABLE_DEFINITION:var p=ve(a,r.type);this._inputTypeStack.push(pe(p)?p:void 0);break;case v.ARGUMENT:var d,h,y,I=null!==(d=this.getDirective())&&void 0!==d?d:this.getFieldDef();I&&(h=ln(I.args,function(K){return K.name===r.name.value}))&&(y=h.type),this._argument=h,this._defaultValueStack.push(h?h.defaultValue:void 0),this._inputTypeStack.push(pe(y)?y:void 0);break;case v.LIST:var N=Gt(this.getInputType()),b=W(N)?N.ofType:N;this._defaultValueStack.push(void 0),this._inputTypeStack.push(pe(b)?b:void 0);break;case v.OBJECT_FIELD:var V,R,F=ce(this.getInputType());z(F)&&(R=F.getFields()[r.name.value])&&(V=R.type),this._defaultValueStack.push(R?R.defaultValue:void 0),this._inputTypeStack.push(pe(V)?V:void 0);break;case v.ENUM:var X,Y=ce(this.getInputType());re(Y)&&(X=Y.getValue(r.value)),this._enumValue=X}},n.leave=function(r){switch(r.kind){case v.SELECTION_SET:this._parentTypeStack.pop();break;case v.FIELD:this._fieldDefStack.pop(),this._typeStack.pop();break;case v.DIRECTIVE:this._directive=null;break;case v.OPERATION_DEFINITION:case v.INLINE_FRAGMENT:case v.FRAGMENT_DEFINITION:this._typeStack.pop();break;case v.VARIABLE_DEFINITION:this._inputTypeStack.pop();break;case v.ARGUMENT:this._argument=null,this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case v.LIST:case v.OBJECT_FIELD:this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case v.ENUM:this._enumValue=null}},e}();function Ys(e,n,t){var r=t.name.value;return r===Rn.name&&e.getQueryType()===n?Rn:r===Ln.name&&e.getQueryType()===n?Ln:r===Fn.name&&_e(n)?Fn:U(n)||j(n)?n.getFields()[r]:void 0}function lr(e,n){return{enter:function(r){e.enter(r);var a=cn(n,r.kind,!1);if(a){var i=a.apply(n,arguments);return void 0!==i&&(e.leave(r),Lt(i)&&e.enter(i)),i}},leave:function(r){var i,a=cn(n,r.kind,!0);return a&&(i=a.apply(n,arguments)),e.leave(r),i}}}function qs(e){return fr(e)||pr(e)||vr(e)}function fr(e){return e.kind===v.OPERATION_DEFINITION||e.kind===v.FRAGMENT_DEFINITION}function Ks(e){return e.kind===v.FIELD||e.kind===v.FRAGMENT_SPREAD||e.kind===v.INLINE_FRAGMENT}function Js(e){return e.kind===v.VARIABLE||e.kind===v.INT||e.kind===v.FLOAT||e.kind===v.STRING||e.kind===v.BOOLEAN||e.kind===v.NULL||e.kind===v.ENUM||e.kind===v.LIST||e.kind===v.OBJECT}function Xs(e){return e.kind===v.NAMED_TYPE||e.kind===v.LIST_TYPE||e.kind===v.NON_NULL_TYPE}function pr(e){return e.kind===v.SCHEMA_DEFINITION||En(e)||e.kind===v.DIRECTIVE_DEFINITION}function En(e){return e.kind===v.SCALAR_TYPE_DEFINITION||e.kind===v.OBJECT_TYPE_DEFINITION||e.kind===v.INTERFACE_TYPE_DEFINITION||e.kind===v.UNION_TYPE_DEFINITION||e.kind===v.ENUM_TYPE_DEFINITION||e.kind===v.INPUT_OBJECT_TYPE_DEFINITION}function vr(e){return e.kind===v.SCHEMA_EXTENSION||dt(e)}function dt(e){return e.kind===v.SCALAR_TYPE_EXTENSION||e.kind===v.OBJECT_TYPE_EXTENSION||e.kind===v.INTERFACE_TYPE_EXTENSION||e.kind===v.UNION_TYPE_EXTENSION||e.kind===v.ENUM_TYPE_EXTENSION||e.kind===v.INPUT_OBJECT_TYPE_EXTENSION}function Na(e){return{Document:function(t){for(var r=0,a=t.definitions;r<a.length;r++){var i=a[r];fr(i)||e.reportError(new E("The ".concat(i.kind===v.SCHEMA_DEFINITION||i.kind===v.SCHEMA_EXTENSION?"schema":'"'+i.name.value+'"'," definition is not executable."),i))}return!1}}}function Oa(e){var n=Object.create(null);return{OperationDefinition:function(r){var a=r.name;return a&&(n[a.value]?e.reportError(new E('There can be only one operation named "'.concat(a.value,'".'),[n[a.value],a])):n[a.value]=a),!1},FragmentDefinition:function(){return!1}}}function ba(e){var n=0;return{Document:function(r){n=r.definitions.filter(function(a){return a.kind===v.OPERATION_DEFINITION}).length},OperationDefinition:function(r){!r.name&&n>1&&e.reportError(new E("This anonymous operation must be the only defined operation.",r))}}}function Da(e){return{OperationDefinition:function(t){"subscription"===t.operation&&1!==t.selectionSet.selections.length&&e.reportError(new E(t.name?'Subscription "'.concat(t.name.value,'" must select only one top level field.'):"Anonymous Subscription must select only one top level field.",t.selectionSet.selections.slice(1)))}}}function dr(e){for(var n=e.getSchema(),t=n?n.getTypeMap():Object.create(null),r=Object.create(null),a=0,i=e.getDocument().definitions;a<i.length;a++){var o=i[a];En(o)&&(r[o.name.value]=!0)}var s=Object.keys(t).concat(Object.keys(r));return{NamedType:function(c,l,f,p,d){var h=c.name.value;if(!t[h]&&!r[h]){var y,I=null!==(y=d[2])&&void 0!==y?y:f,N=null!=I&&function zs(e){return!Array.isArray(e)&&(pr(e)||vr(e))}(I);if(N&&function Hs(e){return-1!==Sa.indexOf(e)}(h))return;var b=Ke(h,N?Sa.concat(s):s);e.reportError(new E('Unknown type "'.concat(h,'".')+Ue(b),c))}}}}var Sa=[].concat(wn,kn).map(function(e){return e.name});function wa(e){return{InlineFragment:function(t){var r=t.typeCondition;if(r){var a=ve(e.getSchema(),r);if(a&&!_e(a)){var i=H(r);e.reportError(new E('Fragment cannot condition on non composite type "'.concat(i,'".'),r))}}},FragmentDefinition:function(t){var r=ve(e.getSchema(),t.typeCondition);if(r&&!_e(r)){var a=H(t.typeCondition);e.reportError(new E('Fragment "'.concat(t.name.value,'" cannot condition on non composite type "').concat(a,'".'),t.typeCondition))}}}}function Aa(e){return{VariableDefinition:function(t){var r=ve(e.getSchema(),t.type);if(r&&!pe(r)){var a=t.variable.name.value,i=H(t.type);e.reportError(new E('Variable "$'.concat(a,'" cannot be non-input type "').concat(i,'".'),t.type))}}}}function Ra(e){return{Field:function(t){var r=e.getType(),a=t.selectionSet;if(r)if(ke(ce(r))){if(a){var i=t.name.value,o=T(r);e.reportError(new E('Field "'.concat(i,'" must not have a selection since type "').concat(o,'" has no subfields.'),a))}}else if(!a){var s=t.name.value,u=T(r);e.reportError(new E('Field "'.concat(s,'" of type "').concat(u,'" must have a selection of subfields. Did you mean "').concat(s,' { ... }"?'),t))}}}}function La(e){return{Field:function(t){var r=e.getParentType();if(r&&!e.getFieldDef()){var i=e.getSchema(),o=t.name.value,s=Ue("to use an inline fragment on",function Ws(e,n,t){if(!Ve(n))return[];for(var r=new Set,a=Object.create(null),i=0,o=e.getPossibleTypes(n);i<o.length;i++){var s=o[i];if(s.getFields()[t]){r.add(s),a[s.name]=1;for(var u=0,c=s.getInterfaces();u<c.length;u++){var l,f=c[u];f.getFields()[t]&&(r.add(f),a[f.name]=(null!==(l=a[f.name])&&void 0!==l?l:0)+1)}}}return pa(r).sort(function(p,d){var h=a[d.name]-a[p.name];return 0!==h?h:j(p)&&e.isSubType(p,d)?-1:j(d)&&e.isSubType(d,p)?1:$n(p.name,d.name)}).map(function(p){return p.name})}(i,r,o));""===s&&(s=Ue(function Zs(e,n){return U(e)||j(e)?Ke(n,Object.keys(e.getFields())):[]}(r,o))),e.reportError(new E('Cannot query field "'.concat(o,'" on type "').concat(r.name,'".')+s,t))}}}}function Fa(e){var n=Object.create(null);return{OperationDefinition:function(){return!1},FragmentDefinition:function(r){var a=r.name.value;return n[a]?e.reportError(new E('There can be only one fragment named "'.concat(a,'".'),[n[a],r.name])):n[a]=r.name,!1}}}function ka(e){return{FragmentSpread:function(t){var r=t.name.value;e.getFragment(r)||e.reportError(new E('Unknown fragment "'.concat(r,'".'),t.name))}}}function _a(e){var n=[],t=[];return{OperationDefinition:function(a){return n.push(a),!1},FragmentDefinition:function(a){return t.push(a),!1},Document:{leave:function(){for(var a=Object.create(null),i=0;i<n.length;i++)for(var s=0,u=e.getRecursivelyReferencedFragments(n[i]);s<u.length;s++)a[u[s].name.value]=!0;for(var l=0;l<t.length;l++){var f=t[l],p=f.name.value;!0!==a[p]&&e.reportError(new E('Fragment "'.concat(p,'" is never used.'),f))}}}}}function Va(e){return{InlineFragment:function(t){var r=e.getType(),a=e.getParentType();if(_e(r)&&_e(a)&&!Bt(e.getSchema(),r,a)){var i=T(a),o=T(r);e.reportError(new E('Fragment cannot be spread here as objects of type "'.concat(i,'" can never be of type "').concat(o,'".'),t))}},FragmentSpread:function(t){var r=t.name.value,a=function $s(e,n){var t=e.getFragment(n);if(t){var r=ve(e.getSchema(),t.typeCondition);if(_e(r))return r}}(e,r),i=e.getParentType();if(a&&i&&!Bt(e.getSchema(),a,i)){var o=T(i),s=T(a);e.reportError(new E('Fragment "'.concat(r,'" cannot be spread here as objects of type "').concat(o,'" can never be of type "').concat(s,'".'),t))}}}}function Pa(e){var n=Object.create(null),t=[],r=Object.create(null);return{OperationDefinition:function(){return!1},FragmentDefinition:function(o){return a(o),!1}};function a(i){if(!n[i.name.value]){var o=i.name.value;n[o]=!0;var s=e.getFragmentSpreads(i.selectionSet);if(0!==s.length){r[o]=t.length;for(var u=0;u<s.length;u++){var c=s[u],l=c.name.value,f=r[l];if(t.push(c),void 0===f){var p=e.getFragment(l);p&&a(p)}else{var d=t.slice(f),h=d.slice(0,-1).map(function(y){return'"'+y.name.value+'"'}).join(", ");e.reportError(new E('Cannot spread fragment "'.concat(l,'" within itself')+(""!==h?" via ".concat(h,"."):"."),d))}t.pop()}r[o]=void 0}}}}function Ma(e){var n=Object.create(null);return{OperationDefinition:function(){n=Object.create(null)},VariableDefinition:function(r){var a=r.variable.name.value;n[a]?e.reportError(new E('There can be only one variable named "$'.concat(a,'".'),[n[a],r.variable.name])):n[a]=r.variable.name}}}function Ca(e){var n=Object.create(null);return{OperationDefinition:{enter:function(){n=Object.create(null)},leave:function(r){for(var a=e.getRecursiveVariableUsages(r),i=0;i<a.length;i++){var s=a[i].node,u=s.name.value;!0!==n[u]&&e.reportError(new E(r.name?'Variable "$'.concat(u,'" is not defined by operation "').concat(r.name.value,'".'):'Variable "$'.concat(u,'" is not defined.'),[s,r]))}}},VariableDefinition:function(r){n[r.variable.name.value]=!0}}}function Ua(e){var n=[];return{OperationDefinition:{enter:function(){n=[]},leave:function(r){for(var a=Object.create(null),i=e.getRecursiveVariableUsages(r),o=0;o<i.length;o++)a[i[o].node.name.value]=!0;for(var c=0,l=n;c<l.length;c++){var f=l[c],p=f.variable.name.value;!0!==a[p]&&e.reportError(new E(r.name?'Variable "$'.concat(p,'" is never used in operation "').concat(r.name.value,'".'):'Variable "$'.concat(p,'" is never used.'),f))}}},VariableDefinition:function(r){n.push(r)}}}function hr(e){for(var n=Object.create(null),t=e.getSchema(),r=t?t.getDirectives():je,a=0;a<r.length;a++){var i=r[a];n[i.name]=i.locations}for(var o=e.getDocument().definitions,s=0;s<o.length;s++){var u=o[s];u.kind===v.DIRECTIVE_DEFINITION&&(n[u.name.value]=u.locations.map(function(c){return c.value}))}return{Directive:function(l,f,p,d,h){var y=l.name.value,I=n[y];if(I){var N=function eu(e){var n=e[e.length-1];switch(!Array.isArray(n)||$(0),n.kind){case v.OPERATION_DEFINITION:return function nu(e){switch(e){case"query":return L.QUERY;case"mutation":return L.MUTATION;case"subscription":return L.SUBSCRIPTION}$(0,"Unexpected operation: "+T(e))}(n.operation);case v.FIELD:return L.FIELD;case v.FRAGMENT_SPREAD:return L.FRAGMENT_SPREAD;case v.INLINE_FRAGMENT:return L.INLINE_FRAGMENT;case v.FRAGMENT_DEFINITION:return L.FRAGMENT_DEFINITION;case v.VARIABLE_DEFINITION:return L.VARIABLE_DEFINITION;case v.SCHEMA_DEFINITION:case v.SCHEMA_EXTENSION:return L.SCHEMA;case v.SCALAR_TYPE_DEFINITION:case v.SCALAR_TYPE_EXTENSION:return L.SCALAR;case v.OBJECT_TYPE_DEFINITION:case v.OBJECT_TYPE_EXTENSION:return L.OBJECT;case v.FIELD_DEFINITION:return L.FIELD_DEFINITION;case v.INTERFACE_TYPE_DEFINITION:case v.INTERFACE_TYPE_EXTENSION:return L.INTERFACE;case v.UNION_TYPE_DEFINITION:case v.UNION_TYPE_EXTENSION:return L.UNION;case v.ENUM_TYPE_DEFINITION:case v.ENUM_TYPE_EXTENSION:return L.ENUM;case v.ENUM_VALUE_DEFINITION:return L.ENUM_VALUE;case v.INPUT_OBJECT_TYPE_DEFINITION:case v.INPUT_OBJECT_TYPE_EXTENSION:return L.INPUT_OBJECT;case v.INPUT_VALUE_DEFINITION:return e[e.length-3].kind===v.INPUT_OBJECT_TYPE_DEFINITION?L.INPUT_FIELD_DEFINITION:L.ARGUMENT_DEFINITION}}(h);N&&-1===I.indexOf(N)&&e.reportError(new E('Directive "@'.concat(y,'" may not be used on ').concat(N,"."),l))}else e.reportError(new E('Unknown directive "@'.concat(y,'".'),l))}}}function mr(e){for(var n=Object.create(null),t=e.getSchema(),r=t?t.getDirectives():je,a=0;a<r.length;a++){var i=r[a];n[i.name]=!i.isRepeatable}for(var o=e.getDocument().definitions,s=0;s<o.length;s++){var u=o[s];u.kind===v.DIRECTIVE_DEFINITION&&(n[u.name.value]=!u.repeatable)}var c=Object.create(null),l=Object.create(null);return{enter:function(p){if(null!=p.directives){var d;if(p.kind===v.SCHEMA_DEFINITION||p.kind===v.SCHEMA_EXTENSION)d=c;else if(En(p)||dt(p)){var h=p.name.value;void 0===(d=l[h])&&(l[h]=d=Object.create(null))}else d=Object.create(null);for(var y=0,I=p.directives;y<I.length;y++){var N=I[y],b=N.name.value;n[b]&&(d[b]?e.reportError(new E('The directive "@'.concat(b,'" can only be used once at this location.'),[d[b],N])):d[b]=N)}}}}}function xa(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function ja(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?xa(Object(t),!0).forEach(function(r){tu(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):xa(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function tu(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Ga(e){return ja(ja({},Ba(e)),{},{Argument:function(t){var r=e.getArgument(),a=e.getFieldDef(),i=e.getParentType();if(!r&&a&&i){var o=t.name.value,u=Ke(o,a.args.map(function(c){return c.name}));e.reportError(new E('Unknown argument "'.concat(o,'" on field "').concat(i.name,".").concat(a.name,'".')+Ue(u),t))}}})}function Ba(e){for(var n=Object.create(null),t=e.getSchema(),r=t?t.getDirectives():je,a=0;a<r.length;a++){var i=r[a];n[i.name]=i.args.map(function(f){return f.name})}for(var o=e.getDocument().definitions,s=0;s<o.length;s++){var u=o[s];if(u.kind===v.DIRECTIVE_DEFINITION){var c,l=null!==(c=u.arguments)&&void 0!==c?c:[];n[u.name.value]=l.map(function(f){return f.name.value})}}return{Directive:function(p){var d=p.name.value,h=n[d];if(p.arguments&&h)for(var y=0,I=p.arguments;y<I.length;y++){var N=I[y],b=N.name.value;if(-1===h.indexOf(b)){var F=Ke(b,h);e.reportError(new E('Unknown argument "'.concat(b,'" on directive "@').concat(d,'".')+Ue(F),N))}}return!1}}}function yr(e){var n=Object.create(null);return{Field:function(){n=Object.create(null)},Directive:function(){n=Object.create(null)},Argument:function(r){var a=r.name.value;return n[a]?e.reportError(new E('There can be only one argument named "'.concat(a,'".'),[n[a],r.name])):n[a]=r.name,!1}}}function Qa(e){return{ListValue:function(t){if(!W(Gt(e.getParentInputType())))return an(e,t),!1},ObjectValue:function(t){var r=ce(e.getInputType());if(!z(r))return an(e,t),!1;for(var a=Se(t.fields,function(l){return l.name.value}),i=0,o=q(r.getFields());i<o.length;i++){var s=o[i];if(!a[s.name]&&ot(s)){var c=T(s.type);e.reportError(new E('Field "'.concat(r.name,".").concat(s.name,'" of required type "').concat(c,'" was not provided.'),t))}}},ObjectField:function(t){var r=ce(e.getParentInputType());if(!e.getInputType()&&z(r)){var i=Ke(t.name.value,Object.keys(r.getFields()));e.reportError(new E('Field "'.concat(t.name.value,'" is not defined by type "').concat(r.name,'".')+Ue(i),t))}},NullValue:function(t){var r=e.getInputType();_(r)&&e.reportError(new E('Expected value of type "'.concat(T(r),'", found ').concat(H(t),"."),t))},EnumValue:function(t){return an(e,t)},IntValue:function(t){return an(e,t)},FloatValue:function(t){return an(e,t)},StringValue:function(t){return an(e,t)},BooleanValue:function(t){return an(e,t)}}}function an(e,n){var t=e.getInputType();if(t){var r=ce(t);if(!ke(r)){var a=T(t);return void e.reportError(new E('Expected value of type "'.concat(a,'", found ').concat(H(n),"."),n))}try{if(void 0===r.parseLiteral(n,void 0)){var o=T(t);e.reportError(new E('Expected value of type "'.concat(o,'", found ').concat(H(n),"."),n))}}catch(u){var s=T(t);e.reportError(u instanceof E?u:new E('Expected value of type "'.concat(s,'", found ').concat(H(n),"; ")+u.message,n,void 0,void 0,void 0,u))}}}function Ya(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function qa(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Ya(Object(t),!0).forEach(function(r){ru(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ya(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ru(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Ka(e){return qa(qa({},Ja(e)),{},{Field:{leave:function(t){var r,a=e.getFieldDef();if(!a)return!1;for(var o=Se(null!==(r=t.arguments)&&void 0!==r?r:[],function(p){return p.name.value}),s=0,u=a.args;s<u.length;s++){var c=u[s];if(!o[c.name]&&Xe(c)){var f=T(c.type);e.reportError(new E('Field "'.concat(a.name,'" argument "').concat(c.name,'" of type "').concat(f,'" is required, but it was not provided.'),t))}}}}})}function Ja(e){for(var n=Object.create(null),t=e.getSchema(),r=t?t.getDirectives():je,a=0;a<r.length;a++){var i=r[a];n[i.name]=Se(i.args.filter(Xe),function(f){return f.name})}for(var o=e.getDocument().definitions,s=0;s<o.length;s++){var u=o[s];if(u.kind===v.DIRECTIVE_DEFINITION){var c,l=null!==(c=u.arguments)&&void 0!==c?c:[];n[u.name.value]=Se(l.filter(au),function(f){return f.name.value})}}return{Directive:{leave:function(p){var d=p.name.value,h=n[d];if(h)for(var y,N=Se(null!==(y=p.arguments)&&void 0!==y?y:[],function(X){return X.name.value}),b=0,F=Object.keys(h);b<F.length;b++){var V=F[b];if(!N[V]){var R=h[V].type,Y=rt(R)?T(R):H(R);e.reportError(new E('Directive "@'.concat(d,'" argument "').concat(V,'" of type "').concat(Y,'" is required, but it was not provided.'),p))}}}}}}function au(e){return e.type.kind===v.NON_NULL_TYPE&&null==e.defaultValue}function Xa(e){var n=Object.create(null);return{OperationDefinition:{enter:function(){n=Object.create(null)},leave:function(r){for(var a=e.getRecursiveVariableUsages(r),i=0;i<a.length;i++){var o=a[i],s=o.node,u=o.type,c=o.defaultValue,l=s.name.value,f=n[l];if(f&&u){var p=e.getSchema(),d=ve(p,f.type);if(d&&!iu(p,d,f.defaultValue,u,c)){var h=T(d),y=T(u);e.reportError(new E('Variable "$'.concat(l,'" of type "').concat(h,'" used in position expecting type "').concat(y,'".'),[f,s]))}}}}},VariableDefinition:function(r){n[r.variable.name.value]=r}}}function iu(e,n,t,r,a){return _(r)&&!_(n)?(null!=t&&t.kind!==v.NULL||void 0!==a)&&nn(e,n,r.ofType):nn(e,n,r)}function Ha(e){return Array.isArray(e)?e.map(function(n){var r=n[1];return'subfields "'.concat(n[0],'" conflict because ')+Ha(r)}).join(" and "):e}function za(e){var n=new pu,t=new Map;return{SelectionSet:function(a){for(var i=function ou(e,n,t,r,a){var i=[],o=yt(e,n,r,a),s=o[0],u=o[1];if(function uu(e,n,t,r,a){for(var i=0,o=fn(a);i<o.length;i++){var s=o[i],u=s[0],c=s[1];if(c.length>1)for(var l=0;l<c.length;l++)for(var f=l+1;f<c.length;f++){var p=Wa(e,t,r,!1,u,c[l],c[f]);p&&n.push(p)}}}(e,i,n,t,s),0!==u.length)for(var c=0;c<u.length;c++){ht(e,i,n,t,!1,s,u[c]);for(var l=c+1;l<u.length;l++)mt(e,i,n,t,!1,u[c],u[l])}return i}(e,t,n,e.getParentType(),a),o=0;o<i.length;o++){var s=i[o],u=s[0],c=u[0],f=s[1],p=s[2],d=Ha(u[1]);e.reportError(new E('Fields "'.concat(c,'" conflict because ').concat(d,". Use different aliases on the fields to fetch both if this was intentional."),f.concat(p)))}}}}function ht(e,n,t,r,a,i,o){var s=e.getFragment(o);if(s){var u=gr(e,t,s),c=u[0],l=u[1];if(i!==c){Er(e,n,t,r,a,i,c);for(var f=0;f<l.length;f++)ht(e,n,t,r,a,i,l[f])}}}function mt(e,n,t,r,a,i,o){if(i!==o&&!r.has(i,o,a)){r.add(i,o,a);var s=e.getFragment(i),u=e.getFragment(o);if(s&&u){var c=gr(e,t,s),l=c[0],f=c[1],p=gr(e,t,u),h=p[1];Er(e,n,t,r,a,l,p[0]);for(var y=0;y<h.length;y++)mt(e,n,t,r,a,i,h[y]);for(var I=0;I<f.length;I++)mt(e,n,t,r,a,f[I],o)}}}function Er(e,n,t,r,a,i,o){for(var s=0,u=Object.keys(i);s<u.length;s++){var c=u[s],l=o[c];if(l)for(var f=i[c],p=0;p<f.length;p++)for(var d=0;d<l.length;d++){var h=Wa(e,t,r,a,c,f[p],l[d]);h&&n.push(h)}}}function Wa(e,n,t,r,a,i,o){var s=i[0],u=i[1],c=i[2],l=o[0],f=o[1],p=o[2],d=r||s!==l&&U(s)&&U(l);if(!d){var h,y,I=u.name.value,N=f.name.value;if(I!==N)return[[a,'"'.concat(I,'" and "').concat(N,'" are different fields')],[u],[f]];if(!function cu(e,n){return e.length===n.length&&e.every(function(t){var r=ln(n,function(a){return a.name.value===t.name.value});return!!r&&function lu(e,n){return H(e)===H(n)}(t.value,r.value)})}(null!==(h=u.arguments)&&void 0!==h?h:[],null!==(y=f.arguments)&&void 0!==y?y:[]))return[[a,"they have differing arguments"],[u],[f]]}var V=c?.type,R=p?.type;if(V&&R&&Tr(V,R))return[[a,'they return conflicting types "'.concat(T(V),'" and "').concat(T(R),'"')],[u],[f]];var Y=u.selectionSet,X=f.selectionSet;if(Y&&X){var K=function su(e,n,t,r,a,i,o,s){var u=[],c=yt(e,n,a,i),l=c[0],f=c[1],p=yt(e,n,o,s),d=p[0],h=p[1];if(Er(e,u,n,t,r,l,d),0!==h.length)for(var y=0;y<h.length;y++)ht(e,u,n,t,r,l,h[y]);if(0!==f.length)for(var I=0;I<f.length;I++)ht(e,u,n,t,r,d,f[I]);for(var N=0;N<f.length;N++)for(var b=0;b<h.length;b++)mt(e,u,n,t,r,f[N],h[b]);return u}(e,n,t,d,ce(V),Y,ce(R),X);return function fu(e,n,t,r){if(e.length>0)return[[n,e.map(function(a){return a[0]})],e.reduce(function(a,i){return a.concat(i[1])},[t]),e.reduce(function(a,i){return a.concat(i[2])},[r])]}(K,a,u,f)}}function Tr(e,n){return W(e)?!W(n)||Tr(e.ofType,n.ofType):!!W(n)||(_(e)?!_(n)||Tr(e.ofType,n.ofType):!!_(n)||!(!ke(e)&&!ke(n))&&e!==n)}function yt(e,n,t,r){var a=n.get(r);if(!a){var i=Object.create(null),o=Object.create(null);Za(e,t,r,i,o),a=[i,Object.keys(o)],n.set(r,a)}return a}function gr(e,n,t){var r=n.get(t.selectionSet);if(r)return r;var a=ve(e.getSchema(),t.typeCondition);return yt(e,n,a,t.selectionSet)}function Za(e,n,t,r,a){for(var i=0,o=t.selections;i<o.length;i++){var s=o[i];switch(s.kind){case v.FIELD:var u=s.name.value,c=void 0;(U(n)||j(n))&&(c=n.getFields()[u]);var l=s.alias?s.alias.value:u;r[l]||(r[l]=[]),r[l].push([n,s,c]);break;case v.FRAGMENT_SPREAD:a[s.name.value]=!0;break;case v.INLINE_FRAGMENT:var f=s.typeCondition,p=f?ve(e.getSchema(),f):n;Za(e,p,s.selectionSet,r,a)}}}var He,pu=function(){function e(){this._data=Object.create(null)}var n=e.prototype;return n.has=function(r,a,i){var o=this._data[r],s=o&&o[a];return void 0!==s&&(!1!==i||!1===s)},n.add=function(r,a,i){this._pairSetAdd(r,a,i),this._pairSetAdd(a,r,i)},n._pairSetAdd=function(r,a,i){var o=this._data[r];o||(this._data[r]=o=Object.create(null)),o[a]=i},e}();function Ir(e){var n=[],t=Object.create(null);return{ObjectValue:{enter:function(){n.push(t),t=Object.create(null)},leave:function(){t=n.pop()}},ObjectField:function(a){var i=a.name.value;t[i]?e.reportError(new E('There can be only one input field named "'.concat(i,'".'),[t[i],a.name])):t[i]=a.name}}}function $a(e){var n,t,r,a=e.getSchema(),i=null!==(n=null!==(t=null!==(r=a?.astNode)&&void 0!==r?r:a?.getQueryType())&&void 0!==t?t:a?.getMutationType())&&void 0!==n?n:a?.getSubscriptionType(),o=0;return{SchemaDefinition:function(u){i?e.reportError(new E("Cannot define a new schema within a schema extension.",u)):(o>0&&e.reportError(new E("Must provide only one schema definition.",u)),++o)}}}function ei(e){var n=e.getSchema(),t=Object.create(null),r=n?{query:n.getQueryType(),mutation:n.getMutationType(),subscription:n.getSubscriptionType()}:{};return{SchemaDefinition:a,SchemaExtension:a};function a(i){for(var o,s=null!==(o=i.operationTypes)&&void 0!==o?o:[],u=0;u<s.length;u++){var c=s[u],l=c.operation,f=t[l];r[l]?e.reportError(new E("Type for ".concat(l," already defined in the schema. It cannot be redefined."),c)):f?e.reportError(new E("There can be only one ".concat(l," type in schema."),[f,c])):t[l]=c}return!1}}function ni(e){var n=Object.create(null),t=e.getSchema();return{ScalarTypeDefinition:r,ObjectTypeDefinition:r,InterfaceTypeDefinition:r,UnionTypeDefinition:r,EnumTypeDefinition:r,InputObjectTypeDefinition:r};function r(a){var i=a.name.value;if(null==t||!t.getType(i))return n[i]?e.reportError(new E('There can be only one type named "'.concat(i,'".'),[n[i],a.name])):n[i]=a.name,!1;e.reportError(new E('Type "'.concat(i,'" already exists in the schema. It cannot also be defined in this type definition.'),a.name))}}function ti(e){var n=e.getSchema(),t=n?n.getTypeMap():Object.create(null),r=Object.create(null);return{EnumTypeDefinition:a,EnumTypeExtension:a};function a(i){var o,s=i.name.value;r[s]||(r[s]=Object.create(null));for(var u=null!==(o=i.values)&&void 0!==o?o:[],c=r[s],l=0;l<u.length;l++){var f=u[l],p=f.name.value,d=t[s];re(d)&&d.getValue(p)?e.reportError(new E('Enum value "'.concat(s,".").concat(p,'" already exists in the schema. It cannot also be defined in this type extension.'),f.name)):c[p]?e.reportError(new E('Enum value "'.concat(s,".").concat(p,'" can only be defined once.'),[c[p],f.name])):c[p]=f.name}return!1}}function ri(e){var n=e.getSchema(),t=n?n.getTypeMap():Object.create(null),r=Object.create(null);return{InputObjectTypeDefinition:a,InputObjectTypeExtension:a,InterfaceTypeDefinition:a,InterfaceTypeExtension:a,ObjectTypeDefinition:a,ObjectTypeExtension:a};function a(i){var o,s=i.name.value;r[s]||(r[s]=Object.create(null));for(var u=null!==(o=i.fields)&&void 0!==o?o:[],c=r[s],l=0;l<u.length;l++){var f=u[l],p=f.name.value;vu(t[s],p)?e.reportError(new E('Field "'.concat(s,".").concat(p,'" already exists in the schema. It cannot also be defined in this type extension.'),f.name)):c[p]?e.reportError(new E('Field "'.concat(s,".").concat(p,'" can only be defined once.'),[c[p],f.name])):c[p]=f.name}return!1}}function vu(e,n){return!!(U(e)||j(e)||z(e))&&null!=e.getFields()[n]}function ai(e){var n=Object.create(null),t=e.getSchema();return{DirectiveDefinition:function(a){var i=a.name.value;if(null==t||!t.getDirective(i))return n[i]?e.reportError(new E('There can be only one directive named "@'.concat(i,'".'),[n[i],a.name])):n[i]=a.name,!1;e.reportError(new E('Directive "@'.concat(i,'" already exists in the schema. It cannot be redefined.'),a.name))}}}function Tn(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function ii(e){for(var n=e.getSchema(),t=Object.create(null),r=0,a=e.getDocument().definitions;r<a.length;r++){var i=a[r];En(i)&&(t[i.name.value]=i)}return{ScalarTypeExtension:o,ObjectTypeExtension:o,InterfaceTypeExtension:o,UnionTypeExtension:o,EnumTypeExtension:o,InputObjectTypeExtension:o};function o(s){var f,u=s.name.value,c=t[u],l=n?.getType(u);if(c?f=du[c.kind]:l&&(f=function hu(e){return ye(e)?v.SCALAR_TYPE_EXTENSION:U(e)?v.OBJECT_TYPE_EXTENSION:j(e)?v.INTERFACE_TYPE_EXTENSION:se(e)?v.UNION_TYPE_EXTENSION:re(e)?v.ENUM_TYPE_EXTENSION:z(e)?v.INPUT_OBJECT_TYPE_EXTENSION:void $(0,"Unexpected type: "+T(e))}(l)),f){if(f!==s.kind){var p=function mu(e){switch(e){case v.SCALAR_TYPE_EXTENSION:return"scalar";case v.OBJECT_TYPE_EXTENSION:return"object";case v.INTERFACE_TYPE_EXTENSION:return"interface";case v.UNION_TYPE_EXTENSION:return"union";case v.ENUM_TYPE_EXTENSION:return"enum";case v.INPUT_OBJECT_TYPE_EXTENSION:return"input object"}$(0,"Unexpected kind: "+T(e))}(s.kind);e.reportError(new E("Cannot extend non-".concat(p,' type "').concat(u,'".'),c?[c,s]:s))}}else{var d=Object.keys(t);n&&(d=d.concat(Object.keys(n.getTypeMap())));var h=Ke(u,d);e.reportError(new E('Cannot extend type "'.concat(u,'" because it is not defined.')+Ue(h),s.name))}}}var du=(Tn(He={},v.SCALAR_TYPE_DEFINITION,v.SCALAR_TYPE_EXTENSION),Tn(He,v.OBJECT_TYPE_DEFINITION,v.OBJECT_TYPE_EXTENSION),Tn(He,v.INTERFACE_TYPE_DEFINITION,v.INTERFACE_TYPE_EXTENSION),Tn(He,v.UNION_TYPE_DEFINITION,v.UNION_TYPE_EXTENSION),Tn(He,v.ENUM_TYPE_DEFINITION,v.ENUM_TYPE_EXTENSION),Tn(He,v.INPUT_OBJECT_TYPE_DEFINITION,v.INPUT_OBJECT_TYPE_EXTENSION),He),oi=Object.freeze([Na,Oa,ba,Da,dr,wa,Aa,Ra,La,Fa,ka,_a,Va,Pa,Ma,Ca,Ua,hr,mr,Ga,yr,Qa,Ka,Xa,za,Ir]),yu=Object.freeze([$a,ei,ni,ti,ri,ai,dr,hr,mr,ii,Ba,yr,Ir,Ja]);function si(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n}var ui=function(){function e(t,r){this._ast=t,this._fragments=void 0,this._fragmentSpreads=new Map,this._recursivelyReferencedFragments=new Map,this._onError=r}var n=e.prototype;return n.reportError=function(r){this._onError(r)},n.getDocument=function(){return this._ast},n.getFragment=function(r){var a=this._fragments;return a||(this._fragments=a=this.getDocument().definitions.reduce(function(i,o){return o.kind===v.FRAGMENT_DEFINITION&&(i[o.name.value]=o),i},Object.create(null))),a[r]},n.getFragmentSpreads=function(r){var a=this._fragmentSpreads.get(r);if(!a){a=[];for(var i=[r];0!==i.length;)for(var s=0,u=i.pop().selections;s<u.length;s++){var c=u[s];c.kind===v.FRAGMENT_SPREAD?a.push(c):c.selectionSet&&i.push(c.selectionSet)}this._fragmentSpreads.set(r,a)}return a},n.getRecursivelyReferencedFragments=function(r){var a=this._recursivelyReferencedFragments.get(r);if(!a){a=[];for(var i=Object.create(null),o=[r.selectionSet];0!==o.length;)for(var s=o.pop(),u=0,c=this.getFragmentSpreads(s);u<c.length;u++){var f=c[u].name.value;if(!0!==i[f]){i[f]=!0;var p=this.getFragment(f);p&&(a.push(p),o.push(p.selectionSet))}}this._recursivelyReferencedFragments.set(r,a)}return a},e}(),Eu=function(e){function n(r,a,i){var o;return(o=e.call(this,r,i)||this)._schema=a,o}return si(n,e),n.prototype.getSchema=function(){return this._schema},n}(ui),ci=function(e){function n(r,a,i,o){var s;return(s=e.call(this,a,o)||this)._schema=r,s._typeInfo=i,s._variableUsages=new Map,s._recursiveVariableUsages=new Map,s}si(n,e);var t=n.prototype;return t.getSchema=function(){return this._schema},t.getVariableUsages=function(a){var i=this._variableUsages.get(a);if(!i){var o=[],s=new cr(this._schema);We(a,lr(s,{VariableDefinition:function(){return!1},Variable:function(c){o.push({node:c,type:s.getInputType(),defaultValue:s.getDefaultValue()})}})),this._variableUsages.set(a,i=o)}return i},t.getRecursiveVariableUsages=function(a){var i=this._recursiveVariableUsages.get(a);if(!i){i=this.getVariableUsages(a);for(var o=0,s=this.getRecursivelyReferencedFragments(a);o<s.length;o++)i=i.concat(this.getVariableUsages(s[o]));this._recursiveVariableUsages.set(a,i)}return i},t.getType=function(){return this._typeInfo.getType()},t.getParentType=function(){return this._typeInfo.getParentType()},t.getInputType=function(){return this._typeInfo.getInputType()},t.getParentInputType=function(){return this._typeInfo.getParentInputType()},t.getFieldDef=function(){return this._typeInfo.getFieldDef()},t.getDirective=function(){return this._typeInfo.getDirective()},t.getArgument=function(){return this._typeInfo.getArgument()},t.getEnumValue=function(){return this._typeInfo.getEnumValue()},n}(ui);function Nr(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:oi,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new cr(e),a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{maxErrors:void 0};n||P(0,"Must provide document."),ir(e);var i=Object.freeze({}),o=[],s=new ci(e,n,r,function(c){if(null!=a.maxErrors&&o.length>=a.maxErrors)throw o.push(new E("Too many validation errors, error limit reached. Validation aborted.")),i;o.push(c)}),u=Ct(t.map(function(c){return c(s)}));try{We(n,lr(r,u))}catch(c){if(c!==i)throw c}return o}function li(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:yu,r=[],a=new Eu(e,n,function(o){r.push(o)});return We(e,Ct(t.map(function(o){return o(a)}))),r}function gn(e,n,t){return{prev:e,key:n,typename:t}}function de(e){for(var n=[],t=e;t;)n.push(t.key),t=t.prev;return n.reverse()}function Or(e,n){if("query"===n.operation){var t=e.getQueryType();if(!t)throw new E("Schema does not define the required query root type.",n);return t}if("mutation"===n.operation){var r=e.getMutationType();if(!r)throw new E("Schema is not configured for mutations.",n);return r}if("subscription"===n.operation){var a=e.getSubscriptionType();if(!a)throw new E("Schema is not configured for subscriptions.",n);return a}throw new E("Can only have query, mutation and subscription operations.",n)}function fi(e){return e.map(function(n){return"number"==typeof n?"["+n.toString()+"]":"."+n}).join("")}function Me(e,n,t){if(e){if(e.kind===v.VARIABLE){var r=e.name.value;if(null==t||void 0===t[r])return;var a=t[r];return null===a&&_(n)?void 0:a}if(_(n))return e.kind===v.NULL?void 0:Me(e,n.ofType,t);if(e.kind===v.NULL)return null;if(W(n)){var i=n.ofType;if(e.kind===v.LIST){for(var o=[],s=0,u=e.values;s<u.length;s++){var c=u[s];if(pi(c,t)){if(_(i))return;o.push(null)}else{var l=Me(c,i,t);if(void 0===l)return;o.push(l)}}return o}var f=Me(e,i,t);return void 0===f?void 0:[f]}if(z(n)){if(e.kind!==v.OBJECT)return;for(var p=Object.create(null),d=Se(e.fields,function(V){return V.name.value}),h=0,y=q(n.getFields());h<y.length;h++){var I=y[h],N=d[I.name];if(N&&!pi(N.value,t)){var b=Me(N.value,I.type,t);if(void 0===b)return;p[I.name]=b}else if(void 0!==I.defaultValue)p[I.name]=I.defaultValue;else if(_(I.type))return}return p}if(ke(n)){var F;try{F=n.parseLiteral(e,t)}catch{return}return void 0===F?void 0:F}$(0,"Unexpected input type: "+T(n))}}function pi(e,n){return e.kind===v.VARIABLE&&(null==n||void 0===n[e.name.value])}function vi(e,n){return Vn(e,n,arguments.length>2&&void 0!==arguments[2]?arguments[2]:bu)}function bu(e,n,t){var r="Invalid value "+T(n);throw e.length>0&&(r+=' at "value'.concat(fi(e),'"')),t.message=r+": "+t.message,t}function Vn(e,n,t,r){if(_(n))return null!=e?Vn(e,n.ofType,t,r):void t(de(r),e,new E('Expected non-nullable type "'.concat(T(n),'" not to be null.')));if(null==e)return null;if(W(n)){var a=n.ofType;return Qt(e,function(b,F){var V=gn(r,F,void 0);return Vn(b,a,t,V)})??[Vn(e,a,t,r)]}if(z(n)){if(!he(e))return void t(de(r),e,new E('Expected type "'.concat(n.name,'" to be an object.')));for(var o={},s=n.getFields(),u=0,c=q(s);u<c.length;u++){var l=c[u],f=e[l.name];if(void 0!==f)o[l.name]=Vn(f,l.type,t,gn(r,l.name,n.name));else if(void 0!==l.defaultValue)o[l.name]=l.defaultValue;else if(_(l.type)){var p=T(l.type);t(de(r),e,new E('Field "'.concat(l.name,'" of required type "').concat(p,'" was not provided.')))}}for(var d=0,h=Object.keys(e);d<h.length;d++){var y=h[d];if(!s[y]){var I=Ke(y,Object.keys(n.getFields()));t(de(r),e,new E('Field "'.concat(y,'" is not defined by type "').concat(n.name,'".')+Ue(I)))}}return o}if(ke(n)){var N;try{N=n.parseValue(e)}catch(b){return void t(de(r),e,b instanceof E?b:new E('Expected type "'.concat(n.name,'". ')+b.message,void 0,void 0,void 0,void 0,b))}return void 0===N&&t(de(r),e,new E('Expected type "'.concat(n.name,'".'))),N}$(0,"Unexpected input type: "+T(n))}function br(e,n,t){for(var r,a={},o=Se(null!==(r=n.arguments)&&void 0!==r?r:[],function(N){return N.name.value}),s=0,u=e.args;s<u.length;s++){var c=u[s],l=c.name,f=c.type,p=o[l];if(p){var d=p.value,h=d.kind===v.NULL;if(d.kind===v.VARIABLE){var y=d.name.value;if(null==t||!di(t,y)){if(void 0!==c.defaultValue)a[l]=c.defaultValue;else if(_(f))throw new E('Argument "'.concat(l,'" of required type "').concat(T(f),'" ')+'was provided the variable "$'.concat(y,'" which was not provided a runtime value.'),d);continue}h=null==t[y]}if(h&&_(f))throw new E('Argument "'.concat(l,'" of non-null type "').concat(T(f),'" ')+"must not be null.",d);var I=Me(d,f,t);if(void 0===I)throw new E('Argument "'.concat(l,'" has invalid value ').concat(H(d),"."),d);a[l]=I}else if(void 0!==c.defaultValue)a[l]=c.defaultValue;else if(_(f))throw new E('Argument "'.concat(l,'" of required type "').concat(T(f),'" ')+"was not provided.",n)}return a}function Pn(e,n,t){var r=n.directives&&ln(n.directives,function(a){return a.name.value===e.name});if(r)return br(e,r,t)}function di(e,n){return Object.prototype.hasOwnProperty.call(e,n)}function Dr(e,n,t,r,a,i,o,s){return Sr(1===arguments.length?e:{schema:e,document:n,rootValue:t,contextValue:r,variableValues:a,operationName:i,fieldResolver:o,typeResolver:s})}function hi(e){var n=Sr(e);if(le(n))throw new Error("GraphQL execution failed to complete synchronously.");return n}function Sr(e){var n=e.schema,t=e.document,r=e.rootValue,a=e.contextValue,i=e.variableValues,o=e.operationName,s=e.fieldResolver,u=e.typeResolver;yi(n,t,i);var c=Ei(n,t,r,a,i,o,s,u);if(Array.isArray(c))return{errors:c};var l=function wu(e,n,t){var r=Or(e.schema,n),a=Mn(e,r,n.selectionSet,Object.create(null),Object.create(null)),i=void 0;try{var o="mutation"===n.operation?function Au(e,n,t,r,a){return function Nu(e,n,t){return e.reduce(function(r,a){return le(r)?r.then(function(i){return n(i,a)}):n(r,a)},t)}(Object.keys(a),function(i,o){var s=a[o],u=gn(r,o,n.name),c=Ii(e,n,t,s,u);return void 0===c?i:le(c)?c.then(function(l){return i[o]=l,i}):(i[o]=c,i)},Object.create(null))}(e,r,t,i,a):Ti(e,r,t,i,a);return le(o)?o.then(void 0,function(s){return e.errors.push(s),Promise.resolve(null)}):o}catch(s){return e.errors.push(s),null}}(c,c.operation,r);return mi(c,l)}function mi(e,n){return le(n)?n.then(function(t){return mi(e,t)}):0===e.errors.length?{data:n}:{errors:e.errors,data:n}}function yi(e,n,t){n||P(0,"Must provide document."),ir(e),null==t||he(t)||P(0,"Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.")}function Ei(e,n,t,r,a,i,o,s){for(var u,c,l,f=Object.create(null),p=0,d=n.definitions;p<d.length;p++){var h=d[p];switch(h.kind){case v.OPERATION_DEFINITION:if(null==i){if(void 0!==l)return[new E("Must provide operation name if query contains multiple operations.")];l=h}else(null===(u=h.name)||void 0===u?void 0:u.value)===i&&(l=h);break;case v.FRAGMENT_DEFINITION:f[h.name.value]=h}}if(!l)return null!=i?[new E('Unknown operation named "'.concat(i,'".'))]:[new E("Must provide an operation.")];var I=function Du(e,n,t,r){var a=[],i=r?.maxErrors;try{var o=function Su(e,n,t,r){for(var a={},i=function(c){var l=n[c],f=l.variable.name.value,p=ve(e,l.type);if(!pe(p)){var d=H(l.type);return r(new E('Variable "$'.concat(f,'" expected value of type "').concat(d,'" which cannot be used as an input type.'),l.type)),"continue"}if(!di(t,f)){if(l.defaultValue)a[f]=Me(l.defaultValue,p);else if(_(p)){var h=T(p);r(new E('Variable "$'.concat(f,'" of required type "').concat(h,'" was not provided.'),l))}return"continue"}var y=t[f];if(null===y&&_(p)){var I=T(p);return r(new E('Variable "$'.concat(f,'" of non-null type "').concat(I,'" must not be null.'),l)),"continue"}a[f]=vi(y,p,function(N,b,F){var V='Variable "$'.concat(f,'" got invalid value ')+T(b);N.length>0&&(V+=' at "'.concat(f).concat(fi(N),'"')),r(new E(V+"; "+F.message,l,void 0,void 0,void 0,F.originalError))})},o=0;o<n.length;o++)i(o);return a}(e,n,t,function(s){if(null!=i&&a.length>=i)throw new E("Too many errors processing variables, error limit reached. Execution aborted.");a.push(s)});if(0===a.length)return{coerced:o}}catch(s){a.push(s)}return{errors:a}}(e,null!==(c=l.variableDefinitions)&&void 0!==c?c:[],a??{},{maxErrors:50});return I.errors?I.errors:{schema:e,fragments:f,rootValue:t,contextValue:r,operation:l,variableValues:I.coerced,fieldResolver:o??wi,typeResolver:s??Si,errors:[]}}function Ti(e,n,t,r,a){for(var i=Object.create(null),o=!1,s=0,u=Object.keys(a);s<u.length;s++){var c=u[s],p=Ii(e,n,t,a[c],gn(r,c,n.name));void 0!==p&&(i[c]=p,le(p)&&(o=!0))}return o?function Ou(e){var n=Object.keys(e),t=n.map(function(r){return e[r]});return Promise.all(t).then(function(r){return r.reduce(function(a,i,o){return a[n[o]]=i,a},Object.create(null))})}(i):i}function Mn(e,n,t,r,a){for(var i=0,o=t.selections;i<o.length;i++){var s=o[i];switch(s.kind){case v.FIELD:if(!wr(e,s))continue;var u=Ru(s);r[u]||(r[u]=[]),r[u].push(s);break;case v.INLINE_FRAGMENT:if(!wr(e,s)||!gi(e,s,n))continue;Mn(e,n,s.selectionSet,r,a);break;case v.FRAGMENT_SPREAD:var c=s.name.value;if(a[c]||!wr(e,s))continue;a[c]=!0;var l=e.fragments[c];if(!l||!gi(e,l,n))continue;Mn(e,n,l.selectionSet,r,a)}}return r}function wr(e,n){return!0!==Pn($t,n,e.variableValues)?.if&&!1!==Pn(Zt,n,e.variableValues)?.if}function gi(e,n,t){var r=n.typeCondition;if(!r)return!0;var a=ve(e.schema,r);return a===t||!!Ve(a)&&e.schema.isSubType(a,t)}function Ru(e){return e.alias?e.alias.value:e.name.value}function Ii(e,n,t,r,a){var i,u=Ai(e.schema,n,r[0].name.value);if(u){var c=u.type,l=null!==(i=u.resolve)&&void 0!==i?i:e.fieldResolver,f=Ni(e,u,r,n,a);try{var y,h=l(t,br(u,r[0],e.variableValues),e.contextValue,f);return y=le(h)?h.then(function(N){return Cn(e,c,r,f,a,N)}):Cn(e,c,r,f,a,h),le(y)?y.then(void 0,function(N){return Et(Ye(N,r,de(a)),c,e)}):y}catch(N){return Et(Ye(N,r,de(a)),c,e)}}}function Ni(e,n,t,r,a){return{fieldName:n.name,fieldNodes:t,returnType:n.type,parentType:r,path:a,schema:e.schema,fragments:e.fragments,rootValue:e.rootValue,operation:e.operation,variableValues:e.variableValues}}function Et(e,n,t){if(_(n))throw e;return t.errors.push(e),null}function Cn(e,n,t,r,a,i){if(i instanceof Error)throw i;if(_(n)){var o=Cn(e,n.ofType,t,r,a,i);if(null===o)throw new Error("Cannot return null for non-nullable field ".concat(r.parentType.name,".").concat(r.fieldName,"."));return o}return null==i?null:W(n)?function Lu(e,n,t,r,a,i){var o=n.ofType,s=!1,u=Qt(i,function(c,l){var f=gn(a,l,void 0);try{var p;return p=le(c)?c.then(function(h){return Cn(e,o,t,r,f,h)}):Cn(e,o,t,r,f,c),le(p)?(s=!0,p.then(void 0,function(h){return Et(Ye(h,t,de(f)),o,e)})):p}catch(h){return Et(Ye(h,t,de(f)),o,e)}});if(null==u)throw new E('Expected Iterable, but did not find one for field "'.concat(r.parentType.name,".").concat(r.fieldName,'".'));return s?Promise.all(u):u}(e,n,t,r,a,i):ke(n)?function Fu(e,n){var t=e.serialize(n);if(void 0===t)throw new Error('Expected a value of type "'.concat(T(e),'" but ')+"received: ".concat(T(n)));return t}(n,i):Ve(n)?function ku(e,n,t,r,a,i){var o,c=(null!==(o=n.resolveType)&&void 0!==o?o:e.typeResolver)(i,e.contextValue,r,n);return le(c)?c.then(function(l){return Ar(e,Oi(l,e,n,t,r,i),t,r,a,i)}):Ar(e,Oi(c,e,n,t,r,i),t,r,a,i)}(e,n,t,r,a,i):U(n)?Ar(e,n,t,r,a,i):void $(0,"Cannot complete value of unexpected output type: "+T(n))}function Oi(e,n,t,r,a,i){if(null==e)throw new E('Abstract type "'.concat(t.name,'" must resolve to an Object type at runtime for field "').concat(a.parentType.name,".").concat(a.fieldName,'". Either the "').concat(t.name,'" type should provide a "resolveType" function or each possible type should provide an "isTypeOf" function.'),r);var o=vn(e)?e.name:e;if("string"!=typeof o)throw new E('Abstract type "'.concat(t.name,'" must resolve to an Object type at runtime for field "').concat(a.parentType.name,".").concat(a.fieldName,'" with ')+"value ".concat(T(i),', received "').concat(T(e),'".'));var s=n.schema.getType(o);if(null==s)throw new E('Abstract type "'.concat(t.name,'" was resolve to a type "').concat(o,'" that does not exist inside schema.'),r);if(!U(s))throw new E('Abstract type "'.concat(t.name,'" was resolve to a non-object type "').concat(o,'".'),r);if(!n.schema.isSubType(t,s))throw new E('Runtime Object type "'.concat(s.name,'" is not a possible type for "').concat(t.name,'".'),r);return s}function Ar(e,n,t,r,a,i){if(n.isTypeOf){var o=n.isTypeOf(i,e.contextValue,r);if(le(o))return o.then(function(s){if(!s)throw bi(n,i,t);return Di(e,n,t,a,i)});if(!o)throw bi(n,i,t)}return Di(e,n,t,a,i)}function bi(e,n,t){return new E('Expected value of type "'.concat(e.name,'" but got: ').concat(T(n),"."),t)}function Di(e,n,t,r,a){return Ti(e,n,a,r,_u(e,n,t))}var _u=function Iu(e){var n;return function(r,a,i){n||(n=new WeakMap);var s,o=n.get(r);if(o){if(s=o.get(a)){var u=s.get(i);if(void 0!==u)return u}}else n.set(r,o=new WeakMap);s||o.set(a,s=new WeakMap);var c=e(r,a,i);return s.set(i,c),c}}(function Vu(e,n,t){for(var r=Object.create(null),a=Object.create(null),i=0;i<t.length;i++){var o=t[i];o.selectionSet&&(r=Mn(e,n,o.selectionSet,r,a))}return r}),Si=function(n,t,r,a){if(he(n)&&"string"==typeof n.__typename)return n.__typename;for(var i=r.schema.getPossibleTypes(a),o=[],s=0;s<i.length;s++){var u=i[s];if(u.isTypeOf){var c=u.isTypeOf(n,t,r);if(le(c))o[s]=c;else if(c)return u.name}}return o.length?Promise.all(o).then(function(l){for(var f=0;f<l.length;f++)if(l[f])return i[f].name}):void 0},wi=function(n,t,r,a){if(he(n)||"function"==typeof n){var i=n[a.fieldName];return"function"==typeof i?n[a.fieldName](t,r,a):i}};function Ai(e,n,t){return t===Rn.name&&e.getQueryType()===n?Rn:t===Ln.name&&e.getQueryType()===n?Ln:t===Fn.name?Fn:n.getFields()[t]}function Pu(e,n,t,r,a,i,o,s){var u=arguments;return new Promise(function(c){return c(Tt(1===u.length?e:{schema:e,source:n,rootValue:t,contextValue:r,variableValues:a,operationName:i,fieldResolver:o,typeResolver:s}))})}function Mu(e,n,t,r,a,i,o,s){var u=Tt(1===arguments.length?e:{schema:e,source:n,rootValue:t,contextValue:r,variableValues:a,operationName:i,fieldResolver:o,typeResolver:s});if(le(u))throw new Error("GraphQL execution failed to complete synchronously.");return u}function Tt(e){var l,n=e.schema,t=e.source,r=e.rootValue,a=e.contextValue,i=e.variableValues,o=e.operationName,s=e.fieldResolver,u=e.typeResolver,c=ar(n);if(c.length>0)return{errors:c};try{l=Zn(t)}catch(p){return{errors:[p]}}var f=Nr(n,l);return f.length>0?{errors:f}:Dr({schema:n,document:l,rootValue:r,contextValue:a,variableValues:i,operationName:o,fieldResolver:s,typeResolver:u})}function Ri(e){return"function"==typeof e?.[Dt]}function Li(e,n){return new Promise(function(t){return t(n(e))})}function Fi(e){return{value:e,done:!1}}function xu(e,n,t,r,a,i,o,s){return function _i(e){var n=e.schema,t=e.document,a=e.contextValue,i=e.variableValues,o=e.operationName,s=e.fieldResolver,c=Vi(n,t,e.rootValue,a,i,o,e.subscribeFieldResolver),l=function(p){return Dr({schema:n,document:t,rootValue:p,contextValue:a,variableValues:i,operationName:o,fieldResolver:s})};return c.then(function(f){return Ri(f)?function Uu(e,n,t){var i,o,u,a=e[Dt].call(e);function s(l){return l.done?l:Li(l.value,n).then(Fi,o)}if("function"==typeof a.return&&(i=a.return,o=function(f){var p=function(){return Promise.reject(f)};return i.call(a).then(p,p)}),t){var c=t;u=function(f){return Li(f,c).then(Fi,o)}}return function Cu(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}({next:function(){return a.next().then(s,u)},return:function(){return i?i.call(a).then(s,u):Promise.resolve({value:void 0,done:!0})},throw:function(f){return"function"==typeof a.throw?a.throw(f).then(s,u):Promise.reject(f).catch(o)}},Dt,function(){return this})}(f,l,ki):f})}(1===arguments.length?e:{schema:e,document:n,rootValue:t,contextValue:r,variableValues:a,operationName:i,fieldResolver:o,subscribeFieldResolver:s})}function ki(e){if(e instanceof E)return{errors:[e]};throw e}function Vi(e,n,t,r,a,i,o){return yi(e,n,a),new Promise(function(s){var u=Ei(e,n,t,r,a,i,o);s(Array.isArray(u)?{errors:u}:function ju(e){var n=e.schema,t=e.operation,r=e.variableValues,a=e.rootValue,i=Or(n,t),o=Mn(e,i,t.selectionSet,Object.create(null),Object.create(null)),u=Object.keys(o)[0],c=o[u],f=c[0].name.value,p=Ai(n,i,f);if(!p)throw new E('The subscription field "'.concat(f,'" is not defined.'),c);var d=gn(void 0,u,i.name),h=Ni(e,p,c,i,d);return new Promise(function(y){var I,N=br(p,c[0],r);y((null!==(I=p.subscribe)&&void 0!==I?I:e.fieldResolver)(a,N,e.contextValue,h))}).then(function(y){if(y instanceof Error)throw Ye(y,c,de(d));if(!Ri(y))throw new Error("Subscription field must return Async Iterable. "+"Received: ".concat(T(y),"."));return y},function(y){throw Ye(y,c,de(d))})}(u))}).catch(ki)}function Pi(e){return{Field:function(t){var r=e.getFieldDef(),a=r?.deprecationReason;if(r&&null!=a){var i=e.getParentType();null!=i||$(0),e.reportError(new E("The field ".concat(i.name,".").concat(r.name," is deprecated. ").concat(a),t))}},Argument:function(t){var r=e.getArgument(),a=r?.deprecationReason;if(r&&null!=a){var i=e.getDirective();if(null!=i)e.reportError(new E('Directive "@'.concat(i.name,'" argument "').concat(r.name,'" is deprecated. ').concat(a),t));else{var o=e.getParentType(),s=e.getFieldDef();null!=o&&null!=s||$(0),e.reportError(new E('Field "'.concat(o.name,".").concat(s.name,'" argument "').concat(r.name,'" is deprecated. ').concat(a),t))}}},ObjectField:function(t){var r=ce(e.getParentInputType());if(z(r)){var a=r.getFields()[t.name.value],i=a?.deprecationReason;null!=i&&e.reportError(new E("The input field ".concat(r.name,".").concat(a.name," is deprecated. ").concat(i),t))}},EnumValue:function(t){var r=e.getEnumValue(),a=r?.deprecationReason;if(r&&null!=a){var i=ce(e.getInputType());null!=i||$(0),e.reportError(new E('The enum value "'.concat(i.name,".").concat(r.name,'" is deprecated. ').concat(a),t))}}}}function Gu(e){return{Field:function(t){var r=ce(e.getType());r&&tn(r)&&e.reportError(new E('GraphQL introspection has been disabled, but the requested query contained the field "'.concat(t.name.value,'".'),t))}}}function Bu(e){var n;e||P(0,"Received null or undefined error.");var t=null!==(n=e.message)&&void 0!==n?n:"An unknown error occurred.",r=e.locations,a=e.path,i=e.extensions;return i&&Object.keys(i).length>0?{message:t,locations:r,path:a,extensions:i}:{message:t,locations:r,path:a}}function Mi(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Yu(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Ci(e){var n=function Qu(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Mi(Object(t),!0).forEach(function(r){Yu(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Mi(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}({descriptions:!0,specifiedByUrl:!1,directiveIsRepeatable:!1,schemaDescription:!1,inputValueDeprecation:!1},e),t=n.descriptions?"description":"",r=n.specifiedByUrl?"specifiedByUrl":"",a=n.directiveIsRepeatable?"isRepeatable":"";function o(s){return n.inputValueDeprecation?s:""}return"\n    query IntrospectionQuery {\n      __schema {\n        ".concat(n.schemaDescription?t:"","\n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ").concat(t,"\n          ").concat(a,"\n          locations\n          args").concat(o("(includeDeprecated: true)")," {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ").concat(t,"\n      ").concat(r,"\n      fields(includeDeprecated: true) {\n        name\n        ").concat(t,"\n        args").concat(o("(includeDeprecated: true)")," {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields").concat(o("(includeDeprecated: true)")," {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ").concat(t,"\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ").concat(t,"\n      type { ...TypeRef }\n      defaultValue\n      ").concat(o("isDeprecated"),"\n      ").concat(o("deprecationReason"),"\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  ")}function qu(e,n){for(var t=null,r=0,a=e.definitions;r<a.length;r++){var o,i=a[r];if(i.kind===v.OPERATION_DEFINITION)if(null==n){if(t)return null;t=i}else if((null===(o=i.name)||void 0===o?void 0:o.value)===n)return i}return t}function Ui(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Ju(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Xu(e,n){var t=function Ku(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Ui(Object(t),!0).forEach(function(r){Ju(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ui(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}({specifiedByUrl:!0,directiveIsRepeatable:!0,schemaDescription:!0,inputValueDeprecation:!0},n),a=hi({schema:e,document:Zn(Ci(t))});return!a.errors&&a.data||$(0),a.data}function Hu(e,n){he(e)&&he(e.__schema)||P(0,'Invalid or incomplete introspection result. Ensure that you are passing "data" property of introspection response and no "errors" was returned alongside: '.concat(T(e),"."));for(var t=e.__schema,r=qe(t.types,function(O){return O.name},function(O){return function y(O){if(null!=O&&null!=O.name&&null!=O.kind)switch(O.kind){case J.SCALAR:return function I(O){return new Re({name:O.name,description:O.description,specifiedByUrl:O.specifiedByUrl})}(O);case J.OBJECT:return function b(O){return new Ee({name:O.name,description:O.description,interfaces:function(){return N(O)},fields:function(){return X(O)}})}(O);case J.INTERFACE:return function F(O){return new Ze({name:O.name,description:O.description,interfaces:function(){return N(O)},fields:function(){return X(O)}})}(O);case J.UNION:return function V(O){if(!O.possibleTypes){var M=T(O);throw new Error("Introspection result missing possibleTypes: ".concat(M,"."))}return new $e({name:O.name,description:O.description,types:function(){return O.possibleTypes.map(d)}})}(O);case J.ENUM:return function R(O){if(!O.enumValues){var M=T(O);throw new Error("Introspection result missing enumValues: ".concat(M,"."))}return new xe({name:O.name,description:O.description,values:qe(O.enumValues,function(oe){return oe.name},function(oe){return{description:oe.description,deprecationReason:oe.deprecationReason}})})}(O);case J.INPUT_OBJECT:return function Y(O){if(!O.inputFields){var M=T(O);throw new Error("Introspection result missing inputFields: ".concat(M,"."))}return new en({name:O.name,description:O.description,fields:function(){return ue(O.inputFields)}})}(O)}var M=T(O);throw new Error("Invalid or incomplete introspection result. Ensure that a full introspection query is used in order to build a client schema: ".concat(M,"."))}(O)}),a=0,i=[].concat(wn,kn);a<i.length;a++){var o=i[a];r[o.name]&&(r[o.name]=o)}var s=t.queryType?d(t.queryType):null,u=t.mutationType?d(t.mutationType):null,c=t.subscriptionType?d(t.subscriptionType):null,l=t.directives?t.directives.map(function Qe(O){if(!O.args){var M=T(O);throw new Error("Introspection result missing directive args: ".concat(M,"."))}if(!O.locations){var oe=T(O);throw new Error("Introspection result missing directive locations: ".concat(oe,"."))}return new Le({name:O.name,description:O.description,isRepeatable:O.isRepeatable,locations:O.locations.slice(),args:ue(O.args)})}):[];return new mn({description:t.description,query:s,mutation:u,subscription:c,types:q(r),directives:l,assumeValid:n?.assumeValid});function f(O){if(O.kind===J.LIST){var M=O.ofType;if(!M)throw new Error("Decorated type deeper than introspection query.");return new te(f(M))}if(O.kind===J.NON_NULL){var oe=O.ofType;if(!oe)throw new Error("Decorated type deeper than introspection query.");return new k(jt(f(oe)))}return p(O)}function p(O){var M=O.name;if(!M)throw new Error("Unknown type reference: ".concat(T(O),"."));var oe=r[M];if(!oe)throw new Error("Invalid or incomplete schema, unknown type: ".concat(M,". Ensure that a full introspection query is used in order to build a client schema."));return oe}function d(O){return ia(p(O))}function h(O){return oa(p(O))}function N(O){if(null===O.interfaces&&O.kind===J.INTERFACE)return[];if(!O.interfaces){var M=T(O);throw new Error("Introspection result missing interfaces: ".concat(M,"."))}return O.interfaces.map(h)}function X(O){if(!O.fields)throw new Error("Introspection result missing fields: ".concat(T(O),"."));return qe(O.fields,function(M){return M.name},K)}function K(O){var M=f(O.type);if(!Je(M)){var oe=T(M);throw new Error("Introspection must provide output type for fields, but received: ".concat(oe,"."))}if(!O.args){var sn=T(O);throw new Error("Introspection result missing field args: ".concat(sn,"."))}return{description:O.description,deprecationReason:O.deprecationReason,type:M,args:ue(O.args)}}function ue(O){return qe(O,function(M){return M.name},ge)}function ge(O){var M=f(O.type);if(!pe(M)){var oe=T(M);throw new Error("Introspection must provide input type for arguments, but received: ".concat(oe,"."))}var sn=null!=O.defaultValue?Me(Zr(O.defaultValue),M):void 0;return{description:O.description,type:M,defaultValue:sn,deprecationReason:O.deprecationReason}}}function xi(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function x(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?xi(Object(t),!0).forEach(function(r){zu(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):xi(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function zu(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Wu(e,n,t){rr(e),null!=n&&n.kind===v.DOCUMENT||P(0,"Must provide valid Document AST."),!0!==t?.assumeValid&&!0!==t?.assumeValidSDL&&function gu(e,n){var t=li(e,n);if(0!==t.length)throw new Error(t.map(function(r){return r.message}).join("\n\n"))}(n,e);var r=e.toConfig(),a=ji(r,n,t);return r===a?e:new mn(a)}function ji(e,n,t){for(var r,a,i,o,l,s=[],u=Object.create(null),c=[],f=[],p=0,d=n.definitions;p<d.length;p++){var h=d[p];if(h.kind===v.SCHEMA_DEFINITION)l=h;else if(h.kind===v.SCHEMA_EXTENSION)f.push(h);else if(En(h))s.push(h);else if(dt(h)){var y=h.name.value,I=u[y];u[y]=I?I.concat([h]):[h]}else h.kind===v.DIRECTIVE_DEFINITION&&c.push(h)}if(0===Object.keys(u).length&&0===s.length&&0===c.length&&0===f.length&&null==l)return e;for(var N=Object.create(null),b=0,F=e.types;b<F.length;b++){var V=F[b];N[V.name]=tn(g=V)||lt(g)?g:ye(g)?function Lc(g){for(var A,D=g.toConfig(),w=null!==(A=u[D.name])&&void 0!==A?A:[],C=D.specifiedByUrl,G=0;G<w.length;G++){var B;C=null!==(B=Bi(w[G]))&&void 0!==B?B:C}return new Re(x(x({},D),{},{specifiedByUrl:C,extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):U(g)?function Fc(g){var A,D=g.toConfig(),w=null!==(A=u[D.name])&&void 0!==A?A:[];return new Ee(x(x({},D),{},{interfaces:function(){return[].concat(g.getInterfaces().map(Qe),bt(w))},fields:function(){return x(x({},Ce(D.fields,io)),Ot(w))},extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):j(g)?function kc(g){var A,D=g.toConfig(),w=null!==(A=u[D.name])&&void 0!==A?A:[];return new Ze(x(x({},D),{},{interfaces:function(){return[].concat(g.getInterfaces().map(Qe),bt(w))},fields:function(){return x(x({},Ce(D.fields,io)),Ot(w))},extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):se(g)?function _c(g){var A,D=g.toConfig(),w=null!==(A=u[D.name])&&void 0!==A?A:[];return new $e(x(x({},D),{},{types:function(){return[].concat(g.getTypes().map(Qe),fo(w))},extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):re(g)?function sn(g){var A,D=g.toConfig(),w=null!==(A=u[g.name])&&void 0!==A?A:[];return new xe(x(x({},D),{},{values:x(x({},D.values),lo(w)),extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):z(g)?function oe(g){var A,D=g.toConfig(),w=null!==(A=u[D.name])&&void 0!==A?A:[];return new en(x(x({},D),{},{fields:function(){return x(x({},Ce(D.fields,function(G){return x(x({},G),{},{type:ge(G.type)})})),co(w))},extensionASTNodes:D.extensionASTNodes.concat(w)}))}(g):void $(0,"Unexpected type: "+T(g))}for(var g,R=0;R<s.length;R++){var Y,X=s[R],K=X.name.value;N[K]=null!==(Y=Gi[K])&&void 0!==Y?Y:Pc(X)}var ue=x(x({query:e.query&&Qe(e.query),mutation:e.mutation&&Qe(e.mutation),subscription:e.subscription&&Qe(e.subscription)},l&&so([l])),so(f));return x(x({description:null===(r=l)||void 0===r||null===(a=r.description)||void 0===a?void 0:a.value},ue),{},{types:q(N),directives:[].concat(e.directives.map(function O(g){var A=g.toConfig();return new Le(x(x({},A),{},{args:Ce(A.args,oo)}))}),c.map(function Vc(g){var A=g.locations.map(function(D){return D.value});return new Le({name:g.name.value,description:on(g,t),locations:A,isRepeatable:g.repeatable,args:uo(g.arguments),astNode:g})})),extensions:void 0,astNode:null!==(i=l)&&void 0!==i?i:e.astNode,extensionASTNodes:e.extensionASTNodes.concat(f),assumeValid:null!==(o=t?.assumeValid)&&void 0!==o&&o});function ge(g){return W(g)?new te(ge(g.ofType)):_(g)?new k(ge(g.ofType)):Qe(g)}function Qe(g){return N[g.name]}function io(g){return x(x({},g),{},{type:ge(g.type),args:Ce(g.args,oo)})}function oo(g){return x(x({},g),{},{type:ge(g.type)})}function so(g){for(var A={},D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].operationTypes)&&void 0!==w?w:[],B=0;B<G.length;B++){var Q=G[B];A[Q.operation]=Nt(Q.type)}return A}function Nt(g){var A,D=g.name.value,w=null!==(A=Gi[D])&&void 0!==A?A:N[D];if(void 0===w)throw new Error('Unknown type: "'.concat(D,'".'));return w}function jn(g){return g.kind===v.LIST_TYPE?new te(jn(g.type)):g.kind===v.NON_NULL_TYPE?new k(jn(g.type)):Nt(g)}function Ot(g){for(var A=Object.create(null),D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].fields)&&void 0!==w?w:[],B=0;B<G.length;B++){var Q=G[B];A[Q.name.value]={type:jn(Q.type),description:on(Q,t),args:uo(Q.arguments),deprecationReason:gt(Q),astNode:Q}}return A}function uo(g){for(var A=g??[],D=Object.create(null),w=0;w<A.length;w++){var C=A[w],G=jn(C.type);D[C.name.value]={type:G,description:on(C,t),defaultValue:Me(C.defaultValue,G),deprecationReason:gt(C),astNode:C}}return D}function co(g){for(var A=Object.create(null),D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].fields)&&void 0!==w?w:[],B=0;B<G.length;B++){var Q=G[B],Gn=jn(Q.type);A[Q.name.value]={type:Gn,description:on(Q,t),defaultValue:Me(Q.defaultValue,Gn),deprecationReason:gt(Q),astNode:Q}}return A}function lo(g){for(var A=Object.create(null),D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].values)&&void 0!==w?w:[],B=0;B<G.length;B++){var Q=G[B];A[Q.name.value]={description:on(Q,t),deprecationReason:gt(Q),astNode:Q}}return A}function bt(g){for(var A=[],D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].interfaces)&&void 0!==w?w:[],B=0;B<G.length;B++)A.push(Nt(G[B]));return A}function fo(g){for(var A=[],D=0;D<g.length;D++)for(var w,G=null!==(w=g[D].types)&&void 0!==w?w:[],B=0;B<G.length;B++)A.push(Nt(G[B]));return A}function Pc(g){var A,D=g.name.value,w=on(g,t),C=null!==(A=u[D])&&void 0!==A?A:[];switch(g.kind){case v.OBJECT_TYPE_DEFINITION:var G=C,B=[g].concat(G);return new Ee({name:D,description:w,interfaces:function(){return bt(B)},fields:function(){return Ot(B)},astNode:g,extensionASTNodes:G});case v.INTERFACE_TYPE_DEFINITION:var Q=C,Gn=[g].concat(Q);return new Ze({name:D,description:w,interfaces:function(){return bt(Gn)},fields:function(){return Ot(Gn)},astNode:g,extensionASTNodes:Q});case v.ENUM_TYPE_DEFINITION:var po=C,Mc=[g].concat(po);return new xe({name:D,description:w,values:lo(Mc),astNode:g,extensionASTNodes:po});case v.UNION_TYPE_DEFINITION:var vo=C,Cc=[g].concat(vo);return new $e({name:D,description:w,types:function(){return fo(Cc)},astNode:g,extensionASTNodes:vo});case v.SCALAR_TYPE_DEFINITION:var Uc=C;return new Re({name:D,description:w,specifiedByUrl:Bi(g),astNode:g,extensionASTNodes:Uc});case v.INPUT_OBJECT_TYPE_DEFINITION:var ho=C,xc=[g].concat(ho);return new en({name:D,description:w,fields:function(){return co(xc)},astNode:g,extensionASTNodes:ho})}$(0,"Unexpected type definition node: "+T(g))}}var Gi=Se(wn.concat(kn),function(e){return e.name});function gt(e){return Pn(vt,e)?.reason}function Bi(e){return Pn(nr,e)?.url}function on(e,n){if(e.description)return e.description.value;if(!0===n?.commentDescriptions){var t=function Zu(e){var n=e.loc;if(n){for(var t=[],r=n.startToken.prev;null!=r&&r.kind===m.COMMENT&&r.next&&r.prev&&r.line+1===r.next.line&&r.line!==r.prev.line;){var a=String(r.value);t.push(a),r=r.prev}return t.length>0?t.reverse().join("\n"):void 0}}(e);if(void 0!==t)return kt("\n"+t)}}function Qi(e,n){null!=e&&e.kind===v.DOCUMENT||P(0,"Must provide valid Document AST."),!0!==n?.assumeValid&&!0!==n?.assumeValidSDL&&function Tu(e){var n=li(e);if(0!==n.length)throw new Error(n.map(function(t){return t.message}).join("\n\n"))}(e);var r=ji({description:void 0,types:[],directives:[],extensions:void 0,extensionASTNodes:[],assumeValid:!1},e,n);if(null==r.astNode)for(var a=0,i=r.types;a<i.length;a++){var o=i[a];switch(o.name){case"Query":r.query=o;break;case"Mutation":r.mutation=o;break;case"Subscription":r.subscription=o}}for(var s=r.directives,u=function(f){var p=je[f];s.every(function(d){return d.name!==p.name})&&s.push(p)},c=0;c<je.length;c++)u(c);return new mn(r)}function $u(e,n){return Qi(Zn(e,{noLocation:n?.noLocation,allowLegacySDLEmptyFields:n?.allowLegacySDLEmptyFields,allowLegacySDLImplementsInterfaces:n?.allowLegacySDLImplementsInterfaces,experimentalFragmentVariables:n?.experimentalFragmentVariables}),{commentDescriptions:n?.commentDescriptions,assumeValidSDL:n?.assumeValidSDL,assumeValid:n?.assumeValid})}function Yi(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function ae(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Yi(Object(t),!0).forEach(function(r){ec(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Yi(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ec(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function nc(e){var n=e.toConfig(),t=qe(Rr(n.types),function(p){return p.name},function f(p){if(ye(p)||tn(p))return p;if(U(p)){var d=p.toConfig();return new Ee(ae(ae({},d),{},{interfaces:function(){return l(d.interfaces)},fields:function(){return u(d.fields)}}))}if(j(p)){var h=p.toConfig();return new Ze(ae(ae({},h),{},{interfaces:function(){return l(h.interfaces)},fields:function(){return u(h.fields)}}))}if(se(p)){var y=p.toConfig();return new $e(ae(ae({},y),{},{types:function(){return l(y.types)}}))}if(re(p)){var I=p.toConfig();return new xe(ae(ae({},I),{},{values:It(I.values)}))}if(z(p)){var N=p.toConfig();return new en(ae(ae({},N),{},{fields:function(){return function c(p){return It(p,function(d){return ae(ae({},d),{},{type:r(d.type)})})}(N.fields)}}))}$(0,"Unexpected type: "+T(p))});return new mn(ae(ae({},n),{},{types:q(t),directives:Rr(n.directives).map(function o(p){var d=p.toConfig();return new Le(ae(ae({},d),{},{locations:Lr(d.locations,function(h){return h}),args:s(d.args)}))}),query:i(n.query),mutation:i(n.mutation),subscription:i(n.subscription)}));function r(p){return W(p)?new te(r(p.ofType)):_(p)?new k(r(p.ofType)):a(p)}function a(p){return t[p.name]}function i(p){return p&&a(p)}function s(p){return It(p,function(d){return ae(ae({},d),{},{type:r(d.type)})})}function u(p){return It(p,function(d){return ae(ae({},d),{},{type:r(d.type),args:s(d.args)})})}function l(p){return Rr(p).map(a)}}function It(e,n){for(var t=Object.create(null),r=Lr(Object.keys(e),function(s){return s}),a=0;a<r.length;a++){var i=r[a],o=e[i];t[i]=n?n(o):o}return t}function Rr(e){return Lr(e,function(n){return n.name})}function Lr(e,n){return e.slice().sort(function(t,r){return $n(n(t),n(r))})}function tc(e,n){return qi(e,function(t){return!tr(t)},ac,n)}function rc(e,n){return qi(e,tr,tn,n)}function ac(e){return!lt(e)&&!tn(e)}function qi(e,n,t,r){var a=e.getDirectives().filter(n),i=q(e.getTypeMap()).filter(t);return[ic(e)].concat(a.map(function(o){return function vc(e,n){return Oe(n,e)+"directive @"+e.name+Hi(n,e.args)+(e.isRepeatable?" repeatable":"")+" on "+e.locations.join(" | ")}(o,r)}),i.map(function(o){return Ki(o,r)})).filter(Boolean).join("\n\n")+"\n"}function ic(e){if(null!=e.description||!function oc(e){var n=e.getQueryType();if(n&&"Query"!==n.name)return!1;var t=e.getMutationType();if(t&&"Mutation"!==t.name)return!1;var r=e.getSubscriptionType();return!(r&&"Subscription"!==r.name)}(e)){var n=[],t=e.getQueryType();t&&n.push("  query: ".concat(t.name));var r=e.getMutationType();r&&n.push("  mutation: ".concat(r.name));var a=e.getSubscriptionType();return a&&n.push("  subscription: ".concat(a.name)),Oe({},e)+"schema {\n".concat(n.join("\n"),"\n}")}}function Ki(e,n){return ye(e)?function sc(e,n){return Oe(n,e)+"scalar ".concat(e.name)+function dc(e){if(null==e.specifiedByUrl)return"";var t=Pe(e.specifiedByUrl,Z);return t||$(0,"Unexpected null value returned from `astFromValue` for specifiedByUrl")," @specifiedBy(url: "+H(t)+")"}(e)}(e,n):U(e)?function uc(e,n){return Oe(n,e)+"type ".concat(e.name)+Ji(e)+Xi(n,e)}(e,n):j(e)?function cc(e,n){return Oe(n,e)+"interface ".concat(e.name)+Ji(e)+Xi(n,e)}(e,n):se(e)?function lc(e,n){var t=e.getTypes(),r=t.length?" = "+t.join(" | "):"";return Oe(n,e)+"union "+e.name+r}(e,n):re(e)?function fc(e,n){var t=e.getValues().map(function(r,a){return Oe(n,r,"  ",!a)+"  "+r.name+_r(r.deprecationReason)});return Oe(n,e)+"enum ".concat(e.name)+Fr(t)}(e,n):z(e)?function pc(e,n){var t=q(e.getFields()).map(function(r,a){return Oe(n,r,"  ",!a)+"  "+kr(r)});return Oe(n,e)+"input ".concat(e.name)+Fr(t)}(e,n):void $(0,"Unexpected type: "+T(e))}function Ji(e){var n=e.getInterfaces();return n.length?" implements "+n.map(function(t){return t.name}).join(" & "):""}function Xi(e,n){return Fr(q(n.getFields()).map(function(r,a){return Oe(e,r,"  ",!a)+"  "+r.name+Hi(e,r.args,"  ")+": "+String(r.type)+_r(r.deprecationReason)}))}function Fr(e){return 0!==e.length?" {\n"+e.join("\n")+"\n}":""}function Hi(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return 0===n.length?"":n.every(function(r){return!r.description})?"("+n.map(kr).join(", ")+")":"(\n"+n.map(function(r,a){return Oe(e,r,"  "+t,!a)+"  "+t+kr(r)}).join("\n")+"\n"+t+")"}function kr(e){var n=Pe(e.defaultValue,e.type),t=e.name+": "+String(e.type);return n&&(t+=" = ".concat(H(n))),t+_r(e.deprecationReason)}function _r(e){if(null==e)return"";var n=Pe(e,Z);return n&&e!==er?" @deprecated(reason: "+H(n)+")":" @deprecated"}function Oe(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=n.description;return null==a?"":!0===e?.commentDescriptions?function hc(e,n,t){return(n&&!t?"\n":"")+e.split("\n").map(function(i){return n+(""!==i?"# "+i:"#")}).join("\n")+"\n"}(a,t,r):(t&&!r?"\n"+t:t)+zr(a,"",a.length>70).replace(/\n/g,"\n"+t)+"\n"}function mc(e){for(var n=[],t=0;t<e.length;t++)n=n.concat(e[t].definitions);return{kind:"Document",definitions:n}}function yc(e){for(var n=[],t=Object.create(null),r=0,a=e.definitions;r<a.length;r++){var i=a[r];switch(i.kind){case v.OPERATION_DEFINITION:n.push(i);break;case v.FRAGMENT_DEFINITION:t[i.name.value]=Wi(i.selectionSet)}}for(var o=Object.create(null),s=function(l){for(var f=n[l],p=new Set,d=0,h=Wi(f.selectionSet);d<h.length;d++)zi(p,t,h[d]);o[f.name?f.name.value:""]={kind:v.DOCUMENT,definitions:e.definitions.filter(function(N){return N===f||N.kind===v.FRAGMENT_DEFINITION&&p.has(N.name.value)})}},u=0;u<n.length;u++)s(u);return o}function zi(e,n,t){if(!e.has(t)){e.add(t);var r=n[t];if(void 0!==r)for(var a=0;a<r.length;a++)zi(e,n,r[a])}}function Wi(e){var n=[];return We(e,{FragmentSpread:function(r){n.push(r.name.value)}}),n}function Ec(e){for(var n=Jr(e)?e:new zn(e),t=n.body,r=new _t(n),a="",i=!1;r.advance().kind!==m.EOF;){var o=r.token,s=o.kind,u=!Wr(o.kind);i&&(u||o.kind===m.SPREAD)&&(a+=" ");var c=t.slice(o.start,o.end);a+=s===m.BLOCK_STRING?Tc(c):c,i=u}return a}function Tc(e){var t=kt(e.slice(3,-3));Hr(t)>0&&(t="\n"+t);var r=t[t.length-1];return('"'===r&&'\\"""'!==t.slice(-4)||"\\"===r)&&(t+="\n"),'"""'+t+'"""'}function Zi(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function $i(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Zi(Object(t),!0).forEach(function(r){gc(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Zi(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function gc(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var ie=Object.freeze({TYPE_REMOVED:"TYPE_REMOVED",TYPE_CHANGED_KIND:"TYPE_CHANGED_KIND",TYPE_REMOVED_FROM_UNION:"TYPE_REMOVED_FROM_UNION",VALUE_REMOVED_FROM_ENUM:"VALUE_REMOVED_FROM_ENUM",REQUIRED_INPUT_FIELD_ADDED:"REQUIRED_INPUT_FIELD_ADDED",IMPLEMENTED_INTERFACE_REMOVED:"IMPLEMENTED_INTERFACE_REMOVED",FIELD_REMOVED:"FIELD_REMOVED",FIELD_CHANGED_KIND:"FIELD_CHANGED_KIND",REQUIRED_ARG_ADDED:"REQUIRED_ARG_ADDED",ARG_REMOVED:"ARG_REMOVED",ARG_CHANGED_KIND:"ARG_CHANGED_KIND",DIRECTIVE_REMOVED:"DIRECTIVE_REMOVED",DIRECTIVE_ARG_REMOVED:"DIRECTIVE_ARG_REMOVED",REQUIRED_DIRECTIVE_ARG_ADDED:"REQUIRED_DIRECTIVE_ARG_ADDED",DIRECTIVE_REPEATABLE_REMOVED:"DIRECTIVE_REPEATABLE_REMOVED",DIRECTIVE_LOCATION_REMOVED:"DIRECTIVE_LOCATION_REMOVED"}),Ge=Object.freeze({VALUE_ADDED_TO_ENUM:"VALUE_ADDED_TO_ENUM",TYPE_ADDED_TO_UNION:"TYPE_ADDED_TO_UNION",OPTIONAL_INPUT_FIELD_ADDED:"OPTIONAL_INPUT_FIELD_ADDED",OPTIONAL_ARG_ADDED:"OPTIONAL_ARG_ADDED",IMPLEMENTED_INTERFACE_ADDED:"IMPLEMENTED_INTERFACE_ADDED",ARG_DEFAULT_VALUE_CHANGE:"ARG_DEFAULT_VALUE_CHANGE"});function Ic(e,n){return eo(e,n).filter(function(r){return r.type in ie})}function Nc(e,n){return eo(e,n).filter(function(r){return r.type in Ge})}function eo(e,n){return[].concat(function bc(e,n){for(var t=[],r=Be(q(e.getTypeMap()),q(n.getTypeMap())),a=0,i=r.removed;a<i.length;a++){var o=i[a];t.push({type:ie.TYPE_REMOVED,description:lt(o)?"Standard scalar ".concat(o.name," was removed because it is not referenced anymore."):"".concat(o.name," was removed.")})}for(var s=0,u=r.persisted;s<u.length;s++){var c=u[s],l=c[0],f=c[1];re(l)&&re(f)?t.push.apply(t,wc(l,f)):se(l)&&se(f)?t.push.apply(t,Sc(l,f)):z(l)&&z(f)?t.push.apply(t,Dc(l,f)):U(l)&&U(f)||j(l)&&j(f)?t.push.apply(t,to(l,f).concat(no(l,f))):l.constructor!==f.constructor&&t.push({type:ie.TYPE_CHANGED_KIND,description:"".concat(l.name," changed from ")+"".concat(ro(l)," to ").concat(ro(f),".")})}return t}(e,n),function Oc(e,n){for(var t=[],r=Be(e.getDirectives(),n.getDirectives()),a=0,i=r.removed;a<i.length;a++)t.push({type:ie.DIRECTIVE_REMOVED,description:"".concat(i[a].name," was removed.")});for(var s=0,u=r.persisted;s<u.length;s++){for(var c=u[s],l=c[0],f=c[1],p=Be(l.args,f.args),d=0,h=p.added;d<h.length;d++){var y=h[d];Xe(y)&&t.push({type:ie.REQUIRED_DIRECTIVE_ARG_ADDED,description:"A required arg ".concat(y.name," on directive ").concat(l.name," was added.")})}for(var I=0,N=p.removed;I<N.length;I++)t.push({type:ie.DIRECTIVE_ARG_REMOVED,description:"".concat(N[I].name," was removed from ").concat(l.name,".")});l.isRepeatable&&!f.isRepeatable&&t.push({type:ie.DIRECTIVE_REPEATABLE_REMOVED,description:"Repeatable flag was removed from ".concat(l.name,".")});for(var F=0,V=l.locations;F<V.length;F++){var R=V[F];-1===f.locations.indexOf(R)&&t.push({type:ie.DIRECTIVE_LOCATION_REMOVED,description:"".concat(R," was removed from ").concat(l.name,".")})}}return t}(e,n))}function Dc(e,n){for(var t=[],r=Be(q(e.getFields()),q(n.getFields())),a=0,i=r.added;a<i.length;a++){var o=i[a];ot(o)?t.push({type:ie.REQUIRED_INPUT_FIELD_ADDED,description:"A required field ".concat(o.name," on input type ").concat(e.name," was added.")}):t.push({type:Ge.OPTIONAL_INPUT_FIELD_ADDED,description:"An optional field ".concat(o.name," on input type ").concat(e.name," was added.")})}for(var s=0,u=r.removed;s<u.length;s++){var c=u[s];t.push({type:ie.FIELD_REMOVED,description:"".concat(e.name,".").concat(c.name," was removed.")})}for(var l=0,f=r.persisted;l<f.length;l++){var p=f[l],d=p[0],h=p[1];xn(d.type,h.type)||t.push({type:ie.FIELD_CHANGED_KIND,description:"".concat(e.name,".").concat(d.name," changed type from ")+"".concat(String(d.type)," to ").concat(String(h.type),".")})}return t}function Sc(e,n){for(var t=[],r=Be(e.getTypes(),n.getTypes()),a=0,i=r.added;a<i.length;a++)t.push({type:Ge.TYPE_ADDED_TO_UNION,description:"".concat(i[a].name," was added to union type ").concat(e.name,".")});for(var s=0,u=r.removed;s<u.length;s++)t.push({type:ie.TYPE_REMOVED_FROM_UNION,description:"".concat(u[s].name," was removed from union type ").concat(e.name,".")});return t}function wc(e,n){for(var t=[],r=Be(e.getValues(),n.getValues()),a=0,i=r.added;a<i.length;a++)t.push({type:Ge.VALUE_ADDED_TO_ENUM,description:"".concat(i[a].name," was added to enum type ").concat(e.name,".")});for(var s=0,u=r.removed;s<u.length;s++)t.push({type:ie.VALUE_REMOVED_FROM_ENUM,description:"".concat(u[s].name," was removed from enum type ").concat(e.name,".")});return t}function no(e,n){for(var t=[],r=Be(e.getInterfaces(),n.getInterfaces()),a=0,i=r.added;a<i.length;a++)t.push({type:Ge.IMPLEMENTED_INTERFACE_ADDED,description:"".concat(i[a].name," added to interfaces implemented by ").concat(e.name,".")});for(var s=0,u=r.removed;s<u.length;s++){var c=u[s];t.push({type:ie.IMPLEMENTED_INTERFACE_REMOVED,description:"".concat(e.name," no longer implements interface ").concat(c.name,".")})}return t}function to(e,n){for(var t=[],r=Be(q(e.getFields()),q(n.getFields())),a=0,i=r.removed;a<i.length;a++){var o=i[a];t.push({type:ie.FIELD_REMOVED,description:"".concat(e.name,".").concat(o.name," was removed.")})}for(var s=0,u=r.persisted;s<u.length;s++){var c=u[s],l=c[0],f=c[1];t.push.apply(t,Ac(e,l,f)),Un(l.type,f.type)||t.push({type:ie.FIELD_CHANGED_KIND,description:"".concat(e.name,".").concat(l.name," changed type from ")+"".concat(String(l.type)," to ").concat(String(f.type),".")})}return t}function Ac(e,n,t){for(var r=[],a=Be(n.args,t.args),i=0,o=a.removed;i<o.length;i++){var s=o[i];r.push({type:ie.ARG_REMOVED,description:"".concat(e.name,".").concat(n.name," arg ").concat(s.name," was removed.")})}for(var u=0,c=a.persisted;u<c.length;u++){var l=c[u],f=l[0],p=l[1];if(xn(f.type,p.type)){if(void 0!==f.defaultValue)if(void 0===p.defaultValue)r.push({type:Ge.ARG_DEFAULT_VALUE_CHANGE,description:"".concat(e.name,".").concat(n.name," arg ").concat(f.name," defaultValue was removed.")});else{var h=ao(f.defaultValue,f.type),y=ao(p.defaultValue,p.type);h!==y&&r.push({type:Ge.ARG_DEFAULT_VALUE_CHANGE,description:"".concat(e.name,".").concat(n.name," arg ").concat(f.name," has changed defaultValue from ").concat(h," to ").concat(y,".")})}}else r.push({type:ie.ARG_CHANGED_KIND,description:"".concat(e.name,".").concat(n.name," arg ").concat(f.name," has changed type from ")+"".concat(String(f.type)," to ").concat(String(p.type),".")})}for(var I=0,N=a.added;I<N.length;I++){var b=N[I];Xe(b)?r.push({type:ie.REQUIRED_ARG_ADDED,description:"A required arg ".concat(b.name," on ").concat(e.name,".").concat(n.name," was added.")}):r.push({type:Ge.OPTIONAL_ARG_ADDED,description:"An optional arg ".concat(b.name," on ").concat(e.name,".").concat(n.name," was added.")})}return r}function Un(e,n){return W(e)?W(n)&&Un(e.ofType,n.ofType)||_(n)&&Un(e,n.ofType):_(e)?_(n)&&Un(e.ofType,n.ofType):vn(n)&&e.name===n.name||_(n)&&Un(e,n.ofType)}function xn(e,n){return W(e)?W(n)&&xn(e.ofType,n.ofType):_(e)?_(n)&&xn(e.ofType,n.ofType)||!_(n)&&xn(e.ofType,n):vn(n)&&e.name===n.name}function ro(e){return ye(e)?"a Scalar type":U(e)?"an Object type":j(e)?"an Interface type":se(e)?"a Union type":re(e)?"an Enum type":z(e)?"an Input type":void $(0,"Unexpected type: "+T(e))}function ao(e,n){var t=Pe(e,n);return null!=t||$(0),H(We(t,{ObjectValue:function(i){var o=[].concat(i.fields);return o.sort(function(s,u){return $n(s.name.value,u.name.value)}),$i($i({},i),{},{fields:o})}}))}function Be(e,n){for(var t=[],r=[],a=[],i=Se(e,function(p){return p.name}),o=Se(n,function(p){return p.name}),s=0;s<e.length;s++){var u=e[s],c=o[u.name];void 0===c?r.push(u):a.push([u,c])}for(var l=0;l<n.length;l++){var f=n[l];void 0===i[f.name]&&t.push(f)}return{added:t,persisted:a,removed:r}}function Rc(e,n){return Nr(e,n,[Pi])}}}]);