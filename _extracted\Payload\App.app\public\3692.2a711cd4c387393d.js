(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3692],{63692:(F,m,n)=>{n.r(m),n.d(m,{MboTransfiyaContactsPageModule:()=>j});var c=n(17007),d=n(78007),p=n(79798),s=n(30263),f=n(15861),u=n(39904),g=n(89148),b=n(95437),v=n(57544),h=n(17698),y=n(19198),t=n(99877),C=n(48774),x=n(48030),P=n(61383),E=n(45542),M=n(50689);function T(a,i){if(1&a){const e=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",10),t.\u0275\u0275element(1,"bocc-avatar-icon",11),t.\u0275\u0275elementStart(2,"div",12)(3,"label",13),t.\u0275\u0275text(4),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(5,"p",14),t.\u0275\u0275text(6),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(7,"button",15),t.\u0275\u0275listener("click",function(){const l=t.\u0275\u0275restoreView(e).$implicit,I=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(I.onDelete(l))}),t.\u0275\u0275elementStart(8,"span"),t.\u0275\u0275text(9,"Eliminar"),t.\u0275\u0275elementEnd()()()}if(2&a){const e=i.$implicit,o=t.\u0275\u0275nextContext();t.\u0275\u0275advance(4),t.\u0275\u0275textInterpolate1(" ",e.numberFormat," "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" El d\xednero ser\xe1 recibido en: ",null==o.productControl.value?null:o.productControl.value.name," ")}}function z(a,i){1&a&&(t.\u0275\u0275elementStart(0,"mbo-message-empty"),t.\u0275\u0275text(1," No tienes ning\xfan contacto seleccionado para aceptar transferencias autom\xe1ticamente. "),t.\u0275\u0275elementEnd())}class A{constructor(i){this.value=i;const r=`${i.type===g.Gt.SavingAccount?"Ahorro":"Corriente"} - ${i.number.slice(-4)}`;this.title=r,this.description=r}compareTo({id:i}){return this.value.id===i}filtrable(i){return!0}}let S=(()=>{class a{constructor(e,o,r,l){this.modalConfirmation=e,this.mboProvider=o,this.requestConfiguration=r,this.repository=l,this.products=[],this.phones=[],this.backAction={id:"btn_transfiya-contacts_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(u.Z6.TRANSFERS.CELTOCEL.HOME)}},this.collection=new Map,this.productControl=new v.FormControl}ngOnInit(){this.unsubscription=this.productControl.subscribe(e=>{e&&this.requestPhones(e).then(o=>{this.phones=o,this.collection.set(e.id,o)})}),setTimeout(()=>{this.initializatedConfiguration()},120)}ngOnDestroy(){this.unsubscription&&this.unsubscription()}onDelete(e){this.modalConfirmation.execute({title:"Eliminar contacto",message:`\xbfEst\xe1s seguro que d\xe9seas eliminar el contacto ${e.number}?`,accept:{label:"Aceptar",theme:"danger",click:()=>{this.confirmDeleteContact(e)}},decline:{label:"Cancelar"}})}initializatedConfiguration(){var e=this;return(0,f.Z)(function*(){e.mboProvider.loader.open("Consultando productos, por favor espere..."),(yield e.requestConfiguration.contacts()).when({success:({products:o})=>{e.products=o.map(r=>new A(r))}},()=>{e.mboProvider.loader.close()})})()}requestPhones(e){const o=this.collection.get(e.id);return o?Promise.resolve(o):(this.mboProvider.loader.open("Consultando contactos, por favor espere..."),this.repository.request(e).catch(()=>[]).finally(()=>{this.mboProvider.loader.close()}))}confirmDeleteContact(e){this.mboProvider.loader.open("Eliminando contacto, por favor espere..."),this.repository.remove(this.productControl.value,e).then(()=>{this.phones=this.phones.filter(o=>o!==e),this.collection.set(this.productControl.value.id,this.phones),this.mboProvider.toast.success("Tu contacto fue eliminado exitosamente de la base de datos.","Eliminaci\xf3n exitosa")}).catch(()=>{this.mboProvider.toast.error("Por favor intenta m\xe1s tarde.","Eliminaci\xf3n no exitosa")}).finally(()=>{this.mboProvider.loader.close()})}}return a.\u0275fac=function(e){return new(e||a)(t.\u0275\u0275directiveInject(s.$e),t.\u0275\u0275directiveInject(b.ZL),t.\u0275\u0275directiveInject(h.tE),t.\u0275\u0275directiveInject(y.pv))},a.\u0275cmp=t.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-contacts-page"]],decls:12,vars:5,consts:[[1,"mbo-transfiya-contacts-page__content"],[1,"mbo-transfiya-contacts-page__header"],["title","Contactos",3,"leftAction"],[1,"mbo-transfiya-contacts-page__body"],[1,"mbo-transfiya-contacts-page__title","subtitle2-medium"],[1,"mbo-transfiya-contacts-page__message","body2-medium"],["elementId","lst_transfiya-contacts_product","label","Ver celulares de confianza de la cuenta","placeholder","Seleccionar producto",3,"suggestions","formControl"],[1,"mbo-transfiya-contacts-page__phones"],["class","mbo-transfiya-contacts-page__phone",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"mbo-transfiya-contacts-page__phone"],["icon","mobile-contact"],[1,"mbo-transfiya-contacts-page__phone__info"],[1,"body2-medium"],[1,"smalltext-medium"],["bocc-button","flat",3,"click"]],template:function(e,o){1&e&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"label",4),t.\u0275\u0275text(5," Administra tus celulares de confianza "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"p",5),t.\u0275\u0275text(7," Cuando alguno de estos celulares te env\xede dinero, la transferencia ser\xe1 aceptada autom\xe1ticamente. "),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(8,"bocc-select-box",6),t.\u0275\u0275elementStart(9,"div",7),t.\u0275\u0275template(10,T,10,2,"div",8),t.\u0275\u0275template(11,z,2,0,"mbo-message-empty",9),t.\u0275\u0275elementEnd()()()),2&e&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",o.backAction),t.\u0275\u0275advance(6),t.\u0275\u0275property("suggestions",o.products)("formControl",o.productControl),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngForOf",o.phones),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!o.phones.length))},dependencies:[c.NgForOf,c.NgIf,C.J,x.t,P.j,E.P,M.A],styles:["/*!\n * MBO TransfiyaContacts Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 28/Jun/2023\n * Updated: 08/Jul/2024\n*/mbo-transfiya-contacts-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x8)}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__title{color:var(--color-carbon-darker-1000)}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__message{color:var(--color-carbon-lighter-700)}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__phone{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x8) 0rem;box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__phone__info{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-transfiya-contacts-page .mbo-transfiya-contacts-page__phone__info p{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),a})(),j=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=t.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=t.\u0275\u0275defineInjector({imports:[c.CommonModule,d.RouterModule.forChild([{path:"",component:S}]),s.Jx,s.tv,s.jK,s.P8,p.Aj]}),a})()}}]);