(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1639],{21639:(u,l,n)=>{n.r(l),n.d(l,{MboPaymentInvoiceManualModule:()=>e});var t=n(17007),M=n(78007),a=n(99877);const d=[{path:"",redirectTo:"select",pathMatch:"full"},{path:"select",loadChildren:()=>n.e(4386).then(n.bind(n,84386)).then(o=>o.MboPaymentInvoiceManualSelectPageModule)},{path:"agreement",loadChildren:()=>n.e(2836).then(n.bind(n,72836)).then(o=>o.MboPaymentInvoiceManualAgreementPageModule)},{path:"reference",loadChildren:()=>n.e(254).then(n.bind(n,30254)).then(o=>o.MboPaymentInvoiceManualReferencePageModule)},{path:"source",loadChildren:()=>n.e(8477).then(n.bind(n,88477)).then(o=>o.MboPaymentInvoiceManualSourcePageModule)},{path:"confirmation",loadChildren:()=>n.e(260).then(n.bind(n,40260)).then(o=>o.MboPaymentInvoiceManualConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(6648).then(n.bind(n,76648)).then(o=>o.MboPaymentInvoiceManualResultPageModule)}];let e=(()=>{class o{}return o.\u0275fac=function(P){return new(P||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[t.CommonModule,M.RouterModule.forChild(d)]}),o})()}}]);