(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1750],{24006:(wn,Ee,C)=>{C.r(Ee),C.d(Ee,{AbstractControl:()=>M,AbstractControlDirective:()=>X,AbstractFormGroupDirective:()=>le,COMPOSITION_BUFFER_MODE:()=>Se,CheckboxControlValueAccessor:()=>z,CheckboxRequiredValidator:()=>ve,ControlContainer:()=>u,DefaultValueAccessor:()=>w,EmailValidator:()=>Ce,FormArray:()=>$,FormArrayName:()=>U,FormBuilder:()=>be,FormControl:()=>y,FormControlDirective:()=>ge,FormControlName:()=>me,FormGroup:()=>m,FormGroupDirective:()=>B,FormGroupName:()=>j,FormRecord:()=>re,FormsModule:()=>Fn,MaxLengthValidator:()=>De,MaxValidator:()=>_e,MinLengthValidator:()=>Ve,MinValidator:()=>ye,NG_ASYNC_VALIDATORS:()=>c,NG_VALIDATORS:()=>l,NG_VALUE_ACCESSOR:()=>d,NgControl:()=>h,NgControlStatus:()=>et,NgControlStatusGroup:()=>tt,NgForm:()=>T,NgModel:()=>de,NgModelGroup:()=>ue,NgSelectOption:()=>mt,NonNullableFormBuilder:()=>An,NumberValueAccessor:()=>ce,PatternValidator:()=>Ae,RadioControlValueAccessor:()=>he,RangeValueAccessor:()=>fe,ReactiveFormsModule:()=>En,RequiredValidator:()=>W,SelectControlValueAccessor:()=>H,SelectMultipleControlValueAccessor:()=>L,UntypedFormArray:()=>Vn,UntypedFormBuilder:()=>Mn,UntypedFormControl:()=>Yt,UntypedFormGroup:()=>Tt,VERSION:()=>bn,Validators:()=>wt,isFormArray:()=>Dn,isFormControl:()=>dt,isFormGroup:()=>Bt,isFormRecord:()=>jt,\u0275InternalFormsSharedModule:()=>Me,\u0275NgNoValidate:()=>ht,\u0275NgSelectMultipleOption:()=>yt});var i=C(99877),we=C(17007),Ne=C(42168),Dt=C(84757);let Ie=(()=>{class n{constructor(e,r){this._renderer=e,this._elementRef=r,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(e,r){this._renderer.setProperty(this._elementRef.nativeElement,e,r)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.Renderer2),i.\u0275\u0275directiveInject(i.ElementRef))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n}),n})(),g=(()=>{class n extends Ie{}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,features:[i.\u0275\u0275InheritDefinitionFeature]}),n})();const d=new i.InjectionToken("NgValueAccessor"),At={provide:d,useExisting:(0,i.forwardRef)(()=>z),multi:!0};let z=(()=>{class n extends g{writeValue(e){this.setProperty("checked",e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("change",function(s){return r.onChange(s.target.checked)})("blur",function(){return r.onTouched()})},features:[i.\u0275\u0275ProvidersFeature([At]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const Mt={provide:d,useExisting:(0,i.forwardRef)(()=>w),multi:!0},Se=new i.InjectionToken("CompositionEventMode");let w=(()=>{class n extends Ie{constructor(e,r,o){super(e,r),this._compositionMode=o,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function bt(){const n=(0,we.\u0275getDOM)()?(0,we.\u0275getDOM)().getUserAgent():"";return/android (\d+)/.test(n.toLowerCase())}())}writeValue(e){this.setProperty("value",e??"")}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.Renderer2),i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(Se,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("input",function(s){return r._handleInput(s.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(s){return r._compositionEnd(s.target.value)})},features:[i.\u0275\u0275ProvidersFeature([Mt]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const Ft=!1;function p(n){return null==n||("string"==typeof n||Array.isArray(n))&&0===n.length}function Oe(n){return null!=n&&"number"==typeof n.length}const l=new i.InjectionToken("NgValidators"),c=new i.InjectionToken("NgAsyncValidators"),Et=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;class wt{static min(t){return Re(t)}static max(t){return xe(t)}static required(t){return Pe(t)}static requiredTrue(t){return Ge(t)}static email(t){return ke(t)}static minLength(t){return Te(t)}static maxLength(t){return Be(t)}static pattern(t){return je(t)}static nullValidator(t){return null}static compose(t){return qe(t)}static composeAsync(t){return ze(t)}}function Re(n){return t=>{if(p(t.value)||p(n))return null;const e=parseFloat(t.value);return!isNaN(e)&&e<n?{min:{min:n,actual:t.value}}:null}}function xe(n){return t=>{if(p(t.value)||p(n))return null;const e=parseFloat(t.value);return!isNaN(e)&&e>n?{max:{max:n,actual:t.value}}:null}}function Pe(n){return p(n.value)?{required:!0}:null}function Ge(n){return!0===n.value?null:{required:!0}}function ke(n){return p(n.value)||Et.test(n.value)?null:{email:!0}}function Te(n){return t=>p(t.value)||!Oe(t.value)?null:t.value.length<n?{minlength:{requiredLength:n,actualLength:t.value.length}}:null}function Be(n){return t=>Oe(t.value)&&t.value.length>n?{maxlength:{requiredLength:n,actualLength:t.value.length}}:null}function je(n){if(!n)return N;let t,e;return"string"==typeof n?(e="","^"!==n.charAt(0)&&(e+="^"),e+=n,"$"!==n.charAt(n.length-1)&&(e+="$"),t=new RegExp(e)):(e=n.toString(),t=n),r=>{if(p(r.value))return null;const o=r.value;return t.test(o)?null:{pattern:{requiredPattern:e,actualValue:o}}}}function N(n){return null}function Ue(n){return null!=n}function He(n){const t=(0,i.\u0275isPromise)(n)?(0,Ne.from)(n):n;if(Ft&&!(0,i.\u0275isObservable)(t)){let e="Expected async validator to return Promise or Observable.";throw"object"==typeof n&&(e+=" Are you using a synchronous validator where an async validator is expected?"),new i.\u0275RuntimeError(-1101,e)}return t}function Le(n){let t={};return n.forEach(e=>{t=null!=e?{...t,...e}:t}),0===Object.keys(t).length?null:t}function We(n,t){return t.map(e=>e(n))}function $e(n){return n.map(t=>function Nt(n){return!n.validate}(t)?t:e=>t.validate(e))}function qe(n){if(!n)return null;const t=n.filter(Ue);return 0==t.length?null:function(e){return Le(We(e,t))}}function K(n){return null!=n?qe($e(n)):null}function ze(n){if(!n)return null;const t=n.filter(Ue);return 0==t.length?null:function(e){const r=We(e,t).map(He);return(0,Ne.forkJoin)(r).pipe((0,Dt.map)(Le))}}function Z(n){return null!=n?ze($e(n)):null}function Ke(n,t){return null===n?[t]:Array.isArray(n)?[...n,t]:[n,t]}function Ze(n){return n._rawValidators}function Ye(n){return n._rawAsyncValidators}function Y(n){return n?Array.isArray(n)?n:[n]:[]}function I(n,t){return Array.isArray(n)?n.includes(t):n===t}function Xe(n,t){const e=Y(t);return Y(n).forEach(o=>{I(e,o)||e.push(o)}),e}function Je(n,t){return Y(t).filter(e=>!I(n,e))}class X{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=K(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Z(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t){this.control&&this.control.reset(t)}hasError(t,e){return!!this.control&&this.control.hasError(t,e)}getError(t,e){return this.control?this.control.getError(t,e):null}}class u extends X{get formDirective(){return null}get path(){return null}}class h extends X{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class Qe{constructor(t){this._cd=t}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let et=(()=>{class n extends Qe{constructor(e){super(e)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(h,2))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(e,r){2&e&&i.\u0275\u0275classProp("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},features:[i.\u0275\u0275InheritDefinitionFeature]}),n})(),tt=(()=>{class n extends Qe{constructor(e){super(e)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,10))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(e,r){2&e&&i.\u0275\u0275classProp("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)("ng-submitted",r.isSubmitted)},features:[i.\u0275\u0275InheritDefinitionFeature]}),n})();function nt(n,t){return n?`with name: '${t}'`:`at index: ${t}`}const ee=!1,D="VALID",O="INVALID",V="PENDING",A="DISABLED";function te(n){return(R(n)?n.validators:n)||null}function ne(n,t){return(R(t)?t.asyncValidators:n)||null}function R(n){return null!=n&&!Array.isArray(n)&&"object"==typeof n}function rt(n,t,e){const r=n.controls;if(!(t?Object.keys(r):r).length)throw new i.\u0275RuntimeError(1e3,ee?function Rt(n){return`\n    There are no form controls registered with this ${n?"group":"array"} yet. If you're using ngModel,\n    you may want to check next tick (e.g. use setTimeout).\n  `}(t):"");if(!r[e])throw new i.\u0275RuntimeError(1001,ee?function xt(n,t){return`Cannot find form control ${nt(n,t)}`}(t,e):"")}function it(n,t,e){n._forEachChild((r,o)=>{if(void 0===e[o])throw new i.\u0275RuntimeError(1002,ee?function Pt(n,t){return`Must supply a value for form control ${nt(n,t)}`}(t,o):"")})}class M{constructor(t,e){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get valid(){return this.status===D}get invalid(){return this.status===O}get pending(){return this.status==V}get disabled(){return this.status===A}get enabled(){return this.status!==A}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Xe(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Xe(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Je(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Je(t,this._rawAsyncValidators))}hasValidator(t){return I(this._rawValidators,t)}hasAsyncValidator(t){return I(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){this.touched=!0,this._parent&&!t.onlySelf&&this._parent.markAsTouched(t)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(t=>t.markAllAsTouched())}markAsUntouched(t={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(e=>{e.markAsUntouched({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}markAsDirty(t={}){this.pristine=!1,this._parent&&!t.onlySelf&&this._parent.markAsDirty(t)}markAsPristine(t={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(e=>{e.markAsPristine({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}markAsPending(t={}){this.status=V,!1!==t.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!t.onlySelf&&this._parent.markAsPending(t)}disable(t={}){const e=this._parentMarkedDirty(t.onlySelf);this.status=A,this.errors=null,this._forEachChild(r=>{r.disable({...t,onlySelf:!0})}),this._updateValue(),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...t,skipPristineCheck:e}),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){const e=this._parentMarkedDirty(t.onlySelf);this.status=D,this._forEachChild(r=>{r.enable({...t,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors({...t,skipPristineCheck:e}),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===D||this.status===V)&&this._runAsyncValidator(t.emitEvent)),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(t)}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?A:D}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t){if(this.asyncValidator){this.status=V,this._hasOwnPendingAsyncValidator=!0;const e=He(this.asyncValidator(this));this._asyncValidationSubscription=e.subscribe(r=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(r,{emitEvent:t})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(t,e={}){this.errors=t,this._updateControlsErrors(!1!==e.emitEvent)}get(t){let e=t;return null==e||(Array.isArray(e)||(e=e.split(".")),0===e.length)?null:e.reduce((r,o)=>r&&r._find(o),this)}getError(t,e){const r=e?this.get(e):this;return r&&r.errors?r.errors[t]:null}hasError(t,e){return!!this.getError(t,e)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(t)}_initObservables(){this.valueChanges=new i.EventEmitter,this.statusChanges=new i.EventEmitter}_calculateStatus(){return this._allControlsDisabled()?A:this.errors?O:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(V)?V:this._anyControlsHaveStatus(O)?O:D}_anyControlsHaveStatus(t){return this._anyControls(e=>e.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t={}){this.pristine=!this._anyControlsDirty(),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}_updateTouched(t={}){this.touched=this._anyControlsTouched(),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){R(t)&&null!=t.updateOn&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){return!t&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=function Gt(n){return Array.isArray(n)?K(n):n||null}(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=function kt(n){return Array.isArray(n)?Z(n):n||null}(this._rawAsyncValidators)}}class m extends M{constructor(t,e,r){super(te(e),ne(r,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,e){return this.controls[t]?this.controls[t]:(this.controls[t]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(t,e,r={}){this.registerControl(t,e),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,e={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(t,e,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],e&&this.registerControl(t,e),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,e={}){it(this,!0,t),Object.keys(t).forEach(r=>{rt(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){null!=t&&(Object.keys(t).forEach(r=>{const o=this.controls[r];o&&o.patchValue(t[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t={},e={}){this._forEachChild((r,o)=>{r.reset(t[o],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(t,e,r)=>(t[r]=e.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(e,r)=>!!r._syncPendingControls()||e);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(e=>{const r=this.controls[e];r&&t(r,e)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(const[e,r]of Object.entries(this.controls))if(this.contains(e)&&t(r))return!0;return!1}_reduceValue(){return this._reduceChildren({},(e,r,o)=>((r.enabled||this.disabled)&&(e[o]=r.value),e))}_reduceChildren(t,e){let r=t;return this._forEachChild((o,s)=>{r=e(r,o,s)}),r}_allControlsDisabled(){for(const t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}}const Tt=m,Bt=n=>n instanceof m;class re extends m{}const jt=n=>n instanceof re,_=new i.InjectionToken("CallSetDisabledState",{providedIn:"root",factory:()=>b}),b="always";function x(n,t){return[...t.path,n]}function F(n,t,e=b){ie(n,t),t.valueAccessor.writeValue(n.value),(n.disabled||"always"===e)&&t.valueAccessor.setDisabledState?.(n.disabled),function Ht(n,t){t.valueAccessor.registerOnChange(e=>{n._pendingValue=e,n._pendingChange=!0,n._pendingDirty=!0,"change"===n.updateOn&&ot(n,t)})}(n,t),function Wt(n,t){const e=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};n.registerOnChange(e),t._registerOnDestroy(()=>{n._unregisterOnChange(e)})}(n,t),function Lt(n,t){t.valueAccessor.registerOnTouched(()=>{n._pendingTouched=!0,"blur"===n.updateOn&&n._pendingChange&&ot(n,t),"submit"!==n.updateOn&&n.markAsTouched()})}(n,t),function Ut(n,t){if(t.valueAccessor.setDisabledState){const e=r=>{t.valueAccessor.setDisabledState(r)};n.registerOnDisabledChange(e),t._registerOnDestroy(()=>{n._unregisterOnDisabledChange(e)})}}(n,t)}function P(n,t,e=!0){const r=()=>{};t.valueAccessor&&(t.valueAccessor.registerOnChange(r),t.valueAccessor.registerOnTouched(r)),k(n,t),n&&(t._invokeOnDestroyCallbacks(),n._registerOnCollectionChange(()=>{}))}function G(n,t){n.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(t)})}function ie(n,t){const e=Ze(n);null!==t.validator?n.setValidators(Ke(e,t.validator)):"function"==typeof e&&n.setValidators([e]);const r=Ye(n);null!==t.asyncValidator?n.setAsyncValidators(Ke(r,t.asyncValidator)):"function"==typeof r&&n.setAsyncValidators([r]);const o=()=>n.updateValueAndValidity();G(t._rawValidators,o),G(t._rawAsyncValidators,o)}function k(n,t){let e=!1;if(null!==n){if(null!==t.validator){const o=Ze(n);if(Array.isArray(o)&&o.length>0){const s=o.filter(a=>a!==t.validator);s.length!==o.length&&(e=!0,n.setValidators(s))}}if(null!==t.asyncValidator){const o=Ye(n);if(Array.isArray(o)&&o.length>0){const s=o.filter(a=>a!==t.asyncValidator);s.length!==o.length&&(e=!0,n.setAsyncValidators(s))}}}const r=()=>{};return G(t._rawValidators,r),G(t._rawAsyncValidators,r),e}function ot(n,t){n._pendingDirty&&n.markAsDirty(),n.setValue(n._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(n._pendingValue),n._pendingChange=!1}function st(n,t){ie(n,t)}function se(n,t){if(!n.hasOwnProperty("model"))return!1;const e=n.model;return!!e.isFirstChange()||!Object.is(t,e.currentValue)}function at(n,t){n._syncPendingControls(),t.forEach(e=>{const r=e.control;"submit"===r.updateOn&&r._pendingChange&&(e.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function ae(n,t){if(!t)return null;let e,r,o;return Array.isArray(t),t.forEach(s=>{s.constructor===w?e=s:function zt(n){return Object.getPrototypeOf(n.constructor)===g}(s)?r=s:o=s}),o||r||e||null}const Zt={provide:u,useExisting:(0,i.forwardRef)(()=>T)},E=(()=>Promise.resolve())();let T=(()=>{class n extends u{constructor(e,r,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._directives=new Set,this.ngSubmit=new i.EventEmitter,this.form=new m({},K(e),Z(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){E.then(()=>{const r=this._findContainer(e.path);e.control=r.registerControl(e.name,e.control),F(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){E.then(()=>{const r=this._findContainer(e.path);r&&r.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){E.then(()=>{const r=this._findContainer(e.path),o=new m({});st(o,e),r.registerControl(e.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){E.then(()=>{const r=this._findContainer(e.path);r&&r.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,r){E.then(()=>{this.form.get(e.path).setValue(r)})}setValue(e){this.control.setValue(e)}onSubmit(e){return this.submitted=!0,at(this.form,this._directives),this.ngSubmit.emit(e),"dialog"===e?.target?.method}onReset(){this.resetForm()}resetForm(e){this.form.reset(e),this.submitted=!1}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10),i.\u0275\u0275directiveInject(_,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("submit",function(s){return r.onSubmit(s)})("reset",function(){return r.onReset()})},inputs:{options:["ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[i.\u0275\u0275ProvidersFeature([Zt]),i.\u0275\u0275InheritDefinitionFeature]}),n})();function lt(n,t){const e=n.indexOf(t);e>-1&&n.splice(e,1)}function ut(n){return"object"==typeof n&&null!==n&&2===Object.keys(n).length&&"value"in n&&"disabled"in n}const y=class extends M{constructor(t=null,e,r){super(te(e),ne(r,e)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),R(e)&&(e.nonNullable||e.initialValueIsDefault)&&(this.defaultValue=ut(t)?t.value:t)}setValue(t,e={}){this.value=this._pendingValue=t,this._onChange.length&&!1!==e.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==e.emitViewToModelChange)),this.updateValueAndValidity(e)}patchValue(t,e={}){this.setValue(t,e)}reset(t=this.defaultValue,e={}){this._applyFormState(t),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){lt(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){lt(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(t){ut(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}},Yt=y,dt=n=>n instanceof y;let le=(()=>{class n extends u{ngOnInit(){this._checkParentType(),this.formDirective.addFormGroup(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormGroup(this)}get control(){return this.formDirective.getFormGroup(this)}get path(){return x(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,features:[i.\u0275\u0275InheritDefinitionFeature]}),n})();const Xt={provide:u,useExisting:(0,i.forwardRef)(()=>ue)};let ue=(()=>{class n extends le{constructor(e,r,o){super(),this._parent=e,this._setValidators(r),this._setAsyncValidators(o)}_checkParentType(){}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,5),i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","ngModelGroup",""]],inputs:{name:["ngModelGroup","name"]},exportAs:["ngModelGroup"],features:[i.\u0275\u0275ProvidersFeature([Xt]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const Jt={provide:h,useExisting:(0,i.forwardRef)(()=>de)},ct=(()=>Promise.resolve())();let de=(()=>{class n extends h{constructor(e,r,o,s,a,f){super(),this._changeDetectorRef=a,this.callSetDisabledState=f,this.control=new y,this._registered=!1,this.update=new i.EventEmitter,this._parent=e,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=ae(0,s)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){const r=e.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),se(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){F(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(e){ct.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){const r=e.isDisabled.currentValue,o=0!==r&&(0,i.\u0275coerceToBoolean)(r);ct.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?x(e,this._parent):[e]}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,9),i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10),i.\u0275\u0275directiveInject(d,10),i.\u0275\u0275directiveInject(i.ChangeDetectorRef,8),i.\u0275\u0275directiveInject(_,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[i.\u0275\u0275ProvidersFeature([Jt]),i.\u0275\u0275InheritDefinitionFeature,i.\u0275\u0275NgOnChangesFeature]}),n})(),ht=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]}),n})();const Qt={provide:d,useExisting:(0,i.forwardRef)(()=>ce),multi:!0};let ce=(()=>{class n extends g{writeValue(e){this.setProperty("value",e??"")}registerOnChange(e){this.onChange=r=>{e(""==r?null:parseFloat(r))}}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("input",function(s){return r.onChange(s.target.value)})("blur",function(){return r.onTouched()})},features:[i.\u0275\u0275ProvidersFeature([Qt]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const en={provide:d,useExisting:(0,i.forwardRef)(()=>he),multi:!0};let ft=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({}),n})(),tn=(()=>{class n{constructor(){this._accessors=[]}add(e,r){this._accessors.push([e,r])}remove(e){for(let r=this._accessors.length-1;r>=0;--r)if(this._accessors[r][1]===e)return void this._accessors.splice(r,1)}select(e){this._accessors.forEach(r=>{this._isSameGroup(r,e)&&r[1]!==e&&r[1].fireUncheck(e.value)})}_isSameGroup(e,r){return!!e[0].control&&e[0]._parent===r._control._parent&&e[1].name===r.name}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:ft}),n})(),he=(()=>{class n extends g{constructor(e,r,o,s){super(e,r),this._registry=o,this._injector=s,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(0,i.inject)(_,{optional:!0})??b}ngOnInit(){this._control=this._injector.get(h),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(e){this._state=e===this.value,this.setProperty("checked",this._state)}registerOnChange(e){this._fn=e,this.onChange=()=>{e(this.value),this._registry.select(this)}}setDisabledState(e){(this.setDisabledStateFired||e||"whenDisabledForLegacyCode"===this.callSetDisabledState)&&this.setProperty("disabled",e),this.setDisabledStateFired=!0}fireUncheck(e){this.writeValue(e)}_checkName(){!this.name&&this.formControlName&&(this.name=this.formControlName)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.Renderer2),i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(tn),i.\u0275\u0275directiveInject(i.Injector))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("change",function(){return r.onChange()})("blur",function(){return r.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[i.\u0275\u0275ProvidersFeature([en]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const nn={provide:d,useExisting:(0,i.forwardRef)(()=>fe),multi:!0};let fe=(()=>{class n extends g{writeValue(e){this.setProperty("value",parseFloat(e))}registerOnChange(e){this.onChange=r=>{e(""==r?null:parseFloat(r))}}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","range","formControlName",""],["input","type","range","formControl",""],["input","type","range","ngModel",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("change",function(s){return r.onChange(s.target.value)})("input",function(s){return r.onChange(s.target.value)})("blur",function(){return r.onTouched()})},features:[i.\u0275\u0275ProvidersFeature([nn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const pe=new i.InjectionToken("NgModelWithFormControlWarning"),rn={provide:h,useExisting:(0,i.forwardRef)(()=>ge)};let ge=(()=>{class n extends h{set isDisabled(e){}constructor(e,r,o,s,a){super(),this._ngModelWarningConfig=s,this.callSetDisabledState=a,this.update=new i.EventEmitter,this._ngModelWarningSent=!1,this._setValidators(e),this._setAsyncValidators(r),this.valueAccessor=ae(0,o)}ngOnChanges(e){if(this._isControlChanged(e)){const r=e.form.previousValue;r&&P(r,this,!1),F(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}se(e,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&P(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_isControlChanged(e){return e.hasOwnProperty("form")}}return n._ngModelWarningSentOnce=!1,n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10),i.\u0275\u0275directiveInject(d,10),i.\u0275\u0275directiveInject(pe,8),i.\u0275\u0275directiveInject(_,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formControl",""]],inputs:{form:["formControl","form"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],features:[i.\u0275\u0275ProvidersFeature([rn]),i.\u0275\u0275InheritDefinitionFeature,i.\u0275\u0275NgOnChangesFeature]}),n})();const on={provide:u,useExisting:(0,i.forwardRef)(()=>B)};let B=(()=>{class n extends u{constructor(e,r,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new i.EventEmitter,this._setValidators(e),this._setAsyncValidators(r)}ngOnChanges(e){this._checkFormPresent(),e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(k(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){const r=this.form.get(e.path);return F(r,e,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),r}getControl(e){return this.form.get(e.path)}removeControl(e){P(e.control||null,e,!1),function Kt(n,t){const e=n.indexOf(t);e>-1&&n.splice(e,1)}(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,r){this.form.get(e.path).setValue(r)}onSubmit(e){return this.submitted=!0,at(this.form,this.directives),this.ngSubmit.emit(e),"dialog"===e?.target?.method}onReset(){this.resetForm()}resetForm(e){this.form.reset(e),this.submitted=!1}_updateDomValue(){this.directives.forEach(e=>{const r=e.control,o=this.form.get(e.path);r!==o&&(P(r||null,e),dt(o)&&(F(o,e,this.callSetDisabledState),e.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){const r=this.form.get(e.path);st(r,e),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){const r=this.form.get(e.path);r&&function $t(n,t){return k(n,t)}(r,e)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){ie(this.form,this),this._oldForm&&k(this._oldForm,this)}_checkFormPresent(){}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10),i.\u0275\u0275directiveInject(_,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formGroup",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("submit",function(s){return r.onSubmit(s)})("reset",function(){return r.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[i.\u0275\u0275ProvidersFeature([on]),i.\u0275\u0275InheritDefinitionFeature,i.\u0275\u0275NgOnChangesFeature]}),n})();const sn={provide:u,useExisting:(0,i.forwardRef)(()=>j)};let j=(()=>{class n extends le{constructor(e,r,o){super(),this._parent=e,this._setValidators(r),this._setAsyncValidators(o)}_checkParentType(){pt(this._parent)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,13),i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formGroupName",""]],inputs:{name:["formGroupName","name"]},features:[i.\u0275\u0275ProvidersFeature([sn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const an={provide:u,useExisting:(0,i.forwardRef)(()=>U)};let U=(()=>{class n extends u{constructor(e,r,o){super(),this._parent=e,this._setValidators(r),this._setAsyncValidators(o)}ngOnInit(){this._checkParentType(),this.formDirective.addFormArray(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormArray(this)}get control(){return this.formDirective.getFormArray(this)}get formDirective(){return this._parent?this._parent.formDirective:null}get path(){return x(null==this.name?this.name:this.name.toString(),this._parent)}_checkParentType(){pt(this._parent)}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,13),i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formArrayName",""]],inputs:{name:["formArrayName","name"]},features:[i.\u0275\u0275ProvidersFeature([an]),i.\u0275\u0275InheritDefinitionFeature]}),n})();function pt(n){return!(n instanceof j||n instanceof B||n instanceof U)}const ln={provide:h,useExisting:(0,i.forwardRef)(()=>me)};let me=(()=>{class n extends h{set isDisabled(e){}constructor(e,r,o,s,a){super(),this._ngModelWarningConfig=a,this._added=!1,this.update=new i.EventEmitter,this._ngModelWarningSent=!1,this._parent=e,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=ae(0,s)}ngOnChanges(e){this._added||this._setUpControl(),se(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return x(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}}return n._ngModelWarningSentOnce=!1,n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(u,13),i.\u0275\u0275directiveInject(l,10),i.\u0275\u0275directiveInject(c,10),i.\u0275\u0275directiveInject(d,10),i.\u0275\u0275directiveInject(pe,8))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","formControlName",""]],inputs:{name:["formControlName","name"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},features:[i.\u0275\u0275ProvidersFeature([ln]),i.\u0275\u0275InheritDefinitionFeature,i.\u0275\u0275NgOnChangesFeature]}),n})();const un={provide:d,useExisting:(0,i.forwardRef)(()=>H),multi:!0};function gt(n,t){return null==n?`${t}`:(t&&"object"==typeof t&&(t="Object"),`${n}: ${t}`.slice(0,50))}let H=(()=>{class n extends g{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){this.value=e;const o=gt(this._getOptionId(e),e);this.setProperty("value",o)}registerOnChange(e){this.onChange=r=>{this.value=this._getOptionValue(r),e(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(e){for(const r of Array.from(this._optionMap.keys()))if(this._compareWith(this._optionMap.get(r),e))return r;return null}_getOptionValue(e){const r=function dn(n){return n.split(":")[0]}(e);return this._optionMap.has(r)?this._optionMap.get(r):e}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("change",function(s){return r.onChange(s.target.value)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},features:[i.\u0275\u0275ProvidersFeature([un]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),mt=(()=>{class n{constructor(e,r,o){this._element=e,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(e){null!=this._select&&(this._select._optionMap.set(this.id,e),this._setElementValue(gt(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._setElementValue(e),this._select&&this._select.writeValue(this._select.value)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(i.Renderer2),i.\u0275\u0275directiveInject(H,9))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}}),n})();const cn={provide:d,useExisting:(0,i.forwardRef)(()=>L),multi:!0};function _t(n,t){return null==n?`${t}`:("string"==typeof t&&(t=`'${t}'`),t&&"object"==typeof t&&(t="Object"),`${n}: ${t}`.slice(0,50))}let L=(()=>{class n extends g{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){let r;if(this.value=e,Array.isArray(e)){const o=e.map(s=>this._getOptionId(s));r=(s,a)=>{s._setSelected(o.indexOf(a.toString())>-1)}}else r=(o,s)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(e){this.onChange=r=>{const o=[],s=r.selectedOptions;if(void 0!==s){const a=s;for(let f=0;f<a.length;f++){const Fe=this._getOptionValue(a[f].value);o.push(Fe)}}else{const a=r.options;for(let f=0;f<a.length;f++){const q=a[f];if(q.selected){const Fe=this._getOptionValue(q.value);o.push(Fe)}}}this.value=o,e(o)}}_registerOption(e){const r=(this._idCounter++).toString();return this._optionMap.set(r,e),r}_getOptionId(e){for(const r of Array.from(this._optionMap.keys()))if(this._compareWith(this._optionMap.get(r)._value,e))return r;return null}_getOptionValue(e){const r=function hn(n){return n.split(":")[0]}(e);return this._optionMap.has(r)?this._optionMap.get(r)._value:e}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(e,r){1&e&&i.\u0275\u0275listener("change",function(s){return r.onChange(s.target)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},features:[i.\u0275\u0275ProvidersFeature([cn]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),yt=(()=>{class n{constructor(e,r,o){this._element=e,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(e){null!=this._select&&(this._value=e,this._setElementValue(_t(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._select?(this._value=e,this._setElementValue(_t(this.id,e)),this._select.writeValue(this._select.value)):this._setElementValue(e)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}_setSelected(e){this._renderer.setProperty(this._element.nativeElement,"selected",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}}return n.\u0275fac=function(e){return new(e||n)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(i.Renderer2),i.\u0275\u0275directiveInject(L,9))},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}}),n})();function vt(n){return"number"==typeof n?n:parseInt(n,10)}function Ct(n){return"number"==typeof n?n:parseFloat(n)}let v=(()=>{class n{constructor(){this._validator=N}ngOnChanges(e){if(this.inputName in e){const r=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):N,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return null!=e}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275dir=i.\u0275\u0275defineDirective({type:n,features:[i.\u0275\u0275NgOnChangesFeature]}),n})();const fn={provide:l,useExisting:(0,i.forwardRef)(()=>_e),multi:!0};let _e=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=e=>Ct(e),this.createValidator=e=>xe(e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("max",r._enabled?r.max:null)},inputs:{max:"max"},features:[i.\u0275\u0275ProvidersFeature([fn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const pn={provide:l,useExisting:(0,i.forwardRef)(()=>ye),multi:!0};let ye=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=e=>Ct(e),this.createValidator=e=>Re(e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("min",r._enabled?r.min:null)},inputs:{min:"min"},features:[i.\u0275\u0275ProvidersFeature([pn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const gn={provide:l,useExisting:(0,i.forwardRef)(()=>W),multi:!0},mn={provide:l,useExisting:(0,i.forwardRef)(()=>ve),multi:!0};let W=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=i.\u0275coerceToBoolean,this.createValidator=e=>Pe}enabled(e){return e}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("required",r._enabled?"":null)},inputs:{required:"required"},features:[i.\u0275\u0275ProvidersFeature([gn]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),ve=(()=>{class n extends W{constructor(){super(...arguments),this.createValidator=e=>Ge}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["input","type","checkbox","required","","formControlName",""],["input","type","checkbox","required","","formControl",""],["input","type","checkbox","required","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("required",r._enabled?"":null)},features:[i.\u0275\u0275ProvidersFeature([mn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const _n={provide:l,useExisting:(0,i.forwardRef)(()=>Ce),multi:!0};let Ce=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="email",this.normalizeInput=i.\u0275coerceToBoolean,this.createValidator=e=>ke}enabled(e){return e}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","email","","formControlName",""],["","email","","formControl",""],["","email","","ngModel",""]],inputs:{email:"email"},features:[i.\u0275\u0275ProvidersFeature([_n]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const yn={provide:l,useExisting:(0,i.forwardRef)(()=>Ve),multi:!0};let Ve=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="minlength",this.normalizeInput=e=>vt(e),this.createValidator=e=>Te(e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","minlength","","formControlName",""],["","minlength","","formControl",""],["","minlength","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("minlength",r._enabled?r.minlength:null)},inputs:{minlength:"minlength"},features:[i.\u0275\u0275ProvidersFeature([yn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const vn={provide:l,useExisting:(0,i.forwardRef)(()=>De),multi:!0};let De=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=e=>vt(e),this.createValidator=e=>Be(e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("maxlength",r._enabled?r.maxlength:null)},inputs:{maxlength:"maxlength"},features:[i.\u0275\u0275ProvidersFeature([vn]),i.\u0275\u0275InheritDefinitionFeature]}),n})();const Cn={provide:l,useExisting:(0,i.forwardRef)(()=>Ae),multi:!0};let Ae=(()=>{class n extends v{constructor(){super(...arguments),this.inputName="pattern",this.normalizeInput=e=>e,this.createValidator=e=>je(e)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275dir=i.\u0275\u0275defineDirective({type:n,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(e,r){2&e&&i.\u0275\u0275attribute("pattern",r._enabled?r.pattern:null)},inputs:{pattern:"pattern"},features:[i.\u0275\u0275ProvidersFeature([Cn]),i.\u0275\u0275InheritDefinitionFeature]}),n})(),Me=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({imports:[ft]}),n})();class $ extends M{constructor(t,e,r){super(te(e),ne(r,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(t){return this.controls[this._adjustIndex(t)]}push(t,e={}){this.controls.push(t),this._registerControl(t),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(t,e,r={}){this.controls.splice(t,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(t,e={}){let r=this._adjustIndex(t);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(t,e,r={}){let o=this._adjustIndex(t);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),e&&(this.controls.splice(o,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(t,e={}){it(this,!1,t),t.forEach((r,o)=>{rt(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){null!=t&&(t.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t=[],e={}){this._forEachChild((r,o)=>{r.reset(t[o],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(t=>t.getRawValue())}clear(t={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:t.emitEvent}))}_adjustIndex(t){return t<0?t+this.length:t}_syncPendingControls(){let t=this.controls.reduce((e,r)=>!!r._syncPendingControls()||e,!1);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){this.controls.forEach((e,r)=>{t(e,r)})}_updateValue(){this.value=this.controls.filter(t=>t.enabled||this.disabled).map(t=>t.value)}_anyControls(t){return this.controls.some(e=>e.enabled&&t(e))}_setUpControls(){this._forEachChild(t=>this._registerControl(t))}_allControlsDisabled(){for(const t of this.controls)if(t.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(t){t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)}_find(t){return this.at(t)??null}}const Vn=$,Dn=n=>n instanceof $;function Vt(n){return!!n&&(void 0!==n.asyncValidators||void 0!==n.validators||void 0!==n.updateOn)}let be=(()=>{class n{constructor(){this.useNonNullable=!1}get nonNullable(){const e=new n;return e.useNonNullable=!0,e}group(e,r=null){const o=this._reduceControls(e);let s={};return Vt(r)?s=r:null!==r&&(s.validators=r.validator,s.asyncValidators=r.asyncValidator),new m(o,s)}record(e,r=null){const o=this._reduceControls(e);return new re(o,r)}control(e,r,o){let s={};return this.useNonNullable?(Vt(r)?s=r:(s.validators=r,s.asyncValidators=o),new y(e,{...s,nonNullable:!0})):new y(e,r,o)}array(e,r,o){const s=e.map(a=>this._createControl(a));return new $(s,r,o)}_reduceControls(e){const r={};return Object.keys(e).forEach(o=>{r[o]=this._createControl(e[o])}),r}_createControl(e){return e instanceof y||e instanceof M?e:Array.isArray(e)?this.control(e[0],e.length>1?e[1]:null,e.length>2?e[2]:null):this.control(e)}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),An=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:function(){return(0,i.inject)(be).nonNullable},providedIn:"root"}),n})(),Mn=(()=>{class n extends be{group(e,r=null){return super.group(e,r)}control(e,r,o){return super.control(e,r,o)}array(e,r,o){return super.array(e,r,o)}}return n.\u0275fac=function(){let t;return function(r){return(t||(t=i.\u0275\u0275getInheritedFactory(n)))(r||n)}}(),n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();const bn=new i.Version("15.2.10");let Fn=(()=>{class n{static withConfig(e){return{ngModule:n,providers:[{provide:_,useValue:e.callSetDisabledState??b}]}}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({imports:[Me]}),n})(),En=(()=>{class n{static withConfig(e){return{ngModule:n,providers:[{provide:pe,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:_,useValue:e.callSetDisabledState??b}]}}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({imports:[Me]}),n})()}}]);