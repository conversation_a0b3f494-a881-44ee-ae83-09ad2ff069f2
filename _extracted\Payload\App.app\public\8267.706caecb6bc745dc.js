(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8267],{48267:(E,l,o)=>{o.r(l),o.d(l,{MboCustomerCapsModule:()=>d});var u=o(17007),M=o(78007),a=o(99877);const t=[{path:"",loadChildren:()=>o.e(6387).then(o.bind(o,36387)).then(n=>n.MboCustomerCapsPageModule)}];let d=(()=>{class n{}return n.\u0275fac=function(s){return new(s||n)},n.\u0275mod=a.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=a.\u0275\u0275defineInjector({imports:[u.CommonModule,M.RouterModule.forChild(t)]}),n})()}}]);