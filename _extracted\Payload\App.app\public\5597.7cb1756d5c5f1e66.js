(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5597],{77493:(T,I,r)=>{r.d(I,{P:()=>p,G:()=>B});var m=r(15861),d=r(87956),l=r(53113),a=r(98699),s=r(38074),R=r(29306),C=r(87903),v=r(66067);class j{constructor(N,t,e,o){this.destination=N,this.source=t,this.amount=e,this.manual=o}}function g(n){return new j(n.destination,n.source,n.amount,n.manual)}var E=r(71776),P=r(39904),b=r(42168),y=r(84757),u=r(99877);let M=(()=>{class n{constructor(t,e){this.http=t,e.subscribes(P.PU,()=>{this.loans=void 0})}request(){return this.loans?Promise.resolve(this.loans):(0,b.firstValueFrom)(this.http.get(P.bV.PAYMENTS.DEBTS.CATALOG,{params:{exclude:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,y.map)(({content:t})=>t.map(e=>function F(n){return new v.T2(n.id,n.acctType,n.acctTypeName,n.loanName,n.acctId,new R.Br(n.bankId,n.bankName),n.isAval,n.dynamo||!1,n.isOwner,n.isOwner?null:n.owner,n.isOwner?null:new l.dp((0,C.nX)(n.ownerIdType),n.ownerId))}(e))),(0,y.tap)(t=>{this.loans=t})))}send(t){return(0,b.firstValueFrom)(this.http.post(P.bV.PAYMENTS.LOAN,function D(n){return{acctIdFrom:n.source.id,acctNickNameFrom:n.source.nickname,bankIdFrom:n.source.bank.id,acctIdTo:n.destination.id,acctNameTo:n.destination.nickname,bankIdTo:n.destination.bank.id,bankNameTo:n.destination.bank.name,amt:Math.ceil(n.amount),curCode:"COP",paymentDesc:""}}(t)).pipe((0,y.map)(e=>(0,C.l1)(e,"SUCCESS")))).catch(e=>(0,C.rU)(e))}}return n.\u0275fac=function(t){return new(t||n)(u.\u0275\u0275inject(E.HttpClient),u.\u0275\u0275inject(d.Yd))},n.\u0275prov=u.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var L=r(20691);let i=(()=>{class n extends L.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,manual:!1}),this.eventBusService=t,this.eventBusService.subscribes(P.PU,()=>{this.reset()})}setDestination(t,e=!1){this.reduce(o=>({...o,destination:t,fromCustomer:e}))}getDestination(){return this.select(({destination:t})=>t)}setProduct(t){this.reduce(e=>({...e,product:t}))}getProduct(){return this.select(({product:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(e=>({...e,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount({amount:t,manual:e}){this.reduce(o=>({...o,manual:e,amount:t}))}selectForAmount(){return this.select(({amount:t,confirmation:e,destination:o,source:h})=>({amount:t,confirmation:e,destination:o,source:h}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(u.\u0275\u0275inject(d.Yd))},n.\u0275prov=u.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),p=(()=>{class n{constructor(t,e,o,h){this.productService=t,this.repository=e,this.store=o,this.eventBusService=h}setDestination(t){var e=this;return(0,m.Z)(function*(){try{return(0,s.p)(t)&&(yield e.productService.requestInformation(t)),a.Either.success(e.store.setDestination(t))}catch{return a.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return a.Either.success(this.store.setSource(t))}catch({message:e}){return a.Either.failure({message:e})}}setAmount(t,e=!1){try{return a.Either.success(this.store.setAmount({amount:t,manual:e}))}catch({message:o}){return a.Either.failure({message:o})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getProduct();return this.store.reset(),a.Either.success({fromCustomer:t,product:e})}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const e=g(t.store.currentState),o=yield t.execute(e);return t.eventBusService.emit(o.channel),a.Either.success({loan:e,status:o})})()}execute(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(l.LN.error(e))}}}return n.\u0275fac=function(t){return new(t||n)(u.\u0275\u0275inject(d.M5),u.\u0275\u0275inject(M),u.\u0275\u0275inject(i),u.\u0275\u0275inject(d.Yd))},n.\u0275prov=u.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var c=r(89148);const{DlaPayMin:f,DlaPayTotal:O,LoanPayMin:U,LoanPayTotal:x}=c.Av;let B=(()=>{class n{constructor(t,e,o,h){this.products=t,this.productService=e,this.repository=o,this.store=h}destination(){var t=this;return(0,m.Z)(function*(){try{return a.Either.success(yield t.requestLoans())}catch({message:e}){return a.Either.failure({message:e})}})()}source(t){var e=this;return(0,m.Z)(function*(){try{const o=yield e.products.requestAccountsForTransfer(),h=e.store.itIsConfirmation(),S=yield e.requestLoan(t);return a.Either.success({confirmation:h,destination:S,products:o})}catch({message:o}){return a.Either.failure({message:o})}})()}selectAmount(){var t=this;return(0,m.Z)(function*(){try{const e=t.store.itIsConfirmation(),o=t.store.getSource(),h=t.store.getDestination(),S=yield t.productService.requestInformation(h),A=t.getMinPayment(S),V=t.getTotalPayment(S);return a.Either.success({confirmation:e,cop:{min:A,total:V},source:o})}catch({message:e}){return a.Either.failure({message:e})}})()}amount(){var t=this;return(0,m.Z)(function*(){try{const e=t.store.getDestination(),o=(0,s.p)(e)?yield t.productService.requestInformation(t.store.getDestination()):void 0,h=o&&t.getTotalPayment(o);return a.Either.success({...t.store.selectForAmount(),total:h})}catch({message:e}){return a.Either.failure({message:e})}})()}confirmation(){try{const t=g(this.store.currentState);return a.Either.success({payment:t})}catch({message:t}){return a.Either.failure({message:t})}}requestLoans(){return this.repository.request().then(t=>t.reduce((e,o)=>{const{others:h,principals:S}=e;return(o.bank.isOccidente?S:h).push(o),e},{others:[],principals:[]}))}requestLoan(t){var e=this;return(0,m.Z)(function*(){let o=e.store.getDestination();if(!o&&t){const h=yield e.products.requestProductForId(t);if(e.store.setProduct(h),h){const{principals:S}=yield e.requestLoans(),A=S.find(({number:V})=>V===h.number);A&&(yield e.productService.requestInformation(A)),o=A||h,e.store.setDestination(o,!0)}}return o})()}getMinPayment(t){return t?.getSection(U)||t?.getSection(f)}getTotalPayment(t){return t?.getSection(x)||t?.getSection(O)}}return n.\u0275fac=function(t){return new(t||n)(u.\u0275\u0275inject(d.hM),u.\u0275\u0275inject(d.M5),u.\u0275\u0275inject(M),u.\u0275\u0275inject(i))},n.\u0275prov=u.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},75597:(T,I,r)=>{r.r(I),r.d(I,{MboPaymentLoanResultPageModule:()=>L});var m=r(17007),d=r(78007),l=r(79798),a=r(15861),s=r(99877),R=r(39904),C=r(95437),v=r(87903),j=r(53113);function F(i){const{isError:p,message:c}=i;return{animation:(0,v.jY)(i),title:p?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:c}}function D({isError:i}){return i?[(0,v.wT)("Finalizar","finish","outline"),(0,v.wT)("Volver a intentar","retry")]:[(0,v.wT)("Hacer otro pago","retry","outline"),(0,v.wT)("Finalizar","finish")]}var E=r(77493),P=r(10464),b=r(78021),y=r(16442);function u(i,p){if(1&i&&(s.\u0275\u0275elementStart(0,"div",4),s.\u0275\u0275element(1,"mbo-header-result",5),s.\u0275\u0275elementEnd()),2&i){const c=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("rightActions",c.rightActions)}}let M=(()=>{class i{constructor(c,f,O){this.ref=c,this.mboProvider=f,this.managerLoan=O,this.requesting=!0,this.template=R.$d,this.rightActions=[{id:"btn_payment-loan-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-loan-result-page_template"),this.initializatedTransaction()}onAction(c){this.mboProvider.navigation.next("finish"===c?R.Z6.CUSTOMER.PRODUCTS.HOME:R.Z6.PAYMENTS.LOAN.DESTINATION)}initializatedTransaction(){var c=this;return(0,a.Z)(function*(){(yield c.managerLoan.send()).when({success:f=>{c.template=function g(i){const{dateFormat:p,timeFormat:c}=new j.ou,{status:f,loan:O}=i;return{actions:D(f),error:f.isError,header:F(f),informations:[(0,v.SP)("DESTINO",O.destination.nickname,O.destination.number,O.destination.bank.name),(0,v._f)("SUMA DE",O.amount),(0,v.cZ)(p,c)],skeleton:!1}}(f)}},()=>{c.requesting=!1,c.managerLoan.reset()})})()}}return i.\u0275fac=function(c){return new(c||i)(s.\u0275\u0275directiveInject(s.ElementRef),s.\u0275\u0275directiveInject(C.ZL),s.\u0275\u0275directiveInject(E.P))},i.\u0275cmp=s.\u0275\u0275defineComponent({type:i,selectors:[["mbo-payment-loan-result-page"]],decls:5,vars:2,consts:[[1,"mbo-payment-loan-result-page__content","mbo-page__scroller"],["class","mbo-payment-loan-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-loan-result-page__body"],["id","crd_payment-loan-result-page_template",3,"template","action"],[1,"mbo-payment-loan-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(c,f){1&c&&(s.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),s.\u0275\u0275template(2,u,2,1,"div",1),s.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),s.\u0275\u0275listener("action",function(U){return f.onAction(U)}),s.\u0275\u0275elementEnd()()()()),2&c&&(s.\u0275\u0275advance(2),s.\u0275\u0275property("ngIf",!f.requesting),s.\u0275\u0275advance(2),s.\u0275\u0275property("template",f.template))},dependencies:[m.NgIf,P.K,b.c,y.u],styles:["/*!\n * MBO PaymentLoanResult Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 27/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-payment-loan-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-loan-result-page .mbo-payment-loan-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-loan-result-page .mbo-payment-loan-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),i})(),L=(()=>{class i{}return i.\u0275fac=function(c){return new(c||i)},i.\u0275mod=s.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=s.\u0275\u0275defineInjector({imports:[m.CommonModule,d.RouterModule.forChild([{path:"",component:M}]),l.KI,l.cN,l.tu]}),i})()},63674:(T,I,r)=>{r.d(I,{Eg:()=>v,Lo:()=>a,Wl:()=>s,ZC:()=>R,_f:()=>d,br:()=>C,tl:()=>l});var m=r(29306);const d={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},l=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),a={color:"success",key:"paid",label:"Pagada"},s={color:"alert",key:"pending",label:"Por pagar"},R={color:"danger",key:"expired",label:"Vencida"},C={color:"info",key:"recurring",label:"Pago recurrente"},v={color:"info",key:"programmed",label:"Programado"}},66067:(T,I,r)=>{r.d(I,{S6:()=>j,T2:()=>C,UQ:()=>F,mZ:()=>v});var m=r(39904),d=r(6472),a=r(63674),s=r(31707);class C{constructor(g,E,P,b,y,u,M,L,i,p,c){this.id=g,this.type=E,this.name=P,this.nickname=b,this.number=y,this.bank=u,this.isAval=M,this.isProtected=L,this.isOwner=i,this.ownerName=p,this.ownerDocument=c,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,d.initials)(b),this.shortNumber=y.substring(y.length-4),this.descriptionNumber=`${P} ${y}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:u.logo,light:u.logo,standard:u.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(g){this.informationValue||(this.informationValue=g)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(g){return this.currenciesValue.includes(g)}}class v{constructor(g,E){this.id=g,this.type=E}}class j{constructor(g,E,P,b,y,u,M,L,i,p,c,f){this.uuid=g,this.number=E,this.nie=P,this.nickname=b,this.companyId=y,this.companyName=u,this.amount=M,this.registerDate=L,this.expirationDate=i,this.paid=p,this.statusCode=c,this.references=f,this.recurring=f.length>0,this.status=function R(D){switch(D){case s.U.EXPIRED:return a.ZC;case s.U.PENDING:return a.Wl;case s.U.PROGRAMMED:return a.Eg;case s.U.RECURRING:return a.br;default:return a.Lo}}(c)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class F{constructor(g,E,P,b,y,u,M,L){this.uuid=g,this.number=E,this.nickname=P,this.companyId=b,this.companyName=y,this.city=u,this.amount=M,this.isBiller=L}}},38074:(T,I,r)=>{r.d(I,{p:()=>d});var m=r(29306);function d(l){return l instanceof m.xs||l.isRequiredInformation}},31707:(T,I,r)=>{r.d(I,{U:()=>m,f:()=>d});var m=(()=>{return(l=m||(m={})).RECURRING="1",l.EXPIRED="2",l.PENDING="3",l.PROGRAMMED="4",m;var l})(),d=(()=>{return(l=d||(d={})).BILLER="Servicio",l.NON_BILLER="Servicio",l.PSE="Servicio",l.TAX="Impuesto",l.LOAN="Obligaci\xf3n financiera",l.CREDIT_CARD="Obligaci\xf3n financiera",d;var l})()}}]);