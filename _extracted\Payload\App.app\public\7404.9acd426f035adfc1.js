(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7404],{108:(b,h,r)=>{r.d(h,{a:()=>y,z:()=>A});var e=r(87903),u=r(53113);function f(t){const{isError:n,message:s,type:c}=t;return{animation:(0,e.jY)(t),title:n?"\xa1Transferencia fallida!":"PENDING"===c?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:s}}function g({isError:t}){return t?[(0,e.wT)("Finalizar","finish","outline"),(0,e.wT)("Volver a intentar","retry")]:[(0,e.wT)("Hacer otra transferencia","retry","outline"),(0,e.wT)("Finalizar","finish")]}function T(t){const{approved:n,pending:s}=t;if(n)return function o(t,n){const{dateFormat:s,timeFormat:c}=new u.ou,p=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&p.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),p.push((0,e.cZ)(s,c)),p}(t,n);if(s)return function v(t,n){const{dateFormat:s,timeFormat:c}=new u.ou,p=[(0,e.SP)("DESTINO",t.product.nickname,t.product.bank.name,t.product.publicNumber),(0,e._f)("SUMA DE",n.amount),(0,e.SP)("DESDE",n.phone)];return n.description&&p.push((0,e.SP)("DESCRIPCI\xd3N",n.description)),p.push((0,e.cZ)(s,c)),p}(t,s);const{dateFormat:c,timeFormat:p}=new u.ou,l=[(0,e.SP)("TRANSFER"===t.type?"ENVIADO A":"SOLICITADO A",t.contact?.name,t.contact?.number),(0,e._f)("SUMA DE",t.amount)];return t.description&&l.push((0,e.SP)("DESCRIPCI\xd3N","","",t.description)),l.push((0,e.cZ)(c,p)),l}function A(t){const{status:n,transfiya:s}=t;return{actions:g(n),error:n.isError,header:f(n),informations:T(s),skeleton:!1}}function y(t){const n=[],{amount:s,category:c,color:p,date:{dateFormat:l,timeFormat:i},description:m,phoneFormat:a,reference:d}=t;return n.push((0,e.SP)("REFERENCIA",d)),n.push((0,e.fW)("TIPO DE TRANSACCI\xd3N",p,c)),n.push((0,e.SP)("CONTACTO",a)),n.push((0,e._f)("LA SUMA DE",s)),m&&n.push((0,e.Kt)("DESCRIPCI\xd3N",m)),n.push((0,e.cZ)(l,i)),n}},27404:(b,h,r)=>{r.r(h),r.d(h,{MboTransfiyaApprovedResultPageModule:()=>l});var e=r(17007),u=r(78007),f=r(79798),g=r(15861),o=r(99877),v=r(39904),T=r(95437),A=r(108),y=r(17698),t=r(10464),n=r(78021),s=r(16442);function c(i,m){if(1&i&&(o.\u0275\u0275elementStart(0,"div",4),o.\u0275\u0275element(1,"mbo-header-result",5),o.\u0275\u0275elementEnd()),2&i){const a=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("rightActions",a.rightActions)}}let p=(()=>{class i{constructor(a,d,P){this.ref=a,this.mboProvider=d,this.managerTransfiya=P,this.requesting=!0,this.template=v.$d,this.rightActions=[{id:"btn_transfiya-approved-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfiya-approved-result-page_template"),this.initializatedTransaction()}onAction(a){this.mboProvider.navigation.next("finish"===a?v.Z6.CUSTOMER.PRODUCTS.HOME:v.Z6.TRANSFERS.TRANSFIYA.APPROVED.HOME)}initializatedTransaction(){var a=this;return(0,g.Z)(function*(){(yield a.managerTransfiya.confirmApproved()).when({success:d=>{a.template=(0,A.z)(d)}},()=>{a.requesting=!1,a.managerTransfiya.reset()})})()}}return i.\u0275fac=function(a){return new(a||i)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(T.ZL),o.\u0275\u0275directiveInject(y.Pm))},i.\u0275cmp=o.\u0275\u0275defineComponent({type:i,selectors:[["mbo-transfiya-approved-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfiya-approved-result-page__content","mbo-page__scroller"],["class","mbo-transfiya-approved-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfiya-approved-result-page__body"],["id","crd_transfiya-approved-result-page_template",3,"template","action"],[1,"mbo-transfiya-approved-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(a,d){1&a&&(o.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),o.\u0275\u0275template(2,c,2,1,"div",1),o.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),o.\u0275\u0275listener("action",function(E){return d.onAction(E)}),o.\u0275\u0275elementEnd()()()()),2&a&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",!d.requesting),o.\u0275\u0275advance(2),o.\u0275\u0275property("template",d.template))},dependencies:[e.NgIf,t.K,n.c,s.u],styles:["/*!\n * MBO TransfiyaApprovedResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-approved-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-approved-result-page .mbo-transfiya-approved-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-approved-result-page .mbo-transfiya-approved-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),i})(),l=(()=>{class i{}return i.\u0275fac=function(a){return new(a||i)},i.\u0275mod=o.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=o.\u0275\u0275defineInjector({imports:[e.CommonModule,u.RouterModule.forChild([{path:"",component:p}]),f.KI,f.cN,f.tu]}),i})()}}]);