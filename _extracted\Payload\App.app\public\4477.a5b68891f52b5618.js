(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4477],{94477:(h,u,n)=>{n.r(u),n.d(u,{AppWeb:()=>r});var t=n(15861),a=n(17737);class r extends a.WebPlugin{constructor(){super(),this.handleVisibilityChange=()=>{const e={isActive:!0!==document.hidden};this.notifyListeners("appStateChange",e),document.hidden?this.notifyListeners("pause",null):this.notifyListeners("resume",null)},document.addEventListener("visibilitychange",this.handleVisibilityChange,!1)}exitApp(){throw this.unimplemented("Not implemented on web.")}getInfo(){var e=this;return(0,t.Z)(function*(){throw e.unimplemented("Not implemented on web.")})()}getLaunchUrl(){return(0,t.Z)(function*(){return{url:""}})()}getState(){return(0,t.Z)(function*(){return{isActive:!0!==document.hidden}})()}minimizeApp(){var e=this;return(0,t.Z)(function*(){throw e.unimplemented("Not implemented on web.")})()}}},15861:(h,u,n)=>{function t(_,r,c,e,d,l,s){try{var o=_[l](s),i=o.value}catch(m){return void c(m)}o.done?r(i):Promise.resolve(i).then(e,d)}function a(_){return function(){var r=this,c=arguments;return new Promise(function(e,d){var l=_.apply(r,c);function s(i){t(l,e,d,s,o,"next",i)}function o(i){t(l,e,d,s,o,"throw",i)}s(void 0)})}}n.d(u,{Z:()=>a})}}]);