(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2879],{22879:(C,u,o)=>{o.r(u),o.d(u,{MboCustomerHelpModule:()=>d});var M=o(17007),t=o(78007),n=o(99877);const a=[{path:"",loadChildren:()=>o.e(7075).then(o.bind(o,27075)).then(l=>l.MboCustomerHelpPageModule)}];let d=(()=>{class l{}return l.\u0275fac=function(e){return new(e||l)},l.\u0275mod=n.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=n.\u0275\u0275defineInjector({imports:[M.CommonModule,t.RouterModule.forChild(a)]}),l})()}}]);