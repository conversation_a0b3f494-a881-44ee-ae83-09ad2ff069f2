(self.webpackChunkapp=self.webpackChunkapp||[]).push([[12],{44963:(<PERSON><PERSON>,he,x)=>{x.d(he,{c:()=>G});var e=x(72972),oe=x(78635);let U;const pe=y=>y.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),W=y=>(void 0===U&&(U=void 0===y.style.animationName&&void 0!==y.style.webkitAnimationName?"-webkit-":""),U),u=(y,p,m)=>{const Z=p.startsWith("animation")?W(y):"";y.style.setProperty(Z+p,m)},w=(y,p)=>{const m=p.startsWith("animation")?W(y):"";y.style.removeProperty(m+p)},F=[],j=(y=[],p)=>{if(void 0!==p){const m=Array.isArray(p)?p:[p];return[...y,...m]}return y},G=y=>{let p,m,Z,V,ae,ee,l,Q,v,A,te,$,re,ie=[],C=[],B=[],H=!1,S={},_=[],M=[],ne={},de=0,De=!1,E=!1,ue=!0,L=!1,me=!0,Ae=!1;const Ee=y,ke=[],we=[],Te=[],ye=[],R=[],D=[],I=[],se=[],fe=[],$e=[],be=[],_e="function"==typeof AnimationEffect||void 0!==e.w&&"function"==typeof e.w.AnimationEffect,Ie="function"==typeof Element&&"function"==typeof Element.prototype.animate&&_e,Le=()=>be,gt=(g,K)=>{const o=K.findIndex(r=>r.c===g);o>-1&&K.splice(o,1)},et=(g,K)=>((K?.oneTimeCallback?we:ke).push({c:g,o:K}),re),Ze=()=>{if(Ie)be.forEach(g=>{g.cancel()}),be.length=0;else{const g=ye.slice();(0,oe.r)(()=>{g.forEach(K=>{w(K,"animation-name"),w(K,"animation-duration"),w(K,"animation-timing-function"),w(K,"animation-iteration-count"),w(K,"animation-delay"),w(K,"animation-play-state"),w(K,"animation-fill-mode"),w(K,"animation-direction")})})}},ct=()=>{D.forEach(g=>{g?.parentNode&&g.parentNode.removeChild(g)}),D.length=0},tt=()=>void 0!==ae?ae:l?l.getFill():"both",Ke=()=>void 0!==v?v:void 0!==ee?ee:l?l.getDirection():"normal",Ve=()=>De?"linear":void 0!==Z?Z:l?l.getEasing():"linear",je=()=>E?0:void 0!==A?A:void 0!==m?m:l?l.getDuration():0,ze=()=>void 0!==V?V:l?l.getIterations():1,Ye=()=>void 0!==te?te:void 0!==p?p:l?l.getDelay():0,We=()=>{0!==de&&(de--,0===de&&((()=>{ut(),fe.forEach(a=>a()),$e.forEach(a=>a());const g=ue?1:0,K=_,o=M,r=ne;ye.forEach(a=>{const c=a.classList;K.forEach(h=>c.add(h)),o.forEach(h=>c.remove(h));for(const h in r)r.hasOwnProperty(h)&&u(a,h,r[h])}),A=void 0,v=void 0,te=void 0,ke.forEach(a=>a.c(g,re)),we.forEach(a=>a.c(g,re)),we.length=0,me=!0,ue&&(L=!0),ue=!0})(),l&&l.animationFinish()))},Fe=(g=!0)=>{ct();const K=(y=>(y.forEach(p=>{for(const m in p)if(p.hasOwnProperty(m)){const Z=p[m];if("easing"===m)p["animation-timing-function"]=Z,delete p[m];else{const V=pe(m);V!==m&&(p[V]=Z,delete p[m])}}}),y))(ie);ye.forEach(o=>{if(K.length>0){const r=((y=[])=>y.map(p=>{const m=p.offset,Z=[];for(const V in p)p.hasOwnProperty(V)&&"offset"!==V&&Z.push(`${V}: ${p[V]};`);return`${100*m}% { ${Z.join(" ")} }`}).join(" "))(K);$=void 0!==y?y:(y=>{let p=F.indexOf(y);return p<0&&(p=F.push(y)-1),`ion-animation-${p}`})(r);const a=((y,p,m)=>{var Z;const V=(y=>{const p=void 0!==y.getRootNode?y.getRootNode():y;return p.head||p})(m),ae=W(m),ee=V.querySelector("#"+y);if(ee)return ee;const ie=(null!==(Z=m.ownerDocument)&&void 0!==Z?Z:document).createElement("style");return ie.id=y,ie.textContent=`@${ae}keyframes ${y} { ${p} } @${ae}keyframes ${y}-alt { ${p} }`,V.appendChild(ie),ie})($,r,o);D.push(a),u(o,"animation-duration",`${je()}ms`),u(o,"animation-timing-function",Ve()),u(o,"animation-delay",`${Ye()}ms`),u(o,"animation-fill-mode",tt()),u(o,"animation-direction",Ke());const c=ze()===1/0?"infinite":ze().toString();u(o,"animation-iteration-count",c),u(o,"animation-play-state","paused"),g&&u(o,"animation-name",`${a.id}-alt`),(0,oe.r)(()=>{u(o,"animation-name",a.id||null)})}})},$t=(g=!0)=>{(()=>{I.forEach(r=>r()),se.forEach(r=>r());const g=C,K=B,o=S;ye.forEach(r=>{const a=r.classList;g.forEach(c=>a.add(c)),K.forEach(c=>a.remove(c));for(const c in o)o.hasOwnProperty(c)&&u(r,c,o[c])})})(),ie.length>0&&(Ie?(ye.forEach(g=>{const K=g.animate(ie,{id:Ee,delay:Ye(),duration:je(),easing:Ve(),iterations:ze(),fill:tt(),direction:Ke()});K.pause(),be.push(K)}),be.length>0&&(be[0].onfinish=()=>{We()})):Fe(g)),H=!0},qe=g=>{if(g=Math.min(Math.max(g,0),.9999),Ie)be.forEach(K=>{K.currentTime=K.effect.getComputedTiming().delay+je()*g,K.pause()});else{const K=`-${je()*g}ms`;ye.forEach(o=>{ie.length>0&&(u(o,"animation-delay",K),u(o,"animation-play-state","paused"))})}},Xe=g=>{be.forEach(K=>{K.effect.updateTiming({delay:Ye(),duration:je(),easing:Ve(),iterations:ze(),fill:tt(),direction:Ke()})}),void 0!==g&&qe(g)},dt=(g=!0,K)=>{(0,oe.r)(()=>{ye.forEach(o=>{u(o,"animation-name",$||null),u(o,"animation-duration",`${je()}ms`),u(o,"animation-timing-function",Ve()),u(o,"animation-delay",void 0!==K?`-${K*je()}ms`:`${Ye()}ms`),u(o,"animation-fill-mode",tt()||null),u(o,"animation-direction",Ke()||null);const r=ze()===1/0?"infinite":ze().toString();u(o,"animation-iteration-count",r),g&&u(o,"animation-name",`${$}-alt`),(0,oe.r)(()=>{u(o,"animation-name",$||null)})})})},Pe=(g=!1,K=!0,o)=>(g&&R.forEach(r=>{r.update(g,K,o)}),Ie?Xe(o):dt(K,o),re),Se=()=>{H&&(Ie?be.forEach(g=>{g.pause()}):ye.forEach(g=>{u(g,"animation-play-state","paused")}),Ae=!0)},Gt=()=>{Q=void 0,We()},ut=()=>{Q&&clearTimeout(Q)},st=g=>new Promise(K=>{g?.sync&&(E=!0,et(()=>E=!1,{oneTimeCallback:!0})),H||$t(),L&&(Ie?(qe(0),Xe()):dt(),L=!1),me&&(de=R.length+1,me=!1);const o=()=>{gt(r,we),K()},r=()=>{gt(o,Te),K()};et(r,{oneTimeCallback:!0}),((g,K)=>{Te.push({c:g,o:{oneTimeCallback:!0}})})(o),R.forEach(a=>{a.play()}),Ie?(be.forEach(g=>{g.play()}),(0===ie.length||0===ye.length)&&We()):(()=>{if(ut(),(0,oe.r)(()=>{ye.forEach(g=>{ie.length>0&&u(g,"animation-play-state","running")})}),0===ie.length||0===ye.length)We();else{const g=Ye()||0,K=je()||0,o=ze()||1;isFinite(o)&&(Q=setTimeout(Gt,g+K*o+100)),((y,p)=>{let m;const Z={passive:!0},ae=ee=>{y===ee.target&&(m&&m(),ut(),(0,oe.r)(()=>{ye.forEach(g=>{w(g,"animation-duration"),w(g,"animation-delay"),w(g,"animation-play-state")}),(0,oe.r)(We)}))};y&&(y.addEventListener("webkitAnimationEnd",ae,Z),y.addEventListener("animationend",ae,Z),m=()=>{y.removeEventListener("webkitAnimationEnd",ae,Z),y.removeEventListener("animationend",ae,Z)})})(ye[0])}})(),Ae=!1}),pt=(g,K)=>{const o=ie[0];return void 0===o||void 0!==o.offset&&0!==o.offset?ie=[{offset:0,[g]:K},...ie]:o[g]=K,re};return re={parentAnimation:l,elements:ye,childAnimations:R,id:Ee,animationFinish:We,from:pt,to:(g,K)=>{const o=ie[ie.length-1];return void 0===o||void 0!==o.offset&&1!==o.offset?ie=[...ie,{offset:1,[g]:K}]:o[g]=K,re},fromTo:(g,K,o)=>pt(g,K).to(g,o),parent:g=>(l=g,re),play:st,pause:()=>(R.forEach(g=>{g.pause()}),Se(),re),stop:()=>{R.forEach(g=>{g.stop()}),H&&(Ze(),H=!1),De=!1,E=!1,me=!0,v=void 0,A=void 0,te=void 0,de=0,L=!1,ue=!0,Ae=!1,Te.forEach(g=>g.c(0,re)),Te.length=0},destroy:g=>(R.forEach(K=>{K.destroy(g)}),(g=>{Ze(),g&&ct()})(g),ye.length=0,R.length=0,ie.length=0,ke.length=0,we.length=0,H=!1,me=!0,re),keyframes:g=>{const K=ie!==g;return ie=g,K&&(g=>{Ie?Le().forEach(K=>{const o=K.effect;if(o.setKeyframes)o.setKeyframes(g);else{const r=new KeyframeEffect(o.target,g,o.getTiming());K.effect=r}}):Fe()})(ie),re},addAnimation:g=>{if(null!=g)if(Array.isArray(g))for(const K of g)K.parent(re),R.push(K);else g.parent(re),R.push(g);return re},addElement:g=>{if(null!=g)if(1===g.nodeType)ye.push(g);else if(g.length>=0)for(let K=0;K<g.length;K++)ye.push(g[K]);else console.error("Invalid addElement value");return re},update:Pe,fill:g=>(ae=g,Pe(!0),re),direction:g=>(ee=g,Pe(!0),re),iterations:g=>(V=g,Pe(!0),re),duration:g=>(!Ie&&0===g&&(g=1),m=g,Pe(!0),re),easing:g=>(Z=g,Pe(!0),re),delay:g=>(p=g,Pe(!0),re),getWebAnimations:Le,getKeyframes:()=>ie,getFill:tt,getDirection:Ke,getDelay:Ye,getIterations:ze,getEasing:Ve,getDuration:je,afterAddRead:g=>(fe.push(g),re),afterAddWrite:g=>($e.push(g),re),afterClearStyles:(g=[])=>{for(const K of g)ne[K]="";return re},afterStyles:(g={})=>(ne=g,re),afterRemoveClass:g=>(M=j(M,g),re),afterAddClass:g=>(_=j(_,g),re),beforeAddRead:g=>(I.push(g),re),beforeAddWrite:g=>(se.push(g),re),beforeClearStyles:(g=[])=>{for(const K of g)S[K]="";return re},beforeStyles:(g={})=>(S=g,re),beforeRemoveClass:g=>(B=j(B,g),re),beforeAddClass:g=>(C=j(C,g),re),onFinish:et,isRunning:()=>0!==de&&!Ae,progressStart:(g=!1,K)=>(R.forEach(o=>{o.progressStart(g,K)}),Se(),De=g,H||$t(),Pe(!1,!0,K),re),progressStep:g=>(R.forEach(K=>{K.progressStep(g)}),qe(g),re),progressEnd:(g,K,o)=>(De=!1,R.forEach(r=>{r.progressEnd(g,K,o)}),void 0!==o&&(A=o),L=!1,ue=!0,0===g?(v="reverse"===Ke()?"normal":"reverse","reverse"===v&&(ue=!1),Ie?(Pe(),qe(1-K)):(te=(1-K)*je()*-1,Pe(!1,!1))):1===g&&(Ie?(Pe(),qe(K)):(te=K*je()*-1,Pe(!1,!1))),void 0!==g&&!l&&st(),re)}}},87036:(Oe,he,x)=>{x.d(he,{E:()=>X,I:()=>u,a:()=>e,s:()=>w});const e=F=>{try{if(F instanceof u)return F.value;if(!b()||"string"!=typeof F||""===F)return F;if(F.includes("onload="))return"";const J=document.createDocumentFragment(),T=document.createElement("div");J.appendChild(T),T.innerHTML=F,W.forEach(y=>{const p=J.querySelectorAll(y);for(let m=p.length-1;m>=0;m--){const Z=p[m];Z.parentNode?Z.parentNode.removeChild(Z):J.removeChild(Z);const V=U(Z);for(let ae=0;ae<V.length;ae++)oe(V[ae])}});const k=U(J);for(let y=0;y<k.length;y++)oe(k[y]);const j=document.createElement("div");j.appendChild(J);const G=j.querySelector("div");return null!==G?G.innerHTML:j.innerHTML}catch(J){return console.error(J),""}},oe=F=>{if(F.nodeType&&1!==F.nodeType)return;if(typeof NamedNodeMap<"u"&&!(F.attributes instanceof NamedNodeMap))return void F.remove();for(let T=F.attributes.length-1;T>=0;T--){const k=F.attributes.item(T),j=k.name;if(!pe.includes(j.toLowerCase())){F.removeAttribute(j);continue}const G=k.value,y=F[j];(null!=G&&G.toLowerCase().includes("javascript:")||null!=y&&y.toLowerCase().includes("javascript:"))&&F.removeAttribute(j)}const J=U(F);for(let T=0;T<J.length;T++)oe(J[T])},U=F=>null!=F.children?F.children:F.childNodes,b=()=>{var F;const T=null===(F=window?.Ionic)||void 0===F?void 0:F.config;return!T||(T.get?T.get("sanitizerEnabled",!0):!0===T.sanitizerEnabled||void 0===T.sanitizerEnabled)},pe=["class","id","href","src","name","slot"],W=["script","style","iframe","meta","link","object","embed"];class u{constructor(J){this.value=J}}const w=F=>{const J=window,T=J.Ionic;if(!T||!T.config||"Object"===T.config.constructor.name)return J.Ionic=J.Ionic||{},J.Ionic.config=Object.assign(Object.assign({},J.Ionic.config),F),J.Ionic.config},X=!1},65069:(Oe,he,x)=>{x.d(he,{g:()=>e});const e=(W,u,w,q,X)=>U(W[1],u[1],w[1],q[1],X).map(F=>oe(W[0],u[0],w[0],q[0],F)),oe=(W,u,w,q,X)=>X*(3*u*Math.pow(X-1,2)+X*(-3*w*X+3*w+q*X))-W*Math.pow(X-1,3),U=(W,u,w,q,X)=>pe((q-=X)-3*(w-=X)+3*(u-=X)-(W-=X),3*w-6*u+3*W,3*u-3*W,W).filter(J=>J>=0&&J<=1),pe=(W,u,w,q)=>{if(0===W)return((W,u,w)=>{const q=u*u-4*W*w;return q<0?[]:[(-u+Math.sqrt(q))/(2*W),(-u-Math.sqrt(q))/(2*W)]})(u,w,q);const X=(3*(w/=W)-(u/=W)*u)/3,F=(2*u*u*u-9*u*w+27*(q/=W))/27;if(0===X)return[Math.pow(-F,1/3)];if(0===F)return[Math.sqrt(-X),-Math.sqrt(-X)];const J=Math.pow(F/2,2)+Math.pow(X/3,3);if(0===J)return[Math.pow(F/2,.5)-u/3];if(J>0)return[Math.pow(-F/2+Math.sqrt(J),1/3)-Math.pow(F/2+Math.sqrt(J),1/3)-u/3];const T=Math.sqrt(Math.pow(-X/3,3)),k=Math.acos(-F/(2*Math.sqrt(Math.pow(-X/3,3)))),j=2*Math.pow(T,1/3);return[j*Math.cos(k/3)-u/3,j*Math.cos((k+2*Math.PI)/3)-u/3,j*Math.cos((k+4*Math.PI)/3)-u/3]}},25030:(Oe,he,x)=>{x.d(he,{C:()=>pe,a:()=>U,d:()=>b});var e=x(15861),oe=x(78635);const U=function(){var W=(0,e.Z)(function*(u,w,q,X,F,J){var T;if(u)return u.attachViewToDom(w,q,F,X);if(!(J||"string"==typeof q||q instanceof HTMLElement))throw new Error("framework delegate is missing");const k="string"==typeof q?null===(T=w.ownerDocument)||void 0===T?void 0:T.createElement(q):q;return X&&X.forEach(j=>k.classList.add(j)),F&&Object.assign(k,F),w.appendChild(k),yield new Promise(j=>(0,oe.c)(k,j)),k});return function(w,q,X,F,J,T){return W.apply(this,arguments)}}(),b=(W,u)=>{if(u){if(W)return W.removeViewFromDom(u.parentElement,u);u.remove()}return Promise.resolve()},pe=()=>{let W,u;return{attachViewToDom:function(){var X=(0,e.Z)(function*(F,J,T={},k=[]){var j,G;let y;if(W=F,J){const m="string"==typeof J?null===(j=W.ownerDocument)||void 0===j?void 0:j.createElement(J):J;k.forEach(Z=>m.classList.add(Z)),Object.assign(m,T),W.appendChild(m),y=m,yield new Promise(Z=>(0,oe.c)(m,Z))}else if(W.children.length>0&&("ION-MODAL"===W.tagName||"ION-POPOVER"===W.tagName)&&!(y=W.children[0]).classList.contains("ion-delegate-host")){const Z=null===(G=W.ownerDocument)||void 0===G?void 0:G.createElement("div");Z.classList.add("ion-delegate-host"),k.forEach(V=>Z.classList.add(V)),Z.append(...W.children),W.appendChild(Z),y=Z}const p=document.querySelector("ion-app")||document.body;return u=document.createComment("ionic teleport"),W.parentNode.insertBefore(u,W),p.appendChild(W),y??W});return function(J,T){return X.apply(this,arguments)}}(),removeViewFromDom:()=>(W&&u&&(u.parentNode.insertBefore(W,u),u.remove()),Promise.resolve())}}},22889:(Oe,he,x)=>{x.d(he,{G:()=>pe});class oe{constructor(u,w,q,X,F){this.id=w,this.name=q,this.disableScroll=F,this.priority=1e6*X+w,this.ctrl=u}canStart(){return!!this.ctrl&&this.ctrl.canStart(this.name)}start(){return!!this.ctrl&&this.ctrl.start(this.name,this.id,this.priority)}capture(){if(!this.ctrl)return!1;const u=this.ctrl.capture(this.name,this.id,this.priority);return u&&this.disableScroll&&this.ctrl.disableScroll(this.id),u}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}}class U{constructor(u,w,q,X){this.id=w,this.disable=q,this.disableScroll=X,this.ctrl=u}block(){if(this.ctrl){if(this.disable)for(const u of this.disable)this.ctrl.disableGesture(u,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(const u of this.disable)this.ctrl.enableGesture(u,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}}const b="backdrop-no-scroll",pe=new class e{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(u){var w;return new oe(this,this.newID(),u.name,null!==(w=u.priority)&&void 0!==w?w:0,!!u.disableScroll)}createBlocker(u={}){return new U(this,this.newID(),u.disable,!!u.disableScroll)}start(u,w,q){return this.canStart(u)?(this.requestedStart.set(w,q),!0):(this.requestedStart.delete(w),!1)}capture(u,w,q){if(!this.start(u,w,q))return!1;const X=this.requestedStart;let F=-1e4;if(X.forEach(J=>{F=Math.max(F,J)}),F===q){this.capturedId=w,X.clear();const J=new CustomEvent("ionGestureCaptured",{detail:{gestureName:u}});return document.dispatchEvent(J),!0}return X.delete(w),!1}release(u){this.requestedStart.delete(u),this.capturedId===u&&(this.capturedId=void 0)}disableGesture(u,w){let q=this.disabledGestures.get(u);void 0===q&&(q=new Set,this.disabledGestures.set(u,q)),q.add(w)}enableGesture(u,w){const q=this.disabledGestures.get(u);void 0!==q&&q.delete(w)}disableScroll(u){this.disabledScroll.add(u),1===this.disabledScroll.size&&document.body.classList.add(b)}enableScroll(u){this.disabledScroll.delete(u),0===this.disabledScroll.size&&document.body.classList.remove(b)}canStart(u){return!(void 0!==this.capturedId||this.isDisabled(u))}isCaptured(){return void 0!==this.capturedId}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(u){const w=this.disabledGestures.get(u);return!!(w&&w.size>0)}newID(){return this.gestureId++,this.gestureId}}},33006:(Oe,he,x)=>{x.r(he),x.d(he,{MENU_BACK_BUTTON_PRIORITY:()=>q,OVERLAY_BACK_BUTTON_PRIORITY:()=>w,blockHardwareBackButton:()=>W,shouldUseCloseWatcher:()=>pe,startHardwareBackButton:()=>u});var e=x(15861),oe=x(72972),U=x(37943);x(42477);const pe=()=>U.c.get("experimentalCloseWatcher",!1)&&void 0!==oe.w&&"CloseWatcher"in oe.w,W=()=>{document.addEventListener("backbutton",()=>{})},u=()=>{const X=document;let F=!1;const J=()=>{if(F)return;let T=0,k=[];const j=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(p,m){k.push({priority:p,handler:m,id:T++})}}});X.dispatchEvent(j);const G=function(){var p=(0,e.Z)(function*(m){try{if(m?.handler){const Z=m.handler(y);null!=Z&&(yield Z)}}catch(Z){console.error(Z)}});return function(Z){return p.apply(this,arguments)}}(),y=()=>{if(k.length>0){let p={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};k.forEach(m=>{m.priority>=p.priority&&(p=m)}),F=!0,k=k.filter(m=>m.id!==p.id),G(p).then(()=>F=!1)}};y()};if(pe()){let T;const k=()=>{T?.destroy(),T=new oe.w.CloseWatcher,T.onclose=()=>{J(),k()}};k()}else X.addEventListener("backbutton",J)},w=100,q=99},78635:(Oe,he,x)=>{x.d(he,{a:()=>w,b:()=>q,c:()=>U,d:()=>G,e:()=>j,f:()=>k,g:()=>X,h:()=>T,i:()=>u,j:()=>ae,k:()=>pe,l:()=>y,m:()=>b,n:()=>J,o:()=>p,p:()=>V,q:()=>ee,r:()=>F,s:()=>ie,t:()=>e,u:()=>m,v:()=>Z});const e=(C,B=0)=>new Promise(H=>{oe(C,B,H)}),oe=(C,B=0,H)=>{let l,S;const _={passive:!0},ne=()=>{l&&l()},de=De=>{(void 0===De||C===De.target)&&(ne(),H(De))};return C&&(C.addEventListener("webkitTransitionEnd",de,_),C.addEventListener("transitionend",de,_),S=setTimeout(de,B+500),l=()=>{void 0!==S&&(clearTimeout(S),S=void 0),C.removeEventListener("webkitTransitionEnd",de,_),C.removeEventListener("transitionend",de,_)}),ne},U=(C,B)=>{C.componentOnReady?C.componentOnReady().then(H=>B(H)):F(()=>B(C))},b=C=>void 0!==C.componentOnReady,pe=(C,B=[])=>{const H={};return B.forEach(l=>{C.hasAttribute(l)&&(null!==C.getAttribute(l)&&(H[l]=C.getAttribute(l)),C.removeAttribute(l))}),H},W=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],u=(C,B)=>{let H=W;return B&&B.length>0&&(H=H.filter(l=>!B.includes(l))),pe(C,H)},w=(C,B,H,l)=>{var S;if(typeof window<"u"){const M=null===(S=window?.Ionic)||void 0===S?void 0:S.config;if(M){const ne=M.get("_ael");if(ne)return ne(C,B,H,l);if(M._ael)return M._ael(C,B,H,l)}}return C.addEventListener(B,H,l)},q=(C,B,H,l)=>{var S;if(typeof window<"u"){const M=null===(S=window?.Ionic)||void 0===S?void 0:S.config;if(M){const ne=M.get("_rel");if(ne)return ne(C,B,H,l);if(M._rel)return M._rel(C,B,H,l)}}return C.removeEventListener(B,H,l)},X=(C,B=C)=>C.shadowRoot||B,F=C=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(C):"function"==typeof requestAnimationFrame?requestAnimationFrame(C):setTimeout(C),J=C=>!!C.shadowRoot&&!!C.attachShadow,T=C=>{const B=C.closest("ion-item");return B?B.querySelector("ion-label"):null},k=C=>{if(C.focus(),C.classList.contains("ion-focusable")){const B=C.closest("ion-app");B&&B.setFocus([C])}},j=(C,B)=>{let H;const l=C.getAttribute("aria-labelledby"),S=C.id;let _=null!==l&&""!==l.trim()?l:B+"-lbl",M=null!==l&&""!==l.trim()?document.getElementById(l):T(C);return M?(null===l&&(M.id=_),H=M.textContent,M.setAttribute("aria-hidden","true")):""!==S.trim()&&(M=document.querySelector(`label[for="${S}"]`),M&&(""!==M.id?_=M.id:M.id=_=`${S}-lbl`,H=M.textContent)),{label:M,labelId:_,labelText:H}},G=(C,B,H,l,S)=>{if(C||J(B)){let _=B.querySelector("input.aux-input");_||(_=B.ownerDocument.createElement("input"),_.type="hidden",_.classList.add("aux-input"),B.appendChild(_)),_.disabled=S,_.name=H,_.value=l||""}},y=(C,B,H)=>Math.max(C,Math.min(B,H)),p=(C,B)=>{if(!C){const H="ASSERT: "+B;throw console.error(H),new Error(H)}},m=C=>C.timeStamp||Date.now(),Z=C=>{if(C){const B=C.changedTouches;if(B&&B.length>0){const H=B[0];return{x:H.clientX,y:H.clientY}}if(void 0!==C.pageX)return{x:C.pageX,y:C.pageY}}return{x:0,y:0}},V=C=>{const B="rtl"===document.dir;switch(C){case"start":return B;case"end":return!B;default:throw new Error(`"${C}" is not a valid value for [side]. Use "start" or "end" instead.`)}},ae=(C,B)=>{const H=C._original||C;return{_original:C,emit:ee(H.emit.bind(H),B)}},ee=(C,B=0)=>{let H;return(...l)=>{clearTimeout(H),H=setTimeout(C,B,...l)}},ie=(C,B)=>{if(C??(C={}),B??(B={}),C===B)return!0;const H=Object.keys(C);if(H.length!==Object.keys(B).length)return!1;for(const l of H)if(!(l in B)||C[l]!==B[l])return!1;return!0}},35067:(Oe,he,x)=>{x.r(he),x.d(he,{GESTURE_CONTROLLER:()=>e.G,createGesture:()=>q});var e=x(22889);const oe=(T,k,j,G)=>{const y=U(T)?{capture:!!G.capture,passive:!!G.passive}:!!G.capture;let p,m;return T.__zone_symbol__addEventListener?(p="__zone_symbol__addEventListener",m="__zone_symbol__removeEventListener"):(p="addEventListener",m="removeEventListener"),T[p](k,j,y),()=>{T[m](k,j,y)}},U=T=>{if(void 0===b)try{const k=Object.defineProperty({},"passive",{get:()=>{b=!0}});T.addEventListener("optsTest",()=>{},k)}catch{b=!1}return!!b};let b;const u=T=>T instanceof Document?T:T.ownerDocument,q=T=>{let k=!1,j=!1,G=!0,y=!1;const p=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},T),m=p.canStart,Z=p.onWillStart,V=p.onStart,ae=p.onEnd,ee=p.notCaptured,ie=p.onMove,C=p.threshold,B=p.passive,H=p.blurOnStart,l={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},S=((T,k,j)=>{const G=j*(Math.PI/180),y="x"===T,p=Math.cos(G),m=k*k;let Z=0,V=0,ae=!1,ee=0;return{start(ie,C){Z=ie,V=C,ee=0,ae=!0},detect(ie,C){if(!ae)return!1;const B=ie-Z,H=C-V,l=B*B+H*H;if(l<m)return!1;const S=Math.sqrt(l),_=(y?B:H)/S;return ee=_>p?1:_<-p?-1:0,ae=!1,!0},isGesture:()=>0!==ee,getDirection:()=>ee}})(p.direction,p.threshold,p.maxAngle),_=e.G.createGesture({name:T.gestureName,priority:T.gesturePriority,disableScroll:T.disableScroll}),de=()=>{k&&(y=!1,ie&&ie(l))},De=()=>!!_.capture()&&(k=!0,G=!1,l.startX=l.currentX,l.startY=l.currentY,l.startTime=l.currentTime,Z?Z(l).then(Q):Q(),!0),Q=()=>{H&&(()=>{if(typeof document<"u"){const L=document.activeElement;L?.blur&&L.blur()}})(),V&&V(l),G=!0},v=()=>{k=!1,j=!1,y=!1,G=!0,_.release()},A=L=>{const me=k,$=G;if(v(),$){if(X(l,L),me)return void(ae&&ae(l));ee&&ee(l)}},te=((T,k,j,G,y)=>{let p,m,Z,V,ae,ee,ie,C=0;const B=E=>{C=Date.now()+2e3,k(E)&&(!m&&j&&(m=oe(T,"touchmove",j,y)),Z||(Z=oe(E.target,"touchend",l,y)),V||(V=oe(E.target,"touchcancel",l,y)))},H=E=>{C>Date.now()||k(E)&&(!ee&&j&&(ee=oe(u(T),"mousemove",j,y)),ie||(ie=oe(u(T),"mouseup",S,y)))},l=E=>{_(),G&&G(E)},S=E=>{M(),G&&G(E)},_=()=>{m&&m(),Z&&Z(),V&&V(),m=Z=V=void 0},M=()=>{ee&&ee(),ie&&ie(),ee=ie=void 0},ne=()=>{_(),M()},de=(E=!0)=>{E?(p||(p=oe(T,"touchstart",B,y)),ae||(ae=oe(T,"mousedown",H,y))):(p&&p(),ae&&ae(),p=ae=void 0,ne())};return{enable:de,stop:ne,destroy:()=>{de(!1),G=j=k=void 0}}})(p.el,L=>{const me=J(L);return!(j||!G||(F(L,l),l.startX=l.currentX,l.startY=l.currentY,l.startTime=l.currentTime=me,l.velocityX=l.velocityY=l.deltaX=l.deltaY=0,l.event=L,m&&!1===m(l))||(_.release(),!_.start()))&&(j=!0,0===C?De():(S.start(l.startX,l.startY),!0))},L=>{k?!y&&G&&(y=!0,X(l,L),requestAnimationFrame(de)):(X(l,L),S.detect(l.currentX,l.currentY)&&(!S.isGesture()||!De())&&ue())},A,{capture:!1,passive:B}),ue=()=>{v(),te.stop(),ee&&ee(l)};return{enable(L=!0){L||(k&&A(void 0),v()),te.enable(L)},destroy(){_.destroy(),te.destroy()}}},X=(T,k)=>{if(!k)return;const j=T.currentX,G=T.currentY,y=T.currentTime;F(k,T);const p=T.currentX,m=T.currentY,V=(T.currentTime=J(k))-y;if(V>0&&V<100){const ee=(m-G)/V;T.velocityX=(p-j)/V*.7+.3*T.velocityX,T.velocityY=.7*ee+.3*T.velocityY}T.deltaX=p-T.startX,T.deltaY=m-T.startY,T.event=k},F=(T,k)=>{let j=0,G=0;if(T){const y=T.changedTouches;if(y&&y.length>0){const p=y[0];j=p.clientX,G=p.clientY}else void 0!==T.pageX&&(j=T.pageX,G=T.pageY)}k.currentX=j,k.currentY=G},J=T=>T.timeStamp||Date.now()},16523:(Oe,he,x)=>{x.d(he,{m:()=>T});var e=x(15861),oe=x(72972),U=x(33006),b=x(28909),pe=x(78635),W=x(37943),u=x(44963);const w=k=>(0,u.c)().duration(k?400:300),q=k=>{let j,G;const y=k.width+8,p=(0,u.c)(),m=(0,u.c)();k.isEndSide?(j=y+"px",G="0px"):(j=-y+"px",G="0px"),p.addElement(k.menuInnerEl).fromTo("transform",`translateX(${j})`,`translateX(${G})`);const V="ios"===(0,W.b)(k),ae=V?.2:.25;return m.addElement(k.backdropEl).fromTo("opacity",.01,ae),w(V).addAnimation([p,m])},X=k=>{let j,G;const y=(0,W.b)(k),p=k.width;k.isEndSide?(j=-p+"px",G=p+"px"):(j=p+"px",G=-p+"px");const m=(0,u.c)().addElement(k.menuInnerEl).fromTo("transform",`translateX(${G})`,"translateX(0px)"),Z=(0,u.c)().addElement(k.contentEl).fromTo("transform","translateX(0px)",`translateX(${j})`),V=(0,u.c)().addElement(k.backdropEl).fromTo("opacity",.01,.32);return w("ios"===y).addAnimation([m,Z,V])},F=k=>{const j=(0,W.b)(k),G=k.width*(k.isEndSide?-1:1)+"px",y=(0,u.c)().addElement(k.contentEl).fromTo("transform","translateX(0px)",`translateX(${G})`);return w("ios"===j).addAnimation(y)},T=(()=>{const k=new Map,j=[],G=function(){var v=(0,e.Z)(function*(A){const te=yield ee(A,!0);return!!te&&te.open()});return function(te){return v.apply(this,arguments)}}(),y=function(){var v=(0,e.Z)(function*(A){const te=yield void 0!==A?ee(A,!0):ie();return void 0!==te&&te.close()});return function(te){return v.apply(this,arguments)}}(),p=function(){var v=(0,e.Z)(function*(A){const te=yield ee(A,!0);return!!te&&te.toggle()});return function(te){return v.apply(this,arguments)}}(),m=function(){var v=(0,e.Z)(function*(A,te){const ue=yield ee(te);return ue&&(ue.disabled=!A),ue});return function(te,ue){return v.apply(this,arguments)}}(),Z=function(){var v=(0,e.Z)(function*(A,te){const ue=yield ee(te);return ue&&(ue.swipeGesture=A),ue});return function(te,ue){return v.apply(this,arguments)}}(),V=function(){var v=(0,e.Z)(function*(A){if(null!=A){const te=yield ee(A);return void 0!==te&&te.isOpen()}return void 0!==(yield ie())});return function(te){return v.apply(this,arguments)}}(),ae=function(){var v=(0,e.Z)(function*(A){const te=yield ee(A);return!!te&&!te.disabled});return function(te){return v.apply(this,arguments)}}(),ee=function(){var v=(0,e.Z)(function*(A,te=!1){if(yield Q(),"start"===A||"end"===A){const L=j.filter($=>$.side===A&&!$.disabled);if(L.length>=1)return L.length>1&&te&&(0,b.p)(`menuController queried for a menu on the "${A}" side, but ${L.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,L.map($=>$.el)),L[0].el;const me=j.filter($=>$.side===A);if(me.length>=1)return me.length>1&&te&&(0,b.p)(`menuController queried for a menu on the "${A}" side, but ${me.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,me.map($=>$.el)),me[0].el}else if(null!=A)return E(L=>L.menuId===A);return E(L=>!L.disabled)||(j.length>0?j[0].el:void 0)});return function(te){return v.apply(this,arguments)}}(),ie=function(){var v=(0,e.Z)(function*(){return yield Q(),ne()});return function(){return v.apply(this,arguments)}}(),C=function(){var v=(0,e.Z)(function*(){return yield Q(),de()});return function(){return v.apply(this,arguments)}}(),B=function(){var v=(0,e.Z)(function*(){return yield Q(),De()});return function(){return v.apply(this,arguments)}}(),H=(v,A)=>{k.set(v,A)},_=function(){var v=(0,e.Z)(function*(A,te,ue){if(De())return!1;if(te){const L=yield ie();L&&A.el!==L&&(yield L.setOpen(!1,!1))}return A._setOpen(te,ue)});return function(te,ue,L){return v.apply(this,arguments)}}(),ne=()=>E(v=>v._isOpen),de=()=>j.map(v=>v.el),De=()=>j.some(v=>v.isAnimating),E=v=>{const A=j.find(v);if(void 0!==A)return A.el},Q=()=>Promise.all(Array.from(document.querySelectorAll("ion-menu")).map(v=>new Promise(A=>(0,pe.c)(v,A))));return H("reveal",F),H("push",X),H("overlay",q),null==oe.d||oe.d.addEventListener("ionBackButton",v=>{const A=ne();A&&v.detail.register(U.MENU_BACK_BUTTON_PRIORITY,()=>A.close())}),{registerAnimation:H,get:ee,getMenus:C,getOpen:ie,isEnabled:ae,swipeGesture:Z,isAnimating:B,isOpen:V,enable:m,toggle:p,close:y,open:G,_getOpenSync:ne,_createAnimation:(v,A)=>{const te=k.get(v);if(!te)throw new Error("animation not registered");return te(A)},_register:v=>{j.indexOf(v)<0&&j.push(v)},_unregister:v=>{const A=j.indexOf(v);A>-1&&j.splice(A,1)},_setOpen:_}})()},28909:(Oe,he,x)=>{x.d(he,{a:()=>oe,b:()=>U,p:()=>e});const e=(b,...pe)=>console.warn(`[Ionic Warning]: ${b}`,...pe),oe=(b,...pe)=>console.error(`[Ionic Error]: ${b}`,...pe),U=(b,...pe)=>console.error(`<${b.tagName.toLowerCase()}> must be used inside ${pe.join(" or ")}.`)},42477:(Oe,he,x)=>{x.d(he,{B:()=>J,H:()=>De,a:()=>Gt,b:()=>Nt,c:()=>$,d:()=>ke,e:()=>g,f:()=>Ee,g:()=>re,h:()=>ne,i:()=>Ke,j:()=>T,r:()=>Ht,w:()=>K});var e=x(15861);let b,pe,W,u=!1,w=!1,q=!1,X=!1,F=!1;const J={isDev:!1,isBrowser:!0,isServer:!1,isTesting:!1},T=o=>{const r=new URL(o,Ce.$resourcesUrl$);return r.origin!==Je.location.origin?r.href:r.pathname},Z="s-id",V="sty-id",ie="slot-fb{display:contents}slot-fb[hidden]{display:none}",C="http://www.w3.org/1999/xlink",B={},_=o=>"object"==(o=typeof o)||"function"===o;function M(o){var r,a,c;return null!==(c=null===(a=null===(r=o.head)||void 0===r?void 0:r.querySelector('meta[name="csp-nonce"]'))||void 0===a?void 0:a.getAttribute("content"))&&void 0!==c?c:void 0}const ne=(o,r,...a)=>{let c=null,h=null,f=null,N=!1,O=!1;const z=[],P=ce=>{for(let le=0;le<ce.length;le++)c=ce[le],Array.isArray(c)?P(c):null!=c&&"boolean"!=typeof c&&((N="function"!=typeof o&&!_(c))&&(c=String(c)),N&&O?z[z.length-1].$text$+=c:z.push(N?de(null,c):c),O=N)};if(P(a),r){r.key&&(h=r.key),r.name&&(f=r.name);{const ce=r.className||r.class;ce&&(r.class="object"!=typeof ce?ce:Object.keys(ce).filter(le=>ce[le]).join(" "))}}if("function"==typeof o)return o(null===r?{}:r,z,Q);const Y=de(o,null);return Y.$attrs$=r,z.length>0&&(Y.$children$=z),Y.$key$=h,Y.$name$=f,Y},de=(o,r)=>({$flags$:0,$tag$:o,$text$:r,$elm$:null,$children$:null,$attrs$:null,$key$:null,$name$:null}),De={},Q={forEach:(o,r)=>o.map(v).forEach(r),map:(o,r)=>o.map(v).map(r).map(A)},v=o=>({vattrs:o.$attrs$,vchildren:o.$children$,vkey:o.$key$,vname:o.$name$,vtag:o.$tag$,vtext:o.$text$}),A=o=>{if("function"==typeof o.vtag){const a=Object.assign({},o.vattrs);return o.vkey&&(a.key=o.vkey),o.vname&&(a.name=o.vname),ne(o.vtag,a,...o.vchildren||[])}const r=de(o.vtag,o.vtext);return r.$attrs$=o.vattrs,r.$children$=o.vchildren,r.$key$=o.vkey,r.$name$=o.vname,r},ue=(o,r,a,c,h,f,N)=>{let O,z,P,Y;if(1===f.nodeType){for(O=f.getAttribute("c-id"),O&&(z=O.split("."),(z[0]===N||"0"===z[0])&&(P={$flags$:0,$hostId$:z[0],$nodeId$:z[1],$depth$:z[2],$index$:z[3],$tag$:f.tagName.toLowerCase(),$elm$:f,$attrs$:null,$children$:null,$key$:null,$name$:null,$text$:null},r.push(P),f.removeAttribute("c-id"),o.$children$||(o.$children$=[]),o.$children$[P.$index$]=P,o=P,c&&"0"===P.$depth$&&(c[P.$index$]=P.$elm$))),Y=f.childNodes.length-1;Y>=0;Y--)ue(o,r,a,c,h,f.childNodes[Y],N);if(f.shadowRoot)for(Y=f.shadowRoot.childNodes.length-1;Y>=0;Y--)ue(o,r,a,c,h,f.shadowRoot.childNodes[Y],N)}else if(8===f.nodeType)z=f.nodeValue.split("."),(z[1]===N||"0"===z[1])&&(O=z[0],P={$flags$:0,$hostId$:z[1],$nodeId$:z[2],$depth$:z[3],$index$:z[4],$elm$:f,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null},"t"===O?(P.$elm$=f.nextSibling,P.$elm$&&3===P.$elm$.nodeType&&(P.$text$=P.$elm$.textContent,r.push(P),f.remove(),o.$children$||(o.$children$=[]),o.$children$[P.$index$]=P,c&&"0"===P.$depth$&&(c[P.$index$]=P.$elm$))):P.$hostId$===N&&("s"===O?(P.$tag$="slot",f["s-sn"]=z[5]?P.$name$=z[5]:"",f["s-sr"]=!0,c&&(P.$elm$=Se.createElement(P.$tag$),P.$name$&&P.$elm$.setAttribute("name",P.$name$),f.parentNode.insertBefore(P.$elm$,f),f.remove(),"0"===P.$depth$&&(c[P.$index$]=P.$elm$)),a.push(P),o.$children$||(o.$children$=[]),o.$children$[P.$index$]=P):"r"===O&&(c?f.remove():(h["s-cr"]=f,f["s-cn"]=!0))));else if(o&&"style"===o.$tag$){const ce=de(null,f.textContent);ce.$elm$=f,ce.$index$="0",o.$children$=[ce]}},L=(o,r)=>{if(1===o.nodeType){let a=0;for(;a<o.childNodes.length;a++)L(o.childNodes[a],r);if(o.shadowRoot)for(a=0;a<o.shadowRoot.childNodes.length;a++)L(o.shadowRoot.childNodes[a],r)}else if(8===o.nodeType){const a=o.nodeValue.split(".");"o"===a[0]&&(r.set(a[1]+"."+a[2],o),o.nodeValue="",o["s-en"]=a[3])}},$=o=>Tt.push(o),re=o=>Fe(o).$modeName$,Ee=o=>Fe(o).$hostElement$,ke=(o,r,a)=>{const c=Ee(o);return{emit:h=>we(c,r,{bubbles:!!(4&a),composed:!!(2&a),cancelable:!!(1&a),detail:h})}},we=(o,r,a)=>{const c=Ce.ce(r,a);return o.dispatchEvent(c),c},Te=new WeakMap,ye=(o,r,a)=>{let c=Qe.get(o);rt&&a?(c=c||new CSSStyleSheet,"string"==typeof c?c=r:c.replaceSync(r)):c=r,Qe.set(o,c)},R=(o,r,a)=>{var c;const h=I(r,a),f=Qe.get(h);if(o=11===o.nodeType?o:Se,f)if("string"==typeof f){let O,N=Te.get(o=o.head||o);if(N||Te.set(o,N=new Set),!N.has(h)){if(o.host&&(O=o.querySelector(`[${V}="${h}"]`)))O.innerHTML=f;else{O=Se.createElement("style"),O.innerHTML=f;const z=null!==(c=Ce.$nonce$)&&void 0!==c?c:M(Se);null!=z&&O.setAttribute("nonce",z),o.insertBefore(O,o.querySelector("link"))}4&r.$flags$&&(O.innerHTML+=ie),N&&N.add(h)}}else o.adoptedStyleSheets.includes(f)||(o.adoptedStyleSheets=[...o.adoptedStyleSheets,f]);return h},I=(o,r)=>"sc-"+(r&&32&o.$flags$?o.$tagName$+"-"+r:o.$tagName$),se=o=>o.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),fe=(o,r,a,c,h,f)=>{if(a!==c){let N=qe(o,r),O=r.toLowerCase();if("class"===r){const z=o.classList,P=be(a),Y=be(c);z.remove(...P.filter(ce=>ce&&!Y.includes(ce))),z.add(...Y.filter(ce=>ce&&!P.includes(ce)))}else if("style"===r){for(const z in a)(!c||null==c[z])&&(z.includes("-")?o.style.removeProperty(z):o.style[z]="");for(const z in c)(!a||c[z]!==a[z])&&(z.includes("-")?o.style.setProperty(z,c[z]):o.style[z]=c[z])}else if("key"!==r)if("ref"===r)c&&c(o);else if(N||"o"!==r[0]||"n"!==r[1]){const z=_(c);if((N||z&&null!==c)&&!h)try{if(o.tagName.includes("-"))o[r]=c;else{const Y=c??"";"list"===r?N=!1:(null==a||o[r]!=Y)&&(o[r]=Y)}}catch{}let P=!1;O!==(O=O.replace(/^xlink\:?/,""))&&(r=O,P=!0),null==c||!1===c?(!1!==c||""===o.getAttribute(r))&&(P?o.removeAttributeNS(C,r):o.removeAttribute(r)):(!N||4&f||h)&&!z&&(c=!0===c?"":c,P?o.setAttributeNS(C,r,c):o.setAttribute(r,c))}else if(r="-"===r[2]?r.slice(3):qe(Je,O)?O.slice(2):O[2]+r.slice(3),a||c){const z=r.endsWith(_e);r=r.replace(Ie,""),a&&Ce.rel(o,r,a,z),c&&Ce.ael(o,r,c,z)}}},$e=/\s/,be=o=>o?o.split($e):[],_e="Capture",Ie=new RegExp(_e+"$"),Re=(o,r,a,c)=>{const h=11===r.$elm$.nodeType&&r.$elm$.host?r.$elm$.host:r.$elm$,f=o&&o.$attrs$||B,N=r.$attrs$||B;for(c of Le(Object.keys(f)))c in N||fe(h,c,f[c],void 0,a,r.$flags$);for(c of Le(Object.keys(N)))fe(h,c,f[c],N[c],a,r.$flags$)};function Le(o){return o.includes("ref")?[...o.filter(r=>"ref"!==r),"ref"]:o}const Ge=(o,r,a,c)=>{var h;const f=r.$children$[a];let O,z,P,N=0;if(u||(q=!0,"slot"===f.$tag$&&(b&&c.classList.add(b+"-s"),f.$flags$|=f.$children$?2:1)),null!==f.$text$)O=f.$elm$=Se.createTextNode(f.$text$);else if(1&f.$flags$)O=f.$elm$=Se.createTextNode("");else{if(X||(X="svg"===f.$tag$),O=f.$elm$=Se.createElementNS(X?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",2&f.$flags$?"slot-fb":f.$tag$),X&&"foreignObject"===f.$tag$&&(X=!1),Re(null,f,X),(o=>null!=o)(b)&&O["s-si"]!==b&&O.classList.add(O["s-si"]=b),f.$children$)for(N=0;N<f.$children$.length;++N)z=Ge(o,f,N,O),z&&O.appendChild(z);"svg"===f.$tag$?X=!1:"foreignObject"===O.tagName&&(X=!0)}return O["s-hn"]=W,3&f.$flags$&&(O["s-sr"]=!0,O["s-cr"]=pe,O["s-sn"]=f.$name$||"",O["s-rf"]=null===(h=f.$attrs$)||void 0===h?void 0:h.ref,P=o&&o.$children$&&o.$children$[a],P&&P.$tag$===f.$tag$&&o.$elm$&&Ue(o.$elm$,!1)),O},Ue=(o,r)=>{Ce.$flags$|=1;const a=Array.from(o.childNodes);for(let c=a.length-1;c>=0;c--){const h=a[c];h["s-hn"]!==W&&h["s-ol"]&&(ht(h).insertBefore(h,et(h)),h["s-ol"].remove(),h["s-ol"]=void 0,h["s-sh"]=void 0,q=!0),r&&Ue(h,r)}Ce.$flags$&=-2},nt=(o,r,a,c,h,f)=>{let O,N=o["s-cr"]&&o["s-cr"].parentNode||o;for(N.shadowRoot&&N.tagName===W&&(N=N.shadowRoot);h<=f;++h)c[h]&&(O=Ge(null,a,h,o),O&&(c[h].$elm$=O,N.insertBefore(O,et(r))))},mt=(o,r,a)=>{for(let c=r;c<=a;++c){const h=o[c];if(h){const f=h.$elm$;It(h),f&&(w=!0,f["s-ol"]?f["s-ol"].remove():Ue(f,!0),f.remove())}}},ot=(o,r,a=!1)=>o.$tag$===r.$tag$&&("slot"===o.$tag$?o.$name$===r.$name$:!!a||o.$key$===r.$key$),et=o=>o&&o["s-ol"]||o,ht=o=>(o["s-ol"]?o["s-ol"]:o).parentNode,Ze=(o,r,a=!1)=>{const c=r.$elm$=o.$elm$,h=o.$children$,f=r.$children$,N=r.$tag$,O=r.$text$;let z;null===O?(X="svg"===N||"foreignObject"!==N&&X,"slot"===N&&!u||Re(o,r,X),null!==h&&null!==f?((o,r,a,c,h=!1)=>{let Be,Me,f=0,N=0,O=0,z=0,P=r.length-1,Y=r[0],ce=r[P],le=c.length-1,ve=c[0],xe=c[le];for(;f<=P&&N<=le;)if(null==Y)Y=r[++f];else if(null==ce)ce=r[--P];else if(null==ve)ve=c[++N];else if(null==xe)xe=c[--le];else if(ot(Y,ve,h))Ze(Y,ve,h),Y=r[++f],ve=c[++N];else if(ot(ce,xe,h))Ze(ce,xe,h),ce=r[--P],xe=c[--le];else if(ot(Y,xe,h))("slot"===Y.$tag$||"slot"===xe.$tag$)&&Ue(Y.$elm$.parentNode,!1),Ze(Y,xe,h),o.insertBefore(Y.$elm$,ce.$elm$.nextSibling),Y=r[++f],xe=c[--le];else if(ot(ce,ve,h))("slot"===Y.$tag$||"slot"===xe.$tag$)&&Ue(ce.$elm$.parentNode,!1),Ze(ce,ve,h),o.insertBefore(ce.$elm$,Y.$elm$),ce=r[--P],ve=c[++N];else{for(O=-1,z=f;z<=P;++z)if(r[z]&&null!==r[z].$key$&&r[z].$key$===ve.$key$){O=z;break}O>=0?(Me=r[O],Me.$tag$!==ve.$tag$?Be=Ge(r&&r[N],a,O,o):(Ze(Me,ve,h),r[O]=void 0,Be=Me.$elm$),ve=c[++N]):(Be=Ge(r&&r[N],a,N,o),ve=c[++N]),Be&&ht(Y.$elm$).insertBefore(Be,et(Y.$elm$))}f>P?nt(o,null==c[le+1]?null:c[le+1].$elm$,a,c,N,le):N>le&&mt(r,f,P)})(c,h,r,f,a):null!==f?(null!==o.$text$&&(c.textContent=""),nt(c,null,r,f,0,f.length-1)):null!==h&&mt(h,0,h.length-1),X&&"svg"===N&&(X=!1)):(z=c["s-cr"])?z.parentNode.textContent=O:o.$text$!==O&&(c.data=O)},ct=o=>{const r=o.childNodes;for(const a of r)if(1===a.nodeType){if(a["s-sr"]){const c=a["s-sn"];a.hidden=!1;for(const h of r)if(h!==a)if(h["s-hn"]!==a["s-hn"]||""!==c){if(1===h.nodeType&&(c===h.getAttribute("slot")||c===h["s-sn"])){a.hidden=!0;break}}else if(1===h.nodeType||3===h.nodeType&&""!==h.textContent.trim()){a.hidden=!0;break}}ct(a)}},Ne=[],vt=o=>{let r,a,c;for(const h of o.childNodes){if(h["s-sr"]&&(r=h["s-cr"])&&r.parentNode){a=r.parentNode.childNodes;const f=h["s-sn"];for(c=a.length-1;c>=0;c--)if(r=a[c],!r["s-cn"]&&!r["s-nr"]&&r["s-hn"]!==h["s-hn"])if(yt(r,f)){let N=Ne.find(O=>O.$nodeToRelocate$===r);w=!0,r["s-sn"]=r["s-sn"]||f,N?(N.$nodeToRelocate$["s-sh"]=h["s-hn"],N.$slotRefNode$=h):(r["s-sh"]=h["s-hn"],Ne.push({$slotRefNode$:h,$nodeToRelocate$:r})),r["s-sr"]&&Ne.map(O=>{yt(O.$nodeToRelocate$,r["s-sn"])&&(N=Ne.find(z=>z.$nodeToRelocate$===r),N&&!O.$slotRefNode$&&(O.$slotRefNode$=N.$slotRefNode$))})}else Ne.some(N=>N.$nodeToRelocate$===r)||Ne.push({$nodeToRelocate$:r})}1===h.nodeType&&vt(h)}},yt=(o,r)=>1===o.nodeType?null===o.getAttribute("slot")&&""===r||o.getAttribute("slot")===r:o["s-sn"]===r||""===r,It=o=>{o.$attrs$&&o.$attrs$.ref&&o.$attrs$.ref(null),o.$children$&&o.$children$.map(It)},bt=(o,r)=>{r&&!o.$onRenderResolve$&&r["s-p"]&&r["s-p"].push(new Promise(a=>o.$onRenderResolve$=a))},it=(o,r)=>{if(o.$flags$|=16,!(4&o.$flags$))return bt(o,o.$ancestorComponent$),K(()=>Rt(o,r));o.$flags$|=512},Rt=(o,r)=>{const c=o.$lazyInstance$;let h;return r&&(o.$flags$|=256,o.$queuedListeners$&&(o.$queuedListeners$.map(([f,N])=>je(c,f,N)),o.$queuedListeners$=void 0),h=je(c,"componentWillLoad")),h=Ct(h,()=>je(c,"componentWillRender")),Ct(h,()=>wt(o,c,r))},Ct=(o,r)=>Mt(o)?o.then(r):r(),Mt=o=>o instanceof Promise||o&&o.then&&"function"==typeof o.then,wt=function(){var o=(0,e.Z)(function*(r,a,c){var h;const f=r.$hostElement$,O=f["s-rc"];c&&(o=>{const r=o.$cmpMeta$,a=o.$hostElement$,c=r.$flags$,f=R(a.shadowRoot?a.shadowRoot:a.getRootNode(),r,o.$modeName$);10&c&&(a["s-sc"]=f,a.classList.add(f+"-h"),2&c&&a.classList.add(f+"-s"))})(r);Ot(r,a,f,c),O&&(O.map(P=>P()),f["s-rc"]=void 0);{const P=null!==(h=f["s-p"])&&void 0!==h?h:[],Y=()=>tt(r);0===P.length?Y():(Promise.all(P).then(Y),r.$flags$|=4,P.length=0)}});return function(a,c,h){return o.apply(this,arguments)}}(),Ot=(o,r,a,c)=>{try{r=r.render&&r.render(),o.$flags$&=-17,o.$flags$|=2,((o,r,a=!1)=>{var c,h,f,N;const O=o.$hostElement$,z=o.$cmpMeta$,P=o.$vnode$||de(null,null),Y=(o=>o&&o.$tag$===De)(r)?r:ne(null,null,r);if(W=O.tagName,z.$attrsToReflect$&&(Y.$attrs$=Y.$attrs$||{},z.$attrsToReflect$.map(([ce,le])=>Y.$attrs$[le]=O[ce])),a&&Y.$attrs$)for(const ce of Object.keys(Y.$attrs$))O.hasAttribute(ce)&&!["key","ref","style","class"].includes(ce)&&(Y.$attrs$[ce]=O[ce]);if(Y.$tag$=null,Y.$flags$|=4,o.$vnode$=Y,Y.$elm$=P.$elm$=O.shadowRoot||O,b=O["s-sc"],u=0!=(1&z.$flags$),pe=O["s-cr"],w=!1,Ze(P,Y,a),Ce.$flags$|=1,q){vt(Y.$elm$);for(const ce of Ne){const le=ce.$nodeToRelocate$;if(!le["s-ol"]){const ve=Se.createTextNode("");ve["s-nr"]=le,le.parentNode.insertBefore(le["s-ol"]=ve,le)}}for(const ce of Ne){const le=ce.$nodeToRelocate$,ve=ce.$slotRefNode$;if(ve){const xe=ve.parentNode;let Be=ve.nextSibling;{let Me=null===(c=le["s-ol"])||void 0===c?void 0:c.previousSibling;for(;Me;){let t=null!==(h=Me["s-nr"])&&void 0!==h?h:null;if(t&&t["s-sn"]===le["s-sn"]&&xe===t.parentNode){for(t=t.nextSibling;t===le||t?.["s-sr"];)t=t?.nextSibling;if(!t||!t["s-nr"]){Be=t;break}}Me=Me.previousSibling}}(!Be&&xe!==le.parentNode||le.nextSibling!==Be)&&le!==Be&&(!le["s-hn"]&&le["s-ol"]&&(le["s-hn"]=le["s-ol"].parentNode.nodeName),xe.insertBefore(le,Be),1===le.nodeType&&(le.hidden=null!==(f=le["s-ih"])&&void 0!==f&&f)),le&&"function"==typeof ve["s-rf"]&&ve["s-rf"](le)}else 1===le.nodeType&&(a&&(le["s-ih"]=null!==(N=le.hidden)&&void 0!==N&&N),le.hidden=!0)}}w&&ct(Y.$elm$),Ce.$flags$&=-2,Ne.length=0,pe=void 0})(o,r,c)}catch(h){Xe(h,o.$hostElement$)}return null},tt=o=>{const a=o.$hostElement$,h=o.$lazyInstance$,f=o.$ancestorComponent$;je(h,"componentDidRender"),64&o.$flags$?je(h,"componentDidUpdate"):(o.$flags$|=64,ze(a),je(h,"componentDidLoad"),o.$onReadyResolve$(a),f||Ve()),o.$onInstanceResolve$(a),o.$onRenderResolve$&&(o.$onRenderResolve$(),o.$onRenderResolve$=void 0),512&o.$flags$&&xt(()=>it(o,!1)),o.$flags$&=-517},Ke=o=>{{const r=Fe(o),a=r.$hostElement$.isConnected;return a&&2==(18&r.$flags$)&&it(r,!1),a}},Ve=o=>{ze(Se.documentElement),xt(()=>we(Je,"appload",{detail:{namespace:"ionic"}}))},je=(o,r,a)=>{if(o&&o[r])try{return o[r](a)}catch(c){Xe(c)}},ze=o=>o.classList.add("hydrated"),Dt=(o,r,a)=>{var c;const h=o.prototype;if(r.$members$){o.watchers&&(r.$watchers$=o.watchers);const f=Object.entries(r.$members$);if(f.map(([N,[O]])=>{31&O||2&a&&32&O?Object.defineProperty(h,N,{get(){return((o,r)=>Fe(this).$instanceValues$.get(r))(0,N)},set(z){((o,r,a,c)=>{const h=Fe(o),f=h.$hostElement$,N=h.$instanceValues$.get(r),O=h.$flags$,z=h.$lazyInstance$;a=((o,r)=>null==o||_(o)?o:4&r?"false"!==o&&(""===o||!!o):2&r?parseFloat(o):1&r?String(o):o)(a,c.$members$[r][0]);const P=Number.isNaN(N)&&Number.isNaN(a);if((!(8&O)||void 0===N)&&a!==N&&!P&&(h.$instanceValues$.set(r,a),z)){if(c.$watchers$&&128&O){const ce=c.$watchers$[r];ce&&ce.map(le=>{try{z[le](a,N,r)}catch(ve){Xe(ve,f)}})}2==(18&O)&&it(h,!1)}})(this,N,z,r)},configurable:!0,enumerable:!0}):1&a&&64&O&&Object.defineProperty(h,N,{value(...z){var P;const Y=Fe(this);return null===(P=Y?.$onInstancePromise$)||void 0===P?void 0:P.then(()=>{var ce;return null===(ce=Y.$lazyInstance$)||void 0===ce?void 0:ce[N](...z)})}})}),1&a){const N=new Map;h.attributeChangedCallback=function(O,z,P){Ce.jmp(()=>{var Y;const ce=N.get(O);if(this.hasOwnProperty(ce))P=this[ce],delete this[ce];else{if(h.hasOwnProperty(ce)&&"number"==typeof this[ce]&&this[ce]==P)return;if(null==ce){const le=Fe(this),ve=le?.$flags$;if(ve&&!(8&ve)&&128&ve&&P!==z){const xe=le.$lazyInstance$;(null===(Y=r.$watchers$)||void 0===Y?void 0:Y[O])?.forEach(Me=>{null!=xe[Me]&&xe[Me].call(xe,P,z,O)})}return}}this[ce]=(null!==P||"boolean"!=typeof this[ce])&&P})},o.observedAttributes=Array.from(new Set([...Object.keys(null!==(c=r.$watchers$)&&void 0!==c?c:{}),...f.filter(([O,z])=>15&z[0]).map(([O,z])=>{var P;const Y=z[1]||O;return N.set(Y,O),512&z[0]&&(null===(P=r.$attrsToReflect$)||void 0===P||P.push([O,Y])),Y})]))}}return o},Bt=function(){var o=(0,e.Z)(function*(r,a,c,h){let f;if(!(32&a.$flags$)){if(a.$flags$|=32,c.$lazyBundleId$){if(f=Pe(c),f.then){const Y=()=>{};f=yield f,Y()}f.isProxied||(c.$watchers$=f.watchers,Dt(f,c,2),f.isProxied=!0);const P=()=>{};a.$flags$|=8;try{new f(a)}catch(Y){Xe(Y)}a.$flags$&=-9,a.$flags$|=128,P(),lt(a.$lazyInstance$)}else f=r.constructor,customElements.whenDefined(c.$tagName$).then(()=>a.$flags$|=128);if(f.style){let P=f.style;"string"!=typeof P&&(P=P[a.$modeName$=(o=>Tt.map(r=>r(o)).find(r=>!!r))(r)]);const Y=I(c,a.$modeName$);if(!Qe.has(Y)){const ce=()=>{};ye(Y,P,!!(1&c.$flags$)),ce()}}}const N=a.$ancestorComponent$,O=()=>it(a,!0);N&&N["s-rc"]?N["s-rc"].push(O):O()});return function(a,c,h,f){return o.apply(this,arguments)}}(),lt=o=>{je(o,"connectedCallback")},Pt=o=>{const r=o["s-cr"]=Se.createComment("");r["s-cn"]=!0,o.insertBefore(r,o.firstChild)},_t=o=>{je(o,"disconnectedCallback")},Ft=function(){var o=(0,e.Z)(function*(r){if(!(1&Ce.$flags$)){const a=Fe(r);a.$rmListeners$&&(a.$rmListeners$.map(c=>c()),a.$rmListeners$=void 0),a?.$lazyInstance$?_t(a.$lazyInstance$):a?.$onReadyPromise$&&a.$onReadyPromise$.then(()=>_t(a.$lazyInstance$))}});return function(a){return o.apply(this,arguments)}}(),Nt=(o,r={})=>{var a;const h=[],f=r.exclude||[],N=Je.customElements,O=Se.head,z=O.querySelector("meta[charset]"),P=Se.createElement("style"),Y=[],ce=Se.querySelectorAll(`[${V}]`);let le,ve=!0,xe=0;for(Object.assign(Ce,r),Ce.$resourcesUrl$=new URL(r.resourcesUrl||"./",Se.baseURI).href,Ce.$flags$|=2;xe<ce.length;xe++)ye(ce[xe].getAttribute(V),se(ce[xe].innerHTML),!0);let Be=!1;if(o.map(Me=>{Me[1].map(t=>{var i;const n={$flags$:t[0],$tagName$:t[1],$members$:t[2],$listeners$:t[3]};4&n.$flags$&&(Be=!0),n.$members$=t[2],n.$listeners$=t[3],n.$attrsToReflect$=[],n.$watchers$=null!==(i=t[4])&&void 0!==i?i:{};const s=n.$tagName$,d=class extends HTMLElement{constructor(ge){super(ge),$t(ge=this,n),1&n.$flags$&&ge.attachShadow({mode:"open",delegatesFocus:!!(16&n.$flags$)})}connectedCallback(){le&&(clearTimeout(le),le=null),ve?Y.push(this):Ce.jmp(()=>(o=>{if(!(1&Ce.$flags$)){const r=Fe(o),a=r.$cmpMeta$,c=()=>{};if(1&r.$flags$)Et(o,r,a.$listeners$),r?.$lazyInstance$?lt(r.$lazyInstance$):r?.$onReadyPromise$&&r.$onReadyPromise$.then(()=>lt(r.$lazyInstance$));else{let h;if(r.$flags$|=1,h=o.getAttribute(Z),h){if(1&a.$flags$){const f=R(o.shadowRoot,a,o.getAttribute("s-mode"));o.classList.remove(f+"-h",f+"-s")}((o,r,a,c)=>{const f=o.shadowRoot,N=[],z=f?[]:null,P=c.$vnode$=de(r,null);Ce.$orgLocNodes$||L(Se.body,Ce.$orgLocNodes$=new Map),o[Z]=a,o.removeAttribute(Z),ue(P,N,[],z,o,o,a),N.map(Y=>{const ce=Y.$hostId$+"."+Y.$nodeId$,le=Ce.$orgLocNodes$.get(ce),ve=Y.$elm$;le&&ut&&""===le["s-en"]&&le.parentNode.insertBefore(ve,le.nextSibling),f||(ve["s-hn"]=r,le&&(ve["s-ol"]=le,ve["s-ol"]["s-nr"]=ve)),Ce.$orgLocNodes$.delete(ce)}),f&&z.map(Y=>{Y&&f.appendChild(Y)})})(o,a.$tagName$,h,r)}h||12&a.$flags$&&Pt(o);{let f=o;for(;f=f.parentNode||f.host;)if(1===f.nodeType&&f.hasAttribute("s-id")&&f["s-p"]||f["s-p"]){bt(r,r.$ancestorComponent$=f);break}}a.$members$&&Object.entries(a.$members$).map(([f,[N]])=>{if(31&N&&o.hasOwnProperty(f)){const O=o[f];delete o[f],o[f]=O}}),Bt(o,r,a)}c()}})(this))}disconnectedCallback(){Ce.jmp(()=>Ft(this))}componentOnReady(){return Fe(this).$onReadyPromise$}};n.$lazyBundleId$=Me[0],!f.includes(s)&&!N.get(s)&&(h.push(s),N.define(s,Dt(d,n,1)))})}),h.length>0&&(Be&&(P.textContent+=ie),P.textContent+=h+"{visibility:hidden}.hydrated{visibility:inherit}",P.innerHTML.length)){P.setAttribute("data-styles","");const Me=null!==(a=Ce.$nonce$)&&void 0!==a?a:M(Se);null!=Me&&P.setAttribute("nonce",Me),O.insertBefore(P,z?z.nextSibling:O.firstChild)}ve=!1,Y.length?Y.map(Me=>Me.connectedCallback()):Ce.jmp(()=>le=setTimeout(Ve,30))},Et=(o,r,a,c)=>{a&&a.map(([h,f,N])=>{const O=Wt(o,h),z=zt(r,N),P=Xt(h);Ce.ael(O,f,z,P),(r.$rmListeners$=r.$rmListeners$||[]).push(()=>Ce.rel(O,f,z,P))})},zt=(o,r)=>a=>{try{256&o.$flags$?o.$lazyInstance$[r](a):(o.$queuedListeners$=o.$queuedListeners$||[]).push([r,a])}catch(c){Xe(c)}},Wt=(o,r)=>4&r?Se:8&r?Je:16&r?Se.body:o,Xt=o=>Zt?{passive:0!=(1&o),capture:0!=(2&o)}:0!=(2&o),We=new WeakMap,Fe=o=>We.get(o),Ht=(o,r)=>We.set(r.$lazyInstance$=o,r),$t=(o,r)=>{const a={$flags$:0,$hostElement$:o,$cmpMeta$:r,$instanceValues$:new Map};return a.$onInstancePromise$=new Promise(c=>a.$onInstanceResolve$=c),a.$onReadyPromise$=new Promise(c=>a.$onReadyResolve$=c),o["s-p"]=[],o["s-rc"]=[],Et(o,a,r.$listeners$),We.set(o,a)},qe=(o,r)=>r in o,Xe=(o,r)=>(0,console.error)(o,r),dt=new Map,Pe=(o,r,a)=>{const c=o.$tagName$.replace(/-/g,"_"),h=o.$lazyBundleId$,f=dt.get(h);return f?f[c]:x(50863)(`./${h}.entry.js`).then(N=>(dt.set(h,N),N[c]),Xe)},Qe=new Map,Tt=[],Je=typeof window<"u"?window:{},Se=Je.document||{head:{}},Ce={$flags$:0,$resourcesUrl$:"",jmp:o=>o(),raf:o=>requestAnimationFrame(o),ael:(o,r,a,c)=>o.addEventListener(r,a,c),rel:(o,r,a,c)=>o.removeEventListener(r,a,c),ce:(o,r)=>new CustomEvent(o,r)},Gt=o=>{Object.assign(Ce,o)},ut=!0,Zt=(()=>{let o=!1;try{Se.addEventListener("e",null,Object.defineProperty({},"passive",{get(){o=!0}}))}catch{}return o})(),rt=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch{}return!1})(),ft=[],st=[],St=(o,r)=>a=>{o.push(a),F||(F=!0,r&&4&Ce.$flags$?xt(At):Ce.raf(At))},pt=o=>{for(let r=0;r<o.length;r++)try{o[r](performance.now())}catch(a){Xe(a)}o.length=0},At=()=>{pt(ft),pt(st),(F=ft.length>0)&&Ce.raf(At)},xt=o=>Promise.resolve(void 0).then(o),g=St(ft,!1),K=St(st,!0)},72972:(Oe,he,x)=>{x.d(he,{d:()=>oe,w:()=>e});const e=typeof window<"u"?window:void 0,oe=typeof document<"u"?document:void 0},39721:(Oe,he,x)=>{x.d(he,{b:()=>W,c:()=>u,d:()=>w,e:()=>C,g:()=>l,l:()=>ee,s:()=>B,t:()=>F,w:()=>ie});var e=x(15861),oe=x(42477),U=x(78635);const W="ionViewWillLeave",u="ionViewDidLeave",w="ionViewWillUnload",F=S=>new Promise((_,M)=>{(0,oe.w)(()=>{J(S),T(S).then(ne=>{ne.animation&&ne.animation.destroy(),k(S),_(ne)},ne=>{k(S),M(ne)})})}),J=S=>{const _=S.enteringEl,M=S.leavingEl;H(_,M,S.direction),S.showGoBack?_.classList.add("can-go-back"):_.classList.remove("can-go-back"),B(_,!1),_.style.setProperty("pointer-events","none"),M&&(B(M,!1),M.style.setProperty("pointer-events","none"))},T=function(){var S=(0,e.Z)(function*(_){const M=yield j(_);return M&&oe.B.isBrowser?G(M,_):y(_)});return function(M){return S.apply(this,arguments)}}(),k=S=>{const _=S.enteringEl,M=S.leavingEl;_.classList.remove("ion-page-invisible"),_.style.removeProperty("pointer-events"),void 0!==M&&(M.classList.remove("ion-page-invisible"),M.style.removeProperty("pointer-events"))},j=function(){var S=(0,e.Z)(function*(_){return _.leavingEl&&_.animated&&0!==_.duration?_.animationBuilder?_.animationBuilder:"ios"===_.mode?(yield Promise.resolve().then(x.bind(x,59758))).iosTransitionAnimation:(yield Promise.resolve().then(x.bind(x,36160))).mdTransitionAnimation:void 0});return function(M){return S.apply(this,arguments)}}(),G=function(){var S=(0,e.Z)(function*(_,M){yield p(M,!0);const ne=_(M.baseEl,M);V(M.enteringEl,M.leavingEl);const de=yield Z(ne,M);return M.progressCallback&&M.progressCallback(void 0),de&&ae(M.enteringEl,M.leavingEl),{hasCompleted:de,animation:ne}});return function(M,ne){return S.apply(this,arguments)}}(),y=function(){var S=(0,e.Z)(function*(_){const M=_.enteringEl,ne=_.leavingEl;return yield p(_,!1),V(M,ne),ae(M,ne),{hasCompleted:!0}});return function(M){return S.apply(this,arguments)}}(),p=function(){var S=(0,e.Z)(function*(_,M){(void 0!==_.deepWait?_.deepWait:M)&&(yield Promise.all([C(_.enteringEl),C(_.leavingEl)])),yield m(_.viewIsReady,_.enteringEl)});return function(M,ne){return S.apply(this,arguments)}}(),m=function(){var S=(0,e.Z)(function*(_,M){_&&(yield _(M))});return function(M,ne){return S.apply(this,arguments)}}(),Z=(S,_)=>{const M=_.progressCallback,ne=new Promise(de=>{S.onFinish(De=>de(1===De))});return M?(S.progressStart(!0),M(S)):S.play(),ne},V=(S,_)=>{ee(_,W),ee(S,"ionViewWillEnter")},ae=(S,_)=>{ee(S,"ionViewDidEnter"),ee(_,u)},ee=(S,_)=>{if(S){const M=new CustomEvent(_,{bubbles:!1,cancelable:!1});S.dispatchEvent(M)}},ie=()=>new Promise(S=>(0,U.r)(()=>(0,U.r)(()=>S()))),C=function(){var S=(0,e.Z)(function*(_){const M=_;if(M){if(null!=M.componentOnReady){if(null!=(yield M.componentOnReady()))return}else if(null!=M.__registerHost)return void(yield new Promise(de=>(0,U.r)(de)));yield Promise.all(Array.from(M.children).map(C))}});return function(M){return S.apply(this,arguments)}}(),B=(S,_)=>{_?(S.setAttribute("aria-hidden","true"),S.classList.add("ion-page-hidden")):(S.hidden=!1,S.removeAttribute("aria-hidden"),S.classList.remove("ion-page-hidden"))},H=(S,_,M)=>{void 0!==S&&(S.style.zIndex="back"===M?"99":"101"),void 0!==_&&(_.style.zIndex="100")},l=S=>S.classList.contains("ion-page")?S:S.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||S},37943:(Oe,he,x)=>{x.d(he,{a:()=>F,b:()=>de,c:()=>U,g:()=>X,i:()=>De});var e=x(42477);class oe{constructor(){this.m=new Map}reset(Q){this.m=new Map(Object.entries(Q))}get(Q,v){const A=this.m.get(Q);return void 0!==A?A:v}getBoolean(Q,v=!1){const A=this.m.get(Q);return void 0===A?v:"string"==typeof A?"true"===A:!!A}getNumber(Q,v){const A=parseFloat(this.m.get(Q));return isNaN(A)?void 0!==v?v:NaN:A}set(Q,v){this.m.set(Q,v)}}const U=new oe,w="ionic:",q="ionic-persist-config",X=E=>J(E),F=(E,Q)=>("string"==typeof E&&(Q=E,E=void 0),X(E).includes(Q)),J=(E=window)=>{if(typeof E>"u")return[];E.Ionic=E.Ionic||{};let Q=E.Ionic.platforms;return null==Q&&(Q=E.Ionic.platforms=T(E),Q.forEach(v=>E.document.documentElement.classList.add(`plt-${v}`))),Q},T=E=>{const Q=U.get("platform");return Object.keys(M).filter(v=>{const A=Q?.[v];return"function"==typeof A?A(E):M[v](E)})},j=E=>!!(S(E,/iPad/i)||S(E,/Macintosh/i)&&ae(E)),p=E=>S(E,/android|sink/i),ae=E=>_(E,"(any-pointer:coarse)"),ie=E=>C(E)||B(E),C=E=>!!(E.cordova||E.phonegap||E.PhoneGap),B=E=>!!E.Capacitor?.isNative,S=(E,Q)=>Q.test(E.navigator.userAgent),_=(E,Q)=>{var v;return null===(v=E.matchMedia)||void 0===v?void 0:v.call(E,Q).matches},M={ipad:j,iphone:E=>S(E,/iPhone/i),ios:E=>S(E,/iPhone|iPod/i)||j(E),android:p,phablet:E=>{const Q=E.innerWidth,v=E.innerHeight,A=Math.min(Q,v),te=Math.max(Q,v);return A>390&&A<520&&te>620&&te<800},tablet:E=>{const Q=E.innerWidth,v=E.innerHeight,A=Math.min(Q,v),te=Math.max(Q,v);return j(E)||(E=>p(E)&&!S(E,/mobile/i))(E)||A>460&&A<820&&te>780&&te<1400},cordova:C,capacitor:B,electron:E=>S(E,/electron/i),pwa:E=>{var Q;return!!(null!==(Q=E.matchMedia)&&void 0!==Q&&Q.call(E,"(display-mode: standalone)").matches||E.navigator.standalone)},mobile:ae,mobileweb:E=>ae(E)&&!ie(E),desktop:E=>!ae(E),hybrid:ie};let ne;const de=E=>E&&(0,e.g)(E)||ne,De=(E={})=>{if(typeof window>"u")return;const Q=window.document,v=window,A=v.Ionic=v.Ionic||{},te={};E._ael&&(te.ael=E._ael),E._rel&&(te.rel=E._rel),E._ce&&(te.ce=E._ce),(0,e.a)(te);const ue=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(E=>{try{const Q=E.sessionStorage.getItem(q);return null!==Q?JSON.parse(Q):{}}catch{return{}}})(v)),{persistConfig:!1}),A.config),(E=>{const Q={};return E.location.search.slice(1).split("&").map(v=>v.split("=")).map(([v,A])=>[decodeURIComponent(v),decodeURIComponent(A)]).filter(([v])=>((E,Q)=>E.substr(0,Q.length)===Q)(v,w)).map(([v,A])=>[v.slice(w.length),A]).forEach(([v,A])=>{Q[v]=A}),Q})(v)),E);U.reset(ue),U.getBoolean("persistConfig")&&((E,Q)=>{try{E.sessionStorage.setItem(q,JSON.stringify(Q))}catch{return}})(v,ue),J(v),A.config=U,A.mode=ne=U.get("mode",Q.documentElement.getAttribute("mode")||(F(v,"ios")?"ios":"md")),U.set("mode",ne),Q.documentElement.setAttribute("mode",ne),Q.documentElement.classList.add(ne),U.getBoolean("_testing")&&U.set("animated",!1);const L=$=>{var re;return null===(re=$.tagName)||void 0===re?void 0:re.startsWith("ION-")},me=$=>["ios","md"].includes($);(0,e.c)($=>{for(;$;){const re=$.mode||$.getAttribute("mode");if(re){if(me(re))return re;L($)&&console.warn('Invalid ionic mode: "'+re+'", expected: "ios" or "md"')}$=$.parentElement}return ne})}},59758:(Oe,he,x)=>{x.r(he),x.d(he,{iosTransitionAnimation:()=>T,shadow:()=>u});var e=x(44963),oe=x(39721);x(72972),x(42477);const W=j=>document.querySelector(`${j}.ion-cloned-element`),u=j=>j.shadowRoot||j,w=j=>{const G="ION-TABS"===j.tagName?j:j.querySelector("ion-tabs"),y="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(null!=G){const p=G.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return null!=p?p.querySelector(y):null}return j.querySelector(y)},q=(j,G)=>{const y="ION-TABS"===j.tagName?j:j.querySelector("ion-tabs");let p=[];if(null!=y){const m=y.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");null!=m&&(p=m.querySelectorAll("ion-buttons"))}else p=j.querySelectorAll("ion-buttons");for(const m of p){const Z=m.closest("ion-header"),V=Z&&!Z.classList.contains("header-collapse-condense-inactive"),ae=m.querySelector("ion-back-button"),ee=m.classList.contains("buttons-collapse");if(null!==ae&&("start"===m.slot||""===m.slot)&&(ee&&V&&G||!ee))return ae}return null},F=(j,G,y,p,m,Z,V,ae,ee)=>{var ie,C;const B=G?`calc(100% - ${m.right+4}px)`:m.left-4+"px",H=G?"right":"left",l=G?"left":"right",S=G?"right":"left",_=(null===(ie=Z.textContent)||void 0===ie?void 0:ie.trim())===(null===(C=ae.textContent)||void 0===C?void 0:C.trim()),ne=(ee.height-k)/V.height,de=_?`scale(${ee.width/V.width}, ${ne})`:`scale(${ne})`,De="scale(1)",Q=u(p).querySelector("ion-icon").getBoundingClientRect(),v=G?Q.width/2-(Q.right-m.right)+"px":m.left-Q.width/2+"px",A=G?`-${window.innerWidth-m.right}px`:`${m.left}px`,te=`${ee.top}px`,ue=`${m.top}px`,$=y?[{offset:0,transform:`translate3d(${A}, ${ue}, 0)`},{offset:1,transform:`translate3d(${v}, ${te}, 0)`}]:[{offset:0,transform:`translate3d(${v}, ${te}, 0)`},{offset:1,transform:`translate3d(${A}, ${ue}, 0)`}],Ee=y?[{offset:0,opacity:1,transform:De},{offset:1,opacity:0,transform:de}]:[{offset:0,opacity:0,transform:de},{offset:1,opacity:1,transform:De}],Te=y?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],ye=(0,e.c)(),R=(0,e.c)(),D=(0,e.c)(),I=W("ion-back-button"),se=u(I).querySelector(".button-text"),fe=u(I).querySelector("ion-icon");I.text=p.text,I.mode=p.mode,I.icon=p.icon,I.color=p.color,I.disabled=p.disabled,I.style.setProperty("display","block"),I.style.setProperty("position","fixed"),R.addElement(fe),ye.addElement(se),D.addElement(I),D.beforeStyles({position:"absolute",top:"0px",[S]:"0px"}).keyframes($),ye.beforeStyles({"transform-origin":`${H} top`}).beforeAddWrite(()=>{p.style.setProperty("display","none"),I.style.setProperty(H,B)}).afterAddWrite(()=>{p.style.setProperty("display",""),I.style.setProperty("display","none"),I.style.removeProperty(H)}).keyframes(Ee),R.beforeStyles({"transform-origin":`${l} center`}).keyframes(Te),j.addAnimation([ye,R,D])},J=(j,G,y,p,m,Z,V,ae)=>{var ee,ie;const C=G?"right":"left",B=G?`calc(100% - ${m.right}px)`:`${m.left}px`,l=`${m.top}px`,_=G?`-${window.innerWidth-ae.right-8}px`:ae.x-8+"px",ne=ae.y-2+"px",de=(null===(ee=V.textContent)||void 0===ee?void 0:ee.trim())===(null===(ie=p.textContent)||void 0===ie?void 0:ie.trim()),E=ae.height/(Z.height-k),Q="scale(1)",v=de?`scale(${ae.width/Z.width}, ${E})`:`scale(${E})`,ue=y?[{offset:0,opacity:0,transform:`translate3d(${_}, ${ne}, 0) ${v}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(0px, ${l}, 0) ${Q}`}]:[{offset:0,opacity:.99,transform:`translate3d(0px, ${l}, 0) ${Q}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${_}, ${ne}, 0) ${v}`}],L=W("ion-title"),me=(0,e.c)();L.innerText=p.innerText,L.size=p.size,L.color=p.color,me.addElement(L),me.beforeStyles({"transform-origin":`${C} top`,height:`${m.height}px`,display:"",position:"relative",[C]:B}).beforeAddWrite(()=>{p.style.setProperty("opacity","0")}).afterAddWrite(()=>{p.style.setProperty("opacity",""),L.style.setProperty("display","none")}).keyframes(ue),j.addAnimation(me)},T=(j,G)=>{var y;try{const p="cubic-bezier(0.32,0.72,0,1)",m="opacity",Z="transform",V="0%",ee="rtl"===j.ownerDocument.dir,ie=ee?"-99.5%":"99.5%",C=ee?"33%":"-33%",B=G.enteringEl,H=G.leavingEl,l="back"===G.direction,S=B.querySelector(":scope > ion-content"),_=B.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),M=B.querySelectorAll(":scope > ion-header > ion-toolbar"),ne=(0,e.c)(),de=(0,e.c)();if(ne.addElement(B).duration((null!==(y=G.duration)&&void 0!==y?y:0)||540).easing(G.easing||p).fill("both").beforeRemoveClass("ion-page-invisible"),H&&null!=j){const v=(0,e.c)();v.addElement(j),ne.addAnimation(v)}if(S||0!==M.length||0!==_.length?(de.addElement(S),de.addElement(_)):de.addElement(B.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),ne.addAnimation(de),l?de.beforeClearStyles([m]).fromTo("transform",`translateX(${C})`,`translateX(${V})`).fromTo(m,.8,1):de.beforeClearStyles([m]).fromTo("transform",`translateX(${ie})`,`translateX(${V})`),S){const v=u(S).querySelector(".transition-effect");if(v){const A=v.querySelector(".transition-cover"),te=v.querySelector(".transition-shadow"),ue=(0,e.c)(),L=(0,e.c)(),me=(0,e.c)();ue.addElement(v).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),L.addElement(A).beforeClearStyles([m]).fromTo(m,0,.1),me.addElement(te).beforeClearStyles([m]).fromTo(m,.03,.7),ue.addAnimation([L,me]),de.addAnimation([ue])}}const De=B.querySelector("ion-header.header-collapse-condense"),{forward:E,backward:Q}=((j,G,y,p,m)=>{const Z=q(p,y),V=w(m),ae=w(p),ee=q(m,y),ie=null!==Z&&null!==V&&!y,C=null!==ae&&null!==ee&&y;if(ie){const B=V.getBoundingClientRect(),H=Z.getBoundingClientRect(),l=u(Z).querySelector(".button-text"),S=l.getBoundingClientRect(),M=u(V).querySelector(".toolbar-title").getBoundingClientRect();J(j,G,y,V,B,M,l,S),F(j,G,y,Z,H,l,S,V,M)}else if(C){const B=ae.getBoundingClientRect(),H=ee.getBoundingClientRect(),l=u(ee).querySelector(".button-text"),S=l.getBoundingClientRect(),M=u(ae).querySelector(".toolbar-title").getBoundingClientRect();J(j,G,y,ae,B,M,l,S),F(j,G,y,ee,H,l,S,ae,M)}return{forward:ie,backward:C}})(ne,ee,l,B,H);if(M.forEach(v=>{const A=(0,e.c)();A.addElement(v),ne.addAnimation(A);const te=(0,e.c)();te.addElement(v.querySelector("ion-title"));const ue=(0,e.c)(),L=Array.from(v.querySelectorAll("ion-buttons,[menuToggle]")),me=v.closest("ion-header"),$=me?.classList.contains("header-collapse-condense-inactive");let re;re=L.filter(l?Te=>{const ye=Te.classList.contains("buttons-collapse");return ye&&!$||!ye}:Te=>!Te.classList.contains("buttons-collapse")),ue.addElement(re);const Ae=(0,e.c)();Ae.addElement(v.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const Ee=(0,e.c)();Ee.addElement(u(v).querySelector(".toolbar-background"));const ke=(0,e.c)(),we=v.querySelector("ion-back-button");if(we&&ke.addElement(we),A.addAnimation([te,ue,Ae,Ee,ke]),ue.fromTo(m,.01,1),Ae.fromTo(m,.01,1),l)$||te.fromTo("transform",`translateX(${C})`,`translateX(${V})`).fromTo(m,.01,1),Ae.fromTo("transform",`translateX(${C})`,`translateX(${V})`),ke.fromTo(m,.01,1);else if(De||te.fromTo("transform",`translateX(${ie})`,`translateX(${V})`).fromTo(m,.01,1),Ae.fromTo("transform",`translateX(${ie})`,`translateX(${V})`),Ee.beforeClearStyles([m,"transform"]),me?.translucent?Ee.fromTo("transform",ee?"translateX(-100%)":"translateX(100%)","translateX(0px)"):Ee.fromTo(m,.01,"var(--opacity)"),E||ke.fromTo(m,.01,1),we&&!E){const ye=(0,e.c)();ye.addElement(u(we).querySelector(".button-text")).fromTo("transform",ee?"translateX(-100px)":"translateX(100px)","translateX(0px)"),A.addAnimation(ye)}}),H){const v=(0,e.c)(),A=H.querySelector(":scope > ion-content"),te=H.querySelectorAll(":scope > ion-header > ion-toolbar"),ue=H.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if(A||0!==te.length||0!==ue.length?(v.addElement(A),v.addElement(ue)):v.addElement(H.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),ne.addAnimation(v),l){v.beforeClearStyles([m]).fromTo("transform",`translateX(${V})`,ee?"translateX(-100%)":"translateX(100%)");const L=(0,oe.g)(H);ne.afterAddWrite(()=>{"normal"===ne.getDirection()&&L.style.setProperty("display","none")})}else v.fromTo("transform",`translateX(${V})`,`translateX(${C})`).fromTo(m,1,.8);if(A){const L=u(A).querySelector(".transition-effect");if(L){const me=L.querySelector(".transition-cover"),$=L.querySelector(".transition-shadow"),re=(0,e.c)(),Ae=(0,e.c)(),Ee=(0,e.c)();re.addElement(L).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),Ae.addElement(me).beforeClearStyles([m]).fromTo(m,.1,0),Ee.addElement($).beforeClearStyles([m]).fromTo(m,.7,.03),re.addAnimation([Ae,Ee]),v.addAnimation([re])}}te.forEach(L=>{const me=(0,e.c)();me.addElement(L);const $=(0,e.c)();$.addElement(L.querySelector("ion-title"));const re=(0,e.c)(),Ae=L.querySelectorAll("ion-buttons,[menuToggle]"),Ee=L.closest("ion-header"),ke=Ee?.classList.contains("header-collapse-condense-inactive"),we=Array.from(Ae).filter(se=>{const fe=se.classList.contains("buttons-collapse");return fe&&!ke||!fe});re.addElement(we);const Te=(0,e.c)(),ye=L.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");ye.length>0&&Te.addElement(ye);const R=(0,e.c)();R.addElement(u(L).querySelector(".toolbar-background"));const D=(0,e.c)(),I=L.querySelector("ion-back-button");if(I&&D.addElement(I),me.addAnimation([$,re,Te,D,R]),ne.addAnimation(me),D.fromTo(m,.99,0),re.fromTo(m,.99,0),Te.fromTo(m,.99,0),l){if(ke||$.fromTo("transform",`translateX(${V})`,ee?"translateX(-100%)":"translateX(100%)").fromTo(m,.99,0),Te.fromTo("transform",`translateX(${V})`,ee?"translateX(-100%)":"translateX(100%)"),R.beforeClearStyles([m,"transform"]),Ee?.translucent?R.fromTo("transform","translateX(0px)",ee?"translateX(-100%)":"translateX(100%)"):R.fromTo(m,"var(--opacity)",0),I&&!Q){const fe=(0,e.c)();fe.addElement(u(I).querySelector(".button-text")).fromTo("transform",`translateX(${V})`,`translateX(${(ee?-124:124)+"px"})`),me.addAnimation(fe)}}else ke||$.fromTo("transform",`translateX(${V})`,`translateX(${C})`).fromTo(m,.99,0).afterClearStyles([Z,m]),Te.fromTo("transform",`translateX(${V})`,`translateX(${C})`).afterClearStyles([Z,m]),D.afterClearStyles([m]),$.afterClearStyles([m]),re.afterClearStyles([m])})}return ne}catch(p){throw p}},k=10},36160:(Oe,he,x)=>{x.r(he),x.d(he,{mdTransitionAnimation:()=>pe});var e=x(44963),oe=x(39721);x(72972),x(42477);const pe=(W,u)=>{var w,q,X;const F="40px",T="back"===u.direction,j=u.leavingEl,G=(0,oe.g)(u.enteringEl),y=G.querySelector("ion-toolbar"),p=(0,e.c)();if(p.addElement(G).fill("both").beforeRemoveClass("ion-page-invisible"),T?p.duration((null!==(w=u.duration)&&void 0!==w?w:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):p.duration((null!==(q=u.duration)&&void 0!==q?q:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform",`translateY(${F})`,"translateY(0px)").fromTo("opacity",.01,1),y){const m=(0,e.c)();m.addElement(y),p.addAnimation(m)}if(j&&T){p.duration((null!==(X=u.duration)&&void 0!==X?X:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const m=(0,e.c)();m.addElement((0,oe.g)(j)).onFinish(Z=>{1===Z&&m.elements.length>0&&m.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)",`translateY(${F})`).fromTo("opacity",1,0),p.addAnimation(m)}return p}},57346:(Oe,he,x)=>{x.d(he,{B:()=>re,G:()=>Ae,O:()=>Ee,a:()=>J,b:()=>T,c:()=>y,d:()=>ke,e:()=>we,f:()=>De,g:()=>Q,h:()=>te,i:()=>L,j:()=>m,k:()=>Z,l:()=>k,m:()=>j,n:()=>ie,o:()=>ne,p:()=>G,s:()=>$,t:()=>p});var e=x(15861),oe=x(72972),U=x(33006),b=x(37943),pe=x(25030),W=x(78635),u=x(28909);let w=0,q=0;const X=new WeakMap,F=R=>({create:D=>V(R,D),dismiss:(D,I,se)=>S(document,D,I,R,se),getTop:()=>(0,e.Z)(function*(){return ne(document,R)})()}),J=F("ion-alert"),T=F("ion-action-sheet"),k=F("ion-loading"),j=F("ion-modal"),G=F("ion-picker"),y=F("ion-popover"),p=F("ion-toast"),m=R=>{typeof document<"u"&&l(document);const D=w++;R.overlayIndex=D},Z=R=>(R.hasAttribute("id")||(R.id="ion-overlay-"+ ++q),R.id),V=(R,D)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(R).then(()=>{const I=document.createElement(R);return I.classList.add("overlay-hidden"),Object.assign(I,Object.assign(Object.assign({},D),{hasController:!0})),v(document).appendChild(I),new Promise(se=>(0,W.c)(I,se))}):Promise.resolve(),ae='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',ie=(R,D)=>{const I=R.querySelector(ae);B(I,D)},C=(R,D)=>{const I=Array.from(R.querySelectorAll(ae));B(I.length>0?I[I.length-1]:null,D)},B=(R,D)=>{let I=R;const se=R?.shadowRoot;se&&(I=se.querySelector(ae)||R),I?(0,W.f)(I):D.focus()},l=R=>{0===w&&(w=1,R.addEventListener("focus",D=>{((R,D)=>{const I=ne(D,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover"),se=R.target;I&&se&&!I.classList.contains("ion-disable-focus-trap")&&(I.shadowRoot?(()=>{if(I.contains(se))I.lastFocus=se;else if("ION-TOAST"===se.tagName)B(I.lastFocus,I);else{const be=I.lastFocus;ie(I,I),be===D.activeElement&&C(I,I),I.lastFocus=D.activeElement}})():(()=>{if(I===se)I.lastFocus=void 0;else if("ION-TOAST"===se.tagName)B(I.lastFocus,I);else{const be=(0,W.g)(I);if(!be.contains(se))return;const _e=be.querySelector(".ion-overlay-wrapper");if(!_e)return;if(_e.contains(se)||se===be.querySelector("ion-backdrop"))I.lastFocus=se;else{const Ie=I.lastFocus;ie(_e,I),Ie===D.activeElement&&C(_e,I),I.lastFocus=D.activeElement}}})())})(D,R)},!0),R.addEventListener("ionBackButton",D=>{const I=ne(R);I?.backdropDismiss&&D.detail.register(U.OVERLAY_BACK_BUTTON_PRIORITY,()=>{I.dismiss(void 0,re)})}),(0,U.shouldUseCloseWatcher)()||R.addEventListener("keydown",D=>{if("Escape"===D.key){const I=ne(R);I?.backdropDismiss&&I.dismiss(void 0,re)}}))},S=(R,D,I,se,fe)=>{const $e=ne(R,se,fe);return $e?$e.dismiss(D,I):Promise.reject("overlay does not exist")},M=(R,D)=>((R,D)=>(void 0===D&&(D="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover,ion-toast"),Array.from(R.querySelectorAll(D)).filter(I=>I.overlayIndex>0)))(R,D).filter(I=>!(R=>R.classList.contains("overlay-hidden"))(I)),ne=(R,D,I)=>{const se=M(R,D);return void 0===I?se[se.length-1]:se.find(fe=>fe.id===I)},de=(R=!1)=>{const I=v(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");I&&(R?I.setAttribute("aria-hidden","true"):I.removeAttribute("aria-hidden"))},De=function(){var R=(0,e.Z)(function*(D,I,se,fe,$e){var be,_e;if(D.presented)return;de(!0),Te(D.el),D.presented=!0,D.willPresent.emit(),null===(be=D.willPresentShorthand)||void 0===be||be.emit();const Ie=(0,b.b)(D),Re=D.enterAnimation?D.enterAnimation:b.c.get(I,"ios"===Ie?se:fe);(yield A(D,Re,D.el,$e))&&(D.didPresent.emit(),null===(_e=D.didPresentShorthand)||void 0===_e||_e.emit()),"ION-TOAST"!==D.el.tagName&&E(D.el),D.keyboardClose&&(null===document.activeElement||!D.el.contains(document.activeElement))&&D.el.focus(),D.el.removeAttribute("aria-hidden")});return function(I,se,fe,$e,be){return R.apply(this,arguments)}}(),E=function(){var R=(0,e.Z)(function*(D){let I=document.activeElement;if(!I)return;const se=I?.shadowRoot;se&&(I=se.querySelector(ae)||I),yield D.onDidDismiss(),(null===document.activeElement||document.activeElement===document.body)&&I.focus()});return function(I){return R.apply(this,arguments)}}(),Q=function(){var R=(0,e.Z)(function*(D,I,se,fe,$e,be,_e){var Ie,Re;if(!D.presented)return!1;void 0!==oe.d&&1===M(oe.d).length&&de(!1),D.presented=!1;try{D.el.style.setProperty("pointer-events","none"),D.willDismiss.emit({data:I,role:se}),null===(Ie=D.willDismissShorthand)||void 0===Ie||Ie.emit({data:I,role:se});const Le=(0,b.b)(D),Ge=D.leaveAnimation?D.leaveAnimation:b.c.get(fe,"ios"===Le?$e:be);se!==Ae&&(yield A(D,Ge,D.el,_e)),D.didDismiss.emit({data:I,role:se}),null===(Re=D.didDismissShorthand)||void 0===Re||Re.emit({data:I,role:se}),(X.get(D)||[]).forEach(nt=>nt.destroy()),X.delete(D),D.el.classList.add("overlay-hidden"),D.el.style.removeProperty("pointer-events"),void 0!==D.el.lastFocus&&(D.el.lastFocus=void 0)}catch(Le){console.error(Le)}return D.el.remove(),ye(),!0});return function(I,se,fe,$e,be,_e,Ie){return R.apply(this,arguments)}}(),v=R=>R.querySelector("ion-app")||R.body,A=function(){var R=(0,e.Z)(function*(D,I,se,fe){se.classList.remove("overlay-hidden");const be=I(D.el,fe);(!D.animated||!b.c.getBoolean("animated",!0))&&be.duration(0),D.keyboardClose&&be.beforeAddWrite(()=>{const Ie=se.ownerDocument.activeElement;Ie?.matches("input,ion-input, ion-textarea")&&Ie.blur()});const _e=X.get(D)||[];return X.set(D,[..._e,be]),yield be.play(),!0});return function(I,se,fe,$e){return R.apply(this,arguments)}}(),te=(R,D)=>{let I;const se=new Promise(fe=>I=fe);return ue(R,D,fe=>{I(fe.detail)}),se},ue=(R,D,I)=>{const se=fe=>{(0,W.b)(R,D,se),I(fe)};(0,W.a)(R,D,se)},L=R=>"cancel"===R||R===re,me=R=>R(),$=(R,D)=>{if("function"==typeof R)return b.c.get("_zoneGate",me)(()=>{try{return R(D)}catch(se){throw se}})},re="backdrop",Ae="gesture",Ee=39,ke=R=>{let I,D=!1;const se=(0,pe.C)(),fe=(_e=!1)=>{if(I&&!_e)return{delegate:I,inline:D};const{el:Ie,hasController:Re,delegate:Le}=R;return D=null!==Ie.parentNode&&!Re,I=D?Le||se:Le,{inline:D,delegate:I}};return{attachViewToDom:function(){var _e=(0,e.Z)(function*(Ie){const{delegate:Re}=fe(!0);if(Re)return yield Re.attachViewToDom(R.el,Ie);const{hasController:Le}=R;if(Le&&void 0!==Ie)throw new Error("framework delegate is missing");return null});return function(Re){return _e.apply(this,arguments)}}(),removeViewFromDom:()=>{const{delegate:_e}=fe();_e&&void 0!==R.el&&_e.removeViewFromDom(R.el.parentElement,R.el)}}},we=()=>{let R;const D=()=>{R&&(R(),R=void 0)};return{addClickListener:(se,fe)=>{D();const $e=void 0!==fe?document.getElementById(fe):null;$e?R=((_e,Ie)=>{const Re=()=>{Ie.present()};return _e.addEventListener("click",Re),()=>{_e.removeEventListener("click",Re)}})($e,se):(0,u.p)(`A trigger element with the ID "${fe}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,se)},removeClickListener:D}},Te=R=>{var D;if(void 0===oe.d)return;const I=M(oe.d);for(let se=I.length-1;se>=0;se--){const fe=I[se],$e=null!==(D=I[se+1])&&void 0!==D?D:R;($e.hasAttribute("aria-hidden")||"ION-TOAST"!==$e.tagName)&&fe.setAttribute("aria-hidden","true")}},ye=()=>{if(void 0===oe.d)return;const R=M(oe.d);for(let D=R.length-1;D>=0;D--){const I=R[D];if(I.removeAttribute("aria-hidden"),"ION-TOAST"!==I.tagName)break}}},23814:(Oe,he,x)=>{x.d(he,{c:()=>U,g:()=>pe,h:()=>oe,o:()=>u});var e=x(15861);const oe=(w,q)=>null!==q.closest(w),U=(w,q)=>"string"==typeof w&&w.length>0?Object.assign({"ion-color":!0,[`ion-color-${w}`]:!0},q):q,pe=w=>{const q={};return(w=>void 0!==w?(Array.isArray(w)?w:w.split(" ")).filter(X=>null!=X).map(X=>X.trim()).filter(X=>""!==X):[])(w).forEach(X=>q[X]=!0),q},W=/^[a-z][a-z0-9+\-.]*:/,u=function(){var w=(0,e.Z)(function*(q,X,F,J){if(null!=q&&"#"!==q[0]&&!W.test(q)){const T=document.querySelector("ion-router");if(T)return X?.preventDefault(),T.push(q,F,J)}return!1});return function(X,F,J,T){return w.apply(this,arguments)}}()},50863:(Oe,he,x)=>{var e={"./ion-accordion_2.entry.js":[89654,9654],"./ion-action-sheet.entry.js":[3648,3648],"./ion-alert.entry.js":[11118,1118],"./ion-app_8.entry.js":[80053,53],"./ion-avatar_3.entry.js":[54753,3793],"./ion-back-button.entry.js":[92073,2073],"./ion-backdrop.entry.js":[98939,8939],"./ion-breadcrumb_2.entry.js":[47544,7544],"./ion-button_2.entry.js":[15652,5652],"./ion-card_5.entry.js":[50388,388],"./ion-checkbox.entry.js":[9922,9922],"./ion-chip.entry.js":[10657,657],"./ion-col_3.entry.js":[19824,9824],"./ion-datetime-button.entry.js":[49230,9230],"./ion-datetime_3.entry.js":[54959,4959],"./ion-fab_3.entry.js":[65836,5836],"./ion-img.entry.js":[71033,1033],"./ion-infinite-scroll_2.entry.js":[8034,8034],"./ion-input.entry.js":[51217,1217],"./ion-item-option_3.entry.js":[52933,2933],"./ion-item_8.entry.js":[94711,4711],"./ion-loading.entry.js":[79434,9434],"./ion-menu_3.entry.js":[38136,8136],"./ion-modal.entry.js":[42349,2349],"./ion-nav_2.entry.js":[45349,5349],"./ion-picker-column-internal.entry.js":[7602,7602],"./ion-picker-internal.entry.js":[9016,9016],"./ion-popover.entry.js":[83804,3804],"./ion-progress-bar.entry.js":[54174,4174],"./ion-radio_2.entry.js":[24432,3182],"./ion-range.entry.js":[31709,1709],"./ion-refresher_2.entry.js":[93326,3326],"./ion-reorder_2.entry.js":[93583,3583],"./ion-ripple-effect.entry.js":[99958,9958],"./ion-route_4.entry.js":[4330,4330],"./ion-searchbar.entry.js":[98628,8628],"./ion-segment_2.entry.js":[59325,9325],"./ion-select_3.entry.js":[12773,2773],"./ion-spinner.entry.js":[44908,4908],"./ion-split-pane.entry.js":[39536,9536],"./ion-tab-bar_2.entry.js":[438,438],"./ion-tab_2.entry.js":[91536,1536],"./ion-text.entry.js":[74376,4376],"./ion-textarea.entry.js":[56560,6560],"./ion-toast.entry.js":[76120,6120],"./ion-toggle.entry.js":[85168,5168]};function oe(U){if(!x.o(e,U))return Promise.resolve().then(()=>{var W=new Error("Cannot find module '"+U+"'");throw W.code="MODULE_NOT_FOUND",W});var b=e[U],pe=b[0];return x.e(b[1]).then(()=>x(pe))}oe.keys=()=>Object.keys(e),oe.id=50863,Oe.exports=oe},20012:(Oe,he,x)=>{x.r(he),x.d(he,{ActionSheetController:()=>f,AlertController:()=>c,AngularDelegate:()=>U.AngularDelegate,AnimationController:()=>h,BooleanValueAccessor:()=>De,Config:()=>U.Config,DomController:()=>U.DomController,GestureController:()=>N,ION_MAX_VALIDATOR:()=>K,ION_MIN_VALIDATOR:()=>r,IonAccordion:()=>re,IonAccordionGroup:()=>Ae,IonActionSheet:()=>Ee,IonAlert:()=>ke,IonApp:()=>we,IonAvatar:()=>Te,IonBackButton:()=>st,IonBackButtonDelegate:()=>st,IonBackdrop:()=>ye,IonBadge:()=>R,IonBreadcrumb:()=>D,IonBreadcrumbs:()=>I,IonButton:()=>se,IonButtons:()=>fe,IonCard:()=>$e,IonCardContent:()=>be,IonCardHeader:()=>_e,IonCardSubtitle:()=>Ie,IonCardTitle:()=>Re,IonCheckbox:()=>Le,IonChip:()=>Ge,IonCol:()=>Ue,IonContent:()=>nt,IonDatetime:()=>mt,IonDatetimeButton:()=>gt,IonFab:()=>ot,IonFabButton:()=>et,IonFabList:()=>ht,IonFooter:()=>Ze,IonGrid:()=>ct,IonHeader:()=>Ne,IonIcon:()=>vt,IonImg:()=>yt,IonInfiniteScroll:()=>It,IonInfiniteScrollContent:()=>jt,IonInput:()=>bt,IonItem:()=>it,IonItemDivider:()=>Rt,IonItemGroup:()=>Ct,IonItemOption:()=>Mt,IonItemOptions:()=>wt,IonItemSliding:()=>Ot,IonLabel:()=>tt,IonList:()=>Ke,IonListHeader:()=>Ve,IonLoading:()=>je,IonMaxValidator:()=>o,IonMenu:()=>ze,IonMenuButton:()=>Ye,IonMenuToggle:()=>kt,IonMinValidator:()=>a,IonModal:()=>xt,IonNav:()=>St,IonNavLink:()=>Dt,IonNote:()=>Bt,IonPicker:()=>lt,IonPopover:()=>g,IonProgressBar:()=>Lt,IonRadio:()=>Pt,IonRadioGroup:()=>_t,IonRange:()=>Ft,IonRefresher:()=>Nt,IonRefresherContent:()=>Et,IonReorder:()=>zt,IonReorderGroup:()=>Wt,IonRippleEffect:()=>Xt,IonRouterOutlet:()=>rt,IonRow:()=>Kt,IonSearchbar:()=>We,IonSegment:()=>Fe,IonSegmentButton:()=>Ht,IonSelect:()=>$t,IonSelectOption:()=>qe,IonSkeletonText:()=>Xe,IonSpinner:()=>dt,IonSplitPane:()=>Pe,IonTabBar:()=>Qe,IonTabButton:()=>Tt,IonTabs:()=>ft,IonText:()=>Je,IonTextarea:()=>Se,IonThumbnail:()=>Ce,IonTitle:()=>Gt,IonToast:()=>ut,IonToggle:()=>Zt,IonToolbar:()=>Ut,IonicModule:()=>Me,IonicRouteStrategy:()=>U.IonicRouteStrategy,IonicSafeString:()=>j.I,IonicSlides:()=>ee,LoadingController:()=>O,MenuController:()=>z,ModalController:()=>P,NavController:()=>U.NavController,NavParams:()=>U.NavParams,NumericValueAccessor:()=>E,PickerController:()=>Y,Platform:()=>U.Platform,PopoverController:()=>ce,RadioValueAccessor:()=>Q,RouterLinkDelegate:()=>pt,RouterLinkWithHrefDelegate:()=>At,SelectValueAccessor:()=>v,TextValueAccessor:()=>A,ToastController:()=>le,createAnimation:()=>w.c,createGesture:()=>T.createGesture,getIonPageElement:()=>q.g,getPlatforms:()=>k.g,getTimeGivenProgression:()=>J.g,iosTransitionAnimation:()=>X.iosTransitionAnimation,isPlatform:()=>k.a,mdTransitionAnimation:()=>F.mdTransitionAnimation,openURL:()=>G.o});var e=x(99877),oe=x(88191),U=x(86789),b=x(97582),pe=x(42168),W=x(17007),u=x(78007),w=x(44963),q=x(39721),X=x(59758),F=x(36160),J=x(65069),T=x(35067),k=x(37943),j=x(87036),G=x(23814),y=x(16523),p=x(57346),Z=(x(72972),x(42477));x(22889),x(33006);const ee=t=>{const{swiper:i,extendParams:n}=t,s={effect:void 0,direction:"horizontal",initialSlide:0,loop:!1,parallax:!1,slidesPerView:1,spaceBetween:0,speed:300,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,centeredSlides:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,touchEventsTarget:"container",freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,zoom:{maxRatio:3,minRatio:1,toggle:!1},touchRatio:1,touchAngle:45,simulateTouch:!0,touchStartPreventDefault:!1,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,threshold:0,touchMoveStopPropagation:!0,touchReleaseOnEdges:!1,iOSEdgeSwipeDetection:!1,iOSEdgeSwipeThreshold:20,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loopAdditionalSlides:0,noSwiping:!0,runCallbacksOnInit:!0,coverflowEffect:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0},flipEffect:{slideShadows:!0,limitRotation:!0},cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94},fadeEffect:{crossFade:!1},a11y:{prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide"}};i.pagination&&(s.pagination={type:"bullets",clickable:!1,hideOnClick:!1}),i.scrollbar&&(s.scrollbar={hide:!0}),n(s)};var C=x(15861);const B=k.i,H=function(){var t=(0,C.Z)(function*(i,n){if(!(typeof window>"u"))return yield B(),(0,Z.b)(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-input",[[38,"ion-input",{"color":[513],"accept":[1],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[4],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[4],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"size":[2],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"placeholder":["placeholderChanged"],"value":["valueChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"legacy":[4],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[1,"ion-skeleton-text",{"animated":[4]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[49,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[4],"download":[1],"fill":[1],"shape":[1],"href":[1],"rel":[1],"lines":[1],"counter":[4],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"counterFormatter":[16],"multipleInputs":[32],"focusable":[32],"counterString":[32]},[[0,"ionInput","handleIonInput"],[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"],"counterFormatter":["counterFormatterChanged"]}],[34,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-note",{"color":[513]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"isExpanded":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-picker-internal",[[33,"ion-picker-internal",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"],"checked":["styleChanged"],"color":["styleChanged"],"disabled":["styleChanged"]}],[0,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[34,"ion-buttons",{"collapse":[4]}]]],["ion-picker-column-internal",[[33,"ion-picker-column-internal",{"disabled":[4],"items":[16],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64]},null,{"value":["valueChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"legacy":[4]},null,{"checked":["styleChanged"],"disabled":["styleChanged"]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]]]'),n)});return function(n,s){return t.apply(this,arguments)}}(),l=["*"],S=["outlet"],_=[[["","slot","top"]],"*"],M=["[slot=top]","*"];function ne(t,i){if(1&t&&(e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275elementContainer(1,2),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngTemplateOutlet",n.template)}}function de(t,i){if(1&t&&e.\u0275\u0275elementContainer(0,1),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngTemplateOutlet",n.template)}}let De=(()=>{class t extends U.ValueAccessor{constructor(n,s){super(n,s)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,(0,U.setIonicClasses)(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionChange",function(ge){return s._handleIonChange(ge.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:oe.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),E=(()=>{class t extends U.ValueAccessor{constructor(n,s){super(n,s)}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){super.registerOnChange(s=>{n(""===s?null:parseFloat(s))})}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionInput",function(ge){return s.handleInputEvent(ge.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:oe.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),Q=(()=>{class t extends U.ValueAccessor{constructor(n,s){super(n,s)}_handleIonSelect(n){this.handleValueChange(n,n.checked)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-radio"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionSelect",function(ge){return s._handleIonSelect(ge.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:oe.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),v=(()=>{class t extends U.ValueAccessor{constructor(n,s){super(n,s)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionChange",function(ge){return s._handleChangeEvent(ge.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:oe.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),A=(()=>{class t extends U.ValueAccessor{constructor(n,s){super(n,s)}_handleInputEvent(n){this.handleValueChange(n,n.value)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(e.ElementRef))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"],["ion-range"]],hostBindings:function(n,s){1&n&&e.\u0275\u0275listener("ionInput",function(ge){return s._handleInputEvent(ge.target)})},features:[e.\u0275\u0275ProvidersFeature([{provide:oe.NG_VALUE_ACCESSOR,useExisting:t,multi:!0}]),e.\u0275\u0275InheritDefinitionFeature]}),t})();const te=(t,i)=>{const n=t.prototype;i.forEach(s=>{Object.defineProperty(n,s,{get(){return this.el[s]},set(d){this.z.runOutsideAngular(()=>this.el[s]=d)},configurable:!0})})},ue=(t,i)=>{const n=t.prototype;i.forEach(s=>{n[s]=function(){const d=arguments;return this.z.runOutsideAngular(()=>this.el[s].apply(this.el,d))}})},L=(t,i,n)=>{n.forEach(s=>t[s]=(0,pe.fromEvent)(i,s))};function $(t){return function(n){const{defineCustomElementFn:s,inputs:d,methods:ge}=t;return void 0!==s&&s(),d&&te(n,d),ge&&ue(n,ge),n}}let re=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],t),t})(),Ae=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],t),t})(),Ee=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),ke=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),we=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-app"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),Te=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-avatar"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),ye=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionBackdropTap"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["stopPropagation","tappable","visible"]})],t),t})(),R=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),D=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],t),t})(),I=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionCollapsedClick"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],t),t})(),se=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],t),t})(),fe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["collapse"]})],t),t})(),$e=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),be=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["mode"]})],t),t})(),_e=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode","translucent"]})],t),t})(),Ie=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),Re=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),Le=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["alignment","checked","color","disabled","indeterminate","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),Ge=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","disabled","mode","outline"]})],t),t})(),Ue=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],t),t})(),nt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-content"]],inputs:{color:"color",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],t),t})(),mt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],t),t})(),gt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","datetime","disabled","mode"]})],t),t})(),ot=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],t),t})(),et=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],t),t})(),ht=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["activated","side"]})],t),t})(),Ze=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["collapse","mode","translucent"]})],t),t})(),ct=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["fixed"]})],t),t})(),Ne=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["collapse","mode","translucent"]})],t),t})(),vt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],t),t})(),yt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["alt","src"]})],t),t})(),It=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionInfinite"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled","position","threshold"],methods:["complete"]})],t),t})(),jt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["loadingSpinner","loadingText"]})],t),t})(),bt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-input"]],inputs:{accept:"accept",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",size:"size",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["accept","autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","legacy","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","size","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),it=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item"]],inputs:{button:"button",color:"color",counter:"counter",counterFormatter:"counterFormatter",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",fill:"fill",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",target:"target",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["button","color","counter","counterFormatter","detail","detailIcon","disabled","download","fill","href","lines","mode","rel","routerAnimation","routerDirection","shape","target","type"]})],t),t})(),Rt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode","sticky"]})],t),t})(),Ct=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-group"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),Mt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],t),t})(),wt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionSwipe"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-options"]],inputs:{side:"side"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["side"]})],t),t})(),Ot=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionDrag"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],t),t})(),tt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode","position"]})],t),t})(),Ke=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],t),t})(),Ve=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","lines","mode"]})],t),t})(),je=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),ze=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],t),t})(),Ye=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["autoHide","color","disabled","menu","mode","type"]})],t),t})(),kt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["autoHide","menu"]})],t),t})(),Dt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["component","componentProps","routerAnimation","routerDirection"]})],t),t})(),Bt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),lt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-picker"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],t),t})(),Lt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["buffer","color","mode","reversed","type","value"]})],t),t})(),Pt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["alignment","color","disabled","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),_t=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",name:"name",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["allowEmptySelection","compareWith","name","value"]})],t),t})(),Ft=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","legacy","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],t),t})(),Nt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionRefresh","ionPull","ionStart"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],t),t})(),Et=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],t),t})(),zt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-reorder"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),Wt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionItemReorder"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled"],methods:["complete"]})],t),t})(),Xt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["type"],methods:["addRipple"]})],t),t})(),Kt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-row"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),We=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),Fe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],t),t})(),Ht=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-segment-button"]],inputs:{disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled","layout","mode","type","value"]})],t),t})(),$t=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",expandedIcon:"expandedIcon",fill:"fill",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["cancelText","color","compareWith","disabled","expandedIcon","fill","interface","interfaceOptions","justify","label","labelPlacement","legacy","mode","multiple","name","okText","placeholder","selectedText","shape","toggleIcon","value"],methods:["open"]})],t),t})(),qe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled","value"]})],t),t})(),Xe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated"]})],t),t})(),dt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","duration","name","paused"]})],t),t})(),Pe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionSplitPaneVisible"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["contentId","disabled","when"]})],t),t})(),Qe=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode","selectedTab","translucent"]})],t),t})(),Tt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],t),t})(),Je=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),Se=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",legacy:"legacy",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","legacy","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],t),t})(),Ce=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-thumbnail"]],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({})],t),t})(),Gt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","size"]})],t),t})(),ut=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),Zt=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement,L(this,this.el,["ionChange","ionFocus","ionBlur"])}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",justify:"justify",labelPlacement:"labelPlacement",legacy:"legacy",mode:"mode",name:"name",value:"value"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["alignment","checked","color","disabled","enableOnOffLabels","justify","labelPlacement","legacy","mode","name","value"]})],t),t})(),Ut=(()=>{let t=class{constructor(n,s,d){this.z=d,n.detach(),this.el=s.nativeElement}};return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t=(0,b.gn)([$({inputs:["color","mode"]})],t),t})(),rt=(()=>{class t extends U.IonRouterOutlet{constructor(n,s,d,ge,He,at,Vt,Yt){super(n,s,d,ge,He,at,Vt,Yt),this.parentOutlet=Yt}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275injectAttribute("name"),e.\u0275\u0275injectAttribute("tabs"),e.\u0275\u0275directiveInject(W.Location),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(u.Router),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(u.ActivatedRoute),e.\u0275\u0275directiveInject(t,12))},t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-router-outlet"]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),ft=(()=>{class t extends U.IonTabs{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-tabs"]],contentQueries:function(n,s,d){if(1&n&&(e.\u0275\u0275contentQuery(d,Qe,5),e.\u0275\u0275contentQuery(d,Qe,4)),2&n){let ge;e.\u0275\u0275queryRefresh(ge=e.\u0275\u0275loadQuery())&&(s.tabBar=ge.first),e.\u0275\u0275queryRefresh(ge=e.\u0275\u0275loadQuery())&&(s.tabBars=ge)}},viewQuery:function(n,s){if(1&n&&e.\u0275\u0275viewQuery(S,5,rt),2&n){let d;e.\u0275\u0275queryRefresh(d=e.\u0275\u0275loadQuery())&&(s.outlet=d.first)}},features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:M,decls:6,vars:0,consts:[[1,"tabs-inner"],["tabsInner",""],["tabs","true",3,"stackWillChange","stackDidChange"],["outlet",""]],template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(_),e.\u0275\u0275projection(0),e.\u0275\u0275elementStart(1,"div",0,1)(3,"ion-router-outlet",2,3),e.\u0275\u0275listener("stackWillChange",function(ge){return s.onStackWillChange(ge)})("stackDidChange",function(ge){return s.onStackDidChange(ge)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275projection(5,1))},dependencies:[rt],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]}),t})(),st=(()=>{class t extends U.IonBackButton{constructor(n,s,d,ge,He,at){super(n,s,d,ge,He,at)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(rt,8),e.\u0275\u0275directiveInject(U.NavController),e.\u0275\u0275directiveInject(U.Config),e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(e.ChangeDetectorRef))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-back-button"]],features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t})(),St=(()=>{class t extends U.IonNav{constructor(n,s,d,ge,He,at){super(n,s,d,ge,He,at)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(e.EnvironmentInjector),e.\u0275\u0275directiveInject(e.Injector),e.\u0275\u0275directiveInject(U.AngularDelegate),e.\u0275\u0275directiveInject(e.NgZone),e.\u0275\u0275directiveInject(e.ChangeDetectorRef))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-nav"]],features:[e.\u0275\u0275InheritDefinitionFeature],ngContentSelectors:l,decls:1,vars:0,template:function(n,s){1&n&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275projection(0))},encapsulation:2,changeDetection:0}),t})(),pt=(()=>{class t extends U.RouterLinkDelegateDirective{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["","routerLink","",5,"a",5,"area"]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),At=(()=>{class t extends U.RouterLinkWithHrefDelegateDirective{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["a","routerLink",""],["area","routerLink",""]],features:[e.\u0275\u0275InheritDefinitionFeature]}),t})(),xt=(()=>{class t extends U.IonModal{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-modal"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(n,s){1&n&&e.\u0275\u0275template(0,ne,2,1,"div",0),2&n&&e.\u0275\u0275property("ngIf",s.isCmpOpen||s.keepContentsMounted)},dependencies:[W.NgIf,W.NgTemplateOutlet],encapsulation:2,changeDetection:0}),t})(),g=(()=>{class t extends U.IonPopover{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["ion-popover"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(n,s){1&n&&e.\u0275\u0275template(0,de,1,1,"ng-container",0),2&n&&e.\u0275\u0275property("ngIf",s.isCmpOpen||s.keepContentsMounted)},dependencies:[W.NgIf,W.NgTemplateOutlet],encapsulation:2,changeDetection:0}),t})();const K={provide:oe.NG_VALIDATORS,useExisting:(0,e.forwardRef)(()=>o),multi:!0};let o=(()=>{class t extends oe.MaxValidator{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(n,s){2&n&&e.\u0275\u0275attribute("max",s._enabled?s.max:null)},features:[e.\u0275\u0275ProvidersFeature([K]),e.\u0275\u0275InheritDefinitionFeature]}),t})();const r={provide:oe.NG_VALIDATORS,useExisting:(0,e.forwardRef)(()=>a),multi:!0};let a=(()=>{class t extends oe.MinValidator{}return t.\u0275fac=function(){let i;return function(s){return(i||(i=e.\u0275\u0275getInheritedFactory(t)))(s||t)}}(),t.\u0275dir=e.\u0275\u0275defineDirective({type:t,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(n,s){2&n&&e.\u0275\u0275attribute("min",s._enabled?s.min:null)},features:[e.\u0275\u0275ProvidersFeature([r]),e.\u0275\u0275InheritDefinitionFeature]}),t})(),c=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.a)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),h=(()=>{class t{create(n){return(0,w.c)(n)}easingTime(n,s,d,ge,He){return(0,J.g)(n,s,d,ge,He)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),f=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.b)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),N=(()=>{class t{constructor(n){this.zone=n}create(n,s=!1){return s&&Object.getOwnPropertyNames(n).forEach(d=>{if("function"==typeof n[d]){const ge=n[d];n[d]=(...He)=>this.zone.run(()=>ge(...He))}}),(0,T.createGesture)(n)}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275inject(e.NgZone))},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),O=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.l)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),z=(()=>{class t extends U.MenuController{constructor(){super(y.m)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),P=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.m),this.angularDelegate=(0,e.inject)(U.AngularDelegate),this.injector=(0,e.inject)(e.Injector),this.environmentInjector=(0,e.inject)(e.EnvironmentInjector)}create(n){return super.create({...n,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")})}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac}),t})(),Y=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.p)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();class ce extends U.OverlayBaseController{constructor(){super(p.c),this.angularDelegate=(0,e.inject)(U.AngularDelegate),this.injector=(0,e.inject)(e.Injector),this.environmentInjector=(0,e.inject)(e.EnvironmentInjector)}create(i){return super.create({...i,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")})}}let le=(()=>{class t extends U.OverlayBaseController{constructor(){super(p.t)}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=e.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const ve=(t,i,n)=>()=>{const s=i.defaultView;if(s&&typeof window<"u"){(0,j.s)({...t,_zoneGate:ge=>n.run(ge)});const d="__zone_symbol__addEventListener"in i.body?"__zone_symbol__addEventListener":"addEventListener";return function ie(){var t=[];if(typeof window<"u"){var i=window;(!i.customElements||i.Element&&(!i.Element.prototype.closest||!i.Element.prototype.matches||!i.Element.prototype.remove||!i.Element.prototype.getRootNode))&&t.push(x.e(6748).then(x.t.bind(x,30723,23))),("function"!=typeof Object.assign||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||i.NodeList&&!i.NodeList.prototype.forEach||!i.fetch||!function(){try{var s=new URL("b","http://a");return s.pathname="c%20d","http://a/c%20d"===s.href&&s.searchParams}catch{return!1}}()||typeof WeakMap>"u")&&t.push(x.e(2214).then(x.t.bind(x,24144,23)))}return Promise.all(t)}().then(()=>H(s,{exclude:["ion-tabs","ion-tab"],syncQueue:!0,raf:U.raf,jmp:ge=>n.runOutsideAngular(ge),ael(ge,He,at,Vt){ge[d](He,at,Vt)},rel(ge,He,at,Vt){ge.removeEventListener(He,at,Vt)}}))}};let Me=(()=>{class t{static forRoot(n){return{ngModule:t,providers:[{provide:U.ConfigToken,useValue:n},{provide:e.APP_INITIALIZER,useFactory:ve,multi:!0,deps:[U.ConfigToken,W.DOCUMENT,e.NgZone]},(0,U.provideComponentInputBinding)()]}}}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({providers:[U.AngularDelegate,P,ce],imports:[W.CommonModule]}),t})()}}]);