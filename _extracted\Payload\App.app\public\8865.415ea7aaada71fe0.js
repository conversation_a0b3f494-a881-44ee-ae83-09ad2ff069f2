(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8865],{77660:(K,P,t)=>{t.d(P,{M:()=>i,w:()=>c});var i=(()=>{return(e=i||(i={})).INIT="Init",e.END_PROCESS="EndProcess",e.BIOMETRICS_VALIDATION="BiometricsValidation",e.OTP_INICIAL="OtpInicial",e.SECURE_QUESTION="SecureQuestion",e.PASSWORD="Password",i;var e})(),c=(()=>((c||(c={})).FORGOT_PASSWORD="forgotPassword",c))()},24613:(K,P,t)=>{t.d(P,{y:()=>W,n:()=>Z});var i=t(15861),c=t(39904),e=t(87956),n=t(53113),o=t(13973),a=t(98699),u=t(77660);class m extends a.PartialSealed{static error(O){return new m("error",O)}static finish(O=""){return new m("finish",O)}static next(O){return new m("next",O)}}var s=t(71776),b=t(42168);class r{constructor(O,y,M,B,j,U,I){this.success=O,this.error=y,this.value=M,this.executionArn=B,this.taskToken=j,this.bodyStep=U,this.errorMessage=I}get message(){return this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP01)."}static error(){return new r(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP02).")}}function x(E,O=!1,y=!0){const M=E.body;let B;return M&&(B=Object.keys(M).find(U=>void 0!==M[U])),new r(y,O,B,E.executionArn,E.taskToken,E.body,E.error)}var h=t(51176),p=t(70658),d=t(99877);const _=p.N.cognitoForgotPassword.url;let v=(()=>{class E{constructor(y,M){this.http=y,this.forgotPasswordStore=M}cognitoForgotpassword(){const y=(new s.HttpParams).set("grant_type","client_credentials").set("client_id",p.N.cognitoForgotPassword.clientId).set("client_secret",p.N.cognitoForgotPassword.clientSecret),M=new s.HttpHeaders({"Content-Type":"application/x-www-form-urlencoded"});return(0,b.firstValueFrom)(this.http.post(_,y.toString(),{headers:M}).pipe((0,b.map)(B=>(this.forgotPasswordStore.setCognitoToken(B.access_token),B))))}requestForgotPassword(y,M){var B=this;return(0,i.Z)(function*(){const j=new s.HttpHeaders({Authorization:M});return(0,b.firstValueFrom)(B.http.post(c.bV.FORGOT_PASSWORD,{...y},{headers:j,observe:"response"}).pipe((0,b.map)(U=>{if(206===U.status&&U.body)throw new Error;return x(U.body)}),(0,b.tap)(({bodyStep:U})=>{U&&(B.forgotPasswordStore.setBodystep(U),U.SecureQuestion&&B.forgotPasswordStore.setSecureData(U.SecureQuestion))}),(0,b.catchError)(U=>{throw x(U,!0,!1),U}))).catch(U=>U?x(U,!0,!1):r.error())})()}}return E.\u0275fac=function(y){return new(y||E)(d.\u0275\u0275inject(s.HttpClient),d.\u0275\u0275inject(h.N))},E.\u0275prov=d.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})();var T=t(81536),S=t(95437);let C=(()=>{class E{constructor(y,M,B,j,U,I){this.forgotPasswordRepository=y,this.deviceService=M,this.forgotPasswordStore=B,this.cryptoService=j,this.publicKeyRepository=U,this.mboProvider=I}token(){var y=this;return(0,i.Z)(function*(){try{return yield y.forgotPasswordRepository.cognitoForgotpassword()}catch(M){throw y.mboProvider.toast.error("Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (cognito)"),M}})()}init(y){var M=this;return(0,i.Z)(function*(){return Promise.all([M.deviceService.getFingerprint(),M.deviceService.getInfo()]).then(([B,j])=>M.service({documentNumber:y.number,documentType:y.type,ipAddress:"0.0.0.0",deviceSerial:`${j.uuid}-${j.model}`,deviceFingerPrint:B})).then(B=>(M.forgotPasswordStore.setDocument(y.type,y.number),M.forgotPasswordStore.setExecutionArn(B.executionArn),M.forgotPasswordStore.setTaskToken(B.taskToken),B))})()}password(y){var M=this;return(0,i.Z)(function*(){const{executionArn:B,taskToken:j,rescue:U}=M.forgotPasswordStore.currentState,k={universalPassword:yield M.publicKeyRepository.requestEnrollment().then(w=>M.cryptoService.encodeRSA(w,y))};return U||(k.responseBiometricsValidation=!0),M.service({executionArn:B,body:k,taskToken:j})})()}rescue(){var y=this;return(0,i.Z)(function*(){const{executionArn:M,taskToken:B,rescue:j}=y.forgotPasswordStore.currentState,U=yield y.deviceService.getInfo();return y.service({executionArn:M,body:{responseBiometricsValidation:!j,...j&&{deviceOS:U.operatingSystem,deviceName:U.name}},taskToken:B}).then(I=>(y.forgotPasswordStore.setExecutionArn(I.executionArn),y.forgotPasswordStore.setTaskToken(I.taskToken),I))})()}otp(y){var M=this;return(0,i.Z)(function*(){const{executionArn:B,taskToken:j}=M.forgotPasswordStore.currentState,U=yield M.deviceService.getInfo();return M.service({executionArn:B,body:{otpValue:(yield M.cryptoService.encodeKeyEnrollment(y)).toString(),deviceOS:U.operatingSystem,deviceName:U.name},taskToken:j}).then(I=>(M.forgotPasswordStore.setExecutionArn(I.executionArn),M.forgotPasswordStore.setTaskToken(I.taskToken),I))})()}product(y){var M=this;return(0,i.Z)(function*(){const{executionArn:B,taskToken:j}=M.forgotPasswordStore.currentState;return M.service({executionArn:B,body:{responseSecureQuestion:y},taskToken:j}).then(U=>(M.forgotPasswordStore.setExecutionArn(U.executionArn),M.forgotPasswordStore.setTaskToken(U.taskToken),U))})()}service(y){const M=this.forgotPasswordStore.getCognitoToken();return this.forgotPasswordRepository.requestForgotPassword(y,M)}}return E.\u0275fac=function(y){return new(y||E)(d.\u0275\u0275inject(v),d.\u0275\u0275inject(e.U8),d.\u0275\u0275inject(h.N),d.\u0275\u0275inject(e.$I),d.\u0275\u0275inject(T.aH),d.\u0275\u0275inject(S.ZL))},E.\u0275prov=d.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})();const{AUTHENTICATION:{FORGOT_PASSWORD:N,ERRORS:A,LOGIN:R}}=c.Z6;let W=(()=>{class E{constructor(y){this.customerService=y}welcome(){return this.customerService.request().then(y=>a.Either.success(y)).catch(({message:y})=>a.Either.failure({message:y}))}}return E.\u0275fac=function(y){return new(y||E)(d.\u0275\u0275inject(e.vZ))},E.\u0275prov=d.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})(),Z=(()=>{class E{constructor(y,M,B){this.facialBiometricsStore=y,this.forgotPasswordStore=M,this.forgotPasswordStepService=B}document(y){var M=this;return(0,i.Z)(function*(){try{yield M.forgotPasswordStepService.token();const B=yield M.forgotPasswordStepService.init(y);return M.getNextStepForgotPassword(B)}catch(B){return m.error(B)}})()}password(y){var M=this;return(0,i.Z)(function*(){try{const B=yield M.forgotPasswordStepService.password(y);return M.forgotPasswordStore.setPassword(y),M.getNextStepForgotPassword(B)}catch({message:B}){return m.error(B)}})()}rescue(){var y=this;return(0,i.Z)(function*(){y.forgotPasswordStore.setRescue(!0);try{const M=yield y.forgotPasswordStepService.rescue(),B=y.getNextStepForgotPassword(M);return new Promise((j,U)=>{B.when({next:I=>j(I),error:I=>U(I)})})}catch(M){return Promise.reject(m.error(M))}})()}getCustomerDocument(){const{documentNumber:y,documentType:M}=this.forgotPasswordStore.currentState;return M&&y?new n.dp(M,y):void 0}otp(y){var M=this;return(0,i.Z)(function*(){try{const B=yield M.forgotPasswordStepService.otp(y);return M.getNextStepForgotPassword(B)}catch({message:B}){return m.error(B)}})()}product(y){var M=this;return(0,i.Z)(function*(){try{const B=yield M.forgotPasswordStepService.product(y);return M.getNextStepForgotPassword(B)}catch({message:B}){return m.error(B)}})()}getNextStepForgotPassword(y){const{success:M,value:B,error:j}=y,U=this.getStepForRedirect(B);return M&&B===u.M.END_PROCESS?(this.clearStoreState(),m.finish(U)):j?(this.clearStoreState(),m.error(U)):M?m.next(U):void 0}getStepForRedirect(y){switch(y){case u.M.BIOMETRICS_VALIDATION:return u.M.BIOMETRICS_VALIDATION.toString();case u.M.OTP_INICIAL:return N.OTP_VERIFICATION;case u.M.SECURE_QUESTION:return N.PRODUCT_VERIFICATION;case u.M.PASSWORD:return N.PASSWORD_ASSIGNMENT;case u.M.END_PROCESS:return R;default:return A.DEFAULT_MESSAGE}}clearStoreState(){this.forgotPasswordStore.clearState(),this.facialBiometricsStore.clearState()}}return E.\u0275fac=function(y){return new(y||E)(d.\u0275\u0275inject(o.H),d.\u0275\u0275inject(h.N),d.\u0275\u0275inject(C))},E.\u0275prov=d.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})()},51176:(K,P,t)=>{t.d(P,{N:()=>o});var i=t(20691),e=t(99877);let o=(()=>{class a extends i.Store{constructor(){super({rescue:!1,cognitoToken:null})}setDocument(m,s){this.reduce(b=>({...b,documentType:m,documentNumber:s}))}setRescue(m){this.reduce(s=>({...s,rescue:m}))}setExecutionArn(m){this.reduce(s=>({...s,executionArn:m}))}setPassword(m){this.reduce(s=>({...s,password:m}))}setBodystep(m){this.reduce(s=>({...s,body:m}))}setTaskToken(m){this.reduce(s=>({...s,taskToken:m}))}setSecureData(m){this.reduce(s=>({...s,secureData:m}))}getSecureData(){return this.select(({secureData:m})=>m)}setErrorCode(m){this.reduce(s=>({...s,errorCode:m}))}setCognitoToken(m){this.reduce(s=>({...s,cognitoToken:m}))}getExecutionArn(){return this.select(({executionArn:m})=>m)}getTaskToken(){return this.select(({taskToken:m})=>m)}getErrorCode(){return this.select(({errorCode:m})=>m)}getCognitoToken(){return this.select(({cognitoToken:m})=>m)}clearState(){this.reduce(()=>({}))}}return a.\u0275fac=function(m){return new(m||a)},a.\u0275prov=e.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},8865:(K,P,t)=>{t.r(P),t.d(P,{MboForgotPasswordWelcomePageModule:()=>R});var i=t(17007),c=t(78007),e=t(15861),n=t(24495),o=t(95437),a=t(39904),u=t(88844),m=t(53113),f=(t(50203),t(23730),t(9811)),l=(t(63805),t(57544)),x=t(77660),h=t(24613),p=t(99877),d=t(19102),_=t(48774),v=t(65887),T=t(45542),S=t(52528);let C=(()=>{class W{constructor(E,O,y,M){this.mboProvider=E,this.managerConfiguration=O,this.facialBiometricsInteractor=y,this.verifyForgotPassword=M,this.requesting=!1,this.documents=[u.jq,u.E2],this.documentNumber=new l.FormControl("",[n.C1]),this.documentType=new l.FormControl(a.Gd,[n.C1]),this.documentControls=new l.FormGroup({documentType:this.documentType,documentNumber:this.documentNumber}),this.backAction={id:"btn_welcome_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting,click:()=>{this.mboProvider.navigation.back(a.Z6.TRANSFERS.HOME)}}}ngOnInit(){this.subsTriggerCapture=this.facialBiometricsInteractor.triggerCapture.subscribe(E=>{this.requesting=E}),this.managerConfiguration.welcome().then(E=>{E.when({success:O=>{this.documentNumber.setValue(O?.documentType||a.Gd),this.documentNumber.setValue(O?.documentNumber)}})})}ngOnDestroy(){this.subsTriggerCapture.unsubscribe()}get disabled(){return this.documentControls.invalid}onSubmit(){var E=this;return(0,e.Z)(function*(){E.mboProvider.loader.open("Por favor espere..."),E.requesting=!0;try{const M=new m.dp(E.documentType.value?.code??"",E.documentNumber.value);yield(yield E.verifyForgotPassword.document(M)).when({next:(j=(0,e.Z)(function*(){yield E.onFacialBiometricProcess(M,x.w.FORGOT_PASSWORD)}),function(){return j.apply(this,arguments)}),error:j=>{E.mboProvider.navigation.back(j),E.verifyForgotPassword.clearStoreState(),E.mboProvider.loader.close(),E.requesting=!1}})}catch{E.mboProvider.loader.close(),E.requesting=!1}var j})()}onFacialBiometricProcess(E,O){var y=this;return(0,e.Z)(function*(){try{yield(yield y.facialBiometricsInteractor.verifySTep(E)).when({completed:B=>{y.facialBiometricsInteractor.saveDataParameters(B,O),y.facialBiometricsInteractor.handlerSheet(B)},error:B=>{y.mboProvider.toast.warning(B.messages.title,B.messages.description)}})}finally{y.mboProvider.loader.close(),y.requesting=!1}})()}}return W.\u0275fac=function(E){return new(E||W)(p.\u0275\u0275directiveInject(o.ZL),p.\u0275\u0275directiveInject(h.y),p.\u0275\u0275directiveInject(f.U),p.\u0275\u0275directiveInject(h.n))},W.\u0275cmp=p.\u0275\u0275defineComponent({type:W,selectors:[["bocc-forgot-password-welcome-page"]],decls:14,vars:7,consts:[[1,"bocc-forgot-password-page"],[1,"bocc-forgot-password-page__header"],[3,"leftAction"],["body","",1,"bocc-forgot-password-page__body"],[1,"body2-medium","bocc-forgot-password-page__body--center"],[1,"bocc-forgot-password-page__body__box-data"],["pageId","facial-biometrics-document",3,"documents","documentNumber","documentType","disabled"],[1,"bocc-forgot-password-page__footer"],["id","btn_security-forgot-password_submit","bocc-button","raised",3,"spinner","disabled","click"]],template:function(E,O){1&E&&(p.\u0275\u0275elementStart(0,"div",0)(1,"div",1),p.\u0275\u0275element(2,"bocc-header-form",2),p.\u0275\u0275elementEnd(),p.\u0275\u0275elementStart(3,"div",3),p.\u0275\u0275element(4,"mbo-bank-logo"),p.\u0275\u0275elementStart(5,"div",4),p.\u0275\u0275text(6," Olvido de contrase\xf1a "),p.\u0275\u0275elementEnd(),p.\u0275\u0275elementStart(7,"div",5),p.\u0275\u0275element(8,"mbo-document-customer",6),p.\u0275\u0275elementEnd()(),p.\u0275\u0275elementStart(9,"div",7)(10,"button",8),p.\u0275\u0275listener("click",function(){return O.onSubmit()}),p.\u0275\u0275elementStart(11,"span"),p.\u0275\u0275text(12,"Continuar"),p.\u0275\u0275elementEnd()()()(),p.\u0275\u0275element(13,"bocc-template-form")),2&E&&(p.\u0275\u0275advance(2),p.\u0275\u0275property("leftAction",O.backAction),p.\u0275\u0275advance(6),p.\u0275\u0275property("documents",O.documents)("documentNumber",O.documentNumber)("documentType",O.documentType)("disabled",O.requesting),p.\u0275\u0275advance(2),p.\u0275\u0275property("spinner",O.requesting)("disabled",O.disabled||O.requesting))},dependencies:[d.r,_.J,v.X,T.P,S.A],styles:["/*!\n * BOCC Welcome Forgot Password\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 10/Sep/2024\n * Updated: 30/Abr/2025\n */.bocc-forgot-password-page{width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-between}.bocc-forgot-password-page__header{--bocc-button-padding: 0rem var(--sizing-x2);--bocc-button-height: var(--sizing-x16);position:relative;display:flex;justify-content:space-between;align-items:flex-start}.bocc-forgot-password-page__header--invert{flex-direction:row-reverse}.bocc-forgot-password-page__header .bocc-header-form{padding:var(--sizing-x4) var(--sizing-x3)}.bocc-forgot-password-page__body{padding:var(--sizing-x8)}.bocc-forgot-password-page__body mbo-bank-logo{padding-bottom:var(--sizing-x16)}.bocc-forgot-password-page__body__box-data{padding:var(--sizing-x16) 0}.bocc-forgot-password-page__body--center{padding-bottom:var(--sizing-x8);text-align:center}.bocc-forgot-password-page__footer{padding:var(--sizing-x8)}.bocc-forgot-password-page__footer button{width:100%}@supports (-webkit-touch-callout: none){.bocc-forgot-password-page__header{padding-top:calc(0rem + var(--sizing-safe-top, 0rem))}.bocc-forgot-password-page__footer{padding-bottom:calc(0rem + var(--sizing-safe-top, 0rem))}}\n"],encapsulation:2}),W})();var N=t(79798),A=t(30263);let R=(()=>{class W{}return W.\u0275fac=function(E){return new(E||W)},W.\u0275mod=p.\u0275\u0275defineNgModule({type:W}),W.\u0275inj=p.\u0275\u0275defineInjector({imports:[i.CommonModule,c.RouterModule.forChild([{path:"",component:C}]),N.rw,A.Jx,N.XH,A.aR,A.P8,A.Av]}),W})()},19102:(K,P,t)=>{t.d(P,{r:()=>o});var i=t(17007),e=t(99877);let o=(()=>{class a{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return a.\u0275fac=function(m){return new(m||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(m,s){1&m&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&m&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",s.src,e.\u0275\u0275sanitizeUrl))},dependencies:[i.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),a})()},52701:(K,P,t)=>{t.d(P,{q:()=>a});var i=t(17007),e=t(30263),n=t(99877);let a=(()=>{class u{}return u.\u0275fac=function(s){return new(s||u)},u.\u0275cmp=n.\u0275\u0275defineComponent({type:u,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(s,b){1&s&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"bocc-icon",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"label",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()()),2&s&&(n.\u0275\u0275classMap(b.classTheme),n.\u0275\u0275advance(3),n.\u0275\u0275property("icon",b.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",b.label," "))},dependencies:[i.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),u})()},55648:(K,P,t)=>{t.d(P,{u:()=>b});var i=t(15861),c=t(17007),n=t(30263),o=t(78506),a=t(99877);function m(f,r){if(1&f){const l=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",2),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(l);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&f){const l=a.\u0275\u0275nextContext();a.\u0275\u0275property("prefixIcon",l.icon)("disabled",l.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate1(" ",l.label," ")}}function s(f,r){if(1&f){const l=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",3),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(l);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementEnd()}if(2&f){const l=a.\u0275\u0275nextContext();a.\u0275\u0275property("bocc-button-action",l.icon)("disabled",l.disabled)}}let b=(()=>{class f{constructor(l){this.preferences=l,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:l})=>{this.isIncognito=l||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var l=this;return(0,i.Z)(function*(){yield l.preferences.toggleIncognito()})()}}return f.\u0275fac=function(l){return new(l||f)(a.\u0275\u0275directiveInject(o.Bx))},f.\u0275cmp=a.\u0275\u0275defineComponent({type:f,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(l,x){1&l&&(a.\u0275\u0275template(0,m,3,3,"button",0),a.\u0275\u0275template(1,s,1,2,"button",1)),2&l&&(a.\u0275\u0275property("ngIf",!x.actionMode),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",x.actionMode))},dependencies:[c.CommonModule,c.NgIf,n.P8,n.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),f})()},72765:(K,P,t)=>{t.d(P,{rw:()=>i.r,qr:()=>c.q,uf:()=>e.u,Z:()=>m,t5:()=>h,$O:()=>x});var i=t(19102),c=t(52701),e=t(55648),n=t(17007),o=t(30263),a=t(99877);const u=["*"];let m=(()=>{class p{constructor(){this.disabled=!1}}return p.\u0275fac=function(_){return new(_||p)},p.\u0275cmp=a.\u0275\u0275defineComponent({type:p,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],ngContentSelectors:u,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(_,v){1&_&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275element(2,"bocc-icon",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",3),a.\u0275\u0275projection(4),a.\u0275\u0275elementEnd()()),2&_&&(a.\u0275\u0275classProp("mbo-poster__content--disabled",v.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275property("icon",v.icon))},dependencies:[n.CommonModule,o.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),p})();var s=t(33395),b=t(77279),f=t(87903),r=t(87956),l=t(25317);let x=(()=>{class p{constructor(_){this.eventBusService=_}onCopy(){this.value&&((0,f.Bn)(this.value),this.eventBusService.emit(b.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return p.\u0275fac=function(_){return new(_||p)(a.\u0275\u0275directiveInject(r.Yd))},p.\u0275cmp=a.\u0275\u0275defineComponent({type:p,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(_,v){1&_&&(a.\u0275\u0275elementStart(0,"bocc-icon",0),a.\u0275\u0275listener("click",function(){return v.onCopy()}),a.\u0275\u0275elementEnd()),2&_&&a.\u0275\u0275property("id",v.elementId)},dependencies:[n.CommonModule,o.Zl,s.kW,l.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),p})(),h=(()=>{class p{}return p.\u0275fac=function(_){return new(_||p)},p.\u0275cmp=a.\u0275\u0275defineComponent({type:p,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(_,v){1&_&&a.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&_&&(a.\u0275\u0275property("value",v.value),a.\u0275\u0275advance(1),a.\u0275\u0275property("value",v.value))},dependencies:[n.CommonModule,o.qd,x],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),p})()},79798:(K,P,t)=>{t.d(P,{Vc:()=>c.Vc,rw:()=>i.rw,k4:()=>c.k4,qr:()=>i.qr,uf:()=>i.uf,xO:()=>n.x,A6:()=>e.A,tu:()=>l,Tj:()=>x,GI:()=>I,Uy:()=>k,To:()=>B,w7:()=>U,o2:()=>c.o2,B_:()=>c.B_,fi:()=>c.fi,XH:()=>c.XH,cN:()=>c.cN,Aj:()=>c.Aj,J5:()=>c.J5,DB:()=>H.D,NH:()=>w.N,ES:()=>L.E,Nu:()=>c.Nu,x6:()=>D.x,KI:()=>Y.K,iF:()=>c.iF,u8:()=>X.u,eM:()=>oe.e,ZF:()=>ne.Z,wu:()=>J.w,$n:()=>q.$,KN:()=>re.K,cV:()=>ce.c,t5:()=>i.t5,$O:()=>i.$O,ZS:()=>se.Z,sO:()=>le.s,bL:()=>pe,zO:()=>te.z});var i=t(72765),c=t(27302),e=t(1027),n=t(7427),a=(t(16442),t(17007)),u=t(30263),m=t(44487),s=t.n(m),b=t(13462),f=t(21498),r=t(99877);let l=(()=>{class F{}return F.\u0275fac=function(z){return new(z||F)},F.\u0275mod=r.\u0275\u0275defineNgModule({type:F}),F.\u0275inj=r.\u0275\u0275defineInjector({imports:[a.CommonModule,b.LottieModule.forRoot({player:()=>s()}),i.rw,u.P8,u.Dj,f.P]}),F})(),x=(()=>{class F{ngBoccPortal(z){this.portal=z}onSubmit(){this.portal?.close()}}return F.\u0275fac=function(z){return new(z||F)},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementStart(3,"label"),r.\u0275\u0275text(4," \xa1Atenci\xf3n! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p"),r.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),r.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"li",4),r.\u0275\u0275text(11,"Transacciones a celulares."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"li",4),r.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"li",4),r.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(16,"p",5),r.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(18,"div",6)(19,"button",7),r.\u0275\u0275listener("click",function(){return G.onSubmit()}),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,"Continuar"),r.\u0275\u0275elementEnd()()())},dependencies:[a.CommonModule,u.Zl,u.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),F})();var h=t(7603),p=t(87956),d=t(74520),_=t(39904),v=t(87903);function S(F,V){if(1&F){const z=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",6)(1,"label",7),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"span",8),r.\u0275\u0275text(4,"Tu gerente asignado (a)"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",8),r.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"button",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(z);const $=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView($.onEmail($.manager.email))}),r.\u0275\u0275elementStart(8,"span",10),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()()}if(2&F){const z=r.\u0275\u0275nextContext(2);r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",z.manager.name," "),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",z.manager.email," ")}}function C(F,V){if(1&F){const z=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),r.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"button",12),r.\u0275\u0275listener("click",function($){r.\u0275\u0275restoreView(z);const ee=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(ee.onRetryManager($))}),r.\u0275\u0275elementStart(4,"span"),r.\u0275\u0275text(5,"Recargar"),r.\u0275\u0275elementEnd()()()}}function N(F,V){if(1&F&&(r.\u0275\u0275elementStart(0,"div",3),r.\u0275\u0275template(1,S,10,2,"div",4),r.\u0275\u0275template(2,C,6,0,"div",5),r.\u0275\u0275elementEnd()),2&F){const z=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",z.manager),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!z.manager)}}function A(F,V){1&F&&(r.\u0275\u0275elementStart(0,"div",13),r.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),r.\u0275\u0275elementEnd()),2&F&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0))}t(29306);let R=(()=>{class F{constructor(z){this.customerService=z,this.requesting=!1}onRetryManager(z){this.customerService.requestManager(),z.stopPropagation()}onEmail(z){(0,v.Gw)(`mailto:${z}`)}onWhatsapp(){(0,v.Gw)(_.BA.WHATSAPP)}}return F.\u0275fac=function(z){return new(z||F)(r.\u0275\u0275directiveInject(p.vZ))},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,N,3,2,"div",1),r.\u0275\u0275template(2,A,5,4,"div",2),r.\u0275\u0275elementEnd()),2&z&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!G.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",G.requesting))},dependencies:[a.CommonModule,a.NgIf,u.P8,u.Dj,c.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),F})();const W={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},Z={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},E={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function y(F,V){if(1&F&&(r.\u0275\u0275elementStart(0,"div",7),r.\u0275\u0275element(1,"mbo-contact-manager",8),r.\u0275\u0275elementEnd()),2&F){const z=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("manager",z.manager)("requesting",z.requesting)}}function M(F,V){if(1&F){const z=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"li",9)(1,"div",10),r.\u0275\u0275listener("click",function($){const be=r.\u0275\u0275restoreView(z).$implicit,_e=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(_e.onOption(be,$))}),r.\u0275\u0275elementStart(2,"label",11),r.\u0275\u0275text(3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",12)(5,"div",13),r.\u0275\u0275element(6,"bocc-icon",14),r.\u0275\u0275elementEnd()()()()}if(2&F){const z=V.$implicit;r.\u0275\u0275property("id",z.id),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",z.label," "),r.\u0275\u0275advance(1),r.\u0275\u0275attribute("bocc-theme",z.boccTheme),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",z.icon)}}let B=(()=>{class F{constructor(z,G,$){this.utagService=z,this.customerStore=G,this.customerService=$,this.isManagerEnabled=!1,this.requesting=!1,this.options=[W,Z,E]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:z})=>{this.isManagerEnabled=z?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(G=>{this.manager=G.manager,this.requesting=G.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(z){this.portal=z}onOption(z,G){this.utagService.link("click",z.id),this.portal?.send({action:"option",value:z}),G.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return F.\u0275fac=function(z){return new(z||F)(r.\u0275\u0275directiveInject(h.D),r.\u0275\u0275directiveInject(d.f),r.\u0275\u0275directiveInject(p.vZ))},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-contact-information"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275listener("click",function(){return G.onClose()}),r.\u0275\u0275template(1,y,2,2,"div",1),r.\u0275\u0275elementStart(2,"ul",2),r.\u0275\u0275template(3,M,7,4,"li",3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),r.\u0275\u0275listener("click",function(){return G.onClose()}),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(6,"div",6),r.\u0275\u0275listener("click",function(){return G.onClose()}),r.\u0275\u0275elementEnd()),2&z&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",G.isManagerEnabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngForOf",G.options))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,u.Zl,R],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),F})();var j=t(95437);let U=(()=>{class F{constructor(z,G){this.floatingService=z,this.mboProvider=G,this.contactsFloating=this.floatingService.create(B),this.contactsFloating?.subscribe(({action:$,value:ee})=>{"option"===$?this.dispatchOption(ee):this.close()})}subscribe(z){this.subscriber=z}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(z){"PQRS"===z.action?this.mboProvider.openUrl(_.BA.PQRS):this.subscriber&&this.subscriber(z)}}return F.\u0275fac=function(z){return new(z||F)(r.\u0275\u0275inject(u.B7),r.\u0275\u0275inject(j.ZL))},F.\u0275prov=r.\u0275\u0275defineInjectable({token:F,factory:F.\u0275fac,providedIn:"root"}),F})(),I=(()=>{class F{constructor(){this.defenderLineNumber=_._L.DEFENDER_LINE,this.defenderLinePhone=_.WB.DEFENDER_LINE}ngBoccPortal(z){}onEmail(){(0,v.Gw)("mailto:<EMAIL>")}}return F.\u0275fac=function(z){return new(z||F)},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-contact-phones"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275element(1,"mbo-attention-lines-form"),r.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),r.\u0275\u0275element(5,"bocc-icon",4),r.\u0275\u0275elementStart(6,"span",5),r.\u0275\u0275text(7,"Defensor del consumidor financiero"),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),r.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"label",8)(13,"span"),r.\u0275\u0275text(14,"Lorena Cerchar Rosado"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(15,"bocc-badge",9),r.\u0275\u0275text(16," Suplente "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(17,"div",10),r.\u0275\u0275element(18,"bocc-icon",11),r.\u0275\u0275elementStart(19,"div",12)(20,"span",13),r.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(22,"div",10),r.\u0275\u0275element(23,"bocc-icon",14),r.\u0275\u0275elementStart(24,"div",12)(25,"a",15),r.\u0275\u0275text(26),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(27,"span",13),r.\u0275\u0275text(28," Ext. 15318 - 15311 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(29,"div",10),r.\u0275\u0275element(30,"bocc-icon",16),r.\u0275\u0275elementStart(31,"div",12)(32,"span",17),r.\u0275\u0275listener("click",function(){return G.onEmail()}),r.\u0275\u0275text(33," <EMAIL> "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(34,"div",10),r.\u0275\u0275element(35,"bocc-icon",18),r.\u0275\u0275elementStart(36,"div",12)(37,"span",13),r.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),r.\u0275\u0275elementEnd()()()()()()),2&z&&(r.\u0275\u0275advance(25),r.\u0275\u0275property("href",G.defenderLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",G.defenderLineNumber," "))},dependencies:[a.CommonModule,u.Zl,u.Oh,c.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),F})(),k=(()=>{class F{constructor(){this.whatsappNumber=_._L.WHATSAPP}ngBoccPortal(z){}onClick(){(0,v.Gw)(_.BA.WHATSAPP)}}return F.\u0275fac=function(z){return new(z||F)},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",2)(4,"button",3),r.\u0275\u0275listener("click",function(){return G.onClick()}),r.\u0275\u0275elementStart(5,"span"),r.\u0275\u0275text(6),r.\u0275\u0275elementEnd()()()()),2&z&&(r.\u0275\u0275advance(6),r.\u0275\u0275textInterpolate(G.whatsappNumber))},dependencies:[a.CommonModule,u.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),F})();var w=t(10119),D=(t(87677),t(68789)),L=t(10455),H=t(91642),Y=t(10464),X=t(75221),te=t(88649),oe=t(13043),ne=t(38116),J=t(68819),q=t(19310),re=t(94614),ce=(t(70957),t(91248),t(4663)),se=t(13961),le=t(66709),Q=t(24495),ie=t(57544),de=t(53113);class me extends ie.FormGroup{constructor(){const V=new ie.FormControl("",[Q.zf,Q.O_,Q.Y2,(0,Q.Mv)(24)]),z=new ie.FormControl("",[Q.C1,Q.zf,Q.O_,Q.Y2,(0,Q.Mv)(24)]);super({controls:{description:z,reference:V}}),this.description=z,this.reference=V}setNote(V){this.description.setValue(V?.description),this.reference.setValue(V?.reference)}getNote(){return new de.$H(this.description.value,this.reference.value)}}function ue(F,V){if(1&F&&r.\u0275\u0275element(0,"bocc-input-box",7),2&F){const z=r.\u0275\u0275nextContext();r.\u0275\u0275property("formControl",z.formControls.reference)}}let pe=(()=>{class F{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new me}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(z){this.portal=z}}return F.\u0275fac=function(z){return new(z||F)},F.\u0275cmp=r.\u0275\u0275defineComponent({type:F,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(z,G){1&z&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"bocc-input-box",5),r.\u0275\u0275template(7,ue,1,1,"bocc-input-box",6),r.\u0275\u0275elementEnd()()),2&z&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",G.cancelAction)("rightAction",G.saveAction),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",G.title," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("formControl",G.formControls.description),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",G.requiredReference))},dependencies:[a.CommonModule,a.NgIf,u.Jx,u.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),F})()},35324:(K,P,t)=>{t.d(P,{V:()=>s});var i=t(17007),e=t(30263),n=t(39904),o=t(87903),a=t(99877);function m(b,f){if(1&b){const r=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"a",9),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(r);const x=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(x.onWhatsapp())}),a.\u0275\u0275elementStart(1,"div",3),a.\u0275\u0275element(2,"bocc-icon",10),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",5)(4,"label",6),a.\u0275\u0275text(5," Whatsapp "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"label",7),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd()()()}if(2&b){const r=a.\u0275\u0275nextContext();a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",r.whatsappNumber," ")}}let s=(()=>{class b{constructor(){this.whatsapp=!1,this.whatsappNumber=n._L.WHATSAPP,this.nationalLineNumber=n._L.NATIONAL_LINE,this.bogotaLineNumber=n._L.BOGOTA_LINE,this.nationalLinePhone=n.WB.NATIONAL_LINE,this.bogotaLinePhone=n.WB.BOGOTA_LINE}onWhatsapp(){(0,o.Gw)(n.BA.WHATSAPP)}}return b.\u0275fac=function(r){return new(r||b)},b.\u0275cmp=a.\u0275\u0275defineComponent({type:b,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(r,l){1&r&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275template(1,m,8,1,"a",1),a.\u0275\u0275elementStart(2,"a",2)(3,"div",3),a.\u0275\u0275element(4,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(5,"div",5)(6,"label",6),a.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(8,"label",7),a.\u0275\u0275text(9),a.\u0275\u0275elementEnd()()(),a.\u0275\u0275elementStart(10,"a",8)(11,"div",3),a.\u0275\u0275element(12,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(13,"div",5)(14,"label",6),a.\u0275\u0275text(15," Bogot\xe1 "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(16,"label",7),a.\u0275\u0275text(17),a.\u0275\u0275elementEnd()()()()),2&r&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",l.whatsapp),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",l.nationalLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",l.nationalLineNumber," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",l.bogotaLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",l.bogotaLineNumber," "))},dependencies:[i.CommonModule,i.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),b})()},9593:(K,P,t)=>{t.d(P,{k:()=>m});var i=t(17007),e=t(30263),n=t(39904),o=t(95437),a=t(99877);let m=(()=>{class s{constructor(f){this.mboProvider=f,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(n.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(n.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(n.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return s.\u0275fac=function(f){return new(f||s)(a.\u0275\u0275directiveInject(o.ZL))},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(f,r){1&f&&(a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275listener("click",function(){return r.onProducts()}),a.\u0275\u0275element(3,"bocc-icon",3),a.\u0275\u0275elementStart(4,"label",4),a.\u0275\u0275text(5," Productos "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(6,"div",5),a.\u0275\u0275listener("click",function(){return r.onTransfers()}),a.\u0275\u0275element(7,"bocc-icon",6),a.\u0275\u0275elementStart(8,"label",4),a.\u0275\u0275text(9," Transferir "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(10,"div",7),a.\u0275\u0275listener("click",function(){return r.onPaymentQR()}),a.\u0275\u0275elementStart(11,"div",8)(12,"div",9),a.\u0275\u0275element(13,"bocc-icon",10),a.\u0275\u0275elementEnd()(),a.\u0275\u0275element(14,"bocc-icon",11),a.\u0275\u0275elementStart(15,"label",4),a.\u0275\u0275text(16," Pago QR "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(17,"div",12),a.\u0275\u0275listener("click",function(){return r.onPayments()}),a.\u0275\u0275element(18,"bocc-icon",13),a.\u0275\u0275elementStart(19,"label",4),a.\u0275\u0275text(20," Pagar "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(21,"div",14),a.\u0275\u0275listener("click",function(){return r.onToken()}),a.\u0275\u0275element(22,"bocc-icon",15),a.\u0275\u0275elementStart(23,"label",4),a.\u0275\u0275text(24," Token "),a.\u0275\u0275elementEnd()()()()),2&f&&(a.\u0275\u0275advance(2),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isProducts),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isTransfers),a.\u0275\u0275advance(11),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isPayments),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[i.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),s})()},83867:(K,P,t)=>{t.d(P,{o:()=>p});var i=t(17007),e=t(30263),n=t(8834),o=t(98699),s=(t(57544),t(99877));function f(d,_){if(1&d&&(s.\u0275\u0275elementStart(0,"label",11),s.\u0275\u0275text(1),s.\u0275\u0275elementEnd()),2&d){const v=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-currency-box__rate--active",v.hasValue),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate2(" ",v.valueFormat," ",v.rateCode," ")}}function r(d,_){if(1&d&&(s.\u0275\u0275elementStart(0,"div",12),s.\u0275\u0275element(1,"img",13),s.\u0275\u0275elementEnd()),2&d){const v=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("src",v.icon,s.\u0275\u0275sanitizeUrl)}}function l(d,_){if(1&d&&(s.\u0275\u0275elementStart(0,"div",14),s.\u0275\u0275text(1),s.\u0275\u0275elementEnd()),2&d){const v=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",v.currencyCode," ")}}function x(d,_){if(1&d&&(s.\u0275\u0275elementStart(0,"div",15),s.\u0275\u0275element(1,"bocc-icon",16),s.\u0275\u0275elementStart(2,"span",17),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd()()),2&d){const v=s.\u0275\u0275nextContext();s.\u0275\u0275advance(3),s.\u0275\u0275textInterpolate1(" ",null==v.formControl.error?null:v.formControl.error.message," ")}}function h(d,_){if(1&d&&(s.\u0275\u0275elementStart(0,"div",18),s.\u0275\u0275element(1,"bocc-icon",19),s.\u0275\u0275elementStart(2,"span",17),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd()()),2&d){const v=s.\u0275\u0275nextContext();s.\u0275\u0275advance(3),s.\u0275\u0275textInterpolate1(" ",v.helperInfo," ")}}let p=(()=>{class d{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,o.itIsDefined)(this.rate)}get value(){const v=+this.formControl?.value;return isNaN(v)?0:this.hasRate?v/this.rate:0}get valueFormat(){return(0,n.b)({value:this.value,symbol:"$",decimals:!0})}}return d.\u0275fac=function(v){return new(v||d)},d.\u0275cmp=s.\u0275\u0275defineComponent({type:d,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(v,T){1&v&&(s.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),s.\u0275\u0275text(3),s.\u0275\u0275elementEnd(),s.\u0275\u0275template(4,f,2,4,"label",3),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"div",4)(6,"div",5),s.\u0275\u0275template(7,r,2,1,"div",6),s.\u0275\u0275element(8,"bocc-currency-field",7),s.\u0275\u0275template(9,l,2,1,"div",8),s.\u0275\u0275elementEnd()(),s.\u0275\u0275template(10,x,4,1,"div",9),s.\u0275\u0275template(11,h,4,1,"div",10),s.\u0275\u0275elementEnd()),2&v&&(s.\u0275\u0275classProp("mbo-currency-box--focused",T.formControl.focused)("mbo-currency-box--error",T.formControl.invalid&&T.formControl.touched)("mbo-currency-box--disabled",T.formControl.disabled||T.disabled),s.\u0275\u0275advance(2),s.\u0275\u0275property("for",T.elementId),s.\u0275\u0275advance(1),s.\u0275\u0275textInterpolate1(" ",T.label," "),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",T.hasRate),s.\u0275\u0275advance(3),s.\u0275\u0275property("ngIf",T.icon),s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",T.elementId)("placeholder",T.placeholder)("disabled",T.disabled)("formControl",T.formControl),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",T.currencyCode),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",T.formControl.invalid&&T.formControl.touched),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",T.helperInfo&&!(T.formControl.invalid&&T.formControl.touched)))},dependencies:[i.CommonModule,i.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),d})()},85070:(K,P,t)=>{t.d(P,{f:()=>u});var i=t(17007),e=t(78506),n=t(99877);const a=["*"];let u=(()=>{class m{constructor(b){this.session=b}ngOnInit(){this.session.customer().then(b=>this.customer=b)}}return m.\u0275fac=function(b){return new(b||m)(n.\u0275\u0275directiveInject(e._I))},m.\u0275cmp=n.\u0275\u0275defineComponent({type:m,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(b,f){1&b&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"label",1),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",2),n.\u0275\u0275projection(4),n.\u0275\u0275elementEnd()()),2&b&&(n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==f.customer?null:f.customer.shortName))},dependencies:[i.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),m})()},65887:(K,P,t)=>{t.d(P,{X:()=>b});var i=t(17007),e=t(99877),o=t(30263),a=t(24495);function s(f,r){1&f&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let b=(()=>{class f{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[a.C1]:[]),this.unsubscription=this.documentType.subscribe(l=>{l&&(this.updateNumber(l,this.required),this.inputType=this.getInputType(l))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(l){if(l.required){const x=l.required.currentValue;this.documentType.setValidators(x?[a.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,x)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(l){return"PA"===l.code?"text":"number"}updateNumber(l,x){const h=this.validatorsForNumber(l,x);this.documentNumber.setValidators(h),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(l,x){return this.validatorsFromType(l).concat(x?[a.C1]:[])}maxLength(l){return x=>x&&x.length>l?{id:"maxLength",message:`Debe tener m\xe1ximo ${l} caracteres`}:null}validatorsFromType(l){switch(l.code){case"PA":return[a.JF];case"NIT":return[a.X1,this.maxLength(15)];default:return[a.X1]}}}return f.\u0275fac=function(l){return new(l||f)},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(l,x){1&l&&(e.\u0275\u0275template(0,s,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&l&&(e.\u0275\u0275property("ngIf",x.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",x.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",x.elementSelectId)("label",x.labelType)("suggestions",x.documents)("disabled",x.disabled)("formControl",x.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",x.elementInputId)("label",x.labelNumber)("type",x.inputType)("disabled",x.disabled)("formControl",x.documentNumber))},dependencies:[i.CommonModule,i.NgIf,o.DT,o.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),f})()},78021:(K,P,t)=>{t.d(P,{c:()=>f});var i=t(17007),e=t(30263),n=t(7603),o=t(98699),u=t(99877);function s(r,l){if(1&r){const x=u.\u0275\u0275getCurrentView();u.\u0275\u0275elementStart(0,"button",5),u.\u0275\u0275listener("click",function(){u.\u0275\u0275restoreView(x);const p=u.\u0275\u0275nextContext();return u.\u0275\u0275resetView(p.onAction(p.leftAction))}),u.\u0275\u0275elementStart(1,"span"),u.\u0275\u0275text(2),u.\u0275\u0275elementEnd()()}if(2&r){const x=u.\u0275\u0275nextContext();u.\u0275\u0275property("id",x.leftAction.id)("bocc-button",x.leftAction.type||"flat")("prefixIcon",x.leftAction.prefixIcon)("disabled",x.itIsDisabled(x.leftAction))("hidden",x.itIsHidden(x.leftAction)),u.\u0275\u0275advance(2),u.\u0275\u0275textInterpolate(x.leftAction.label)}}function b(r,l){if(1&r){const x=u.\u0275\u0275getCurrentView();u.\u0275\u0275elementStart(0,"button",6),u.\u0275\u0275listener("click",function(){const d=u.\u0275\u0275restoreView(x).$implicit,_=u.\u0275\u0275nextContext();return u.\u0275\u0275resetView(_.onAction(d))}),u.\u0275\u0275elementEnd()}if(2&r){const x=l.$implicit,h=u.\u0275\u0275nextContext();u.\u0275\u0275property("id",x.id)("type",x.type||"flat")("bocc-button-action",x.icon)("disabled",h.itIsDisabled(x))("hidden",h.itIsHidden(x))}}let f=(()=>{class r{constructor(x){this.utagService=x,this.rightActions=[]}itIsDisabled({disabled:x}){return(0,o.evalValueOrFunction)(x)}itIsHidden({hidden:x}){return(0,o.evalValueOrFunction)(x)}onAction(x){const{id:h}=x;h&&this.utagService.link("click",h),x.click()}}return r.\u0275fac=function(x){return new(x||r)(u.\u0275\u0275directiveInject(n.D))},r.\u0275cmp=u.\u0275\u0275defineComponent({type:r,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[u.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(x,h){1&x&&(u.\u0275\u0275elementStart(0,"div",0)(1,"div",1),u.\u0275\u0275template(2,s,3,6,"button",2),u.\u0275\u0275elementEnd(),u.\u0275\u0275elementStart(3,"div",3),u.\u0275\u0275template(4,b,1,5,"button",4),u.\u0275\u0275elementEnd()()),2&x&&(u.\u0275\u0275advance(2),u.\u0275\u0275property("ngIf",h.leftAction),u.\u0275\u0275advance(2),u.\u0275\u0275property("ngForOf",h.rightActions))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),r})()},27302:(K,P,t)=>{t.d(P,{Vc:()=>i.V,k4:()=>c.k,o2:()=>e.o,B_:()=>m,fi:()=>s.f,XH:()=>b.X,cN:()=>x.c,Aj:()=>h.A,J5:()=>N.J,Nu:()=>W,iF:()=>U});var i=t(35324),c=t(9593),e=t(83867),n=t(17007),o=t(99877);function u(I,k){if(1&I){const w=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"div",2),o.\u0275\u0275listener("click",function(){const L=o.\u0275\u0275restoreView(w).$implicit,H=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(H.onClickCurrency(L))}),o.\u0275\u0275elementStart(1,"div",3),o.\u0275\u0275element(2,"img",4)(3,"img",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()}if(2&I){const w=k.$implicit,g=o.\u0275\u0275nextContext();o.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",g.isEnabled(w)),o.\u0275\u0275advance(2),o.\u0275\u0275property("src",w.enabledIcon,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(1),o.\u0275\u0275property("src",w.disabledIcon,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",w.label," ")}}t(57544);let m=(()=>{class I{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[w]=this.currencies;this.formControl.setValue(w)}}ngOnChanges(w){const{currencies:g}=w;if(g){const[D]=g.currentValue;this.formControl&&this.formControl.setValue(D)}}isEnabled(w){return w===this.formControl?.value}onClickCurrency(w){this.formControl&&!this.disabled&&this.formControl.setValue(w)}changeCurriencies(w){if(w.currencies){const g=w.currencies.currentValue,[D]=g;this.formControl&&this.formControl.setValue(D)}}}return I.\u0275fac=function(w){return new(w||I)},I.\u0275cmp=o.\u0275\u0275defineComponent({type:I,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(w,g){1&w&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,u,6,5,"div",1),o.\u0275\u0275elementEnd()),2&w&&(o.\u0275\u0275classProp("mbo-currency-toggle--disabled",g.disabled),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngForOf",g.currencies))},dependencies:[n.CommonModule,n.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),I})();var s=t(85070),b=t(65887),f=t(30263),x=t(78021),h=t(50689),_=(t(7603),t(98699),t(72765)),N=t(88014);function A(I,k){if(1&I&&(o.\u0275\u0275elementStart(0,"div",4),o.\u0275\u0275element(1,"img",5),o.\u0275\u0275elementEnd()),2&I){const w=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("src",w.src,o.\u0275\u0275sanitizeUrl)}}const R=["*"];let W=(()=>{class I{}return I.\u0275fac=function(w){return new(w||I)},I.\u0275cmp=o.\u0275\u0275defineComponent({type:I,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:R,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(w,g){1&w&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,A,2,1,"div",1),o.\u0275\u0275elementStart(2,"div",2)(3,"div",3),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()()),2&w&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",g.src))},dependencies:[n.CommonModule,n.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),I})();var Z=t(24495);const E=/[A-Z]/,O=/[a-z]/,y=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,M=I=>I&&!E.test(I)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,B=I=>I&&!O.test(I)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,j=I=>I&&!y.test(I)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let U=(()=>{class I{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([Z.C1,B,M,j,(0,Z.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const w=this.formControl.errors.reduce((D,{id:L})=>[...D,L],[]),g=w.includes("required");this.smallInvalid=w.includes("smallCase")||g,this.capitalInvalid=w.includes("capitalCase")||g,this.specialCharInvalid=w.includes("specialChar")||g,this.minLengthInvalid=w.includes("minlength")||g})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return I.\u0275fac=function(w){return new(w||I)},I.\u0275cmp=o.\u0275\u0275defineComponent({type:I,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(w,g){1&w&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"bocc-password-box",1),o.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),o.\u0275\u0275text(4," Min\xfascula "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"mbo-poster",4),o.\u0275\u0275text(6," May\xfascula "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(7,"mbo-poster",5),o.\u0275\u0275text(8," Especial "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"mbo-poster",6),o.\u0275\u0275text(10," Caracteres "),o.\u0275\u0275elementEnd()()()),2&w&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("elementId",g.elementId)("disabled",g.disabled)("formControl",g.formControl),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",g.smallInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",g.capitalInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",g.specialCharInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",g.minLengthInvalid))},dependencies:[n.CommonModule,f.sC,_.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),I})()},50689:(K,P,t)=>{t.d(P,{A:()=>a});var i=t(17007),e=t(99877);const o=["*"];let a=(()=>{class u{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return u.\u0275fac=function(s){return new(s||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:o,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(s,b){1&s&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&s&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",b.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[i.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),u})()},88014:(K,P,t)=>{t.d(P,{J:()=>o});var i=t(17007),e=t(99877);let o=(()=>{class a{}return a.\u0275fac=function(m){return new(m||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(m,s){1&m&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[i.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),a})()},21498:(K,P,t)=>{t.d(P,{P:()=>r});var i=t(17007),e=t(30263),n=t(99877);function a(l,x){if(1&l&&n.\u0275\u0275element(0,"bocc-card-product-summary",7),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",h.information.product.color)("icon",h.information.product.icon)("number",h.information.product.number)("title",h.information.product.title)("subtitle",h.information.product.subtitle)}}function u(l,x){if(1&l&&n.\u0275\u0275element(0,"bocc-card-summary",8),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",h.information.standard.header)("title",h.information.standard.title)("subtitle",h.information.standard.subtitle)("detail",h.information.standard.detail)}}function m(l,x){if(1&l&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",h.information.amount.header)("amount",h.information.amount.value)("symbol",h.information.amount.symbol)("amountSmall",h.information.amount.small)}}function s(l,x){if(1&l&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",h.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(h.information.text.content)}}function b(l,x){if(1&l&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",12),n.\u0275\u0275element(1,"bocc-icon",13),n.\u0275\u0275elementStart(2,"span",14),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",15),n.\u0275\u0275elementStart(5,"span",14),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",h.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",h.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",h.information.datetime.time," ")}}function f(l,x){if(1&l&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&l){const h=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",h.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",h.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",h.information.badge.label," ")}}let r=(()=>{class l{}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=n.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(h,p){1&h&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,a,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,u,1,4,"bocc-card-summary",2),n.\u0275\u0275template(3,m,1,4,"bocc-card-summary",3),n.\u0275\u0275template(4,s,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,b,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,f,3,4,"bocc-card-summary",6),n.\u0275\u0275elementEnd()),2&h&&(n.\u0275\u0275property("ngSwitch",p.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[i.CommonModule,i.NgSwitch,i.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),l})()},7427:(K,P,t)=>{t.d(P,{x:()=>r});var i=t(17007),e=t(30263),n=t(87903),a=(t(29306),t(77279)),u=t(87956),m=t(68789),s=t(13961),b=t(99877);let r=(()=>{class l{constructor(h,p){this.eventBusService=h,this.onboardingScreenService=p,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,n.Bn)(this.product.tagAval),this.eventBusService.emit(a.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(s.Z)),this.tagAvalonboarding.open()}}return l.\u0275fac=function(h){return new(h||l)(b.\u0275\u0275directiveInject(u.Yd),b.\u0275\u0275directiveInject(m.x))},l.\u0275cmp=b.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[b.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(h,p){1&h&&(b.\u0275\u0275elementStart(0,"bocc-card-product",0),b.\u0275\u0275listener("key",function(){return p.onTagAval()})("onboarding",function(){return p.onBoarding()}),b.\u0275\u0275elementEnd()),2&h&&(b.\u0275\u0275classMap(p.product.bank.className),b.\u0275\u0275property("iconTitle",p.iconTitle)("title",p.product.nickname||p.product.name)("icon",p.product.logo)("tagAval",p.product.tagAvalFormat)("actions",p.actions)("color",p.product.color)("code",p.product.shortNumber)("label",p.product.label)("amount",p.product.amount)("incognito",p.incognito)("displayCard",!0)("statusLabel",null==p.product.status?null:p.product.status.label)("statusColor",null==p.product.status?null:p.product.status.color)("cromaline",!0)("msgError",p.msgError))},dependencies:[i.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),l})()},1027:(K,P,t)=>{t.d(P,{A:()=>h});var i=t(17007),c=t(72765),e=t(30263),n=t(99877);function o(p,d){if(1&p&&n.\u0275\u0275element(0,"bocc-card-product-summary",8),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("color",_.information.product.color)("icon",_.information.product.icon)("number",_.information.product.number)("title",_.information.product.title)("subtitle",_.information.product.subtitle)}}function a(p,d){if(1&p&&n.\u0275\u0275element(0,"bocc-card-summary",9),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.standard.header)("title",_.information.standard.title)("subtitle",_.information.standard.subtitle)}}function u(p,d){if(1&p&&n.\u0275\u0275element(0,"bocc-card-summary",10),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.amount.header)("amount",_.information.amount.value)("symbol",_.information.amount.symbol)}}function m(p,d){if(1&p&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.text.header)("customizedContent",!0),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(_.information.text.content)}}function s(p,d){if(1&p&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",13),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"bocc-icon",16),n.\u0275\u0275elementStart(5,"span",15),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.datetime.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",_.information.datetime.date," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",_.information.datetime.time," ")}}function b(p,d){if(1&p&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",17),n.\u0275\u0275element(1,"bocc-icon",14),n.\u0275\u0275elementStart(2,"span",15),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd()()),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.date.header)("customizedContent",!0),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",_.information.date.date," ")}}function f(p,d){if(1&p&&(n.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()),2&p){const _=n.\u0275\u0275nextContext();n.\u0275\u0275property("header",_.information.badge.header)("customizedContent",!0),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",_.information.badge.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",_.information.badge.label," ")}}let r=(()=>{class p{}return p.\u0275fac=function(_){return new(_||p)},p.\u0275cmp=n.\u0275\u0275defineComponent({type:p,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(_,v){1&_&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,o,1,5,"bocc-card-product-summary",1),n.\u0275\u0275template(2,a,1,3,"bocc-card-summary",2),n.\u0275\u0275template(3,u,1,3,"bocc-card-summary",3),n.\u0275\u0275template(4,m,3,3,"bocc-card-summary",4),n.\u0275\u0275template(5,s,7,4,"bocc-card-summary",5),n.\u0275\u0275template(6,b,4,3,"bocc-card-summary",6),n.\u0275\u0275template(7,f,3,4,"bocc-card-summary",7),n.\u0275\u0275elementEnd()),2&_&&(n.\u0275\u0275property("ngSwitch",v.information.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","product"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","standard"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","amount"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","text"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","datetime"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","date"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[i.CommonModule,i.NgSwitch,i.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),p})();function l(p,d){1&p&&n.\u0275\u0275element(0,"mbo-card-information-element",8),2&p&&n.\u0275\u0275property("information",d.$implicit)}const x=["*"];let h=(()=>{class p{constructor(){this.skeleton=!1,this.informations=[]}}return p.\u0275fac=function(_){return new(_||p)},p.\u0275cmp=n.\u0275\u0275defineComponent({type:p,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:x,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(_,v){1&_&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"mbo-bank-logo",3),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(4,"div",4),n.\u0275\u0275element(5,"div",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275projection(7),n.\u0275\u0275template(8,l,1,1,"mbo-card-information-element",7),n.\u0275\u0275elementEnd()()),2&_&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("result",!0),n.\u0275\u0275advance(5),n.\u0275\u0275property("ngForOf",v.informations))},dependencies:[i.CommonModule,i.NgForOf,c.rw,r],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),p})()},16442:(K,P,t)=>{t.d(P,{u:()=>_});var i=t(99877),e=t(17007),o=t(13462),u=t(19102),m=t(45542),s=t(65467),b=t(21498);function f(v,T){if(1&v&&(i.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&v){const S=i.\u0275\u0275nextContext();i.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",S.template.skeleton),i.\u0275\u0275property("secondary",!0)("active",S.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",S.template.header.subtitle," ")}}function r(v,T){1&v&&i.\u0275\u0275element(0,"mbo-card-information",16),2&v&&i.\u0275\u0275property("information",T.$implicit)}function l(v,T){if(1&v&&(i.\u0275\u0275elementStart(0,"div",14),i.\u0275\u0275projection(1),i.\u0275\u0275template(2,r,1,1,"mbo-card-information",15),i.\u0275\u0275elementEnd()),2&v){const S=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275property("ngForOf",S.template.informations)}}function x(v,T){1&v&&(i.\u0275\u0275elementStart(0,"div",17),i.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),i.\u0275\u0275elementEnd()),2&v&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0)("secondary",!0),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!0)("secondary",!0))}function h(v,T){if(1&v){const S=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",23),i.\u0275\u0275listener("click",function(){const A=i.\u0275\u0275restoreView(S).$implicit,R=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(R.onAction(A))}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&v){const S=T.$implicit;i.\u0275\u0275property("bocc-button",S.type)("prefixIcon",S.prefixIcon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(S.label)}}function p(v,T){if(1&v&&(i.\u0275\u0275elementStart(0,"div",21),i.\u0275\u0275template(1,h,3,3,"button",22),i.\u0275\u0275elementEnd()),2&v){const S=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("ngForOf",S.template.actions)}}const d=["*"];let _=(()=>{class v{constructor(){this.disabled=!1,this.action=new i.EventEmitter}onAction({event:S}){this.action.emit(S)}}return v.\u0275fac=function(S){return new(S||v)},v.\u0275cmp=i.\u0275\u0275defineComponent({type:v,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:d,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(S,C){1&S&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275element(3,"mbo-bank-logo",3),i.\u0275\u0275elementStart(4,"div",4),i.\u0275\u0275element(5,"ng-lottie",5),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),i.\u0275\u0275text(7),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(8,f,2,5,"bocc-skeleton-text",7),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(9,"div",8),i.\u0275\u0275element(10,"div",9),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(11,l,3,1,"div",10),i.\u0275\u0275template(12,x,4,5,"div",11),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(13,p,2,1,"div",12)),2&S&&(i.\u0275\u0275classProp("animation",!C.template.skeleton),i.\u0275\u0275advance(3),i.\u0275\u0275property("result",!0),i.\u0275\u0275advance(2),i.\u0275\u0275property("options",C.template.header.animation),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",C.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",C.template.header.title," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",C.template.skeleton||C.template.header.subtitle),i.\u0275\u0275advance(3),i.\u0275\u0275property("ngIf",!C.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",C.template.skeleton),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",C.template.actions.length&&!C.disabled))},dependencies:[e.NgForOf,e.NgIf,o.LottieComponent,u.r,m.P,s.D,b.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),v})()},10119:(K,P,t)=>{t.d(P,{N:()=>x});var i=t(17007),e=t(99877),o=t(30263),a=t(7603),u=t(98699);function s(h,p){if(1&h&&e.\u0275\u0275element(0,"bocc-diamond",14),2&h){const d=p.$implicit,_=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",_.itIsSelected(d))}}function b(h,p){if(1&h){const d=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(d);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const d=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",d.footerActionLeft.id)("bocc-button",d.footerActionLeft.type)("prefixIcon",d.footerActionLeft.prefixIcon)("disabled",d.itIsDisabled(d.footerActionLeft))("hidden",d.itIsHidden(d.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(d.footerActionLeft.label)}}function f(h,p){if(1&h){const d=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(d);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const d=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",d.footerActionRight.id)("bocc-button",d.footerActionRight.type)("prefixIcon",d.footerActionRight.prefixIcon)("disabled",d.itIsDisabled(d.footerActionRight))("hidden",d.itIsHidden(d.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(d.footerActionRight.label)}}const r=["*"];let x=(()=>{class h{constructor(d,_){this.ref=d,this.utagService=_,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((d,_)=>_),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(d){return d===this.currentPosition}itIsDisabled({disabled:d}){return(0,u.evalValueOrFunction)(d)}itIsHidden({hidden:d}){return(0,u.evalValueOrFunction)(d)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(d){const{id:_}=d;_&&this.utagService.link("click",_),d.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(d){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(d),this.automatic=!1,this.setTranslatePosition(d)}setTranslatePosition(d){this.translateX=d*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(d){this.transformContent=`translateX(${d}px)`}emitPosition(d){this.finished||(this.finished=d+1===this.elements.length),this.position.emit({position:d,finished:this.finished})}getPositionSlide(d){return d>=this.elements.length?this.elements.length-1:d<0?0:d}setTouchHandler(d){let _=0,v=0;d.addEventListener("touchstart",T=>{if(T.changedTouches.length){const{clientX:S}=T.changedTouches.item(0);_=0,this.touched=!0,v=S}}),d.addEventListener("touchmove",T=>{if(T.changedTouches.length){const S=T.changedTouches.item(0),C=S.clientX-v;v=S.clientX,this.translateX+=C,_+=C,this.setTranslateContent(this.translateX)}}),d.addEventListener("touchend",T=>{this.touched=!1,T.changedTouches.length&&(Math.abs(_)/this.widthBody*100>=40&&(_>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return h.\u0275fac=function(d){return new(d||h)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(a.D))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(d,_){1&d&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return _.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return _.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,s,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return _.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,b,3,6,"button",13),e.\u0275\u0275template(16,f,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&d&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",_.headerActionLeft)("rightAction",_.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",_.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",_.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",_.widthContent)("transform",_.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",_.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!_.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",_.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!_.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",_.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.footerActionRight))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,o.P8,o.u1,o.ou,o.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),h})()},68789:(K,P,t)=>{t.d(P,{x:()=>a});var i=t(7603),c=t(10455),e=t(87677),n=t(99877);let a=(()=>{class u{constructor(s){this.portalService=s}information(){this.portal||(this.portal=this.portalService.container({component:c.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(s,b){return this.portalService.container({component:s,container:e.C,props:{container:b?.containerProps,component:b?.componentProps}})}}return u.\u0275fac=function(s){return new(s||u)(n.\u0275\u0275inject(i.v))},u.\u0275prov=n.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},87677:(K,P,t)=>{t.d(P,{C:()=>e});var i=t(99877);let e=(()=>{class n{constructor(a){this.ref=a,this.visible=!1,this.visibleChange=new i.EventEmitter}open(a=0){setTimeout(()=>{this.changeVisible(!0)},a)}close(a=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},a)}append(a){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(a)}ngBoccPortal(a){this.portal=a}changeVisible(a){this.visible=a,this.visibleChange.emit(a)}}return n.\u0275fac=function(a){return new(a||n)(i.\u0275\u0275directiveInject(i.ElementRef))},n.\u0275cmp=i.\u0275\u0275defineComponent({type:n,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(a,u){1&a&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275element(1,"div",1),i.\u0275\u0275elementEnd()),2&a&&i.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",u.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),n})()},10455:(K,P,t)=>{t.d(P,{E:()=>u});var i=t(17007),e=t(99877),o=t(27302),a=t(10119);let u=(()=>{class m{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(b){this.portal=b}onPosition({finished:b}){this.finished=b,b&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return m.\u0275fac=function(b){return new(b||m)},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(b,f){1&b&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(l){return f.onPosition(l)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&b&&e.\u0275\u0275property("footerActionLeft",f.footerLeft)("footerActionRight",f.footerRight)("gradient",!0)},dependencies:[i.CommonModule,o.Nu,a.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),m})()},91642:(K,P,t)=>{t.d(P,{D:()=>_});var i=t(17007),e=t(99877),o=t(30263),a=t(87542),u=t(70658),m=t(3372),s=t(87956),b=t(72765);function f(v,T){1&v&&e.\u0275\u0275element(0,"mbo-bank-logo")}function r(v,T){1&v&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function l(v,T){if(1&v&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&v){const S=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",S.verifying)}}function x(v,T){1&v&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function h(v,T){if(1&v){const S=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(S);const N=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(N.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&v){const S=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",S.verifying)}}const p=["*"],{OtpInputSuperuser:d}=m.M;let _=(()=>{class v{constructor(S,C,N,A){this.ref=S,this.otpService=C,this.deviceService=N,this.preferencesService=A,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=a.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new a.yV}ngOnInit(){this.otpService.onCode(S=>{this.otpControls.setCode(S),this.otpControls.valid&&this.onAutocomplete(S)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(S){const{documentNumber:C}=S;C&&this.preferencesService.applyFunctionality(d,C.currentValue).then(N=>{this.itIsDocumentSuperuser=N})}get otpVisible(){return!u.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return u.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&u.N.otpReadonlyMobile}onAutocomplete(S){this.code.emit(S)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return v.\u0275fac=function(S){return new(S||v)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(s.no),e.\u0275\u0275directiveInject(s.U8),e.\u0275\u0275directiveInject(s.yW))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:p,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(S,C){1&S&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,f,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,r,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(A){return C.onAutocomplete(A)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,l,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,x,7,0,"div",8),e.\u0275\u0275template(13,h,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&S&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",C.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",C.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",C.otpVisible),e.\u0275\u0275property("formControls",C.otpControls)("readonly",C.otpReadonly)("mobile",C.otpMobile)("disabled",C.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",C.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",C.isIos))},dependencies:[i.CommonModule,i.NgIf,o.P8,o.Yx,b.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),v})()},10464:(K,P,t)=>{t.d(P,{K:()=>u});var i=t(17007),e=t(99877),o=t(22816);const a=["*"];let u=(()=>{class m{constructor(b){this.ref=b,this.scroller=new o.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(b){this.scroller.reset(b.target)}}return m.\u0275fac=function(b){return new(b||m)(e.\u0275\u0275directiveInject(e.ElementRef))},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(b,f){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(l){return f.onScroll(l)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&b&&e.\u0275\u0275classProp("mbo-page__content--start",f.scrollStart)("mbo-page__content--end",f.scrollEnd)},dependencies:[i.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),m})()},75221:(K,P,t)=>{t.d(P,{u:()=>m});var i=t(17007),e=t(30263),n=t(27302),a=(t(88649),t(99877));let m=(()=>{class s{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return s.\u0275fac=function(f){return new(f||s)},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(f,r){1&f&&a.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&f&&(a.\u0275\u0275property("formControl",r.passwordControl.controls.password)("disabled",r.disabled)("elementId",r.elementPasswordId),a.\u0275\u0275advance(1),a.\u0275\u0275property("elementId",r.elementConfirmId)("disabled",r.disabled)("formControl",r.passwordControl.controls.repeatPassword))},dependencies:[i.CommonModule,e.sC,n.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),s})()},88649:(K,P,t)=>{t.d(P,{z:()=>n});var i=t(57544),c=t(24495);class n extends i.FormGroup{constructor(){const a=new i.FormControl(""),u=new i.FormControl("",[c.C1,(o=a,a=>a&&a!==o.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var o;super({controls:{password:a,repeatPassword:u}})}get password(){return this.controls.password.value}}},13043:(K,P,t)=>{t.d(P,{e:()=>h});var i=t(17007),e=t(99877),o=t(30263),m=(t(57544),t(27302));function s(p,d){1&p&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function b(p,d){if(1&p&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&p){const _=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",_.title," ")}}function f(p,d){if(1&p){const _=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const S=e.\u0275\u0275restoreView(_).$implicit,C=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(C.onProduct(S))}),e.\u0275\u0275elementEnd()}if(2&p){const _=d.$implicit,v=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",_.color)("icon",_.logo)("title",_.nickname)("number",_.publicNumber)("detail",_.bank.name)("ghost",v.ghost)}}function r(p,d){if(1&p&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,b,2,1,"div",8),e.\u0275\u0275template(3,f,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&p){const _=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",_.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",_.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!_.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",_.msgError," ")}}function l(p,d){1&p&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&p&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const x=["*"];let h=(()=>{class p{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(_){return this.productControl?.value?.id===_.id}onProduct(_){this.select.emit(_),this.productControl?.setValue(_)}}return p.\u0275fac=function(_){return new(_||p)},p.\u0275cmp=e.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:x,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(_,v){1&_&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,s,3,0,"div",1),e.\u0275\u0275template(2,r,6,5,"div",2),e.\u0275\u0275template(3,l,3,2,"div",3),e.\u0275\u0275elementEnd()),2&_&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!v.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.skeleton))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,o.w_,m.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),p})()},38116:(K,P,t)=>{t.d(P,{Z:()=>u});var i=t(17007),e=t(99877),o=t(30263);function a(m,s){if(1&m){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(b).$implicit,x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onAction(l))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&m){const b=s.$implicit,f=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(f.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",f.itIsDisabled(b)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(f.theme(b)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",b.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",b.label," ")}}let u=(()=>{class m{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(b){return b.requiredInformation&&b.errorInformation}theme(b){return this.itIsDisabled(b)?"none":b.theme}onAction(b){!this.itIsDisabled(b)&&this.action.emit(b.type)}}return m.\u0275fac=function(b){return new(b||m)},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(b,f){1&b&&e.\u0275\u0275template(0,a,6,8,"div",0),2&b&&e.\u0275\u0275property("ngForOf",f.actions)},dependencies:[i.CommonModule,i.NgForOf,o.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),m})()},68819:(K,P,t)=>{t.d(P,{w:()=>R});var i=t(17007),e=t(99877),o=t(30263),a=t(39904),s=(t(57544),t(78506)),f=(t(29306),t(87903)),r=t(95437),l=t(27302),x=t(70957),h=t(91248),p=t(13961),d=t(68789),_=t(33395),v=t(25317);function T(W,Z){if(1&W){const E=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(y){e.\u0275\u0275restoreView(E);const M=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(M.onBoarding(y))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(E);const y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onCopyKey(y.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(E);const y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(y.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&W){const E=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",E.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",E.product.tagAval)}}function S(W,Z){if(1&W&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&W){const E=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",E.digitalSection)("currencyCode",null==E.currencyControl.value?null:E.currencyControl.value.code)("hidden",E.itIsVisibleMovements)}}function C(W,Z){if(1&W&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&W){const E=Z.$implicit,O=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",E)("currencyCode",null==O.currencyControl.value?null:O.currencyControl.value.code)}}const N=[[["","header",""]],"*"],A=["[header]","*"];let R=(()=>{class W{constructor(E,O,y){this.mboProvider=E,this.managerInformation=O,this.onboardingScreenService=y,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(E){const{movements:O,sections:y}=E;O&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!O.currentValue),y&&this.product&&this.refreshComponent(this.product,y.currentValue),this.managerInformation.requestInfoBody().then(M=>{M.when({success:({canEditTagAval:B})=>{this.canEditTagAval=B}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(E){(0,f.Bn)(E),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(a.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(E,O){const y=(0,f.A2)(E);if(this.sectionPosition=0,O?.length){const M=O.map(({title:B},j)=>({label:B,value:j}));y&&(this.headerMovements.value=this.sections.length,M.push(this.headerMovements)),this.headers=M}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const M=[{label:"Error",value:1}];y&&M.unshift(this.headerMovements),this.headers=M}}onBoarding(E){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(p.Z)),this.tagAvalonboarding.open(),E.stopPropagation()}}return W.\u0275fac=function(E){return new(E||W)(e.\u0275\u0275directiveInject(r.ZL),e.\u0275\u0275directiveInject(s.vu),e.\u0275\u0275directiveInject(d.x))},W.\u0275cmp=e.\u0275\u0275defineComponent({type:W,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:A,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(E,O){1&E&&(e.\u0275\u0275projectionDef(N),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(M){return O.sectionPosition=M}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,T,15,2,"div",4),e.\u0275\u0275template(6,S,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,C,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&E&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",O.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",O.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",O.headers)("value",O.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",O.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==O.product?null:O.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",O.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",O.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",O.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",O.movements)("header",O.header)("product",O.product)("currencyCode",null==O.currencyControl.value?null:O.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",O.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!O.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,o.Gf,o.qw,o.P8,o.Dj,o.qd,x.K,h.I,l.Aj,_.kW,v.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),W})()},19310:(K,P,t)=>{t.d(P,{$:()=>S});var i=t(17007),c=t(99877),e=t(30263),n=t(87903);let o=(()=>{class C{transform(A,R,W=" "){return(0,n.rd)(A,R,W)}}return C.\u0275fac=function(A){return new(A||C)},C.\u0275pipe=c.\u0275\u0275definePipe({name:"codeSplit",type:C,pure:!0}),C})(),a=(()=>{class C{}return C.\u0275fac=function(A){return new(A||C)},C.\u0275mod=c.\u0275\u0275defineNgModule({type:C}),C.\u0275inj=c.\u0275\u0275defineInjector({imports:[i.CommonModule]}),C})();t(57544);var m=t(70658),s=t(78506),f=(t(29306),t(87956)),r=t(72765),l=t(27302);function x(C,N){if(1&C){const A=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",18),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(A);const W=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(W.onDigital())}),c.\u0275\u0275elementStart(1,"span"),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd()()}if(2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275property("prefixIcon",A.digitalIcon),c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate(A.digitalIncognito?"Ver datos":"Ocultar datos")}}function h(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"span"),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate(null==A.product?null:A.product.publicNumber)}}function p(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"span",19),c.\u0275\u0275text(1),c.\u0275\u0275pipe(2,"codeSplit"),c.\u0275\u0275elementEnd()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",c.\u0275\u0275pipeBind2(2,1,A.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":A.digitalNumber,4)," ")}}function d(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"bocc-badge",20),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275attribute("bocc-theme",null==A.product||null==A.product.status?null:A.product.status.color),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",null==A.product||null==A.product.status?null:A.product.status.label," ")}}function _(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"div",21),c.\u0275\u0275element(1,"bocc-progress-bar",22),c.\u0275\u0275elementEnd()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("theme",A.progressBarTheme)("width",A.progressBarStatus)}}function v(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"div",23)(1,"label",24),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),c.\u0275\u0275element(5,"bocc-amount",26),c.\u0275\u0275elementEnd(),c.\u0275\u0275element(6,"mbo-button-incognito-mode",27),c.\u0275\u0275elementEnd()()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate1(" ",null==A.product?null:A.product.label," "),c.\u0275\u0275advance(2),c.\u0275\u0275property("active",!A.product),c.\u0275\u0275advance(1),c.\u0275\u0275property("amount",null==A.product?null:A.product.amount)("incognito",A.incognito),c.\u0275\u0275advance(1),c.\u0275\u0275property("actionMode",!0)("hidden",!A.product)}}function T(C,N){if(1&C&&(c.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),c.\u0275\u0275text(3,"Vence"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(4,"span",19),c.\u0275\u0275text(5),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(6,"div",29)(7,"label",20),c.\u0275\u0275text(8,"CVC"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(9,"span",19),c.\u0275\u0275text(10),c.\u0275\u0275elementEnd()()()),2&C){const A=c.\u0275\u0275nextContext();c.\u0275\u0275advance(5),c.\u0275\u0275textInterpolate1(" ",A.digitalIncognito?"\u2022\u2022 | \u2022\u2022":A.digitalExpAt," "),c.\u0275\u0275advance(5),c.\u0275\u0275textInterpolate1(" ",A.digitalIncognito?"\u2022\u2022\u2022":A.digitalCVC," ")}}let S=(()=>{class C{constructor(A,R){this.managerPreferences=A,this.digitalService=R,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new c.EventEmitter,this.digital=new c.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:A})=>{this.incognito=A})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:A,value:R})=>{this.product&&this.product.id===A&&this.refreshDigitalState(R)}))}ngOnChanges(A){const{product:R}=A;if(R&&R.currentValue){const W=R.currentValue;this.refreshDigitalState(this.digitalService.request(W.id)),this.activateDigitalCountdown(W)}}ngOnDestroy(){this.unsubscriptions.forEach(A=>A())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(A){const{incognito:R,requiredRequest:W,cvc:Z,expirationAt:E,number:O}=A;this.digitalIncognito=R,this.digitalExpAt=E,this.digitalCVC=Z,this.digitalNumber=O,this.digitalIcon=R?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=W}activateDigitalCountdown(A){const{countdown$:R}=this.digitalService.request(A.id);R?(this.progressBarRequired=!0,this.progressBarPercent=100,R.subscribe(W=>{this.progressBarRequired=!(W>=m.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-W/m.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return C.\u0275fac=function(A){return new(A||C)(c.\u0275\u0275directiveInject(s.Bx),c.\u0275\u0275directiveInject(f.ZP))},C.\u0275cmp=c.\u0275\u0275defineComponent({type:C,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[c.\u0275\u0275NgOnChangesFeature,c.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(A,R){1&A&&(c.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),c.\u0275\u0275listener("click",function(){return R.onClose()}),c.\u0275\u0275elementStart(3,"span"),c.\u0275\u0275text(4,"Atr\xe1s"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275element(5,"mbo-currency-toggle",3),c.\u0275\u0275template(6,x,3,2,"button",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(7,"div",5)(8,"div",6),c.\u0275\u0275element(9,"img",7),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),c.\u0275\u0275text(12),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),c.\u0275\u0275template(15,h,2,1,"span",12),c.\u0275\u0275template(16,p,3,4,"span",13),c.\u0275\u0275template(17,d,2,2,"bocc-badge",14),c.\u0275\u0275elementEnd()(),c.\u0275\u0275template(18,_,2,2,"div",15),c.\u0275\u0275elementEnd()(),c.\u0275\u0275template(19,v,7,6,"div",16),c.\u0275\u0275template(20,T,11,2,"div",17),c.\u0275\u0275elementEnd()),2&A&&(c.\u0275\u0275classMap(null==R.product?null:R.product.bank.className),c.\u0275\u0275property("color",null==R.product?null:R.product.color),c.\u0275\u0275advance(5),c.\u0275\u0275property("formControl",R.currencyControl)("currencies",R.currencies)("hidden",!(null!=R.product&&R.product.bank.isOccidente)||R.currencies.length<2),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==R.product?null:R.product.isDigital),c.\u0275\u0275advance(3),c.\u0275\u0275property("src",null==R.product?null:R.product.logo,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(2),c.\u0275\u0275property("active",!R.product),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",(null==R.product?null:R.product.nickname)||(null==R.product?null:R.product.name)," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!R.product),c.\u0275\u0275advance(1),c.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!R.product),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!(null!=R.product&&R.product.isDigital)),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==R.product?null:R.product.isDigital),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",(null==R.product||null==R.product.status?null:R.product.status.label)&&R.digitalIncognito),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",R.progressBarRequired),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!(null!=R.product&&R.product.isDigital)),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",null==R.product?null:R.product.isDigital))},dependencies:[i.CommonModule,i.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,a,o,r.uf,l.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),C})()},94614:(K,P,t)=>{t.d(P,{K:()=>s});var i=t(17007),e=t(30263),n=t(39904),a=(t(29306),t(95437)),u=t(99877);let s=(()=>{class b{constructor(r){this.mboProvider=r,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:r,id:l,parentProduct:x}=this.product;"covered"===r&&x?this.goToPage(x.id,l):this.goToPage(l)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(r,l){this.mboProvider.navigation.next(n.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:r,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:l})}}return b.\u0275fac=function(r){return new(r||b)(u.\u0275\u0275directiveInject(a.ZL))},b.\u0275cmp=u.\u0275\u0275defineComponent({type:b,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[u.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(r,l){1&r&&(u.\u0275\u0275elementStart(0,"div",0),u.\u0275\u0275listener("click",function(){return l.onComponent()}),u.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),u.\u0275\u0275text(3),u.\u0275\u0275elementEnd(),u.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),u.\u0275\u0275text(5),u.\u0275\u0275elementEnd()(),u.\u0275\u0275elementStart(6,"div",4),u.\u0275\u0275element(7,"bocc-amount",5),u.\u0275\u0275elementEnd()()),2&r&&(u.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",l.skeleton),u.\u0275\u0275advance(2),u.\u0275\u0275property("active",l.skeleton),u.\u0275\u0275advance(1),u.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.dateFormat," "),u.\u0275\u0275advance(1),u.\u0275\u0275property("active",l.skeleton),u.\u0275\u0275advance(1),u.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.description," "),u.\u0275\u0275advance(1),u.\u0275\u0275property("hidden",l.skeleton),u.\u0275\u0275advance(1),u.\u0275\u0275property("amount",null==l.movement?null:l.movement.value)("currencyCode",null==l.movement?null:l.movement.currencyCode)("theme",!0))},dependencies:[i.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),b})()},70957:(K,P,t)=>{t.d(P,{K:()=>_});var i=t(15861),c=t(17007),n=t(99877),a=t(30263),u=t(78506),m=t(39904),b=(t(29306),t(87903)),f=t(95437),r=t(27302),l=t(94614);function x(v,T){if(1&v){const S=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",8),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(S);const N=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(N.onRedirectAll())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Ver todos"),n.\u0275\u0275elementEnd()()}}function h(v,T){if(1&v&&(n.\u0275\u0275elementStart(0,"div",5)(1,"label",6),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(3,x,3,0,"button",7),n.\u0275\u0275elementEnd()),2&v){const S=n.\u0275\u0275nextContext();n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",(null==S.productMovements||null==S.productMovements.range?null:S.productMovements.range.label)||"Sin resultados"," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==S.productMovements?null:S.productMovements.range)}}function p(v,T){if(1&v&&n.\u0275\u0275element(0,"mbo-product-info-movement",9),2&v){const S=T.$implicit,C=n.\u0275\u0275nextContext();n.\u0275\u0275property("movement",S)("product",C.product)}}function d(v,T){if(1&v&&(n.\u0275\u0275elementStart(0,"mbo-message-empty",10),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&v){const S=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",S.msgError," ")}}let _=(()=>{class v{constructor(S,C){this.mboProvider=S,this.managerProductMovements=C,this.header=!0,this.requesting=!1}ngOnChanges(S){const{currencyCode:C,product:N}=S;if(!this.movements&&(N||C)){const A=C?.currentValue||this.currencyCode,R=N?.currentValue||this.product;this.currentMovements=void 0,(0,b.A2)(R)&&this.requestFirstPage(R,A)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:S,id:C,parentProduct:N}=this.product;this.mboProvider.navigation.next(m.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===S?{productId:N?.id,coveredCardId:C}:{productId:C},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(S,C){var N=this;return(0,i.Z)(function*(){N.requesting=!0,(yield N.managerProductMovements.requestForProduct({product:S,currencyCode:C})).when({success:A=>{N.currentMovements=A},failure:()=>{N.currentMovements=void 0}},()=>{N.requesting=!1})})()}}return v.\u0275fac=function(S){return new(S||v)(n.\u0275\u0275directiveInject(f.ZL),n.\u0275\u0275directiveInject(u.sy))},v.\u0275cmp=n.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[n.\u0275\u0275NgOnChangesFeature,n.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(S,C){1&S&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,h,4,2,"div",1),n.\u0275\u0275elementStart(2,"div",2),n.\u0275\u0275template(3,p,1,2,"mbo-product-info-movement",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(4,d,2,1,"mbo-message-empty",4),n.\u0275\u0275elementEnd()),2&S&&(n.\u0275\u0275property("hidden",C.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",C.header),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",null==C.productMovements?null:C.productMovements.firstPage),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==C.productMovements?null:C.productMovements.isEmpty))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,a.P8,l.K,r.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),v})()},91248:(K,P,t)=>{t.d(P,{I:()=>b});var i=t(17007),e=t(30263),n=t(99877);function a(f,r){if(1&f&&n.\u0275\u0275element(0,"bocc-amount",10),2&f){const l=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("amount",l.value)("currencyCode",l.currencyCode)}}function u(f,r){if(1&f&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&f){const l=n.\u0275\u0275nextContext().$implicit,x=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",x.incognito||x.section.incognito?l.mask:l.value," ")}}function m(f,r){if(1&f){const l=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",11),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(l);const h=n.\u0275\u0275nextContext().$implicit;return n.\u0275\u0275resetView(h.action.click())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()}if(2&f){const l=n.\u0275\u0275nextContext().$implicit;n.\u0275\u0275property("suffixIcon",l.action.icon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(l.action.label)}}function s(f,r){if(1&f&&(n.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"label",6),n.\u0275\u0275template(5,a,1,2,"bocc-amount",7),n.\u0275\u0275template(6,u,2,1,"span",8),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(7,m,3,2,"button",9),n.\u0275\u0275elementEnd()),2&f){const l=r.$implicit,x=n.\u0275\u0275nextContext();n.\u0275\u0275property("hidden",(null==l?null:l.currencyCode)!==x.currencyCode),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",l.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",l.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!l.money),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.action&&!(x.incognito||x.section.incognito))}}let b=(()=>{class f{constructor(){this.currencyCode="COP"}}return f.\u0275fac=function(l){return new(l||f)},f.\u0275cmp=n.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(l,x){1&l&&(n.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),n.\u0275\u0275template(2,s,8,5,"li",2),n.\u0275\u0275elementEnd()()),2&l&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",x.section.datas))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),f})()},4663:(K,P,t)=>{t.d(P,{c:()=>r});var i=t(17007),e=t(99877),o=t(30263),a=t(27302);function u(l,x){1&l&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function m(l,x){if(1&l&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&l){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",h.title," ")}}function s(l,x){if(1&l){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const _=e.\u0275\u0275restoreView(h).$implicit,v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onProduct(_))}),e.\u0275\u0275elementEnd()}if(2&l){const h=x.$implicit,p=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",h.color)("icon",h.logo)("title",h.nickname)("number",h.publicNumber)("ghost",p.ghost)("amount",h.amount)("tagAval",h.tagAvalFormat),e.\u0275\u0275attribute("amount-status",p.amountColorProduct(h))}}function b(l,x){1&l&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const f=["*"];let r=(()=>{class l{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(h){return h.amount>0?"success":h.amount<0?"danger":"empty"}onProduct(h){this.select.emit(h)}}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(h,p){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,u,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,m,2,1,"div",5),e.\u0275\u0275template(8,s,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,b,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",p.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",p.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",p.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!p.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!p.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,o.w_,o.P8,a.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),l})()},13961:(K,P,t)=>{t.d(P,{Z:()=>u});var i=t(17007),e=t(27302),n=t(10119),o=t(99877);let u=(()=>{class m{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(b){this.portal=b}}return m.\u0275fac=function(b){return new(b||m)},m.\u0275cmp=o.\u0275\u0275defineComponent({type:m,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(b,f){1&b&&(o.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),o.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"p"),o.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),o.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"p"),o.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),o.\u0275\u0275elementEnd()()()),2&b&&o.\u0275\u0275property("headerActionRight",f.headerAction)("gradient",!0)},dependencies:[i.CommonModule,e.Nu,n.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),m})()},66709:(K,P,t)=>{t.d(P,{s:()=>u});var i=t(17007),c=t(99877),e=t(30263),n=t(87542);let o=(()=>{class m{ngBoccPortal(b){}}return m.\u0275fac=function(b){return new(b||m)},m.\u0275cmp=c.\u0275\u0275defineComponent({type:m,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(b,f){1&b&&(c.\u0275\u0275elementStart(0,"div",0)(1,"label",1),c.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),c.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),c.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(11,"p",4),c.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),c.\u0275\u0275element(13,"br"),c.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),c.\u0275\u0275element(15,"br"),c.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),c.\u0275\u0275elementStart(17,"span"),c.\u0275\u0275text(18,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(19," Configuraci\xf3n "),c.\u0275\u0275elementStart(20,"span"),c.\u0275\u0275text(21,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(22," Seguridad "),c.\u0275\u0275elementStart(23,"span"),c.\u0275\u0275text(24,">"),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(25," Activar Token Mobile."),c.\u0275\u0275element(26,"br"),c.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),c.\u0275\u0275elementEnd()()()())},dependencies:[i.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),m})();const a=["*"];let u=(()=>{class m{constructor(b,f){this.ref=b,this.bottomSheetService=f,this.verifying=!1,this.tokenLength=n.Xi,this.code=new c.EventEmitter,this.tokenControls=new n.b2}ngOnInit(){const b=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(o),setTimeout(()=>{b?.focus()},120)}onAutocomplete(b){this.code.emit(b)}onInfo(){this.infoSheet?.open()}}return m.\u0275fac=function(b){return new(b||m)(c.\u0275\u0275directiveInject(c.ElementRef),c.\u0275\u0275directiveInject(e.fG))},m.\u0275cmp=c.\u0275\u0275defineComponent({type:m,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(b,f){1&b&&(c.\u0275\u0275projectionDef(),c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275projection(2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"p",2),c.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"p",2),c.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),c.\u0275\u0275elementStart(7,"a"),c.\u0275\u0275text(8),c.\u0275\u0275elementEnd(),c.\u0275\u0275text(9,". "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(10,"div",3),c.\u0275\u0275element(11,"img",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),c.\u0275\u0275listener("autocomplete",function(l){return f.onAutocomplete(l)}),c.\u0275\u0275text(13," Ingresa tu clave "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(14,"button",6),c.\u0275\u0275listener("click",function(){return f.onInfo()}),c.\u0275\u0275elementStart(15,"span"),c.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),c.\u0275\u0275elementEnd()()()),2&b&&(c.\u0275\u0275advance(8),c.\u0275\u0275textInterpolate1("",f.tokenLength," d\xedgitos"),c.\u0275\u0275advance(4),c.\u0275\u0275property("disabled",f.verifying)("formControls",f.tokenControls),c.\u0275\u0275advance(2),c.\u0275\u0275property("disabled",f.verifying))},dependencies:[i.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),m})()},88844:(K,P,t)=>{t.d(P,{YI:()=>m,tc:()=>p,iR:()=>h,jq:()=>b,Hv:()=>u,S6:()=>f,E2:()=>l,V4:()=>x,wp:()=>j,CE:()=>S,YQ:()=>e,ND:()=>o,t1:()=>_});var i=t(6472);class c{constructor(I){this.value=I}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:I}){return this.value.id===I}filtrable(I){return(0,i.hasPattern)(this.value.name,I)}}function e(U){return U.map(I=>new c(I))}class n{constructor(I){this.currency=I}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(I){return this.currency.code===I?.code}filtrable(I){return!0}}function o(U){return U.map(I=>new n(I))}var a=t(39904);class u{constructor(I){this.value=I}get title(){return this.value.label}get description(){return this.value.label}compareTo(I){return this.value.reference===I.reference}filtrable(I){return!0}}const m=a.Bf.map(U=>new u(U));class s{constructor(I,k){this.value=I,this.title=this.value.label,this.description=k?this.value.code:this.value.label}compareTo(I){return this.value===I}filtrable(I){return!0}}const b=new s(a.Gd),f=new s(a.XU),r=new s(a.t$),l=new s(a.j1),x=new s(a.k7),h=[b,f,r,l],p=[new s(a.Gd,!0),new s(a.XU,!0),new s(a.t$,!0),new s(a.j1,!0)];class d{constructor(I){this.product=I}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(I){return this.product.id===I?.id}filtrable(I){return!0}}function _(U){return U.map(I=>new d(I))}var v=t(89148);class T{constructor(I){this.filter=I}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(I){return this.value===I}filtrable(I){return!0}}const S=new T({label:"Todos los productos",short:"Todos",value:v.Gt.None}),C=new T({label:"Cuentas de ahorro",short:"Ahorros",value:v.Gt.SavingAccount}),N=new T({label:"Cuentas corriente",short:"Corrientes",value:v.Gt.CheckingAccount}),A=new T({label:"Depositos electr\xf3nicos",short:"Depositos",value:v.Gt.ElectronicDeposit}),R=new T({label:"Cuentas AFC",short:"AFC",value:v.Gt.AfcAccount}),W=new T({label:"Tarjetas de cr\xe9dito",short:"TC",value:v.Gt.CreditCard}),Z=new T({label:"Inversiones",short:"Inversiones",value:v.Gt.CdtAccount}),E=new T({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:v.Gt.Loan}),O=new T({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:v.Gt.ResolvingCredit}),y=new T({label:"Productos Aval",short:"Aval",value:v.Gt.Aval}),M=new T({label:"Productos fiduciarios",short:"Fiducias",value:v.Gt.Trustfund}),B=new T({label:"Otros productos",short:"Otros",value:v.Gt.None}),j={SDA:C,DDA:N,EDA:A,AFC:R,CCA:W,CDA:Z,DLA:E,LOC:O,AVAL:y,80:M,MDA:B,NONE:B,SBA:B,VDA:B}},96977:(K,P,t)=>{t.d(P,{J:()=>m});var i=t(39904),c=t(95437),e=t(30263),n=t(9811),o=t(99877);const{LOGIN:u}=i.Z6.AUTHENTICATION;let m=(()=>{class s{constructor(f,r,l){this.modalConfirmationService=f,this.mboProvider=r,this.facialBiometricsInteractor=l}execute(){this.modalConfirmationService.execute({title:"CANCELAR REGISTRO",message:"\xbfDeseas cancelar el registro a Banca M\xf3vil?",accept:{label:"Continuar el registro"},decline:{label:"Salir",click:()=>{this.facialBiometricsInteractor.clearStoreState(),this.mboProvider.navigation.back(u)}}})}}return s.\u0275fac=function(f){return new(f||s)(o.\u0275\u0275inject(e.$e),o.\u0275\u0275inject(c.ZL),o.\u0275\u0275inject(n.U))},s.\u0275prov=o.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},85239:(K,P,t)=>{t.d(P,{g:()=>x});var i=t(99877),e=t(17007);const o=["slide"],a=["slideElement"];function u(h,p){1&h&&i.\u0275\u0275elementContainer(0)}function m(h,p){if(1&h&&i.\u0275\u0275template(0,u,1,0,"ng-container",5),2&h){i.\u0275\u0275nextContext();const d=i.\u0275\u0275reference(7);i.\u0275\u0275property("ngTemplateOutlet",d)}}function s(h,p){1&h&&i.\u0275\u0275elementContainer(0)}function b(h,p){if(1&h&&i.\u0275\u0275template(0,s,1,0,"ng-container",5),2&h){i.\u0275\u0275nextContext();const d=i.\u0275\u0275reference(7);i.\u0275\u0275property("ngTemplateOutlet",d)}}function f(h,p){if(1&h){const d=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"span",8),i.\u0275\u0275listener("click",function(){const T=i.\u0275\u0275restoreView(d).$implicit,S=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(S.goToSlide(T))}),i.\u0275\u0275elementEnd()}if(2&h){const d=p.$implicit,_=i.\u0275\u0275nextContext(2);i.\u0275\u0275classProp("active",d===_.slideIndex)}}function r(h,p){if(1&h&&(i.\u0275\u0275elementStart(0,"div",6),i.\u0275\u0275template(1,f,1,2,"span",7),i.\u0275\u0275elementEnd()),2&h){const d=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("ngForOf",d.dotIndexes)}}const l=["*"];let x=(()=>{class h{constructor(d){this.elementRef=d,this.slideChanged=new i.EventEmitter,this.showDotsTop=!0,this.autoSlide=!1,this.slideIndex=0,this.moveThreshold=50,this.autoSlideInterval=1e4,this.calculateSlideHeight(this.slideIndex)}ngAfterContentInit(){this.slides&&this.slides.length>0&&(this.initSlides(),setTimeout(()=>{this.calculateSlideHeight(this.slideIndex)},50)),this.autoSlide&&this.startAutoSlide()}ngOnDestroy(){this.stopAutoSlide()}initSlides(){if(this.slides&&this.slides.length>0){const d=this.slides.first;d&&d.nativeElement&&(this.slideWidth=d.nativeElement.clientWidth,this.dotIndexes=Array.from({length:this.slides.length},(_,v)=>v),this.calculateSlideHeight(this.slideIndex))}}prevSlide(){this.slideIndex=(this.slideIndex-1+this.dotIndexes.length)%this.dotIndexes.length,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}nextSlide(){this.slideIndex=(this.slideIndex+1)%this.dotIndexes.length,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}onTouchStart(d){this.startX=d.touches[0].clientX,this.startY=d.touches[0].clientY}onTouchMove(d){if(!this.startX||!this.startY)return;const _=d.touches[0].clientX-this.startX,v=d.touches[0].clientY-this.startY;Math.abs(_)>Math.abs(v)&&Math.abs(_)>this.moveThreshold&&(_>0?this.prevSlide():this.nextSlide(),this.startX=null,this.startY=null)}startAutoSlide(){this.autoSlideTimer_=setInterval(()=>{this.nextSlide()},this.autoSlideInterval)}stopAutoSlide(){clearInterval(this.autoSlideTimer_)}goToSlide(d){this.slideIndex=d,this.calculateSlideHeight(this.slideIndex),this.slideChanged.emit()}calculateSlideHeight(d){if(this.slides&&this.slides.length>0&&this.slides.length>d){const _=this.slides.toArray()[d].nativeElement.querySelector(".auto-height-box");if(_){const v=_.scrollHeight+"px",T=this.elementRef.nativeElement.querySelector(".slider-container"),S=this.elementRef.nativeElement.querySelector(".slides");T&&(T.style.height=v,S.style.height=v)}}}}return h.\u0275fac=function(d){return new(d||h)(i.\u0275\u0275directiveInject(i.ElementRef))},h.\u0275cmp=i.\u0275\u0275defineComponent({type:h,selectors:[["bocc-slider"]],contentQueries:function(d,_,v){if(1&d&&i.\u0275\u0275contentQuery(v,o,4),2&d){let T;i.\u0275\u0275queryRefresh(T=i.\u0275\u0275loadQuery())&&(_.slides=T)}},viewQuery:function(d,_){if(1&d&&i.\u0275\u0275viewQuery(a,5),2&d){let v;i.\u0275\u0275queryRefresh(v=i.\u0275\u0275loadQuery())&&(_.slideElement=v.first)}},inputs:{showDotsTop:"showDotsTop",autoSlide:"autoSlide"},outputs:{slideChanged:"slideChanged"},ngContentSelectors:l,decls:8,vars:4,consts:[[1,"bocc-slide"],[3,"ngIf"],[1,"slider-container"],[1,"slides",3,"touchstart","touchmove"],["dotsTemplate",""],[4,"ngTemplateOutlet"],[1,"dots"],["class","dot","tabindex","0",3,"active","click",4,"ngFor","ngForOf"],["tabindex","0",1,"dot",3,"click"]],template:function(d,_){1&d&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,m,1,1,"ng-template",1),i.\u0275\u0275elementStart(2,"div",2)(3,"div",3),i.\u0275\u0275listener("touchstart",function(T){return _.onTouchStart(T)})("touchmove",function(T){return _.onTouchMove(T)}),i.\u0275\u0275projection(4),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(5,b,1,1,"ng-template",1),i.\u0275\u0275template(6,r,2,1,"ng-template",null,4,i.\u0275\u0275templateRefExtractor),i.\u0275\u0275elementEnd()),2&d&&(i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",_.showDotsTop),i.\u0275\u0275advance(2),i.\u0275\u0275styleProp("transform","translateX("+-_.slideIndex*_.slideWidth+"px)"),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!_.showDotsTop))},dependencies:[e.NgForOf,e.NgIf,e.NgTemplateOutlet],styles:['.slider-container{position:relative;width:100%;overflow:hidden}.slides{display:flex;transition:transform .5s ease}.prev,.next{position:absolute;top:50%;transform:translateY(-50%);background-color:transparent;border:none;color:#fff;font-size:20px;cursor:pointer}.prev{left:10px}.next{right:10px}.dots{text-align:center;margin-top:5px}.dot{display:inline-block;width:4px;height:4px;margin:0 5px;position:relative;background-color:#0081ff;transform:rotate(45deg)}.dot.active{background-color:#0056cb;width:5px;height:5px}.dot.active:after{content:"";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);border:1px solid #0081FF;box-sizing:border-box;transform:rotate(90deg)}\n'],encapsulation:2}),h})()},9811:(K,P,t)=>{t.d(P,{U:()=>I});var i=t(15861),c=t(42168),e=t(30263),n=t(50203),o=t(99877),a=t(23730),u=t(13973);let m=(()=>{class k{constructor(g,D){this.facialStepBiometricsServices=g,this.facialBiometricsStore=D}document(g){try{return this.facialStepBiometricsServices.getParams({documentType:g.type,identificationNumber:g.number})}catch(D){throw console.error("Error en document():",D),D}}saveImages(){var g=this;return(0,i.Z)(function*(){const D=g.facialBiometricsStore.currentState;if(!D)throw new Error("FacialBiometricsStore.currentState es undefined");try{const L=g.makeRequestImages(D);return yield g.facialStepBiometricsServices.setDataCapture(L)}catch(L){throw console.error("Error en saveImages():",L),L}})()}startProcess(){var g=this;return(0,i.Z)(function*(){const D=g.facialBiometricsStore.currentState;if(!D)throw new Error("FacialBiometricsStore.currentState es undefined");try{const L=g.makeRequestStart(D);return yield g.facialStepBiometricsServices.startProcess(L)}catch(L){throw console.error("Error en startProcess():",L),L}})()}makeRequestImages(g){if(!(g.captureFace?.Image&&g.documentType&&g.documentNumber&&g.presignedUrls?.uploadLiveness))throw new Error("Datos insuficientes en currentState para makeRequestImages()");const D={livenes:g.captureFace.Image},L={uploadLiveness:g.presignedUrls.uploadLiveness};return g.captureFrontSide?.Image&&g.captureBackSide?.Image&&g.presignedUrls?.uploadDocumentFrontSide&&g.presignedUrls?.uploadDocumentBackSide&&(D.documentFrontSide=g.captureFrontSide.Image,D.documentBackSide=g.captureBackSide.Image,L.uploadDocumentFrontSide=g.presignedUrls.uploadDocumentFrontSide,L.uploadDocumentBackSide=g.presignedUrls.uploadDocumentBackSide),{documentType:g.documentType,identificationNumber:g.documentNumber,dataImages:D,presignedUrl:L}}makeRequestStart(g){if(!g.captureFace?.keyProcessLiveness||!g.uiDevice)throw new Error("Datos insuficientes en currentState para makeRequestStart()");return{documentType:g.documentType,identificationNumber:g.documentNumber,keyProcessLiveness:g.captureFace.keyProcessLiveness,sessionId:g.uiDevice}}}return k.\u0275fac=function(g){return new(g||k)(o.\u0275\u0275inject(a.z),o.\u0275\u0275inject(u.H))},k.\u0275prov=o.\u0275\u0275defineInjectable({token:k,factory:k.\u0275fac,providedIn:"root"}),k})();var s=t(7324),b=t(85239),f=t(96977),r=t(17007),l=t(45542),x=t(2460);const h=["foo"];function p(k,w){if(1&k){const g=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",23),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(g);const L=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(L.nextSlide())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Siguiente"),o.\u0275\u0275elementEnd()()}}function d(k,w){if(1&k){const g=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",23),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(g);const L=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(L.onNext())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Continuar"),o.\u0275\u0275elementEnd()()}}let _=(()=>{class k{constructor(g,D,L,H){this.elementRef=g,this.modalCancel=D,this.facialBiometricsInteractor=L,this.facialBiometricsStore=H,this.slideIndex=0,this.showButton=!0}ngBoccPortal(g){this.portal=g}ngOnInit(){this.scrollToBottom()}onNext(){var g=this;return(0,i.Z)(function*(){g.facialBiometricsStore.setStep(s.$j.CAPTURE_ID_FRONT),g.facialBiometricsInteractor.handlerCapture(),g.portal.close()})()}onCancel(){this.modalCancel.execute(),this.portal.close()}nextSlide(){this.boccSlider.nextSlide(),this.showButton=!1}scrollToBottom(){const g=this.elementRef.nativeElement.querySelector(".foo");g&&setTimeout(()=>{g.scrollIntoView({behavior:"smooth",block:"end"})},500)}}return k.\u0275fac=function(g){return new(g||k)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(f.J),o.\u0275\u0275directiveInject(I),o.\u0275\u0275directiveInject(u.H))},k.\u0275cmp=o.\u0275\u0275defineComponent({type:k,selectors:[["mbo-guide-line-document-sheet"]],viewQuery:function(g,D){if(1&g&&(o.\u0275\u0275viewQuery(b.g,5),o.\u0275\u0275viewQuery(h,5)),2&g){let L;o.\u0275\u0275queryRefresh(L=o.\u0275\u0275loadQuery())&&(D.boccSlider=L.first),o.\u0275\u0275queryRefresh(L=o.\u0275\u0275loadQuery())&&(D.boxTabElement=L.first)}},decls:52,vars:2,consts:[[1,"mbo-guide-line-document-sheet"],["src","assets/shared/backgrounds/image_id.jpg","alt","img-guide-line_bkg",1,"mbo-guide-line-document-sheet--img"],[1,"mbo-guide-line-document-sheet__box-tab"],[3,"slideChanged"],[1,"slide"],["slide",""],[1,"auto-height-box"],[1,"mbo-guide-line-document-sheet__box-tab__header"],[1,"mbo-guide-line-document-sheet__box-tab__title","smalltext-semibold"],[1,"mbo-guide-line-document-sheet__box-tab__body"],[1,"mbo-guide-line-document-sheet__box-tab__message","body2-default"],[1,"body2-semibold"],[1,"mbo-guide-line-document-sheet__box-tab__box-info"],[1,"mbo-guide-line-document-sheet__box-tab__box-info--icon"],["icon","lock-circular",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-document-sheet__box-tab__info","body2-default"],["icon","product-certification",2,"color","var(--color-blue-700)"],["icon","eye-visible",2,"color","var(--color-blue-700)"],["icon","light-bulb-2",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-document-sheet__box-tab__footer"],["id","btn_login-customer_close","bocc-button","flat",3,"click"],["id","btn_login-customer_remove","bocc-button","raised",3,"click",4,"ngIf"],[1,"mbo-guide-line-document-sheet__box-tab__bottomTab","foo"],["id","btn_login-customer_remove","bocc-button","raised",3,"click"]],template:function(g,D){1&g&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"img",1),o.\u0275\u0275elementStart(2,"div",2)(3,"bocc-slider",3),o.\u0275\u0275listener("slideChanged",function(){return D.scrollToBottom()}),o.\u0275\u0275elementStart(4,"div",4,5)(6,"div",6)(7,"div",7)(8,"div",8),o.\u0275\u0275text(9," REGISTRO A BANCA M\xd3VIL "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(10,"div",9)(11,"p",10),o.\u0275\u0275text(12," Primero, toma una fotograf\xeda de tu "),o.\u0275\u0275elementStart(13,"span",11),o.\u0275\u0275text(14,"c\xe9dula de ciudadan\xeda"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(15," por ambos lados. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(16,"div",12)(17,"div",13),o.\u0275\u0275element(18,"bocc-icon",14),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(19,"p",15),o.\u0275\u0275text(20," Tu informaci\xf3n estar\xe1 segura y solo se utilizar\xe1 para verificar tu identidad. "),o.\u0275\u0275elementEnd()()()()(),o.\u0275\u0275elementStart(21,"div",4,5)(23,"div",6)(24,"div",7)(25,"div",8),o.\u0275\u0275text(26," IMPORTANTE "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(27,"div",9)(28,"p",10),o.\u0275\u0275text(29," Antes de tomar la foto ten encuenta "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(30,"div",12)(31,"div",13),o.\u0275\u0275element(32,"bocc-icon",16),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(33,"p",15),o.\u0275\u0275text(34," Tu c\xe9dula debe estar centrada con la plantilla que aparecer\xe1 para tomar la foto. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(35,"div",12)(36,"div",13),o.\u0275\u0275element(37,"bocc-icon",17),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(38,"p",15),o.\u0275\u0275text(39," Verifica que tu foto sea clara y los textos legibles. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(40,"div",12)(41,"div",13),o.\u0275\u0275element(42,"bocc-icon",18),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(43,"p",15),o.\u0275\u0275text(44," La foto no debe tener reflejos o sombras que impidan la claridad del documento. "),o.\u0275\u0275elementEnd()()()()()(),o.\u0275\u0275elementStart(45,"div",19)(46,"button",20),o.\u0275\u0275listener("click",function(){return D.onCancel()}),o.\u0275\u0275elementStart(47,"span"),o.\u0275\u0275text(48,"Cancelar"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(49,p,3,0,"button",21),o.\u0275\u0275template(50,d,3,0,"button",21),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(51,"div",22),o.\u0275\u0275elementEnd()()),2&g&&(o.\u0275\u0275advance(49),o.\u0275\u0275property("ngIf",D.showButton),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!D.showButton))},dependencies:[r.NgIf,l.P,x.Z,b.g],styles:["mbo-guide-line-document-sheet{position:relative;width:100%;display:block}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet{position:relative;display:flex;width:100%;flex-direction:column;box-sizing:border-box}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet--img{background-size:cover;background-repeat:no-repeat;background-position:center;background-attachment:fixed;display:block;width:100%}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab{background-color:var(--color-carbon-lighter-200);transform:translateY(-12px);border-top-left-radius:15px;border-top-right-radius:15px;padding-top:var(--sizing-x8);text-align:center}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-info{display:flex}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-info--icon{position:relative;background:var(--color-blue-200);border-radius:var(--sizing-x4);margin:auto 0rem;padding:var(--sizing-x3)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__title{font-size:12px;text-align:center;color:var(--color-carbon-darker-1000);padding-top:36px}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__message{text-align:center;color:var(--color-carbon-lighter-700);padding:var(--sizing-x8) 0}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__info{text-align:left;color:var(--color-carbon-lighter-700);padding:var(--sizing-x4) 0px var(--sizing-x4) var(--sizing-x4)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer{display:flex;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x8) 0 var(--sizing-x8)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button{width:100%}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button:nth-child(1){margin-right:var(--sizing-x2)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__footer button:nth-child(2){margin-left:var(--sizing-x2)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__box-slide{padding:var(--sizing-x8)}mbo-guide-line-document-sheet .mbo-guide-line-document-sheet__box-tab__bottomTab{width:100%;height:var(--sizing-x6);background-color:var(--color-carbon-lighter-200);position:absolute}.slide{flex:0 0 100%;padding:0}.auto-height-box{height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;padding:0 var(--sizing-x7)}\n"],encapsulation:2}),k})();const v=["foo"];function T(k,w){if(1&k){const g=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",23),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(g);const L=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(L.nextSlide())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Siguiente"),o.\u0275\u0275elementEnd()()}}function S(k,w){if(1&k){const g=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",23),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(g);const L=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(L.onNext())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Continuar"),o.\u0275\u0275elementEnd()()}}let C=(()=>{class k{constructor(g,D,L,H){this.elementRef=g,this.modalCancel=D,this.facialBiometricsInteractor=L,this.facialBiometricsStore=H,this.showButton=!0}ngBoccPortal(g){this.portal=g}ngOnInit(){}nextSlide(){this.boccSlider.nextSlide(),this.showButton=!1}onNext(){this.facialBiometricsStore.setStep(s.$j.CAPTURE_FACE),this.facialBiometricsInteractor.handlerCapture(),this.portal.close()}onCancel(){this.modalCancel.execute(),this.portal.close()}scrollToBottom(){const g=this.elementRef.nativeElement.querySelector(".foo");g&&setTimeout(()=>{g.scrollIntoView({behavior:"smooth",block:"end"})},500)}}return k.\u0275fac=function(g){return new(g||k)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(f.J),o.\u0275\u0275directiveInject(I),o.\u0275\u0275directiveInject(u.H))},k.\u0275cmp=o.\u0275\u0275defineComponent({type:k,selectors:[["mbo-guide-line-face-sheet"]],viewQuery:function(g,D){if(1&g&&(o.\u0275\u0275viewQuery(b.g,5),o.\u0275\u0275viewQuery(v,5)),2&g){let L;o.\u0275\u0275queryRefresh(L=o.\u0275\u0275loadQuery())&&(D.boccSlider=L.first),o.\u0275\u0275queryRefresh(L=o.\u0275\u0275loadQuery())&&(D.boxTabElement=L.first)}},decls:44,vars:2,consts:[[1,"mbo-guide-line-face-sheet"],["src","assets/shared/backgrounds/face-capture.png","alt","img-guide-line_bkg",1,"mbo-guide-line-face-sheet--img"],[1,"mbo-guide-line-face-sheet__box-tab"],[3,"slideChanged"],[1,"slide"],["slide",""],[1,"auto-height-box"],[1,"mbo-guide-line-face-sheet__box-tab__header"],[1,"mbo-guide-line-face-sheet__box-tab__title","smalltext-semibold"],[1,"mbo-guide-line-face-sheet__box-tab__body"],[1,"mbo-guide-line-face-sheet__box-tab__message","body2-medium"],[1,"mbo-guide-line-face-sheet__box-tab__title_","smalltext-semibold"],[1,"mbo-guide-line-face-sheet__box-tab__message_","body2-default"],[1,"mbo-guide-line-face-sheet__box-tab__box-info"],[1,"mbo-guide-line-face-sheet__box-tab__box-info--icon"],["icon","face-id",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-face-sheet__box-tab__info","body2-default"],["icon","light-bulb-2",2,"color","var(--color-blue-700)"],["icon","glasses",2,"color","var(--color-blue-700)"],[1,"mbo-guide-line-face-sheet__box-tab__footer"],["id","btn_login-customer_close","bocc-button","flat",3,"click"],["id","btn_login-customer_remove","bocc-button","raised",3,"click",4,"ngIf"],[1,"mbo-guide-line-face-sheet__box-tab__bottomTab","foo"],["id","btn_login-customer_remove","bocc-button","raised",3,"click"]],template:function(g,D){1&g&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"img",1),o.\u0275\u0275elementStart(2,"div",2)(3,"bocc-slider",3),o.\u0275\u0275listener("slideChanged",function(){return D.scrollToBottom()}),o.\u0275\u0275elementStart(4,"div",4,5)(6,"div",6)(7,"div",7)(8,"div",8),o.\u0275\u0275text(9," REGISTRO A BANCA M\xd3VIL "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(10,"div",9)(11,"p",10),o.\u0275\u0275text(12," Ahora, t\xf3mate una foto "),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(13,"div",4,5)(15,"div",6)(16,"div",7)(17,"div",11),o.\u0275\u0275text(18," TEN ENCUENTA "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(19,"div",9)(20,"p",12),o.\u0275\u0275text(21," Antes de tomar la foto ten en cuenta "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(22,"div",13)(23,"div",14),o.\u0275\u0275element(24,"bocc-icon",15),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(25,"p",16),o.\u0275\u0275text(26," Mant\xe9n tu rostro alineado con la c\xe1mara "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(27,"div",13)(28,"div",14),o.\u0275\u0275element(29,"bocc-icon",17),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(30,"p",16),o.\u0275\u0275text(31," Tu rostro debe estar bien iluminado, evita que la luz venga detr\xe1s de ti "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(32,"div",13)(33,"div",14),o.\u0275\u0275element(34,"bocc-icon",18),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(35,"p",16),o.\u0275\u0275text(36," Recuerda no usar accesorios como gafas, tapabocas, gorros o aud\xedfonos "),o.\u0275\u0275elementEnd()()()()()(),o.\u0275\u0275elementStart(37,"div",19)(38,"button",20),o.\u0275\u0275listener("click",function(){return D.onCancel()}),o.\u0275\u0275elementStart(39,"span"),o.\u0275\u0275text(40,"Cancelar"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(41,T,3,0,"button",21),o.\u0275\u0275template(42,S,3,0,"button",21),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(43,"div",22),o.\u0275\u0275elementEnd()()),2&g&&(o.\u0275\u0275advance(41),o.\u0275\u0275property("ngIf",D.showButton),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!D.showButton))},dependencies:[r.NgIf,l.P,x.Z,b.g],styles:["mbo-guide-line-face-sheet{width:100%;position:relative;display:block}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet{width:100%;position:relative;display:flex;flex-direction:column;box-sizing:border-box}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet--img{width:100%;display:block;background-size:cover;background-repeat:no-repeat;background-position:center;background-attachment:fixed}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab{background-color:var(--color-carbon-lighter-200);transform:translateY(-12px);border-top-left-radius:15px;border-top-right-radius:15px;padding-top:var(--sizing-x8);text-align:center}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-info{display:flex}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-info--icon{position:relative;background:var(--color-blue-200);border-radius:var(--sizing-x4);margin:auto 0rem;padding:var(--sizing-x3)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__title{text-align:center;color:var(--color-carbon-darker-1000);padding-top:var(--sizing-x24)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__title_{text-align:center;color:var(--color-carbon-darker-1000);padding-top:var(--sizing-x16)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__message{text-align:center;color:var(--color-carbon-lighter-700);padding-top:var(--sizing-x8);padding-bottom:var(--sizing-x16);padding-left:0;padding-right:0}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__message_{text-align:center;color:var(--color-carbon-lighter-700);padding:var(--sizing-x8) 0}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__info{text-align:left;color:var(--color-carbon-lighter-700);padding:var(--sizing-x4) 0px var(--sizing-x4) var(--sizing-x4)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer{display:flex;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x8) 0 var(--sizing-x8)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button{width:100%}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button:nth-child(1){margin-right:var(--sizing-x2)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__footer button:nth-child(2){margin-left:var(--sizing-x2)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__box-slide{padding:var(--sizing-x8)}mbo-guide-line-face-sheet .mbo-guide-line-face-sheet__box-tab__bottomTab{width:100%;height:var(--sizing-x6);background-color:var(--color-carbon-lighter-200);position:absolute}.slide{flex:0 0 100%;padding:0;margin:auto}.auto-height-box{height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;padding:0 var(--sizing-x7)}\n"],encapsulation:2}),k})();var N=t(95437),A=t(39904),R=t(24613),W=t(96764),Z=t(63805);const{LOGIN:E,ENROLLMENT:O,FORGOT_PASSWORD:y}=A.Z6.AUTHENTICATION,{CAPTURE_ID_FRONT:M,CAPTURE_FACE:B,CAPTURE_ID_BACK:j,INIT:U}=s.$j;let I=(()=>{class k{constructor(g,D,L,H,Y,X){this.amplifyFacialBiometricsService=g,this.facialBiometricsRepository=D,this.facialBiometricsStore=L,this.mboProvider=H,this.sheetService=Y,this.verifyForgotPasswordStep=X,this.progressSource=new c.BehaviorSubject(0),this.portal=null,this.stepMappings={[M]:{method:"setCaptureFrontSide",route:O.FACIAL_BIOMETRICS.SUMMARY_ID_FRONT},[j]:{method:"setCaptureBackSide",route:O.FACIAL_BIOMETRICS.SUMMARY_ID_BACK},[B]:{method:"setCaptureFace",route:O.FACIAL_BIOMETRICS.SUMMARY_FACE}},this.captureUpdated=new o.EventEmitter,this.triggerCapture=new o.EventEmitter,this.isIndeterminate=new o.EventEmitter,this.content=null,this.progress$=this.progressSource.asObservable(),this.liveness=4,this.cardCapture=3,this.progressDuration=240,this.isResultReceived=!1}ngOnDestroy(){this.unsubscribeFromMessages(),this.stopProgressBar()}onSubmit(){var g=this;return(0,i.Z)(function*(){g.mboProvider.loader.open("Por favor espere...");try{if(!(yield g.tryWithRetries((0,i.Z)(function*(){return!0===(yield g.facialBiometricsRepository.saveImages())?.success}))))return void g.redirectProcess(!1);if(!(yield g.tryWithRetries((0,i.Z)(function*(){return!0===(yield g.facialBiometricsRepository.startProcess())?.success}))))return void g.redirectProcess(!1);g.mboProvider.navigation.next(O.FACIAL_BIOMETRICS.CONFIRMATION),g.openSubscription()}catch(D){console.error(D),g.redirectProcess(!1)}finally{g.mboProvider.loader.close()}})()}tryWithRetries(g){return(0,i.Z)(function*(){const D=L=>new Promise(H=>setTimeout(H,L));for(let L=1;L<=2;L++){try{if(yield g())return!0}catch(H){console.error(H)}L<2&&(yield D(1e3))}return!1})()}openSubscription(){this.unsubscribeFromMessages(),this.startProgressBar(),this.amplifySubscription=this.amplifyFacialBiometricsService.publicResult().subscribe({next:g=>{try{const D=g?.data?.onBiometricsResult?.data;if(!D)return void this.handleError();const H=JSON.parse(D)?.data;H&&H.message&&(this.isResultReceived=!0,H.message===s.x5?this.handleUnsatisfactoryResult(H):H.message===s.gi&&this.handleSatisfactoryResult())}catch{this.handleError()}finally{this.stopProgressBar()}},error:g=>{console.error("Error en la suscripci\xf3n:",g),this.isResultReceived=!0,this.handleError()}})}handleUnsatisfactoryResult(g){this.mboProvider.toast.warning(g.detail,"Registro biom\xe9trico no exitoso"),g.rejectionCode&&s.u.includes(g.rejectionCode)||g.messageCode&&s.u.includes(g.messageCode)?this.cancelProcess():this.redirectProcess(!1)}handleSatisfactoryResult(){this.mboProvider.toast.success("Lo usaremos para validar tu identidad en transacciones m\xf3viles.","Registro biom\xe9trico exitoso"),this.redirectProcess(!0)}handleError(){this.mboProvider.toast.warning("Lo sentimos, ocurri\xf3 un error. Por favor, intenta nuevamente.","No pudimos verificar la imagen"),this.redirectProcess(!1),this.unsubscribeFromMessages(),this.stopProgressBar()}startProgressBar(){this.progressSource.next(0);let g=0;const D=100/this.progressDuration;this.progressInterval=setInterval(()=>{g<100?(g+=D,this.progressSource.next(Math.min(g,100))):clearInterval(this.progressInterval)},1e3),this.progressTimeout=setTimeout(()=>{this.onProgressCompleteUntilResponse()},1e3*this.progressDuration)}stopProgressBar(){this.progressSource.next(0),this.progressInterval&&clearInterval(this.progressInterval),this.progressTimeout&&clearTimeout(this.progressTimeout)}onProgressCompleteUntilResponse(){this.isResultReceived||(this.stopProgressBar(),this.unsubscribeFromMessages(),this.redirectProcess(!1))}redirectProcess(g){var D=this;return(0,i.Z)(function*(){try{const L=D.facialBiometricsStore.getProcessExecutor().toString();if("forgotPassword"===(D.isIndeterminate.emit(!0),L)){if(g)return D.mboProvider.navigation.next(y.PASSWORD_ASSIGNMENT);try{const H=yield D.verifyForgotPasswordStep.rescue();return D.mboProvider.navigation.next(H)}catch(H){return D.mboProvider.navigation.next(H)}}return D.mboProvider.navigation.next(E)}finally{D.isIndeterminate.emit(!1),D.clearStoreState(),D.unsubscribeFromMessages()}})()}unsubscribeFromMessages(){this.amplifySubscription&&(this.amplifySubscription.unsubscribe(),this.amplifySubscription=void 0)}verifySTep(g){var D=this;return(0,i.Z)(function*(){try{const L=yield D.facialBiometricsRepository.document(g);if(L.success){const{type:H,number:Y}=g;return D.facialBiometricsStore.setDocument(H,Y),n.F.completed(L)}throw new Error}catch(L){return n.F.error(L)}})()}verifyCaptureProcess(g){return!!g?.presignedUrls&&Object.keys(g.presignedUrls).length>1}getSheetGuideLine(){const g=this.verifyCaptureProcess(this.content);return this.facialBiometricsStore.setStep(g?M:B),g?_:C}handlerSheet(g){this.portal&&(this.portal.destroy(),this.portal=null),this.portal=this.sheetService.create(void 0===g?C:this.getSheetGuideLine(),{containerProps:{condense:!0}}),this.portal.open()}saveDataParameters(g,D){this.facialBiometricsStore.setStep(U),this.content=g,this.getInfoDevice(),this.facialBiometricsStore.setParamsSDK(g.params,g.presignedUrls),this.facialBiometricsStore.setProcessExecutor(D)}evaluateStepDdl(g){return g===B?this.liveness:this.cardCapture}handlerCapture(){const{uiDevice:g,currentStep:D}=this.facialBiometricsStore.currentState,{access_token:L}=this.facialBiometricsStore.currentState.params.tokenAdo,{baseUrl:H,projectName:Y,apiKey:X,productId:te}=this.facialBiometricsStore.currentState.params,oe=this.evaluateStepDdl(D),ne=D===M;this.triggerCapture.emit(!0),capture(H,Y,X,te,oe,ne,g,L).then(J=>{if(D===B&&!1===J.IsAlive)throw new Error;this.captureUpdated.emit(J.Image);const q=this.stepMappings[D];if(q){const{method:re,route:ae}=q;this.facialBiometricsStore[re](J),this.mboProvider.navigation.next(ae)}this.triggerCapture.emit(!1)}).catch(()=>{this.mboProvider.toast.warning("Lo sentimos, por favor sigue los pasos para finalizar tu registro.","No pudimos verificar la imagen")}).finally(()=>{this.triggerCapture.emit(!1)})}getInfoDevice(){W.Device.getId().then(g=>{this.facialBiometricsStore.setUiDevice(g?.identifier)}).catch(g=>{console.error("Error al obtener el deviceId:",g)})}clearStoreState(){this.facialBiometricsStore.clearState()}cancelProcess(){this.unsubscribeFromMessages(),this.stopProgressBar(),this.clearStoreState(),this.mboProvider.navigation.next(E)}}return k.\u0275fac=function(g){return new(g||k)(o.\u0275\u0275inject(Z.K),o.\u0275\u0275inject(m),o.\u0275\u0275inject(u.H),o.\u0275\u0275inject(N.ZL),o.\u0275\u0275inject(e.fG),o.\u0275\u0275inject(R.n))},k.\u0275prov=o.\u0275\u0275defineInjectable({token:k,factory:k.\u0275fac,providedIn:"root"}),k})()},63805:(K,P,t)=>{t.d(P,{K:()=>b});var i=t(42168),e=t(65520),o=t(98778),u=t(13973),m=t(99877);let b=(()=>{class f{constructor(l){this.facialBiometricsStore=l,this.subscriptionInstance=null,this.api=new e.GraphQLAPIClass}publicResult(){const x={sessionId:this.facialBiometricsStore.currentState.uiDevice};return new i.Observable(p=>{this.subscriptionInstance=this.api.graphql(o.Amplify,{query:"\n      subscription onBiometricsResult($sessionId: ID!) {\n        onBiometricsResult(sessionId: $sessionId) {\n          sessionId\n          date\n          code\n          data\n        }\n      }\n    ",variables:x,authMode:"apiKey"});const d=this.subscriptionInstance.subscribe({next:_=>{p.next(_)},error:_=>{p.error(_)},complete:()=>{p.complete()}});return()=>{d&&d.unsubscribe()}})}}return f.\u0275fac=function(l){return new(l||f)(m.\u0275\u0275inject(u.H))},f.\u0275prov=m.\u0275\u0275defineInjectable({token:f,factory:f.\u0275fac,providedIn:"root"}),f})()},50203:(K,P,t)=>{t.d(P,{F:()=>e});var i=t(98699);class e extends i.PartialSealed{static error(o){return new e("error",o)}static completed(o){return new e("completed",o)}}},23730:(K,P,t)=>{t.d(P,{z:()=>m});var i=t(15861);class c{constructor(b,f,r,l){this.params=b,this.presignedUrls=f,this.success=r,this.error=l}get messages(){return{title:"Lo sentimos, por favor sigue los pasos para finalizar tu registro.",description:"No pudimos verificar la imagen"}}static error(){return new c(void 0,null,!1,!0)}}function e(s,b=!1){return new c(s.params,s.presignedUrls,!!s.params,b)}var n=t(39904),o=t(42168),a=t(71776),u=t(99877);let m=(()=>{class s{constructor(f){this.http=f}getParams(f){return(0,o.firstValueFrom)(this.http.post(n.bV.FACIAL_BIOMETRICS.GET_PARAMS,{...f}).pipe((0,o.map)(r=>e(r)))).catch(({error:r})=>r?e(r,!0):c.error())}setDataCapture(f){var r=this;return(0,i.Z)(function*(){try{return yield(0,o.firstValueFrom)(r.http.post(n.bV.FACIAL_BIOMETRICS.UPLOAD_IMAGES,{...f}).pipe((0,o.map)(l=>l)))}catch(l){return console.error("Error en setDataCapture:",l),{success:!1,httpCode:"500",msgRsHdr:null}}})()}startProcess(f){var r=this;return(0,i.Z)(function*(){try{return yield(0,o.firstValueFrom)(r.http.post(n.bV.FACIAL_BIOMETRICS.START_ANALYSIS,{...f}).pipe((0,o.map)(l=>l)))}catch(l){return console.error("Error en startProcess:",l),{success:!1,httpCode:500,msgRsHdr:null}}})()}}return s.\u0275fac=function(f){return new(f||s)(u.\u0275\u0275inject(a.HttpClient))},s.\u0275prov=u.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},13973:(K,P,t)=>{t.d(P,{H:()=>o});var i=t(20691),e=t(99877);let o=(()=>{class a extends i.Store{constructor(){super({})}setDocument(m,s){this.reduce(b=>({...b,documentType:m,documentNumber:s}))}setParamsSDK(m,s){this.reduce(b=>({...b,params:m,presignedUrls:s}))}setUiDevice(m){this.reduce(s=>({...s,uiDevice:m}))}setStep(m){this.reduce(s=>({...s,currentStep:m}))}setCaptureFace(m){this.reduce(s=>({...s,captureFace:m}))}setCaptureFrontSide(m){this.reduce(s=>({...s,captureFrontSide:m}))}setCaptureBackSide(m){this.reduce(s=>({...s,captureBackSide:m}))}setUrlLiveness(m){this.reduce(s=>({...s,UploadLiveness:m}))}setUrlDocument(m,s){this.reduce(b=>({...b,UploadDocumentFrontSide:m,UploadDocumentBackSide:s}))}setProcessExecutor(m){this.reduce(s=>({...s,processExecutor:m}))}clearState(){this.reduce(()=>({}))}getCaptureIdFront(){return this.select(({captureFrontSide:m})=>m)}getCaptureIdBack(){return this.select(({captureBackSide:m})=>m)}getCaptureFace(){return this.select(({captureFace:m})=>m)}getProcessExecutor(){return this.select(({processExecutor:m})=>m)}}return a.\u0275fac=function(m){return new(m||a)},a.\u0275prov=e.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},7324:(K,P,t)=>{t.d(P,{$j:()=>i,gi:()=>c,u:()=>n,x5:()=>e});var i=(()=>{return(o=i||(i={})).INIT="INIT",o.CAPTURE_ID_FRONT="CAPTURE ID FRONT",o.CAPTURE_ID_BACK="CAPTURE ID BACK",o.CAPTURE_FACE="CAPTURE FACE",i;var o})();const c="SATISFACTORIO",e="NO SATISFACTORIO",n=["NSA01-3","NSA01-4","NSA01-5","NSA01-6","NSA01-7","NSA01-14","NSA01-15","NSA01-16","NSA09","NSA05","NSA04","NSA03","NSA02"]},22816:(K,P,t)=>{t.d(P,{S:()=>i});class i{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);