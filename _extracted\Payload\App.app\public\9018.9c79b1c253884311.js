(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9018],{99018:(be,b,_)=>{_.r(b),_.d(b,{Criteria:()=>ee,Criterias:()=>V,Double:()=>u,Either:()=>Z,Optional:()=>j,PartialSealed:()=>U,PromiseSealed:()=>T,Queque:()=>L,ReportState:()=>N,ScrollerElement:()=>Ke,Sealed:()=>X,SecureMap:()=>de,ViewState:()=>z,base64ToBlob:()=>Ve,callback:()=>Le,catchPromise:()=>je,currencyFormat:()=>me,deepClone:()=>Ze,deepFreeze:()=>ue,evalValueOrFunction:()=>Se,fromPromise:()=>We,itIsDefined:()=>k,itIsUndefined:()=>he,observable:()=>fe,parse:()=>Ce,parseBoolean:()=>qe,rxPromise:()=>J,rxPromiseResolve:()=>Be,rxPromiseStatus:()=>He,securePromise:()=>Qe,thenPromise:()=>Xe,voidPromise:()=>Ue,zipPromise:()=>Je});class de extends Map{constructor(e){super(),this.initialValue=e}request(e,s){let t=this.get(e);return t||(t=s??this.initialValue(),this.set(e,t)),t}}class ee{constructor(e,s){this.key=e,this.value=s}assign(e){e(this.key,this.value)}equals(e){return this.value===e}}class V{constructor(){this.collection=new Map}append(e,s){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e?this.collection.set(e,new ee(e,s)):this.collection.set(e.key,e),this}request(e){return this.collection.get(e)}value(e){return this.request(e)?.value}equals({collection:e}){if(this.collection.size!==e.size)return!1;const s=Array.from(e.values());let t=!0,n=0;for(;t&&n<s.length;){const{key:i,value:l}=s[n],c=this.request(i);t=t&&!!c&&c.equals(l),n++}return t}toLiteralObject(){const e={};function s(t,n){e[t]=n}return this.collection.forEach(t=>{t.assign(s)}),e}static fromLiteralObject(e){const s=new V;return Object.entries(e).forEach(([t,n])=>{s.append(t,n)}),s}}function me(r){const{value:e,decimals:s,symbol:t}=r,[n,i]=Math.abs(e).toString().split(".");let l="",c=0;for(let o=1;o<=n.length;o++)3===c&&(c=0,l=`.${l}`),c++,l=`${n.charAt(n.length-o)}${l}`;return s&&i&&(l=`${l},${i.slice(0,2)}`),e<0&&(l=`-${l}`),t?`${t} ${l}`:l}const xe=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=1e5,F=Math.floor(1801439850948198.2),R={decimals:[0],exp:0,signed:0};class u{constructor(e){const{decimals:s,exp:t,signed:n}=Ie(e);this.decimals=s,this.exp=t,this.signed=n}get value(){return+this}get fixed(){return+this.toFixed()}plus(e){const s=E(e);return s.isZero()?this.clone():this.signed==s.signed?se(this,s):ne(this,s.negative())}minus(e){const s=E(e);return s.isZero()?this.clone():this.signed==s.signed?ne(this,s):se(this,s.negative())}multiply(e){return Ne(this,E(e))}divide(e){return ie({double1:this,double2:E(e)})}module(e){const s=E(e);if(!s.signed)throw Error("[DecimalError] Exponent out of range: NaN");if(!this.signed)return W(this,20);const t=ie({double1:this,double2:s,places:!0,precision:0}).multiply(s);return this.minus(t)}percentage(e){return this.multiply(e/100)}equals(e){const s=E(e);if(this.signed!==s.signed||this.exp!==s.exp||this.decimals.length!==s.decimals.length)return!1;for(let t=0;t<this.decimals.length;++t)if(this.decimals[t]!==s.decimals[t])return!1;return!0}greaterThan(e){return S(this,E(e))>0}greaterThanOrEqualTo(e){return S(this,E(e))>=0}lessThan(e){return S(this,E(e))<0}lessThanOrEqualTo(e){return S(this,E(e))<1}abs(){return new u({decimals:this.decimals,exp:this.exp,signed:1})}negative(){return new u({decimals:this.decimals,exp:this.exp,signed:-this.signed})}isNegative(){return this.signed<0}isPositive(){return this.signed>0}isZero(){return 0===this.signed}toFixed(e=2,s=4){ce(e,0,1e9),ce(s,0,8);const t=W(this,e+y(this)+1,s),n=le(t.abs(),!1,e+y(t)+1);return this.isNegative()&&!this.isZero()?`-${n}`:n}roundCeil(){const{decimals:e,exp:s,signed:t}=this,[n,i]=e;return new u({decimals:[i&&i>0?n+1:n],exp:s,signed:t})}roundFloor(){const{decimals:e,exp:s,signed:t}=this,[n]=e;return new u({decimals:[n],exp:s,signed:t})}clone(){return new u(this.props())}props(){const{decimals:e,exp:s,signed:t}=this;return{decimals:[...e],exp:s,signed:t}}toString(){return le(this,ze(y(this)))}static create(e){return new u(e)}static zero(){return new u(R)}}const re=(r,e)=>{let s=e.indexOf("."),t=e.search(/e/i),n=e.length;for(s>-1&&(e=e.replace(".","")),t>0?(s<0&&(s=t),s+=+e.slice(t+1),e=e.substring(0,t)):s<0&&(s=e.length),t=0;48===e.charCodeAt(t);)++t;for(n=e.length;48===e.charCodeAt(n-1);)--n;if(e=e.slice(t,n)){s=s-t-1,n-=t;let i=Math.floor(s/5);const l=[];if(t=(s+1)%5,s<0&&(t+=5),t<n){for(t&&l.push(+e.slice(0,t)),n-=5;t<n;)l.push(+e.slice(t,t+=5));t=5-(e=e.slice(t)).length}else t-=n;for(;t--;)e+="0";if(l.push(+e),i>F||i<-F)throw Error("[DecimalError] Exponent out of range: "+s);return{decimals:l,exp:i,signed:r}}return R},E=r=>r instanceof u?r:u.create(r),Ie=r=>r instanceof u?r.props():"number"==typeof r?(r=>{if(0*r!=0)throw Error("[DecimalError] Invalid argument: "+r);if(0===r)return R;let e=1;return r<0&&(r=-r,e=-1),re(e,r.toString())})(r):"string"==typeof r?Ae(r):r,Ae=r=>{let e=1;if(45===r.charCodeAt(0)&&(r=r.slice(1),e=-1),!xe.test(r))throw Error("[DecimalError] Invalid argument: "+r);return re(e,r)},se=(r,e)=>{if(r.isZero()&&e.isZero())return u.zero();let t,n,s=0,i=[...r.decimals].slice(),l=[...e.decimals],c=r.exp,o=e.exp,a=c-o;if(a){for(a<0?(t=i,a=-a,n=l.length):(t=l,o=c,n=i.length),c=Math.ceil(4),n=c>n?c+1:n+1,a>n&&(a=n,t.length=1),t.reverse();a--;)t.push(0);t.reverse()}for(n=i.length,a=l.length,n-a<0&&(a=n,t=l,l=i,i=t);a;)s=(i[--a]=i[a]+l[a]+s)/d|0,i[a]%=d;for(s&&(i.unshift(s),++o),n=i.length;0==i[--n];)i.pop();return u.create({decimals:i,exp:o,signed:r.signed})},ne=(r,e)=>{if(r.isZero()&&e.isZero())return u.zero();let s,t,n,i,l,c=[...r.decimals].slice(),o=[...e.decimals],a=e.signed,g=r.exp,m=e.exp,h=g-m;if(h){for(l=h<0,l?(s=c,h=-h,i=o.length):(s=o,m=g,i=c.length),t=Math.max(Math.ceil(4),i)+2,h>t&&(h=t,s.length=1),s.reverse(),t=h;t--;)s.push(0);s.reverse()}else{for(i=o.length,t=c.length,l=t<i,l&&(i=t),t=0;t<i;t++)if(c[t]!=o[t]){l=c[t]<o[t];break}h=0}for(l&&(s=c,c=o,o=s,0!=a&&(a=-a)),i=c.length,t=o.length-i;t>0;--t)c[i++]=0;for(t=o.length;t>h;){if(c[--t]<o[t]){for(n=t;n&&0===c[--n];)c[n]=d-1;--c[n],c[t]+=d}c[t]-=o[t]}for(;0===c[--i];)c.pop();for(;0===c[0];c.shift())--m;return c[0]?u.create({decimals:c,exp:m,signed:a}):u.zero()},Ne=(r,e)=>{if(r.isZero()||e.isZero())return u.zero();const s=r.signed*e.signed;let t,n,m,h,i=r.exp+e.exp,l=[...r.decimals],c=[...e.decimals],o=[],a=l.length,g=c.length;for(a<g&&(o=l,l=c,c=o,n=a,a=g,g=n),o=[],n=a+g,t=n;t--;)o.push(0);for(t=g;--t>=0;){for(m=0,h=a+t;h>t;){const x=o[h]+c[t]*l[h-t-1]+m;o[h--]=x%d|0,m=x/d|0}o[h]=(o[h]+m)%d|0}for(;!o[--n];)o.pop();return m?++i:o.shift(),u.create({decimals:o,exp:i,signed:s})},ie=({double1:r,double2:e,places:s,precision:t})=>{if(e.isZero())throw Error("[DecimalError] Division by zero");if(r.isZero())return u.zero();const n=r.signed==e.signed?1:-1;let i,l,c,o=[...r.decimals],a=[...e.decimals],g=[],m=r.decimals.length,h=e.decimals.length,x=r.exp-e.exp;for(i=0;a[i]==(o[i]||0);)++i;if(a[i]>(o[i]||0)&&--x,c=t?s?t+(y(r)-y(e))+1:t:t=20,c<0)return u.zero();if(c=c/5+2|0,i=0,1==h){const M=a[0];for(l=0,c++;(i<m||l)&&c--;i++){const p=l*d+(o[i]||0);g[i]=p/M|0,l=p%M|0}}else{l=d/(a[0]+1)|0,l>1&&(a=$(a,l),o=$(o,l),h=a.length,m=o.length);let M=h,p=o.slice(0,h),w=p.length;for(;w<h;)p[w++]=0;let Y=a.slice();Y.unshift(0);let pe=a[0];e.decimals[1]>=d/2&&++pe;do{l=0;let O=B(e.decimals,p,h,w);if(O<0){let I,D,K=p[0];h!=w&&(K=K*d+(p[1]||0)),l=K/pe|0,l>1?(l>=d&&(l=d-1),I=$(a,l),D=I.length,w=p.length,O=B(I,p,D,w),1==O&&(l--,H(I,h<D?Y:a,D))):(0==l&&(O=l=1),I=a.slice()),D=I.length,D<w&&I.unshift(0),H(p,I,w),-1==O&&(w=p.length,O=B(a,p,h,w),O<1&&(l++,H(p,h<w?Y:a,w))),w=p.length}else 0===O&&(l++,p=[0]);g[i++]=l,O&&p[0]?p[w++]=o[M]||0:(p=[o[M]],w=1)}while((M++<m||void 0!==p[0])&&c--)}g[0]||g.shift();const v=u.create({decimals:g,exp:x,signed:n}),Q=s?t+y(v)+1:t;return W(v,Q)},$=(r,e)=>{let n,s=r.length,t=0;for(r=r.slice();s--;)n=r[s]*e+t,r[s]=n%d|0,t=n/d|0;return t&&r.unshift(t),r},S=(r,e)=>{if(r.signed!==e.signed)return r.signed||-e.signed;if(r.exp!==e.exp)return r.exp>e.exp!=r.signed<0?1:-1;const s=r.decimals,t=e.decimals,n=s.length,i=t.length;for(let l=0,c=n<i?n:i;l<c;++l)if(s[l]!==t[l])return s[l]>t[l]!=r.signed<0?1:-1;return n===i?0:n>i!=r.signed<0?1:-1},B=(r,e,s,t)=>{if(s!=t)return s>t?1:-1;let n=0,i=0;for(;i<s;i++)if(r[i]!=e[i]){n=r[i]>e[i]?1:-1;break}return n},H=(r,e,s)=>{for(let t=0;s--;)r[s]-=t,t=r[s]<e[s]?1:0,r[s]=t*d+r[s]-e[s];for(;!r[0]&&r.length>1;)r.shift()},W=(r,e,s)=>{const t=[...r.decimals];let n=1;for(let x=t[0];x>=10;x/=10)n++;let l,a,i=e-n,c=t[0],o=0;if(i<0)l=e,i+=5;else{if(o=Math.ceil((i+1)/5),o>=t.length)return r.clone();let x=c=t[o];for(n=1;x>=10;x/=10)n++;i%=5,l=i-5+n}if(s){const x=Math.pow(10,n-l-1);let v=c/x%10|0;a=e<0||void 0!==t[o+1]||c%x,a=s<4?(v||a)&&(0==s||s==(r.signed<0?3:2)):v>5||5==v&&(4==s||a||6==s&&(i>0?l>0?c/Math.pow(10,n-l):0:t[o-1])%10&1||s==(r.signed<0?8:7))}if(e<1||!t[0]){if(a){e=e-y(r)-1;const v=Math.pow(10,(5-e%5)%5),Q=Math.floor(-e/5)||0;return u.create({decimals:[v],exp:Q,signed:r.signed})}return u.zero()}let g=Math.pow(10,5-i),m=r.exp,h=r.signed;if(0==i?(t.length=o,g=1,o--):(t.length=o+1,t[o]=l>0?(c/Math.pow(10,n-l)%Math.pow(10,l)|0)*g:0),a)for(;;){if(0==o){(t[0]+=g)==d&&(t[0]=1,++m);break}if(t[o]+=g,t[o]!=d)break;t[o--]=0,g=1}for(i=t.length;0===t[--i];)t.pop();if(r.exp>F||r.exp<-F)throw Error("[DecimalError] Exponent out of range: "+y(r));return u.create({decimals:t,exp:m,signed:h})},le=(r,e,s)=>{let l,t=Me(r.decimals),n=t.length,i=y(r);return e?(s&&(l=s-n)>0?t=t.charAt(0)+"."+t.slice(1)+P(l):n>1&&(t=t.charAt(0)+"."+t.slice(1)),t=t+(i<0?"e":"e+")+i):i<0?(t="0."+P(-i-1)+t,s&&(l=s-n)>0&&(t+=P(l))):i>=n?(t+=P(i+1-n),s&&(l=s-i-1)>0&&(t=t+"."+P(l))):((l=i+1)<n&&(t=t.slice(0,l)+"."+t.slice(l)),s&&(l=s-n)>0&&(i+1===n&&(t+="."),t+=P(l))),r.signed<0?"-"+t:t},ze=r=>r<=-5||r>=15,y=r=>{let e=5*r.exp,s=r.decimals[0];for(;s>=10;s/=10)e++;return e},Me=r=>{let e=r.length-1,s="",t=r[0];if(e>0){let i,n=1;for(s+=t;n<e;n++){const l=r[n].toString();i=5-l.length,i&&(s+=P(i)),s+=l}t=r[n],i=5-t.toString().length,i&&(s+=P(i))}else if(0===t)return"0";for(;t%10==0;)t/=10;return s+t},P=r=>{let e="";for(;r--;)e+="0";return e},ce=(r,e,s)=>{if(r!==~~r||r<e||r>s)throw Error("[DecimalError] Invalid argument: "+r)};class X{constructor(e,s){this.key=e,this.value=s}otherwise(e){return this.sealedOtherwise=e,this}when(e,s){const t=e[this.key],n=s||this.sealedOtherwise;if(n&&n(),t)return t(this.value);throw Error("Sealed class could not resolve call")}is(e){return this.key===e}}class U{constructor(e,s){this.key=e,this.value=s}otherwise(e){return this.sealedOtherwise=e,this}when(e,s){const t=e[this.key],n=s||this.sealedOtherwise;return n&&n(),t?t(this.value):void 0}}class Z extends U{static success(e){return new Z("success",e)}static failure(e){return new Z("failure",e)}}const De=[Date,RegExp,Function,String,Boolean,Number],oe=512,ke=["false","undefined","0",0],Fe=Object.prototype.toString;function ae(r,e){if("object"!=typeof r)return r;if("[object Object]"===Fe.call(r)){const[i]=e.filter(l=>l===r);if(i)return i;e.push(r)}const t=Object.getPrototypeOf(r).constructor;if(De.includes(t))return new t(r);const n=new t;for(const i in r)n[i]=ae(r[i],e);return n}function k(r){return typeof r<"u"&&null!==r}function he(r){return!k(r)}function qe(r){return!(he(r)||!1===r||ke.includes(r))}function Ce(r){try{return JSON.parse(r)}catch{return r}}function Se(r){return"function"==typeof r?r():r}function Ze(r){return ae(r,[])}function ue(r){for(const e in r){const s=r[e];"object"==typeof s&&!Object.isFrozen(s)&&ue(s)}return Object.freeze(r)}function Le(r,...e){return"function"!=typeof r?void 0:r.apply(r,e)}function Ve(r,e){const s=r.replace(/^[^,]+,/,"").replace(/\s/g,""),t=window.atob(s),n=[];for(let i=0;i<t.length;i+=oe){const l=t.slice(i,i+oe),c=new Array(l.length);for(let a=0;a<l.length;a++)c[a]=l.charCodeAt(a);const o=new Uint8Array(c);n.push(o)}return new Blob(n,{type:e})}class Re{constructor(e){this.observers=[],this.currentState=e}subscribe(e){return this.observers.push(e),e(this.currentState),()=>{this.observers=this.observers.filter(s=>s!==e)}}next(e){this.currentState=e,this.observers.forEach(s=>{s(e)})}}function fe(r){return new Re(r)}class j{constructor(e){this.value=e}present(e){return this.isPresent()?e(this.get()):void 0}empty(e){return this.isEmpty()?e():void 0}when(e,s){return this.isPresent()?e(this.get()):s()}static build(e){return k(e)?this.of(e):this.empty()}static of(e){if(k(e))return new Ge(e);throw new Error("The passed value was null or undefined.")}static empty(){return new $e}}class Ge extends j{constructor(e){super(e),this.presentValue=e}isPresent(){return!0}isEmpty(){return!1}get(){return this.presentValue}}class $e extends j{isPresent(){return!1}isEmpty(){return!0}get(){throw new Error("The optional is not present.")}}class T extends U{static loading(){return new T("loading")}static success(e){return new T("success",e)}static failure(e){return new T("failure",e)}}class A{constructor(e,s,t,n,i){this.isLoading=e,this.isSuccessful=s,this.isError=t,this.value=n,this.error=i}static loading(){return new A(!0,!1,!1)}static successful(e){return new A(!1,!0,!1,e)}static error(e){return new A(!1,!1,!0,void 0,e)}}function J(r){const e=fe(T.loading()),s=Date.now();return r.then(t=>{e.next(T.success({response:t,responseTime:Date.now()-s}))}).catch(t=>{e.next(T.failure(t))}),{subscribe:t=>e.subscribe(t)}}function Be(r){return{subscribe:e=>J(r).subscribe(s=>{s.when(e)})}}function He(r){return{subscribe:e=>J(r).subscribe(s=>{e(s.when({loading:()=>A.loading(),success:({response:t})=>A.successful(t),failure:t=>A.error(t)}))})}}function We(r){return r instanceof Promise?r:Promise.resolve(r)}function Xe(r,e=!1){return r.then(()=>{}).catch(s=>{throw e&&console.log(s),s})}function Ue(r,e=!1){return r.then(()=>{}).catch(s=>{e&&console.log(s)})}function je(r,e=!1){return r.catch(s=>{e&&console.log(s)})}function ge(r){const{callbacks:e,catchError:s,index:t,resolve:n,result:i}=r;if(t===e.length)return n(i);new Promise(()=>{e[t]().then(l=>(i.push(l),ge({...r,index:t+1,result:i}))).catch(l=>s(l))})}function Je(r){const e=[];return new Promise((s,t)=>{ge({callbacks:r,catchError:n=>{t(n)},index:0,resolve:n=>{s(n)},result:e})})}function Qe(r,e){let s;function n(){return s||(s=r().catch(c=>{const o=e&&e(c);if(i(),o)return o;throw c})),s}function i(){s=void 0}return{itIsInstanced:function t(){return k(s)},refresh:function l(){return s=void 0,n()},reset:i,resolve:n}}class Ye{constructor(e){this.value=e}set next(e){this.nextElement=e}get next(){return this.nextElement}}class L{constructor(){this.lengthValue=0}get length(){return this.lengthValue}enqueue(e){const s=new Ye(e);this.head?this.tail&&(this.tail.next=s):this.head=s,this.tail=s,this.lengthValue++}dequeue(){if(!this.head)return;const{next:e,value:s}=this.head;return this.head=e,this.lengthValue--,s}static fromArray(e){const s=new L;return e.forEach(t=>s.enqueue(t)),s}static map(e,s){const t=new L;return e.map(n=>t.enqueue(s(n))),t}}class Ke{constructor(e,s=0){this.element=e,this.scrollError=s}get scrollWidth(){return this.element.scrollWidth}get scrollHeight(){return this.element.scrollHeight}get scrollLeft(){return this.element.scrollLeft}get scrollTop(){return this.element.scrollTop}get clientWidth(){return this.element.clientWidth}get clientHeight(){return this.element.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){return 100*(this.scrollTop/this.scrollHeight-this.clientHeight)}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){return 100*(this.scrollLeft/this.scrollWidth-this.clientWidth)}}class z extends X{static loading(e){return new z("loading",e)}static success(e){return new z("success",e)}static empty(e){return new z("empty",e)}static failure(e){return new z("failure",e)}}class N extends X{static welcome(e){return new N("welcome",e)}static loading(e){return new N("loading",e)}static success(e){return new N("success",e)}static empty(e){return new N("empty",e)}static failure(e){return new N("failure",e)}}}}]);