(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4006],{94006:(A,s,n)=>{n.r(s),n.d(s,{MboTransferGenericAmountPageModule:()=>z});var l=n(17007),u=n(78007),a=n(30263),i=n(24495),C=n(39904),T=n(87903),I=n(95437),b=n(57544),M=n(40914),v=n(99013),E=n(18767),e=n(99877),N=n(83413),y=n(35641),R=n(48774),S=n(60817),x=n(45542);const{MIN_UNREGISTERED:G}=M.R,d=C.Z6.TRANSFERS.GENERIC;let P=(()=>{class t{constructor(r,o,m,g){this.mboProvider=r,this.requestConfiguration=o,this.managerTransfer=m,this.cancelProvider=g,this.type="affiliation",this.confirmation=!1,this.backAction={id:"btn_transfer-generic-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back("unregistered"===this.type?d.UNREGISTERED:d.DESTINATION)}},this.cancelAction={id:"btn_transfer-generic-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new b.FormControl,this.requiredCostControl=new b.FormControl(!1)}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerTransfer.setAmount(this.amountControl.value,this.requiredCostControl.value).when({success:()=>{this.mboProvider.navigation.next(d.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:r=>{const{amount:o,confirmation:m,requiredCost:g,source:p,type:h}=r;this.confirmation=m,this.type=h,this.source=p,o&&this.amountControl.setValue(o);const f=[i.C1,i.LU,i.PO];(0,T.VN)(p)&&f.push((0,i.vB)(p.amount)),"unregistered"===h&&f.push((0,i.VV)(G)),this.amountControl.setValidators(f),this.requiredCostControl.setValue(g)}})}}return t.\u0275fac=function(r){return new(r||t)(e.\u0275\u0275directiveInject(I.ZL),e.\u0275\u0275directiveInject(v.ow),e.\u0275\u0275directiveInject(v.Al),e.\u0275\u0275directiveInject(E.S))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfer-generic-amount-page"]],decls:14,vars:10,consts:[[1,"mbo-transfer-generic-amount-page__content"],[1,"mbo-transfer-generic-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfer-generic-amount-page__body"],[1,"mbo-transfer-generic-amount-page__message","subtitle2-medium"],["elementId","txt_transfer-generic-amount_value","label","Valor a transferir","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],["elementId","chck_transfer-generic-amount_cost",3,"formControl"],[1,"mbo-transfer-generic-amount-page__footer"],["id","btn_transfer-generic-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(r,o){1&r&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"p",4),e.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas transferir? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),e.\u0275\u0275elementStart(8,"bocc-checkbox-label",7),e.\u0275\u0275text(9," Deseo ver el costo de la transferencia "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(10,"div",8)(11,"button",9),e.\u0275\u0275listener("click",function(){return o.onSubmit()}),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13,"Continuar"),e.\u0275\u0275elementEnd()()()),2&r&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("formControl",o.amountControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("color",null==o.source?null:o.source.color)("icon",null==o.source?null:o.source.logo)("title",null==o.source?null:o.source.nickname)("number",null==o.source?null:o.source.publicNumber)("amount",null==o.source?null:o.source.amount),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",o.requiredCostControl),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",o.amountControl.invalid))},dependencies:[N.D,y.d,R.J,S.a,x.P],styles:["/*!\n * MBO TransferGenericAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 25/Jun/2022\n * Updated: 06/Ene/2024\n*/mbo-transfer-generic-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);--pvt-checkbox-margin-top: var(--sizing-x20);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__body bocc-checkbox-label{margin-top:var(--pvt-checkbox-margin-top)}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-generic-amount-page .mbo-transfer-generic-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfer-generic-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20);--pvt-checkbox-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),t})(),z=(()=>{class t{}return t.\u0275fac=function(r){return new(r||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[l.CommonModule,u.RouterModule.forChild([{path:"",component:P}]),a.D1,a.dH,a.Jx,a.aR,a.P8]}),t})()},40914:(A,s,n)=>{n.d(s,{R:()=>l,r:()=>u});const l={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},u={MIN_TRUSTFUND:2e5}}}]);