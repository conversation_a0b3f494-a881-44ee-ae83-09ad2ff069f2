(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6648],{9736:(D,j,r)=>{r.d(j,{Hu:()=>u,wG:()=>g,rQ:()=>B,_G:()=>Z});var p=r(15861),E=r(87956),M=r(53113),a=r(98699);class i{constructor(l,e,n,o,v){this.id=l,this.nit=e,this.name=n,this.city=o,this.exampleUrl=v}}class P{constructor(l,e,n,o){this.number=l,this.amount=e,this.expirationDate=n,this.companyId=o}get expirationFormat(){return this.expirationDate.dateFormat}}class x{constructor(l,e,n){this.agreement=l,this.invoice=e,this.source=n}}function h(t){return new x(t.agreement,t.invoice,t.source)}function C(t){return new i(t.orgIdNum,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var S=r(71776),f=r(39904),R=r(87903),y=r(42168),I=r(84757),s=r(99877);let N=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,y.firstValueFrom)(this.http.get(f.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,I.map)(({content:n})=>n.map(o=>C(o)))))}requestCompanyId(e){return(0,y.firstValueFrom)(this.http.get(f.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,I.map)(({content:n})=>n.length?C(n[0]):null)))}requestInvoice(e,n){return(0,y.firstValueFrom)(this.http.get(f.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,I.map)(o=>function T(t){return new P(t.nie||t.invoiceNum,+t.amt,new M.ou(t.expDt),t.orgIdNum)}(o))))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(S.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),V=(()=>{class t{constructor(e){this.http=e}send(e){return(0,y.firstValueFrom)(this.http.post(f.bV.PAYMENTS.INVOICE_MANUAL,function F(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,I.map)(([n])=>(0,R.l1)(n,"SUCCESS")))).catch(n=>(0,R.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(S.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var c=r(20691);let d=(()=>{class t extends c.Store{constructor(e){super({confirmation:!1}),e.subscribes(f.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(E.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),u=(()=>{class t{constructor(e,n,o){this.repository=e,this.store=n,this.eventBusService=o}setAgreement(e){try{return a.Either.success(this.store.setAgreement(e))}catch({message:n}){return a.Either.failure({message:n})}}setInvoice(e){try{return a.Either.success(this.store.setInvoice(e))}catch({message:n}){return a.Either.failure({message:n})}}setSource(e){try{return a.Either.success(this.store.setSource(e))}catch({message:n}){return a.Either.failure({message:n})}}reset(){try{return a.Either.success(this.store.reset())}catch({message:e}){return a.Either.failure({message:e})}}send(){var e=this;return(0,p.Z)(function*(){const n=h(e.store.currentState),o=yield e.execute(n);return e.eventBusService.emit(o.channel),a.Either.success({invoice:n,status:o})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(M.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(V),s.\u0275\u0275inject(d),s.\u0275\u0275inject(E.Yd))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),g=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,p.Z)(function*(){try{return a.Either.success(yield n.repository.requestAll(e))}catch({message:o,status:v}){return 400===v?a.Either.success([]):a.Either.failure({message:o})}})()}invoice(e,{id:n}){var o=this;return(0,p.Z)(function*(){try{return a.Either.success(yield o.repository.requestInvoice(e,n))}catch({message:v,status:A}){return a.Either.failure({value:400===A,message:v})}})()}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(N))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),B=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return a.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return a.Either.failure({message:e})}}source(){var e=this;return(0,p.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),o=e.store.getAgreement(),v=e.store.getInvoice();return a.Either.success({agreement:o,invoice:v,products:n})}catch({message:n}){return a.Either.failure({message:n})}})()}confirmation(){try{const e=h(this.store.currentState);return a.Either.success({payment:e})}catch({message:e}){return a.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(E.hM),s.\u0275\u0275inject(d))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const O=/^415(\d+)8020(\d+)$/;let U=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,p.Z)(function*(){const o=e.replace(/\D/g,"").match(O);if(!o)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const v=yield n.requestNuraCodes(),A=o[1],b=v.find(({ean_code:H})=>H===A);if(!b)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:o[2].slice(0,+b.length),companyId:b.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,y.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,I.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(S.HttpClient))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),Z=(()=>{class t{constructor(e,n,o){this.repository=e,this.store=n,this.barcodeService=o}execute(e){var n=this;return(0,p.Z)(function*(){try{const{reference:o,companyId:v}=yield n.barcodeService.execute(e),A=yield n.repository.requestCompanyId(v),b=yield n.repository.requestInvoice(o,v);return n.store.setAgreement(A),n.store.setInvoice(b),a.Either.success()}catch{return a.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275inject(N),s.\u0275\u0275inject(d),s.\u0275\u0275inject(U))},t.\u0275prov=s.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},76648:(D,j,r)=>{r.r(j),r.d(j,{MboPaymentInvoiceManualResultPageModule:()=>V});var p=r(17007),E=r(78007),M=r(79798),a=r(15861),i=r(99877),P=r(39904),x=r(95437),h=r(87903),C=r(53113);function T(c){const{isError:d,message:u}=c;return{animation:(0,h.jY)(c),title:d?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:u}}function F({isError:c}){return c?[(0,h.wT)("Finalizar","finish","outline"),(0,h.wT)("Volver a intentar","retry")]:[(0,h.wT)("Hacer otro pago","retry","outline"),(0,h.wT)("Finalizar","finish")]}var f=r(9736),R=r(10464),y=r(78021),I=r(16442);function s(c,d){if(1&c&&(i.\u0275\u0275elementStart(0,"div",4),i.\u0275\u0275element(1,"mbo-header-result",5),i.\u0275\u0275elementEnd()),2&c){const u=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("rightActions",u.rightActions)}}let N=(()=>{class c{constructor(u,m,g){this.ref=u,this.mboProvider=m,this.managerInvoice=g,this.requesting=!0,this.template=P.$d,this.rightActions=[{id:"btn_payment-invoice-manual-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-invoice-manual-result-page_template"),this.initializatedTransaction()}onAction(u){this.mboProvider.navigation.next("finish"===u?P.Z6.CUSTOMER.PRODUCTS.HOME:P.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL.SELECT)}initializatedTransaction(){var u=this;return(0,a.Z)(function*(){(yield u.managerInvoice.send()).when({success:m=>{u.template=function S(c){const{dateFormat:d,timeFormat:u}=new C.ou,{status:m,invoice:g}=c;return{actions:F(m),error:m.isError,header:T(m),informations:[(0,h.SP)("DESTINO",g.agreement.name,g.invoice.number),(0,h._f)("SUMA DE",g.invoice.amount),(0,h.cZ)(d,u)],skeleton:!1}}(m)}},()=>{u.requesting=!1,u.managerInvoice.reset()})})()}}return c.\u0275fac=function(u){return new(u||c)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(x.ZL),i.\u0275\u0275directiveInject(f.Hu))},c.\u0275cmp=i.\u0275\u0275defineComponent({type:c,selectors:[["mbo-payment-invoice-manual-result-page"]],decls:5,vars:2,consts:[[1,"mbo-payment-invoice-manual-result-page__content","mbo-page__scroller"],["class","mbo-payment-invoice-manual-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-invoice-manual-result-page__body"],["id","crd_payment-invoice-manual-result-page_template",3,"template","action"],[1,"mbo-payment-invoice-manual-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(u,m){1&u&&(i.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),i.\u0275\u0275template(2,s,2,1,"div",1),i.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),i.\u0275\u0275listener("action",function(B){return m.onAction(B)}),i.\u0275\u0275elementEnd()()()()),2&u&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!m.requesting),i.\u0275\u0275advance(2),i.\u0275\u0275property("template",m.template))},dependencies:[p.NgIf,R.K,y.c,I.u],styles:["/*!\n * MBO PaymentInvoiceManualResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-invoice-manual-result-page .mbo-payment-invoice-manual-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-invoice-manual-result-page .mbo-payment-invoice-manual-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),c})(),V=(()=>{class c{}return c.\u0275fac=function(u){return new(u||c)},c.\u0275mod=i.\u0275\u0275defineNgModule({type:c}),c.\u0275inj=i.\u0275\u0275defineInjector({imports:[p.CommonModule,E.RouterModule.forChild([{path:"",component:N}]),M.KI,M.cN,M.tu]}),c})()}}]);