(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9434],{79434:(_,c,o)=>{o.r(c),o.d(c,{ion_loading:()=>u});var h=o(15861),e=o(42477),l=o(87036),f=o(78635),g=o(37389),s=o(57346),m=o(23814),d=o(37943),r=o(44963);o(72972),o(33006);const y=i=>{const t=(0,r.c)(),n=(0,r.c)(),a=(0,r.c)();return n.addElement(i.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),a.addElement(i.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),t.addElement(i).easing("ease-in-out").duration(200).addAnimation([n,a])},k=i=>{const t=(0,r.c)(),n=(0,r.c)(),a=(0,r.c)();return n.addElement(i.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),a.addElement(i.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),t.addElement(i).easing("ease-in-out").duration(200).addAnimation([n,a])},x=i=>{const t=(0,r.c)(),n=(0,r.c)(),a=(0,r.c)();return n.addElement(i.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),a.addElement(i.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),t.addElement(i).easing("ease-in-out").duration(200).addAnimation([n,a])},v=i=>{const t=(0,r.c)(),n=(0,r.c)(),a=(0,r.c)();return n.addElement(i.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),a.addElement(i.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),t.addElement(i).easing("ease-in-out").duration(200).addAnimation([n,a])},u=class{constructor(i){(0,e.r)(this,i),this.didPresent=(0,e.d)(this,"ionLoadingDidPresent",7),this.willPresent=(0,e.d)(this,"ionLoadingWillPresent",7),this.willDismiss=(0,e.d)(this,"ionLoadingWillDismiss",7),this.didDismiss=(0,e.d)(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=(0,e.d)(this,"didPresent",7),this.willPresentShorthand=(0,e.d)(this,"willPresent",7),this.willDismissShorthand=(0,e.d)(this,"willDismiss",7),this.didDismissShorthand=(0,e.d)(this,"didDismiss",7),this.delegateController=(0,s.d)(this),this.lockController=(0,g.c)(),this.triggerController=(0,s.e)(),this.customHTMLEnabled=d.c.get("innerHTMLTemplatesEnabled",l.E),this.presented=!1,this.onBackdropTap=()=>{this.dismiss(void 0,s.B)},this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.message=void 0,this.cssClass=void 0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.spinner=void 0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(i,t){!0===i&&!1===t?this.present():!1===i&&!0===t&&this.dismiss()}triggerChanged(){const{trigger:i,el:t,triggerController:n}=this;i&&n.addClickListener(t,i)}connectedCallback(){(0,s.j)(this.el),this.triggerChanged()}componentWillLoad(){if(void 0===this.spinner){const i=(0,d.b)(this);this.spinner=d.c.get("loadingSpinner",d.c.get("spinner","ios"===i?"lines":"crescent"))}(0,s.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,f.r)(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){var i=this;return(0,h.Z)(function*(){const t=yield i.lockController.lock();yield i.delegateController.attachViewToDom(),yield(0,s.f)(i,"loadingEnter",y,x),i.duration>0&&(i.durationTimeout=setTimeout(()=>i.dismiss(),i.duration+10)),t()})()}dismiss(i,t){var n=this;return(0,h.Z)(function*(){const a=yield n.lockController.lock();n.durationTimeout&&clearTimeout(n.durationTimeout);const p=yield(0,s.g)(n,i,t,"loadingLeave",k,v);return p&&n.delegateController.removeViewFromDom(),a(),p})()}onDidDismiss(){return(0,s.h)(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return(0,s.h)(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(i){const{customHTMLEnabled:t,message:n}=this;return t?(0,e.h)("div",{class:"loading-content",id:i,innerHTML:(0,l.a)(n)}):(0,e.h)("div",{class:"loading-content",id:i},n)}render(){const{message:i,spinner:t,htmlAttributes:n,overlayIndex:a}=this,p=(0,d.b)(this),b=`loading-${a}-msg`;return(0,e.h)(e.H,Object.assign({key:"e780853dc67b7b4ebd8dd65cadab648e4238c6ee",role:"dialog","aria-modal":"true","aria-labelledby":void 0!==i?b:null,tabindex:"-1"},n,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},(0,m.g)(this.cssClass)),{[p]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),(0,e.h)("ion-backdrop",{key:"8cd59ca7bc97b981fd578a526dfe859847e4d392",visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,e.h)("div",{key:"ef392aaf2cb7f6f9cecc685525cce3abc333e800",tabindex:"0"}),(0,e.h)("div",{key:"f1f6df21a7fa6565fe33acb4a5f355b5ec3e65b2",class:"loading-wrapper ion-overlay-wrapper"},t&&(0,e.h)("div",{key:"725cf5a206152885e31ab061b0c466fe1ead0225",class:"loading-spinner"},(0,e.h)("ion-spinner",{key:"5891dc39fa133b71576aec219f552386b202e163",name:t,"aria-hidden":"true"})),void 0!==i&&this.renderLoadingMessage(b)),(0,e.h)("div",{key:"8103269f1181325a507ed1c681f5ef15e40fbc34",tabindex:"0"}))}get el(){return(0,e.f)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}};u.style={ios:".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, #666666);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",md:".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, #f2f2f2);--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #3880ff);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, #262626);font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}"}},37389:(_,c,o)=>{o.d(c,{c:()=>e});var h=o(15861);const e=()=>{let l;return{lock:function(){var g=(0,h.Z)(function*(){const s=l;let m;return l=new Promise(d=>m=d),void 0!==s&&(yield s),m});return function(){return g.apply(this,arguments)}}()}}}}]);