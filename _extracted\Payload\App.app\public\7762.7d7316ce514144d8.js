(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7762],{53644:(P,f,r)=>{r.d(f,{MK:()=>T,NM:()=>_,Rm:()=>v,VS:()=>c,ay:()=>d});var m=r(90806);class d{constructor(a){this.tagAval=a}}class _{constructor(a,o){this.subtitle=a,this.title=o}}class c{constructor(a,o,t){this.fullName=a,this.documentType=o,this.documentNumber=t,this.maskName=(0,m.Z)(a)}}class v{constructor(a,o,t,e,n,h){this.keyType=a,this.tagAval=o,this.accountReceptor=t,this.type=e,this.bank=n,this.customer=h}get customerMaskName(){return this.customer.maskName}}class T{constructor(a,o,t,e,n,h,g){this.source=a,this.account=o,this.contact=t,this.customerName=e,this.ipAddress=n,this.amount=h,this.note=g}}},30786:(P,f,r)=>{r.d(f,{$:()=>T,Ry:()=>a,iK:()=>l});var m=r(29306),d=r(64892),_=r(87903),c=r(53644);const v={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function T(o){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:t,CustIdentType:e,GovIssueIdent:n,PersonName:{FirstName:h}},DepAcctId:{AcctId:g,AcctType:p,BankInfo:u}},RefInfo:E}=o,s=n?.GovIssueIdentType||e,i=n?.IdentSerialNum||t,{RefId:A,RefType:I}=E[0];return new c.Rm(I,A,g,p,new m.Br(u.BankId,u.Name,u.BankId===d.qE.Occidente),new c.VS(h,(0,_.nX)(v[s]),i))}function l(o){return{fromDepAcctId:o.source.id,fromDepAcctName:o.source.name,fromDepAcctType:o.source.type,fromNickName:o.source.nickname,toDepAcctBankId:o.account.bank.id,toDepAcctType:o.account.type,toDepAcctId:o.account.accountReceptor,toDepAcctName:o.account.customer.fullName,toNickName:"",toUserIdNumber:o.account.customer.documentNumber,toUserIdType:o.account.customer.documentType.code,keyInfo:{key:o.account.tagAval,type:o.account.keyType},personInfoTo:{fullName:o.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:o.account.customer.documentType.code,identSerialNum:o.account.customer.documentNumber}},personInfoFrom:{firstName:o.customerName.clientFirstName,lastName:o.customerName.clientLastName,legalName:`${o.customerName.clientFirstName} ${o.customerName.clientLastName}`},curAmt:o.amount.toString(),refId:o.note?.reference||"",memo:o.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function a(o){return new c.MK(o.source,o.account,new c.NM(o.destination.tagAval,o.account.customer.fullName),o.customerName,o.ipAddress,o.amount,o.note)}},90806:(P,f,r)=>{r.d(f,{D:()=>v,Z:()=>T});var m=r(87903),d=r(53113);function _(l){const{isError:a,message:o}=l;return{animation:(0,m.jY)(l),title:a?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:o}}function c({isError:l}){return l?[(0,m.wT)("Finalizar","finish","outline"),(0,m.wT)("Volver a intentar","retry")]:[(0,m.wT)("Hacer otra transferencia","retry","outline"),(0,m.wT)("Finalizar","finish")]}function v(l){const{dateFormat:a,timeFormat:o}=new d.ou,{status:t,tagAval:e}=l,n=[(0,m.SP)("DESTINO",e.account.customer.maskName,e.account.tagAval,e.account.bank.name),(0,m._f)("SUMA DE",e.amount)];return e.note&&n.push((0,m.SP)("DESCRIPCI\xd3N",e.note.description,"",e.note.reference)),n.push((0,m.cZ)(a,o)),{actions:c(t),error:t.isError,header:_(t),informations:n,skeleton:!1}}function T(l){const a=l.split(" "),[o]=a,t=a[a.length-1],e=t.length,h=e>3?3:2;return`${o.substring(0,o.length>4?4:2)}*****${t.substring(e-h,e)}`}},90596:(P,f,r)=>{r.d(f,{$:()=>m.$,N:()=>a});var m=r(50142),d=r(15861),_=r(87956),c=r(98699),v=r(30786),T=r(23604),l=r(99877);let a=(()=>{class o{constructor(e,n){this.store=e,this.products=n}source(){var e=this;return(0,d.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),h=e.store.itIsConfirmation();return c.Either.success({confirmation:h,products:n})}catch({message:n}){return c.Either.failure({message:n})}})()}destination(){var e=this;return(0,d.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),h=e.store.itIsConfirmation();return c.Either.success({confirmation:h,hasOneSource:n.length<2,destination:e.store.getTagAval()})}catch({message:n}){return c.Either.failure({message:n})}})()}amount(){try{const e=this.store.itIsConfirmation(),n=this.store.getSource(),h=this.store.getAmount(),g=this.store.getAccount();return c.Either.success({account:g,amount:h,confirmation:e,source:n})}catch({message:e}){return c.Either.failure({message:e})}}confirmation(){try{const e=(0,v.Ry)(this.store.currentState);return c.Either.success({transfer:e})}catch({message:e}){return c.Either.failure({message:e})}}}return o.\u0275fac=function(e){return new(e||o)(l.\u0275\u0275inject(T.B),l.\u0275\u0275inject(_.hM))},o.\u0275prov=l.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},50142:(P,f,r)=>{r.d(f,{$:()=>p});var m=r(15861),d=r(87956),_=r(53113),c=r(98699),v=r(30786),T=r(71776),l=r(39904),a=r(87903),o=r(42168),t=r(84757),e=r(99877);let n=(()=>{class u{constructor(s){this.http=s}requestVerifyAccount(s){var i=this;return(0,m.Z)(function*(){return(0,o.firstValueFrom)(i.http.post(l.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:s},pilot:!0}).pipe((0,t.map)(A=>A.map(I=>(0,v.$)(I))),(0,t.catchError)(A=>{if("206"===A.error?.MsgRsHdr?.Status?.StatusCode)return(0,o.of)([]);throw A})))})()}send(s){return(0,o.firstValueFrom)(this.http.post(l.bV.TRANSFERS.TAG_AVAL,(0,v.iK)(s),{headers:{"X-Customer-Ip":s.ipAddress}}).pipe((0,t.map)(i=>(0,a.l1)(i,"SUCCESS")))).catch(i=>(0,a.rU)(i))}}return u.\u0275fac=function(s){return new(s||u)(e.\u0275\u0275inject(T.HttpClient))},u.\u0275prov=e.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})();var h=r(23604),g=r(74520);let p=(()=>{class u{constructor(s,i,A,I){this.repository=s,this.store=i,this.eventBusService=A,this.customerStore=I}setSource(s,i=!1){try{return c.Either.success(this.store.setSource(s,i))}catch({message:A}){return c.Either.failure({message:A})}}verfiyAccount(s){var i=this;return(0,m.Z)(function*(){try{const A=yield i.requestAccount(s);return i.store.setTagAval(s),c.Either.success(!!A)}catch({message:A}){return c.Either.failure({message:A})}})()}setAmount(s){try{return c.Either.success(this.store.setAmount(s))}catch({message:i}){return c.Either.failure({message:i})}}reset(){try{const s=this.store.itIsFromCustomer(),i=this.store.getSource();return this.store.reset(),c.Either.success({fromCustomer:s,source:i})}catch({message:s}){return c.Either.failure({message:s})}}send(){var s=this;return(0,m.Z)(function*(){const i=s.customerStore.currentState,{session:{ip:A,customer:{clientFirstName:I,clientLastName:C}}}=i;s.store.setIpAddress(A),s.store.setCustomerName({clientFirstName:I,clientLastName:C});const y=(0,v.Ry)(s.store.currentState),M=yield s.execute(y);return s.eventBusService.emit(M.channel),c.Either.success({tagAval:y,status:M})})()}execute(s){try{return this.repository.send(s)}catch({message:i}){return Promise.resolve(_.LN.error(i))}}requestAccount(s){var i=this;return(0,m.Z)(function*(){const A=i.store.getTagAval();let I=i.store.getAccount();const{tagAval:C}=s;return(A?.tagAval!==C||!I)&&([I]=yield i.repository.requestVerifyAccount(C),i.store.setAccount(I)),I})()}setNote(s){try{return c.Either.success(this.store.setNote(s))}catch({message:i}){return c.Either.failure({message:i})}}removeNote(){try{return c.Either.success(this.store.removeNote())}catch({message:s}){return c.Either.failure({message:s})}}}return u.\u0275fac=function(s){return new(s||u)(e.\u0275\u0275inject(n),e.\u0275\u0275inject(h.B),e.\u0275\u0275inject(d.Yd),e.\u0275\u0275inject(g.f))},u.\u0275prov=e.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},32435:(P,f,r)=>{r.d(f,{Z:()=>a});var m=r(30263),d=r(39904),_=r(95437),c=r(90596),v=r(99877);const l=d.Z6.TRANSFERS.GENERIC;let a=(()=>{class o{constructor(e,n,h){this.modalConfirmation=e,this.mboProvider=n,this.managerTagAval=h}execute(e=!0){e?this.confirmation():this.cancelConfirmed(!0)}backCustomer(e=!0){e?this.mboProvider.navigation.back(d.Z6.TRANSFERS.TAG_AVAL.SOURCE):this.backConfirmed(!0)}confirmation(e=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre Tags Aval actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(e)}},decline:{label:"Continuar"}}).then(n=>"accept"===n)}cancelConfirmed(e){const n=this.managerTagAval.reset();e&&n.when({success:({fromCustomer:h,source:g})=>{h?this.mboProvider.navigation.back(d.Z6.CUSTOMER.PRODUCTS.INFO,{productId:g.id}):this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)}})}backConfirmed(e){const n=this.managerTagAval.reset();e&&n.when({success:({fromCustomer:h,source:g})=>{h?this.mboProvider.navigation.back(l.DESTINATION,{productId:g.id}):this.mboProvider.navigation.back(d.Z6.TRANSFERS.TAG_AVAL.SOURCE)},failure:()=>{this.mboProvider.navigation.back(d.Z6.TRANSFERS.HOME)}})}}return o.\u0275fac=function(e){return new(e||o)(v.\u0275\u0275inject(m.$e),v.\u0275\u0275inject(_.ZL),v.\u0275\u0275inject(c.$))},o.\u0275prov=v.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},23604:(P,f,r)=>{r.d(f,{B:()=>l});var m=r(39904),d=r(87956),_=r(20691),v=r(99877);let l=(()=>{class a extends _.Store{constructor(t){super({confirmation:!1,fromCustomer:!1}),t.subscribes(m.PU,()=>{this.reset()})}setIpAddress(t){this.reduce(e=>({...e,ipAddress:t}))}setCustomerName(t){this.reduce(e=>({...e,customerName:t}))}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t,e=!1){this.reduce(n=>({...n,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}setTagAval(t){this.reduce(e=>({...e,destination:t}))}getTagAval(){return this.select(({destination:t})=>t)}setAccount(t){this.reduce(e=>({...e,account:t}))}getAccount(){return this.select(({account:t})=>t)}setAmount(t){this.reduce(e=>({...e,amount:t,confirmation:!0}))}getAmount(){return this.select(({amount:t})=>t)}itIsConfirmation(){return this.select(({confirmation:t})=>t)}setNote(t){this.reduce(e=>({...e,note:t}))}removeNote(){this.reduce(t=>({...t,note:void 0}))}}return a.\u0275fac=function(t){return new(t||a)(v.\u0275\u0275inject(d.Yd))},a.\u0275prov=v.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},77762:(P,f,r)=>{r.r(f),r.d(f,{MboTransferTagAvalSourcePage:()=>h});var m=r(15861),d=r(17007),c=r(30263),v=r(79798),T=r(39904),l=r(95437),a=r(90596),o=r(32435),t=r(99877);const n=T.Z6.TRANSFERS.TAG_AVAL;let h=(()=>{class g{constructor(u,E,s,i){this.mboProvider=u,this.requestConfiguration=E,this.managerTagAval=s,this.cancelProvider=i,this.confirmation=!1,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_transfer-tag-aval-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(u){this.managerTagAval.setSource(u).when({success:()=>{this.mboProvider.navigation.next(n.DESTINATION)}})}initializatedConfiguration(){var u=this;return(0,m.Z)(function*(){(yield u.requestConfiguration.source()).when({success:({products:E})=>{u.products=E}},()=>{u.requesting=!1})})()}}return g.\u0275fac=function(u){return new(u||g)(t.\u0275\u0275directiveInject(l.ZL),t.\u0275\u0275directiveInject(a.N),t.\u0275\u0275directiveInject(a.$),t.\u0275\u0275directiveInject(o.Z))},g.\u0275cmp=t.\u0275\u0275defineComponent({type:g,selectors:[["mbo-transfer-tag-aval-source-page"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[1,"mbo-transfer-tag-aval-source-page__content"],[1,"mbo-transfer-tag-aval-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-transfer-tag-aval-source-page__body"],[3,"skeleton","products","select"]],template:function(u,E){1&u&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),t.\u0275\u0275listener("select",function(i){return E.onProduct(i)}),t.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas transferir hoy? "),t.\u0275\u0275elementEnd()()()),2&u&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("rightAction",E.cancelAction),t.\u0275\u0275advance(2),t.\u0275\u0275property("skeleton",E.requesting)("products",E.products))},dependencies:[d.CommonModule,v.cV,c.Jx],styles:["/*!\n * MBO TransferTagAvalSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 13/Aug/2024\n * Updated: 13/Aug/2024\n*/mbo-transfer-tag-aval-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-tag-aval-source-page .mbo-transfer-tag-aval-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-transfer-tag-aval-source-page .mbo-transfer-tag-aval-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),g})()}}]);