(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9190,2634],{22634:(p,c,o)=>{o.r(c),o.d(c,{Clipboard:()=>u});var l=o(17737),d=o(15861);class s extends l.WebPlugin{write(e){var r=this;return(0,d.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard)throw r.unavailable("Clipboard API not available in this browser");if(void 0!==e.string)yield r.writeText(e.string);else if(e.url)yield r.writeText(e.url);else{if(!e.image)throw new Error("Nothing to write");if(!(typeof ClipboardItem<"u"))throw r.unavailable("Writing images to the clipboard is not supported in this browser");try{const t=yield(yield fetch(e.image)).blob(),i=new ClipboardItem({[t.type]:t});yield navigator.clipboard.write([i])}catch{throw new Error("Failed to write image")}}})()}read(){var e=this;return(0,d.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard)throw e.unavailable("Clipboard API not available in this browser");if(!(typeof ClipboardItem<"u"))return e.readText();try{const r=yield navigator.clipboard.read(),t=r[0].types[0],i=yield r[0].getType(t);return{value:yield e._getBlobData(i,t),type:t}}catch{return e.readText()}})()}readText(){var e=this;return(0,d.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard||!navigator.clipboard.readText)throw e.unavailable("Reading from clipboard not supported in this browser");return{value:yield navigator.clipboard.readText(),type:"text/plain"}})()}writeText(e){var r=this;return(0,d.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard||!navigator.clipboard.writeText)throw r.unavailable("Writting to clipboard not supported in this browser");yield navigator.clipboard.writeText(e)})()}_getBlobData(e,r){return new Promise((t,i)=>{const a=new FileReader;r.includes("image")?a.readAsDataURL(e):a.readAsText(e),a.onloadend=()=>{t(a.result)},a.onerror=n=>{i(n)}})}}const u=(0,l.registerPlugin)("Clipboard",{web:()=>new s})},15861:(p,c,o)=>{function l(s,u,b,e,r,t,i){try{var a=s[t](i),n=a.value}catch(f){return void b(f)}a.done?u(n):Promise.resolve(n).then(e,r)}function d(s){return function(){var u=this,b=arguments;return new Promise(function(e,r){var t=s.apply(u,b);function i(n){l(t,e,r,i,a,"next",n)}function a(n){l(t,e,r,i,a,"throw",n)}i(void 0)})}}o.d(c,{Z:()=>d})}}]);