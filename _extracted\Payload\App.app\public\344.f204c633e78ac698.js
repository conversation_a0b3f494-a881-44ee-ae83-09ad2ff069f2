(self.webpackChunkapp=self.webpackChunkapp||[]).push([[344],{90344:(T,d,a)=>{a.r(d),a.d(d,{MboTransferAdvanceConfirmationPageModule:()=>P});var l=a(17007),m=a(78007),v=a(79798),i=a(30263),f=a(39904),u=a(95437),b=a(64847),p=a(80045),n=a(99877),g=a(10464),h=a(48774),A=a(45542),C=a(17941),M=a(66613);const r=f.Z6.TRANSFERS.ADVANCE;let y=(()=>{class t{constructor(o,e,s){this.mboProvider=o,this.requestConfiguration=e,this.cancelProvider=s,this.backAction={id:"btn_transfer-advance-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(r.AMOUNT)}},this.cancelAction={id:"btn_transfer-advance-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_transfer-advance-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(r.DESTINATION)}}],this.amountActions=[{id:"btn_transfer-advance-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(r.AMOUNT)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(r.RESULT)}initializatedConfiguration(){this.requestConfiguration.confirmation().when({success:({advance:o})=>{this.advance=o}})}}return t.\u0275fac=function(o){return new(o||t)(n.\u0275\u0275directiveInject(u.ZL),n.\u0275\u0275directiveInject(b.m),n.\u0275\u0275directiveInject(p.j))},t.\u0275cmp=n.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfer-advance-confirmation-page"]],decls:23,vars:11,consts:[[1,"mbo-transfer-advance-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfer-advance-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfer-advance-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],["icon","bell",3,"visible"],[1,"mbo-transfer-advance-confirmation-page__footer"],["id","btn_transfer-advance-confirmation_submit","bocc-button","raised","prefixIcon","transaction-next",3,"click"]],template:function(o,e){1&o&&(n.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),n.\u0275\u0275element(3,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),n.\u0275\u0275text(7," \xbfDeseas realizar este avance? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"div",6),n.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),n.\u0275\u0275elementStart(12,"bocc-alert",10),n.\u0275\u0275text(13," Esta transacci\xf3n tiene un costo de "),n.\u0275\u0275elementStart(14,"b"),n.\u0275\u0275text(15,"$ 0"),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(16," y el valor se diferir\xe1 a "),n.\u0275\u0275elementStart(17,"b"),n.\u0275\u0275text(18,"48 cuotas"),n.\u0275\u0275elementEnd()()()()()(),n.\u0275\u0275elementStart(19,"div",11)(20,"button",12),n.\u0275\u0275listener("click",function(){return e.onSubmit()}),n.\u0275\u0275elementStart(21,"span"),n.\u0275\u0275text(22,"Realizar avance"),n.\u0275\u0275elementEnd()()()()),2&o&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("leftAction",e.backAction)("rightAction",e.cancelAction),n.\u0275\u0275advance(6),n.\u0275\u0275property("title",null==e.advance||null==e.advance.destination?null:e.advance.destination.nickname)("subtitle",null==e.advance||null==e.advance.destination?null:e.advance.destination.number)("detail",null==e.advance||null==e.advance.destination?null:e.advance.destination.name)("actions",e.destinationActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==e.advance?null:e.advance.amount)("actions",e.amountActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("title",null==e.advance||null==e.advance.source?null:e.advance.source.nickname)("subtitle",null==e.advance||null==e.advance.source?null:e.advance.source.number),n.\u0275\u0275advance(1),n.\u0275\u0275property("visible",!0))},dependencies:[g.K,h.J,A.P,C.D,M.B],styles:["/*!\n * MBO TransferAdvanceConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 09/Jun/2022\n * Updated: 07/Ene/2024\n*/mbo-transfer-advance-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-advance-confirmation-page .mbo-transfer-advance-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-advance-confirmation-page .mbo-transfer-advance-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfer-advance-confirmation-page .mbo-transfer-advance-confirmation-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-transfer-advance-confirmation-page .mbo-transfer-advance-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-advance-confirmation-page .mbo-transfer-advance-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),t})(),P=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,m.RouterModule.forChild([{path:"",component:y}]),v.KI,i.Jx,i.P8,i.DM,i.B4,i.Dj]}),t})()}}]);