(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6552],{26552:(Yt,at,ut)=>{ut.r(at),ut.d(at,{JSEncrypt:()=>Et,default:()=>$t});var xt="0123456789abcdefghijklmnopqrstuvwxyz";function A(r){return xt.charAt(r)}function Rt(r,t){return r&t}function j(r,t){return r|t}function lt(r,t){return r^t}function ct(r,t){return r&~t}function Bt(r){if(0==r)return-1;var t=0;return 65535&r||(r>>=16,t+=16),255&r||(r>>=8,t+=8),15&r||(r>>=4,t+=4),3&r||(r>>=2,t+=2),1&r||++t,t}function At(r){for(var t=0;0!=r;)r&=r-1,++t;return t}var M="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",pt="=";function z(r){var t,e,i="";for(t=0;t+3<=r.length;t+=3)e=parseInt(r.substring(t,t+3),16),i+=M.charAt(e>>6)+M.charAt(63&e);for(t+1==r.length?(e=parseInt(r.substring(t,t+1),16),i+=M.charAt(e<<2)):t+2==r.length&&(e=parseInt(r.substring(t,t+2),16),i+=M.charAt(e>>2)+M.charAt((3&e)<<4));(3&i.length)>0;)i+=pt;return i}function et(r){var e,t="",i=0,n=0;for(e=0;e<r.length&&r.charAt(e)!=pt;++e){var s=M.indexOf(r.charAt(e));s<0||(0==i?(t+=A(s>>2),n=3&s,i=1):1==i?(t+=A(n<<2|s>>4),n=15&s,i=2):2==i?(t+=A(n),t+=A(s>>2),n=3&s,i=3):(t+=A(n<<2|s>>4),t+=A(15&s),i=0))}return 1==i&&(t+=A(n<<2)),t}var C,N,it={decode:function(r){var t;if(void 0===N){var i="= \f\n\r\t\xa0\u2028\u2029";for(N=Object.create(null),t=0;t<64;++t)N["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(N["-"]=62,N._=63,t=0;t<i.length;++t)N[i.charAt(t)]=-1}var n=[],s=0,h=0;for(t=0;t<r.length;++t){var o=r.charAt(t);if("="==o)break;if(-1!=(o=N[o])){if(void 0===o)throw new Error("Illegal character at offset "+t);s|=o,++h>=4?(n[n.length]=s>>16,n[n.length]=s>>8&255,n[n.length]=255&s,s=0,h=0):s<<=6}}switch(h){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:n[n.length]=s>>10;break;case 3:n[n.length]=s>>16,n[n.length]=s>>8&255}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(r){var t=it.re.exec(r);if(t)if(t[1])r=t[1];else{if(!t[2])throw new Error("RegExp out of sync");r=t[2]}return it.decode(r)}},H=1e13,k=function(){function r(t){this.buf=[+t||0]}return r.prototype.mulAdd=function(t,e){var s,h,i=this.buf,n=i.length;for(s=0;s<n;++s)(h=i[s]*t+e)<H?e=0:h-=(e=0|h/H)*H,i[s]=h;e>0&&(i[s]=e)},r.prototype.sub=function(t){var n,s,e=this.buf,i=e.length;for(n=0;n<i;++n)(s=e[n]-t)<0?(s+=H,t=1):t=0,e[n]=s;for(;0===e[e.length-1];)e.pop()},r.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,i=e[e.length-1].toString(),n=e.length-2;n>=0;--n)i+=(H+e[n]).toString().substring(1);return i},r.prototype.valueOf=function(){for(var t=this.buf,e=0,i=t.length-1;i>=0;--i)e=e*H+t[i];return e},r.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},r}(),vt="\u2026",Vt=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,It=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function q(r,t){return r.length>t&&(r=r.substring(0,t)+vt),r}var O,rt=function(){function r(t,e){this.hexDigits="0123456789ABCDEF",t instanceof r?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}return r.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},r.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},r.prototype.hexDump=function(t,e,i){for(var n="",s=t;s<e;++s)if(n+=this.hexByte(this.get(s)),!0!==i)switch(15&s){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n},r.prototype.isASCII=function(t,e){for(var i=t;i<e;++i){var n=this.get(i);if(n<32||n>176)return!1}return!0},r.prototype.parseStringISO=function(t,e){for(var i="",n=t;n<e;++n)i+=String.fromCharCode(this.get(n));return i},r.prototype.parseStringUTF=function(t,e){for(var i="",n=t;n<e;){var s=this.get(n++);i+=String.fromCharCode(s<128?s:s>191&&s<224?(31&s)<<6|63&this.get(n++):(15&s)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return i},r.prototype.parseStringBMP=function(t,e){for(var n,s,i="",h=t;h<e;)n=this.get(h++),s=this.get(h++),i+=String.fromCharCode(n<<8|s);return i},r.prototype.parseTime=function(t,e,i){var n=this.parseStringISO(t,e),s=(i?Vt:It).exec(n);return s?(i&&(s[1]=+s[1],s[1]+=+s[1]<70?2e3:1900),n=s[1]+"-"+s[2]+"-"+s[3]+" "+s[4],s[5]&&(n+=":"+s[5],s[6]&&(n+=":"+s[6],s[7]&&(n+="."+s[7]))),s[8]&&(n+=" UTC","Z"!=s[8]&&(n+=s[8],s[9]&&(n+=":"+s[9]))),n):"Unrecognized time: "+n},r.prototype.parseInteger=function(t,e){for(var h,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0==(h=e-t))return n?-1:0;if(h>4){for(o=i,h<<=3;!(128&(+o^s));)o=+o<<1,--h;o="("+h+" bit)\n"}n&&(i-=256);for(var f=new k(i),u=t+1;u<e;++u)f.mulAdd(256,this.get(u));return o+f.toString()},r.prototype.parseBitString=function(t,e,i){for(var n=this.get(t),h="("+((e-t-1<<3)-n)+" bit)\n",o="",f=t+1;f<e;++f){for(var u=this.get(f),l=f==e-1?n:0,v=7;v>=l;--v)o+=u>>v&1?"1":"0";if(o.length>i)return h+q(o,i)}return h+o},r.prototype.parseOctetString=function(t,e,i){if(this.isASCII(t,e))return q(this.parseStringISO(t,e),i);var n=e-t,s="("+n+" byte)\n";n>(i/=2)&&(e=t+i);for(var h=t;h<e;++h)s+=this.hexByte(this.get(h));return n>i&&(s+=vt),s},r.prototype.parseOID=function(t,e,i){for(var n="",s=new k,h=0,o=t;o<e;++o){var f=this.get(o);if(s.mulAdd(128,127&f),h+=7,!(128&f)){if(""===n)if((s=s.simplify())instanceof k)s.sub(80),n="2."+s.toString();else{var u=s<80?s<40?0:1:2;n=u+"."+(s-40*u)}else n+="."+s.toString();if(n.length>i)return q(n,i);s=new k,h=0}}return h>0&&(n+=".incomplete"),n},r}(),Nt=function(){function r(t,e,i,n,s){if(!(n instanceof gt))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=i,this.tag=n,this.sub=s}return r.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},r.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),i=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+i);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+i,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);case 6:return this.stream.parseOID(e,e+i,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return q(this.stream.parseStringUTF(e,e+i),t);case 18:case 19:case 20:case 21:case 22:case 26:return q(this.stream.parseStringISO(e,e+i),t);case 30:return q(this.stream.parseStringBMP(e,e+i),t);case 23:case 24:return this.stream.parseTime(e,e+i,23==this.tag.tagNumber)}return null},r.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},r.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":this.tag.isUniversal()&&(3==this.tag.tagNumber||4==this.tag.tagNumber)&&null!==this.sub&&(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var i=0,n=this.sub.length;i<n;++i)e+=this.sub[i].toPrettyString(t)}return e},r.prototype.posStart=function(){return this.stream.pos},r.prototype.posContent=function(){return this.stream.pos+this.header},r.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},r.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},r.decodeLength=function(t){var e=t.get(),i=127&e;if(i==e)return i;if(i>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===i)return null;e=0;for(var n=0;n<i;++n)e=256*e+t.get();return e},r.prototype.getHexStringValue=function(){return this.toHexString().substr(2*this.header,2*this.length)},r.decode=function(t){var e;e=t instanceof rt?t:new rt(t,0);var i=new rt(e),n=new gt(e),s=r.decodeLength(e),h=e.pos,o=h-i.pos,f=null,u=function(){var v=[];if(null!==s){for(var d=h+s;e.pos<d;)v[v.length]=r.decode(e);if(e.pos!=d)throw new Error("Content size is not correct for container starting at offset "+h)}else try{for(;;){var y=r.decode(e);if(y.tag.isEOC())break;v[v.length]=y}s=h-e.pos}catch(b){throw new Error("Exception while decoding undefined length content: "+b)}return v};if(n.tagConstructed)f=u();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=e.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");f=u();for(var l=0;l<f.length;++l)if(f[l].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{f=null}if(null===f){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+h);e.pos=h+Math.abs(s)}return new r(i,o,s,n,f)},r}(),gt=function(){function r(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){var i=new k;do{e=t.get(),i.mulAdd(128,127&e)}while(128&e);this.tagNumber=i.simplify()}}return r.prototype.isUniversal=function(){return 0===this.tagClass},r.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},r}(),E=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Mt=(1<<26)/E[E.length-1],c=function(){function r(t,e,i){null!=t&&("number"==typeof t?this.fromNumber(t,e,i):this.fromString(t,null==e&&"string"!=typeof t?256:e))}return r.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var n,i=(1<<e)-1,s=!1,h="",o=this.t,f=this.DB-o*this.DB%e;if(o-- >0)for(f<this.DB&&(n=this[o]>>f)>0&&(s=!0,h=A(n));o>=0;)f<e?(n=(this[o]&(1<<f)-1)<<e-f,n|=this[--o]>>(f+=this.DB-e)):(n=this[o]>>(f-=e)&i,f<=0&&(f+=this.DB,--o)),n>0&&(s=!0),s&&(h+=A(n));return s?h:"0"},r.prototype.negate=function(){var t=p();return r.ZERO.subTo(this,t),t},r.prototype.abs=function(){return this.s<0?this.negate():this},r.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var i=this.t;if(0!=(e=i-t.t))return this.s<0?-e:e;for(;--i>=0;)if(0!=(e=this[i]-t[i]))return e;return 0},r.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+G(this[this.t-1]^this.s&this.DM)},r.prototype.mod=function(t){var e=p();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(r.ZERO)>0&&t.subTo(e,e),e},r.prototype.modPowInt=function(t,e){var i;return i=t<256||e.isEven()?new yt(e):new St(e),this.exp(t,i)},r.prototype.clone=function(){var t=p();return this.copyTo(t),t},r.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},r.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},r.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},r.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},r.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var n,i=this.DB-t*this.DB%8,s=0;if(t-- >0)for(i<this.DB&&(n=this[t]>>i)!=(this.s&this.DM)>>i&&(e[s++]=n|this.s<<this.DB-i);t>=0;)i<8?(n=(this[t]&(1<<i)-1)<<8-i,n|=this[--t]>>(i+=this.DB-8)):(n=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&n&&(n|=-256),0==s&&(128&this.s)!=(128&n)&&++s,(s>0||n!=this.s)&&(e[s++]=n);return e},r.prototype.equals=function(t){return 0==this.compareTo(t)},r.prototype.min=function(t){return this.compareTo(t)<0?this:t},r.prototype.max=function(t){return this.compareTo(t)>0?this:t},r.prototype.and=function(t){var e=p();return this.bitwiseTo(t,Rt,e),e},r.prototype.or=function(t){var e=p();return this.bitwiseTo(t,j,e),e},r.prototype.xor=function(t){var e=p();return this.bitwiseTo(t,lt,e),e},r.prototype.andNot=function(t){var e=p();return this.bitwiseTo(t,ct,e),e},r.prototype.not=function(){for(var t=p(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},r.prototype.shiftLeft=function(t){var e=p();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},r.prototype.shiftRight=function(t){var e=p();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},r.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+Bt(this[t]);return this.s<0?this.t*this.DB:-1},r.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,i=0;i<this.t;++i)t+=At(this[i]^e);return t},r.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},r.prototype.setBit=function(t){return this.changeBit(t,j)},r.prototype.clearBit=function(t){return this.changeBit(t,ct)},r.prototype.flipBit=function(t){return this.changeBit(t,lt)},r.prototype.add=function(t){var e=p();return this.addTo(t,e),e},r.prototype.subtract=function(t){var e=p();return this.subTo(t,e),e},r.prototype.multiply=function(t){var e=p();return this.multiplyTo(t,e),e},r.prototype.divide=function(t){var e=p();return this.divRemTo(t,e,null),e},r.prototype.remainder=function(t){var e=p();return this.divRemTo(t,null,e),e},r.prototype.divideAndRemainder=function(t){var e=p(),i=p();return this.divRemTo(t,e,i),[e,i]},r.prototype.modPow=function(t,e){var n,h,i=t.bitLength(),s=V(1);if(i<=0)return s;n=i<18?1:i<48?3:i<144?4:i<768?5:6,h=i<8?new yt(e):e.isEven()?new Ht(e):new St(e);var o=[],f=3,u=n-1,l=(1<<n)-1;if(o[1]=h.convert(this),n>1){var v=p();for(h.sqrTo(o[1],v);f<=l;)o[f]=p(),h.mulTo(v,o[f-2],o[f]),f+=2}var y,w,d=t.t-1,b=!0,T=p();for(i=G(t[d])-1;d>=0;){for(i>=u?y=t[d]>>i-u&l:(y=(t[d]&(1<<i+1)-1)<<u-i,d>0&&(y|=t[d-1]>>this.DB+i-u)),f=n;!(1&y);)y>>=1,--f;if((i-=f)<0&&(i+=this.DB,--d),b)o[y].copyTo(s),b=!1;else{for(;f>1;)h.sqrTo(s,T),h.sqrTo(T,s),f-=2;f>0?h.sqrTo(s,T):(w=s,s=T,T=w),h.mulTo(T,o[y],s)}for(;d>=0&&!(t[d]&1<<i);)h.sqrTo(s,T),w=s,s=T,T=w,--i<0&&(i=this.DB-1,--d)}return h.revert(s)},r.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return r.ZERO;for(var i=t.clone(),n=this.clone(),s=V(1),h=V(0),o=V(0),f=V(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),e?((!s.isEven()||!h.isEven())&&(s.addTo(this,s),h.subTo(t,h)),s.rShiftTo(1,s)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);for(;n.isEven();)n.rShiftTo(1,n),e?((!o.isEven()||!f.isEven())&&(o.addTo(this,o),f.subTo(t,f)),o.rShiftTo(1,o)):f.isEven()||f.subTo(t,f),f.rShiftTo(1,f);i.compareTo(n)>=0?(i.subTo(n,i),e&&s.subTo(o,s),h.subTo(f,h)):(n.subTo(i,n),e&&o.subTo(s,o),f.subTo(h,f))}return 0!=n.compareTo(r.ONE)?r.ZERO:f.compareTo(t)>=0?f.subtract(t):f.signum()<0?(f.addTo(t,f),f.signum()<0?f.add(t):f):f},r.prototype.pow=function(t){return this.exp(t,new Ct)},r.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(e.compareTo(i)<0){var n=e;e=i,i=n}var s=e.getLowestSetBit(),h=i.getLowestSetBit();if(h<0)return e;for(s<h&&(h=s),h>0&&(e.rShiftTo(h,e),i.rShiftTo(h,i));e.signum()>0;)(s=e.getLowestSetBit())>0&&e.rShiftTo(s,e),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),e.compareTo(i)>=0?(e.subTo(i,e),e.rShiftTo(1,e)):(i.subTo(e,i),i.rShiftTo(1,i));return h>0&&i.lShiftTo(h,i),i},r.prototype.isProbablePrime=function(t){var e,i=this.abs();if(1==i.t&&i[0]<=E[E.length-1]){for(e=0;e<E.length;++e)if(i[0]==E[e])return!0;return!1}if(i.isEven())return!1;for(e=1;e<E.length;){for(var n=E[e],s=e+1;s<E.length&&n<Mt;)n*=E[s++];for(n=i.modInt(n);e<s;)if(n%E[e++]==0)return!1}return i.millerRabin(t)},r.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},r.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},r.prototype.fromString=function(t,e){var i;if(16==e)i=4;else if(8==e)i=3;else if(256==e)i=8;else if(2==e)i=1;else if(32==e)i=5;else{if(4!=e)return void this.fromRadix(t,e);i=2}this.t=0,this.s=0;for(var n=t.length,s=!1,h=0;--n>=0;){var o=8==i?255&+t[n]:mt(t,n);o<0?"-"==t.charAt(n)&&(s=!0):(s=!1,0==h?this[this.t++]=o:h+i>this.DB?(this[this.t-1]|=(o&(1<<this.DB-h)-1)<<h,this[this.t++]=o>>this.DB-h):this[this.t-1]|=o<<h,(h+=i)>=this.DB&&(h-=this.DB))}8==i&&128&+t[0]&&(this.s=-1,h>0&&(this[this.t-1]|=(1<<this.DB-h)-1<<h)),this.clamp(),s&&r.ZERO.subTo(this,this)},r.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},r.prototype.dlShiftTo=function(t,e){var i;for(i=this.t-1;i>=0;--i)e[i+t]=this[i];for(i=t-1;i>=0;--i)e[i]=0;e.t=this.t+t,e.s=this.s},r.prototype.drShiftTo=function(t,e){for(var i=t;i<this.t;++i)e[i-t]=this[i];e.t=Math.max(this.t-t,0),e.s=this.s},r.prototype.lShiftTo=function(t,e){for(var i=t%this.DB,n=this.DB-i,s=(1<<n)-1,h=Math.floor(t/this.DB),o=this.s<<i&this.DM,f=this.t-1;f>=0;--f)e[f+h+1]=this[f]>>n|o,o=(this[f]&s)<<i;for(f=h-1;f>=0;--f)e[f]=0;e[h]=o,e.t=this.t+h+1,e.s=this.s,e.clamp()},r.prototype.rShiftTo=function(t,e){e.s=this.s;var i=Math.floor(t/this.DB);if(i>=this.t)e.t=0;else{var n=t%this.DB,s=this.DB-n,h=(1<<n)-1;e[0]=this[i]>>n;for(var o=i+1;o<this.t;++o)e[o-i-1]|=(this[o]&h)<<s,e[o-i]=this[o]>>n;n>0&&(e[this.t-i-1]|=(this.s&h)<<s),e.t=this.t-i,e.clamp()}},r.prototype.subTo=function(t,e){for(var i=0,n=0,s=Math.min(t.t,this.t);i<s;)n+=this[i]-t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n-=t[i],e[i++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[i++]=this.DV+n:n>0&&(e[i++]=n),e.t=i,e.clamp()},r.prototype.multiplyTo=function(t,e){var i=this.abs(),n=t.abs(),s=i.t;for(e.t=s+n.t;--s>=0;)e[s]=0;for(s=0;s<n.t;++s)e[s+i.t]=i.am(0,n[s],e,s,0,i.t);e.s=0,e.clamp(),this.s!=t.s&&r.ZERO.subTo(e,e)},r.prototype.squareTo=function(t){for(var e=this.abs(),i=t.t=2*e.t;--i>=0;)t[i]=0;for(i=0;i<e.t-1;++i){var n=e.am(i,e[i],t,2*i,0,1);(t[i+e.t]+=e.am(i+1,2*e[i],t,2*i+1,n,e.t-i-1))>=e.DV&&(t[i+e.t]-=e.DV,t[i+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(i,e[i],t,2*i,0,1)),t.s=0,t.clamp()},r.prototype.divRemTo=function(t,e,i){var n=t.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return e?.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=p());var h=p(),o=this.s,f=t.s,u=this.DB-G(n[n.t-1]);u>0?(n.lShiftTo(u,h),s.lShiftTo(u,i)):(n.copyTo(h),s.copyTo(i));var l=h.t,v=h[l-1];if(0!=v){var d=v*(1<<this.F1)+(l>1?h[l-2]>>this.F2:0),y=this.FV/d,b=(1<<this.F1)/d,T=1<<this.F2,w=i.t,_=w-l,B=e??p();for(h.dlShiftTo(_,B),i.compareTo(B)>=0&&(i[i.t++]=1,i.subTo(B,i)),r.ONE.dlShiftTo(l,B),B.subTo(h,h);h.t<l;)h[h.t++]=0;for(;--_>=0;){var L=i[--w]==v?this.DM:Math.floor(i[w]*y+(i[w-1]+T)*b);if((i[w]+=h.am(0,L,i,_,0,l))<L)for(h.dlShiftTo(_,B),i.subTo(B,i);i[w]<--L;)i.subTo(B,i)}null!=e&&(i.drShiftTo(l,e),o!=f&&r.ZERO.subTo(e,e)),i.t=l,i.clamp(),u>0&&i.rShiftTo(u,i),o<0&&r.ZERO.subTo(i,i)}}},r.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},r.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},r.prototype.exp=function(t,e){if(t>4294967295||t<1)return r.ONE;var i=p(),n=p(),s=e.convert(this),h=G(t)-1;for(s.copyTo(i);--h>=0;)if(e.sqrTo(i,n),(t&1<<h)>0)e.mulTo(n,s,i);else{var o=i;i=n,n=o}return e.revert(i)},r.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},r.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),i=Math.pow(t,e),n=V(i),s=p(),h=p(),o="";for(this.divRemTo(n,s,h);s.signum()>0;)o=(i+h.intValue()).toString(t).substr(1)+o,s.divRemTo(n,s,h);return h.intValue().toString(t)+o},r.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var i=this.chunkSize(e),n=Math.pow(e,i),s=!1,h=0,o=0,f=0;f<t.length;++f){var u=mt(t,f);u<0?"-"==t.charAt(f)&&0==this.signum()&&(s=!0):(o=e*o+u,++h>=i&&(this.dMultiply(n),this.dAddOffset(o,0),h=0,o=0))}h>0&&(this.dMultiply(Math.pow(e,h)),this.dAddOffset(o,0)),s&&r.ZERO.subTo(this,this)},r.prototype.fromNumber=function(t,e,i){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(r.ONE.shiftLeft(t-1),j,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(r.ONE.shiftLeft(t-1),this);else{var n=[],s=7&t;n.length=1+(t>>3),e.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},r.prototype.bitwiseTo=function(t,e,i){var n,s,h=Math.min(t.t,this.t);for(n=0;n<h;++n)i[n]=e(this[n],t[n]);if(t.t<this.t){for(s=t.s&this.DM,n=h;n<this.t;++n)i[n]=e(this[n],s);i.t=this.t}else{for(s=this.s&this.DM,n=h;n<t.t;++n)i[n]=e(s,t[n]);i.t=t.t}i.s=e(this.s,t.s),i.clamp()},r.prototype.changeBit=function(t,e){var i=r.ONE.shiftLeft(t);return this.bitwiseTo(i,e,i),i},r.prototype.addTo=function(t,e){for(var i=0,n=0,s=Math.min(t.t,this.t);i<s;)n+=this[i]+t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n+=t[i],e[i++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[i++]=n:n<-1&&(e[i++]=this.DV+n),e.t=i,e.clamp()},r.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},r.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},r.prototype.multiplyLowerTo=function(t,e,i){var n=Math.min(this.t+t.t,e);for(i.s=0,i.t=n;n>0;)i[--n]=0;for(var s=i.t-this.t;n<s;++n)i[n+this.t]=this.am(0,t[n],i,n,0,this.t);for(s=Math.min(t.t,e);n<s;++n)this.am(0,t[n],i,n,0,e-n);i.clamp()},r.prototype.multiplyUpperTo=function(t,e,i){--e;var n=i.t=this.t+t.t-e;for(i.s=0;--n>=0;)i[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)i[this.t+n-e]=this.am(e-n,t[n],i,0,0,this.t+n-e);i.clamp(),i.drShiftTo(1,i)},r.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,i=this.s<0?t-1:0;if(this.t>0)if(0==e)i=this[0]%t;else for(var n=this.t-1;n>=0;--n)i=(e*i+this[n])%t;return i},r.prototype.millerRabin=function(t){var e=this.subtract(r.ONE),i=e.getLowestSetBit();if(i<=0)return!1;var n=e.shiftRight(i);(t=t+1>>1)>E.length&&(t=E.length);for(var s=p(),h=0;h<t;++h){s.fromInt(E[Math.floor(Math.random()*E.length)]);var o=s.modPow(n,this);if(0!=o.compareTo(r.ONE)&&0!=o.compareTo(e)){for(var f=1;f++<i&&0!=o.compareTo(e);)if(0==(o=o.modPowInt(2,this)).compareTo(r.ONE))return!1;if(0!=o.compareTo(e))return!1}}return!0},r.prototype.square=function(){var t=p();return this.squareTo(t),t},r.prototype.gcda=function(t,e){var i=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(i.compareTo(n)<0){var s=i;i=n,n=s}var h=i.getLowestSetBit(),o=n.getLowestSetBit();if(o<0)e(i);else{h<o&&(o=h),o>0&&(i.rShiftTo(o,i),n.rShiftTo(o,n));var f=function(){(h=i.getLowestSetBit())>0&&i.rShiftTo(h,i),(h=n.getLowestSetBit())>0&&n.rShiftTo(h,n),i.compareTo(n)>=0?(i.subTo(n,i),i.rShiftTo(1,i)):(n.subTo(i,n),n.rShiftTo(1,n)),i.signum()>0?setTimeout(f,0):(o>0&&n.lShiftTo(o,n),setTimeout(function(){e(n)},0))};setTimeout(f,10)}},r.prototype.fromNumberAsync=function(t,e,i,n){if("number"==typeof e)if(t<2)this.fromInt(1);else{this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(r.ONE.shiftLeft(t-1),j,this),this.isEven()&&this.dAddOffset(1,0);var s=this,h=function(){s.dAddOffset(2,0),s.bitLength()>t&&s.subTo(r.ONE.shiftLeft(t-1),s),s.isProbablePrime(e)?setTimeout(function(){n()},0):setTimeout(h,0)};setTimeout(h,0)}else{var o=[],f=7&t;o.length=1+(t>>3),e.nextBytes(o),f>0?o[0]&=(1<<f)-1:o[0]=0,this.fromString(o,256)}},r}(),Ct=function(){function r(){}return r.prototype.convert=function(t){return t},r.prototype.revert=function(t){return t},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i)},r.prototype.sqrTo=function(t,e){t.squareTo(e)},r}(),yt=function(){function r(t){this.m=t}return r.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},r.prototype.revert=function(t){return t},r.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}(),St=function(){function r(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return r.prototype.convert=function(t){var e=p();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(c.ZERO)>0&&this.m.subTo(e,e),e},r.prototype.revert=function(t){var e=p();return t.copyTo(e),this.reduce(e),e},r.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var i=32767&t[e],n=i*this.mpl+((i*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[i=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[i]>=t.DV;)t[i]-=t.DV,t[++i]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}(),Ht=function(){function r(t){this.m=t,this.r2=p(),this.q3=p(),c.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return r.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=p();return t.copyTo(e),this.reduce(e),e},r.prototype.revert=function(t){return t},r.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}();function p(){return new c(null)}function S(r,t){return new c(r,t)}var Tt=typeof navigator<"u";Tt&&"Microsoft Internet Explorer"==navigator.appName?(c.prototype.am=function(t,e,i,n,s,h){for(var o=32767&e,f=e>>15;--h>=0;){var u=32767&this[t],l=this[t++]>>15,v=f*u+l*o;s=((u=o*u+((32767&v)<<15)+i[n]+(1073741823&s))>>>30)+(v>>>15)+f*l+(s>>>30),i[n++]=1073741823&u}return s},O=30):Tt&&"Netscape"!=navigator.appName?(c.prototype.am=function(t,e,i,n,s,h){for(;--h>=0;){var o=e*this[t++]+i[n]+s;s=Math.floor(o/67108864),i[n++]=67108863&o}return s},O=26):(c.prototype.am=function(t,e,i,n,s,h){for(var o=16383&e,f=e>>14;--h>=0;){var u=16383&this[t],l=this[t++]>>14,v=f*u+l*o;s=((u=o*u+((16383&v)<<14)+i[n]+s)>>28)+(v>>14)+f*l,i[n++]=268435455&u}return s},O=28),c.prototype.DB=O,c.prototype.DM=(1<<O)-1,c.prototype.DV=1<<O,c.prototype.FV=Math.pow(2,52),c.prototype.F1=52-O,c.prototype.F2=2*O-52;var F,D,Z=[];for(F="0".charCodeAt(0),D=0;D<=9;++D)Z[F++]=D;for(F="a".charCodeAt(0),D=10;D<36;++D)Z[F++]=D;for(F="A".charCodeAt(0),D=10;D<36;++D)Z[F++]=D;function mt(r,t){return Z[r.charCodeAt(t)]??-1}function V(r){var t=p();return t.fromInt(r),t}function G(r){var e,t=1;return 0!=(e=r>>>16)&&(r=e,t+=16),0!=(e=r>>8)&&(r=e,t+=8),0!=(e=r>>4)&&(r=e,t+=4),0!=(e=r>>2)&&(r=e,t+=2),0!=(e=r>>1)&&(r=e,t+=1),t}c.ZERO=V(0),c.ONE=V(1);var $,x,qt=function(){function r(){this.i=0,this.j=0,this.S=[]}return r.prototype.init=function(t){var e,i,n;for(e=0;e<256;++e)this.S[e]=e;for(i=0,e=0;e<256;++e)n=this.S[e],this.S[e]=this.S[i=i+this.S[e]+t[e%t.length]&255],this.S[i]=n;this.i=0,this.j=0},r.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},r}(),bt=256,I=null;if(null==I){I=[],x=0;var J=void 0;if(typeof window<"u"&&window.crypto&&window.crypto.getRandomValues){var st=new Uint32Array(256);for(window.crypto.getRandomValues(st),J=0;J<st.length;++J)I[x++]=255&st[J]}var Y=0,X=function(r){if((Y=Y||0)>=256||x>=bt)window.removeEventListener?window.removeEventListener("mousemove",X,!1):window.detachEvent&&window.detachEvent("onmousemove",X);else try{I[x++]=255&r.x+r.y,Y+=1}catch{}};typeof window<"u"&&(window.addEventListener?window.addEventListener("mousemove",X,!1):window.attachEvent&&window.attachEvent("onmousemove",X))}function _t(){if(null==$){for($=function Ft(){return new qt}();x<bt;){var r=Math.floor(65536*Math.random());I[x++]=255&r}for($.init(I),x=0;x<I.length;++x)I[x]=0;x=0}return $.next()}var ot=function(){function r(){}return r.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=_t()},r}(),Kt=function(){function r(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return r.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},r.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),i=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(i)<0;)e=e.add(this.p);return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i)},r.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=S(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},r.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,i=function kt(r,t){if(t<r.length+11)return console.error("Message too long for RSA"),null;for(var e=[],i=r.length-1;i>=0&&t>0;){var n=r.charCodeAt(i--);n<128?e[--t]=n:n>127&&n<2048?(e[--t]=63&n|128,e[--t]=n>>6|192):(e[--t]=63&n|128,e[--t]=n>>6&63|128,e[--t]=n>>12|224)}e[--t]=0;for(var s=new ot,h=[];t>2;){for(h[0]=0;0==h[0];)s.nextBytes(h);e[--t]=h[0]}return e[--t]=2,e[--t]=0,new c(e)}(t,e);if(null==i)return null;var n=this.doPublic(i);if(null==n)return null;for(var s=n.toString(16),h=s.length,o=0;o<2*e-h;o++)s="0"+s;return s},r.prototype.setPrivate=function(t,e,i){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=S(t,16),this.e=parseInt(e,16),this.d=S(i,16)):console.error("Invalid RSA private key")},r.prototype.setPrivateEx=function(t,e,i,n,s,h,o,f){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=S(t,16),this.e=parseInt(e,16),this.d=S(i,16),this.p=S(n,16),this.q=S(s,16),this.dmp1=S(h,16),this.dmq1=S(o,16),this.coeff=S(f,16)):console.error("Invalid RSA private key")},r.prototype.generate=function(t,e){var i=new ot,n=t>>1;this.e=parseInt(e,16);for(var s=new c(e,16);;){for(;this.p=new c(t-n,1,i),0!=this.p.subtract(c.ONE).gcd(s).compareTo(c.ONE)||!this.p.isProbablePrime(10););for(;this.q=new c(n,1,i),0!=this.q.subtract(c.ONE).gcd(s).compareTo(c.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var h=this.p;this.p=this.q,this.q=h}var o=this.p.subtract(c.ONE),f=this.q.subtract(c.ONE),u=o.multiply(f);if(0==u.gcd(s).compareTo(c.ONE)){this.n=this.p.multiply(this.q),this.d=s.modInverse(u),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(f),this.coeff=this.q.modInverse(this.p);break}}},r.prototype.decrypt=function(t){var e=S(t,16),i=this.doPrivate(e);return null==i?null:function Ut(r,t){for(var e=r.toByteArray(),i=0;i<e.length&&0==e[i];)++i;if(e.length-i!=t-1||2!=e[i])return null;for(++i;0!=e[i];)if(++i>=e.length)return null;for(var n="";++i<e.length;){var s=255&e[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&e[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&e[i+1])<<6|63&e[i+2]),i+=2)}return n}(i,this.n.bitLength()+7>>3)},r.prototype.generateAsync=function(t,e,i){var n=new ot,s=t>>1;this.e=parseInt(e,16);var h=new c(e,16),o=this,f=function(){var u=function(){if(o.p.compareTo(o.q)<=0){var d=o.p;o.p=o.q,o.q=d}var y=o.p.subtract(c.ONE),b=o.q.subtract(c.ONE),T=y.multiply(b);0==T.gcd(h).compareTo(c.ONE)?(o.n=o.p.multiply(o.q),o.d=h.modInverse(T),o.dmp1=o.d.mod(y),o.dmq1=o.d.mod(b),o.coeff=o.q.modInverse(o.p),setTimeout(function(){i()},0)):setTimeout(f,0)},l=function(){o.q=p(),o.q.fromNumberAsync(s,1,n,function(){o.q.subtract(c.ONE).gcda(h,function(d){0==d.compareTo(c.ONE)&&o.q.isProbablePrime(10)?setTimeout(u,0):setTimeout(l,0)})})},v=function(){o.p=p(),o.p.fromNumberAsync(t-s,1,n,function(){o.p.subtract(c.ONE).gcda(h,function(d){0==d.compareTo(c.ONE)&&o.p.isProbablePrime(10)?setTimeout(l,0):setTimeout(v,0)})})};setTimeout(v,0)};setTimeout(f,0)},r.prototype.sign=function(t,e,i){var n=function jt(r){return Q[r]||""}(i),h=function Lt(r,t){if(t<r.length+22)return console.error("Message too long for RSA"),null;for(var e=t-r.length-6,i="",n=0;n<e;n+=2)i+="ff";return S("0001"+i+"00"+r,16)}(n+e(t).toString(),this.n.bitLength()/4);if(null==h)return null;var o=this.doPrivate(h);if(null==o)return null;var f=o.toString(16);return 1&f.length?"0"+f:f},r.prototype.verify=function(t,e,i){var n=S(e,16),s=this.doPublic(n);if(null==s)return null;var o=function zt(r){for(var t in Q)if(Q.hasOwnProperty(t)){var e=Q[t],i=e.length;if(r.substr(0,i)==e)return r.substr(i)}return r}(s.toString(16).replace(/^1f+00/,""));return o==i(t).toString()},r}(),Q={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"},m={};m.lang={extend:function(r,t,e){if(!t||!r)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=t.prototype,r.prototype=new i,r.prototype.constructor=r,r.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),e){var n;for(n in e)r.prototype[n]=e[n];var s=function(){},h=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(o,f){for(n=0;n<h.length;n+=1){var u=h[n],l=f[u];"function"==typeof l&&l!=Object.prototype[u]&&(o[u]=l)}})}catch{}s(r.prototype,e)}}};var a={};(typeof a.asn1>"u"||!a.asn1)&&(a.asn1={}),a.asn1.ASN1Util=new function(){this.integerToByteHex=function(r){var t=r.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(r){var t=r.toString(16);if("-"!=t.substr(0,1))t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var i=t.substr(1).length;i%2==1?i+=1:t.match(/^[0-7]/)||(i+=2);for(var n="",s=0;s<i;s++)n+="f";t=new c(n,16).xor(r).add(c.ONE).toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(r,t){return hextopem(r,t)},this.newObject=function(r){var e=a.asn1,i=e.DERBoolean,n=e.DERInteger,s=e.DERBitString,h=e.DEROctetString,o=e.DERNull,f=e.DERObjectIdentifier,u=e.DEREnumerated,l=e.DERUTF8String,v=e.DERNumericString,d=e.DERPrintableString,y=e.DERTeletexString,b=e.DERIA5String,T=e.DERUTCTime,w=e.DERGeneralizedTime,_=e.DERSequence,B=e.DERSet,L=e.DERTaggedObject,W=e.ASN1Util.newObject,Dt=Object.keys(r);if(1!=Dt.length)throw"key of param shall be only one.";var g=Dt[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+g+":"))throw"undefined key: "+g;if("bool"==g)return new i(r[g]);if("int"==g)return new n(r[g]);if("bitstr"==g)return new s(r[g]);if("octstr"==g)return new h(r[g]);if("null"==g)return new o(r[g]);if("oid"==g)return new f(r[g]);if("enum"==g)return new u(r[g]);if("utf8str"==g)return new l(r[g]);if("numstr"==g)return new v(r[g]);if("prnstr"==g)return new d(r[g]);if("telstr"==g)return new y(r[g]);if("ia5str"==g)return new b(r[g]);if("utctime"==g)return new T(r[g]);if("gentime"==g)return new w(r[g]);if("seq"==g){for(var K=r[g],U=[],P=0;P<K.length;P++){var ft=W(K[P]);U.push(ft)}return new _({array:U})}if("set"==g){for(K=r[g],U=[],P=0;P<K.length;P++)ft=W(K[P]),U.push(ft);return new B({array:U})}if("tag"==g){var R=r[g];if("[object Array]"===Object.prototype.toString.call(R)&&3==R.length){var Jt=W(R[2]);return new L({tag:R[0],explicit:R[1],obj:Jt})}var tt={};if(void 0!==R.explicit&&(tt.explicit=R.explicit),void 0!==R.tag&&(tt.tag=R.tag),void 0===R.obj)throw"obj shall be specified for 'tag'.";return tt.obj=W(R.obj),new L(tt)}},this.jsonToASN1HEX=function(r){return this.newObject(r).getEncodedHex()}},a.asn1.ASN1Util.oidHexToInt=function(r){for(var n="",t=parseInt(r.substr(0,2),16),s=(n=Math.floor(t/40)+"."+t%40,""),h=2;h<r.length;h+=2){var f=("00000000"+parseInt(r.substr(h,2),16).toString(2)).slice(-8);s+=f.substr(1,7),"0"==f.substr(0,1)&&(n=n+"."+new c(s,2).toString(10),s="")}return n},a.asn1.ASN1Util.oidIntToHex=function(r){var t=function(o){var f=o.toString(16);return 1==f.length&&(f="0"+f),f},e=function(o){var f="",l=new c(o,10).toString(2),v=7-l.length%7;7==v&&(v=0);for(var d="",y=0;y<v;y++)d+="0";for(l=d+l,y=0;y<l.length-1;y+=7){var b=l.substr(y,7);y!=l.length-7&&(b="1"+b),f+=t(parseInt(b,2))}return f};if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var i="",n=r.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=t(s),n.splice(0,2);for(var h=0;h<n.length;h++)i+=e(n[h]);return i},a.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(typeof this.hV>"u"||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var s=this.hV.length/2,h=s.toString(16);if(h.length%2==1&&(h="0"+h),s<128)return h;var o=h.length/2;if(o>15)throw"ASN.1 length too long to represent by 8x: n = "+s.toString(16);return(128+o).toString(16)+h},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},a.asn1.DERAbstractString=function(r){a.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(i){this.hTLV=null,this.isModified=!0,this.s=i,this.hV=stohex(this.s)},this.setStringHex=function(i){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&("string"==typeof r?this.setString(r):typeof r.str<"u"?this.setString(r.str):typeof r.hex<"u"&&this.setStringHex(r.hex))},m.lang.extend(a.asn1.DERAbstractString,a.asn1.ASN1Object),a.asn1.DERAbstractTime=function(r){a.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(i){return utc=i.getTime()+6e4*i.getTimezoneOffset(),new Date(utc)},this.formatDate=function(i,n,s){var h=this.zeroPadding,o=this.localDateToUTC(i),f=String(o.getFullYear());"utc"==n&&(f=f.substr(2,2));var b=f+h(String(o.getMonth()+1),2)+h(String(o.getDate()),2)+h(String(o.getHours()),2)+h(String(o.getMinutes()),2)+h(String(o.getSeconds()),2);if(!0===s){var T=o.getMilliseconds();if(0!=T){var w=h(String(T),3);b=b+"."+(w=w.replace(/[0]+$/,""))}}return b+"Z"},this.zeroPadding=function(i,n){return i.length>=n?i:new Array(n-i.length+1).join("0")+i},this.getString=function(){return this.s},this.setString=function(i){this.hTLV=null,this.isModified=!0,this.s=i,this.hV=stohex(i)},this.setByDateValue=function(i,n,s,h,o,f){var u=new Date(Date.UTC(i,n-1,s,h,o,f,0));this.setByDate(u)},this.getFreshValueHex=function(){return this.hV}},m.lang.extend(a.asn1.DERAbstractTime,a.asn1.ASN1Object),a.asn1.DERAbstractStructured=function(r){a.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(e){this.hTLV=null,this.isModified=!0,this.asn1Array=e},this.appendASN1Object=function(e){this.hTLV=null,this.isModified=!0,this.asn1Array.push(e)},this.asn1Array=new Array,typeof r<"u"&&typeof r.array<"u"&&(this.asn1Array=r.array)},m.lang.extend(a.asn1.DERAbstractStructured,a.asn1.ASN1Object),a.asn1.DERBoolean=function(){a.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},m.lang.extend(a.asn1.DERBoolean,a.asn1.ASN1Object),a.asn1.DERInteger=function(r){a.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=a.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new c(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.bigint<"u"?this.setByBigInteger(r.bigint):typeof r.int<"u"?this.setByInteger(r.int):"number"==typeof r?this.setByInteger(r):typeof r.hex<"u"&&this.setValueHex(r.hex))},m.lang.extend(a.asn1.DERInteger,a.asn1.ASN1Object),a.asn1.DERBitString=function(r){if(void 0!==r&&typeof r.obj<"u"){var t=a.asn1.ASN1Util.newObject(r.obj);r.hex="00"+t.getEncodedHex()}a.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,i){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+i},this.setByBinaryString=function(e){var i=8-(e=e.replace(/0+$/,"")).length%8;8==i&&(i=0);for(var n=0;n<=i;n++)e+="0";var s="";for(n=0;n<e.length-1;n+=8){var h=e.substr(n,8),o=parseInt(h,2).toString(16);1==o.length&&(o="0"+o),s+=o}this.hTLV=null,this.isModified=!0,this.hV="0"+i+s},this.setByBooleanArray=function(e){for(var i="",n=0;n<e.length;n++)i+=1==e[n]?"1":"0";this.setByBinaryString(i)},this.newFalseArray=function(e){for(var i=new Array(e),n=0;n<e;n++)i[n]=!1;return i},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&("string"==typeof r&&r.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(r):typeof r.hex<"u"?this.setHexValueIncludingUnusedBits(r.hex):typeof r.bin<"u"?this.setByBinaryString(r.bin):typeof r.array<"u"&&this.setByBooleanArray(r.array))},m.lang.extend(a.asn1.DERBitString,a.asn1.ASN1Object),a.asn1.DEROctetString=function(r){if(void 0!==r&&typeof r.obj<"u"){var t=a.asn1.ASN1Util.newObject(r.obj);r.hex=t.getEncodedHex()}a.asn1.DEROctetString.superclass.constructor.call(this,r),this.hT="04"},m.lang.extend(a.asn1.DEROctetString,a.asn1.DERAbstractString),a.asn1.DERNull=function(){a.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},m.lang.extend(a.asn1.DERNull,a.asn1.ASN1Object),a.asn1.DERObjectIdentifier=function(r){var t=function(i){var n=i.toString(16);return 1==n.length&&(n="0"+n),n},e=function(i){var n="",h=new c(i,10).toString(2),o=7-h.length%7;7==o&&(o=0);for(var f="",u=0;u<o;u++)f+="0";for(h=f+h,u=0;u<h.length-1;u+=7){var l=h.substr(u,7);u!=h.length-7&&(l="1"+l),n+=t(parseInt(l,2))}return n};a.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(i){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueOidString=function(i){if(!i.match(/^[0-9.]+$/))throw"malformed oid string: "+i;var n="",s=i.split("."),h=40*parseInt(s[0])+parseInt(s[1]);n+=t(h),s.splice(0,2);for(var o=0;o<s.length;o++)n+=e(s[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(i){var n=a.asn1.x509.OID.name2oid(i);if(""===n)throw"DERObjectIdentifier oidName undefined: "+i;this.setValueOidString(n)},this.getFreshValueHex=function(){return this.hV},void 0!==r&&("string"==typeof r?r.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(r):this.setValueName(r):void 0!==r.oid?this.setValueOidString(r.oid):void 0!==r.hex?this.setValueHex(r.hex):void 0!==r.name&&this.setValueName(r.name))},m.lang.extend(a.asn1.DERObjectIdentifier,a.asn1.ASN1Object),a.asn1.DEREnumerated=function(r){a.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=a.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new c(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.int<"u"?this.setByInteger(r.int):"number"==typeof r?this.setByInteger(r):typeof r.hex<"u"&&this.setValueHex(r.hex))},m.lang.extend(a.asn1.DEREnumerated,a.asn1.ASN1Object),a.asn1.DERUTF8String=function(r){a.asn1.DERUTF8String.superclass.constructor.call(this,r),this.hT="0c"},m.lang.extend(a.asn1.DERUTF8String,a.asn1.DERAbstractString),a.asn1.DERNumericString=function(r){a.asn1.DERNumericString.superclass.constructor.call(this,r),this.hT="12"},m.lang.extend(a.asn1.DERNumericString,a.asn1.DERAbstractString),a.asn1.DERPrintableString=function(r){a.asn1.DERPrintableString.superclass.constructor.call(this,r),this.hT="13"},m.lang.extend(a.asn1.DERPrintableString,a.asn1.DERAbstractString),a.asn1.DERTeletexString=function(r){a.asn1.DERTeletexString.superclass.constructor.call(this,r),this.hT="14"},m.lang.extend(a.asn1.DERTeletexString,a.asn1.DERAbstractString),a.asn1.DERIA5String=function(r){a.asn1.DERIA5String.superclass.constructor.call(this,r),this.hT="16"},m.lang.extend(a.asn1.DERIA5String,a.asn1.DERAbstractString),a.asn1.DERUTCTime=function(r){a.asn1.DERUTCTime.superclass.constructor.call(this,r),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return typeof this.date>"u"&&typeof this.s>"u"&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==r&&(void 0!==r.str?this.setString(r.str):"string"==typeof r&&r.match(/^[0-9]{12}Z$/)?this.setString(r):void 0!==r.hex?this.setStringHex(r.hex):void 0!==r.date&&this.setByDate(r.date))},m.lang.extend(a.asn1.DERUTCTime,a.asn1.DERAbstractTime),a.asn1.DERGeneralizedTime=function(r){a.asn1.DERGeneralizedTime.superclass.constructor.call(this,r),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==r&&(void 0!==r.str?this.setString(r.str):"string"==typeof r&&r.match(/^[0-9]{14}Z$/)?this.setString(r):void 0!==r.hex?this.setStringHex(r.hex):void 0!==r.date&&this.setByDate(r.date),!0===r.millis&&(this.withMillis=!0))},m.lang.extend(a.asn1.DERGeneralizedTime,a.asn1.DERAbstractTime),a.asn1.DERSequence=function(r){a.asn1.DERSequence.superclass.constructor.call(this,r),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},m.lang.extend(a.asn1.DERSequence,a.asn1.DERAbstractStructured),a.asn1.DERSet=function(r){a.asn1.DERSet.superclass.constructor.call(this,r),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++)t.push(this.asn1Array[e].getEncodedHex());return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},typeof r<"u"&&typeof r.sortflag<"u"&&0==r.sortflag&&(this.sortFlag=!1)},m.lang.extend(a.asn1.DERSet,a.asn1.DERAbstractStructured),a.asn1.DERTaggedObject=function(r){a.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,i){this.hT=e,this.isExplicit=t,this.asn1Object=i,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=i.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.tag<"u"&&(this.hT=r.tag),typeof r.explicit<"u"&&(this.isExplicit=r.explicit),typeof r.obj<"u"&&(this.asn1Object=r.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},m.lang.extend(a.asn1.DERTaggedObject,a.asn1.ASN1Object);var ht,r,Zt=(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,n){i.__proto__=n}||function(i,n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(i[s]=n[s])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),wt=function(r){function t(e){var i=r.call(this)||this;return e&&("string"==typeof e?i.parseKey(e):(t.hasPrivateKeyProperty(e)||t.hasPublicKeyProperty(e))&&i.parsePropertiesFrom(e)),i}return Zt(t,r),t.prototype.parseKey=function(e){try{var i=0,n=0,h=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?function(r){var t;if(void 0===C){var e="0123456789ABCDEF",i=" \f\n\r\t\xa0\u2028\u2029";for(C={},t=0;t<16;++t)C[e.charAt(t)]=t;for(e=e.toLowerCase(),t=10;t<16;++t)C[e.charAt(t)]=t;for(t=0;t<i.length;++t)C[i.charAt(t)]=-1}var n=[],s=0,h=0;for(t=0;t<r.length;++t){var o=r.charAt(t);if("="==o)break;if(-1!=(o=C[o])){if(void 0===o)throw new Error("Illegal character at offset "+t);s|=o,++h>=2?(n[n.length]=s,s=0,h=0):s<<=4}}if(h)throw new Error("Hex encoding incomplete: 4 bits missing");return n}(e):it.unarmor(e),o=Nt.decode(h);if(3===o.sub.length&&(o=o.sub[2].sub[0]),9===o.sub.length){i=o.sub[1].getHexStringValue(),this.n=S(i,16),n=o.sub[2].getHexStringValue(),this.e=parseInt(n,16);var f=o.sub[3].getHexStringValue();this.d=S(f,16);var u=o.sub[4].getHexStringValue();this.p=S(u,16);var l=o.sub[5].getHexStringValue();this.q=S(l,16);var v=o.sub[6].getHexStringValue();this.dmp1=S(v,16);var d=o.sub[7].getHexStringValue();this.dmq1=S(d,16);var y=o.sub[8].getHexStringValue();this.coeff=S(y,16)}else{if(2!==o.sub.length)return!1;if(o.sub[0].sub){var T=o.sub[1].sub[0];i=T.sub[0].getHexStringValue(),this.n=S(i,16),n=T.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else i=o.sub[0].getHexStringValue(),this.n=S(i,16),n=o.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new a.asn1.DERInteger({int:0}),new a.asn1.DERInteger({bigint:this.n}),new a.asn1.DERInteger({int:this.e}),new a.asn1.DERInteger({bigint:this.d}),new a.asn1.DERInteger({bigint:this.p}),new a.asn1.DERInteger({bigint:this.q}),new a.asn1.DERInteger({bigint:this.dmp1}),new a.asn1.DERInteger({bigint:this.dmq1}),new a.asn1.DERInteger({bigint:this.coeff})]};return new a.asn1.DERSequence(e).getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return z(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new a.asn1.DERSequence({array:[new a.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new a.asn1.DERNull]}),i=new a.asn1.DERSequence({array:[new a.asn1.DERInteger({bigint:this.n}),new a.asn1.DERInteger({int:this.e})]}),n=new a.asn1.DERBitString({hex:"00"+i.getEncodedHex()});return new a.asn1.DERSequence({array:[e,n]}).getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return z(this.getPublicBaseKey())},t.wordwrap=function(e,i){return i=i||64,e?e.match(RegExp("(.{1,"+i+"})( +|$\n?)|(.{1,"+i+"})","g")).join("\n"):e},t.prototype.getPrivateKey=function(){var e="-----BEGIN RSA PRIVATE KEY-----\n";return(e+=t.wordwrap(this.getPrivateBaseKeyB64())+"\n")+"-----END RSA PRIVATE KEY-----"},t.prototype.getPublicKey=function(){var e="-----BEGIN PUBLIC KEY-----\n";return(e+=t.wordwrap(this.getPublicBaseKeyB64())+"\n")+"-----END PUBLIC KEY-----"},t.hasPublicKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}(Kt),Gt=typeof process<"u"?null===(ht=process.env)||void 0===ht?void 0:ht.npm_package_version:void 0,Et=function(){function r(t){void 0===t&&(t={}),this.default_key_size=(t=t||{}).default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return r.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new wt(t)},r.prototype.setPrivateKey=function(t){this.setKey(t)},r.prototype.setPublicKey=function(t){this.setKey(t)},r.prototype.decrypt=function(t){try{return this.getKey().decrypt(et(t))}catch{return!1}},r.prototype.encrypt=function(t){try{return z(this.getKey().encrypt(t))}catch{return!1}},r.prototype.sign=function(t,e,i){try{return z(this.getKey().sign(t,e,i))}catch{return!1}},r.prototype.verify=function(t,e,i){try{return this.getKey().verify(t,et(e),i)}catch{return!1}},r.prototype.getKey=function(t){if(!this.key){if(this.key=new wt,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},r.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},r.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},r.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},r.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},r.version=Gt,r}();const $t=Et}}]);