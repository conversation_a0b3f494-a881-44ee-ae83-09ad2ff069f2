(self.webpackChunkapp=self.webpackChunkapp||[]).push([[571],{40571:(F,z,t)=>{t.r(z),t.d(z,{MboCustomerTuplusHomePageModule:()=>pe});var c=t(17007),i=t(78007),e=t(30263),o=t(83651),l=t(79798),a=t(29306);class d{constructor(A,C){this.bank=A,this.points=C}}class x{constructor(A,C){this.points=A,this.banks=C}}class m{constructor(A,C,_,D,E,L,V,J){this.uuid=A,this.type=C,this.bank=_,this.points=D,this.amount=E,this.expeditionAt=L,this.observations=V,this.description=J,this.status="Acumulaci\xf3n"===C?"plus":"minus"}}class p extends a.Ay{static empty(){return new p([],0,!0)}}var n=t(99877),r=t(65467),s=t(10634);function f(y,A){1&y&&n.\u0275\u0275element(0,"div",6)}function h(y,A){if(1&y&&n.\u0275\u0275element(0,"img",7),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275property("src",null==C.loyalty?null:C.loyalty.bank.logo,n.\u0275\u0275sanitizeUrl)("alt",null==C.loyalty?null:C.loyalty.bank.name)}}function b(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275pipe(2,"boccNumberFormat"),n.\u0275\u0275elementEnd()),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind1(2,1,null==C.loyalty?null:C.loyalty.points.toString())," ")}}function g(y,A){1&y&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1,"\u2022\u2022\u2022\u2022"),n.\u0275\u0275elementEnd())}let u=(()=>{class y{constructor(){this.skeleton=!1,this.incognito=!1}}return y.\u0275fac=function(C){return new(C||y)},y.\u0275cmp=n.\u0275\u0275defineComponent({type:y,selectors:[["mbo-loyalty-bank"]],inputs:{loyalty:"loyalty",skeleton:"skeleton",incognito:"incognito"},decls:9,vars:7,consts:[["class","mbo-loyalty-bank__skeleton__icon",4,"ngIf"],[3,"src","alt",4,"ngIf"],[1,"mbo-loyalty-bank__info"],[1,"overline-medium","truncate",3,"active"],[1,"subtitle1-medium",3,"active"],[4,"ngIf"],[1,"mbo-loyalty-bank__skeleton__icon"],[3,"src","alt"]],template:function(C,_){1&C&&(n.\u0275\u0275template(0,f,1,0,"div",0),n.\u0275\u0275template(1,h,1,2,"img",1),n.\u0275\u0275elementStart(2,"div",2)(3,"bocc-skeleton-text",3)(4,"label"),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(6,"bocc-skeleton-text",4),n.\u0275\u0275template(7,b,3,3,"span",5),n.\u0275\u0275template(8,g,2,0,"span",5),n.\u0275\u0275elementEnd()()),2&C&&(n.\u0275\u0275property("ngIf",_.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!_.skeleton),n.\u0275\u0275advance(2),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",null==_.loyalty?null:_.loyalty.bank.name," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!_.incognito),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",_.incognito))},dependencies:[c.NgIf,r.D,s.q],styles:["/*!\n * MBO LoyaltyBank Component\n * v1.0.1\n * Author: MB Frontend Developers\n * Created: 04/Mar/2024\n * Updated: 17/Jun/2024\n*/mbo-loyalty-bank{--pvt-span-font-size: var(--subtitle1-size);position:relative;display:flex;padding:var(--sizing-x4);box-sizing:border-box;overflow:hidden;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}mbo-loyalty-bank img{width:var(--sizing-x16);height:var(--sizing-x16);padding:var(--sizing-x1);box-sizing:border-box}mbo-loyalty-bank .mbo-loyalty-bank__info{display:flex;width:calc(100% - var(--sizing-x18));flex-direction:column;row-gap:var(--sizing-x2)}mbo-loyalty-bank .mbo-loyalty-bank__info label{color:var(--color-carbon-lighter-700)}mbo-loyalty-bank .mbo-loyalty-bank__info span{font-size:var(--pvt-span-font-size)}mbo-loyalty-bank .mbo-loyalty-bank__skeleton__icon{width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-carbon-lighter-300);border-radius:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-loyalty-bank{--pvt-span-font-size: var(--sizing-x8)}}\n"],encapsulation:2}),y})(),v=(()=>{class y{}return y.\u0275fac=function(C){return new(C||y)},y.\u0275mod=n.\u0275\u0275defineNgModule({type:y}),y.\u0275inj=n.\u0275\u0275defineInjector({imports:[c.CommonModule,e.Qg,e.Dj,o.P6]}),y})();var T=t(55944);let P=(()=>{class y{constructor(){this.skeleton=!1}}return y.\u0275fac=function(C){return new(C||y)},y.\u0275cmp=n.\u0275\u0275defineComponent({type:y,selectors:[["mbo-loyalty-history"]],inputs:{history:"history",skeleton:"skeleton"},decls:19,vars:14,consts:[[1,"mbo-loyalty-history__content"],[1,"mbo-loyalty-history__line"],[1,"mbo-loyalty-history__points","body2-medium",3,"active"],[1,"mbo-loyalty-history__observations","body2-medium",3,"active"],[3,"active"],[3,"amount"],[1,"mbo-loyalty-history__label","body2-medium",3,"active"]],template:function(C,_){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-skeleton-text",2)(3,"span"),n.\u0275\u0275text(4),n.\u0275\u0275pipe(5,"boccNumberFormat"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(6,"div",1)(7,"bocc-skeleton-text",3)(8,"p"),n.\u0275\u0275text(9),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(10,"bocc-skeleton-text",4),n.\u0275\u0275element(11,"bocc-amount",5),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(12,"div",1)(13,"bocc-skeleton-text",6)(14,"label"),n.\u0275\u0275text(15),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(16,"bocc-skeleton-text",6)(17,"label"),n.\u0275\u0275text(18),n.\u0275\u0275elementEnd()()()()),2&C&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275classMap(null==_.history?null:_.history.status),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind1(5,12,null==_.history?null:_.history.points.toString())," "),n.\u0275\u0275advance(3),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==_.history?null:_.history.observations),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==_.history?null:_.history.amount),n.\u0275\u0275advance(2),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==_.history?null:_.history.expeditionAt.dateFormat),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",_.skeleton),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(null==_.history?null:_.history.bank))},dependencies:[T.Q,r.D,s.q],styles:['/*!\n * MBO LoyaltyHistory Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 04/Mar/2024\n * Updated: 04/Mar/2024\n*/mbo-loyalty-history{--pvt-point-signo: "+";position:relative;width:100%;display:block;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-loyalty-history .mbo-loyalty-history__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x6) 0rem;box-sizing:border-box}mbo-loyalty-history .mbo-loyalty-history__line{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center}mbo-loyalty-history .mbo-loyalty-history__line p,mbo-loyalty-history .mbo-loyalty-history__line bocc-amount{color:var(--color-carbon-lighter-700)}mbo-loyalty-history .mbo-loyalty-history__line label{color:var(--color-carbon-lighter-400)}mbo-loyalty-history .mbo-loyalty-history__points{min-width:25%}mbo-loyalty-history .mbo-loyalty-history__points span:before{content:var(--pvt-point-signo)}mbo-loyalty-history .mbo-loyalty-history__points span.plus{--pvt-point-signo: "+";color:var(--color-semantic-success-700)}mbo-loyalty-history .mbo-loyalty-history__points span.minus{--pvt-point-signo: "-";color:var(--color-semantic-danger-700)}mbo-loyalty-history .mbo-loyalty-history__observations{min-width:50%}mbo-loyalty-history .mbo-loyalty-history__label{min-width:30%}\n'],encapsulation:2}),y})(),M=(()=>{class y{}return y.\u0275fac=function(C){return new(C||y)},y.\u0275mod=n.\u0275\u0275defineNgModule({type:y}),y.\u0275inj=n.\u0275\u0275defineInjector({imports:[c.CommonModule,e.Qg,e.Dj,o.P6]}),y})();var B=t(15861),I=t(39904),S=t(95437),W=t(87956),H=t(98699),R=t(71776),w=t(42168),N=t(53113),j=t(33876);const G=I.xP.map(y=>a.Br.fromId(y)),k={currency:"COP",items:"10",order:"DESC",orderField:"date"};let K=(()=>{class y{constructor(C){this.http=C}request(){return(0,w.firstValueFrom)(this.http.get(I.bV.TUPLUS.CUSTOMER).pipe((0,w.map)(C=>function X(y){const A=G.map(C=>{const _=y.listPartnerMemberStatus.find(({namePartner:D})=>D===C.name);return new d(C,_?+_.partnerBalance:0)});return new x(+y.pointOfService||0,A)}(C))))}transactions(C){if(this.history)return Promise.resolve(this.history);const _=C||I.GW,{end:D,start:E}=_.getFormat();return(0,w.firstValueFrom)(this.remote({...k,page:"0",EndDt:D,StartDt:E}).pipe((0,w.tap)(L=>{L.range=_,this.history=L})))}transactionForUuid(C){return this.history?.requestForUuid(C)}nextPage(){var C=this;return(0,B.Z)(function*(){if(!C.history)return C.transactions().then(({collection:E})=>E);const{end:_,start:D}=C.history.range.getFormat();return(0,w.firstValueFrom)(C.remote({...k,page:C.history.currentPage.toString(),EndDt:_,StartDt:D}).pipe((0,w.map)(({collection:E})=>(C.history.merge(E),C.history.collection))))})()}remote(C){return this.http.get(I.bV.TUPLUS.HISTORY,{params:{...C}}).pipe((0,w.map)(({pagedRows:_,totalPage:D})=>new p(_.map(E=>function q(y){return new m((0,j.v4)(),y.type,y.entity,+y.points,+y.amount,new N.ou(y.date),y.branchName,y.description)}(E)),D)),(0,w.catchError)(_=>{if(this.history)return(0,w.of)(p.empty());throw _}))}}return y.\u0275fac=function(C){return new(C||y)(n.\u0275\u0275inject(R.HttpClient))},y.\u0275prov=n.\u0275\u0275defineInjectable({token:y,factory:y.\u0275fac,providedIn:"root"}),y})(),O=(()=>{class y{constructor(C,_){this.repository=C,this.preferencesService=_}home(){var C=this;return(0,B.Z)(function*(){try{const _=yield C.repository.request(),D=C.preferences(),E=C.repository.transactions();return H.Either.success({loyalty:_,preferences$:D,transactions$:E})}catch({message:_}){return H.Either.failure({message:_})}})()}preferences(){return this.preferencesService.request(),C=>this.preferencesService.subscribe(C)}}return y.\u0275fac=function(C){return new(C||y)(n.\u0275\u0275inject(K),n.\u0275\u0275inject(W.fT))},y.\u0275prov=n.\u0275\u0275defineInjectable({token:y,factory:y.\u0275fac,providedIn:"root"}),y})();var U=t(44926),$=t(66613),Y=t(45542),Q=t(55648),te=t(52701),oe=t(50689);function ne(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275pipe(2,"boccNumberFormat"),n.\u0275\u0275elementEnd()),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind1(2,1,null==C.loyalty?null:C.loyalty.points.toString())," ")}}function re(y,A){1&y&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1,"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"),n.\u0275\u0275elementEnd())}function ie(y,A){1&y&&(n.\u0275\u0275elementStart(0,"bocc-alert",24),n.\u0275\u0275text(1," Puntos m\xednimo: "),n.\u0275\u0275elementStart(2,"b"),n.\u0275\u0275text(3,"2000"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"br"),n.\u0275\u0275text(5,"No cumples con los puntos minimos para redimir, te invitamos a hacer uso de nuestros productos "),n.\u0275\u0275elementEnd()),2&y&&n.\u0275\u0275property("visible",!0)}function ae(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"div",22),n.\u0275\u0275template(1,ie,6,1,"bocc-alert",23),n.\u0275\u0275elementEnd()),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",C.alertEnabled)}}function ce(y,A){if(1&y&&n.\u0275\u0275element(0,"mbo-loyalty-bank",27),2&y){const C=A.$implicit,_=n.\u0275\u0275nextContext(2);n.\u0275\u0275property("loyalty",C)("incognito",_.incognito)}}function se(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"div",25),n.\u0275\u0275template(1,ce,1,2,"mbo-loyalty-bank",26),n.\u0275\u0275elementEnd()),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngForOf",null==C.loyalty?null:C.loyalty.banks)}}function ue(y,A){1&y&&(n.\u0275\u0275elementStart(0,"div",28),n.\u0275\u0275element(1,"mbo-loyalty-bank",29)(2,"mbo-loyalty-bank",29)(3,"mbo-loyalty-bank",29)(4,"mbo-loyalty-bank",29),n.\u0275\u0275elementEnd()),2&y&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0))}function be(y,A){1&y&&n.\u0275\u0275element(0,"mbo-loyalty-history",36),2&y&&n.\u0275\u0275property("history",A.$implicit)}function le(y,A){1&y&&n.\u0275\u0275element(0,"mbo-loyalty-history",29),2&y&&n.\u0275\u0275property("skeleton",!0)}function me(y,A){1&y&&n.\u0275\u0275element(0,"mbo-loyalty-history",29),2&y&&n.\u0275\u0275property("skeleton",!0)}function de(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"mbo-message-empty"),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&y){const C=n.\u0275\u0275nextContext(2);n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",C.msgError," ")}}function Z(y,A){if(1&y&&(n.\u0275\u0275elementStart(0,"div",30)(1,"div",31)(2,"span",32),n.\u0275\u0275text(3,"PUNTOS"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"span",32),n.\u0275\u0275text(5,"VALOR | ENTIDAD"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(6,"div",33),n.\u0275\u0275template(7,be,1,1,"mbo-loyalty-history",34),n.\u0275\u0275template(8,le,1,1,"mbo-loyalty-history",35),n.\u0275\u0275template(9,me,1,1,"mbo-loyalty-history",35),n.\u0275\u0275template(10,de,2,1,"mbo-message-empty",12),n.\u0275\u0275elementEnd()()),2&y){const C=n.\u0275\u0275nextContext();n.\u0275\u0275advance(7),n.\u0275\u0275property("ngForOf",null==C.history?null:C.history.collection),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!C.history),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!C.history),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",C.isEmpty)}}let ee=(()=>{class y{constructor(C,_,D,E){this.ref=C,this.mboProvider=_,this.modalConfirmation=D,this.requestConfiguration=E,this.requesting=!0,this.incognito=!1}ngOnInit(){this.ref.nativeElement.classList.add(I.fc),this.initializatedConfiguration()}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get isEmpty(){return this.history&&(0===this.history.collection.length||this.history.isError)}get alertEnabled(){return!this.requesting&&this.loyalty?.points<2e3}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus movimientos realizados.":"Lo sentimos, por el momento no cuentas con movimientos en tus puntos."}onBack(){this.mboProvider.navigation.back(I.Z6.CUSTOMER.PRODUCTS.HOME)}onRedirectWeb(){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/external-site.svg",title:"SALIENDO DE BANCA M\xd3VIL",message:"Para ver el cat\xe1logo de productos, ser\xe1s dirigido al sitio web de TuPl\xfas.",accept:{label:"Aceptar",click:()=>{this.mboProvider.openUrl(I.BA.TUPLUS)}},decline:{label:"Cancelar"}})}initializatedConfiguration(){var C=this;return(0,B.Z)(function*(){(yield C.requestConfiguration.home()).when({success:({loyalty:_,preferences$:D,transactions$:E})=>{C.loyalty=_,E.then(L=>C.history=L),C.requesting=!1,C.unsubscription=D(({isIncognito:L})=>C.incognito=L)},failure:()=>{C.mboProvider.toast.error("Ocurrio un error al tratar de consultar tus puntos, por favor intente m\xe1s tarde."),C.mboProvider.navigation.back(I.Z6.CUSTOMER.PRODUCTS.HOME)}})})()}}return y.\u0275fac=function(C){return new(C||y)(n.\u0275\u0275directiveInject(n.ElementRef),n.\u0275\u0275directiveInject(S.ZL),n.\u0275\u0275directiveInject(e.$e),n.\u0275\u0275directiveInject(O))},y.\u0275cmp=n.\u0275\u0275defineComponent({type:y,selectors:[["mbo-customer-tuplus-home-page"]],decls:27,vars:9,consts:[[1,"mbo-customer-tuplus-home-page__card"],["color","tuplus",1,"mbo-customer-tuplus-home-page__card__content"],[1,"mbo-customer-tuplus-home-page__card__header"],["id","btn_customer-tuplus-page_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[1,"mbo-customer-tuplus-home-page__card__body"],[1,"mbo-customer-tuplus-home-page__card__body__avatar"],["src","assets/shared/logos/banks/tuplus.svg"],[1,"mbo-customer-tuplus-home-page__card__title","body2-medium"],[1,"mbo-customer-tuplus-home-page__card__footer"],[1,"mbo-customer-tuplus-home-page__card__subtitle","caption-medium"],[1,"mbo-customer-tuplus-home-page__card__amount"],[1,"h5-medium",3,"active"],[4,"ngIf"],[3,"actionMode","disabled"],[1,"mbo-customer-tuplus-home-page__body"],["class","mbo-customer-tuplus-home-page__alert",4,"ngIf"],["class","mbo-customer-tuplus-home-page__banks",4,"ngIf"],["class","mbo-customer-tuplus-home-page__skeletons",4,"ngIf"],["class","mbo-customer-tuplus-home-page__transactions",4,"ngIf"],[1,"mbo-customer-tuplus-home-page__footer"],[1,"mbo-customer-tuplus-home-page__footer__content"],["icon","web-page","bocc-theme","info","label","Portal",3,"click"],[1,"mbo-customer-tuplus-home-page__alert"],["icon","bell",3,"visible",4,"ngIf"],["icon","bell",3,"visible"],[1,"mbo-customer-tuplus-home-page__banks"],[3,"loyalty","incognito",4,"ngFor","ngForOf"],[3,"loyalty","incognito"],[1,"mbo-customer-tuplus-home-page__skeletons"],[3,"skeleton"],[1,"mbo-customer-tuplus-home-page__transactions"],[1,"mbo-customer-tuplus-home-page__transactions__header"],[1,"smalltext-bold"],[1,"mbo-customer-tuplus-home-page__transactions__content"],[3,"history",4,"ngFor","ngForOf"],[3,"skeleton",4,"ngIf"],[3,"history"]],template:function(C,_){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"bocc-card-product-background",1)(2,"div",2)(3,"button",3),n.\u0275\u0275listener("click",function(){return _.onBack()}),n.\u0275\u0275elementStart(4,"span"),n.\u0275\u0275text(5,"Atr\xe1s"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(6,"div",4)(7,"div",5),n.\u0275\u0275element(8,"img",6),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"label",7),n.\u0275\u0275text(10," Puntos tupl\xfas "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(11,"div",8)(12,"label",9),n.\u0275\u0275text(13," Puntos totales "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(14,"div",10)(15,"bocc-skeleton-text",11),n.\u0275\u0275template(16,ne,3,3,"span",12),n.\u0275\u0275template(17,re,2,0,"span",12),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(18,"mbo-button-incognito-mode",13),n.\u0275\u0275elementEnd()()()(),n.\u0275\u0275elementStart(19,"div",14),n.\u0275\u0275template(20,ae,2,1,"div",15),n.\u0275\u0275template(21,se,2,1,"div",16),n.\u0275\u0275template(22,ue,5,4,"div",17),n.\u0275\u0275template(23,Z,11,4,"div",18),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(24,"div",19)(25,"div",20)(26,"mbo-button-diamond-action",21),n.\u0275\u0275listener("click",function(){return _.onRedirectWeb()}),n.\u0275\u0275elementEnd()()()),2&C&&(n.\u0275\u0275advance(15),n.\u0275\u0275property("active",_.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!_.incognito),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",_.incognito),n.\u0275\u0275advance(1),n.\u0275\u0275property("actionMode",!0)("disabled",_.requesting),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",_.alertEnabled),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!_.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",_.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!_.requesting))},dependencies:[c.NgForOf,c.NgIf,U.X,$.B,Y.P,r.D,Q.u,te.q,oe.A,u,P,s.q],styles:["mbo-customer-tuplus-home-page{--pvt-tuplus-footer-height: var(--sizing-x32);position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card{position:relative;width:100%;overflow:hidden}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card .bocc-card-product-background{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:calc(var(--mbo-application-body-safe-spacing) + var(--sizing-x8)) 0rem var(--sizing-x8) 0rem;box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__header{--bocc-button-padding: 0rem var(--sizing-x8) 0rem 0rem;position:relative;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__body__avatar{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__body label{margin:auto 0rem auto var(--sizing-x6);color:var(--bocc-card-product-color-title)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x6) var(--sizing-safe-bottom) var(--sizing-x6);box-sizing:border-box;margin-top:var(--sizing-x10)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__footer label{width:100%;text-align:center}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__amount{position:relative;display:flex;width:100%;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);margin-bottom:var(--sizing-x4)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__amount bocc-skeleton-text{min-width:var(--sizing-x12);color:var(--color-carbon-lighter-50)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__card__amount mbo-button-incognito-mode{width:var(--sizing-x12);height:var(--sizing-x12);color:var(--bocc-card-product-color-button)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x12) 0rem;box-sizing:border-box;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);transform:translateY(calc(var(--sizing-x8) * -1));background:var(--color-carbon-lighter-50);margin-bottom:calc(24rem + var(--sizing-safe-bottom-x12))}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__alert{padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__banks,mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__skeletons{display:grid;row-gap:var(--sizing-x6);-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);grid-template-columns:1fr 1fr;padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__transactions__header{display:flex;justify-content:space-between;align-items:center;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;margin:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__transactions__header span{color:var(--color-carbon-lighter-700)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__transactions__content{padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__footer{position:fixed;bottom:0rem;width:100%;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-customer-tuplus-home-page .mbo-customer-tuplus-home-page__footer__content{position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-tuplus-footer-height);padding:0rem var(--sizing-x4);box-sizing:border-box}@media screen and (max-width: 320px){bocc-customer-tuplus-page{--pvt-tuplus-footer-height: var(--sizing-x28)}}\n"],encapsulation:2}),y})(),pe=(()=>{class y{}return y.\u0275fac=function(C){return new(C||y)},y.\u0275mod=n.\u0275\u0275defineNgModule({type:y}),y.\u0275inj=n.\u0275\u0275defineInjector({imports:[c.CommonModule,i.RouterModule.forChild([{path:"",component:ee}]),e.X6,e.B4,e.P8,e.Dj,o.P6,l.uf,l.qr,l.Aj,v,M]}),y})()},19102:(F,z,t)=>{t.d(z,{r:()=>l});var c=t(17007),e=t(99877);let l=(()=>{class a{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return a.\u0275fac=function(x){return new(x||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(x,m){1&x&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&x&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",m.src,e.\u0275\u0275sanitizeUrl))},dependencies:[c.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),a})()},52701:(F,z,t)=>{t.d(z,{q:()=>a});var c=t(17007),e=t(30263),o=t(99877);let a=(()=>{class d{}return d.\u0275fac=function(m){return new(m||d)},d.\u0275cmp=o.\u0275\u0275defineComponent({type:d,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(m,p){1&m&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"bocc-icon",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()),2&m&&(o.\u0275\u0275classMap(p.classTheme),o.\u0275\u0275advance(3),o.\u0275\u0275property("icon",p.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",p.label," "))},dependencies:[c.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),d})()},55648:(F,z,t)=>{t.d(z,{u:()=>p});var c=t(15861),i=t(17007),o=t(30263),l=t(78506),a=t(99877);function x(n,r){if(1&n){const s=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",2),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(s);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&n){const s=a.\u0275\u0275nextContext();a.\u0275\u0275property("prefixIcon",s.icon)("disabled",s.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate1(" ",s.label," ")}}function m(n,r){if(1&n){const s=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",3),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(s);const h=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(h.onClick())}),a.\u0275\u0275elementEnd()}if(2&n){const s=a.\u0275\u0275nextContext();a.\u0275\u0275property("bocc-button-action",s.icon)("disabled",s.disabled)}}let p=(()=>{class n{constructor(s){this.preferences=s,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:s})=>{this.isIncognito=s||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var s=this;return(0,c.Z)(function*(){yield s.preferences.toggleIncognito()})()}}return n.\u0275fac=function(s){return new(s||n)(a.\u0275\u0275directiveInject(l.Bx))},n.\u0275cmp=a.\u0275\u0275defineComponent({type:n,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(s,f){1&s&&(a.\u0275\u0275template(0,x,3,3,"button",0),a.\u0275\u0275template(1,m,1,2,"button",1)),2&s&&(a.\u0275\u0275property("ngIf",!f.actionMode),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",f.actionMode))},dependencies:[i.CommonModule,i.NgIf,o.P8,o.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),n})()},72765:(F,z,t)=>{t.d(z,{rw:()=>c.r,qr:()=>i.q,uf:()=>e.u,Z:()=>x,t5:()=>h,$O:()=>f});var c=t(19102),i=t(52701),e=t(55648),o=t(17007),l=t(30263),a=t(99877);const d=["*"];let x=(()=>{class b{constructor(){this.disabled=!1}}return b.\u0275fac=function(u){return new(u||b)},b.\u0275cmp=a.\u0275\u0275defineComponent({type:b,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(u,v){1&u&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275element(2,"bocc-icon",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",3),a.\u0275\u0275projection(4),a.\u0275\u0275elementEnd()()),2&u&&(a.\u0275\u0275classProp("mbo-poster__content--disabled",v.disabled),a.\u0275\u0275advance(2),a.\u0275\u0275property("icon",v.icon))},dependencies:[o.CommonModule,l.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),b})();var m=t(33395),p=t(77279),n=t(87903),r=t(87956),s=t(25317);let f=(()=>{class b{constructor(u){this.eventBusService=u}onCopy(){this.value&&((0,n.Bn)(this.value),this.eventBusService.emit(p.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return b.\u0275fac=function(u){return new(u||b)(a.\u0275\u0275directiveInject(r.Yd))},b.\u0275cmp=a.\u0275\u0275defineComponent({type:b,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(u,v){1&u&&(a.\u0275\u0275elementStart(0,"bocc-icon",0),a.\u0275\u0275listener("click",function(){return v.onCopy()}),a.\u0275\u0275elementEnd()),2&u&&a.\u0275\u0275property("id",v.elementId)},dependencies:[o.CommonModule,l.Zl,m.kW,s.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),b})(),h=(()=>{class b{}return b.\u0275fac=function(u){return new(u||b)},b.\u0275cmp=a.\u0275\u0275defineComponent({type:b,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(u,v){1&u&&a.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&u&&(a.\u0275\u0275property("value",v.value),a.\u0275\u0275advance(1),a.\u0275\u0275property("value",v.value))},dependencies:[o.CommonModule,l.qd,f],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),b})()},79798:(F,z,t)=>{t.d(z,{Vc:()=>i.Vc,rw:()=>c.rw,k4:()=>i.k4,qr:()=>c.qr,uf:()=>c.uf,xO:()=>o.x,A6:()=>e.A,tu:()=>s,Tj:()=>f,GI:()=>k,Uy:()=>K,To:()=>G,w7:()=>q,o2:()=>i.o2,B_:()=>i.B_,fi:()=>i.fi,XH:()=>i.XH,cN:()=>i.cN,Aj:()=>i.Aj,J5:()=>i.J5,DB:()=>Q.D,NH:()=>O.N,ES:()=>Y.E,Nu:()=>i.Nu,x6:()=>$.x,KI:()=>te.K,iF:()=>i.iF,u8:()=>oe.u,eM:()=>re.e,ZF:()=>ie.Z,wu:()=>ae.w,$n:()=>ce.$,KN:()=>se.K,cV:()=>le.c,t5:()=>c.t5,$O:()=>c.$O,ZS:()=>me.Z,sO:()=>de.s,bL:()=>C,zO:()=>ne.z});var c=t(72765),i=t(27302),e=t(1027),o=t(7427),a=(t(16442),t(17007)),d=t(30263),x=t(44487),m=t.n(x),p=t(13462),n=t(21498),r=t(99877);let s=(()=>{class _{}return _.\u0275fac=function(E){return new(E||_)},_.\u0275mod=r.\u0275\u0275defineNgModule({type:_}),_.\u0275inj=r.\u0275\u0275defineInjector({imports:[a.CommonModule,p.LottieModule.forRoot({player:()=>m()}),c.rw,d.P8,d.Dj,n.P]}),_})(),f=(()=>{class _{ngBoccPortal(E){this.portal=E}onSubmit(){this.portal?.close()}}return _.\u0275fac=function(E){return new(E||_)},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementStart(3,"label"),r.\u0275\u0275text(4," \xa1Atenci\xf3n! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p"),r.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),r.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"li",4),r.\u0275\u0275text(11,"Transacciones a celulares."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"li",4),r.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"li",4),r.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(16,"p",5),r.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(18,"div",6)(19,"button",7),r.\u0275\u0275listener("click",function(){return L.onSubmit()}),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,"Continuar"),r.\u0275\u0275elementEnd()()())},dependencies:[a.CommonModule,d.Zl,d.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),_})();var h=t(7603),b=t(87956),g=t(74520),u=t(39904),v=t(87903);function P(_,D){if(1&_){const E=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",6)(1,"label",7),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"span",8),r.\u0275\u0275text(4,"Tu gerente asignado (a)"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",8),r.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"button",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(E);const V=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(V.onEmail(V.manager.email))}),r.\u0275\u0275elementStart(8,"span",10),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()()}if(2&_){const E=r.\u0275\u0275nextContext(2);r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",E.manager.name," "),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",E.manager.email," ")}}function M(_,D){if(1&_){const E=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),r.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"button",12),r.\u0275\u0275listener("click",function(V){r.\u0275\u0275restoreView(E);const J=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(J.onRetryManager(V))}),r.\u0275\u0275elementStart(4,"span"),r.\u0275\u0275text(5,"Recargar"),r.\u0275\u0275elementEnd()()()}}function B(_,D){if(1&_&&(r.\u0275\u0275elementStart(0,"div",3),r.\u0275\u0275template(1,P,10,2,"div",4),r.\u0275\u0275template(2,M,6,0,"div",5),r.\u0275\u0275elementEnd()),2&_){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",E.manager),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!E.manager)}}function I(_,D){1&_&&(r.\u0275\u0275elementStart(0,"div",13),r.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),r.\u0275\u0275elementEnd()),2&_&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0))}t(29306);let S=(()=>{class _{constructor(E){this.customerService=E,this.requesting=!1}onRetryManager(E){this.customerService.requestManager(),E.stopPropagation()}onEmail(E){(0,v.Gw)(`mailto:${E}`)}onWhatsapp(){(0,v.Gw)(u.BA.WHATSAPP)}}return _.\u0275fac=function(E){return new(E||_)(r.\u0275\u0275directiveInject(b.vZ))},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,B,3,2,"div",1),r.\u0275\u0275template(2,I,5,4,"div",2),r.\u0275\u0275elementEnd()),2&E&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!L.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",L.requesting))},dependencies:[a.CommonModule,a.NgIf,d.P8,d.Dj,i.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),_})();const W={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},H={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},R={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function N(_,D){if(1&_&&(r.\u0275\u0275elementStart(0,"div",7),r.\u0275\u0275element(1,"mbo-contact-manager",8),r.\u0275\u0275elementEnd()),2&_){const E=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("manager",E.manager)("requesting",E.requesting)}}function j(_,D){if(1&_){const E=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"li",9)(1,"div",10),r.\u0275\u0275listener("click",function(V){const _e=r.\u0275\u0275restoreView(E).$implicit,ge=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(ge.onOption(_e,V))}),r.\u0275\u0275elementStart(2,"label",11),r.\u0275\u0275text(3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",12)(5,"div",13),r.\u0275\u0275element(6,"bocc-icon",14),r.\u0275\u0275elementEnd()()()()}if(2&_){const E=D.$implicit;r.\u0275\u0275property("id",E.id),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",E.label," "),r.\u0275\u0275advance(1),r.\u0275\u0275attribute("bocc-theme",E.boccTheme),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",E.icon)}}let G=(()=>{class _{constructor(E,L,V){this.utagService=E,this.customerStore=L,this.customerService=V,this.isManagerEnabled=!1,this.requesting=!1,this.options=[W,H,R]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:E})=>{this.isManagerEnabled=E?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(L=>{this.manager=L.manager,this.requesting=L.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(E){this.portal=E}onOption(E,L){this.utagService.link("click",E.id),this.portal?.send({action:"option",value:E}),L.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return _.\u0275fac=function(E){return new(E||_)(r.\u0275\u0275directiveInject(h.D),r.\u0275\u0275directiveInject(g.f),r.\u0275\u0275directiveInject(b.vZ))},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-contact-information"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275listener("click",function(){return L.onClose()}),r.\u0275\u0275template(1,N,2,2,"div",1),r.\u0275\u0275elementStart(2,"ul",2),r.\u0275\u0275template(3,j,7,4,"li",3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),r.\u0275\u0275listener("click",function(){return L.onClose()}),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(6,"div",6),r.\u0275\u0275listener("click",function(){return L.onClose()}),r.\u0275\u0275elementEnd()),2&E&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",L.isManagerEnabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngForOf",L.options))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,d.Zl,S],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),_})();var X=t(95437);let q=(()=>{class _{constructor(E,L){this.floatingService=E,this.mboProvider=L,this.contactsFloating=this.floatingService.create(G),this.contactsFloating?.subscribe(({action:V,value:J})=>{"option"===V?this.dispatchOption(J):this.close()})}subscribe(E){this.subscriber=E}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(E){"PQRS"===E.action?this.mboProvider.openUrl(u.BA.PQRS):this.subscriber&&this.subscriber(E)}}return _.\u0275fac=function(E){return new(E||_)(r.\u0275\u0275inject(d.B7),r.\u0275\u0275inject(X.ZL))},_.\u0275prov=r.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})(),k=(()=>{class _{constructor(){this.defenderLineNumber=u._L.DEFENDER_LINE,this.defenderLinePhone=u.WB.DEFENDER_LINE}ngBoccPortal(E){}onEmail(){(0,v.Gw)("mailto:<EMAIL>")}}return _.\u0275fac=function(E){return new(E||_)},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-contact-phones"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275element(1,"mbo-attention-lines-form"),r.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),r.\u0275\u0275element(5,"bocc-icon",4),r.\u0275\u0275elementStart(6,"span",5),r.\u0275\u0275text(7,"Defensor del consumidor financiero"),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),r.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"label",8)(13,"span"),r.\u0275\u0275text(14,"Lorena Cerchar Rosado"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(15,"bocc-badge",9),r.\u0275\u0275text(16," Suplente "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(17,"div",10),r.\u0275\u0275element(18,"bocc-icon",11),r.\u0275\u0275elementStart(19,"div",12)(20,"span",13),r.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(22,"div",10),r.\u0275\u0275element(23,"bocc-icon",14),r.\u0275\u0275elementStart(24,"div",12)(25,"a",15),r.\u0275\u0275text(26),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(27,"span",13),r.\u0275\u0275text(28," Ext. 15318 - 15311 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(29,"div",10),r.\u0275\u0275element(30,"bocc-icon",16),r.\u0275\u0275elementStart(31,"div",12)(32,"span",17),r.\u0275\u0275listener("click",function(){return L.onEmail()}),r.\u0275\u0275text(33," <EMAIL> "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(34,"div",10),r.\u0275\u0275element(35,"bocc-icon",18),r.\u0275\u0275elementStart(36,"div",12)(37,"span",13),r.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),r.\u0275\u0275elementEnd()()()()()()),2&E&&(r.\u0275\u0275advance(25),r.\u0275\u0275property("href",L.defenderLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",L.defenderLineNumber," "))},dependencies:[a.CommonModule,d.Zl,d.Oh,i.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),_})(),K=(()=>{class _{constructor(){this.whatsappNumber=u._L.WHATSAPP}ngBoccPortal(E){}onClick(){(0,v.Gw)(u.BA.WHATSAPP)}}return _.\u0275fac=function(E){return new(E||_)},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",2)(4,"button",3),r.\u0275\u0275listener("click",function(){return L.onClick()}),r.\u0275\u0275elementStart(5,"span"),r.\u0275\u0275text(6),r.\u0275\u0275elementEnd()()()()),2&E&&(r.\u0275\u0275advance(6),r.\u0275\u0275textInterpolate(L.whatsappNumber))},dependencies:[a.CommonModule,d.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),_})();var O=t(10119),$=(t(87677),t(68789)),Y=t(10455),Q=t(91642),te=t(10464),oe=t(75221),ne=t(88649),re=t(13043),ie=t(38116),ae=t(68819),ce=t(19310),se=t(94614),le=(t(70957),t(91248),t(4663)),me=t(13961),de=t(66709),Z=t(24495),ee=t(57544),pe=t(53113);class y extends ee.FormGroup{constructor(){const D=new ee.FormControl("",[Z.zf,Z.O_,Z.Y2,(0,Z.Mv)(24)]),E=new ee.FormControl("",[Z.C1,Z.zf,Z.O_,Z.Y2,(0,Z.Mv)(24)]);super({controls:{description:E,reference:D}}),this.description=E,this.reference=D}setNote(D){this.description.setValue(D?.description),this.reference.setValue(D?.reference)}getNote(){return new pe.$H(this.description.value,this.reference.value)}}function A(_,D){if(1&_&&r.\u0275\u0275element(0,"bocc-input-box",7),2&_){const E=r.\u0275\u0275nextContext();r.\u0275\u0275property("formControl",E.formControls.reference)}}let C=(()=>{class _{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new y}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(E){this.portal=E}}return _.\u0275fac=function(E){return new(E||_)},_.\u0275cmp=r.\u0275\u0275defineComponent({type:_,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(E,L){1&E&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"bocc-input-box",5),r.\u0275\u0275template(7,A,1,1,"bocc-input-box",6),r.\u0275\u0275elementEnd()()),2&E&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",L.cancelAction)("rightAction",L.saveAction),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",L.title," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("formControl",L.formControls.description),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",L.requiredReference))},dependencies:[a.CommonModule,a.NgIf,d.Jx,d.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),_})()},35324:(F,z,t)=>{t.d(z,{V:()=>m});var c=t(17007),e=t(30263),o=t(39904),l=t(87903),a=t(99877);function x(p,n){if(1&p){const r=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"a",9),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(r);const f=a.\u0275\u0275nextContext();return a.\u0275\u0275resetView(f.onWhatsapp())}),a.\u0275\u0275elementStart(1,"div",3),a.\u0275\u0275element(2,"bocc-icon",10),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",5)(4,"label",6),a.\u0275\u0275text(5," Whatsapp "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"label",7),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd()()()}if(2&p){const r=a.\u0275\u0275nextContext();a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",r.whatsappNumber," ")}}let m=(()=>{class p{constructor(){this.whatsapp=!1,this.whatsappNumber=o._L.WHATSAPP,this.nationalLineNumber=o._L.NATIONAL_LINE,this.bogotaLineNumber=o._L.BOGOTA_LINE,this.nationalLinePhone=o.WB.NATIONAL_LINE,this.bogotaLinePhone=o.WB.BOGOTA_LINE}onWhatsapp(){(0,l.Gw)(o.BA.WHATSAPP)}}return p.\u0275fac=function(r){return new(r||p)},p.\u0275cmp=a.\u0275\u0275defineComponent({type:p,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(r,s){1&r&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275template(1,x,8,1,"a",1),a.\u0275\u0275elementStart(2,"a",2)(3,"div",3),a.\u0275\u0275element(4,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(5,"div",5)(6,"label",6),a.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(8,"label",7),a.\u0275\u0275text(9),a.\u0275\u0275elementEnd()()(),a.\u0275\u0275elementStart(10,"a",8)(11,"div",3),a.\u0275\u0275element(12,"bocc-icon",4),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(13,"div",5)(14,"label",6),a.\u0275\u0275text(15," Bogot\xe1 "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(16,"label",7),a.\u0275\u0275text(17),a.\u0275\u0275elementEnd()()()()),2&r&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",s.whatsapp),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",s.nationalLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",s.nationalLineNumber," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("href",s.bogotaLinePhone,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(7),a.\u0275\u0275textInterpolate1(" ",s.bogotaLineNumber," "))},dependencies:[c.CommonModule,c.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),p})()},9593:(F,z,t)=>{t.d(z,{k:()=>x});var c=t(17007),e=t(30263),o=t(39904),l=t(95437),a=t(99877);let x=(()=>{class m{constructor(n){this.mboProvider=n,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(o.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(o.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(o.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return m.\u0275fac=function(n){return new(n||m)(a.\u0275\u0275directiveInject(l.ZL))},m.\u0275cmp=a.\u0275\u0275defineComponent({type:m,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(n,r){1&n&&(a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275listener("click",function(){return r.onProducts()}),a.\u0275\u0275element(3,"bocc-icon",3),a.\u0275\u0275elementStart(4,"label",4),a.\u0275\u0275text(5," Productos "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(6,"div",5),a.\u0275\u0275listener("click",function(){return r.onTransfers()}),a.\u0275\u0275element(7,"bocc-icon",6),a.\u0275\u0275elementStart(8,"label",4),a.\u0275\u0275text(9," Transferir "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(10,"div",7),a.\u0275\u0275listener("click",function(){return r.onPaymentQR()}),a.\u0275\u0275elementStart(11,"div",8)(12,"div",9),a.\u0275\u0275element(13,"bocc-icon",10),a.\u0275\u0275elementEnd()(),a.\u0275\u0275element(14,"bocc-icon",11),a.\u0275\u0275elementStart(15,"label",4),a.\u0275\u0275text(16," Pago QR "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(17,"div",12),a.\u0275\u0275listener("click",function(){return r.onPayments()}),a.\u0275\u0275element(18,"bocc-icon",13),a.\u0275\u0275elementStart(19,"label",4),a.\u0275\u0275text(20," Pagar "),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(21,"div",14),a.\u0275\u0275listener("click",function(){return r.onToken()}),a.\u0275\u0275element(22,"bocc-icon",15),a.\u0275\u0275elementStart(23,"label",4),a.\u0275\u0275text(24," Token "),a.\u0275\u0275elementEnd()()()()),2&n&&(a.\u0275\u0275advance(2),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isProducts),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isTransfers),a.\u0275\u0275advance(11),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isPayments),a.\u0275\u0275advance(4),a.\u0275\u0275classProp("bocc-footer-form__element--active",r.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[c.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),m})()},83867:(F,z,t)=>{t.d(z,{o:()=>b});var c=t(17007),e=t(30263),o=t(8834),l=t(98699),m=(t(57544),t(99877));function n(g,u){if(1&g&&(m.\u0275\u0275elementStart(0,"label",11),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&g){const v=m.\u0275\u0275nextContext();m.\u0275\u0275classProp("mbo-currency-box__rate--active",v.hasValue),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate2(" ",v.valueFormat," ",v.rateCode," ")}}function r(g,u){if(1&g&&(m.\u0275\u0275elementStart(0,"div",12),m.\u0275\u0275element(1,"img",13),m.\u0275\u0275elementEnd()),2&g){const v=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275property("src",v.icon,m.\u0275\u0275sanitizeUrl)}}function s(g,u){if(1&g&&(m.\u0275\u0275elementStart(0,"div",14),m.\u0275\u0275text(1),m.\u0275\u0275elementEnd()),2&g){const v=m.\u0275\u0275nextContext();m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",v.currencyCode," ")}}function f(g,u){if(1&g&&(m.\u0275\u0275elementStart(0,"div",15),m.\u0275\u0275element(1,"bocc-icon",16),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&g){const v=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",null==v.formControl.error?null:v.formControl.error.message," ")}}function h(g,u){if(1&g&&(m.\u0275\u0275elementStart(0,"div",18),m.\u0275\u0275element(1,"bocc-icon",19),m.\u0275\u0275elementStart(2,"span",17),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd()()),2&g){const v=m.\u0275\u0275nextContext();m.\u0275\u0275advance(3),m.\u0275\u0275textInterpolate1(" ",v.helperInfo," ")}}let b=(()=>{class g{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,l.itIsDefined)(this.rate)}get value(){const v=+this.formControl?.value;return isNaN(v)?0:this.hasRate?v/this.rate:0}get valueFormat(){return(0,o.b)({value:this.value,symbol:"$",decimals:!0})}}return g.\u0275fac=function(v){return new(v||g)},g.\u0275cmp=m.\u0275\u0275defineComponent({type:g,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(v,T){1&v&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275template(4,n,2,4,"label",3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(5,"div",4)(6,"div",5),m.\u0275\u0275template(7,r,2,1,"div",6),m.\u0275\u0275element(8,"bocc-currency-field",7),m.\u0275\u0275template(9,s,2,1,"div",8),m.\u0275\u0275elementEnd()(),m.\u0275\u0275template(10,f,4,1,"div",9),m.\u0275\u0275template(11,h,4,1,"div",10),m.\u0275\u0275elementEnd()),2&v&&(m.\u0275\u0275classProp("mbo-currency-box--focused",T.formControl.focused)("mbo-currency-box--error",T.formControl.invalid&&T.formControl.touched)("mbo-currency-box--disabled",T.formControl.disabled||T.disabled),m.\u0275\u0275advance(2),m.\u0275\u0275property("for",T.elementId),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",T.label," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",T.hasRate),m.\u0275\u0275advance(3),m.\u0275\u0275property("ngIf",T.icon),m.\u0275\u0275advance(1),m.\u0275\u0275property("elementId",T.elementId)("placeholder",T.placeholder)("disabled",T.disabled)("formControl",T.formControl),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",T.currencyCode),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",T.formControl.invalid&&T.formControl.touched),m.\u0275\u0275advance(1),m.\u0275\u0275property("ngIf",T.helperInfo&&!(T.formControl.invalid&&T.formControl.touched)))},dependencies:[c.CommonModule,c.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),g})()},85070:(F,z,t)=>{t.d(z,{f:()=>d});var c=t(17007),e=t(78506),o=t(99877);const a=["*"];let d=(()=>{class x{constructor(p){this.session=p}ngOnInit(){this.session.customer().then(p=>this.customer=p)}}return x.\u0275fac=function(p){return new(p||x)(o.\u0275\u0275directiveInject(e._I))},x.\u0275cmp=o.\u0275\u0275defineComponent({type:x,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(p,n){1&p&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",2),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()),2&p&&(o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(null==n.customer?null:n.customer.shortName))},dependencies:[c.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),x})()},65887:(F,z,t)=>{t.d(z,{X:()=>p});var c=t(17007),e=t(99877),l=t(30263),a=t(24495);function m(n,r){1&n&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let p=(()=>{class n{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[a.C1]:[]),this.unsubscription=this.documentType.subscribe(s=>{s&&(this.updateNumber(s,this.required),this.inputType=this.getInputType(s))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(s){if(s.required){const f=s.required.currentValue;this.documentType.setValidators(f?[a.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,f)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(s){return"PA"===s.code?"text":"number"}updateNumber(s,f){const h=this.validatorsForNumber(s,f);this.documentNumber.setValidators(h),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(s,f){return this.validatorsFromType(s).concat(f?[a.C1]:[])}maxLength(s){return f=>f&&f.length>s?{id:"maxLength",message:`Debe tener m\xe1ximo ${s} caracteres`}:null}validatorsFromType(s){switch(s.code){case"PA":return[a.JF];case"NIT":return[a.X1,this.maxLength(15)];default:return[a.X1]}}}return n.\u0275fac=function(s){return new(s||n)},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(s,f){1&s&&(e.\u0275\u0275template(0,m,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&s&&(e.\u0275\u0275property("ngIf",f.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",f.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",f.elementSelectId)("label",f.labelType)("suggestions",f.documents)("disabled",f.disabled)("formControl",f.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",f.elementInputId)("label",f.labelNumber)("type",f.inputType)("disabled",f.disabled)("formControl",f.documentNumber))},dependencies:[c.CommonModule,c.NgIf,l.DT,l.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),n})()},78021:(F,z,t)=>{t.d(z,{c:()=>n});var c=t(17007),e=t(30263),o=t(7603),l=t(98699),d=t(99877);function m(r,s){if(1&r){const f=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",5),d.\u0275\u0275listener("click",function(){d.\u0275\u0275restoreView(f);const b=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(b.onAction(b.leftAction))}),d.\u0275\u0275elementStart(1,"span"),d.\u0275\u0275text(2),d.\u0275\u0275elementEnd()()}if(2&r){const f=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",f.leftAction.id)("bocc-button",f.leftAction.type||"flat")("prefixIcon",f.leftAction.prefixIcon)("disabled",f.itIsDisabled(f.leftAction))("hidden",f.itIsHidden(f.leftAction)),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate(f.leftAction.label)}}function p(r,s){if(1&r){const f=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",6),d.\u0275\u0275listener("click",function(){const g=d.\u0275\u0275restoreView(f).$implicit,u=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(u.onAction(g))}),d.\u0275\u0275elementEnd()}if(2&r){const f=s.$implicit,h=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",f.id)("type",f.type||"flat")("bocc-button-action",f.icon)("disabled",h.itIsDisabled(f))("hidden",h.itIsHidden(f))}}let n=(()=>{class r{constructor(f){this.utagService=f,this.rightActions=[]}itIsDisabled({disabled:f}){return(0,l.evalValueOrFunction)(f)}itIsHidden({hidden:f}){return(0,l.evalValueOrFunction)(f)}onAction(f){const{id:h}=f;h&&this.utagService.link("click",h),f.click()}}return r.\u0275fac=function(f){return new(f||r)(d.\u0275\u0275directiveInject(o.D))},r.\u0275cmp=d.\u0275\u0275defineComponent({type:r,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(f,h){1&f&&(d.\u0275\u0275elementStart(0,"div",0)(1,"div",1),d.\u0275\u0275template(2,m,3,6,"button",2),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(3,"div",3),d.\u0275\u0275template(4,p,1,5,"button",4),d.\u0275\u0275elementEnd()()),2&f&&(d.\u0275\u0275advance(2),d.\u0275\u0275property("ngIf",h.leftAction),d.\u0275\u0275advance(2),d.\u0275\u0275property("ngForOf",h.rightActions))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),r})()},27302:(F,z,t)=>{t.d(z,{Vc:()=>c.V,k4:()=>i.k,o2:()=>e.o,B_:()=>x,fi:()=>m.f,XH:()=>p.X,cN:()=>f.c,Aj:()=>h.A,J5:()=>B.J,Nu:()=>W,iF:()=>q});var c=t(35324),i=t(9593),e=t(83867),o=t(17007),l=t(99877);function d(k,K){if(1&k){const O=l.\u0275\u0275getCurrentView();l.\u0275\u0275elementStart(0,"div",2),l.\u0275\u0275listener("click",function(){const Y=l.\u0275\u0275restoreView(O).$implicit,Q=l.\u0275\u0275nextContext();return l.\u0275\u0275resetView(Q.onClickCurrency(Y))}),l.\u0275\u0275elementStart(1,"div",3),l.\u0275\u0275element(2,"img",4)(3,"img",5),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(4,"label",6),l.\u0275\u0275text(5),l.\u0275\u0275elementEnd()()}if(2&k){const O=K.$implicit,U=l.\u0275\u0275nextContext();l.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",U.isEnabled(O)),l.\u0275\u0275advance(2),l.\u0275\u0275property("src",O.enabledIcon,l.\u0275\u0275sanitizeUrl),l.\u0275\u0275advance(1),l.\u0275\u0275property("src",O.disabledIcon,l.\u0275\u0275sanitizeUrl),l.\u0275\u0275advance(2),l.\u0275\u0275textInterpolate1(" ",O.label," ")}}t(57544);let x=(()=>{class k{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[O]=this.currencies;this.formControl.setValue(O)}}ngOnChanges(O){const{currencies:U}=O;if(U){const[$]=U.currentValue;this.formControl&&this.formControl.setValue($)}}isEnabled(O){return O===this.formControl?.value}onClickCurrency(O){this.formControl&&!this.disabled&&this.formControl.setValue(O)}changeCurriencies(O){if(O.currencies){const U=O.currencies.currentValue,[$]=U;this.formControl&&this.formControl.setValue($)}}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=l.\u0275\u0275defineComponent({type:k,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[l.\u0275\u0275NgOnChangesFeature,l.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(O,U){1&O&&(l.\u0275\u0275elementStart(0,"div",0),l.\u0275\u0275template(1,d,6,5,"div",1),l.\u0275\u0275elementEnd()),2&O&&(l.\u0275\u0275classProp("mbo-currency-toggle--disabled",U.disabled),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngForOf",U.currencies))},dependencies:[o.CommonModule,o.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),k})();var m=t(85070),p=t(65887),n=t(30263),f=t(78021),h=t(50689),u=(t(7603),t(98699),t(72765)),B=t(88014);function I(k,K){if(1&k&&(l.\u0275\u0275elementStart(0,"div",4),l.\u0275\u0275element(1,"img",5),l.\u0275\u0275elementEnd()),2&k){const O=l.\u0275\u0275nextContext();l.\u0275\u0275advance(1),l.\u0275\u0275property("src",O.src,l.\u0275\u0275sanitizeUrl)}}const S=["*"];let W=(()=>{class k{}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=l.\u0275\u0275defineComponent({type:k,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[l.\u0275\u0275StandaloneFeature],ngContentSelectors:S,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(O,U){1&O&&(l.\u0275\u0275projectionDef(),l.\u0275\u0275elementStart(0,"div",0),l.\u0275\u0275template(1,I,2,1,"div",1),l.\u0275\u0275elementStart(2,"div",2)(3,"div",3),l.\u0275\u0275projection(4),l.\u0275\u0275elementEnd()()()),2&O&&(l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",U.src))},dependencies:[o.CommonModule,o.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),k})();var H=t(24495);const R=/[A-Z]/,w=/[a-z]/,N=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,j=k=>k&&!R.test(k)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,G=k=>k&&!w.test(k)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,X=k=>k&&!N.test(k)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let q=(()=>{class k{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([H.C1,G,j,X,(0,H.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const O=this.formControl.errors.reduce(($,{id:Y})=>[...$,Y],[]),U=O.includes("required");this.smallInvalid=O.includes("smallCase")||U,this.capitalInvalid=O.includes("capitalCase")||U,this.specialCharInvalid=O.includes("specialChar")||U,this.minLengthInvalid=O.includes("minlength")||U})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return k.\u0275fac=function(O){return new(O||k)},k.\u0275cmp=l.\u0275\u0275defineComponent({type:k,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[l.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(O,U){1&O&&(l.\u0275\u0275elementStart(0,"div",0),l.\u0275\u0275element(1,"bocc-password-box",1),l.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),l.\u0275\u0275text(4," Min\xfascula "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(5,"mbo-poster",4),l.\u0275\u0275text(6," May\xfascula "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(7,"mbo-poster",5),l.\u0275\u0275text(8," Especial "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(9,"mbo-poster",6),l.\u0275\u0275text(10," Caracteres "),l.\u0275\u0275elementEnd()()()),2&O&&(l.\u0275\u0275advance(1),l.\u0275\u0275property("elementId",U.elementId)("disabled",U.disabled)("formControl",U.formControl),l.\u0275\u0275advance(2),l.\u0275\u0275property("disabled",U.smallInvalid),l.\u0275\u0275advance(2),l.\u0275\u0275property("disabled",U.capitalInvalid),l.\u0275\u0275advance(2),l.\u0275\u0275property("disabled",U.specialCharInvalid),l.\u0275\u0275advance(2),l.\u0275\u0275property("disabled",U.minLengthInvalid))},dependencies:[o.CommonModule,n.sC,u.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),k})()},50689:(F,z,t)=>{t.d(z,{A:()=>a});var c=t(17007),e=t(99877);const l=["*"];let a=(()=>{class d{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return d.\u0275fac=function(m){return new(m||d)},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:l,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(m,p){1&m&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&m&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",p.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[c.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),d})()},88014:(F,z,t)=>{t.d(z,{J:()=>l});var c=t(17007),e=t(99877);let l=(()=>{class a{}return a.\u0275fac=function(x){return new(x||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(x,m){1&x&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[c.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),a})()},21498:(F,z,t)=>{t.d(z,{P:()=>r});var c=t(17007),e=t(30263),o=t(99877);function a(s,f){if(1&s&&o.\u0275\u0275element(0,"bocc-card-product-summary",7),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",h.information.product.color)("icon",h.information.product.icon)("number",h.information.product.number)("title",h.information.product.title)("subtitle",h.information.product.subtitle)}}function d(s,f){if(1&s&&o.\u0275\u0275element(0,"bocc-card-summary",8),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.standard.header)("title",h.information.standard.title)("subtitle",h.information.standard.subtitle)("detail",h.information.standard.detail)}}function x(s,f){if(1&s&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.amount.header)("amount",h.information.amount.value)("symbol",h.information.amount.symbol)("amountSmall",h.information.amount.small)}}function m(s,f){if(1&s&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(h.information.text.content)}}function p(s,f){if(1&s&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",12),o.\u0275\u0275element(1,"bocc-icon",13),o.\u0275\u0275elementStart(2,"span",14),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",15),o.\u0275\u0275elementStart(5,"span",14),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.time," ")}}function n(s,f){if(1&s&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&s){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",h.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",h.information.badge.label," ")}}let r=(()=>{class s{}return s.\u0275fac=function(h){return new(h||s)},s.\u0275cmp=o.\u0275\u0275defineComponent({type:s,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(h,b){1&h&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,a,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,d,1,4,"bocc-card-summary",2),o.\u0275\u0275template(3,x,1,4,"bocc-card-summary",3),o.\u0275\u0275template(4,m,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,p,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,n,3,4,"bocc-card-summary",6),o.\u0275\u0275elementEnd()),2&h&&(o.\u0275\u0275property("ngSwitch",b.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[c.CommonModule,c.NgSwitch,c.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),s})()},7427:(F,z,t)=>{t.d(z,{x:()=>r});var c=t(17007),e=t(30263),o=t(87903),a=(t(29306),t(77279)),d=t(87956),x=t(68789),m=t(13961),p=t(99877);let r=(()=>{class s{constructor(h,b){this.eventBusService=h,this.onboardingScreenService=b,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,o.Bn)(this.product.tagAval),this.eventBusService.emit(a.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(m.Z)),this.tagAvalonboarding.open()}}return s.\u0275fac=function(h){return new(h||s)(p.\u0275\u0275directiveInject(d.Yd),p.\u0275\u0275directiveInject(x.x))},s.\u0275cmp=p.\u0275\u0275defineComponent({type:s,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(h,b){1&h&&(p.\u0275\u0275elementStart(0,"bocc-card-product",0),p.\u0275\u0275listener("key",function(){return b.onTagAval()})("onboarding",function(){return b.onBoarding()}),p.\u0275\u0275elementEnd()),2&h&&(p.\u0275\u0275classMap(b.product.bank.className),p.\u0275\u0275property("iconTitle",b.iconTitle)("title",b.product.nickname||b.product.name)("icon",b.product.logo)("tagAval",b.product.tagAvalFormat)("actions",b.actions)("color",b.product.color)("code",b.product.shortNumber)("label",b.product.label)("amount",b.product.amount)("incognito",b.incognito)("displayCard",!0)("statusLabel",null==b.product.status?null:b.product.status.label)("statusColor",null==b.product.status?null:b.product.status.color)("cromaline",!0)("msgError",b.msgError))},dependencies:[c.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),s})()},1027:(F,z,t)=>{t.d(z,{A:()=>h});var c=t(17007),i=t(72765),e=t(30263),o=t(99877);function l(b,g){if(1&b&&o.\u0275\u0275element(0,"bocc-card-product-summary",8),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",u.information.product.color)("icon",u.information.product.icon)("number",u.information.product.number)("title",u.information.product.title)("subtitle",u.information.product.subtitle)}}function a(b,g){if(1&b&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.standard.header)("title",u.information.standard.title)("subtitle",u.information.standard.subtitle)}}function d(b,g){if(1&b&&o.\u0275\u0275element(0,"bocc-card-summary",10),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.amount.header)("amount",u.information.amount.value)("symbol",u.information.amount.symbol)}}function x(b,g){if(1&b&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(u.information.text.content)}}function m(b,g){if(1&b&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",13),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",16),o.\u0275\u0275elementStart(5,"span",15),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.datetime.time," ")}}function p(b,g){if(1&b&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",17),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd()()),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.date.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",u.information.date.date," ")}}function n(b,g){if(1&b&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&b){const u=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",u.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",u.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",u.information.badge.label," ")}}let r=(()=>{class b{}return b.\u0275fac=function(u){return new(u||b)},b.\u0275cmp=o.\u0275\u0275defineComponent({type:b,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(u,v){1&u&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,l,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,a,1,3,"bocc-card-summary",2),o.\u0275\u0275template(3,d,1,3,"bocc-card-summary",3),o.\u0275\u0275template(4,x,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,m,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,p,4,3,"bocc-card-summary",6),o.\u0275\u0275template(7,n,3,4,"bocc-card-summary",7),o.\u0275\u0275elementEnd()),2&u&&(o.\u0275\u0275property("ngSwitch",v.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","date"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[c.CommonModule,c.NgSwitch,c.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),b})();function s(b,g){1&b&&o.\u0275\u0275element(0,"mbo-card-information-element",8),2&b&&o.\u0275\u0275property("information",g.$implicit)}const f=["*"];let h=(()=>{class b{constructor(){this.skeleton=!1,this.informations=[]}}return b.\u0275fac=function(u){return new(u||b)},b.\u0275cmp=o.\u0275\u0275defineComponent({type:b,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(u,v){1&u&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"mbo-bank-logo",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"div",4),o.\u0275\u0275element(5,"div",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275projection(7),o.\u0275\u0275template(8,s,1,1,"mbo-card-information-element",7),o.\u0275\u0275elementEnd()()),2&u&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("result",!0),o.\u0275\u0275advance(5),o.\u0275\u0275property("ngForOf",v.informations))},dependencies:[c.CommonModule,c.NgForOf,i.rw,r],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),b})()},16442:(F,z,t)=>{t.d(z,{u:()=>u});var c=t(99877),e=t(17007),l=t(13462),d=t(19102),x=t(45542),m=t(65467),p=t(21498);function n(v,T){if(1&v&&(c.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&v){const P=c.\u0275\u0275nextContext();c.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",P.template.skeleton),c.\u0275\u0275property("secondary",!0)("active",P.template.skeleton),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",P.template.header.subtitle," ")}}function r(v,T){1&v&&c.\u0275\u0275element(0,"mbo-card-information",16),2&v&&c.\u0275\u0275property("information",T.$implicit)}function s(v,T){if(1&v&&(c.\u0275\u0275elementStart(0,"div",14),c.\u0275\u0275projection(1),c.\u0275\u0275template(2,r,1,1,"mbo-card-information",15),c.\u0275\u0275elementEnd()),2&v){const P=c.\u0275\u0275nextContext();c.\u0275\u0275advance(2),c.\u0275\u0275property("ngForOf",P.template.informations)}}function f(v,T){1&v&&(c.\u0275\u0275elementStart(0,"div",17),c.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),c.\u0275\u0275elementEnd()),2&v&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0)("secondary",!0),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0)("secondary",!0))}function h(v,T){if(1&v){const P=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",23),c.\u0275\u0275listener("click",function(){const I=c.\u0275\u0275restoreView(P).$implicit,S=c.\u0275\u0275nextContext(2);return c.\u0275\u0275resetView(S.onAction(I))}),c.\u0275\u0275elementStart(1,"span"),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd()()}if(2&v){const P=T.$implicit;c.\u0275\u0275property("bocc-button",P.type)("prefixIcon",P.prefixIcon),c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate(P.label)}}function b(v,T){if(1&v&&(c.\u0275\u0275elementStart(0,"div",21),c.\u0275\u0275template(1,h,3,3,"button",22),c.\u0275\u0275elementEnd()),2&v){const P=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("ngForOf",P.template.actions)}}const g=["*"];let u=(()=>{class v{constructor(){this.disabled=!1,this.action=new c.EventEmitter}onAction({event:P}){this.action.emit(P)}}return v.\u0275fac=function(P){return new(P||v)},v.\u0275cmp=c.\u0275\u0275defineComponent({type:v,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:g,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(P,M){1&P&&(c.\u0275\u0275projectionDef(),c.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),c.\u0275\u0275element(3,"mbo-bank-logo",3),c.\u0275\u0275elementStart(4,"div",4),c.\u0275\u0275element(5,"ng-lottie",5),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),c.\u0275\u0275text(7),c.\u0275\u0275elementEnd(),c.\u0275\u0275template(8,n,2,5,"bocc-skeleton-text",7),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(9,"div",8),c.\u0275\u0275element(10,"div",9),c.\u0275\u0275elementEnd(),c.\u0275\u0275template(11,s,3,1,"div",10),c.\u0275\u0275template(12,f,4,5,"div",11),c.\u0275\u0275elementEnd(),c.\u0275\u0275template(13,b,2,1,"div",12)),2&P&&(c.\u0275\u0275classProp("animation",!M.template.skeleton),c.\u0275\u0275advance(3),c.\u0275\u0275property("result",!0),c.\u0275\u0275advance(2),c.\u0275\u0275property("options",M.template.header.animation),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",M.template.skeleton),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",M.template.header.title," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",M.template.skeleton||M.template.header.subtitle),c.\u0275\u0275advance(3),c.\u0275\u0275property("ngIf",!M.template.skeleton),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",M.template.skeleton),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",M.template.actions.length&&!M.disabled))},dependencies:[e.NgForOf,e.NgIf,l.LottieComponent,d.r,x.P,m.D,p.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),v})()},10119:(F,z,t)=>{t.d(z,{N:()=>f});var c=t(17007),e=t(99877),l=t(30263),a=t(7603),d=t(98699);function m(h,b){if(1&h&&e.\u0275\u0275element(0,"bocc-diamond",14),2&h){const g=b.$implicit,u=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",u.itIsSelected(g))}}function p(h,b){if(1&h){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(g);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const g=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",g.footerActionLeft.id)("bocc-button",g.footerActionLeft.type)("prefixIcon",g.footerActionLeft.prefixIcon)("disabled",g.itIsDisabled(g.footerActionLeft))("hidden",g.itIsHidden(g.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(g.footerActionLeft.label)}}function n(h,b){if(1&h){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(g);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const g=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",g.footerActionRight.id)("bocc-button",g.footerActionRight.type)("prefixIcon",g.footerActionRight.prefixIcon)("disabled",g.itIsDisabled(g.footerActionRight))("hidden",g.itIsHidden(g.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(g.footerActionRight.label)}}const r=["*"];let f=(()=>{class h{constructor(g,u){this.ref=g,this.utagService=u,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((g,u)=>u),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(g){return g===this.currentPosition}itIsDisabled({disabled:g}){return(0,d.evalValueOrFunction)(g)}itIsHidden({hidden:g}){return(0,d.evalValueOrFunction)(g)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(g){const{id:u}=g;u&&this.utagService.link("click",u),g.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(g){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(g),this.automatic=!1,this.setTranslatePosition(g)}setTranslatePosition(g){this.translateX=g*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(g){this.transformContent=`translateX(${g}px)`}emitPosition(g){this.finished||(this.finished=g+1===this.elements.length),this.position.emit({position:g,finished:this.finished})}getPositionSlide(g){return g>=this.elements.length?this.elements.length-1:g<0?0:g}setTouchHandler(g){let u=0,v=0;g.addEventListener("touchstart",T=>{if(T.changedTouches.length){const{clientX:P}=T.changedTouches.item(0);u=0,this.touched=!0,v=P}}),g.addEventListener("touchmove",T=>{if(T.changedTouches.length){const P=T.changedTouches.item(0),M=P.clientX-v;v=P.clientX,this.translateX+=M,u+=M,this.setTranslateContent(this.translateX)}}),g.addEventListener("touchend",T=>{this.touched=!1,T.changedTouches.length&&(Math.abs(u)/this.widthBody*100>=40&&(u>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return h.\u0275fac=function(g){return new(g||h)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(a.D))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(g,u){1&g&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return u.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return u.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,m,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return u.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,p,3,6,"button",13),e.\u0275\u0275template(16,n,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&g&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",u.headerActionLeft)("rightAction",u.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",u.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",u.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",u.widthContent)("transform",u.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",u.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!u.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",u.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!u.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",u.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.footerActionRight))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,l.P8,l.u1,l.ou,l.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),h})()},68789:(F,z,t)=>{t.d(z,{x:()=>a});var c=t(7603),i=t(10455),e=t(87677),o=t(99877);let a=(()=>{class d{constructor(m){this.portalService=m}information(){this.portal||(this.portal=this.portalService.container({component:i.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(m,p){return this.portalService.container({component:m,container:e.C,props:{container:p?.containerProps,component:p?.componentProps}})}}return d.\u0275fac=function(m){return new(m||d)(o.\u0275\u0275inject(c.v))},d.\u0275prov=o.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},87677:(F,z,t)=>{t.d(z,{C:()=>e});var c=t(99877);let e=(()=>{class o{constructor(a){this.ref=a,this.visible=!1,this.visibleChange=new c.EventEmitter}open(a=0){setTimeout(()=>{this.changeVisible(!0)},a)}close(a=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},a)}append(a){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(a)}ngBoccPortal(a){this.portal=a}changeVisible(a){this.visible=a,this.visibleChange.emit(a)}}return o.\u0275fac=function(a){return new(a||o)(c.\u0275\u0275directiveInject(c.ElementRef))},o.\u0275cmp=c.\u0275\u0275defineComponent({type:o,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(a,d){1&a&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275element(1,"div",1),c.\u0275\u0275elementEnd()),2&a&&c.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",d.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),o})()},10455:(F,z,t)=>{t.d(z,{E:()=>d});var c=t(17007),e=t(99877),l=t(27302),a=t(10119);let d=(()=>{class x{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(p){this.portal=p}onPosition({finished:p}){this.finished=p,p&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return x.\u0275fac=function(p){return new(p||x)},x.\u0275cmp=e.\u0275\u0275defineComponent({type:x,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(p,n){1&p&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(s){return n.onPosition(s)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&p&&e.\u0275\u0275property("footerActionLeft",n.footerLeft)("footerActionRight",n.footerRight)("gradient",!0)},dependencies:[c.CommonModule,l.Nu,a.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),x})()},91642:(F,z,t)=>{t.d(z,{D:()=>u});var c=t(17007),e=t(99877),l=t(30263),a=t(87542),d=t(70658),x=t(3372),m=t(87956),p=t(72765);function n(v,T){1&v&&e.\u0275\u0275element(0,"mbo-bank-logo")}function r(v,T){1&v&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function s(v,T){if(1&v&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&v){const P=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",P.verifying)}}function f(v,T){1&v&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function h(v,T){if(1&v){const P=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(P);const B=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(B.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&v){const P=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",P.verifying)}}const b=["*"],{OtpInputSuperuser:g}=x.M;let u=(()=>{class v{constructor(P,M,B,I){this.ref=P,this.otpService=M,this.deviceService=B,this.preferencesService=I,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=a.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new a.yV}ngOnInit(){this.otpService.onCode(P=>{this.otpControls.setCode(P),this.otpControls.valid&&this.onAutocomplete(P)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(P){const{documentNumber:M}=P;M&&this.preferencesService.applyFunctionality(g,M.currentValue).then(B=>{this.itIsDocumentSuperuser=B})}get otpVisible(){return!d.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return d.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&d.N.otpReadonlyMobile}onAutocomplete(P){this.code.emit(P)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return v.\u0275fac=function(P){return new(P||v)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(m.no),e.\u0275\u0275directiveInject(m.U8),e.\u0275\u0275directiveInject(m.yW))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:b,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(P,M){1&P&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,n,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,r,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(I){return M.onAutocomplete(I)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,s,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,f,7,0,"div",8),e.\u0275\u0275template(13,h,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&P&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",M.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",M.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",M.otpVisible),e.\u0275\u0275property("formControls",M.otpControls)("readonly",M.otpReadonly)("mobile",M.otpMobile)("disabled",M.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",M.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",M.isIos))},dependencies:[c.CommonModule,c.NgIf,l.P8,l.Yx,p.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),v})()},10464:(F,z,t)=>{t.d(z,{K:()=>d});var c=t(17007),e=t(99877),l=t(22816);const a=["*"];let d=(()=>{class x{constructor(p){this.ref=p,this.scroller=new l.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(p){this.scroller.reset(p.target)}}return x.\u0275fac=function(p){return new(p||x)(e.\u0275\u0275directiveInject(e.ElementRef))},x.\u0275cmp=e.\u0275\u0275defineComponent({type:x,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(p,n){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(s){return n.onScroll(s)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&p&&e.\u0275\u0275classProp("mbo-page__content--start",n.scrollStart)("mbo-page__content--end",n.scrollEnd)},dependencies:[c.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),x})()},75221:(F,z,t)=>{t.d(z,{u:()=>x});var c=t(17007),e=t(30263),o=t(27302),a=(t(88649),t(99877));let x=(()=>{class m{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return m.\u0275fac=function(n){return new(n||m)},m.\u0275cmp=a.\u0275\u0275defineComponent({type:m,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(n,r){1&n&&a.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&n&&(a.\u0275\u0275property("formControl",r.passwordControl.controls.password)("disabled",r.disabled)("elementId",r.elementPasswordId),a.\u0275\u0275advance(1),a.\u0275\u0275property("elementId",r.elementConfirmId)("disabled",r.disabled)("formControl",r.passwordControl.controls.repeatPassword))},dependencies:[c.CommonModule,e.sC,o.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),m})()},88649:(F,z,t)=>{t.d(z,{z:()=>o});var c=t(57544),i=t(24495);class o extends c.FormGroup{constructor(){const a=new c.FormControl(""),d=new c.FormControl("",[i.C1,(l=a,a=>a&&a!==l.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var l;super({controls:{password:a,repeatPassword:d}})}get password(){return this.controls.password.value}}},13043:(F,z,t)=>{t.d(z,{e:()=>h});var c=t(17007),e=t(99877),l=t(30263),x=(t(57544),t(27302));function m(b,g){1&b&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function p(b,g){if(1&b&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&b){const u=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",u.title," ")}}function n(b,g){if(1&b){const u=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const P=e.\u0275\u0275restoreView(u).$implicit,M=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(M.onProduct(P))}),e.\u0275\u0275elementEnd()}if(2&b){const u=g.$implicit,v=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",u.color)("icon",u.logo)("title",u.nickname)("number",u.publicNumber)("detail",u.bank.name)("ghost",v.ghost)}}function r(b,g){if(1&b&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,p,2,1,"div",8),e.\u0275\u0275template(3,n,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&b){const u=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",u.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",u.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",u.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!u.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",u.msgError," ")}}function s(b,g){1&b&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&b&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const f=["*"];let h=(()=>{class b{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(u){return this.productControl?.value?.id===u.id}onProduct(u){this.select.emit(u),this.productControl?.setValue(u)}}return b.\u0275fac=function(u){return new(u||b)},b.\u0275cmp=e.\u0275\u0275defineComponent({type:b,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(u,v){1&u&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275template(2,r,6,5,"div",2),e.\u0275\u0275template(3,s,3,2,"div",3),e.\u0275\u0275elementEnd()),2&u&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!v.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.skeleton))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,l.w_,x.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),b})()},38116:(F,z,t)=>{t.d(z,{Z:()=>d});var c=t(17007),e=t(99877),l=t(30263);function a(x,m){if(1&x){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const s=e.\u0275\u0275restoreView(p).$implicit,f=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(f.onAction(s))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&x){const p=m.$implicit,n=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(n.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",n.itIsDisabled(p)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(n.theme(p)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",p.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",p.label," ")}}let d=(()=>{class x{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(p){return p.requiredInformation&&p.errorInformation}theme(p){return this.itIsDisabled(p)?"none":p.theme}onAction(p){!this.itIsDisabled(p)&&this.action.emit(p.type)}}return x.\u0275fac=function(p){return new(p||x)},x.\u0275cmp=e.\u0275\u0275defineComponent({type:x,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(p,n){1&p&&e.\u0275\u0275template(0,a,6,8,"div",0),2&p&&e.\u0275\u0275property("ngForOf",n.actions)},dependencies:[c.CommonModule,c.NgForOf,l.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),x})()},68819:(F,z,t)=>{t.d(z,{w:()=>S});var c=t(17007),e=t(99877),l=t(30263),a=t(39904),m=(t(57544),t(78506)),n=(t(29306),t(87903)),r=t(95437),s=t(27302),f=t(70957),h=t(91248),b=t(13961),g=t(68789),u=t(33395),v=t(25317);function T(W,H){if(1&W){const R=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(N){e.\u0275\u0275restoreView(R);const j=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(j.onBoarding(N))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(R);const N=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(N.onCopyKey(N.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(R);const N=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(N.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&W){const R=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",R.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",R.product.tagAval)}}function P(W,H){if(1&W&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&W){const R=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",R.digitalSection)("currencyCode",null==R.currencyControl.value?null:R.currencyControl.value.code)("hidden",R.itIsVisibleMovements)}}function M(W,H){if(1&W&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&W){const R=H.$implicit,w=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",R)("currencyCode",null==w.currencyControl.value?null:w.currencyControl.value.code)}}const B=[[["","header",""]],"*"],I=["[header]","*"];let S=(()=>{class W{constructor(R,w,N){this.mboProvider=R,this.managerInformation=w,this.onboardingScreenService=N,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(R){const{movements:w,sections:N}=R;w&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!w.currentValue),N&&this.product&&this.refreshComponent(this.product,N.currentValue),this.managerInformation.requestInfoBody().then(j=>{j.when({success:({canEditTagAval:G})=>{this.canEditTagAval=G}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(R){(0,n.Bn)(R),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(a.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(R,w){const N=(0,n.A2)(R);if(this.sectionPosition=0,w?.length){const j=w.map(({title:G},X)=>({label:G,value:X}));N&&(this.headerMovements.value=this.sections.length,j.push(this.headerMovements)),this.headers=j}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const j=[{label:"Error",value:1}];N&&j.unshift(this.headerMovements),this.headers=j}}onBoarding(R){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(b.Z)),this.tagAvalonboarding.open(),R.stopPropagation()}}return W.\u0275fac=function(R){return new(R||W)(e.\u0275\u0275directiveInject(r.ZL),e.\u0275\u0275directiveInject(m.vu),e.\u0275\u0275directiveInject(g.x))},W.\u0275cmp=e.\u0275\u0275defineComponent({type:W,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:I,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(R,w){1&R&&(e.\u0275\u0275projectionDef(B),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(j){return w.sectionPosition=j}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,T,15,2,"div",4),e.\u0275\u0275template(6,P,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,M,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&R&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",w.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",w.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",w.headers)("value",w.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",w.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==w.product?null:w.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",w.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",w.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",w.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",w.movements)("header",w.header)("product",w.product)("currencyCode",null==w.currencyControl.value?null:w.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",w.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!w.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,l.Gf,l.qw,l.P8,l.Dj,l.qd,f.K,h.I,s.Aj,u.kW,v.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),W})()},19310:(F,z,t)=>{t.d(z,{$:()=>P});var c=t(17007),i=t(99877),e=t(30263),o=t(87903);let l=(()=>{class M{transform(I,S,W=" "){return(0,o.rd)(I,S,W)}}return M.\u0275fac=function(I){return new(I||M)},M.\u0275pipe=i.\u0275\u0275definePipe({name:"codeSplit",type:M,pure:!0}),M})(),a=(()=>{class M{}return M.\u0275fac=function(I){return new(I||M)},M.\u0275mod=i.\u0275\u0275defineNgModule({type:M}),M.\u0275inj=i.\u0275\u0275defineInjector({imports:[c.CommonModule]}),M})();t(57544);var x=t(70658),m=t(78506),n=(t(29306),t(87956)),r=t(72765),s=t(27302);function f(M,B){if(1&M){const I=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",18),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(I);const W=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(W.onDigital())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275property("prefixIcon",I.digitalIcon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(I.digitalIncognito?"Ver datos":"Ocultar datos")}}function h(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"span"),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate(null==I.product?null:I.product.publicNumber)}}function b(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"span",19),i.\u0275\u0275text(1),i.\u0275\u0275pipe(2,"codeSplit"),i.\u0275\u0275elementEnd()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind2(2,1,I.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":I.digitalNumber,4)," ")}}function g(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"bocc-badge",20),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275attribute("bocc-theme",null==I.product||null==I.product.status?null:I.product.status.color),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",null==I.product||null==I.product.status?null:I.product.status.label," ")}}function u(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"div",21),i.\u0275\u0275element(1,"bocc-progress-bar",22),i.\u0275\u0275elementEnd()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("theme",I.progressBarTheme)("width",I.progressBarStatus)}}function v(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"div",23)(1,"label",24),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),i.\u0275\u0275element(5,"bocc-amount",26),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(6,"mbo-button-incognito-mode",27),i.\u0275\u0275elementEnd()()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",null==I.product?null:I.product.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!I.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("amount",null==I.product?null:I.product.amount)("incognito",I.incognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("actionMode",!0)("hidden",!I.product)}}function T(M,B){if(1&M&&(i.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),i.\u0275\u0275text(3,"Vence"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"span",19),i.\u0275\u0275text(5),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(6,"div",29)(7,"label",20),i.\u0275\u0275text(8,"CVC"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(9,"span",19),i.\u0275\u0275text(10),i.\u0275\u0275elementEnd()()()),2&M){const I=i.\u0275\u0275nextContext();i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",I.digitalIncognito?"\u2022\u2022 | \u2022\u2022":I.digitalExpAt," "),i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",I.digitalIncognito?"\u2022\u2022\u2022":I.digitalCVC," ")}}let P=(()=>{class M{constructor(I,S){this.managerPreferences=I,this.digitalService=S,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new i.EventEmitter,this.digital=new i.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:I})=>{this.incognito=I})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:I,value:S})=>{this.product&&this.product.id===I&&this.refreshDigitalState(S)}))}ngOnChanges(I){const{product:S}=I;if(S&&S.currentValue){const W=S.currentValue;this.refreshDigitalState(this.digitalService.request(W.id)),this.activateDigitalCountdown(W)}}ngOnDestroy(){this.unsubscriptions.forEach(I=>I())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(I){const{incognito:S,requiredRequest:W,cvc:H,expirationAt:R,number:w}=I;this.digitalIncognito=S,this.digitalExpAt=R,this.digitalCVC=H,this.digitalNumber=w,this.digitalIcon=S?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=W}activateDigitalCountdown(I){const{countdown$:S}=this.digitalService.request(I.id);S?(this.progressBarRequired=!0,this.progressBarPercent=100,S.subscribe(W=>{this.progressBarRequired=!(W>=x.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-W/x.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return M.\u0275fac=function(I){return new(I||M)(i.\u0275\u0275directiveInject(m.Bx),i.\u0275\u0275directiveInject(n.ZP))},M.\u0275cmp=i.\u0275\u0275defineComponent({type:M,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[i.\u0275\u0275NgOnChangesFeature,i.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(I,S){1&I&&(i.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),i.\u0275\u0275listener("click",function(){return S.onClose()}),i.\u0275\u0275elementStart(3,"span"),i.\u0275\u0275text(4,"Atr\xe1s"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(5,"mbo-currency-toggle",3),i.\u0275\u0275template(6,f,3,2,"button",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(7,"div",5)(8,"div",6),i.\u0275\u0275element(9,"img",7),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),i.\u0275\u0275text(12),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),i.\u0275\u0275template(15,h,2,1,"span",12),i.\u0275\u0275template(16,b,3,4,"span",13),i.\u0275\u0275template(17,g,2,2,"bocc-badge",14),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(18,u,2,2,"div",15),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(19,v,7,6,"div",16),i.\u0275\u0275template(20,T,11,2,"div",17),i.\u0275\u0275elementEnd()),2&I&&(i.\u0275\u0275classMap(null==S.product?null:S.product.bank.className),i.\u0275\u0275property("color",null==S.product?null:S.product.color),i.\u0275\u0275advance(5),i.\u0275\u0275property("formControl",S.currencyControl)("currencies",S.currencies)("hidden",!(null!=S.product&&S.product.bank.isOccidente)||S.currencies.length<2),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==S.product?null:S.product.isDigital),i.\u0275\u0275advance(3),i.\u0275\u0275property("src",null==S.product?null:S.product.logo,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!S.product),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",(null==S.product?null:S.product.nickname)||(null==S.product?null:S.product.name)," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!S.product),i.\u0275\u0275advance(1),i.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!S.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=S.product&&S.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==S.product?null:S.product.isDigital),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",(null==S.product||null==S.product.status?null:S.product.status.label)&&S.digitalIncognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",S.progressBarRequired),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=S.product&&S.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==S.product?null:S.product.isDigital))},dependencies:[c.CommonModule,c.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,a,l,r.uf,s.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),M})()},94614:(F,z,t)=>{t.d(z,{K:()=>m});var c=t(17007),e=t(30263),o=t(39904),a=(t(29306),t(95437)),d=t(99877);let m=(()=>{class p{constructor(r){this.mboProvider=r,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:r,id:s,parentProduct:f}=this.product;"covered"===r&&f?this.goToPage(f.id,s):this.goToPage(s)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(r,s){this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:r,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:s})}}return p.\u0275fac=function(r){return new(r||p)(d.\u0275\u0275directiveInject(a.ZL))},p.\u0275cmp=d.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(r,s){1&r&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275listener("click",function(){return s.onComponent()}),d.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"div",4),d.\u0275\u0275element(7,"bocc-amount",5),d.\u0275\u0275elementEnd()()),2&r&&(d.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",s.skeleton),d.\u0275\u0275advance(2),d.\u0275\u0275property("active",s.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==s.movement?null:s.movement.dateFormat," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("active",s.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==s.movement?null:s.movement.description," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("hidden",s.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275property("amount",null==s.movement?null:s.movement.value)("currencyCode",null==s.movement?null:s.movement.currencyCode)("theme",!0))},dependencies:[c.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),p})()},70957:(F,z,t)=>{t.d(z,{K:()=>u});var c=t(15861),i=t(17007),o=t(99877),a=t(30263),d=t(78506),x=t(39904),p=(t(29306),t(87903)),n=t(95437),r=t(27302),s=t(94614);function f(v,T){if(1&v){const P=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",8),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(P);const B=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(B.onRedirectAll())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Ver todos"),o.\u0275\u0275elementEnd()()}}function h(v,T){if(1&v&&(o.\u0275\u0275elementStart(0,"div",5)(1,"label",6),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(3,f,3,0,"button",7),o.\u0275\u0275elementEnd()),2&v){const P=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",(null==P.productMovements||null==P.productMovements.range?null:P.productMovements.range.label)||"Sin resultados"," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==P.productMovements?null:P.productMovements.range)}}function b(v,T){if(1&v&&o.\u0275\u0275element(0,"mbo-product-info-movement",9),2&v){const P=T.$implicit,M=o.\u0275\u0275nextContext();o.\u0275\u0275property("movement",P)("product",M.product)}}function g(v,T){if(1&v&&(o.\u0275\u0275elementStart(0,"mbo-message-empty",10),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&v){const P=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",P.msgError," ")}}let u=(()=>{class v{constructor(P,M){this.mboProvider=P,this.managerProductMovements=M,this.header=!0,this.requesting=!1}ngOnChanges(P){const{currencyCode:M,product:B}=P;if(!this.movements&&(B||M)){const I=M?.currentValue||this.currencyCode,S=B?.currentValue||this.product;this.currentMovements=void 0,(0,p.A2)(S)&&this.requestFirstPage(S,I)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:P,id:M,parentProduct:B}=this.product;this.mboProvider.navigation.next(x.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===P?{productId:B?.id,coveredCardId:M}:{productId:M},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(P,M){var B=this;return(0,c.Z)(function*(){B.requesting=!0,(yield B.managerProductMovements.requestForProduct({product:P,currencyCode:M})).when({success:I=>{B.currentMovements=I},failure:()=>{B.currentMovements=void 0}},()=>{B.requesting=!1})})()}}return v.\u0275fac=function(P){return new(P||v)(o.\u0275\u0275directiveInject(n.ZL),o.\u0275\u0275directiveInject(d.sy))},v.\u0275cmp=o.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(P,M){1&P&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,h,4,2,"div",1),o.\u0275\u0275elementStart(2,"div",2),o.\u0275\u0275template(3,b,1,2,"mbo-product-info-movement",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(4,g,2,1,"mbo-message-empty",4),o.\u0275\u0275elementEnd()),2&P&&(o.\u0275\u0275property("hidden",M.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",M.header),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",null==M.productMovements?null:M.productMovements.firstPage),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==M.productMovements?null:M.productMovements.isEmpty))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,a.P8,s.K,r.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),v})()},91248:(F,z,t)=>{t.d(z,{I:()=>p});var c=t(17007),e=t(30263),o=t(99877);function a(n,r){if(1&n&&o.\u0275\u0275element(0,"bocc-amount",10),2&n){const s=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("amount",s.value)("currencyCode",s.currencyCode)}}function d(n,r){if(1&n&&(o.\u0275\u0275elementStart(0,"span"),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&n){const s=o.\u0275\u0275nextContext().$implicit,f=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",f.incognito||f.section.incognito?s.mask:s.value," ")}}function x(n,r){if(1&n){const s=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(s);const h=o.\u0275\u0275nextContext().$implicit;return o.\u0275\u0275resetView(h.action.click())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&n){const s=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("suffixIcon",s.action.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(s.action.label)}}function m(n,r){if(1&n&&(o.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275template(5,a,1,2,"bocc-amount",7),o.\u0275\u0275template(6,d,2,1,"span",8),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(7,x,3,2,"button",9),o.\u0275\u0275elementEnd()),2&n){const s=r.$implicit,f=o.\u0275\u0275nextContext();o.\u0275\u0275property("hidden",(null==s?null:s.currencyCode)!==f.currencyCode),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",s.label," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",s.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!s.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",s.action&&!(f.incognito||f.section.incognito))}}let p=(()=>{class n{constructor(){this.currencyCode="COP"}}return n.\u0275fac=function(s){return new(s||n)},n.\u0275cmp=o.\u0275\u0275defineComponent({type:n,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(s,f){1&s&&(o.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),o.\u0275\u0275template(2,m,8,5,"li",2),o.\u0275\u0275elementEnd()()),2&s&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",f.section.datas))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),n})()},4663:(F,z,t)=>{t.d(z,{c:()=>r});var c=t(17007),e=t(99877),l=t(30263),a=t(27302);function d(s,f){1&s&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function x(s,f){if(1&s&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&s){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",h.title," ")}}function m(s,f){if(1&s){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const u=e.\u0275\u0275restoreView(h).$implicit,v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onProduct(u))}),e.\u0275\u0275elementEnd()}if(2&s){const h=f.$implicit,b=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",h.color)("icon",h.logo)("title",h.nickname)("number",h.publicNumber)("ghost",b.ghost)("amount",h.amount)("tagAval",h.tagAvalFormat),e.\u0275\u0275attribute("amount-status",b.amountColorProduct(h))}}function p(s,f){1&s&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const n=["*"];let r=(()=>{class s{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(h){return h.amount>0?"success":h.amount<0?"danger":"empty"}onProduct(h){this.select.emit(h)}}return s.\u0275fac=function(h){return new(h||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:n,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(h,b){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,d,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,x,2,1,"div",5),e.\u0275\u0275template(8,m,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,p,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",b.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",b.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",b.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!b.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!b.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,l.w_,l.P8,a.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),s})()},13961:(F,z,t)=>{t.d(z,{Z:()=>d});var c=t(17007),e=t(27302),o=t(10119),l=t(99877);let d=(()=>{class x{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(p){this.portal=p}}return x.\u0275fac=function(p){return new(p||x)},x.\u0275cmp=l.\u0275\u0275defineComponent({type:x,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[l.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(p,n){1&p&&(l.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),l.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(4,"p"),l.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),l.\u0275\u0275elementEnd()(),l.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),l.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(9,"p"),l.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),l.\u0275\u0275elementEnd()()()),2&p&&l.\u0275\u0275property("headerActionRight",n.headerAction)("gradient",!0)},dependencies:[c.CommonModule,e.Nu,o.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),x})()},66709:(F,z,t)=>{t.d(z,{s:()=>d});var c=t(17007),i=t(99877),e=t(30263),o=t(87542);let l=(()=>{class x{ngBoccPortal(p){}}return x.\u0275fac=function(p){return new(p||x)},x.\u0275cmp=i.\u0275\u0275defineComponent({type:x,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(p,n){1&p&&(i.\u0275\u0275elementStart(0,"div",0)(1,"label",1),i.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),i.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),i.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(11,"p",4),i.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),i.\u0275\u0275element(13,"br"),i.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),i.\u0275\u0275element(15,"br"),i.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),i.\u0275\u0275elementStart(17,"span"),i.\u0275\u0275text(18,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(19," Configuraci\xf3n "),i.\u0275\u0275elementStart(20,"span"),i.\u0275\u0275text(21,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(22," Seguridad "),i.\u0275\u0275elementStart(23,"span"),i.\u0275\u0275text(24,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(25," Activar Token Mobile."),i.\u0275\u0275element(26,"br"),i.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),i.\u0275\u0275elementEnd()()()())},dependencies:[c.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),x})();const a=["*"];let d=(()=>{class x{constructor(p,n){this.ref=p,this.bottomSheetService=n,this.verifying=!1,this.tokenLength=o.Xi,this.code=new i.EventEmitter,this.tokenControls=new o.b2}ngOnInit(){const p=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(l),setTimeout(()=>{p?.focus()},120)}onAutocomplete(p){this.code.emit(p)}onInfo(){this.infoSheet?.open()}}return x.\u0275fac=function(p){return new(p||x)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(e.fG))},x.\u0275cmp=i.\u0275\u0275defineComponent({type:x,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:a,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(p,n){1&p&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275projection(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"p",2),i.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p",2),i.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),i.\u0275\u0275elementStart(7,"a"),i.\u0275\u0275text(8),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(9,". "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",3),i.\u0275\u0275element(11,"img",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),i.\u0275\u0275listener("autocomplete",function(s){return n.onAutocomplete(s)}),i.\u0275\u0275text(13," Ingresa tu clave "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(14,"button",6),i.\u0275\u0275listener("click",function(){return n.onInfo()}),i.\u0275\u0275elementStart(15,"span"),i.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()()()),2&p&&(i.\u0275\u0275advance(8),i.\u0275\u0275textInterpolate1("",n.tokenLength," d\xedgitos"),i.\u0275\u0275advance(4),i.\u0275\u0275property("disabled",n.verifying)("formControls",n.tokenControls),i.\u0275\u0275advance(2),i.\u0275\u0275property("disabled",n.verifying))},dependencies:[c.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),x})()},22816:(F,z,t)=>{t.d(z,{S:()=>c});class c{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);