(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5062],{35062:(h,l,n)=>{n.r(l),n.d(l,{MboPaymentInvoiceModule:()=>e});var a=n(17007),d=n(78007),t=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(4432).then(n.bind(n,84432)).then(o=>o.MboPaymentInvoiceSourcePageModule)},{path:"confirmation",loadChildren:()=>n.e(4208).then(n.bind(n,14208)).then(o=>o.MboPaymentInvoiceConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(6263).then(n.bind(n,45507)).then(o=>o.MboPaymentInvoiceResultPageModule)}];let e=(()=>{class o{}return o.\u0275fac=function(P){return new(P||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[a.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);