(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6797],{88844:(U,f,t)=>{t.d(f,{YI:()=>u,tc:()=>T,iR:()=>G,jq:()=>D,Hv:()=>I,S6:()=>E,E2:()=>R,V4:()=>C,wp:()=>$,CE:()=>O,YQ:()=>h,ND:()=>N,t1:()=>x});var i=t(6472);class A{constructor(o){this.value=o}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:o}){return this.value.id===o}filtrable(o){return(0,i.hasPattern)(this.value.name,o)}}function h(m){return m.map(o=>new A(o))}class p{constructor(o){this.currency=o}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(o){return this.currency.code===o?.code}filtrable(o){return!0}}function N(m){return m.map(o=>new p(o))}var l=t(39904);class I{constructor(o){this.value=o}get title(){return this.value.label}get description(){return this.value.label}compareTo(o){return this.value.reference===o.reference}filtrable(o){return!0}}const u=l.Bf.map(m=>new I(m));class c{constructor(o,K){this.value=o,this.title=this.value.label,this.description=K?this.value.code:this.value.label}compareTo(o){return this.value===o}filtrable(o){return!0}}const D=new c(l.Gd),E=new c(l.XU),b=new c(l.t$),R=new c(l.j1),C=new c(l.k7),G=[D,E,b,R],T=[new c(l.Gd,!0),new c(l.XU,!0),new c(l.t$,!0),new c(l.j1,!0)];class _{constructor(o){this.product=o}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(o){return this.product.id===o?.id}filtrable(o){return!0}}function x(m){return m.map(o=>new _(o))}var g=t(89148);class d{constructor(o){this.filter=o}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(o){return this.value===o}filtrable(o){return!0}}const O=new d({label:"Todos los productos",short:"Todos",value:g.Gt.None}),e=new d({label:"Cuentas de ahorro",short:"Ahorros",value:g.Gt.SavingAccount}),F=new d({label:"Cuentas corriente",short:"Corrientes",value:g.Gt.CheckingAccount}),L=new d({label:"Depositos electr\xf3nicos",short:"Depositos",value:g.Gt.ElectronicDeposit}),w=new d({label:"Cuentas AFC",short:"AFC",value:g.Gt.AfcAccount}),V=new d({label:"Tarjetas de cr\xe9dito",short:"TC",value:g.Gt.CreditCard}),z=new d({label:"Inversiones",short:"Inversiones",value:g.Gt.CdtAccount}),k=new d({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:g.Gt.Loan}),j=new d({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:g.Gt.ResolvingCredit}),B=new d({label:"Productos Aval",short:"Aval",value:g.Gt.Aval}),X=new d({label:"Productos fiduciarios",short:"Fiducias",value:g.Gt.Trustfund}),P=new d({label:"Otros productos",short:"Otros",value:g.Gt.None}),$={SDA:e,DDA:F,EDA:L,AFC:w,CCA:V,CDA:z,DLA:k,LOC:j,AVAL:B,80:X,MDA:P,NONE:P,SBA:P,VDA:P}},80174:(U,f,t)=>{t.d(f,{HV:()=>N,Js:()=>p,Pt:()=>h});var i=t(89148);const h={accountNumberSize:20,products:[]},p={accountNumberSize:20,products:[i.Gt.SavingAccount,i.Gt.CheckingAccount,i.Gt.ElectronicDeposit]},N={"0283":{accountNumberSize:8,products:[i.Gt.SavingAccount,i.Gt.ElectronicDeposit]},"0809":{accountNumberSize:8,products:[i.Gt.SavingAccount]},"0819":{accountNumberSize:17,products:[i.Gt.SavingAccount,i.Gt.ElectronicDeposit]}}},99076:(U,f,t)=>{t.d(f,{I:()=>I,m:()=>l});var i=t(87903),A=t(53113),h=t(80174);function p(u){const{isError:c,message:D,type:E}=u;return{animation:(0,i.jY)(u),title:c?"\xa1Transferencia fallida!":"PENDING"===E?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:D}}function N({isError:u}){return u?[(0,i.wT)("Finalizar","finish","outline"),(0,i.wT)("Volver a intentar","retry")]:[(0,i.wT)("Hacer otra transferencia","retry","outline"),(0,i.wT)("Finalizar","finish")]}function l(u){const{dateFormat:c,timeFormat:D}=new A.ou,{status:E,transfer:b}=u,R=[(0,i.SP)("DESTINO",b.destinationName,b.destination.number,b.destination.bankName),(0,i._f)("SUMA DE",b.amount)];return b.note&&R.push((0,i.SP)("DESCRIPCI\xd3N",b.note.description,"",b.note.reference)),R.push((0,i.cZ)(c,D)),{actions:N(E),error:E.isError,header:p(E),informations:R,skeleton:!1}}function I(u){return u?h.HV[u.id]||h.Js:h.Pt}},46797:(U,f,t)=>{t.r(f),t.d(f,{MboTransferGenericUnregisteredPageModule:()=>W});var i=t(17007),A=t(78007),h=t(79798),p=t(30263),N=t(15861),l=t(8834),I=t(39904),u=t(88844),c=t(89148),D=t(95437),E=t(40914),b=t(99013),R=t(18767),C=t(24495),G=t(53113),T=t(57544),_=t(80174),x=t(4669),g=t(99076);const d=s=>a=>a&&a.length>s?{id:"maxAccountDigits",message:`N\xfamero de cuenta admite m\xe1ximo ${s} d\xedgitos`}:null;class O extends T.FormGroup{constructor(){const a=new T.FormControl(void 0,[C.C1]),n=new T.FormControl(c.Gt.SavingAccount,[C.C1]),r=new T.FormControl(void 0,[C.C1,C.X1,d(17)]),v=new T.FormControl(!1),S=new T.FormControl,y=new T.FormControl(I.Gd),M=new T.FormControl(void 0,[C.X1]);super({controls:{bank:a,accountType:n,accountNumber:r,customer:v,name:S,documentType:y,documentNumber:M}}),this.bankRule=_.Pt,this.unsubscriptions=[],this.bank=a,this.accountType=n,this.accountNumber=r,this.customer=v,this.name=S,this.documentType=y,this.documentNumber=M,this.unsubscriptions.push(a.subscribe(Y=>{this.bankRule=(0,g.I)(Y),r.setValidators([C.C1,C.X1,d(this.bankRule.accountNumberSize)]),this.bankRule.products.includes(n.value)||n.setValue(this.bankRule.products[0])}))}get accountValid(){return this.bank.valid&&this.accountType.valid&&this.accountNumber.valid}get customerValid(){return this.name.valid&&this.documentType.valid&&this.documentNumber.valid}get bankCustomerValid(){return!!this.bank.value?.isOccidente||this.customer.value||this.customerValid}get invalid(){return!this.accountValid||!this.bankCustomerValid}unsubscribe(){this.unsubscriptions.forEach(a=>{a()})}canAccountType(a){return this.bankRule.products.includes(a)}setUnregistered(a){this.bank.setValue(a.bank),this.accountNumber.setValue(a.accountNumber),this.accountType.setValue(a.accountType);const{owner:n}=a;this.name.setValue(n?.name),this.documentType.setValue(n?.document.type||I.Gd),this.documentNumber.setValue(n?.document.number),this.customer.setValue(!!n?.customer)}getUnregistered(a){return new x.AT(this.bank.value,this.accountType.value,this.accountNumber.value,this.getProductOwner(a))}getProductOwner({clientName:a,document:n}){const{bank:r,customer:v,documentNumber:S,documentType:y,name:M}=this.controls;if(v.value)return new G.X6(a,n,v.value);if(r.value.isOccidente)return;const Y=new G.dp(y.value,S.value);return new G.X6(M.value,Y,v.value)}}var e=t(99877),F=t(10464),L=t(48774),w=t(66613),V=t(18443),z=t(48030),k=t(64181),j=t(51458),B=t(65887),X=t(45542);function P(s,a){if(1&s&&(e.\u0275\u0275elementStart(0,"p",20),e.\u0275\u0275text(1," Ten en cuenta que solo podr\xe1s transferir hasta "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," al mes y hasta "),e.\u0275\u0275elementStart(5,"b"),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(7," diarios a cuentas de "),e.\u0275\u0275elementStart(8,"b"),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(10," sin necesidad de inscripci\xf3n. (M\xe1ximo 3 operaciones diarias y 6 durante el mes) "),e.\u0275\u0275elementEnd()),2&s){const n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("$",n.maxAmount,""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("$",n.minAmount,""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.unregisteredControl.bank.value.name)}}function $(s,a){if(1&s&&(e.\u0275\u0275elementStart(0,"p",20),e.\u0275\u0275text(1," Recuerda que la transferencia a una cuenta diferente de los Bancos del "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3,"Grupo Aval (Transferencias ACH)"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," que realicen despu\xe9s de las "),e.\u0275\u0275elementStart(5,"b"),e.\u0275\u0275text(6,"3:00 P.M."),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(7," y durante el fin de semana ser\xe1n efectivas el siguiente d\xeda h\xe1bil y su confirmaci\xf3n depender\xe1 del banco destino. Nuestros topes han cambiado, ahora podr\xe1s realizar 3 transferencias diarias, 6 al mes y que sumen un monto de "),e.\u0275\u0275elementStart(8,"b"),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(10," al d\xeda y en el mes hasta "),e.\u0275\u0275elementStart(11,"b"),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(13," a cuentas ACH "),e.\u0275\u0275elementEnd()),2&s){const n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(9),e.\u0275\u0275textInterpolate1("$",n.minAmount,""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("$",n.maxAmount,"")}}function m(s,a){if(1&s&&(e.\u0275\u0275elementStart(0,"bocc-alert",18),e.\u0275\u0275template(1,P,11,3,"p",19),e.\u0275\u0275template(2,$,14,2,"p",19),e.\u0275\u0275elementEnd()),2&s){const n=e.\u0275\u0275nextContext();e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.unregisteredControl.bank.value.isAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.unregisteredControl.bank.value.isAval)}}function o(s,a){1&s&&(e.\u0275\u0275elementStart(0,"button",21)(1,"span"),e.\u0275\u0275text(2,"\xbfCu\xe1ndo llega la transferencia?"),e.\u0275\u0275elementEnd()())}const{MAX_UNREGISTERED:K,MIN_UNREGISTERED:J}=E.R,H=I.Z6.TRANSFERS.GENERIC;let Z=(()=>{class s{constructor(n,r,v,S){this.mboProvider=n,this.requestConfiguration=r,this.managerTransfer=v,this.cancelProvider=S,this.confirmation=!1,this.minAmount=(0,l.b)({value:J}),this.maxAmount=(0,l.b)({value:K}),this.savingAccount=c.Gt.SavingAccount,this.checkingAccount=c.Gt.CheckingAccount,this.electronicDeposit=c.Gt.ElectronicDeposit,this.banks=[],this.documents=u.iR,this.backAction={id:"btn_transfer-generic-quickly_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.cancelProvider.backCustomer(this.confirmation)}},this.cancelAction={id:"btn_transfer-generic-quickly_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}},this.unregisteredControl=new O}ngOnInit(){setTimeout(()=>{this.initializatedConfiguration()},120)}ngOnDestroy(){this.unregisteredControl.unsubscribe()}onSubmit(){var n=this;return(0,N.Z)(function*(){(yield n.managerTransfer.setUnregistered(n.unregisteredControl.getUnregistered(n.customer))).when({success:()=>{n.mboProvider.navigation.next(n.confirmation?H.CONFIRMATION:H.AMOUNT)}})})()}initializatedConfiguration(){var n=this;return(0,N.Z)(function*(){n.mboProvider.loader.open("Solicitando datos, por favor espere..."),(yield n.requestConfiguration.unregistered()).when({success:({banks:r,confirmation:v,customer:S,unregistered:y})=>{n.banks=(0,u.YQ)(r),n.customer=S,n.confirmation=v,n.documents=[...u.iR,u.V4],y&&n.unregisteredControl.setUnregistered(y)}},()=>{n.mboProvider.loader.close()})})()}}return s.\u0275fac=function(n){return new(n||s)(e.\u0275\u0275directiveInject(D.ZL),e.\u0275\u0275directiveInject(b.ow),e.\u0275\u0275directiveInject(b.Al),e.\u0275\u0275directiveInject(R.S))},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-transfer-generic-unregistered-page"]],decls:25,vars:27,consts:[[1,"mbo-transfer-generic-unregistered-page__content"],[1,"mbo-transfer-generic-unregistered-page__header"],["title","Sin inscripci\xf3n","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfer-generic-unregistered-page__body"],["elementId","lst_transfer-generic-unregistered_bank","alignContent","left","label","Entidad bancaria","placeholder","Seleccionar entidad bancaria",3,"formControl","suggestions","filtrable"],["icon","bell",3,"visible",4,"ngIf"],[1,"mbo-transfer-generic-unregistered-page__accounts"],["elementId","tog_transfer-generic-unregistered_saving-account","icon","coins","bocc-theme","success",3,"value","formControl","disabled"],["elementId","tog_transfer-generic-unregistered_checking-account","icon","wallet-percent","bocc-theme","amathyst",3,"value","formControl","disabled"],["elementId","tog_transfer-generic-unregistered_electronic-deposit","icon","wireless-lock","bocc-theme","ocher",3,"value","formControl","disabled"],["elementId","txt_transfer-generic-unregistered_account-number","type","number","label","N\xfamero de cuenta","placeholder","D\xedgita el n\xfamero de cuenta",3,"formControl"],[3,"formControl"],[1,"mbo-transfer-generic-unregistered-page__customer",3,"hidden"],["elementId","txt_transfer-generic-unregistered_owner-name","label","Nombre del t\xedtular","placeholder","Ejemplo: Pepito Per\xe9z",3,"formControl","hidden"],["pageId","transfer-generic-unregistered",3,"documents","documentNumber","documentType","hidden","required"],["id","btn_transfer-generic-unregistered_timeout","bocc-button","flat","prefixIcon","chat-message",4,"ngIf"],[1,"mbo-transfer-generic-unregistered-page__footer"],["id","btn_transfer-generic-unregistered_submit","bocc-button","raised",3,"disabled","click"],["icon","bell",3,"visible"],["class","overline-regular",4,"ngIf"],[1,"overline-regular"],["id","btn_transfer-generic-unregistered_timeout","bocc-button","flat","prefixIcon","chat-message"]],template:function(n,r){1&n&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3),e.\u0275\u0275element(5,"bocc-select-box",4),e.\u0275\u0275template(6,m,3,3,"bocc-alert",5),e.\u0275\u0275elementStart(7,"div",6)(8,"bocc-button-toggle",7),e.\u0275\u0275text(9," Cuenta de ahorros "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"bocc-button-toggle",8),e.\u0275\u0275text(11," Cuenta corriente "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"bocc-button-toggle",9),e.\u0275\u0275text(13," Dep\xf3sito electr\xf3nico "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(14,"bocc-input-box",10),e.\u0275\u0275elementStart(15,"bocc-switch-label",11),e.\u0275\u0275text(16," Soy el t\xedtular de la cuenta "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"div",12),e.\u0275\u0275element(18,"bocc-input-box",13)(19,"mbo-document-customer",14),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(20,o,3,0,"button",15),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",16)(22,"button",17),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(23,"span"),e.\u0275\u0275text(24,"Continuar"),e.\u0275\u0275elementEnd()()()()),2&n&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",r.backAction)("rightAction",r.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("formControl",r.unregisteredControl.bank)("suggestions",r.banks)("filtrable",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",r.unregisteredControl.bank.value),e.\u0275\u0275advance(2),e.\u0275\u0275property("value",r.savingAccount)("formControl",r.unregisteredControl.accountType)("disabled",!r.unregisteredControl.canAccountType(r.savingAccount)),e.\u0275\u0275advance(2),e.\u0275\u0275property("value",r.checkingAccount)("formControl",r.unregisteredControl.accountType)("disabled",!r.unregisteredControl.canAccountType(r.checkingAccount)),e.\u0275\u0275advance(2),e.\u0275\u0275property("value",r.electronicDeposit)("formControl",r.unregisteredControl.accountType)("disabled",!r.unregisteredControl.canAccountType(r.electronicDeposit)),e.\u0275\u0275advance(2),e.\u0275\u0275property("formControl",r.unregisteredControl.accountNumber),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",r.unregisteredControl.customer),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",null==r.unregisteredControl.bank.value?null:r.unregisteredControl.bank.value.isOccidente),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",r.unregisteredControl.name)("hidden",r.unregisteredControl.customer.value),e.\u0275\u0275advance(1),e.\u0275\u0275property("documents",r.documents)("documentNumber",r.unregisteredControl.documentNumber)("documentType",r.unregisteredControl.documentType)("hidden",r.unregisteredControl.customer.value)("required",!r.unregisteredControl.customer.value),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",r.unregisteredControl.invalid))},dependencies:[i.NgIf,F.K,L.J,w.B,V.u,z.t,k.D,j.O,B.X,X.P],styles:["/*!\n * MBO TransferGenericUnregistered Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 30/Jun/2022\n * Updated: 19/Jun/2024\n*/mbo-transfer-generic-unregistered-page{position:relative;display:flex;width:100%;height:100%}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__title{position:relative;width:100%;text-align:center;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__customer{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x12);box-sizing:border-box}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__accounts{position:relative;display:flex;justify-content:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-generic-unregistered-page .mbo-transfer-generic-unregistered-page__footer button{width:100%}\n"],encapsulation:2}),s})(),W=(()=>{class s{}return s.\u0275fac=function(n){return new(n||s)},s.\u0275mod=e.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=e.\u0275\u0275defineInjector({imports:[i.CommonModule,A.RouterModule.forChild([{path:"",component:Z}]),h.KI,p.Jx,p.B4,p.us,p.tv,p.DT,p.OS,h.XH,p.P8]}),s})()},40914:(U,f,t)=>{t.d(f,{R:()=>i,r:()=>A});const i={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},A={MIN_TRUSTFUND:2e5}}}]);