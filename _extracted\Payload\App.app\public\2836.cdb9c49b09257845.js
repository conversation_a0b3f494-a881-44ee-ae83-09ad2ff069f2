(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2836],{9736:(U,A,o)=>{o.d(A,{Hu:()=>f,wG:()=>m,rQ:()=>x,_G:()=>L});var v=o(15861),y=o(87956),C=o(53113),a=o(98699);class h{constructor(u,e,n,i,d){this.id=u,this.nit=e,this.name=n,this.city=i,this.exampleUrl=d}}class T{constructor(u,e,n,i){this.number=u,this.amount=e,this.expirationDate=n,this.companyId=i}get expirationFormat(){return this.expirationDate.dateFormat}}class j{constructor(u,e,n){this.agreement=u,this.invoice=e,this.source=n}}function g(t){return new j(t.agreement,t.invoice,t.source)}function I(t){return new h(t.orgIdNum,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var E=o(71776),M=o(39904),D=o(87903),P=o(42168),b=o(84757),c=o(99877);let O=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,b.map)(({content:n})=>n.map(i=>I(i)))))}requestCompanyId(e){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,b.map)(({content:n})=>n.length?I(n[0]):null)))}requestInvoice(e,n){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,b.map)(i=>function p(t){return new T(t.nie||t.invoiceNum,+t.amt,new C.ou(t.expDt),t.orgIdNum)}(i))))}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),R=(()=>{class t{constructor(e){this.http=e}send(e){return(0,P.firstValueFrom)(this.http.post(M.bV.PAYMENTS.INVOICE_MANUAL,function r(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,b.map)(([n])=>(0,D.l1)(n,"SUCCESS")))).catch(n=>(0,D.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var B=o(20691);let l=(()=>{class t extends B.Store{constructor(e){super({confirmation:!1}),e.subscribes(M.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(y.Yd))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),f=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.eventBusService=i}setAgreement(e){try{return a.Either.success(this.store.setAgreement(e))}catch({message:n}){return a.Either.failure({message:n})}}setInvoice(e){try{return a.Either.success(this.store.setInvoice(e))}catch({message:n}){return a.Either.failure({message:n})}}setSource(e){try{return a.Either.success(this.store.setSource(e))}catch({message:n}){return a.Either.failure({message:n})}}reset(){try{return a.Either.success(this.store.reset())}catch({message:e}){return a.Either.failure({message:e})}}send(){var e=this;return(0,v.Z)(function*(){const n=g(e.store.currentState),i=yield e.execute(n);return e.eventBusService.emit(i.channel),a.Either.success({invoice:n,status:i})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(C.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(R),c.\u0275\u0275inject(l),c.\u0275\u0275inject(y.Yd))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),m=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,v.Z)(function*(){try{return a.Either.success(yield n.repository.requestAll(e))}catch({message:i,status:d}){return 400===d?a.Either.success([]):a.Either.failure({message:i})}})()}invoice(e,{id:n}){var i=this;return(0,v.Z)(function*(){try{return a.Either.success(yield i.repository.requestInvoice(e,n))}catch({message:d,status:N}){return a.Either.failure({value:400===N,message:d})}})()}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(O))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),x=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return a.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return a.Either.failure({message:e})}}source(){var e=this;return(0,v.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),i=e.store.getAgreement(),d=e.store.getInvoice();return a.Either.success({agreement:i,invoice:d,products:n})}catch({message:n}){return a.Either.failure({message:n})}})()}confirmation(){try{const e=g(this.store.currentState);return a.Either.success({payment:e})}catch({message:e}){return a.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(y.hM),c.\u0275\u0275inject(l))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const S=/^415(\d+)8020(\d+)$/;let V=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,v.Z)(function*(){const i=e.replace(/\D/g,"").match(S);if(!i)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const d=yield n.requestNuraCodes(),N=i[1],F=d.find(({ean_code:_})=>_===N);if(!F)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:i[2].slice(0,+F.length),companyId:F.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,P.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,b.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),L=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.barcodeService=i}execute(e){var n=this;return(0,v.Z)(function*(){try{const{reference:i,companyId:d}=yield n.barcodeService.execute(e),N=yield n.repository.requestCompanyId(d),F=yield n.repository.requestInvoice(i,d);return n.store.setAgreement(N),n.store.setInvoice(F),a.Either.success()}catch{return a.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(c.\u0275\u0275inject(O),c.\u0275\u0275inject(l),c.\u0275\u0275inject(V))},t.\u0275prov=c.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},74242:(U,A,o)=>{o.d(A,{s:()=>j});var v=o(39904),y=o(95437),C=o(30263),a=o(9736),h=o(99877);let j=(()=>{class g{constructor(p,r,E){this.modalConfirmation=p,this.mboProvider=r,this.managerInvoice=E}execute(p=!0){p?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(v.Z6.PAYMENTS.HOME)}}return g.\u0275fac=function(p){return new(p||g)(h.\u0275\u0275inject(C.$e),h.\u0275\u0275inject(y.ZL),h.\u0275\u0275inject(a.Hu))},g.\u0275prov=h.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},72836:(U,A,o)=>{o.r(A),o.d(A,{MboPaymentInvoiceManualAgreementPageModule:()=>B});var v=o(17007),y=o(78007),C=o(79798),a=o(30263),h=o(15861),T=o(39904),j=o(95437),g=o(57544),I=o(9736),p=o(74242),r=o(99877),E=o(48774),M=o(64181),D=o(23436),P=o(50689);function b(l,f){if(1&l){const s=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"bocc-card-category",9),r.\u0275\u0275listener("click",function(){const S=r.\u0275\u0275restoreView(s).$implicit,V=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(V.onAgreement(S))}),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()}if(2&l){const s=f.$implicit;r.\u0275\u0275property("subtitle",s.city),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",s.name," ")}}const c=T.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL;let R=(()=>{class l{constructor(s,m,x,S){this.mboProvider=s,this.requestAgreements=m,this.managerInvoice=x,this.cancelProvider=S,this.isError=!1,this.requesting=!1,this.backAction={id:"btn_payment-invoice-manual-company_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(c.SELECT)}},this.cancelAction={id:"btn_payment-invoice-manual-company_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.companyControl=new g.FormControl("")}ngOnInit(){this.unsubscription=this.companyControl.subscribe(s=>{s.length>=3?(clearTimeout(this.timeoutId),this.timeoutId=setTimeout(()=>this.searchAgreementsForPattern(s),1e3)):this.timeoutId&&clearTimeout(this.timeoutId)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get msgEmpty(){return this.agreements?this.isError?"Ocurrio un error inesperado mientras realizabamos la busqueda, por favor intentalo m\xe1s tarde.":"No se encontraron resultados para tu busqueda.":"D\xedgita m\xednimo 3 car\xe1cteres para buscar la empresa prestadora de tu servicio."}onAgreement(s){this.managerInvoice.setAgreement(s).when({success:()=>{this.mboProvider.navigation.next(c.REFERENCE)}})}searchAgreementsForPattern(s){var m=this;return(0,h.Z)(function*(){m.mboProvider.loader.open("Solicitando convenios, por favor espere..."),m.requesting=!0,(yield m.requestAgreements.all(s)).when({success:x=>{m.agreements=x},failure:()=>{m.agreements=[],m.isError=!0}},()=>{m.requesting=!1,m.mboProvider.loader.close()})})()}}return l.\u0275fac=function(s){return new(s||l)(r.\u0275\u0275directiveInject(j.ZL),r.\u0275\u0275directiveInject(I.wG),r.\u0275\u0275directiveInject(I.Hu),r.\u0275\u0275directiveInject(p.s))},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-invoice-manual-agreement-page"]],decls:11,vars:7,consts:[[1,"mbo-payment-invoice-manual-agreement-page__header"],["title","Empresa","progress","40%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-manual-agreement-page__content"],[1,"mbo-payment-invoice-manual-agreement-page__title","subtitle2-medium"],[1,"mbo-payment-invoice-manual-agreement-page__body"],["elementId","txt_payment-invoice-manual-agreement_filter","label","Nombre del servicio o empresa","placeholder","Buscar",3,"disabled","formControl"],[1,"mbo-payment-invoice-manual-agreement-page__agreements"],[3,"subtitle","click",4,"ngFor","ngForOf"],[3,"hidden"],[3,"subtitle","click"]],template:function(s,m){1&s&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275element(1,"bocc-header-form",1),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(2,"div",2)(3,"div",3),r.\u0275\u0275text(4," Buscar empresa prestadora "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"div",4),r.\u0275\u0275element(6,"bocc-input-box",5),r.\u0275\u0275elementStart(7,"div",6),r.\u0275\u0275template(8,b,2,2,"bocc-card-category",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(9,"mbo-message-empty",8),r.\u0275\u0275text(10),r.\u0275\u0275elementEnd()()()),2&s&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("leftAction",m.backAction)("rightAction",m.cancelAction),r.\u0275\u0275advance(5),r.\u0275\u0275property("disabled",m.requesting)("formControl",m.companyControl),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngForOf",m.agreements),r.\u0275\u0275advance(1),r.\u0275\u0275property("hidden",null==m.agreements?null:m.agreements.length),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",m.msgEmpty," "))},dependencies:[v.NgForOf,E.J,M.D,D.D,P.A],styles:["/*!\n * MBO PaymentInvoiceManualAgreement Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-agreement-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:auto}mbo-payment-invoice-manual-agreement-page .mbo-payment-invoice-manual-agreement-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-invoice-manual-agreement-page .mbo-payment-invoice-manual-agreement-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-payment-invoice-manual-agreement-page .mbo-payment-invoice-manual-agreement-page__agreements{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-invoice-manual-agreement-page .mbo-payment-invoice-manual-agreement-page__agreements .bocc-card-category__content{box-shadow:none}\n"],encapsulation:2}),l})(),B=(()=>{class l{}return l.\u0275fac=function(s){return new(s||l)},l.\u0275mod=r.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=r.\u0275\u0275defineInjector({imports:[v.CommonModule,y.RouterModule.forChild([{path:"",component:R}]),a.Jx,a.DT,a.D0,C.Aj]}),l})()}}]);