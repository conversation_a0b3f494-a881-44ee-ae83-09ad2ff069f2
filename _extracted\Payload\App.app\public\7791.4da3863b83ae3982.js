(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7791],{47791:(Hn,Ir,Ar)=>{Ar.r(Ir),Ar.d(Ir,{EventBus:()=>An,EventDriven:()=>ft,Topic:()=>xa});var Ma=Object.defineProperty,We=(t,e,a)=>((t,e,a)=>e in t?Ma(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a)(t,"symbol"!=typeof e?e+"":e,a);let It;const Ha=new Uint8Array(16);function ja(){if(!It&&(It=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!It))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return It(Ha)}const Me=[];for(let t=0;t<256;++t)Me.push((t+256).toString(16).slice(1));const Br={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function Qa(t,e,a){if(Br.randomUUID&&!t)return Br.randomUUID();const d=(t=t||{}).random||(t.rng||ja)();return d[6]=15&d[6]|64,d[8]=63&d[8]|128,function Ga(t,e=0){return Me[t[e+0]]+Me[t[e+1]]+Me[t[e+2]]+Me[t[e+3]]+"-"+Me[t[e+4]]+Me[t[e+5]]+"-"+Me[t[e+6]]+Me[t[e+7]]+"-"+Me[t[e+8]]+Me[t[e+9]]+"-"+Me[t[e+10]]+Me[t[e+11]]+Me[t[e+12]]+Me[t[e+13]]+Me[t[e+14]]+Me[t[e+15]]}(d)}const Nr=()=>((t=!0)=>{const{userAgent:e,language:a,languages:d,hardwareConcurrency:R}=window.navigator,{colorDepth:T,pixelDepth:h}=window.screen,r=(new Date).getTimezoneOffset(),v=Intl.DateTimeFormat().resolvedOptions().timeZone,l="ontouchstart"in window,S=(()=>{try{const t=document.createElement("canvas"),e=t.getContext("2d");if(!e)throw"context es null";return e.textBaseline="top",e.font="14px 'Arial'",e.textBaseline="alphabetic",e.fillStyle="#f60",e.fillRect(125,1,62,20),e.fillStyle="#069",e.fillText("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`~1!2@3#4$5%6^7&8*9(0)-_=+[{]}|;:',<.>/?",2,15),e.fillStyle="rgba(102, 204, 0, 0.7)",e.fillText("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`~1!2@3#4$5%6^7&8*9(0)-_=+[{]}|;:',<.>/?",4,17),t.toDataURL()}catch(t){throw new Error(`Unable to get canvas properties: (${t})`)}})();return JSON.stringify(t?{userAgent:e,language:a,languages:d,hardwareConcurrency:R,colorDepth:T,pixelDepth:h,timezoneOffset:r,timezone:v,touchSupport:l,canvas:S}:{canvas:S})})(!0);var Rr,wr,Xa=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Wa(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Za(t){if(Object.prototype.hasOwnProperty.call(t,"__esModule"))return t;var e=t.default;if("function"==typeof e){var a=function d(){return this instanceof d?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};a.prototype=e.prototype}else a={};return Object.defineProperty(a,"__esModule",{value:!0}),Object.keys(t).forEach(function(d){var R=Object.getOwnPropertyDescriptor(t,d);Object.defineProperty(a,d,R.get?R:{enumerable:!0,get:function(){return t[d]}})}),a}function ue(){return wr||(wr=1,Rr={options:{usePureJavaScript:!1}}),Rr}var Ot,_r,kr,Pt,Lr,Vt={exports:{}};function fe(){if(kr)return Vt.exports;kr=1;var t=ue(),e=function $a(){if(_r)return Ot;_r=1;var t={};Ot=t;var e={};return t.encode=function(d,R,T){if("string"!=typeof R)throw new TypeError('"alphabet" must be a string.');if(void 0!==T&&"number"!=typeof T)throw new TypeError('"maxline" must be a number.');var h="";if(d instanceof Uint8Array){var r=0,v=R.length,l=R.charAt(0),S=[0];for(r=0;r<d.length;++r){for(var D=0,E=d[r];D<S.length;++D)S[D]=(E+=S[D]<<8)%v,E=E/v|0;for(;E>0;)S.push(E%v),E=E/v|0}for(r=0;0===d[r]&&r<d.length-1;++r)h+=l;for(r=S.length-1;r>=0;--r)h+=R[S[r]]}else h=function a(d,R){var T=0,h=R.length,r=R.charAt(0),v=[0];for(T=0;T<d.length();++T){for(var l=0,S=d.at(T);l<v.length;++l)v[l]=(S+=v[l]<<8)%h,S=S/h|0;for(;S>0;)v.push(S%h),S=S/h|0}var D="";for(T=0;0===d.at(T)&&T<d.length()-1;++T)D+=r;for(T=v.length-1;T>=0;--T)D+=R[v[T]];return D}(d,R);if(T){var u=new RegExp(".{1,"+T+"}","g");h=h.match(u).join("\r\n")}return h},t.decode=function(d,R){if("string"!=typeof d)throw new TypeError('"input" must be a string.');if("string"!=typeof R)throw new TypeError('"alphabet" must be a string.');var T=e[R];if(!T){T=e[R]=[];for(var h=0;h<R.length;++h)T[R.charCodeAt(h)]=h}d=d.replace(/\s/g,"");var r=R.length,v=R.charAt(0),l=[0];for(h=0;h<d.length;h++){var S=T[d.charCodeAt(h)];if(void 0===S)return;for(var D=0,E=S;D<l.length;++D)l[D]=255&(E+=l[D]*r),E>>=8;for(;E>0;)l.push(255&E),E>>=8}for(var u=0;d[u]===v&&u<d.length-1;++u)l.push(0);return typeof Buffer<"u"?Buffer.from(l.reverse()):new Uint8Array(l.reverse())},Ot}(),a=Vt.exports=t.util=t.util||{};function d(s){if(8!==s&&16!==s&&24!==s&&32!==s)throw new Error("Only 8, 16, 24, or 32 bits supported: "+s)}function R(s){if(this.data="",this.read=0,"string"==typeof s)this.data=s;else if(a.isArrayBuffer(s)||a.isArrayBufferView(s))if(typeof Buffer<"u"&&s instanceof Buffer)this.data=s.toString("binary");else{var f=new Uint8Array(s);try{this.data=String.fromCharCode.apply(null,f)}catch{for(var y=0;y<f.length;++y)this.putByte(f[y])}}else(s instanceof R||"object"==typeof s&&"string"==typeof s.data&&"number"==typeof s.read)&&(this.data=s.data,this.read=s.read);this._constructedStringLength=0}(function(){if(typeof process<"u"&&process.nextTick&&!process.browser)return a.nextTick=process.nextTick,void(a.setImmediate="function"==typeof setImmediate?setImmediate:a.nextTick);if("function"==typeof setImmediate)return a.setImmediate=function(){return setImmediate.apply(void 0,arguments)},void(a.nextTick=function(i){return setImmediate(i)});if(a.setImmediate=function(i){setTimeout(i,0)},typeof window<"u"&&"function"==typeof window.postMessage){let i=function(n){if(n.source===window&&n.data===s){n.stopPropagation();var B=f.slice();f.length=0,B.forEach(function(U){U()})}};var s="forge.setImmediate",f=[];a.setImmediate=function(n){f.push(n),1===f.length&&window.postMessage(s,"*")},window.addEventListener("message",i,!0)}if(typeof MutationObserver<"u"){var y=Date.now(),I=!0,_=document.createElement("div");f=[],new MutationObserver(function(){var n=f.slice();f.length=0,n.forEach(function(B){B()})}).observe(_,{attributes:!0});var p=a.setImmediate;a.setImmediate=function(n){Date.now()-y>15?(y=Date.now(),p(n)):(f.push(n),1===f.length&&_.setAttribute("a",I=!I))}}a.nextTick=a.setImmediate})(),a.isNodejs=typeof process<"u"&&process.versions&&process.versions.node,a.globalScope=a.isNodejs?Xa:typeof self>"u"?window:self,a.isArray=Array.isArray||function(s){return"[object Array]"===Object.prototype.toString.call(s)},a.isArrayBuffer=function(s){return typeof ArrayBuffer<"u"&&s instanceof ArrayBuffer},a.isArrayBufferView=function(s){return s&&a.isArrayBuffer(s.buffer)&&void 0!==s.byteLength},a.ByteBuffer=R,a.ByteStringBuffer=R,a.ByteStringBuffer.prototype._optimizeConstructedString=function(s){this._constructedStringLength+=s,this._constructedStringLength>4096&&(this.data.substr(0,1),this._constructedStringLength=0)},a.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read},a.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0},a.ByteStringBuffer.prototype.putByte=function(s){return this.putBytes(String.fromCharCode(s))},a.ByteStringBuffer.prototype.fillWithByte=function(s,f){s=String.fromCharCode(s);for(var y=this.data;f>0;)1&f&&(y+=s),(f>>>=1)>0&&(s+=s);return this.data=y,this._optimizeConstructedString(f),this},a.ByteStringBuffer.prototype.putBytes=function(s){return this.data+=s,this._optimizeConstructedString(s.length),this},a.ByteStringBuffer.prototype.putString=function(s){return this.putBytes(a.encodeUtf8(s))},a.ByteStringBuffer.prototype.putInt16=function(s){return this.putBytes(String.fromCharCode(s>>8&255)+String.fromCharCode(255&s))},a.ByteStringBuffer.prototype.putInt24=function(s){return this.putBytes(String.fromCharCode(s>>16&255)+String.fromCharCode(s>>8&255)+String.fromCharCode(255&s))},a.ByteStringBuffer.prototype.putInt32=function(s){return this.putBytes(String.fromCharCode(s>>24&255)+String.fromCharCode(s>>16&255)+String.fromCharCode(s>>8&255)+String.fromCharCode(255&s))},a.ByteStringBuffer.prototype.putInt16Le=function(s){return this.putBytes(String.fromCharCode(255&s)+String.fromCharCode(s>>8&255))},a.ByteStringBuffer.prototype.putInt24Le=function(s){return this.putBytes(String.fromCharCode(255&s)+String.fromCharCode(s>>8&255)+String.fromCharCode(s>>16&255))},a.ByteStringBuffer.prototype.putInt32Le=function(s){return this.putBytes(String.fromCharCode(255&s)+String.fromCharCode(s>>8&255)+String.fromCharCode(s>>16&255)+String.fromCharCode(s>>24&255))},a.ByteStringBuffer.prototype.putInt=function(s,f){d(f);var y="";do{f-=8,y+=String.fromCharCode(s>>f&255)}while(f>0);return this.putBytes(y)},a.ByteStringBuffer.prototype.putSignedInt=function(s,f){return s<0&&(s+=2<<f-1),this.putInt(s,f)},a.ByteStringBuffer.prototype.putBuffer=function(s){return this.putBytes(s.getBytes())},a.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)},a.ByteStringBuffer.prototype.getInt16=function(){var s=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1);return this.read+=2,s},a.ByteStringBuffer.prototype.getInt24=function(){var s=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,s},a.ByteStringBuffer.prototype.getInt32=function(){var s=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,s},a.ByteStringBuffer.prototype.getInt16Le=function(){var s=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,s},a.ByteStringBuffer.prototype.getInt24Le=function(){var s=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,s},a.ByteStringBuffer.prototype.getInt32Le=function(){var s=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24;return this.read+=4,s},a.ByteStringBuffer.prototype.getInt=function(s){d(s);var f=0;do{f=(f<<8)+this.data.charCodeAt(this.read++),s-=8}while(s>0);return f},a.ByteStringBuffer.prototype.getSignedInt=function(s){var f=this.getInt(s),y=2<<s-2;return f>=y&&(f-=y<<1),f},a.ByteStringBuffer.prototype.getBytes=function(s){var f;return s?(s=Math.min(this.length(),s),f=this.data.slice(this.read,this.read+s),this.read+=s):0===s?f="":(f=0===this.read?this.data:this.data.slice(this.read),this.clear()),f},a.ByteStringBuffer.prototype.bytes=function(s){return typeof s>"u"?this.data.slice(this.read):this.data.slice(this.read,this.read+s)},a.ByteStringBuffer.prototype.at=function(s){return this.data.charCodeAt(this.read+s)},a.ByteStringBuffer.prototype.setAt=function(s,f){return this.data=this.data.substr(0,this.read+s)+String.fromCharCode(f)+this.data.substr(this.read+s+1),this},a.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)},a.ByteStringBuffer.prototype.copy=function(){var s=a.createBuffer(this.data);return s.read=this.read,s},a.ByteStringBuffer.prototype.compact=function(){return this.read>0&&(this.data=this.data.slice(this.read),this.read=0),this},a.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this},a.ByteStringBuffer.prototype.truncate=function(s){var f=Math.max(0,this.length()-s);return this.data=this.data.substr(this.read,f),this.read=0,this},a.ByteStringBuffer.prototype.toHex=function(){for(var s="",f=this.read;f<this.data.length;++f){var y=this.data.charCodeAt(f);y<16&&(s+="0"),s+=y.toString(16)}return s},a.ByteStringBuffer.prototype.toString=function(){return a.decodeUtf8(this.bytes())},a.DataBuffer=function h(s,f){this.read=(f=f||{}).readOffset||0,this.growSize=f.growSize||1024;var y=a.isArrayBuffer(s),I=a.isArrayBufferView(s);if(y||I)return this.data=y?new DataView(s):new DataView(s.buffer,s.byteOffset,s.byteLength),void(this.write="writeOffset"in f?f.writeOffset:this.data.byteLength);this.data=new DataView(new ArrayBuffer(0)),this.write=0,null!=s&&this.putBytes(s),"writeOffset"in f&&(this.write=f.writeOffset)},a.DataBuffer.prototype.length=function(){return this.write-this.read},a.DataBuffer.prototype.isEmpty=function(){return this.length()<=0},a.DataBuffer.prototype.accommodate=function(s,f){if(this.length()>=s)return this;f=Math.max(f||this.growSize,s);var y=new Uint8Array(this.data.buffer,this.data.byteOffset,this.data.byteLength),I=new Uint8Array(this.length()+f);return I.set(y),this.data=new DataView(I.buffer),this},a.DataBuffer.prototype.putByte=function(s){return this.accommodate(1),this.data.setUint8(this.write++,s),this},a.DataBuffer.prototype.fillWithByte=function(s,f){this.accommodate(f);for(var y=0;y<f;++y)this.data.setUint8(s);return this},a.DataBuffer.prototype.putBytes=function(s,f){if(a.isArrayBufferView(s)){var I=(y=new Uint8Array(s.buffer,s.byteOffset,s.byteLength)).byteLength-y.byteOffset;return this.accommodate(I),new Uint8Array(this.data.buffer,this.write).set(y),this.write+=I,this}if(a.isArrayBuffer(s)){var y=new Uint8Array(s);return this.accommodate(y.byteLength),new Uint8Array(this.data.buffer).set(y,this.write),this.write+=y.byteLength,this}if(s instanceof a.DataBuffer||"object"==typeof s&&"number"==typeof s.read&&"number"==typeof s.write&&a.isArrayBufferView(s.data))return y=new Uint8Array(s.data.byteLength,s.read,s.length()),this.accommodate(y.byteLength),new Uint8Array(s.data.byteLength,this.write).set(y),this.write+=y.byteLength,this;if(s instanceof a.ByteStringBuffer&&(s=s.data,f="binary"),f=f||"binary","string"==typeof s){var p;if("hex"===f)return this.accommodate(Math.ceil(s.length/2)),p=new Uint8Array(this.data.buffer,this.write),this.write+=a.binary.hex.decode(s,p,this.write),this;if("base64"===f)return this.accommodate(3*Math.ceil(s.length/4)),p=new Uint8Array(this.data.buffer,this.write),this.write+=a.binary.base64.decode(s,p,this.write),this;if("utf8"===f&&(s=a.encodeUtf8(s),f="binary"),"binary"===f||"raw"===f)return this.accommodate(s.length),p=new Uint8Array(this.data.buffer,this.write),this.write+=a.binary.raw.decode(p),this;if("utf16"===f)return this.accommodate(2*s.length),p=new Uint16Array(this.data.buffer,this.write),this.write+=a.text.utf16.encode(p),this;throw new Error("Invalid encoding: "+f)}throw Error("Invalid parameter: "+s)},a.DataBuffer.prototype.putBuffer=function(s){return this.putBytes(s),s.clear(),this},a.DataBuffer.prototype.putString=function(s){return this.putBytes(s,"utf16")},a.DataBuffer.prototype.putInt16=function(s){return this.accommodate(2),this.data.setInt16(this.write,s),this.write+=2,this},a.DataBuffer.prototype.putInt24=function(s){return this.accommodate(3),this.data.setInt16(this.write,s>>8&65535),this.data.setInt8(this.write,s>>16&255),this.write+=3,this},a.DataBuffer.prototype.putInt32=function(s){return this.accommodate(4),this.data.setInt32(this.write,s),this.write+=4,this},a.DataBuffer.prototype.putInt16Le=function(s){return this.accommodate(2),this.data.setInt16(this.write,s,!0),this.write+=2,this},a.DataBuffer.prototype.putInt24Le=function(s){return this.accommodate(3),this.data.setInt8(this.write,s>>16&255),this.data.setInt16(this.write,s>>8&65535,!0),this.write+=3,this},a.DataBuffer.prototype.putInt32Le=function(s){return this.accommodate(4),this.data.setInt32(this.write,s,!0),this.write+=4,this},a.DataBuffer.prototype.putInt=function(s,f){d(f),this.accommodate(f/8);do{f-=8,this.data.setInt8(this.write++,s>>f&255)}while(f>0);return this},a.DataBuffer.prototype.putSignedInt=function(s,f){return d(f),this.accommodate(f/8),s<0&&(s+=2<<f-1),this.putInt(s,f)},a.DataBuffer.prototype.getByte=function(){return this.data.getInt8(this.read++)},a.DataBuffer.prototype.getInt16=function(){var s=this.data.getInt16(this.read);return this.read+=2,s},a.DataBuffer.prototype.getInt24=function(){var s=this.data.getInt16(this.read)<<8^this.data.getInt8(this.read+2);return this.read+=3,s},a.DataBuffer.prototype.getInt32=function(){var s=this.data.getInt32(this.read);return this.read+=4,s},a.DataBuffer.prototype.getInt16Le=function(){var s=this.data.getInt16(this.read,!0);return this.read+=2,s},a.DataBuffer.prototype.getInt24Le=function(){var s=this.data.getInt8(this.read)^this.data.getInt16(this.read+1,!0)<<8;return this.read+=3,s},a.DataBuffer.prototype.getInt32Le=function(){var s=this.data.getInt32(this.read,!0);return this.read+=4,s},a.DataBuffer.prototype.getInt=function(s){d(s);var f=0;do{f=(f<<8)+this.data.getInt8(this.read++),s-=8}while(s>0);return f},a.DataBuffer.prototype.getSignedInt=function(s){var f=this.getInt(s),y=2<<s-2;return f>=y&&(f-=y<<1),f},a.DataBuffer.prototype.getBytes=function(s){var f;return s?(s=Math.min(this.length(),s),f=this.data.slice(this.read,this.read+s),this.read+=s):0===s?f="":(f=0===this.read?this.data:this.data.slice(this.read),this.clear()),f},a.DataBuffer.prototype.bytes=function(s){return typeof s>"u"?this.data.slice(this.read):this.data.slice(this.read,this.read+s)},a.DataBuffer.prototype.at=function(s){return this.data.getUint8(this.read+s)},a.DataBuffer.prototype.setAt=function(s,f){return this.data.setUint8(s,f),this},a.DataBuffer.prototype.last=function(){return this.data.getUint8(this.write-1)},a.DataBuffer.prototype.copy=function(){return new a.DataBuffer(this)},a.DataBuffer.prototype.compact=function(){if(this.read>0){var s=new Uint8Array(this.data.buffer,this.read),f=new Uint8Array(s.byteLength);f.set(s),this.data=new DataView(f),this.write-=this.read,this.read=0}return this},a.DataBuffer.prototype.clear=function(){return this.data=new DataView(new ArrayBuffer(0)),this.read=this.write=0,this},a.DataBuffer.prototype.truncate=function(s){return this.write=Math.max(0,this.length()-s),this.read=Math.min(this.read,this.write),this},a.DataBuffer.prototype.toHex=function(){for(var s="",f=this.read;f<this.data.byteLength;++f){var y=this.data.getUint8(f);y<16&&(s+="0"),s+=y.toString(16)}return s},a.DataBuffer.prototype.toString=function(s){var f=new Uint8Array(this.data,this.read,this.length());if("binary"===(s=s||"utf8")||"raw"===s)return a.binary.raw.encode(f);if("hex"===s)return a.binary.hex.encode(f);if("base64"===s)return a.binary.base64.encode(f);if("utf8"===s)return a.text.utf8.decode(f);if("utf16"===s)return a.text.utf16.decode(f);throw new Error("Invalid encoding: "+s)},a.createBuffer=function(s,f){return f=f||"raw",void 0!==s&&"utf8"===f&&(s=a.encodeUtf8(s)),new a.ByteBuffer(s)},a.fillString=function(s,f){for(var y="";f>0;)1&f&&(y+=s),(f>>>=1)>0&&(s+=s);return y},a.xorBytes=function(s,f,y){for(var I="",_="",p="",i=0,n=0;y>0;--y,++i)_=s.charCodeAt(i)^f.charCodeAt(i),n>=10&&(I+=p,p="",n=0),p+=String.fromCharCode(_),++n;return I+p},a.hexToBytes=function(s){var f="",y=0;for(!0&s.length&&(y=1,f+=String.fromCharCode(parseInt(s[0],16)));y<s.length;y+=2)f+=String.fromCharCode(parseInt(s.substr(y,2),16));return f},a.bytesToHex=function(s){return a.createBuffer(s).toHex()},a.int32ToBytes=function(s){return String.fromCharCode(s>>24&255)+String.fromCharCode(s>>16&255)+String.fromCharCode(s>>8&255)+String.fromCharCode(255&s)};var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",v=[62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],l="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";a.encode64=function(s,f){for(var _,p,i,y="",I="",n=0;n<s.length;)_=s.charCodeAt(n++),p=s.charCodeAt(n++),i=s.charCodeAt(n++),y+=r.charAt(_>>2),y+=r.charAt((3&_)<<4|p>>4),isNaN(p)?y+="==":(y+=r.charAt((15&p)<<2|i>>6),y+=isNaN(i)?"=":r.charAt(63&i)),f&&y.length>f&&(I+=y.substr(0,f)+"\r\n",y=y.substr(f));return I+y},a.decode64=function(s){s=s.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var y,I,_,p,f="",i=0;i<s.length;)y=v[s.charCodeAt(i++)-43],I=v[s.charCodeAt(i++)-43],_=v[s.charCodeAt(i++)-43],p=v[s.charCodeAt(i++)-43],f+=String.fromCharCode(y<<2|I>>4),64!==_&&(f+=String.fromCharCode((15&I)<<4|_>>2),64!==p&&(f+=String.fromCharCode((3&_)<<6|p)));return f},a.encodeUtf8=function(s){return unescape(encodeURIComponent(s))},a.decodeUtf8=function(s){return decodeURIComponent(escape(s))},a.binary={raw:{},hex:{},base64:{},base58:{},baseN:{encode:e.encode,decode:e.decode}},a.binary.raw.encode=function(s){return String.fromCharCode.apply(null,s)},a.binary.raw.decode=function(s,f,y){var I=f;I||(I=new Uint8Array(s.length));for(var _=y=y||0,p=0;p<s.length;++p)I[_++]=s.charCodeAt(p);return f?_-y:I},a.binary.hex.encode=a.bytesToHex,a.binary.hex.decode=function(s,f,y){var I=f;I||(I=new Uint8Array(Math.ceil(s.length/2)));var _=0,p=y=y||0;for(1&s.length&&(_=1,I[p++]=parseInt(s[0],16));_<s.length;_+=2)I[p++]=parseInt(s.substr(_,2),16);return f?p-y:I},a.binary.base64.encode=function(s,f){for(var _,p,i,y="",I="",n=0;n<s.byteLength;)_=s[n++],p=s[n++],i=s[n++],y+=r.charAt(_>>2),y+=r.charAt((3&_)<<4|p>>4),isNaN(p)?y+="==":(y+=r.charAt((15&p)<<2|i>>6),y+=isNaN(i)?"=":r.charAt(63&i)),f&&y.length>f&&(I+=y.substr(0,f)+"\r\n",y=y.substr(f));return I+y},a.binary.base64.decode=function(s,f,y){var I=f;I||(I=new Uint8Array(3*Math.ceil(s.length/4))),s=s.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var _,p,i,n,B=0,U=y=y||0;B<s.length;)_=v[s.charCodeAt(B++)-43],p=v[s.charCodeAt(B++)-43],i=v[s.charCodeAt(B++)-43],n=v[s.charCodeAt(B++)-43],I[U++]=_<<2|p>>4,64!==i&&(I[U++]=(15&p)<<4|i>>2,64!==n&&(I[U++]=(3&i)<<6|n));return f?U-y:I.subarray(0,U)},a.binary.base58.encode=function(s,f){return a.binary.baseN.encode(s,l,f)},a.binary.base58.decode=function(s,f){return a.binary.baseN.decode(s,l,f)},a.text={utf8:{},utf16:{}},a.text.utf8.encode=function(s,f,y){s=a.encodeUtf8(s);var I=f;I||(I=new Uint8Array(s.length));for(var _=y=y||0,p=0;p<s.length;++p)I[_++]=s.charCodeAt(p);return f?_-y:I},a.text.utf8.decode=function(s){return a.decodeUtf8(String.fromCharCode.apply(null,s))},a.text.utf16.encode=function(s,f,y){var I=f;I||(I=new Uint8Array(2*s.length));for(var _=new Uint16Array(I.buffer),p=y=y||0,i=y,n=0;n<s.length;++n)_[i++]=s.charCodeAt(n),p+=2;return f?p-y:I},a.text.utf16.decode=function(s){return String.fromCharCode.apply(null,new Uint16Array(s.buffer))},a.deflate=function(s,f,y){if(f=a.decode64(s.deflate(a.encode64(f)).rval),y){var I=2;32&f.charCodeAt(1)&&(I=6),f=f.substring(I,f.length-4)}return f},a.inflate=function(s,f,y){var I=s.inflate(a.encode64(f)).rval;return null===I?null:a.decode64(I)};var S=function(s,f,y){if(!s)throw new Error("WebStorage not available.");var I;if(null===y?I=s.removeItem(f):(y=a.encode64(JSON.stringify(y)),I=s.setItem(f,y)),typeof I<"u"&&!0!==I.rval){var _=new Error(I.error.message);throw _.id=I.error.id,_.name=I.error.name,_}},D=function(s,f){if(!s)throw new Error("WebStorage not available.");var y=s.getItem(f);if(s.init)if(null===y.rval){if(y.error){var I=new Error(y.error.message);throw I.id=y.error.id,I.name=y.error.name,I}y=null}else y=y.rval;return null!==y&&(y=JSON.parse(a.decode64(y))),y},E=function(s,f,y,I){var _=D(s,f);null===_&&(_={}),_[y]=I,S(s,f,_)},u=function(s,f,y){var I=D(s,f);return null!==I&&(I=y in I?I[y]:null),I},g=function(s,f,y){var I=D(s,f);if(null!==I&&y in I){delete I[y];var _=!0;for(var p in I){_=!1;break}_&&(I=null),S(s,f,I)}},C=function(s,f){S(s,f,null)},m=function(s,f,y){var I=null;typeof y>"u"&&(y=["web","flash"]);var _,p=!1,i=null;for(var n in y){_=y[n];try{if("flash"===_||"both"===_){if(null===f[0])throw new Error("Flash local storage not available.");I=s.apply(this,f),p="flash"===_}("web"===_||"both"===_)&&(f[0]=localStorage,I=s.apply(this,f),p=!0)}catch(B){i=B}if(p)break}if(!p)throw i;return I};return a.setItem=function(s,f,y,I,_){m(E,arguments,_)},a.getItem=function(s,f,y,I){return m(u,arguments,I)},a.removeItem=function(s,f,y,I){m(g,arguments,I)},a.clearItems=function(s,f,y){m(C,arguments,y)},a.isEmpty=function(s){for(var f in s)if(s.hasOwnProperty(f))return!1;return!0},a.format=function(s){for(var y,I,f=/%./g,_=0,p=[],i=0;y=f.exec(s);){(I=s.substring(i,f.lastIndex-2)).length>0&&p.push(I),i=f.lastIndex;var n=y[0][1];switch(n){case"s":case"o":p.push(_<arguments.length?arguments[1+_++]:"<?>");break;case"%":p.push("%");break;default:p.push("<%"+n+"?>")}}return p.push(s.substring(i)),p.join("")},a.formatNumber=function(s,f,y,I){var _=s,p=isNaN(f=Math.abs(f))?2:f,i=void 0===y?",":y,n=void 0===I?".":I,B=_<0?"-":"",U=parseInt(_=Math.abs(+_||0).toFixed(p),10)+"",P=U.length>3?U.length%3:0;return B+(P?U.substr(0,P)+n:"")+U.substr(P).replace(/(\d{3})(?=\d)/g,"$1"+n)+(p?i+Math.abs(_-U).toFixed(p).slice(2):"")},a.formatSize=function(s){return s>=1073741824?a.formatNumber(s/1073741824,2,".","")+" GiB":s>=1048576?a.formatNumber(s/1048576,2,".","")+" MiB":s>=1024?a.formatNumber(s/1024,0)+" KiB":a.formatNumber(s,0)+" bytes"},a.bytesFromIP=function(s){return-1!==s.indexOf(".")?a.bytesFromIPv4(s):-1!==s.indexOf(":")?a.bytesFromIPv6(s):null},a.bytesFromIPv4=function(s){if(4!==(s=s.split(".")).length)return null;for(var f=a.createBuffer(),y=0;y<s.length;++y){var I=parseInt(s[y],10);if(isNaN(I))return null;f.putByte(I)}return f.getBytes()},a.bytesFromIPv6=function(s){for(var f=0,y=2*(8-(s=s.split(":").filter(function(i){return 0===i.length&&++f,!0})).length+f),I=a.createBuffer(),_=0;_<8;++_)if(s[_]&&0!==s[_].length){var p=a.hexToBytes(s[_]);p.length<2&&I.putByte(0),I.putBytes(p)}else I.fillWithByte(0,y),y=0;return I.getBytes()},a.bytesToIP=function(s){return 4===s.length?a.bytesToIPv4(s):16===s.length?a.bytesToIPv6(s):null},a.bytesToIPv4=function(s){if(4!==s.length)return null;for(var f=[],y=0;y<s.length;++y)f.push(s.charCodeAt(y));return f.join(".")},a.bytesToIPv6=function(s){if(16!==s.length)return null;for(var f=[],y=[],I=0,_=0;_<s.length;_+=2){for(var p=a.bytesToHex(s[_]+s[_+1]);"0"===p[0]&&"0"!==p;)p=p.substr(1);if("0"===p){var i=y[y.length-1],n=f.length;i&&n===i.end+1?(i.end=n,i.end-i.start>y[I].end-y[I].start&&(I=y.length-1)):y.push({start:n,end:n})}f.push(p)}if(y.length>0){var B=y[I];B.end-B.start>0&&(f.splice(B.start,B.end-B.start+1,""),0===B.start&&f.unshift(""),7===B.end&&f.push(""))}return f.join(":")},a.estimateCores=function(s,f){if("function"==typeof s&&(f=s,s={}),s=s||{},"cores"in a&&!s.update)return f(null,a.cores);if(typeof navigator<"u"&&"hardwareConcurrency"in navigator&&navigator.hardwareConcurrency>0)return a.cores=navigator.hardwareConcurrency,f(null,a.cores);if(typeof Worker>"u")return a.cores=1,f(null,a.cores);if(typeof Blob>"u")return a.cores=2,f(null,a.cores);var y=URL.createObjectURL(new Blob(["(",function(){self.addEventListener("message",function(i){var n=Date.now();self.postMessage({st:n,et:n+4})})}.toString(),")()"],{type:"application/javascript"}));!function I(i,n,B){if(0===n){var U=Math.floor(i.reduce(function(P,L){return P+L},0)/i.length);return a.cores=Math.max(1,U),URL.revokeObjectURL(y),f(null,a.cores)}!function _(i,n){for(var B=[],U=[],P=0;P<i;++P){var L=new Worker(y);L.addEventListener("message",function(x){if(U.push(x.data),U.length===i){for(var H=0;H<i;++H)B[H].terminate();n(null,U)}}),B.push(L)}for(P=0;P<i;++P)B[P].postMessage(P)}(B,function(P,L){i.push(function p(i,n){for(var B=[],U=0;U<i;++U)for(var P=n[U],L=B[U]=[],x=0;x<i;++x)if(U!==x){var H=n[x];(P.st>H.st&&P.st<H.et||H.st>P.st&&H.st<P.et)&&L.push(x)}return B.reduce(function(F,j){return Math.max(F,j.length)},0)}(B,L)),I(i,n-1,B)})}([],5,16)},Vt.exports}function xt(){if(Lr)return Pt;Lr=1;var t=ue();fe(),Pt=t.cipher=t.cipher||{},t.cipher.algorithms=t.cipher.algorithms||{},t.cipher.createCipher=function(a,d){var R=a;if("string"==typeof R&&(R=t.cipher.getAlgorithm(R))&&(R=R()),!R)throw new Error("Unsupported algorithm: "+a);return new t.cipher.BlockCipher({algorithm:R,key:d,decrypt:!1})},t.cipher.createDecipher=function(a,d){var R=a;if("string"==typeof R&&(R=t.cipher.getAlgorithm(R))&&(R=R()),!R)throw new Error("Unsupported algorithm: "+a);return new t.cipher.BlockCipher({algorithm:R,key:d,decrypt:!0})},t.cipher.registerAlgorithm=function(a,d){a=a.toUpperCase(),t.cipher.algorithms[a]=d},t.cipher.getAlgorithm=function(a){return(a=a.toUpperCase())in t.cipher.algorithms?t.cipher.algorithms[a]:null};var e=t.cipher.BlockCipher=function(a){this.algorithm=a.algorithm,this.mode=this.algorithm.mode,this.blockSize=this.mode.blockSize,this._finish=!1,this._input=null,this.output=null,this._op=a.decrypt?this.mode.decrypt:this.mode.encrypt,this._decrypt=a.decrypt,this.algorithm.initialize(a)};return e.prototype.start=function(a){a=a||{};var d={};for(var R in a)d[R]=a[R];d.decrypt=this._decrypt,this._finish=!1,this._input=t.util.createBuffer(),this.output=a.output||t.util.createBuffer(),this.mode.start(d)},e.prototype.update=function(a){for(a&&this._input.putBuffer(a);!this._op.call(this.mode,this._input,this.output,this._finish)&&!this._finish;);this._input.compact()},e.prototype.finish=function(a){a&&("ECB"===this.mode.name||"CBC"===this.mode.name)&&(this.mode.pad=function(R){return a(this.blockSize,R,!1)},this.mode.unpad=function(R){return a(this.blockSize,R,!0)});var d={};return d.decrypt=this._decrypt,d.overflow=this._input.length()%this.blockSize,!(!this._decrypt&&this.mode.pad&&!this.mode.pad(this._input,d)||(this._finish=!0,this.update(),this._decrypt&&this.mode.unpad&&!this.mode.unpad(this.output,d))||this.mode.afterFinish&&!this.mode.afterFinish(this.output,d))},Pt}var Ur,Mt,Vr,Kt={exports:{}};function Dr(){if(Ur)return Kt.exports;Ur=1;var t=ue();fe(),t.cipher=t.cipher||{};var e=Kt.exports=t.cipher.modes=t.cipher.modes||{};function a(T,h){if("string"==typeof T&&(T=t.util.createBuffer(T)),t.util.isArray(T)&&T.length>4){var r=T;T=t.util.createBuffer();for(var v=0;v<r.length;++v)T.putByte(r[v])}if(T.length()<h)throw new Error("Invalid IV length; got "+T.length()+" bytes and expected "+h+" bytes.");if(!t.util.isArray(T)){var l=[],S=h/4;for(v=0;v<S;++v)l.push(T.getInt32());T=l}return T}function d(T){T[T.length-1]=T[T.length-1]+1&4294967295}function R(T){return[T/4294967296|0,4294967295&T]}return e.ecb=function(T){T=T||{},this.name="ECB",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},e.ecb.prototype.start=function(T){},e.ecb.prototype.encrypt=function(T,h,r){if(T.length()<this.blockSize&&!(r&&T.length()>0))return!0;for(var v=0;v<this._ints;++v)this._inBlock[v]=T.getInt32();for(this.cipher.encrypt(this._inBlock,this._outBlock),v=0;v<this._ints;++v)h.putInt32(this._outBlock[v])},e.ecb.prototype.decrypt=function(T,h,r){if(T.length()<this.blockSize&&!(r&&T.length()>0))return!0;for(var v=0;v<this._ints;++v)this._inBlock[v]=T.getInt32();for(this.cipher.decrypt(this._inBlock,this._outBlock),v=0;v<this._ints;++v)h.putInt32(this._outBlock[v])},e.ecb.prototype.pad=function(T,h){var r=T.length()===this.blockSize?this.blockSize:this.blockSize-T.length();return T.fillWithByte(r,r),!0},e.ecb.prototype.unpad=function(T,h){if(h.overflow>0)return!1;var r=T.length(),v=T.at(r-1);return!(v>this.blockSize<<2||(T.truncate(v),0))},e.cbc=function(T){T=T||{},this.name="CBC",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},e.cbc.prototype.start=function(T){if(null===T.iv){if(!this._prev)throw new Error("Invalid IV parameter.");this._iv=this._prev.slice(0)}else{if(!("iv"in T))throw new Error("Invalid IV parameter.");this._iv=a(T.iv,this.blockSize),this._prev=this._iv.slice(0)}},e.cbc.prototype.encrypt=function(T,h,r){if(T.length()<this.blockSize&&!(r&&T.length()>0))return!0;for(var v=0;v<this._ints;++v)this._inBlock[v]=this._prev[v]^T.getInt32();for(this.cipher.encrypt(this._inBlock,this._outBlock),v=0;v<this._ints;++v)h.putInt32(this._outBlock[v]);this._prev=this._outBlock},e.cbc.prototype.decrypt=function(T,h,r){if(T.length()<this.blockSize&&!(r&&T.length()>0))return!0;for(var v=0;v<this._ints;++v)this._inBlock[v]=T.getInt32();for(this.cipher.decrypt(this._inBlock,this._outBlock),v=0;v<this._ints;++v)h.putInt32(this._prev[v]^this._outBlock[v]);this._prev=this._inBlock.slice(0)},e.cbc.prototype.pad=function(T,h){var r=T.length()===this.blockSize?this.blockSize:this.blockSize-T.length();return T.fillWithByte(r,r),!0},e.cbc.prototype.unpad=function(T,h){if(h.overflow>0)return!1;var r=T.length(),v=T.at(r-1);return!(v>this.blockSize<<2||(T.truncate(v),0))},e.cfb=function(T){T=T||{},this.name="CFB",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialBlock=new Array(this._ints),this._partialOutput=t.util.createBuffer(),this._partialBytes=0},e.cfb.prototype.start=function(T){if(!("iv"in T))throw new Error("Invalid IV parameter.");this._iv=a(T.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},e.cfb.prototype.encrypt=function(T,h,r){var v=T.length();if(0===v)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&v>=this.blockSize)for(var l=0;l<this._ints;++l)this._inBlock[l]=T.getInt32()^this._outBlock[l],h.putInt32(this._inBlock[l]);else{var S=(this.blockSize-v)%this.blockSize;for(S>0&&(S=this.blockSize-S),this._partialOutput.clear(),l=0;l<this._ints;++l)this._partialBlock[l]=T.getInt32()^this._outBlock[l],this._partialOutput.putInt32(this._partialBlock[l]);if(S>0)T.read-=this.blockSize;else for(l=0;l<this._ints;++l)this._inBlock[l]=this._partialBlock[l];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),S>0&&!r)return h.putBytes(this._partialOutput.getBytes(S-this._partialBytes)),this._partialBytes=S,!0;h.putBytes(this._partialOutput.getBytes(v-this._partialBytes)),this._partialBytes=0}},e.cfb.prototype.decrypt=function(T,h,r){var v=T.length();if(0===v)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&v>=this.blockSize)for(var l=0;l<this._ints;++l)this._inBlock[l]=T.getInt32(),h.putInt32(this._inBlock[l]^this._outBlock[l]);else{var S=(this.blockSize-v)%this.blockSize;for(S>0&&(S=this.blockSize-S),this._partialOutput.clear(),l=0;l<this._ints;++l)this._partialBlock[l]=T.getInt32(),this._partialOutput.putInt32(this._partialBlock[l]^this._outBlock[l]);if(S>0)T.read-=this.blockSize;else for(l=0;l<this._ints;++l)this._inBlock[l]=this._partialBlock[l];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),S>0&&!r)return h.putBytes(this._partialOutput.getBytes(S-this._partialBytes)),this._partialBytes=S,!0;h.putBytes(this._partialOutput.getBytes(v-this._partialBytes)),this._partialBytes=0}},e.ofb=function(T){T=T||{},this.name="OFB",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=t.util.createBuffer(),this._partialBytes=0},e.ofb.prototype.start=function(T){if(!("iv"in T))throw new Error("Invalid IV parameter.");this._iv=a(T.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},e.ofb.prototype.encrypt=function(T,h,r){var v=T.length();if(0===T.length())return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&v>=this.blockSize)for(var l=0;l<this._ints;++l)h.putInt32(T.getInt32()^this._outBlock[l]),this._inBlock[l]=this._outBlock[l];else{var S=(this.blockSize-v)%this.blockSize;for(S>0&&(S=this.blockSize-S),this._partialOutput.clear(),l=0;l<this._ints;++l)this._partialOutput.putInt32(T.getInt32()^this._outBlock[l]);if(S>0)T.read-=this.blockSize;else for(l=0;l<this._ints;++l)this._inBlock[l]=this._outBlock[l];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),S>0&&!r)return h.putBytes(this._partialOutput.getBytes(S-this._partialBytes)),this._partialBytes=S,!0;h.putBytes(this._partialOutput.getBytes(v-this._partialBytes)),this._partialBytes=0}},e.ofb.prototype.decrypt=e.ofb.prototype.encrypt,e.ctr=function(T){T=T||{},this.name="CTR",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=t.util.createBuffer(),this._partialBytes=0},e.ctr.prototype.start=function(T){if(!("iv"in T))throw new Error("Invalid IV parameter.");this._iv=a(T.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0},e.ctr.prototype.encrypt=function(T,h,r){var v=T.length();if(0===v)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&v>=this.blockSize)for(var l=0;l<this._ints;++l)h.putInt32(T.getInt32()^this._outBlock[l]);else{var S=(this.blockSize-v)%this.blockSize;for(S>0&&(S=this.blockSize-S),this._partialOutput.clear(),l=0;l<this._ints;++l)this._partialOutput.putInt32(T.getInt32()^this._outBlock[l]);if(S>0&&(T.read-=this.blockSize),this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),S>0&&!r)return h.putBytes(this._partialOutput.getBytes(S-this._partialBytes)),this._partialBytes=S,!0;h.putBytes(this._partialOutput.getBytes(v-this._partialBytes)),this._partialBytes=0}d(this._inBlock)},e.ctr.prototype.decrypt=e.ctr.prototype.encrypt,e.gcm=function(T){T=T||{},this.name="GCM",this.cipher=T.cipher,this.blockSize=T.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints),this._partialOutput=t.util.createBuffer(),this._partialBytes=0,this._R=3774873600},e.gcm.prototype.start=function(T){if(!("iv"in T))throw new Error("Invalid IV parameter.");var r,h=t.util.createBuffer(T.iv);if(this._cipherLength=0,r="additionalData"in T?t.util.createBuffer(T.additionalData):t.util.createBuffer(),this._tagLength="tagLength"in T?T.tagLength:128,this._tag=null,T.decrypt&&(this._tag=t.util.createBuffer(T.tag).getBytes(),this._tag.length!==this._tagLength/8))throw new Error("Authentication tag does not match tag length.");this._hashBlock=new Array(this._ints),this.tag=null,this._hashSubkey=new Array(this._ints),this.cipher.encrypt([0,0,0,0],this._hashSubkey),this.componentBits=4,this._m=this.generateHashTable(this._hashSubkey,this.componentBits);var v=h.length();if(12===v)this._j0=[h.getInt32(),h.getInt32(),h.getInt32(),1];else{for(this._j0=[0,0,0,0];h.length()>0;)this._j0=this.ghash(this._hashSubkey,this._j0,[h.getInt32(),h.getInt32(),h.getInt32(),h.getInt32()]);this._j0=this.ghash(this._hashSubkey,this._j0,[0,0].concat(R(8*v)))}this._inBlock=this._j0.slice(0),d(this._inBlock),this._partialBytes=0,r=t.util.createBuffer(r),this._aDataLength=R(8*r.length());var l=r.length()%this.blockSize;for(l&&r.fillWithByte(0,this.blockSize-l),this._s=[0,0,0,0];r.length()>0;)this._s=this.ghash(this._hashSubkey,this._s,[r.getInt32(),r.getInt32(),r.getInt32(),r.getInt32()])},e.gcm.prototype.encrypt=function(T,h,r){var v=T.length();if(0===v)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&v>=this.blockSize){for(var l=0;l<this._ints;++l)h.putInt32(this._outBlock[l]^=T.getInt32());this._cipherLength+=this.blockSize}else{var S=(this.blockSize-v)%this.blockSize;for(S>0&&(S=this.blockSize-S),this._partialOutput.clear(),l=0;l<this._ints;++l)this._partialOutput.putInt32(T.getInt32()^this._outBlock[l]);if(S<=0||r){if(r){var D=v%this.blockSize;this._cipherLength+=D,this._partialOutput.truncate(this.blockSize-D)}else this._cipherLength+=this.blockSize;for(l=0;l<this._ints;++l)this._outBlock[l]=this._partialOutput.getInt32();this._partialOutput.read-=this.blockSize}if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),S>0&&!r)return T.read-=this.blockSize,h.putBytes(this._partialOutput.getBytes(S-this._partialBytes)),this._partialBytes=S,!0;h.putBytes(this._partialOutput.getBytes(v-this._partialBytes)),this._partialBytes=0}this._s=this.ghash(this._hashSubkey,this._s,this._outBlock),d(this._inBlock)},e.gcm.prototype.decrypt=function(T,h,r){var v=T.length();if(v<this.blockSize&&!(r&&v>0))return!0;this.cipher.encrypt(this._inBlock,this._outBlock),d(this._inBlock),this._hashBlock[0]=T.getInt32(),this._hashBlock[1]=T.getInt32(),this._hashBlock[2]=T.getInt32(),this._hashBlock[3]=T.getInt32(),this._s=this.ghash(this._hashSubkey,this._s,this._hashBlock);for(var l=0;l<this._ints;++l)h.putInt32(this._outBlock[l]^this._hashBlock[l]);this._cipherLength+=v<this.blockSize?v%this.blockSize:this.blockSize},e.gcm.prototype.afterFinish=function(T,h){var r=!0;h.decrypt&&h.overflow&&T.truncate(this.blockSize-h.overflow),this.tag=t.util.createBuffer();var v=this._aDataLength.concat(R(8*this._cipherLength));this._s=this.ghash(this._hashSubkey,this._s,v);var l=[];this.cipher.encrypt(this._j0,l);for(var S=0;S<this._ints;++S)this.tag.putInt32(this._s[S]^l[S]);return this.tag.truncate(this.tag.length()%(this._tagLength/8)),h.decrypt&&this.tag.bytes()!==this._tag&&(r=!1),r},e.gcm.prototype.multiply=function(T,h){for(var r=[0,0,0,0],v=h.slice(0),l=0;l<128;++l)T[l/32|0]&1<<31-l%32&&(r[0]^=v[0],r[1]^=v[1],r[2]^=v[2],r[3]^=v[3]),this.pow(v,v);return r},e.gcm.prototype.pow=function(T,h){for(var r=1&T[3],v=3;v>0;--v)h[v]=T[v]>>>1|(1&T[v-1])<<31;h[0]=T[0]>>>1,r&&(h[0]^=this._R)},e.gcm.prototype.tableMultiply=function(T){for(var h=[0,0,0,0],r=0;r<32;++r){var S=this._m[r][T[r/8|0]>>>4*(7-r%8)&15];h[0]^=S[0],h[1]^=S[1],h[2]^=S[2],h[3]^=S[3]}return h},e.gcm.prototype.ghash=function(T,h,r){return h[0]^=r[0],h[1]^=r[1],h[2]^=r[2],h[3]^=r[3],this.tableMultiply(h)},e.gcm.prototype.generateHashTable=function(T,h){for(var r=8/h,v=4*r,l=16*r,S=new Array(l),D=0;D<l;++D){var E=[0,0,0,0];E[D/v|0]=1<<h-1<<(v-1-D%v)*h,S[D]=this.generateSubHashTable(this.multiply(E,T),h)}return S},e.gcm.prototype.generateSubHashTable=function(T,h){var r=1<<h,v=r>>>1,l=new Array(r);l[v]=T.slice(0);for(var S=v>>>1;S>0;)this.pow(l[2*S],l[S]=[]),S>>=1;for(S=2;S<v;){for(var D=1;D<S;++D){var E=l[S],u=l[D];l[S+D]=[E[0]^u[0],E[1]^u[1],E[2]^u[2],E[3]^u[3]]}S*=2}for(l[0]=[0,0,0,0],S=v+1;S<r;++S){var g=l[S^v];l[S]=[T[0]^g[0],T[1]^g[1],T[2]^g[2],T[3]^g[3]]}return l},Kt.exports}function lt(){if(Vr)return Mt;Vr=1;var t=ue();function e(u,g){t.cipher.registerAlgorithm(u,function(){return new t.aes.Algorithm(u,g)})}xt(),Dr(),fe(),Mt=t.aes=t.aes||{},t.aes.startEncrypting=function(u,g,C,m){var s=E({key:u,output:C,decrypt:!1,mode:m});return s.start(g),s},t.aes.createEncryptionCipher=function(u,g){return E({key:u,output:null,decrypt:!1,mode:g})},t.aes.startDecrypting=function(u,g,C,m){var s=E({key:u,output:C,decrypt:!0,mode:m});return s.start(g),s},t.aes.createDecryptionCipher=function(u,g){return E({key:u,output:null,decrypt:!0,mode:g})},t.aes.Algorithm=function(u,g){a||l();var C=this;C.name=u,C.mode=new g({blockSize:16,cipher:{encrypt:function(m,s){return D(C._w,m,s,!1)},decrypt:function(m,s){return D(C._w,m,s,!0)}}}),C._init=!1},t.aes.Algorithm.prototype.initialize=function(u){if(!this._init){var C,g=u.key;if("string"!=typeof g||16!==g.length&&24!==g.length&&32!==g.length){if(t.util.isArray(g)&&(16===g.length||24===g.length||32===g.length)){C=g,g=t.util.createBuffer();for(var m=0;m<C.length;++m)g.putByte(C[m])}}else g=t.util.createBuffer(g);if(!t.util.isArray(g)){C=g,g=[];var s=C.length();if(16===s||24===s||32===s)for(s>>>=2,m=0;m<s;++m)g.push(C.getInt32())}if(!t.util.isArray(g)||4!==g.length&&6!==g.length&&8!==g.length)throw new Error("Invalid key parameter.");var y=-1!==["CFB","OFB","CTR","GCM"].indexOf(this.mode.name);this._w=S(g,u.decrypt&&!y),this._init=!0}},t.aes._expandKey=function(u,g){return a||l(),S(u,g)},t.aes._updateBlock=D,e("AES-ECB",t.cipher.modes.ecb),e("AES-CBC",t.cipher.modes.cbc),e("AES-CFB",t.cipher.modes.cfb),e("AES-OFB",t.cipher.modes.ofb),e("AES-CTR",t.cipher.modes.ctr),e("AES-GCM",t.cipher.modes.gcm);var R,T,h,r,v,a=!1,d=4;function l(){a=!0,h=[0,1,2,4,8,16,32,64,128,27,54];for(var u=new Array(256),g=0;g<128;++g)u[g]=g<<1,u[g+128]=g+128<<1^283;for(R=new Array(256),T=new Array(256),r=new Array(4),v=new Array(4),g=0;g<4;++g)r[g]=new Array(256),v[g]=new Array(256);var s,f,y,I,_,p,i,C=0,m=0;for(g=0;g<256;++g){R[C]=I=(I=m^m<<1^m<<2^m<<3^m<<4)>>8^255&I^99,T[I]=C,p=(_=u[I])<<24^I<<16^I<<8^I^_,i=((s=u[C])^(f=u[s])^(y=u[f]))<<24^(C^y)<<16^(C^f^y)<<8^C^s^y;for(var n=0;n<4;++n)r[n][C]=p,v[n][I]=i,p=p<<24|p>>>8,i=i<<24|i>>>8;0===C?C=m=1:(C=s^u[u[u[s^y]]],m^=u[u[m]])}}function S(u,g){for(var m,C=u.slice(0),s=1,f=C.length,I=d*(f+6+1),_=f;_<I;++_)m=C[_-1],_%f==0?(m=R[m>>>16&255]<<24^R[m>>>8&255]<<16^R[255&m]<<8^R[m>>>24]^h[s]<<24,s++):f>6&&_%f==4&&(m=R[m>>>24]<<24^R[m>>>16&255]<<16^R[m>>>8&255]<<8^R[255&m]),C[_]=C[_-f]^m;if(g){for(var p,i=v[0],n=v[1],B=v[2],U=v[3],P=C.slice(0),L=(_=0,(I=C.length)-d);_<I;_+=d,L-=d)if(0===_||_===I-d)P[_]=C[L],P[_+1]=C[L+3],P[_+2]=C[L+2],P[_+3]=C[L+1];else for(var x=0;x<d;++x)P[_+(3&-x)]=i[R[(p=C[L+x])>>>24]]^n[R[p>>>16&255]]^B[R[p>>>8&255]]^U[R[255&p]];C=P}return C}function D(u,g,C,m){var f,y,I,_,p,i,n,B,U,P,L,x,s=u.length/4-1;m?(f=v[0],y=v[1],I=v[2],_=v[3],p=T):(f=r[0],y=r[1],I=r[2],_=r[3],p=R),i=g[0]^u[0],n=g[m?3:1]^u[1],B=g[2]^u[2],U=g[m?1:3]^u[3];for(var H=3,F=1;F<s;++F)P=f[i>>>24]^y[n>>>16&255]^I[B>>>8&255]^_[255&U]^u[++H],L=f[n>>>24]^y[B>>>16&255]^I[U>>>8&255]^_[255&i]^u[++H],x=f[B>>>24]^y[U>>>16&255]^I[i>>>8&255]^_[255&n]^u[++H],U=f[U>>>24]^y[i>>>16&255]^I[n>>>8&255]^_[255&B]^u[++H],i=P,n=L,B=x;C[0]=p[i>>>24]<<24^p[n>>>16&255]<<16^p[B>>>8&255]<<8^p[255&U]^u[++H],C[m?3:1]=p[n>>>24]<<24^p[B>>>16&255]<<16^p[U>>>8&255]<<8^p[255&i]^u[++H],C[2]=p[B>>>24]<<24^p[U>>>16&255]<<16^p[i>>>8&255]<<8^p[255&n]^u[++H],C[m?1:3]=p[U>>>24]<<24^p[i>>>16&255]<<16^p[n>>>8&255]<<8^p[255&B]^u[++H]}function E(u){var m,C="AES-"+((u=u||{}).mode||"CBC").toUpperCase(),s=(m=u.decrypt?t.cipher.createDecipher(C,u.key):t.cipher.createCipher(C,u.key)).start;return m.start=function(f,y){var I=null;y instanceof t.util.ByteBuffer&&(I=y,y={}),(y=y||{}).output=I,y.iv=f,s.call(m,y)},m}return Mt}var Or,Pr,Ft={exports:{}},Ht={exports:{}},jt={exports:{}};function pt(){if(Or)return jt.exports;Or=1;var t=ue();t.pki=t.pki||{};var e=jt.exports=t.pki.oids=t.oids=t.oids||{};function a(R,T){e[R]=T,e[T]=R}function d(R,T){e[R]=T}return a("1.2.840.113549.1.1.1","rsaEncryption"),a("1.2.840.113549.1.1.4","md5WithRSAEncryption"),a("1.2.840.113549.1.1.5","sha1WithRSAEncryption"),a("1.2.840.113549.1.1.7","RSAES-OAEP"),a("1.2.840.113549.1.1.8","mgf1"),a("1.2.840.113549.1.1.9","pSpecified"),a("1.2.840.113549.1.1.10","RSASSA-PSS"),a("1.2.840.113549.1.1.11","sha256WithRSAEncryption"),a("1.2.840.113549.1.1.12","sha384WithRSAEncryption"),a("1.2.840.113549.1.1.13","sha512WithRSAEncryption"),a("***********","EdDSA25519"),a("1.2.840.10040.4.3","dsa-with-sha1"),a("********.2.7","desCBC"),a("********.2.26","sha1"),a("********.2.29","sha1WithRSASignature"),a("2.16.840.*********.2.1","sha256"),a("2.16.840.*********.2.2","sha384"),a("2.16.840.*********.2.3","sha512"),a("2.16.840.*********.2.4","sha224"),a("2.16.840.*********.2.5","sha512-224"),a("2.16.840.*********.2.6","sha512-256"),a("1.2.840.113549.2.2","md2"),a("1.2.840.113549.2.5","md5"),a("1.2.840.113549.1.7.1","data"),a("1.2.840.113549.1.7.2","signedData"),a("1.2.840.113549.1.7.3","envelopedData"),a("1.2.840.113549.1.7.4","signedAndEnvelopedData"),a("1.2.840.113549.1.7.5","digestedData"),a("1.2.840.113549.1.7.6","encryptedData"),a("1.2.840.113549.1.9.1","emailAddress"),a("1.2.840.113549.1.9.2","unstructuredName"),a("1.2.840.113549.1.9.3","contentType"),a("1.2.840.113549.1.9.4","messageDigest"),a("1.2.840.113549.1.9.5","signingTime"),a("1.2.840.113549.1.9.6","counterSignature"),a("1.2.840.113549.1.9.7","challengePassword"),a("1.2.840.113549.1.9.8","unstructuredAddress"),a("1.2.840.113549.1.9.14","extensionRequest"),a("1.2.840.113549.1.9.20","friendlyName"),a("1.2.840.113549.1.9.21","localKeyId"),a("1.2.840.113549.********","x509Certificate"),a("1.2.840.113549.*********.1","keyBag"),a("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),a("1.2.840.113549.*********.3","certBag"),a("1.2.840.113549.*********.4","crlBag"),a("1.2.840.113549.*********.5","secretBag"),a("1.2.840.113549.*********.6","safeContentsBag"),a("1.2.840.113549.1.5.13","pkcs5PBES2"),a("1.2.840.113549.1.5.12","pkcs5PBKDF2"),a("1.2.840.113549.********","pbeWithSHAAnd128BitRC4"),a("1.2.840.113549.********","pbeWithSHAAnd40BitRC4"),a("1.2.840.113549.********","pbeWithSHAAnd3-KeyTripleDES-CBC"),a("1.2.840.113549.********","pbeWithSHAAnd2-KeyTripleDES-CBC"),a("1.2.840.113549.********","pbeWithSHAAnd128BitRC2-CBC"),a("1.2.840.113549.********","pbewithSHAAnd40BitRC2-CBC"),a("1.2.840.113549.2.7","hmacWithSHA1"),a("1.2.840.113549.2.8","hmacWithSHA224"),a("1.2.840.113549.2.9","hmacWithSHA256"),a("1.2.840.113549.2.10","hmacWithSHA384"),a("1.2.840.113549.2.11","hmacWithSHA512"),a("1.2.840.113549.3.7","des-EDE3-CBC"),a("2.16.840.*********.1.2","aes128-CBC"),a("2.16.840.*********.1.22","aes192-CBC"),a("2.16.840.*********.1.42","aes256-CBC"),a("*******","commonName"),a("*******","surname"),a("*******","serialNumber"),a("*******","countryName"),a("*******","localityName"),a("*******","stateOrProvinceName"),a("*******","streetAddress"),a("********","organizationName"),a("********","organizationalUnitName"),a("********","title"),a("********","description"),a("********","businessCategory"),a("********","postalCode"),a("*******2","givenName"),a("*******.4.1.311.********","jurisdictionOfIncorporationStateOrProvinceName"),a("*******.4.1.311.********","jurisdictionOfIncorporationCountryName"),a("2.16.840.1.113730.1.1","nsCertType"),a("2.16.840.1.113730.1.13","nsComment"),d("********","authorityKeyIdentifier"),d("********","keyAttributes"),d("********","certificatePolicies"),d("********","keyUsageRestriction"),d("********","policyMapping"),d("********","subtreesConstraint"),d("********","subjectAltName"),d("********","issuerAltName"),d("********","subjectDirectoryAttributes"),d("*********","basicConstraints"),d("*********","nameConstraints"),d("*********","policyConstraints"),d("*********","basicConstraints"),a("*********","subjectKeyIdentifier"),a("*********","keyUsage"),d("*********","privateKeyUsagePeriod"),a("*********","subjectAltName"),a("*********","issuerAltName"),a("*********","basicConstraints"),d("*********","cRLNumber"),d("********1","cRLReason"),d("********2","expirationDate"),d("********3","instructionCode"),d("********4","invalidityDate"),d("********5","cRLDistributionPoints"),d("********6","issuingDistributionPoint"),d("********7","deltaCRLIndicator"),d("********8","issuingDistributionPoint"),d("********9","certificateIssuer"),d("********0","nameConstraints"),a("********1","cRLDistributionPoints"),a("********2","certificatePolicies"),d("********3","policyMappings"),d("********4","policyConstraints"),a("********5","authorityKeyIdentifier"),d("********6","policyConstraints"),a("********7","extKeyUsage"),d("********6","freshestCRL"),d("********4","inhibitAnyPolicy"),a("*******.4.1.11129.2.4.2","timestampList"),a("*******.5.5.7.1.1","authorityInfoAccess"),a("*******.5.5.7.3.1","serverAuth"),a("*******.5.5.7.3.2","clientAuth"),a("*******.5.5.7.3.3","codeSigning"),a("*******.5.5.7.3.4","emailProtection"),a("*******.5.5.7.3.8","timeStamping"),jt.exports}function rt(){if(Pr)return Ht.exports;Pr=1;var t=ue();fe(),pt();var e=Ht.exports=t.asn1=t.asn1||{};function a(h,r,v){if(v>r){var l=new Error("Too few bytes to parse DER.");throw l.available=h.length(),l.remaining=r,l.requested=v,l}}e.Class={UNIVERSAL:0,APPLICATION:64,CONTEXT_SPECIFIC:128,PRIVATE:192},e.Type={NONE:0,BOOLEAN:1,INTEGER:2,BITSTRING:3,OCTETSTRING:4,NULL:5,OID:6,ODESC:7,EXTERNAL:8,REAL:9,ENUMERATED:10,EMBEDDED:11,UTF8:12,ROID:13,SEQUENCE:16,SET:17,PRINTABLESTRING:19,IA5STRING:22,UTCTIME:23,GENERALIZEDTIME:24,BMPSTRING:30},e.create=function(h,r,v,l,S){if(t.util.isArray(l)){for(var D=[],E=0;E<l.length;++E)void 0!==l[E]&&D.push(l[E]);l=D}var u={tagClass:h,type:r,constructed:v,composed:v||t.util.isArray(l),value:l};return S&&"bitStringContents"in S&&(u.bitStringContents=S.bitStringContents,u.original=e.copy(u)),u},e.copy=function(h,r){var v;if(t.util.isArray(h)){v=[];for(var l=0;l<h.length;++l)v.push(e.copy(h[l],r));return v}return"string"==typeof h?h:(v={tagClass:h.tagClass,type:h.type,constructed:h.constructed,composed:h.composed,value:e.copy(h.value,r)},r&&!r.excludeBitStringContents&&(v.bitStringContents=h.bitStringContents),v)},e.equals=function(h,r,v){if(t.util.isArray(h)){if(!t.util.isArray(r)||h.length!==r.length)return!1;for(var l=0;l<h.length;++l)if(!e.equals(h[l],r[l]))return!1;return!0}if(typeof h!=typeof r)return!1;if("string"==typeof h)return h===r;var S=h.tagClass===r.tagClass&&h.type===r.type&&h.constructed===r.constructed&&h.composed===r.composed&&e.equals(h.value,r.value);return v&&v.includeBitStringContents&&(S=S&&h.bitStringContents===r.bitStringContents),S},e.getBerValueLength=function(h){var r=h.getByte();if(128!==r)return 128&r?h.getInt((127&r)<<3):r};var d=function(h,r){var v=h.getByte();if(r--,128!==v){var l;if(128&v){var D=127&v;a(h,r,D),l=h.getInt(D<<3)}else l=v;if(l<0)throw new Error("Negative length: "+l);return l}};function R(h,r,v,l){var S;a(h,r,2);var D=h.getByte();r--;var E=192&D,u=31&D;S=h.length();var g=d(h,r);if(r-=S-h.length(),void 0!==g&&g>r){if(l.strict){var C=new Error("Too few bytes to read ASN.1 value.");throw C.available=h.length(),C.remaining=r,C.requested=g,C}g=r}var m,s,f=32==(32&D);if(f)if(m=[],void 0===g)for(;;){if(a(h,r,2),"\0\0"===h.bytes(2)){h.getBytes(2),r-=2;break}S=h.length(),m.push(R(h,r,v+1,l)),r-=S-h.length()}else for(;g>0;)S=h.length(),m.push(R(h,g,v+1,l)),r-=S-h.length(),g-=S-h.length();if(void 0===m&&E===e.Class.UNIVERSAL&&u===e.Type.BITSTRING&&(s=h.bytes(g)),void 0===m&&l.decodeBitStrings&&E===e.Class.UNIVERSAL&&u===e.Type.BITSTRING&&g>1){var y=h.read,I=r,_=0;if(u===e.Type.BITSTRING&&(a(h,r,1),_=h.getByte(),r--),0===_)try{S=h.length();var i=R(h,r,v+1,{strict:!0,decodeBitStrings:!0}),n=S-h.length();r-=n,u==e.Type.BITSTRING&&n++;var B=i.tagClass;n===g&&(B===e.Class.UNIVERSAL||B===e.Class.CONTEXT_SPECIFIC)&&(m=[i])}catch{}void 0===m&&(h.read=y,r=I)}if(void 0===m){if(void 0===g){if(l.strict)throw new Error("Non-constructed ASN.1 object of indefinite length.");g=r}if(u===e.Type.BMPSTRING)for(m="";g>0;g-=2)a(h,r,2),m+=String.fromCharCode(h.getInt16()),r-=2;else m=h.getBytes(g),r-=g}return e.create(E,u,f,m,void 0===s?null:{bitStringContents:s})}e.fromDer=function(h,r){void 0===r&&(r={strict:!0,parseAllBytes:!0,decodeBitStrings:!0}),"boolean"==typeof r&&(r={strict:r,parseAllBytes:!0,decodeBitStrings:!0}),"strict"in r||(r.strict=!0),"parseAllBytes"in r||(r.parseAllBytes=!0),"decodeBitStrings"in r||(r.decodeBitStrings=!0),"string"==typeof h&&(h=t.util.createBuffer(h));var v=h.length(),l=R(h,h.length(),0,r);if(r.parseAllBytes&&0!==h.length()){var S=new Error("Unparsed DER bytes remain after ASN.1 parsing.");throw S.byteCount=v,S.remaining=h.length(),S}return l},e.toDer=function(h){var r=t.util.createBuffer(),v=h.tagClass|h.type,l=t.util.createBuffer(),S=!1;if("bitStringContents"in h&&(S=!0,h.original&&(S=e.equals(h,h.original))),S)l.putBytes(h.bitStringContents);else if(h.composed){h.constructed?v|=32:l.putByte(0);for(var D=0;D<h.value.length;++D)void 0!==h.value[D]&&l.putBuffer(e.toDer(h.value[D]))}else if(h.type===e.Type.BMPSTRING)for(D=0;D<h.value.length;++D)l.putInt16(h.value.charCodeAt(D));else h.type===e.Type.INTEGER&&h.value.length>1&&(0===h.value.charCodeAt(0)&&!(128&h.value.charCodeAt(1))||255===h.value.charCodeAt(0)&&128==(128&h.value.charCodeAt(1)))?l.putBytes(h.value.substr(1)):l.putBytes(h.value);if(r.putByte(v),l.length()<=127)r.putByte(127&l.length());else{var E=l.length(),u="";do{u+=String.fromCharCode(255&E),E>>>=8}while(E>0);for(r.putByte(128|u.length),D=u.length-1;D>=0;--D)r.putByte(u.charCodeAt(D))}return r.putBuffer(l),r},e.oidToDer=function(h){var r=h.split("."),v=t.util.createBuffer();v.putByte(40*parseInt(r[0],10)+parseInt(r[1],10));for(var l,S,D,E,u=2;u<r.length;++u){l=!0,S=[],D=parseInt(r[u],10);do{E=127&D,D>>>=7,l||(E|=128),S.push(E),l=!1}while(D>0);for(var g=S.length-1;g>=0;--g)v.putByte(S[g])}return v},e.derToOid=function(h){var r;"string"==typeof h&&(h=t.util.createBuffer(h));var v=h.getByte();r=Math.floor(v/40)+"."+v%40;for(var l=0;h.length()>0;)l<<=7,128&(v=h.getByte())?l+=127&v:(r+="."+(l+v),l=0);return r},e.utcTimeToDate=function(h){var r=new Date,v=parseInt(h.substr(0,2),10);v=v>=50?1900+v:2e3+v;var l=parseInt(h.substr(2,2),10)-1,S=parseInt(h.substr(4,2),10),D=parseInt(h.substr(6,2),10),E=parseInt(h.substr(8,2),10),u=0;if(h.length>11){var g=h.charAt(10),C=10;"+"!==g&&"-"!==g&&(u=parseInt(h.substr(10,2),10),C+=2)}if(r.setUTCFullYear(v,l,S),r.setUTCHours(D,E,u,0),C&&("+"===(g=h.charAt(C))||"-"===g)){var f=60*parseInt(h.substr(C+1,2),10)+parseInt(h.substr(C+4,2),10);f*=6e4,r.setTime("+"===g?+r-f:+r+f)}return r},e.generalizedTimeToDate=function(h){var r=new Date,v=parseInt(h.substr(0,4),10),l=parseInt(h.substr(4,2),10)-1,S=parseInt(h.substr(6,2),10),D=parseInt(h.substr(8,2),10),E=parseInt(h.substr(10,2),10),u=parseInt(h.substr(12,2),10),g=0,C=0,m=!1;"Z"===h.charAt(h.length-1)&&(m=!0);var s=h.length-5,f=h.charAt(s);return"+"!==f&&"-"!==f||(C=60*parseInt(h.substr(s+1,2),10)+parseInt(h.substr(s+4,2),10),C*=6e4,"+"===f&&(C*=-1),m=!0),"."===h.charAt(14)&&(g=1e3*parseFloat(h.substr(14),10)),m?(r.setUTCFullYear(v,l,S),r.setUTCHours(D,E,u,g),r.setTime(+r+C)):(r.setFullYear(v,l,S),r.setHours(D,E,u,g)),r},e.dateToUtcTime=function(h){if("string"==typeof h)return h;var r="",v=[];v.push((""+h.getUTCFullYear()).substr(2)),v.push(""+(h.getUTCMonth()+1)),v.push(""+h.getUTCDate()),v.push(""+h.getUTCHours()),v.push(""+h.getUTCMinutes()),v.push(""+h.getUTCSeconds());for(var l=0;l<v.length;++l)v[l].length<2&&(r+="0"),r+=v[l];return r+"Z"},e.dateToGeneralizedTime=function(h){if("string"==typeof h)return h;var r="",v=[];v.push(""+h.getUTCFullYear()),v.push(""+(h.getUTCMonth()+1)),v.push(""+h.getUTCDate()),v.push(""+h.getUTCHours()),v.push(""+h.getUTCMinutes()),v.push(""+h.getUTCSeconds());for(var l=0;l<v.length;++l)v[l].length<2&&(r+="0"),r+=v[l];return r+"Z"},e.integerToDer=function(h){var r=t.util.createBuffer();if(h>=-128&&h<128)return r.putSignedInt(h,8);if(h>=-32768&&h<32768)return r.putSignedInt(h,16);if(h>=-8388608&&h<8388608)return r.putSignedInt(h,24);if(h>=-2147483648&&h<2147483648)return r.putSignedInt(h,32);var v=new Error("Integer too large; max is 32-bits.");throw v.integer=h,v},e.derToInteger=function(h){"string"==typeof h&&(h=t.util.createBuffer(h));var r=8*h.length();if(r>32)throw new Error("Integer too large; max is 32-bits.");return h.getSignedInt(r)},e.validate=function(h,r,v,l){var S=!1;if((h.tagClass===r.tagClass||typeof r.tagClass>"u")&&(h.type===r.type||typeof r.type>"u"))if(h.constructed===r.constructed||typeof r.constructed>"u"){if(S=!0,r.value&&t.util.isArray(r.value))for(var D=0,E=0;S&&E<r.value.length;++E)S=r.value[E].optional||!1,h.value[D]&&((S=e.validate(h.value[D],r.value[E],v,l))?++D:r.value[E].optional&&(S=!0)),!S&&l&&l.push("["+r.name+'] Tag class "'+r.tagClass+'", type "'+r.type+'" expected value length "'+r.value.length+'", got "'+h.value.length+'"');if(S&&v&&(r.capture&&(v[r.capture]=h.value),r.captureAsn1&&(v[r.captureAsn1]=h),r.captureBitStringContents&&"bitStringContents"in h&&(v[r.captureBitStringContents]=h.bitStringContents),r.captureBitStringValue&&"bitStringContents"in h))if(h.bitStringContents.length<2)v[r.captureBitStringValue]="";else{if(0!==h.bitStringContents.charCodeAt(0))throw new Error("captureBitStringValue only supported for zero unused bits");v[r.captureBitStringValue]=h.bitStringContents.slice(1)}}else l&&l.push("["+r.name+'] Expected constructed "'+r.constructed+'", got "'+h.constructed+'"');else l&&(h.tagClass!==r.tagClass&&l.push("["+r.name+'] Expected tag class "'+r.tagClass+'", got "'+h.tagClass+'"'),h.type!==r.type&&l.push("["+r.name+'] Expected type "'+r.type+'", got "'+h.type+'"'));return S};var T=/[^\\u0000-\\u00ff]/;return e.prettyPrint=function(h,r,v){var l="";v=v||2,(r=r||0)>0&&(l+="\n");for(var S="",D=0;D<r*v;++D)S+=" ";switch(l+=S+"Tag: ",h.tagClass){case e.Class.UNIVERSAL:l+="Universal:";break;case e.Class.APPLICATION:l+="Application:";break;case e.Class.CONTEXT_SPECIFIC:l+="Context-Specific:";break;case e.Class.PRIVATE:l+="Private:"}if(h.tagClass===e.Class.UNIVERSAL)switch(l+=h.type,h.type){case e.Type.NONE:l+=" (None)";break;case e.Type.BOOLEAN:l+=" (Boolean)";break;case e.Type.INTEGER:l+=" (Integer)";break;case e.Type.BITSTRING:l+=" (Bit string)";break;case e.Type.OCTETSTRING:l+=" (Octet string)";break;case e.Type.NULL:l+=" (Null)";break;case e.Type.OID:l+=" (Object Identifier)";break;case e.Type.ODESC:l+=" (Object Descriptor)";break;case e.Type.EXTERNAL:l+=" (External or Instance of)";break;case e.Type.REAL:l+=" (Real)";break;case e.Type.ENUMERATED:l+=" (Enumerated)";break;case e.Type.EMBEDDED:l+=" (Embedded PDV)";break;case e.Type.UTF8:l+=" (UTF8)";break;case e.Type.ROID:l+=" (Relative Object Identifier)";break;case e.Type.SEQUENCE:l+=" (Sequence)";break;case e.Type.SET:l+=" (Set)";break;case e.Type.PRINTABLESTRING:l+=" (Printable String)";break;case e.Type.IA5String:l+=" (IA5String (ASCII))";break;case e.Type.UTCTIME:l+=" (UTC time)";break;case e.Type.GENERALIZEDTIME:l+=" (Generalized time)";break;case e.Type.BMPSTRING:l+=" (BMP String)"}else l+=h.type;if(l+="\n",l+=S+"Constructed: "+h.constructed+"\n",h.composed){var E=0,u="";for(D=0;D<h.value.length;++D)void 0!==h.value[D]&&(E+=1,u+=e.prettyPrint(h.value[D],r+1,v),D+1<h.value.length&&(u+=","));l+=S+"Sub values: "+E+u}else{if(l+=S+"Value: ",h.type===e.Type.OID){var g=e.derToOid(h.value);l+=g,t.pki&&t.pki.oids&&g in t.pki.oids&&(l+=" ("+t.pki.oids[g]+") ")}if(h.type===e.Type.INTEGER)try{l+=e.derToInteger(h.value)}catch{l+="0x"+t.util.bytesToHex(h.value)}else if(h.type===e.Type.BITSTRING){if(l+=h.value.length>1?"0x"+t.util.bytesToHex(h.value.slice(1)):"(none)",h.value.length>0){var C=h.value.charCodeAt(0);1==C?l+=" (1 unused bit shown)":C>1&&(l+=" ("+C+" unused bits shown)")}}else if(h.type===e.Type.OCTETSTRING)T.test(h.value)||(l+="("+h.value+") "),l+="0x"+t.util.bytesToHex(h.value);else if(h.type===e.Type.UTF8)try{l+=t.util.decodeUtf8(h.value)}catch(m){if("URI malformed"!==m.message)throw m;l+="0x"+t.util.bytesToHex(h.value)+" (malformed UTF8)"}else h.type===e.Type.PRINTABLESTRING||h.type===e.Type.IA5String?l+=h.value:T.test(h.value)?l+="0x"+t.util.bytesToHex(h.value):l+=0===h.value.length?"[null]":h.value}return l},Ht.exports}var qt,xr,Kr,Gt={exports:{}};function nt(){if(xr)return qt;xr=1;var t=ue();return qt=t.md=t.md||{},t.md.algorithms=t.md.algorithms||{},qt}function Et(){if(Kr)return Gt.exports;Kr=1;var t=ue();return nt(),fe(),(Gt.exports=t.hmac=t.hmac||{}).create=function(){var a=null,d=null,R=null,T=null,h={start:function(r,v){if(null!==r)if("string"==typeof r){if(!((r=r.toLowerCase())in t.md.algorithms))throw new Error('Unknown hash algorithm "'+r+'"');d=t.md.algorithms[r].create()}else d=r;if(null===v)v=a;else{if("string"==typeof v)v=t.util.createBuffer(v);else if(t.util.isArray(v)){var l=v;v=t.util.createBuffer();for(var S=0;S<l.length;++S)v.putByte(l[S])}var D=v.length();for(D>d.blockLength&&(d.start(),d.update(v.bytes()),v=d.digest()),R=t.util.createBuffer(),T=t.util.createBuffer(),D=v.length(),S=0;S<D;++S)l=v.at(S),R.putByte(54^l),T.putByte(92^l);if(D<d.blockLength)for(l=d.blockLength-D,S=0;S<l;++S)R.putByte(54),T.putByte(92);a=v,R=R.bytes(),T=T.bytes()}d.start(),d.update(R)},update:function(r){d.update(r)},getMac:function(){var r=d.digest().bytes();return d.start(),d.update(T),d.update(r),d.digest()}};return h.digest=h.getMac,h},Gt.exports}var Mr,Qt={exports:{}};function zt(){if(Mr)return Qt.exports;Mr=1;var t=ue();nt(),fe();var e=Qt.exports=t.md5=t.md5||{};t.md.md5=t.md.algorithms.md5=e,e.create=function(){h||function r(){a="\x80",a+=t.util.fillString("\0",64),d=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,1,6,11,0,5,10,15,4,9,14,3,8,13,2,7,12,5,8,11,14,1,4,7,10,13,0,3,6,9,12,15,2,0,7,14,5,12,3,10,1,8,15,6,13,4,11,2,9],R=[7,12,17,22,7,12,17,22,7,12,17,22,7,12,17,22,5,9,14,20,5,9,14,20,5,9,14,20,5,9,14,20,4,11,16,23,4,11,16,23,4,11,16,23,4,11,16,23,6,10,15,21,6,10,15,21,6,10,15,21,6,10,15,21],T=new Array(64);for(var l=0;l<64;++l)T[l]=Math.floor(4294967296*Math.abs(Math.sin(l+1)));h=!0}();var l=null,S=t.util.createBuffer(),D=new Array(16),E={algorithm:"md5",blockLength:64,digestLength:16,messageLength:0,fullMessageLength:null,messageLengthSize:8,start:function(){E.messageLength=0,E.fullMessageLength=E.messageLength64=[];for(var u=E.messageLengthSize/4,g=0;g<u;++g)E.fullMessageLength.push(0);return S=t.util.createBuffer(),l={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878},E}};return E.start(),E.update=function(u,g){"utf8"===g&&(u=t.util.encodeUtf8(u));var C=u.length;E.messageLength+=C,C=[C/4294967296>>>0,C>>>0];for(var m=E.fullMessageLength.length-1;m>=0;--m)E.fullMessageLength[m]+=C[1],C[1]=C[0]+(E.fullMessageLength[m]/4294967296>>>0),E.fullMessageLength[m]=E.fullMessageLength[m]>>>0,C[0]=C[1]/4294967296>>>0;return S.putBytes(u),v(l,D,S),(S.read>2048||0===S.length())&&S.compact(),E},E.digest=function(){var u=t.util.createBuffer();u.putBytes(S.bytes()),u.putBytes(a.substr(0,E.blockLength-(E.fullMessageLength[E.fullMessageLength.length-1]+E.messageLengthSize&E.blockLength-1)));for(var m,s=0,f=E.fullMessageLength.length-1;f>=0;--f)s=(m=8*E.fullMessageLength[f]+s)/4294967296>>>0,u.putInt32Le(m>>>0);var y={h0:l.h0,h1:l.h1,h2:l.h2,h3:l.h3};v(y,D,u);var I=t.util.createBuffer();return I.putInt32Le(y.h0),I.putInt32Le(y.h1),I.putInt32Le(y.h2),I.putInt32Le(y.h3),I},E};var a=null,d=null,R=null,T=null,h=!1;function v(l,S,D){for(var E,u,g,C,m,f,y,I=D.length();I>=64;){for(u=l.h0,g=l.h1,C=l.h2,m=l.h3,y=0;y<16;++y)S[y]=D.getInt32Le(),E=u+(m^g&(C^m))+T[y]+S[y],u=m,m=C,C=g,g+=E<<(f=R[y])|E>>>32-f;for(;y<32;++y)E=u+(C^m&(g^C))+T[y]+S[d[y]],u=m,m=C,C=g,g+=E<<(f=R[y])|E>>>32-f;for(;y<48;++y)E=u+(g^C^m)+T[y]+S[d[y]],u=m,m=C,C=g,g+=E<<(f=R[y])|E>>>32-f;for(;y<64;++y)E=u+(C^(g|~m))+T[y]+S[d[y]],u=m,m=C,C=g,g+=E<<(f=R[y])|E>>>32-f;l.h0=l.h0+u|0,l.h1=l.h1+g|0,l.h2=l.h2+C|0,l.h3=l.h3+m|0,I-=64}}return Qt.exports}var Fr,Yt={exports:{}};function yt(){if(Fr)return Yt.exports;Fr=1;var t=ue();fe();var e=Yt.exports=t.pem=t.pem||{};function a(R){for(var T=R.name+": ",h=[],r=function(E,u){return" "+u},v=0;v<R.values.length;++v)h.push(R.values[v].replace(/^(\S+\r\n)/,r));T+=h.join(",")+"\r\n";var l=0,S=-1;for(v=0;v<T.length;++v,++l)if(l>65&&-1!==S){var D=T[S];","===D?(++S,T=T.substr(0,S)+"\r\n "+T.substr(S)):T=T.substr(0,S)+"\r\n"+D+T.substr(S+1),l=v-S-1,S=-1,++v}else(" "===T[v]||"\t"===T[v]||","===T[v])&&(S=v);return T}function d(R){return R.replace(/^\s+/,"")}return e.encode=function(R,T){T=T||{};var r,h="-----BEGIN "+R.type+"-----\r\n";if(R.procType&&(h+=a(r={name:"Proc-Type",values:[String(R.procType.version),R.procType.type]})),R.contentDomain&&(h+=a(r={name:"Content-Domain",values:[R.contentDomain]})),R.dekInfo&&(r={name:"DEK-Info",values:[R.dekInfo.algorithm]},R.dekInfo.parameters&&r.values.push(R.dekInfo.parameters),h+=a(r)),R.headers)for(var v=0;v<R.headers.length;++v)h+=a(R.headers[v]);return R.procType&&(h+="\r\n"),(h+=t.util.encode64(R.body,T.maxline||64)+"\r\n")+"-----END "+R.type+"-----\r\n"},e.decode=function(R){for(var l,T=[],h=/\s*-----BEGIN ([A-Z0-9- ]+)-----\r?\n?([\x21-\x7e\s]+?(?:\r?\n\r?\n))?([:A-Za-z0-9+\/=\s]+?)-----END \1-----/g,r=/([\x21-\x7e]+):\s*([\x21-\x7e\s^:]+)/,v=/\r?\n/;l=h.exec(R);){var S=l[1];"NEW CERTIFICATE REQUEST"===S&&(S="CERTIFICATE REQUEST");var D={type:S,procType:null,contentDomain:null,dekInfo:null,headers:[],body:t.util.decode64(l[3])};if(T.push(D),l[2]){for(var E=l[2].split(v),u=0;l&&u<E.length;){for(var g=E[u].replace(/\s+$/,""),C=u+1;C<E.length;++C){var m=E[C];if(!/\s/.test(m[0]))break;g+=m,u=C}if(l=g.match(r)){for(var s={name:l[1],values:[]},f=l[2].split(","),y=0;y<f.length;++y)s.values.push(d(f[y]));if(D.procType)if(D.contentDomain||"Content-Domain"!==s.name)if(D.dekInfo||"DEK-Info"!==s.name)D.headers.push(s);else{if(0===s.values.length)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must have at least one subfield.');D.dekInfo={algorithm:f[0],parameters:f[1]||null}}else D.contentDomain=f[0]||"";else{if("Proc-Type"!==s.name)throw new Error('Invalid PEM formatted message. The first encapsulated header must be "Proc-Type".');if(2!==s.values.length)throw new Error('Invalid PEM formatted message. The "Proc-Type" header must have two subfields.');D.procType={version:f[0],type:f[1]}}}++u}if("ENCRYPTED"===D.procType&&!D.dekInfo)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must be present if "Proc-Type" is "ENCRYPTED".')}}if(0===T.length)throw new Error("Invalid PEM formatted message.");return T},Yt.exports}var Wt,Hr,Xt={exports:{}};function At(){if(Hr)return Wt;Hr=1;var t=ue();function e(u,g){t.cipher.registerAlgorithm(u,function(){return new t.des.Algorithm(u,g)})}xt(),Dr(),fe(),Wt=t.des=t.des||{},t.des.startEncrypting=function(u,g,C,m){var s=E({key:u,output:C,decrypt:!1,mode:m||(null===g?"ECB":"CBC")});return s.start(g),s},t.des.createEncryptionCipher=function(u,g){return E({key:u,output:null,decrypt:!1,mode:g})},t.des.startDecrypting=function(u,g,C,m){var s=E({key:u,output:C,decrypt:!0,mode:m||(null===g?"ECB":"CBC")});return s.start(g),s},t.des.createDecryptionCipher=function(u,g){return E({key:u,output:null,decrypt:!0,mode:g})},t.des.Algorithm=function(u,g){var C=this;C.name=u,C.mode=new g({blockSize:8,cipher:{encrypt:function(m,s){return D(C._keys,m,s,!1)},decrypt:function(m,s){return D(C._keys,m,s,!0)}}}),C._init=!1},t.des.Algorithm.prototype.initialize=function(u){if(!this._init){var g=t.util.createBuffer(u.key);if(0===this.name.indexOf("3DES")&&24!==g.length())throw new Error("Invalid Triple-DES key size: "+8*g.length());this._keys=function S(u){for(var j,g=[0,4,536870912,536870916,65536,65540,536936448,536936452,512,516,536871424,536871428,66048,66052,536936960,536936964],C=[0,1,1048576,1048577,67108864,67108865,68157440,68157441,256,257,1048832,1048833,67109120,67109121,68157696,68157697],m=[0,8,2048,2056,16777216,16777224,16779264,16779272,0,8,2048,2056,16777216,16777224,16779264,16779272],s=[0,2097152,134217728,136314880,8192,2105344,134225920,136323072,131072,2228224,134348800,136445952,139264,2236416,134356992,136454144],f=[0,262144,16,262160,0,262144,16,262160,4096,266240,4112,266256,4096,266240,4112,266256],y=[0,1024,32,1056,0,1024,32,1056,33554432,33555456,33554464,33555488,33554432,33555456,33554464,33555488],I=[0,268435456,524288,268959744,2,268435458,524290,268959746,0,268435456,524288,268959744,2,268435458,524290,268959746],_=[0,65536,2048,67584,536870912,536936448,536872960,536938496,131072,196608,133120,198656,537001984,537067520,537004032,537069568],p=[0,262144,0,262144,2,262146,2,262146,33554432,33816576,33554432,33816576,33554434,33816578,33554434,33816578],i=[0,268435456,8,268435464,0,268435456,8,268435464,1024,268436480,1032,268436488,1024,268436480,1032,268436488],n=[0,32,0,32,1048576,1048608,1048576,1048608,8192,8224,8192,8224,1056768,1056800,1056768,1056800],B=[0,16777216,512,16777728,2097152,18874368,2097664,18874880,67108864,83886080,67109376,83886592,69206016,85983232,69206528,85983744],U=[0,4096,134217728,134221824,524288,528384,134742016,134746112,16,4112,134217744,134221840,524304,528400,134742032,134746128],P=[0,4,256,260,0,4,256,260,1,5,257,261,1,5,257,261],L=u.length()>8?3:1,x=[],H=[0,0,1,1,1,1,1,1,0,1,1,1,1,1,1,0],F=0,z=0;z<L;z++){var X=u.getInt32(),$=u.getInt32();X^=(j=252645135&(X>>>4^$))<<4,X^=j=65535&(($^=j)>>>-16^X),X^=(j=858993459&(X>>>2^($^=j<<-16)))<<2,X^=j=65535&(($^=j)>>>-16^X),X^=(j=1431655765&(X>>>1^($^=j<<-16)))<<1,X^=j=16711935&(($^=j)>>>8^X),j=(X^=(j=1431655765&(X>>>1^($^=j<<8)))<<1)<<8|($^=j)>>>20&240,X=$<<24|$<<8&16711680|$>>>8&65280|$>>>24&240,$=j;for(var ne=0;ne<H.length;++ne){H[ne]?(X=X<<2|X>>>26,$=$<<2|$>>>26):(X=X<<1|X>>>27,$=$<<1|$>>>27);var se=g[(X&=-15)>>>28]|C[X>>>24&15]|m[X>>>20&15]|s[X>>>16&15]|f[X>>>12&15]|y[X>>>8&15]|I[X>>>4&15],he=_[($&=-15)>>>28]|p[$>>>24&15]|i[$>>>20&15]|n[$>>>16&15]|B[$>>>12&15]|U[$>>>8&15]|P[$>>>4&15];x[F++]=se^(j=65535&(he>>>16^se)),x[F++]=he^j<<16}}return x}(g),this._init=!0}},e("DES-ECB",t.cipher.modes.ecb),e("DES-CBC",t.cipher.modes.cbc),e("DES-CFB",t.cipher.modes.cfb),e("DES-OFB",t.cipher.modes.ofb),e("DES-CTR",t.cipher.modes.ctr),e("3DES-ECB",t.cipher.modes.ecb),e("3DES-CBC",t.cipher.modes.cbc),e("3DES-CFB",t.cipher.modes.cfb),e("3DES-OFB",t.cipher.modes.ofb),e("3DES-CTR",t.cipher.modes.ctr);var a=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756],d=[-2146402272,-2147450880,32768,1081376,1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,-2147483648,-2146435040,-2146402272,1081344],R=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,131592,8,134348808,131584],T=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928],h=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,1074266368,1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],r=[536870928,541065216,16384,541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],v=[2097152,69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],l=[268439616,4096,262144,268701760,268435456,268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696];function D(u,g,C,m){var f,s=32===u.length?3:9;f=3===s?m?[30,-2,-2]:[0,32,2]:m?[94,62,-2,32,64,2,30,-2,-2]:[0,32,2,62,30,-2,64,96,2];var y,I=g[0],_=g[1];I^=(y=252645135&(I>>>4^_))<<4,I^=(y=65535&(I>>>16^(_^=y)))<<16,I^=y=858993459&((_^=y)>>>2^I),I^=y=16711935&((_^=y<<2)>>>8^I),I=(I^=(y=1431655765&(I>>>1^(_^=y<<8)))<<1)<<1|I>>>31,_=(_^=y)<<1|_>>>31;for(var p=0;p<s;p+=3){for(var i=f[p+1],n=f[p+2],B=f[p];B!=i;B+=n){var U=_^u[B],P=(_>>>4|_<<28)^u[B+1];y=I,I=_,_=y^(d[U>>>24&63]|T[U>>>16&63]|r[U>>>8&63]|l[63&U]|a[P>>>24&63]|R[P>>>16&63]|h[P>>>8&63]|v[63&P])}y=I,I=_,_=y}_=_>>>1|_<<31,_^=y=1431655765&((I=I>>>1|I<<31)>>>1^_),_^=(y=16711935&(_>>>8^(I^=y<<1)))<<8,_^=(y=858993459&(_>>>2^(I^=y)))<<2,_^=y=65535&((I^=y)>>>16^_),_^=y=252645135&((I^=y<<16)>>>4^_),C[0]=I^=y<<4,C[1]=_}function E(u){var m,C="DES-"+((u=u||{}).mode||"CBC").toUpperCase(),s=(m=u.decrypt?t.cipher.createDecipher(C,u.key):t.cipher.createCipher(C,u.key)).start;return m.start=function(f,y){var I=null;y instanceof t.util.ByteBuffer&&(I=y,y={}),(y=y||{}).output=I,y.iv=f,s.call(m,y)},m}return Wt}const Ja=new Proxy({},{get(t,e){throw new Error(`Module "" has been externalized for browser compatibility. Cannot access ".${e}" in client code.  See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)}}),Zt=Za(Object.freeze(Object.defineProperty({__proto__:null,default:Ja},Symbol.toStringTag,{value:"Module"})));var $t,jr;function Jt(){if(jr)return $t;jr=1;var t=ue();Et(),nt(),fe();var a,e=t.pkcs5=t.pkcs5||{};return t.util.isNodejs&&!t.options.usePureJavaScript&&(a=Zt),$t=t.pbkdf2=e.pbkdf2=function(d,R,T,h,r,v){if("function"==typeof r&&(v=r,r=null),t.util.isNodejs&&!t.options.usePureJavaScript&&a.pbkdf2&&(null===r||"object"!=typeof r)&&(a.pbkdf2Sync.length>4||!r||"sha1"===r))return"string"!=typeof r&&(r="sha1"),d=Buffer.from(d,"binary"),R=Buffer.from(R,"binary"),v?4===a.pbkdf2Sync.length?a.pbkdf2(d,R,T,h,function(p,i){if(p)return v(p);v(null,i.toString("binary"))}):a.pbkdf2(d,R,T,h,r,function(p,i){if(p)return v(p);v(null,i.toString("binary"))}):4===a.pbkdf2Sync.length?a.pbkdf2Sync(d,R,T,h).toString("binary"):a.pbkdf2Sync(d,R,T,h,r).toString("binary");if((typeof r>"u"||null===r)&&(r="sha1"),"string"==typeof r){if(!(r in t.md.algorithms))throw new Error("Unknown hash algorithm: "+r);r=t.md[r].create()}var l=r.digestLength;if(h>4294967295*l){var S=new Error("Derived key is too long.");if(v)return v(S);throw S}var D=Math.ceil(h/l),E=h-(D-1)*l,u=t.hmac.create();u.start(r,d);var C,m,s,g="";if(!v){for(var f=1;f<=D;++f){u.start(null,null),u.update(R),u.update(t.util.int32ToBytes(f)),C=s=u.digest().getBytes();for(var y=2;y<=T;++y)u.start(null,null),u.update(s),m=u.digest().getBytes(),C=t.util.xorBytes(C,m,l),s=m;g+=f<D?C:C.substr(0,E)}return g}function I(){if(f>D)return v(null,g);u.start(null,null),u.update(R),u.update(t.util.int32ToBytes(f)),C=s=u.digest().getBytes(),y=2,_()}function _(){if(y<=T)return u.start(null,null),u.update(s),m=u.digest().getBytes(),C=t.util.xorBytes(C,m,l),s=m,++y,t.util.setImmediate(_);g+=f<D?C:C.substr(0,E),++f,I()}f=1,I()}}var Gr,Bt={exports:{}},er={exports:{}};function qr(){if(Gr)return er.exports;Gr=1;var t=ue();nt(),fe();var e=er.exports=t.sha256=t.sha256||{};t.md.sha256=t.md.algorithms.sha256=e,e.create=function(){d||function T(){a="\x80",a+=t.util.fillString("\0",64),R=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],d=!0}();var r=null,v=t.util.createBuffer(),l=new Array(64),S={algorithm:"sha256",blockLength:64,digestLength:32,messageLength:0,fullMessageLength:null,messageLengthSize:8,start:function(){S.messageLength=0,S.fullMessageLength=S.messageLength64=[];for(var D=S.messageLengthSize/4,E=0;E<D;++E)S.fullMessageLength.push(0);return v=t.util.createBuffer(),r={h0:1779033703,h1:3144134277,h2:1013904242,h3:2773480762,h4:1359893119,h5:2600822924,h6:528734635,h7:1541459225},S}};return S.start(),S.update=function(D,E){"utf8"===E&&(D=t.util.encodeUtf8(D));var u=D.length;S.messageLength+=u,u=[u/4294967296>>>0,u>>>0];for(var g=S.fullMessageLength.length-1;g>=0;--g)S.fullMessageLength[g]+=u[1],u[1]=u[0]+(S.fullMessageLength[g]/4294967296>>>0),S.fullMessageLength[g]=S.fullMessageLength[g]>>>0,u[0]=u[1]/4294967296>>>0;return v.putBytes(D),h(r,l,v),(v.read>2048||0===v.length())&&v.compact(),S},S.digest=function(){var D=t.util.createBuffer();D.putBytes(v.bytes()),D.putBytes(a.substr(0,S.blockLength-(S.fullMessageLength[S.fullMessageLength.length-1]+S.messageLengthSize&S.blockLength-1)));for(var g,m=8*S.fullMessageLength[0],s=0;s<S.fullMessageLength.length-1;++s)D.putInt32((m+=(g=8*S.fullMessageLength[s+1])/4294967296>>>0)>>>0),m=g>>>0;D.putInt32(m);var f={h0:r.h0,h1:r.h1,h2:r.h2,h3:r.h3,h4:r.h4,h5:r.h5,h6:r.h6,h7:r.h7};h(f,l,D);var y=t.util.createBuffer();return y.putInt32(f.h0),y.putInt32(f.h1),y.putInt32(f.h2),y.putInt32(f.h3),y.putInt32(f.h4),y.putInt32(f.h5),y.putInt32(f.h6),y.putInt32(f.h7),y},S};var a=null,d=!1,R=null;function h(r,v,l){for(var S,D,C,m,s,f,y,I,_,p,i,n,B=l.length();B>=64;){for(m=0;m<16;++m)v[m]=l.getInt32();for(;m<64;++m)v[m]=(S=((S=v[m-2])>>>17|S<<15)^(S>>>19|S<<13)^S>>>10)+v[m-7]+(D=((D=v[m-15])>>>7|D<<25)^(D>>>18|D<<14)^D>>>3)+v[m-16]|0;for(s=r.h0,f=r.h1,y=r.h2,I=r.h3,_=r.h4,p=r.h5,i=r.h6,n=r.h7,m=0;m<64;++m)C=s&f|y&(s^f),S=n+((_>>>6|_<<26)^(_>>>11|_<<21)^(_>>>25|_<<7))+(i^_&(p^i))+R[m]+v[m],n=i,i=p,p=_,_=I+S>>>0,I=y,y=f,f=s,s=S+(D=((s>>>2|s<<30)^(s>>>13|s<<19)^(s>>>22|s<<10))+C)>>>0;r.h0=r.h0+s|0,r.h1=r.h1+f|0,r.h2=r.h2+y|0,r.h3=r.h3+I|0,r.h4=r.h4+_|0,r.h5=r.h5+p|0,r.h6=r.h6+i|0,r.h7=r.h7+n|0,B-=64}}return er.exports}var Qr,Yr,rr,Xr,ar,Zr,tr={exports:{}};function zr(){if(Qr)return tr.exports;Qr=1;var t=ue();fe();var e=null;return t.util.isNodejs&&!t.options.usePureJavaScript&&!process.versions["node-webkit"]&&(e=Zt),(tr.exports=t.prng=t.prng||{}).create=function(d){for(var R={plugin:d,key:null,seed:null,time:null,reseeds:0,generated:0,keyBytes:""},T=d.md,h=new Array(32),r=0;r<32;++r)h[r]=T.create();function l(){if(R.pools[0].messageLength>=32)return S();R.collect(R.seedFileSync(32-R.pools[0].messageLength<<5)),S()}function S(){R.reseeds=4294967295===R.reseeds?0:R.reseeds+1;var E=R.plugin.md.create();E.update(R.keyBytes);for(var u=1,g=0;g<32;++g)R.reseeds%u==0&&(E.update(R.pools[g].digest().getBytes()),R.pools[g].start()),u<<=1;R.keyBytes=E.digest().getBytes(),E.start(),E.update(R.keyBytes);var C=E.digest().getBytes();R.key=R.plugin.formatKey(R.keyBytes),R.seed=R.plugin.formatSeed(C),R.generated=0}function D(E){var u=null,g=t.util.globalScope,C=g.crypto||g.msCrypto;C&&C.getRandomValues&&(u=function(n){return C.getRandomValues(n)});var m=t.util.createBuffer();if(u)for(;m.length()<E;){var s=Math.max(1,Math.min(E-m.length(),65536)/4),f=new Uint32Array(Math.floor(s));try{u(f);for(var y=0;y<f.length;++y)m.putInt32(f[y])}catch(n){if(!(typeof QuotaExceededError<"u"&&n instanceof QuotaExceededError))throw n}}if(m.length()<E)for(var I,_,p,i=Math.floor(65536*Math.random());m.length()<E;)for(_=16807*(65535&i),_+=(32767&(I=16807*(i>>16)))<<16,i=4294967295&(_=(2147483647&(_+=I>>15))+(_>>31)),y=0;y<3;++y)p=i>>>(y<<3),p^=Math.floor(256*Math.random()),m.putByte(255&p);return m.getBytes(E)}return R.pools=h,R.pool=0,R.generate=function(E,u){if(!u)return R.generateSync(E);var g=R.plugin.cipher,C=R.plugin.increment,m=R.plugin.formatKey,s=R.plugin.formatSeed,f=t.util.createBuffer();R.key=null,function y(I){if(I)return u(I);if(f.length()>=E)return u(null,f.getBytes(E));if(R.generated>1048575&&(R.key=null),null===R.key)return t.util.nextTick(function(){!function v(E){if(R.pools[0].messageLength>=32)return S(),E();R.seedFile(32-R.pools[0].messageLength<<5,function(g,C){if(g)return E(g);R.collect(C),S(),E()})}(y)});var _=g(R.key,R.seed);R.generated+=_.length,f.putBytes(_),R.key=m(g(R.key,C(R.seed))),R.seed=s(g(R.key,R.seed)),t.util.setImmediate(y)}()},R.generateSync=function(E){var u=R.plugin.cipher,g=R.plugin.increment,C=R.plugin.formatKey,m=R.plugin.formatSeed;R.key=null;for(var s=t.util.createBuffer();s.length()<E;){R.generated>1048575&&(R.key=null),null===R.key&&l();var f=u(R.key,R.seed);R.generated+=f.length,s.putBytes(f),R.key=C(u(R.key,g(R.seed))),R.seed=m(u(R.key,R.seed))}return s.getBytes(E)},e?(R.seedFile=function(E,u){e.randomBytes(E,function(g,C){if(g)return u(g);u(null,C.toString())})},R.seedFileSync=function(E){return e.randomBytes(E).toString()}):(R.seedFile=function(E,u){try{u(null,D(E))}catch(g){u(g)}},R.seedFileSync=D),R.collect=function(E){for(var u=E.length,g=0;g<u;++g)R.pools[R.pool].update(E.substr(g,1)),R.pool=31===R.pool?0:R.pool+1},R.collectInt=function(E,u){for(var g="",C=0;C<u;C+=8)g+=String.fromCharCode(E>>C&255);R.collect(g)},R.registerWorker=function(E){E===self?R.seedFile=function(g,C){self.addEventListener("message",function m(s){var f=s.data;f.forge&&f.forge.prng&&(self.removeEventListener("message",m),C(f.forge.prng.err,f.forge.prng.bytes))}),self.postMessage({forge:{prng:{needed:g}}})}:E.addEventListener("message",function(g){var C=g.data;C.forge&&C.forge.prng&&R.seedFile(C.forge.prng.needed,function(m,s){E.postMessage({forge:{prng:{err:m,bytes:s}}})})})},R},tr.exports}function Ze(){if(Yr)return Bt.exports;Yr=1;var t=ue();return lt(),qr(),zr(),fe(),t.random&&t.random.getBytes?Bt.exports=t.random:function(e){var a={},d=new Array(4),R=t.util.createBuffer();function T(){var E=t.prng.create(a);return E.getBytes=function(u,g){return E.generate(u,g)},E.getBytesSync=function(u){return E.generate(u)},E}a.formatKey=function(E){var u=t.util.createBuffer(E);return(E=new Array(4))[0]=u.getInt32(),E[1]=u.getInt32(),E[2]=u.getInt32(),E[3]=u.getInt32(),t.aes._expandKey(E,!1)},a.formatSeed=function(E){var u=t.util.createBuffer(E);return(E=new Array(4))[0]=u.getInt32(),E[1]=u.getInt32(),E[2]=u.getInt32(),E[3]=u.getInt32(),E},a.cipher=function(E,u){return t.aes._updateBlock(E,u,d,!1),R.putInt32(d[0]),R.putInt32(d[1]),R.putInt32(d[2]),R.putInt32(d[3]),R.getBytes()},a.increment=function(E){return++E[3],E},a.md=t.md.sha256;var h=T(),r=null,v=t.util.globalScope,l=v.crypto||v.msCrypto;if(l&&l.getRandomValues&&(r=function(E){return l.getRandomValues(E)}),t.options.usePureJavaScript||!t.util.isNodejs&&!r){if(h.collectInt(+new Date,32),typeof navigator<"u"){var S="";for(var D in navigator)try{"string"==typeof navigator[D]&&(S+=navigator[D])}catch{}h.collect(S),S=null}e&&(e().mousemove(function(E){h.collectInt(E.clientX,16),h.collectInt(E.clientY,16)}),e().keypress(function(E){h.collectInt(E.charCode,8)}))}if(t.random)for(var D in h)t.random[D]=h[D];else t.random=h;t.random.createInstance=T,Bt.exports=t.random}(typeof jQuery<"u"?jQuery:null),Bt.exports}function Wr(){if(Xr)return rr;Xr=1;var t=ue();fe();var e=[217,120,249,196,25,221,181,237,40,233,253,121,74,160,216,157,198,126,55,131,43,118,83,142,98,76,100,136,68,139,251,162,23,154,89,245,135,179,79,19,97,69,109,141,9,129,125,50,189,143,64,235,134,183,123,11,240,149,33,34,92,107,78,130,84,214,101,147,206,96,178,28,115,86,192,20,167,140,241,220,18,117,202,31,59,190,228,209,66,61,212,48,163,60,182,38,111,191,14,218,70,105,7,87,39,242,29,155,188,148,67,3,248,17,199,246,144,239,62,231,6,195,213,47,200,102,30,215,8,232,234,222,128,82,238,247,132,170,114,172,53,77,106,42,150,26,210,113,90,21,73,116,75,159,208,94,4,24,164,236,194,224,65,110,15,81,203,204,36,145,175,80,161,244,112,57,153,124,58,133,35,184,180,122,252,2,54,91,37,85,151,49,45,93,250,152,227,138,146,174,5,223,41,16,103,108,186,201,211,0,230,207,225,158,168,44,99,22,1,63,88,226,137,169,13,56,52,27,171,51,255,176,187,72,12,95,185,177,205,46,197,243,219,71,229,165,156,119,10,166,32,104,254,127,193,173],a=[1,2,3,5],d=function(h,r){return h<<r&65535|(65535&h)>>16-r},R=function(h,r){return(65535&h)>>r|h<<16-r&65535};rr=t.rc2=t.rc2||{},t.rc2.expandKey=function(h,r){"string"==typeof h&&(h=t.util.createBuffer(h)),r=r||128;var u,v=h,l=h.length(),S=r,D=Math.ceil(S/8),E=255>>(7&S);for(u=l;u<128;u++)v.putByte(e[v.at(u-1)+v.at(u-l)&255]);for(v.setAt(128-D,e[v.at(128-D)&E]),u=127-D;u>=0;u--)v.setAt(u,e[v.at(u+1)^v.at(u+D)]);return v};var T=function(h,r,v){var u,g,C,m,l=!1,S=null,D=null,E=null,s=[];for(h=t.rc2.expandKey(h,r),C=0;C<64;C++)s.push(h.getInt16Le());v?(u=function(I){for(C=0;C<4;C++)I[C]+=s[m]+(I[(C+3)%4]&I[(C+2)%4])+(~I[(C+3)%4]&I[(C+1)%4]),I[C]=d(I[C],a[C]),m++},g=function(I){for(C=0;C<4;C++)I[C]+=s[63&I[(C+3)%4]]}):(u=function(I){for(C=3;C>=0;C--)I[C]=R(I[C],a[C]),I[C]-=s[m]+(I[(C+3)%4]&I[(C+2)%4])+(~I[(C+3)%4]&I[(C+1)%4]),m--},g=function(I){for(C=3;C>=0;C--)I[C]-=s[63&I[(C+3)%4]]});var f=function(I){var _=[];for(C=0;C<4;C++){var p=S.getInt16Le();null!==E&&(v?p^=E.getInt16Le():E.putInt16Le(p)),_.push(65535&p)}m=v?0:63;for(var i=0;i<I.length;i++)for(var n=0;n<I[i][0];n++)I[i][1](_);for(C=0;C<4;C++)null!==E&&(v?E.putInt16Le(_[C]):_[C]^=E.getInt16Le()),D.putInt16Le(_[C])},y=null;return y={start:function(I,_){I&&"string"==typeof I&&(I=t.util.createBuffer(I)),l=!1,S=t.util.createBuffer(),D=_||new t.util.createBuffer,E=I,y.output=D},update:function(I){for(l||S.putBuffer(I);S.length()>=8;)f([[5,u],[1,g],[6,u],[1,g],[5,u]])},finish:function(I){var _=!0;if(v)if(I)_=I(8,S,!v);else{var p=8===S.length()?8:8-S.length();S.fillWithByte(p,p)}if(_&&(l=!0,y.update()),!v&&(_=0===S.length()))if(I)_=I(8,D,!v);else{var i=D.length(),n=D.at(i-1);n>i?_=!1:D.truncate(n)}return _}}};return t.rc2.startEncrypting=function(h,r,v){var l=t.rc2.createEncryptionCipher(h,128);return l.start(r,v),l},t.rc2.createEncryptionCipher=function(h,r){return T(h,r,!0)},t.rc2.startDecrypting=function(h,r,v){var l=t.rc2.createDecryptionCipher(h,128);return l.start(r,v),l},t.rc2.createDecryptionCipher=function(h,r){return T(h,r,!1)},rr}function Nt(){if(Zr)return ar;Zr=1;var e,t=ue();function a(b,N,V){this.data=[],null!=b&&("number"==typeof b?this.fromNumber(b,N,V):this.fromString(b,null==N&&"string"!=typeof b?256:N))}function d(){return new a(null)}function h(b,N,V,M,q,Y){for(var te=16383&N,J=N>>14;--Y>=0;){var de=16383&this.data[b],Ke=this.data[b++]>>14,Xe=J*de+Ke*te;q=((de=te*de+((16383&Xe)<<14)+V.data[M]+q)>>28)+(Xe>>14)+J*Ke,V.data[M++]=268435455&de}return q}ar=t.jsbn=t.jsbn||{},t.jsbn.BigInteger=a,typeof navigator>"u"?(a.prototype.am=h,e=28):"Microsoft Internet Explorer"==navigator.appName?(a.prototype.am=function T(b,N,V,M,q,Y){for(var te=32767&N,J=N>>15;--Y>=0;){var de=32767&this.data[b],Ke=this.data[b++]>>15,Xe=J*de+Ke*te;q=((de=te*de+((32767&Xe)<<15)+V.data[M]+(1073741823&q))>>>30)+(Xe>>>15)+J*Ke+(q>>>30),V.data[M++]=1073741823&de}return q},e=30):"Netscape"!=navigator.appName?(a.prototype.am=function R(b,N,V,M,q,Y){for(;--Y>=0;){var te=N*this.data[b++]+V.data[M]+q;q=Math.floor(te/67108864),V.data[M++]=67108863&te}return q},e=26):(a.prototype.am=h,e=28),a.prototype.DB=e,a.prototype.DM=(1<<e)-1,a.prototype.DV=1<<e,a.prototype.FV=Math.pow(2,52),a.prototype.F1=52-e,a.prototype.F2=2*e-52;var S,D,v="0123456789abcdefghijklmnopqrstuvwxyz",l=new Array;for(S=48,D=0;D<=9;++D)l[S++]=D;for(S=97,D=10;D<36;++D)l[S++]=D;for(S=65,D=10;D<36;++D)l[S++]=D;function E(b){return v.charAt(b)}function u(b,N){return l[b.charCodeAt(N)]??-1}function m(b){var N=d();return N.fromInt(b),N}function i(b){var V,N=1;return 0!=(V=b>>>16)&&(b=V,N+=16),0!=(V=b>>8)&&(b=V,N+=8),0!=(V=b>>4)&&(b=V,N+=4),0!=(V=b>>2)&&(b=V,N+=2),0!=(V=b>>1)&&(b=V,N+=1),N}function X(b){this.m=b}function ce(b){this.m=b,this.mp=b.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<b.DB-15)-1,this.mt2=2*b.t}function ye(b,N){return b&N}function be(b,N){return b|N}function He(b,N){return b^N}function qe(b,N){return b&~N}function st(b){if(0==b)return-1;var N=0;return!(65535&b)&&(b>>=16,N+=16),!(255&b)&&(b>>=8,N+=8),!(15&b)&&(b>>=4,N+=4),!(3&b)&&(b>>=2,N+=2),!(1&b)&&++N,N}function dt(b){for(var N=0;0!=b;)b&=b-1,++N;return N}function Ee(){}function Ie(b){return b}function mt(b){this.r2=d(),this.q3=d(),a.ONE.dlShiftTo(2*b.t,this.r2),this.mu=this.r2.divide(b),this.m=b}X.prototype.convert=function $(b){return b.s<0||b.compareTo(this.m)>=0?b.mod(this.m):b},X.prototype.revert=function ne(b){return b},X.prototype.reduce=function se(b){b.divRemTo(this.m,null,b)},X.prototype.mulTo=function he(b,N,V){b.multiplyTo(N,V),this.reduce(V)},X.prototype.sqrTo=function ve(b,N){b.squareTo(N),this.reduce(N)},ce.prototype.convert=function Z(b){var N=d();return b.abs().dlShiftTo(this.m.t,N),N.divRemTo(this.m,null,N),b.s<0&&N.compareTo(a.ZERO)>0&&this.m.subTo(N,N),N},ce.prototype.revert=function je(b){var N=d();return b.copyTo(N),this.reduce(N),N},ce.prototype.reduce=function le(b){for(;b.t<=this.mt2;)b.data[b.t++]=0;for(var N=0;N<this.m.t;++N){var V=32767&b.data[N],M=V*this.mpl+((V*this.mph+(b.data[N]>>15)*this.mpl&this.um)<<15)&b.DM;for(b.data[V=N+this.m.t]+=this.m.am(0,M,b,N,0,this.m.t);b.data[V]>=b.DV;)b.data[V]-=b.DV,b.data[++V]++}b.clamp(),b.drShiftTo(this.m.t,b),b.compareTo(this.m)>=0&&b.subTo(this.m,b)},ce.prototype.mulTo=function k(b,N,V){b.multiplyTo(N,V),this.reduce(V)},ce.prototype.sqrTo=function c(b,N){b.squareTo(N),this.reduce(N)},a.prototype.copyTo=function g(b){for(var N=this.t-1;N>=0;--N)b.data[N]=this.data[N];b.t=this.t,b.s=this.s},a.prototype.fromInt=function C(b){this.t=1,this.s=b<0?-1:0,b>0?this.data[0]=b:b<-1?this.data[0]=b+this.DV:this.t=0},a.prototype.fromString=function s(b,N){var V;if(16==N)V=4;else if(8==N)V=3;else if(256==N)V=8;else if(2==N)V=1;else if(32==N)V=5;else{if(4!=N)return void this.fromRadix(b,N);V=2}this.t=0,this.s=0;for(var M=b.length,q=!1,Y=0;--M>=0;){var te=8==V?255&b[M]:u(b,M);te<0?"-"==b.charAt(M)&&(q=!0):(q=!1,0==Y?this.data[this.t++]=te:Y+V>this.DB?(this.data[this.t-1]|=(te&(1<<this.DB-Y)-1)<<Y,this.data[this.t++]=te>>this.DB-Y):this.data[this.t-1]|=te<<Y,(Y+=V)>=this.DB&&(Y-=this.DB))}8==V&&128&b[0]&&(this.s=-1,Y>0&&(this.data[this.t-1]|=(1<<this.DB-Y)-1<<Y)),this.clamp(),q&&a.ZERO.subTo(this,this)},a.prototype.clamp=function f(){for(var b=this.s&this.DM;this.t>0&&this.data[this.t-1]==b;)--this.t},a.prototype.dlShiftTo=function B(b,N){var V;for(V=this.t-1;V>=0;--V)N.data[V+b]=this.data[V];for(V=b-1;V>=0;--V)N.data[V]=0;N.t=this.t+b,N.s=this.s},a.prototype.drShiftTo=function U(b,N){for(var V=b;V<this.t;++V)N.data[V-b]=this.data[V];N.t=Math.max(this.t-b,0),N.s=this.s},a.prototype.lShiftTo=function P(b,N){var J,V=b%this.DB,M=this.DB-V,q=(1<<M)-1,Y=Math.floor(b/this.DB),te=this.s<<V&this.DM;for(J=this.t-1;J>=0;--J)N.data[J+Y+1]=this.data[J]>>M|te,te=(this.data[J]&q)<<V;for(J=Y-1;J>=0;--J)N.data[J]=0;N.data[Y]=te,N.t=this.t+Y+1,N.s=this.s,N.clamp()},a.prototype.rShiftTo=function L(b,N){N.s=this.s;var V=Math.floor(b/this.DB);if(V>=this.t)N.t=0;else{var M=b%this.DB,q=this.DB-M,Y=(1<<M)-1;N.data[0]=this.data[V]>>M;for(var te=V+1;te<this.t;++te)N.data[te-V-1]|=(this.data[te]&Y)<<q,N.data[te-V]=this.data[te]>>M;M>0&&(N.data[this.t-V-1]|=(this.s&Y)<<q),N.t=this.t-V,N.clamp()}},a.prototype.subTo=function x(b,N){for(var V=0,M=0,q=Math.min(b.t,this.t);V<q;)M+=this.data[V]-b.data[V],N.data[V++]=M&this.DM,M>>=this.DB;if(b.t<this.t){for(M-=b.s;V<this.t;)M+=this.data[V],N.data[V++]=M&this.DM,M>>=this.DB;M+=this.s}else{for(M+=this.s;V<b.t;)M-=b.data[V],N.data[V++]=M&this.DM,M>>=this.DB;M-=b.s}N.s=M<0?-1:0,M<-1?N.data[V++]=this.DV+M:M>0&&(N.data[V++]=M),N.t=V,N.clamp()},a.prototype.multiplyTo=function H(b,N){var V=this.abs(),M=b.abs(),q=V.t;for(N.t=q+M.t;--q>=0;)N.data[q]=0;for(q=0;q<M.t;++q)N.data[q+V.t]=V.am(0,M.data[q],N,q,0,V.t);N.s=0,N.clamp(),this.s!=b.s&&a.ZERO.subTo(N,N)},a.prototype.squareTo=function F(b){for(var N=this.abs(),V=b.t=2*N.t;--V>=0;)b.data[V]=0;for(V=0;V<N.t-1;++V){var M=N.am(V,N.data[V],b,2*V,0,1);(b.data[V+N.t]+=N.am(V+1,2*N.data[V],b,2*V+1,M,N.t-V-1))>=N.DV&&(b.data[V+N.t]-=N.DV,b.data[V+N.t+1]=1)}b.t>0&&(b.data[b.t-1]+=N.am(V,N.data[V],b,2*V,0,1)),b.s=0,b.clamp()},a.prototype.divRemTo=function j(b,N,V){var M=b.abs();if(!(M.t<=0)){var q=this.abs();if(q.t<M.t)return N?.fromInt(0),void(null!=V&&this.copyTo(V));null==V&&(V=d());var Y=d(),te=this.s,J=b.s,de=this.DB-i(M.data[M.t-1]);de>0?(M.lShiftTo(de,Y),q.lShiftTo(de,V)):(M.copyTo(Y),q.copyTo(V));var Ke=Y.t,Xe=Y.data[Ke-1];if(0!=Xe){var ze=Xe*(1<<this.F1)+(Ke>1?Y.data[Ke-2]>>this.F2:0),ot=this.FV/ze,Ut=(1<<this.F1)/ze,et=1<<this.F2,tt=V.t,Dt=tt-Ke,ct=N??d();for(Y.dlShiftTo(Dt,ct),V.compareTo(ct)>=0&&(V.data[V.t++]=1,V.subTo(ct,V)),a.ONE.dlShiftTo(Ke,ct),ct.subTo(Y,Y);Y.t<Ke;)Y.data[Y.t++]=0;for(;--Dt>=0;){var br=V.data[--tt]==Xe?this.DM:Math.floor(V.data[tt]*ot+(V.data[tt-1]+et)*Ut);if((V.data[tt]+=Y.am(0,br,V,Dt,0,Ke))<br)for(Y.dlShiftTo(Dt,ct),V.subTo(ct,V);V.data[tt]<--br;)V.subTo(ct,V)}null!=N&&(V.drShiftTo(Ke,N),te!=J&&a.ZERO.subTo(N,N)),V.t=Ke,V.clamp(),de>0&&V.rShiftTo(de,V),te<0&&a.ZERO.subTo(V,V)}}},a.prototype.invDigit=function W(){if(this.t<1)return 0;var b=this.data[0];if(!(1&b))return 0;var N=3&b;return(N=(N=(N=(N=N*(2-(15&b)*N)&15)*(2-(255&b)*N)&255)*(2-((65535&b)*N&65535))&65535)*(2-b*N%this.DV)%this.DV)>0?this.DV-N:-N},a.prototype.isEven=function w(){return 0==(this.t>0?1&this.data[0]:this.s)},a.prototype.exp=function o(b,N){if(b>4294967295||b<1)return a.ONE;var V=d(),M=d(),q=N.convert(this),Y=i(b)-1;for(q.copyTo(V);--Y>=0;)if(N.sqrTo(V,M),(b&1<<Y)>0)N.mulTo(M,q,V);else{var te=V;V=M,M=te}return N.revert(V)},a.prototype.toString=function y(b){if(this.s<0)return"-"+this.negate().toString(b);var N;if(16==b)N=4;else if(8==b)N=3;else if(2==b)N=1;else if(32==b)N=5;else{if(4!=b)return this.toRadix(b);N=2}var M,V=(1<<N)-1,q=!1,Y="",te=this.t,J=this.DB-te*this.DB%N;if(te-- >0)for(J<this.DB&&(M=this.data[te]>>J)>0&&(q=!0,Y=E(M));te>=0;)J<N?(M=(this.data[te]&(1<<J)-1)<<N-J,M|=this.data[--te]>>(J+=this.DB-N)):(M=this.data[te]>>(J-=N)&V,J<=0&&(J+=this.DB,--te)),M>0&&(q=!0),q&&(Y+=E(M));return q?Y:"0"},a.prototype.negate=function I(){var b=d();return a.ZERO.subTo(this,b),b},a.prototype.abs=function _(){return this.s<0?this.negate():this},a.prototype.compareTo=function p(b){var N=this.s-b.s;if(0!=N)return N;var V=this.t;if(0!=(N=V-b.t))return this.s<0?-N:N;for(;--V>=0;)if(0!=(N=this.data[V]-b.data[V]))return N;return 0},a.prototype.bitLength=function n(){return this.t<=0?0:this.DB*(this.t-1)+i(this.data[this.t-1]^this.s&this.DM)},a.prototype.mod=function z(b){var N=d();return this.abs().divRemTo(b,null,N),this.s<0&&N.compareTo(a.ZERO)>0&&b.subTo(N,N),N},a.prototype.modPowInt=function A(b,N){var V;return V=b<256||N.isEven()?new X(N):new ce(N),this.exp(b,V)},a.ZERO=m(0),a.ONE=m(1),Ee.prototype.convert=Ie,Ee.prototype.revert=Ie,Ee.prototype.mulTo=function Pe(b,N,V){b.multiplyTo(N,V)},Ee.prototype.sqrTo=function xe(b,N){b.squareTo(N)},mt.prototype.convert=function wn(b){if(b.s<0||b.t>2*this.m.t)return b.mod(this.m);if(b.compareTo(this.m)<0)return b;var N=d();return b.copyTo(N),this.reduce(N),N},mt.prototype.revert=function _n(b){return b},mt.prototype.reduce=function kn(b){for(b.drShiftTo(this.m.t-1,this.r2),b.t>this.m.t+1&&(b.t=this.m.t+1,b.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);b.compareTo(this.r2)<0;)b.dAddOffset(1,this.m.t+1);for(b.subTo(this.r2,b);b.compareTo(this.m)>=0;)b.subTo(this.m,b)},mt.prototype.mulTo=function Un(b,N,V){b.multiplyTo(N,V),this.reduce(V)},mt.prototype.sqrTo=function Ln(b,N){b.squareTo(N),this.reduce(N)};var Je=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509],xn=(1<<26)/Je[Je.length-1];return a.prototype.chunkSize=function ae(b){return Math.floor(Math.LN2*this.DB/Math.log(b))},a.prototype.toRadix=function ee(b){if(null==b&&(b=10),0==this.signum()||b<2||b>36)return"0";var N=this.chunkSize(b),V=Math.pow(b,N),M=m(V),q=d(),Y=d(),te="";for(this.divRemTo(M,q,Y);q.signum()>0;)te=(V+Y.intValue()).toString(b).substr(1)+te,q.divRemTo(M,q,Y);return Y.intValue().toString(b)+te},a.prototype.fromRadix=function ie(b,N){this.fromInt(0),null==N&&(N=10);for(var V=this.chunkSize(N),M=Math.pow(N,V),q=!1,Y=0,te=0,J=0;J<b.length;++J){var de=u(b,J);de<0?"-"==b.charAt(J)&&0==this.signum()&&(q=!0):(te=N*te+de,++Y>=V&&(this.dMultiply(M),this.dAddOffset(te,0),Y=0,te=0))}Y>0&&(this.dMultiply(Math.pow(N,Y)),this.dAddOffset(te,0)),q&&a.ZERO.subTo(this,this)},a.prototype.fromNumber=function pe(b,N,V){if("number"==typeof N)if(b<2)this.fromInt(1);else for(this.fromNumber(b,V),this.testBit(b-1)||this.bitwiseTo(a.ONE.shiftLeft(b-1),be,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(N);)this.dAddOffset(2,0),this.bitLength()>b&&this.subTo(a.ONE.shiftLeft(b-1),this);else{var M=new Array,q=7&b;M.length=1+(b>>3),N.nextBytes(M),q>0?M[0]&=(1<<q)-1:M[0]=0,this.fromString(M,256)}},a.prototype.bitwiseTo=function Ce(b,N,V){var M,q,Y=Math.min(b.t,this.t);for(M=0;M<Y;++M)V.data[M]=N(this.data[M],b.data[M]);if(b.t<this.t){for(q=b.s&this.DM,M=Y;M<this.t;++M)V.data[M]=N(this.data[M],q);V.t=this.t}else{for(q=this.s&this.DM,M=Y;M<b.t;++M)V.data[M]=N(q,b.data[M]);V.t=b.t}V.s=N(this.s,b.s),V.clamp()},a.prototype.changeBit=function Lt(b,N){var V=a.ONE.shiftLeft(b);return this.bitwiseTo(V,N,V),V},a.prototype.addTo=function Re(b,N){for(var V=0,M=0,q=Math.min(b.t,this.t);V<q;)M+=this.data[V]+b.data[V],N.data[V++]=M&this.DM,M>>=this.DB;if(b.t<this.t){for(M+=b.s;V<this.t;)M+=this.data[V],N.data[V++]=M&this.DM,M>>=this.DB;M+=this.s}else{for(M+=this.s;V<b.t;)M+=b.data[V],N.data[V++]=M&this.DM,M>>=this.DB;M+=b.s}N.s=M<0?-1:0,M>0?N.data[V++]=M:M<-1&&(N.data[V++]=this.DV+M),N.t=V,N.clamp()},a.prototype.dMultiply=function Ve(b){this.data[this.t]=this.am(0,b-1,this,0,0,this.t),++this.t,this.clamp()},a.prototype.dAddOffset=function Oe(b,N){if(0!=b){for(;this.t<=N;)this.data[this.t++]=0;for(this.data[N]+=b;this.data[N]>=this.DV;)this.data[N]-=this.DV,++N>=this.t&&(this.data[this.t++]=0),++this.data[N]}},a.prototype.multiplyLowerTo=function Nn(b,N,V){var q,M=Math.min(this.t+b.t,N);for(V.s=0,V.t=M;M>0;)V.data[--M]=0;for(q=V.t-this.t;M<q;++M)V.data[M+this.t]=this.am(0,b.data[M],V,M,0,this.t);for(q=Math.min(b.t,N);M<q;++M)this.am(0,b.data[M],V,M,0,N-M);V.clamp()},a.prototype.multiplyUpperTo=function Rn(b,N,V){--N;var M=V.t=this.t+b.t-N;for(V.s=0;--M>=0;)V.data[M]=0;for(M=Math.max(N-this.t,0);M<b.t;++M)V.data[this.t+M-N]=this.am(N-M,b.data[M],V,0,0,this.t+M-N);V.clamp(),V.drShiftTo(1,V)},a.prototype.modInt=function On(b){if(b<=0)return 0;var N=this.DV%b,V=this.s<0?b-1:0;if(this.t>0)if(0==N)V=this.data[0]%b;else for(var M=this.t-1;M>=0;--M)V=(N*V+this.data[M])%b;return V},a.prototype.millerRabin=function Mn(b){var N=this.subtract(a.ONE),V=N.getLowestSetBit();if(V<=0)return!1;for(var Y,M=N.shiftRight(V),q=function Fn(){return{nextBytes:function(b){for(var N=0;N<b.length;++N)b[N]=Math.floor(256*Math.random())}}}(),te=0;te<b;++te){do{Y=new a(this.bitLength(),q)}while(Y.compareTo(a.ONE)<=0||Y.compareTo(N)>=0);var J=Y.modPow(M,this);if(0!=J.compareTo(a.ONE)&&0!=J.compareTo(N)){for(var de=1;de++<V&&0!=J.compareTo(N);)if(0==(J=J.modPowInt(2,this)).compareTo(a.ONE))return!1;if(0!=J.compareTo(N))return!1}}return!0},a.prototype.clone=function K(){var b=d();return this.copyTo(b),b},a.prototype.intValue=function O(){if(this.s<0){if(1==this.t)return this.data[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this.data[0];if(0==this.t)return 0}return(this.data[1]&(1<<32-this.DB)-1)<<this.DB|this.data[0]},a.prototype.byteValue=function Q(){return 0==this.t?this.s:this.data[0]<<24>>24},a.prototype.shortValue=function re(){return 0==this.t?this.s:this.data[0]<<16>>16},a.prototype.signum=function G(){return this.s<0?-1:this.t<=0||1==this.t&&this.data[0]<=0?0:1},a.prototype.toByteArray=function oe(){var b=this.t,N=new Array;N[0]=this.s;var M,V=this.DB-b*this.DB%8,q=0;if(b-- >0)for(V<this.DB&&(M=this.data[b]>>V)!=(this.s&this.DM)>>V&&(N[q++]=M|this.s<<this.DB-V);b>=0;)V<8?(M=(this.data[b]&(1<<V)-1)<<8-V,M|=this.data[--b]>>(V+=this.DB-8)):(M=this.data[b]>>(V-=8)&255,V<=0&&(V+=this.DB,--b)),128&M&&(M|=-256),0==q&&(128&this.s)!=(128&M)&&++q,(q>0||M!=this.s)&&(N[q++]=M);return N},a.prototype.equals=function me(b){return 0==this.compareTo(b)},a.prototype.min=function Te(b){return this.compareTo(b)<0?this:b},a.prototype.max=function Se(b){return this.compareTo(b)>0?this:b},a.prototype.and=function ge(b){var N=d();return this.bitwiseTo(b,ye,N),N},a.prototype.or=function Fe(b){var N=d();return this.bitwiseTo(b,be,N),N},a.prototype.xor=function Ge(b){var N=d();return this.bitwiseTo(b,He,N),N},a.prototype.andNot=function Qe(b){var N=d();return this.bitwiseTo(b,qe,N),N},a.prototype.not=function Ye(){for(var b=d(),N=0;N<this.t;++N)b.data[N]=this.DM&~this.data[N];return b.t=this.t,b.s=~this.s,b},a.prototype.shiftLeft=function $e(b){var N=d();return b<0?this.rShiftTo(-b,N):this.lShiftTo(b,N),N},a.prototype.shiftRight=function at(b){var N=d();return b<0?this.lShiftTo(-b,N):this.rShiftTo(b,N),N},a.prototype.getLowestSetBit=function ut(){for(var b=0;b<this.t;++b)if(0!=this.data[b])return b*this.DB+st(this.data[b]);return this.s<0?this.t*this.DB:-1},a.prototype.bitCount=function vt(){for(var b=0,N=this.s&this.DM,V=0;V<this.t;++V)b+=dt(this.data[V]^N);return b},a.prototype.testBit=function bt(b){var N=Math.floor(b/this.DB);return N>=this.t?0!=this.s:0!=(this.data[N]&1<<b%this.DB)},a.prototype.setBit=function Ae(b){return this.changeBit(b,be)},a.prototype.clearBit=function Be(b){return this.changeBit(b,qe)},a.prototype.flipBit=function Ne(b){return this.changeBit(b,He)},a.prototype.add=function we(b){var N=d();return this.addTo(b,N),N},a.prototype.subtract=function _e(b){var N=d();return this.subTo(b,N),N},a.prototype.multiply=function ke(b){var N=d();return this.multiplyTo(b,N),N},a.prototype.divide=function Le(b){var N=d();return this.divRemTo(b,N,null),N},a.prototype.remainder=function Ue(b){var N=d();return this.divRemTo(b,null,N),N},a.prototype.divideAndRemainder=function De(b){var N=d(),V=d();return this.divRemTo(b,N,V),new Array(N,V)},a.prototype.modPow=function Dn(b,N){var M,Y,V=b.bitLength(),q=m(1);if(V<=0)return q;M=V<18?1:V<48?3:V<144?4:V<768?5:6,Y=V<8?new X(N):N.isEven()?new mt(N):new ce(N);var te=new Array,J=3,de=M-1,Ke=(1<<M)-1;if(te[1]=Y.convert(this),M>1){var Xe=d();for(Y.sqrTo(te[1],Xe);J<=Ke;)te[J]=d(),Y.mulTo(Xe,te[J-2],te[J]),J+=2}var ot,tt,ze=b.t-1,Ut=!0,et=d();for(V=i(b.data[ze])-1;ze>=0;){for(V>=de?ot=b.data[ze]>>V-de&Ke:(ot=(b.data[ze]&(1<<V+1)-1)<<de-V,ze>0&&(ot|=b.data[ze-1]>>this.DB+V-de)),J=M;!(1&ot);)ot>>=1,--J;if((V-=J)<0&&(V+=this.DB,--ze),Ut)te[ot].copyTo(q),Ut=!1;else{for(;J>1;)Y.sqrTo(q,et),Y.sqrTo(et,q),J-=2;J>0?Y.sqrTo(q,et):(tt=q,q=et,et=tt),Y.mulTo(et,te[ot],q)}for(;ze>=0&&!(b.data[ze]&1<<V);)Y.sqrTo(q,et),tt=q,q=et,et=tt,--V<0&&(V=this.DB-1,--ze)}return Y.revert(q)},a.prototype.modInverse=function Pn(b){var N=b.isEven();if(this.isEven()&&N||0==b.signum())return a.ZERO;for(var V=b.clone(),M=this.clone(),q=m(1),Y=m(0),te=m(0),J=m(1);0!=V.signum();){for(;V.isEven();)V.rShiftTo(1,V),N?((!q.isEven()||!Y.isEven())&&(q.addTo(this,q),Y.subTo(b,Y)),q.rShiftTo(1,q)):Y.isEven()||Y.subTo(b,Y),Y.rShiftTo(1,Y);for(;M.isEven();)M.rShiftTo(1,M),N?((!te.isEven()||!J.isEven())&&(te.addTo(this,te),J.subTo(b,J)),te.rShiftTo(1,te)):J.isEven()||J.subTo(b,J),J.rShiftTo(1,J);V.compareTo(M)>=0?(V.subTo(M,V),N&&q.subTo(te,q),Y.subTo(J,Y)):(M.subTo(V,M),N&&te.subTo(q,te),J.subTo(Y,J))}return 0!=M.compareTo(a.ONE)?a.ZERO:J.compareTo(b)>=0?J.subtract(b):J.signum()<0?(J.addTo(b,J),J.signum()<0?J.add(b):J):J},a.prototype.pow=function Bn(b){return this.exp(b,new Ee)},a.prototype.gcd=function Vn(b){var N=this.s<0?this.negate():this.clone(),V=b.s<0?b.negate():b.clone();if(N.compareTo(V)<0){var M=N;N=V,V=M}var q=N.getLowestSetBit(),Y=V.getLowestSetBit();if(Y<0)return N;for(q<Y&&(Y=q),Y>0&&(N.rShiftTo(Y,N),V.rShiftTo(Y,V));N.signum()>0;)(q=N.getLowestSetBit())>0&&N.rShiftTo(q,N),(q=V.getLowestSetBit())>0&&V.rShiftTo(q,V),N.compareTo(V)>=0?(N.subTo(V,N),N.rShiftTo(1,N)):(V.subTo(N,V),V.rShiftTo(1,V));return Y>0&&V.lShiftTo(Y,V),V},a.prototype.isProbablePrime=function Kn(b){var N,V=this.abs();if(1==V.t&&V.data[0]<=Je[Je.length-1]){for(N=0;N<Je.length;++N)if(V.data[0]==Je[N])return!0;return!1}if(V.isEven())return!1;for(N=1;N<Je.length;){for(var M=Je[N],q=N+1;q<Je.length&&M<xn;)M*=Je[q++];for(M=V.modInt(M);N<q;)if(M%Je[N++]==0)return!1}return V.millerRabin(b)},ar}var $r,Jr,nr={exports:{}},ir={exports:{}};function St(){if($r)return ir.exports;$r=1;var t=ue();nt(),fe();var e=ir.exports=t.sha1=t.sha1||{};t.md.sha1=t.md.algorithms.sha1=e,e.create=function(){d||function R(){a="\x80",a+=t.util.fillString("\0",64),d=!0}();var h=null,r=t.util.createBuffer(),v=new Array(80),l={algorithm:"sha1",blockLength:64,digestLength:20,messageLength:0,fullMessageLength:null,messageLengthSize:8,start:function(){l.messageLength=0,l.fullMessageLength=l.messageLength64=[];for(var S=l.messageLengthSize/4,D=0;D<S;++D)l.fullMessageLength.push(0);return r=t.util.createBuffer(),h={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878,h4:3285377520},l}};return l.start(),l.update=function(S,D){"utf8"===D&&(S=t.util.encodeUtf8(S));var E=S.length;l.messageLength+=E,E=[E/4294967296>>>0,E>>>0];for(var u=l.fullMessageLength.length-1;u>=0;--u)l.fullMessageLength[u]+=E[1],E[1]=E[0]+(l.fullMessageLength[u]/4294967296>>>0),l.fullMessageLength[u]=l.fullMessageLength[u]>>>0,E[0]=E[1]/4294967296>>>0;return r.putBytes(S),T(h,v,r),(r.read>2048||0===r.length())&&r.compact(),l},l.digest=function(){var S=t.util.createBuffer();S.putBytes(r.bytes()),S.putBytes(a.substr(0,l.blockLength-(l.fullMessageLength[l.fullMessageLength.length-1]+l.messageLengthSize&l.blockLength-1)));for(var u,C=8*l.fullMessageLength[0],m=0;m<l.fullMessageLength.length-1;++m)S.putInt32((C+=(u=8*l.fullMessageLength[m+1])/4294967296>>>0)>>>0),C=u>>>0;S.putInt32(C);var s={h0:h.h0,h1:h.h1,h2:h.h2,h3:h.h3,h4:h.h4};T(s,v,S);var f=t.util.createBuffer();return f.putInt32(s.h0),f.putInt32(s.h1),f.putInt32(s.h2),f.putInt32(s.h3),f.putInt32(s.h4),f},l};var a=null,d=!1;function T(h,r,v){for(var l,S,D,E,u,g,m,s=v.length();s>=64;){for(S=h.h0,D=h.h1,E=h.h2,u=h.h3,g=h.h4,m=0;m<16;++m)l=v.getInt32(),r[m]=l,l=(S<<5|S>>>27)+(u^D&(E^u))+g+1518500249+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;for(;m<20;++m)r[m]=l=(l=r[m-3]^r[m-8]^r[m-14]^r[m-16])<<1|l>>>31,l=(S<<5|S>>>27)+(u^D&(E^u))+g+1518500249+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;for(;m<32;++m)r[m]=l=(l=r[m-3]^r[m-8]^r[m-14]^r[m-16])<<1|l>>>31,l=(S<<5|S>>>27)+(D^E^u)+g+1859775393+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;for(;m<40;++m)r[m]=l=(l=r[m-6]^r[m-16]^r[m-28]^r[m-32])<<2|l>>>30,l=(S<<5|S>>>27)+(D^E^u)+g+1859775393+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;for(;m<60;++m)r[m]=l=(l=r[m-6]^r[m-16]^r[m-28]^r[m-32])<<2|l>>>30,l=(S<<5|S>>>27)+(D&E|u&(D^E))+g+2400959708+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;for(;m<80;++m)r[m]=l=(l=r[m-6]^r[m-16]^r[m-28]^r[m-32])<<2|l>>>30,l=(S<<5|S>>>27)+(D^E^u)+g+3395469782+l,g=u,u=E,E=(D<<30|D>>>2)>>>0,D=S,S=l;h.h0=h.h0+S|0,h.h1=h.h1+D|0,h.h2=h.h2+E|0,h.h3=h.h3+u|0,h.h4=h.h4+g|0,s-=64}}return ir.exports}function ea(){if(Jr)return nr.exports;Jr=1;var t=ue();fe(),Ze(),St();var e=nr.exports=t.pkcs1=t.pkcs1||{};function a(d,R,T){T||(T=t.md.sha1.create());for(var h="",r=Math.ceil(R/T.digestLength),v=0;v<r;++v){var l=String.fromCharCode(v>>24&255,v>>16&255,v>>8&255,255&v);T.start(),T.update(d+l),h+=T.digest().getBytes()}return h.substring(0,R)}return e.encode_rsa_oaep=function(d,R,T){var h,r,v,l;"string"==typeof T?(h=T,r=arguments[3]||void 0,v=arguments[4]||void 0):T&&(h=T.label||void 0,r=T.seed||void 0,v=T.md||void 0,T.mgf1&&T.mgf1.md&&(l=T.mgf1.md)),v?v.start():v=t.md.sha1.create(),l||(l=v);var S=Math.ceil(d.n.bitLength()/8),D=S-2*v.digestLength-2;if(R.length>D)throw(E=new Error("RSAES-OAEP input message length is too long.")).length=R.length,E.maxLength=D,E;h||(h=""),v.update(h,"raw");for(var u=v.digest(),g="",C=D-R.length,m=0;m<C;m++)g+="\0";var s=u.getBytes()+g+"\x01"+R;if(r){if(r.length!==v.digestLength){var E;throw(E=new Error("Invalid RSAES-OAEP seed. The seed length must match the digest length.")).seedLength=r.length,E.digestLength=v.digestLength,E}}else r=t.random.getBytes(v.digestLength);var f=a(r,S-v.digestLength-1,l),y=t.util.xorBytes(s,f,s.length),I=a(y,v.digestLength,l);return"\0"+t.util.xorBytes(r,I,r.length)+y},e.decode_rsa_oaep=function(d,R,T){var h,r,v;"string"==typeof T?(h=T,r=arguments[3]||void 0):T&&(h=T.label||void 0,r=T.md||void 0,T.mgf1&&T.mgf1.md&&(v=T.mgf1.md));var l=Math.ceil(d.n.bitLength()/8);if(R.length!==l)throw(S=new Error("RSAES-OAEP encoded message length is invalid.")).length=R.length,S.expectedLength=l,S;if(void 0===r?r=t.md.sha1.create():r.start(),v||(v=r),l<2*r.digestLength+2)throw new Error("RSAES-OAEP key is too short for the hash function.");h||(h=""),r.update(h,"raw");for(var D=r.digest().getBytes(),E=R.charAt(0),u=R.substring(1,r.digestLength+1),g=R.substring(1+r.digestLength),C=a(g,r.digestLength,v),s=a(t.util.xorBytes(u,C,u.length),l-r.digestLength-1,v),f=t.util.xorBytes(g,s,g.length),y=f.substring(0,r.digestLength),S="\0"!==E,I=0;I<r.digestLength;++I)S|=D.charAt(I)!==y.charAt(I);for(var _=1,p=r.digestLength,i=r.digestLength;i<f.length;i++){var n=f.charCodeAt(i);S|=n&(_?65534:0),p+=_&=1&n^1}if(S||1!==f.charCodeAt(p))throw new Error("Invalid RSAES-OAEP padding.");return f.substring(p+1)},nr.exports}var ta,sr,aa,or,na,Rt={exports:{}};function ra(){if(ta)return Rt.exports;ta=1;var t=ue();return fe(),Nt(),Ze(),function(){if(t.prime)Rt.exports=t.prime;else{var e=Rt.exports=t.prime=t.prime||{},a=t.jsbn.BigInteger,d=[6,4,2,4,2,4,6,2],R=new a(null);R.fromInt(30);var T=function(E,u){return E|u};e.generateProbablePrime=function(E,u,g){"function"==typeof u&&(g=u,u={});var C=(u=u||{}).algorithm||"PRIMEINC";"string"==typeof C&&(C={name:C}),C.options=C.options||{};var m=u.prng||t.random;if("PRIMEINC"===C.name)return function h(E,u,g,C){return"workers"in g?function l(E,u,g,C){if(typeof Worker>"u")return r(E,u,g,C);var m=S(E,u),s=g.workers,f=g.workLoad||100,y=30*f/8,I=g.workerScript||"forge/prime.worker.js";if(-1===s)return t.util.estimateCores(function(p,i){p&&(i=2),s=i-1,_()});function _(){s=Math.max(1,s);for(var p=[],i=0;i<s;++i)p[i]=new Worker(I);for(i=0;i<s;++i)p[i].addEventListener("message",B);var n=!1;function B(U){if(!n){var P=U.data;if(P.found){for(var L=0;L<p.length;++L)p[L].terminate();return n=!0,C(null,new a(P.prime,16))}m.bitLength()>E&&(m=S(E,u));var x=m.toString(16);U.target.postMessage({hex:x,workLoad:f}),m.dAddOffset(y,0)}}}_()}(E,u,g,C):r(E,u,g,C)}(E,{nextBytes:function(f){for(var y=m.getBytesSync(f.length),I=0;I<f.length;++I)f[I]=y.charCodeAt(I)}},C.options,g);throw new Error("Invalid prime generation algorithm: "+C.name)}}function r(E,u,g,C){var m=S(E,u),f=function D(E){return E<=100?27:E<=150?18:E<=200?15:E<=250?12:E<=300?9:E<=350?8:E<=400?7:E<=500?6:E<=600?5:E<=800?4:E<=1250?3:2}(m.bitLength());"millerRabinTests"in g&&(f=g.millerRabinTests);var y=10;"maxBlockTime"in g&&(y=g.maxBlockTime),v(m,E,u,0,f,y,C)}function v(E,u,g,C,m,s,f){var y=+new Date;do{if(E.bitLength()>u&&(E=S(u,g)),E.isProbablePrime(m))return f(null,E);E.dAddOffset(d[C++%8],0)}while(s<0||+new Date-y<s);t.util.setImmediate(function(){v(E,u,g,C,m,s,f)})}function S(E,u){var g=new a(E,u),C=E-1;return g.testBit(C)||g.bitwiseTo(a.ONE.shiftLeft(C),T,g),g.dAddOffset(31-g.mod(R).byteValue(),0),g}}(),Rt.exports}function wt(){if(aa)return sr;aa=1;var t=ue();if(rt(),Nt(),pt(),ea(),ra(),Ze(),fe(),typeof e>"u")var e=t.jsbn.BigInteger;var a=t.util.isNodejs?Zt:null,d=t.asn1,R=t.util;t.pki=t.pki||{},sr=t.pki.rsa=t.rsa=t.rsa||{};var T=t.pki,h=[6,4,2,4,2,4,6,2],r={name:"PrivateKeyInfo",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:d.Class.UNIVERSAL,type:d.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:d.Class.UNIVERSAL,type:d.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},v={name:"RSAPrivateKey",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPrivateKey.version",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"privateKeyCoefficient"}]},l={name:"RSAPublicKey",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPublicKey.modulus",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",tagClass:d.Class.UNIVERSAL,type:d.Type.INTEGER,constructed:!1,capture:"publicKeyExponent"}]},S=t.pki.rsa.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:d.Class.UNIVERSAL,type:d.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{name:"SubjectPublicKeyInfo.subjectPublicKey",tagClass:d.Class.UNIVERSAL,type:d.Type.BITSTRING,constructed:!1,value:[{name:"SubjectPublicKeyInfo.subjectPublicKey.RSAPublicKey",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"rsaPublicKey"}]}]},D={name:"DigestInfo",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm",tagClass:d.Class.UNIVERSAL,type:d.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm.algorithmIdentifier",tagClass:d.Class.UNIVERSAL,type:d.Type.OID,constructed:!1,capture:"algorithmIdentifier"},{name:"DigestInfo.DigestAlgorithm.parameters",tagClass:d.Class.UNIVERSAL,type:d.Type.NULL,capture:"parameters",optional:!0,constructed:!1}]},{name:"DigestInfo.digest",tagClass:d.Class.UNIVERSAL,type:d.Type.OCTETSTRING,constructed:!1,capture:"digest"}]},E=function(i){if(!(i.algorithm in T.oids)){var B=new Error("Unknown message digest algorithm.");throw B.algorithm=i.algorithm,B}var U=d.oidToDer(T.oids[i.algorithm]).getBytes(),P=d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[]),L=d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[]);L.value.push(d.create(d.Class.UNIVERSAL,d.Type.OID,!1,U)),L.value.push(d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,""));var x=d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,i.digest().getBytes());return P.value.push(L),P.value.push(x),d.toDer(P).getBytes()},u=function(i,n,B){if(B)return i.modPow(n.e,n.n);if(!n.p||!n.q)return i.modPow(n.d,n.n);var U;n.dP||(n.dP=n.d.mod(n.p.subtract(e.ONE))),n.dQ||(n.dQ=n.d.mod(n.q.subtract(e.ONE))),n.qInv||(n.qInv=n.q.modInverse(n.p));do{U=new e(t.util.bytesToHex(t.random.getBytes(n.n.bitLength()/8)),16)}while(U.compareTo(n.n)>=0||!U.gcd(n.n).equals(e.ONE));for(var P=(i=i.multiply(U.modPow(n.e,n.n)).mod(n.n)).mod(n.p).modPow(n.dP,n.p),L=i.mod(n.q).modPow(n.dQ,n.q);P.compareTo(L)<0;)P=P.add(n.p);var x=P.subtract(L).multiply(n.qInv).mod(n.p).multiply(n.q).add(L);return x.multiply(U.modInverse(n.n)).mod(n.n)};function g(i,n,B){var U=t.util.createBuffer(),P=Math.ceil(n.n.bitLength()/8);if(i.length>P-11){var L=new Error("Message is too long for PKCS#1 v1.5 padding.");throw L.length=i.length,L.max=P-11,L}U.putByte(0),U.putByte(B);var H,x=P-3-i.length;if(0===B||1===B){H=0===B?0:255;for(var F=0;F<x;++F)U.putByte(H)}else for(;x>0;){var j=0,z=t.random.getBytes(x);for(F=0;F<x;++F)0===(H=z.charCodeAt(F))?++j:U.putByte(H);x=j}return U.putByte(0),U.putBytes(i),U}function C(i,n,B,U){var P=Math.ceil(n.n.bitLength()/8),L=t.util.createBuffer(i),x=L.getByte(),H=L.getByte();if(0!==x||B&&0!==H&&1!==H||!B&&2!=H||B&&0===H&&typeof U>"u")throw new Error("Encryption block is invalid.");var F=0;if(0===H){F=P-3-U;for(var j=0;j<F;++j)if(0!==L.getByte())throw new Error("Encryption block is invalid.")}else if(1===H)for(F=0;L.length()>1;){if(255!==L.getByte()){--L.read;break}++F}else if(2===H)for(F=0;L.length()>1;){if(0===L.getByte()){--L.read;break}++F}if(0!==L.getByte()||F!==P-3-L.length())throw new Error("Encryption block is invalid.");return L.getBytes()}function s(i){var n=i.toString(16);n[0]>="8"&&(n="00"+n);var B=t.util.hexToBytes(n);return B.length>1&&(0===B.charCodeAt(0)&&!(128&B.charCodeAt(1))||255===B.charCodeAt(0)&&128==(128&B.charCodeAt(1)))?B.substr(1):B}function f(i){return i<=100?27:i<=150?18:i<=200?15:i<=250?12:i<=300?9:i<=350?8:i<=400?7:i<=500?6:i<=600?5:i<=800?4:i<=1250?3:2}function y(i){return t.util.isNodejs&&"function"==typeof a[i]}function I(i){return typeof R.globalScope<"u"&&"object"==typeof R.globalScope.crypto&&"object"==typeof R.globalScope.crypto.subtle&&"function"==typeof R.globalScope.crypto.subtle[i]}function _(i){return typeof R.globalScope<"u"&&"object"==typeof R.globalScope.msCrypto&&"object"==typeof R.globalScope.msCrypto.subtle&&"function"==typeof R.globalScope.msCrypto.subtle[i]}function p(i){for(var n=t.util.hexToBytes(i.toString(16)),B=new Uint8Array(n.length),U=0;U<n.length;++U)B[U]=n.charCodeAt(U);return B}return T.rsa.encrypt=function(i,n,B){var P,U=B,L=Math.ceil(n.n.bitLength()/8);!1!==B&&!0!==B?(U=2===B,P=g(i,n,B)):(P=t.util.createBuffer()).putBytes(i);for(var x=new e(P.toHex(),16),F=u(x,n,U).toString(16),j=t.util.createBuffer(),z=L-Math.ceil(F.length/2);z>0;)j.putByte(0),--z;return j.putBytes(t.util.hexToBytes(F)),j.getBytes()},T.rsa.decrypt=function(i,n,B,U){var P=Math.ceil(n.n.bitLength()/8);if(i.length!==P){var L=new Error("Encrypted message length is invalid.");throw L.length=i.length,L.expected=P,L}var x=new e(t.util.createBuffer(i).toHex(),16);if(x.compareTo(n.n)>=0)throw new Error("Encrypted message is invalid.");for(var F=u(x,n,B).toString(16),j=t.util.createBuffer(),z=P-Math.ceil(F.length/2);z>0;)j.putByte(0),--z;return j.putBytes(t.util.hexToBytes(F)),!1!==U?C(j.getBytes(),n,B):j.getBytes()},T.rsa.createKeyPairGenerationState=function(i,n,B){"string"==typeof i&&(i=parseInt(i,10)),i=i||2048;var x,U=(B=B||{}).prng||t.random,P={nextBytes:function(H){for(var F=U.getBytesSync(H.length),j=0;j<H.length;++j)H[j]=F.charCodeAt(j)}},L=B.algorithm||"PRIMEINC";if("PRIMEINC"!==L)throw new Error("Invalid key generation algorithm: "+L);return(x={algorithm:L,state:0,bits:i,rng:P,eInt:n||65537,e:new e(null),p:null,q:null,qBits:i>>1,pBits:i-(i>>1),pqState:0,num:null,keys:null}).e.fromInt(x.eInt),x},T.rsa.stepKeyPairGenerationState=function(i,n){"algorithm"in i||(i.algorithm="PRIMEINC");var B=new e(null);B.fromInt(30);for(var x,U=0,P=function(X,$){return X|$},L=+new Date,H=0;null===i.keys&&(n<=0||H<n);){if(0===i.state){var F=null===i.p?i.pBits:i.qBits,j=F-1;0===i.pqState?(i.num=new e(F,i.rng),i.num.testBit(j)||i.num.bitwiseTo(e.ONE.shiftLeft(j),P,i.num),i.num.dAddOffset(31-i.num.mod(B).byteValue(),0),U=0,++i.pqState):1===i.pqState?i.num.bitLength()>F?i.pqState=0:i.num.isProbablePrime(f(i.num.bitLength()))?++i.pqState:i.num.dAddOffset(h[U++%8],0):2===i.pqState?i.pqState=0===i.num.subtract(e.ONE).gcd(i.e).compareTo(e.ONE)?3:0:3===i.pqState&&(i.pqState=0,null===i.p?i.p=i.num:i.q=i.num,null!==i.p&&null!==i.q&&++i.state,i.num=null)}else if(1===i.state)i.p.compareTo(i.q)<0&&(i.num=i.p,i.p=i.q,i.q=i.num),++i.state;else if(2===i.state)i.p1=i.p.subtract(e.ONE),i.q1=i.q.subtract(e.ONE),i.phi=i.p1.multiply(i.q1),++i.state;else if(3===i.state)0===i.phi.gcd(i.e).compareTo(e.ONE)?++i.state:(i.p=null,i.q=null,i.state=0);else if(4===i.state)i.n=i.p.multiply(i.q),i.n.bitLength()===i.bits?++i.state:(i.q=null,i.state=0);else if(5===i.state){var z=i.e.modInverse(i.phi);i.keys={privateKey:T.rsa.setPrivateKey(i.n,i.e,z,i.p,i.q,z.mod(i.p1),z.mod(i.q1),i.q.modInverse(i.p)),publicKey:T.rsa.setPublicKey(i.n,i.e)}}H+=(x=+new Date)-L,L=x}return null!==i.keys},T.rsa.generateKeyPair=function(i,n,B,U){if(1===arguments.length?"object"==typeof i?(B=i,i=void 0):"function"==typeof i&&(U=i,i=void 0):2===arguments.length?"number"==typeof i?"function"==typeof n?(U=n,n=void 0):"number"!=typeof n&&(B=n,n=void 0):(B=i,U=n,i=void 0,n=void 0):3===arguments.length&&("number"==typeof n?"function"==typeof B&&(U=B,B=void 0):(U=B,B=n,n=void 0)),B=B||{},void 0===i&&(i=B.bits||2048),void 0===n&&(n=B.e||65537),!t.options.usePureJavaScript&&!B.prng&&i>=256&&i<=16384&&(65537===n||3===n))if(U){if(y("generateKeyPair"))return a.generateKeyPair("rsa",{modulusLength:i,publicExponent:n,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}},function(H,F,j){if(H)return U(H);U(null,{privateKey:T.privateKeyFromPem(j),publicKey:T.publicKeyFromPem(F)})});if(I("generateKey")&&I("exportKey"))return R.globalScope.crypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:i,publicExponent:p(n),hash:{name:"SHA-256"}},!0,["sign","verify"]).then(function(H){return R.globalScope.crypto.subtle.exportKey("pkcs8",H.privateKey)}).then(void 0,function(H){U(H)}).then(function(H){if(H){var F=T.privateKeyFromAsn1(d.fromDer(t.util.createBuffer(H)));U(null,{privateKey:F,publicKey:T.setRsaPublicKey(F.n,F.e)})}});if(_("generateKey")&&_("exportKey")){var P=R.globalScope.msCrypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:i,publicExponent:p(n),hash:{name:"SHA-256"}},!0,["sign","verify"]);return P.oncomplete=function(H){var j=R.globalScope.msCrypto.subtle.exportKey("pkcs8",H.target.result.privateKey);j.oncomplete=function(z){var $=T.privateKeyFromAsn1(d.fromDer(t.util.createBuffer(z.target.result)));U(null,{privateKey:$,publicKey:T.setRsaPublicKey($.n,$.e)})},j.onerror=function(z){U(z)}},void(P.onerror=function(H){U(H)})}}else if(y("generateKeyPairSync")){var L=a.generateKeyPairSync("rsa",{modulusLength:i,publicExponent:n,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}});return{privateKey:T.privateKeyFromPem(L.privateKey),publicKey:T.publicKeyFromPem(L.publicKey)}}var x=T.rsa.createKeyPairGenerationState(i,n,B);if(!U)return T.rsa.stepKeyPairGenerationState(x,0),x.keys;!function m(i,n,B){"function"==typeof n&&(B=n,n={});var U={algorithm:{name:(n=n||{}).algorithm||"PRIMEINC",options:{workers:n.workers||2,workLoad:n.workLoad||100,workerScript:n.workerScript}}};function P(){L(i.pBits,function(H,F){return H?B(H):(i.p=F,null!==i.q?x(H,i.q):void L(i.qBits,x))})}function L(H,F){t.prime.generateProbablePrime(H,U,F)}function x(H,F){if(H)return B(H);if(i.q=F,i.p.compareTo(i.q)<0){var j=i.p;i.p=i.q,i.q=j}if(0!==i.p.subtract(e.ONE).gcd(i.e).compareTo(e.ONE))return i.p=null,void P();if(0!==i.q.subtract(e.ONE).gcd(i.e).compareTo(e.ONE))return i.q=null,void L(i.qBits,x);if(i.p1=i.p.subtract(e.ONE),i.q1=i.q.subtract(e.ONE),i.phi=i.p1.multiply(i.q1),0!==i.phi.gcd(i.e).compareTo(e.ONE))return i.p=i.q=null,void P();if(i.n=i.p.multiply(i.q),i.n.bitLength()!==i.bits)return i.q=null,void L(i.qBits,x);var z=i.e.modInverse(i.phi);i.keys={privateKey:T.rsa.setPrivateKey(i.n,i.e,z,i.p,i.q,z.mod(i.p1),z.mod(i.q1),i.q.modInverse(i.p)),publicKey:T.rsa.setPublicKey(i.n,i.e)},B(null,i.keys)}"prng"in n&&(U.prng=n.prng),P()}(x,B,U)},T.setRsaPublicKey=T.rsa.setPublicKey=function(i,n){var B={n:i,e:n,encrypt:function(U,P,L){if("string"==typeof P?P=P.toUpperCase():void 0===P&&(P="RSAES-PKCS1-V1_5"),"RSAES-PKCS1-V1_5"===P)P={encode:function(H,F,j){return g(H,F,2).getBytes()}};else if("RSA-OAEP"===P||"RSAES-OAEP"===P)P={encode:function(H,F){return t.pkcs1.encode_rsa_oaep(F,H,L)}};else if(-1!==["RAW","NONE","NULL",null].indexOf(P))P={encode:function(H){return H}};else if("string"==typeof P)throw new Error('Unsupported encryption scheme: "'+P+'".');var x=P.encode(U,B,!0);return T.rsa.encrypt(x,B,!0)},verify:function(U,P,L,x){"string"==typeof L?L=L.toUpperCase():void 0===L&&(L="RSASSA-PKCS1-V1_5"),void 0===x&&(x={_parseAllDigestBytes:!0}),"_parseAllDigestBytes"in x||(x._parseAllDigestBytes=!0),"RSASSA-PKCS1-V1_5"===L?L={verify:function(F,j){j=C(j,B,!0);var z=d.fromDer(j,{parseAllBytes:x._parseAllDigestBytes}),X={},$=[];if(!d.validate(z,D,X,$))throw(ne=new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value.")).errors=$,ne;var ne,se=d.derToOid(X.algorithmIdentifier);if(se!==t.oids.md2&&se!==t.oids.md5&&se!==t.oids.sha1&&se!==t.oids.sha224&&se!==t.oids.sha256&&se!==t.oids.sha384&&se!==t.oids.sha512&&se!==t.oids["sha512-224"]&&se!==t.oids["sha512-256"])throw(ne=new Error("Unknown RSASSA-PKCS1-v1_5 DigestAlgorithm identifier.")).oid=se,ne;if((se===t.oids.md2||se===t.oids.md5)&&!("parameters"in X))throw new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value. Missing algorithm identifer NULL parameters.");return F===X.digest}}:("NONE"===L||"NULL"===L||null===L)&&(L={verify:function(F,j){return F===C(j,B,!0)}});var H=T.rsa.decrypt(P,B,!0,!1);return L.verify(U,H,B.n.bitLength())}};return B},T.setRsaPrivateKey=T.rsa.setPrivateKey=function(i,n,B,U,P,L,x,H){var F={n:i,e:n,d:B,p:U,q:P,dP:L,dQ:x,qInv:H,decrypt:function(j,z,X){"string"==typeof z?z=z.toUpperCase():void 0===z&&(z="RSAES-PKCS1-V1_5");var $=T.rsa.decrypt(j,F,!1,!1);if("RSAES-PKCS1-V1_5"===z)z={decode:C};else if("RSA-OAEP"===z||"RSAES-OAEP"===z)z={decode:function(ne,se){return t.pkcs1.decode_rsa_oaep(se,ne,X)}};else{if(-1===["RAW","NONE","NULL",null].indexOf(z))throw new Error('Unsupported encryption scheme: "'+z+'".');z={decode:function(ne){return ne}}}return z.decode($,F,!1)},sign:function(j,z){var X=!1;"string"==typeof z&&(z=z.toUpperCase()),void 0===z||"RSASSA-PKCS1-V1_5"===z?(z={encode:E},X=1):("NONE"===z||"NULL"===z||null===z)&&(z={encode:function(){return j}},X=1);var $=z.encode(j,F.n.bitLength());return T.rsa.encrypt($,F,X)}};return F},T.wrapRsaPrivateKey=function(i){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(0).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(T.oids.rsaEncryption).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")]),d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,d.toDer(i).getBytes())])},T.privateKeyFromAsn1=function(i){var P,L,x,H,F,j,z,X,n={},B=[];if(d.validate(i,r,n,B)&&(i=d.fromDer(t.util.createBuffer(n.privateKey))),!d.validate(i,v,n={},B=[])){var U=new Error("Cannot read private key. ASN.1 object does not contain an RSAPrivateKey.");throw U.errors=B,U}return P=t.util.createBuffer(n.privateKeyModulus).toHex(),L=t.util.createBuffer(n.privateKeyPublicExponent).toHex(),x=t.util.createBuffer(n.privateKeyPrivateExponent).toHex(),H=t.util.createBuffer(n.privateKeyPrime1).toHex(),F=t.util.createBuffer(n.privateKeyPrime2).toHex(),j=t.util.createBuffer(n.privateKeyExponent1).toHex(),z=t.util.createBuffer(n.privateKeyExponent2).toHex(),X=t.util.createBuffer(n.privateKeyCoefficient).toHex(),T.setRsaPrivateKey(new e(P,16),new e(L,16),new e(x,16),new e(H,16),new e(F,16),new e(j,16),new e(z,16),new e(X,16))},T.privateKeyToAsn1=T.privateKeyToRSAPrivateKey=function(i){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(0).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.n)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.e)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.d)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.p)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.q)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.dP)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.dQ)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.qInv))])},T.publicKeyFromAsn1=function(i){var n={},B=[];if(d.validate(i,S,n,B)){var P,U=d.derToOid(n.publicKeyOid);if(U!==T.oids.rsaEncryption)throw(P=new Error("Cannot read public key. Unknown OID.")).oid=U,P;i=n.rsaPublicKey}if(!d.validate(i,l,n,B=[]))throw(P=new Error("Cannot read public key. ASN.1 object does not contain an RSAPublicKey.")).errors=B,P;var L=t.util.createBuffer(n.publicKeyModulus).toHex(),x=t.util.createBuffer(n.publicKeyExponent).toHex();return T.setRsaPublicKey(new e(L,16),new e(x,16))},T.publicKeyToAsn1=T.publicKeyToSubjectPublicKeyInfo=function(i){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(T.oids.rsaEncryption).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")]),d.create(d.Class.UNIVERSAL,d.Type.BITSTRING,!1,[T.publicKeyToRSAPublicKey(i)])])},T.publicKeyToRSAPublicKey=function(i){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.n)),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,s(i.e))])},sr}function ia(){if(na)return or;na=1;var t=ue();if(lt(),rt(),At(),nt(),pt(),Jt(),yt(),Ze(),Wr(),wt(),fe(),typeof e>"u")var e=t.jsbn.BigInteger;var a=t.asn1,d=t.pki=t.pki||{};or=d.pbe=t.pbe=t.pbe||{};var R=d.oids,T={name:"EncryptedPrivateKeyInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedPrivateKeyInfo.encryptionAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"encryptionOid"},{name:"AlgorithmIdentifier.parameters",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,captureAsn1:"encryptionParams"}]},{name:"EncryptedPrivateKeyInfo.encryptedData",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"encryptedData"}]},h={name:"PBES2Algorithms",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc.oid",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"kdfOid"},{name:"PBES2Algorithms.params",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.params.salt",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"kdfSalt"},{name:"PBES2Algorithms.params.iterationCount",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"kdfIterationCount"},{name:"PBES2Algorithms.params.keyLength",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,optional:!0,capture:"keyLength"},{name:"PBES2Algorithms.params.prf",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,optional:!0,value:[{name:"PBES2Algorithms.params.prf.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"prfOid"}]}]}]},{name:"PBES2Algorithms.encryptionScheme",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.encryptionScheme.oid",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"encOid"},{name:"PBES2Algorithms.encryptionScheme.iv",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"encIv"}]}]},r={name:"pkcs-12PbeParams",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"pkcs-12PbeParams.salt",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"salt"},{name:"pkcs-12PbeParams.iterations",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"iterations"}]};function v(E,u){return E.start().update(u).digest().getBytes()}function l(E){var u;if(E){if(!(u=d.oids[a.derToOid(E)])){var g=new Error("Unsupported PRF OID.");throw g.oid=E,g.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],g}}else u="hmacWithSHA1";return S(u)}function S(E){var u=t.md;switch(E){case"hmacWithSHA224":u=t.md.sha512;case"hmacWithSHA1":case"hmacWithSHA256":case"hmacWithSHA384":case"hmacWithSHA512":E=E.substr(8).toLowerCase();break;default:var g=new Error("Unsupported PRF algorithm.");throw g.algorithm=E,g.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],g}if(!u||!(E in u))throw new Error("Unknown hash algorithm: "+E);return u[E].create()}return d.encryptPrivateKeyInfo=function(E,u,g){(g=g||{}).saltSize=g.saltSize||8,g.count=g.count||2048,g.algorithm=g.algorithm||"aes128",g.prfAlgorithm=g.prfAlgorithm||"sha1";var f,y,I,C=t.random.getBytesSync(g.saltSize),m=g.count,s=a.integerToDer(m);if(0===g.algorithm.indexOf("aes")||"des"===g.algorithm){var _,p,i;switch(g.algorithm){case"aes128":f=16,_=16,p=R["aes128-CBC"],i=t.aes.createEncryptionCipher;break;case"aes192":f=24,_=16,p=R["aes192-CBC"],i=t.aes.createEncryptionCipher;break;case"aes256":f=32,_=16,p=R["aes256-CBC"],i=t.aes.createEncryptionCipher;break;case"des":f=8,_=8,p=R.desCBC,i=t.des.createEncryptionCipher;break;default:throw(n=new Error("Cannot encrypt private key. Unknown encryption algorithm.")).algorithm=g.algorithm,n}var B="hmacWith"+g.prfAlgorithm.toUpperCase(),U=S(B),P=t.pkcs5.pbkdf2(u,C,m,f,U),L=t.random.getBytesSync(_);(x=i(P)).start(L),x.update(a.toDer(E)),x.finish(),I=x.output.getBytes();var H=function D(E,u,g,C){var m=a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OCTETSTRING,!1,E),a.create(a.Class.UNIVERSAL,a.Type.INTEGER,!1,u.getBytes())]);return"hmacWithSHA1"!==C&&m.value.push(a.create(a.Class.UNIVERSAL,a.Type.INTEGER,!1,t.util.hexToBytes(g.toString(16))),a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OID,!1,a.oidToDer(d.oids[C]).getBytes()),a.create(a.Class.UNIVERSAL,a.Type.NULL,!1,"")])),m}(C,s,f,B);y=a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OID,!1,a.oidToDer(R.pkcs5PBES2).getBytes()),a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OID,!1,a.oidToDer(R.pkcs5PBKDF2).getBytes()),H]),a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OID,!1,a.oidToDer(p).getBytes()),a.create(a.Class.UNIVERSAL,a.Type.OCTETSTRING,!1,L)])])])}else{var n;if("3des"!==g.algorithm)throw(n=new Error("Cannot encrypt private key. Unknown encryption algorithm.")).algorithm=g.algorithm,n;f=24;var x,F=new t.util.ByteBuffer(C);P=d.pbe.generatePkcs12Key(u,F,1,m,f),L=d.pbe.generatePkcs12Key(u,F,2,m,f),(x=t.des.createEncryptionCipher(P)).start(L),x.update(a.toDer(E)),x.finish(),I=x.output.getBytes(),y=a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OID,!1,a.oidToDer(R["pbeWithSHAAnd3-KeyTripleDES-CBC"]).getBytes()),a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[a.create(a.Class.UNIVERSAL,a.Type.OCTETSTRING,!1,C),a.create(a.Class.UNIVERSAL,a.Type.INTEGER,!1,s.getBytes())])])}return a.create(a.Class.UNIVERSAL,a.Type.SEQUENCE,!0,[y,a.create(a.Class.UNIVERSAL,a.Type.OCTETSTRING,!1,I)])},d.decryptPrivateKeyInfo=function(E,u){var g=null,C={},m=[];if(!a.validate(E,T,C,m)){var s=new Error("Cannot read encrypted private key. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw s.errors=m,s}var f=a.derToOid(C.encryptionOid),y=d.pbe.getCipher(f,C.encryptionParams,u),I=t.util.createBuffer(C.encryptedData);return y.update(I),y.finish()&&(g=a.fromDer(y.output)),g},d.encryptedPrivateKeyToPem=function(E,u){var g={type:"ENCRYPTED PRIVATE KEY",body:a.toDer(E).getBytes()};return t.pem.encode(g,{maxline:u})},d.encryptedPrivateKeyFromPem=function(E){var u=t.pem.decode(E)[0];if("ENCRYPTED PRIVATE KEY"!==u.type){var g=new Error('Could not convert encrypted private key from PEM; PEM header type is "ENCRYPTED PRIVATE KEY".');throw g.headerType=u.type,g}if(u.procType&&"ENCRYPTED"===u.procType.type)throw new Error("Could not convert encrypted private key from PEM; PEM is encrypted.");return a.fromDer(u.body)},d.encryptRsaPrivateKey=function(E,u,g){if(!(g=g||{}).legacy){var C=d.wrapRsaPrivateKey(d.privateKeyToAsn1(E));return C=d.encryptPrivateKeyInfo(C,u,g),d.encryptedPrivateKeyToPem(C)}var m,s,f,y;switch(g.algorithm){case"aes128":m="AES-128-CBC",f=16,s=t.random.getBytesSync(16),y=t.aes.createEncryptionCipher;break;case"aes192":m="AES-192-CBC",f=24,s=t.random.getBytesSync(16),y=t.aes.createEncryptionCipher;break;case"aes256":m="AES-256-CBC",f=32,s=t.random.getBytesSync(16),y=t.aes.createEncryptionCipher;break;case"3des":m="DES-EDE3-CBC",f=24,s=t.random.getBytesSync(8),y=t.des.createEncryptionCipher;break;case"des":m="DES-CBC",f=8,s=t.random.getBytesSync(8),y=t.des.createEncryptionCipher;break;default:var I=new Error('Could not encrypt RSA private key; unsupported encryption algorithm "'+g.algorithm+'".');throw I.algorithm=g.algorithm,I}var p=y(t.pbe.opensslDeriveBytes(u,s.substr(0,8),f));p.start(s),p.update(a.toDer(d.privateKeyToAsn1(E))),p.finish();var i={type:"RSA PRIVATE KEY",procType:{version:"4",type:"ENCRYPTED"},dekInfo:{algorithm:m,parameters:t.util.bytesToHex(s).toUpperCase()},body:p.output.getBytes()};return t.pem.encode(i)},d.decryptRsaPrivateKey=function(E,u){var g=null,C=t.pem.decode(E)[0];if("ENCRYPTED PRIVATE KEY"!==C.type&&"PRIVATE KEY"!==C.type&&"RSA PRIVATE KEY"!==C.type)throw(m=new Error('Could not convert private key from PEM; PEM header type is not "ENCRYPTED PRIVATE KEY", "PRIVATE KEY", or "RSA PRIVATE KEY".')).headerType=m,m;if(C.procType&&"ENCRYPTED"===C.procType.type){var s,f;switch(C.dekInfo.algorithm){case"DES-CBC":s=8,f=t.des.createDecryptionCipher;break;case"DES-EDE3-CBC":s=24,f=t.des.createDecryptionCipher;break;case"AES-128-CBC":s=16,f=t.aes.createDecryptionCipher;break;case"AES-192-CBC":s=24,f=t.aes.createDecryptionCipher;break;case"AES-256-CBC":s=32,f=t.aes.createDecryptionCipher;break;case"RC2-40-CBC":s=5,f=function(i){return t.rc2.createDecryptionCipher(i,40)};break;case"RC2-64-CBC":s=8,f=function(i){return t.rc2.createDecryptionCipher(i,64)};break;case"RC2-128-CBC":s=16,f=function(i){return t.rc2.createDecryptionCipher(i,128)};break;default:var m;throw(m=new Error('Could not decrypt private key; unsupported encryption algorithm "'+C.dekInfo.algorithm+'".')).algorithm=C.dekInfo.algorithm,m}var y=t.util.hexToBytes(C.dekInfo.parameters),_=f(t.pbe.opensslDeriveBytes(u,y.substr(0,8),s));if(_.start(y),_.update(t.util.createBuffer(C.body)),!_.finish())return g;g=_.output.getBytes()}else g=C.body;return null!==(g="ENCRYPTED PRIVATE KEY"===C.type?d.decryptPrivateKeyInfo(a.fromDer(g),u):a.fromDer(g))&&(g=d.privateKeyFromAsn1(g)),g},d.pbe.generatePkcs12Key=function(E,u,g,C,m,s){var f,y;if(typeof s>"u"||null===s){if(!("sha1"in t.md))throw new Error('"sha1" hash algorithm unavailable.');s=t.md.sha1.create()}var I=s.digestLength,_=s.blockLength,p=new t.util.ByteBuffer,i=new t.util.ByteBuffer;if(null!=E){for(y=0;y<E.length;y++)i.putInt16(E.charCodeAt(y));i.putInt16(0)}var n=i.length(),B=u.length(),U=new t.util.ByteBuffer;U.fillWithByte(g,_);var P=_*Math.ceil(B/_),L=new t.util.ByteBuffer;for(y=0;y<P;y++)L.putByte(u.at(y%B));var x=_*Math.ceil(n/_),H=new t.util.ByteBuffer;for(y=0;y<x;y++)H.putByte(i.at(y%n));var F=L;F.putBuffer(H);for(var j=Math.ceil(m/I),z=1;z<=j;z++){var X=new t.util.ByteBuffer;X.putBytes(U.bytes()),X.putBytes(F.bytes());for(var $=0;$<C;$++)s.start(),s.update(X.getBytes()),X=s.digest();var ne=new t.util.ByteBuffer;for(y=0;y<_;y++)ne.putByte(X.at(y%I));var se=Math.ceil(B/_)+Math.ceil(n/_),he=new t.util.ByteBuffer;for(f=0;f<se;f++){var ve=new t.util.ByteBuffer(F.getBytes(_)),W=511;for(y=ne.length()-1;y>=0;y--)W>>=8,W+=ne.at(y)+ve.at(y),ve.setAt(y,255&W);he.putBuffer(ve)}F=he,p.putBuffer(X)}return p.truncate(p.length()-m),p},d.pbe.getCipher=function(E,u,g){switch(E){case d.oids.pkcs5PBES2:return d.pbe.getCipherForPBES2(E,u,g);case d.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:case d.oids["pbewithSHAAnd40BitRC2-CBC"]:return d.pbe.getCipherForPKCS12PBE(E,u,g);default:var C=new Error("Cannot read encrypted PBE data block. Unsupported OID.");throw C.oid=E,C.supportedOids=["pkcs5PBES2","pbeWithSHAAnd3-KeyTripleDES-CBC","pbewithSHAAnd40BitRC2-CBC"],C}},d.pbe.getCipherForPBES2=function(E,u,g){var s,C={},m=[];if(!a.validate(u,h,C,m))throw(s=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.")).errors=m,s;if((E=a.derToOid(C.kdfOid))!==d.oids.pkcs5PBKDF2)throw(s=new Error("Cannot read encrypted private key. Unsupported key derivation function OID.")).oid=E,s.supportedOids=["pkcs5PBKDF2"],s;if((E=a.derToOid(C.encOid))!==d.oids["aes128-CBC"]&&E!==d.oids["aes192-CBC"]&&E!==d.oids["aes256-CBC"]&&E!==d.oids["des-EDE3-CBC"]&&E!==d.oids.desCBC)throw(s=new Error("Cannot read encrypted private key. Unsupported encryption scheme OID.")).oid=E,s.supportedOids=["aes128-CBC","aes192-CBC","aes256-CBC","des-EDE3-CBC","desCBC"],s;var I,_,f=C.kdfSalt,y=t.util.createBuffer(C.kdfIterationCount);switch(y=y.getInt(y.length()<<3),d.oids[E]){case"aes128-CBC":I=16,_=t.aes.createDecryptionCipher;break;case"aes192-CBC":I=24,_=t.aes.createDecryptionCipher;break;case"aes256-CBC":I=32,_=t.aes.createDecryptionCipher;break;case"des-EDE3-CBC":I=24,_=t.des.createDecryptionCipher;break;case"desCBC":I=8,_=t.des.createDecryptionCipher}var p=l(C.prfOid),i=t.pkcs5.pbkdf2(g,f,y,I,p),n=C.encIv,B=_(i);return B.start(n),B},d.pbe.getCipherForPKCS12PBE=function(E,u,g){var C={},m=[];if(!a.validate(u,r,C,m))throw(s=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.")).errors=m,s;var I,_,p,f=t.util.createBuffer(C.salt),y=t.util.createBuffer(C.iterations);switch(y=y.getInt(y.length()<<3),E){case d.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:I=24,_=8,p=t.des.startDecrypting;break;case d.oids["pbewithSHAAnd40BitRC2-CBC"]:I=5,_=8,p=function(P,L){var x=t.rc2.createDecryptionCipher(P,40);return x.start(L,null),x};break;default:var s;throw(s=new Error("Cannot read PKCS #12 PBE data block. Unsupported OID.")).oid=E,s}var i=l(C.prfOid),n=d.pbe.generatePkcs12Key(g,f,1,y,I,i);return i.start(),p(n,d.pbe.generatePkcs12Key(g,f,2,y,_,i))},d.pbe.opensslDeriveBytes=function(E,u,g,C){if(typeof C>"u"||null===C){if(!("md5"in t.md))throw new Error('"md5" hash algorithm unavailable.');C=t.md.md5.create()}null===u&&(u="");for(var m=[v(C,E+u)],s=16,f=1;s<g;++f,s+=16)m.push(v(C,m[f-1]+E+u));return m.join("").substr(0,g)},or}var sa,ur={exports:{}},cr={exports:{}};function oa(){if(sa)return cr.exports;sa=1;var t=ue();rt(),fe();var e=t.asn1,a=cr.exports=t.pkcs7asn1=t.pkcs7asn1||{};t.pkcs7=t.pkcs7||{},t.pkcs7.asn1=a;var d={name:"ContentInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.ContentType",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,captureAsn1:"content"}]};a.contentInfoValidator=d;var R={name:"EncryptedContentInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentType",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"contentType"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentEncryptionAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm.parameter",tagClass:e.Class.UNIVERSAL,captureAsn1:"encParameter"}]},{name:"EncryptedContentInfo.encryptedContent",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,capture:"encryptedContent",captureAsn1:"encryptedContentAsn1"}]};return a.envelopedDataValidator={name:"EnvelopedData",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"EnvelopedData.Version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"version"},{name:"EnvelopedData.RecipientInfos",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,constructed:!0,captureAsn1:"recipientInfos"}].concat(R)},a.encryptedDataValidator={name:"EncryptedData",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedData.Version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"version"}].concat(R)},a.signedDataValidator={name:"SignedData",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"SignedData.Version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"version"},{name:"SignedData.DigestAlgorithms",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,constructed:!0,captureAsn1:"digestAlgorithms"},d,{name:"SignedData.Certificates",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,optional:!0,captureAsn1:"certificates"},{name:"SignedData.CertificateRevocationLists",tagClass:e.Class.CONTEXT_SPECIFIC,type:1,optional:!0,captureAsn1:"crls"},{name:"SignedData.SignerInfos",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,capture:"signerInfos",optional:!0,value:[{name:"SignerInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1},{name:"SignerInfo.issuerAndSerialNumber",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.issuerAndSerialNumber.issuer",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"SignerInfo.issuerAndSerialNumber.serialNumber",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"SignerInfo.digestAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.digestAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"digestAlgorithm"},{name:"SignerInfo.digestAlgorithm.parameter",tagClass:e.Class.UNIVERSAL,constructed:!1,captureAsn1:"digestParameter",optional:!0}]},{name:"SignerInfo.authenticatedAttributes",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"authenticatedAttributes"},{name:"SignerInfo.digestEncryptionAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,capture:"signatureAlgorithm"},{name:"SignerInfo.encryptedDigest",tagClass:e.Class.UNIVERSAL,type:e.Type.OCTETSTRING,constructed:!1,capture:"signature"},{name:"SignerInfo.unauthenticatedAttributes",tagClass:e.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,capture:"unauthenticatedAttributes"}]}]}]},a.recipientInfoValidator={name:"RecipientInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"version"},{name:"RecipientInfo.issuerAndSerial",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.issuerAndSerial.issuer",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"RecipientInfo.issuerAndSerial.serialNumber",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"RecipientInfo.keyEncryptionAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.keyEncryptionAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"RecipientInfo.keyEncryptionAlgorithm.parameter",tagClass:e.Class.UNIVERSAL,constructed:!1,captureAsn1:"encParameter",optional:!0}]},{name:"RecipientInfo.encryptedKey",tagClass:e.Class.UNIVERSAL,type:e.Type.OCTETSTRING,constructed:!1,capture:"encKey"}]},cr.exports}var ua,hr,la,lr={exports:{}},pr={exports:{}};function ca(){if(ua)return pr.exports;ua=1;var t=ue();return fe(),t.mgf=t.mgf||{},(pr.exports=t.mgf.mgf1=t.mgf1=t.mgf1||{}).create=function(a){return{generate:function(R,T){for(var h=new t.util.ByteBuffer,r=Math.ceil(T/a.digestLength),v=0;v<r;v++){var l=new t.util.ByteBuffer;l.putInt32(v),a.start(),a.update(R+l.getBytes()),h.putBuffer(a.digest())}return h.truncate(h.length()-T),h.getBytes()}}},pr.exports}var pa,ha,fa,ya,gr,va,Ca,fr={exports:{}};function dr(){if(pa)return fr.exports;pa=1;var t=ue();return Ze(),fe(),(fr.exports=t.pss=t.pss||{}).create=function(a){3===arguments.length&&(a={md:arguments[0],mgf:arguments[1],saltLength:arguments[2]});var r,d=a.md,R=a.mgf,T=d.digestLength,h=a.salt||null;if("string"==typeof h&&(h=t.util.createBuffer(h)),"saltLength"in a)r=a.saltLength;else{if(null===h)throw new Error("Salt length not specified or specific salt not given.");r=h.length()}if(null!==h&&h.length()!==r)throw new Error("Given salt length does not match length of given salt.");var v=a.prng||t.random;return{encode:function(S,D){var E,m,u=D-1,g=Math.ceil(u/8),C=S.digest().getBytes();if(g<T+r+2)throw new Error("Message is too long to encrypt.");m=null===h?v.getBytesSync(r):h.bytes();var s=new t.util.ByteBuffer;s.fillWithByte(0,8),s.putBytes(C),s.putBytes(m),d.start(),d.update(s.getBytes());var f=d.digest().getBytes(),y=new t.util.ByteBuffer;y.fillWithByte(0,g-r-T-2),y.putByte(1),y.putBytes(m);var I=y.getBytes(),_=g-T-1,p=R.generate(f,_),i="";for(E=0;E<_;E++)i+=String.fromCharCode(I.charCodeAt(E)^p.charCodeAt(E));var n=65280>>8*g-u&255;return(i=String.fromCharCode(i.charCodeAt(0)&~n)+i.substr(1))+f+"\xbc"},verify:function(S,D,E){var u,g=E-1,C=Math.ceil(g/8);if(D=D.substr(-C),C<T+r+2)throw new Error("Inconsistent parameters to PSS signature verification.");if(188!==D.charCodeAt(C-1))throw new Error("Encoded message does not end in 0xBC.");var m=C-T-1,s=D.substr(0,m),f=D.substr(m,T),y=65280>>8*C-g&255;if(s.charCodeAt(0)&y)throw new Error("Bits beyond keysize not zero as expected.");var I=R.generate(f,m),_="";for(u=0;u<m;u++)_+=String.fromCharCode(s.charCodeAt(u)^I.charCodeAt(u));_=String.fromCharCode(_.charCodeAt(0)&~y)+_.substr(1);var p=C-T-r-2;for(u=0;u<p;u++)if(0!==_.charCodeAt(u))throw new Error("Leftmost octets not zero as expected");if(1!==_.charCodeAt(p))throw new Error("Inconsistent PSS signature, 0x01 marker not found");var i=_.substr(-r),n=new t.util.ByteBuffer;return n.fillWithByte(0,8),n.putBytes(S),n.putBytes(i),d.start(),d.update(n.getBytes()),f===d.digest().getBytes()}}},fr.exports}function yr(){if(ha)return lr.exports;ha=1;var t=ue();lt(),rt(),At(),nt(),function tn(){if(la)return hr;la=1;var t=ue();ca(),hr=t.mgf=t.mgf||{},t.mgf.mgf1=t.mgf1}(),pt(),yt(),dr(),wt(),fe();var e=t.asn1,a=lr.exports=t.pki=t.pki||{},d=a.oids,R={};R.CN=d.commonName,R.commonName="CN",R.C=d.countryName,R.countryName="C",R.L=d.localityName,R.localityName="L",R.ST=d.stateOrProvinceName,R.stateOrProvinceName="ST",R.O=d.organizationName,R.organizationName="O",R.OU=d.organizationalUnitName,R.organizationalUnitName="OU",R.E=d.emailAddress,R.emailAddress="E";var T=t.pki.rsa.publicKeyValidator,h={name:"Certificate",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.signature.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"certinfoSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:e.Class.UNIVERSAL,optional:!0,captureAsn1:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"certIssuer"},{name:"Certificate.TBSCertificate.validity",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.validity.notBefore (utc)",tagClass:e.Class.UNIVERSAL,type:e.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity1UTCTime"},{name:"Certificate.TBSCertificate.validity.notBefore (generalized)",tagClass:e.Class.UNIVERSAL,type:e.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity2GeneralizedTime"},{name:"Certificate.TBSCertificate.validity.notAfter (utc)",tagClass:e.Class.UNIVERSAL,type:e.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity3UTCTime"},{name:"Certificate.TBSCertificate.validity.notAfter (generalized)",tagClass:e.Class.UNIVERSAL,type:e.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity4GeneralizedTime"}]},{name:"Certificate.TBSCertificate.subject",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"certSubject"},T,{name:"Certificate.TBSCertificate.issuerUniqueID",tagClass:e.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",tagClass:e.Class.UNIVERSAL,type:e.Type.BITSTRING,constructed:!1,captureBitStringValue:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",tagClass:e.Class.CONTEXT_SPECIFIC,type:2,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",tagClass:e.Class.UNIVERSAL,type:e.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",tagClass:e.Class.CONTEXT_SPECIFIC,type:3,constructed:!0,captureAsn1:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.signatureAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"certSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:e.Class.UNIVERSAL,optional:!0,captureAsn1:"certSignatureParams"}]},{name:"Certificate.signatureValue",tagClass:e.Class.UNIVERSAL,type:e.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSignature"}]},r={name:"rsapss",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.hashAlgorithm",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier",tagClass:e.Class.UNIVERSAL,type:e.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"hashOid"}]}]},{name:"rsapss.maskGenAlgorithm",tagClass:e.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier",tagClass:e.Class.UNIVERSAL,type:e.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"maskGenOid"},{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"maskGenHashOid"}]}]}]},{name:"rsapss.saltLength",tagClass:e.Class.CONTEXT_SPECIFIC,type:2,optional:!0,value:[{name:"rsapss.saltLength.saltLength",tagClass:e.Class.UNIVERSAL,type:e.Class.INTEGER,constructed:!1,capture:"saltLength"}]},{name:"rsapss.trailerField",tagClass:e.Class.CONTEXT_SPECIFIC,type:3,optional:!0,value:[{name:"rsapss.trailer.trailer",tagClass:e.Class.UNIVERSAL,type:e.Class.INTEGER,constructed:!1,capture:"trailer"}]}]},l={name:"CertificationRequest",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"csr",value:[{name:"CertificationRequestInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfo",value:[{name:"CertificationRequestInfo.integer",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"certificationRequestInfoVersion"},{name:"CertificationRequestInfo.subject",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfoSubject"},T,{name:"CertificationRequestInfo.attributes",tagClass:e.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"certificationRequestInfoAttributes",value:[{name:"CertificationRequestInfo.attributes",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequestInfo.attributes.type",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1},{name:"CertificationRequestInfo.attributes.value",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,constructed:!0}]}]}]},{name:"CertificationRequest.signatureAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequest.signatureAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"csrSignatureOid"},{name:"CertificationRequest.signatureAlgorithm.parameters",tagClass:e.Class.UNIVERSAL,optional:!0,captureAsn1:"csrSignatureParams"}]},{name:"CertificationRequest.signature",tagClass:e.Class.UNIVERSAL,type:e.Type.BITSTRING,constructed:!1,captureBitStringValue:"csrSignature"}]};function S(p,i){"string"==typeof i&&(i={shortName:i});for(var B,n=null,U=0;null===n&&U<p.attributes.length;++U)B=p.attributes[U],(i.type&&i.type===B.type||i.name&&i.name===B.name||i.shortName&&i.shortName===B.shortName)&&(n=B);return n}a.RDNAttributesAsArray=function(p,i){for(var B,U,P,n=[],L=0;L<p.value.length;++L){B=p.value[L];for(var x=0;x<B.value.length;++x)(P={}).type=e.derToOid((U=B.value[x]).value[0].value),P.value=U.value[1].value,P.valueTagClass=U.value[1].type,P.type in d&&(P.name=d[P.type],P.name in R&&(P.shortName=R[P.name])),i&&(i.update(P.type),i.update(P.value)),n.push(P)}return n},a.CRIAttributesAsArray=function(p){for(var i=[],n=0;n<p.length;++n)for(var B=p[n],U=e.derToOid(B.value[0].value),P=B.value[1].value,L=0;L<P.length;++L){var x={};if(x.type=U,x.value=P[L].value,x.valueTagClass=P[L].type,x.type in d&&(x.name=d[x.type],x.name in R&&(x.shortName=R[x.name])),x.type===d.extensionRequest){x.extensions=[];for(var H=0;H<x.value.length;++H)x.extensions.push(a.certificateExtensionFromAsn1(x.value[H]))}i.push(x)}return i};var D=function(p,i,n){var B={};if(p!==d["RSASSA-PSS"])return B;n&&(B={hash:{algorithmOid:d.sha1},mgf:{algorithmOid:d.mgf1,hash:{algorithmOid:d.sha1}},saltLength:20});var U={},P=[];if(!e.validate(i,r,U,P)){var L=new Error("Cannot read RSASSA-PSS parameter block.");throw L.errors=P,L}return void 0!==U.hashOid&&(B.hash=B.hash||{},B.hash.algorithmOid=e.derToOid(U.hashOid)),void 0!==U.maskGenOid&&(B.mgf=B.mgf||{},B.mgf.algorithmOid=e.derToOid(U.maskGenOid),B.mgf.hash=B.mgf.hash||{},B.mgf.hash.algorithmOid=e.derToOid(U.maskGenHashOid)),void 0!==U.saltLength&&(B.saltLength=U.saltLength.charCodeAt(0)),B},E=function(p){switch(d[p.signatureOid]){case"sha1WithRSAEncryption":case"sha1WithRSASignature":return t.md.sha1.create();case"md5WithRSAEncryption":return t.md.md5.create();case"sha256WithRSAEncryption":case"RSASSA-PSS":return t.md.sha256.create();case"sha384WithRSAEncryption":return t.md.sha384.create();case"sha512WithRSAEncryption":return t.md.sha512.create();default:var i=new Error("Could not compute "+p.type+" digest. Unknown signature OID.");throw i.signatureOid=p.signatureOid,i}},u=function(p){var n,i=p.certificate;switch(i.signatureOid){case d.sha1WithRSAEncryption:case d.sha1WithRSASignature:break;case d["RSASSA-PSS"]:var B,U,P;if(void 0===(B=d[i.signatureParameters.mgf.hash.algorithmOid])||void 0===t.md[B])throw(P=new Error("Unsupported MGF hash function.")).oid=i.signatureParameters.mgf.hash.algorithmOid,P.name=B,P;if(void 0===(U=d[i.signatureParameters.mgf.algorithmOid])||void 0===t.mgf[U])throw(P=new Error("Unsupported MGF function.")).oid=i.signatureParameters.mgf.algorithmOid,P.name=U,P;if(U=t.mgf[U].create(t.md[B].create()),void 0===(B=d[i.signatureParameters.hash.algorithmOid])||void 0===t.md[B])throw(P=new Error("Unsupported RSASSA-PSS hash function.")).oid=i.signatureParameters.hash.algorithmOid,P.name=B,P;n=t.pss.create(t.md[B].create(),U,i.signatureParameters.saltLength)}return i.publicKey.verify(p.md.digest().getBytes(),p.signature,n)};function g(p){for(var n,B,i=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]),U=p.attributes,P=0;P<U.length;++P){var L=(n=U[P]).value,x=e.Type.PRINTABLESTRING;"valueTagClass"in n&&(x=n.valueTagClass)===e.Type.UTF8&&(L=t.util.encodeUtf8(L)),B=e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(n.type).getBytes()),e.create(e.Class.UNIVERSAL,x,!1,L)])]),i.value.push(B)}return i}function C(p){for(var i,n=0;n<p.length;++n){if(typeof(i=p[n]).name>"u"&&(i.type&&i.type in a.oids?i.name=a.oids[i.type]:i.shortName&&i.shortName in R&&(i.name=a.oids[R[i.shortName]])),typeof i.type>"u"){if(!i.name||!(i.name in a.oids))throw(B=new Error("Attribute type not specified.")).attribute=i,B;i.type=a.oids[i.name]}if(typeof i.shortName>"u"&&i.name&&i.name in R&&(i.shortName=R[i.name]),i.type===d.extensionRequest&&(i.valueConstructed=!0,i.valueTagClass=e.Type.SEQUENCE,!i.value&&i.extensions)){i.value=[];for(var U=0;U<i.extensions.length;++U)i.value.push(a.certificateExtensionToAsn1(m(i.extensions[U])))}var B;if(typeof i.value>"u")throw(B=new Error("Attribute value not specified.")).attribute=i,B}}function m(p,i){if(i=i||{},typeof p.name>"u"&&p.id&&p.id in a.oids&&(p.name=a.oids[p.id]),typeof p.id>"u"){if(!p.name||!(p.name in a.oids))throw(n=new Error("Extension ID not specified.")).extension=p,n;p.id=a.oids[p.name]}if(typeof p.value<"u")return p;if("keyUsage"===p.name){var B=0,U=0,P=0;p.digitalSignature&&(U|=128,B=7),p.nonRepudiation&&(U|=64,B=6),p.keyEncipherment&&(U|=32,B=5),p.dataEncipherment&&(U|=16,B=4),p.keyAgreement&&(U|=8,B=3),p.keyCertSign&&(U|=4,B=2),p.cRLSign&&(U|=2,B=1),p.encipherOnly&&(U|=1,B=0),p.decipherOnly&&(P|=128,B=7);var L=String.fromCharCode(B);0!==P?L+=String.fromCharCode(U)+String.fromCharCode(P):0!==U&&(L+=String.fromCharCode(U)),p.value=e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,L)}else if("basicConstraints"===p.name)p.value=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]),p.cA&&p.value.value.push(e.create(e.Class.UNIVERSAL,e.Type.BOOLEAN,!1,"\xff")),"pathLenConstraint"in p&&p.value.value.push(e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(p.pathLenConstraint).getBytes()));else if("extKeyUsage"===p.name){p.value=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]);var x=p.value.value;for(var H in p)!0===p[H]&&(H in d?x.push(e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(d[H]).getBytes())):-1!==H.indexOf(".")&&x.push(e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(H).getBytes())))}else if("nsCertType"===p.name)B=0,U=0,p.client&&(U|=128,B=7),p.server&&(U|=64,B=6),p.email&&(U|=32,B=5),p.objsign&&(U|=16,B=4),p.reserved&&(U|=8,B=3),p.sslCA&&(U|=4,B=2),p.emailCA&&(U|=2,B=1),p.objCA&&(U|=1,B=0),L=String.fromCharCode(B),0!==U&&(L+=String.fromCharCode(U)),p.value=e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,L);else if("subjectAltName"===p.name||"issuerAltName"===p.name){p.value=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]);for(var j=0;j<p.altNames.length;++j){if(L=(F=p.altNames[j]).value,7===F.type&&F.ip){if(null===(L=t.util.bytesFromIP(F.ip)))throw(n=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.')).extension=p,n}else 8===F.type&&(L=e.oidToDer(F.oid?e.oidToDer(F.oid):L));p.value.value.push(e.create(e.Class.CONTEXT_SPECIFIC,F.type,!1,L))}}else if("nsComment"===p.name&&i.cert){if(!/^[\x00-\x7F]*$/.test(p.comment)||p.comment.length<1||p.comment.length>128)throw new Error('Invalid "nsComment" content.');p.value=e.create(e.Class.UNIVERSAL,e.Type.IA5STRING,!1,p.comment)}else if("subjectKeyIdentifier"===p.name&&i.cert){var z=i.cert.generateSubjectKeyIdentifier();p.subjectKeyIdentifier=z.toHex(),p.value=e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,z.getBytes())}else if("authorityKeyIdentifier"===p.name&&i.cert){if(p.value=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]),x=p.value.value,p.keyIdentifier){var X=!0===p.keyIdentifier?i.cert.generateSubjectKeyIdentifier().getBytes():p.keyIdentifier;x.push(e.create(e.Class.CONTEXT_SPECIFIC,0,!1,X))}if(p.authorityCertIssuer){var $=[e.create(e.Class.CONTEXT_SPECIFIC,4,!0,[g(!0===p.authorityCertIssuer?i.cert.issuer:p.authorityCertIssuer)])];x.push(e.create(e.Class.CONTEXT_SPECIFIC,1,!0,$))}if(p.serialNumber){var ne=t.util.hexToBytes(!0===p.serialNumber?i.cert.serialNumber:p.serialNumber);x.push(e.create(e.Class.CONTEXT_SPECIFIC,2,!1,ne))}}else if("cRLDistributionPoints"===p.name){p.value=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]),x=p.value.value;var F,se=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]),he=e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[]);for(j=0;j<p.altNames.length;++j){if(L=(F=p.altNames[j]).value,7===F.type&&F.ip){if(null===(L=t.util.bytesFromIP(F.ip)))throw(n=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.')).extension=p,n}else 8===F.type&&(L=e.oidToDer(F.oid?e.oidToDer(F.oid):L));he.value.push(e.create(e.Class.CONTEXT_SPECIFIC,F.type,!1,L))}se.value.push(e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[he])),x.push(se)}var n;if(typeof p.value>"u")throw(n=new Error("Extension value not specified.")).extension=p,n;return p}function s(p,i){if(p===d["RSASSA-PSS"]){var n=[];return void 0!==i.hash.algorithmOid&&n.push(e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(i.hash.algorithmOid).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")])])),void 0!==i.mgf.algorithmOid&&n.push(e.create(e.Class.CONTEXT_SPECIFIC,1,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(i.mgf.algorithmOid).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(i.mgf.hash.algorithmOid).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")])])])),void 0!==i.saltLength&&n.push(e.create(e.Class.CONTEXT_SPECIFIC,2,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(i.saltLength).getBytes())])),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,n)}return e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")}function f(p){var i=e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[]);if(0===p.attributes.length)return i;for(var n=p.attributes,B=0;B<n.length;++B){var U=n[B],P=U.value,L=e.Type.UTF8;"valueTagClass"in U&&(L=U.valueTagClass),L===e.Type.UTF8&&(P=t.util.encodeUtf8(P));var x=!1;"valueConstructed"in U&&(x=U.valueConstructed);var H=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(U.type).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[e.create(e.Class.UNIVERSAL,L,x,P)])]);i.value.push(H)}return i}a.certificateFromPem=function(p,i,n){var B=t.pem.decode(p)[0];if("CERTIFICATE"!==B.type&&"X509 CERTIFICATE"!==B.type&&"TRUSTED CERTIFICATE"!==B.type){var U=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw U.headerType=B.type,U}if(B.procType&&"ENCRYPTED"===B.procType.type)throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var P=e.fromDer(B.body,n);return a.certificateFromAsn1(P,i)},a.certificateToPem=function(p,i){var n={type:"CERTIFICATE",body:e.toDer(a.certificateToAsn1(p)).getBytes()};return t.pem.encode(n,{maxline:i})},a.publicKeyFromPem=function(p){var i=t.pem.decode(p)[0];if("PUBLIC KEY"!==i.type&&"RSA PUBLIC KEY"!==i.type){var n=new Error('Could not convert public key from PEM; PEM header type is not "PUBLIC KEY" or "RSA PUBLIC KEY".');throw n.headerType=i.type,n}if(i.procType&&"ENCRYPTED"===i.procType.type)throw new Error("Could not convert public key from PEM; PEM is encrypted.");var B=e.fromDer(i.body);return a.publicKeyFromAsn1(B)},a.publicKeyToPem=function(p,i){var n={type:"PUBLIC KEY",body:e.toDer(a.publicKeyToAsn1(p)).getBytes()};return t.pem.encode(n,{maxline:i})},a.publicKeyToRSAPublicKeyPem=function(p,i){var n={type:"RSA PUBLIC KEY",body:e.toDer(a.publicKeyToRSAPublicKey(p)).getBytes()};return t.pem.encode(n,{maxline:i})},a.getPublicKeyFingerprint=function(p,i){var U,n=(i=i||{}).md||t.md.sha1.create();switch(i.type||"RSAPublicKey"){case"RSAPublicKey":U=e.toDer(a.publicKeyToRSAPublicKey(p)).getBytes();break;case"SubjectPublicKeyInfo":U=e.toDer(a.publicKeyToAsn1(p)).getBytes();break;default:throw new Error('Unknown fingerprint type "'+i.type+'".')}n.start(),n.update(U);var P=n.digest();if("hex"===i.encoding){var L=P.toHex();return i.delimiter?L.match(/.{2}/g).join(i.delimiter):L}if("binary"===i.encoding)return P.getBytes();if(i.encoding)throw new Error('Unknown encoding "'+i.encoding+'".');return P},a.certificationRequestFromPem=function(p,i,n){var B=t.pem.decode(p)[0];if("CERTIFICATE REQUEST"!==B.type){var U=new Error('Could not convert certification request from PEM; PEM header type is not "CERTIFICATE REQUEST".');throw U.headerType=B.type,U}if(B.procType&&"ENCRYPTED"===B.procType.type)throw new Error("Could not convert certification request from PEM; PEM is encrypted.");var P=e.fromDer(B.body,n);return a.certificationRequestFromAsn1(P,i)},a.certificationRequestToPem=function(p,i){var n={type:"CERTIFICATE REQUEST",body:e.toDer(a.certificationRequestToAsn1(p)).getBytes()};return t.pem.encode(n,{maxline:i})},a.createCertificate=function(){var p={version:2,serialNumber:"00",signatureOid:null,signature:null,siginfo:{}};return p.siginfo.algorithmOid=null,p.validity={},p.validity.notBefore=new Date,p.validity.notAfter=new Date,p.issuer={},p.issuer.getField=function(i){return S(p.issuer,i)},p.issuer.addField=function(i){C([i]),p.issuer.attributes.push(i)},p.issuer.attributes=[],p.issuer.hash=null,p.subject={},p.subject.getField=function(i){return S(p.subject,i)},p.subject.addField=function(i){C([i]),p.subject.attributes.push(i)},p.subject.attributes=[],p.subject.hash=null,p.extensions=[],p.publicKey=null,p.md=null,p.setSubject=function(i,n){C(i),p.subject.attributes=i,delete p.subject.uniqueId,n&&(p.subject.uniqueId=n),p.subject.hash=null},p.setIssuer=function(i,n){C(i),p.issuer.attributes=i,delete p.issuer.uniqueId,n&&(p.issuer.uniqueId=n),p.issuer.hash=null},p.setExtensions=function(i){for(var n=0;n<i.length;++n)m(i[n],{cert:p});p.extensions=i},p.getExtension=function(i){"string"==typeof i&&(i={name:i});for(var B,n=null,U=0;null===n&&U<p.extensions.length;++U)B=p.extensions[U],(i.id&&B.id===i.id||i.name&&B.name===i.name)&&(n=B);return n},p.sign=function(i,n){p.md=n||t.md.sha1.create();var B=d[p.md.algorithm+"WithRSAEncryption"];if(!B){var U=new Error("Could not compute certificate digest. Unknown message digest algorithm OID.");throw U.algorithm=p.md.algorithm,U}p.signatureOid=p.siginfo.algorithmOid=B,p.tbsCertificate=a.getTBSCertificate(p);var P=e.toDer(p.tbsCertificate);p.md.update(P.getBytes()),p.signature=i.sign(p.md)},p.verify=function(i){var n=!1;if(!p.issued(i)){var B=i.issuer,U=p.subject,P=new Error("The parent certificate did not issue the given child certificate; the child certificate's issuer does not match the parent's subject.");throw P.expectedIssuer=U.attributes,P.actualIssuer=B.attributes,P}var L=i.md;if(null===L){L=E({signatureOid:i.signatureOid,type:"certificate"});var x=i.tbsCertificate||a.getTBSCertificate(i),H=e.toDer(x);L.update(H.getBytes())}return null!==L&&(n=u({certificate:p,md:L,signature:i.signature})),n},p.isIssuer=function(i){var n=!1,B=p.issuer,U=i.subject;if(B.hash&&U.hash)n=B.hash===U.hash;else if(B.attributes.length===U.attributes.length){n=!0;for(var P,L,x=0;n&&x<B.attributes.length;++x)((P=B.attributes[x]).type!==(L=U.attributes[x]).type||P.value!==L.value)&&(n=!1)}return n},p.issued=function(i){return i.isIssuer(p)},p.generateSubjectKeyIdentifier=function(){return a.getPublicKeyFingerprint(p.publicKey,{type:"RSAPublicKey"})},p.verifySubjectKeyIdentifier=function(){for(var i=d.subjectKeyIdentifier,n=0;n<p.extensions.length;++n){var B=p.extensions[n];if(B.id===i){var U=p.generateSubjectKeyIdentifier().getBytes();return t.util.hexToBytes(B.subjectKeyIdentifier)===U}}return!1},p},a.certificateFromAsn1=function(p,i){var n={},B=[];if(!e.validate(p,h,n,B)){var U=new Error("Cannot read X.509 certificate. ASN.1 object is not an X509v3 Certificate.");throw U.errors=B,U}if(e.derToOid(n.publicKeyOid)!==a.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var L=a.createCertificate();L.version=n.certVersion?n.certVersion.charCodeAt(0):0;var x=t.util.createBuffer(n.certSerialNumber);L.serialNumber=x.toHex(),L.signatureOid=t.asn1.derToOid(n.certSignatureOid),L.signatureParameters=D(L.signatureOid,n.certSignatureParams,!0),L.siginfo.algorithmOid=t.asn1.derToOid(n.certinfoSignatureOid),L.siginfo.parameters=D(L.siginfo.algorithmOid,n.certinfoSignatureParams,!1),L.signature=n.certSignature;var H=[];if(void 0!==n.certValidity1UTCTime&&H.push(e.utcTimeToDate(n.certValidity1UTCTime)),void 0!==n.certValidity2GeneralizedTime&&H.push(e.generalizedTimeToDate(n.certValidity2GeneralizedTime)),void 0!==n.certValidity3UTCTime&&H.push(e.utcTimeToDate(n.certValidity3UTCTime)),void 0!==n.certValidity4GeneralizedTime&&H.push(e.generalizedTimeToDate(n.certValidity4GeneralizedTime)),H.length>2)throw new Error("Cannot read notBefore/notAfter validity times; more than two times were provided in the certificate.");if(H.length<2)throw new Error("Cannot read notBefore/notAfter validity times; they were not provided as either UTCTime or GeneralizedTime.");if(L.validity.notBefore=H[0],L.validity.notAfter=H[1],L.tbsCertificate=n.tbsCertificate,i){L.md=E({signatureOid:L.signatureOid,type:"certificate"});var F=e.toDer(L.tbsCertificate);L.md.update(F.getBytes())}var j=t.md.sha1.create(),z=e.toDer(n.certIssuer);j.update(z.getBytes()),L.issuer.getField=function(ne){return S(L.issuer,ne)},L.issuer.addField=function(ne){C([ne]),L.issuer.attributes.push(ne)},L.issuer.attributes=a.RDNAttributesAsArray(n.certIssuer),n.certIssuerUniqueId&&(L.issuer.uniqueId=n.certIssuerUniqueId),L.issuer.hash=j.digest().toHex();var X=t.md.sha1.create(),$=e.toDer(n.certSubject);return X.update($.getBytes()),L.subject.getField=function(ne){return S(L.subject,ne)},L.subject.addField=function(ne){C([ne]),L.subject.attributes.push(ne)},L.subject.attributes=a.RDNAttributesAsArray(n.certSubject),n.certSubjectUniqueId&&(L.subject.uniqueId=n.certSubjectUniqueId),L.subject.hash=X.digest().toHex(),L.extensions=n.certExtensions?a.certificateExtensionsFromAsn1(n.certExtensions):[],L.publicKey=a.publicKeyFromAsn1(n.subjectPublicKeyInfo),L},a.certificateExtensionsFromAsn1=function(p){for(var i=[],n=0;n<p.value.length;++n)for(var B=p.value[n],U=0;U<B.value.length;++U)i.push(a.certificateExtensionFromAsn1(B.value[U]));return i},a.certificateExtensionFromAsn1=function(p){var i={};if(i.id=e.derToOid(p.value[0].value),i.critical=!1,p.value[1].type===e.Type.BOOLEAN?(i.critical=0!==p.value[1].value.charCodeAt(0),i.value=p.value[2].value):i.value=p.value[1].value,i.id in d)if(i.name=d[i.id],"keyUsage"===i.name){var B=0,U=0;(n=e.fromDer(i.value)).value.length>1&&(B=n.value.charCodeAt(1),U=n.value.length>2?n.value.charCodeAt(2):0),i.digitalSignature=128==(128&B),i.nonRepudiation=64==(64&B),i.keyEncipherment=32==(32&B),i.dataEncipherment=16==(16&B),i.keyAgreement=8==(8&B),i.keyCertSign=4==(4&B),i.cRLSign=2==(2&B),i.encipherOnly=1==(1&B),i.decipherOnly=128==(128&U)}else if("basicConstraints"===i.name){var n=e.fromDer(i.value);i.cA=n.value.length>0&&n.value[0].type===e.Type.BOOLEAN&&0!==n.value[0].value.charCodeAt(0);var P=null;n.value.length>0&&n.value[0].type===e.Type.INTEGER?P=n.value[0].value:n.value.length>1&&(P=n.value[1].value),null!==P&&(i.pathLenConstraint=e.derToInteger(P))}else if("extKeyUsage"===i.name){n=e.fromDer(i.value);for(var L=0;L<n.value.length;++L){var x=e.derToOid(n.value[L].value);x in d?i[d[x]]=!0:i[x]=!0}}else if("nsCertType"===i.name)B=0,(n=e.fromDer(i.value)).value.length>1&&(B=n.value.charCodeAt(1)),i.client=128==(128&B),i.server=64==(64&B),i.email=32==(32&B),i.objsign=16==(16&B),i.reserved=8==(8&B),i.sslCA=4==(4&B),i.emailCA=2==(2&B),i.objCA=1==(1&B);else if("subjectAltName"===i.name||"issuerAltName"===i.name){i.altNames=[],n=e.fromDer(i.value);for(var H,F=0;F<n.value.length;++F){var j={type:(H=n.value[F]).type,value:H.value};switch(i.altNames.push(j),H.type){case 1:case 2:case 6:break;case 7:j.ip=t.util.bytesToIP(H.value);break;case 8:j.oid=e.derToOid(H.value)}}}else"subjectKeyIdentifier"===i.name&&(n=e.fromDer(i.value),i.subjectKeyIdentifier=t.util.bytesToHex(n.value));return i},a.certificationRequestFromAsn1=function(p,i){var n={},B=[];if(!e.validate(p,l,n,B)){var U=new Error("Cannot read PKCS#10 certificate request. ASN.1 object is not a PKCS#10 CertificationRequest.");throw U.errors=B,U}if(e.derToOid(n.publicKeyOid)!==a.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var L=a.createCertificationRequest();if(L.version=n.csrVersion?n.csrVersion.charCodeAt(0):0,L.signatureOid=t.asn1.derToOid(n.csrSignatureOid),L.signatureParameters=D(L.signatureOid,n.csrSignatureParams,!0),L.siginfo.algorithmOid=t.asn1.derToOid(n.csrSignatureOid),L.siginfo.parameters=D(L.siginfo.algorithmOid,n.csrSignatureParams,!1),L.signature=n.csrSignature,L.certificationRequestInfo=n.certificationRequestInfo,i){L.md=E({signatureOid:L.signatureOid,type:"certification request"});var x=e.toDer(L.certificationRequestInfo);L.md.update(x.getBytes())}var H=t.md.sha1.create();return L.subject.getField=function(F){return S(L.subject,F)},L.subject.addField=function(F){C([F]),L.subject.attributes.push(F)},L.subject.attributes=a.RDNAttributesAsArray(n.certificationRequestInfoSubject,H),L.subject.hash=H.digest().toHex(),L.publicKey=a.publicKeyFromAsn1(n.subjectPublicKeyInfo),L.getAttribute=function(F){return S(L,F)},L.addAttribute=function(F){C([F]),L.attributes.push(F)},L.attributes=a.CRIAttributesAsArray(n.certificationRequestInfoAttributes||[]),L},a.createCertificationRequest=function(){var p={version:0,signatureOid:null,signature:null,siginfo:{}};return p.siginfo.algorithmOid=null,p.subject={},p.subject.getField=function(i){return S(p.subject,i)},p.subject.addField=function(i){C([i]),p.subject.attributes.push(i)},p.subject.attributes=[],p.subject.hash=null,p.publicKey=null,p.attributes=[],p.getAttribute=function(i){return S(p,i)},p.addAttribute=function(i){C([i]),p.attributes.push(i)},p.md=null,p.setSubject=function(i){C(i),p.subject.attributes=i,p.subject.hash=null},p.setAttributes=function(i){C(i),p.attributes=i},p.sign=function(i,n){p.md=n||t.md.sha1.create();var B=d[p.md.algorithm+"WithRSAEncryption"];if(!B){var U=new Error("Could not compute certification request digest. Unknown message digest algorithm OID.");throw U.algorithm=p.md.algorithm,U}p.signatureOid=p.siginfo.algorithmOid=B,p.certificationRequestInfo=a.getCertificationRequestInfo(p);var P=e.toDer(p.certificationRequestInfo);p.md.update(P.getBytes()),p.signature=i.sign(p.md)},p.verify=function(){var i=!1,n=p.md;if(null===n){n=E({signatureOid:p.signatureOid,type:"certification request"});var B=p.certificationRequestInfo||a.getCertificationRequestInfo(p),U=e.toDer(B);n.update(U.getBytes())}return null!==n&&(i=u({certificate:p,md:n,signature:p.signature})),i},p};var y=new Date("1950-01-01T00:00:00Z"),I=new Date("2050-01-01T00:00:00Z");function _(p){return p>=y&&p<I?e.create(e.Class.UNIVERSAL,e.Type.UTCTIME,!1,e.dateToUtcTime(p)):e.create(e.Class.UNIVERSAL,e.Type.GENERALIZEDTIME,!1,e.dateToGeneralizedTime(p))}return a.getTBSCertificate=function(p){var i=_(p.validity.notBefore),n=_(p.validity.notAfter),B=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(p.version).getBytes())]),e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,t.util.hexToBytes(p.serialNumber)),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(p.siginfo.algorithmOid).getBytes()),s(p.siginfo.algorithmOid,p.siginfo.parameters)]),g(p.issuer),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[i,n]),g(p.subject),a.publicKeyToAsn1(p.publicKey)]);return p.issuer.uniqueId&&B.value.push(e.create(e.Class.CONTEXT_SPECIFIC,1,!0,[e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,"\0"+p.issuer.uniqueId)])),p.subject.uniqueId&&B.value.push(e.create(e.Class.CONTEXT_SPECIFIC,2,!0,[e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,"\0"+p.subject.uniqueId)])),p.extensions.length>0&&B.value.push(a.certificateExtensionsToAsn1(p.extensions)),B},a.getCertificationRequestInfo=function(p){return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(p.version).getBytes()),g(p.subject),a.publicKeyToAsn1(p.publicKey),f(p)])},a.distinguishedNameToAsn1=function(p){return g(p)},a.certificateToAsn1=function(p){var i=p.tbsCertificate||a.getTBSCertificate(p);return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[i,e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(p.signatureOid).getBytes()),s(p.signatureOid,p.signatureParameters)]),e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,"\0"+p.signature)])},a.certificateExtensionsToAsn1=function(p){var i=e.create(e.Class.CONTEXT_SPECIFIC,3,!0,[]),n=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]);i.value.push(n);for(var B=0;B<p.length;++B)n.value.push(a.certificateExtensionToAsn1(p[B]));return i},a.certificateExtensionToAsn1=function(p){var i=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[]);i.value.push(e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(p.id).getBytes())),p.critical&&i.value.push(e.create(e.Class.UNIVERSAL,e.Type.BOOLEAN,!1,"\xff"));var n=p.value;return"string"!=typeof p.value&&(n=e.toDer(n).getBytes()),i.value.push(e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,n)),i},a.certificationRequestToAsn1=function(p){var i=p.certificationRequestInfo||a.getCertificationRequestInfo(p);return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[i,e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(p.signatureOid).getBytes()),s(p.signatureOid,p.signatureParameters)]),e.create(e.Class.UNIVERSAL,e.Type.BITSTRING,!1,"\0"+p.signature)])},a.createCaStore=function(p){var i={certs:{}};function n(L){return B(L),i.certs[L.hash]||null}function B(L){if(!L.hash){var x=t.md.sha1.create();L.attributes=a.RDNAttributesAsArray(g(L),x),L.hash=x.digest().toHex()}}if(i.getIssuer=function(L){return n(L.issuer)},i.addCertificate=function(L){if("string"==typeof L&&(L=t.pki.certificateFromPem(L)),B(L.subject),!i.hasCertificate(L))if(L.subject.hash in i.certs){var x=i.certs[L.subject.hash];t.util.isArray(x)||(x=[x]),x.push(L),i.certs[L.subject.hash]=x}else i.certs[L.subject.hash]=L},i.hasCertificate=function(L){"string"==typeof L&&(L=t.pki.certificateFromPem(L));var x=n(L.subject);if(!x)return!1;t.util.isArray(x)||(x=[x]);for(var H=e.toDer(a.certificateToAsn1(L)).getBytes(),F=0;F<x.length;++F)if(H===e.toDer(a.certificateToAsn1(x[F])).getBytes())return!0;return!1},i.listAllCertificates=function(){var L=[];for(var x in i.certs)if(i.certs.hasOwnProperty(x)){var H=i.certs[x];if(t.util.isArray(H))for(var F=0;F<H.length;++F)L.push(H[F]);else L.push(H)}return L},i.removeCertificate=function(L){var x;if("string"==typeof L&&(L=t.pki.certificateFromPem(L)),B(L.subject),!i.hasCertificate(L))return null;var H=n(L.subject);if(!t.util.isArray(H))return x=i.certs[L.subject.hash],delete i.certs[L.subject.hash],x;for(var F=e.toDer(a.certificateToAsn1(L)).getBytes(),j=0;j<H.length;++j)F===e.toDer(a.certificateToAsn1(H[j])).getBytes()&&(x=H[j],H.splice(j,1));return 0===H.length&&delete i.certs[L.subject.hash],x},p)for(var U=0;U<p.length;++U)i.addCertificate(p[U]);return i},a.certificateError={bad_certificate:"forge.pki.BadCertificate",unsupported_certificate:"forge.pki.UnsupportedCertificate",certificate_revoked:"forge.pki.CertificateRevoked",certificate_expired:"forge.pki.CertificateExpired",certificate_unknown:"forge.pki.CertificateUnknown",unknown_ca:"forge.pki.UnknownCertificateAuthority"},a.verifyCertificateChain=function(p,i,n){"function"==typeof n&&(n={verify:n}),n=n||{};var B=(i=i.slice(0)).slice(0),U=n.validityCheckDate;typeof U>"u"&&(U=new Date);var P=!0,L=null,x=0;do{var H=i.shift(),F=null,j=!1;if(U&&(U<H.validity.notBefore||U>H.validity.notAfter)&&(L={message:"Certificate is not valid yet or has expired.",error:a.certificateError.certificate_expired,notBefore:H.validity.notBefore,notAfter:H.validity.notAfter,now:U}),null===L){if(null===(F=i[0]||p.getIssuer(H))&&H.isIssuer(H)&&(j=!0,F=H),F){var z=F;t.util.isArray(z)||(z=[z]);for(var X=!1;!X&&z.length>0;){F=z.shift();try{X=F.verify(H)}catch{}}X||(L={message:"Certificate signature is invalid.",error:a.certificateError.bad_certificate})}null===L&&(!F||j)&&!p.hasCertificate(H)&&(L={message:"Certificate is not trusted.",error:a.certificateError.unknown_ca})}if(null===L&&F&&!H.isIssuer(F)&&(L={message:"Certificate issuer is invalid.",error:a.certificateError.bad_certificate}),null===L)for(var $={keyUsage:!0,basicConstraints:!0},ne=0;null===L&&ne<H.extensions.length;++ne){var se=H.extensions[ne];se.critical&&!(se.name in $)&&(L={message:"Certificate has an unsupported critical extension.",error:a.certificateError.unsupported_certificate})}if(null===L&&(!P||0===i.length&&(!F||j))){var he=H.getExtension("basicConstraints"),ve=H.getExtension("keyUsage");null!==ve&&(!ve.keyCertSign||null===he)&&(L={message:"Certificate keyUsage or basicConstraints conflict or indicate that the certificate is not a CA. If the certificate is the only one in the chain or isn't the first then the certificate must be a valid CA.",error:a.certificateError.bad_certificate}),null===L&&null!==he&&!he.cA&&(L={message:"Certificate basicConstraints indicates the certificate is not a CA.",error:a.certificateError.bad_certificate}),null===L&&null!==ve&&"pathLenConstraint"in he&&x-1>he.pathLenConstraint&&(L={message:"Certificate basicConstraints pathLenConstraint violated.",error:a.certificateError.bad_certificate})}var ce=null===L||L.error,Z=n.verify?n.verify(ce,x,B):ce;if(!0!==Z)throw!0===ce&&(L={message:"The application rejected the certificate.",error:a.certificateError.bad_certificate}),(Z||0===Z)&&("object"!=typeof Z||t.util.isArray(Z)?"string"==typeof Z&&(L.error=Z):(Z.message&&(L.message=Z.message),Z.error&&(L.error=Z.error))),L;L=null,P=!1,++x}while(i.length>0);return!0},lr.exports}function da(){if(fa)return ur.exports;fa=1;var t=ue();rt(),Et(),pt(),oa(),ia(),Ze(),wt(),St(),fe(),yr();var e=t.asn1,a=t.pki,d=ur.exports=t.pkcs12=t.pkcs12||{},R={name:"ContentInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.contentType",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:e.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"content"}]},T={name:"PFX",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"version"},R,{name:"PFX.macData",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"mac",value:[{name:"PFX.macData.mac",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"macAlgorithm"},{name:"PFX.macData.mac.digestAlgorithm.parameters",tagClass:e.Class.UNIVERSAL,captureAsn1:"macAlgorithmParameters"}]},{name:"PFX.macData.mac.digest",tagClass:e.Class.UNIVERSAL,type:e.Type.OCTETSTRING,constructed:!1,capture:"macDigest"}]},{name:"PFX.macData.macSalt",tagClass:e.Class.UNIVERSAL,type:e.Type.OCTETSTRING,constructed:!1,capture:"macSalt"},{name:"PFX.macData.iterations",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,optional:!0,capture:"macIterations"}]}]},h={name:"SafeBag",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"SafeBag.bagId",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"bagId"},{name:"SafeBag.bagValue",tagClass:e.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"bagValue"},{name:"SafeBag.bagAttributes",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,constructed:!0,optional:!0,capture:"bagAttributes"}]},r={name:"Attribute",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"Attribute.attrId",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"oid"},{name:"Attribute.attrValues",tagClass:e.Class.UNIVERSAL,type:e.Type.SET,constructed:!0,capture:"values"}]},v={name:"CertBag",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"CertBag.certId",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"certId"},{name:"CertBag.certValue",tagClass:e.Class.CONTEXT_SPECIFIC,constructed:!0,value:[{name:"CertBag.certValue[0]",tagClass:e.Class.UNIVERSAL,type:e.Class.OCTETSTRING,constructed:!1,capture:"cert"}]}]};function l(C,m,s,f){for(var y=[],I=0;I<C.length;I++)for(var _=0;_<C[I].safeBags.length;_++){var p=C[I].safeBags[_];if(void 0===f||p.type===f){if(null===m){y.push(p);continue}void 0!==p.attributes[m]&&p.attributes[m].indexOf(s)>=0&&y.push(p)}}return y}function S(C){if(C.composed||C.constructed){for(var m=t.util.createBuffer(),s=0;s<C.value.length;++s)m.putBytes(C.value[s].value);C.composed=C.constructed=!1,C.value=m.getBytes()}return C}function E(C,m){var s={},f=[];if(!e.validate(C,t.pkcs7.asn1.encryptedDataValidator,s,f))throw(y=new Error("Cannot read EncryptedContentInfo.")).errors=f,y;var y,I=e.derToOid(s.contentType);if(I!==a.oids.data)throw(y=new Error("PKCS#12 EncryptedContentInfo ContentType is not Data.")).oid=I,y;I=e.derToOid(s.encAlgorithm);var _=a.pbe.getCipher(I,s.encParameter,m),p=S(s.encryptedContentAsn1),i=t.util.createBuffer(p.value);if(_.update(i),!_.finish())throw new Error("Failed to decrypt PKCS#12 SafeContents.");return _.output.getBytes()}function u(C,m,s){if(!m&&0===C.length)return[];if((C=e.fromDer(C,m)).tagClass!==e.Class.UNIVERSAL||C.type!==e.Type.SEQUENCE||!0!==C.constructed)throw new Error("PKCS#12 SafeContents expected to be a SEQUENCE OF SafeBag.");for(var f=[],y=0;y<C.value.length;y++){var _={},p=[];if(!e.validate(C.value[y],h,_,p))throw(i=new Error("Cannot read SafeBag.")).errors=p,i;var n={type:e.derToOid(_.bagId),attributes:g(_.bagAttributes)};f.push(n);var B,U,P=_.bagValue.value[0];switch(n.type){case a.oids.pkcs8ShroudedKeyBag:if(null===(P=a.decryptPrivateKeyInfo(P,s)))throw new Error("Unable to decrypt PKCS#8 ShroudedKeyBag, wrong password?");case a.oids.keyBag:try{n.key=a.privateKeyFromAsn1(P)}catch{n.key=null,n.asn1=P}continue;case a.oids.certBag:B=v,U=function(){if(e.derToOid(_.certId)!==a.oids.x509Certificate){var x=new Error("Unsupported certificate type, only X.509 supported.");throw x.oid=e.derToOid(_.certId),x}var H=e.fromDer(_.cert,m);try{n.cert=a.certificateFromAsn1(H,!0)}catch{n.cert=null,n.asn1=H}};break;default:var i;throw(i=new Error("Unsupported PKCS#12 SafeBag type.")).oid=n.type,i}if(void 0!==B&&!e.validate(P,B,_,p))throw(i=new Error("Cannot read PKCS#12 "+B.name)).errors=p,i;U()}return f}function g(C){var m={};if(void 0!==C)for(var s=0;s<C.length;++s){var f={},y=[];if(!e.validate(C[s],r,f,y)){var I=new Error("Cannot read PKCS#12 BagAttribute.");throw I.errors=y,I}var _=e.derToOid(f.oid);if(void 0!==a.oids[_]){m[a.oids[_]]=[];for(var p=0;p<f.values.length;++p)m[a.oids[_]].push(f.values[p].value)}}return m}return d.pkcs12FromAsn1=function(C,m,s){"string"==typeof m?(s=m,m=!0):void 0===m&&(m=!0);var f={};if(!e.validate(C,T,f,[]))throw(I=new Error("Cannot read PKCS#12 PFX. ASN.1 object is not an PKCS#12 PFX.")).errors=I,I;var I,_={version:f.version.charCodeAt(0),safeContents:[],getBags:function(F){var z,j={};return"localKeyId"in F?z=F.localKeyId:"localKeyIdHex"in F&&(z=t.util.hexToBytes(F.localKeyIdHex)),void 0===z&&!("friendlyName"in F)&&"bagType"in F&&(j[F.bagType]=l(_.safeContents,null,null,F.bagType)),void 0!==z&&(j.localKeyId=l(_.safeContents,"localKeyId",z,F.bagType)),"friendlyName"in F&&(j.friendlyName=l(_.safeContents,"friendlyName",F.friendlyName,F.bagType)),j},getBagsByFriendlyName:function(F,j){return l(_.safeContents,"friendlyName",F,j)},getBagsByLocalKeyId:function(F,j){return l(_.safeContents,"localKeyId",F,j)}};if(3!==f.version.charCodeAt(0))throw(I=new Error("PKCS#12 PFX of version other than 3 not supported.")).version=f.version.charCodeAt(0),I;if(e.derToOid(f.contentType)!==a.oids.data)throw(I=new Error("Only PKCS#12 PFX in password integrity mode supported.")).oid=e.derToOid(f.contentType),I;var p=f.content.value[0];if(p.tagClass!==e.Class.UNIVERSAL||p.type!==e.Type.OCTETSTRING)throw new Error("PKCS#12 authSafe content data is not an OCTET STRING.");if(p=S(p),f.mac){var i=null,n=0,B=e.derToOid(f.macAlgorithm);switch(B){case a.oids.sha1:i=t.md.sha1.create(),n=20;break;case a.oids.sha256:i=t.md.sha256.create(),n=32;break;case a.oids.sha384:i=t.md.sha384.create(),n=48;break;case a.oids.sha512:i=t.md.sha512.create(),n=64;break;case a.oids.md5:i=t.md.md5.create(),n=16}if(null===i)throw new Error("PKCS#12 uses unsupported MAC algorithm: "+B);var U=new t.util.ByteBuffer(f.macSalt),P="macIterations"in f?parseInt(t.util.bytesToHex(f.macIterations),16):1,L=d.generateKey(s,U,3,P,n,i),x=t.hmac.create();if(x.start(i,L),x.update(p.value),x.getMac().getBytes()!==f.macDigest)throw new Error("PKCS#12 MAC could not be verified. Invalid password?")}return function D(C,m,s,f){if((m=e.fromDer(m,s)).tagClass!==e.Class.UNIVERSAL||m.type!==e.Type.SEQUENCE||!0!==m.constructed)throw new Error("PKCS#12 AuthenticatedSafe expected to be a SEQUENCE OF ContentInfo");for(var y=0;y<m.value.length;y++){var _={},p=[];if(!e.validate(m.value[y],R,_,p))throw(i=new Error("Cannot read ContentInfo.")).errors=p,i;var n={encrypted:!1},B=null,U=_.content.value[0];switch(e.derToOid(_.contentType)){case a.oids.data:if(U.tagClass!==e.Class.UNIVERSAL||U.type!==e.Type.OCTETSTRING)throw new Error("PKCS#12 SafeContents Data is not an OCTET STRING.");B=S(U).value;break;case a.oids.encryptedData:B=E(U,f),n.encrypted=!0;break;default:var i;throw(i=new Error("Unsupported PKCS#12 contentType.")).contentType=e.derToOid(_.contentType),i}n.safeBags=u(B,s,f),C.safeContents.push(n)}}(_,p.value,m,s),_},d.toPkcs12Asn1=function(C,m,s,f){(f=f||{}).saltSize=f.saltSize||8,f.count=f.count||2048,f.algorithm=f.algorithm||f.encAlgorithm||"aes128","useMac"in f||(f.useMac=!0),"localKeyId"in f||(f.localKeyId=null),"generateLocalKeyId"in f||(f.generateLocalKeyId=!0);var I,y=f.localKeyId;if(null!==y)y=t.util.hexToBytes(y);else if(f.generateLocalKeyId)if(m){var _=t.util.isArray(m)?m[0]:m;"string"==typeof _&&(_=a.certificateFromPem(_)),(p=t.md.sha1.create()).update(e.toDer(a.certificateToAsn1(_)).getBytes()),y=p.digest().getBytes()}else y=t.random.getBytes(20);var i=[];null!==y&&i.push(e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.localKeyId).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,y)])])),"friendlyName"in f&&i.push(e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.friendlyName).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[e.create(e.Class.UNIVERSAL,e.Type.BMPSTRING,!1,f.friendlyName)])])),i.length>0&&(I=e.create(e.Class.UNIVERSAL,e.Type.SET,!0,i));var n=[],B=[];null!==m&&(B=t.util.isArray(m)?m:[m]);for(var U=[],P=0;P<B.length;++P){"string"==typeof(m=B[P])&&(m=a.certificateFromPem(m));var L=0===P?I:void 0,x=a.certificateToAsn1(m),H=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.certBag).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.x509Certificate).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,e.toDer(x).getBytes())])])]),L]);U.push(H)}if(U.length>0){var F=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,U),j=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.data).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,e.toDer(F).getBytes())])]);n.push(j)}var z=null;if(null!==C){var X=a.wrapRsaPrivateKey(a.privateKeyToAsn1(C));z=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,null===s?[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.keyBag).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[X]),I]:[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.pkcs8ShroudedKeyBag).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[a.encryptPrivateKeyInfo(X,s,f)]),I]);var $=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[z]),ne=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.data).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,e.toDer($).getBytes())])]);n.push(ne)}var he,se=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,n);if(f.useMac){var p=t.md.sha1.create(),ve=new t.util.ByteBuffer(t.random.getBytes(f.saltSize)),W=f.count,ce=(C=d.generateKey(s,ve,3,W,20),t.hmac.create());ce.start(p,C),ce.update(e.toDer(se).getBytes());var Z=ce.getMac();he=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.sha1).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")]),e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,Z.getBytes())]),e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,ve.getBytes()),e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(W).getBytes())])}return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(3).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(a.oids.data).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,e.toDer(se).getBytes())])]),he])},d.generateKey=t.pbe.generatePkcs12Key,ur.exports}function ga(){if(ya)return Xt.exports;ya=1;var t=ue();rt(),pt(),ia(),yt(),Jt(),da(),dr(),wt(),fe(),yr();var e=t.asn1,a=Xt.exports=t.pki=t.pki||{};return a.pemToDer=function(d){var R=t.pem.decode(d)[0];if(R.procType&&"ENCRYPTED"===R.procType.type)throw new Error("Could not convert PEM to DER; PEM is encrypted.");return t.util.createBuffer(R.body)},a.privateKeyFromPem=function(d){var R=t.pem.decode(d)[0];if("PRIVATE KEY"!==R.type&&"RSA PRIVATE KEY"!==R.type){var T=new Error('Could not convert private key from PEM; PEM header type is not "PRIVATE KEY" or "RSA PRIVATE KEY".');throw T.headerType=R.type,T}if(R.procType&&"ENCRYPTED"===R.procType.type)throw new Error("Could not convert private key from PEM; PEM is encrypted.");var h=e.fromDer(R.body);return a.privateKeyFromAsn1(h)},a.privateKeyToPem=function(d,R){var T={type:"RSA PRIVATE KEY",body:e.toDer(a.privateKeyToAsn1(d)).getBytes()};return t.pem.encode(T,{maxline:R})},a.privateKeyInfoToPem=function(d,R){var T={type:"PRIVATE KEY",body:e.toDer(d).getBytes()};return t.pem.encode(T,{maxline:R})},Xt.exports}function ma(){if(va)return gr;va=1;var t=ue();rt(),Et(),zt(),yt(),ga(),Ze(),St(),fe();var e=function(c,k,w,o){var A=t.util.createBuffer(),K=c.length>>1,O=K+(1&c.length),Q=c.substr(0,O),re=c.substr(K,O),ae=t.util.createBuffer(),G=t.hmac.create();w=k+w;var ee=Math.ceil(o/16),ie=Math.ceil(o/20);G.start("MD5",Q);var pe=t.util.createBuffer();ae.putBytes(w);for(var oe=0;oe<ee;++oe)G.start(null,null),G.update(ae.getBytes()),ae.putBuffer(G.digest()),G.start(null,null),G.update(ae.bytes()+w),pe.putBuffer(G.digest());G.start("SHA1",re);var me=t.util.createBuffer();for(ae.clear(),ae.putBytes(w),oe=0;oe<ie;++oe)G.start(null,null),G.update(ae.getBytes()),ae.putBuffer(G.digest()),G.start(null,null),G.update(ae.bytes()+w),me.putBuffer(G.digest());return A.putBytes(t.util.xorBytes(pe.getBytes(),me.getBytes(),o)),A},d=function(c,k,w){var o=!1;try{var A=c.deflate(k.fragment.getBytes());k.fragment=t.util.createBuffer(A),k.length=A.length,o=!0}catch{}return o},R=function(c,k,w){var o=!1;try{var A=c.inflate(k.fragment.getBytes());k.fragment=t.util.createBuffer(A),k.length=A.length,o=!0}catch{}return o},T=function(c,k){var w=0;switch(k){case 1:w=c.getByte();break;case 2:w=c.getInt16();break;case 3:w=c.getInt24();break;case 4:w=c.getInt32()}return t.util.createBuffer(c.getBytes(w))},h=function(c,k,w){c.putInt(w.length(),k<<3),c.putBuffer(w)},r={Versions:{TLS_1_0:{major:3,minor:1},TLS_1_1:{major:3,minor:2},TLS_1_2:{major:3,minor:3}}};r.SupportedVersions=[r.Versions.TLS_1_1,r.Versions.TLS_1_0],r.Version=r.SupportedVersions[0],r.MaxFragment=15360,r.ConnectionEnd={server:0,client:1},r.PRFAlgorithm={tls_prf_sha256:0},r.BulkCipherAlgorithm={none:null,rc4:0,des3:1,aes:2},r.CipherType={stream:0,block:1,aead:2},r.MACAlgorithm={none:null,hmac_md5:0,hmac_sha1:1,hmac_sha256:2,hmac_sha384:3,hmac_sha512:4},r.CompressionMethod={none:0,deflate:1},r.ContentType={change_cipher_spec:20,alert:21,handshake:22,application_data:23,heartbeat:24},r.HandshakeType={hello_request:0,client_hello:1,server_hello:2,certificate:11,server_key_exchange:12,certificate_request:13,server_hello_done:14,certificate_verify:15,client_key_exchange:16,finished:20},r.Alert={},r.Alert.Level={warning:1,fatal:2},r.Alert.Description={close_notify:0,unexpected_message:10,bad_record_mac:20,decryption_failed:21,record_overflow:22,decompression_failure:30,handshake_failure:40,bad_certificate:42,unsupported_certificate:43,certificate_revoked:44,certificate_expired:45,certificate_unknown:46,illegal_parameter:47,unknown_ca:48,access_denied:49,decode_error:50,decrypt_error:51,export_restriction:60,protocol_version:70,insufficient_security:71,internal_error:80,user_canceled:90,no_renegotiation:100},r.HeartbeatMessageType={heartbeat_request:1,heartbeat_response:2},r.CipherSuites={},r.getCipherSuite=function(c){var k=null;for(var w in r.CipherSuites){var o=r.CipherSuites[w];if(o.id[0]===c.charCodeAt(0)&&o.id[1]===c.charCodeAt(1)){k=o;break}}return k},r.handleUnexpected=function(c,k){!c.open&&c.entity===r.ConnectionEnd.client||c.error(c,{message:"Unexpected message. Received TLS record out of order.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.unexpected_message}})},r.handleHelloRequest=function(c,k,w){!c.handshaking&&c.handshakes>0&&(r.queue(c,r.createAlert(c,{level:r.Alert.Level.warning,description:r.Alert.Description.no_renegotiation})),r.flush(c)),c.process()},r.parseHelloMessage=function(c,k,w){var o=null,A=c.entity===r.ConnectionEnd.client;if(w<38)c.error(c,{message:A?"Invalid ServerHello message. Message too short.":"Invalid ClientHello message. Message too short.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}});else{var K=k.fragment,O=K.length();if(o={version:{major:K.getByte(),minor:K.getByte()},random:t.util.createBuffer(K.getBytes(32)),session_id:T(K,1),extensions:[]},A?(o.cipher_suite=K.getBytes(2),o.compression_method=K.getByte()):(o.cipher_suites=T(K,2),o.compression_methods=T(K,1)),(O=w-(O-K.length()))>0){for(var Q=T(K,2);Q.length()>0;)o.extensions.push({type:[Q.getByte(),Q.getByte()],data:T(Q,2)});if(!A)for(var re=0;re<o.extensions.length;++re){var ae=o.extensions[re];if(0===ae.type[0]&&0===ae.type[1])for(var G=T(ae.data,2);G.length()>0&&0===G.getByte();)c.session.extensions.server_name.serverNameList.push(T(G,2).getBytes())}}if(c.session.version&&(o.version.major!==c.session.version.major||o.version.minor!==c.session.version.minor))return c.error(c,{message:"TLS version change is disallowed during renegotiation.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.protocol_version}});if(A)c.session.cipherSuite=r.getCipherSuite(o.cipher_suite);else for(var ie=t.util.createBuffer(o.cipher_suites.bytes());ie.length()>0&&(c.session.cipherSuite=r.getCipherSuite(ie.getBytes(2)),null===c.session.cipherSuite););if(null===c.session.cipherSuite)return c.error(c,{message:"No cipher suites in common.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.handshake_failure},cipherSuite:t.util.bytesToHex(o.cipher_suite)});c.session.compressionMethod=A?o.compression_method:r.CompressionMethod.none}return o},r.createSecurityParameters=function(c,k){var w=c.entity===r.ConnectionEnd.client,o=k.random.bytes(),A=w?c.session.sp.client_random:o,K=w?o:r.createRandom().getBytes();c.session.sp={entity:c.entity,prf_algorithm:r.PRFAlgorithm.tls_prf_sha256,bulk_cipher_algorithm:null,cipher_type:null,enc_key_length:null,block_length:null,fixed_iv_length:null,record_iv_length:null,mac_algorithm:null,mac_length:null,mac_key_length:null,compression_algorithm:c.session.compressionMethod,pre_master_secret:null,master_secret:null,client_random:A,server_random:K}},r.handleServerHello=function(c,k,w){var o=r.parseHelloMessage(c,k,w);if(!c.fail){if(!(o.version.minor<=c.version.minor))return c.error(c,{message:"Incompatible TLS version.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.protocol_version}});c.version.minor=o.version.minor,c.session.version=c.version;var A=o.session_id.bytes();A.length>0&&A===c.session.id?(c.expect=u,c.session.resuming=!0,c.session.sp.server_random=o.random.bytes()):(c.expect=l,c.session.resuming=!1,r.createSecurityParameters(c,o)),c.session.id=A,c.process()}},r.handleClientHello=function(c,k,w){var o=r.parseHelloMessage(c,k,w);if(!c.fail){var A=o.session_id.bytes(),K=null;if(c.sessionCache&&(null===(K=c.sessionCache.getSession(A))?A="":(K.version.major!==o.version.major||K.version.minor>o.version.minor)&&(K=null,A="")),0===A.length&&(A=t.random.getBytes(32)),c.session.id=A,c.session.clientHelloVersion=o.version,c.session.sp={},K)c.version=c.session.version=K.version,c.session.sp=K.sp;else{for(var O,Q=1;Q<r.SupportedVersions.length&&!((O=r.SupportedVersions[Q]).minor<=o.version.minor);++Q);c.version={major:O.major,minor:O.minor},c.session.version=c.version}null!==K?(c.expect=_,c.session.resuming=!0,c.session.sp.client_random=o.random.bytes()):(c.expect=!1!==c.verifyClient?f:y,c.session.resuming=!1,r.createSecurityParameters(c,o)),c.open=!0,r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createServerHello(c)})),c.session.resuming?(r.queue(c,r.createRecord(c,{type:r.ContentType.change_cipher_spec,data:r.createChangeCipherSpec()})),c.state.pending=r.createConnectionState(c),c.state.current.write=c.state.pending.write,r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createFinished(c)}))):(r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createCertificate(c)})),c.fail||(r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createServerKeyExchange(c)})),!1!==c.verifyClient&&r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createCertificateRequest(c)})),r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createServerHelloDone(c)})))),r.flush(c),c.process()}},r.handleCertificate=function(c,k,w){if(w<3)return c.error(c,{message:"Invalid Certificate message. Message too short.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}});var K,O,A={certificate_list:T(k.fragment,3)},Q=[];try{for(;A.certificate_list.length()>0;)K=T(A.certificate_list,3),O=t.asn1.fromDer(K),K=t.pki.certificateFromAsn1(O,!0),Q.push(K)}catch(ae){return c.error(c,{message:"Could not parse certificate list.",cause:ae,send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.bad_certificate}})}var re=c.entity===r.ConnectionEnd.client;!re&&!0!==c.verifyClient||0!==Q.length?0===Q.length?c.expect=re?S:y:(re?c.session.serverCertificate=Q[0]:c.session.clientCertificate=Q[0],r.verifyCertificateChain(c,Q)&&(c.expect=re?S:y)):c.error(c,{message:re?"No server certificate provided.":"No client certificate provided.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}}),c.process()},r.handleServerKeyExchange=function(c,k,w){if(w>0)return c.error(c,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.unsupported_certificate}});c.expect=D,c.process()},r.handleClientKeyExchange=function(c,k,w){if(w<48)return c.error(c,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.unsupported_certificate}});var A={enc_pre_master_secret:T(k.fragment,2).getBytes()},K=null;if(c.getPrivateKey)try{K=c.getPrivateKey(c,c.session.serverCertificate),K=t.pki.privateKeyFromPem(K)}catch(re){c.error(c,{message:"Could not get private key.",cause:re,send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}})}if(null===K)return c.error(c,{message:"No private key set.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}});try{var O=c.session.sp;O.pre_master_secret=K.decrypt(A.enc_pre_master_secret);var Q=c.session.clientHelloVersion;if(Q.major!==O.pre_master_secret.charCodeAt(0)||Q.minor!==O.pre_master_secret.charCodeAt(1))throw new Error("TLS version rollback attack detected.")}catch{O.pre_master_secret=t.random.getBytes(48)}c.expect=_,null!==c.session.clientCertificate&&(c.expect=I),c.process()},r.handleCertificateRequest=function(c,k,w){if(w<3)return c.error(c,{message:"Invalid CertificateRequest. Message too short.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}});var o=k.fragment,A={certificate_types:T(o,1),certificate_authorities:T(o,2)};c.session.certificateRequest=A,c.expect=E,c.process()},r.handleCertificateVerify=function(c,k,w){if(w<2)return c.error(c,{message:"Invalid CertificateVerify. Message too short.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}});var o=k.fragment;o.read-=4;var A=o.bytes();o.read+=4;var K={signature:T(o,2).getBytes()},O=t.util.createBuffer();O.putBuffer(c.session.md5.digest()),O.putBuffer(c.session.sha1.digest()),O=O.getBytes();try{if(!c.session.clientCertificate.publicKey.verify(O,K.signature,"NONE"))throw new Error("CertificateVerify signature does not match.");c.session.md5.update(A),c.session.sha1.update(A)}catch{return c.error(c,{message:"Bad signature in CertificateVerify.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.handshake_failure}})}c.expect=_,c.process()},r.handleServerHelloDone=function(c,k,w){if(w>0)return c.error(c,{message:"Invalid ServerHelloDone message. Invalid length.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.record_overflow}});if(null===c.serverCertificate){var o={message:"No server certificate provided. Not enough security.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.insufficient_security}},K=c.verify(c,o.alert.description,0,[]);if(!0!==K)return(K||0===K)&&("object"!=typeof K||t.util.isArray(K)?"number"==typeof K&&(o.alert.description=K):(K.message&&(o.message=K.message),K.alert&&(o.alert.description=K.alert))),c.error(c,o)}null!==c.session.certificateRequest&&(k=r.createRecord(c,{type:r.ContentType.handshake,data:r.createCertificate(c)}),r.queue(c,k)),k=r.createRecord(c,{type:r.ContentType.handshake,data:r.createClientKeyExchange(c)}),r.queue(c,k),c.expect=m;var O=function(Q,re){null!==Q.session.certificateRequest&&null!==Q.session.clientCertificate&&r.queue(Q,r.createRecord(Q,{type:r.ContentType.handshake,data:r.createCertificateVerify(Q,re)})),r.queue(Q,r.createRecord(Q,{type:r.ContentType.change_cipher_spec,data:r.createChangeCipherSpec()})),Q.state.pending=r.createConnectionState(Q),Q.state.current.write=Q.state.pending.write,r.queue(Q,r.createRecord(Q,{type:r.ContentType.handshake,data:r.createFinished(Q)})),Q.expect=u,r.flush(Q),Q.process()};if(null===c.session.certificateRequest||null===c.session.clientCertificate)return O(c,null);r.getClientSignature(c,O)},r.handleChangeCipherSpec=function(c,k){if(1!==k.fragment.getByte())return c.error(c,{message:"Invalid ChangeCipherSpec message received.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.illegal_parameter}});var w=c.entity===r.ConnectionEnd.client;(c.session.resuming&&w||!c.session.resuming&&!w)&&(c.state.pending=r.createConnectionState(c)),c.state.current.read=c.state.pending.read,(!c.session.resuming&&w||c.session.resuming&&!w)&&(c.state.pending=null),c.expect=w?g:p,c.process()},r.handleFinished=function(c,k,w){var o=k.fragment;o.read-=4;var A=o.bytes();o.read+=4;var K=k.fragment.getBytes();(o=t.util.createBuffer()).putBuffer(c.session.md5.digest()),o.putBuffer(c.session.sha1.digest());var O=c.entity===r.ConnectionEnd.client;if((o=e(c.session.sp.master_secret,O?"server finished":"client finished",o.getBytes(),12)).getBytes()!==K)return c.error(c,{message:"Invalid verify_data in Finished message.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.decrypt_error}});c.session.md5.update(A),c.session.sha1.update(A),(c.session.resuming&&O||!c.session.resuming&&!O)&&(r.queue(c,r.createRecord(c,{type:r.ContentType.change_cipher_spec,data:r.createChangeCipherSpec()})),c.state.current.write=c.state.pending.write,c.state.pending=null,r.queue(c,r.createRecord(c,{type:r.ContentType.handshake,data:r.createFinished(c)}))),c.expect=O?C:i,c.handshaking=!1,++c.handshakes,c.peerCertificate=O?c.session.serverCertificate:c.session.clientCertificate,r.flush(c),c.isConnected=!0,c.connected(c),c.process()},r.handleAlert=function(c,k){var A,w=k.fragment,o={level:w.getByte(),description:w.getByte()};switch(o.description){case r.Alert.Description.close_notify:A="Connection closed.";break;case r.Alert.Description.unexpected_message:A="Unexpected message.";break;case r.Alert.Description.bad_record_mac:A="Bad record MAC.";break;case r.Alert.Description.decryption_failed:A="Decryption failed.";break;case r.Alert.Description.record_overflow:A="Record overflow.";break;case r.Alert.Description.decompression_failure:A="Decompression failed.";break;case r.Alert.Description.handshake_failure:A="Handshake failure.";break;case r.Alert.Description.bad_certificate:A="Bad certificate.";break;case r.Alert.Description.unsupported_certificate:A="Unsupported certificate.";break;case r.Alert.Description.certificate_revoked:A="Certificate revoked.";break;case r.Alert.Description.certificate_expired:A="Certificate expired.";break;case r.Alert.Description.certificate_unknown:A="Certificate unknown.";break;case r.Alert.Description.illegal_parameter:A="Illegal parameter.";break;case r.Alert.Description.unknown_ca:A="Unknown certificate authority.";break;case r.Alert.Description.access_denied:A="Access denied.";break;case r.Alert.Description.decode_error:A="Decode error.";break;case r.Alert.Description.decrypt_error:A="Decrypt error.";break;case r.Alert.Description.export_restriction:A="Export restriction.";break;case r.Alert.Description.protocol_version:A="Unsupported protocol version.";break;case r.Alert.Description.insufficient_security:A="Insufficient security.";break;case r.Alert.Description.internal_error:A="Internal error.";break;case r.Alert.Description.user_canceled:A="User canceled.";break;case r.Alert.Description.no_renegotiation:A="Renegotiation not supported.";break;default:A="Unknown error."}if(o.description===r.Alert.Description.close_notify)return c.close();c.error(c,{message:A,send:!1,origin:c.entity===r.ConnectionEnd.client?"server":"client",alert:o}),c.process()},r.handleHandshake=function(c,k){var w=k.fragment,o=w.getByte(),A=w.getInt24();if(A>w.length())return c.fragmented=k,k.fragment=t.util.createBuffer(),w.read-=4,c.process();c.fragmented=null,w.read-=4;var K=w.bytes(A+4);w.read+=4,o in he[c.entity][c.expect]?(c.entity===r.ConnectionEnd.server&&!c.open&&!c.fail&&(c.handshaking=!0,c.session={version:null,extensions:{server_name:{serverNameList:[]}},cipherSuite:null,compressionMethod:null,serverCertificate:null,clientCertificate:null,md5:t.md.md5.create(),sha1:t.md.sha1.create()}),o!==r.HandshakeType.hello_request&&o!==r.HandshakeType.certificate_verify&&o!==r.HandshakeType.finished&&(c.session.md5.update(K),c.session.sha1.update(K)),he[c.entity][c.expect][o](c,k,A)):r.handleUnexpected(c,k)},r.handleApplicationData=function(c,k){c.data.putBuffer(k.fragment),c.dataReady(c),c.process()},r.handleHeartbeat=function(c,k){var w=k.fragment,o=w.getByte(),A=w.getInt16(),K=w.getBytes(A);if(o===r.HeartbeatMessageType.heartbeat_request){if(c.handshaking||A>K.length)return c.process();r.queue(c,r.createRecord(c,{type:r.ContentType.heartbeat,data:r.createHeartbeat(r.HeartbeatMessageType.heartbeat_response,K)})),r.flush(c)}else if(o===r.HeartbeatMessageType.heartbeat_response){if(K!==c.expectedHeartbeatPayload)return c.process();c.heartbeatReceived&&c.heartbeatReceived(c,t.util.createBuffer(K))}c.process()};var l=1,S=2,D=3,E=4,u=5,g=6,C=7,m=8,f=1,y=2,I=3,_=4,p=5,i=6,n=r.handleUnexpected,B=r.handleChangeCipherSpec,U=r.handleAlert,P=r.handleHandshake,L=r.handleApplicationData,x=r.handleHeartbeat,H=[];H[r.ConnectionEnd.client]=[[n,U,P,n,x],[n,U,P,n,x],[n,U,P,n,x],[n,U,P,n,x],[n,U,P,n,x],[B,U,n,n,x],[n,U,P,n,x],[n,U,P,L,x],[n,U,P,n,x]],H[r.ConnectionEnd.server]=[[n,U,P,n,x],[n,U,P,n,x],[n,U,P,n,x],[n,U,P,n,x],[B,U,n,n,x],[n,U,P,n,x],[n,U,P,L,x],[n,U,P,n,x]];var F=r.handleHelloRequest,z=r.handleCertificate,X=r.handleServerKeyExchange,$=r.handleCertificateRequest,ne=r.handleServerHelloDone,se=r.handleFinished,he=[];he[r.ConnectionEnd.client]=[[n,n,r.handleServerHello,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,z,X,$,ne,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,X,$,ne,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,n,$,ne,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,n,n,ne,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,se],[F,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[F,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n]],he[r.ConnectionEnd.server]=[[n,r.handleClientHello,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,z,n,n,n,n,n,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,r.handleClientKeyExchange,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,r.handleCertificateVerify,n,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,se],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n],[n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n]],r.generateKeys=function(c,k){var w=e,o=k.client_random+k.server_random;c.session.resuming||(k.master_secret=w(k.pre_master_secret,"master secret",o,48).bytes(),k.pre_master_secret=null);var A=2*k.mac_key_length+2*k.enc_key_length,K=c.version.major===r.Versions.TLS_1_0.major&&c.version.minor===r.Versions.TLS_1_0.minor;K&&(A+=2*k.fixed_iv_length);var O=w(k.master_secret,"key expansion",o=k.server_random+k.client_random,A),Q={client_write_MAC_key:O.getBytes(k.mac_key_length),server_write_MAC_key:O.getBytes(k.mac_key_length),client_write_key:O.getBytes(k.enc_key_length),server_write_key:O.getBytes(k.enc_key_length)};return K&&(Q.client_write_IV=O.getBytes(k.fixed_iv_length),Q.server_write_IV=O.getBytes(k.fixed_iv_length)),Q},r.createConnectionState=function(c){var k=c.entity===r.ConnectionEnd.client,w=function(){var K={sequenceNumber:[0,0],macKey:null,macLength:0,macFunction:null,cipherState:null,cipherFunction:function(O){return!0},compressionState:null,compressFunction:function(O){return!0},updateSequenceNumber:function(){4294967295===K.sequenceNumber[1]?(K.sequenceNumber[1]=0,++K.sequenceNumber[0]):++K.sequenceNumber[1]}};return K},o={read:w(),write:w()};if(o.read.update=function(K,O){return o.read.cipherFunction(O,o.read)?o.read.compressFunction(K,O,o.read)||K.error(K,{message:"Could not decompress record.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.decompression_failure}}):K.error(K,{message:"Could not decrypt record or bad MAC.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.bad_record_mac}}),!K.fail},o.write.update=function(K,O){return o.write.compressFunction(K,O,o.write)?o.write.cipherFunction(O,o.write)||K.error(K,{message:"Could not encrypt record.",send:!1,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}}):K.error(K,{message:"Could not compress record.",send:!1,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}}),!K.fail},c.session){var A=c.session.sp;switch(c.session.cipherSuite.initSecurityParameters(A),A.keys=r.generateKeys(c,A),o.read.macKey=k?A.keys.server_write_MAC_key:A.keys.client_write_MAC_key,o.write.macKey=k?A.keys.client_write_MAC_key:A.keys.server_write_MAC_key,c.session.cipherSuite.initConnectionState(o,c,A),A.compression_algorithm){case r.CompressionMethod.none:break;case r.CompressionMethod.deflate:o.read.compressFunction=R,o.write.compressFunction=d;break;default:throw new Error("Unsupported compression algorithm.")}}return o},r.createRandom=function(){var c=new Date,k=+c+6e4*c.getTimezoneOffset(),w=t.util.createBuffer();return w.putInt32(k),w.putBytes(t.random.getBytes(28)),w},r.createRecord=function(c,k){return k.data?{type:k.type,version:{major:c.version.major,minor:c.version.minor},length:k.data.length(),fragment:k.data}:null},r.createAlert=function(c,k){var w=t.util.createBuffer();return w.putByte(k.level),w.putByte(k.description),r.createRecord(c,{type:r.ContentType.alert,data:w})},r.createClientHello=function(c){c.session.clientHelloVersion={major:c.version.major,minor:c.version.minor};for(var k=t.util.createBuffer(),w=0;w<c.cipherSuites.length;++w){var o=c.cipherSuites[w];k.putByte(o.id[0]),k.putByte(o.id[1])}var A=k.length(),K=t.util.createBuffer();K.putByte(r.CompressionMethod.none);var O=K.length(),Q=t.util.createBuffer();if(c.virtualHost){var re=t.util.createBuffer();re.putByte(0),re.putByte(0);var ae=t.util.createBuffer();ae.putByte(0),h(ae,2,t.util.createBuffer(c.virtualHost));var G=t.util.createBuffer();h(G,2,ae),h(re,2,G),Q.putBuffer(re)}var ee=Q.length();ee>0&&(ee+=2);var ie=c.session.id,pe=ie.length+1+2+4+28+2+A+1+O+ee,oe=t.util.createBuffer();return oe.putByte(r.HandshakeType.client_hello),oe.putInt24(pe),oe.putByte(c.version.major),oe.putByte(c.version.minor),oe.putBytes(c.session.sp.client_random),h(oe,1,t.util.createBuffer(ie)),h(oe,2,k),h(oe,1,K),ee>0&&h(oe,2,Q),oe},r.createServerHello=function(c){var k=c.session.id,w=k.length+1+2+4+28+2+1,o=t.util.createBuffer();return o.putByte(r.HandshakeType.server_hello),o.putInt24(w),o.putByte(c.version.major),o.putByte(c.version.minor),o.putBytes(c.session.sp.server_random),h(o,1,t.util.createBuffer(k)),o.putByte(c.session.cipherSuite.id[0]),o.putByte(c.session.cipherSuite.id[1]),o.putByte(c.session.compressionMethod),o},r.createCertificate=function(c){var k=c.entity===r.ConnectionEnd.client,w=null;c.getCertificate&&(w=c.getCertificate(c,k?c.session.certificateRequest:c.session.extensions.server_name.serverNameList));var A=t.util.createBuffer();if(null!==w)try{t.util.isArray(w)||(w=[w]);for(var K=null,O=0;O<w.length;++O){var Q=t.pem.decode(w[O])[0];if("CERTIFICATE"!==Q.type&&"X509 CERTIFICATE"!==Q.type&&"TRUSTED CERTIFICATE"!==Q.type){var re=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw re.headerType=Q.type,re}if(Q.procType&&"ENCRYPTED"===Q.procType.type)throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var ae=t.util.createBuffer(Q.body);null===K&&(K=t.asn1.fromDer(ae.bytes(),!1));var G=t.util.createBuffer();h(G,3,ae),A.putBuffer(G)}w=t.pki.certificateFromAsn1(K),k?c.session.clientCertificate=w:c.session.serverCertificate=w}catch(pe){return c.error(c,{message:"Could not send certificate list.",cause:pe,send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.bad_certificate}})}var ee=3+A.length(),ie=t.util.createBuffer();return ie.putByte(r.HandshakeType.certificate),ie.putInt24(ee),h(ie,3,A),ie},r.createClientKeyExchange=function(c){var k=t.util.createBuffer();k.putByte(c.session.clientHelloVersion.major),k.putByte(c.session.clientHelloVersion.minor),k.putBytes(t.random.getBytes(46));var w=c.session.sp;w.pre_master_secret=k.getBytes();var A=(k=c.session.serverCertificate.publicKey.encrypt(w.pre_master_secret)).length+2,K=t.util.createBuffer();return K.putByte(r.HandshakeType.client_key_exchange),K.putInt24(A),K.putInt16(k.length),K.putBytes(k),K},r.createServerKeyExchange=function(c){return t.util.createBuffer()},r.getClientSignature=function(c,k){var w=t.util.createBuffer();w.putBuffer(c.session.md5.digest()),w.putBuffer(c.session.sha1.digest()),w=w.getBytes(),c.getSignature=c.getSignature||function(o,A,K){var O=null;if(o.getPrivateKey)try{O=o.getPrivateKey(o,o.session.clientCertificate),O=t.pki.privateKeyFromPem(O)}catch(Q){o.error(o,{message:"Could not get private key.",cause:Q,send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}})}null===O?o.error(o,{message:"No private key set.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.internal_error}}):A=O.sign(A,null),K(o,A)},c.getSignature(c,w,k)},r.createCertificateVerify=function(c,k){var w=k.length+2,o=t.util.createBuffer();return o.putByte(r.HandshakeType.certificate_verify),o.putInt24(w),o.putInt16(k.length),o.putBytes(k),o},r.createCertificateRequest=function(c){var k=t.util.createBuffer();k.putByte(1);var w=t.util.createBuffer();for(var o in c.caStore.certs){var K=t.pki.distinguishedNameToAsn1(c.caStore.certs[o].subject),O=t.asn1.toDer(K);w.putInt16(O.length()),w.putBuffer(O)}var Q=1+k.length()+2+w.length(),re=t.util.createBuffer();return re.putByte(r.HandshakeType.certificate_request),re.putInt24(Q),h(re,1,k),h(re,2,w),re},r.createServerHelloDone=function(c){var k=t.util.createBuffer();return k.putByte(r.HandshakeType.server_hello_done),k.putInt24(0),k},r.createChangeCipherSpec=function(){var c=t.util.createBuffer();return c.putByte(1),c},r.createFinished=function(c){var k=t.util.createBuffer();k.putBuffer(c.session.md5.digest()),k.putBuffer(c.session.sha1.digest()),k=e(c.session.sp.master_secret,c.entity===r.ConnectionEnd.client?"client finished":"server finished",k.getBytes(),12);var Q=t.util.createBuffer();return Q.putByte(r.HandshakeType.finished),Q.putInt24(k.length()),Q.putBuffer(k),Q},r.createHeartbeat=function(c,k,w){typeof w>"u"&&(w=k.length);var o=t.util.createBuffer();o.putByte(c),o.putInt16(w),o.putBytes(k);var A=o.length(),K=Math.max(16,A-w-3);return o.putBytes(t.random.getBytes(K)),o},r.queue=function(c,k){if(k&&(0!==k.fragment.length()||k.type!==r.ContentType.handshake&&k.type!==r.ContentType.alert&&k.type!==r.ContentType.change_cipher_spec)){if(k.type===r.ContentType.handshake){var w=k.fragment.bytes();c.session.md5.update(w),c.session.sha1.update(w),w=null}var o;if(k.fragment.length()<=r.MaxFragment)o=[k];else{o=[];for(var A=k.fragment.bytes();A.length>r.MaxFragment;)o.push(r.createRecord(c,{type:k.type,data:t.util.createBuffer(A.slice(0,r.MaxFragment))})),A=A.slice(r.MaxFragment);A.length>0&&o.push(r.createRecord(c,{type:k.type,data:t.util.createBuffer(A)}))}for(var K=0;K<o.length&&!c.fail;++K){var O=o[K];c.state.current.write.update(c,O)&&c.records.push(O)}}},r.flush=function(c){for(var k=0;k<c.records.length;++k){var w=c.records[k];c.tlsData.putByte(w.type),c.tlsData.putByte(w.version.major),c.tlsData.putByte(w.version.minor),c.tlsData.putInt16(w.fragment.length()),c.tlsData.putBuffer(c.records[k].fragment)}return c.records=[],c.tlsDataReady(c)};var Z=function(c){switch(c){case!0:return!0;case t.pki.certificateError.bad_certificate:return r.Alert.Description.bad_certificate;case t.pki.certificateError.unsupported_certificate:return r.Alert.Description.unsupported_certificate;case t.pki.certificateError.certificate_revoked:return r.Alert.Description.certificate_revoked;case t.pki.certificateError.certificate_expired:return r.Alert.Description.certificate_expired;case t.pki.certificateError.certificate_unknown:return r.Alert.Description.certificate_unknown;case t.pki.certificateError.unknown_ca:return r.Alert.Description.unknown_ca;default:return r.Alert.Description.bad_certificate}};for(var le in r.verifyCertificateChain=function(c,k){try{var w={};for(var o in c.verifyOptions)w[o]=c.verifyOptions[o];w.verify=function(K,O,Q){Z(K);var ae=c.verify(c,K,O,Q);if(!0!==ae){if("object"==typeof ae&&!t.util.isArray(ae)){var G=new Error("The application rejected the certificate.");throw G.send=!0,G.alert={level:r.Alert.Level.fatal,description:r.Alert.Description.bad_certificate},ae.message&&(G.message=ae.message),ae.alert&&(G.alert.description=ae.alert),G}ae!==K&&(ae=function(c){switch(c){case!0:return!0;case r.Alert.Description.bad_certificate:return t.pki.certificateError.bad_certificate;case r.Alert.Description.unsupported_certificate:return t.pki.certificateError.unsupported_certificate;case r.Alert.Description.certificate_revoked:return t.pki.certificateError.certificate_revoked;case r.Alert.Description.certificate_expired:return t.pki.certificateError.certificate_expired;case r.Alert.Description.certificate_unknown:return t.pki.certificateError.certificate_unknown;case r.Alert.Description.unknown_ca:return t.pki.certificateError.unknown_ca;default:return t.pki.certificateError.bad_certificate}}(ae))}return ae},t.pki.verifyCertificateChain(c.caStore,k,w)}catch(K){var A=K;("object"!=typeof A||t.util.isArray(A))&&(A={send:!0,alert:{level:r.Alert.Level.fatal,description:Z(K)}}),"send"in A||(A.send=!0),"alert"in A||(A.alert={level:r.Alert.Level.fatal,description:Z(A.error)}),c.error(c,A)}return!c.fail},r.createSessionCache=function(c,k){var w=null;if(c&&c.getSession&&c.setSession&&c.order)w=c;else{for(var o in(w={}).cache=c||{},w.capacity=Math.max(k||100,1),w.order=[],c)w.order.length<=k?w.order.push(o):delete c[o];w.getSession=function(A){var K=null,O=null;if(A?O=t.util.bytesToHex(A):w.order.length>0&&(O=w.order[0]),null!==O&&O in w.cache)for(var Q in K=w.cache[O],delete w.cache[O],w.order)if(w.order[Q]===O){w.order.splice(Q,1);break}return K},w.setSession=function(A,K){if(w.order.length===w.capacity){var O=w.order.shift();delete w.cache[O]}O=t.util.bytesToHex(A),w.order.push(O),w.cache[O]=K}}return w},r.createConnection=function(c){var k;k=c.caStore?t.util.isArray(c.caStore)?t.pki.createCaStore(c.caStore):c.caStore:t.pki.createCaStore();var w=c.cipherSuites||null;if(null===w)for(var o in w=[],r.CipherSuites)w.push(r.CipherSuites[o]);var A=c.server?r.ConnectionEnd.server:r.ConnectionEnd.client,K=c.sessionCache?r.createSessionCache(c.sessionCache):null,O={version:{major:r.Version.major,minor:r.Version.minor},entity:A,sessionId:c.sessionId,caStore:k,sessionCache:K,cipherSuites:w,connected:c.connected,virtualHost:c.virtualHost||null,verifyClient:c.verifyClient||!1,verify:c.verify||function(G,ee,ie,pe){return ee},verifyOptions:c.verifyOptions||{},getCertificate:c.getCertificate||null,getPrivateKey:c.getPrivateKey||null,getSignature:c.getSignature||null,input:t.util.createBuffer(),tlsData:t.util.createBuffer(),data:t.util.createBuffer(),tlsDataReady:c.tlsDataReady,dataReady:c.dataReady,heartbeatReceived:c.heartbeatReceived,closed:c.closed,error:function(G,ee){ee.origin=ee.origin||(G.entity===r.ConnectionEnd.client?"client":"server"),ee.send&&(r.queue(G,r.createAlert(G,ee.alert)),r.flush(G));var ie=!1!==ee.fatal;ie&&(G.fail=!0),c.error(G,ee),ie&&G.close(!1)},deflate:c.deflate||null,inflate:c.inflate||null,reset:function(G){O.version={major:r.Version.major,minor:r.Version.minor},O.record=null,O.session=null,O.peerCertificate=null,O.state={pending:null,current:null},O.expect=0,O.fragmented=null,O.records=[],O.open=!1,O.handshakes=0,O.handshaking=!1,O.isConnected=!1,O.fail=!(G||typeof G>"u"),O.input.clear(),O.tlsData.clear(),O.data.clear(),O.state.current=r.createConnectionState(O)}};return O.reset(),O.handshake=function(G){if(O.entity!==r.ConnectionEnd.client)O.error(O,{message:"Cannot initiate handshake as a server.",fatal:!1});else if(O.handshaking)O.error(O,{message:"Handshake already in progress.",fatal:!1});else{O.fail&&!O.open&&0===O.handshakes&&(O.fail=!1),O.handshaking=!0;var ee=null;(G=G||"").length>0&&(O.sessionCache&&(ee=O.sessionCache.getSession(G)),null===ee&&(G="")),0===G.length&&O.sessionCache&&null!==(ee=O.sessionCache.getSession())&&(G=ee.id),O.session={id:G,version:null,cipherSuite:null,compressionMethod:null,serverCertificate:null,certificateRequest:null,clientCertificate:null,sp:{},md5:t.md.md5.create(),sha1:t.md.sha1.create()},ee&&(O.version=ee.version,O.session.sp=ee.sp),O.session.sp.client_random=r.createRandom().getBytes(),O.open=!0,r.queue(O,r.createRecord(O,{type:r.ContentType.handshake,data:r.createClientHello(O)})),r.flush(O)}},O.process=function(G){var ee=0;return G&&O.input.putBytes(G),O.fail||(null!==O.record&&O.record.ready&&O.record.fragment.isEmpty()&&(O.record=null),null===O.record&&(ee=function(G){var ee=0,ie=G.input,pe=ie.length();if(pe<5)ee=5-pe;else{G.record={type:ie.getByte(),version:{major:ie.getByte(),minor:ie.getByte()},length:ie.getInt16(),fragment:t.util.createBuffer(),ready:!1};var oe=G.record.version.major===G.version.major;oe&&G.session&&G.session.version&&(oe=G.record.version.minor===G.version.minor),oe||G.error(G,{message:"Incompatible TLS version.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.protocol_version}})}return ee}(O)),!O.fail&&null!==O.record&&!O.record.ready&&(ee=function(G){var ee=0,ie=G.input,pe=ie.length();return pe<G.record.length?ee=G.record.length-pe:(G.record.fragment.putBytes(ie.getBytes(G.record.length)),ie.compact(),G.state.current.read.update(G,G.record)&&(null!==G.fragmented&&(G.fragmented.type===G.record.type?(G.fragmented.fragment.putBuffer(G.record.fragment),G.record=G.fragmented):G.error(G,{message:"Invalid fragmented record.",send:!0,alert:{level:r.Alert.Level.fatal,description:r.Alert.Description.unexpected_message}})),G.record.ready=!0)),ee}(O)),!O.fail&&null!==O.record&&O.record.ready&&function(G,ee){var ie=ee.type-r.ContentType.change_cipher_spec,pe=H[G.entity][G.expect];ie in pe?pe[ie](G,ee):r.handleUnexpected(G,ee)}(O,O.record)),ee},O.prepare=function(G){return r.queue(O,r.createRecord(O,{type:r.ContentType.application_data,data:t.util.createBuffer(G)})),r.flush(O)},O.prepareHeartbeatRequest=function(G,ee){return G instanceof t.util.ByteBuffer&&(G=G.bytes()),typeof ee>"u"&&(ee=G.length),O.expectedHeartbeatPayload=G,r.queue(O,r.createRecord(O,{type:r.ContentType.heartbeat,data:r.createHeartbeat(r.HeartbeatMessageType.heartbeat_request,G,ee)})),r.flush(O)},O.close=function(G){if(!O.fail&&O.sessionCache&&O.session){var ee={id:O.session.id,version:O.session.version,sp:O.session.sp};ee.sp.keys=null,O.sessionCache.setSession(ee.id,ee)}O.open&&(O.open=!1,O.input.clear(),(O.isConnected||O.handshaking)&&(O.isConnected=O.handshaking=!1,r.queue(O,r.createAlert(O,{level:r.Alert.Level.warning,description:r.Alert.Description.close_notify})),r.flush(O)),O.closed(O)),O.reset(G)},O},gr=t.tls=t.tls||{},r)"function"!=typeof r[le]&&(t.tls[le]=r[le]);return t.tls.prf_tls1=e,t.tls.hmac_sha1=function(c,k,w){var o=t.hmac.create();o.start("SHA1",c);var A=t.util.createBuffer();return A.putInt32(k[0]),A.putInt32(k[1]),A.putByte(w.type),A.putByte(w.version.major),A.putByte(w.version.minor),A.putInt16(w.length),A.putBytes(w.fragment.bytes()),o.update(A.getBytes()),o.digest().getBytes()},t.tls.createSessionCache=r.createSessionCache,t.tls.createConnection=r.createConnection,gr}var Ea,vr={exports:{}};function Sa(){if(Ea)return vr.exports;Ea=1;var t=ue();nt(),fe();var e=vr.exports=t.sha512=t.sha512||{};t.md.sha512=t.md.algorithms.sha512=e;var a=t.sha384=t.sha512.sha384=t.sha512.sha384||{};a.create=function(){return e.create("SHA-384")},t.md.sha384=t.md.algorithms.sha384=a,t.sha512.sha256=t.sha512.sha256||{create:function(){return e.create("SHA-512/256")}},t.md["sha512/256"]=t.md.algorithms["sha512/256"]=t.sha512.sha256,t.sha512.sha224=t.sha512.sha224||{create:function(){return e.create("SHA-512/224")}},t.md["sha512/224"]=t.md.algorithms["sha512/224"]=t.sha512.sha224,e.create=function(l){if(R||function r(){d="\x80",d+=t.util.fillString("\0",128),T=[[1116352408,3609767458],[1899447441,602891725],[3049323471,3964484399],[3921009573,2173295548],[961987163,4081628472],[1508970993,3053834265],[2453635748,2937671579],[2870763221,3664609560],[3624381080,2734883394],[310598401,1164996542],[607225278,1323610764],[1426881987,3590304994],[1925078388,4068182383],[2162078206,991336113],[2614888103,633803317],[3248222580,3479774868],[3835390401,2666613458],[4022224774,944711139],[264347078,2341262773],[604807628,2007800933],[770255983,1495990901],[1249150122,1856431235],[1555081692,3175218132],[1996064986,2198950837],[2554220882,3999719339],[2821834349,766784016],[2952996808,2566594879],[3210313671,3203337956],[3336571891,1034457026],[3584528711,2466948901],[113926993,3758326383],[338241895,168717936],[666307205,1188179964],[773529912,1546045734],[1294757372,1522805485],[1396182291,2643833823],[1695183700,2343527390],[1986661051,1014477480],[2177026350,1206759142],[2456956037,344077627],[2730485921,1290863460],[2820302411,3158454273],[3259730800,3505952657],[3345764771,106217008],[3516065817,3606008344],[3600352804,1432725776],[4094571909,1467031594],[275423344,851169720],[430227734,3100823752],[506948616,1363258195],[659060556,3750685593],[883997877,3785050280],[958139571,3318307427],[1322822218,3812723403],[1537002063,2003034995],[1747873779,3602036899],[1955562222,1575990012],[2024104815,1125592928],[2227730452,2716904306],[2361852424,442776044],[2428436474,593698344],[2756734187,3733110249],[3204031479,2999351573],[3329325298,3815920427],[3391569614,3928383900],[3515267271,566280711],[3940187606,3454069534],[4118630271,4000239992],[116418474,1914138554],[174292421,2731055270],[289380356,3203993006],[460393269,320620315],[685471733,587496836],[852142971,1086792851],[1017036298,365543100],[1126000580,2618297676],[1288033470,3409855158],[1501505948,4234509866],[1607167915,987167468],[1816402316,1246189591]],(h={})["SHA-512"]=[[1779033703,4089235720],[3144134277,2227873595],[1013904242,4271175723],[2773480762,1595750129],[1359893119,2917565137],[2600822924,725511199],[528734635,4215389547],[1541459225,327033209]],h["SHA-384"]=[[3418070365,3238371032],[1654270250,914150663],[2438529370,812702999],[355462360,4144912697],[1731405415,4290775857],[2394180231,1750603025],[3675008525,1694076839],[1203062813,3204075428]],h["SHA-512/256"]=[[573645204,4230739756],[2673172387,3360449730],[596883563,1867755857],[2520282905,1497426621],[2519219938,2827943907],[3193839141,1401305490],[721525244,746961066],[246885852,2177182882]],h["SHA-512/224"]=[[2352822216,424955298],[1944164710,2312950998],[502970286,855612546],[1738396948,1479516111],[258812777,2077511080],[2011393907,79989058],[1067287976,1780299464],[286451373,2446758561]],R=!0}(),typeof l>"u"&&(l="SHA-512"),!(l in h))throw new Error("Invalid SHA-512 algorithm: "+l);for(var S=h[l],D=null,E=t.util.createBuffer(),u=new Array(80),g=0;g<80;++g)u[g]=new Array(2);var C=64;switch(l){case"SHA-384":C=48;break;case"SHA-512/256":C=32;break;case"SHA-512/224":C=28}var m={algorithm:l.replace("-","").toLowerCase(),blockLength:128,digestLength:C,messageLength:0,fullMessageLength:null,messageLengthSize:16,start:function(){m.messageLength=0,m.fullMessageLength=m.messageLength128=[];for(var s=m.messageLengthSize/4,f=0;f<s;++f)m.fullMessageLength.push(0);for(E=t.util.createBuffer(),D=new Array(S.length),f=0;f<S.length;++f)D[f]=S[f].slice(0);return m}};return m.start(),m.update=function(s,f){"utf8"===f&&(s=t.util.encodeUtf8(s));var y=s.length;m.messageLength+=y,y=[y/4294967296>>>0,y>>>0];for(var I=m.fullMessageLength.length-1;I>=0;--I)m.fullMessageLength[I]+=y[1],y[1]=y[0]+(m.fullMessageLength[I]/4294967296>>>0),m.fullMessageLength[I]=m.fullMessageLength[I]>>>0,y[0]=y[1]/4294967296>>>0;return E.putBytes(s),v(D,u,E),(E.read>2048||0===E.length())&&E.compact(),m},m.digest=function(){var s=t.util.createBuffer();s.putBytes(E.bytes()),s.putBytes(d.substr(0,m.blockLength-(m.fullMessageLength[m.fullMessageLength.length-1]+m.messageLengthSize&m.blockLength-1)));for(var I,p=8*m.fullMessageLength[0],i=0;i<m.fullMessageLength.length-1;++i)s.putInt32((p+=(I=8*m.fullMessageLength[i+1])/4294967296>>>0)>>>0),p=I>>>0;s.putInt32(p);var n=new Array(D.length);for(i=0;i<D.length;++i)n[i]=D[i].slice(0);v(n,u,s);var U,B=t.util.createBuffer();for(U="SHA-512"===l?n.length:"SHA-384"===l?n.length-2:n.length-4,i=0;i<U;++i)B.putInt32(n[i][0]),(i!==U-1||"SHA-512/224"!==l)&&B.putInt32(n[i][1]);return B},m};var d=null,R=!1,T=null,h=null;function v(l,S,D){for(var E,u,g,C,n,B,U,P,L,x,H,F,j,z,X,$,ne,se,he,ve,W,ce,Z,je,le,c,k,w=D.length();w>=128;){for(W=0;W<16;++W)S[W][0]=D.getInt32()>>>0,S[W][1]=D.getInt32()>>>0;for(;W<80;++W)u=(((ce=(je=S[W-2])[0])<<13|(Z=je[1])>>>19)^(Z<<3|ce>>>29)^(ce<<26|Z>>>6))>>>0,S[W][0]=(E=((ce>>>19|Z<<13)^(Z>>>29|ce<<3)^ce>>>6)>>>0)+(le=S[W-7])[0]+(g=(((ce=(c=S[W-15])[0])>>>1|(Z=c[1])<<31)^(ce>>>8|Z<<24)^ce>>>7)>>>0)+(k=S[W-16])[0]+((Z=u+le[1]+(C=((ce<<31|Z>>>1)^(ce<<24|Z>>>8)^(ce<<25|Z>>>7))>>>0)+k[1])/4294967296>>>0)>>>0,S[W][1]=Z>>>0;for(n=l[0][0],B=l[0][1],U=l[1][0],P=l[1][1],L=l[2][0],x=l[2][1],H=l[3][0],F=l[3][1],j=l[4][0],z=l[4][1],X=l[5][0],$=l[5][1],ne=l[6][0],se=l[6][1],he=l[7][0],ve=l[7][1],W=0;W<80;++W)E=he+(((j>>>14|z<<18)^(j>>>18|z<<14)^(z>>>9|j<<23))>>>0)+((ne^j&(X^ne))>>>0)+T[W][0]+S[W][0]+((Z=ve+(((j<<18|z>>>14)^(j<<14|z>>>18)^(z<<23|j>>>9))>>>0)+((se^z&($^se))>>>0)+T[W][1]+S[W][1])/4294967296>>>0)>>>0,u=Z>>>0,g=(((n>>>28|B<<4)^(B>>>2|n<<30)^(B>>>7|n<<25))>>>0)+((n&U|L&(n^U))>>>0)+((Z=(((n<<4|B>>>28)^(B<<30|n>>>2)^(B<<25|n>>>7))>>>0)+((B&P|x&(B^P))>>>0))/4294967296>>>0)>>>0,C=Z>>>0,he=ne,ve=se,ne=X,se=$,X=j,$=z,j=H+E+((Z=F+u)/4294967296>>>0)>>>0,z=Z>>>0,H=L,F=x,L=U,x=P,U=n,P=B,n=E+g+((Z=u+C)/4294967296>>>0)>>>0,B=Z>>>0;l[0][0]=l[0][0]+n+((Z=l[0][1]+B)/4294967296>>>0)>>>0,l[0][1]=Z>>>0,l[1][0]=l[1][0]+U+((Z=l[1][1]+P)/4294967296>>>0)>>>0,l[1][1]=Z>>>0,l[2][0]=l[2][0]+L+((Z=l[2][1]+x)/4294967296>>>0)>>>0,l[2][1]=Z>>>0,l[3][0]=l[3][0]+H+((Z=l[3][1]+F)/4294967296>>>0)>>>0,l[3][1]=Z>>>0,l[4][0]=l[4][0]+j+((Z=l[4][1]+z)/4294967296>>>0)>>>0,l[4][1]=Z>>>0,l[5][0]=l[5][0]+X+((Z=l[5][1]+$)/4294967296>>>0)>>>0,l[5][1]=Z>>>0,l[6][0]=l[6][0]+ne+((Z=l[6][1]+se)/4294967296>>>0)>>>0,l[6][1]=Z>>>0,l[7][0]=l[7][0]+he+((Z=l[7][1]+ve)/4294967296>>>0)>>>0,l[7][1]=Z>>>0,w-=128}}return vr.exports}var Ta,mr,ba,Cr,Ia,Er,Aa,Na,_t={};var Ra,Sr={exports:{}};var wa,_a,ka,Tr={exports:{}},La=function pn(){return ka||(ka=1,_a=ue(),lt(),function rn(){if(Ca)return Ft.exports;Ca=1;var t=ue();lt(),ma();var e=Ft.exports=t.tls;function a(v,l,S){var D=l.entity===t.tls.ConnectionEnd.client;v.read.cipherState={init:!1,cipher:t.cipher.createDecipher("AES-CBC",D?S.keys.server_write_key:S.keys.client_write_key),iv:D?S.keys.server_write_IV:S.keys.client_write_IV},v.write.cipherState={init:!1,cipher:t.cipher.createCipher("AES-CBC",D?S.keys.client_write_key:S.keys.server_write_key),iv:D?S.keys.client_write_IV:S.keys.server_write_IV},v.read.cipherFunction=h,v.write.cipherFunction=d,v.read.macLength=v.write.macLength=S.mac_length,v.read.macFunction=v.write.macFunction=e.hmac_sha1}function d(v,l){var E,S=!1,D=l.macFunction(l.macKey,l.sequenceNumber,v);v.fragment.putBytes(D),l.updateSequenceNumber(),E=v.version.minor===e.Versions.TLS_1_0.minor?l.cipherState.init?null:l.cipherState.iv:t.random.getBytesSync(16),l.cipherState.init=!0;var u=l.cipherState.cipher;return u.start({iv:E}),v.version.minor>=e.Versions.TLS_1_1.minor&&u.output.putBytes(E),u.update(v.fragment),u.finish(R)&&(v.fragment=u.output,v.length=v.fragment.length(),S=!0),S}function R(v,l,S){if(!S){var D=v-l.length()%v;l.fillWithByte(D-1,D)}return!0}function T(v,l,S){var D=!0;if(S){for(var E=l.length(),u=l.last(),g=E-1-u;g<E-1;++g)D=D&&l.at(g)==u;D&&l.truncate(u+1)}return D}function h(v,l){var D,S=!1;D=v.version.minor===e.Versions.TLS_1_0.minor?l.cipherState.init?null:l.cipherState.iv:v.fragment.getBytes(16),l.cipherState.init=!0;var E=l.cipherState.cipher;E.start({iv:D}),E.update(v.fragment),S=E.finish(T);var u=l.macLength,g=t.random.getBytesSync(u),C=E.output.length();C>=u?(v.fragment=E.output.getBytes(C-u),g=E.output.getBytes(u)):v.fragment=E.output.getBytes(),v.fragment=t.util.createBuffer(v.fragment),v.length=v.fragment.length();var m=l.macFunction(l.macKey,l.sequenceNumber,v);return l.updateSequenceNumber(),S=function r(v,l,S){var D=t.hmac.create();return D.start("SHA1",v),D.update(l),l=D.digest().getBytes(),D.start(null,null),D.update(S),l===D.digest().getBytes()}(l.macKey,g,m)&&S,S}return e.CipherSuites.TLS_RSA_WITH_AES_128_CBC_SHA={id:[0,47],name:"TLS_RSA_WITH_AES_128_CBC_SHA",initSecurityParameters:function(v){v.bulk_cipher_algorithm=e.BulkCipherAlgorithm.aes,v.cipher_type=e.CipherType.block,v.enc_key_length=16,v.block_length=16,v.fixed_iv_length=16,v.record_iv_length=16,v.mac_algorithm=e.MACAlgorithm.hmac_sha1,v.mac_length=20,v.mac_key_length=20},initConnectionState:a},e.CipherSuites.TLS_RSA_WITH_AES_256_CBC_SHA={id:[0,53],name:"TLS_RSA_WITH_AES_256_CBC_SHA",initSecurityParameters:function(v){v.bulk_cipher_algorithm=e.BulkCipherAlgorithm.aes,v.cipher_type=e.CipherType.block,v.enc_key_length=32,v.block_length=16,v.fixed_iv_length=16,v.record_iv_length=16,v.mac_algorithm=e.MACAlgorithm.hmac_sha1,v.mac_length=20,v.mac_key_length=20},initConnectionState:a},Ft.exports}(),rt(),xt(),At(),function nn(){if(ba)return mr;ba=1;var t=ue();Nt(),Ze(),Sa(),fe();var e=function an(){if(Ta)return _t;Ta=1;var t=ue();rt();var e=t.asn1;return _t.privateKeyValidator={name:"PrivateKeyInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:e.Class.UNIVERSAL,type:e.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},_t.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:e.Class.UNIVERSAL,type:e.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:e.Class.UNIVERSAL,type:e.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{tagClass:e.Class.UNIVERSAL,type:e.Type.BITSTRING,constructed:!1,composed:!0,captureBitStringValue:"ed25519PublicKey"}]},_t}(),a=e.publicKeyValidator,d=e.privateKeyValidator;if(typeof R>"u")var R=t.jsbn.BigInteger;var T=t.util.ByteBuffer,h=typeof Buffer>"u"?Uint8Array:Buffer;t.pki=t.pki||{},mr=t.pki.ed25519=t.ed25519=t.ed25519||{};var r=t.ed25519;function v(c){var k=c.message;if(k instanceof Uint8Array||k instanceof h)return k;var w=c.encoding;if(void 0===k){if(!c.md)throw new TypeError('"options.message" or "options.md" not specified.');k=c.md.digest().getBytes(),w="binary"}if("string"==typeof k&&!w)throw new TypeError('"options.encoding" must be "binary" or "utf8".');if("string"==typeof k){if(typeof Buffer<"u")return Buffer.from(k,w);k=new T(k,w)}else if(!(k instanceof T))throw new TypeError('"options.message" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a string with "options.encoding" specifying its encoding.');for(var o=new h(k.length()),A=0;A<o.length;++A)o[A]=k.at(A);return o}r.constants={},r.constants.PUBLIC_KEY_BYTE_LENGTH=32,r.constants.PRIVATE_KEY_BYTE_LENGTH=64,r.constants.SEED_BYTE_LENGTH=32,r.constants.SIGN_BYTE_LENGTH=64,r.constants.HASH_BYTE_LENGTH=64,r.generateKeyPair=function(c){var k=(c=c||{}).seed;if(void 0===k)k=t.random.getBytesSync(r.constants.SEED_BYTE_LENGTH);else if("string"==typeof k){if(k.length!==r.constants.SEED_BYTE_LENGTH)throw new TypeError('"seed" must be '+r.constants.SEED_BYTE_LENGTH+" bytes in length.")}else if(!(k instanceof Uint8Array))throw new TypeError('"seed" must be a node.js Buffer, Uint8Array, or a binary string.');k=v({message:k,encoding:"binary"});for(var w=new h(r.constants.PUBLIC_KEY_BYTE_LENGTH),o=new h(r.constants.PRIVATE_KEY_BYTE_LENGTH),A=0;A<32;++A)o[A]=k[A];return function f(c,k){var o,w=[W(),W(),W(),W()],A=s(k,32);for(A[0]&=248,A[31]&=127,A[31]|=64,$(w,A),B(c,w),o=0;o<32;++o)k[o+32]=c[o]}(w,o),{publicKey:w,privateKey:o}},r.privateKeyFromAsn1=function(c){var k={},w=[];if(!t.asn1.validate(c,d,k,w)){var A=new Error("Invalid Key.");throw A.errors=w,A}var K=t.asn1.derToOid(k.privateKeyOid),O=t.oids.EdDSA25519;if(K!==O)throw new Error('Invalid OID "'+K+'"; OID must be "'+O+'".');return{privateKeyBytes:v({message:t.asn1.fromDer(k.privateKey).value,encoding:"binary"})}},r.publicKeyFromAsn1=function(c){var k={},w=[];if(!t.asn1.validate(c,a,k,w)){var A=new Error("Invalid Key.");throw A.errors=w,A}var K=t.asn1.derToOid(k.publicKeyOid),O=t.oids.EdDSA25519;if(K!==O)throw new Error('Invalid OID "'+K+'"; OID must be "'+O+'".');var Q=k.ed25519PublicKey;if(Q.length!==r.constants.PUBLIC_KEY_BYTE_LENGTH)throw new Error("Key length is invalid.");return v({message:Q,encoding:"binary"})},r.publicKeyFromPrivateKey=function(c){var k=v({message:(c=c||{}).privateKey,encoding:"binary"});if(k.length!==r.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+r.constants.PRIVATE_KEY_BYTE_LENGTH);for(var w=new h(r.constants.PUBLIC_KEY_BYTE_LENGTH),o=0;o<w.length;++o)w[o]=k[32+o];return w},r.sign=function(c){var k=v(c=c||{}),w=v({message:c.privateKey,encoding:"binary"});if(w.length===r.constants.SEED_BYTE_LENGTH)w=r.generateKeyPair({seed:w}).privateKey;else if(w.length!==r.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+r.constants.SEED_BYTE_LENGTH+" or "+r.constants.PRIVATE_KEY_BYTE_LENGTH);var A=new h(r.constants.SIGN_BYTE_LENGTH+k.length);!function y(c,k,w,o){var A,K,O=new Float64Array(64),Q=[W(),W(),W(),W()],re=s(o,32);re[0]&=248,re[31]&=127,re[31]|=64;for(A=0;A<w;++A)c[64+A]=k[A];for(A=0;A<32;++A)c[32+A]=re[32+A];var G=s(c.subarray(32),w+32);for(p(G),$(Q,G),B(c,Q),A=32;A<64;++A)c[A]=o[A];var ee=s(c,w+64);for(p(ee),A=32;A<64;++A)O[A]=0;for(A=0;A<32;++A)O[A]=G[A];for(A=0;A<32;++A)for(K=0;K<32;K++)O[A+K]+=ee[A]*re[K];_(c.subarray(32),O)}(A,k,k.length,w);for(var K=new h(r.constants.SIGN_BYTE_LENGTH),O=0;O<K.length;++O)K[O]=A[O];return K},r.verify=function(c){var k=v(c=c||{});if(void 0===c.signature)throw new TypeError('"options.signature" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a binary string.');var w=v({message:c.signature,encoding:"binary"});if(w.length!==r.constants.SIGN_BYTE_LENGTH)throw new TypeError('"options.signature" must have a byte length of '+r.constants.SIGN_BYTE_LENGTH);var o=v({message:c.publicKey,encoding:"binary"});if(o.length!==r.constants.PUBLIC_KEY_BYTE_LENGTH)throw new TypeError('"options.publicKey" must have a byte length of '+r.constants.PUBLIC_KEY_BYTE_LENGTH);var O,A=new h(r.constants.SIGN_BYTE_LENGTH+k.length),K=new h(r.constants.SIGN_BYTE_LENGTH+k.length);for(O=0;O<r.constants.SIGN_BYTE_LENGTH;++O)A[O]=w[O];for(O=0;O<k.length;++O)A[O+r.constants.SIGN_BYTE_LENGTH]=k[O];return function I(c,k,w,o){var A,O=new h(32),Q=[W(),W(),W(),W()],re=[W(),W(),W(),W()];if(w<64||function P(c,k){var w=W(),o=W(),A=W(),K=W(),O=W(),Q=W(),re=W();return ne(c[2],S),function L(c,k){var w;for(w=0;w<16;++w)c[w]=k[2*w]+(k[2*w+1]<<8);c[15]&=32767}(c[1],k),je(A,c[1]),le(K,A,D),Z(A,A,c[2]),ce(K,c[2],K),je(O,K),je(Q,O),le(re,Q,O),le(w,re,A),le(w,w,K),function x(c,k){var o,w=W();for(o=0;o<16;++o)w[o]=k[o];for(o=250;o>=0;--o)je(w,w),1!==o&&le(w,w,k);for(o=0;o<16;++o)c[o]=w[o]}(w,w),le(w,w,A),le(w,w,K),le(w,w,K),le(c[0],w,K),je(o,c[0]),le(o,o,K),H(o,A)&&le(c[0],c[0],m),je(o,c[0]),le(o,o,K),H(o,A)?-1:(z(c[0])===k[31]>>7&&Z(c[0],l,c[0]),le(c[3],c[0],c[1]),0)}(re,o))return-1;for(A=0;A<w;++A)c[A]=k[A];for(A=0;A<32;++A)c[A+32]=o[A];var ae=s(c,w);if(p(ae),X(Q,re,ae),$(re,k.subarray(32)),i(Q,re),B(O,Q),w-=64,F(k,0,O,0)){for(A=0;A<w;++A)c[A]=0;return-1}for(A=0;A<w;++A)c[A]=k[A+64];return w}(K,A,A.length,o)>=0};var l=W(),S=W([1]),D=W([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),E=W([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),u=W([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),g=W([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),C=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]),m=W([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function s(c,k){var w=t.md.sha512.create(),o=new T(c);w.update(o.getBytes(k),"binary");var A=w.digest().getBytes();if(typeof Buffer<"u")return Buffer.from(A,"binary");for(var K=new h(r.constants.HASH_BYTE_LENGTH),O=0;O<64;++O)K[O]=A.charCodeAt(O);return K}function _(c,k){var w,o,A,K;for(o=63;o>=32;--o){for(w=0,A=o-32,K=o-12;A<K;++A)k[A]+=w-16*k[o]*C[A-(o-32)],k[A]-=256*(w=k[A]+128>>8);k[A]+=w,k[o]=0}for(w=0,A=0;A<32;++A)k[A]+=w-(k[31]>>4)*C[A],w=k[A]>>8,k[A]&=255;for(A=0;A<32;++A)k[A]-=w*C[A];for(o=0;o<32;++o)k[o+1]+=k[o]>>8,c[o]=255&k[o]}function p(c){for(var k=new Float64Array(64),w=0;w<64;++w)k[w]=c[w],c[w]=0;_(c,k)}function i(c,k){var w=W(),o=W(),A=W(),K=W(),O=W(),Q=W(),re=W(),ae=W(),G=W();Z(w,c[1],c[0]),Z(G,k[1],k[0]),le(w,w,G),ce(o,c[0],c[1]),ce(G,k[0],k[1]),le(o,o,G),le(A,c[3],k[3]),le(A,A,E),le(K,c[2],k[2]),ce(K,K,K),Z(O,o,w),Z(Q,K,A),ce(re,K,A),ce(ae,o,w),le(c[0],O,Q),le(c[1],ae,re),le(c[2],re,Q),le(c[3],O,ae)}function n(c,k,w){for(var o=0;o<4;++o)ve(c[o],k[o],w)}function B(c,k){var w=W(),o=W(),A=W();(function se(c,k){var o,w=W();for(o=0;o<16;++o)w[o]=k[o];for(o=253;o>=0;--o)je(w,w),2!==o&&4!==o&&le(w,w,k);for(o=0;o<16;++o)c[o]=w[o]})(A,k[2]),le(w,k[0],A),le(o,k[1],A),U(c,o),c[31]^=z(w)<<7}function U(c,k){var w,o,A,K=W(),O=W();for(w=0;w<16;++w)O[w]=k[w];for(he(O),he(O),he(O),o=0;o<2;++o){for(K[0]=O[0]-65517,w=1;w<15;++w)K[w]=O[w]-65535-(K[w-1]>>16&1),K[w-1]&=65535;K[15]=O[15]-32767-(K[14]>>16&1),A=K[15]>>16&1,K[14]&=65535,ve(O,K,1-A)}for(w=0;w<16;w++)c[2*w]=255&O[w],c[2*w+1]=O[w]>>8}function H(c,k){var w=new h(32),o=new h(32);return U(w,c),U(o,k),F(w,0,o,0)}function F(c,k,w,o){return function j(c,k,w,o,A){var K,O=0;for(K=0;K<A;++K)O|=c[k+K]^w[o+K];return(1&O-1>>>8)-1}(c,k,w,o,32)}function z(c){var k=new h(32);return U(k,c),1&k[0]}function X(c,k,w){var o,A;for(ne(c[0],l),ne(c[1],S),ne(c[2],S),ne(c[3],l),A=255;A>=0;--A)n(c,k,o=w[A/8|0]>>(7&A)&1),i(k,c),i(c,c),n(c,k,o)}function $(c,k){var w=[W(),W(),W(),W()];ne(w[0],u),ne(w[1],g),ne(w[2],S),le(w[3],u,g),X(c,w,k)}function ne(c,k){var w;for(w=0;w<16;w++)c[w]=0|k[w]}function he(c){var k,w,o=1;for(k=0;k<16;++k)w=c[k]+o+65535,o=Math.floor(w/65536),c[k]=w-65536*o;c[0]+=o-1+37*(o-1)}function ve(c,k,w){for(var o,A=~(w-1),K=0;K<16;++K)c[K]^=o=A&(c[K]^k[K]),k[K]^=o}function W(c){var k,w=new Float64Array(16);if(c)for(k=0;k<c.length;++k)w[k]=c[k];return w}function ce(c,k,w){for(var o=0;o<16;++o)c[o]=k[o]+w[o]}function Z(c,k,w){for(var o=0;o<16;++o)c[o]=k[o]-w[o]}function je(c,k){le(c,k,k)}function le(c,k,w){var o,A,K=0,O=0,Q=0,re=0,ae=0,G=0,ee=0,ie=0,pe=0,oe=0,me=0,Te=0,Se=0,Ce=0,ye=0,ge=0,be=0,Fe=0,He=0,Ge=0,qe=0,Qe=0,Ye=0,$e=0,at=0,st=0,ut=0,dt=0,vt=0,bt=0,Lt=0,Ae=w[0],Be=w[1],Ne=w[2],Re=w[3],we=w[4],_e=w[5],ke=w[6],Le=w[7],Ue=w[8],De=w[9],Ve=w[10],Oe=w[11],Ee=w[12],Ie=w[13],Pe=w[14],xe=w[15];K+=(o=k[0])*Ae,O+=o*Be,Q+=o*Ne,re+=o*Re,ae+=o*we,G+=o*_e,ee+=o*ke,ie+=o*Le,pe+=o*Ue,oe+=o*De,me+=o*Ve,Te+=o*Oe,Se+=o*Ee,Ce+=o*Ie,ye+=o*Pe,ge+=o*xe,O+=(o=k[1])*Ae,Q+=o*Be,re+=o*Ne,ae+=o*Re,G+=o*we,ee+=o*_e,ie+=o*ke,pe+=o*Le,oe+=o*Ue,me+=o*De,Te+=o*Ve,Se+=o*Oe,Ce+=o*Ee,ye+=o*Ie,ge+=o*Pe,be+=o*xe,Q+=(o=k[2])*Ae,re+=o*Be,ae+=o*Ne,G+=o*Re,ee+=o*we,ie+=o*_e,pe+=o*ke,oe+=o*Le,me+=o*Ue,Te+=o*De,Se+=o*Ve,Ce+=o*Oe,ye+=o*Ee,ge+=o*Ie,be+=o*Pe,Fe+=o*xe,re+=(o=k[3])*Ae,ae+=o*Be,G+=o*Ne,ee+=o*Re,ie+=o*we,pe+=o*_e,oe+=o*ke,me+=o*Le,Te+=o*Ue,Se+=o*De,Ce+=o*Ve,ye+=o*Oe,ge+=o*Ee,be+=o*Ie,Fe+=o*Pe,He+=o*xe,ae+=(o=k[4])*Ae,G+=o*Be,ee+=o*Ne,ie+=o*Re,pe+=o*we,oe+=o*_e,me+=o*ke,Te+=o*Le,Se+=o*Ue,Ce+=o*De,ye+=o*Ve,ge+=o*Oe,be+=o*Ee,Fe+=o*Ie,He+=o*Pe,Ge+=o*xe,G+=(o=k[5])*Ae,ee+=o*Be,ie+=o*Ne,pe+=o*Re,oe+=o*we,me+=o*_e,Te+=o*ke,Se+=o*Le,Ce+=o*Ue,ye+=o*De,ge+=o*Ve,be+=o*Oe,Fe+=o*Ee,He+=o*Ie,Ge+=o*Pe,qe+=o*xe,ee+=(o=k[6])*Ae,ie+=o*Be,pe+=o*Ne,oe+=o*Re,me+=o*we,Te+=o*_e,Se+=o*ke,Ce+=o*Le,ye+=o*Ue,ge+=o*De,be+=o*Ve,Fe+=o*Oe,He+=o*Ee,Ge+=o*Ie,qe+=o*Pe,Qe+=o*xe,ie+=(o=k[7])*Ae,pe+=o*Be,oe+=o*Ne,me+=o*Re,Te+=o*we,Se+=o*_e,Ce+=o*ke,ye+=o*Le,ge+=o*Ue,be+=o*De,Fe+=o*Ve,He+=o*Oe,Ge+=o*Ee,qe+=o*Ie,Qe+=o*Pe,Ye+=o*xe,pe+=(o=k[8])*Ae,oe+=o*Be,me+=o*Ne,Te+=o*Re,Se+=o*we,Ce+=o*_e,ye+=o*ke,ge+=o*Le,be+=o*Ue,Fe+=o*De,He+=o*Ve,Ge+=o*Oe,qe+=o*Ee,Qe+=o*Ie,Ye+=o*Pe,$e+=o*xe,oe+=(o=k[9])*Ae,me+=o*Be,Te+=o*Ne,Se+=o*Re,Ce+=o*we,ye+=o*_e,ge+=o*ke,be+=o*Le,Fe+=o*Ue,He+=o*De,Ge+=o*Ve,qe+=o*Oe,Qe+=o*Ee,Ye+=o*Ie,$e+=o*Pe,at+=o*xe,me+=(o=k[10])*Ae,Te+=o*Be,Se+=o*Ne,Ce+=o*Re,ye+=o*we,ge+=o*_e,be+=o*ke,Fe+=o*Le,He+=o*Ue,Ge+=o*De,qe+=o*Ve,Qe+=o*Oe,Ye+=o*Ee,$e+=o*Ie,at+=o*Pe,st+=o*xe,Te+=(o=k[11])*Ae,Se+=o*Be,Ce+=o*Ne,ye+=o*Re,ge+=o*we,be+=o*_e,Fe+=o*ke,He+=o*Le,Ge+=o*Ue,qe+=o*De,Qe+=o*Ve,Ye+=o*Oe,$e+=o*Ee,at+=o*Ie,st+=o*Pe,ut+=o*xe,Se+=(o=k[12])*Ae,Ce+=o*Be,ye+=o*Ne,ge+=o*Re,be+=o*we,Fe+=o*_e,He+=o*ke,Ge+=o*Le,qe+=o*Ue,Qe+=o*De,Ye+=o*Ve,$e+=o*Oe,at+=o*Ee,st+=o*Ie,ut+=o*Pe,dt+=o*xe,Ce+=(o=k[13])*Ae,ye+=o*Be,ge+=o*Ne,be+=o*Re,Fe+=o*we,He+=o*_e,Ge+=o*ke,qe+=o*Le,Qe+=o*Ue,Ye+=o*De,$e+=o*Ve,at+=o*Oe,st+=o*Ee,ut+=o*Ie,dt+=o*Pe,vt+=o*xe,ye+=(o=k[14])*Ae,ge+=o*Be,be+=o*Ne,Fe+=o*Re,He+=o*we,Ge+=o*_e,qe+=o*ke,Qe+=o*Le,Ye+=o*Ue,$e+=o*De,at+=o*Ve,st+=o*Oe,ut+=o*Ee,dt+=o*Ie,vt+=o*Pe,bt+=o*xe,ge+=(o=k[15])*Ae,O+=38*(Fe+=o*Ne),Q+=38*(He+=o*Re),re+=38*(Ge+=o*we),ae+=38*(qe+=o*_e),G+=38*(Qe+=o*ke),ee+=38*(Ye+=o*Le),ie+=38*($e+=o*Ue),pe+=38*(at+=o*De),oe+=38*(st+=o*Ve),me+=38*(ut+=o*Oe),Te+=38*(dt+=o*Ee),Se+=38*(vt+=o*Ie),Ce+=38*(bt+=o*Pe),ye+=38*(Lt+=o*xe),K=(o=(K+=38*(be+=o*Be))+(A=1)+65535)-65536*(A=Math.floor(o/65536)),O=(o=O+A+65535)-65536*(A=Math.floor(o/65536)),Q=(o=Q+A+65535)-65536*(A=Math.floor(o/65536)),re=(o=re+A+65535)-65536*(A=Math.floor(o/65536)),ae=(o=ae+A+65535)-65536*(A=Math.floor(o/65536)),G=(o=G+A+65535)-65536*(A=Math.floor(o/65536)),ee=(o=ee+A+65535)-65536*(A=Math.floor(o/65536)),ie=(o=ie+A+65535)-65536*(A=Math.floor(o/65536)),pe=(o=pe+A+65535)-65536*(A=Math.floor(o/65536)),oe=(o=oe+A+65535)-65536*(A=Math.floor(o/65536)),me=(o=me+A+65535)-65536*(A=Math.floor(o/65536)),Te=(o=Te+A+65535)-65536*(A=Math.floor(o/65536)),Se=(o=Se+A+65535)-65536*(A=Math.floor(o/65536)),Ce=(o=Ce+A+65535)-65536*(A=Math.floor(o/65536)),ye=(o=ye+A+65535)-65536*(A=Math.floor(o/65536)),ge=(o=ge+A+65535)-65536*(A=Math.floor(o/65536)),K=(o=(K+=A-1+37*(A-1))+(A=1)+65535)-65536*(A=Math.floor(o/65536)),O=(o=O+A+65535)-65536*(A=Math.floor(o/65536)),Q=(o=Q+A+65535)-65536*(A=Math.floor(o/65536)),re=(o=re+A+65535)-65536*(A=Math.floor(o/65536)),ae=(o=ae+A+65535)-65536*(A=Math.floor(o/65536)),G=(o=G+A+65535)-65536*(A=Math.floor(o/65536)),ee=(o=ee+A+65535)-65536*(A=Math.floor(o/65536)),ie=(o=ie+A+65535)-65536*(A=Math.floor(o/65536)),pe=(o=pe+A+65535)-65536*(A=Math.floor(o/65536)),oe=(o=oe+A+65535)-65536*(A=Math.floor(o/65536)),me=(o=me+A+65535)-65536*(A=Math.floor(o/65536)),Te=(o=Te+A+65535)-65536*(A=Math.floor(o/65536)),Se=(o=Se+A+65535)-65536*(A=Math.floor(o/65536)),Ce=(o=Ce+A+65535)-65536*(A=Math.floor(o/65536)),ye=(o=ye+A+65535)-65536*(A=Math.floor(o/65536)),ge=(o=ge+A+65535)-65536*(A=Math.floor(o/65536)),c[0]=K+=A-1+37*(A-1),c[1]=O,c[2]=Q,c[3]=re,c[4]=ae,c[5]=G,c[6]=ee,c[7]=ie,c[8]=pe,c[9]=oe,c[10]=me,c[11]=Te,c[12]=Se,c[13]=Ce,c[14]=ye,c[15]=ge}return mr}(),Et(),function sn(){if(Ia)return Cr;Ia=1;var t=ue();fe(),Ze(),Nt(),Cr=t.kem=t.kem||{};var e=t.jsbn.BigInteger;function a(d,R,T,h){d.generate=function(r,v){for(var l=new t.util.ByteBuffer,S=Math.ceil(v/h)+T,D=new t.util.ByteBuffer,E=T;E<S;++E){D.putInt32(E),R.start(),R.update(r+D.getBytes());var u=R.digest();l.putBytes(u.getBytes(h))}return l.truncate(l.length()-v),l.getBytes()}}t.kem.rsa={},t.kem.rsa.create=function(d,R){var T=(R=R||{}).prng||t.random;return{encrypt:function(r,v){var S,l=Math.ceil(r.n.bitLength()/8);do{S=new e(t.util.bytesToHex(T.getBytesSync(l)),16).mod(r.n)}while(S.compareTo(e.ONE)<=0);var D=l-(S=t.util.hexToBytes(S.toString(16))).length;return D>0&&(S=t.util.fillString("\0",D)+S),{encapsulation:r.encrypt(S,"NONE"),key:d.generate(S,v)}},decrypt:function(r,v,l){var S=r.decrypt(v,"NONE");return d.generate(S,l)}}},t.kem.kdf1=function(d,R){a(this,d,0,R||d.digestLength)},t.kem.kdf2=function(d,R){a(this,d,1,R||d.digestLength)}}(),function on(){if(Aa)return Er;Aa=1;var t=ue();fe(),Er=t.log=t.log||{},t.log.levels=["none","error","warning","info","debug","verbose","max"];var e={},a=[],d=null;t.log.LEVEL_LOCKED=2,t.log.NO_LEVEL_CHECK=4,t.log.INTERPOLATE=8;for(var R=0;R<t.log.levels.length;++R){var T=t.log.levels[R];e[T]={index:R,name:T.toUpperCase()}}t.log.logMessage=function(E){for(var u=e[E.level].index,g=0;g<a.length;++g){var C=a[g];C.flags&t.log.NO_LEVEL_CHECK?C.f(E):u<=e[C.level].index&&C.f(C,E)}},t.log.prepareStandard=function(E){"standard"in E||(E.standard=e[E.level].name+" ["+E.category+"] "+E.message)},t.log.prepareFull=function(E){if(!("full"in E)){var u=[E.message];u=u.concat([]),E.full=t.util.format.apply(this,u)}},t.log.prepareStandardFull=function(E){"standardFull"in E||(t.log.prepareStandard(E),E.standardFull=E.standard)};var h=["error","warning","info","debug","verbose"];for(R=0;R<h.length;++R)!function(u){t.log[u]=function(g,C){var m=Array.prototype.slice.call(arguments).slice(2);t.log.logMessage({timestamp:new Date,level:u,category:g,message:C,arguments:m})}}(h[R]);if(t.log.makeLogger=function(E){var u={flags:0,f:E};return t.log.setLevel(u,"none"),u},t.log.setLevel=function(E,u){var g=!1;if(E&&!(E.flags&t.log.LEVEL_LOCKED))for(var C=0;C<t.log.levels.length;++C)if(u==t.log.levels[C]){E.level=u,g=!0;break}return g},t.log.lock=function(E,u){typeof u>"u"||u?E.flags|=t.log.LEVEL_LOCKED:E.flags&=~t.log.LEVEL_LOCKED},t.log.addLogger=function(E){a.push(E)},typeof console<"u"&&"log"in console){var r;if(console.error&&console.warn&&console.info&&console.debug){var v={error:console.error,warning:console.warn,info:console.info,debug:console.debug,verbose:console.debug},l=function(E,u){t.log.prepareStandard(u);var g=v[u.level],C=[u.standard];C=C.concat(u.arguments.slice()),g.apply(console,C)};r=t.log.makeLogger(l)}else l=function(u,g){t.log.prepareStandardFull(g),console.log(g.standardFull)},r=t.log.makeLogger(l);t.log.setLevel(r,"debug"),t.log.addLogger(r),d=r}else console={log:function(){}};if(null!==d&&typeof window<"u"&&window.location){var S=new URL(window.location.href).searchParams;S.has("console.level")&&t.log.setLevel(d,S.get("console.level").slice(-1)[0]),S.has("console.lock")&&"true"==S.get("console.lock").slice(-1)[0]&&t.log.lock(d)}t.log.consoleLogger=d}(),function un(){Na||(Na=1,nt(),zt(),St(),qr(),Sa())}(),ca(),Jt(),yt(),ea(),da(),function cn(){if(Ra)return Sr.exports;Ra=1;var t=ue();lt(),rt(),At(),pt(),yt(),oa(),Ze(),fe(),yr();var e=t.asn1,a=Sr.exports=t.pkcs7=t.pkcs7||{};function d(u){var g={},C=[];if(!e.validate(u,a.asn1.recipientInfoValidator,g,C)){var m=new Error("Cannot read PKCS#7 RecipientInfo. ASN.1 object is not an PKCS#7 RecipientInfo.");throw m.errors=C,m}return{version:g.version.charCodeAt(0),issuer:t.pki.RDNAttributesAsArray(g.issuer),serialNumber:t.util.createBuffer(g.serial).toHex(),encryptedContent:{algorithm:e.derToOid(g.encAlgorithm),parameter:g.encParameter?g.encParameter.value:void 0,content:g.encKey}}}function R(u){return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(u.version).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[t.pki.distinguishedNameToAsn1({attributes:u.issuer}),e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,t.util.hexToBytes(u.serialNumber))]),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.encryptedContent.algorithm).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")]),e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,u.encryptedContent.content)])}function h(u){for(var g=[],C=0;C<u.length;++C)g.push(R(u[C]));return g}function r(u){var g=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(u.version).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[t.pki.distinguishedNameToAsn1({attributes:u.issuer}),e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,t.util.hexToBytes(u.serialNumber))]),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.digestAlgorithm).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")])]);if(u.authenticatedAttributesAsn1&&g.value.push(u.authenticatedAttributesAsn1),g.value.push(e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.signatureAlgorithm).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")])),g.value.push(e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,u.signature)),u.unauthenticatedAttributes.length>0){for(var C=e.create(e.Class.CONTEXT_SPECIFIC,1,!0,[]),m=0;m<u.unauthenticatedAttributes.length;++m)C.values.push(l(u.unauthenticatedAttributes[m]));g.value.push(C)}return g}function l(u){var g;if(u.type===t.pki.oids.contentType)g=e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.value).getBytes());else if(u.type===t.pki.oids.messageDigest)g=e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,u.value.bytes());else if(u.type===t.pki.oids.signingTime){var C=new Date("1950-01-01T00:00:00Z"),m=new Date("2050-01-01T00:00:00Z"),s=u.value;if("string"==typeof s){var f=Date.parse(s);s=isNaN(f)?13===s.length?e.utcTimeToDate(s):e.generalizedTimeToDate(s):new Date(f)}g=s>=C&&s<m?e.create(e.Class.UNIVERSAL,e.Type.UTCTIME,!1,e.dateToUtcTime(s)):e.create(e.Class.UNIVERSAL,e.Type.GENERALIZEDTIME,!1,e.dateToGeneralizedTime(s))}return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.type).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[g])])}function S(u){return[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(t.pki.oids.data).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.algorithm).getBytes()),u.parameter?e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,u.parameter.getBytes()):void 0]),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,u.content.getBytes())])]}function D(u,g,C){var m={};if(!e.validate(g,C,m,[])){var f=new Error("Cannot read PKCS#7 message. ASN.1 object is not a supported PKCS#7 message.");throw f.errors=f,f}if(e.derToOid(m.contentType)!==t.pki.oids.data)throw new Error("Unsupported PKCS#7 message. Only wrapped ContentType Data supported.");if(m.encryptedContent){var I="";if(t.util.isArray(m.encryptedContent))for(var _=0;_<m.encryptedContent.length;++_){if(m.encryptedContent[_].type!==e.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting encrypted content constructed of only OCTET STRING objects.");I+=m.encryptedContent[_].value}else I=m.encryptedContent;u.encryptedContent={algorithm:e.derToOid(m.encAlgorithm),parameter:t.util.createBuffer(m.encParameter.value),content:t.util.createBuffer(I)}}if(m.content){if(I="",t.util.isArray(m.content))for(_=0;_<m.content.length;++_){if(m.content[_].type!==e.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting content constructed of only OCTET STRING objects.");I+=m.content[_].value}else I=m.content;u.content=t.util.createBuffer(I)}return u.version=m.version.charCodeAt(0),u.rawCapture=m,m}function E(u){if(void 0===u.encryptedContent.key)throw new Error("Symmetric key not available.");if(void 0===u.content){var g;switch(u.encryptedContent.algorithm){case t.pki.oids["aes128-CBC"]:case t.pki.oids["aes192-CBC"]:case t.pki.oids["aes256-CBC"]:g=t.aes.createDecryptionCipher(u.encryptedContent.key);break;case t.pki.oids.desCBC:case t.pki.oids["des-EDE3-CBC"]:g=t.des.createDecryptionCipher(u.encryptedContent.key);break;default:throw new Error("Unsupported symmetric cipher, OID "+u.encryptedContent.algorithm)}if(g.start(u.encryptedContent.parameter),g.update(u.encryptedContent.content),!g.finish())throw new Error("Symmetric decryption failed.");u.content=g.output}}return a.messageFromPem=function(u){var g=t.pem.decode(u)[0];if("PKCS7"!==g.type){var C=new Error('Could not convert PKCS#7 message from PEM; PEM header type is not "PKCS#7".');throw C.headerType=g.type,C}if(g.procType&&"ENCRYPTED"===g.procType.type)throw new Error("Could not convert PKCS#7 message from PEM; PEM is encrypted.");var m=e.fromDer(g.body);return a.messageFromAsn1(m)},a.messageToPem=function(u,g){var C={type:"PKCS7",body:e.toDer(u.toAsn1()).getBytes()};return t.pem.encode(C,{maxline:g})},a.messageFromAsn1=function(u){var g={},C=[];if(!e.validate(u,a.asn1.contentInfoValidator,g,C)){var m=new Error("Cannot read PKCS#7 message. ASN.1 object is not an PKCS#7 ContentInfo.");throw m.errors=C,m}var f,s=e.derToOid(g.contentType);switch(s){case t.pki.oids.envelopedData:f=a.createEnvelopedData();break;case t.pki.oids.encryptedData:f=a.createEncryptedData();break;case t.pki.oids.signedData:f=a.createSignedData();break;default:throw new Error("Cannot read PKCS#7 message. ContentType with OID "+s+" is not (yet) supported.")}return f.fromAsn1(g.content.value[0]),f},a.createSignedData=function(){var u=null;return u={type:t.pki.oids.signedData,version:1,certificates:[],crls:[],signers:[],digestAlgorithmIdentifiers:[],contentInfo:null,signerInfos:[],fromAsn1:function(m){if(D(u,m,a.asn1.signedDataValidator),u.certificates=[],u.crls=[],u.digestAlgorithmIdentifiers=[],u.contentInfo=null,u.signerInfos=[],u.rawCapture.certificates)for(var s=u.rawCapture.certificates.value,f=0;f<s.length;++f)u.certificates.push(t.pki.certificateFromAsn1(s[f]))},toAsn1:function(){u.contentInfo||u.sign();for(var m=[],s=0;s<u.certificates.length;++s)m.push(t.pki.certificateToAsn1(u.certificates[s]));var f=[],y=e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(u.version).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,u.digestAlgorithmIdentifiers),u.contentInfo])]);return m.length>0&&y.value[0].value.push(e.create(e.Class.CONTEXT_SPECIFIC,0,!0,m)),f.length>0&&y.value[0].value.push(e.create(e.Class.CONTEXT_SPECIFIC,1,!0,f)),y.value[0].value.push(e.create(e.Class.UNIVERSAL,e.Type.SET,!0,u.signerInfos)),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.type).getBytes()),y])},addSigner:function(m){var s=m.issuer,f=m.serialNumber;if(m.certificate){var y=m.certificate;"string"==typeof y&&(y=t.pki.certificateFromPem(y)),s=y.issuer.attributes,f=y.serialNumber}var I=m.key;if(!I)throw new Error("Could not add PKCS#7 signer; no private key specified.");"string"==typeof I&&(I=t.pki.privateKeyFromPem(I));var _=m.digestAlgorithm||t.pki.oids.sha1;switch(_){case t.pki.oids.sha1:case t.pki.oids.sha256:case t.pki.oids.sha384:case t.pki.oids.sha512:case t.pki.oids.md5:break;default:throw new Error("Could not add PKCS#7 signer; unknown message digest algorithm: "+_)}var p=m.authenticatedAttributes||[];if(p.length>0){for(var i=!1,n=!1,B=0;B<p.length;++B){var U=p[B];if(i||U.type!==t.pki.oids.contentType){if(n||U.type!==t.pki.oids.messageDigest);else if(n=!0,i)break}else if(i=!0,n)break}if(!i||!n)throw new Error("Invalid signer.authenticatedAttributes. If signer.authenticatedAttributes is specified, then it must contain at least two attributes, PKCS #9 content-type and PKCS #9 message-digest.")}u.signers.push({key:I,version:1,issuer:s,serialNumber:f,digestAlgorithm:_,signatureAlgorithm:t.pki.oids.rsaEncryption,signature:null,authenticatedAttributes:p,unauthenticatedAttributes:[]})},sign:function(m){var s;if(m=m||{},"object"==typeof u.content&&null!==u.contentInfo||(u.contentInfo=e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(t.pki.oids.data).getBytes())]),!("content"in u))||(u.content instanceof t.util.ByteBuffer?s=u.content.bytes():"string"==typeof u.content&&(s=t.util.encodeUtf8(u.content)),m.detached?u.detachedContent=e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,s):u.contentInfo.value.push(e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.OCTETSTRING,!1,s)]))),0!==u.signers.length){var f=function g(){for(var m={},s=0;s<u.signers.length;++s){var f=u.signers[s];(y=f.digestAlgorithm)in m||(m[y]=t.md[t.pki.oids[y]].create()),f.md=0===f.authenticatedAttributes.length?m[y]:t.md[t.pki.oids[y]].create()}for(var y in u.digestAlgorithmIdentifiers=[],m)u.digestAlgorithmIdentifiers.push(e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(y).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.NULL,!1,"")]));return m}();!function C(m){var s;if(!(s=u.detachedContent?u.detachedContent:(s=u.contentInfo.value[1]).value[0]))throw new Error("Could not sign PKCS#7 message; there is no content to sign.");var f=e.derToOid(u.contentInfo.value[0].value),y=e.toDer(s);for(var I in y.getByte(),e.getBerValueLength(y),y=y.getBytes(),m)m[I].start().update(y);for(var _=new Date,p=0;p<u.signers.length;++p){var i=u.signers[p];if(0===i.authenticatedAttributes.length){if(f!==t.pki.oids.data)throw new Error("Invalid signer; authenticatedAttributes must be present when the ContentInfo content type is not PKCS#7 Data.")}else{i.authenticatedAttributesAsn1=e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[]);for(var n=e.create(e.Class.UNIVERSAL,e.Type.SET,!0,[]),B=0;B<i.authenticatedAttributes.length;++B){var U=i.authenticatedAttributes[B];U.type===t.pki.oids.messageDigest?U.value=m[i.digestAlgorithm].digest():U.type===t.pki.oids.signingTime&&(U.value||(U.value=_)),n.value.push(l(U)),i.authenticatedAttributesAsn1.value.push(l(U))}y=e.toDer(n).getBytes(),i.md.start().update(y)}i.signature=i.key.sign(i.md,"RSASSA-PKCS1-V1_5")}u.signerInfos=function v(u){for(var g=[],C=0;C<u.length;++C)g.push(r(u[C]));return g}(u.signers)}(f)}},verify:function(){throw new Error("PKCS#7 signature verification not yet implemented.")},addCertificate:function(m){"string"==typeof m&&(m=t.pki.certificateFromPem(m)),u.certificates.push(m)},addCertificateRevokationList:function(m){throw new Error("PKCS#7 CRL support not yet implemented.")}}},a.createEncryptedData=function(){var u=null;return u={type:t.pki.oids.encryptedData,version:0,encryptedContent:{algorithm:t.pki.oids["aes256-CBC"]},fromAsn1:function(g){D(u,g,a.asn1.encryptedDataValidator)},decrypt:function(g){void 0!==g&&(u.encryptedContent.key=g),E(u)}}},a.createEnvelopedData=function(){var u=null;return u={type:t.pki.oids.envelopedData,version:0,recipients:[],encryptedContent:{algorithm:t.pki.oids["aes256-CBC"]},fromAsn1:function(g){var C=D(u,g,a.asn1.envelopedDataValidator);u.recipients=function T(u){for(var g=[],C=0;C<u.length;++C)g.push(d(u[C]));return g}(C.recipientInfos.value)},toAsn1:function(){return e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.OID,!1,e.oidToDer(u.type).getBytes()),e.create(e.Class.CONTEXT_SPECIFIC,0,!0,[e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,[e.create(e.Class.UNIVERSAL,e.Type.INTEGER,!1,e.integerToDer(u.version).getBytes()),e.create(e.Class.UNIVERSAL,e.Type.SET,!0,h(u.recipients)),e.create(e.Class.UNIVERSAL,e.Type.SEQUENCE,!0,S(u.encryptedContent))])])])},findRecipient:function(g){for(var C=g.issuer.attributes,m=0;m<u.recipients.length;++m){var s=u.recipients[m],f=s.issuer;if(s.serialNumber===g.serialNumber&&f.length===C.length){for(var y=!0,I=0;I<C.length;++I)if(f[I].type!==C[I].type||f[I].value!==C[I].value){y=!1;break}if(y)return s}}return null},decrypt:function(g,C){if(void 0===u.encryptedContent.key&&void 0!==g&&void 0!==C)switch(g.encryptedContent.algorithm){case t.pki.oids.rsaEncryption:case t.pki.oids.desCBC:var m=C.decrypt(g.encryptedContent.content);u.encryptedContent.key=t.util.createBuffer(m);break;default:throw new Error("Unsupported asymmetric cipher, OID "+g.encryptedContent.algorithm)}E(u)},addRecipient:function(g){u.recipients.push({version:0,issuer:g.issuer.attributes,serialNumber:g.serialNumber,encryptedContent:{algorithm:t.pki.oids.rsaEncryption,key:g.publicKey}})},encrypt:function(g,C){if(void 0===u.encryptedContent.content){var m,s,f;switch(g=g||u.encryptedContent.key,C=C||u.encryptedContent.algorithm){case t.pki.oids["aes128-CBC"]:m=16,s=16,f=t.aes.createEncryptionCipher;break;case t.pki.oids["aes192-CBC"]:m=24,s=16,f=t.aes.createEncryptionCipher;break;case t.pki.oids["aes256-CBC"]:m=32,s=16,f=t.aes.createEncryptionCipher;break;case t.pki.oids["des-EDE3-CBC"]:m=24,s=8,f=t.des.createEncryptionCipher;break;default:throw new Error("Unsupported symmetric cipher, OID "+C)}if(void 0===g)g=t.util.createBuffer(t.random.getBytes(m));else if(g.length()!=m)throw new Error("Symmetric key has wrong length; got "+g.length()+" bytes, expected "+m+".");u.encryptedContent.algorithm=C,u.encryptedContent.key=g,u.encryptedContent.parameter=t.util.createBuffer(t.random.getBytes(s));var y=f(g);if(y.start(u.encryptedContent.parameter.copy()),y.update(u.content),!y.finish())throw new Error("Symmetric encryption failed.");u.encryptedContent.content=y.output}for(var I=0;I<u.recipients.length;++I){var _=u.recipients[I];if(void 0===_.encryptedContent.content){if(_.encryptedContent.algorithm!==t.pki.oids.rsaEncryption)throw new Error("Unsupported asymmetric cipher, OID "+_.encryptedContent.algorithm);_.encryptedContent.content=_.encryptedContent.key.encrypt(u.encryptedContent.key.data)}}}}},Sr.exports}(),ga(),ra(),zr(),dr(),Ze(),Wr(),function ln(){if(wa)return Tr.exports;wa=1;var t=ue();lt(),Et(),zt(),St(),fe();var e=Tr.exports=t.ssh=t.ssh||{};function a(T,h){var r=h.toString(16);r[0]>="8"&&(r="00"+r);var v=t.util.hexToBytes(r);T.putInt32(v.length),T.putBytes(v)}function d(T,h){T.putInt32(h.length),T.putString(h)}function R(){for(var T=t.md.sha1.create(),h=arguments.length,r=0;r<h;++r)T.update(arguments[r]);return T.digest()}e.privateKeyToPutty=function(T,h,r){var v="ssh-rsa",l=""===(h=h||"")?"none":"aes256-cbc",S="PuTTY-User-Key-File-2: "+v+"\r\n";S+="Encryption: "+l+"\r\n",S+="Comment: "+(r=r||"")+"\r\n";var D=t.util.createBuffer();d(D,v),a(D,T.e),a(D,T.n);var E=t.util.encode64(D.bytes(),64),u=Math.floor(E.length/66)+1;S+="Public-Lines: "+u+"\r\n",S+=E;var C,g=t.util.createBuffer();if(a(g,T.d),a(g,T.p),a(g,T.q),a(g,T.qInv),h){var m=g.length()+16-1;m-=m%16;var s=R(g.bytes());s.truncate(s.length()-m+g.length()),g.putBuffer(s);var f=t.util.createBuffer();f.putBuffer(R("\0\0\0\0",h)),f.putBuffer(R("\0\0\0\x01",h));var y=t.aes.createEncryptionCipher(f.truncate(8),"CBC");y.start(t.util.createBuffer().fillWithByte(0,16)),y.update(g.copy()),y.finish();var I=y.output;I.truncate(16),C=t.util.encode64(I.bytes(),64)}else C=t.util.encode64(g.bytes(),64);S+="\r\nPrivate-Lines: "+(u=Math.floor(C.length/66)+1)+"\r\n",S+=C;var _=R("putty-private-key-file-mac-key",h),p=t.util.createBuffer();d(p,v),d(p,l),d(p,r),p.putInt32(D.length()),p.putBuffer(D),p.putInt32(g.length()),p.putBuffer(g);var i=t.hmac.create();return i.start("sha1",_),i.update(p.bytes()),S+"\r\nPrivate-MAC: "+i.digest().toHex()+"\r\n"},e.publicKeyToOpenSSH=function(T,h){var r="ssh-rsa";h=h||"";var v=t.util.createBuffer();return d(v,r),a(v,T.e),a(v,T.n),r+" "+t.util.encode64(v.bytes())+" "+h},e.privateKeyToOpenSSH=function(T,h){return h?t.pki.encryptRsaPrivateKey(T,h,{legacy:!0,algorithm:"aes128"}):t.pki.privateKeyToPem(T)},e.getPublicKeyFingerprint=function(T,h){var r=(h=h||{}).md||t.md.md5.create(),l=t.util.createBuffer();d(l,"ssh-rsa"),a(l,T.e),a(l,T.n),r.start(),r.update(l.getBytes());var S=r.digest();if("hex"===h.encoding){var D=S.toHex();return h.delimiter?D.match(/.{2}/g).join(h.delimiter):D}if("binary"===h.encoding)return S.getBytes();if(h.encoding)throw new Error('Unknown encoding "'+h.encoding+'".');return S}}(),ma(),fe()),_a}();const it=Wa(La),hn=La.md.sha256,Ua="AES-CBC",Oa=t=>hn.create().update(t).digest(),Pa=t=>{const e=Oa(t).getBytes();return a=>it.util.createBuffer(e).getBytes(a)},Tt=t=>(t=>Oa(t).toHex())(t),mn={has:t=>!!window.sessionStorage.getItem(Tt(t)),getItem(t,e=!1){return this.has(t)?e?window.sessionStorage.getItem(Tt(t)):(t=>(t=>{try{const e=Pa(Nr()),a=it.cipher.createDecipher(Ua,e(16));return a.start({iv:e(16)}),a.update(it.util.createBuffer(it.util.decode64(t))),a.finish(),it.util.decodeUtf8(a.output.data)}catch(e){throw new Error(`Unable to decrypt the value from storage: (${e})`)}})(t))(window.sessionStorage.getItem(Tt(t))):null},setItem(t,e){window.sessionStorage.setItem(Tt(t),(t=>(t=>{const e=Pa(Nr()),a=it.util.encodeUtf8(t),d=it.cipher.createCipher(Ua,e(16));return d.start({iv:e(16)}),d.update(it.util.createBuffer(a)),d.finish(),it.util.encode64(d.output.data)})(t))(e))},removeItem(t){window.sessionStorage.removeItem(Tt(t))},clear(){window.sessionStorage.clear()}};var ht=(t=>(t.Clear="ADLClearEventBus",t.Dispatch="ADLDispatchEventBus",t))(ht||{}),gt=(t=>(t.Stored="ADLEventBusTopic",t.ClearAll="ADLClearAllTopics",t))(gt||{});class kt{static _readStoredMessages(e,a){try{const d=JSON.parse(this.storage(a).getItem(gt.Stored)??"[]");return this._deserializeMessageRegistry(new Map(d),e)}catch(d){return console.error("Error parsing the storage item:",d),new Map}}static _deserializeMessageRegistry(e,a){return e.get(a)&&(t=>{try{return JSON.parse(t),!0}catch{return!1}})(e.get(a))&&e.set(a,JSON.parse(e.get(a))),e}static _writeMessagesToStorage(e,a){const d=JSON.stringify(Array.from(e.entries()));this.storage(a).setItem(gt.Stored,d)}static storage(e){return e?window.sessionStorage:mn}static fetchTopic(e,a){return{topicName:e,topicValue:this._readStoredMessages(e,a).get(e)}}static storeTopic(e,a,d){setTimeout(()=>{const R=this._readStoredMessages(e,d);R.set(e,"object"==typeof a&&null!==a?JSON.stringify(a):a),this._writeMessagesToStorage(R,d)})}static clearTopic(e,a){setTimeout(()=>{const d=this._readStoredMessages(e,a);d.delete(e),this._writeMessagesToStorage(d,a)})}static clearAllTopics(e){setTimeout(()=>{this.storage(e).removeItem(gt.Stored)})}}class xa{constructor(e,a,d,R){We(this,"_topicName"),We(this,"_debug"),We(this,"_currentSubscriberId",""),We(this,"_eventDrivenStrategies"),We(this,"_subscribersRegistry"),this._topicName=e,this._subscribersRegistry=d,this._debug=a,this._eventDrivenStrategies=R}notifySubscribers(e){this._subscribersRegistry.forEach((a,d)=>{d.startsWith("del_")&&this._subscribersRegistry.delete(d),a(e)})}publish(e,a=!1){a&&kt.storeTopic(this._topicName,e,this._debug),this._eventDrivenStrategies.forEach(d=>{d.publish(this._topicName,e)})}subscribe(e,a={readPreviousStoredMessage:!0,onlyOnce:!1}){setTimeout(()=>{const d=kt.fetchTopic(this._topicName,this._debug);let R=!1;null!=d&&d.topicValue&&a.readPreviousStoredMessage&&(e(d),R=!0),this._currentSubscriberId=(a.onlyOnce?"del_":"sub_")+Qa(),(!R||!a.onlyOnce)&&this._subscribersRegistry.set(this._currentSubscriberId,e)})}clearStoredTopic(e){e?kt.clearTopic(e,this._debug):kt.clearAllTopics(this._debug)}unsubscribe(){this._eventDrivenStrategies.forEach(e=>{e.unsubscribe(this._topicName)}),this._subscribersRegistry.delete(this._currentSubscriberId)}}var ft=(t=>(t[t.CustomEvent=1]="CustomEvent",t[t.PostMessage=2]="PostMessage",t))(ft||{});class En{constructor(e){this._topicsRegistry=e}initializeListener(){window.addEventListener(ht.Clear,e=>{e instanceof CustomEvent&&(e.detail.topicName===gt.ClearAll?this._topicsRegistry.clear():this._topicsRegistry.delete(e.detail.topicName))}),window.addEventListener(ht.Dispatch,e=>{if(e instanceof CustomEvent){const a=this._topicsRegistry.get(e.detail.topicName);a&&a.notifySubscribers(e.detail)}})}publish(e,a){setTimeout(()=>{window.dispatchEvent(new CustomEvent(ht.Dispatch,{detail:{topicName:e,topicValue:a,eventDriven:ft.CustomEvent}}))})}unsubscribe(e){setTimeout(()=>{window.dispatchEvent(new CustomEvent(ht.Clear,{detail:{topicName:e}}))})}}class Sn{constructor(e){We(this,"_isIntoReactNativeWebView",!!window.ReactNativeWebView),this._topicsRegistry=e}initializeListener(){window.addEventListener("message",e=>{if(e.origin===window.location.origin)if(e.data.operationType===ht.Clear)e.data.topicName===gt.ClearAll?this._topicsRegistry.clear():this._topicsRegistry.delete(e.data.topicName);else{const a=this._topicsRegistry.get(e.data.topicName);a&&a.notifySubscribers(e.data)}})}publish(e,a){this._postMessage({topicName:e,topicValue:a,eventDriven:ft.PostMessage})}unsubscribe(e){this._postMessage({operationType:ht.Clear,topicName:e})}_postMessage(e){var a;this._isIntoReactNativeWebView&&(null==(a=window.ReactNativeWebView)||a.postMessage(JSON.stringify(e))),window.postMessage(e,window.location.origin)}}class In{constructor(e,a){We(this,"_strategies",[]),0!=(e&Number(ft.CustomEvent))&&this._strategies.push(new En(a)),0!=(e&Number(ft.PostMessage))&&this._strategies.push(new Sn(a))}initialize(){this._strategies.forEach(e=>{e.initializeListener()})}getStrategies(){return this._strategies}}const Ka=class Ct{constructor(e,a){We(this,"_debug"),We(this,"_eventDrivenStrategies"),We(this,"_topicsRegistry",new Map),this._debug=e;const d=new In(a,this._topicsRegistry);d.initialize(),this._eventDrivenStrategies=d.getStrategies()}static getInstance(e=!1,a=ft.PostMessage){return Ct._instance||(Ct._instance=new Ct(e,a),window.ReactNativeWebView&&(window.ReactNativeWebView.EventBus=Ct._instance)),Ct._instance}accessTopic(e){return this._topicsRegistry.has(e)||this._topicsRegistry.set(e,new xa(e,this._debug,new Map,this._eventDrivenStrategies)),this._topicsRegistry.get(e)}};We(Ka,"_instance");let An=Ka}}]);