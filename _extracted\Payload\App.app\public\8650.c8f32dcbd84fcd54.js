(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8650,4900],{40484:(q,W,O)=>{O.d(W,{i:()=>m});var T=O(11253),I=O(64302),D=O(97582),E=O(70591);function z(o,s){return s?s(o):I.y.of()}function d(o){return"function"==typeof o?new m(o):o}function P(o){return o.request.length<=1}var m=function(){function o(s){s&&(this.request=s)}return o.empty=function(){return new o(function(){return I.y.of()})},o.from=function(s){return 0===s.length?o.empty():s.map(d).reduce(function(v,S){return v.concat(S)})},o.split=function(s,v,S){var _,B=d(v),N=d(S||new o(z));return _=P(B)&&P(N)?new o(function(V){return s(V)?B.request(V)||I.y.of():N.request(V)||I.y.of()}):new o(function(V,w){return s(V)?B.request(V,w)||I.y.of():N.request(V,w)||I.y.of()}),Object.assign(_,{left:B,right:N})},o.execute=function(s,v){return s.request(function k(o,s){var v=(0,D.pi)({},o);return Object.defineProperty(s,"setContext",{enumerable:!1,value:function(N){v=(0,D.pi)((0,D.pi)({},v),"function"==typeof N?N(v):N)}}),Object.defineProperty(s,"getContext",{enumerable:!1,value:function(){return(0,D.pi)({},v)}}),s}(v.context,function j(o){var s={variables:o.variables||{},extensions:o.extensions||{},operationName:o.operationName,query:o.query};return s.operationName||(s.operationName="string"!=typeof s.query?(0,E.rY)(s.query)||void 0:""),s}(function A(o){for(var s=["query","operationName","variables","extensions","context"],v=0,S=Object.keys(o);v<S.length;v++){var B=S[v];if(s.indexOf(B)<0)throw(0,T._K)(44,B)}return o}(v))))||I.y.of()},o.concat=function(s,v){var S=d(s);if(P(S))return!1!==globalThis.__DEV__&&T.kG.warn(36,S),S;var N,B=d(v);return N=P(B)?new o(function(_){return S.request(_,function(V){return B.request(V)||I.y.of()})||I.y.of()}):new o(function(_,V){return S.request(_,function(w){return B.request(w,V)||I.y.of()})||I.y.of()}),Object.assign(N,{left:S,right:B})},o.prototype.split=function(s,v,S){return this.concat(o.split(s,v,S||new o(z)))},o.prototype.concat=function(s){return o.concat(this,s)},o.prototype.request=function(s,v){throw(0,T._K)(37)},o.prototype.onError=function(s,v){if(v&&v.error)return v.error(s),!1;throw s},o.prototype.setOnError=function(s){return this.onError=s,this},o}()},19162:(q,W,O)=>{O.d(W,{L:()=>j,s:()=>E});var T=O(5058),I=O(38678),D=new WeakSet;function k(A){A.size<=(A.max||-1)||D.has(A)||(D.add(A),setTimeout(function(){A.clean(),D.delete(A)},100))}var E=function(A,z){var d=new T.k(A,z);return d.set=function(P,m){var o=T.k.prototype.set.call(this,P,m);return k(this),o},d},j=function(A,z){var d=new I.e(A,z);return d.set=function(P,m){var o=I.e.prototype.set.call(this,P,m);return k(this),o},d}},72905:(q,W,O)=>{O.d(W,{Kb:()=>A,q4:()=>j,su:()=>E,zP:()=>k});var T=O(97582),I=O(64171),D={};function k(_,V){D[_]=V}var E=!1!==globalThis.__DEV__?function d(){var _,V,w,g,b;if(!1===globalThis.__DEV__)throw new Error("only supported in development mode");return{limits:Object.fromEntries(Object.entries({parser:1e3,canonicalStringify:1e3,print:2e3,"documentTransform.cache":2e3,"queryManager.getDocumentInfo":2e3,"PersistedQueryLink.persistedQueryHashes":2e3,"fragmentRegistry.transform":2e3,"fragmentRegistry.lookup":1e3,"fragmentRegistry.findFragmentSpreads":4e3,"cache.fragmentQueryDocuments":1e3,"removeTypenameFromVariables.getVariableDefinitions":2e3,"inMemoryCache.maybeBroadcastWatch":5e3,"inMemoryCache.executeSelectionSet":5e4,"inMemoryCache.executeSubSelectedArray":1e4}).map(function(V){var w=V[0];return[w,I.Q[w]||V[1]]})),sizes:(0,T.pi)({print:null===(_=D.print)||void 0===_?void 0:_.call(D),parser:null===(V=D.parser)||void 0===V?void 0:V.call(D),canonicalStringify:null===(w=D.canonicalStringify)||void 0===w?void 0:w.call(D),links:N(this.link),queryManager:{getDocumentInfo:this.queryManager.transformCache.size,documentTransforms:S(this.queryManager.documentTransform)}},null===(b=(g=this.cache).getMemoryInternals)||void 0===b?void 0:b.call(g))}}:void 0,j=!1!==globalThis.__DEV__?function m(){var _=this.config.fragments;return(0,T.pi)((0,T.pi)({},P.apply(this)),{addTypenameDocumentTransform:S(this.addTypenameTransform),inMemoryCache:{executeSelectionSet:s(this.storeReader.executeSelectionSet),executeSubSelectedArray:s(this.storeReader.executeSubSelectedArray),maybeBroadcastWatch:s(this.maybeBroadcastWatch)},fragmentRegistry:{findFragmentSpreads:s(_?.findFragmentSpreads),lookup:s(_?.lookup),transform:s(_?.transform)}})}:void 0,A=!1!==globalThis.__DEV__?P:void 0;function P(){return{cache:{fragmentQueryDocuments:s(this.getFragmentDoc)}}}function s(_){return function o(_){return!!_&&"dirtyKey"in _}(_)?_.size:void 0}function v(_){return null!=_}function S(_){return B(_).map(function(V){return{cache:V}})}function B(_){return _?(0,T.ev)((0,T.ev)([s(_?.performWork)],B(_?.left),!0),B(_?.right),!0).filter(v):[]}function N(_){var V;return _?(0,T.ev)((0,T.ev)([null===(V=_?.getMemoryInternals)||void 0===V?void 0:V.call(_)],N(_?.left),!0),N(_?.right),!0).filter(v):[]}},64171:(q,W,O)=>{O.d(W,{Q:()=>k});var T=O(97582),I=O(11253),D=Symbol.for("apollo.cacheSize"),k=(0,T.pi)({},I.CO[D])},13395:(q,W,O)=>{O.d(W,{B:()=>k});var E,T=O(19162),I=O(64171),D=O(72905),k=Object.assign(function(d){return JSON.stringify(d,j)},{reset:function(){E=new T.L(I.Q.canonicalStringify||1e3)}});function j(z,d){if(d&&"object"==typeof d){var P=Object.getPrototypeOf(d);if(P===Object.prototype||null===P){var m=Object.keys(d);if(m.every(A))return d;var o=JSON.stringify(m),s=E.get(o);if(!s){m.sort();var v=JSON.stringify(m);s=E.get(v)||m,E.set(o,s),E.set(v,s)}var S=Object.create(P);return s.forEach(function(B){S[B]=d[B]}),S}}return d}function A(z,d,P){return 0===d||P[d-1]<=z}!1!==globalThis.__DEV__&&(0,D.zP)("canonicalStringify",function(){return E.size}),k.reset()},48561:(q,W,O)=>{O.d(W,{X:()=>I});var T=new Map;function I(D){var k=T.get(D)||1;return T.set(D,k+1),"".concat(D,":").concat(k,":").concat(Math.random().toString(36).slice(2))}},27062:(q,W,O)=>{function T(D){return null!==D&&"object"==typeof D}O.d(W,{s:()=>T})},69753:(q,W,O)=>{O.d(W,{v:()=>I});var T=O(48561);function I(D,k){void 0===k&&(k=0);var E=(0,T.X)("stringifyForDisplay");return JSON.stringify(D,function(j,A){return void 0===A?E:A},k).split(JSON.stringify(E)).join("<undefined>")}},11253:(q,W,O)=>{O.d(W,{CO:()=>k,kG:()=>A,wY:()=>D,_K:()=>z});var T=O(79292),I=O(56497);function D(v){try{return v()}catch{}}const k=D(function(){return globalThis})||D(function(){return window})||D(function(){return self})||D(function(){return global})||D(function(){return D.constructor("return this")()});var E=O(69753);function j(v){return function(S){for(var B=[],N=1;N<arguments.length;N++)B[N-1]=arguments[N];if("number"==typeof S){var _=S;(S=m(_))||(S=o(_,B),B=[])}v.apply(void 0,[S].concat(B))}}var A=Object.assign(function(S,B){for(var N=[],_=2;_<arguments.length;_++)N[_-2]=arguments[_];S||(0,T.kG)(S,m(B,N)||o(B,N))},{debug:j(T.kG.debug),log:j(T.kG.log),warn:j(T.kG.warn),error:j(T.kG.error)});function z(v){for(var S=[],B=1;B<arguments.length;B++)S[B-1]=arguments[B];return new T.ej(m(v,S)||o(v,S))}var d=Symbol.for("ApolloErrorMessageHandler_"+I.i);function P(v){if("string"==typeof v)return v;try{return(0,E.v)(v,2).slice(0,1e3)}catch{return"<non-serializable>"}}function m(v,S){if(void 0===S&&(S=[]),v)return k[d]&&k[d](v,S.map(P))}function o(v,S){if(void 0===S&&(S=[]),v)return"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({version:I.i,message:v,args:S.map(P)})))}globalThis},89661:(q,W,O)=>{O.d(W,{F:()=>k,Yk:()=>D,hi:()=>E});var T=O(97582),I=O(11253);function D(j,A){var z=A,d=[];return j.definitions.forEach(function(m){if("OperationDefinition"===m.kind)throw(0,I._K)(74,m.operation,m.name?" named '".concat(m.name.value,"'"):"");"FragmentDefinition"===m.kind&&d.push(m)}),typeof z>"u"&&((0,I.kG)(1===d.length,75,d.length),z=d[0].name.value),(0,T.pi)((0,T.pi)({},j),{definitions:(0,T.ev)([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:z}}]}}],j.definitions,!0)})}function k(j){void 0===j&&(j=[]);var A={};return j.forEach(function(z){A[z.name.value]=z}),A}function E(j,A){switch(j.kind){case"InlineFragment":return j;case"FragmentSpread":var z=j.name.value;if("function"==typeof A)return A(z);var d=A&&A[z];return(0,I.kG)(d,76,z),d||null;default:return null}}},70591:(q,W,O)=>{O.d(W,{$H:()=>k,A$:()=>D,O4:()=>P,iW:()=>A,kU:()=>j,p$:()=>d,pD:()=>z,rY:()=>E});var T=O(11253),I=O(97634);function D(m){(0,T.kG)(m&&"Document"===m.kind,77);var o=m.definitions.filter(function(s){return"FragmentDefinition"!==s.kind}).map(function(s){if("OperationDefinition"!==s.kind)throw(0,T._K)(78,s.kind);return s});return(0,T.kG)(o.length<=1,79,o.length),m}function k(m){return D(m),m.definitions.filter(function(o){return"OperationDefinition"===o.kind})[0]}function E(m){return m.definitions.filter(function(o){return"OperationDefinition"===o.kind&&!!o.name}).map(function(o){return o.name.value})[0]||null}function j(m){return m.definitions.filter(function(o){return"FragmentDefinition"===o.kind})}function A(m){var o=k(m);return(0,T.kG)(o&&"query"===o.operation,80),o}function z(m){(0,T.kG)("Document"===m.kind,81),(0,T.kG)(m.definitions.length<=1,82);var o=m.definitions[0];return(0,T.kG)("FragmentDefinition"===o.kind,83),o}function d(m){D(m);for(var o,s=0,v=m.definitions;s<v.length;s++){var S=v[s];if("OperationDefinition"===S.kind){var B=S.operation;if("query"===B||"mutation"===B||"subscription"===B)return S}"FragmentDefinition"===S.kind&&!o&&(o=S)}if(o)return o;throw(0,T._K)(84)}function P(m){var o=Object.create(null),s=m&&m.variableDefinitions;return s&&s.length&&s.forEach(function(v){v.defaultValue&&(0,I.vb)(o,v.variable.name,v.defaultValue)}),o}},97634:(q,W,O)=>{O.d(W,{Ao:()=>f,JW:()=>A,My:()=>r,NC:()=>b,PT:()=>g,Yk:()=>j,kQ:()=>E,qw:()=>K,u2:()=>C,vb:()=>N,vf:()=>_});var T=O(11253),I=O(27062),D=O(89661),k=O(13395);function E(t){return{__ref:String(t)}}function j(t){return Boolean(t&&"object"==typeof t&&"string"==typeof t.__ref)}function A(t){return(0,I.s)(t)&&"Document"===t.kind&&Array.isArray(t.definitions)}function N(t,u,c,x){if(function P(t){return"IntValue"===t.kind}(c)||function m(t){return"FloatValue"===t.kind}(c))t[u.value]=Number(c.value);else if(function d(t){return"BooleanValue"===t.kind}(c)||function z(t){return"StringValue"===t.kind}(c))t[u.value]=c.value;else if(function s(t){return"ObjectValue"===t.kind}(c)){var l={};c.fields.map(function(M){return N(l,M.name,M.value,x)}),t[u.value]=l}else if(function o(t){return"Variable"===t.kind}(c))t[u.value]=(x||{})[c.name.value];else if(function v(t){return"ListValue"===t.kind}(c))t[u.value]=c.values.map(function(M){var F={};return N(F,u,M,x),F[u.value]});else if(function S(t){return"EnumValue"===t.kind}(c))t[u.value]=c.value;else{if(!function B(t){return"NullValue"===t.kind}(c))throw(0,T._K)(85,u.value,c.kind);t[u.value]=null}}function _(t,u){var c=null;t.directives&&(c={},t.directives.forEach(function(l){c[l.name.value]={},l.arguments&&l.arguments.forEach(function(y){return N(c[l.name.value],y.name,y.value,u)})}));var x=null;return t.arguments&&t.arguments.length&&(x={},t.arguments.forEach(function(l){return N(x,l.name,l.value,u)})),g(t.name.value,x,c)}var V=["connection","include","skip","client","rest","export","nonreactive"],w=k.B,g=Object.assign(function(t,u,c){if(u&&c&&c.connection&&c.connection.key){if(c.connection.filter&&c.connection.filter.length>0){var x=c.connection.filter?c.connection.filter:[];x.sort();var l={};return x.forEach(function(F){l[F]=u[F]}),"".concat(c.connection.key,"(").concat(w(l),")")}return c.connection.key}var y=t;if(u){var M=w(u);y+="(".concat(M,")")}return c&&Object.keys(c).forEach(function(F){-1===V.indexOf(F)&&(c[F]&&Object.keys(c[F]).length?y+="@".concat(F,"(").concat(w(c[F]),")"):y+="@".concat(F))}),y},{setStringify:function(t){var u=w;return w=t,u}});function b(t,u){if(t.arguments&&t.arguments.length){var c={};return t.arguments.forEach(function(x){return N(c,x.name,x.value,u)}),c}return null}function C(t){return t.alias?t.alias.value:t.name.value}function K(t,u,c){for(var x,l=0,y=u.selections;l<y.length;l++)if(r(M=y[l])){if("__typename"===M.name.value)return t[C(M)]}else x?x.push(M):x=[M];if("string"==typeof t.__typename)return t.__typename;if(x)for(var F=0,H=x;F<H.length;F++){var M,e=K(t,(0,D.hi)(M=H[F],c).selectionSet,c);if("string"==typeof e)return e}}function r(t){return"Field"===t.kind}function f(t){return"InlineFragment"===t.kind}},56497:(q,W,O)=>{O.d(W,{i:()=>T});var T="3.11.8"},38678:(q,W,O)=>{function T(){}O.d(W,{e:()=>I});class I{constructor(k=1/0,E=T){this.max=k,this.dispose=E,this.map=new Map,this.newest=null,this.oldest=null}has(k){return this.map.has(k)}get(k){const E=this.getNode(k);return E&&E.value}get size(){return this.map.size}getNode(k){const E=this.map.get(k);if(E&&E!==this.newest){const{older:j,newer:A}=E;A&&(A.older=j),j&&(j.newer=A),E.older=this.newest,E.older.newer=E,E.newer=null,this.newest=E,E===this.oldest&&(this.oldest=A)}return E}set(k,E){let j=this.getNode(k);return j?j.value=E:(j={key:k,value:E,newer:null,older:this.newest},this.newest&&(this.newest.newer=j),this.newest=j,this.oldest=this.oldest||j,this.map.set(k,j),j.value)}clean(){for(;this.oldest&&this.map.size>this.max;)this.delete(this.oldest.key)}delete(k){const E=this.map.get(k);return!!E&&(E===this.newest&&(this.newest=E.older),E===this.oldest&&(this.oldest=E.newer),E.newer&&(E.newer.older=E.older),E.older&&(E.older.newer=E.newer),this.map.delete(k),this.dispose(E.value,k),!0)}}},5058:(q,W,O)=>{function T(){}O.d(W,{k:()=>A});const I=T,D=typeof WeakRef<"u"?WeakRef:function(z){return{deref:()=>z}},k=typeof WeakMap<"u"?WeakMap:Map,E=typeof FinalizationRegistry<"u"?FinalizationRegistry:function(){return{register:T,unregister:T}};class A{constructor(d=1/0,P=I){this.max=d,this.dispose=P,this.map=new k,this.newest=null,this.oldest=null,this.unfinalizedNodes=new Set,this.finalizationScheduled=!1,this.size=0,this.finalize=()=>{const m=this.unfinalizedNodes.values();for(let o=0;o<10024;o++){const s=m.next().value;if(!s)break;this.unfinalizedNodes.delete(s);const v=s.key;delete s.key,s.keyRef=new D(v),this.registry.register(v,s,s)}this.unfinalizedNodes.size>0?queueMicrotask(this.finalize):this.finalizationScheduled=!1},this.registry=new E(this.deleteNode.bind(this))}has(d){return this.map.has(d)}get(d){const P=this.getNode(d);return P&&P.value}getNode(d){const P=this.map.get(d);if(P&&P!==this.newest){const{older:m,newer:o}=P;o&&(o.older=m),m&&(m.newer=o),P.older=this.newest,P.older.newer=P,P.newer=null,this.newest=P,P===this.oldest&&(this.oldest=o)}return P}set(d,P){let m=this.getNode(d);return m?m.value=P:(m={key:d,value:P,newer:null,older:this.newest},this.newest&&(this.newest.newer=m),this.newest=m,this.oldest=this.oldest||m,this.scheduleFinalization(m),this.map.set(d,m),this.size++,m.value)}clean(){for(;this.oldest&&this.size>this.max;)this.deleteNode(this.oldest)}deleteNode(d){d===this.newest&&(this.newest=d.older),d===this.oldest&&(this.oldest=d.newer),d.newer&&(d.newer.older=d.older),d.older&&(d.older.newer=d.newer),this.size--;const P=d.key||d.keyRef&&d.keyRef.deref();this.dispose(d.value,P),d.keyRef?this.registry.unregister(d):this.unfinalizedNodes.delete(d),P&&this.map.delete(P)}delete(d){const P=this.map.get(d);return!!P&&(this.deleteNode(P),!0)}scheduleFinalization(d){this.unfinalizedNodes.add(d),this.finalizationScheduled||(this.finalizationScheduled=!0,queueMicrotask(this.finalize))}}},74900:(q,W,O)=>{O.r(W),O.d(W,{HttpBatchLink:()=>V,HttpBatchLinkHandler:()=>_,HttpLink:()=>B,HttpLinkHandler:()=>S});var T=O(50642),I=O(99877),D=O(61595),k=O(42168),E=O(71776),j=O(97582),A=O(40484),z=O(64302),d=function(){function w(g){var b=g.batchDebounce,C=g.batchInterval,K=g.batchMax,r=g.batchHandler,f=g.batchKey;this.batchesByKey=new Map,this.scheduledBatchTimerByKey=new Map,this.batchDebounce=b,this.batchInterval=C,this.batchMax=K||0,this.batchHandler=r,this.batchKey=f||function(){return""}}return w.prototype.enqueueRequest=function(g){var b=this,C=(0,j.pi)((0,j.pi)({},g),{next:[],error:[],complete:[],subscribers:new Set}),K=this.batchKey(g.operation);return C.observable||(C.observable=new z.y(function(r){var f=b.batchesByKey.get(K);f||b.batchesByKey.set(K,f=new Set);var t=0===f.size,u=0===C.subscribers.size;return C.subscribers.add(r),u&&f.add(C),r.next&&C.next.push(r.next.bind(r)),r.error&&C.error.push(r.error.bind(r)),r.complete&&C.complete.push(r.complete.bind(r)),(t||b.batchDebounce)&&b.scheduleQueueConsumption(K),f.size===b.batchMax&&b.consumeQueue(K),function(){var c;C.subscribers.delete(r)&&C.subscribers.size<1&&f.delete(C)&&f.size<1&&(b.consumeQueue(K),null===(c=f.subscription)||void 0===c||c.unsubscribe())}})),C.observable},w.prototype.consumeQueue=function(g){void 0===g&&(g="");var b=this.batchesByKey.get(g);if(this.batchesByKey.delete(g),b&&b.size){var C=[],K=[],r=[],f=[],t=[],u=[];b.forEach(function(l){C.push(l.operation),K.push(l.forward),r.push(l.observable),f.push(l.next),t.push(l.error),u.push(l.complete)});var c=this.batchHandler(C,K)||z.y.of(),x=function(l){t.forEach(function(y){y&&y.forEach(function(M){return M(l)})})};return b.subscription=c.subscribe({next:function(l){if(Array.isArray(l)||(l=[l]),f.length!==l.length){var y=new Error("server returned results with length ".concat(l.length,", expected length of ").concat(f.length));return y.result=l,x(y)}l.forEach(function(M,F){f[F]&&f[F].forEach(function(H){return H(M)})})},error:x,complete:function(){u.forEach(function(l){l&&l.forEach(function(y){return y()})})}}),r}},w.prototype.scheduleQueueConsumption=function(g){var b=this;clearTimeout(this.scheduledBatchTimerByKey.get(g)),this.scheduledBatchTimerByKey.set(g,setTimeout(function(){b.consumeQueue(g),b.scheduledBatchTimerByKey.delete(g)},this.batchInterval))},w}(),P=function(w){function g(b){var C=w.call(this)||this,K=b||{},f=K.batchInterval,u=K.batchMax,x=K.batchHandler,y=K.batchKey;return C.batcher=new d({batchDebounce:K.batchDebounce,batchInterval:void 0===f?10:f,batchMax:void 0===u?0:u,batchHandler:void 0===x?function(){return null}:x,batchKey:void 0===y?function(){return""}:y}),b.batchHandler.length<=1&&(C.request=function(F){return C.batcher.enqueueRequest({operation:F})}),C}return(0,j.ZT)(g,w),g.prototype.request=function(b,C){return this.batcher.enqueueRequest({operation:b,forward:C})},g}(A.i);const m=(w,g,b)=>{const C=-1!==["POST","PUT","PATCH"].indexOf(w.method.toUpperCase()),r=w.body.length;let t,f=w.options&&w.options.useMultipart;if(f){if(r)return new k.Observable(c=>c.error(new Error("File upload is not available when combined with Batching")));if(!C)return new k.Observable(c=>c.error(new Error("File upload is not available when GET is used")));if(!b)return new k.Observable(c=>c.error(new Error('To use File upload you need to pass "extractFiles" function from "extract-files" library to HttpLink\'s options')));t=b(w.body),f=!!t.files.size}let u={};if(r){if(!C)return new k.Observable(c=>c.error(new Error("Batching is not available for GET requests")));u={body:w.body}}else u=C?{body:f?t.clone:w.body}:{params:Object.keys(w.body).reduce((l,y)=>{const M=w.body[y];return l[y]=-1!==["variables","extensions"].indexOf(y.toLowerCase())?JSON.stringify(M):M,l},{})};if(f&&C){const c=new FormData;c.append("operations",JSON.stringify(u.body));const x={},l=t.files;let y=0;l.forEach(M=>{x[++y]=M}),c.append("map",JSON.stringify(x)),y=0,l.forEach((M,F)=>{c.append(++y+"",F,F.name)}),u.body=c}return g.request(w.method,w.url,{observe:"response",responseType:"json",reportProgress:!1,...u,...w.options})},o=(w,g)=>w&&g?g.keys().reduce((C,K)=>C.set(K,g.getAll(K)),w):g||w;function s(...w){const g=w.find(b=>typeof b<"u");return typeof g>"u"?w[w.length-1]:g}function v(w){let g=w.headers&&w.headers instanceof E.HttpHeaders?w.headers:new E.HttpHeaders(w.headers);if(w.clientAwareness){const{name:b,version:C}=w.clientAwareness;b&&!g.has("apollographql-client-name")&&(g=g.set("apollographql-client-name",b)),C&&!g.has("apollographql-client-version")&&(g=g.set("apollographql-client-version",C))}return g}class S extends D.ApolloLink{httpClient;options;requester;print=T.print;constructor(g,b){super(),this.httpClient=g,this.options=b,this.options.operationPrinter&&(this.print=this.options.operationPrinter),this.requester=C=>new D.Observable(K=>{const r=C.getContext(),f=(i,a)=>s(r[i],this.options[i],a);let t=f("method","POST");const u=f("includeQuery",!0),c=f("includeExtensions",!1),x=f("uri","graphql"),l=f("withCredentials"),y=f("useMultipart"),M=!0===this.options.useGETForQueries,F=C.query.definitions.some(i=>"OperationDefinition"===i.kind&&"query"===i.operation);M&&F&&(t="GET");const H={method:t,url:"function"==typeof x?x(C):x,body:{operationName:C.operationName,variables:C.variables},options:{withCredentials:l,useMultipart:y,headers:this.options.headers}};c&&(H.body.extensions=C.extensions),u&&(H.body.query=this.print(C.query));const e=v(r);H.options.headers=o(H.options.headers,e);const n=m(H,this.httpClient,this.options.extractFiles).subscribe({next:i=>{C.setContext({response:i}),K.next(i.body)},error:i=>K.error(i),complete:()=>K.complete()});return()=>{n.closed||n.unsubscribe()}})}request(g){return this.requester(g)}}let B=(()=>{class w{httpClient;constructor(b){this.httpClient=b}create(b){return new S(this.httpClient,b)}static \u0275fac=function(C){return new(C||w)(I.\u0275\u0275inject(E.HttpClient))};static \u0275prov=I.\u0275\u0275defineInjectable({token:w,factory:w.\u0275fac,providedIn:"root"})}return w})();class _ extends D.ApolloLink{httpClient;options;batcher;batchInterval;batchMax;print=T.print;constructor(g,b){super(),this.httpClient=g,this.options=b,this.batchInterval=b.batchInterval||10,this.batchMax=b.batchMax||10,this.options.operationPrinter&&(this.print=this.options.operationPrinter),this.batcher=new P({batchInterval:this.batchInterval,batchMax:this.batchMax,batchKey:b.batchKey||(r=>this.createBatchKey(r)),batchHandler:r=>new D.Observable(f=>{const t=this.createBody(r),u=this.createHeaders(r),{method:c,uri:x,withCredentials:l}=this.createOptions(r);if("function"==typeof x)throw new Error("Option 'uri' is a function, should be a string");const M=m({method:c,url:x,body:t,options:{withCredentials:l,headers:u}},this.httpClient,()=>{throw new Error("File upload is not available when combined with Batching")}).subscribe({next:F=>f.next(F.body),error:F=>f.error(F),complete:()=>f.complete()});return()=>{M.closed||M.unsubscribe()}})})}createOptions(g){const b=g[0].getContext();return{method:s(b.method,this.options.method,"POST"),uri:s(b.uri,this.options.uri,"graphql"),withCredentials:s(b.withCredentials,this.options.withCredentials)}}createBody(g){return g.map(b=>{const C=s(b.getContext().includeExtensions,this.options.includeExtensions,!1),K=s(b.getContext().includeQuery,this.options.includeQuery,!0),r={operationName:b.operationName,variables:b.variables};return C&&(r.extensions=b.extensions),K&&(r.query=this.print(b.query)),r})}createHeaders(g){return g.reduce((b,C)=>o(b,C.getContext().headers),v({headers:this.options.headers,clientAwareness:g[0]?.getContext()?.clientAwareness}))}createBatchKey(g){const b=g.getContext();if(b.skipBatching)return Math.random().toString(36).substr(2,9);const C=b.headers&&b.headers.keys().map(r=>b.headers.get(r)),K=JSON.stringify({includeQuery:b.includeQuery,includeExtensions:b.includeExtensions,headers:C});return s(b.uri,this.options.uri)+K}request(g){return this.batcher.request(g)}}let V=(()=>{class w{httpClient;constructor(b){this.httpClient=b}create(b){return new _(this.httpClient,b)}static \u0275fac=function(C){return new(C||w)(I.\u0275\u0275inject(E.HttpClient))};static \u0275prov=I.\u0275\u0275defineInjectable({token:w,factory:w.\u0275fac,providedIn:"root"})}return w})()},79292:(q,W,O)=>{O.d(W,{U6:()=>P,ej:()=>E,kG:()=>j});var T=O(97582),I="Invariant Violation",D=Object.setPrototypeOf,k=void 0===D?function(o,s){return o.__proto__=s,o}:D,E=function(o){function s(v){void 0===v&&(v=I);var S=o.call(this,"number"==typeof v?I+": "+v+" (see https://github.com/apollographql/invariant-packages)":v)||this;return S.framesToPop=1,S.name=I,k(S,s.prototype),S}return(0,T.ZT)(s,o),s}(Error);function j(o,s){if(!o)throw new E(s)}var o,A=["debug","log","warn","error","silent"],z=A.indexOf("log");function d(o){return function(){if(A.indexOf(o)>=z)return(console[o]||console.log).apply(console,arguments)}}function P(o){var s=A[z];return z=Math.max(0,A.indexOf(o)),s}(o=j||(j={})).debug=d("debug"),o.log=d("log"),o.warn=d("warn"),o.error=d("error")},64302:(q,W,O)=>{function D(r,f){(null==f||f>r.length)&&(f=r.length);for(var t=0,u=new Array(f);t<f;t++)u[t]=r[t];return u}function k(r,f){for(var t=0;t<f.length;t++){var u=f[t];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(r,u.key,u)}}function E(r,f,t){return f&&k(r.prototype,f),t&&k(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}O.d(W,{y:()=>K});var j=function(){return"function"==typeof Symbol},A=function(r){return j()&&Boolean(Symbol[r])},z=function(r){return A(r)?Symbol[r]:"@@"+r};j()&&!A("observable")&&(Symbol.observable=Symbol("observable"));var d=z("iterator"),P=z("observable"),m=z("species");function o(r,f){var t=r[f];if(null!=t){if("function"!=typeof t)throw new TypeError(t+" is not a function");return t}}function s(r){var f=r.constructor;return void 0!==f&&null===(f=f[m])&&(f=void 0),void 0!==f?f:K}function S(r){S.log?S.log(r):setTimeout(function(){throw r})}function B(r){Promise.resolve().then(function(){try{r()}catch(f){S(f)}})}function N(r){var f=r._cleanup;if(void 0!==f&&(r._cleanup=void 0,f))try{if("function"==typeof f)f();else{var t=o(f,"unsubscribe");t&&t.call(f)}}catch(u){S(u)}}function _(r){r._observer=void 0,r._queue=void 0,r._state="closed"}function w(r,f,t){r._state="running";var u=r._observer;try{var c=o(u,f);switch(f){case"next":c&&c.call(u,t);break;case"error":if(_(r),!c)throw t;c.call(u,t);break;case"complete":_(r),c&&c.call(u)}}catch(x){S(x)}"closed"===r._state?N(r):"running"===r._state&&(r._state="ready")}function g(r,f,t){if("closed"!==r._state){if("buffering"===r._state)return void r._queue.push({type:f,value:t});if("ready"!==r._state)return r._state="buffering",r._queue=[{type:f,value:t}],void B(function(){return function V(r){var f=r._queue;if(f){r._queue=void 0,r._state="ready";for(var t=0;t<f.length&&(w(r,f[t].type,f[t].value),"closed"!==r._state);++t);}}(r)});w(r,f,t)}}var b=function(){function r(t,u){this._cleanup=void 0,this._observer=t,this._queue=void 0,this._state="initializing";var c=new C(this);try{this._cleanup=u.call(void 0,c)}catch(x){c.error(x)}"initializing"===this._state&&(this._state="ready")}return r.prototype.unsubscribe=function(){"closed"!==this._state&&(_(this),N(this))},E(r,[{key:"closed",get:function(){return"closed"===this._state}}]),r}(),C=function(){function r(t){this._subscription=t}var f=r.prototype;return f.next=function(u){g(this._subscription,"next",u)},f.error=function(u){g(this._subscription,"error",u)},f.complete=function(){g(this._subscription,"complete")},E(r,[{key:"closed",get:function(){return"closed"===this._subscription._state}}]),r}(),K=function(){function r(t){if(!(this instanceof r))throw new TypeError("Observable cannot be called as a function");if("function"!=typeof t)throw new TypeError("Observable initializer must be a function");this._subscriber=t}var f=r.prototype;return f.subscribe=function(u){return("object"!=typeof u||null===u)&&(u={next:u,error:arguments[1],complete:arguments[2]}),new b(u,this._subscriber)},f.forEach=function(u){var c=this;return new Promise(function(x,l){if("function"==typeof u)var M=c.subscribe({next:function(F){try{u(F,y)}catch(H){l(H),M.unsubscribe()}},error:l,complete:x});else l(new TypeError(u+" is not a function"));function y(){M.unsubscribe(),x()}})},f.map=function(u){var c=this;if("function"!=typeof u)throw new TypeError(u+" is not a function");return new(s(this))(function(l){return c.subscribe({next:function(y){try{y=u(y)}catch(M){return l.error(M)}l.next(y)},error:function(y){l.error(y)},complete:function(){l.complete()}})})},f.filter=function(u){var c=this;if("function"!=typeof u)throw new TypeError(u+" is not a function");return new(s(this))(function(l){return c.subscribe({next:function(y){try{if(!u(y))return}catch(M){return l.error(M)}l.next(y)},error:function(y){l.error(y)},complete:function(){l.complete()}})})},f.reduce=function(u){var c=this;if("function"!=typeof u)throw new TypeError(u+" is not a function");var x=s(this),l=arguments.length>1,y=!1,F=arguments[1];return new x(function(H){return c.subscribe({next:function(e){var n=!y;if(y=!0,!n||l)try{F=u(F,e)}catch(i){return H.error(i)}else F=e},error:function(e){H.error(e)},complete:function(){if(!y&&!l)return H.error(new TypeError("Cannot reduce an empty sequence"));H.next(F),H.complete()}})})},f.concat=function(){for(var u=this,c=arguments.length,x=new Array(c),l=0;l<c;l++)x[l]=arguments[l];var y=s(this);return new y(function(M){var F,H=0;return function e(n){F=n.subscribe({next:function(i){M.next(i)},error:function(i){M.error(i)},complete:function(){H===x.length?(F=void 0,M.complete()):e(y.from(x[H++]))}})}(u),function(){F&&(F.unsubscribe(),F=void 0)}})},f.flatMap=function(u){var c=this;if("function"!=typeof u)throw new TypeError(u+" is not a function");var x=s(this);return new x(function(l){var y=[],M=c.subscribe({next:function(H){if(u)try{H=u(H)}catch(n){return l.error(n)}var e=x.from(H).subscribe({next:function(n){l.next(n)},error:function(n){l.error(n)},complete:function(){var n=y.indexOf(e);n>=0&&y.splice(n,1),F()}});y.push(e)},error:function(H){l.error(H)},complete:function(){F()}});function F(){M.closed&&0===y.length&&l.complete()}return function(){y.forEach(function(H){return H.unsubscribe()}),M.unsubscribe()}})},f[P]=function(){return this},r.from=function(u){var c="function"==typeof this?this:r;if(null==u)throw new TypeError(u+" is not an object");var x=o(u,P);if(x){var l=x.call(u);if(Object(l)!==l)throw new TypeError(l+" is not an object");return function v(r){return r instanceof K}(l)&&l.constructor===c?l:new c(function(y){return l.subscribe(y)})}if(A("iterator")&&(x=o(u,d)))return new c(function(y){B(function(){if(!y.closed){for(var F,M=function T(r,f){var t=typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=function I(r,f){if(r){if("string"==typeof r)return D(r,f);var t=Object.prototype.toString.call(r).slice(8,-1);if("Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return D(r,f)}}(r))||f&&r&&"number"==typeof r.length){t&&(r=t);var u=0;return function(){return u>=r.length?{done:!0}:{done:!1,value:r[u++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(x.call(u));!(F=M()).done;)if(y.next(F.value),y.closed)return;y.complete()}})});if(Array.isArray(u))return new c(function(y){B(function(){if(!y.closed){for(var M=0;M<u.length;++M)if(y.next(u[M]),y.closed)return;y.complete()}})});throw new TypeError(u+" is not observable")},r.of=function(){for(var u=arguments.length,c=new Array(u),x=0;x<u;x++)c[x]=arguments[x];return new("function"==typeof this?this:r)(function(y){B(function(){if(!y.closed){for(var M=0;M<c.length;++M)if(y.next(c[M]),y.closed)return;y.complete()}})})},E(r,null,[{key:m,get:function(){return this}}]),r}();j()&&Object.defineProperty(K,Symbol("extensions"),{value:{symbol:P,hostReportError:S},configurable:!0})},97582:(q,W,O)=>{O.d(W,{FC:()=>b,Jh:()=>s,KL:()=>K,ZT:()=>I,_T:()=>k,cy:()=>r,ev:()=>w,gn:()=>E,mG:()=>o,pi:()=>D,pr:()=>V,qq:()=>g});var T=function(e,n){return(T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var p in a)Object.prototype.hasOwnProperty.call(a,p)&&(i[p]=a[p])})(e,n)};function I(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}T(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var D=function(){return D=Object.assign||function(n){for(var i,a=1,p=arguments.length;a<p;a++)for(var h in i=arguments[a])Object.prototype.hasOwnProperty.call(i,h)&&(n[h]=i[h]);return n},D.apply(this,arguments)};function k(e,n){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&n.indexOf(a)<0&&(i[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var p=0;for(a=Object.getOwnPropertySymbols(e);p<a.length;p++)n.indexOf(a[p])<0&&Object.prototype.propertyIsEnumerable.call(e,a[p])&&(i[a[p]]=e[a[p]])}return i}function E(e,n,i,a){var R,p=arguments.length,h=p<3?n:null===a?a=Object.getOwnPropertyDescriptor(n,i):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)h=Reflect.decorate(e,n,i,a);else for(var U=e.length-1;U>=0;U--)(R=e[U])&&(h=(p<3?R(h):p>3?R(n,i,h):R(n,i))||h);return p>3&&h&&Object.defineProperty(n,i,h),h}function o(e,n,i,a){return new(i||(i=Promise))(function(h,R){function U(Q){try{L(a.next(Q))}catch(J){R(J)}}function Y(Q){try{L(a.throw(Q))}catch(J){R(J)}}function L(Q){Q.done?h(Q.value):function p(h){return h instanceof i?h:new i(function(R){R(h)})}(Q.value).then(U,Y)}L((a=a.apply(e,n||[])).next())})}function s(e,n){var a,p,h,i={label:0,sent:function(){if(1&h[0])throw h[1];return h[1]},trys:[],ops:[]},R=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return R.next=U(0),R.throw=U(1),R.return=U(2),"function"==typeof Symbol&&(R[Symbol.iterator]=function(){return this}),R;function U(L){return function(Q){return function Y(L){if(a)throw new TypeError("Generator is already executing.");for(;R&&(R=0,L[0]&&(i=0)),i;)try{if(a=1,p&&(h=2&L[0]?p.return:L[0]?p.throw||((h=p.return)&&h.call(p),0):p.next)&&!(h=h.call(p,L[1])).done)return h;switch(p=0,h&&(L=[2&L[0],h.value]),L[0]){case 0:case 1:h=L;break;case 4:return i.label++,{value:L[1],done:!1};case 5:i.label++,p=L[1],L=[0];continue;case 7:L=i.ops.pop(),i.trys.pop();continue;default:if(!(h=(h=i.trys).length>0&&h[h.length-1])&&(6===L[0]||2===L[0])){i=0;continue}if(3===L[0]&&(!h||L[1]>h[0]&&L[1]<h[3])){i.label=L[1];break}if(6===L[0]&&i.label<h[1]){i.label=h[1],h=L;break}if(h&&i.label<h[2]){i.label=h[2],i.ops.push(L);break}h[2]&&i.ops.pop(),i.trys.pop();continue}L=n.call(e,i)}catch(Q){L=[6,Q],p=0}finally{a=h=0}if(5&L[0])throw L[1];return{value:L[0]?L[1]:void 0,done:!0}}([L,Q])}}}function V(){for(var e=0,n=0,i=arguments.length;n<i;n++)e+=arguments[n].length;var a=Array(e),p=0;for(n=0;n<i;n++)for(var h=arguments[n],R=0,U=h.length;R<U;R++,p++)a[p]=h[R];return a}function w(e,n,i){if(i||2===arguments.length)for(var h,a=0,p=n.length;a<p;a++)(h||!(a in n))&&(h||(h=Array.prototype.slice.call(n,0,a)),h[a]=n[a]);return e.concat(h||Array.prototype.slice.call(n))}function g(e){return this instanceof g?(this.v=e,this):new g(e)}function b(e,n,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var p,a=i.apply(e,n||[]),h=[];return p=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),U("next"),U("throw"),U("return",function R(G){return function(X){return Promise.resolve(X).then(G,J)}}),p[Symbol.asyncIterator]=function(){return this},p;function U(G,X){a[G]&&(p[G]=function($){return new Promise(function(Z,ee){h.push([G,$,Z,ee])>1||Y(G,$)})},X&&(p[G]=X(p[G])))}function Y(G,X){try{!function L(G){G.value instanceof g?Promise.resolve(G.value.v).then(Q,J):te(h[0][2],G)}(a[G](X))}catch($){te(h[0][3],$)}}function Q(G){Y("next",G)}function J(G){Y("throw",G)}function te(G,X){G(X),h.shift(),h.length&&Y(h[0][0],h[0][1])}}function K(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=e[Symbol.asyncIterator];return n?n.call(e):(e=function B(e){var n="function"==typeof Symbol&&Symbol.iterator,i=n&&e[n],a=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&a>=e.length&&(e=void 0),{value:e&&e[a++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),i={},a("next"),a("throw"),a("return"),i[Symbol.asyncIterator]=function(){return this},i);function a(h){i[h]=e[h]&&function(R){return new Promise(function(U,Y){!function p(h,R,U,Y){Promise.resolve(Y).then(function(L){h({value:L,done:U})},R)}(U,Y,(R=e[h](R)).done,R.value)})}}}function r(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e}"function"==typeof SuppressedError&&SuppressedError}}]);