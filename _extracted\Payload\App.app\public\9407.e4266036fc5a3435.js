(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9407],{39407:(m,o,n)=>{n.r(o),n.d(o,{MboPaymentCreditCardModule:()=>M});var a=n(17007),l=n(78007),d=n(99877);const C=[{path:"",redirectTo:"destination",pathMatch:"full"},{path:"destination",loadChildren:()=>n.e(2107).then(n.bind(n,52107)).then(t=>t.MboPaymentCreditCardDestinationPageModule)},{path:"source",loadChildren:()=>n.e(9838).then(n.bind(n,39838)).then(t=>t.MboPaymentCreditCardSourcePageModule)},{path:"information",loadChildren:()=>n.e(496).then(n.bind(n,40496)).then(t=>t.MboPaymentCreditCardInformationPageModule)},{path:"select-amount",loadChildren:()=>n.e(4087).then(n.bind(n,14087)).then(t=>t.MboPaymentCreditCardSelectAmountPageModule)},{path:"amount",loadChildren:()=>n.e(8374).then(n.bind(n,38374)).then(t=>t.MboPaymentCreditCardAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(6123).then(n.bind(n,26123)).then(t=>t.MboPaymentCreditCardConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(4092).then(n.bind(n,74092)).then(t=>t.MboPaymentCreditCardResultPageModule)}];let M=(()=>{class t{}return t.\u0275fac=function(P){return new(P||t)},t.\u0275mod=d.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=d.\u0275\u0275defineInjector({imports:[a.CommonModule,l.RouterModule.forChild(C)]}),t})()}}]);