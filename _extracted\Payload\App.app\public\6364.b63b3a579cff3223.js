(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6364],{96364:(B,q,_)=>{_.r(q),_.d(q,{FilesystemWeb:()=>x});var c=_(15861),R=_(17737),D=_(61443);function P(h){const p=h.split("/").filter(e=>"."!==e),r=[];return p.forEach(e=>{".."===e&&r.length>0&&".."!==r[r.length-1]?r.pop():r.push(e)}),r.join("/")}let x=(()=>{class h extends R.WebPlugin{constructor(){var r;super(...arguments),r=this,this.DB_VERSION=1,this.DB_NAME="Disc",this._writeCmds=["add","put","delete"],this.downloadFile=function(){var e=(0,c.Z)(function*(n){var t,o;const d=(0,R.buildRequestInit)(n,n.webFetchExtra),i=yield fetch(n.url,d);let a;if(n.progress)if(i?.body){const u=i.body.getReader();let l=0;const f=[],w=i.headers.get("content-type"),b=parseInt(i.headers.get("content-length")||"0",10);for(;;){const{done:m,value:v}=yield u.read();if(m)break;f.push(v),l+=v?.length||0,r.notifyListeners("progress",{url:n.url,bytes:l,contentLength:b})}const y=new Uint8Array(l);let g=0;for(const m of f)typeof m>"u"||(y.set(m,g),g+=m.length);a=new Blob([y.buffer],{type:w||void 0})}else a=new Blob;else a=yield i.blob();return{path:(yield r.writeFile({path:n.path,directory:null!==(t=n.directory)&&void 0!==t?t:void 0,recursive:null!==(o=n.recursive)&&void 0!==o&&o,data:a})).uri,blob:a}});return function(n){return e.apply(this,arguments)}}()}initDb(){var r=this;return(0,c.Z)(function*(){if(void 0!==r._db)return r._db;if(!("indexedDB"in window))throw r.unavailable("This browser doesn't support IndexedDB");return new Promise((e,n)=>{const t=indexedDB.open(r.DB_NAME,r.DB_VERSION);t.onupgradeneeded=h.doUpgrade,t.onsuccess=()=>{r._db=t.result,e(t.result)},t.onerror=()=>n(t.error),t.onblocked=()=>{console.warn("db blocked")}})})()}static doUpgrade(r){const n=r.target.result;n.objectStoreNames.contains("FileStorage")&&n.deleteObjectStore("FileStorage"),n.createObjectStore("FileStorage",{keyPath:"path"}).createIndex("by_folder","folder")}dbRequest(r,e){var n=this;return(0,c.Z)(function*(){const t=-1!==n._writeCmds.indexOf(r)?"readwrite":"readonly";return n.initDb().then(o=>new Promise((d,i)=>{const u=o.transaction(["FileStorage"],t).objectStore("FileStorage")[r](...e);u.onsuccess=()=>d(u.result),u.onerror=()=>i(u.error)}))})()}dbIndexRequest(r,e,n){var t=this;return(0,c.Z)(function*(){const o=-1!==t._writeCmds.indexOf(e)?"readwrite":"readonly";return t.initDb().then(d=>new Promise((i,a)=>{const f=d.transaction(["FileStorage"],o).objectStore("FileStorage").index(r)[e](...n);f.onsuccess=()=>i(f.result),f.onerror=()=>a(f.error)}))})()}getPath(r,e){const n=void 0!==e?e.replace(/^[/]+|[/]+$/g,""):"";let t="";return void 0!==r&&(t+="/"+r),""!==e&&(t+="/"+n),t}clear(){var r=this;return(0,c.Z)(function*(){(yield r.initDb()).transaction(["FileStorage"],"readwrite").objectStore("FileStorage").clear()})()}readFile(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path),t=yield e.dbRequest("get",[n]);if(void 0===t)throw Error("File does not exist.");return{data:t.content?t.content:""}})()}writeFile(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path);let t=r.data;const o=r.encoding,d=r.recursive,i=yield e.dbRequest("get",[n]);if(i&&"directory"===i.type)throw Error("The supplied path is a directory.");const a=n.substr(0,n.lastIndexOf("/"));if(void 0===(yield e.dbRequest("get",[a]))){const f=a.indexOf("/",1);if(-1!==f){const w=a.substr(f);yield e.mkdir({path:w,directory:r.directory,recursive:d})}}if(!(o||t instanceof Blob||(t=t.indexOf(",")>=0?t.split(",")[1]:t,e.isBase64String(t))))throw Error("The supplied data is not valid base64 content.");const u=Date.now(),l={path:n,folder:a,type:"file",size:t instanceof Blob?t.size:t.length,ctime:u,mtime:u,content:t};return yield e.dbRequest("put",[l]),{uri:l.path}})()}appendFile(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path);let t=r.data;const o=r.encoding,d=n.substr(0,n.lastIndexOf("/")),i=Date.now();let a=i;const s=yield e.dbRequest("get",[n]);if(s&&"directory"===s.type)throw Error("The supplied path is a directory.");if(void 0===(yield e.dbRequest("get",[d]))){const f=d.indexOf("/",1);if(-1!==f){const w=d.substr(f);yield e.mkdir({path:w,directory:r.directory,recursive:!0})}}if(!o&&!e.isBase64String(t))throw Error("The supplied data is not valid base64 content.");if(void 0!==s){if(s.content instanceof Blob)throw Error("The occupied entry contains a Blob object which cannot be appended to.");t=void 0===s.content||o?s.content+t:btoa(atob(s.content)+atob(t)),a=s.ctime}const l={path:n,folder:d,type:"file",size:t.length,ctime:a,mtime:i,content:t};yield e.dbRequest("put",[l])})()}deleteFile(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path);if(void 0===(yield e.dbRequest("get",[n])))throw Error("File does not exist.");if(0!==(yield e.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(n)])).length)throw Error("Folder is not empty.");yield e.dbRequest("delete",[n])})()}mkdir(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path),t=r.recursive,o=n.substr(0,n.lastIndexOf("/")),d=(n.match(/\//g)||[]).length,i=yield e.dbRequest("get",[o]),a=yield e.dbRequest("get",[n]);if(1===d)throw Error("Cannot create Root directory");if(void 0!==a)throw Error("Current directory does already exist.");if(!t&&2!==d&&void 0===i)throw Error("Parent directory must exist");if(t&&2!==d&&void 0===i){const l=o.substr(o.indexOf("/",1));yield e.mkdir({path:l,directory:r.directory,recursive:t})}const s=Date.now(),u={path:n,folder:o,type:"directory",size:0,ctime:s,mtime:s};yield e.dbRequest("put",[u])})()}rmdir(r){var e=this;return(0,c.Z)(function*(){const{path:n,directory:t,recursive:o}=r,d=e.getPath(t,n),i=yield e.dbRequest("get",[d]);if(void 0===i)throw Error("Folder does not exist.");if("directory"!==i.type)throw Error("Requested path is not a directory");const a=yield e.readdir({path:n,directory:t});if(0!==a.files.length&&!o)throw Error("Folder is not empty");for(const s of a.files){const u=`${n}/${s.name}`;"file"===(yield e.stat({path:u,directory:t})).type?yield e.deleteFile({path:u,directory:t}):yield e.rmdir({path:u,directory:t,recursive:o})}yield e.dbRequest("delete",[d])})()}readdir(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path),t=yield e.dbRequest("get",[n]);if(""!==r.path&&void 0===t)throw Error("Folder does not exist.");const o=yield e.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(n)]);return{files:yield Promise.all(o.map(function(){var i=(0,c.Z)(function*(a){let s=yield e.dbRequest("get",[a]);return void 0===s&&(s=yield e.dbRequest("get",[a+"/"])),{name:a.substring(n.length+1),type:s.type,size:s.size,ctime:s.ctime,mtime:s.mtime,uri:s.path}});return function(a){return i.apply(this,arguments)}}()))}})()}getUri(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path);let t=yield e.dbRequest("get",[n]);return void 0===t&&(t=yield e.dbRequest("get",[n+"/"])),{uri:t?.path||n}})()}stat(r){var e=this;return(0,c.Z)(function*(){const n=e.getPath(r.directory,r.path);let t=yield e.dbRequest("get",[n]);if(void 0===t&&(t=yield e.dbRequest("get",[n+"/"])),void 0===t)throw Error("Entry does not exist.");return{type:t.type,size:t.size,ctime:t.ctime,mtime:t.mtime,uri:t.path}})()}rename(r){var e=this;return(0,c.Z)(function*(){yield e._copy(r,!0)})()}copy(r){var e=this;return(0,c.Z)(function*(){return e._copy(r,!1)})()}requestPermissions(){return(0,c.Z)(function*(){return{publicStorage:"granted"}})()}checkPermissions(){return(0,c.Z)(function*(){return{publicStorage:"granted"}})()}_copy(r,e=!1){var n=this;return(0,c.Z)(function*(){let{toDirectory:t}=r;const{to:o,from:d,directory:i}=r;if(!o||!d)throw Error("Both to and from must be provided");t||(t=i);const a=n.getPath(i,d),s=n.getPath(t,o);if(a===s)return{uri:s};if(function E(h,p){h=P(h),p=P(p);const r=h.split("/"),e=p.split("/");return h!==p&&r.every((n,t)=>n===e[t])}(a,s))throw Error("To path cannot contain the from path");let u;try{u=yield n.stat({path:o,directory:t})}catch{const y=o.split("/");y.pop();const g=y.join("/");if(y.length>0&&"directory"!==(yield n.stat({path:g,directory:t})).type)throw new Error("Parent directory of the to path is a file")}if(u&&"directory"===u.type)throw new Error("Cannot overwrite a directory with a file");const l=yield n.stat({path:d,directory:i}),f=function(){var b=(0,c.Z)(function*(y,g,m){const v=n.getPath(t,y),F=yield n.dbRequest("get",[v]);F.ctime=g,F.mtime=m,yield n.dbRequest("put",[F])});return function(g,m,v){return b.apply(this,arguments)}}(),w=l.ctime?l.ctime:Date.now();switch(l.type){case"file":{const b=yield n.readFile({path:d,directory:i});let y;e&&(yield n.deleteFile({path:d,directory:i})),!(b.data instanceof Blob)&&!n.isBase64String(b.data)&&(y=D.ez.UTF8);const g=yield n.writeFile({path:o,directory:t,data:b.data,encoding:y});return e&&(yield f(o,w,l.mtime)),g}case"directory":{if(u)throw Error("Cannot move a directory over an existing object");try{yield n.mkdir({path:o,directory:t,recursive:!1}),e&&(yield f(o,w,l.mtime))}catch{}const b=(yield n.readdir({path:d,directory:i})).files;for(const y of b)yield n._copy({from:`${d}/${y.name}`,to:`${o}/${y.name}`,directory:i,toDirectory:t},e);e&&(yield n.rmdir({path:d,directory:i}))}}return{uri:s}})()}isBase64String(r){try{return btoa(atob(r))==r}catch{return!1}}}return h._debug=!0,h})()},15861:(B,q,_)=>{function c(O,D,P,E,x,h,p){try{var r=O[h](p),e=r.value}catch(n){return void P(n)}r.done?D(e):Promise.resolve(e).then(E,x)}function R(O){return function(){var D=this,P=arguments;return new Promise(function(E,x){var h=O.apply(D,P);function p(e){c(h,E,x,p,r,"next",e)}function r(e){c(h,E,x,p,r,"throw",e)}p(void 0)})}}_.d(q,{Z:()=>R})}}]);