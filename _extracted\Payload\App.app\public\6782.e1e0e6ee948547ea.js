(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6782],{16782:(E,t,o)=>{o.r(t),o.d(t,{MboSecurityTravelerModule:()=>e});var d=o(17007),M=o(78007),n=o(99877);const a=[{path:"",loadChildren:()=>o.e(7624).then(o.bind(o,57624)).then(l=>l.MboSecurityTravelerHomePageModule)},{path:"register",loadChildren:()=>o.e(9181).then(o.bind(o,59181)).then(l=>l.MboSecurityTravelerRegisterPageModule)},{path:"history",loadChildren:()=>o.e(8493).then(o.bind(o,98493)).then(l=>l.MboSecurityTravelerHistoryPageModule)}];let e=(()=>{class l{}return l.\u0275fac=function(u){return new(u||l)},l.\u0275mod=n.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=n.\u0275\u0275defineInjector({imports:[d.CommonModule,M.RouterModule.forChild(a)]}),l})()}}]);