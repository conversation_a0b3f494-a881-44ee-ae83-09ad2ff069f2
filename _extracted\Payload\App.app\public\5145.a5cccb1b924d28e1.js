(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5145],{55145:(T,a,o)=>{o.r(a),o.d(a,{MboTransfiyaPendingSourcePageModule:()=>b});var u=o(17007),g=o(78007),l=o(79798),p=o(30263),m=o(15861),f=o(39904),v=o(95437),s=o(17698),e=o(99877),y=o(48774),h=o(4663);const c=f.Z6.TRANSFERS.TRANSFIYA.PENDING;let P=(()=>{class t{constructor(n,r,d){this.mboProvider=n,this.requestConfiguration=r,this.managerTransfiya=d,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_transfiya-pending-source_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.next(c.HOME)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(n){this.managerTransfiya.setProduct(n).when({success:()=>{this.mboProvider.navigation.next(c.CONFIRMATION)}})}initializatedConfiguration(){var n=this;return(0,m.Z)(function*(){(yield n.requestConfiguration.source()).when({success:({products:r})=>{n.products=r}},()=>{n.requesting=!1})})()}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(v.ZL),e.\u0275\u0275directiveInject(s.xt),e.\u0275\u0275directiveInject(s.Pm))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfiya-pending-source-page"]],decls:6,vars:3,consts:[[1,"mbo-transfiya-pending-source-page__content"],[1,"mbo-transfiya-pending-source-page__header"],["title","Origen","progress","75%",3,"rightAction"],[1,"mbo-transfiya-pending-source-page__body"],[3,"skeleton","products","select"]],template:function(n,r){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),e.\u0275\u0275listener("select",function(M){return r.onProduct(M)}),e.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas transferir hoy? "),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("rightAction",r.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",r.requesting)("products",r.products))},dependencies:[y.J,h.c],styles:["/*!\n * MBO TransfiyaPendingSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-pending-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-pending-source-page .mbo-transfiya-pending-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-pending-source-page .mbo-transfiya-pending-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),t})(),b=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[u.CommonModule,g.RouterModule.forChild([{path:"",component:P}]),p.Jx,l.cV]}),t})()}}]);