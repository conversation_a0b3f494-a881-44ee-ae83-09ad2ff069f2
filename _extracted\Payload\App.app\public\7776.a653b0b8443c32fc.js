(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7776],{27776:(u,t,n)=>{n.r(t),n.d(t,{MboTransfiyaAskModule:()=>s});var l=n(17007),d=n(78007),a=n(99877);const M=[{path:"",redirectTo:"destination",pathMatch:"full"},{path:"destination",loadChildren:()=>n.e(9804).then(n.bind(n,79804)).then(o=>o.MboTransfiyaAskDestinationPageModule)},{path:"source",loadChildren:()=>n.e(7233).then(n.bind(n,77233)).then(o=>o.MboTransfiyaAskSourcePageModule)},{path:"amount",loadChildren:()=>n.e(2004).then(n.bind(n,72004)).then(o=>o.MboTransfiyaAskAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(7536).then(n.bind(n,77536)).then(o=>o.MboTransfiyaAskConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(4179).then(n.bind(n,84179)).then(o=>o.MboTransfiyaAskResultPageModule)}];let s=(()=>{class o{}return o.\u0275fac=function(f){return new(f||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[l.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);