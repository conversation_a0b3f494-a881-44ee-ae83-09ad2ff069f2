(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1951],{91951:(T,d,n)=>{n.r(d),n.d(d,{MboTransferCelToCelSendModule:()=>e});var t=n(17007),C=n(78007),l=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(6374).then(n.bind(n,16374)).then(o=>o.MboCelToCelSendSourcePageModule)},{path:"destination",loadChildren:()=>n.e(4709).then(n.bind(n,94709)).then(o=>o.MboCelToCelSendDestinationPageModule)},{path:"account",loadChildren:()=>n.e(9957).then(n.bind(n,99957)).then(o=>o.MboCelToCelSendAccountPageModule)},{path:"amount",loadChildren:()=>n.e(9999).then(n.bind(n,9999)).then(o=>o.MboCelToCelSendAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(2485).then(n.bind(n,42485)).then(o=>o.MboCelToCelSendConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(6935).then(n.bind(n,36935)).then(o=>o.MboCelToCelSendResultPageModule)}];let e=(()=>{class o{}return o.\u0275fac=function(h){return new(h||o)},o.\u0275mod=l.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=l.\u0275\u0275defineInjector({imports:[t.CommonModule,C.RouterModule.forChild(M)]}),o})()}}]);