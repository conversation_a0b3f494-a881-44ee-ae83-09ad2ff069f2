(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2021],{6468:(T,s,t)=>{t.d(s,{Qz:()=>i,Zn:()=>A,ze:()=>N});var d=t(8834),m=t(40914),e=t(99877),f=t(2460),p=t(45542);const{MAX_CEL_TO_CEL:M}=m.R;let i=(()=>{class l{constructor(){this.phoneNumber="",this.limit=(0,d.b)({value:M})}ngBoccPortal(o){this.overlay=o}onSubmit(){this.overlay?.close(),setTimeout(()=>{this.overlay.destroy()},300)}}return l.\u0275fac=function(o){return new(o||l)},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-cel-to-cel-info-bluescreen"]],decls:21,vars:2,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_cel-to-cel-info-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4,"\xa1Importante!"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"p"),e.\u0275\u0275text(6,"Transferencias "),e.\u0275\u0275elementStart(7,"b"),e.\u0275\u0275text(8,"gratis"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(9," entre celulares del Grupo AVAL"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"ul",3)(11,"li",4),e.\u0275\u0275text(12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"li",4),e.\u0275\u0275text(14," El dinero se ver\xe1 reflejado de inmediato en su cuenta del Grupo AVAL. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"li",4),e.\u0275\u0275text(16),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(17,"div",5)(18,"button",6),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(19,"span"),e.\u0275\u0275text(20,"Continuar"),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(12),e.\u0275\u0275textInterpolate1(" El n\xfamero ",r.phoneNumber," esta vinculado a una o m\xe1s entidades AVAL. "),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" No requiere autorizaciones adicionales y puedes transferir un m\xe1ximo de $",r.limit," al d\xeda. "))},dependencies:[f.Z,p.P],styles:["mbo-cel-to-cel-info-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}\n"],encapsulation:2}),l})();t(17007),t(30263);var n=t(15861),u=t(39904),c=t(95437),b=t(57544),S=t(17758),E=t(60817);const g=u.Z6.TRANSFERS.CELTOCEL.SEND;let A=(()=>{class l{constructor(o,r,C){this.mboProvider=o,this.managerCelToCel=r,this.legalDocumentProvider=C,this.downloading=!1,this.termAndConditionControl=new b.FormControl(!1)}ngBoccPortal(o){this.portal=o}onSubmit(){var o=this;return(0,n.Z)(function*(){(yield o.managerCelToCel.approvedTyC()).when({success:()=>{o.mboProvider.navigation.next(g.SOURCE)}},()=>{o.portal?.close(),o.portal?.destroy()})})()}onTermAndConditions(){this.downloading||(this.downloading=!0,this.legalDocumentProvider.termsAndConditions().finally(()=>{this.downloading=!1}))}}return l.\u0275fac=function(o){return new(o||l)(e.\u0275\u0275directiveInject(c.ZL),e.\u0275\u0275directiveInject(S.Ey),e.\u0275\u0275directiveInject(c.uD))},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-cel-to-cel-tyc-modal"]],decls:14,vars:4,consts:[[1,"mbo-cel-to-cel-tyc-modal__content"],[1,"mbo-cel-to-cel-tyc-modal__body"],[1,"mbo-cel-to-cel-tyc-modal__title","smalltext-medium"],[1,"mbo-cel-to-cel-tyc-modal__message","body2-medium"],["elementId","chck_cel-to-cel-tyc-modal_accept",3,"formControl"],["id","lnk_cel-to-cel-modal_tyc",3,"click"],[1,"mbo-cel-to-cel-tyc-modal__footer"],["bocc-button","raised",3,"disabled","click"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),e.\u0275\u0275text(3," \xa1Te damos la bienvenida al servicio de transferencias inmediatas a n\xfameros celulares! "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",3),e.\u0275\u0275text(5," Para continuar debes "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-checkbox-label",4),e.\u0275\u0275text(7," Aceptar "),e.\u0275\u0275elementStart(8,"a",5),e.\u0275\u0275listener("click",function(){return r.onTermAndConditions()}),e.\u0275\u0275text(9," T\xe9rminos y condiciones. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(10,"div",6)(11,"button",7),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13,"Continuar"),e.\u0275\u0275elementEnd()()()()),2&o&&(e.\u0275\u0275advance(6),e.\u0275\u0275property("formControl",r.termAndConditionControl),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("downloading",r.downloading),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",!r.termAndConditionControl.value))},dependencies:[E.a,p.P],styles:["mbo-cel-to-cel-tyc-modal{position:relative;display:block;box-sizing:border-box}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__body bocc-checkbox-label a.downloading{pointer-events:none;opacity:.36}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__title{position:relative;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__footer{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-cel-to-cel-tyc-modal .mbo-cel-to-cel-tyc-modal__footer>button{width:100%}\n"],encapsulation:2}),l})();var x=t(66613);const{GENERIC:I,TRANSFIYA:B}=u.Z6.TRANSFERS,{MAX_CEL_TO_CEL:R}=m.R;let N=(()=>{class l{constructor(o){this.mboProvider=o,this.limit=(0,d.b)({value:R}),this.phoneNumber="",this.alertActions=[{id:"btn_transfiya-redirect-bluescreen_number",label:"Cambiar numero",click:()=>{this.portal?.destroy()}}]}ngBoccPortal(o){this.portal=o}onSubmit(){this.mboProvider.navigation.next(B.TRANSFER.AMOUNT),this.portal?.destroy()}onTransfer(){var o=this;return(0,n.Z)(function*(){o.mboProvider.navigation.next(I.SOURCE),o.portal?.destroy()})()}}return l.\u0275fac=function(o){return new(o||l)(e.\u0275\u0275directiveInject(c.ZL))},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-transfiya-redirect-bluescreen"]],decls:26,vars:4,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","warning"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],["bocc-theme","alert","icon","warning",3,"visible","actions"],[1,"bocc-bluescreen__footer"],["id","btn_transfiya-redirect-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfiya-redirect-bluescreen_go-transfer","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4,"\xa1Importante!"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"p"),e.\u0275\u0275text(6,"Transferencias entre celulares fuera del Grupo AVAL - TransfiYa"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),e.\u0275\u0275text(9," Le notificaremos a tu contacto el env\xedo del dinero por mensaje de texto. Si en 12 horas tu contacto no acepta el dinero, regresar\xe1 a tu cuenta. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"li",4),e.\u0275\u0275text(13," Tu contacto podr\xe1 recibir el dinero a trav\xe9s de la entidad bancaria que elija, siempre y cuando tenga activo el servicio de Transfiya. "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"bocc-alert",5),e.\u0275\u0275text(15," El n\xfamero "),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(18," no est\xe1 vinculado a ninguna entidad AVAL o no est\xe1 habilitado para este servicio. "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",6)(20,"button",7),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(21,"span"),e.\u0275\u0275text(22,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"button",8),e.\u0275\u0275listener("click",function(){return r.onTransfer()}),e.\u0275\u0275elementStart(24,"span"),e.\u0275\u0275text(25,"Ir a Transferencias entre cuentas"),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1(" Realiza hasta 15 transferencias al d\xeda que sumen un valor m\xe1ximo de $",r.limit,". "),e.\u0275\u0275advance(3),e.\u0275\u0275property("visible",!0)("actions",r.alertActions),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(r.phoneNumber))},dependencies:[f.Z,x.B,p.P],styles:["mbo-transfiya-redirect-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-transfiya-redirect-bluescreen bocc-alert{box-shadow:var(--z-bottom-darker-8)}mbo-transfiya-redirect-bluescreen bocc-alert .bocc-alert__message span{color:var(--color-amathyst-700)}mbo-transfiya-redirect-bluescreen bocc-alert .bocc-button{color:var(--color-blue-700)!important}\n"],encapsulation:2}),l})()},72021:(T,s,t)=>{t.r(s),t.d(s,{MboTransferCelToCelModule:()=>v});var d=t(17007),m=t(78007),e=t(15861),f=t(30263),p=t(6468),M=t(17758),i=t(99877);const y=[{path:"",loadChildren:()=>t.e(6767).then(t.bind(t,56767)).then(n=>n.MboTransferCelToCelHomePageModule)},{path:"send",loadChildren:()=>t.e(1951).then(t.bind(t,91951)).then(n=>n.MboTransferCelToCelSendModule),canActivate:[(()=>{class n{constructor(c,b){this.modalService=c,this.requestConfiguration=b}canActivate(){var c=this;return(0,e.Z)(function*(){return(yield c.requestConfiguration.allowAccess()).when({success:b=>(b||c.modalService.create(p.Zn).open(120),Promise.resolve(b)),failure:()=>Promise.resolve(!1)})})()}}return n.\u0275fac=function(c){return new(c||n)(i.\u0275\u0275inject(f.iM),i.\u0275\u0275inject(M.ZW))},n.\u0275prov=i.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()]}];let v=(()=>{class n{}return n.\u0275fac=function(c){return new(c||n)},n.\u0275mod=i.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=i.\u0275\u0275defineInjector({imports:[d.CommonModule,m.RouterModule.forChild(y)]}),n})()},40914:(T,s,t)=>{t.d(s,{R:()=>d,r:()=>m});const d={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},m={MIN_TRUSTFUND:2e5}}}]);