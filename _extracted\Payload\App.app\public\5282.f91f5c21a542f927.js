(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5282],{57929:(_,g,o)=>{o.d(g,{Be:()=>u,q3:()=>d,qQ:()=>h});const d=36,h=0,u=[{code:"0002015502010102125802CO5921DIEGO ANDRES CORREDOR49250103RBM0014CO.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124O2dhtQbToI1IP7xYOHkR1SUm0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099360041740013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064310002ES0121DIEGO ANDRES CORREDOR54061200006304C1DE",name:"DIEGO ANDRES CORREDOR",type:"onlyAccount"},{code:"000201550202010211560105802CO5922ALMACEN Y SASTRERIA *******************.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124bAK0LiWIA7a7GGPS3ZTGmmCv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601315238 DUITAMA8223010100014CO.COM.RBM.IVA503001099100104110013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573154033178070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122ALMACEN Y SASTRERIA IN6304EA05",name:"ALMACEN Y SASTRERIA IN",type:"onlyAccount"},{code:"000201550202010211560105802CO5917SACOS AZULES BOCC49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124cJbE3StP8jyiME/aY+8d/Qo80014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601505664 SAN PEDRO8223010100014CO.COM.RBM.IVA503001099353177830013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573108133170070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064270002ES0117SACOS AZULES BOCC6304C439",name:"SACOS AZULES BOCC",type:"onlyAccount"},{code:"00020101021249250014CO.COM.CRB.RED0103CRB50300013CO.COM.CRB.CU01090164845945204581453031705405150005802CO5914Cci*boxBurguer6011BOGOTA D.C.622501031530708000DE40808020080270016CO.COM.CRB.CANAL0103POS81250015CO.COM.CRB.CIVA01020282230014CO.COM.CRB.IVA0101083240015CO.COM.CRB.BASE0101084250015CO.COM.CRB.CINC01020285230014CO.COM.CRB.INC0101090300016CO.COM.CRB.TRXID010600015491260014CO.COM.CRB.SEC0104627c6304df69",name:"CCI BOX BURGER",type:"onlyCards"},{code:"00020155020201021256076750.005802CO5918PRUEBAS QR REDEBAN49250103RBM0014CO.COM.RBM.RED902701035360016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124oC4KvIdTb9ouPsgVLNsLPwj00014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.*********************.000015CO.COM.RBM.BASE62180708SRB0008208020084250102020015CO.COM.RBM.CINC52040004852601040.000014CO.COM.RBM.INC530317064280002ES0118PRUEBAS QR REDEBAN5405450006304FAD5",name:"PRUEBAS QR REDEBAN",type:"cardsAndAccounts"},{code:"00020155020201021256040.005802CO5912COMERCIO POS49250103RBM0014CO.COM.RBM.RED9028010432620016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124HYImT9C9mng/eqME88+mrObw0014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.********************.000015CO.COM.RBM.BASE5125010454220013CO.COM.RBM.CA62180708SRB0068308020084250102020015CO.COM.RBM.CINC52040000852601040.000014CO.COM.RBM.INC530317064220002ES0112COMERCIO POS5405112206304B678",name:"COMERCIO POS",type:"cardsAndAccounts"},{code:"0002010102115802CO5915BOGOTA BICYCLES49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80260102IM0016CO.COM.RBM.CANAL91270105477470014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA60131100100 BOGOT8226010419.00014CO.COM.RBM.IVA50290108165934100013CO.COM.RBM.*****************.COM.RBM.BASE62180708BL01222608020084250102030015CO.COM.RBM.CINC520459418523010100014CO.COM.RBM.INC530317064250002ES0115BOGOTA BICYCLES6304E56D",name:"BOGOTA BICYCLES",type:"cardsAndAccounts"},{code:"000201550202010211560105802CO5922Hierros de occidente m49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124eH1E5X5opSOneQRXjtmvYMIX0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601227099 BOJAYA8223010100014CO.COM.RBM.IVA503001099353192840013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444454070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122Hierros de occidente m63049CC9",name:"HIERROS OCCIDENTE M",type:"onlyAccount"},{code:"000201550202010211560105802CO5914CAMISAS BOGOTA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL914601244nOdGGoa7JhbdMHsdz6/ZTfw0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601550001 VILLAVICE8223010100014CO.COM.RBM.IVA503001099353189710013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444450070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064240002ES0114CAMISAS BOGOTA63049B14",name:"CAMISAS BOGOTA",type:"onlyAccount"},{code:"000201550202010211560105802CO5911LA PLAZUELA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124kLbT32m0FcJ/Ws+o6IsRzz/C0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099236850430013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573215009881070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064210002ES0111LA PLAZUELA63041E06",name:"LA PLAZUELA",type:"onlyAccount"},{code:"000201550202010211560105802CO5912FLORES JUANA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124sz0Z69d6TSQ0H2oXwdNV1JzE0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099233753060013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573124012500070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064220002ES0112FLORES JUANA6304CD95",name:"FLORES JUANA",type:"onlyAccount"},{code:"0002015502010102115802CO5922COMERCIO DE SIEMPRE *******************.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124YOCYxVWUOwVMrGNJJvp2u8Uv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601366001 PEREIRA8223010100014CO.COM.RBM.IVA503001099102030400013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520405118523010100014CO.COM.RBM.INC530317064320002ES0122COMERCIO DE SIEMPRE BC63040D54",name:"COMERCIO DE SIEMPRE BC",type:"onlyAccount"}]},69595:(_,g,o)=>{o.d(g,{tG:()=>z,bE:()=>Z,PY:()=>Y});var d=o(15861),h=o(87956),u=o(53113),n=o(98699),E=o(89148),P=o(57929),R=(()=>{return(a=R||(R={})).Static="11",a.Dinamic="12",R;var a})();const{CcaBuyAvailableCop:I}=E.Av;class D{constructor(i){this.name=i}}class v{constructor(i,e,t,c,s,l,y,x,q,F,j,w){this.reference=i,this.codeQr=e,this.type=t,this.merchant=c,this.baseAmount=s,this.ivaAmount=l,this.incAmount=y,this.tipAmount=x,this.totalAmount=q,this.betweenAccounts=F,this.belongCommerce=j,this.approvalId=w}get codeDinamic(){return this.type===R.Dinamic}get codeStatic(){return this.type===R.Static}}class b{constructor(i,e,t,c,s,l,y,x,q,F,j){this.product=i,this.id=e,this.name=t,this.number=c,this.amountValue=s,this.logo=l,this.icon=y,this.color=x,this.requiredQuotas=q,this.numberQuotas=F,this.type=j,this.shortNumber=c.substring(c.length-4)}get amount(){return this.amountValue}isAvailableAmount(i){return this.amountValue>=i}}class A extends b{constructor(i,e){super(i,e.id,e.name,e.number,i.amount,e.franchise?e.franchise.getLogoContrast(e.color):i.bank.logo,e.franchise?{dark:e.franchise.logo.dark,light:e.franchise.logo.light,standard:e.franchise.getLogoContrast(e.color)}:{dark:i.bank.logo,light:i.bank.logo,standard:i.bank.logo},e.color,!1,P.qQ,"debit")}}class r extends b{constructor(i,e){if(super(i,i.id,i.name,i.publicNumber,i.amount,i.logo,i.icon,i.color,!0,P.q3,"credit"),e){const t=e.getSection(I);this.amountValue=+t?.value||i.amount}}}class B{constructor(i,e,t,c,s){this.invoice=i,this.type=e,this.amount=t,this.source=c,this.quotas=s}}var V=o(71776),Q=o(39904),N=o(87903),M=o(42168),S=o(84757),C=o(99877);let O=(()=>{class a{constructor(e,t){this.http=e,this.fingerprintService=t}read(e){var t=this;return(0,M.firstValueFrom)(this.http.post(Q.bV.PAYMENTS.QR.READ,{metadata:e}).pipe((0,S.tap)(c=>{if(Object.keys(c).every(l=>!c[l]))throw new Error("Invalid QR")}),(0,S.switchMap)(function(){var c=(0,d.Z)(function*(s){const{acquirerCode:l,merchantCode:y}=s,x=("Redeban"===l||"RBM"===l)&&9===y.length&&"9"===y.charAt(0),q=!x&&(yield t.verifyCommerce(e));return function m(a,i,e,t){return new v(i.billingNumber||i.trnConsecutiveCode,a,i.qrType,new D(i.merchantName),+i.netTrxAmount,+i.ivaValue,+i.incValue,+i.tipValue,+i.totalTrxAmount,e,t,i.approvalId)}(e,s,x,q)});return function(s){return c.apply(this,arguments)}}())))}verifyCommerce(e){return(0,M.firstValueFrom)(this.http.post(Q.bV.PAYMENTS.QR.COMMERCE,{metadata:e}).pipe((0,S.map)(t=>"320"===t?.msgRsHdr?.status?.serverStatusCode)))}send(e){return"card"===e.type?this.sendForQr(e):this.sendForAccount(e)}cancel(e){return(0,M.firstValueFrom)(this.http.post(Q.bV.PAYMENTS.QR.CANCEL,{code:e.invoice.codeQr}).pipe((0,S.map)(t=>(0,N.l1)(t,"SUCCESS")))).catch(t=>(0,N.rU)(t))}sendForQr(e){return(0,M.firstValueFrom)(this.http.post(Q.bV.PAYMENTS.QR.PAY,function p(a){return{code:a.invoice.codeQr,installments:a.quotas,origin:a.source.id}}(e)).pipe((0,S.map)(t=>(0,N.l1)(t,"SUCCESS")))).catch(t=>(0,N.rU)(t))}sendForAccount(e){var t=this;return(0,d.Z)(function*(){const c=yield t.fingerprintService.getInfo();return(0,M.firstValueFrom)(t.http.post(Q.bV.PAYMENTS.QR.PAY_ACCOUNT,function T(a,i){return{code:a.invoice.codeQr,curAmt:{amt:a.amount,curCode:"COP"},deviceAdmin:i,origin:a.source.id}}(e,c)).pipe((0,S.map)(s=>[201,"201"].includes(s.msgRsHdr?.status?.statusCode)?new u.LN("SUCCESS",`Ref: ${s.approvalId}`):"397"===s.msgRsHdr?.status?.additionalStatus?.statusCode?new u.LN("INFO",s.msgRsHdr.status.additionalStatus.statusDesc):(0,N.l1)(s,"SUCCESS")))).catch(s=>(0,N.rU)(s))})()}}return a.\u0275fac=function(e){return new(e||a)(C.\u0275\u0275inject(V.HttpClient),C.\u0275\u0275inject(h.ew))},a.\u0275prov=C.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var f=o(20691);let U=(()=>{class a extends f.Store{constructor(e){super({amount:0,creditCards:[],debitCards:[],products:[],pay:!0,requiredQuotas:!0,type:"card"}),e.subscribes(Q.PU,()=>{this.reset()})}setProducts(e){this.reduce(t=>({...t,products:e}))}getProducts(){return this.select(({products:e})=>e)}setCreditCards(e){this.reduce(t=>({...t,creditCards:e}))}addCreditCard(e){const t=this.getCreditCards();t.filter(({id:s})=>s===e.id).length||this.reduce(s=>({...s,creditCards:[...t,e]}))}getCreditCards(){return this.select(({creditCards:e})=>e)}setDebitCards(e){this.reduce(t=>({...t,debitCards:e}))}addDebitCard(e){const t=this.getDebitCards();t.filter(({id:s})=>s===e.id).length||this.reduce(s=>({...s,debitCards:[...t,e]}))}getDebitCards(){return this.select(({debitCards:e})=>e)}setInvoice(e,t=!0){this.reduce(c=>({...c,invoice:e,pay:t}))}getInvoice(){return this.select(({invoice:e})=>e)}setSourceCard(e){this.reduce(t=>({...t,source:e,requiredQuotas:e.requiredQuotas,type:"card"}))}setSourceProduct(e){this.reduce(t=>({...t,source:e,quotas:0,requiredQuotas:!1,type:"account"}))}getSource(){return this.select(({source:e})=>e)}selectForSource(){return this.select(({amount:e,creditCards:t,debitCards:c,invoice:s,products:l,source:y})=>({amount:s.codeDinamic?s.totalAmount:e,creditCards:t,codeStatic:s.codeStatic,debitCards:c,invoice:s,products:l,source:y}))}setQuotas(e){this.reduce(t=>({...t,quotas:e}))}getQuotas(){return this.select(({quotas:e})=>e)}setAmount(e){this.reduce(t=>({...t,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getPay(){return this.select(({pay:e})=>e)}selectForConfirmation(){return this.select(({amount:e,invoice:t,quotas:c,requiredQuotas:s,source:l})=>({amount:e,invoice:t,quotas:c,requiredQuotas:s,source:l}))}}return a.\u0275fac=function(e){return new(e||a)(C.\u0275\u0275inject(h.Yd))},a.\u0275prov=C.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),z=(()=>{class a{constructor(e,t,c){this.repository=e,this.store=t,this.eventBusService=c}setSourceCard(e){try{const t=this.store.getSource();return t&&t.type!==e.type&&this.store.setQuotas(e.numberQuotas),n.Either.success(this.store.setSourceCard(e))}catch{return n.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setSourceProduct(e){try{return n.Either.success(this.store.setSourceProduct(e))}catch{return n.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setQuotas(e){try{return n.Either.success(this.store.setQuotas(e))}catch({message:t}){return n.Either.failure({message:t})}}setAmount(e){try{return n.Either.success(this.store.setAmount(e))}catch({message:t}){return n.Either.failure({message:t})}}reset(){try{return n.Either.success(this.store.reset())}catch({message:e}){return n.Either.failure({message:e})}}send(){var e=this;return(0,d.Z)(function*(){const t=function L(a){return new B(a.invoice,a.type,a.amount,a.source,a.quotas)}(e.store.currentState),c=yield e.execute(t);return e.eventBusService.emit(c.channel),n.Either.success({paymentQr:t,status:c})})()}execute(e){try{return this.repository.send(e)}catch({message:t}){return Promise.resolve(u.LN.error(t))}}}return a.\u0275fac=function(e){return new(e||a)(C.\u0275\u0275inject(O),C.\u0275\u0275inject(U),C.\u0275\u0275inject(h.Yd))},a.\u0275prov=C.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),Z=(()=>{class a{constructor(e){this.store=e}source(){var e=this;return(0,d.Z)(function*(){try{return n.Either.success(e.store.selectForSource())}catch({message:t}){return n.Either.failure({message:t})}})()}cancel(){var e=this;return(0,d.Z)(function*(){try{const t=e.store.getInvoice();return n.Either.success({invoice:t})}catch({message:t}){return n.Either.failure({message:t})}})()}confirmation(){var e=this;return(0,d.Z)(function*(){try{return n.Either.success(e.store.selectForConfirmation())}catch({message:t}){return n.Either.failure({message:t})}})()}}return a.\u0275fac=function(e){return new(e||a)(C.\u0275\u0275inject(U))},a.\u0275prov=C.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var G=o(70658);let Y=(()=>{class a{constructor(e,t,c,s,l){this.products=e,this.productService=t,this.repository=c,this.store=s,this.scannerService=l}execute(){var e=this;return(0,d.Z)(function*(){try{const{code:t,cancelled:c,granted:s}=yield e.scanQrCode();return s||G.N.navigatorEnabled?c?n.Either.failure({value:!1,message:"Escaneo del c\xf3digo QR cancelado para realizar pago"}):n.Either.success(t):n.Either.failure({value:!1,message:"Banca m\xf3vil no cuenta con permisos para escanear de c\xf3digo QR, por favor habilite los permisos y vuelva a intentarlo"})}catch({message:t}){return n.Either.failure({value:!0,message:t})}})()}payment(e){var t=this;return(0,d.Z)(function*(){try{return t.verifyInvoice(yield t.repository.read(e))}catch({message:c}){return t.store.reset(),n.Either.failure({message:c})}})()}cancel(e){var t=this;return(0,d.Z)(function*(){try{const c=yield t.repository.read(e);return c.approvalId?n.Either.success(t.store.setInvoice(c,!1)):n.Either.failure({message:"C\xf3digo escaneado para pago QR es inv\xe1lido"})}catch({message:c}){return t.store.reset(),n.Either.failure({message:c})}})()}scanQrCode(){var e=this;return(0,d.Z)(function*(){return e.scannerService.qrCode({orientation:"portrait"})})()}verifyInvoice(e){var t=this;return(0,d.Z)(function*(){try{if(e.betweenAccounts){const s=yield t.verifyAccount(!0);return s&&t.store.setSourceProduct(s),n.Either.success(t.store.setInvoice(e))}const c=yield t.verifyCards();if(!e.belongCommerce&&!c)throw Error("Actualmente no cuentas con tarjetas activas para realizar pago QR");if(c&&(t.store.setSourceCard(c),t.store.setQuotas(c.numberQuotas)),e.belongCommerce){const s=yield t.verifyAccount(!1);if(!s&&!c)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");s&&t.store.setSourceProduct(s)}else t.store.setProducts([]);return n.Either.success(t.store.setInvoice(e))}catch({message:c}){return n.Either.failure({message:c})}})()}verifyAccount(e){var t=this;return(0,d.Z)(function*(){const c=(yield t.products.requestAccountsForTransfer()).sort((l,y)=>l.amount>y.amount?-1:1);t.store.setProducts(c);const[s]=c;if(!s&&e)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");return s})()}verifyCards(){var e=this;return(0,d.Z)(function*(){const[[t],[c]]=yield Promise.all([e.requestCreditCards(),e.requestDebitCards()]);return t||c})()}requestCreditCards(){var e=this;return(0,d.Z)(function*(){const t=yield e.products.requestCreditCards(),s=(yield Promise.all(t.map(l=>e.requestCreditCardInformation(l)))).filter(l=>(0,n.itIsDefined)(l)).sort((l,y)=>l.amount>y.amount?-1:1);return e.store.setCreditCards(s),s})()}requestCreditCardInformation(e){return this.productService.requestInformation(e).then(t=>t&&new r(e,t)).catch(()=>{})}requestDebitCards(){var e=this;return(0,d.Z)(function*(){const t=yield e.products.requestAccountsForTransfer(),s=(yield Promise.all(t.map(l=>e.requestDebitCardsInformation(l)))).reduce((l,y)=>l.concat(y),[]).sort((l,y)=>l.amount>y.amount?-1:1);return e.store.setDebitCards(s),s})()}requestDebitCardsInformation(e){return this.productService.requestDebitCards(e).then(t=>t.map(c=>new A(e,c))).catch(()=>[])}}return a.\u0275fac=function(e){return new(e||a)(C.\u0275\u0275inject(h.hM),C.\u0275\u0275inject(h.M5),C.\u0275\u0275inject(O),C.\u0275\u0275inject(U),C.\u0275\u0275inject(h.LQ))},a.\u0275prov=C.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},7747:(_,g,o)=>{o.d(g,{NJ:()=>E,w2:()=>A}),o(74221);var h=o(17007),u=o(30263),n=o(99877);let E=(()=>{class r{}return r.\u0275fac=function(m){return new(m||r)},r.\u0275mod=n.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=n.\u0275\u0275defineInjector({imports:[h.CommonModule,u.Qg]}),r})();var P=o(15861),R=o(39904),I=o(95437),D=o(57929),v=o(69595);function b(r,B){if(1&r){const m=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",3),n.\u0275\u0275listener("click",function(){const L=n.\u0275\u0275restoreView(m).$implicit,V=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(V.onSelect(L))}),n.\u0275\u0275elementStart(1,"div",4),n.\u0275\u0275element(2,"bocc-icon",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"label",6),n.\u0275\u0275text(4),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"span",7),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()}if(2&r){const m=B.$implicit,p=n.\u0275\u0275nextContext();n.\u0275\u0275attribute("bocc-theme",p.getBoccTheme(m)),n.\u0275\u0275advance(4),n.\u0275\u0275textInterpolate(m.name),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(p.getLabelType(m))}}let A=(()=>{class r{constructor(m,p){this.mboProvider=m,this.scannerQr=p,this.qrCodes=D.Be,this.cancelAction={id:"btn_payment-select-qr-code_cancel",label:"Cancelar",click:()=>{this.portal?.close()}}}ngBoccPortal(m){this.portal=m}getLabelType({type:m}){switch(m){case"onlyAccount":return"Entre cuentas";case"onlyCards":return"Solo tarjetas";default:return"Adquiriencia"}}getBoccTheme({type:m}){switch(m){case"onlyAccount":return"blue";case"onlyCards":return"amathyst";default:return"success"}}onSelect({code:m}){var p=this;return(0,P.Z)(function*(){p.portal?.close(),p.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield p.scannerQr.payment(m)).when({success:()=>{p.mboProvider.navigation.next(R.Z6.PAYMENTS.QR.CONFIRMATION)},failure:({message:T})=>{p.mboProvider.toast.error(T)}},()=>{p.mboProvider.loader.close()})})()}}return r.\u0275fac=function(m){return new(m||r)(n.\u0275\u0275directiveInject(I.ZL),n.\u0275\u0275directiveInject(v.PY))},r.\u0275cmp=n.\u0275\u0275defineComponent({type:r,selectors:[["mbo-payment-select-qr-code"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[["title","Seleccionar QR",3,"rightAction"],[1,"mbo-payment-select-qr-code__list"],["class","mbo-payment-select-qr-code__element",3,"click",4,"ngFor","ngForOf"],[1,"mbo-payment-select-qr-code__element",3,"click"],[1,"mbo-payment-select-qr-code__avatar"],["icon","invoice-barcode"],[1,"smalltext-medium"],[1,"caption-semibold"]],template:function(m,p){1&m&&(n.\u0275\u0275element(0,"bocc-header-form",0),n.\u0275\u0275elementStart(1,"div",1),n.\u0275\u0275template(2,b,7,3,"div",2),n.\u0275\u0275elementEnd()),2&m&&(n.\u0275\u0275property("rightAction",p.cancelAction),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",p.qrCodes))},dependencies:[h.CommonModule,h.NgForOf,u.Zl,u.Jx],styles:["/*!\n * MBO PaymentSelectQrCode Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 18/Jul/2024\n * Updated: 18/Jul/2024\n*/mbo-payment-select-qr-code{position:relative;display:flex;flex-direction:column}mbo-payment-select-qr-code bocc-header-form{position:sticky;top:0rem;z-index:var(--z-index-2);background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__list{display:grid;grid-template-columns:1fr 1fr;padding:var(--sizing-safe-footer-x8);box-sizing:border-box;row-gap:var(--sizing-x8);-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element{display:flex;border-radius:var(--sizing-x4);padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;justify-content:center;align-items:center;flex-direction:column;row-gap:var(--sizing-x2);border:var(--border-1-lighter-300)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element label{text-align:center;color:var(--color-carbon-darker-1000)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element span{text-align:center;color:var(--color-carbon-lighter-700)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__avatar{padding:var(--sizing-x2);border-radius:50%;background:var(--color-bocc-200);color:var(--color-bocc-700);margin-bottom:var(--sizing-x4)}\n"],encapsulation:2}),r})()},74221:(_,g,o)=>{o.d(g,{s:()=>D});var d=o(98699),u=o(99877),E=o(17007),R=o(55944);function I(v,b){if(1&v&&u.\u0275\u0275element(0,"img",7),2&v){const A=u.\u0275\u0275nextContext();u.\u0275\u0275property("src",null==A.source?null:A.source.icon.light,u.\u0275\u0275sanitizeUrl)}}let D=(()=>{class v{constructor(){this.amount=0}get hasAmount(){return(0,d.itIsDefined)(this.source.amount)&&this.source.amount>0}get theme(){return this.source.amount>=this.amount?"success":"danger"}}return v.\u0275fac=function(A){return new(A||v)},v.\u0275cmp=u.\u0275\u0275defineComponent({type:v,selectors:[["mbo-payment-qr-source-card"]],inputs:{source:"source",amount:"amount"},decls:9,vars:9,consts:[[1,"mbo-payment-qr-source-card__content"],[1,"mbo-payment-qr-source-card__header"],[1,"subtitle2-medium","truncate"],[3,"src",4,"ngIf"],[1,"mbo-payment-qr-source-card__account"],[1,"smalltext-medium","truncate"],[3,"badgeMode","decimals","amount"],[3,"src"]],template:function(A,r){1&A&&(u.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),u.\u0275\u0275text(3),u.\u0275\u0275elementEnd(),u.\u0275\u0275template(4,I,1,1,"img",3),u.\u0275\u0275elementEnd(),u.\u0275\u0275elementStart(5,"div",4)(6,"label",5),u.\u0275\u0275text(7),u.\u0275\u0275elementEnd(),u.\u0275\u0275element(8,"bocc-amount",6),u.\u0275\u0275elementEnd()()),2&A&&(u.\u0275\u0275classProp("mbo-payment-qr-source-card__content--disabled",!r.hasAmount),u.\u0275\u0275advance(3),u.\u0275\u0275textInterpolate(null==r.source?null:r.source.name),u.\u0275\u0275advance(1),u.\u0275\u0275property("ngIf",null==r.source?null:r.source.icon),u.\u0275\u0275advance(3),u.\u0275\u0275textInterpolate(null==r.source?null:r.source.shortNumber),u.\u0275\u0275advance(1),u.\u0275\u0275property("badgeMode",!0)("decimals",!0)("amount",null==r.source?null:r.source.amount),u.\u0275\u0275attribute("bocc-theme",r.theme))},dependencies:[E.NgIf,R.Q],styles:["/*!\n * MBO PaymentQrSourceCard Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 07/Jul/2023\n * Updated: 10/Ene/2024\n*/mbo-payment-qr-source-card{position:relative;width:100%;display:block}mbo-payment-qr-source-card .mbo-payment-qr-source-card__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x6);box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__content--disabled{opacity:.5}mbo-payment-qr-source-card .mbo-payment-qr-source-card__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-payment-qr-source-card .mbo-payment-qr-source-card__header img{height:var(--sizing-x12);width:var(--sizing-x12);filter:var(--img-filter)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account{position:relative;display:flex;width:100%;justify-content:space-between;align-items:center}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account>label{color:var(--color-amathyst-700)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account bocc-amount{font-weight:var(--font-weight-semibold)}\n"],encapsulation:2}),v})()},65282:(_,g,o)=>{o.r(g),o.d(g,{MboPaymentQrScanPageModule:()=>N});var d=o(17007),h=o(78007),u=o(30263),n=o(79798),E=o(15861),P=o(8834),R=o(39904),I=o(95437),D=o(70658),v=o(63674),b=o(69595),A=o(7747),r=o(99877),B=o(48774),m=o(66613),p=o(23436),T=o(85070);function L(M,S){if(1&M){const C=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"bocc-card-category",8),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(C);const f=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(f.onSelect())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2,"Seleccionar c\xf3digo QR"),r.\u0275\u0275elementEnd()()}}const{MAX_QR_STATIC:V}=v._f;let Q=(()=>{class M{constructor(C,O,f){this.bottomSheetService=C,this.mboProvider=O,this.scannerQr=f,this.maxAmount=(0,P.b)({value:V}),this.backAction={id:"btn_payment-qr-scan_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(R.Z6.CUSTOMER.PRODUCTS.HOME)}}}ngOnDestroy(){this.boccPortal?.destroy()}get canSelectQrCode(){return!D.N.production}onSelect(){this.boccPortal||(this.boccPortal=this.bottomSheetService.create(A.w2,{containerProps:{autoclose:!1}})),this.boccPortal.open()}onPayment(){this.qrScan(C=>this.qrForPayment(C))}onCancel(){this.qrScan(C=>this.qrForCancel(C))}qrScan(C){var O=this;return(0,E.Z)(function*(){(yield O.scannerQr.execute()).when({success:C,failure:({message:f,value:U})=>{O.mboProvider.toast.failure(f,U)}})})()}qrForPayment(C){var O=this;return(0,E.Z)(function*(){O.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield O.scannerQr.payment(C)).when({success:()=>{O.mboProvider.navigation.next(R.Z6.PAYMENTS.QR.CONFIRMATION)},failure:({message:f})=>{O.mboProvider.toast.error(f)}},()=>{O.mboProvider.loader.close()})})()}qrForCancel(C){var O=this;return(0,E.Z)(function*(){O.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield O.scannerQr.cancel(C)).when({success:()=>{O.mboProvider.navigation.next(R.Z6.PAYMENTS.QR.CANCEL)},failure:({message:f})=>{O.mboProvider.toast.error(f)}},()=>{O.mboProvider.loader.close()})})()}}return M.\u0275fac=function(C){return new(C||M)(r.\u0275\u0275directiveInject(u.fG),r.\u0275\u0275directiveInject(I.ZL),r.\u0275\u0275directiveInject(b.PY))},M.\u0275cmp=r.\u0275\u0275defineComponent({type:M,selectors:[["mbo-payment-qr-scan-page"]],decls:17,vars:4,consts:[[1,"mbo-payment-qr-scan-page__content"],[1,"mbo-payment-qr-scan-page__header"],["title","Pago QR",3,"leftAction"],[1,"mbo-payment-qr-scan-page__body"],["icon","bell",3,"visible"],["id","btn_payment-qr-scan_select","icon","invoice-barcode",3,"click",4,"ngIf"],["id","btn_payment-qr-scan_purchase","icon","qr-code",3,"click"],["id","btn_payment-qr-scan_cancel","icon","payment-buy-cancel",3,"click"],["id","btn_payment-qr-scan_select","icon","invoice-barcode",3,"click"]],template:function(C,O){1&C&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"mbo-customer-greeting"),r.\u0275\u0275text(5,"\xbfQu\xe9 deseas hacer hoy?"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(6,"bocc-alert",4),r.\u0275\u0275text(7," Recuerda, el monto m\xe1ximo que puedes comprar con QR es de "),r.\u0275\u0275elementStart(8,"b"),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(10,L,3,0,"bocc-card-category",5),r.\u0275\u0275elementStart(11,"bocc-card-category",6),r.\u0275\u0275listener("click",function(){return O.onPayment()}),r.\u0275\u0275elementStart(12,"span"),r.\u0275\u0275text(13,"Comprar con c\xf3digo QR"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(14,"bocc-card-category",7),r.\u0275\u0275listener("click",function(){return O.onCancel()}),r.\u0275\u0275elementStart(15,"span"),r.\u0275\u0275text(16,"Anular compra con QR"),r.\u0275\u0275elementEnd()()()()),2&C&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",O.backAction),r.\u0275\u0275advance(4),r.\u0275\u0275property("visible",!0),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1("$ ",O.maxAmount,""),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",O.canSelectQrCode))},dependencies:[d.NgIf,B.J,m.B,p.D,T.f],styles:["/*!\n * MBO PaymentQrScan Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 25/Oct/2022\n * Updated: 14/May/2024\n*/mbo-payment-qr-scan-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-qr-scan-page .mbo-payment-qr-scan-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-qr-scan-page .mbo-payment-qr-scan-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),M})(),N=(()=>{class M{}return M.\u0275fac=function(C){return new(C||M)},M.\u0275mod=r.\u0275\u0275defineNgModule({type:M}),M.\u0275inj=r.\u0275\u0275defineInjector({imports:[d.CommonModule,h.RouterModule.forChild([{path:"",component:Q}]),u.Jx,u.B4,u.D0,n.fi]}),M})()},63674:(_,g,o)=>{o.d(g,{Eg:()=>I,Lo:()=>n,Wl:()=>E,ZC:()=>P,_f:()=>h,br:()=>R,tl:()=>u});var d=o(29306);const h={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},u=new d.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),n={color:"success",key:"paid",label:"Pagada"},E={color:"alert",key:"pending",label:"Por pagar"},P={color:"danger",key:"expired",label:"Vencida"},R={color:"info",key:"recurring",label:"Pago recurrente"},I={color:"info",key:"programmed",label:"Programado"}}}]);