(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1618],{1618:(E,d,n)=>{n.r(d),n.d(d,{MboTransfiyaApprovedModule:()=>f});var t=n(17007),l=n(78007),a=n(99877);const M=[{path:"",loadChildren:()=>n.e(2315).then(n.bind(n,92315)).then(o=>o.MboTransfiyaApprovedHomePageModule)},{path:"information",loadComponent:()=>n.e(1368).then(n.bind(n,51368)).then(o=>o.MboTransfiyaApprovedInformationPage)},{path:"destination",loadChildren:()=>n.e(6149).then(n.bind(n,46149)).then(o=>o.MboTransfiyaApprovedDestinationPageModule)},{path:"confirmation",loadChildren:()=>n.e(3637).then(n.bind(n,33637)).then(o=>o.MboTransfiyaApprovedConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(7404).then(n.bind(n,27404)).then(o=>o.MboTransfiyaApprovedResultPageModule)}];let f=(()=>{class o{}return o.\u0275fac=function(s){return new(s||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[t.CommonModule,l.RouterModule.forChild(M)]}),o})()}}]);