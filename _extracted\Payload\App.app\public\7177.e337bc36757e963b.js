(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7177],{87177:(i,a,o)=>{o.r(a),o.d(a,{MboCustomerNotificationsModule:()=>u});var l=o(17007),M=o(78007),t=o(99877);const d=[{path:"",loadChildren:()=>o.e(8877).then(o.bind(o,68877)).then(n=>n.MboCustomerNotificationsHomePageModule)},{path:"information",loadChildren:()=>o.e(3112).then(o.bind(o,43112)).then(n=>n.MboCustomerNotificationInformationPageModule)}];let u=(()=>{class n{}return n.\u0275fac=function(f){return new(f||n)},n.\u0275mod=t.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=t.\u0275\u0275defineInjector({imports:[l.CommonModule,M.RouterModule.forChild(d)]}),n})()}}]);