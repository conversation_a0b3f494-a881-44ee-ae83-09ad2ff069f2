(self.webpackChunkapp=self.webpackChunkapp||[]).push([[106],{72042:(T,E,I)=>{I.d(E,{K:()=>C,p:()=>O});var O=(()=>{return(N=O||(O={}))[N.NONE=0]="NONE",N[N.TOUCH_ID=1]="TOUCH_ID",N[N.FACE_ID=2]="FACE_ID",N[N.FINGERPRINT=3]="FINGERPRINT",N[N.FACE_AUTHENTICATION=4]="FACE_AUTHENTICATION",N[N.IRIS_AUTHENTICATION=5]="IRIS_AUTHENTICATION",N[N.MULTIPLE=6]="MULTIPLE",O;var N})(),C=(()=>{return(N=C||(C={}))[N.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",N[N.BIOMETRICS_UNAVAILABLE=1]="BIOMETRICS_UNAVAILABLE",N[N.USER_LOCKOUT=2]="USER_LOCKOUT",N[N.BIOMETRICS_NOT_ENROLLED=3]="BIOMETRICS_NOT_ENROLLED",N[N.USER_TEMPORARY_LOCKOUT=4]="USER_TEMPORARY_LOCKOUT",N[N.AUTHENTICATION_FAILED=10]="AUTHENTICATION_FAILED",N[N.APP_CANCEL=11]="APP_CANCEL",N[N.INVALID_CONTEXT=12]="INVALID_CONTEXT",N[N.NOT_INTERACTIVE=13]="NOT_INTERACTIVE",N[N.PASSCODE_NOT_SET=14]="PASSCODE_NOT_SET",N[N.SYSTEM_CANCEL=15]="SYSTEM_CANCEL",N[N.USER_CANCEL=16]="USER_CANCEL",N[N.USER_FALLBACK=17]="USER_FALLBACK",C;var N})()},60106:(T,E,I)=>{I.r(E),I.d(E,{BiometricAuthError:()=>N.K,BiometricType:()=>N.p,NativeBiometric:()=>R});var O=I(17737),N=I(72042);const R=(0,O.registerPlugin)("NativeBiometric",{web:()=>I.e(9634).then(I.bind(I,70179)).then(({NativeBiometricWeb:_})=>new _)})}}]);