(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5241],{5241:($,T,s)=>{s.r(T),s.d(T,{MboCustomerProductsPageModule:()=>B});var P=s(17007),A=s(78007),b=s(30263),f=s(79798),p=s(74561),l=s(15861),e=s(99877),F=s(78506),a=s(39904),g=s(88844),m=s(89148),I=s(95437),_=s(87956),x=s(57544),C=s(70658),R=s(54747),D=s(61980),y=s(45542),c=s(55491),d=s(9593),N=s(88014),S=s(56204),M=s(55648),O=s(57384),w=s(20586);function L(n,r){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onClearFilter())}),e.\u0275\u0275text(2," Atr\xe1s "),e.\u0275\u0275elementEnd()()}}function U(n,r){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-banner-card",20),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onBreb())}),e.\u0275\u0275elementEnd()}}function z(n,r){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"mbo-banner-card",21),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onRemittances())}),e.\u0275\u0275elementEnd()}}function j(n,r){if(1&n){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",16),e.\u0275\u0275template(1,U,1,0,"mbo-banner-card",17),e.\u0275\u0275elementStart(2,"mbo-banner-card",18),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.onSOAT())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(3,z,1,0,"mbo-banner-card",19),e.\u0275\u0275elementEnd()}if(2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.enabledBreb),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.enabledRemittains)}}function G(n,r){if(1&n&&e.\u0275\u0275element(0,"adl-consejero-aval",22),2&n){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("environment",t.environment.avalAdviser.environment)("startConversation",t.startConversationAdviserAval)("getInternalURL",t.redirectFromAdviserAval)}}const V=[m.Gt.Aval,m.Gt.Trustfund];let h=(()=>{class n{constructor(t,o,i,u,v,E,k,Z,Y,H,J){this.ref=t,this.activateRoute=o,this.modalConfirmation=i,this.mboProvider=u,this.deviceService=v,this.requestConfiguration=E,this.managerNotification=k,this.productsService=Z,this.walletManagerService=Y,this.inactivityProvider=H,this.verfiyStatusConsentSPI=J,this.unsubscriptions=[],this.customerProducts=[],this.excludedProducts={},this.environment=C.N,this.requesting=!0,this.incognito=!1,this.additionals=!0,this.products=[],this.productId="",this.avalAdviserStart=!1,this.enabledAvalAdviser=!0,this.enabledRemittains=!0,this.enabledBreb=!0,this.enabledTuPlus=!0,this.condense=!1,this.filters=[],this.filterControl=new x.FormControl("NONE")}ngOnInit(){this.ref.nativeElement.classList.add(a.fc),this.verfiyStatusConsentSPI.execute(),this.initializatedConfiguration(),this.startConversationAdviserAval=(()=>this.requestConfiguration.requestAdviserAval()).bind(this),this.redirectFromAdviserAval=(t=>{this.mboProvider.navigation.next(t)}).bind(this),this.unsubscriptions.push(this.filterControl.subscribe(t=>{this.additionals=!t||"NONE"===t,this.products=V.includes(t)?this.excludedProducts[t]||[]:t&&"NONE"!==t?this.customerProducts.filter(({type:o})=>o===t):this.customerProducts})),this.unsubscriptions.push(this.productsService.subscribe(t=>t.when({product:o=>{o.hasErrorPreview?this.modalConfirmation.execute({title:"Error de visualizaci\xf3n",message:"No logramos mostrar la informaci\xf3n del producto. Estamos trabajando en ello.",accept:{label:"Entendido"}}):(o.bank.belongOccidente||a.xP.includes(o.bank.id))&&this.mboProvider.navigation.next(a.Z6.CUSTOMER.PRODUCTS.INFO,{productId:o.id,productFilter:this.filterControl.value})},products:({type:o,value:i})=>{this.addFilterForProducts(i,o)}})))}ngOnDestroy(){this.unsubscriptions.forEach(t=>{t()})}get requiredBack(){return"AVAL"===this.filterControl.value||"80"===this.filterControl.value}get appVersion(){return C.N.appVersion}onBreb(){this.mboProvider.navigation.next(a.Z6.MICROFRONTENDS.SPI)}onSOAT(){this.mboProvider.openUrl(a.BA.CARROYA)}onRemittances(){this.mboProvider.navigation.next(a.Z6.CUSTOMER.PRODUCTS.REMITTANCES)}onClearFilter(){this.filterControl.setValue("NONE")}initializatedConfiguration(){var t=this;return(0,l.Z)(function*(){(yield t.requestConfiguration.home()).when({success:o=>{const{preferences$:i,products$:u}=o;t.modalForActivateBiometric(o.activateBiometric),t.enabledTuPlus=o.enabledTuPlus,t.enabledAvalAdviser=o.enabledAvalAdviser,t.enabledBreb=o.enabledBreb,t.enabledRemittains=o.enabledRemittains,u.then(v=>{t.filters=t.createFilters(v[0]),t.initializatedProducts(v),t.walletManagerService.pushCardsInWallet(v[0])}).finally(()=>{t.resolveFinish()}),t.unsubscriptions.push(i(({isIncognito:v})=>{t.incognito=v}))},failure:()=>{t.resolveFinish()}}),!C.N.navigatorEnabled&&t.inactivityProvider.on(),t.deviceService.controller.itIsMobile&&t.managerNotification.activate().then(o=>{o.when({failure:({message:i,value:u})=>{u&&t.mboProvider.toast.warning(i)}})})})()}initializatedProducts(t){const[o,i,u]=t;this.customerProducts=o,this.products=o;const{productId:v,productFilter:E}=this.activateRoute.snapshot.queryParams,k=this.getProductsForFilterId(t,E).find(({id:Z})=>Z===v);setTimeout(()=>{switch(E){case m.Gt.Trustfund:i&&this.addFilterForProducts(i,m.Gt.Trustfund);break;case m.Gt.Aval:u&&this.addFilterForProducts(u,m.Gt.Aval);break;default:this.filterControl.setValue(E||"NONE")}this.productId=k?.id},120)}getProductsForFilterId(t,o){const[i,u,v]=t;switch(o){case m.Nb.Trustfund:return u;case m.Nb.Aval:return v;default:return i}}addFilterForProducts(t,o){const i=g.wp[o];this.excludedProducts[o]=t,i&&!this.filters.includes(i)&&this.filters.push(i),this.filterControl.setValue(i?.value)}resolveFinish(){setTimeout(()=>{this.condense=!0},1e4),this.requesting=!1}modalForActivateBiometric(t){t&&this.modalConfirmation.execute({title:"Autenticaci\xf3n biom\xe9trica",message:"Activa la autenticaci\xf3n biom\xe9trica de tu celular e inicia sesi\xf3n de forma f\xe1cil y r\xe1pida.",accept:{label:"Activar biometr\xeda",click:()=>this.mboProvider.navigation.next(a.Z6.CUSTOMER.SECURITY.BIOMETRIC,{from:"products"})},decline:{label:"En otro momento"}})}createFilters(t){return[g.CE,...t.reduce((o,{type:i})=>(o.includes(i)||o.push(i),o),[]).reduce((o,i)=>{const u=g.wp[i];return u&&o.push(u),o},[])]}}return n.\u0275fac=function(t){return new(t||n)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(A.ActivatedRoute),e.\u0275\u0275directiveInject(b.$e),e.\u0275\u0275directiveInject(I.ZL),e.\u0275\u0275directiveInject(_.U8),e.\u0275\u0275directiveInject(R.mb),e.\u0275\u0275directiveInject(F.AZ),e.\u0275\u0275directiveInject(D.su),e.\u0275\u0275directiveInject(D.hH),e.\u0275\u0275directiveInject(I._5),e.\u0275\u0275directiveInject(R.Yp))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-customer-products-page"]],decls:14,vars:25,consts:[[1,"mbo-customer-products-page__content"],[1,"mbo-customer-products-page__body"],[3,"hidden"],[1,"mbo-customer-products-page__actions",3,"hidden"],["id","btn_customer-products_incognito"],[3,"suggestions","formControl"],["class","mbo-customer-products-page__back",4,"ngIf"],[1,"mbo-customer-products-page__carousel",3,"skeleton","products","productId","incognito","additionals","enabledTuPlus"],["class","mbo-banner-cards",4,"ngIf"],["id","mth_pm",1,"mathilde-ads"],[1,"mbo-customer-products-page__contacts",3,"hidden"],["entity","bocc","channel","bancaMovil",3,"environment","startConversation","getInternalURL",4,"ngIf"],[3,"condense"],["active","products"],[1,"mbo-customer-products-page__back"],["bocc-button","flat","prefixIcon","prev-page",3,"click"],[1,"mbo-banner-cards"],["avatarSrc","assets/shared/logos/breb.svg","title","Zona","subtitle","Bre-B",3,"click",4,"ngIf"],["avatarSrc","assets/shared/logos/SOAT.svg","title","SOAT","subtitle","C\xf3mpralo aqu\xed",3,"click"],["avatarSrc","assets/shared/logos/remittances-shadow.svg","title","Remesas","subtitle","Desde EE.UU \u{1f1fa}\u{1f1f8}",3,"click",4,"ngIf"],["avatarSrc","assets/shared/logos/breb.svg","title","Zona","subtitle","Bre-B",3,"click"],["avatarSrc","assets/shared/logos/remittances-shadow.svg","title","Remesas","subtitle","Desde EE.UU \u{1f1fa}\u{1f1f8}",3,"click"],["entity","bocc","channel","bancaMovil",3,"environment","startConversation","getInternalURL"]],template:function(t,o){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"mbo-message-greeting",2),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275element(4,"mbo-button-incognito-mode",4)(5,"bocc-select-button",5),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,L,3,0,"div",6),e.\u0275\u0275element(7,"mbo-products-carousel",7),e.\u0275\u0275template(8,j,4,2,"div",8),e.\u0275\u0275element(9,"div",9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",10),e.\u0275\u0275template(11,G,1,3,"adl-consejero-aval",11),e.\u0275\u0275element(12,"mbo-button-contacts",12),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(13,"mbo-bottom-navigation",13)),2&t&&(e.\u0275\u0275classProp("mbo-customer-products-page__content--requesting",o.requesting)("aval-adviser",o.enabledAvalAdviser&&o.avalAdviserStart)("condense",o.condense),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",o.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",o.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("suggestions",o.filters)("formControl",o.filterControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.requiredBack),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",o.requesting)("products",o.products)("productId",o.productId)("incognito",o.incognito)("additionals",o.additionals)("enabledTuPlus",o.enabledTuPlus),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!o.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mathilde-ads--waiting",o.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("condense",o.condense),e.\u0275\u0275property("hidden",o.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.enabledAvalAdviser),e.\u0275\u0275advance(1),e.\u0275\u0275property("condense",o.condense))},dependencies:[P.NgIf,y.P,c.G,d.k,N.J,S.V,M.u,O.$,w.y],styles:["/*!\n * MBO CustomerProducts Page\n * v2.3.3\n * Author: MB Frontend Developers\n * Created: 23/Jun/2022\n * Updated: 29/Apr/2025\n*/mbo-customer-products-page{--bocc-card-product-height: var(--mbo-product-carousel-height);--pvt-body-margin-bottom: 90rem;position:relative;display:block;width:100%;height:100vh;overflow:hidden}mbo-customer-products-page .mbo-customer-products-page__content{height:100vh;padding-top:var(--mbo-application-body-safe-spacing);box-sizing:border-box;overflow-y:auto;overflow-x:hidden}mbo-customer-products-page .mbo-customer-products-page__content.condense{--pvt-body-margin-bottom: 72rem}mbo-customer-products-page .mbo-customer-products-page__content.aval-adviser{--pvt-body-margin-bottom: 108rem}mbo-customer-products-page .mbo-customer-products-page__content.aval-adviser.condense{--pvt-body-margin-bottom: 98rem}mbo-customer-products-page .mbo-customer-products-page__content--requesting{overflow-y:hidden}mbo-customer-products-page .mbo-customer-products-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);margin-bottom:var(--pvt-body-margin-bottom);padding:var(--sizing-x4) var(--sizing-form);box-sizing:border-box;transition:margin-bottom .24s var(--standard-curve)}mbo-customer-products-page .mbo-customer-products-page__body mbo-message-greeting{margin:var(--sizing-x6) 0rem var(--sizing-x4) 0rem}mbo-customer-products-page .mbo-customer-products-page__skeleton{position:relative;padding:var(--sizing-x4) var(--sizing-form);box-sizing:border-box}mbo-customer-products-page .mbo-customer-products-page__actions{position:relative;display:flex;width:100%;justify-content:space-between}mbo-customer-products-page .mbo-customer-products-page__actions>mbo-button-incognito-mode{--bocc-button-padding: 0rem var(--sizing-x2) 0rem 0rem;max-width:50%;height:var(--sizing-x16)}mbo-customer-products-page .mbo-customer-products-page__actions>mbo-button-incognito-mode .bocc-button__label{color:var(--color-carbon-darker-1000)}mbo-customer-products-page .mbo-customer-products-page__actions>bocc-select-button{--bocc-button-padding: 0rem 0rem 0rem var(--sizing-x2);max-width:50%;height:var(--sizing-x16)}mbo-customer-products-page .mbo-customer-products-page__actions>bocc-select-button .bocc-button__label{color:var(--color-carbon-lighter-700)}mbo-customer-products-page .mbo-customer-products-page__back{--bocc-button-padding: 0rem var(--sizing-x1)}mbo-customer-products-page .mbo-customer-products-page__back .bocc-button{height:var(--sizing-x16)}mbo-customer-products-page .mbo-customer-products-page__contacts{position:absolute;display:flex;right:var(--sizing-x8);bottom:calc(54rem + var(--sizing-safe-bottom, 0rem));flex-direction:column;align-items:flex-end;row-gap:var(--sizing-x6);z-index:var(--z-index-24)}mbo-customer-products-page .mbo-customer-products-page__contacts.condense{transition:bottom .24s var(--standard-curve);transition-delay:.24s;bottom:calc(42rem + var(--sizing-safe-bottom, 0rem))}mbo-customer-products-page .mbo-banner-cards{display:flex;padding:var(--sizing-x8) 0;gap:var(--sizing-x6);justify-content:space-between}mbo-customer-products-page .mbo-banner-cards mbo-banner-card{width:100%}mbo-customer-products-page .mathilde-ads{position:relative;width:100%}mbo-customer-products-page .mathilde-ads--waiting{visibility:hidden}mbo-customer-products-page .mathilde-ads>span{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}mbo-customer-products-page .mathilde-ads>span img{width:100%}mbo-customer-products-page .mathilde-ads .mth_contenedor{margin:0rem;border-radius:var(--sizing-x4);overflow:hidden}\n"],encapsulation:2}),n})(),B=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[P.CommonModule,A.RouterModule.forChild([{path:"",component:h}]),b.P8,b.Oh,b.Zl,b.Dj,b.GM,b.oc,f.k4,f.J5,p.Ep,p.D9,p.Pq,f.uf,p.Y6,p.yb]}),n})()},88844:($,T,s)=>{s.d(T,{YI:()=>F,tc:()=>R,iR:()=>C,jq:()=>g,Hv:()=>e,S6:()=>m,E2:()=>_,V4:()=>x,wp:()=>B,CE:()=>N,YQ:()=>b,ND:()=>p,t1:()=>y});var P=s(6472);class A{constructor(r){this.value=r}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:r}){return this.value.id===r}filtrable(r){return(0,P.hasPattern)(this.value.name,r)}}function b(n){return n.map(r=>new A(r))}class f{constructor(r){this.currency=r}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(r){return this.currency.code===r?.code}filtrable(r){return!0}}function p(n){return n.map(r=>new f(r))}var l=s(39904);class e{constructor(r){this.value=r}get title(){return this.value.label}get description(){return this.value.label}compareTo(r){return this.value.reference===r.reference}filtrable(r){return!0}}const F=l.Bf.map(n=>new e(n));class a{constructor(r,t){this.value=r,this.title=this.value.label,this.description=t?this.value.code:this.value.label}compareTo(r){return this.value===r}filtrable(r){return!0}}const g=new a(l.Gd),m=new a(l.XU),I=new a(l.t$),_=new a(l.j1),x=new a(l.k7),C=[g,m,I,_],R=[new a(l.Gd,!0),new a(l.XU,!0),new a(l.t$,!0),new a(l.j1,!0)];class D{constructor(r){this.product=r}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(r){return this.product.id===r?.id}filtrable(r){return!0}}function y(n){return n.map(r=>new D(r))}var c=s(89148);class d{constructor(r){this.filter=r}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(r){return this.value===r}filtrable(r){return!0}}const N=new d({label:"Todos los productos",short:"Todos",value:c.Gt.None}),S=new d({label:"Cuentas de ahorro",short:"Ahorros",value:c.Gt.SavingAccount}),M=new d({label:"Cuentas corriente",short:"Corrientes",value:c.Gt.CheckingAccount}),O=new d({label:"Depositos electr\xf3nicos",short:"Depositos",value:c.Gt.ElectronicDeposit}),w=new d({label:"Cuentas AFC",short:"AFC",value:c.Gt.AfcAccount}),L=new d({label:"Tarjetas de cr\xe9dito",short:"TC",value:c.Gt.CreditCard}),U=new d({label:"Inversiones",short:"Inversiones",value:c.Gt.CdtAccount}),z=new d({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:c.Gt.Loan}),j=new d({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:c.Gt.ResolvingCredit}),G=new d({label:"Productos Aval",short:"Aval",value:c.Gt.Aval}),V=new d({label:"Productos fiduciarios",short:"Fiducias",value:c.Gt.Trustfund}),h=new d({label:"Otros productos",short:"Otros",value:c.Gt.None}),B={SDA:S,DDA:M,EDA:O,AFC:w,CCA:L,CDA:U,DLA:z,LOC:j,AVAL:G,80:V,MDA:h,NONE:h,SBA:h,VDA:h}}}]);