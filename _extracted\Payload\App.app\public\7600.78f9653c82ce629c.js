(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7600],{87600:(h,d,e)=>{e.r(d),e.d(d,{MboTransferTrustfundAmountPageModule:()=>O});var c=e(17007),f=e(78007),u=e(30263),A=e(15861),v=e(8834),l=e(24495),C=e(39904),i=e(89148),g=e(87903),M=e(95437),I=e(98017),N=e(57544),E=e(40914),b=e(95137),y=e(12884),t=e(99877),P=e(83413),S=e(35641),R=e(48774),F=e(45542);const p=C.Z6.TRANSFERS.TRUSTFUND,{MIN_TRUSTFUND:T}=E.r,D=[i.E$.RentaFijaDinamica,i.E$.MetaCrecimiento,i.E$.MetaPlaneada,i.E$.MetaDecidida];let x=(()=>{class r{constructor(o,n,a,m,z){this.modalConfirmation=o,this.mboProvider=n,this.requestConfiguration=a,this.managerTransfer=m,this.cancelProvider=z,this.confirmation=!1,this.backAction={id:"btn_transfer-trustfund-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(p.DESTINATION)}},this.cancelAction={id:"btn_transfer-trustfund-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new N.FormControl}ngOnInit(){this.initializatedConfiguration()}onSubmit(){var o=this;return(0,A.Z)(function*(){o.hasPenality()?(o.mboProvider.loader.open("Consultando valor de penalizaci\xf3n, for favor espere..."),(yield o.managerTransfer.penality(o.amountControl.value)).when({success:n=>{o.confirmationTransfer(n)},failure:({message:n})=>{o.mboProvider.toast.error(n)}},()=>o.mboProvider.loader.close())):o.managerTransfer.setAmount(o.amountControl.value).when({success:()=>{o.mboProvider.navigation.next(p.CONFIRMATION)}})})()}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({amount:o,confirmation:n,source:a})=>{o&&this.amountControl.setValue(o),this.source=a,this.confirmation=n;const m=[l.C1,l.LU,l.PO];(0,g.VN)(a)&&m.push((0,l.vB)(a.amount)),a.type===i.Gt.Trustfund&&m.push(function j(r){return s=>s&&r-s<T?{id:"minBalanceTrustfund",message:`Debes dejar un m\xednimo de ${(0,v.K)(T)} en tu producto de inversi\xf3n.`}:null}(a.amount)),this.amountControl.setValidators(m)}})}hasPenality(){return this.source.type===i.Gt.Trustfund&&(0,g.G_)(this.source,D)}confirmationTransfer(o){const n=(0,I.dateFormatTemplate)(new Date,"{mx}, {dd} de {aa}"),a=(0,v.K)(o);this.modalConfirmation.execute({logo:"assets/transfers/logos/trustfund-penality.svg",title:"CONFIRMAR MOVIMIENTO",message:`Este retiro tiene una penalizaci\xf3n de <b>${a}</b> a la fecha de operaci\xf3n <b>${n}</b><br>\xbfDeseas continuar con la operaci\xf3n?`,accept:{label:"Continuar",click:()=>this.managerTransfer.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(p.CONFIRMATION)}})},decline:{label:"Cancelar"}})}}return r.\u0275fac=function(o){return new(o||r)(t.\u0275\u0275directiveInject(u.$e),t.\u0275\u0275directiveInject(M.ZL),t.\u0275\u0275directiveInject(b.X),t.\u0275\u0275directiveInject(b.M),t.\u0275\u0275directiveInject(y.S))},r.\u0275cmp=t.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfer-trustfund-amount-page"]],decls:12,vars:9,consts:[[1,"mbo-transfer-trustfund-amount-page__content"],[1,"mbo-transfer-trustfund-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfer-trustfund-amount-page__body"],[1,"mbo-transfer-trustfund-amount-page__message","subtitle2-medium"],["elementId","txt_transfer-trustfund-amount_value","label","Valor a transferir","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-transfer-trustfund-amount-page__footer"],["id","btn_transfer-trustfund-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(o,n){1&o&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"p",4),t.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas transferir? "),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(8,"div",7)(9,"button",8),t.\u0275\u0275listener("click",function(){return n.onSubmit()}),t.\u0275\u0275elementStart(10,"span"),t.\u0275\u0275text(11,"Continuar"),t.\u0275\u0275elementEnd()()()),2&o&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",n.backAction)("rightAction",n.cancelAction),t.\u0275\u0275advance(4),t.\u0275\u0275property("formControl",n.amountControl),t.\u0275\u0275advance(1),t.\u0275\u0275property("color",null==n.source?null:n.source.color)("icon",null==n.source?null:n.source.logo)("title",null==n.source?null:n.source.nickname)("number",null==n.source?null:n.source.publicNumber)("amount",null==n.source?null:n.source.amount),t.\u0275\u0275advance(2),t.\u0275\u0275property("disabled",n.amountControl.invalid))},dependencies:[P.D,S.d,R.J,F.P],styles:["/*!\n * MBO TransferTrustfundAmount Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 12/Jul/2022\n * Updated: 08/Ene/2024\n*/mbo-transfer-trustfund-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-transfer-trustfund-amount-page .mbo-transfer-trustfund-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfer-trustfund-amount-page .mbo-transfer-trustfund-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfer-trustfund-amount-page .mbo-transfer-trustfund-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfer-trustfund-amount-page .mbo-transfer-trustfund-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-trustfund-amount-page .mbo-transfer-trustfund-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfer-trustfund-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20)}}\n"],encapsulation:2}),r})(),O=(()=>{class r{}return r.\u0275fac=function(o){return new(o||r)},r.\u0275mod=t.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=t.\u0275\u0275defineInjector({imports:[c.CommonModule,f.RouterModule.forChild([{path:"",component:x}]),u.D1,u.dH,u.Jx,u.P8]}),r})()},40914:(h,d,e)=>{e.d(d,{R:()=>c,r:()=>f});const c={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},f={MIN_TRUSTFUND:2e5}}}]);