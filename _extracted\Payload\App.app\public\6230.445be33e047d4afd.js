(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6230],{77660:(k,M,t)=>{t.d(M,{M:()=>a,w:()=>i});var a=(()=>{return(e=a||(a={})).INIT="Init",e.END_PROCESS="EndProcess",e.BIOMETRICS_VALIDATION="BiometricsValidation",e.OTP_INICIAL="OtpInicial",e.SECURE_QUESTION="SecureQuestion",e.PASSWORD="Password",a;var e})(),i=(()=>((i||(i={})).FORGOT_PASSWORD="forgotPassword",i))()},24613:(k,M,t)=>{t.d(M,{y:()=>F,n:()=>N});var a=t(15861),i=t(39904),e=t(87956),o=t(53113),d=t(13973),r=t(98699),m=t(77660);class s extends r.PartialSealed{static error(w){return new s("error",w)}static finish(w=""){return new s("finish",w)}static next(w){return new s("next",w)}}var c=t(71776),p=t(42168);class n{constructor(w,x,C,A,j,L,B){this.success=w,this.error=x,this.value=C,this.executionArn=A,this.taskToken=j,this.bodyStep=L,this.errorMessage=B}get message(){return this.errorMessage||"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP01)."}static error(){return new n(!1,!0,void 0,"","",void 0,"Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (FP02).")}}function u(S,w=!1,x=!0){const C=S.body;let A;return C&&(A=Object.keys(C).find(L=>void 0!==C[L])),new n(x,w,A,S.executionArn,S.taskToken,S.body,S.error)}var h=t(51176),g=t(70658),b=t(99877);const _=g.N.cognitoForgotPassword.url;let v=(()=>{class S{constructor(x,C){this.http=x,this.forgotPasswordStore=C}cognitoForgotpassword(){const x=(new c.HttpParams).set("grant_type","client_credentials").set("client_id",g.N.cognitoForgotPassword.clientId).set("client_secret",g.N.cognitoForgotPassword.clientSecret),C=new c.HttpHeaders({"Content-Type":"application/x-www-form-urlencoded"});return(0,p.firstValueFrom)(this.http.post(_,x.toString(),{headers:C}).pipe((0,p.map)(A=>(this.forgotPasswordStore.setCognitoToken(A.access_token),A))))}requestForgotPassword(x,C){var A=this;return(0,a.Z)(function*(){const j=new c.HttpHeaders({Authorization:C});return(0,p.firstValueFrom)(A.http.post(i.bV.FORGOT_PASSWORD,{...x},{headers:j,observe:"response"}).pipe((0,p.map)(L=>{if(206===L.status&&L.body)throw new Error;return u(L.body)}),(0,p.tap)(({bodyStep:L})=>{L&&(A.forgotPasswordStore.setBodystep(L),L.SecureQuestion&&A.forgotPasswordStore.setSecureData(L.SecureQuestion))}),(0,p.catchError)(L=>{throw u(L,!0,!1),L}))).catch(L=>L?u(L,!0,!1):n.error())})()}}return S.\u0275fac=function(x){return new(x||S)(b.\u0275\u0275inject(c.HttpClient),b.\u0275\u0275inject(h.N))},S.\u0275prov=b.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})();var z=t(81536),P=t(95437);let y=(()=>{class S{constructor(x,C,A,j,L,B){this.forgotPasswordRepository=x,this.deviceService=C,this.forgotPasswordStore=A,this.cryptoService=j,this.publicKeyRepository=L,this.mboProvider=B}token(){var x=this;return(0,a.Z)(function*(){try{return yield x.forgotPasswordRepository.cognitoForgotpassword()}catch(C){throw x.mboProvider.toast.error("Lo sentimos, ha ocurrido un error durante el proceso de cambio de contrase\xf1a, por favor intente m\xe1s tarde (cognito)"),C}})()}init(x){var C=this;return(0,a.Z)(function*(){return Promise.all([C.deviceService.getFingerprint(),C.deviceService.getInfo()]).then(([A,j])=>C.service({documentNumber:x.number,documentType:x.type,ipAddress:"0.0.0.0",deviceSerial:`${j.uuid}-${j.model}`,deviceFingerPrint:A})).then(A=>(C.forgotPasswordStore.setDocument(x.type,x.number),C.forgotPasswordStore.setExecutionArn(A.executionArn),C.forgotPasswordStore.setTaskToken(A.taskToken),A))})()}password(x){var C=this;return(0,a.Z)(function*(){const{executionArn:A,taskToken:j,rescue:L}=C.forgotPasswordStore.currentState,V={universalPassword:yield C.publicKeyRepository.requestEnrollment().then(D=>C.cryptoService.encodeRSA(D,x))};return L||(V.responseBiometricsValidation=!0),C.service({executionArn:A,body:V,taskToken:j})})()}rescue(){var x=this;return(0,a.Z)(function*(){const{executionArn:C,taskToken:A,rescue:j}=x.forgotPasswordStore.currentState,L=yield x.deviceService.getInfo();return x.service({executionArn:C,body:{responseBiometricsValidation:!j,...j&&{deviceOS:L.operatingSystem,deviceName:L.name}},taskToken:A}).then(B=>(x.forgotPasswordStore.setExecutionArn(B.executionArn),x.forgotPasswordStore.setTaskToken(B.taskToken),B))})()}otp(x){var C=this;return(0,a.Z)(function*(){const{executionArn:A,taskToken:j}=C.forgotPasswordStore.currentState,L=yield C.deviceService.getInfo();return C.service({executionArn:A,body:{otpValue:(yield C.cryptoService.encodeKeyEnrollment(x)).toString(),deviceOS:L.operatingSystem,deviceName:L.name},taskToken:j}).then(B=>(C.forgotPasswordStore.setExecutionArn(B.executionArn),C.forgotPasswordStore.setTaskToken(B.taskToken),B))})()}product(x){var C=this;return(0,a.Z)(function*(){const{executionArn:A,taskToken:j}=C.forgotPasswordStore.currentState;return C.service({executionArn:A,body:{responseSecureQuestion:x},taskToken:j}).then(L=>(C.forgotPasswordStore.setExecutionArn(L.executionArn),C.forgotPasswordStore.setTaskToken(L.taskToken),L))})()}service(x){const C=this.forgotPasswordStore.getCognitoToken();return this.forgotPasswordRepository.requestForgotPassword(x,C)}}return S.\u0275fac=function(x){return new(x||S)(b.\u0275\u0275inject(v),b.\u0275\u0275inject(e.U8),b.\u0275\u0275inject(h.N),b.\u0275\u0275inject(e.$I),b.\u0275\u0275inject(z.aH),b.\u0275\u0275inject(P.ZL))},S.\u0275prov=b.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})();const{AUTHENTICATION:{FORGOT_PASSWORD:R,ERRORS:E,LOGIN:O}}=i.Z6;let F=(()=>{class S{constructor(x){this.customerService=x}welcome(){return this.customerService.request().then(x=>r.Either.success(x)).catch(({message:x})=>r.Either.failure({message:x}))}}return S.\u0275fac=function(x){return new(x||S)(b.\u0275\u0275inject(e.vZ))},S.\u0275prov=b.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})(),N=(()=>{class S{constructor(x,C,A){this.facialBiometricsStore=x,this.forgotPasswordStore=C,this.forgotPasswordStepService=A}document(x){var C=this;return(0,a.Z)(function*(){try{yield C.forgotPasswordStepService.token();const A=yield C.forgotPasswordStepService.init(x);return C.getNextStepForgotPassword(A)}catch(A){return s.error(A)}})()}password(x){var C=this;return(0,a.Z)(function*(){try{const A=yield C.forgotPasswordStepService.password(x);return C.forgotPasswordStore.setPassword(x),C.getNextStepForgotPassword(A)}catch({message:A}){return s.error(A)}})()}rescue(){var x=this;return(0,a.Z)(function*(){x.forgotPasswordStore.setRescue(!0);try{const C=yield x.forgotPasswordStepService.rescue(),A=x.getNextStepForgotPassword(C);return new Promise((j,L)=>{A.when({next:B=>j(B),error:B=>L(B)})})}catch(C){return Promise.reject(s.error(C))}})()}getCustomerDocument(){const{documentNumber:x,documentType:C}=this.forgotPasswordStore.currentState;return C&&x?new o.dp(C,x):void 0}otp(x){var C=this;return(0,a.Z)(function*(){try{const A=yield C.forgotPasswordStepService.otp(x);return C.getNextStepForgotPassword(A)}catch({message:A}){return s.error(A)}})()}product(x){var C=this;return(0,a.Z)(function*(){try{const A=yield C.forgotPasswordStepService.product(x);return C.getNextStepForgotPassword(A)}catch({message:A}){return s.error(A)}})()}getNextStepForgotPassword(x){const{success:C,value:A,error:j}=x,L=this.getStepForRedirect(A);return C&&A===m.M.END_PROCESS?(this.clearStoreState(),s.finish(L)):j?(this.clearStoreState(),s.error(L)):C?s.next(L):void 0}getStepForRedirect(x){switch(x){case m.M.BIOMETRICS_VALIDATION:return m.M.BIOMETRICS_VALIDATION.toString();case m.M.OTP_INICIAL:return R.OTP_VERIFICATION;case m.M.SECURE_QUESTION:return R.PRODUCT_VERIFICATION;case m.M.PASSWORD:return R.PASSWORD_ASSIGNMENT;case m.M.END_PROCESS:return O;default:return E.DEFAULT_MESSAGE}}clearStoreState(){this.forgotPasswordStore.clearState(),this.facialBiometricsStore.clearState()}}return S.\u0275fac=function(x){return new(x||S)(b.\u0275\u0275inject(d.H),b.\u0275\u0275inject(h.N),b.\u0275\u0275inject(y))},S.\u0275prov=b.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})()},11742:(k,M,t)=>{t.d(M,{r:()=>m});var a=t(30263),i=t(39904),e=t(95437),o=t(24613),d=t(99877);let m=(()=>{class s{constructor(p,f,n){this.modalConfirmationService=p,this.mboProvider=f,this.verifyForgotPasswordStep=n}execute(){this.modalConfirmationService.execute({title:"Abandonar registro",message:"\xbfEstas seguro que no deseas continuar con el cambio de contrase\xf1a en la Banca M\xf3vil?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed()}},decline:{label:"Continuar"}})}cancelConfirmed(){this.verifyForgotPasswordStep.clearStoreState(),this.mboProvider.navigation.back(i.Z6.AUTHENTICATION.LOGIN)}}return s.\u0275fac=function(p){return new(p||s)(d.\u0275\u0275inject(a.$e),d.\u0275\u0275inject(e.ZL),d.\u0275\u0275inject(o.n))},s.\u0275prov=d.\u0275\u0275defineInjectable({token:s,factory:s.\u0275fac,providedIn:"root"}),s})()},51176:(k,M,t)=>{t.d(M,{N:()=>d});var a=t(20691),e=t(99877);let d=(()=>{class r extends a.Store{constructor(){super({rescue:!1,cognitoToken:null})}setDocument(s,c){this.reduce(p=>({...p,documentType:s,documentNumber:c}))}setRescue(s){this.reduce(c=>({...c,rescue:s}))}setExecutionArn(s){this.reduce(c=>({...c,executionArn:s}))}setPassword(s){this.reduce(c=>({...c,password:s}))}setBodystep(s){this.reduce(c=>({...c,body:s}))}setTaskToken(s){this.reduce(c=>({...c,taskToken:s}))}setSecureData(s){this.reduce(c=>({...c,secureData:s}))}getSecureData(){return this.select(({secureData:s})=>s)}setErrorCode(s){this.reduce(c=>({...c,errorCode:s}))}setCognitoToken(s){this.reduce(c=>({...c,cognitoToken:s}))}getExecutionArn(){return this.select(({executionArn:s})=>s)}getTaskToken(){return this.select(({taskToken:s})=>s)}getErrorCode(){return this.select(({errorCode:s})=>s)}getCognitoToken(){return this.select(({cognitoToken:s})=>s)}clearState(){this.reduce(()=>({}))}}return r.\u0275fac=function(s){return new(s||r)},r.\u0275prov=e.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},66230:(k,M,t)=>{t.r(M),t.d(M,{MboForgotPasswordProductPageModule:()=>R});var a=t(17007),i=t(78007),e=t(79798),o=t(30263),d=t(33395),r=t(15861),m=t(24495),c=(t(39904),t(95437)),p=t(57544),f=t(24613),n=t(11742),l=t(51176),u=t(99877),h=t(25317),g=t(52528),b=t(45542),_=t(48774),v=t(895),z=t(19102);let y=(()=>{class E{constructor(F,N,S,w){this.mboProvider=F,this.forgotPasswordStore=N,this.cancelProvider=S,this.verifyForgotPassword=w,this.requesting=!1,this.productControl=new p.FormControl({validators:[m.C1]}),this.cancelAction={id:"btn_forgot-password-product_cancel",label:"Cancelar",disabled:()=>this.requesting,click:()=>{this.cancelProvider.execute()}}}ngOnInit(){this.secureData=this.forgotPasswordStore.getSecureData(),this.productControl.setValidators([m.C1])}get invalid(){return this.productControl.invalid||this.requesting}onSubmit(){var F=this;return(0,r.Z)(function*(){F.requesting=!0,(yield F.verifyForgotPassword.product(F.productControl.value)).when({next:N=>{F.mboProvider.navigation.next(N)},error:N=>{F.mboProvider.navigation.back(N)}},()=>{F.requesting=!1})})()}}return E.\u0275fac=function(F){return new(F||E)(u.\u0275\u0275directiveInject(c.ZL),u.\u0275\u0275directiveInject(l.N),u.\u0275\u0275directiveInject(n.r),u.\u0275\u0275directiveInject(f.n))},E.\u0275cmp=u.\u0275\u0275defineComponent({type:E,selectors:[["mbo-forgot-password-product-page"]],decls:15,vars:7,consts:[[1,"mbo-forgot-password-product-page",3,"header"],["body","",1,"mbo-forgot-password-product-page__content"],["header","",3,"rightAction"],["body","",1,"mbo-forgot-password-product-page__body"],["body","",1,"mbo-forgot-password-product-page__info"],[1,"mbo-forgot-password-product-page__title","subtitle2-medium"],[1,"mbo-forgot-password-product-page__message","body2-medium"],["elementId","pwd_forgot-password-product_number","placeholder","D\xedgite n\xfamero del producto",3,"disabled","formControl"],["footer","",1,"mbo-forgot-password-product-page__footer"],["id","btn_forgot-password-product_submit","bocc-button","raised","boccUtagComponent","click",3,"spinner","disabled","click"]],template:function(F,N){1&F&&(u.\u0275\u0275elementStart(0,"bocc-template-form",0)(1,"div",1),u.\u0275\u0275element(2,"bocc-header-form",2),u.\u0275\u0275elementStart(3,"div",3),u.\u0275\u0275element(4,"mbo-bank-logo"),u.\u0275\u0275elementStart(5,"div",4)(6,"div",5),u.\u0275\u0275text(7," Registro Banca M\xf3vil "),u.\u0275\u0275elementEnd(),u.\u0275\u0275elementStart(8,"p",6),u.\u0275\u0275text(9),u.\u0275\u0275elementEnd(),u.\u0275\u0275element(10,"bocc-password-box",7),u.\u0275\u0275elementEnd()()(),u.\u0275\u0275elementStart(11,"div",8)(12,"button",9),u.\u0275\u0275listener("click",function(){return N.onSubmit()}),u.\u0275\u0275elementStart(13,"span"),u.\u0275\u0275text(14,"Continuar"),u.\u0275\u0275elementEnd()()()()),2&F&&(u.\u0275\u0275property("header",!1),u.\u0275\u0275advance(2),u.\u0275\u0275property("rightAction",N.cancelAction),u.\u0275\u0275advance(7),u.\u0275\u0275textInterpolate1(" ",N.secureData," "),u.\u0275\u0275advance(1),u.\u0275\u0275property("disabled",N.requesting)("formControl",N.productControl),u.\u0275\u0275advance(2),u.\u0275\u0275property("spinner",N.requesting)("disabled",N.invalid))},dependencies:[h.I,g.A,b.P,_.J,v.s,z.r],styles:["mbo-forgot-password-product-page{--mbo-bank-logo-height: 26rem;--pvt-body-rowgap: var(--sizing-x16);--pvt-info-rowgap: var(--sizing-x8)}mbo-forgot-password-product-page .mbo-forgot-password-product-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-body-rowgap);padding-top:var(--sizing-safe-top)}mbo-forgot-password-product-page .mbo-forgot-password-product-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-forgot-password-product-page .mbo-forgot-password-product-page__info{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-info-rowgap)}mbo-forgot-password-product-page .mbo-forgot-password-product-page__title{position:relative;width:100%;text-align:center}mbo-forgot-password-product-page .mbo-forgot-password-product-page__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-forgot-password-product-page .mbo-forgot-password-product-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-forgot-password-product-page .mbo-forgot-password-product-page__footer button{width:100%}@media screen and (max-height: 800px){mbo-forgot-password-product-page{--mbo-bank-logo-height: var(--sizing-x24)}}@media screen and (max-height: 700px){mbo-forgot-password-product-page{--mbo-bank-logo-height: var(--sizing-x22)}}@media screen and (max-height: 600px){mbo-forgot-password-product-page{--mbo-bank-logo-height: var(--sizing-x20);--pvt-body-rowgap: var(--sizing-x12)}}\n"],encapsulation:2}),E})(),R=(()=>{class E{}return E.\u0275fac=function(F){return new(F||E)},E.\u0275mod=u.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=u.\u0275\u0275defineInjector({imports:[a.CommonModule,i.RouterModule.forChild([{path:"",component:y}]),d.kW,o.Av,o.P8,o.Jx,o.sC,e.rw]}),E})()},19102:(k,M,t)=>{t.d(M,{r:()=>d});var a=t(17007),e=t(99877);let d=(()=>{class r{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return r.\u0275fac=function(s){return new(s||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(s,c){1&s&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&s&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",c.src,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),r})()},52701:(k,M,t)=>{t.d(M,{q:()=>r});var a=t(17007),e=t(30263),o=t(99877);let r=(()=>{class m{}return m.\u0275fac=function(c){return new(c||m)},m.\u0275cmp=o.\u0275\u0275defineComponent({type:m,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(c,p){1&c&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"bocc-icon",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()),2&c&&(o.\u0275\u0275classMap(p.classTheme),o.\u0275\u0275advance(3),o.\u0275\u0275property("icon",p.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",p.label," "))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),m})()},55648:(k,M,t)=>{t.d(M,{u:()=>p});var a=t(15861),i=t(17007),o=t(30263),d=t(78506),r=t(99877);function s(f,n){if(1&f){const l=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",2),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(l);const h=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(h.onClick())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd()()}if(2&f){const l=r.\u0275\u0275nextContext();r.\u0275\u0275property("prefixIcon",l.icon)("disabled",l.disabled),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" ",l.label," ")}}function c(f,n){if(1&f){const l=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",3),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(l);const h=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(h.onClick())}),r.\u0275\u0275elementEnd()}if(2&f){const l=r.\u0275\u0275nextContext();r.\u0275\u0275property("bocc-button-action",l.icon)("disabled",l.disabled)}}let p=(()=>{class f{constructor(l){this.preferences=l,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:l})=>{this.isIncognito=l||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var l=this;return(0,a.Z)(function*(){yield l.preferences.toggleIncognito()})()}}return f.\u0275fac=function(l){return new(l||f)(r.\u0275\u0275directiveInject(d.Bx))},f.\u0275cmp=r.\u0275\u0275defineComponent({type:f,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(l,u){1&l&&(r.\u0275\u0275template(0,s,3,3,"button",0),r.\u0275\u0275template(1,c,1,2,"button",1)),2&l&&(r.\u0275\u0275property("ngIf",!u.actionMode),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",u.actionMode))},dependencies:[i.CommonModule,i.NgIf,o.P8,o.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),f})()},72765:(k,M,t)=>{t.d(M,{rw:()=>a.r,qr:()=>i.q,uf:()=>e.u,Z:()=>s,t5:()=>h,$O:()=>u});var a=t(19102),i=t(52701),e=t(55648),o=t(17007),d=t(30263),r=t(99877);const m=["*"];let s=(()=>{class g{constructor(){this.disabled=!1}}return g.\u0275fac=function(_){return new(_||g)},g.\u0275cmp=r.\u0275\u0275defineComponent({type:g,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],ngContentSelectors:m,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(_,v){1&_&&(r.\u0275\u0275projectionDef(),r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3),r.\u0275\u0275projection(4),r.\u0275\u0275elementEnd()()),2&_&&(r.\u0275\u0275classProp("mbo-poster__content--disabled",v.disabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",v.icon))},dependencies:[o.CommonModule,d.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),g})();var c=t(33395),p=t(77279),f=t(87903),n=t(87956),l=t(25317);let u=(()=>{class g{constructor(_){this.eventBusService=_}onCopy(){this.value&&((0,f.Bn)(this.value),this.eventBusService.emit(p.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return g.\u0275fac=function(_){return new(_||g)(r.\u0275\u0275directiveInject(n.Yd))},g.\u0275cmp=r.\u0275\u0275defineComponent({type:g,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(_,v){1&_&&(r.\u0275\u0275elementStart(0,"bocc-icon",0),r.\u0275\u0275listener("click",function(){return v.onCopy()}),r.\u0275\u0275elementEnd()),2&_&&r.\u0275\u0275property("id",v.elementId)},dependencies:[o.CommonModule,d.Zl,c.kW,l.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),g})(),h=(()=>{class g{}return g.\u0275fac=function(_){return new(_||g)},g.\u0275cmp=r.\u0275\u0275defineComponent({type:g,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(_,v){1&_&&r.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&_&&(r.\u0275\u0275property("value",v.value),r.\u0275\u0275advance(1),r.\u0275\u0275property("value",v.value))},dependencies:[o.CommonModule,d.qd,u],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),g})()},79798:(k,M,t)=>{t.d(M,{Vc:()=>i.Vc,rw:()=>a.rw,k4:()=>i.k4,qr:()=>a.qr,uf:()=>a.uf,xO:()=>o.x,A6:()=>e.A,tu:()=>l,Tj:()=>u,GI:()=>B,Uy:()=>V,To:()=>A,w7:()=>L,o2:()=>i.o2,B_:()=>i.B_,fi:()=>i.fi,XH:()=>i.XH,cN:()=>i.cN,Aj:()=>i.Aj,J5:()=>i.J5,DB:()=>Q.D,NH:()=>D.N,ES:()=>G.E,Nu:()=>i.Nu,x6:()=>H.x,KI:()=>J.K,iF:()=>i.iF,u8:()=>q.u,eM:()=>te.e,ZF:()=>oe.Z,wu:()=>ne.w,$n:()=>re.$,KN:()=>ie.K,cV:()=>ae.c,t5:()=>a.t5,$O:()=>a.$O,ZS:()=>ce.Z,sO:()=>se.s,bL:()=>pe,zO:()=>ee.z});var a=t(72765),i=t(27302),e=t(1027),o=t(7427),r=(t(16442),t(17007)),m=t(30263),s=t(44487),c=t.n(s),p=t(13462),f=t(21498),n=t(99877);let l=(()=>{class T{}return T.\u0275fac=function(I){return new(I||T)},T.\u0275mod=n.\u0275\u0275defineNgModule({type:T}),T.\u0275inj=n.\u0275\u0275defineInjector({imports:[r.CommonModule,p.LottieModule.forRoot({player:()=>c()}),a.rw,m.P8,m.Dj,f.P]}),T})(),u=(()=>{class T{ngBoccPortal(I){this.portal=I}onSubmit(){this.portal?.close()}}return T.\u0275fac=function(I){return new(I||T)},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-icon",2),n.\u0275\u0275elementStart(3,"label"),n.\u0275\u0275text(4," \xa1Atenci\xf3n! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p"),n.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),n.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"li",4),n.\u0275\u0275text(11,"Transacciones a celulares."),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"li",4),n.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(14,"li",4),n.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(16,"p",5),n.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(18,"div",6)(19,"button",7),n.\u0275\u0275listener("click",function(){return W.onSubmit()}),n.\u0275\u0275elementStart(20,"span"),n.\u0275\u0275text(21,"Continuar"),n.\u0275\u0275elementEnd()()())},dependencies:[r.CommonModule,m.Zl,m.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),T})();var h=t(7603),g=t(87956),b=t(74520),_=t(39904),v=t(87903);function P(T,U){if(1&T){const I=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",6)(1,"label",7),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"span",8),n.\u0275\u0275text(4,"Tu gerente asignado (a)"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p",8),n.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(7,"button",9),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(I);const Z=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(Z.onEmail(Z.manager.email))}),n.\u0275\u0275elementStart(8,"span",10),n.\u0275\u0275text(9),n.\u0275\u0275elementEnd()()()}if(2&T){const I=n.\u0275\u0275nextContext(2);n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",I.manager.name," "),n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",I.manager.email," ")}}function y(T,U){if(1&T){const I=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),n.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"button",12),n.\u0275\u0275listener("click",function(Z){n.\u0275\u0275restoreView(I);const Y=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(Y.onRetryManager(Z))}),n.\u0275\u0275elementStart(4,"span"),n.\u0275\u0275text(5,"Recargar"),n.\u0275\u0275elementEnd()()()}}function R(T,U){if(1&T&&(n.\u0275\u0275elementStart(0,"div",3),n.\u0275\u0275template(1,P,10,2,"div",4),n.\u0275\u0275template(2,y,6,0,"div",5),n.\u0275\u0275elementEnd()),2&T){const I=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",I.manager),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!I.manager)}}function E(T,U){1&T&&(n.\u0275\u0275elementStart(0,"div",13),n.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),n.\u0275\u0275elementEnd()),2&T&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0))}t(29306);let O=(()=>{class T{constructor(I){this.customerService=I,this.requesting=!1}onRetryManager(I){this.customerService.requestManager(),I.stopPropagation()}onEmail(I){(0,v.Gw)(`mailto:${I}`)}onWhatsapp(){(0,v.Gw)(_.BA.WHATSAPP)}}return T.\u0275fac=function(I){return new(I||T)(n.\u0275\u0275directiveInject(g.vZ))},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,R,3,2,"div",1),n.\u0275\u0275template(2,E,5,4,"div",2),n.\u0275\u0275elementEnd()),2&I&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!W.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",W.requesting))},dependencies:[r.CommonModule,r.NgIf,m.P8,m.Dj,i.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),T})();const F={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},N={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},S={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function x(T,U){if(1&T&&(n.\u0275\u0275elementStart(0,"div",7),n.\u0275\u0275element(1,"mbo-contact-manager",8),n.\u0275\u0275elementEnd()),2&T){const I=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("manager",I.manager)("requesting",I.requesting)}}function C(T,U){if(1&T){const I=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"li",9)(1,"div",10),n.\u0275\u0275listener("click",function(Z){const ue=n.\u0275\u0275restoreView(I).$implicit,be=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(be.onOption(ue,Z))}),n.\u0275\u0275elementStart(2,"label",11),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",12)(5,"div",13),n.\u0275\u0275element(6,"bocc-icon",14),n.\u0275\u0275elementEnd()()()()}if(2&T){const I=U.$implicit;n.\u0275\u0275property("id",I.id),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",I.label," "),n.\u0275\u0275advance(1),n.\u0275\u0275attribute("bocc-theme",I.boccTheme),n.\u0275\u0275advance(2),n.\u0275\u0275property("icon",I.icon)}}let A=(()=>{class T{constructor(I,W,Z){this.utagService=I,this.customerStore=W,this.customerService=Z,this.isManagerEnabled=!1,this.requesting=!1,this.options=[F,N,S]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:I})=>{this.isManagerEnabled=I?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(W=>{this.manager=W.manager,this.requesting=W.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(I){this.portal=I}onOption(I,W){this.utagService.link("click",I.id),this.portal?.send({action:"option",value:I}),W.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return T.\u0275fac=function(I){return new(I||T)(n.\u0275\u0275directiveInject(h.D),n.\u0275\u0275directiveInject(b.f),n.\u0275\u0275directiveInject(g.vZ))},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-contact-information"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("click",function(){return W.onClose()}),n.\u0275\u0275template(1,x,2,2,"div",1),n.\u0275\u0275elementStart(2,"ul",2),n.\u0275\u0275template(3,C,7,4,"li",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),n.\u0275\u0275listener("click",function(){return W.onClose()}),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(6,"div",6),n.\u0275\u0275listener("click",function(){return W.onClose()}),n.\u0275\u0275elementEnd()),2&I&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",W.isManagerEnabled),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",W.options))},dependencies:[r.CommonModule,r.NgForOf,r.NgIf,m.Zl,O],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),T})();var j=t(95437);let L=(()=>{class T{constructor(I,W){this.floatingService=I,this.mboProvider=W,this.contactsFloating=this.floatingService.create(A),this.contactsFloating?.subscribe(({action:Z,value:Y})=>{"option"===Z?this.dispatchOption(Y):this.close()})}subscribe(I){this.subscriber=I}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(I){"PQRS"===I.action?this.mboProvider.openUrl(_.BA.PQRS):this.subscriber&&this.subscriber(I)}}return T.\u0275fac=function(I){return new(I||T)(n.\u0275\u0275inject(m.B7),n.\u0275\u0275inject(j.ZL))},T.\u0275prov=n.\u0275\u0275defineInjectable({token:T,factory:T.\u0275fac,providedIn:"root"}),T})(),B=(()=>{class T{constructor(){this.defenderLineNumber=_._L.DEFENDER_LINE,this.defenderLinePhone=_.WB.DEFENDER_LINE}ngBoccPortal(I){}onEmail(){(0,v.Gw)("mailto:<EMAIL>")}}return T.\u0275fac=function(I){return new(I||T)},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-contact-phones"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"mbo-attention-lines-form"),n.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),n.\u0275\u0275element(5,"bocc-icon",4),n.\u0275\u0275elementStart(6,"span",5),n.\u0275\u0275text(7,"Defensor del consumidor financiero"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),n.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"label",8)(13,"span"),n.\u0275\u0275text(14,"Lorena Cerchar Rosado"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(15,"bocc-badge",9),n.\u0275\u0275text(16," Suplente "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(17,"div",10),n.\u0275\u0275element(18,"bocc-icon",11),n.\u0275\u0275elementStart(19,"div",12)(20,"span",13),n.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(22,"div",10),n.\u0275\u0275element(23,"bocc-icon",14),n.\u0275\u0275elementStart(24,"div",12)(25,"a",15),n.\u0275\u0275text(26),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(27,"span",13),n.\u0275\u0275text(28," Ext. 15318 - 15311 "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(29,"div",10),n.\u0275\u0275element(30,"bocc-icon",16),n.\u0275\u0275elementStart(31,"div",12)(32,"span",17),n.\u0275\u0275listener("click",function(){return W.onEmail()}),n.\u0275\u0275text(33," <EMAIL> "),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(34,"div",10),n.\u0275\u0275element(35,"bocc-icon",18),n.\u0275\u0275elementStart(36,"div",12)(37,"span",13),n.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),n.\u0275\u0275elementEnd()()()()()()),2&I&&(n.\u0275\u0275advance(25),n.\u0275\u0275property("href",W.defenderLinePhone,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",W.defenderLineNumber," "))},dependencies:[r.CommonModule,m.Zl,m.Oh,i.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),T})(),V=(()=>{class T{constructor(){this.whatsappNumber=_._L.WHATSAPP}ngBoccPortal(I){}onClick(){(0,v.Gw)(_.BA.WHATSAPP)}}return T.\u0275fac=function(I){return new(I||T)},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",2)(4,"button",3),n.\u0275\u0275listener("click",function(){return W.onClick()}),n.\u0275\u0275elementStart(5,"span"),n.\u0275\u0275text(6),n.\u0275\u0275elementEnd()()()()),2&I&&(n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate(W.whatsappNumber))},dependencies:[r.CommonModule,m.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),T})();var D=t(10119),H=(t(87677),t(68789)),G=t(10455),Q=t(91642),J=t(10464),q=t(75221),ee=t(88649),te=t(13043),oe=t(38116),ne=t(68819),re=t(19310),ie=t(94614),ae=(t(70957),t(91248),t(4663)),ce=t(13961),se=t(66709),$=t(24495),X=t(57544),le=t(53113);class de extends X.FormGroup{constructor(){const U=new X.FormControl("",[$.zf,$.O_,$.Y2,(0,$.Mv)(24)]),I=new X.FormControl("",[$.C1,$.zf,$.O_,$.Y2,(0,$.Mv)(24)]);super({controls:{description:I,reference:U}}),this.description=I,this.reference=U}setNote(U){this.description.setValue(U?.description),this.reference.setValue(U?.reference)}getNote(){return new le.$H(this.description.value,this.reference.value)}}function me(T,U){if(1&T&&n.\u0275\u0275element(0,"bocc-input-box",7),2&T){const I=n.\u0275\u0275nextContext();n.\u0275\u0275property("formControl",I.formControls.reference)}}let pe=(()=>{class T{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new de}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(I){this.portal=I}}return T.\u0275fac=function(I){return new(I||T)},T.\u0275cmp=n.\u0275\u0275defineComponent({type:T,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(I,W){1&I&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"div",4),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-input-box",5),n.\u0275\u0275template(7,me,1,1,"bocc-input-box",6),n.\u0275\u0275elementEnd()()),2&I&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",W.cancelAction)("rightAction",W.saveAction),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",W.title," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("formControl",W.formControls.description),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",W.requiredReference))},dependencies:[r.CommonModule,r.NgIf,m.Jx,m.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),T})()},35324:(k,M,t)=>{t.d(M,{V:()=>c});var a=t(17007),e=t(30263),o=t(39904),d=t(87903),r=t(99877);function s(p,f){if(1&p){const n=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"a",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(n);const u=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(u.onWhatsapp())}),r.\u0275\u0275elementStart(1,"div",3),r.\u0275\u0275element(2,"bocc-icon",10),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",5)(4,"label",6),r.\u0275\u0275text(5," Whatsapp "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(6,"label",7),r.\u0275\u0275text(7),r.\u0275\u0275elementEnd()()()}if(2&p){const n=r.\u0275\u0275nextContext();r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",n.whatsappNumber," ")}}let c=(()=>{class p{constructor(){this.whatsapp=!1,this.whatsappNumber=o._L.WHATSAPP,this.nationalLineNumber=o._L.NATIONAL_LINE,this.bogotaLineNumber=o._L.BOGOTA_LINE,this.nationalLinePhone=o.WB.NATIONAL_LINE,this.bogotaLinePhone=o.WB.BOGOTA_LINE}onWhatsapp(){(0,d.Gw)(o.BA.WHATSAPP)}}return p.\u0275fac=function(n){return new(n||p)},p.\u0275cmp=r.\u0275\u0275defineComponent({type:p,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(n,l){1&n&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,s,8,1,"a",1),r.\u0275\u0275elementStart(2,"a",2)(3,"div",3),r.\u0275\u0275element(4,"bocc-icon",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"div",5)(6,"label",6),r.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"label",7),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(10,"a",8)(11,"div",3),r.\u0275\u0275element(12,"bocc-icon",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"div",5)(14,"label",6),r.\u0275\u0275text(15," Bogot\xe1 "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(16,"label",7),r.\u0275\u0275text(17),r.\u0275\u0275elementEnd()()()()),2&n&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",l.whatsapp),r.\u0275\u0275advance(1),r.\u0275\u0275property("href",l.nationalLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",l.nationalLineNumber," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("href",l.bogotaLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",l.bogotaLineNumber," "))},dependencies:[a.CommonModule,a.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),p})()},9593:(k,M,t)=>{t.d(M,{k:()=>s});var a=t(17007),e=t(30263),o=t(39904),d=t(95437),r=t(99877);let s=(()=>{class c{constructor(f){this.mboProvider=f,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(o.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(o.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(o.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return c.\u0275fac=function(f){return new(f||c)(r.\u0275\u0275directiveInject(d.ZL))},c.\u0275cmp=r.\u0275\u0275defineComponent({type:c,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(f,n){1&f&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),r.\u0275\u0275listener("click",function(){return n.onProducts()}),r.\u0275\u0275element(3,"bocc-icon",3),r.\u0275\u0275elementStart(4,"label",4),r.\u0275\u0275text(5," Productos "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",5),r.\u0275\u0275listener("click",function(){return n.onTransfers()}),r.\u0275\u0275element(7,"bocc-icon",6),r.\u0275\u0275elementStart(8,"label",4),r.\u0275\u0275text(9," Transferir "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(10,"div",7),r.\u0275\u0275listener("click",function(){return n.onPaymentQR()}),r.\u0275\u0275elementStart(11,"div",8)(12,"div",9),r.\u0275\u0275element(13,"bocc-icon",10),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(14,"bocc-icon",11),r.\u0275\u0275elementStart(15,"label",4),r.\u0275\u0275text(16," Pago QR "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(17,"div",12),r.\u0275\u0275listener("click",function(){return n.onPayments()}),r.\u0275\u0275element(18,"bocc-icon",13),r.\u0275\u0275elementStart(19,"label",4),r.\u0275\u0275text(20," Pagar "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(21,"div",14),r.\u0275\u0275listener("click",function(){return n.onToken()}),r.\u0275\u0275element(22,"bocc-icon",15),r.\u0275\u0275elementStart(23,"label",4),r.\u0275\u0275text(24," Token "),r.\u0275\u0275elementEnd()()()()),2&f&&(r.\u0275\u0275advance(2),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isProducts),r.\u0275\u0275advance(4),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isTransfers),r.\u0275\u0275advance(11),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isPayments),r.\u0275\u0275advance(4),r.\u0275\u0275classProp("bocc-footer-form__element--active",n.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[a.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),c})()},83867:(k,M,t)=>{t.d(M,{o:()=>g});var a=t(17007),e=t(30263),o=t(8834),d=t(98699),c=(t(57544),t(99877));function f(b,_){if(1&b&&(c.\u0275\u0275elementStart(0,"label",11),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&b){const v=c.\u0275\u0275nextContext();c.\u0275\u0275classProp("mbo-currency-box__rate--active",v.hasValue),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate2(" ",v.valueFormat," ",v.rateCode," ")}}function n(b,_){if(1&b&&(c.\u0275\u0275elementStart(0,"div",12),c.\u0275\u0275element(1,"img",13),c.\u0275\u0275elementEnd()),2&b){const v=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("src",v.icon,c.\u0275\u0275sanitizeUrl)}}function l(b,_){if(1&b&&(c.\u0275\u0275elementStart(0,"div",14),c.\u0275\u0275text(1),c.\u0275\u0275elementEnd()),2&b){const v=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",v.currencyCode," ")}}function u(b,_){if(1&b&&(c.\u0275\u0275elementStart(0,"div",15),c.\u0275\u0275element(1,"bocc-icon",16),c.\u0275\u0275elementStart(2,"span",17),c.\u0275\u0275text(3),c.\u0275\u0275elementEnd()()),2&b){const v=c.\u0275\u0275nextContext();c.\u0275\u0275advance(3),c.\u0275\u0275textInterpolate1(" ",null==v.formControl.error?null:v.formControl.error.message," ")}}function h(b,_){if(1&b&&(c.\u0275\u0275elementStart(0,"div",18),c.\u0275\u0275element(1,"bocc-icon",19),c.\u0275\u0275elementStart(2,"span",17),c.\u0275\u0275text(3),c.\u0275\u0275elementEnd()()),2&b){const v=c.\u0275\u0275nextContext();c.\u0275\u0275advance(3),c.\u0275\u0275textInterpolate1(" ",v.helperInfo," ")}}let g=(()=>{class b{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,d.itIsDefined)(this.rate)}get value(){const v=+this.formControl?.value;return isNaN(v)?0:this.hasRate?v/this.rate:0}get valueFormat(){return(0,o.b)({value:this.value,symbol:"$",decimals:!0})}}return b.\u0275fac=function(v){return new(v||b)},b.\u0275cmp=c.\u0275\u0275defineComponent({type:b,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(v,z){1&v&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),c.\u0275\u0275text(3),c.\u0275\u0275elementEnd(),c.\u0275\u0275template(4,f,2,4,"label",3),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"div",4)(6,"div",5),c.\u0275\u0275template(7,n,2,1,"div",6),c.\u0275\u0275element(8,"bocc-currency-field",7),c.\u0275\u0275template(9,l,2,1,"div",8),c.\u0275\u0275elementEnd()(),c.\u0275\u0275template(10,u,4,1,"div",9),c.\u0275\u0275template(11,h,4,1,"div",10),c.\u0275\u0275elementEnd()),2&v&&(c.\u0275\u0275classProp("mbo-currency-box--focused",z.formControl.focused)("mbo-currency-box--error",z.formControl.invalid&&z.formControl.touched)("mbo-currency-box--disabled",z.formControl.disabled||z.disabled),c.\u0275\u0275advance(2),c.\u0275\u0275property("for",z.elementId),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",z.label," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",z.hasRate),c.\u0275\u0275advance(3),c.\u0275\u0275property("ngIf",z.icon),c.\u0275\u0275advance(1),c.\u0275\u0275property("elementId",z.elementId)("placeholder",z.placeholder)("disabled",z.disabled)("formControl",z.formControl),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",z.currencyCode),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",z.formControl.invalid&&z.formControl.touched),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",z.helperInfo&&!(z.formControl.invalid&&z.formControl.touched)))},dependencies:[a.CommonModule,a.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),b})()},85070:(k,M,t)=>{t.d(M,{f:()=>m});var a=t(17007),e=t(78506),o=t(99877);const r=["*"];let m=(()=>{class s{constructor(p){this.session=p}ngOnInit(){this.session.customer().then(p=>this.customer=p)}}return s.\u0275fac=function(p){return new(p||s)(o.\u0275\u0275directiveInject(e._I))},s.\u0275cmp=o.\u0275\u0275defineComponent({type:s,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(p,f){1&p&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",2),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()),2&p&&(o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(null==f.customer?null:f.customer.shortName))},dependencies:[a.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),s})()},65887:(k,M,t)=>{t.d(M,{X:()=>p});var a=t(17007),e=t(99877),d=t(30263),r=t(24495);function c(f,n){1&f&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let p=(()=>{class f{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[r.C1]:[]),this.unsubscription=this.documentType.subscribe(l=>{l&&(this.updateNumber(l,this.required),this.inputType=this.getInputType(l))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(l){if(l.required){const u=l.required.currentValue;this.documentType.setValidators(u?[r.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,u)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(l){return"PA"===l.code?"text":"number"}updateNumber(l,u){const h=this.validatorsForNumber(l,u);this.documentNumber.setValidators(h),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(l,u){return this.validatorsFromType(l).concat(u?[r.C1]:[])}maxLength(l){return u=>u&&u.length>l?{id:"maxLength",message:`Debe tener m\xe1ximo ${l} caracteres`}:null}validatorsFromType(l){switch(l.code){case"PA":return[r.JF];case"NIT":return[r.X1,this.maxLength(15)];default:return[r.X1]}}}return f.\u0275fac=function(l){return new(l||f)},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(l,u){1&l&&(e.\u0275\u0275template(0,c,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&l&&(e.\u0275\u0275property("ngIf",u.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",u.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",u.elementSelectId)("label",u.labelType)("suggestions",u.documents)("disabled",u.disabled)("formControl",u.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",u.elementInputId)("label",u.labelNumber)("type",u.inputType)("disabled",u.disabled)("formControl",u.documentNumber))},dependencies:[a.CommonModule,a.NgIf,d.DT,d.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),f})()},78021:(k,M,t)=>{t.d(M,{c:()=>f});var a=t(17007),e=t(30263),o=t(7603),d=t(98699),m=t(99877);function c(n,l){if(1&n){const u=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",5),m.\u0275\u0275listener("click",function(){m.\u0275\u0275restoreView(u);const g=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(g.onAction(g.leftAction))}),m.\u0275\u0275elementStart(1,"span"),m.\u0275\u0275text(2),m.\u0275\u0275elementEnd()()}if(2&n){const u=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",u.leftAction.id)("bocc-button",u.leftAction.type||"flat")("prefixIcon",u.leftAction.prefixIcon)("disabled",u.itIsDisabled(u.leftAction))("hidden",u.itIsHidden(u.leftAction)),m.\u0275\u0275advance(2),m.\u0275\u0275textInterpolate(u.leftAction.label)}}function p(n,l){if(1&n){const u=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",6),m.\u0275\u0275listener("click",function(){const b=m.\u0275\u0275restoreView(u).$implicit,_=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(_.onAction(b))}),m.\u0275\u0275elementEnd()}if(2&n){const u=l.$implicit,h=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",u.id)("type",u.type||"flat")("bocc-button-action",u.icon)("disabled",h.itIsDisabled(u))("hidden",h.itIsHidden(u))}}let f=(()=>{class n{constructor(u){this.utagService=u,this.rightActions=[]}itIsDisabled({disabled:u}){return(0,d.evalValueOrFunction)(u)}itIsHidden({hidden:u}){return(0,d.evalValueOrFunction)(u)}onAction(u){const{id:h}=u;h&&this.utagService.link("click",h),u.click()}}return n.\u0275fac=function(u){return new(u||n)(m.\u0275\u0275directiveInject(o.D))},n.\u0275cmp=m.\u0275\u0275defineComponent({type:n,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(u,h){1&u&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1),m.\u0275\u0275template(2,c,3,6,"button",2),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(3,"div",3),m.\u0275\u0275template(4,p,1,5,"button",4),m.\u0275\u0275elementEnd()()),2&u&&(m.\u0275\u0275advance(2),m.\u0275\u0275property("ngIf",h.leftAction),m.\u0275\u0275advance(2),m.\u0275\u0275property("ngForOf",h.rightActions))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),n})()},27302:(k,M,t)=>{t.d(M,{Vc:()=>a.V,k4:()=>i.k,o2:()=>e.o,B_:()=>s,fi:()=>c.f,XH:()=>p.X,cN:()=>u.c,Aj:()=>h.A,J5:()=>R.J,Nu:()=>F,iF:()=>L});var a=t(35324),i=t(9593),e=t(83867),o=t(17007),d=t(99877);function m(B,V){if(1&B){const D=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"div",2),d.\u0275\u0275listener("click",function(){const G=d.\u0275\u0275restoreView(D).$implicit,Q=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(Q.onClickCurrency(G))}),d.\u0275\u0275elementStart(1,"div",3),d.\u0275\u0275element(2,"img",4)(3,"img",5),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"label",6),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()()}if(2&B){const D=V.$implicit,K=d.\u0275\u0275nextContext();d.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",K.isEnabled(D)),d.\u0275\u0275advance(2),d.\u0275\u0275property("src",D.enabledIcon,d.\u0275\u0275sanitizeUrl),d.\u0275\u0275advance(1),d.\u0275\u0275property("src",D.disabledIcon,d.\u0275\u0275sanitizeUrl),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate1(" ",D.label," ")}}t(57544);let s=(()=>{class B{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[D]=this.currencies;this.formControl.setValue(D)}}ngOnChanges(D){const{currencies:K}=D;if(K){const[H]=K.currentValue;this.formControl&&this.formControl.setValue(H)}}isEnabled(D){return D===this.formControl?.value}onClickCurrency(D){this.formControl&&!this.disabled&&this.formControl.setValue(D)}changeCurriencies(D){if(D.currencies){const K=D.currencies.currentValue,[H]=K;this.formControl&&this.formControl.setValue(H)}}}return B.\u0275fac=function(D){return new(D||B)},B.\u0275cmp=d.\u0275\u0275defineComponent({type:B,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[d.\u0275\u0275NgOnChangesFeature,d.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(D,K){1&D&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275template(1,m,6,5,"div",1),d.\u0275\u0275elementEnd()),2&D&&(d.\u0275\u0275classProp("mbo-currency-toggle--disabled",K.disabled),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngForOf",K.currencies))},dependencies:[o.CommonModule,o.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),B})();var c=t(85070),p=t(65887),f=t(30263),u=t(78021),h=t(50689),_=(t(7603),t(98699),t(72765)),R=t(88014);function E(B,V){if(1&B&&(d.\u0275\u0275elementStart(0,"div",4),d.\u0275\u0275element(1,"img",5),d.\u0275\u0275elementEnd()),2&B){const D=d.\u0275\u0275nextContext();d.\u0275\u0275advance(1),d.\u0275\u0275property("src",D.src,d.\u0275\u0275sanitizeUrl)}}const O=["*"];let F=(()=>{class B{}return B.\u0275fac=function(D){return new(D||B)},B.\u0275cmp=d.\u0275\u0275defineComponent({type:B,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],ngContentSelectors:O,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(D,K){1&D&&(d.\u0275\u0275projectionDef(),d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275template(1,E,2,1,"div",1),d.\u0275\u0275elementStart(2,"div",2)(3,"div",3),d.\u0275\u0275projection(4),d.\u0275\u0275elementEnd()()()),2&D&&(d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",K.src))},dependencies:[o.CommonModule,o.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),B})();var N=t(24495);const S=/[A-Z]/,w=/[a-z]/,x=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,C=B=>B&&!S.test(B)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,A=B=>B&&!w.test(B)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,j=B=>B&&!x.test(B)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let L=(()=>{class B{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([N.C1,A,C,j,(0,N.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const D=this.formControl.errors.reduce((H,{id:G})=>[...H,G],[]),K=D.includes("required");this.smallInvalid=D.includes("smallCase")||K,this.capitalInvalid=D.includes("capitalCase")||K,this.specialCharInvalid=D.includes("specialChar")||K,this.minLengthInvalid=D.includes("minlength")||K})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return B.\u0275fac=function(D){return new(D||B)},B.\u0275cmp=d.\u0275\u0275defineComponent({type:B,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(D,K){1&D&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275element(1,"bocc-password-box",1),d.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),d.\u0275\u0275text(4," Min\xfascula "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(5,"mbo-poster",4),d.\u0275\u0275text(6," May\xfascula "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(7,"mbo-poster",5),d.\u0275\u0275text(8," Especial "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(9,"mbo-poster",6),d.\u0275\u0275text(10," Caracteres "),d.\u0275\u0275elementEnd()()()),2&D&&(d.\u0275\u0275advance(1),d.\u0275\u0275property("elementId",K.elementId)("disabled",K.disabled)("formControl",K.formControl),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",K.smallInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",K.capitalInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",K.specialCharInvalid),d.\u0275\u0275advance(2),d.\u0275\u0275property("disabled",K.minLengthInvalid))},dependencies:[o.CommonModule,f.sC,_.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),B})()},50689:(k,M,t)=>{t.d(M,{A:()=>r});var a=t(17007),e=t(99877);const d=["*"];let r=(()=>{class m{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return m.\u0275fac=function(c){return new(c||m)},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(c,p){1&c&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&c&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",p.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[a.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),m})()},88014:(k,M,t)=>{t.d(M,{J:()=>d});var a=t(17007),e=t(99877);let d=(()=>{class r{}return r.\u0275fac=function(s){return new(s||r)},r.\u0275cmp=e.\u0275\u0275defineComponent({type:r,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(s,c){1&s&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[a.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),r})()},21498:(k,M,t)=>{t.d(M,{P:()=>n});var a=t(17007),e=t(30263),o=t(99877);function r(l,u){if(1&l&&o.\u0275\u0275element(0,"bocc-card-product-summary",7),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",h.information.product.color)("icon",h.information.product.icon)("number",h.information.product.number)("title",h.information.product.title)("subtitle",h.information.product.subtitle)}}function m(l,u){if(1&l&&o.\u0275\u0275element(0,"bocc-card-summary",8),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.standard.header)("title",h.information.standard.title)("subtitle",h.information.standard.subtitle)("detail",h.information.standard.detail)}}function s(l,u){if(1&l&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.amount.header)("amount",h.information.amount.value)("symbol",h.information.amount.symbol)("amountSmall",h.information.amount.small)}}function c(l,u){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(h.information.text.content)}}function p(l,u){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",12),o.\u0275\u0275element(1,"bocc-icon",13),o.\u0275\u0275elementStart(2,"span",14),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",15),o.\u0275\u0275elementStart(5,"span",14),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",h.information.datetime.time," ")}}function f(l,u){if(1&l&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&l){const h=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",h.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",h.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",h.information.badge.label," ")}}let n=(()=>{class l{}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=o.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(h,g){1&h&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,r,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,m,1,4,"bocc-card-summary",2),o.\u0275\u0275template(3,s,1,4,"bocc-card-summary",3),o.\u0275\u0275template(4,c,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,p,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,f,3,4,"bocc-card-summary",6),o.\u0275\u0275elementEnd()),2&h&&(o.\u0275\u0275property("ngSwitch",g.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),l})()},7427:(k,M,t)=>{t.d(M,{x:()=>n});var a=t(17007),e=t(30263),o=t(87903),r=(t(29306),t(77279)),m=t(87956),s=t(68789),c=t(13961),p=t(99877);let n=(()=>{class l{constructor(h,g){this.eventBusService=h,this.onboardingScreenService=g,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,o.Bn)(this.product.tagAval),this.eventBusService.emit(r.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(c.Z)),this.tagAvalonboarding.open()}}return l.\u0275fac=function(h){return new(h||l)(p.\u0275\u0275directiveInject(m.Yd),p.\u0275\u0275directiveInject(s.x))},l.\u0275cmp=p.\u0275\u0275defineComponent({type:l,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(h,g){1&h&&(p.\u0275\u0275elementStart(0,"bocc-card-product",0),p.\u0275\u0275listener("key",function(){return g.onTagAval()})("onboarding",function(){return g.onBoarding()}),p.\u0275\u0275elementEnd()),2&h&&(p.\u0275\u0275classMap(g.product.bank.className),p.\u0275\u0275property("iconTitle",g.iconTitle)("title",g.product.nickname||g.product.name)("icon",g.product.logo)("tagAval",g.product.tagAvalFormat)("actions",g.actions)("color",g.product.color)("code",g.product.shortNumber)("label",g.product.label)("amount",g.product.amount)("incognito",g.incognito)("displayCard",!0)("statusLabel",null==g.product.status?null:g.product.status.label)("statusColor",null==g.product.status?null:g.product.status.color)("cromaline",!0)("msgError",g.msgError))},dependencies:[a.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),l})()},1027:(k,M,t)=>{t.d(M,{A:()=>h});var a=t(17007),i=t(72765),e=t(30263),o=t(99877);function d(g,b){if(1&g&&o.\u0275\u0275element(0,"bocc-card-product-summary",8),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",_.information.product.color)("icon",_.information.product.icon)("number",_.information.product.number)("title",_.information.product.title)("subtitle",_.information.product.subtitle)}}function r(g,b){if(1&g&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.standard.header)("title",_.information.standard.title)("subtitle",_.information.standard.subtitle)}}function m(g,b){if(1&g&&o.\u0275\u0275element(0,"bocc-card-summary",10),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.amount.header)("amount",_.information.amount.value)("symbol",_.information.amount.symbol)}}function s(g,b){if(1&g&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(_.information.text.content)}}function c(g,b){if(1&g&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",13),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",16),o.\u0275\u0275elementStart(5,"span",15),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",_.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",_.information.datetime.time," ")}}function p(g,b){if(1&g&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",17),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd()()),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.date.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",_.information.date.date," ")}}function f(g,b){if(1&g&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&g){const _=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",_.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",_.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",_.information.badge.label," ")}}let n=(()=>{class g{}return g.\u0275fac=function(_){return new(_||g)},g.\u0275cmp=o.\u0275\u0275defineComponent({type:g,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(_,v){1&_&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,d,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,r,1,3,"bocc-card-summary",2),o.\u0275\u0275template(3,m,1,3,"bocc-card-summary",3),o.\u0275\u0275template(4,s,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,c,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,p,4,3,"bocc-card-summary",6),o.\u0275\u0275template(7,f,3,4,"bocc-card-summary",7),o.\u0275\u0275elementEnd()),2&_&&(o.\u0275\u0275property("ngSwitch",v.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","date"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[a.CommonModule,a.NgSwitch,a.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),g})();function l(g,b){1&g&&o.\u0275\u0275element(0,"mbo-card-information-element",8),2&g&&o.\u0275\u0275property("information",b.$implicit)}const u=["*"];let h=(()=>{class g{constructor(){this.skeleton=!1,this.informations=[]}}return g.\u0275fac=function(_){return new(_||g)},g.\u0275cmp=o.\u0275\u0275defineComponent({type:g,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:u,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(_,v){1&_&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"mbo-bank-logo",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"div",4),o.\u0275\u0275element(5,"div",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275projection(7),o.\u0275\u0275template(8,l,1,1,"mbo-card-information-element",7),o.\u0275\u0275elementEnd()()),2&_&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("result",!0),o.\u0275\u0275advance(5),o.\u0275\u0275property("ngForOf",v.informations))},dependencies:[a.CommonModule,a.NgForOf,i.rw,n],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),g})()},16442:(k,M,t)=>{t.d(M,{u:()=>_});var a=t(99877),e=t(17007),d=t(13462),m=t(19102),s=t(45542),c=t(65467),p=t(21498);function f(v,z){if(1&v&&(a.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),a.\u0275\u0275text(1),a.\u0275\u0275elementEnd()),2&v){const P=a.\u0275\u0275nextContext();a.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",P.template.skeleton),a.\u0275\u0275property("secondary",!0)("active",P.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",P.template.header.subtitle," ")}}function n(v,z){1&v&&a.\u0275\u0275element(0,"mbo-card-information",16),2&v&&a.\u0275\u0275property("information",z.$implicit)}function l(v,z){if(1&v&&(a.\u0275\u0275elementStart(0,"div",14),a.\u0275\u0275projection(1),a.\u0275\u0275template(2,n,1,1,"mbo-card-information",15),a.\u0275\u0275elementEnd()),2&v){const P=a.\u0275\u0275nextContext();a.\u0275\u0275advance(2),a.\u0275\u0275property("ngForOf",P.template.informations)}}function u(v,z){1&v&&(a.\u0275\u0275elementStart(0,"div",17),a.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),a.\u0275\u0275elementEnd()),2&v&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",!0)("secondary",!0))}function h(v,z){if(1&v){const P=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",23),a.\u0275\u0275listener("click",function(){const E=a.\u0275\u0275restoreView(P).$implicit,O=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(O.onAction(E))}),a.\u0275\u0275elementStart(1,"span"),a.\u0275\u0275text(2),a.\u0275\u0275elementEnd()()}if(2&v){const P=z.$implicit;a.\u0275\u0275property("bocc-button",P.type)("prefixIcon",P.prefixIcon),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate(P.label)}}function g(v,z){if(1&v&&(a.\u0275\u0275elementStart(0,"div",21),a.\u0275\u0275template(1,h,3,3,"button",22),a.\u0275\u0275elementEnd()),2&v){const P=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("ngForOf",P.template.actions)}}const b=["*"];let _=(()=>{class v{constructor(){this.disabled=!1,this.action=new a.EventEmitter}onAction({event:P}){this.action.emit(P)}}return v.\u0275fac=function(P){return new(P||v)},v.\u0275cmp=a.\u0275\u0275defineComponent({type:v,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:b,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(P,y){1&P&&(a.\u0275\u0275projectionDef(),a.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),a.\u0275\u0275element(3,"mbo-bank-logo",3),a.\u0275\u0275elementStart(4,"div",4),a.\u0275\u0275element(5,"ng-lottie",5),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),a.\u0275\u0275text(7),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(8,f,2,5,"bocc-skeleton-text",7),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(9,"div",8),a.\u0275\u0275element(10,"div",9),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(11,l,3,1,"div",10),a.\u0275\u0275template(12,u,4,5,"div",11),a.\u0275\u0275elementEnd(),a.\u0275\u0275template(13,g,2,1,"div",12)),2&P&&(a.\u0275\u0275classProp("animation",!y.template.skeleton),a.\u0275\u0275advance(3),a.\u0275\u0275property("result",!0),a.\u0275\u0275advance(2),a.\u0275\u0275property("options",y.template.header.animation),a.\u0275\u0275advance(1),a.\u0275\u0275property("active",y.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275textInterpolate1(" ",y.template.header.title," "),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",y.template.skeleton||y.template.header.subtitle),a.\u0275\u0275advance(3),a.\u0275\u0275property("ngIf",!y.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",y.template.skeleton),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",y.template.actions.length&&!y.disabled))},dependencies:[e.NgForOf,e.NgIf,d.LottieComponent,m.r,s.P,c.D,p.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),v})()},10119:(k,M,t)=>{t.d(M,{N:()=>u});var a=t(17007),e=t(99877),d=t(30263),r=t(7603),m=t(98699);function c(h,g){if(1&h&&e.\u0275\u0275element(0,"bocc-diamond",14),2&h){const b=g.$implicit,_=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",_.itIsSelected(b))}}function p(h,g){if(1&h){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionLeft.id)("bocc-button",b.footerActionLeft.type)("prefixIcon",b.footerActionLeft.prefixIcon)("disabled",b.itIsDisabled(b.footerActionLeft))("hidden",b.itIsHidden(b.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionLeft.label)}}function f(h,g){if(1&h){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onAction(v.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&h){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",b.footerActionRight.id)("bocc-button",b.footerActionRight.type)("prefixIcon",b.footerActionRight.prefixIcon)("disabled",b.itIsDisabled(b.footerActionRight))("hidden",b.itIsHidden(b.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(b.footerActionRight.label)}}const n=["*"];let u=(()=>{class h{constructor(b,_){this.ref=b,this.utagService=_,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((b,_)=>_),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(b){return b===this.currentPosition}itIsDisabled({disabled:b}){return(0,m.evalValueOrFunction)(b)}itIsHidden({hidden:b}){return(0,m.evalValueOrFunction)(b)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(b){const{id:_}=b;_&&this.utagService.link("click",_),b.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(b){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(b),this.automatic=!1,this.setTranslatePosition(b)}setTranslatePosition(b){this.translateX=b*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(b){this.transformContent=`translateX(${b}px)`}emitPosition(b){this.finished||(this.finished=b+1===this.elements.length),this.position.emit({position:b,finished:this.finished})}getPositionSlide(b){return b>=this.elements.length?this.elements.length-1:b<0?0:b}setTouchHandler(b){let _=0,v=0;b.addEventListener("touchstart",z=>{if(z.changedTouches.length){const{clientX:P}=z.changedTouches.item(0);_=0,this.touched=!0,v=P}}),b.addEventListener("touchmove",z=>{if(z.changedTouches.length){const P=z.changedTouches.item(0),y=P.clientX-v;v=P.clientX,this.translateX+=y,_+=y,this.setTranslateContent(this.translateX)}}),b.addEventListener("touchend",z=>{this.touched=!1,z.changedTouches.length&&(Math.abs(_)/this.widthBody*100>=40&&(_>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return h.\u0275fac=function(b){return new(b||h)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(r.D))},h.\u0275cmp=e.\u0275\u0275defineComponent({type:h,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:n,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(b,_){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return _.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return _.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,c,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return _.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,p,3,6,"button",13),e.\u0275\u0275template(16,f,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&b&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",_.headerActionLeft)("rightAction",_.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",_.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",_.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",_.widthContent)("transform",_.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",_.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!_.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",_.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!_.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",_.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.footerActionRight))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,d.P8,d.u1,d.ou,d.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),h})()},68789:(k,M,t)=>{t.d(M,{x:()=>r});var a=t(7603),i=t(10455),e=t(87677),o=t(99877);let r=(()=>{class m{constructor(c){this.portalService=c}information(){this.portal||(this.portal=this.portalService.container({component:i.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(c,p){return this.portalService.container({component:c,container:e.C,props:{container:p?.containerProps,component:p?.componentProps}})}}return m.\u0275fac=function(c){return new(c||m)(o.\u0275\u0275inject(a.v))},m.\u0275prov=o.\u0275\u0275defineInjectable({token:m,factory:m.\u0275fac,providedIn:"root"}),m})()},87677:(k,M,t)=>{t.d(M,{C:()=>e});var a=t(99877);let e=(()=>{class o{constructor(r){this.ref=r,this.visible=!1,this.visibleChange=new a.EventEmitter}open(r=0){setTimeout(()=>{this.changeVisible(!0)},r)}close(r=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},r)}append(r){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(r)}ngBoccPortal(r){this.portal=r}changeVisible(r){this.visible=r,this.visibleChange.emit(r)}}return o.\u0275fac=function(r){return new(r||o)(a.\u0275\u0275directiveInject(a.ElementRef))},o.\u0275cmp=a.\u0275\u0275defineComponent({type:o,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(r,m){1&r&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"div",1),a.\u0275\u0275elementEnd()),2&r&&a.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",m.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),o})()},10455:(k,M,t)=>{t.d(M,{E:()=>m});var a=t(17007),e=t(99877),d=t(27302),r=t(10119);let m=(()=>{class s{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(p){this.portal=p}onPosition({finished:p}){this.finished=p,p&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return s.\u0275fac=function(p){return new(p||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(p,f){1&p&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(l){return f.onPosition(l)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&p&&e.\u0275\u0275property("footerActionLeft",f.footerLeft)("footerActionRight",f.footerRight)("gradient",!0)},dependencies:[a.CommonModule,d.Nu,r.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),s})()},91642:(k,M,t)=>{t.d(M,{D:()=>_});var a=t(17007),e=t(99877),d=t(30263),r=t(87542),m=t(70658),s=t(3372),c=t(87956),p=t(72765);function f(v,z){1&v&&e.\u0275\u0275element(0,"mbo-bank-logo")}function n(v,z){1&v&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function l(v,z){if(1&v&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&v){const P=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",P.verifying)}}function u(v,z){1&v&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function h(v,z){if(1&v){const P=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(P);const R=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(R.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&v){const P=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",P.verifying)}}const g=["*"],{OtpInputSuperuser:b}=s.M;let _=(()=>{class v{constructor(P,y,R,E){this.ref=P,this.otpService=y,this.deviceService=R,this.preferencesService=E,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=r.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new r.yV}ngOnInit(){this.otpService.onCode(P=>{this.otpControls.setCode(P),this.otpControls.valid&&this.onAutocomplete(P)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(P){const{documentNumber:y}=P;y&&this.preferencesService.applyFunctionality(b,y.currentValue).then(R=>{this.itIsDocumentSuperuser=R})}get otpVisible(){return!m.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return m.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&m.N.otpReadonlyMobile}onAutocomplete(P){this.code.emit(P)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return v.\u0275fac=function(P){return new(P||v)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(c.no),e.\u0275\u0275directiveInject(c.U8),e.\u0275\u0275directiveInject(c.yW))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:g,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(P,y){1&P&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,f,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,n,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(E){return y.onAutocomplete(E)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,l,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,u,7,0,"div",8),e.\u0275\u0275template(13,h,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&P&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",y.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",y.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",y.otpVisible),e.\u0275\u0275property("formControls",y.otpControls)("readonly",y.otpReadonly)("mobile",y.otpMobile)("disabled",y.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",y.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",y.isIos))},dependencies:[a.CommonModule,a.NgIf,d.P8,d.Yx,p.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),v})()},10464:(k,M,t)=>{t.d(M,{K:()=>m});var a=t(17007),e=t(99877),d=t(22816);const r=["*"];let m=(()=>{class s{constructor(p){this.ref=p,this.scroller=new d.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(p){this.scroller.reset(p.target)}}return s.\u0275fac=function(p){return new(p||s)(e.\u0275\u0275directiveInject(e.ElementRef))},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(p,f){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(l){return f.onScroll(l)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&p&&e.\u0275\u0275classProp("mbo-page__content--start",f.scrollStart)("mbo-page__content--end",f.scrollEnd)},dependencies:[a.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),s})()},75221:(k,M,t)=>{t.d(M,{u:()=>s});var a=t(17007),e=t(30263),o=t(27302),r=(t(88649),t(99877));let s=(()=>{class c{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return c.\u0275fac=function(f){return new(f||c)},c.\u0275cmp=r.\u0275\u0275defineComponent({type:c,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(f,n){1&f&&r.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&f&&(r.\u0275\u0275property("formControl",n.passwordControl.controls.password)("disabled",n.disabled)("elementId",n.elementPasswordId),r.\u0275\u0275advance(1),r.\u0275\u0275property("elementId",n.elementConfirmId)("disabled",n.disabled)("formControl",n.passwordControl.controls.repeatPassword))},dependencies:[a.CommonModule,e.sC,o.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),c})()},88649:(k,M,t)=>{t.d(M,{z:()=>o});var a=t(57544),i=t(24495);class o extends a.FormGroup{constructor(){const r=new a.FormControl(""),m=new a.FormControl("",[i.C1,(d=r,r=>r&&r!==d.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var d;super({controls:{password:r,repeatPassword:m}})}get password(){return this.controls.password.value}}},13043:(k,M,t)=>{t.d(M,{e:()=>h});var a=t(17007),e=t(99877),d=t(30263),s=(t(57544),t(27302));function c(g,b){1&g&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function p(g,b){if(1&g&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&g){const _=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",_.title," ")}}function f(g,b){if(1&g){const _=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const P=e.\u0275\u0275restoreView(_).$implicit,y=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(y.onProduct(P))}),e.\u0275\u0275elementEnd()}if(2&g){const _=b.$implicit,v=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",_.color)("icon",_.logo)("title",_.nickname)("number",_.publicNumber)("detail",_.bank.name)("ghost",v.ghost)}}function n(g,b){if(1&g&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,p,2,1,"div",8),e.\u0275\u0275template(3,f,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&g){const _=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",_.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",_.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!_.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",_.msgError," ")}}function l(g,b){1&g&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const u=["*"];let h=(()=>{class g{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(_){return this.productControl?.value?.id===_.id}onProduct(_){this.select.emit(_),this.productControl?.setValue(_)}}return g.\u0275fac=function(_){return new(_||g)},g.\u0275cmp=e.\u0275\u0275defineComponent({type:g,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:u,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(_,v){1&_&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,c,3,0,"div",1),e.\u0275\u0275template(2,n,6,5,"div",2),e.\u0275\u0275template(3,l,3,2,"div",3),e.\u0275\u0275elementEnd()),2&_&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!v.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",v.skeleton))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,d.w_,s.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),g})()},38116:(k,M,t)=>{t.d(M,{Z:()=>m});var a=t(17007),e=t(99877),d=t(30263);function r(s,c){if(1&s){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(p).$implicit,u=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(u.onAction(l))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&s){const p=c.$implicit,f=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(f.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",f.itIsDisabled(p)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(f.theme(p)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",p.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",p.label," ")}}let m=(()=>{class s{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(p){return p.requiredInformation&&p.errorInformation}theme(p){return this.itIsDisabled(p)?"none":p.theme}onAction(p){!this.itIsDisabled(p)&&this.action.emit(p.type)}}return s.\u0275fac=function(p){return new(p||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(p,f){1&p&&e.\u0275\u0275template(0,r,6,8,"div",0),2&p&&e.\u0275\u0275property("ngForOf",f.actions)},dependencies:[a.CommonModule,a.NgForOf,d.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),s})()},68819:(k,M,t)=>{t.d(M,{w:()=>O});var a=t(17007),e=t(99877),d=t(30263),r=t(39904),c=(t(57544),t(78506)),f=(t(29306),t(87903)),n=t(95437),l=t(27302),u=t(70957),h=t(91248),g=t(13961),b=t(68789),_=t(33395),v=t(25317);function z(F,N){if(1&F){const S=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(x){e.\u0275\u0275restoreView(S);const C=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(C.onBoarding(x))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(S);const x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onCopyKey(x.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(S);const x=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(x.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&F){const S=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",S.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",S.product.tagAval)}}function P(F,N){if(1&F&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&F){const S=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",S.digitalSection)("currencyCode",null==S.currencyControl.value?null:S.currencyControl.value.code)("hidden",S.itIsVisibleMovements)}}function y(F,N){if(1&F&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&F){const S=N.$implicit,w=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",S)("currencyCode",null==w.currencyControl.value?null:w.currencyControl.value.code)}}const R=[[["","header",""]],"*"],E=["[header]","*"];let O=(()=>{class F{constructor(S,w,x){this.mboProvider=S,this.managerInformation=w,this.onboardingScreenService=x,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(S){const{movements:w,sections:x}=S;w&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!w.currentValue),x&&this.product&&this.refreshComponent(this.product,x.currentValue),this.managerInformation.requestInfoBody().then(C=>{C.when({success:({canEditTagAval:A})=>{this.canEditTagAval=A}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(S){(0,f.Bn)(S),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(r.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(S,w){const x=(0,f.A2)(S);if(this.sectionPosition=0,w?.length){const C=w.map(({title:A},j)=>({label:A,value:j}));x&&(this.headerMovements.value=this.sections.length,C.push(this.headerMovements)),this.headers=C}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const C=[{label:"Error",value:1}];x&&C.unshift(this.headerMovements),this.headers=C}}onBoarding(S){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(g.Z)),this.tagAvalonboarding.open(),S.stopPropagation()}}return F.\u0275fac=function(S){return new(S||F)(e.\u0275\u0275directiveInject(n.ZL),e.\u0275\u0275directiveInject(c.vu),e.\u0275\u0275directiveInject(b.x))},F.\u0275cmp=e.\u0275\u0275defineComponent({type:F,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:E,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(S,w){1&S&&(e.\u0275\u0275projectionDef(R),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(C){return w.sectionPosition=C}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,z,15,2,"div",4),e.\u0275\u0275template(6,P,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,y,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&S&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",w.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",w.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",w.headers)("value",w.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",w.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==w.product?null:w.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",w.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",w.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",w.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",w.movements)("header",w.header)("product",w.product)("currencyCode",null==w.currencyControl.value?null:w.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",w.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!w.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,d.Gf,d.qw,d.P8,d.Dj,d.qd,u.K,h.I,l.Aj,_.kW,v.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),F})()},19310:(k,M,t)=>{t.d(M,{$:()=>P});var a=t(17007),i=t(99877),e=t(30263),o=t(87903);let d=(()=>{class y{transform(E,O,F=" "){return(0,o.rd)(E,O,F)}}return y.\u0275fac=function(E){return new(E||y)},y.\u0275pipe=i.\u0275\u0275definePipe({name:"codeSplit",type:y,pure:!0}),y})(),r=(()=>{class y{}return y.\u0275fac=function(E){return new(E||y)},y.\u0275mod=i.\u0275\u0275defineNgModule({type:y}),y.\u0275inj=i.\u0275\u0275defineInjector({imports:[a.CommonModule]}),y})();t(57544);var s=t(70658),c=t(78506),f=(t(29306),t(87956)),n=t(72765),l=t(27302);function u(y,R){if(1&y){const E=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",18),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(E);const F=i.\u0275\u0275nextContext();return i.\u0275\u0275resetView(F.onDigital())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275property("prefixIcon",E.digitalIcon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(E.digitalIncognito?"Ver datos":"Ocultar datos")}}function h(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"span"),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate(null==E.product?null:E.product.publicNumber)}}function g(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"span",19),i.\u0275\u0275text(1),i.\u0275\u0275pipe(2,"codeSplit"),i.\u0275\u0275elementEnd()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind2(2,1,E.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":E.digitalNumber,4)," ")}}function b(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"bocc-badge",20),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275attribute("bocc-theme",null==E.product||null==E.product.status?null:E.product.status.color),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",null==E.product||null==E.product.status?null:E.product.status.label," ")}}function _(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"div",21),i.\u0275\u0275element(1,"bocc-progress-bar",22),i.\u0275\u0275elementEnd()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("theme",E.progressBarTheme)("width",E.progressBarStatus)}}function v(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"div",23)(1,"label",24),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),i.\u0275\u0275element(5,"bocc-amount",26),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(6,"mbo-button-incognito-mode",27),i.\u0275\u0275elementEnd()()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",null==E.product?null:E.product.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!E.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("amount",null==E.product?null:E.product.amount)("incognito",E.incognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("actionMode",!0)("hidden",!E.product)}}function z(y,R){if(1&y&&(i.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),i.\u0275\u0275text(3,"Vence"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"span",19),i.\u0275\u0275text(5),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(6,"div",29)(7,"label",20),i.\u0275\u0275text(8,"CVC"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(9,"span",19),i.\u0275\u0275text(10),i.\u0275\u0275elementEnd()()()),2&y){const E=i.\u0275\u0275nextContext();i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",E.digitalIncognito?"\u2022\u2022 | \u2022\u2022":E.digitalExpAt," "),i.\u0275\u0275advance(5),i.\u0275\u0275textInterpolate1(" ",E.digitalIncognito?"\u2022\u2022\u2022":E.digitalCVC," ")}}let P=(()=>{class y{constructor(E,O){this.managerPreferences=E,this.digitalService=O,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new i.EventEmitter,this.digital=new i.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:E})=>{this.incognito=E})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:E,value:O})=>{this.product&&this.product.id===E&&this.refreshDigitalState(O)}))}ngOnChanges(E){const{product:O}=E;if(O&&O.currentValue){const F=O.currentValue;this.refreshDigitalState(this.digitalService.request(F.id)),this.activateDigitalCountdown(F)}}ngOnDestroy(){this.unsubscriptions.forEach(E=>E())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(E){const{incognito:O,requiredRequest:F,cvc:N,expirationAt:S,number:w}=E;this.digitalIncognito=O,this.digitalExpAt=S,this.digitalCVC=N,this.digitalNumber=w,this.digitalIcon=O?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=F}activateDigitalCountdown(E){const{countdown$:O}=this.digitalService.request(E.id);O?(this.progressBarRequired=!0,this.progressBarPercent=100,O.subscribe(F=>{this.progressBarRequired=!(F>=s.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-F/s.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return y.\u0275fac=function(E){return new(E||y)(i.\u0275\u0275directiveInject(c.Bx),i.\u0275\u0275directiveInject(f.ZP))},y.\u0275cmp=i.\u0275\u0275defineComponent({type:y,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[i.\u0275\u0275NgOnChangesFeature,i.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(E,O){1&E&&(i.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),i.\u0275\u0275listener("click",function(){return O.onClose()}),i.\u0275\u0275elementStart(3,"span"),i.\u0275\u0275text(4,"Atr\xe1s"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(5,"mbo-currency-toggle",3),i.\u0275\u0275template(6,u,3,2,"button",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(7,"div",5)(8,"div",6),i.\u0275\u0275element(9,"img",7),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),i.\u0275\u0275text(12),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),i.\u0275\u0275template(15,h,2,1,"span",12),i.\u0275\u0275template(16,g,3,4,"span",13),i.\u0275\u0275template(17,b,2,2,"bocc-badge",14),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(18,_,2,2,"div",15),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(19,v,7,6,"div",16),i.\u0275\u0275template(20,z,11,2,"div",17),i.\u0275\u0275elementEnd()),2&E&&(i.\u0275\u0275classMap(null==O.product?null:O.product.bank.className),i.\u0275\u0275property("color",null==O.product?null:O.product.color),i.\u0275\u0275advance(5),i.\u0275\u0275property("formControl",O.currencyControl)("currencies",O.currencies)("hidden",!(null!=O.product&&O.product.bank.isOccidente)||O.currencies.length<2),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital),i.\u0275\u0275advance(3),i.\u0275\u0275property("src",null==O.product?null:O.product.logo,i.\u0275\u0275sanitizeUrl),i.\u0275\u0275advance(2),i.\u0275\u0275property("active",!O.product),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",(null==O.product?null:O.product.nickname)||(null==O.product?null:O.product.name)," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("active",!O.product),i.\u0275\u0275advance(1),i.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!O.product),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=O.product&&O.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",(null==O.product||null==O.product.status?null:O.product.status.label)&&O.digitalIncognito),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",O.progressBarRequired),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!(null!=O.product&&O.product.isDigital)),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital))},dependencies:[a.CommonModule,a.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,r,d,n.uf,l.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),y})()},94614:(k,M,t)=>{t.d(M,{K:()=>c});var a=t(17007),e=t(30263),o=t(39904),r=(t(29306),t(95437)),m=t(99877);let c=(()=>{class p{constructor(n){this.mboProvider=n,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:n,id:l,parentProduct:u}=this.product;"covered"===n&&u?this.goToPage(u.id,l):this.goToPage(l)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(n,l){this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:n,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:l})}}return p.\u0275fac=function(n){return new(n||p)(m.\u0275\u0275directiveInject(r.ZL))},p.\u0275cmp=m.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(n,l){1&n&&(m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275listener("click",function(){return l.onComponent()}),m.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),m.\u0275\u0275text(5),m.\u0275\u0275elementEnd()(),m.\u0275\u0275elementStart(6,"div",4),m.\u0275\u0275element(7,"bocc-amount",5),m.\u0275\u0275elementEnd()()),2&n&&(m.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",l.skeleton),m.\u0275\u0275advance(2),m.\u0275\u0275property("active",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.dateFormat," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("active",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==l.movement?null:l.movement.description," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("hidden",l.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275property("amount",null==l.movement?null:l.movement.value)("currencyCode",null==l.movement?null:l.movement.currencyCode)("theme",!0))},dependencies:[a.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),p})()},70957:(k,M,t)=>{t.d(M,{K:()=>_});var a=t(15861),i=t(17007),o=t(99877),r=t(30263),m=t(78506),s=t(39904),p=(t(29306),t(87903)),f=t(95437),n=t(27302),l=t(94614);function u(v,z){if(1&v){const P=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",8),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(P);const R=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(R.onRedirectAll())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Ver todos"),o.\u0275\u0275elementEnd()()}}function h(v,z){if(1&v&&(o.\u0275\u0275elementStart(0,"div",5)(1,"label",6),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(3,u,3,0,"button",7),o.\u0275\u0275elementEnd()),2&v){const P=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",(null==P.productMovements||null==P.productMovements.range?null:P.productMovements.range.label)||"Sin resultados"," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==P.productMovements?null:P.productMovements.range)}}function g(v,z){if(1&v&&o.\u0275\u0275element(0,"mbo-product-info-movement",9),2&v){const P=z.$implicit,y=o.\u0275\u0275nextContext();o.\u0275\u0275property("movement",P)("product",y.product)}}function b(v,z){if(1&v&&(o.\u0275\u0275elementStart(0,"mbo-message-empty",10),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&v){const P=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",P.msgError," ")}}let _=(()=>{class v{constructor(P,y){this.mboProvider=P,this.managerProductMovements=y,this.header=!0,this.requesting=!1}ngOnChanges(P){const{currencyCode:y,product:R}=P;if(!this.movements&&(R||y)){const E=y?.currentValue||this.currencyCode,O=R?.currentValue||this.product;this.currentMovements=void 0,(0,p.A2)(O)&&this.requestFirstPage(O,E)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:P,id:y,parentProduct:R}=this.product;this.mboProvider.navigation.next(s.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===P?{productId:R?.id,coveredCardId:y}:{productId:y},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(P,y){var R=this;return(0,a.Z)(function*(){R.requesting=!0,(yield R.managerProductMovements.requestForProduct({product:P,currencyCode:y})).when({success:E=>{R.currentMovements=E},failure:()=>{R.currentMovements=void 0}},()=>{R.requesting=!1})})()}}return v.\u0275fac=function(P){return new(P||v)(o.\u0275\u0275directiveInject(f.ZL),o.\u0275\u0275directiveInject(m.sy))},v.\u0275cmp=o.\u0275\u0275defineComponent({type:v,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(P,y){1&P&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,h,4,2,"div",1),o.\u0275\u0275elementStart(2,"div",2),o.\u0275\u0275template(3,g,1,2,"mbo-product-info-movement",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(4,b,2,1,"mbo-message-empty",4),o.\u0275\u0275elementEnd()),2&P&&(o.\u0275\u0275property("hidden",y.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",y.header),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",null==y.productMovements?null:y.productMovements.firstPage),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==y.productMovements?null:y.productMovements.isEmpty))},dependencies:[i.CommonModule,i.NgForOf,i.NgIf,r.P8,l.K,n.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),v})()},91248:(k,M,t)=>{t.d(M,{I:()=>p});var a=t(17007),e=t(30263),o=t(99877);function r(f,n){if(1&f&&o.\u0275\u0275element(0,"bocc-amount",10),2&f){const l=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("amount",l.value)("currencyCode",l.currencyCode)}}function m(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"span"),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&f){const l=o.\u0275\u0275nextContext().$implicit,u=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",u.incognito||u.section.incognito?l.mask:l.value," ")}}function s(f,n){if(1&f){const l=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(l);const h=o.\u0275\u0275nextContext().$implicit;return o.\u0275\u0275resetView(h.action.click())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&f){const l=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("suffixIcon",l.action.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(l.action.label)}}function c(f,n){if(1&f&&(o.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275template(5,r,1,2,"bocc-amount",7),o.\u0275\u0275template(6,m,2,1,"span",8),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(7,s,3,2,"button",9),o.\u0275\u0275elementEnd()),2&f){const l=n.$implicit,u=o.\u0275\u0275nextContext();o.\u0275\u0275property("hidden",(null==l?null:l.currencyCode)!==u.currencyCode),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",l.label," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",l.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!l.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",l.action&&!(u.incognito||u.section.incognito))}}let p=(()=>{class f{constructor(){this.currencyCode="COP"}}return f.\u0275fac=function(l){return new(l||f)},f.\u0275cmp=o.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(l,u){1&l&&(o.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),o.\u0275\u0275template(2,c,8,5,"li",2),o.\u0275\u0275elementEnd()()),2&l&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",u.section.datas))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),f})()},4663:(k,M,t)=>{t.d(M,{c:()=>n});var a=t(17007),e=t(99877),d=t(30263),r=t(27302);function m(l,u){1&l&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function s(l,u){if(1&l&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&l){const h=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",h.title," ")}}function c(l,u){if(1&l){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const _=e.\u0275\u0275restoreView(h).$implicit,v=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(v.onProduct(_))}),e.\u0275\u0275elementEnd()}if(2&l){const h=u.$implicit,g=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",h.color)("icon",h.logo)("title",h.nickname)("number",h.publicNumber)("ghost",g.ghost)("amount",h.amount)("tagAval",h.tagAvalFormat),e.\u0275\u0275attribute("amount-status",g.amountColorProduct(h))}}function p(l,u){1&l&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const f=["*"];let n=(()=>{class l{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(h){return h.amount>0?"success":h.amount<0?"danger":"empty"}onProduct(h){this.select.emit(h)}}return l.\u0275fac=function(h){return new(h||l)},l.\u0275cmp=e.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(h,g){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,s,2,1,"div",5),e.\u0275\u0275template(8,c,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,p,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",g.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",g.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!g.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!g.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[a.CommonModule,a.NgForOf,a.NgIf,d.w_,d.P8,r.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),l})()},13961:(k,M,t)=>{t.d(M,{Z:()=>m});var a=t(17007),e=t(27302),o=t(10119),d=t(99877);let m=(()=>{class s{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(p){this.portal=p}}return s.\u0275fac=function(p){return new(p||s)},s.\u0275cmp=d.\u0275\u0275defineComponent({type:s,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(p,f){1&p&&(d.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),d.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"p"),d.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),d.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(9,"p"),d.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),d.\u0275\u0275elementEnd()()()),2&p&&d.\u0275\u0275property("headerActionRight",f.headerAction)("gradient",!0)},dependencies:[a.CommonModule,e.Nu,o.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),s})()},66709:(k,M,t)=>{t.d(M,{s:()=>m});var a=t(17007),i=t(99877),e=t(30263),o=t(87542);let d=(()=>{class s{ngBoccPortal(p){}}return s.\u0275fac=function(p){return new(p||s)},s.\u0275cmp=i.\u0275\u0275defineComponent({type:s,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(p,f){1&p&&(i.\u0275\u0275elementStart(0,"div",0)(1,"label",1),i.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),i.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),i.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(11,"p",4),i.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),i.\u0275\u0275element(13,"br"),i.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),i.\u0275\u0275element(15,"br"),i.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),i.\u0275\u0275elementStart(17,"span"),i.\u0275\u0275text(18,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(19," Configuraci\xf3n "),i.\u0275\u0275elementStart(20,"span"),i.\u0275\u0275text(21,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(22," Seguridad "),i.\u0275\u0275elementStart(23,"span"),i.\u0275\u0275text(24,">"),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(25," Activar Token Mobile."),i.\u0275\u0275element(26,"br"),i.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),i.\u0275\u0275elementEnd()()()())},dependencies:[a.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),s})();const r=["*"];let m=(()=>{class s{constructor(p,f){this.ref=p,this.bottomSheetService=f,this.verifying=!1,this.tokenLength=o.Xi,this.code=new i.EventEmitter,this.tokenControls=new o.b2}ngOnInit(){const p=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(d),setTimeout(()=>{p?.focus()},120)}onAutocomplete(p){this.code.emit(p)}onInfo(){this.infoSheet?.open()}}return s.\u0275fac=function(p){return new(p||s)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(e.fG))},s.\u0275cmp=i.\u0275\u0275defineComponent({type:s,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(p,f){1&p&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1),i.\u0275\u0275projection(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"p",2),i.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p",2),i.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),i.\u0275\u0275elementStart(7,"a"),i.\u0275\u0275text(8),i.\u0275\u0275elementEnd(),i.\u0275\u0275text(9,". "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(10,"div",3),i.\u0275\u0275element(11,"img",4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),i.\u0275\u0275listener("autocomplete",function(l){return f.onAutocomplete(l)}),i.\u0275\u0275text(13," Ingresa tu clave "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(14,"button",6),i.\u0275\u0275listener("click",function(){return f.onInfo()}),i.\u0275\u0275elementStart(15,"span"),i.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),i.\u0275\u0275elementEnd()()()),2&p&&(i.\u0275\u0275advance(8),i.\u0275\u0275textInterpolate1("",f.tokenLength," d\xedgitos"),i.\u0275\u0275advance(4),i.\u0275\u0275property("disabled",f.verifying)("formControls",f.tokenControls),i.\u0275\u0275advance(2),i.\u0275\u0275property("disabled",f.verifying))},dependencies:[a.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),s})()},13973:(k,M,t)=>{t.d(M,{H:()=>d});var a=t(20691),e=t(99877);let d=(()=>{class r extends a.Store{constructor(){super({})}setDocument(s,c){this.reduce(p=>({...p,documentType:s,documentNumber:c}))}setParamsSDK(s,c){this.reduce(p=>({...p,params:s,presignedUrls:c}))}setUiDevice(s){this.reduce(c=>({...c,uiDevice:s}))}setStep(s){this.reduce(c=>({...c,currentStep:s}))}setCaptureFace(s){this.reduce(c=>({...c,captureFace:s}))}setCaptureFrontSide(s){this.reduce(c=>({...c,captureFrontSide:s}))}setCaptureBackSide(s){this.reduce(c=>({...c,captureBackSide:s}))}setUrlLiveness(s){this.reduce(c=>({...c,UploadLiveness:s}))}setUrlDocument(s,c){this.reduce(p=>({...p,UploadDocumentFrontSide:s,UploadDocumentBackSide:c}))}setProcessExecutor(s){this.reduce(c=>({...c,processExecutor:s}))}clearState(){this.reduce(()=>({}))}getCaptureIdFront(){return this.select(({captureFrontSide:s})=>s)}getCaptureIdBack(){return this.select(({captureBackSide:s})=>s)}getCaptureFace(){return this.select(({captureFace:s})=>s)}getProcessExecutor(){return this.select(({processExecutor:s})=>s)}}return r.\u0275fac=function(s){return new(s||r)},r.\u0275prov=e.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},22816:(k,M,t)=>{t.d(M,{S:()=>a});class a{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);