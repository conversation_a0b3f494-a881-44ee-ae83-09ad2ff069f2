(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7632],{87632:(A,s,a)=>{a.r(s),a.d(s,{MboTransfiyaPendingConfirmationPageModule:()=>I});var d=a(17007),f=a(78007),m=a(79798),o=a(30263),p=a(15861),g=a(39904),u=a(95437),y=a(17698),b=a(73004),n=a(99877),v=a(10464),h=a(48774),P=a(17941),C=a(45542);function M(t,r){if(1&t&&n.\u0275\u0275element(0,"bocc-card-summary",13),2&t){const e=n.\u0275\u0275nextContext();n.\u0275\u0275property("detail",null==e.transfiya||null==e.transfiya.pending?null:e.transfiya.pending.description)}}const l=g.Z6.TRANSFERS.TRANSFIYA.PENDING;let T=(()=>{class t{constructor(e,i,c){this.mboProvider=e,this.requestConfiguration=i,this.cancelProvider=c,this.backAction={id:"btn_transfiya-pending-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(l.SOURCE)}},this.cancelAction={id:"btn_transfiya-pending-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}}}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(l.RESULT)}initializatedConfiguration(){var e=this;return(0,p.Z)(function*(){(yield e.requestConfiguration.confirmation()).when({success:({transfiya:i})=>{e.transfiya=i}})})()}}return t.\u0275fac=function(e){return new(e||t)(n.\u0275\u0275directiveInject(u.ZL),n.\u0275\u0275directiveInject(y.xt),n.\u0275\u0275directiveInject(b.c))},t.\u0275cmp=n.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfiya-pending-confirmation-page"]],decls:17,vars:7,consts:[[1,"mbo-transfiya-pending-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfiya-pending-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfiya-pending-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],["header","DESTINO",3,"subtitle"],["header","LA SUMA DE",3,"amount"],["header","DESDE",3,"title","subtitle"],["header","DESCRIPCI\xd3N",3,"detail",4,"ngIf"],[1,"mbo-transfiya-pending-confirmation-page__footer"],["id","btn_transfiya-pending-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"click"],["header","DESCRIPCI\xd3N",3,"detail"]],template:function(e,i){1&e&&(n.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),n.\u0275\u0275element(3,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),n.\u0275\u0275text(7," \xbfDeseas transferirle a? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"div",6),n.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),n.\u0275\u0275template(12,M,1,1,"bocc-card-summary",10),n.\u0275\u0275elementEnd()()()(),n.\u0275\u0275elementStart(13,"div",11)(14,"button",12),n.\u0275\u0275listener("click",function(){return i.onSubmit()}),n.\u0275\u0275elementStart(15,"span"),n.\u0275\u0275text(16,"Transferir"),n.\u0275\u0275elementEnd()()()()),2&e&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("leftAction",i.backAction)("rightAction",i.cancelAction),n.\u0275\u0275advance(6),n.\u0275\u0275property("subtitle",null==i.transfiya||null==i.transfiya.pending?null:i.transfiya.pending.phone),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==i.transfiya||null==i.transfiya.pending?null:i.transfiya.pending.amount),n.\u0275\u0275advance(1),n.\u0275\u0275property("title",null==i.transfiya||null==i.transfiya.product?null:i.transfiya.product.nickname)("subtitle",null==i.transfiya||null==i.transfiya.product?null:i.transfiya.product.number),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==i.transfiya||null==i.transfiya.pending?null:i.transfiya.pending.description))},dependencies:[d.NgIf,v.K,h.J,P.D,C.P],styles:["/*!\n * MBO TransfiyaPendingConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-pending-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-pending-confirmation-page .mbo-transfiya-pending-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-pending-confirmation-page .mbo-transfiya-pending-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-pending-confirmation-page .mbo-transfiya-pending-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-pending-confirmation-page .mbo-transfiya-pending-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),t})(),I=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[d.CommonModule,f.RouterModule.forChild([{path:"",component:T}]),m.KI,o.Jx,o.DM,o.P8,o.b6,o.oc]}),t})()}}]);