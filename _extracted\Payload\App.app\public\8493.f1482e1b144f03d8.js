(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8493],{16892:(M,b,e)=>{e.d(b,{K:()=>g}),e(57544);var o=e(80439),s=e(99877);let g=(()=>{class c{constructor(){this.input=""}onKeydown(a){!o.regOnlyNumber.test(a.key)&&"Backspace"!==a.code&&a.preventDefault()}onBlur(){this.formControl.touch()}onInput(a){const{value:p}=a.target,l=p.replace(/ /g,"");this.formControl.setValue(l),this.input=function v(c){let y=0;return"\u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf".replace(/[\u25cf]/g,()=>c[y++]||"").trim()}(l)}}return c.\u0275fac=function(a){return new(a||c)},c.\u0275cmp=s.\u0275\u0275defineComponent({type:c,selectors:[["mbo-activate-field"]],inputs:{formControl:"formControl"},decls:1,vars:1,consts:[["placeholder","\u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf \u25cf\u25cf\u25cf\u25cf","maxlength","19",3,"value","keydown","blur","input"]],template:function(a,p){1&a&&(s.\u0275\u0275elementStart(0,"input",0),s.\u0275\u0275listener("keydown",function(d){return p.onKeydown(d)})("blur",function(){return p.onBlur()})("input",function(d){return p.onInput(d)}),s.\u0275\u0275elementEnd()),2&a&&s.\u0275\u0275property("value",p.input)},styles:["/*!\n * MBO ActivateField Component\n * v1.0.1\n * Author: MB Frontend Developers\n * Created: 01/Ene/2024\n * Updated: 01/Ene/2024\n*/mbo-activate-field{position:relative;display:block}mbo-activate-field input{background:transparent;width:100%;height:var(--body2-line-height);line-height:var(--body2-line-height);padding:0rem;cursor:text;border:none;outline:none;color:var(--color-carbon-lighter-700);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:.5rem;text-align:var(--bocc-field-input-text-align)}mbo-activate-field input::placeholder{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),c})()},86263:(M,b,e)=>{e.d(b,{LZ:()=>f,Hs:()=>y,gI:()=>d}),e(16892);var h=e(17007),o=e(99877);let f=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=o.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=o.\u0275\u0275defineInjector({imports:[h.CommonModule]}),n})();var s=e(39904),m=e(95437),r=e(2460),v=e(45542),g=e(48774);const{SECURITY:c}=s.Z6.CUSTOMER;let y=(()=>{class n{constructor(i){this.mboProvider=i,this.cancelAction={id:"btn_traveler-bluescreen_cancel",label:"Cancelar",click:()=>{this.close(!1)}}}ngBoccPortal(i){this.portal=i}onSubmit(){this.close(!0)}close(i){this.portal?.close(),setTimeout(()=>{i&&this.mboProvider.navigation.next(c.TRAVELER.HOME),this.portal?.destroy()},240)}}return n.\u0275fac=function(i){return new(i||n)(o.\u0275\u0275directiveInject(m.ZL))},n.\u0275cmp=o.\u0275\u0275defineComponent({type:n,selectors:[["mbo-traveler-bluescreen"]],decls:16,vars:1,consts:[[1,"bocc-bluescreen__content"],[1,"bocc-bluescreen__header",3,"rightAction"],[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_traveler-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(i,_){1&i&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"bocc-header-form",1),o.\u0275\u0275elementStart(2,"div",2)(3,"div",3),o.\u0275\u0275element(4,"bocc-icon",4),o.\u0275\u0275elementStart(5,"label"),o.\u0275\u0275text(6," Evitar bloqueo en otro pa\xeds "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(7,"ul",5)(8,"li",6),o.\u0275\u0275text(9," Para evitar fraudes internacionales, las compras con tus tarjetas d\xe9bito y cr\xe9dito en otros pa\xedses est\xe1n restringidas. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(10,"li",6),o.\u0275\u0275text(11," Inf\xf3rmanos cu\xe1ndo estar\xe1s de viaje y evita bloqueos en tus productos cuando los uses en el exterior. "),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(12,"div",7)(13,"button",8),o.\u0275\u0275listener("click",function(){return _.onSubmit()}),o.\u0275\u0275elementStart(14,"span"),o.\u0275\u0275text(15,"Informar sobre viaje"),o.\u0275\u0275elementEnd()()()),2&i&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("rightAction",_.cancelAction))},dependencies:[r.Z,v.P,g.J],styles:["mbo-traveler-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),n})();var a=e(30263);e(39260);let d=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=o.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=o.\u0275\u0275defineInjector({imports:[h.CommonModule,a.Zl,a.P8]}),n})()},39260:(M,b,e)=>{e.d(b,{U:()=>s});var t=e(99877),o=e(2460),f=e(45542);let s=(()=>{class m{}return m.\u0275fac=function(v){return new(v||m)},m.\u0275cmp=t.\u0275\u0275defineComponent({type:m,selectors:[["mbo-traveler-history-element"]],decls:28,vars:0,consts:[[1,"mbo-traveler-history-element__content"],[1,"mbo-traveler-history-element__header"],[1,"mbo-traveler-history-element__data","input"],["icon","arrow-input"],[1,"mbo-traveler-history-element__data","output"],["icon","arrow-output"],[1,"mbo-traveler-history-element__body"],[1,"mbo-traveler-history-element__data","destination"],["icon","travel-world"],[1,"mbo-traveler-history-element__actions"],["bocc-button","flat","suffixIcon","list-open"]],template:function(v,g){1&v&&(t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"label"),t.\u0275\u0275element(4,"bocc-icon",3),t.\u0275\u0275elementStart(5,"span"),t.\u0275\u0275text(6,"ENTRADA"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(7,"span"),t.\u0275\u0275text(8,"Abr. 15, 2023"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(9,"div",4)(10,"label"),t.\u0275\u0275element(11,"bocc-icon",5),t.\u0275\u0275elementStart(12,"span"),t.\u0275\u0275text(13,"SALIDA"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(14,"span"),t.\u0275\u0275text(15,"Abr. 24, 2023"),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(16,"div",6)(17,"div",7)(18,"label"),t.\u0275\u0275element(19,"bocc-icon",8),t.\u0275\u0275elementStart(20,"span"),t.\u0275\u0275text(21,"DESTINO"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(22,"span"),t.\u0275\u0275text(23,"Varios paises"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(24,"div",9)(25,"button",10)(26,"span"),t.\u0275\u0275text(27,"Acciones"),t.\u0275\u0275elementEnd()()()()())},dependencies:[o.Z,f.P],styles:["/*!\n * MBO TravelerHistoryElement Component\n * v1.0.1\n * Author: MB Frontend Developers\n * Created: 28/Jun/2024\n * Updated: 08/Jul/2024\n*/mbo-traveler-history-element{--pvt-icon-color: var(--color-blue-700);position:relative;display:block}mbo-traveler-history-element .mbo-traveler-history-element__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x6);box-sizing:border-box;border:var(--border-1) solid var(--color-carbon-lighter-300);border-radius:var(--sizing-x4)}mbo-traveler-history-element .mbo-traveler-history-element__header{display:flex;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1);align-items:center}mbo-traveler-history-element .mbo-traveler-history-element__body{display:flex;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1);align-items:center}mbo-traveler-history-element .mbo-traveler-history-element__data{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1)}mbo-traveler-history-element .mbo-traveler-history-element__data.input{--pvt-icon-color: var(--color-semantic-success-700)}mbo-traveler-history-element .mbo-traveler-history-element__data.output{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-traveler-history-element .mbo-traveler-history-element__data>label{--bocc-icon-dimension: var(--sizing-x8);display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-400)}mbo-traveler-history-element .mbo-traveler-history-element__data>label>bocc-icon{color:var(--pvt-icon-color)}mbo-traveler-history-element .mbo-traveler-history-element__data>label>span{font-weight:var(--font-weight-semibold)}mbo-traveler-history-element .mbo-traveler-history-element__data>span{padding-left:10rem;box-sizing:border-box;font-weight:var(--font-weight-medium);font-size:var(--smalltext-size);letter-spacing:var(--smalltext-letter-spacing);line-height:var(--smalltext-line-height);color:var(--color-carbon-lighter-700)}mbo-traveler-history-element .mbo-traveler-history-element__actions{--bocc-button-padding: 0rem var(--sizing-x4);display:flex;justify-content:flex-end}mbo-traveler-history-element .mbo-traveler-history-element__actions .bocc-button{height:var(--sizing-x18)}\n"],encapsulation:2}),m})()},98493:(M,b,e)=>{e.r(b),e.d(b,{MboSecurityTravelerHistoryPageModule:()=>p});var t=e(17007),h=e(78007),o=e(30263),f=e(86263),s=e(39904),m=e(95437),r=e(99877),v=e(48774),g=e(66613),c=e(39260);const{TRAVELER:y}=s.Z6.CUSTOMER.SECURITY;let a=(()=>{class l{constructor(n){this.mboProvider=n,this.backAction={id:"btn_traveler-history_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(y.HOME)}}}onRegister(){}}return l.\u0275fac=function(n){return new(n||l)(r.\u0275\u0275directiveInject(m.ZL))},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-security-traveler-history-page"]],decls:9,vars:2,consts:[[1,"mbo-security-traveler-history-page__content"],[1,"mbo-security-traveler-history-page__header"],["title","Mis viajes",3,"leftAction"],[1,"mbo-security-traveler-history-page__body"],["icon","bell",3,"visible"],[1,"mbo-security-traveler-history-page__list"]],template:function(n,u){1&n&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"bocc-alert",4),r.\u0275\u0275text(5," Aqu\xed puedes consultar, editar o eliminar la informaci\xf3n de tus viajes. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(6,"div",5),r.\u0275\u0275element(7,"mbo-traveler-history-element")(8,"mbo-traveler-history-element"),r.\u0275\u0275elementEnd()()()),2&n&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",u.backAction),r.\u0275\u0275advance(2),r.\u0275\u0275property("visible",!0))},dependencies:[v.J,g.B,c.U],styles:["/*!\n * MBO SecurityTravelerHistory Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 28/Jun/2024\n * Updated: 28/Jun/2024\n*/mbo-security-traveler-history-page{position:relative;display:block;width:100%;height:100%;overflow:hidden}mbo-security-traveler-history-page .mbo-security-traveler-history-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-security-traveler-history-page .mbo-security-traveler-history-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-security-traveler-history-page .mbo-security-traveler-history-page__list{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),l})(),p=(()=>{class l{}return l.\u0275fac=function(n){return new(n||l)},l.\u0275mod=r.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=r.\u0275\u0275defineInjector({imports:[t.CommonModule,h.RouterModule.forChild([{path:"",component:a}]),o.Jx,o.B4,f.gI]}),l})()}}]);