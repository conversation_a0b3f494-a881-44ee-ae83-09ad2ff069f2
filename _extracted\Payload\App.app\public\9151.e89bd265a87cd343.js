(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9151],{49151:()=>{var S=function e(){this.start=0,this.end=0,this.previous=null,this.parent=null,this.rules=null,this.parsedCssText="",this.cssText="",this.atRule=!1,this.type=0,this.keyframesName="",this.selector="",this.parsedSelector=""};function I(e){return d(function V(e){var r=new S;r.start=0,r.end=e.length;for(var t=r,n=0,a=e.length;n<a;n++)if(e[n]===U){t.rules||(t.rules=[]);var s=t,i=s.rules[s.rules.length-1]||null;(t=new S).start=n+1,t.parent=s,t.previous=i,s.rules.push(t)}else e[n]===G&&(t.end=n+1,t=t.parent||r);return r}(e=function N(e){return e.replace(p.comments,"").replace(p.port,"")}(e)),e)}function d(e,r){var t=r.substring(e.start,e.end-1);if(e.parsedCssText=e.cssText=t.trim(),e.parent){t=function O(e){return e.replace(/\\([0-9a-f]{1,6})\s/gi,function(){for(var r=arguments[1],t=6-r.length;t--;)r="0"+r;return"\\"+r})}(t=r.substring(e.previous?e.previous.end:e.parent.start,e.start-1)),t=(t=t.replace(p.multipleSpaces," ")).substring(t.lastIndexOf(";")+1);var a=e.parsedSelector=e.selector=t.trim();e.atRule=0===a.indexOf(P),e.atRule?0===a.indexOf(D)?e.type=c.MEDIA_RULE:a.match(p.keyframesRule)&&(e.type=c.KEYFRAMES_RULE,e.keyframesName=e.selector.split(p.multipleSpaces).pop()):e.type=0===a.indexOf(T)?c.MIXIN_RULE:c.STYLE_RULE}var s=e.rules;if(s)for(var i=0,o=s.length,u=void 0;i<o&&(u=s[i]);i++)d(u,r);return e}var c={STYLE_RULE:1,KEYFRAMES_RULE:7,MEDIA_RULE:4,MIXIN_RULE:1e3},U="{",G="}",p={comments:/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,port:/@import[^;]*;/gim,customProp:/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?(?:[;\n]|$)/gim,mixinProp:/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?{[^}]*?}(?:[;\n]|$)?/gim,mixinApply:/@apply\s*\(?[^);]*\)?\s*(?:[;\n]|$)?/gim,varApply:/[^;:]*?:[^;]*?var\([^;]*\)(?:[;\n]|$)?/gim,keyframesRule:/^@[^\s]*keyframes/,multipleSpaces:/\s+/g},T="--",D="@media",P="@";function g(e,r,t){e.lastIndex=0;var n=r.substring(t).match(e);if(n){var a=t+n.index;return{start:a,end:a+n[0].length}}return null}var F=/\bvar\(/,$=/\B--[\w-]+\s*:/,j=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,B=/^[\t ]+\n/gm;function W(e,r,t){var n=function Y(e,r){var t=g(F,e,r);if(!t)return null;var n=function X(e,r){for(var t=0,n=r;n<e.length;n++){var a=e[n];if("("===a)t++;else if(")"===a&&--t<=0)return n+1}return n}(e,t.start),s=e.substring(t.end,n-1).split(","),i=s[0],o=s.slice(1);return{start:t.start,end:n,propName:i.trim(),fallback:o.length>0?o.join(",").trim():void 0}}(e,t);if(!n)return r.push(e.substring(t,e.length)),e.length;var a=n.propName,s=null!=n.fallback?h(n.fallback):void 0;return r.push(e.substring(t,n.start),function(i){return function H(e,r,t){return e[r]?e[r]:t?f(t,e):""}(i,a,s)}),n.end}function f(e,r){for(var t="",n=0;n<e.length;n++){var a=e[n];t+="string"==typeof a?a:a(r)}return t}function q(e,r){for(var t=!1,n=!1,a=r;a<e.length;a++){var s=e[a];if(t)n&&'"'===s&&(t=!1),!n&&"'"===s&&(t=!1);else if('"'===s)t=!0,n=!0;else if("'"===s)t=!0,n=!1;else{if(";"===s)return a+1;if("}"===s)return a}}return a}function h(e){var r=0;e=function z(e){for(var r="",t=0;;){var n=g($,e,t),a=n?n.start:e.length;if(r+=e.substring(t,a),!n)break;t=q(e,a)}return r}(e=e.replace(j,"")).replace(B,"");for(var t=[];r<e.length;)r=W(e,t,r);return t}function y(e){var r={};e.forEach(function(o){o.declarations.forEach(function(u){r[u.prop]=u.value})});for(var t={},n=Object.entries(r),a=function(o){var u=!1;if(n.forEach(function(w){var L=w[0],k=f(w[1],t);k!==t[L]&&(t[L]=k,u=!0)}),!u)return"break"},s=0;s<10&&"break"!==a();s++);return t}function K(e,r){if(void 0===r&&(r=0),!e.rules)return[];var t=[];return e.rules.filter(function(n){return n.type===c.STYLE_RULE}).forEach(function(n){var a=function J(e){for(var t,r=[];t=Z.exec(e.trim());){var n=Q(t[2]),a=n.value,s=n.important;r.push({prop:t[1].trim(),value:h(a),important:s})}return r}(n.cssText);a.length>0&&n.parsedSelector.split(",").forEach(function(s){s=s.trim(),t.push({selector:s,declarations:a,specificity:1,nu:r})}),r++}),t}var b="!important",Z=/(?:^|[;\s{]\s*)(--[\w-]*?)\s*:\s*(?:((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};{])+)|\{([^}]*)\}(?:(?=[;\s}])|$))/gm;function Q(e){var t=(e=e.replace(/\s+/gim," ").trim()).endsWith(b);return t&&(e=e.substr(0,e.length-b.length).trim()),{value:e,important:t}}function E(e){var r=[];return e.forEach(function(t){r.push.apply(r,t.selectors)}),r}function R(e){var r=I(e),t=h(e);return{original:e,template:t,selectors:K(r),usesCssVars:t.length>1}}function v(e,r){if(e.some(function(n){return n.styleEl===r}))return!1;var t=R(r.textContent);return t.styleEl=r,e.push(t),!0}function A(e){var t=y(E(e));e.forEach(function(n){n.usesCssVars&&(n.styleEl.textContent=f(n.template,t))})}function _(e,r,t){return e=function se(e,r,t){return e.replace(new RegExp(r,"g"),t)}(e,"\\."+r,"."+t),e}function C(e,r){return Array.from(e.querySelectorAll("style:not([data-styles]):not([data-no-shim])")).map(function(n){return v(r,n)}).some(Boolean)}function M(e,r,t){var n=t.href;return fetch(n).then(function(a){return a.text()}).then(function(a){if(function ce(e){return e.indexOf("var(")>-1||le.test(e)}(a)&&t.parentNode){(function pe(e){return m.lastIndex=0,m.test(e)})(a)&&(a=function fe(e,r){var t=r.replace(/[^/]*$/,"");return e.replace(m,function(n,a){return n.replace(a,t+a)})}(a,n));var s=e.createElement("style");s.setAttribute("data-styles",""),s.textContent=a,v(r,s),t.parentNode.insertBefore(s,t),t.remove()}}).catch(function(a){console.error(a)})}var le=/[\s;{]--[-a-zA-Z0-9]+\s*:/m,m=/url[\s]*\([\s]*['"]?(?!(?:https?|data)\:|\/)([^\'\"\)]*)[\s]*['"]?\)[\s]*/gim,he=function(){function e(r,t){this.win=r,this.doc=t,this.count=0,this.hostStyleMap=new WeakMap,this.hostScopeMap=new WeakMap,this.globalScopes=[],this.scopesMap=new Map,this.didInit=!1}return e.prototype.initShim=function(){var r=this;return this.didInit?Promise.resolve():(this.didInit=!0,new Promise(function(t){r.win.requestAnimationFrame(function(){(function oe(e,r){new MutationObserver(function(){C(e,r)&&A(r)}).observe(document.head,{childList:!0})})(r.doc,r.globalScopes),function ie(e,r){return C(e,r),function ue(e,r){for(var t=[],n=e.querySelectorAll('link[rel="stylesheet"][href]:not([data-no-shim])'),a=0;a<n.length;a++)t.push(M(e,r,n[a]));return Promise.all(t)}(e,r)}(r.doc,r.globalScopes).then(function(){return t()})})}))},e.prototype.addLink=function(r){var t=this;return M(this.doc,this.globalScopes,r).then(function(){t.updateGlobal()})},e.prototype.addGlobalStyle=function(r){v(this.globalScopes,r)&&this.updateGlobal()},e.prototype.createHostStyle=function(r,t,n,a){if(this.hostScopeMap.has(r))throw new Error("host style already created");var s=this.registerHostTemplate(n,t,a),i=this.doc.createElement("style");return i.setAttribute("data-styles",""),s.usesCssVars?a?(i["s-sc"]=t=s.scopeId+"-"+this.count,i.textContent="/*needs update*/",this.hostStyleMap.set(r,i),this.hostScopeMap.set(r,function ae(e,r){var t=e.template.map(function(a){return"string"==typeof a?_(a,e.scopeId,r):a}),n=e.selectors.map(function(a){return Object.assign(Object.assign({},a),{selector:_(a.selector,e.scopeId,r)})});return Object.assign(Object.assign({},e),{template:t,selectors:n,scopeId:r})}(s,t)),this.count++):(s.styleEl=i,s.usesCssVars||(i.textContent=f(s.template,{})),this.globalScopes.push(s),this.updateGlobal(),this.hostScopeMap.set(r,s)):i.textContent=n,i},e.prototype.removeHost=function(r){var t=this.hostStyleMap.get(r);t&&t.remove(),this.hostStyleMap.delete(r),this.hostScopeMap.delete(r)},e.prototype.updateHost=function(r){var t=this.hostScopeMap.get(r);if(t&&t.usesCssVars&&t.isScoped){var n=this.hostStyleMap.get(r);if(n){var a=function ee(e,r,t){var n=[],a=function te(e,r){for(var t=[];r;){var n=e.get(r);n&&t.push(n),r=r.parentElement}return t}(r,e);t.forEach(function(o){return n.push(o)}),a.forEach(function(o){return n.push(o)});var i=E(n).filter(function(o){return function ne(e,r){return":root"===r||"html"===r||e.matches(r)}(e,o.selector)});return function re(e){return e.sort(function(r,t){return r.specificity===t.specificity?r.nu-t.nu:r.specificity-t.specificity}),e}(i)}(r,this.hostScopeMap,this.globalScopes),s=y(a);n.textContent=f(t.template,s)}}},e.prototype.updateGlobal=function(){A(this.globalScopes)},e.prototype.registerHostTemplate=function(r,t,n){var a=this.scopesMap.get(t);return a||((a=R(r)).scopeId=t,a.isScoped=n,this.scopesMap.set(t,a)),a},e}(),l=window;!l.__stencil_cssshim&&function ve(){return!(l.CSS&&l.CSS.supports&&l.CSS.supports("color","var(--c)"))}()&&(l.__stencil_cssshim=new he(l,document))}}]);