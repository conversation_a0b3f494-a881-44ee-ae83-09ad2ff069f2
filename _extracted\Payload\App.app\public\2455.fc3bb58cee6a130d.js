(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2455],{22455:(j,p,e)=>{e.r(p),e.d(p,{MboProductMovementInformationPageModule:()=>O});var b=e(17007),f=e(78007),d=e(79798),v=e(30263),M=e(15861),o=e(99877),P=e(78506),h=e(39904),m=e(87903),I=e(95437),y=e(10464),C=e(78021),A=e(1027);const{INFO:E,MOVEMENTS:R}=h.Z6.CUSTOMER.PRODUCTS;let S=(()=>{class n{constructor(t,i,r,a){this.ref=t,this.activateRoute=i,this.mboProvider=r,this.managerProductMovements=a,this.informations=[],this.backAction={id:"btn_product-movement-information_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.back()}},this.rightActions=[{id:"btn_product-movement-information_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_product-movement-information_result"),this.ref.nativeElement.classList.add(h.fc),this.initializatedConfiguration()}back(){const{coveredCardId:t,currencyCode:i,productId:r,redirect:a}=this.activateRoute.snapshot.queryParams;this.mboProvider.navigation.back("movements"===a?R:E,{coveredCardId:t,currencyCode:i,productId:r,section:"movements"})}initializatedConfiguration(){var t=this;return(0,M.Z)(function*(){const{coveredCardId:i,currencyCode:r,productId:a,uuid:s}=t.activateRoute.snapshot.queryParams;(yield t.managerProductMovements.requestInformation({currencyCode:r,productId:a,uuid:s,coveredCardId:i})).when({success:({movement:u,product:l})=>{t.createInformations(u,l)},failure:()=>{t.back()}})})()}createInformations(t,i){const{nickname:r,publicNumber:a}=i,{amount:s,currencyCode:u,description:l,dateFormat:x,reference:g}=t;this.informations.push((0,m.SP)("PRODUCTO",r,a)),this.informations.push((0,m._f)("SUMA DE",s,"COP"===u?"$":"USD")),this.informations.push((0,m.Kt)("DESCRIPCI\xd3N",l)),this.informations.push((0,m.tS)(x)),g&&this.informations.push((0,m.SP)("REFERENCIA",g)),this.informations.push((0,m.fW)("RESULTADO","success","Exitoso"))}}return n.\u0275fac=function(t){return new(t||n)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(f.ActivatedRoute),o.\u0275\u0275directiveInject(I.ZL),o.\u0275\u0275directiveInject(P.sy))},n.\u0275cmp=o.\u0275\u0275defineComponent({type:n,selectors:[["mbo-product-movement-information-page"]],decls:8,vars:3,consts:[[1,"mbo-product-movement-information-page__content","mbo-page__scroller"],[1,"mbo-page__header"],[3,"leftAction","rightActions"],[1,"mbo-product-movement-information-page__body"],["id","crd_product-movement-information_result",3,"informations"],[1,"mbo-product-movement-information-page__title","subtitle2-medium"]],template:function(t,i){1&t&&(o.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),o.\u0275\u0275element(3,"mbo-header-result",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information",4)(6,"label",5),o.\u0275\u0275text(7," Datos del movimiento "),o.\u0275\u0275elementEnd()()()()()),2&t&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("leftAction",i.backAction)("rightActions",i.rightActions),o.\u0275\u0275advance(2),o.\u0275\u0275property("informations",i.informations))},dependencies:[y.K,C.c,A.A],styles:["/*!\n * MBO ProductMovementInformation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 20/Oct/2022\n * Updated: 10/Jul/2024\n*/mbo-product-movement-information-page{--mbo-header-result-padding: calc( var(--mbo-application-body-safe-spacing) + var(--sizing-x4) ) var(--sizing-x4) var(--sizing-x4) var(--sizing-x4);position:relative;display:block;width:100%;height:100%}mbo-product-movement-information-page .mbo-product-movement-information-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-movement-information-page .mbo-product-movement-information-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-product-movement-information-page .mbo-product-movement-information-page__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),n})(),O=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=o.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=o.\u0275\u0275defineInjector({imports:[b.CommonModule,f.RouterModule.forChild([{path:"",component:S}]),v.DM,v.Zl,v.Oh,d.KI,d.cN,d.A6]}),n})()}}]);