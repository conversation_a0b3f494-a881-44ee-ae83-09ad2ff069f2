(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6821],{37700:(Ln,J,w)=>{w.d(J,{c:()=>F});const k=typeof window<"u"?window:void 0;typeof document<"u"&&document;var T=w(59804);let Q;const N=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),D=i=>(void 0===Q&&(Q=void 0===i.style.animationName&&void 0!==i.style.webkitAnimationName?"-webkit-":""),Q),s=(i,o,a)=>{const l=o.startsWith("animation")?D(i):"";i.style.setProperty(l+o,a)},g=(i,o)=>{const a=o.startsWith("animation")?D(i):"";i.style.removeProperty(a+o)},nn=[],y=(i=[],o)=>{if(void 0!==o){const a=Array.isArray(o)?o:[o];return[...i,...a]}return i},F=i=>{let o,a,l,u,C,$,m,sn,O,U,W,j,r,c=[],un=[],dn=[],R=!1,mn={},hn=[],gn=[],pn={},K=0,rn=!1,an=!1,L=!0,B=!1,Y=!0,fn=!1;const bn=i,yn=[],V=[],cn=[],h=[],p=[],En=[],vn=[],Sn=[],Tn=[],Fn=[],S=[],In="function"==typeof AnimationEffect||void 0!==k&&"function"==typeof k.AnimationEffect,b="function"==typeof Element&&"function"==typeof Element.prototype.animate&&In,$n=()=>S,Pn=(n,t)=>{const e=t.findIndex(f=>f.c===n);e>-1&&t.splice(e,1)},An=(n,t)=>((t?.oneTimeCallback?V:yn).push({c:n,o:t}),r),_n=()=>{if(b)S.forEach(n=>{n.cancel()}),S.length=0;else{const n=h.slice();(0,T.r)(()=>{n.forEach(t=>{g(t,"animation-name"),g(t,"animation-duration"),g(t,"animation-timing-function"),g(t,"animation-iteration-count"),g(t,"animation-delay"),g(t,"animation-play-state"),g(t,"animation-fill-mode"),g(t,"animation-direction")})})}},wn=()=>{En.forEach(n=>{n?.parentNode&&n.parentNode.removeChild(n)}),En.length=0},G=()=>void 0!==C?C:m?m.getFill():"both",I=()=>void 0!==O?O:void 0!==$?$:m?m.getDirection():"normal",H=()=>rn?"linear":void 0!==l?l:m?m.getEasing():"linear",v=()=>an?0:void 0!==U?U:void 0!==a?a:m?m.getDuration():0,_=()=>void 0!==u?u:m?m.getIterations():1,M=()=>void 0!==W?W:void 0!==o?o:m?m.getDelay():0,z=()=>{0!==K&&(K--,0===K&&((()=>{Cn(),Tn.forEach(d=>d()),Fn.forEach(d=>d());const n=L?1:0,t=hn,e=gn,f=pn;h.forEach(d=>{const A=d.classList;t.forEach(x=>A.add(x)),e.forEach(x=>A.remove(x));for(const x in f)f.hasOwnProperty(x)&&s(d,x,f[x])}),U=void 0,O=void 0,W=void 0,yn.forEach(d=>d.c(n,r)),V.forEach(d=>d.c(n,r)),V.length=0,Y=!0,L&&(B=!0),L=!0})(),m&&m.animationFinish()))},Dn=(n=!0)=>{wn();const t=(i=>(i.forEach(o=>{for(const a in o)if(o.hasOwnProperty(a)){const l=o[a];if("easing"===a)o["animation-timing-function"]=l,delete o[a];else{const u=N(a);u!==a&&(o[u]=l,delete o[a])}}}),i))(c);h.forEach(e=>{if(t.length>0){const f=((i=[])=>i.map(o=>{const a=o.offset,l=[];for(const u in o)o.hasOwnProperty(u)&&"offset"!==u&&l.push(`${u}: ${o[u]};`);return`${100*a}% { ${l.join(" ")} }`}).join(" "))(t);j=void 0!==i?i:(i=>{let o=nn.indexOf(i);return o<0&&(o=nn.push(i)-1),`ion-animation-${o}`})(f);const d=((i,o,a)=>{var l;const u=(i=>{const o=void 0!==i.getRootNode?i.getRootNode():i;return o.head||o})(a),C=D(a),$=u.querySelector("#"+i);if($)return $;const c=(null!==(l=a.ownerDocument)&&void 0!==l?l:document).createElement("style");return c.id=i,c.textContent=`@${C}keyframes ${i} { ${o} } @${C}keyframes ${i}-alt { ${o} }`,u.appendChild(c),c})(j,f,e);En.push(d),s(e,"animation-duration",`${v()}ms`),s(e,"animation-timing-function",H()),s(e,"animation-delay",`${M()}ms`),s(e,"animation-fill-mode",G()),s(e,"animation-direction",I());const A=_()===1/0?"infinite":_().toString();s(e,"animation-iteration-count",A),s(e,"animation-play-state","paused"),n&&s(e,"animation-name",`${d.id}-alt`),(0,T.r)(()=>{s(e,"animation-name",d.id||null)})}})},Rn=(n=!0)=>{(()=>{vn.forEach(f=>f()),Sn.forEach(f=>f());const n=un,t=dn,e=mn;h.forEach(f=>{const d=f.classList;n.forEach(A=>d.add(A)),t.forEach(A=>d.remove(A));for(const A in e)e.hasOwnProperty(A)&&s(f,A,e[A])})})(),c.length>0&&(b?(h.forEach(n=>{const t=n.animate(c,{id:bn,delay:M(),duration:v(),easing:H(),iterations:_(),fill:G(),direction:I()});t.pause(),S.push(t)}),S.length>0&&(S[0].onfinish=()=>{z()})):Dn(n)),R=!0},Z=n=>{if(n=Math.min(Math.max(n,0),.9999),b)S.forEach(t=>{t.currentTime=t.effect.getComputedTiming().delay+v()*n,t.pause()});else{const t=`-${v()*n}ms`;h.forEach(e=>{c.length>0&&(s(e,"animation-delay",t),s(e,"animation-play-state","paused"))})}},xn=n=>{S.forEach(t=>{t.effect.updateTiming({delay:M(),duration:v(),easing:H(),iterations:_(),fill:G(),direction:I()})}),void 0!==n&&Z(n)},kn=(n=!0,t)=>{(0,T.r)(()=>{h.forEach(e=>{s(e,"animation-name",j||null),s(e,"animation-duration",`${v()}ms`),s(e,"animation-timing-function",H()),s(e,"animation-delay",void 0!==t?`-${t*v()}ms`:`${M()}ms`),s(e,"animation-fill-mode",G()||null),s(e,"animation-direction",I()||null);const f=_()===1/0?"infinite":_().toString();s(e,"animation-iteration-count",f),n&&s(e,"animation-name",`${j}-alt`),(0,T.r)(()=>{s(e,"animation-name",j||null)})})})},E=(n=!1,t=!0,e)=>(n&&p.forEach(f=>{f.update(n,t,e)}),b?xn(e):kn(t,e),r),Kn=()=>{R&&(b?S.forEach(n=>{n.pause()}):h.forEach(n=>{s(n,"animation-play-state","paused")}),fn=!0)},St=()=>{sn=void 0,z()},Cn=()=>{sn&&clearTimeout(sn)},On=n=>new Promise(t=>{n?.sync&&(an=!0,An(()=>an=!1,{oneTimeCallback:!0})),R||Rn(),B&&(b?(Z(0),xn()):kn(),B=!1),Y&&(K=p.length+1,Y=!1);const e=()=>{Pn(f,V),t()},f=()=>{Pn(e,cn),t()};An(f,{oneTimeCallback:!0}),((n,t)=>{cn.push({c:n,o:{oneTimeCallback:!0}})})(e),p.forEach(d=>{d.play()}),b?(S.forEach(n=>{n.play()}),(0===c.length||0===h.length)&&z()):(()=>{if(Cn(),(0,T.r)(()=>{h.forEach(n=>{c.length>0&&s(n,"animation-play-state","running")})}),0===c.length||0===h.length)z();else{const n=M()||0,t=v()||0,e=_()||1;isFinite(e)&&(sn=setTimeout(St,n+t*e+100)),((i,o)=>{let a;const l={passive:!0},C=$=>{i===$.target&&(a&&a(),Cn(),(0,T.r)(()=>{h.forEach(n=>{g(n,"animation-duration"),g(n,"animation-delay"),g(n,"animation-play-state")}),(0,T.r)(z)}))};i&&(i.addEventListener("webkitAnimationEnd",C,l),i.addEventListener("animationend",C,l),a=()=>{i.removeEventListener("webkitAnimationEnd",C,l),i.removeEventListener("animationend",C,l)})})(h[0])}})(),fn=!1}),Wn=(n,t)=>{const e=c[0];return void 0===e||void 0!==e.offset&&0!==e.offset?c=[{offset:0,[n]:t},...c]:e[n]=t,r};return r={parentAnimation:m,elements:h,childAnimations:p,id:bn,animationFinish:z,from:Wn,to:(n,t)=>{const e=c[c.length-1];return void 0===e||void 0!==e.offset&&1!==e.offset?c=[...c,{offset:1,[n]:t}]:e[n]=t,r},fromTo:(n,t,e)=>Wn(n,t).to(n,e),parent:n=>(m=n,r),play:On,pause:()=>(p.forEach(n=>{n.pause()}),Kn(),r),stop:()=>{p.forEach(n=>{n.stop()}),R&&(_n(),R=!1),rn=!1,an=!1,Y=!0,O=void 0,U=void 0,W=void 0,K=0,B=!1,L=!0,fn=!1,cn.forEach(n=>n.c(0,r)),cn.length=0},destroy:n=>(p.forEach(t=>{t.destroy(n)}),(n=>{_n(),n&&wn()})(n),h.length=0,p.length=0,c.length=0,yn.length=0,V.length=0,R=!1,Y=!0,r),keyframes:n=>{const t=c!==n;return c=n,t&&(n=>{b?$n().forEach(t=>{const e=t.effect;if(e.setKeyframes)e.setKeyframes(n);else{const f=new KeyframeEffect(e.target,n,e.getTiming());t.effect=f}}):Dn()})(c),r},addAnimation:n=>{if(null!=n)if(Array.isArray(n))for(const t of n)t.parent(r),p.push(t);else n.parent(r),p.push(n);return r},addElement:n=>{if(null!=n)if(1===n.nodeType)h.push(n);else if(n.length>=0)for(let t=0;t<n.length;t++)h.push(n[t]);else console.error("Invalid addElement value");return r},update:E,fill:n=>(C=n,E(!0),r),direction:n=>($=n,E(!0),r),iterations:n=>(u=n,E(!0),r),duration:n=>(!b&&0===n&&(n=1),a=n,E(!0),r),easing:n=>(l=n,E(!0),r),delay:n=>(o=n,E(!0),r),getWebAnimations:$n,getKeyframes:()=>c,getFill:G,getDirection:I,getDelay:M,getIterations:_,getEasing:H,getDuration:v,afterAddRead:n=>(Tn.push(n),r),afterAddWrite:n=>(Fn.push(n),r),afterClearStyles:(n=[])=>{for(const t of n)pn[t]="";return r},afterStyles:(n={})=>(pn=n,r),afterRemoveClass:n=>(gn=y(gn,n),r),afterAddClass:n=>(hn=y(hn,n),r),beforeAddRead:n=>(vn.push(n),r),beforeAddWrite:n=>(Sn.push(n),r),beforeClearStyles:(n=[])=>{for(const t of n)mn[t]="";return r},beforeStyles:(n={})=>(mn=n,r),beforeRemoveClass:n=>(dn=y(dn,n),r),beforeAddClass:n=>(un=y(un,n),r),onFinish:An,isRunning:()=>0!==K&&!fn,progressStart:(n=!1,t)=>(p.forEach(e=>{e.progressStart(n,t)}),Kn(),rn=n,R||Rn(),E(!1,!0,t),r),progressStep:n=>(p.forEach(t=>{t.progressStep(n)}),Z(n),r),progressEnd:(n,t,e)=>(rn=!1,p.forEach(f=>{f.progressEnd(n,t,e)}),void 0!==e&&(U=e),B=!1,L=!0,0===n?(O="reverse"===I()?"normal":"reverse","reverse"===O&&(L=!1),b?(E(),Z(1-t)):(W=(1-t)*v()*-1,E(!1,!1))):1===n&&(b?(E(),Z(t)):(W=t*v()*-1,E(!1,!1))),void 0!==n&&!m&&On(),r)}}},6821:(Ln,J,w)=>{w.r(J),w.d(J,{mdTransitionAnimation:()=>T});var k=w(37700),ln=w(93435);const T=(Q,P)=>{var N,D,s;const g="40px",q="back"===P.direction,tn=P.leavingEl,en=(0,ln.g)(P.enteringEl),on=en.querySelector("ion-toolbar"),y=(0,k.c)();if(y.addElement(en).fill("both").beforeRemoveClass("ion-page-invisible"),q?y.duration((null!==(N=P.duration)&&void 0!==N?N:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):y.duration((null!==(D=P.duration)&&void 0!==D?D:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform",`translateY(${g})`,"translateY(0px)").fromTo("opacity",.01,1),on){const F=(0,k.c)();F.addElement(on),y.addAnimation(F)}if(tn&&q){y.duration((null!==(s=P.duration)&&void 0!==s?s:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const F=(0,k.c)();F.addElement((0,ln.g)(tn)).onFinish(i=>{1===i&&F.elements.length>0&&F.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)",`translateY(${g})`).fromTo("opacity",1,0),y.addAnimation(F)}return y}}}]);