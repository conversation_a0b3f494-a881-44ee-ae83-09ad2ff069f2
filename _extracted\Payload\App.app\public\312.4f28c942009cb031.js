(self.webpackChunkapp=self.webpackChunkapp||[]).push([[312],{50312:(S,d,e)=>{e.r(d),e.d(d,{MboProductInfoPageModule:()=>C});var m=e(17007),s=e(78007),u=e(74561),o=e(99877),l=e(30263),a=e(39904),p=e(27236),f=e(95437),h=e(87956),v=e(54747),P=e(38487);let g=(()=>{class r{constructor(t,n,i,I,b,M,R){this.ref=t,this.activateRoute=n,this.blueScreenService=i,this.storageService=I,this.mboProvider=b,this.requestConfiguration=M,this.managerCovereds=R,this.visible=!1}ngOnInit(){this.ref.nativeElement.classList.add(a.fc);const{coveredCardId:t,productId:n}=this.activateRoute.snapshot.queryParams;t?this.managerCovereds.requestForId(n,t).then(i=>{i?this.openCoveredCard(i,n):this.mboProvider.navigation.back(a.Z6.CUSTOMER.PRODUCTS.COVERED_CARDS,{productId:n})}):this.requestConfiguration.requestProductAnyForId(n).then(i=>{i?this.openProduct(i):this.mboProvider.navigation.back(a.Z6.CUSTOMER.PRODUCTS.HOME)})}onBack(){const{productFilter:t}=this.activateRoute.snapshot.queryParams;this.creditCardId?this.mboProvider.navigation.back(a.Z6.CUSTOMER.PRODUCTS.COVERED_CARDS,{productId:this.creditCardId,productFilter:t}):this.mboProvider.navigation.back(a.Z6.CUSTOMER.PRODUCTS.HOME,{productId:this.product?.id,productFilter:t})}openProduct(t){this.product=t,t.isDigital&&this.storageService.get(p.Z.DigitalInformation).then(n=>{n||this.blueScreenService.create(u.CO).open(240)})}openCoveredCard(t,n){this.product=t,this.creditCardId=n}}return r.\u0275fac=function(t){return new(t||r)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(s.ActivatedRoute),o.\u0275\u0275directiveInject(l.Dl),o.\u0275\u0275directiveInject(h.V1),o.\u0275\u0275directiveInject(f.ZL),o.\u0275\u0275directiveInject(v.mb),o.\u0275\u0275directiveInject(v._L))},r.\u0275cmp=o.\u0275\u0275defineComponent({type:r,selectors:[["mbo-product-info-page"]],decls:1,vars:1,consts:[[3,"product","back"]],template:function(t,n){1&t&&(o.\u0275\u0275elementStart(0,"mbo-product-info-modal",0),o.\u0275\u0275listener("back",function(){return n.onBack()}),o.\u0275\u0275elementEnd()),2&t&&o.\u0275\u0275property("product",n.product)},dependencies:[P.G],styles:["mbo-product-info-page mbo-product-info-modal .bocc-card-product-background{padding-top:calc(var(--mbo-application-body-safe-spacing) + var(--sizing-x8))}\n"],encapsulation:2}),r})(),C=(()=>{class r{}return r.\u0275fac=function(t){return new(t||r)},r.\u0275mod=o.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=o.\u0275\u0275defineInjector({imports:[m.CommonModule,s.RouterModule.forChild([{path:"",component:g}]),u.D9]}),r})()}}]);