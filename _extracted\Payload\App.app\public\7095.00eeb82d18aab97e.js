(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7095],{77095:(s,l,o)=>{o.r(l),o.d(l,{MboPaymentsHomeModule:()=>m});var d=o(17007),a=o(78007),t=o(99877);const M=[{path:"",loadChildren:()=>o.e(8058).then(o.bind(o,38058)).then(n=>n.MboPaymentsHomePageModule)},{path:"history",loadChildren:()=>o.e(9641).then(o.bind(o,69641)).then(n=>n.MboPaymentsHistoryPageModule)},{path:"history/information",loadChildren:()=>o.e(7533).then(o.bind(o,17533)).then(n=>n.MboPaymentHistoryInformationPageModule)},{path:"services",loadChildren:()=>o.e(4753).then(o.bind(o,34536)).then(n=>n.MboPaymentServicesPageModule)}];let m=(()=>{class n{}return n.\u0275fac=function(h){return new(h||n)},n.\u0275mod=t.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=t.\u0275\u0275defineInjector({imports:[d.CommonModule,a.RouterModule.forChild(M)]}),n})()}}]);