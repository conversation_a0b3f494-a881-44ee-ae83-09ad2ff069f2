(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3501],{88039:(N,A,i)=>{i.d(A,{Oz:()=>S,fN:()=>l,vA:()=>e});var m=i(29306);class S{constructor(y,a,h,E,b){this.number=y,this.type=a,this.name=h,this.nickname=E,this.bank=b}get description(){return this.nickname||this.name}}class l{constructor(y,a,h,E,b,v,R,F,P){this.uuid=y,this.date=a,this.source=h,this.destination=E,this.amount=b,this.type=v,this.status=R,this.category=F,this.approvedCode=P}get dateFormat(){return this.date.dateFormat}get timeFormat(){return this.date.timeFormat}}class e extends m.Ay{static empty(){return new e([],0,!0)}}},93637:(N,A,i)=>{i.d(A,{U:()=>I,a:()=>M});var m=i(15861),S=i(3372),l=i(87956),e=i(98699),g=i(71776),y=i(39904),a=i(42168),h=i(84757);const E={D:{color:"success",label:"Exitosa"},R:{color:"danger",label:"Fallida"},P:{color:"alert",label:"Enviada"}};var v=i(88039),R=i(29306),F=i(53113),P=i(33876);const s={color:"primary",label:"Desconocido"};var n=i(99877);const f={items:String(10),order:"DESC",orderField:"xferEffDt"};let p=(()=>{class r{constructor(t,o){this.http=t,this.eventBusService=o,this.eventBusService.subscribes(y.PU,()=>{this.history=void 0})}request(t){if(this.history)return Promise.resolve(this.history);const o=t||y.cC,{end:u,start:d}=o.getFormat();return(0,a.firstValueFrom)(this.remote({...f,page:"0",EndDt:u,StartDt:d}).pipe((0,h.tap)(j=>{j.range=o,this.history=j})))}refresh(t){const{end:o,start:u}=t.getFormat();return(0,a.firstValueFrom)(this.remote({...f,page:"0",EndDt:o,StartDt:u}).pipe((0,h.tap)(d=>{d.range=t,this.history=d})))}requestForUuid(t){return this.history?.requestForUuid(t)}nextPage(){var t=this;return(0,m.Z)(function*(){if(!t.history)return t.request().then(({collection:d})=>d);const{end:o,start:u}=t.history.range.getFormat();return(0,a.firstValueFrom)(t.remote({...f,page:t.history.currentPage.toString(),EndDt:o,StartDt:u}).pipe((0,h.map)(({collection:d})=>(t.history.merge(d),t.history.collection))))})()}remote(t){return this.http.get(y.bV.TRANSFERS.HISTORY,{params:{...t}}).pipe((0,h.map)(({content:o,totalPage:u})=>new v.vA(o.map(d=>function c(r){return new v.fN((0,P.v4)(),new F.ou(r.xferEffDt),new v.Oz(r.fromAcctId,r.fromAcctType,r.fromAcctName,r.fromNickName,new R.Br(r.fromBankId,r.fromBankName)),new v.Oz(r.toAcctId,r.toAcctType,r.toAcctName,r.toNickName,new R.Br(r.toBankId,r.toBankName)),+r.amt,r.trnType,E[r.xferStatusDesc]||s,r.trnReccategory,r.approvalId)}(d)),u)),(0,h.catchError)(o=>{if(this.history)return(0,a.of)(v.vA.empty());throw o}))}}return r.\u0275fac=function(t){return new(t||r)(n.\u0275\u0275inject(g.HttpClient),n.\u0275\u0275inject(l.Yd))},r.\u0275prov=n.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),M=(()=>{class r{constructor(t,o,u){this.histories=t,this.products=o,this.preferencesService=u}transfers(){var t=this;return(0,m.Z)(function*(){try{const o=yield t.preferencesService.requestBoolean(S.M.TagAval),u=t.histories.request();return e.Either.success({enabledTagAval:o,history$:u})}catch({message:o}){return e.Either.failure({message:o})}})()}trustfunds(){var t=this;return(0,m.Z)(function*(){try{const o=yield t.products.requestTrustfunds();return o.length?e.Either.success(o):e.Either.failure({message:"Da clic en <b>Quiero invertir</b> y crea tu inversi\xf3n",value:!1})}catch{return e.Either.failure({message:"Ocurrio un error al tratar de consultar tus productos fiduciarios, por favor intente m\xe1s tarde",value:!0})}})()}}return r.\u0275fac=function(t){return new(t||r)(n.\u0275\u0275inject(p),n.\u0275\u0275inject(l.hM),n.\u0275\u0275inject(l.yW))},r.\u0275prov=n.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),I=(()=>{class r{constructor(t){this.histories=t}firstPage(){var t=this;return(0,m.Z)(function*(){try{return e.Either.success(yield t.histories.request())}catch({message:o}){return e.Either.failure({message:o})}})()}nextPage(){var t=this;return(0,m.Z)(function*(){try{return e.Either.success(yield t.histories.nextPage())}catch({message:o}){return e.Either.failure({message:o})}})()}refresh(t){var o=this;return(0,m.Z)(function*(){try{return e.Either.success(yield o.histories.refresh(t))}catch({message:u}){return e.Either.failure({message:u})}})()}historyForUuid(t){try{const o=this.histories.requestForUuid(t);return o?e.Either.success(o):e.Either.failure()}catch({message:o}){return e.Either.failure({message:o})}}}return r.\u0275fac=function(t){return new(t||r)(n.\u0275\u0275inject(p))},r.\u0275prov=n.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},43501:(N,A,i)=>{i.r(A),i.d(A,{MboTransferHistoryInformationPageModule:()=>P});var m=i(17007),S=i(78007),l=i(79798),e=i(99877),g=i(39904),y=i(95437),a=i(87903),E=i(93637),b=i(10464),v=i(78021),R=i(1027);let F=(()=>{class s{constructor(n,f,p,M){this.ref=n,this.activateRoute=f,this.mboProvider=p,this.requestConfiguration=M,this.redirectPage=g.Z6.TRANSFERS.HOME,this.informations=[],this.backAction={id:"btn_transfer-history-information_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(this.redirectPage)}},this.rightActions=[{id:"btn_transfer-history-information_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){const{redirect:n,uuid:f}=this.activateRoute.snapshot.queryParams;this.redirectPage="history"===n?g.Z6.TRANSFERS.HISTORY:g.Z6.TRANSFERS.HOME,this.element=this.ref.nativeElement.querySelector("#crd_transfer-history-information_result"),this.ref.nativeElement.classList.add(g.fc),this.requestConfiguration.historyForUuid(f).when({success:p=>{this.informations=function h(s){const c=[],{amount:n,approvedCode:f,category:p,dateFormat:M,destination:I,source:{description:r,number:T},status:{color:t,label:o},timeFormat:u}=s;return c.push({type:"product",product:{icon:I.bank.logo,color:"ghost",number:I.number,title:I.description,subtitle:I.bank.name}}),c.push((0,a._f)("LA SUMA DE",n)),c.push((0,a.SP)("DESDE",r,T)),c.push((0,a.SP)("TIPO DE TRANSFERENCIA",p)),c.push((0,a.cZ)(M,u)),f&&c.push((0,a.SP)("N\xdaMERO DE TRANSFERENCIA",f)),c.push((0,a.fW)("RESULTADO",t,o)),c}(p)},failure:()=>{this.mboProvider.navigation.back(this.redirectPage)}})}}return s.\u0275fac=function(n){return new(n||s)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(S.ActivatedRoute),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(E.U))},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-transfer-history-information-page"]],decls:8,vars:3,consts:[[1,"mbo-transfer-history-information-page__content","mbo-page__scroller"],[1,"mbo-page__header"],[3,"leftAction","rightActions"],[1,"mbo-transfer-history-information-page__body"],["id","crd_transfer-history-information_result",3,"informations"],[1,"mbo-transfer-history-information-page__title","subtitle2-medium"]],template:function(n,f){1&n&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"mbo-header-result",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information",4)(6,"label",5),e.\u0275\u0275text(7," Datos de la transferencia "),e.\u0275\u0275elementEnd()()()()()),2&n&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",f.backAction)("rightActions",f.rightActions),e.\u0275\u0275advance(2),e.\u0275\u0275property("informations",f.informations))},dependencies:[b.K,v.c,R.A],styles:["/*!\n * MBO TransferHistoryInformation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 19/Oct/2022\n * Updated: 10/Jul/2024\n*/mbo-transfer-history-information-page{--mbo-header-result-padding: calc( var(--mbo-application-body-safe-spacing) + var(--sizing-x4) ) var(--sizing-x4) var(--sizing-x4) var(--sizing-x4);position:relative;width:100%;height:100%;display:block}mbo-transfer-history-information-page .mbo-transfer-history-information-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-transfer-history-information-page .mbo-transfer-history-information-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-transfer-history-information-page .mbo-transfer-history-information-page__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),s})(),P=(()=>{class s{}return s.\u0275fac=function(n){return new(n||s)},s.\u0275mod=e.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=e.\u0275\u0275defineInjector({imports:[m.CommonModule,S.RouterModule.forChild([{path:"",component:F}]),l.KI,l.cN,l.A6]}),s})()}}]);