(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2687],{12687:(E,d,o)=>{o.r(d),o.d(d,{MboCustomerCertificationsModule:()=>C});var l=o(17007),a=o(78007),n=o(99877);const M=[{path:"",loadChildren:()=>o.e(4707).then(o.bind(o,24707)).then(t=>t.MboHomeCertificationsPageModule)},{path:"taxes",loadChildren:()=>o.e(1430).then(o.bind(o,51430)).then(t=>t.MboTaxCertificationsPageModule)},{path:"products",loadChildren:()=>o.e(9557).then(o.bind(o,19557)).then(t=>t.MboProductCertificationsPageModule)}];let C=(()=>{class t{}return t.\u0275fac=function(s){return new(s||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,a.RouterModule.forChild(M)]}),t})()}}]);