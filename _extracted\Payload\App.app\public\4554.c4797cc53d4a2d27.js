(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4554],{77493:(S,y,o)=>{o.d(y,{P:()=>M,G:()=>V});var h=o(15861),m=o(87956),l=o(53113),s=o(98699),g=o(38074),L=o(29306),b=o(87903),v=o(66067);class R{constructor(O,t,e,i){this.destination=O,this.source=t,this.amount=e,this.manual=i}}function p(n){return new R(n.destination,n.source,n.amount,n.manual)}var C=o(71776),I=o(39904),D=o(42168),d=o(84757),c=o(99877);let u=(()=>{class n{constructor(t,e){this.http=t,e.subscribes(I.PU,()=>{this.loans=void 0})}request(){return this.loans?Promise.resolve(this.loans):(0,D.firstValueFrom)(this.http.get(I.bV.PAYMENTS.DEBTS.CATALOG,{params:{exclude:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,d.map)(({content:t})=>t.map(e=>function r(n){return new v.T2(n.id,n.acctType,n.acctTypeName,n.loanName,n.acctId,new L.Br(n.bankId,n.bankName),n.isAval,n.dynamo||!1,n.isOwner,n.isOwner?null:n.owner,n.isOwner?null:new l.dp((0,b.nX)(n.ownerIdType),n.ownerId))}(e))),(0,d.tap)(t=>{this.loans=t})))}send(t){return(0,D.firstValueFrom)(this.http.post(I.bV.PAYMENTS.LOAN,function E(n){return{acctIdFrom:n.source.id,acctNickNameFrom:n.source.nickname,bankIdFrom:n.source.bank.id,acctIdTo:n.destination.id,acctNameTo:n.destination.nickname,bankIdTo:n.destination.bank.id,bankNameTo:n.destination.bank.name,amt:Math.ceil(n.amount),curCode:"COP",paymentDesc:""}}(t)).pipe((0,d.map)(e=>(0,b.l1)(e,"SUCCESS")))).catch(e=>(0,b.rU)(e))}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(C.HttpClient),c.\u0275\u0275inject(m.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var a=o(20691);let P=(()=>{class n extends a.Store{constructor(t){super({confirmation:!1,fromCustomer:!1,manual:!1}),this.eventBusService=t,this.eventBusService.subscribes(I.PU,()=>{this.reset()})}setDestination(t,e=!1){this.reduce(i=>({...i,destination:t,fromCustomer:e}))}getDestination(){return this.select(({destination:t})=>t)}setProduct(t){this.reduce(e=>({...e,product:t}))}getProduct(){return this.select(({product:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setSource(t){this.reduce(e=>({...e,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount({amount:t,manual:e}){this.reduce(i=>({...i,manual:e,amount:t}))}selectForAmount(){return this.select(({amount:t,confirmation:e,destination:i,source:f})=>({amount:t,confirmation:e,destination:i,source:f}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(m.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),M=(()=>{class n{constructor(t,e,i,f){this.productService=t,this.repository=e,this.store=i,this.eventBusService=f}setDestination(t){var e=this;return(0,h.Z)(function*(){try{return(0,g.p)(t)&&(yield e.productService.requestInformation(t)),s.Either.success(e.store.setDestination(t))}catch{return s.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(t){try{return s.Either.success(this.store.setSource(t))}catch({message:e}){return s.Either.failure({message:e})}}setAmount(t,e=!1){try{return s.Either.success(this.store.setAmount({amount:t,manual:e}))}catch({message:i}){return s.Either.failure({message:i})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getProduct();return this.store.reset(),s.Either.success({fromCustomer:t,product:e})}catch({message:t}){return s.Either.failure({message:t})}}send(){var t=this;return(0,h.Z)(function*(){const e=p(t.store.currentState),i=yield t.execute(e);return t.eventBusService.emit(i.channel),s.Either.success({loan:e,status:i})})()}execute(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(l.LN.error(e))}}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(m.M5),c.\u0275\u0275inject(u),c.\u0275\u0275inject(P),c.\u0275\u0275inject(m.Yd))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var N=o(89148);const{DlaPayMin:T,DlaPayTotal:U,LoanPayMin:F,LoanPayTotal:B}=N.Av;let V=(()=>{class n{constructor(t,e,i,f){this.products=t,this.productService=e,this.repository=i,this.store=f}destination(){var t=this;return(0,h.Z)(function*(){try{return s.Either.success(yield t.requestLoans())}catch({message:e}){return s.Either.failure({message:e})}})()}source(t){var e=this;return(0,h.Z)(function*(){try{const i=yield e.products.requestAccountsForTransfer(),f=e.store.itIsConfirmation(),A=yield e.requestLoan(t);return s.Either.success({confirmation:f,destination:A,products:i})}catch({message:i}){return s.Either.failure({message:i})}})()}selectAmount(){var t=this;return(0,h.Z)(function*(){try{const e=t.store.itIsConfirmation(),i=t.store.getSource(),f=t.store.getDestination(),A=yield t.productService.requestInformation(f),_=t.getMinPayment(A),j=t.getTotalPayment(A);return s.Either.success({confirmation:e,cop:{min:_,total:j},source:i})}catch({message:e}){return s.Either.failure({message:e})}})()}amount(){var t=this;return(0,h.Z)(function*(){try{const e=t.store.getDestination(),i=(0,g.p)(e)?yield t.productService.requestInformation(t.store.getDestination()):void 0,f=i&&t.getTotalPayment(i);return s.Either.success({...t.store.selectForAmount(),total:f})}catch({message:e}){return s.Either.failure({message:e})}})()}confirmation(){try{const t=p(this.store.currentState);return s.Either.success({payment:t})}catch({message:t}){return s.Either.failure({message:t})}}requestLoans(){return this.repository.request().then(t=>t.reduce((e,i)=>{const{others:f,principals:A}=e;return(i.bank.isOccidente?A:f).push(i),e},{others:[],principals:[]}))}requestLoan(t){var e=this;return(0,h.Z)(function*(){let i=e.store.getDestination();if(!i&&t){const f=yield e.products.requestProductForId(t);if(e.store.setProduct(f),f){const{principals:A}=yield e.requestLoans(),_=A.find(({number:j})=>j===f.number);_&&(yield e.productService.requestInformation(_)),i=_||f,e.store.setDestination(i,!0)}}return i})()}getMinPayment(t){return t?.getSection(F)||t?.getSection(T)}getTotalPayment(t){return t?.getSection(B)||t?.getSection(U)}}return n.\u0275fac=function(t){return new(t||n)(c.\u0275\u0275inject(m.hM),c.\u0275\u0275inject(m.M5),c.\u0275\u0275inject(u),c.\u0275\u0275inject(P))},n.\u0275prov=c.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},20225:(S,y,o)=>{o.d(y,{w:()=>b});var h=o(30263),m=o(39904),l=o(95437),s=o(77493),g=o(99877);let b=(()=>{class v{constructor(r,E,p){this.modalConfirmation=r,this.mboProvider=E,this.managerLoan=p}execute(r=!0){r?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de cr\xe9dito actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerLoan.reset().when({success:({fromCustomer:r,product:E})=>{r?this.mboProvider.navigation.back(m.Z6.CUSTOMER.PRODUCTS.INFO,{productId:E.id}):this.mboProvider.navigation.back(m.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(m.Z6.PAYMENTS.HOME)}})}}return v.\u0275fac=function(r){return new(r||v)(g.\u0275\u0275inject(h.$e),g.\u0275\u0275inject(l.ZL),g.\u0275\u0275inject(s.P))},v.\u0275prov=g.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},24554:(S,y,o)=>{o.r(y),o.d(y,{MboPaymentLoanDestinationPageModule:()=>D});var h=o(17007),m=o(78007),l=o(79798),s=o(30263),g=o(15861),L=o(39904),b=o(95437),v=o(77493),R=o(20225),r=o(99877),E=o(13043),p=o(48774),C=o(50689);let I=(()=>{class d{constructor(u,a,P,M){this.mboProvider=u,this.requestConfiguration=a,this.buildPayment=P,this.cancelProvider=M,this.confirmation=!1,this.error=!1,this.requesting=!0,this.principals=[],this.others=[],this.cancelAction={id:"btn_payment-loan-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}get hasPrincipals(){return this.principals.length>0}get hasOthers(){return this.others.length>0}get isEmpty(){return!this.requesting&&0===this.principals.length&&0===this.others.length}get msgEmpty(){return this.error?"Ocurrio un error inesperado mientras consultamos tus cr\xe9ditos por pagar, por favor intentalo de nuevo en unos minutos.":"Actualmente no cuentas con cr\xe9ditos pendientes por pagar"}onProduct(u){var a=this;return(0,g.Z)(function*(){u.isRequiredInformation&&a.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield a.buildPayment.setDestination(u)).when({success:()=>{a.mboProvider.navigation.next(L.Z6.PAYMENTS.LOAN.SOURCE)},failure:({message:P})=>{a.mboProvider.toast.error(P)}},()=>{a.mboProvider.loader.close()})})()}initializatedConfiguration(){var u=this;return(0,g.Z)(function*(){(yield u.requestConfiguration.destination()).when({success:({others:a,principals:P})=>{u.others=a,u.principals=P},failure:()=>{u.error=!0}},()=>{u.requesting=!1})})()}}return d.\u0275fac=function(u){return new(u||d)(r.\u0275\u0275directiveInject(b.ZL),r.\u0275\u0275directiveInject(v.G),r.\u0275\u0275directiveInject(v.P),r.\u0275\u0275directiveInject(R.w))},d.\u0275cmp=r.\u0275\u0275defineComponent({type:d,selectors:[["mbo-payment-loan-destination-page"]],decls:12,vars:11,consts:[[1,"mbo-payment-loan-destination-page__content"],[1,"mbo-payment-loan-destination-page__header"],["title","Cr\xe9dito","progress","25%",3,"rightAction"],[1,"mbo-payment-loan-destination-page__body"],[1,"mbo-payment-loan-destination-page__body__title","subtitle2-medium"],[1,"mbo-payment-loan-destination-page__body__content",3,"hidden"],["title","CR\xc9DITOS BANCO DE OCCIDENTE",3,"skeleton","products","header","hidden","select"],["title","CR\xc9DITOS DE OTRAS ENTIDADES",3,"products","header","hidden","select"],[1,"mbo-payment-loan-destination-page__empty",3,"hidden"]],template:function(u,a){1&u&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4),r.\u0275\u0275text(5," \xbfCual cr\xe9dito desear pagar hoy? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(6,"div",5)(7,"mbo-product-destination-selector",6),r.\u0275\u0275listener("select",function(M){return a.onProduct(M)}),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"mbo-product-destination-selector",7),r.\u0275\u0275listener("select",function(M){return a.onProduct(M)}),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(9,"mbo-message-empty",8)(10,"span"),r.\u0275\u0275text(11),r.\u0275\u0275elementEnd()()()()),2&u&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("rightAction",a.cancelAction),r.\u0275\u0275advance(4),r.\u0275\u0275property("hidden",a.isEmpty),r.\u0275\u0275advance(1),r.\u0275\u0275property("skeleton",a.requesting)("products",a.principals)("header",!1)("hidden",!a.requesting&&!a.hasPrincipals),r.\u0275\u0275advance(1),r.\u0275\u0275property("products",a.others)("header",!1)("hidden",a.requesting||!a.hasOthers),r.\u0275\u0275advance(1),r.\u0275\u0275property("hidden",!a.isEmpty),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate(a.msgEmpty))},dependencies:[E.e,p.J,C.A],styles:["/*!\n * MBO PaymentLoanDestination Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 27/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-payment-loan-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-loan-destination-page .mbo-payment-loan-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-loan-destination-page .mbo-payment-loan-destination-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-loan-destination-page .mbo-payment-loan-destination-page__body__title{color:var(--color-carbon-darker-1000)}mbo-payment-loan-destination-page .mbo-payment-loan-destination-page__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}\n"],encapsulation:2}),d})(),D=(()=>{class d{}return d.\u0275fac=function(u){return new(u||d)},d.\u0275mod=r.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=r.\u0275\u0275defineInjector({imports:[h.CommonModule,m.RouterModule.forChild([{path:"",component:I}]),l.eM,s.Jx,l.Aj]}),d})()},63674:(S,y,o)=>{o.d(y,{Eg:()=>v,Lo:()=>s,Wl:()=>g,ZC:()=>L,_f:()=>m,br:()=>b,tl:()=>l});var h=o(29306);const m={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},l=new h.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),s={color:"success",key:"paid",label:"Pagada"},g={color:"alert",key:"pending",label:"Por pagar"},L={color:"danger",key:"expired",label:"Vencida"},b={color:"info",key:"recurring",label:"Pago recurrente"},v={color:"info",key:"programmed",label:"Programado"}},66067:(S,y,o)=>{o.d(y,{S6:()=>R,T2:()=>b,UQ:()=>r,mZ:()=>v});var h=o(39904),m=o(6472),s=o(63674),g=o(31707);class b{constructor(p,C,I,D,d,c,u,a,P,M,N){this.id=p,this.type=C,this.name=I,this.nickname=D,this.number=d,this.bank=c,this.isAval=u,this.isProtected=a,this.isOwner=P,this.ownerName=M,this.ownerDocument=N,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[h.y1],this.initialsName=(0,m.initials)(D),this.shortNumber=d.substring(d.length-4),this.descriptionNumber=`${I} ${d}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(p){this.informationValue||(this.informationValue=p)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(p){return this.currenciesValue.includes(p)}}class v{constructor(p,C){this.id=p,this.type=C}}class R{constructor(p,C,I,D,d,c,u,a,P,M,N,T){this.uuid=p,this.number=C,this.nie=I,this.nickname=D,this.companyId=d,this.companyName=c,this.amount=u,this.registerDate=a,this.expirationDate=P,this.paid=M,this.statusCode=N,this.references=T,this.recurring=T.length>0,this.status=function L(E){switch(E){case g.U.EXPIRED:return s.ZC;case g.U.PENDING:return s.Wl;case g.U.PROGRAMMED:return s.Eg;case g.U.RECURRING:return s.br;default:return s.Lo}}(N)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class r{constructor(p,C,I,D,d,c,u,a){this.uuid=p,this.number=C,this.nickname=I,this.companyId=D,this.companyName=d,this.city=c,this.amount=u,this.isBiller=a}}},38074:(S,y,o)=>{o.d(y,{p:()=>m});var h=o(29306);function m(l){return l instanceof h.xs||l.isRequiredInformation}},31707:(S,y,o)=>{o.d(y,{U:()=>h,f:()=>m});var h=(()=>{return(l=h||(h={})).RECURRING="1",l.EXPIRED="2",l.PENDING="3",l.PROGRAMMED="4",h;var l})(),m=(()=>{return(l=m||(m={})).BILLER="Servicio",l.NON_BILLER="Servicio",l.PSE="Servicio",l.TAX="Impuesto",l.LOAN="Obligaci\xf3n financiera",l.CREDIT_CARD="Obligaci\xf3n financiera",m;var l})()}}]);