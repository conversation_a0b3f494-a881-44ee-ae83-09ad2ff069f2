(self.webpackChunkapp=self.webpackChunkapp||[]).push([[254],{9736:(B,C,o)=>{o.d(C,{Hu:()=>s,wG:()=>F,rQ:()=>D,_G:()=>L});var f=o(15861),b=o(87956),p=o(53113),a=o(98699);class g{constructor(u,e,n,i,d){this.id=u,this.nit=e,this.name=n,this.city=i,this.exampleUrl=d}}class N{constructor(u,e,n,i){this.number=u,this.amount=e,this.expirationDate=n,this.companyId=i}get expirationFormat(){return this.expirationDate.dateFormat}}class x{constructor(u,e,n){this.agreement=u,this.invoice=e,this.source=n}}function v(t){return new x(t.agreement,t.invoice,t.source)}function y(t){return new g(t.orgIdNum,t.industNum,t.name,t.city,t.imageUrl||t.image?.url)}var E=o(71776),M=o(39904),O=o(87903),P=o(42168),I=o(84757),l=o(99877);let T=(()=>{class t{constructor(e){this.http=e}requestAll(e){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"20",name:e,page:"0"}}).pipe((0,I.map)(({content:n})=>n.map(i=>y(i)))))}requestCompanyId(e){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.AGREEMENT,{params:{channel:"PB",items:"1",orgIdNum:e,page:"0"}}).pipe((0,I.map)(({content:n})=>n.length?y(n[0]):null)))}requestInvoice(e,n){return(0,P.firstValueFrom)(this.http.get(M.bV.PAYMENTS.INVOICE,{params:{nie:e,orgIdNum:n}}).pipe((0,I.map)(i=>function h(t){return new N(t.nie||t.invoiceNum,+t.amt,new p.ou(t.expDt),t.orgIdNum)}(i))))}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),m=(()=>{class t{constructor(e){this.http=e}send(e){return(0,P.firstValueFrom)(this.http.post(M.bV.PAYMENTS.INVOICE_MANUAL,function r(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:t.invoice.amount.toString(),nie:t.invoice.number,invoiceNum:t.invoice.number,pmtCodServ:t.invoice.companyId,toEntity:t.agreement.name,toNickname:t.agreement.name,expDt:t.invoice.expirationDate.toISOString()}]}(e)).pipe((0,I.map)(([n])=>(0,O.l1)(n,"SUCCESS")))).catch(n=>(0,O.rU)(n))}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var A=o(20691);let c=(()=>{class t extends A.Store{constructor(e){super({confirmation:!1}),e.subscribes(M.PU,()=>{this.reset()})}setAgreement(e){this.reduce(n=>({...n,agreement:e}))}getAgreement(){return this.select(({agreement:e})=>e)}setInvoice(e){this.reduce(n=>({...n,invoice:e}))}getInvoice(){return this.select(({invoice:e})=>e)}setSource(e){this.reduce(n=>({...n,source:e}))}getSource(){return this.select(({source:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(b.Yd))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),s=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.eventBusService=i}setAgreement(e){try{return a.Either.success(this.store.setAgreement(e))}catch({message:n}){return a.Either.failure({message:n})}}setInvoice(e){try{return a.Either.success(this.store.setInvoice(e))}catch({message:n}){return a.Either.failure({message:n})}}setSource(e){try{return a.Either.success(this.store.setSource(e))}catch({message:n}){return a.Either.failure({message:n})}}reset(){try{return a.Either.success(this.store.reset())}catch({message:e}){return a.Either.failure({message:e})}}send(){var e=this;return(0,f.Z)(function*(){const n=v(e.store.currentState),i=yield e.execute(n);return e.eventBusService.emit(i.channel),a.Either.success({invoice:n,status:i})})()}execute(e){try{return this.repository.send(e)}catch({message:n}){return Promise.resolve(p.LN.error(n))}}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(m),l.\u0275\u0275inject(c),l.\u0275\u0275inject(b.Yd))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),F=(()=>{class t{constructor(e){this.repository=e}all(e){var n=this;return(0,f.Z)(function*(){try{return a.Either.success(yield n.repository.requestAll(e))}catch({message:i,status:d}){return 400===d?a.Either.success([]):a.Either.failure({message:i})}})()}invoice(e,{id:n}){var i=this;return(0,f.Z)(function*(){try{return a.Either.success(yield i.repository.requestInvoice(e,n))}catch({message:d,status:j}){return a.Either.failure({value:400===j,message:d})}})()}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(T))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),D=(()=>{class t{constructor(e,n){this.products=e,this.store=n}reference(){try{const e=this.store.getAgreement(),n=this.store.getInvoice();return a.Either.success({agreement:e,reference:n?.number})}catch({message:e}){return a.Either.failure({message:e})}}source(){var e=this;return(0,f.Z)(function*(){try{const n=yield e.products.requestAccountsForTransfer(),i=e.store.getAgreement(),d=e.store.getInvoice();return a.Either.success({agreement:i,invoice:d,products:n})}catch({message:n}){return a.Either.failure({message:n})}})()}confirmation(){try{const e=v(this.store.currentState);return a.Either.success({payment:e})}catch({message:e}){return a.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(b.hM),l.\u0275\u0275inject(c))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const U=/^415(\d+)8020(\d+)$/;let V=(()=>{class t{constructor(e){this.http=e}execute(e){var n=this;return(0,f.Z)(function*(){const i=e.replace(/\D/g,"").match(U);if(!i)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");const d=yield n.requestNuraCodes(),j=i[1],R=d.find(({ean_code:_})=>_===j);if(!R)throw new Error("El c\xf3digo proporcionado no es una factura reconocida");return{reference:i[2].slice(0,+R.length),companyId:R.service_code.padStart(8,"0")}})()}requestNuraCodes(){return this.codes?Promise.resolve(this.codes):(0,P.firstValueFrom)(this.http.get("assets/nura-codes.json").pipe((0,I.tap)(e=>this.codes=e)))}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(E.HttpClient))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),L=(()=>{class t{constructor(e,n,i){this.repository=e,this.store=n,this.barcodeService=i}execute(e){var n=this;return(0,f.Z)(function*(){try{const{reference:i,companyId:d}=yield n.barcodeService.execute(e),j=yield n.repository.requestCompanyId(d),R=yield n.repository.requestInvoice(i,d);return n.store.setAgreement(j),n.store.setInvoice(R),a.Either.success()}catch{return a.Either.failure({message:"No se pudo recuperar la informaci\xf3n del c\xf3digo de factura escaneado."})}})()}}return t.\u0275fac=function(e){return new(e||t)(l.\u0275\u0275inject(T),l.\u0275\u0275inject(c),l.\u0275\u0275inject(V))},t.\u0275prov=l.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},74242:(B,C,o)=>{o.d(C,{s:()=>x});var f=o(39904),b=o(95437),p=o(30263),a=o(9736),g=o(99877);let x=(()=>{class v{constructor(h,r,E){this.modalConfirmation=h,this.mboProvider=r,this.managerInvoice=E}execute(h=!0){h?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de la factura actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerInvoice.reset(),this.mboProvider.navigation.back(f.Z6.PAYMENTS.HOME)}}return v.\u0275fac=function(h){return new(h||v)(g.\u0275\u0275inject(p.$e),g.\u0275\u0275inject(b.ZL),g.\u0275\u0275inject(a.Hu))},v.\u0275prov=g.\u0275\u0275defineInjectable({token:v,factory:v.\u0275fac,providedIn:"root"}),v})()},30254:(B,C,o)=>{o.r(C),o.d(C,{MboPaymentInvoiceManualReferencePageModule:()=>T});var f=o(17007),b=o(78007),p=o(30263),a=o(15861),g=o(24495),N=o(39904),x=o(95437),v=o(57544),y=o(9736),h=o(74242),r=o(99877),E=o(48774),M=o(6661),O=o(35641),P=o(45542);const I=N.Z6.PAYMENTS.SERVICES.INVOICE_MANUAL;let l=(()=>{class m{constructor(c,s,S,F,D){this.mboProvider=c,this.requestConfiguration=s,this.requestAgreements=S,this.managerInvoice=F,this.cancelProvider=D,this.requesting=!1,this.exampleStatus=!1,this.backAction={id:"btn_payment-invoice-manual-reference_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(I.AGREEMENT)}},this.cancelAction={id:"btn_payment-invoice-manual-reference_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.referenceControl=new v.FormControl(void 0,[g.C1])}ngOnInit(){this.initializatedConfiguration()}get exampleLabel(){return this.exampleStatus?"Ocultar ejemplo":"Mostrar ejemplo"}get exampleIcon(){return this.exampleStatus?"eye-incognito-on":"eye-show-visible"}get disabled(){return this.referenceControl.invalid||this.requesting}onExample(){this.exampleStatus=!this.exampleStatus}onSubmit(){var c=this;return(0,a.Z)(function*(){c.mboProvider.loader.open("Solicitando facturas, por favor espere..."),c.requesting=!0,(yield c.requestAgreements.invoice(c.referenceControl.value,c.agreement)).when({success:s=>c.managerInvoice.setInvoice(s).when({success:()=>{c.mboProvider.navigation.next(I.SOURCE)}}),failure:()=>c.mboProvider.toast.error("No se encontr\xf3 factura con la referencia establecida","Error en factura")},()=>{c.mboProvider.loader.close(),c.requesting=!1})})()}initializatedConfiguration(){this.requestConfiguration.reference().when({success:({agreement:c,reference:s})=>{this.agreement=c,this.referenceControl.setValue(s)}})}}return m.\u0275fac=function(c){return new(c||m)(r.\u0275\u0275directiveInject(x.ZL),r.\u0275\u0275directiveInject(y.rQ),r.\u0275\u0275directiveInject(y.wG),r.\u0275\u0275directiveInject(y.Hu),r.\u0275\u0275directiveInject(h.s))},m.\u0275cmp=r.\u0275\u0275defineComponent({type:m,selectors:[["mbo-payment-invoice-manual-reference-page"]],decls:17,vars:11,consts:[[1,"mbo-payment-invoice-manual-reference-page__content"],[1,"mbo-payment-invoice-manual-reference-page__header"],["title","Referencia","progress","60%",3,"leftAction","rightAction"],[1,"mbo-payment-invoice-manual-reference-page__body"],[3,"title","subtitle"],[1,"mbo-payment-invoice-manual-reference-page__title","subtitle2-medium"],["elementId","txt_payment-invoice-manual-reference_value","label","Referencia de pago","placeholder","000 000 00","type","none",3,"formControl"],["id","btn_payment-invoice-manual-reference_example","bocc-button","flat",3,"prefixIcon","disabled","click"],[1,"mbo-payment-invoice-manual-reference-page__example",3,"hidden"],[3,"src"],[1,"mbo-payment-invoice-manual-reference-page__footer"],["id","btn_payment-invoice-manual-reference_submit","bocc-button","raised",3,"disabled","click"]],template:function(c,s){1&c&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3),r.\u0275\u0275element(4,"bocc-card-service",4),r.\u0275\u0275elementStart(5,"div",5),r.\u0275\u0275text(6," Ingrese la referencia de pago "),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(7,"bocc-growing-box",6),r.\u0275\u0275elementStart(8,"button",7),r.\u0275\u0275listener("click",function(){return s.onExample()}),r.\u0275\u0275elementStart(9,"span"),r.\u0275\u0275text(10),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(11,"div",8),r.\u0275\u0275element(12,"img",9),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(13,"div",10)(14,"button",11),r.\u0275\u0275listener("click",function(){return s.onSubmit()}),r.\u0275\u0275elementStart(15,"span"),r.\u0275\u0275text(16,"Continuar"),r.\u0275\u0275elementEnd()()()),2&c&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",s.backAction)("rightAction",s.cancelAction),r.\u0275\u0275advance(2),r.\u0275\u0275property("title",null==s.agreement?null:s.agreement.name)("subtitle",null==s.agreement?null:s.agreement.city),r.\u0275\u0275advance(3),r.\u0275\u0275property("formControl",s.referenceControl),r.\u0275\u0275advance(1),r.\u0275\u0275property("prefixIcon",s.exampleIcon)("disabled",!(null!=s.agreement&&s.agreement.exampleUrl)),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate(s.exampleLabel),r.\u0275\u0275advance(1),r.\u0275\u0275property("hidden",!s.exampleStatus),r.\u0275\u0275advance(1),r.\u0275\u0275property("src",null==s.agreement?null:s.agreement.exampleUrl,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(2),r.\u0275\u0275property("disabled",s.disabled))},dependencies:[E.J,M.S,O.d,P.P],styles:["/*!\n * MBO PaymentInvoiceManualReference Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 02/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-invoice-manual-reference-page{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__content .bocc-card-service{margin-bottom:var(--sizing-x12)}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__content .bocc-card-service__content{background:var(--color-carbon-lighter-200);border:none;box-shadow:none}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__body button{width:100%}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__example{position:relative;display:flex;width:100%;justify-content:center}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__example img{max-width:100%}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-payment-invoice-manual-reference-page .mbo-payment-invoice-manual-reference-page__footer button{width:100%}\n"],encapsulation:2}),m})(),T=(()=>{class m{}return m.\u0275fac=function(c){return new(c||m)},m.\u0275mod=r.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=r.\u0275\u0275defineInjector({imports:[f.CommonModule,b.RouterModule.forChild([{path:"",component:l}]),p.Jx,p.S8,p.dH,p.Zl,p.P8]}),m})()}}]);