(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5038],{86437:(H,T,a)=>{a.d(T,{f:()=>D});var g=a(97582),p=64,E=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),A=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],I=Math.pow(2,53)-1,h=function(){function S(){this.state=Int32Array.from(A),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return S.prototype.update=function(l){if(this.finished)throw new Error("Attempted to update an already finished hash.");var m=0,v=l.byteLength;if(this.bytesHashed+=v,8*this.bytesHashed>I)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;v>0;)this.buffer[this.bufferLength++]=l[m++],v--,this.bufferLength===p&&(this.hashBuffer(),this.bufferLength=0)},S.prototype.digest=function(){if(!this.finished){var l=8*this.bytesHashed,m=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),v=this.bufferLength;if(m.setUint8(this.bufferLength++,128),v%p>=56){for(var k=this.bufferLength;k<p;k++)m.setUint8(k,0);this.hashBuffer(),this.bufferLength=0}for(k=this.bufferLength;k<56;k++)m.setUint8(k,0);m.setUint32(56,Math.floor(l/4294967296),!0),m.setUint32(60,l),this.hashBuffer(),this.finished=!0}var G=new Uint8Array(32);for(k=0;k<8;k++)G[4*k]=this.state[k]>>>24&255,G[4*k+1]=this.state[k]>>>16&255,G[4*k+2]=this.state[k]>>>8&255,G[4*k+3]=this.state[k]>>>0&255;return G},S.prototype.hashBuffer=function(){for(var m=this.buffer,v=this.state,k=v[0],G=v[1],Y=v[2],q=v[3],ee=v[4],ce=v[5],se=v[6],b=v[7],x=0;x<p;x++){if(x<16)this.temp[x]=(255&m[4*x])<<24|(255&m[4*x+1])<<16|(255&m[4*x+2])<<8|255&m[4*x+3];else{var U=this.temp[x-2];this.temp[x]=(((U>>>17|U<<15)^(U>>>19|U<<13)^U>>>10)+this.temp[x-7]|0)+((((U=this.temp[x-15])>>>7|U<<25)^(U>>>18|U<<14)^U>>>3)+this.temp[x-16]|0)}var X=(((ee>>>6|ee<<26)^(ee>>>11|ee<<21)^(ee>>>25|ee<<7))+(ee&ce^~ee&se)|0)+(b+(E[x]+this.temp[x]|0)|0)|0,te=((k>>>2|k<<30)^(k>>>13|k<<19)^(k>>>22|k<<10))+(k&G^k&Y^G&Y)|0;b=se,se=ce,ce=ee,ee=q+X|0,q=Y,Y=G,G=k,k=X+te|0}v[0]+=k,v[1]+=G,v[2]+=Y,v[3]+=q,v[4]+=ee,v[5]+=ce,v[6]+=se,v[7]+=b},S}(),y=typeof Buffer<"u"&&Buffer.from?function(S){return Buffer.from(S,"utf8")}:S=>(new TextEncoder).encode(S);function L(S){return S instanceof Uint8Array?S:"string"==typeof S?y(S):ArrayBuffer.isView(S)?new Uint8Array(S.buffer,S.byteOffset,S.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(S)}var D=function(){function S(l){this.secret=l,this.hash=new h,this.reset()}return S.prototype.update=function(l){if(!function C(S){return"string"==typeof S?0===S.length:0===S.byteLength}(l)&&!this.error)try{this.hash.update(L(l))}catch(m){this.error=m}},S.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},S.prototype.digest=function(){return(0,g.mG)(this,void 0,void 0,function(){return(0,g.Jh)(this,function(l){return[2,this.digestSync()]})})},S.prototype.reset=function(){if(this.hash=new h,this.secret){this.outer=new h;var l=function P(S){var l=L(S);if(l.byteLength>p){var m=new h;m.update(l),l=m.digest()}var v=new Uint8Array(p);return v.set(l),v}(this.secret),m=new Uint8Array(p);m.set(l);for(var v=0;v<p;v++)l[v]^=54,m[v]^=92;for(this.hash.update(l),this.outer.update(m),v=0;v<l.byteLength;v++)l[v]=0}},S}()},95472:(H,T,a)=>{a.d(T,{N:()=>E});const g={},p={};for(let A=0;A<256;A++){let I=A.toString(16).toLowerCase();1===I.length&&(I=`0${I}`),g[A]=I,p[I]=A}function E(A){let I="";for(let h=0;h<A.byteLength;h++)I+=g[A[h]];return I}},23192:(H,T,a)=>{a.d(T,{SQ:()=>E,Xb:()=>h});var g=a(55502),p=a(92261),M=a(91935);const E=typeof Symbol<"u"?Symbol("amplify_default"):"@@amplify_default",A=new g.k("Hub");class I{constructor(L){this.listeners=new Map,this.protectedChannels=["core","auth","api","analytics","interactions","pubsub","storage","ui","xr"],this.name=L}_remove(L,C){const D=this.listeners.get(L);D?this.listeners.set(L,[...D.filter(({callback:P})=>P!==C)]):A.warn(`No listeners for ${L}`)}dispatch(L,C,D,P){"string"==typeof L&&this.protectedChannels.indexOf(L)>-1&&(P===E||A.warn(`WARNING: ${L} is protected and dispatching on it can have unintended consequences`));const S={channel:L,payload:{...C},source:D,patternInfo:[]};try{this._toListeners(S)}catch(l){A.error(l)}}listen(L,C,D="noname"){let P;if("function"!=typeof C)throw new M._({name:p.z2,message:"No callback supplied to Hub"});P=C;let S=this.listeners.get(L);return S||(S=[],this.listeners.set(L,S)),S.push({name:D,callback:P}),()=>{this._remove(L,P)}}_toListeners(L){const{channel:C,payload:D}=L,P=this.listeners.get(C);P&&P.forEach(S=>{A.debug(`Dispatching to ${C} with `,D);try{S.callback(L)}catch(l){A.error(l)}})}}const h=new I("__default__");new I("internal-hub")},55502:(H,T,a)=>{a.d(T,{k:()=>E});var g=a(92261),p=(()=>{return(A=p||(p={})).DEBUG="DEBUG",A.ERROR="ERROR",A.INFO="INFO",A.WARN="WARN",A.VERBOSE="VERBOSE",A.NONE="NONE",p;var A})();const M={VERBOSE:1,DEBUG:2,INFO:3,WARN:4,ERROR:5,NONE:6};let E=(()=>{class A{constructor(h,R=p.WARN){this.name=h,this.level=R,this._pluggables=[]}_padding(h){return h<10?"0"+h:""+h}_ts(){const h=new Date;return[this._padding(h.getMinutes()),this._padding(h.getSeconds())].join(":")+"."+h.getMilliseconds()}configure(h){return h?(this._config=h,this._config):this._config}_log(h,...R){let y=this.level;if(A.LOG_LEVEL&&(y=A.LOG_LEVEL),typeof window<"u"&&window.LOG_LEVEL&&(y=window.LOG_LEVEL),!(M[h]>=M[y]))return;let D=console.log.bind(console);h===p.ERROR&&console.error&&(D=console.error.bind(console)),h===p.WARN&&console.warn&&(D=console.warn.bind(console)),A.BIND_ALL_LOG_LEVELS&&(h===p.INFO&&console.info&&(D=console.info.bind(console)),h===p.DEBUG&&console.debug&&(D=console.debug.bind(console)));const P=`[${h}] ${this._ts()} ${this.name}`;let S="";if(1===R.length&&"string"==typeof R[0])S=`${P} - ${R[0]}`,D(S);else if(1===R.length)S=`${P} ${R[0]}`,D(P,R[0]);else if("string"==typeof R[0]){let l=R.slice(1);1===l.length&&(l=l[0]),S=`${P} - ${R[0]} ${l}`,D(`${P} - ${R[0]}`,l)}else S=`${P} ${R}`,D(P,R);for(const l of this._pluggables){const m={message:S,timestamp:Date.now()};l.pushLogs([m])}}log(...h){this._log(p.INFO,...h)}info(...h){this._log(p.INFO,...h)}warn(...h){this._log(p.WARN,...h)}error(...h){this._log(p.ERROR,...h)}debug(...h){this._log(p.DEBUG,...h)}verbose(...h){this._log(p.VERBOSE,...h)}addPluggable(h){h&&h.getCategoryName()===g.YG&&(this._pluggables.push(h),h.configure(this._config))}listPluggables(){return this._pluggables}}return A.LOG_LEVEL=null,A.BIND_ALL_LOG_LEVELS=!1,A})()},91396:(H,T,a)=>{a.d(T,{Cj:()=>te,QW:()=>he});var g=a(81220);const p=()=>typeof global<"u",E=()=>typeof window<"u",A=()=>typeof document<"u",I=()=>typeof process<"u",h=(Z,o)=>!!Object.keys(Z).find(i=>i.startsWith(o)),ce=[{platform:g.gQ.Expo,detectionMethod:function q(){return p()&&typeof global.expo<"u"}},{platform:g.gQ.ReactNative,detectionMethod:function Y(){return typeof navigator<"u"&&typeof navigator.product<"u"&&"ReactNative"===navigator.product}},{platform:g.gQ.NextJs,detectionMethod:function S(){return E()&&window.next&&"object"==typeof window.next}},{platform:g.gQ.Nuxt,detectionMethod:function m(){return E()&&(void 0!==window.__NUXT__||void 0!==window.$nuxt)}},{platform:g.gQ.Angular,detectionMethod:function k(){const Z=Boolean(A()&&document.querySelector("[ng-version]")),o=Boolean(E()&&typeof window.ng<"u");return Z||o}},{platform:g.gQ.React,detectionMethod:function R(){const Z=c=>c.startsWith("_react")||c.startsWith("__react");return A()&&Array.from(document.querySelectorAll("[id]")).some(c=>Object.keys(c).find(Z))}},{platform:g.gQ.VueJs,detectionMethod:function L(){return E()&&h(window,"__VUE")}},{platform:g.gQ.Svelte,detectionMethod:function D(){return E()&&h(window,"__SVELTE")}},{platform:g.gQ.WebUnknown,detectionMethod:function ee(){return E()}},{platform:g.gQ.NextJsSSR,detectionMethod:function l(){return p()&&(h(global,"__next")||h(global,"__NEXT"))}},{platform:g.gQ.NuxtSSR,detectionMethod:function v(){return p()&&typeof global.__NUXT_PATHS__<"u"}},{platform:g.gQ.ReactSSR,detectionMethod:function y(){return I()&&typeof process.env<"u"&&!!Object.keys(process.env).find(Z=>Z.includes("react"))}},{platform:g.gQ.VueJsSSR,detectionMethod:function C(){return p()&&h(global,"__VUE")}},{platform:g.gQ.AngularSSR,detectionMethod:function G(){return I()&&"object"==typeof process.env&&process.env.npm_lifecycle_script?.startsWith("ng ")||!1}},{platform:g.gQ.SvelteSSR,detectionMethod:function P(){return I()&&typeof process.env<"u"&&!!Object.keys(process.env).find(Z=>Z.includes("svelte"))}}];let b;const x=[];let U=!1;const X=1e3,te=()=>{if(!b){if(b=function se(){return ce.find(Z=>Z.detectionMethod())?.platform||g.gQ.ServerSideUnknown}(),U)for(;x.length;)x.pop()?.();else x.forEach(Z=>{Z()});ue(g.gQ.ServerSideUnknown,10),ue(g.gQ.WebUnknown,10)}return b},he=Z=>{U||x.push(Z)};function ue(Z,o){b===Z&&!U&&setTimeout(()=>{(function ie(){b=void 0})(),U=!0,setTimeout(te,X)},o)}},5919:(H,T,a)=>{a.d(T,{Zm:()=>D});var g=a(81220);const p="6.13.1";var M=a(91396);const E={},h="aws-amplify",R=P=>P.replace(/\+.*/,"");new class y{constructor(){this.userAgent=`${h}/${R(p)}`}get framework(){return(0,M.Cj)()}get isReactNative(){return this.framework===g.gQ.ReactNative||this.framework===g.gQ.Expo}observeFrameworkChanges(S){(0,M.QW)(S)}};const D=P=>(({category:P,action:S}={})=>{const l=[[h,R(p)]];if(P&&l.push([P,S]),l.push(["framework",(0,M.Cj)()]),P&&S){const m=((P,S)=>E[P]?.[S]?.additionalDetails)(P,S);m&&m.forEach(v=>{l.push(v)})}return l})(P).map(([m,v])=>m&&v?`${m}/${v}`:m).join(" ")},81220:(H,T,a)=>{a.d(T,{WD:()=>p,gQ:()=>g,gq:()=>A});var g=(()=>{return(l=g||(g={})).WebUnknown="0",l.React="1",l.NextJs="2",l.Angular="3",l.VueJs="4",l.Nuxt="5",l.Svelte="6",l.ServerSideUnknown="100",l.ReactSSR="101",l.NextJsSSR="102",l.AngularSSR="103",l.VueJsSSR="104",l.NuxtSSR="105",l.SvelteSSR="106",l.ReactNative="201",l.Expo="202",g;var l})(),p=(()=>{return(l=p||(p={})).AI="ai",l.API="api",l.Auth="auth",l.Analytics="analytics",l.DataStore="datastore",l.Geo="geo",l.InAppMessaging="inappmessaging",l.Interactions="interactions",l.Predictions="predictions",l.PubSub="pubsub",l.PushNotification="pushnotification",l.Storage="storage",p;var l})(),A=(()=>{return(l=A||(A={})).GraphQl="1",l.Get="2",l.Post="3",l.Put="4",l.Patch="5",l.Del="6",l.Head="7",A;var l})()},40454:(H,T,a)=>{a.d(T,{b:()=>M});const g={id:"aws",outputs:{dnsSuffix:"amazonaws.com"},regionRegex:"^(us|eu|ap|sa|ca|me|af)\\-\\w+\\-\\d+$",regions:["aws-global"]},p={partitions:[g,{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn"},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:["aws-cn-global"]}]},M=E=>{const{partitions:A}=p;for(const{regions:I,outputs:h,regionRegex:R}of A){const y=new RegExp(R);if(I.includes(E)||y.test(E))return h.dnsSuffix}return g.outputs.dnsSuffix}},6639:(H,T,a)=>{a.d(T,{Z:()=>l});var g=a(3454),p=a(15861),M=a(74109);a(86437),a(95472);const I=m=>new Date(Date.now()+m);var D=a(46570),P=a(4079),S=a(6990);const l=(0,P.V)(S.S,[D.n,g.d,({credentials:m,region:v,service:k,uriEscapePath:G=!0})=>{let Y;return(q,ee)=>function(){var ce=(0,p.Z)(function*(b){Y=Y??0;const x={credentials:"function"==typeof m?yield m({forceRefresh:!!ee?.isCredentialsExpired}):m,signingDate:I(Y),signingRegion:v,signingService:k,uriEscapePath:G},U=yield(0,M.C)(b,x),$=yield q(U),F=(({headers:m}={})=>m?.date??m?.Date??m?.["x-amz-date"])($);return F&&(Y=((m,v)=>((m,v)=>Math.abs(I(v).getTime()-m)>=3e5)(m,v)?m-Date.now():v)(Date.parse(F),Y)),$});return function se(b){return ce.apply(this,arguments)}}()}])},6990:(H,T,a)=>{a.d(T,{S:()=>I});var g=a(15861),p=a(91935),M=a(87199);const E=h=>{let R;return()=>(R||(R=h()),R)},A=h=>!["HEAD","GET","DELETE"].includes(h.toUpperCase()),I=function(){var h=(0,g.Z)(function*({url:R,method:y,headers:L,body:C},{abortSignal:D,cache:P,withCrossDomainCredentials:S}){let l;try{l=yield fetch(R,{method:y,headers:L,body:A(y)?C:void 0,signal:D,cache:P,credentials:S?"include":"same-origin"})}catch(G){throw G instanceof TypeError?new p._({name:M.Z.NetworkError,message:"A network error has occurred.",underlyingError:G}):G}const m={};return l.headers?.forEach((G,Y)=>{m[Y.toLowerCase()]=G}),{statusCode:l.status,headers:m,body:null,body:Object.assign(l.body??{},{text:E(()=>l.text()),blob:E(()=>l.blob()),json:E(()=>l.json())})}});return function(y,L){return h.apply(this,arguments)}}()},54473:(H,T,a)=>{a.d(T,{y:()=>A});var g=a(3454),p=a(46570),M=a(4079),E=a(6990);const A=(0,M.V)(E.S,[p.n,g.d])},62942:(H,T,a)=>{a.d(T,{z:()=>p});var g=a(15861);const p=(M,E,A,I)=>function(){var h=(0,g.Z)(function*(R,y){const L={...I,...R},C=yield L.endpointResolver(L,y),D=yield E(y,C),P=yield M(D,{...L});return A(P)});return function(R,y){return h.apply(this,arguments)}}()},4079:(H,T,a)=>{a.d(T,{V:()=>g});const g=(p,M)=>(E,A)=>{const I={};let h=R=>p(R,A);for(let R=M.length-1;R>=0;R--)h=(0,M[R])(A)(h,I);return h(E)}},67834:(H,T,a)=>{a.d(T,{j:()=>A});var g=a(15861),p=a(87199);const M=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch","BadRequestException"],E=C=>!!C&&M.includes(C),A=C=>function(){var D=(0,g.Z)(function*(P,S){const l=S??(yield C(P))??void 0,m=l?.code||l?.name,v=P?.statusCode;return{retryable:y(S)||R(v,m)||E(m)||L(v,m)}});return function(P,S){return D.apply(this,arguments)}}(),I=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException"],h=["TimeoutError","RequestTimeout","RequestTimeoutException"],R=(C,D)=>429===C||!!D&&I.includes(D),y=C=>[p.Z.NetworkError,"ERR_NETWORK"].includes(C?.name),L=(C,D)=>!!C&&[500,502,503,504].includes(C)||!!D&&h.includes(D)},79987:(H,T,a)=>{a.d(T,{k:()=>M});var g=a(75599);const p=3e5,M=E=>{const I=(0,g.k)(p)(E);return!1===I?p:I}},3454:(H,T,a)=>{a.d(T,{d:()=>M});var g=a(15861);const M=({maxAttempts:I=3,retryDecider:h,computeDelay:R,abortSignal:y})=>{if(I<1)throw new Error("maxAttempts must be greater than 0");return(L,C)=>function(){var D=(0,g.Z)(function*(S){let l,v,m=C.attemptsCount??0;const k=()=>{if(v)return A(v,m),v;throw A(l,m),l};for(;!y?.aborted&&m<I;){try{v=yield L(S),l=void 0}catch(q){l=q,v=void 0}m=(C.attemptsCount??0)>m?C.attemptsCount??0:m+1,C.attemptsCount=m;const{isCredentialsExpiredError:G,retryable:Y}=yield h(v,l,C);if(!Y)return k();if(C.isCredentialsExpired=!!G,!y?.aborted&&m<I){const q=R(m);yield E(q,y)}}if(y?.aborted)throw new Error("Request aborted.");return k()});return function P(S){return D.apply(this,arguments)}}()},E=(I,h)=>{if(h?.aborted)return Promise.resolve();let R,y;const L=new Promise(C=>{y=C,R=setTimeout(C,I)});return h?.addEventListener("abort",function C(D){clearTimeout(R),h?.removeEventListener("abort",C),y()}),L},A=(I,h)=>{"[object Object]"===Object.prototype.toString.call(I)&&(I.$metadata={...I.$metadata??{},attempts:h})}},74109:(H,T,a)=>{a.d(T,{C:()=>c});const g=n=>Object.keys(n).map(d=>d.toLowerCase()).sort().join(";"),D="X-Amz-Date".toLowerCase(),P="X-Amz-Security-Token".toLowerCase(),S="aws4_request",l="AWS4-HMAC-SHA256";var ee=a(86437),ce=a(95472);const se=(n,d)=>{const f=new ee.f(n??void 0);return f.update(d),f.digestSync()},b=(n,d)=>{const f=se(n,d);return(0,ce.N)(f)},x=n=>Object.entries(n).map(([d,f])=>({key:d.toLowerCase(),value:f?.trim().replace(/\s+/g," ")??""})).sort((d,f)=>d.key<f.key?-1:1).map(d=>`${d.key}:${d.value}\n`).join(""),U=n=>Array.from(n).sort(([d,f],[O,j])=>d===O?f<j?-1:1:d<O?-1:1).map(([d,f])=>`${$(d)}=${$(f)}`).join("&"),$=n=>encodeURIComponent(n).replace(/[!'()*]/g,F),F=n=>`%${n.charCodeAt(0).toString(16).toUpperCase()}`,X=(n,d=!0)=>n?d?encodeURIComponent(n).replace(/%2F/g,"/"):n:"/",te=n=>null==n?"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855":he(n)?b(null,n):"UNSIGNED-PAYLOAD",he=n=>"string"==typeof n||ArrayBuffer.isView(n)||ie(n),ie=n=>"function"==typeof ArrayBuffer&&n instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(n),c=(n,d)=>{const f=(({credentials:n,signingDate:d=new Date,signingRegion:f,signingService:O,uriEscapePath:j=!0})=>{const{accessKeyId:V,secretAccessKey:N,sessionToken:B}=n,{longDate:J,shortDate:le}=(n=>{const d=n.toISOString().replace(/[:-]|\.\d{3}/g,"");return{longDate:d,shortDate:d.slice(0,8)}})(d),z=((n,d,f)=>`${n}/${d}/${f}/${S}`)(le,f,O);return{accessKeyId:V,credentialScope:z,longDate:J,secretAccessKey:N,sessionToken:B,shortDate:le,signingRegion:f,signingService:O,uriEscapePath:j}})(d),{accessKeyId:O,credentialScope:j,longDate:V,sessionToken:N}=f,B={...n.headers};B.host=n.url.host,B[D]=V,N&&(B[P]=N);const J={...n,headers:B},le=((n,{credentialScope:d,longDate:f,secretAccessKey:O,shortDate:j,signingRegion:V,signingService:N,uriEscapePath:B})=>{const J=(({body:n,headers:d,method:f,url:O},j=!0)=>[f,X(O.pathname,j),U(O.searchParams),x(d),g(d),te(n)].join("\n"))(n,B),z=((n,d,f)=>[l,n,d,f].join("\n"))(f,d,b(null,J));return b(((n,d,f,O)=>{const V=se(`AWS4${n}`,d),N=se(V,f),B=se(N,O);return se(B,S)})(O,j,V,N),z)})(J,f),z=`Credential=${O}/${j}`,ne=`SignedHeaders=${g(B)}`;return B.authorization=`${l} ${z}, ${ne}, Signature=${le}`,J}},46570:(H,T,a)=>{a.d(T,{n:()=>p});var g=a(15861);const p=({userAgentHeader:M="x-amz-user-agent",userAgentValue:E=""})=>A=>function(){var I=(0,g.Z)(function*(R){if(0===E.trim().length)return yield A(R);{const y=M.toLowerCase();return R.headers[y]=R.headers[y]?`${R.headers[y]} ${E}`:E,yield A(R)}});return function h(R){return I.apply(this,arguments)}}()},54974:(H,T,a)=>{a.d(T,{e:()=>E,f:()=>M});var g=a(15861),p=a(97282);const M=function(){var A=(0,g.Z)(function*(I){if(!I||I.statusCode<300)return;const h=yield E(I),y=(D=>{const[P]=D.toString().split(/[,:]+/);return P.includes("#")?P.split("#")[1]:P})(I.headers["x-amzn-errortype"]??h.code??h.__type??"UnknownError"),C=new Error(h.message??h.Message??"Unknown error");return Object.assign(C,{name:y,$metadata:(0,p.B)(I)})});return function(h){return A.apply(this,arguments)}}(),E=function(){var A=(0,g.Z)(function*(I){if(!I.body)throw new Error("Missing response payload");const h=yield I.body.json();return Object.assign(h,{$metadata:(0,p.B)(I)})});return function(h){return A.apply(this,arguments)}}()},97282:(H,T,a)=>{a.d(T,{B:()=>g});const g=M=>{const{headers:E,statusCode:A}=M;return{...p(M)?M.$metadata:{},httpStatusCode:A,requestId:E["x-amzn-requestid"]??E["x-amzn-request-id"]??E["x-amz-request-id"],extendedRequestId:E["x-amz-id-2"],cfId:E["x-amz-cf-id"]}},p=M=>"object"==typeof M?.$metadata},92261:(H,T,a)=>{a.d(T,{Mt:()=>p,YG:()=>g,z2:()=>M});const g="Logging",p="x-amz-user-agent",M="NoHubcallbackProvidedException"},91935:(H,T,a)=>{a.d(T,{_:()=>g});class g extends Error{constructor({message:M,name:E,recoverySuggestion:A,underlyingError:I}){super(M),this.name=E,this.underlyingError=I,this.recoverySuggestion=A,this.constructor=g,Object.setPrototypeOf(this,g.prototype)}}},82847:(H,T,a)=>{a.d(T,{$:()=>p});var g=a(91935);const p=(M,E=g._)=>(A,I,h)=>{const{message:R,recoverySuggestion:y}=M[I];if(!A)throw new E({name:I,message:h?`${R} ${h}`:R,recoverySuggestion:y})}},70364:(H,T,a)=>{a.r(T),a.d(T,{Amplify:()=>D,AmplifyClassV6:()=>C,Cache:()=>rt,ConsoleLogger:()=>N.k,CookieStorage:()=>rn,Hub:()=>g.Xb,I18n:()=>be,ServiceWorker:()=>tn,clearCredentials:()=>l,decodeJWT:()=>p.xp,defaultStorage:()=>de,fetchAuthSession:()=>S,getCredentialsForIdentity:()=>d,getId:()=>o,sessionStorage:()=>Ue,sharedInMemoryStorage:()=>et,syncSessionStorage:()=>Le});var g=a(23192),p=a(74173);const M=u=>{const e=Reflect.ownKeys(u);for(const t of e){const r=u[t];(r&&"object"==typeof r||"function"==typeof r)&&M(r)}return Object.freeze(u)},E=Symbol("oauth-listener");a(83364);var I=a(52458),y=(a(86437),a(95472),a(15861));class L{configure(e,t){this.authConfig=e,this.authOptions=t}fetchAuthSession(e={}){var t=this;return(0,y.Z)(function*(){let r,s;const _=yield t.getTokens(e);return _?(s=_.accessToken?.payload?.sub,r=yield t.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({authConfig:t.authConfig,tokens:_,authenticated:!0,forceRefresh:e.forceRefresh})):r=yield t.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({authConfig:t.authConfig,authenticated:!1,forceRefresh:e.forceRefresh}),{tokens:_,credentials:r?.credentials,identityId:r?.identityId,userSub:s}})()}clearCredentials(){var e=this;return(0,y.Z)(function*(){yield e.authOptions?.credentialsProvider?.clearCredentialsAndIdentityId()})()}getTokens(e){var t=this;return(0,y.Z)(function*(){return(yield t.authOptions?.tokenProvider?.getTokens(e))??void 0})()}}class C{constructor(){this.oAuthListener=void 0,this.resourcesConfig={},this.libraryOptions={},this.Auth=new L}configure(e,t){const r=(0,I.h)(e);this.resourcesConfig=r,t&&(this.libraryOptions=t),this.resourcesConfig=M(this.resourcesConfig),this.Auth.configure(this.resourcesConfig.Auth,this.libraryOptions.Auth),g.Xb.dispatch("core",{event:"configure",data:this.resourcesConfig},"Configure",g.SQ),this.notifyOAuthListener()}getConfig(){return this.resourcesConfig}[E](e){this.resourcesConfig.Auth?.Cognito.loginWith?.oauth?e(this.resourcesConfig.Auth?.Cognito):this.oAuthListener=e}notifyOAuthListener(){!this.resourcesConfig.Auth?.Cognito.loginWith?.oauth||!this.oAuthListener||(this.oAuthListener(this.resourcesConfig.Auth?.Cognito),this.oAuthListener=void 0)}}const D=new C,S=u=>((u,e)=>u.Auth.fetchAuthSession(e))(D,u);function l(){return D.Auth.clearCredentials()}var m=a(97282),v=a(54974),k=a(62942),G=a(40454),Y=a(54473),q=a(79987),ee=a(67834),ce=a(99120),se=a(4079),b=a(5919),x=a(91396);const X=(0,se.V)(Y.y,[()=>u=>function(){var e=(0,y.Z)(function*(r){return r.headers["cache-control"]="no-store",u(r)});return function t(r){return e.apply(this,arguments)}}()]),te={service:"cognito-identity",endpointResolver:({region:u})=>({url:new ce.a(`https://cognito-identity.${u}.${(0,G.b)(u)}`)}),retryDecider:(0,ee.j)(v.f),computeDelay:q.k,userAgentValue:(0,b.Zm)(),cache:"no-store"};(0,x.QW)(()=>{te.userAgentValue=(0,b.Zm)()});const he=u=>({"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${u}`}),ie=({url:u},e,t)=>({headers:e,url:u,body:t,method:"POST"}),o=(0,k.z)(X,(u,e)=>{const t=he("GetId"),r=JSON.stringify(u);return ie(e,t,r)},function(){var u=(0,y.Z)(function*(e){if(e.statusCode>=300)throw yield(0,v.f)(e);return{IdentityId:(yield(0,v.e)(e)).IdentityId,$metadata:(0,m.B)(e)}});return function(t){return u.apply(this,arguments)}}(),te),c=function(){var u=(0,y.Z)(function*(e){if(e.statusCode>=300)throw yield(0,v.f)(e);{const t=yield(0,v.e)(e);return{IdentityId:t.IdentityId,Credentials:n(t.Credentials),$metadata:(0,m.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),n=({AccessKeyId:u,SecretKey:e,SessionToken:t,Expiration:r}={})=>({AccessKeyId:u,SecretKey:e,SessionToken:t,Expiration:r&&new Date(1e3*r)}),d=(0,k.z)(X,(u,e)=>{const t=he("GetCredentialsForIdentity"),r=JSON.stringify(u);return ie(e,t,r)},c,te);var f=a(87199),O=a(91935);class j extends O._{constructor(){super({name:f.Z.PlatformNotSupported,message:"Function not supported on current platform"})}}class V{constructor(e){this.storage=e}setItem(e,t){var r=this;return(0,y.Z)(function*(){if(!r.storage)throw new j;r.storage.setItem(e,t)})()}getItem(e){var t=this;return(0,y.Z)(function*(){if(!t.storage)throw new j;return t.storage.getItem(e)})()}removeItem(e){var t=this;return(0,y.Z)(function*(){if(!t.storage)throw new j;t.storage.removeItem(e)})()}clear(){var e=this;return(0,y.Z)(function*(){if(!e.storage)throw new j;e.storage.clear()})()}}var N=a(55502);class B{constructor(){this.storage=new Map}get length(){return this.storage.size}key(e){return e>this.length-1?null:Array.from(this.storage.keys())[e]}setItem(e,t){this.storage.set(e,t)}getItem(e){return this.storage.get(e)??null}removeItem(e){this.storage.delete(e)}clear(){this.storage.clear()}}const J=new N.k("CoreStorageUtils"),le=()=>{try{if(typeof window<"u"&&window.localStorage)return window.localStorage}catch{J.info("localStorage not found. InMemoryStorage is used as a fallback.")}return new B},z=()=>{try{if(typeof window<"u"&&window.sessionStorage)return window.sessionStorage.getItem("test"),window.sessionStorage;throw new Error("sessionStorage is not defined")}catch{return J.info("sessionStorage not found. InMemoryStorage is used as a fallback."),new B}};class ge{constructor(e){this._storage=e}get storage(){if(!this._storage)throw new j;return this._storage}setItem(e,t){this.storage.setItem(e,t)}getItem(e){return this.storage.getItem(e)}removeItem(e){this.storage.removeItem(e)}clear(){this.storage.clear()}}const de=new class ne extends V{constructor(){super(le())}},Ue=new class fe extends V{constructor(){super(z())}},Le=new class pe extends ge{constructor(){super(z())}},et=new V(new B),Ie={keyPrefix:"aws-amplify-cache",capacityInBytes:1048576,itemMaxSize:21e4,defaultTTL:2592e5,defaultPriority:5,warningThreshold:.8},Ce="CurSize";function $e(u){let e=0;e=u.length;for(let t=u.length;t>=0;t-=1){const r=u.charCodeAt(t);r>127&&r<=2047?e+=1:r>2047&&r<=65535&&(e+=2),r>=56320&&r<=57343&&(t-=1)}return e}function Ae(){return(new Date).getTime()}const Re=u=>`${u}${Ce}`;var Oe=a(82847),Q=(()=>{return(u=Q||(Q={})).NoCacheItem="NoCacheItem",u.NullNextNode="NullNextNode",u.NullPreviousNode="NullPreviousNode",Q;var u})();const Ge=(0,Oe.$)({[Q.NoCacheItem]:{message:"Item not found in the cache storage."},[Q.NullNextNode]:{message:"Next node is null."},[Q.NullPreviousNode]:{message:"Previous node is null."}}),re=new N.k("StorageCache");class Fe{constructor({config:e,keyValueStorage:t}){this.config={...Ie,...e},this.keyValueStorage=t,this.sanitizeConfig()}getModuleName(){return"Cache"}configure(e){return e&&(e.keyPrefix&&re.warn("keyPrefix can not be re-configured on an existing Cache instance."),this.config={...this.config,...e}),this.sanitizeConfig(),this.config}getCurrentCacheSize(){var e=this;return(0,y.Z)(function*(){let t=yield e.getStorage().getItem(Re(e.config.keyPrefix));return t||(yield e.getStorage().setItem(Re(e.config.keyPrefix),"0"),t="0"),Number(t)})()}setItem(e,t,r){var s=this;return(0,y.Z)(function*(){if(re.debug(`Set item: key is ${e}, value is ${t} with options: ${r}`),!e||e===Ce)return void re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`);if(typeof t>"u")return void re.warn("The value of item should not be undefined!");const _={priority:void 0!==r?.priority?r.priority:s.config.defaultPriority,expires:void 0!==r?.expires?r.expires:s.config.defaultTTL+Ae()};if(_.priority<1||_.priority>5)return void re.warn("Invalid parameter: priority due to out or range. It should be within 1 and 5.");const w=`${s.config.keyPrefix}${e}`,W=s.fillCacheItem(w,t,_);if(W.byteSize>s.config.itemMaxSize)re.warn(`Item with key: ${e} you are trying to put into is too big!`);else try{const K=yield s.getStorage().getItem(w);if(K&&(yield s.removeCacheItem(w,JSON.parse(K).byteSize)),yield s.isCacheFull(W.byteSize)){const oe=yield s.clearInvalidAndGetRemainingKeys();if(yield s.isCacheFull(W.byteSize)){const ae=yield s.sizeToPop(W.byteSize);yield s.popOutItems(oe,ae)}}return s.setCacheItem(w,W)}catch(K){re.warn(`setItem failed! ${K}`)}})()}getItem(e,t){var r=this;return(0,y.Z)(function*(){let s;if(re.debug(`Get item: key is ${e} with options ${t}`),!e||e===Ce)return re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`),null;const _=`${r.config.keyPrefix}${e}`;try{if(s=yield r.getStorage().getItem(_),null!=s){if(!(yield r.isExpired(_)))return(yield r.updateVisitedTime(JSON.parse(s),_)).data;yield r.removeCacheItem(_,JSON.parse(s).byteSize)}if(t?.callback){const w=t.callback();return null!==w&&(yield r.setItem(e,w,t)),w}return null}catch(w){return re.warn(`getItem failed! ${w}`),null}})()}removeItem(e){var t=this;return(0,y.Z)(function*(){if(re.debug(`Remove item: key is ${e}`),!e||e===Ce)return void re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`);const r=`${t.config.keyPrefix}${e}`;try{const s=yield t.getStorage().getItem(r);s&&(yield t.removeCacheItem(r,JSON.parse(s).byteSize))}catch(s){re.warn(`removeItem failed! ${s}`)}})()}getAllKeys(){var e=this;return(0,y.Z)(function*(){try{return yield e.getAllCacheKeys()}catch(t){return re.warn(`getAllkeys failed! ${t}`),[]}})()}getStorage(){return this.keyValueStorage}isExpired(e){var t=this;return(0,y.Z)(function*(){const r=yield t.getStorage().getItem(e);Ge(null!==r,Q.NoCacheItem,`Key: ${e}`);const s=JSON.parse(r);return Ae()>=s.expires})()}removeCacheItem(e,t){var r=this;return(0,y.Z)(function*(){const s=yield r.getStorage().getItem(e);Ge(null!==s,Q.NoCacheItem,`Key: ${e}`);const _=t??JSON.parse(s).byteSize;yield r.decreaseCurrentSizeInBytes(_);try{yield r.getStorage().removeItem(e)}catch(w){yield r.increaseCurrentSizeInBytes(_),re.error(`Failed to remove item: ${w}`)}})()}fillCacheItem(e,t,r){const s={key:e,data:t,timestamp:Ae(),visitedTime:Ae(),priority:r.priority??0,expires:r.expires??0,type:typeof t,byteSize:0};return s.byteSize=$e(JSON.stringify(s)),s.byteSize=$e(JSON.stringify(s)),s}sanitizeConfig(){this.config.itemMaxSize>this.config.capacityInBytes&&(re.error("Invalid parameter: itemMaxSize. It should be smaller than capacityInBytes. Setting back to default."),this.config.itemMaxSize=Ie.itemMaxSize),(this.config.defaultPriority>5||this.config.defaultPriority<1)&&(re.error("Invalid parameter: defaultPriority. It should be between 1 and 5. Setting back to default."),this.config.defaultPriority=Ie.defaultPriority),(Number(this.config.warningThreshold)>1||Number(this.config.warningThreshold)<0)&&(re.error("Invalid parameter: warningThreshold. It should be between 0 and 1. Setting back to default."),this.config.warningThreshold=Ie.warningThreshold),this.config.capacityInBytes>5242880&&(re.error("Cache Capacity should be less than 5MB. Setting back to default. Setting back to default."),this.config.capacityInBytes=Ie.capacityInBytes)}increaseCurrentSizeInBytes(e){var t=this;return(0,y.Z)(function*(){const r=yield t.getCurrentCacheSize();yield t.getStorage().setItem(Re(t.config.keyPrefix),(r+e).toString())})()}decreaseCurrentSizeInBytes(e){var t=this;return(0,y.Z)(function*(){const r=yield t.getCurrentCacheSize();yield t.getStorage().setItem(Re(t.config.keyPrefix),(r-e).toString())})()}updateVisitedTime(e,t){var r=this;return(0,y.Z)(function*(){return e.visitedTime=Ae(),yield r.getStorage().setItem(t,JSON.stringify(e)),e})()}setCacheItem(e,t){var r=this;return(0,y.Z)(function*(){yield r.increaseCurrentSizeInBytes(t.byteSize);try{yield r.getStorage().setItem(e,JSON.stringify(t))}catch(s){yield r.decreaseCurrentSizeInBytes(t.byteSize),re.error(`Failed to set item ${s}`)}})()}sizeToPop(e){var t=this;return(0,y.Z)(function*(){const s=(yield t.getCurrentCacheSize())+e-t.config.capacityInBytes,_=(1-t.config.warningThreshold)*t.config.capacityInBytes;return s>_?s:_})()}isCacheFull(e){var t=this;return(0,y.Z)(function*(){const r=yield t.getCurrentCacheSize();return e+r>t.config.capacityInBytes})()}popOutItems(e,t){var r=this;return(0,y.Z)(function*(){const s=[];let _=t;for(const w of e){const W=yield r.getStorage().getItem(w);if(null!=W){const K=JSON.parse(W);s.push(K)}}s.sort((w,W)=>w.priority>W.priority?-1:w.priority<W.priority?1:w.visitedTime<W.visitedTime?-1:1);for(const w of s)if(yield r.removeCacheItem(w.key,w.byteSize),_-=w.byteSize,_<=0)return})()}clearInvalidAndGetRemainingKeys(){var e=this;return(0,y.Z)(function*(){const t=[],r=yield e.getAllCacheKeys({omitSizeKey:!0});for(const s of r)(yield e.isExpired(s))?yield e.removeCacheItem(s):t.push(s);return t})()}clear(){var e=this;return(0,y.Z)(function*(){re.debug("Clear Cache");try{const t=yield e.getAllKeys();for(const r of t){const s=`${e.config.keyPrefix}${r}`;yield e.getStorage().removeItem(s)}}catch(t){re.warn(`clear failed! ${t}`)}})()}}const He=new N.k("StorageCache");class nt extends Fe{constructor(e){const t=le();super({config:e,keyValueStorage:new V(t)}),this.storage=t,this.getItem=this.getItem.bind(this),this.setItem=this.setItem.bind(this),this.removeItem=this.removeItem.bind(this)}getAllCacheKeys(e){var t=this;return(0,y.Z)(function*(){const{omitSizeKey:r}=e??{},s=[];for(let _=0;_<t.storage.length;_++){const w=t.storage.key(_);r&&w===Re(t.config.keyPrefix)||w?.startsWith(t.config.keyPrefix)&&s.push(w.substring(t.config.keyPrefix.length))}return s})()}createInstance(e){return(!e.keyPrefix||e.keyPrefix===Ie.keyPrefix)&&(He.error("invalid keyPrefix, setting keyPrefix with timeStamp"),e.keyPrefix=Ae.toString()),new nt(e)}}const rt=new nt,vt=new N.k("I18n");var Pe=(()=>((Pe||(Pe={})).NotConfigured="NotConfigured",Pe))();const Ze=(0,Oe.$)({[Pe.NotConfigured]:{message:"i18n is not configured."}}),dt=new N.k("I18n");let Ke={language:null},ye=null;class be{static configure(e){return dt.debug("configure I18n"),e&&(Ke=Object.assign({},Ke,e.I18n||e),be.createInstance()),Ke}static getModuleName(){return"I18n"}static createInstance(){dt.debug("create I18n instance"),!ye&&(ye=new class{constructor(){this._options=null,this._lang=null,this._dict={}}setDefaultLanguage(){!this._lang&&typeof window<"u"&&window&&window.navigator&&(this._lang=window.navigator.language),vt.debug(this._lang)}setLanguage(e){this._lang=e}get(e,t){if(this.setDefaultLanguage(),!this._lang)return typeof t<"u"?t:e;const r=this._lang;let s=this.getByLanguage(e,r);return s||(r.indexOf("-")>0&&(s=this.getByLanguage(e,r.split("-")[0])),s)?s:typeof t<"u"?t:e}getByLanguage(e,t,r=null){if(!t)return r;const s=this._dict[t];return s?s[e]:r}putVocabulariesForLanguage(e,t){let r=this._dict[e];r||(r=this._dict[e]={}),this._dict[e]={...r,...t}}putVocabularies(e){Object.keys(e).forEach(t=>{this.putVocabulariesForLanguage(t,e[t])})}})}static setLanguage(e){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.setLanguage(e)}static get(e,t){return be.checkConfig()?(Ze(!!ye,Pe.NotConfigured),ye.get(e,t)):typeof t>"u"?e:t}static putVocabulariesForLanguage(e,t){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.putVocabulariesForLanguage(e,t)}static putVocabularies(e){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.putVocabularies(e)}static checkConfig(){return ye||be.createInstance(),!0}}be.createInstance();var bt=a(96594),Ve=a(38261);const wt=(0,Oe.$)({[f.Z.NoEndpointId]:{message:"Endpoint ID was not found and was unable to be created."},[f.Z.PlatformNotSupported]:{message:"Function not supported on current platform."},[f.Z.Unknown]:{message:"An unknown error occurred."},[f.Z.NetworkError]:{message:"A network error has occurred."}}),lt=new N.k("getClientInfo");var ft=a(6639);const ot=u=>encodeURIComponent(u).replace(/[!'()*]/g,Rt),Rt=u=>`%${u.charCodeAt(0).toString(16).toUpperCase()}`,ht={service:"mobiletargeting",endpointResolver:({region:u})=>({url:new ce.a(`https://pinpoint.${u}.${(0,G.b)(u)}`)}),retryDecider:(0,ee.j)(v.f),computeDelay:q.k,userAgentValue:(0,b.Zm)()},Dt=(0,k.z)(ft.Z,({ApplicationId:u="",EndpointId:e="",EndpointRequest:t},r)=>{const _=new ce.a(r.url);return _.pathname=`v1/apps/${ot(u)}/endpoints/${ot(e)}`,{method:"PUT",headers:{"content-type":"application/json"},url:_,body:JSON.stringify(t)}},function(){var u=(0,y.Z)(function*(e){if(e.statusCode>=300)throw yield(0,v.f)(e);{const{Message:t,RequestID:r}=yield(0,v.e)(e);return{MessageBody:{Message:t,RequestID:r},$metadata:(0,m.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),ht),Qe=(u,e)=>`${e}:pinpoint:${u}`,Nt=function(){var u=(0,y.Z)(function*(e,t,r){const s=Qe(e,t),w=(new Date).getTime()+31536e8;return rt.setItem(s,r,{expires:w,priority:1})});return function(t,r,s){return u.apply(this,arguments)}}(),Je={},it=function(){var u=(0,y.Z)(function*(e,t){const r=Qe(e,t);return(yield rt.getItem(r))??void 0});return function(t,r){return u.apply(this,arguments)}}(),kt=function(){var u=(0,y.Z)(function*({address:e,appId:t,category:r,channelType:s,credentials:_,identityId:w,optOut:W,region:K,userAttributes:oe,userId:ae,userProfile:me,userAgentValue:De}){const Ee=yield it(t,r),ve=Ee?void 0:((u,e)=>{const t=Qe(u,e);return Je[t]||(Je[t]=(0,Ve.r)()),Je[t]})(t,r),{customProperties:Me,demographic:qe,email:We,location:we,metrics:yt,name:ct,plan:ut}=me??{},Ne={},mt=ve?ae??w:ae;if(ve){const ze=function It(){return typeof window>"u"?{}:function Ct(){if(typeof window>"u")return lt.warn("No window object available to get browser client info"),{};const u=window.navigator;if(!u)return lt.warn("No navigator object available to get browser client info"),{};const{platform:e,product:t,vendor:r,userAgent:s,language:_}=u,w=function xt(u){const e=/.+(Opera[\s[A-Z]*|OPR[\sA-Z]*)\/([0-9.]+).*/i.exec(u);if(e)return{type:e[1],version:e[2]};const t=/.+(Trident|Edge|Edg|EdgA|EdgiOS)\/([0-9.]+).*/i.exec(u);if(t)return{type:t[1],version:t[2]};const r=/.+(Chrome|CriOS|Firefox|FxiOS)\/([0-9.]+).*/i.exec(u);if(r)return{type:r[1],version:r[2]};const s=/.+(Safari)\/([0-9.]+).*/i.exec(u);if(s)return{type:s[1],version:s[2]};const _=/.+(AppleWebKit)\/([0-9.]+).*/i.exec(u);if(_)return{type:_[1],version:_[2]};const w=/.*([A-Z]+)\/([0-9.]+).*/i.exec(u);return w?{type:w[1],version:w[2]}:{type:"",version:""}}(s),W=function Pt(){const u=/\(([A-Za-z\s].*)\)/.exec((new Date).toString());return u&&u[1]||""}();return{platform:e,make:t||r,model:w.type,version:w.version,appVersion:[w.type,w.version].join("/"),language:_,timezone:W}}()}();Ne.appVersion=ze.appVersion,Ne.make=ze.make,Ne.model=ze.model,Ne.modelVersion=ze.version,Ne.platform=ze.platform}const xe={...Ne,...qe},on={...We&&{email:[We]},...ct&&{name:[ct]},...ut&&{plan:[ut]},...Me},sn=ve||qe,an=We||Me||ct||ut,cn=mt||oe,un={ApplicationId:t,EndpointId:Ee??ve,EndpointRequest:{RequestId:(0,Ve.r)(),EffectiveDate:(new Date).toISOString(),ChannelType:s,Address:e,...an&&{Attributes:on},...sn&&{Demographic:{AppVersion:xe.appVersion,Locale:xe.locale,Make:xe.make,Model:xe.model,ModelVersion:xe.modelVersion,Platform:xe.platform,PlatformVersion:xe.platformVersion,Timezone:xe.timezone}},...we&&{Location:{City:we.city,Country:we.country,Latitude:we.latitude,Longitude:we.longitude,PostalCode:we.postalCode,Region:we.region}},Metrics:yt,OptOut:W,...cn&&{User:{UserId:mt,UserAttributes:oe}}}};try{yield Dt({credentials:_,region:K,userAgentValue:De},un),ve&&(yield Nt(t,r,ve))}finally{ve&&((u,e)=>{const t=Qe(u,e);delete Je[t]})(t,r)}});return function(t){return u.apply(this,arguments)}}(),jt=function(){var u=(0,y.Z)(function*({address:e,appId:t,category:r,channelType:s,credentials:_,identityId:w,region:W,userAgentValue:K}){let oe=yield it(t,r);return oe||(yield kt({address:e,appId:t,category:r,channelType:s,credentials:_,identityId:w,region:W,userAgentValue:K}),oe=yield it(t,r)),wt(!!oe,f.Z.NoEndpointId),oe});return function(t){return u.apply(this,arguments)}}();var ke=(()=>((ke||(ke={})).NoAppId="NoAppId",ke))();const Gt=(0,Oe.$)({[ke.NoAppId]:{message:"Missing application id."}}),Zt=(0,k.z)(ft.Z,({ApplicationId:u,EventsRequest:e},t)=>{Gt(!!u,ke.NoAppId);const s=new ce.a(t.url);return s.pathname=`v1/apps/${ot(u)}/events`,{method:"POST",headers:{"content-type":"application/json"},url:s,body:JSON.stringify(e??{})}},function(){var u=(0,y.Z)(function*(e){if(e.statusCode>=300)throw yield(0,v.f)(e);{const{Results:t}=yield(0,v.e)(e);return{EventsResponse:{Results:t},$metadata:(0,m.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),ht),je=new N.k("PinpointEventBuffer"),_t=[429,500],Vt=[202];class Qt{constructor(e){this._interval=void 0,this._pause=!1,this._flush=!1,this._buffer=[],this._config=e,this._sendBatch=this._sendBatch.bind(this),this._startLoop()}push(e){this._buffer.length>=this._config.bufferSize?je.debug("Exceeded Pinpoint event buffer limits, event dropped.",{eventId:e.eventId}):this._buffer.push({[e.eventId]:e})}pause(){this._pause=!0}resume(){this._pause=!1}flush(){this._flush=!0}identityHasChanged(e){return this._config.identityId!==e}flushAll(){this._putEvents(this._buffer.splice(0,this._buffer.length))}_startLoop(){this._interval&&clearInterval(this._interval);const{flushInterval:e}=this._config;this._interval=setInterval(this._sendBatch,e)}_sendBatch(){const e=this._buffer.length;if(this._flush&&!e&&this._interval&&clearInterval(this._interval),this._pause||!e)return;const{flushSize:t}=this._config,r=Math.min(t,e),s=this._buffer.splice(0,r);this._putEvents(s)}_putEvents(e){var t=this;return(0,y.Z)(function*(){const r=t._bufferToMap(e),s=t._generateBatchEventParams(r);try{const{credentials:_,region:w,userAgentValue:W}=t._config,K=yield Zt({credentials:_,region:w,userAgentValue:W},s);t._processPutEventsSuccessResponse(K,r)}catch(_){t._handlePutEventsFailure(_,r)}})()}_generateBatchEventParams(e){const t={};return Object.values(e).forEach(r=>{const{event:s,timestamp:_,endpointId:w,eventId:W,session:K}=r,{name:oe,attributes:ae,metrics:me}=s;t[w]={Endpoint:{...t[w]?.Endpoint},Events:{...t[w]?.Events,[W]:{EventType:oe,Timestamp:new Date(_).toISOString(),Attributes:ae,Metrics:me,Session:K}}}}),{ApplicationId:this._config.appId,EventsRequest:{BatchItem:t}}}_handlePutEventsFailure(e,t){if(je.debug("putEvents call to Pinpoint failed.",e),_t.includes(e.$metadata&&e.$metadata.httpStatusCode)){const s=Object.values(t);this._retry(s)}}_processPutEventsSuccessResponse(e,t){const{Results:r={}}=e.EventsResponse??{},s=[];Object.entries(r).forEach(([_,w])=>{Object.entries(w.EventsItemResponse??{}).forEach(([K,oe])=>{const ae=t[K];if(!ae)return;const{StatusCode:me,Message:De}=oe??{};if(me&&Vt.includes(me))return;if(me&&_t.includes(me))return void s.push(ae);const{name:Ee}=ae.event;je.warn("Pinpoint event failed to send.",{eventId:K,name:Ee,message:De})})}),s.length&&this._retry(s)}_retry(e){const t=[];e.forEach(r=>{const{eventId:s}=r,{name:_}=r.event;if(r.resendLimit-- >0)return je.debug("Resending event.",{eventId:s,name:_,remainingAttempts:r.resendLimit}),void t.push({[s]:r});je.debug("No retry attempts remaining for event.",{eventId:s,name:_})}),this._buffer.unshift(...t)}_bufferToMap(e){return e.reduce((t,r)=>{const[[s,_]]=Object.entries(r);return t[s]=_,t},{})}}const Be={};let Te;const qt=function(){var u=(0,y.Z)(function*({appId:e,category:t,channelType:r,credentials:s,event:_,identityId:w,region:W,userAgentValue:K,bufferSize:oe,flushInterval:ae,flushSize:me,resendLimit:De}){let Ee=Te;const ve=new Date,Me=ve.toISOString(),qe=(0,Ve.r)(),We=(({appId:u,region:e,credentials:t,bufferSize:r,flushInterval:s,flushSize:_,resendLimit:w,identityId:W,userAgentValue:K})=>{if(Be[e]?.[u]){const ae=Be[e][u];if(!ae.identityHasChanged(W))return ae;ae.flush()}const oe=new Qt({appId:u,bufferSize:r,credentials:t,flushInterval:s,flushSize:_,identityId:W,region:e,resendLimit:w,userAgentValue:K});return Be[e]||(Be[e]={}),Be[e][u]=oe,oe})({appId:e,region:W,credentials:s,bufferSize:oe??1e3,flushInterval:ae??5e3,flushSize:me??100,resendLimit:De??5,identityId:w,userAgentValue:K}),we=yield jt({appId:e,category:t,channelType:r,credentials:s,identityId:w,region:W,userAgentValue:K});(!Ee||"_session.start"===_.name)&&(Te={Id:(0,Ve.r)(),StartTimestamp:Me},Ee=Te),Te&&"_session.stop"===_.name&&(Ee={...Te,StopTimestamp:Me,Duration:ve.getTime()-new Date(Te.StartTimestamp).getTime()},Te=void 0),We.push({eventId:qe,endpointId:we,event:_,session:Ee,timestamp:Me,resendLimit:De??5})});return function(t){return u.apply(this,arguments)}}();var _e=(()=>{return(u=_e||(_e={})).UndefinedInstance="UndefinedInstance",u.UndefinedRegistration="UndefinedRegistration",u.Unavailable="Unavailable",_e;var u})();const st=(0,Oe.$)({[_e.UndefinedInstance]:{message:"Service Worker instance is undefined."},[_e.UndefinedRegistration]:{message:"Service Worker registration is undefined."},[_e.Unavailable]:{message:"Service Worker not available."}});class tn{constructor(){this._logger=new N.k("ServiceWorker")}get serviceWorker(){return st(void 0!==this._serviceWorker,_e.UndefinedInstance),this._serviceWorker}register(e="/service-worker.js",t="/"){return this._logger.debug(`registering ${e}`),this._logger.debug(`registering service worker with scope ${t}`),new Promise((r,s)=>{navigator&&"serviceWorker"in navigator?navigator.serviceWorker.register(e,{scope:t}).then(_=>{_.installing?this._serviceWorker=_.installing:_.waiting?this._serviceWorker=_.waiting:_.active&&(this._serviceWorker=_.active),this._registration=_,this._setupListeners(),this._logger.debug(`Service Worker Registration Success: ${_}`),r(_)}).catch(_=>{this._logger.debug(`Service Worker Registration Failed ${_}`),s(new O._({name:_e.Unavailable,message:"Service Worker not available",underlyingError:_}))}):s(new O._({name:_e.Unavailable,message:"Service Worker not available"}))})}enablePush(e){return st(void 0!==this._registration,_e.UndefinedRegistration),this._publicKey=e,new Promise((t,r)=>{(0,bt.j)()?(st(void 0!==this._registration,_e.UndefinedRegistration),this._registration.pushManager.getSubscription().then(s=>{if(!s)return this._logger.debug("User is NOT subscribed to push"),this._registration.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this._urlB64ToUint8Array(e)}).then(_=>{this._subscription=_,this._logger.debug(`User subscribed: ${JSON.stringify(_)}`),t(_)}).catch(_=>{this._logger.error(_)});this._subscription=s,this._logger.debug(`User is subscribed to push: ${JSON.stringify(s)}`),t(s)})):r(new O._({name:_e.Unavailable,message:"Service Worker not available"}))})}_urlB64ToUint8Array(e){const r=(e+"=".repeat((4-e.length%4)%4)).replace(/-/g,"+").replace(/_/g,"/"),s=window.atob(r),_=new Uint8Array(s.length);for(let w=0;w<s.length;++w)_[w]=s.charCodeAt(w);return _}send(e){this._serviceWorker&&this._serviceWorker.postMessage("object"==typeof e?JSON.stringify(e):e)}_setupListeners(){var e=this;this.serviceWorker.addEventListener("statechange",(0,y.Z)(function*(){const t=e.serviceWorker.state;e._logger.debug(`ServiceWorker statechange: ${t}`);const{appId:r,region:s,bufferSize:_,flushInterval:w,flushSize:W,resendLimit:K}=D.getConfig().Analytics?.Pinpoint??{},{credentials:oe}=yield S();r&&s&&oe&&qt({appId:r,region:s,category:"Core",credentials:oe,bufferSize:_,flushInterval:w,flushSize:W,resendLimit:K,event:{name:"ServiceWorker",attributes:{state:t}}})})),this.serviceWorker.addEventListener("message",t=>{this._logger.debug(`ServiceWorker message event: ${t}`)})}}function Ye(u){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)u[r]=t[r]}return u}var Xe=function at(u,e){function t(s,_,w){if(!(typeof document>"u")){"number"==typeof(w=Ye({},e,w)).expires&&(w.expires=new Date(Date.now()+864e5*w.expires)),w.expires&&(w.expires=w.expires.toUTCString()),s=encodeURIComponent(s).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var W="";for(var K in w)w[K]&&(W+="; "+K,!0!==w[K]&&(W+="="+w[K].split(";")[0]));return document.cookie=s+"="+u.write(_,s)+W}}return Object.create({set:t,get:function r(s){if(!(typeof document>"u"||arguments.length&&!s)){for(var _=document.cookie?document.cookie.split("; "):[],w={},W=0;W<_.length;W++){var K=_[W].split("="),oe=K.slice(1).join("=");try{var ae=decodeURIComponent(K[0]);if(w[ae]=u.read(oe,ae),s===ae)break}catch{}}return s?w[s]:w}},remove:function(s,_){t(s,"",Ye({},_,{expires:-1}))},withAttributes:function(s){return at(this.converter,Ye({},this.attributes,s))},withConverter:function(s){return at(Ye({},this.converter,s),this.attributes)}},{attributes:{value:Object.freeze(e)},converter:{value:Object.freeze(u)}})}({read:function(u){return'"'===u[0]&&(u=u.slice(1,-1)),u.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(u){return encodeURIComponent(u).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});class rn{constructor(e={}){const{path:t,domain:r,expires:s,sameSite:_,secure:w}=e;if(this.domain=r,this.path=t||"/",this.expires=Object.prototype.hasOwnProperty.call(e,"expires")?s:365,this.secure=!Object.prototype.hasOwnProperty.call(e,"secure")||w,Object.prototype.hasOwnProperty.call(e,"sameSite")){if(!_||!["strict","lax","none"].includes(_))throw new Error('The sameSite value of cookieStorage must be "lax", "strict" or "none".');if("none"===_&&!this.secure)throw new Error("sameSite = None requires the Secure attribute in latest browser versions.");this.sameSite=_}}setItem(e,t){var r=this;return(0,y.Z)(function*(){Xe.set(e,t,r.getData())})()}getItem(e){return(0,y.Z)(function*(){return Xe.get(e)??null})()}removeItem(e){var t=this;return(0,y.Z)(function*(){Xe.remove(e,t.getData())})()}clear(){var e=this;return(0,y.Z)(function*(){const t=Xe.get(),r=Object.keys(t).map(s=>e.removeItem(s));yield Promise.all(r)})()}getData(){return{path:this.path,expires:this.expires,domain:this.domain,secure:this.secure,...this.sameSite&&{sameSite:this.sameSite}}}}},74173:(H,T,a)=>{a.d(T,{YE:()=>y,FG:()=>h,xp:()=>L});var g=a(10180);const p={convert(C,D){let P=C;return D?.urlSafe&&(P=P.replace(/-/g,"+").replace(/_/g,"/")),(0,g.tl)()(P)}};var M=a(82847),E=(()=>{return(C=E||(E={})).AuthTokenConfigException="AuthTokenConfigException",C.AuthUserPoolAndIdentityPoolException="AuthUserPoolAndIdentityPoolException",C.AuthUserPoolException="AuthUserPoolException",C.InvalidIdentityPoolIdException="InvalidIdentityPoolIdException",C.OAuthNotConfigureException="OAuthNotConfigureException",E;var C})();const I=(0,M.$)({[E.AuthTokenConfigException]:{message:"Auth Token Provider not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app."},[E.AuthUserPoolAndIdentityPoolException]:{message:"Auth UserPool or IdentityPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with UserPoolId and IdentityPoolId."},[E.AuthUserPoolException]:{message:"Auth UserPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with userPoolId and userPoolClientId."},[E.InvalidIdentityPoolIdException]:{message:"Invalid identity pool id provided.",recoverySuggestion:"Make sure a valid identityPoolId is given in the config."},[E.OAuthNotConfigureException]:{message:"oauth param not configured.",recoverySuggestion:"Make sure to call Amplify.configure with oauth parameter in your app."}});function h(C){let D=!0;D=!!C&&!!C.userPoolId&&!!C.userPoolClientId,I(D,E.AuthUserPoolException)}function y(C){I(!!C?.identityPoolId,E.InvalidIdentityPoolIdException)}function L(C){const D=C.split(".");if(3!==D.length)throw new Error("Invalid token");try{const S=D[1].replace(/-/g,"+").replace(/_/g,"/"),l=decodeURIComponent(p.convert(S).split("").map(v=>`%${`00${v.charCodeAt(0).toString(16)}`.slice(-2)}`).join(""));return{toString:()=>C,payload:JSON.parse(l)}}catch{throw new Error("Invalid token payload")}}},87199:(H,T,a)=>{a.d(T,{Z:()=>g});var g=(()=>{return(p=g||(g={})).NoEndpointId="NoEndpointId",p.PlatformNotSupported="PlatformNotSupported",p.Unknown="Unknown",p.NetworkError="NetworkError",g;var p})()},99120:(H,T,a)=>{a.d(T,{a:()=>g,z:()=>p});const g=URL,p=URLSearchParams},38261:(H,T,a)=>{a.d(T,{r:()=>p});const p=a(83364).v4},10180:(H,T,a)=>{a.d(T,{Ds:()=>M,tl:()=>E});var g=a(91935);const M=()=>{if(typeof window<"u"&&"function"==typeof window.btoa)return window.btoa;if("function"==typeof btoa)return btoa;throw new g._({name:"Base64EncoderError",message:"Cannot resolve the `btoa` function from the environment."})},E=()=>{if(typeof window<"u"&&"function"==typeof window.atob)return window.atob;if("function"==typeof atob)return atob;throw new g._({name:"Base64EncoderError",message:"Cannot resolve the `atob` function from the environment."})}},96594:(H,T,a)=>{a.d(T,{j:()=>g});const g=()=>typeof window<"u"&&typeof window.document<"u"},52458:(H,T,a)=>{a.d(T,{h:()=>se});var g=a(55502),p=a(91935);const M=new g.k("parseAWSExports"),E={API_KEY:"apiKey",AWS_IAM:"iam",AMAZON_COGNITO_USER_POOLS:"userPool",OPENID_CONNECT:"oidc",NONE:"none",AWS_LAMBDA:"lambda",LAMBDA:"lambda"},I=b=>b?.split(",")??[],h=({domain:b,scope:x,redirectSignIn:U,redirectSignOut:$,responseType:F})=>({domain:b,scopes:x,redirectSignIn:I(U),redirectSignOut:I($),responseType:F}),R=b=>b.map(x=>{const U=x.toLowerCase();return U.charAt(0).toUpperCase()+U.slice(1)});const k={AMAZON_COGNITO_USER_POOLS:"userPool",API_KEY:"apiKey",AWS_IAM:"iam",AWS_LAMBDA:"lambda",OPENID_CONNECT:"oidc"};function G(b){return k[b]}const Y={GOOGLE:"Google",LOGIN_WITH_AMAZON:"Amazon",FACEBOOK:"Facebook",SIGN_IN_WITH_APPLE:"Apple"};function q(b=[]){return b.reduce((x,U)=>(void 0!==Y[U]&&x.push(Y[U]),x),[])}function ee(b){return"OPTIONAL"===b?"optional":"REQUIRED"===b?"on":"off"}function ce(b){const x={};return b.forEach(({name:U,bucket_name:$,aws_region:F,paths:X})=>{if(U in x)throw new Error(`Duplicate friendly name found: ${U}. Name must be unique.`);const te=X?Object.entries(X).reduce((he,[ie,ue])=>(void 0!==ue&&(he[ie]=ue),he),{}):void 0;x[U]={bucketName:$,region:F,paths:te}}),x}const se=b=>Object.keys(b).some(x=>x.startsWith("aws_"))?((b={})=>{if(!Object.prototype.hasOwnProperty.call(b,"aws_project_region"))throw new p._({name:"InvalidParameterException",message:"Invalid config parameter.",recoverySuggestion:"Ensure passing the config object imported from  `amplifyconfiguration.json`."});const{aws_appsync_apiKey:x,aws_appsync_authenticationType:U,aws_appsync_graphqlEndpoint:$,aws_appsync_region:F,aws_bots_config:X,aws_cognito_identity_pool_id:te,aws_cognito_sign_up_verification_method:he,aws_cognito_mfa_configuration:ie,aws_cognito_mfa_types:ue,aws_cognito_password_protection_settings:Z,aws_cognito_verification_mechanisms:o,aws_cognito_signup_attributes:i,aws_cognito_social_providers:c,aws_cognito_username_attributes:n,aws_mandatory_sign_in:d,aws_mobile_analytics_app_id:f,aws_mobile_analytics_app_region:O,aws_user_files_s3_bucket:j,aws_user_files_s3_bucket_region:V,aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing:N,aws_user_pools_id:B,aws_user_pools_web_client_id:J,geo:le,oauth:z,predictions:ne,aws_cloud_logic_custom:fe,Notifications:ge,modelIntrospection:pe}=b,de={};f&&(de.Analytics={Pinpoint:{appId:f,region:O}});const{InAppMessaging:Ue,Push:Le}=ge??{};if(Ue?.AWSPinpoint||Le?.AWSPinpoint){if(Ue?.AWSPinpoint){const{appId:Q,region:Se}=Ue.AWSPinpoint;de.Notifications={InAppMessaging:{Pinpoint:{appId:Q,region:Se}}}}if(Le?.AWSPinpoint){const{appId:Q,region:Se}=Le.AWSPinpoint;de.Notifications={...de.Notifications,PushNotification:{Pinpoint:{appId:Q,region:Se}}}}}if(Array.isArray(X)&&(de.Interactions={LexV1:Object.fromEntries(X.map(Q=>[Q.name,Q]))}),$){const Q=E[U];Q||M.debug(`Invalid authentication type ${U}. Falling back to IAM.`),de.API={GraphQL:{endpoint:$,apiKey:x,region:F,defaultAuthMode:Q??"iam"}},pe&&(de.API.GraphQL.modelIntrospection=pe)}const et=ie?{status:ie&&ie.toLowerCase(),totpEnabled:ue?.includes("TOTP")??!1,smsEnabled:ue?.includes("SMS")??!1}:void 0,Ie=Z?{minLength:Z.passwordPolicyMinLength,requireLowercase:Z.passwordPolicyCharacters?.includes("REQUIRES_LOWERCASE")??!1,requireUppercase:Z.passwordPolicyCharacters?.includes("REQUIRES_UPPERCASE")??!1,requireNumbers:Z.passwordPolicyCharacters?.includes("REQUIRES_NUMBERS")??!1,requireSpecialCharacters:Z.passwordPolicyCharacters?.includes("REQUIRES_SYMBOLS")??!1}:void 0,$e=Array.from(new Set([...o??[],...i??[]])).reduce((Q,Se)=>({...Q,[Se.toLowerCase()]:{required:!0}}),{}),Ae=n?.includes("EMAIL")??!1,tt=n?.includes("PHONE_NUMBER")??!1;(te||B)&&(de.Auth={Cognito:{identityPoolId:te,allowGuestAccess:"enable"!==d,signUpVerificationMethod:he,userAttributes:$e,userPoolClientId:J,userPoolId:B,mfa:et,passwordFormat:Ie,loginWith:{username:!(Ae||tt),email:Ae,phone:tt}}});const Re=!!z&&Object.keys(z).length>0,Oe=!!c&&c.length>0;if(de.Auth&&Re&&(de.Auth.Cognito.loginWith={...de.Auth.Cognito.loginWith,oauth:{...h(z),...Oe&&{providers:R(c)}}}),j&&(de.Storage={S3:{bucket:j,region:V,dangerouslyConnectToHttpEndpointForTesting:N}}),le){const{amazon_location_service:Q}=le;de.Geo={LocationService:{maps:Q.maps,geofenceCollections:Q.geofenceCollections,searchIndices:Q.search_indices,region:Q.region}}}if(fe&&(de.API={...de.API,REST:fe.reduce((Q,Se)=>{const{name:Ge,endpoint:re,region:Fe,service:He}=Se;return{...Q,[Ge]:{endpoint:re,...He?{service:He}:void 0,...Fe?{region:Fe}:void 0}}},{})}),ne){const{VoiceId:Q}=ne?.convert?.speechGenerator?.defaults??{};de.Predictions=Q?{...ne,convert:{...ne.convert,speechGenerator:{...ne.convert.speechGenerator,defaults:{voiceId:Q}}}}:ne}return de})(b):function y(b){const{version:x}=b;return!!x&&x.startsWith("1")}(b)?function v(b){const x={};if(b.storage&&(x.Storage=function L(b){if(!b)return;const{bucket_name:x,aws_region:U,buckets:$}=b;return{S3:{bucket:x,region:U,buckets:$&&ce($)}}}(b.storage)),b.auth&&(x.Auth=function C(b){if(!b)return;const{user_pool_id:x,user_pool_client_id:U,identity_pool_id:$,password_policy:F,mfa_configuration:X,mfa_methods:te,unauthenticated_identities_enabled:he,oauth:ie,username_attributes:ue,standard_required_attributes:Z,groups:o}=b,i={Cognito:{userPoolId:x,userPoolClientId:U,groups:o}};return $&&(i.Cognito={...i.Cognito,identityPoolId:$}),F&&(i.Cognito.passwordFormat={requireLowercase:F.require_lowercase,requireNumbers:F.require_numbers,requireUppercase:F.require_uppercase,requireSpecialCharacters:F.require_symbols,minLength:F.min_length??6}),X&&(i.Cognito.mfa={status:ee(X),smsEnabled:te?.includes("SMS"),totpEnabled:te?.includes("TOTP")}),he&&(i.Cognito.allowGuestAccess=he),ie&&(i.Cognito.loginWith={oauth:{domain:ie.domain,redirectSignIn:ie.redirect_sign_in_uri,redirectSignOut:ie.redirect_sign_out_uri,responseType:"token"===ie.response_type?"token":"code",scopes:ie.scopes,providers:q(ie.identity_providers)}}),ue&&(i.Cognito.loginWith={...i.Cognito.loginWith,email:ue.includes("email"),phone:ue.includes("phone_number"),username:ue.includes("username")}),Z&&(i.Cognito.userAttributes=Z.reduce((c,n)=>({...c,[n]:{required:!0}}),{})),i}(b.auth)),b.analytics&&(x.Analytics=function D(b){if(!b?.amazon_pinpoint)return;const{amazon_pinpoint:x}=b;return{Pinpoint:{appId:x.app_id,region:x.aws_region}}}(b.analytics)),b.geo&&(x.Geo=function P(b){if(!b)return;const{aws_region:x,geofence_collections:U,maps:$,search_indices:F}=b;return{LocationService:{region:x,searchIndices:F,geofenceCollections:U,maps:$}}}(b.geo)),b.data&&(x.API=function S(b){if(!b)return;const{aws_region:x,default_authorization_type:U,url:$,api_key:F,model_introspection:X}=b;return{GraphQL:{endpoint:$,defaultAuthMode:G(U),region:x,apiKey:F,modelIntrospection:X}}}(b.data)),b.custom){const U=function l(b){if(!b?.events)return;const{url:x,aws_region:U,api_key:$,default_authorization_type:F}=b.events;return{Events:{endpoint:x,defaultAuthMode:G(F),region:U,apiKey:$}}}(b.custom);U&&"Events"in U&&(x.API={...x.API,...U})}return b.notifications&&(x.Notifications=function m(b){if(!b)return;const{aws_region:x,channels:U,amazon_pinpoint_app_id:$}=b,F=U.includes("IN_APP_MESSAGING"),X=U.includes("APNS")||U.includes("FCM");if(!F&&!X)return;const te={};return F&&(te.InAppMessaging={Pinpoint:{appId:$,region:x}}),X&&(te.PushNotification={Pinpoint:{appId:$,region:x}}),te}(b.notifications)),x}(b):b},90206:(H,T,a)=>{a.d(T,{t:()=>g});const g=3e5},75599:(H,T,a)=>{a.d(T,{k:()=>p});var g=a(90206);function p(M=g.t){return I=>{const h=2**I*100+100*Math.random();return!(h>M)&&h}}},97582:(H,T,a)=>{a.d(T,{FC:()=>ee,Jh:()=>P,KL:()=>se,ZT:()=>p,_T:()=>E,cy:()=>b,ev:()=>Y,gn:()=>A,mG:()=>D,pi:()=>M,pr:()=>G,qq:()=>q});var g=function(o,i){return(g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,n){c.__proto__=n}||function(c,n){for(var d in n)Object.prototype.hasOwnProperty.call(n,d)&&(c[d]=n[d])})(o,i)};function p(o,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function c(){this.constructor=o}g(o,i),o.prototype=null===i?Object.create(i):(c.prototype=i.prototype,new c)}var M=function(){return M=Object.assign||function(i){for(var c,n=1,d=arguments.length;n<d;n++)for(var f in c=arguments[n])Object.prototype.hasOwnProperty.call(c,f)&&(i[f]=c[f]);return i},M.apply(this,arguments)};function E(o,i){var c={};for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&i.indexOf(n)<0&&(c[n]=o[n]);if(null!=o&&"function"==typeof Object.getOwnPropertySymbols){var d=0;for(n=Object.getOwnPropertySymbols(o);d<n.length;d++)i.indexOf(n[d])<0&&Object.prototype.propertyIsEnumerable.call(o,n[d])&&(c[n[d]]=o[n[d]])}return c}function A(o,i,c,n){var O,d=arguments.length,f=d<3?i:null===n?n=Object.getOwnPropertyDescriptor(i,c):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)f=Reflect.decorate(o,i,c,n);else for(var j=o.length-1;j>=0;j--)(O=o[j])&&(f=(d<3?O(f):d>3?O(i,c,f):O(i,c))||f);return d>3&&f&&Object.defineProperty(i,c,f),f}function D(o,i,c,n){return new(c||(c=Promise))(function(f,O){function j(B){try{N(n.next(B))}catch(J){O(J)}}function V(B){try{N(n.throw(B))}catch(J){O(J)}}function N(B){B.done?f(B.value):function d(f){return f instanceof c?f:new c(function(O){O(f)})}(B.value).then(j,V)}N((n=n.apply(o,i||[])).next())})}function P(o,i){var n,d,f,c={label:0,sent:function(){if(1&f[0])throw f[1];return f[1]},trys:[],ops:[]},O=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return O.next=j(0),O.throw=j(1),O.return=j(2),"function"==typeof Symbol&&(O[Symbol.iterator]=function(){return this}),O;function j(N){return function(B){return function V(N){if(n)throw new TypeError("Generator is already executing.");for(;O&&(O=0,N[0]&&(c=0)),c;)try{if(n=1,d&&(f=2&N[0]?d.return:N[0]?d.throw||((f=d.return)&&f.call(d),0):d.next)&&!(f=f.call(d,N[1])).done)return f;switch(d=0,f&&(N=[2&N[0],f.value]),N[0]){case 0:case 1:f=N;break;case 4:return c.label++,{value:N[1],done:!1};case 5:c.label++,d=N[1],N=[0];continue;case 7:N=c.ops.pop(),c.trys.pop();continue;default:if(!(f=(f=c.trys).length>0&&f[f.length-1])&&(6===N[0]||2===N[0])){c=0;continue}if(3===N[0]&&(!f||N[1]>f[0]&&N[1]<f[3])){c.label=N[1];break}if(6===N[0]&&c.label<f[1]){c.label=f[1],f=N;break}if(f&&c.label<f[2]){c.label=f[2],c.ops.push(N);break}f[2]&&c.ops.pop(),c.trys.pop();continue}N=i.call(o,c)}catch(B){N=[6,B],d=0}finally{n=f=0}if(5&N[0])throw N[1];return{value:N[0]?N[1]:void 0,done:!0}}([N,B])}}}function G(){for(var o=0,i=0,c=arguments.length;i<c;i++)o+=arguments[i].length;var n=Array(o),d=0;for(i=0;i<c;i++)for(var f=arguments[i],O=0,j=f.length;O<j;O++,d++)n[d]=f[O];return n}function Y(o,i,c){if(c||2===arguments.length)for(var f,n=0,d=i.length;n<d;n++)(f||!(n in i))&&(f||(f=Array.prototype.slice.call(i,0,n)),f[n]=i[n]);return o.concat(f||Array.prototype.slice.call(i))}function q(o){return this instanceof q?(this.v=o,this):new q(o)}function ee(o,i,c){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var d,n=c.apply(o,i||[]),f=[];return d=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),j("next"),j("throw"),j("return",function O(z){return function(ne){return Promise.resolve(ne).then(z,J)}}),d[Symbol.asyncIterator]=function(){return this},d;function j(z,ne){n[z]&&(d[z]=function(fe){return new Promise(function(ge,pe){f.push([z,fe,ge,pe])>1||V(z,fe)})},ne&&(d[z]=ne(d[z])))}function V(z,ne){try{!function N(z){z.value instanceof q?Promise.resolve(z.value.v).then(B,J):le(f[0][2],z)}(n[z](ne))}catch(fe){le(f[0][3],fe)}}function B(z){V("next",z)}function J(z){V("throw",z)}function le(z,ne){z(ne),f.shift(),f.length&&V(f[0][0],f[0][1])}}function se(o){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c,i=o[Symbol.asyncIterator];return i?i.call(o):(o=function m(o){var i="function"==typeof Symbol&&Symbol.iterator,c=i&&o[i],n=0;if(c)return c.call(o);if(o&&"number"==typeof o.length)return{next:function(){return o&&n>=o.length&&(o=void 0),{value:o&&o[n++],done:!o}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")}(o),c={},n("next"),n("throw"),n("return"),c[Symbol.asyncIterator]=function(){return this},c);function n(f){c[f]=o[f]&&function(O){return new Promise(function(j,V){!function d(f,O,j,V){Promise.resolve(V).then(function(N){f({value:N,done:j})},O)}(j,V,(O=o[f](O)).done,O.value)})}}}function b(o,i){return Object.defineProperty?Object.defineProperty(o,"raw",{value:i}):o.raw=i,o}"function"==typeof SuppressedError&&SuppressedError}}]);