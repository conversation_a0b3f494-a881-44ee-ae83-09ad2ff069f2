(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3637],{33637:(N,s,o)=>{o.r(s),o.d(s,{MboTransfiyaApprovedConfirmationPageModule:()=>S});var c=o(17007),m=o(78007),f=o(79798),r=o(30263),u=o(15861),v=o(39904),y=o(95437),b=o(57544),d=o(17698),g=o(73004),a=o(99877),h=o(10464),C=o(48774),A=o(17941),T=o(45542),P=o(60817);function I(t,i){if(1&t&&a.\u0275\u0275element(0,"bocc-card-summary",14),2&t){const e=a.\u0275\u0275nextContext();a.\u0275\u0275property("detail",null==e.transfiya||null==e.transfiya.approved?null:e.transfiya.approved.description)}}const l=v.Z6.TRANSFERS.TRANSFIYA.APPROVED;let M=(()=>{class t{constructor(e,n,p,E){this.mboProvider=e,this.requestConfiguration=n,this.managerTransfiya=p,this.cancelProvider=E,this.backAction={id:"btn_transfiya-approved-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(l.DESTINATION)}},this.cancelAction={id:"btn_transfiya-approved-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.trustedContact=new b.FormControl(!1)}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerTransfiya.setTrustedContact(this.trustedContact.value).when({success:()=>{this.mboProvider.navigation.next(l.RESULT)}})}initializatedConfiguration(){var e=this;return(0,u.Z)(function*(){(yield e.requestConfiguration.confirmation()).when({success:({transfiya:n})=>{e.transfiya=n}})})()}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275directiveInject(y.ZL),a.\u0275\u0275directiveInject(d.NF),a.\u0275\u0275directiveInject(d.Pm),a.\u0275\u0275directiveInject(g.c))},t.\u0275cmp=a.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfiya-approved-confirmation-page"]],decls:19,vars:8,consts:[[1,"mbo-transfiya-approved-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfiya-approved-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfiya-approved-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],["header","ENV\xcdA",3,"subtitle"],["header","LA SUMA DE",3,"amount"],["header","DESTINO",3,"title","subtitle"],["header","DESCRIPCI\xd3N",3,"detail",4,"ngIf"],[3,"formControl"],[1,"mbo-transfiya-approved-confirmation-page__footer"],["id","btn_transfiya-approved-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"click"],["header","DESCRIPCI\xd3N",3,"detail"]],template:function(e,n){1&e&&(a.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),a.\u0275\u0275element(3,"bocc-header-form",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),a.\u0275\u0275text(7," \xbfDeseas confirmar esta transferencia? "),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(8,"div",6),a.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),a.\u0275\u0275template(12,I,1,1,"bocc-card-summary",10),a.\u0275\u0275elementStart(13,"bocc-checkbox-label",11),a.\u0275\u0275text(14," Acepto guardar este celular de confianza para recibir autom\xe1ticamente en esta cuenta sus futuras transferencias "),a.\u0275\u0275elementEnd()()()()(),a.\u0275\u0275elementStart(15,"div",12)(16,"button",13),a.\u0275\u0275listener("click",function(){return n.onSubmit()}),a.\u0275\u0275elementStart(17,"span"),a.\u0275\u0275text(18,"Aceptar transferencia"),a.\u0275\u0275elementEnd()()()()),2&e&&(a.\u0275\u0275advance(3),a.\u0275\u0275property("leftAction",n.backAction)("rightAction",n.cancelAction),a.\u0275\u0275advance(6),a.\u0275\u0275property("subtitle",null==n.transfiya||null==n.transfiya.approved?null:n.transfiya.approved.phone),a.\u0275\u0275advance(1),a.\u0275\u0275property("amount",null==n.transfiya||null==n.transfiya.approved?null:n.transfiya.approved.amount),a.\u0275\u0275advance(1),a.\u0275\u0275property("title",null==n.transfiya||null==n.transfiya.product?null:n.transfiya.product.nickname)("subtitle",null==n.transfiya||null==n.transfiya.product?null:n.transfiya.product.number),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",null==n.transfiya||null==n.transfiya.approved?null:n.transfiya.approved.description),a.\u0275\u0275advance(1),a.\u0275\u0275property("formControl",n.trustedContact))},dependencies:[c.NgIf,h.K,C.J,A.D,T.P,P.a],styles:["/*!\n * MBO TransfiyaApprovedConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-approved-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-approved-confirmation-page .mbo-transfiya-approved-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-approved-confirmation-page .mbo-transfiya-approved-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-approved-confirmation-page .mbo-transfiya-approved-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-approved-confirmation-page .mbo-transfiya-approved-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),t})(),S=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=a.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=a.\u0275\u0275defineInjector({imports:[c.CommonModule,m.RouterModule.forChild([{path:"",component:M}]),f.KI,r.Jx,r.DM,r.P8,r.b6,r.oc,r.aR]}),t})()}}]);