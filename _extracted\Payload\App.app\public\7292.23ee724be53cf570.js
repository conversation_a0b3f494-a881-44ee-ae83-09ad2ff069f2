(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7292],{7292:(T0,M,$)=>{$.r(M),$.d(M,{MAX:()=>F,NIL:()=>G,parse:()=>p,stringify:()=>Z,v1:()=>v,v1ToV6:()=>C,v3:()=>l0,v4:()=>d0,v5:()=>u0,v6:()=>U0,v6ToV1:()=>s0,v7:()=>I0,validate:()=>D,version:()=>D0});const F="ffffffff-ffff-ffff-ffff-ffffffffffff",G="00000000-0000-0000-0000-000000000000",J=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,D=function P(n){return"string"==typeof n&&J.test(n)},p=function Q(n){if(!D(n))throw TypeError("Invalid UUID");let f;return Uint8Array.of((f=parseInt(n.slice(0,8),16))>>>24,f>>>16&255,f>>>8&255,255&f,(f=parseInt(n.slice(9,13),16))>>>8,255&f,(f=parseInt(n.slice(14,18),16))>>>8,255&f,(f=parseInt(n.slice(19,23),16))>>>8,255&f,(f=parseInt(n.slice(24,36),16))/1099511627776&255,f/4294967296&255,f>>>24&255,f>>>16&255,f>>>8&255,255&f)},d=[];for(let n=0;n<256;++n)d.push((n+256).toString(16).slice(1));function U(n,f=0){return(d[n[f+0]]+d[n[f+1]]+d[n[f+2]]+d[n[f+3]]+"-"+d[n[f+4]]+d[n[f+5]]+"-"+d[n[f+6]]+d[n[f+7]]+"-"+d[n[f+8]]+d[n[f+9]]+"-"+d[n[f+10]]+d[n[f+11]]+d[n[f+12]]+d[n[f+13]]+d[n[f+14]]+d[n[f+15]]).toLowerCase()}const Z=function Y(n,f=0){const t=U(n,f);if(!D(t))throw TypeError("Stringified UUID is invalid");return t};let R;const b=new Uint8Array(16);function I(){if(!R){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");R=crypto.getRandomValues.bind(crypto)}return R(b)}const A={};function N(n,f,t,r,e,c,o=0){if(n.length<16)throw new Error("Random bytes length must be >= 16");if(c){if(o<0||o+16>c.length)throw new RangeError(`UUID byte range ${o}:${o+15} is out of buffer bounds`)}else c=new Uint8Array(16),o=0;f??=Date.now(),t??=0,r??=16383&(n[8]<<8|n[9]),e??=n.slice(10,16);const i=(1e4*(268435455&(f+=122192928e5))+t)%4294967296;c[o++]=i>>>24&255,c[o++]=i>>>16&255,c[o++]=i>>>8&255,c[o++]=255&i;const l=f/4294967296*1e4&268435455;c[o++]=l>>>8&255,c[o++]=255&l,c[o++]=l>>>24&15|16,c[o++]=l>>>16&255,c[o++]=r>>>8|128,c[o++]=255&r;for(let a=0;a<6;++a)c[o++]=e[a];return c}const v=function z(n,f,t){let r;const e=n?._v6??!1;if(n){const c=Object.keys(n);1===c.length&&"_v6"===c[0]&&(n=void 0)}if(n)r=N(n.random??n.rng?.()??I(),n.msecs,n.nsecs,n.clockseq,n.node,f,t);else{const c=Date.now(),o=I();(function B(n,f,t){n.msecs??=-1/0,n.nsecs??=0,f===n.msecs?(n.nsecs++,n.nsecs>=1e4&&(n.node=void 0,n.nsecs=0)):f>n.msecs?n.nsecs=0:f<n.msecs&&(n.node=void 0),n.node||(n.node=t.slice(10,16),n.node[0]|=1,n.clockseq=16383&(t[8]<<8|t[9])),n.msecs=f})(A,c,o),r=N(o,A.msecs,A.nsecs,e?void 0:A.clockseq,e?void 0:A.node,f,t)}return f??U(r)};function C(n){const t=function n0(n){return Uint8Array.of((15&n[6])<<4|n[7]>>4&15,(15&n[7])<<4|(240&n[4])>>4,(15&n[4])<<4|(240&n[5])>>4,(15&n[5])<<4|(240&n[0])>>4,(15&n[0])<<4|(240&n[1])>>4,(15&n[1])<<4|(240&n[2])>>4,96|15&n[2],n[3],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])}("string"==typeof n?p(n):n);return"string"==typeof n?U(t):t}function j(n){return 14+(n+64>>>9<<4)+1}function s(n,f){const t=(65535&n)+(65535&f);return(n>>16)+(f>>16)+(t>>16)<<16|65535&t}function y(n,f,t,r,e,c){return s(function c0(n,f){return n<<f|n>>>32-f}(s(s(f,n),s(r,c)),e),t)}function w(n,f,t,r,e,c,o){return y(f&t|~f&r,n,f,e,c,o)}function h(n,f,t,r,e,c,o){return y(f&r|t&~r,n,f,e,c,o)}function u(n,f,t,r,e,c,o){return y(f^t^r,n,f,e,c,o)}function m(n,f,t,r,e,c,o){return y(t^(f|~r),n,f,e,c,o)}const o0=function r0(n){const f=function t0(n){if(0===n.length)return new Uint32Array;const f=new Uint32Array(j(8*n.length)).fill(0);for(let t=0;t<n.length;t++)f[t>>2]|=(255&n[t])<<t%4*8;return f}(n);return function e0(n){const f=new Uint8Array(4*n.length);for(let t=0;t<4*n.length;t++)f[t]=n[t>>2]>>>t%4*8&255;return f}(function f0(n,f){const t=new Uint32Array(j(f)).fill(0);t.set(n),t[f>>5]|=128<<f%32,t[t.length-1]=f,n=t;let r=1732584193,e=-271733879,c=-1732584194,o=271733878;for(let i=0;i<n.length;i+=16){const l=r,a=e,x=c,_=o;r=w(r,e,c,o,n[i],7,-680876936),o=w(o,r,e,c,n[i+1],12,-389564586),c=w(c,o,r,e,n[i+2],17,606105819),e=w(e,c,o,r,n[i+3],22,-1044525330),r=w(r,e,c,o,n[i+4],7,-176418897),o=w(o,r,e,c,n[i+5],12,1200080426),c=w(c,o,r,e,n[i+6],17,-1473231341),e=w(e,c,o,r,n[i+7],22,-45705983),r=w(r,e,c,o,n[i+8],7,1770035416),o=w(o,r,e,c,n[i+9],12,-1958414417),c=w(c,o,r,e,n[i+10],17,-42063),e=w(e,c,o,r,n[i+11],22,-1990404162),r=w(r,e,c,o,n[i+12],7,1804603682),o=w(o,r,e,c,n[i+13],12,-40341101),c=w(c,o,r,e,n[i+14],17,-1502002290),e=w(e,c,o,r,n[i+15],22,1236535329),r=h(r,e,c,o,n[i+1],5,-165796510),o=h(o,r,e,c,n[i+6],9,-1069501632),c=h(c,o,r,e,n[i+11],14,643717713),e=h(e,c,o,r,n[i],20,-373897302),r=h(r,e,c,o,n[i+5],5,-701558691),o=h(o,r,e,c,n[i+10],9,38016083),c=h(c,o,r,e,n[i+15],14,-660478335),e=h(e,c,o,r,n[i+4],20,-405537848),r=h(r,e,c,o,n[i+9],5,568446438),o=h(o,r,e,c,n[i+14],9,-1019803690),c=h(c,o,r,e,n[i+3],14,-187363961),e=h(e,c,o,r,n[i+8],20,1163531501),r=h(r,e,c,o,n[i+13],5,-1444681467),o=h(o,r,e,c,n[i+2],9,-51403784),c=h(c,o,r,e,n[i+7],14,1735328473),e=h(e,c,o,r,n[i+12],20,-1926607734),r=u(r,e,c,o,n[i+5],4,-378558),o=u(o,r,e,c,n[i+8],11,-2022574463),c=u(c,o,r,e,n[i+11],16,1839030562),e=u(e,c,o,r,n[i+14],23,-35309556),r=u(r,e,c,o,n[i+1],4,-1530992060),o=u(o,r,e,c,n[i+4],11,1272893353),c=u(c,o,r,e,n[i+7],16,-155497632),e=u(e,c,o,r,n[i+10],23,-1094730640),r=u(r,e,c,o,n[i+13],4,681279174),o=u(o,r,e,c,n[i],11,-358537222),c=u(c,o,r,e,n[i+3],16,-722521979),e=u(e,c,o,r,n[i+6],23,76029189),r=u(r,e,c,o,n[i+9],4,-640364487),o=u(o,r,e,c,n[i+12],11,-421815835),c=u(c,o,r,e,n[i+15],16,530742520),e=u(e,c,o,r,n[i+2],23,-995338651),r=m(r,e,c,o,n[i],6,-198630844),o=m(o,r,e,c,n[i+7],10,1126891415),c=m(c,o,r,e,n[i+14],15,-1416354905),e=m(e,c,o,r,n[i+5],21,-57434055),r=m(r,e,c,o,n[i+12],6,1700485571),o=m(o,r,e,c,n[i+3],10,-1894986606),c=m(c,o,r,e,n[i+10],15,-1051523),e=m(e,c,o,r,n[i+1],21,-2054922799),r=m(r,e,c,o,n[i+8],6,1873313359),o=m(o,r,e,c,n[i+15],10,-30611744),c=m(c,o,r,e,n[i+6],15,-1560198380),e=m(e,c,o,r,n[i+13],21,1309151649),r=m(r,e,c,o,n[i+4],6,-145523070),o=m(o,r,e,c,n[i+11],10,-1120210379),c=m(c,o,r,e,n[i+2],15,718787259),e=m(e,c,o,r,n[i+9],21,-343485551),r=s(r,l),e=s(e,a),c=s(c,x),o=s(o,_)}return Uint32Array.of(r,e,c,o)}(f,8*n.length))},O="6ba7b810-9dad-11d1-80b4-00c04fd430c8",q="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function H(n,f,t,r,e,c){const o="string"==typeof t?function i0(n){n=unescape(encodeURIComponent(n));const f=new Uint8Array(n.length);for(let t=0;t<n.length;++t)f[t]=n.charCodeAt(t);return f}(t):t,i="string"==typeof r?p(r):r;if("string"==typeof r&&(r=p(r)),16!==r?.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+o.length);if(l.set(i),l.set(o,i.length),l=f(l),l[6]=15&l[6]|n,l[8]=63&l[8]|128,e){c=c||0;for(let a=0;a<16;++a)e[c+a]=l[a];return e}return U(l)}function V(n,f,t,r){return H(48,o0,n,f,t,r)}V.DNS=O,V.URL=q;const l0=V,K={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},d0=function a0(n,f,t){if(K.randomUUID&&!f&&!n)return K.randomUUID();const r=(n=n||{}).random??n.rng?.()??I();if(r.length<16)throw new Error("Random bytes length must be >= 16");if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,f){if((t=t||0)<0||t+16>f.length)throw new RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)f[t+e]=r[e];return f}return U(r)};function g0(n,f,t,r){switch(n){case 0:return f&t^~f&r;case 1:case 3:return f^t^r;case 2:return f&t^f&r^t&r}}function E(n,f){return n<<f|n>>>32-f}const h0=function w0(n){const f=[1518500249,1859775393,2400959708,3395469782],t=[1732584193,4023233417,2562383102,271733878,3285377520],r=new Uint8Array(n.length+1);r.set(n),r[n.length]=128,n=r;const c=Math.ceil((n.length/4+2)/16),o=new Array(c);for(let i=0;i<c;++i){const l=new Uint32Array(16);for(let a=0;a<16;++a)l[a]=n[64*i+4*a]<<24|n[64*i+4*a+1]<<16|n[64*i+4*a+2]<<8|n[64*i+4*a+3];o[i]=l}o[c-1][14]=8*(n.length-1)/Math.pow(2,32),o[c-1][14]=Math.floor(o[c-1][14]),o[c-1][15]=8*(n.length-1)&4294967295;for(let i=0;i<c;++i){const l=new Uint32Array(80);for(let g=0;g<16;++g)l[g]=o[i][g];for(let g=16;g<80;++g)l[g]=E(l[g-3]^l[g-8]^l[g-14]^l[g-16],1);let a=t[0],x=t[1],_=t[2],T=t[3],k=t[4];for(let g=0;g<80;++g){const X=Math.floor(g/20),y0=E(a,5)+g0(X,x,_,T)+k+f[X]+l[g]>>>0;k=T,T=_,_=E(x,30)>>>0,x=a,a=y0}t[0]=t[0]+a>>>0,t[1]=t[1]+x>>>0,t[2]=t[2]+_>>>0,t[3]=t[3]+T>>>0,t[4]=t[4]+k>>>0}return Uint8Array.of(t[0]>>24,t[0]>>16,t[0]>>8,t[0],t[1]>>24,t[1]>>16,t[1]>>8,t[1],t[2]>>24,t[2]>>16,t[2]>>8,t[2],t[3]>>24,t[3]>>16,t[3]>>8,t[3],t[4]>>24,t[4]>>16,t[4]>>8,t[4])};function S(n,f,t,r){return H(80,h0,n,f,t,r)}S.DNS=O,S.URL=q;const u0=S,U0=function m0(n,f,t){n??={},t??=0;let r=v({...n,_v6:!0},new Uint8Array(16));if(r=C(r),f){for(let e=0;e<16;e++)f[t+e]=r[e];return f}return U(r)};function s0(n){const t=function x0(n){return Uint8Array.of((15&n[3])<<4|n[4]>>4&15,(15&n[4])<<4|(240&n[5])>>4,(15&n[5])<<4|15&n[6],n[7],(15&n[1])<<4|(240&n[2])>>4,(15&n[2])<<4|(240&n[3])>>4,16|(240&n[0])>>4,(15&n[0])<<4|(240&n[1])>>4,n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])}("string"==typeof n?p(n):n);return"string"==typeof n?U(t):t}const L={};function W(n,f,t,r,e=0){if(n.length<16)throw new Error("Random bytes length must be >= 16");if(r){if(e<0||e+16>r.length)throw new RangeError(`UUID byte range ${e}:${e+15} is out of buffer bounds`)}else r=new Uint8Array(16),e=0;return f??=Date.now(),t??=127*n[6]<<24|n[7]<<16|n[8]<<8|n[9],r[e++]=f/1099511627776&255,r[e++]=f/4294967296&255,r[e++]=f/16777216&255,r[e++]=f/65536&255,r[e++]=f/256&255,r[e++]=255&f,r[e++]=112|t>>>28&15,r[e++]=t>>>20&255,r[e++]=128|t>>>14&63,r[e++]=t>>>6&255,r[e++]=t<<2&255|3&n[10],r[e++]=n[11],r[e++]=n[12],r[e++]=n[13],r[e++]=n[14],r[e++]=n[15],r}const I0=function _0(n,f,t){let r;if(n)r=W(n.random??n.rng?.()??I(),n.msecs,n.seq,f,t);else{const e=Date.now(),c=I();(function p0(n,f,t){n.msecs??=-1/0,n.seq??=0,f>n.msecs?(n.seq=t[6]<<23|t[7]<<16|t[8]<<8|t[9],n.msecs=f):(n.seq=n.seq+1|0,0===n.seq&&n.msecs++)})(L,e,c),r=W(c,L.msecs,L.seq,f,t)}return f??U(r)},D0=function A0(n){if(!D(n))throw TypeError("Invalid UUID");return parseInt(n.slice(14,15),16)}}}]);