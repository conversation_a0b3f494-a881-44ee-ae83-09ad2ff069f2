(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6123],{10954:(N,x,t)=>{t.d(x,{V:()=>I,Ws:()=>d,YH:()=>P,d6:()=>A,uJ:()=>v});var e=t(39904),C=t(87903),n=t(53113),i=t(66067);class d extends i.T2{constructor(o,l,b,u,s,E,y,p,R,L,m,O,T){super(o,l,b,u,s,y,p,R,L,O,T),this.colorValue=E,this.franchise=m,this.bank.isOccidente&&R&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,C.mm)(this,m),this.currenciesValue=m?.currencies||[e.y1],this.digitalValue="DIGITAL"===E}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class v{constructor(o,l,b){this.code=o,this.amount=l,this.amountCurrency=b||0}}class P{constructor(o,l,b,u,s){this.label=o,this.mode=l,this.copTotal=u?.value||0,this.usdTotal=s?.value||0,this.copUsdTotal=b?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new v("COP",this.copTotal,this.copTotal)}usdValue(){return new v("USD",this.copUsdTotal,this.usdTotal)}}class g{constructor(o,l,b){this.destination=o,this.source=l,this.currency=b}}class I{constructor(o,l,b,u,s,E){this.destination=o,this.source=l,this.isManual=b,this.trm=u,this.cop=s,this.usd=E,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new g(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new g(this.destination,this.source,this.usd):void 0}}class A extends n.LN{constructor(o,l,b){super(o,l),this.currency=b}}},96381:(N,x,t)=>{t.d(x,{T:()=>T,P:()=>J});var e=t(15861),C=t(77279),n=t(81536),i=t(87956),d=t(98699),v=t(10954),P=t(39904),g=t(29306),I=t(7464),A=t(87903),M=t(53113),o=t(1131);function u(a,z){return new v.V(a.destination,a.source,a.mode===o.o.MANUAL,z,a.cop,a.usd)}var E=t(71776),y=t(42168),p=t(99877);let R=(()=>{class a{constructor(r,c){this.http=r,c.subscribes(P.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,y.firstValueFrom)(this.http.get(P.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,y.map)(({content:r})=>r.map(c=>function b(a){return new v.Ws(a.id,a.acctType,a.acctTypeName,"DIGITAL"===a.color?P.CG:a.loanName,a.acctId,a.isOwner&&a.color||"NONE",(0,I.RO)(a.bankId,a.bankName),a.isAval,a.dynamo||!1,a.isOwner,a.creditCardType?function l(a){return new g.dD(a?.code,a?.description,0,0)}(a.creditCardType):void 0,a.isOwner?void 0:a.owner,a.isOwner?void 0:new M.dp((0,A.nX)(a.ownerIdType),a.ownerId))}(c))),(0,y.tap)(r=>{this.creditCards=r})))}}return a.\u0275fac=function(r){return new(r||a)(p.\u0275\u0275inject(E.HttpClient),p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),L=(()=>{class a{constructor(r){this.http=r}send(r){return(0,A.EC)([r.getPaymentCop(),r.getPaymentUsd()].filter(c=>!!c).map(c=>()=>this.sendCurrency(c)))}sendCurrency(r){return(0,y.firstValueFrom)(this.http.post(P.bV.PAYMENTS.CREDIT_CARD,function s(a){return{acctIdFrom:a.source.id,acctNickNameFrom:a.source.nickname,bankIdFrom:a.source.bank.id,acctIdTo:a.destination.id,acctNameTo:a.destination.nickname,bankIdTo:a.destination.bank.id,bankNameTo:a.destination.bank.name,amt:Math.ceil(a.currency.amount),curCode:a.currency.code,paymentDesc:""}}(r)).pipe((0,y.map)(c=>{const _=(0,A.l1)(c,"SUCCESS"),{type:D,message:S}=_;return new v.d6(D,S,r.currency)}),(0,y.catchError)(c=>{const{message:_}=(0,A.rU)(c);return(0,y.of)(new v.d6("ERROR",_,r.currency))})))}}return a.\u0275fac=function(r){return new(r||a)(p.\u0275\u0275inject(E.HttpClient))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var m=t(20691);let O=(()=>{class a extends m.Store{constructor(r){super({confirmation:!1,fromCustomer:!1,mode:o.o.PAY_MIN}),this.eventBusService=r,this.eventBusService.subscribes(P.PU,()=>{this.reset()})}setDestination(r,c=!1){this.reduce(_=>({..._,destination:r,fromCustomer:c}))}getDestination(){return this.select(({destination:r})=>r)}itIsFromCustomer(){return this.select(({fromCustomer:r})=>r)}setSource(r){this.reduce(c=>({...c,source:r}))}getSource(){return this.select(({source:r})=>r)}setAmount(r){const{cop:c,mode:_,usd:D}=r;this.reduce(S=>({...S,cop:c,mode:_,usd:D,confirmation:!0}))}getAmount(){return this.select(({mode:r,cop:c,usd:_})=>({cop:c,mode:r,usd:_}))}setCurrencyCode(r){this.reduce(c=>({...c,currencyCode:r}))}itIsConfirmation(){return this.select(({confirmation:r})=>r)}}return a.\u0275fac=function(r){return new(r||a)(p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),T=(()=>{class a{constructor(r,c,_,D,S){this.financials=r,this.productService=c,this.repository=_,this.store=D,this.eventBusService=S}setDestination(r){var c=this;return(0,e.Z)(function*(){try{return r.isRequiredInformation&&(yield c.productService.requestInformation(r)),d.Either.success(c.store.setDestination(r))}catch{return d.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(r){try{return d.Either.success(this.store.setSource(r))}catch({message:c}){return d.Either.failure({message:c})}}setAmount(r){try{return d.Either.success(this.store.setAmount(r))}catch({message:c}){return d.Either.failure({message:c})}}setCurrencyCode(r){try{return d.Either.success(this.store.setCurrencyCode(r))}catch({message:c}){return d.Either.failure({message:c})}}reset(){try{const r=this.store.itIsFromCustomer(),c=this.store.getDestination();return this.store.reset(),d.Either.success({fromCustomer:r,destination:c})}catch({message:r}){return d.Either.failure({message:r})}}send(){var r=this;return(0,e.Z)(function*(){const c=u(r.store.currentState,yield r.requestTrmUsd()),_=yield r.execute(c),D=_.reduce((S,{isError:B})=>S&&!B,!0);return r.eventBusService.emit(D?C.q.TransactionSuccess:C.q.TransactionFailed),d.Either.success({creditCard:c,status:_})})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([r])=>r))}execute(r){try{return this.repository.send(r)}catch({message:c}){return Promise.resolve([new v.d6("ERROR",c)])}}}return a.\u0275fac=function(r){return new(r||a)(p.\u0275\u0275inject(n.rm),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(L),p.\u0275\u0275inject(O),p.\u0275\u0275inject(i.Yd))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var K=t(89148);const{MANUAL:U,PAY_ALTERNATIVE:F,PAY_MIN:f,PAY_TOTAL:h}=o.o,{CcaDatePayMinCop:j,CcaDatePayMinUsd:w,CcaPayAltMinCop:W,CcaPayAltMinUsd:Y,CcaPayMinCop:G,CcaPayMinUsd:Z,CcaPayTotalCop:k,CcaPayTotalUsd:H}=K.Av;let J=(()=>{class a{constructor(r,c,_,D,S){this.products=r,this.productService=c,this.financials=_,this.repository=D,this.store=S}destination(){var r=this;return(0,e.Z)(function*(){try{return d.Either.success((yield r.repository.request()).reduce((c,_)=>{const{others:D,principals:S}=c;return(_.bank.isOccidente?S:D).push(_),c},{others:[],principals:[]}))}catch({message:c}){return d.Either.failure({message:c})}})()}source(r){var c=this;return(0,e.Z)(function*(){try{const _=yield c.products.requestAccountsForTransfer(),D=c.store.itIsConfirmation(),S=yield c.requestCreditCard(r);return d.Either.success({confirmation:D,destination:S,products:_})}catch({message:_}){return d.Either.failure({message:_})}})()}information(){var r=this;return(0,e.Z)(function*(){try{const c=r.store.getDestination(),_=yield r.productService.requestInformation(c),D=yield r.requestTrmUsd(),S=_?.getSection(j),B=_?.getSection(w);return d.Either.success({destination:c,min:new v.YH("VALOR M\xcdNIMO A PAGAR",f,D,_?.getSection(G),_?.getSection(Z)),alternative:new v.YH("VALOR M\xcdNIMO ALTERNO",F,D,_?.getSection(W),_?.getSection(Y)),total:new v.YH("SALDO ACTUAL",h,D,_?.getSection(k),_?.getSection(H)),dateCop:S?.valueFormat,dateUsd:B?.valueFormat})}catch({message:c}){return d.Either.failure({message:c})}})()}selectAmount(){var r=this;return(0,e.Z)(function*(){try{const c=r.store.itIsConfirmation(),_=r.store.getAmount(),D=r.store.getSource(),S=r.store.getDestination(),B=yield r.productService.requestInformation(S),V=yield r.requestTrmUsd();return d.Either.success({destination:S,amount:_,confirmation:c,trm:V,source:D,min:new v.YH("Pago m\xednimo",f,V,B?.getSection(G),B?.getSection(Z)),alternative:new v.YH("Pago m\xednimo alterno",F,V,B?.getSection(W),B?.getSection(Y)),total:new v.YH("Saldo actual",h,V,B?.getSection(k),B?.getSection(H)),manual:new v.YH("Otro valor",U)})}catch({message:c}){return d.Either.failure({message:c})}})()}amount(){try{const r=this.store.itIsConfirmation(),c=this.store.getSource(),_=this.store.getDestination(),{cop:D}=this.store.getAmount();return d.Either.success({amount:D?.amount||0,confirmation:r,destination:_,source:c})}catch({message:r}){return d.Either.failure({message:r})}}confirmation(){var r=this;return(0,e.Z)(function*(){try{const c=u(r.store.currentState,yield r.requestTrmUsd());return d.Either.success({payment:c})}catch({message:c}){return d.Either.failure({message:c})}})()}requestCreditCard(r){var c=this;return(0,e.Z)(function*(){let _=c.store.getDestination();return!_&&r&&(_=(yield c.products.requestCreditCards()).find(({id:D})=>D===r),c.store.setDestination(_,!0)),_})()}requestTrmUsd(){return(0,d.catchPromise)(this.financials.request().then(([r])=>r))}}return a.\u0275fac=function(r){return new(r||a)(p.\u0275\u0275inject(i.hM),p.\u0275\u0275inject(i.M5),p.\u0275\u0275inject(n.rm),p.\u0275\u0275inject(R),p.\u0275\u0275inject(O))},a.\u0275prov=p.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},55351:(N,x,t)=>{t.d(x,{t:()=>P});var e=t(30263),C=t(39904),n=t(95437),i=t(96381),d=t(99877);let P=(()=>{class g{constructor(A,M,o){this.modalConfirmation=A,this.mboProvider=M,this.managerCreditCard=o}execute(A=!0){A?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:A,destination:M})=>{A?this.mboProvider.navigation.back(C.Z6.CUSTOMER.PRODUCTS.INFO,{productId:M.id}):this.mboProvider.navigation.back(C.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(C.Z6.PAYMENTS.HOME)}})}}return g.\u0275fac=function(A){return new(A||g)(d.\u0275\u0275inject(e.$e),d.\u0275\u0275inject(n.ZL),d.\u0275\u0275inject(i.T))},g.\u0275prov=d.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},1131:(N,x,t)=>{t.d(x,{o:()=>e});var e=(()=>{return(C=e||(e={}))[C.PAY_MIN=0]="PAY_MIN",C[C.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",C[C.PAY_TOTAL=2]="PAY_TOTAL",C[C.MANUAL=3]="MANUAL",e;var C})()},63111:(N,x,t)=>{t.d(x,{m:()=>i});var e=t(99877),n=t(3235);let i=(()=>{class d{constructor(){this.copAmount=0,this.copUsdAmount=0,this.usdAmount=0}}return d.\u0275fac=function(P){return new(P||d)},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-card-currency-amount"]],inputs:{copAmount:"copAmount",copUsdAmount:"copUsdAmount",usdAmount:"usdAmount",theme:"theme"},decls:18,vars:15,consts:[[1,"mbo-card-currency-amount__content"],[1,"mbo-card-currency-amount__currency"],[1,"mbo-card-currency-amount__currency__logo"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"mbo-card-currency-amount__currency__content"],[1,"mbo-card-currency-amount__currency__detail","caption-medium"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"mbo-card-currency-amount__currency__value","caption-medium"]],template:function(P,g){1&P&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),e.\u0275\u0275element(3,"img",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",4)(5,"span",5),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"boccCurrency"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(8,"div",1)(9,"div",2),e.\u0275\u0275element(10,"img",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",4)(12,"span",5),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"boccCurrency"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"span",7),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"boccCurrency"),e.\u0275\u0275elementEnd()()()()),2&P&&(e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(7,3,null==g.copAmount?null:g.copAmount.toString(),"$",!1)," "),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(14,7,null==g.copUsdAmount?null:g.copUsdAmount.toString(),"$",!1)," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(17,11,null==g.usdAmount?null:g.usdAmount.toString(),"USD",!0)," "))},dependencies:[n.T],styles:["/*!\n * MBO CardCurrencyAmount Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 28/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-card-currency-amount{position:relative;width:100%;display:block}mbo-card-currency-amount .mbo-card-currency-amount__content{position:relative;display:flex;width:100%;padding:var(--sizing-x6);box-sizing:border-box}mbo-card-currency-amount .mbo-card-currency-amount__currency{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo{max-width:var(--sizing-x8);max-height:var(--sizing-x8)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo img{width:100%;height:100%}mbo-card-currency-amount .mbo-card-currency-amount__currency__content{display:flex;width:calc(100% - 14rem);flex-direction:column;row-gap:var(--sizing-x1)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail{position:relative;width:100%;color:var(--color-carbon-lighter-700)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail:before{margin-right:var(--sizing-x1);text-align:left;width:var(--sizing-x4)}mbo-card-currency-amount .mbo-card-currency-amount__currency__value{position:relative;width:100%;color:var(--color-amathyst-700)}\n"],encapsulation:2}),d})()},2297:(N,x,t)=>{t.d(x,{p:()=>o});var e=t(30263),n=(t(10954),t(99877)),d=t(17007),P=t(2460),g=t(45542),I=t(3235),A=t(16450);function M(l,b){if(1&l&&(n.\u0275\u0275elementStart(0,"div",7)(1,"div",8),n.\u0275\u0275element(2,"img",15),n.\u0275\u0275elementStart(3,"span",10),n.\u0275\u0275text(4,"Deuda en d\xf3lares"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(5,"div",11)(6,"span",10),n.\u0275\u0275text(7),n.\u0275\u0275pipe(8,"boccCurrencyCop"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"span",16),n.\u0275\u0275text(10),n.\u0275\u0275pipe(11,"boccCurrency"),n.\u0275\u0275elementEnd()()()),2&l){const u=n.\u0275\u0275nextContext();n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(8,2,null==u.currency?null:u.currency.copUsdTotal.toString(),!1)," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind3(11,5,null==u.currency?null:u.currency.usdTotal.toString(),"USD",!1)," ")}}let o=(()=>{class l{constructor(u){this.modalConfirmation=u,this.canUsd=!0,this.condense=!0}onHeader(){this.condense=!this.condense}onInformation(){this.modalConfirmation.execute({message:this.message,title:this.title,accept:{label:"Aceptar"}})}}return l.\u0275fac=function(u){return new(u||l)(n.\u0275\u0275directiveInject(e.$e))},l.\u0275cmp=n.\u0275\u0275defineComponent({type:l,selectors:[["mbo-creditcard-information"]],inputs:{currency:"currency",title:"title",message:"message",canUsd:"canUsd"},decls:24,vars:13,consts:[[1,"mbo-creditcard-information__content"],[1,"mbo-creditcard-information__header",3,"click"],[1,"overline-medium"],[1,"mbo-creditcard-information__header__amount"],[1,"body1-medium"],[3,"icon"],[1,"mbo-creditcard-information__body",3,"hidden"],[1,"mbo-creditcard-information__currency"],[1,"mbo-creditcard-information__currency__label"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"caption-medium"],[1,"mbo-creditcard-information__currency__amount"],["class","mbo-creditcard-information__currency",4,"ngIf"],["bocc-button","flat","prefixIcon","chat-info",3,"click"],[1,"bocc-divider"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"usd","caption-medium"]],template:function(u,s){1&u&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275listener("click",function(){return s.onHeader()}),n.\u0275\u0275elementStart(2,"label",2),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"span",4),n.\u0275\u0275text(6),n.\u0275\u0275pipe(7,"boccCurrencyCop"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(8,"bocc-icon",5),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(9,"div",6)(10,"div",7)(11,"div",8),n.\u0275\u0275element(12,"img",9),n.\u0275\u0275elementStart(13,"span",10),n.\u0275\u0275text(14,"Deuda en pesos"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(15,"div",11)(16,"span",10),n.\u0275\u0275text(17),n.\u0275\u0275pipe(18,"boccCurrencyCop"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275template(19,M,12,9,"div",12),n.\u0275\u0275elementStart(20,"button",13),n.\u0275\u0275listener("click",function(){return s.onInformation()}),n.\u0275\u0275elementStart(21,"span"),n.\u0275\u0275text(22),n.\u0275\u0275elementEnd()(),n.\u0275\u0275element(23,"div",14),n.\u0275\u0275elementEnd()()),2&u&&(n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate(null==s.currency?null:s.currency.label),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(7,7,null==s.currency?null:s.currency.amountTotal.toString(),!1)," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("icon",s.condense?"list-open":"list-close"),n.\u0275\u0275advance(1),n.\u0275\u0275property("hidden",s.condense),n.\u0275\u0275advance(8),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(18,10,null==s.currency?null:s.currency.copTotal.toString(),!1)," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",s.canUsd),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate(s.title))},dependencies:[d.NgIf,P.Z,g.P,I.T,A.f],styles:["/*!\n * MBO CreditCardInformation Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Mar/2023\n * Updated: 08/Jul/2024\n*/mbo-creditcard-information{position:relative;width:100%;display:block}mbo-creditcard-information .mbo-creditcard-information__content{position:relative;width:100%}mbo-creditcard-information .mbo-creditcard-information__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header>label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__header__amount{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header__amount>span{width:100%;text-align:right}mbo-creditcard-information .mbo-creditcard-information__header__amount>bocc-icon{color:var(--color-blue-700)}mbo-creditcard-information .mbo-creditcard-information__body{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;display:flex;width:100%;margin-top:var(--sizing-x6);flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button{width:100%}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label img{margin-right:var(--sizing-x4);width:var(--sizing-x8);height:var(--sizing-x8)}mbo-creditcard-information .mbo-creditcard-information__currency__label span{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency__amount{padding-right:var(--sizing-x6);box-sizing:border-box;margin:auto 0rem;display:flex;flex-direction:column}mbo-creditcard-information .mbo-creditcard-information__currency__amount span{color:var(--color-carbon-lighter-700);text-align:right}mbo-creditcard-information .mbo-creditcard-information__currency__amount span.usd{color:var(--color-amathyst-700)}\n"],encapsulation:2}),l})()},33022:(N,x,t)=>{t.d(x,{H:()=>u});var e=t(99877),n=t(39904),d=(t(10954),t(17007)),P=t(13462),I=t(45542),A=t(92275),M=t(55944),o=t(19102);function l(s,E){if(1&s){const y=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"div",15)(2,"label",16),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"span",17),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div")(7,"bocc-badge"),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(9,"div",18)(10,"button",19),e.\u0275\u0275listener("click",function(){const L=e.\u0275\u0275restoreView(y).$implicit,m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.onSelect(L))}),e.\u0275\u0275text(11," Ver recibo "),e.\u0275\u0275elementEnd()()()}if(2&s){const y=E.$implicit,p=E.index,R=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("PAGO ",p+1,""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(R.getPayLabel(y)),e.\u0275\u0275advance(2),e.\u0275\u0275attribute("bocc-theme",R.getPayColor(y)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",R.getPayStatus(y)," ")}}var b=(()=>{return(s=b||(b={}))[s.FAILURE=0]="FAILURE",s[s.INCOMPLETE=1]="INCOMPLETE",s[s.SUCCESS=2]="SUCCESS",b;var s})();let u=(()=>{class s{constructor(){this.status=[],this.select=new e.EventEmitter}get state(){return this.status.reduce((y,{isError:p})=>p?y:y+1,0)}get title(){switch(this.state){case b.SUCCESS:return"\xa1Pagos exitosos!";case b.INCOMPLETE:return"\xa1Pagos parcialmente exitosos!";default:return"\xa1Pagos fallidos!"}}get animation(){switch(this.state){case b.SUCCESS:case b.INCOMPLETE:return n.F6;default:return n.cj}}getPayLabel(y){return"USD"===y.currency?.code?"Pago en d\xf3lares":"Pago en pesos"}getPayColor(y){return y.isError?"danger":"success"}getPayStatus(y){return y.isError?"Fallido":"Exitoso"}onSelect(y){this.select.emit(y)}}return s.\u0275fac=function(y){return new(y||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-creditcard-payments-card"]],inputs:{payment:"payment",status:"status"},outputs:{select:"select"},decls:17,vars:5,consts:[[1,"mbo-creditcard-payments-card__content"],[1,"mbo-creditcard-payments-card__header"],[3,"result"],[1,"mbo-creditcard-payments-card__status"],[3,"options"],[1,"mbo-creditcard-payments-card__subheader"],[1,"subtitle2-medium"],[1,"body2-medium"],[1,"bocc-divider"],[1,"mbo-creditcard-payments-card__body"],["class","mbo-creditcard-payments-card__payment",4,"ngFor","ngForOf"],[1,"mbo-creditcard-payments-card__amount"],[1,"smalltext-bold"],[3,"amount"],[1,"mbo-creditcard-payments-card__payment"],[1,"mbo-creditcard-payments-card__payment__content"],[1,"caption-medium"],[1,"smalltext-medium"],[1,"mbo-creditcard-payments-card__payment__action"],["bocc-button","flat","prefixIcon","extract-receive",3,"click"]],template:function(y,p){1&y&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"mbo-bank-logo",2),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275element(4,"ng-lottie",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"label",6),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"span",7),e.\u0275\u0275text(9,"Revis\xe1 los detalles de los pagos."),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(10,"div",8),e.\u0275\u0275elementStart(11,"div",9),e.\u0275\u0275template(12,l,12,4,"div",10),e.\u0275\u0275elementStart(13,"div",11)(14,"label",12),e.\u0275\u0275text(15,"TOTAL PAGADO"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(16,"bocc-amount",13),e.\u0275\u0275elementEnd()()()),2&y&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("result",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("options",p.animation),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(p.title),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",p.status),e.\u0275\u0275advance(4),e.\u0275\u0275property("amount",null==p.payment?null:p.payment.totalAmount))},dependencies:[d.NgForOf,P.LottieComponent,I.P,A.O,M.Q,o.r],styles:["/*!\n * MBO CreditCardPaymentsCard Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 09/Feb/2024\n * Updated: 08/Jul/2024\n*/mbo-creditcard-payments-card{position:relative;display:block;padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x8);box-shadow:var(--z-bottom-lighter-8);background:var(--color-carbon-lighter-50)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__header{position:relative;display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>label{color:var(--color-carbon-darker-1000);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment{position:relative;display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>label{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>span{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__action{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount label{color:var(--color-carbon-lighter-400);text-align:right}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount bocc-amount{text-align:right}\n"],encapsulation:2}),s})()},50773:(N,x,t)=>{t.d(x,{n:()=>o}),t(57544),t(10954);var i=t(99877),v=t(17007),g=t(80349),I=t(63111),A=t(3235);function M(l,b){if(1&l&&(i.\u0275\u0275elementStart(0,"div",6),i.\u0275\u0275element(1,"mbo-card-currency-amount",7),i.\u0275\u0275elementEnd()),2&l){const u=i.\u0275\u0275nextContext();i.\u0275\u0275classProp("mbo-creditcard-radiobutton__footer--disabled",u.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("copAmount",null==u.value?null:u.value.copTotal)("copUsdAmount",null==u.value?null:u.value.copUsdTotal)("usdAmount",null==u.value?null:u.value.usdTotal)}}let o=(()=>{class l{constructor(){this.disabled=!1,this.hasFooter=!1,this.skeleton=!1}get checked(){return this.radioControl&&this.radioControl.value===this.value}onComponent(){this.disabled||this.radioControl?.setValue(this.value)}}return l.\u0275fac=function(u){return new(u||l)},l.\u0275cmp=i.\u0275\u0275defineComponent({type:l,selectors:[["mbo-creditcard-radiobutton"]],inputs:{radioControl:"radioControl",value:"value",theme:"theme",disabled:"disabled",hasFooter:"hasFooter",skeleton:"skeleton"},decls:9,vars:10,consts:[[1,"mbo-creditcard-radiobutton__content",3,"click"],[3,"checked"],[1,"mbo-creditcard-radiobutton__body"],[1,"mbo-creditcard-radiobutton__label","body2-medium"],[1,"mbo-creditcard-radiobutton__amount","subtitle1-medium"],["class","mbo-creditcard-radiobutton__footer",3,"mbo-creditcard-radiobutton__footer--disabled",4,"ngIf"],[1,"mbo-creditcard-radiobutton__footer"],[3,"copAmount","copUsdAmount","usdAmount"]],template:function(u,s){1&u&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275listener("click",function(){return s.onComponent()}),i.\u0275\u0275element(1,"bocc-radiobutton",1),i.\u0275\u0275elementStart(2,"div",2)(3,"span",3),i.\u0275\u0275text(4),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"span",4),i.\u0275\u0275text(6),i.\u0275\u0275pipe(7,"boccCurrency"),i.\u0275\u0275elementEnd()()(),i.\u0275\u0275template(8,M,2,5,"div",5)),2&u&&(i.\u0275\u0275classProp("mbo-creditcard-radiobutton__content--disabled",s.disabled),i.\u0275\u0275advance(1),i.\u0275\u0275property("checked",s.checked),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",null==s.value?null:s.value.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",i.\u0275\u0275pipeBind3(7,6,null==s.value?null:s.value.amountTotal.toString(),"$",!1)," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!s.hasFooter))},dependencies:[v.NgIf,g.V,I.m,A.T],styles:["/*!\n * MBO CreditCardRadiobutton Component\n * v2.2.0\n * Author: MB Frontend Developers\n * Created: 17/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x8);--pvt-body-padding: var(--sizing-x8);--pvt-amount-padding: 0rem var(--sizing-x2);position:relative;display:block;width:100%;box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content--disabled{opacity:.5;pointer-events:none}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer{position:relative;display:flex;width:100%;padding:var(--pvt-body-padding);box-sizing:border-box;border-top:var(--border-1-lighter-300);background:var(--overlay-lgrey-40)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer--disabled{pointer-events:none;opacity:.5}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer .mbo-card-currency-amount__content{padding:var(--pvt-amount-padding)}@media screen and (max-width: 320px){mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x6);--pvt-body-padding: var(--sizing-x6)}}\n"],encapsulation:2}),l})()},11747:(N,x,t)=>{t.d(x,{PF:()=>d,Hf:()=>b,xm:()=>l,kz:()=>s,Cq:()=>L}),t(63111);var C=t(17007),n=t(83651),i=t(99877);let d=(()=>{class m{}return m.\u0275fac=function(T){return new(T||m)},m.\u0275mod=i.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=i.\u0275\u0275defineInjector({imports:[C.CommonModule,n.P6]}),m})();t(2297),t(33022);var g=t(79798),I=t(30263),A=t(44487),M=t.n(A),o=t(13462);let l=(()=>{class m{}return m.\u0275fac=function(T){return new(T||m)},m.\u0275mod=i.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=i.\u0275\u0275defineInjector({imports:[C.CommonModule,o.LottieModule.forRoot({player:()=>M()}),I.P8,I.Oh,I.Qg,g.rw]}),m})(),b=(()=>{class m{}return m.\u0275fac=function(T){return new(T||m)},m.\u0275mod=i.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=i.\u0275\u0275defineInjector({imports:[C.CommonModule,I.Zl,I.P8,I.oc,n.P6]}),m})();t(50773);let s=(()=>{class m{}return m.\u0275fac=function(T){return new(T||m)},m.\u0275mod=i.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=i.\u0275\u0275defineInjector({imports:[C.CommonModule,I.Dj,I.V6,I.Qg,n.P6,d]}),m})();t(57544);let L=(()=>{class m{}return m.\u0275fac=function(T){return new(T||m)},m.\u0275mod=i.\u0275\u0275defineNgModule({type:m}),m.\u0275inj=i.\u0275\u0275defineInjector({imports:[C.CommonModule]}),m})()},26123:(N,x,t)=>{t.r(x),t.d(x,{MboPaymentCreditCardConfirmationPageModule:()=>K});var e=t(17007),C=t(78007),n=t(79798),i=t(30263),d=t(83651),v=t(11747),P=t(15861),g=t(39904),I=t(95437),A=t(96381),M=t(55351),o=t(99877),l=t(10464),b=t(48774),u=t(17941),s=t(45542),E=t(63111);function y(U,F){if(1&U&&(o.\u0275\u0275elementStart(0,"div",13),o.\u0275\u0275element(1,"bocc-card-summary",14)(2,"mbo-card-currency-amount",15),o.\u0275\u0275elementEnd()),2&U){const f=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("amount",null==f.payment?null:f.payment.totalAmount)("actions",f.amountActions),o.\u0275\u0275advance(1),o.\u0275\u0275property("copAmount",null==f.payment?null:f.payment.copAmount)("copUsdAmount",null==f.payment?null:f.payment.copUsdAmount)("usdAmount",null==f.payment?null:f.payment.usdAmount)}}function p(U,F){if(1&U&&o.\u0275\u0275element(0,"bocc-card-summary",16),2&U){const f=o.\u0275\u0275nextContext();o.\u0275\u0275property("amount",null==f.payment?null:f.payment.totalAmount)("actions",f.amountActions)("skeleton",!f.payment)}}const{AMOUNT:R,DESTINATION:L,RESULT:m,SELECT_AMOUNT:O}=g.Z6.PAYMENTS.CREDIT_CARD;let T=(()=>{class U{constructor(f,h,j){this.mboProvider=f,this.requestConfiguration=h,this.cancelProvider=j,this.canMultipleCurrency=!1,this.hasUsd=!1,this.backAction={id:"btn_payment-creditcard-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(this.canMultipleCurrency?O:R)}},this.cancelAction={id:"btn_payment-creditcard-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_payment-creditcard-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(L)}}],this.amountActions=[{id:"btn_payment-creditcard-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(this.canMultipleCurrency?O:R)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(m)}initializatedConfiguration(){var f=this;return(0,P.Z)(function*(){(yield f.requestConfiguration.confirmation()).when({success:({payment:h})=>{const{destination:j}=h,{isRequiredInformation:w,isProtected:W}=j;f.hasUsd=j.hasCurrency(g.qB)&&!W,f.canMultipleCurrency=w&&!W,f.payment=h}})})()}}return U.\u0275fac=function(f){return new(f||U)(o.\u0275\u0275directiveInject(I.ZL),o.\u0275\u0275directiveInject(A.P),o.\u0275\u0275directiveInject(M.t))},U.\u0275cmp=o.\u0275\u0275defineComponent({type:U,selectors:[["mbo-payment-creditcard-confirmation-page"]],decls:17,vars:12,consts:[[1,"mbo-payment-creditcard-confirmation-page__content","mbo-page__scroller"],[1,"mbo-payment-creditcard-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-payment-creditcard-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions","skeleton"],["header","ORIGEN",3,"title","subtitle","skeleton"],["class","mbo-payment-creditcard-confirmation-page__amount",4,"ngIf"],["header","VALOR",3,"amount","actions","skeleton",4,"ngIf"],[1,"mbo-payment-creditcard-confirmation-page__footer"],["id","btn_payment-creditcard-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"click"],[1,"mbo-payment-creditcard-confirmation-page__amount"],["header","VALOR",3,"amount","actions"],[3,"copAmount","copUsdAmount","usdAmount"],["header","VALOR",3,"amount","actions","skeleton"]],template:function(f,h){1&f&&(o.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),o.\u0275\u0275element(3,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),o.\u0275\u0275text(7,"\xbfDeseas pagar a?"),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(8,"div",6),o.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8),o.\u0275\u0275template(11,y,3,5,"div",9),o.\u0275\u0275template(12,p,1,3,"bocc-card-summary",10),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(13,"div",11)(14,"button",12),o.\u0275\u0275listener("click",function(){return h.onSubmit()}),o.\u0275\u0275elementStart(15,"span"),o.\u0275\u0275text(16,"Pagar"),o.\u0275\u0275elementEnd()()()()),2&f&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("leftAction",h.backAction)("rightAction",h.cancelAction),o.\u0275\u0275advance(6),o.\u0275\u0275property("title",null==h.payment||null==h.payment.destination?null:h.payment.destination.nickname)("subtitle",null==h.payment||null==h.payment.destination?null:h.payment.destination.publicNumber)("detail",null==h.payment||null==h.payment.destination?null:h.payment.destination.name)("actions",h.destinationActions)("skeleton",!h.payment),o.\u0275\u0275advance(1),o.\u0275\u0275property("title",null==h.payment||null==h.payment.source?null:h.payment.source.nickname)("subtitle",null==h.payment||null==h.payment.source?null:h.payment.source.number)("skeleton",!h.payment),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",h.payment&&h.hasUsd),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!h.hasUsd))},dependencies:[e.NgIf,l.K,b.J,u.D,s.P,E.m],styles:["/*!\n * MBO PaymentCreditCardConfirmation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 26/Jul/2022\n * Updated: 10/Feb/2024\n*/mbo-payment-creditcard-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-creditcard-confirmation-page .mbo-payment-creditcard-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-confirmation-page .mbo-payment-creditcard-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-creditcard-confirmation-page .mbo-payment-creditcard-confirmation-page__amount{padding:var(--sizing-x4);box-sizing:border-box;background:var(--overlay-lgrey-60);border-radius:var(--sizing-x4)}mbo-payment-creditcard-confirmation-page .mbo-payment-creditcard-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-payment-creditcard-confirmation-page .mbo-payment-creditcard-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),U})(),K=(()=>{class U{}return U.\u0275fac=function(f){return new(f||U)},U.\u0275mod=o.\u0275\u0275defineNgModule({type:U}),U.\u0275inj=o.\u0275\u0275defineInjector({imports:[e.CommonModule,C.RouterModule.forChild([{path:"",component:T}]),n.KI,i.Jx,i.DM,i.B4,i.Dj,d.P6,i.P8,v.PF]}),U})()},63674:(N,x,t)=>{t.d(x,{Eg:()=>g,Lo:()=>i,Wl:()=>d,ZC:()=>v,_f:()=>C,br:()=>P,tl:()=>n});var e=t(29306);const C={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},n=new e.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),i={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},v={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},g={color:"info",key:"programmed",label:"Programado"}},66067:(N,x,t)=>{t.d(x,{S6:()=>I,T2:()=>P,UQ:()=>A,mZ:()=>g});var e=t(39904),C=t(6472),i=t(63674),d=t(31707);class P{constructor(o,l,b,u,s,E,y,p,R,L,m){this.id=o,this.type=l,this.name=b,this.nickname=u,this.number=s,this.bank=E,this.isAval=y,this.isProtected=p,this.isOwner=R,this.ownerName=L,this.ownerDocument=m,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[e.y1],this.initialsName=(0,C.initials)(u),this.shortNumber=s.substring(s.length-4),this.descriptionNumber=`${b} ${s}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:E.logo,light:E.logo,standard:E.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(o){this.informationValue||(this.informationValue=o)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(o){return this.currenciesValue.includes(o)}}class g{constructor(o,l){this.id=o,this.type=l}}class I{constructor(o,l,b,u,s,E,y,p,R,L,m,O){this.uuid=o,this.number=l,this.nie=b,this.nickname=u,this.companyId=s,this.companyName=E,this.amount=y,this.registerDate=p,this.expirationDate=R,this.paid=L,this.statusCode=m,this.references=O,this.recurring=O.length>0,this.status=function v(M){switch(M){case d.U.EXPIRED:return i.ZC;case d.U.PENDING:return i.Wl;case d.U.PROGRAMMED:return i.Eg;case d.U.RECURRING:return i.br;default:return i.Lo}}(m)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class A{constructor(o,l,b,u,s,E,y,p){this.uuid=o,this.number=l,this.nickname=b,this.companyId=u,this.companyName=s,this.city=E,this.amount=y,this.isBiller=p}}},31707:(N,x,t)=>{t.d(x,{U:()=>e,f:()=>C});var e=(()=>{return(n=e||(e={})).RECURRING="1",n.EXPIRED="2",n.PENDING="3",n.PROGRAMMED="4",e;var n})(),C=(()=>{return(n=C||(C={})).BILLER="Servicio",n.NON_BILLER="Servicio",n.PSE="Servicio",n.TAX="Impuesto",n.LOAN="Obligaci\xf3n financiera",n.CREDIT_CARD="Obligaci\xf3n financiera",C;var n})()}}]);