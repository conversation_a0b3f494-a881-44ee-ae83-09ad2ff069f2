(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5334],{55334:(C,d,o)=>{o.r(d),o.d(d,{MboCustomerSecurityModule:()=>u});var l=o(17007),M=o(78007),n=o(99877);const h=[{path:"",loadChildren:()=>o.e(2974).then(o.bind(o,72974)).then(t=>t.MboCustomerSecurityPageModule)},{path:"change-password",loadChildren:()=>o.e(1023).then(o.bind(o,61023)).then(t=>t.MboSecurityChangePasswordPageModule)},{path:"biometric",loadChildren:()=>o.e(2654).then(o.bind(o,42654)).then(t=>t.MboSecurityBiometricPageModule)},{path:"biometric-success",loadChildren:()=>o.e(2024).then(o.bind(o,12024)).then(t=>t.MboSecurityBiometricSuccessPageModule)},{path:"activate-products",loadChildren:()=>o.e(8538).then(o.bind(o,48538)).then(t=>t.MboSecurityActivateProductsModule)},{path:"traveler",loadChildren:()=>o.e(6782).then(o.bind(o,16782)).then(t=>t.MboSecurityTravelerModule)}];let u=(()=>{class t{}return t.\u0275fac=function(s){return new(s||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,M.RouterModule.forChild(h)]}),t})()}}]);