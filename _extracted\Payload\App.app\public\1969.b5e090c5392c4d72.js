(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1969],{88844:(K,x,n)=>{n.d(x,{YI:()=>O,tc:()=>P,iR:()=>u,jq:()=>A,Hv:()=>S,S6:()=>R,E2:()=>i,V4:()=>g,wp:()=>Z,CE:()=>w,YQ:()=>M,ND:()=>f,t1:()=>L});var T=n(6472);class D{constructor(s){this.value=s}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:s}){return this.value.id===s}filtrable(s){return(0,T.hasPattern)(this.value.name,s)}}function M(h){return h.map(s=>new D(s))}class c{constructor(s){this.currency=s}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(s){return this.currency.code===s?.code}filtrable(s){return!0}}function f(h){return h.map(s=>new c(s))}var m=n(39904);class S{constructor(s){this.value=s}get title(){return this.value.label}get description(){return this.value.label}compareTo(s){return this.value.reference===s.reference}filtrable(s){return!0}}const O=m.Bf.map(h=>new S(h));class v{constructor(s,H){this.value=s,this.title=this.value.label,this.description=H?this.value.code:this.value.label}compareTo(s){return this.value===s}filtrable(s){return!0}}const A=new v(m.Gd),R=new v(m.XU),E=new v(m.t$),i=new v(m.j1),g=new v(m.k7),u=[A,R,E,i],P=[new v(m.Gd,!0),new v(m.XU,!0),new v(m.t$,!0),new v(m.j1,!0)];class C{constructor(s){this.product=s}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(s){return this.product.id===s?.id}filtrable(s){return!0}}function L(h){return h.map(s=>new C(s))}var p=n(89148);class b{constructor(s){this.filter=s}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(s){return this.value===s}filtrable(s){return!0}}const w=new b({label:"Todos los productos",short:"Todos",value:p.Gt.None}),j=new b({label:"Cuentas de ahorro",short:"Ahorros",value:p.Gt.SavingAccount}),e=new b({label:"Cuentas corriente",short:"Corrientes",value:p.Gt.CheckingAccount}),U=new b({label:"Depositos electr\xf3nicos",short:"Depositos",value:p.Gt.ElectronicDeposit}),G=new b({label:"Cuentas AFC",short:"AFC",value:p.Gt.AfcAccount}),N=new b({label:"Tarjetas de cr\xe9dito",short:"TC",value:p.Gt.CreditCard}),_=new b({label:"Inversiones",short:"Inversiones",value:p.Gt.CdtAccount}),$=new b({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:p.Gt.Loan}),z=new b({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:p.Gt.ResolvingCredit}),V=new b({label:"Productos Aval",short:"Aval",value:p.Gt.Aval}),B=new b({label:"Productos fiduciarios",short:"Fiducias",value:p.Gt.Trustfund}),y=new b({label:"Otros productos",short:"Otros",value:p.Gt.None}),Z={SDA:j,DDA:e,EDA:U,AFC:G,CCA:N,CDA:_,DLA:$,LOC:z,AVAL:V,80:B,MDA:y,NONE:y,SBA:y,VDA:y}},21260:(K,x,n)=>{n.d(x,{$:()=>R,L:()=>E});var T=n(30263),D=n(39904),M=n(75652),c=n(99877),f=n(45542),m=n(35324);let S=(()=>{class i{constructor(u){this.mboProvider=u}ngBoccPortal(u){this.portal=u}onLocation(){this.mboProvider.openUrl(D.BA.GEOLOCATION)}onClose(){this.portal?.close()}}return i.\u0275fac=function(u){return new(u||i)(c.\u0275\u0275directiveInject(M.Z))},i.\u0275cmp=c.\u0275\u0275defineComponent({type:i,selectors:[["mbo-document-error-modal"]],decls:12,vars:1,consts:[[1,"mbo-document-error-modal"],["src","assets/shared/logos/document-error.svg"],[1,"mbo-document-error-modal__body"],[1,"smalltext-medium"],[1,"body2-medium"],[3,"whatsapp"],[1,"mbo-document-error-modal__footer"],["id","btn_statement-error-modal_close","bocc-button","raised",3,"click"]],template:function(u,P){1&u&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275element(1,"img",1),c.\u0275\u0275elementStart(2,"div",2)(3,"label",3),c.\u0275\u0275text(4," DOCUMENTO NO DISPONIBLE "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"p",4),c.\u0275\u0275text(6," Puedes solicitarlo mediante nuestro canal de Whatsapp o en cualquiera de nuestras l\xedneas de atenci\xf3n "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275element(7,"mbo-attention-lines-form",5),c.\u0275\u0275elementStart(8,"div",6)(9,"button",7),c.\u0275\u0275listener("click",function(){return P.onClose()}),c.\u0275\u0275elementStart(10,"span"),c.\u0275\u0275text(11,"Entendido"),c.\u0275\u0275elementEnd()()()()),2&u&&(c.\u0275\u0275advance(7),c.\u0275\u0275property("whatsapp",!0))},dependencies:[f.P,m.V],styles:["mbo-document-error-modal{--pvt-img-width: 44rem;--pvt-row-spacing: var(--sizing-x12)}mbo-document-error-modal .mbo-document-error-modal{display:flex;flex-direction:column;row-gap:var(--pvt-row-spacing)}mbo-document-error-modal .mbo-document-error-modal img{align-self:center;width:var(--pvt-img-width)}mbo-document-error-modal .mbo-document-error-modal__body{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-document-error-modal .mbo-document-error-modal__body label{text-align:center}mbo-document-error-modal .mbo-document-error-modal__body p{color:var(--color-carbon-lighter-700);text-align:center}mbo-document-error-modal .mbo-document-error-modal__location{width:auto;align-self:center}mbo-document-error-modal .mbo-document-error-modal__footer{padding:var(--sizing-x4);box-sizing:border-box;border-top:1px solid var(--color-carbon-lighter-400)}mbo-document-error-modal .mbo-document-error-modal__footer button{width:100%}@media screen and (max-height: 600px){mbo-document-error-modal{--pvt-img-width: 40rem;--pvt-row-spacing: var(--sizing-x8)}}\n"],encapsulation:2}),i})();n(17007),n(79798);let R=(()=>{class i{constructor(u){this.errorModal=u.create(S)}execute(){this.errorModal.open()}}return i.\u0275fac=function(u){return new(u||i)(c.\u0275\u0275inject(T.iM))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),E=(()=>{class i{constructor(u,P){this.modalConfirmation=u,this.mboProvider=P}execute(){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/search-result-none.svg",title:"SIN PRODUCTOS FIDUCIARIOS",description:"\xa1Tranquilo! Est\xe1s a un clic de empezar a cumplir tus metas",message:'Da clic en "Quiero invertir" y crea tu inversi\xf3n',accept:{label:"Quiero invertir",click:()=>{this.mboProvider.openUrl(D.BA.TRUSTFUND)}},decline:{label:"Cancelar"}})}}return i.\u0275fac=function(u){return new(u||i)(c.\u0275\u0275inject(T.$e),c.\u0275\u0275inject(M.Z))},i.\u0275prov=c.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},91969:(K,x,n)=>{n.r(x),n.d(x,{MboProductStatementPageModule:()=>nt});var T=n(17007),D=n(78007),M=n(79798),c=n(30263),f=n(15861),m=n(39904),S=n(88844),O=n(95437),v=n(21260);class A{constructor(l){this.period=l}get value(){return this.period}get title(){return this.period.description}get description(){return this.period.description}compareTo(l){return this.period.description===l?.description}filtrable(l){return!0}}var E=n(89148),i=n(3372),g=n(38165),u=n(87903),P=n(87956),C=n(98699),L=n(71776),p=n(42168),b=n(84757);class w{constructor(l,t,r){this.id=l,this.name=t,this.description=r}}class j{constructor(l,t){this.type=l,this.data=t}}var e=n(99877);let N=(()=>{class o{constructor(t){this.http=t}periods(t,r){return(0,p.firstValueFrom)(this.http.get(`${m.bV.PRODUCTS.PERIODS}/${t.type}/${t.id}`,{params:{acctCur:r?.code||"COP"}}).pipe((0,b.map)(a=>a.map(d=>function U(o){return new w(o.fileId,o.fileName,o.fileDesc)}(d))),(0,b.catchError)(a=>{if(400===a.status)return(0,p.of)([]);throw a})))}file(t,r){return(0,p.firstValueFrom)(this.http.get(`${m.bV.PRODUCTS.STATEMENTS}/${t.type}/${t.id}`,{params:{file_desc:r.description,file_id:r.id,file_name:r.name}}).pipe((0,b.map)(a=>function G(o){return new j(o.documentType,o.binData)}(a))))}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275inject(L.HttpClient))},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();const _=[E.Gt.SavingAccount,E.Gt.CheckingAccount,E.Gt.CreditCard,E.Gt.Loan,E.Gt.ResolvingCredit],$={SDA:"ahorros",DDA:"corrientes",CCA:"creditcard",DLA:"loan",LOC:"loan"};let z=(()=>{class o{constructor(t,r){this.customerProducts=t,this.statementRepository=r}requestProduct(t){var r=this;return(0,f.Z)(function*(){try{const a=yield r.customerProducts.requestProducts(_),d=a.find(({id:I})=>I===t);return C.Either.success({products:a,product:d})}catch({message:a}){return C.Either.failure({message:a})}})()}requestPeriods(t,r){var a=this;return(0,f.Z)(function*(){try{const[d]=r?[r]:t.currencies,I=yield a.statementRepository.periods(t,d);return C.Either.success({currency:d,periods:I})}catch({message:d}){return C.Either.failure({message:d,value:t})}})()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275inject(P.hM),e.\u0275\u0275inject(N))},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),V=(()=>{class o{constructor(t,r,a){this.repository=t,this.preferencesService=r,this.fileManagerService=a}execute(t,r){var a=this;return(0,f.Z)(function*(){try{const{data:d}=yield a.repository.file(t,r),I=$[t.type]||"product",F=r.description.replace(/[^a-zA-Z0-9]/g,"").toLowerCase().trim(),Y=`${t.shortNumber}-${I}-${F}-${(0,u.CW)()}.pdf`,st=yield a.preferencesService.requestBoolean(i.M.ProductUniqueStatement),{status:ct}=st?yield a.fileManagerService.downloadPdfForUrl({name:Y,url:d}):yield a.fileManagerService.downloadPdf({name:Y,base64:d});return ct===g.K.Saved?C.Either.success():C.Either.failure({message:"Lo sentimos, ocurrio un error al tratar de guardar extracto en dispositivo, por favor int\xe9ntelo de nuevo.",value:!0})}catch({message:d}){return C.Either.failure({message:d,value:!1})}})()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275inject(N),e.\u0275\u0275inject(P.yW),e.\u0275\u0275inject(P.j5))},o.\u0275prov=e.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var B=n(24495),y=n(57544);class Z extends y.FormGroup{constructor(){super({controls:{product:new y.FormControl,currency:new y.FormControl,period:new y.FormControl(void 0,[B.C1])}})}get product(){return this.controls.product}get currency(){return this.controls.currency}get period(){return this.controls.period}}var h=n(48774),s=n(83413),H=n(45542),Q=n(48030),W=n(10464),J=n(50689);function X(o,l){if(1&o&&e.\u0275\u0275element(0,"bocc-card-product-summary",11),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("icon",null==t.product?null:t.product.logo)("title",null==t.product?null:t.product.nickname)("number",null==t.product?null:t.product.publicNumber)("subtitle",null==t.product?null:t.product.bank.name)("color",null==t.product?null:t.product.color)}}function k(o,l){if(1&o&&e.\u0275\u0275element(0,"bocc-select-box",12),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",t.requesting||!t.products.length)("suggestions",t.products)("formControl",t.controls.product)}}function q(o,l){if(1&o&&e.\u0275\u0275element(0,"bocc-select-box",13),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",t.requesting)("suggestions",t.currencies)("formControl",t.controls.currency)}}function tt(o,l){if(1&o&&(e.\u0275\u0275elementStart(0,"mbo-message-empty"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.msgEmpty," ")}}const{CERTIFICATIONS:et,PRODUCTS:ot}=m.Z6.CUSTOMER;let rt=(()=>{class o{constructor(t,r,a,d,I){this.activateRoute=t,this.mboProvider=r,this.documentErrorModal=a,this.requestStatement=d,this.downloadStatement=I,this.unsubscriptions=[],this.requesting=!0,this.products=[],this.currencies=[],this.backAction={id:"btn_product-statement_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{const{productId:F}=this.activateRoute.snapshot.queryParams;F?this.mboProvider.navigation.back(ot.INFO,{productId:F}):this.mboProvider.navigation.back(et.HOME)}},this.controls=new Z}ngOnInit(){this.unsubscriptions.push(this.controls.product.subscribe(t=>{this.product||this.setCurriencies(t)})),this.unsubscriptions.push(this.controls.currency.subscribe(t=>{t&&this.requestPeriods(t)})),this.initializatedConfiguration()}ngOnDestroy(){this.unsubscriptions.forEach(t=>t())}get canCurrencies(){return this.currencies.length>1}get msgEmptyVisible(){return!(this.requesting||this.products.length&&(!this.product&&!this.controls.product.value||0!==this.periods?.length))}get msgEmpty(){return this.products.length?"Lo sentimos, este producto no cuenta con periodos disponibles para generar extractos":"Lo sentimos, no pudimos consultar tus productos para generar extractos. Por favor intente m\xe1s tarde"}onSubmit(){var t=this;return(0,f.Z)(function*(){t.mboProvider.loader.open("Generando extracto, por favor espere...");const r=t.product||t.controls.product.value;(yield t.downloadStatement.execute(r,t.controls.period.value)).when({failure:({message:a,value:d})=>{d?t.mboProvider.toast.warning(a,"Extracto no almacenado"):t.documentErrorModal.execute()}},()=>{t.mboProvider.loader.close()})})()}initializatedConfiguration(){var t=this;return(0,f.Z)(function*(){const{productId:r}=t.activateRoute.snapshot.queryParams;(yield t.requestStatement.requestProduct(r)).when({success:({products:a,product:d})=>{t.products=(0,S.t1)(a),t.product=d,t.controls.product.setValue(d),d&&t.setCurriencies(d)}},()=>{t.requesting=!1})})()}setCurriencies(t){const r=t?.currencies||[];this.currencies=(0,S.ND)(r);const[a]=r;this.controls.currency.setValue(r.length<2?a:void 0)}requestPeriods(t){var r=this;return(0,f.Z)(function*(){r.mboProvider.loader.open("Solicitando periodos, por favor espere..."),r.requesting=!0;const a=r.product||r.controls.product.value;(yield r.requestStatement.requestPeriods(a,t)).when({success:({periods:d})=>{r.periods=function R(o){return o.map(l=>new A(l))}(d),d.length||r.documentErrorModal.execute()}},()=>{r.requesting=!1,r.mboProvider.loader.close()})})()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(D.ActivatedRoute),e.\u0275\u0275directiveInject(O.ZL),e.\u0275\u0275directiveInject(v.$),e.\u0275\u0275directiveInject(z),e.\u0275\u0275directiveInject(V))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-product-statement-page"]],decls:14,vars:9,consts:[[1,"mbo-product-statement-page__content"],[1,"mbo-product-statement-page__header"],["title","Extractos",3,"leftAction"],[1,"mbo-product-statement-page__body"],[3,"icon","title","number","subtitle","color",4,"ngIf"],["elementId","lst_product-statement_product","placeholder","Seleccionar producto","label","Producto",3,"disabled","suggestions","formControl",4,"ngIf"],["elementId","lst_product-statement_currency","placeholder","Seleccionar tipo de deuda","label","Deuda",3,"disabled","suggestions","formControl",4,"ngIf"],["elementId","lst_product-statement_period","placeholder","Seleccionar periodo","label","Periodo",3,"disabled","suggestions","formControl"],[4,"ngIf"],[1,"mbo-product-statement-page__footer"],["id","btn_product-statement_submit","bocc-button","raised","prefixIcon","certificate-products",3,"disabled","click"],[3,"icon","title","number","subtitle","color"],["elementId","lst_product-statement_product","placeholder","Seleccionar producto","label","Producto",3,"disabled","suggestions","formControl"],["elementId","lst_product-statement_currency","placeholder","Seleccionar tipo de deuda","label","Deuda",3,"disabled","suggestions","formControl"]],template:function(t,r){1&t&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3),e.\u0275\u0275template(5,X,1,5,"bocc-card-product-summary",4),e.\u0275\u0275template(6,k,1,3,"bocc-select-box",5),e.\u0275\u0275template(7,q,1,3,"bocc-select-box",6),e.\u0275\u0275element(8,"bocc-select-box",7),e.\u0275\u0275template(9,tt,2,1,"mbo-message-empty",8),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9)(11,"button",10),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13,"Generar extracto"),e.\u0275\u0275elementEnd()()()()),2&t&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",r.backAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",r.product),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!r.product),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",r.canCurrencies),e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",r.requesting||!(null!=r.periods&&r.periods.length))("suggestions",r.periods)("formControl",r.controls.period),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",r.msgEmptyVisible),e.\u0275\u0275advance(2),e.\u0275\u0275property("disabled",r.controls.invalid))},dependencies:[T.NgIf,h.J,s.D,H.P,Q.t,W.K,J.A],styles:["/*!\n * MBO ProductStatement Page\n * v2.1.0\n * Author: MB Frontend Developers\n * Created: 16/Nov/2022\n * Updated: 22/May/2024\n */mbo-product-statement-page{position:relative;width:100%;height:100%;display:block}mbo-product-statement-page .mbo-product-statement-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-statement-page .mbo-product-statement-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);align-items:center;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-product-statement-page .mbo-product-statement-page__title{position:relative;width:100%;text-align:center;color:var(--color-carbon-darker-1000)}mbo-product-statement-page .mbo-product-statement-page__list{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-statement-page .mbo-product-statement-page__list .bocc-card-radiobutton__content{box-shadow:none}mbo-product-statement-page .mbo-product-statement-page__footer{position:sticky;bottom:0rem;padding:var(--sizing-safe-footer-x8);box-sizing:border-box;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);background:var(--overlay-white-80)}mbo-product-statement-page .mbo-product-statement-page__footer button{width:100%}\n"],encapsulation:2}),o})(),nt=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[T.CommonModule,D.RouterModule.forChild([{path:"",component:rt}]),c.Jx,c.D1,c.DO,c.P8,c.tv,M.KI,M.B_,M.Aj]}),o})()}}]);