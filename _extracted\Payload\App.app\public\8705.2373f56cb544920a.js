(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8705],{18705:(h,a,o)=>{o.r(a),o.d(a,{MboTransfersHomeModule:()=>s});var t=o(17007),d=o(78007),l=o(99877);const M=[{path:"",loadChildren:()=>o.e(2479).then(o.bind(o,12479)).then(n=>n.MboTransfersHomePageModule)},{path:"history",loadChildren:()=>o.e(8755).then(o.bind(o,98755)).then(n=>n.MboTransfersHistoryPageModule)},{path:"history/information",loadChildren:()=>o.e(3501).then(o.bind(o,43501)).then(n=>n.MboTransferHistoryInformationPageModule)}];let s=(()=>{class n{}return n.\u0275fac=function(r){return new(r||n)},n.\u0275mod=l.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=l.\u0275\u0275defineInjector({imports:[t.CommonModule,d.RouterModule.forChild(M)]}),n})()}}]);