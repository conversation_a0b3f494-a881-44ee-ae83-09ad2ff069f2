(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8337],{57929:(z,f,i)=>{i.d(f,{Be:()=>h,q3:()=>O,qQ:()=>p});const O=36,p=0,h=[{code:"0002015502010102125802CO5921DIEGO ANDRES CORREDOR49250103RBM0014CO.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124O2dhtQbToI1IP7xYOHkR1SUm0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099360041740013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064310002ES0121DIEGO ANDRES CORREDOR54061200006304C1DE",name:"DIEGO ANDRES CORREDOR",type:"onlyAccount"},{code:"000201550202010211560105802CO5922ALMACEN Y SASTRERIA *******************.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124bAK0LiWIA7a7GGPS3ZTGmmCv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601315238 DUITAMA8223010100014CO.COM.RBM.IVA503001099100104110013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573154033178070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122ALMACEN Y SASTRERIA IN6304EA05",name:"ALMACEN Y SASTRERIA IN",type:"onlyAccount"},{code:"000201550202010211560105802CO5917SACOS AZULES BOCC49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124cJbE3StP8jyiME/aY+8d/Qo80014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601505664 SAN PEDRO8223010100014CO.COM.RBM.IVA503001099353177830013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573108133170070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064270002ES0117SACOS AZULES BOCC6304C439",name:"SACOS AZULES BOCC",type:"onlyAccount"},{code:"00020101021249250014CO.COM.CRB.RED0103CRB50300013CO.COM.CRB.CU01090164845945204581453031705405150005802CO5914Cci*boxBurguer6011BOGOTA D.C.622501031530708000DE40808020080270016CO.COM.CRB.CANAL0103POS81250015CO.COM.CRB.CIVA01020282230014CO.COM.CRB.IVA0101083240015CO.COM.CRB.BASE0101084250015CO.COM.CRB.CINC01020285230014CO.COM.CRB.INC0101090300016CO.COM.CRB.TRXID010600015491260014CO.COM.CRB.SEC0104627c6304df69",name:"CCI BOX BURGER",type:"onlyCards"},{code:"00020155020201021256076750.005802CO5918PRUEBAS QR REDEBAN49250103RBM0014CO.COM.RBM.RED902701035360016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124oC4KvIdTb9ouPsgVLNsLPwj00014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.*********************.000015CO.COM.RBM.BASE62180708SRB0008208020084250102020015CO.COM.RBM.CINC52040004852601040.000014CO.COM.RBM.INC530317064280002ES0118PRUEBAS QR REDEBAN5405450006304FAD5",name:"PRUEBAS QR REDEBAN",type:"cardsAndAccounts"},{code:"00020155020201021256040.005802CO5912COMERCIO POS49250103RBM0014CO.COM.RBM.RED9028010432620016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124HYImT9C9mng/eqME88+mrObw0014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.********************.000015CO.COM.RBM.BASE5125010454220013CO.COM.RBM.CA62180708SRB0068308020084250102020015CO.COM.RBM.CINC52040000852601040.000014CO.COM.RBM.INC530317064220002ES0112COMERCIO POS5405112206304B678",name:"COMERCIO POS",type:"cardsAndAccounts"},{code:"0002010102115802CO5915BOGOTA BICYCLES49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80260102IM0016CO.COM.RBM.CANAL91270105477470014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA60131100100 BOGOT8226010419.00014CO.COM.RBM.IVA50290108165934100013CO.COM.RBM.*****************.COM.RBM.BASE62180708BL01222608020084250102030015CO.COM.RBM.CINC520459418523010100014CO.COM.RBM.INC530317064250002ES0115BOGOTA BICYCLES6304E56D",name:"BOGOTA BICYCLES",type:"cardsAndAccounts"},{code:"000201550202010211560105802CO5922Hierros de occidente m49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124eH1E5X5opSOneQRXjtmvYMIX0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601227099 BOJAYA8223010100014CO.COM.RBM.IVA503001099353192840013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444454070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122Hierros de occidente m63049CC9",name:"HIERROS OCCIDENTE M",type:"onlyAccount"},{code:"000201550202010211560105802CO5914CAMISAS BOGOTA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL914601244nOdGGoa7JhbdMHsdz6/ZTfw0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601550001 VILLAVICE8223010100014CO.COM.RBM.IVA503001099353189710013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444450070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064240002ES0114CAMISAS BOGOTA63049B14",name:"CAMISAS BOGOTA",type:"onlyAccount"},{code:"000201550202010211560105802CO5911LA PLAZUELA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124kLbT32m0FcJ/Ws+o6IsRzz/C0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099236850430013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573215009881070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064210002ES0111LA PLAZUELA63041E06",name:"LA PLAZUELA",type:"onlyAccount"},{code:"000201550202010211560105802CO5912FLORES JUANA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124sz0Z69d6TSQ0H2oXwdNV1JzE0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099233753060013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573124012500070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064220002ES0112FLORES JUANA6304CD95",name:"FLORES JUANA",type:"onlyAccount"},{code:"0002015502010102115802CO5922COMERCIO DE SIEMPRE *******************.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124YOCYxVWUOwVMrGNJJvp2u8Uv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601366001 PEREIRA8223010100014CO.COM.RBM.IVA503001099102030400013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520405118523010100014CO.COM.RBM.INC530317064320002ES0122COMERCIO DE SIEMPRE BC63040D54",name:"COMERCIO DE SIEMPRE BC",type:"onlyAccount"}]},69595:(z,f,i)=>{i.d(f,{tG:()=>Z,bE:()=>G,PY:()=>X});var O=i(15861),p=i(87956),h=i(53113),s=i(98699),b=i(89148),E=i(57929),y=(()=>{return(n=y||(y={})).Static="11",n.Dinamic="12",y;var n})();const{CcaBuyAvailableCop:P}=b.Av;class r{constructor(c){this.name=c}}class Q{constructor(c,e,t,a,o,u,M,B,g,j,F,w){this.reference=c,this.codeQr=e,this.type=t,this.merchant=a,this.baseAmount=o,this.ivaAmount=u,this.incAmount=M,this.tipAmount=B,this.totalAmount=g,this.betweenAccounts=j,this.belongCommerce=F,this.approvalId=w}get codeDinamic(){return this.type===y.Dinamic}get codeStatic(){return this.type===y.Static}}class S{constructor(c,e,t,a,o,u,M,B,g,j,F){this.product=c,this.id=e,this.name=t,this.number=a,this.amountValue=o,this.logo=u,this.icon=M,this.color=B,this.requiredQuotas=g,this.numberQuotas=j,this.type=F,this.shortNumber=a.substring(a.length-4)}get amount(){return this.amountValue}isAvailableAmount(c){return this.amountValue>=c}}class N extends S{constructor(c,e){super(c,e.id,e.name,e.number,c.amount,e.franchise?e.franchise.getLogoContrast(e.color):c.bank.logo,e.franchise?{dark:e.franchise.logo.dark,light:e.franchise.logo.light,standard:e.franchise.getLogoContrast(e.color)}:{dark:c.bank.logo,light:c.bank.logo,standard:c.bank.logo},e.color,!1,E.qQ,"debit")}}class D extends S{constructor(c,e){if(super(c,c.id,c.name,c.publicNumber,c.amount,c.logo,c.icon,c.color,!0,E.q3,"credit"),e){const t=e.getSection(P);this.amountValue=+t?.value||c.amount}}}class T{constructor(c,e,t,a,o){this.invoice=c,this.type=e,this.amount=t,this.source=a,this.quotas=o}}var U=i(71776),R=i(39904),A=i(87903),v=i(42168),C=i(84757),l=i(99877);let m=(()=>{class n{constructor(e,t){this.http=e,this.fingerprintService=t}read(e){var t=this;return(0,v.firstValueFrom)(this.http.post(R.bV.PAYMENTS.QR.READ,{metadata:e}).pipe((0,C.tap)(a=>{if(Object.keys(a).every(u=>!a[u]))throw new Error("Invalid QR")}),(0,C.switchMap)(function(){var a=(0,O.Z)(function*(o){const{acquirerCode:u,merchantCode:M}=o,B=("Redeban"===u||"RBM"===u)&&9===M.length&&"9"===M.charAt(0),g=!B&&(yield t.verifyCommerce(e));return function L(n,c,e,t){return new Q(c.billingNumber||c.trnConsecutiveCode,n,c.qrType,new r(c.merchantName),+c.netTrxAmount,+c.ivaValue,+c.incValue,+c.tipValue,+c.totalTrxAmount,e,t,c.approvalId)}(e,o,B,g)});return function(o){return a.apply(this,arguments)}}())))}verifyCommerce(e){return(0,v.firstValueFrom)(this.http.post(R.bV.PAYMENTS.QR.COMMERCE,{metadata:e}).pipe((0,C.map)(t=>"320"===t?.msgRsHdr?.status?.serverStatusCode)))}send(e){return"card"===e.type?this.sendForQr(e):this.sendForAccount(e)}cancel(e){return(0,v.firstValueFrom)(this.http.post(R.bV.PAYMENTS.QR.CANCEL,{code:e.invoice.codeQr}).pipe((0,C.map)(t=>(0,A.l1)(t,"SUCCESS")))).catch(t=>(0,A.rU)(t))}sendForQr(e){return(0,v.firstValueFrom)(this.http.post(R.bV.PAYMENTS.QR.PAY,function V(n){return{code:n.invoice.codeQr,installments:n.quotas,origin:n.source.id}}(e)).pipe((0,C.map)(t=>(0,A.l1)(t,"SUCCESS")))).catch(t=>(0,A.rU)(t))}sendForAccount(e){var t=this;return(0,O.Z)(function*(){const a=yield t.fingerprintService.getInfo();return(0,v.firstValueFrom)(t.http.post(R.bV.PAYMENTS.QR.PAY_ACCOUNT,function x(n,c){return{code:n.invoice.codeQr,curAmt:{amt:n.amount,curCode:"COP"},deviceAdmin:c,origin:n.source.id}}(e,a)).pipe((0,C.map)(o=>[201,"201"].includes(o.msgRsHdr?.status?.statusCode)?new h.LN("SUCCESS",`Ref: ${o.approvalId}`):"397"===o.msgRsHdr?.status?.additionalStatus?.statusCode?new h.LN("INFO",o.msgRsHdr.status.additionalStatus.statusDesc):(0,A.l1)(o,"SUCCESS")))).catch(o=>(0,A.rU)(o))})()}}return n.\u0275fac=function(e){return new(e||n)(l.\u0275\u0275inject(U.HttpClient),l.\u0275\u0275inject(p.ew))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var d=i(20691);let I=(()=>{class n extends d.Store{constructor(e){super({amount:0,creditCards:[],debitCards:[],products:[],pay:!0,requiredQuotas:!0,type:"card"}),e.subscribes(R.PU,()=>{this.reset()})}setProducts(e){this.reduce(t=>({...t,products:e}))}getProducts(){return this.select(({products:e})=>e)}setCreditCards(e){this.reduce(t=>({...t,creditCards:e}))}addCreditCard(e){const t=this.getCreditCards();t.filter(({id:o})=>o===e.id).length||this.reduce(o=>({...o,creditCards:[...t,e]}))}getCreditCards(){return this.select(({creditCards:e})=>e)}setDebitCards(e){this.reduce(t=>({...t,debitCards:e}))}addDebitCard(e){const t=this.getDebitCards();t.filter(({id:o})=>o===e.id).length||this.reduce(o=>({...o,debitCards:[...t,e]}))}getDebitCards(){return this.select(({debitCards:e})=>e)}setInvoice(e,t=!0){this.reduce(a=>({...a,invoice:e,pay:t}))}getInvoice(){return this.select(({invoice:e})=>e)}setSourceCard(e){this.reduce(t=>({...t,source:e,requiredQuotas:e.requiredQuotas,type:"card"}))}setSourceProduct(e){this.reduce(t=>({...t,source:e,quotas:0,requiredQuotas:!1,type:"account"}))}getSource(){return this.select(({source:e})=>e)}selectForSource(){return this.select(({amount:e,creditCards:t,debitCards:a,invoice:o,products:u,source:M})=>({amount:o.codeDinamic?o.totalAmount:e,creditCards:t,codeStatic:o.codeStatic,debitCards:a,invoice:o,products:u,source:M}))}setQuotas(e){this.reduce(t=>({...t,quotas:e}))}getQuotas(){return this.select(({quotas:e})=>e)}setAmount(e){this.reduce(t=>({...t,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getPay(){return this.select(({pay:e})=>e)}selectForConfirmation(){return this.select(({amount:e,invoice:t,quotas:a,requiredQuotas:o,source:u})=>({amount:e,invoice:t,quotas:a,requiredQuotas:o,source:u}))}}return n.\u0275fac=function(e){return new(e||n)(l.\u0275\u0275inject(p.Yd))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),Z=(()=>{class n{constructor(e,t,a){this.repository=e,this.store=t,this.eventBusService=a}setSourceCard(e){try{const t=this.store.getSource();return t&&t.type!==e.type&&this.store.setQuotas(e.numberQuotas),s.Either.success(this.store.setSourceCard(e))}catch{return s.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setSourceProduct(e){try{return s.Either.success(this.store.setSourceProduct(e))}catch{return s.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setQuotas(e){try{return s.Either.success(this.store.setQuotas(e))}catch({message:t}){return s.Either.failure({message:t})}}setAmount(e){try{return s.Either.success(this.store.setAmount(e))}catch({message:t}){return s.Either.failure({message:t})}}reset(){try{return s.Either.success(this.store.reset())}catch({message:e}){return s.Either.failure({message:e})}}send(){var e=this;return(0,O.Z)(function*(){const t=function q(n){return new T(n.invoice,n.type,n.amount,n.source,n.quotas)}(e.store.currentState),a=yield e.execute(t);return e.eventBusService.emit(a.channel),s.Either.success({paymentQr:t,status:a})})()}execute(e){try{return this.repository.send(e)}catch({message:t}){return Promise.resolve(h.LN.error(t))}}}return n.\u0275fac=function(e){return new(e||n)(l.\u0275\u0275inject(m),l.\u0275\u0275inject(I),l.\u0275\u0275inject(p.Yd))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),G=(()=>{class n{constructor(e){this.store=e}source(){var e=this;return(0,O.Z)(function*(){try{return s.Either.success(e.store.selectForSource())}catch({message:t}){return s.Either.failure({message:t})}})()}cancel(){var e=this;return(0,O.Z)(function*(){try{const t=e.store.getInvoice();return s.Either.success({invoice:t})}catch({message:t}){return s.Either.failure({message:t})}})()}confirmation(){var e=this;return(0,O.Z)(function*(){try{return s.Either.success(e.store.selectForConfirmation())}catch({message:t}){return s.Either.failure({message:t})}})()}}return n.\u0275fac=function(e){return new(e||n)(l.\u0275\u0275inject(I))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var Y=i(70658);let X=(()=>{class n{constructor(e,t,a,o,u){this.products=e,this.productService=t,this.repository=a,this.store=o,this.scannerService=u}execute(){var e=this;return(0,O.Z)(function*(){try{const{code:t,cancelled:a,granted:o}=yield e.scanQrCode();return o||Y.N.navigatorEnabled?a?s.Either.failure({value:!1,message:"Escaneo del c\xf3digo QR cancelado para realizar pago"}):s.Either.success(t):s.Either.failure({value:!1,message:"Banca m\xf3vil no cuenta con permisos para escanear de c\xf3digo QR, por favor habilite los permisos y vuelva a intentarlo"})}catch({message:t}){return s.Either.failure({value:!0,message:t})}})()}payment(e){var t=this;return(0,O.Z)(function*(){try{return t.verifyInvoice(yield t.repository.read(e))}catch({message:a}){return t.store.reset(),s.Either.failure({message:a})}})()}cancel(e){var t=this;return(0,O.Z)(function*(){try{const a=yield t.repository.read(e);return a.approvalId?s.Either.success(t.store.setInvoice(a,!1)):s.Either.failure({message:"C\xf3digo escaneado para pago QR es inv\xe1lido"})}catch({message:a}){return t.store.reset(),s.Either.failure({message:a})}})()}scanQrCode(){var e=this;return(0,O.Z)(function*(){return e.scannerService.qrCode({orientation:"portrait"})})()}verifyInvoice(e){var t=this;return(0,O.Z)(function*(){try{if(e.betweenAccounts){const o=yield t.verifyAccount(!0);return o&&t.store.setSourceProduct(o),s.Either.success(t.store.setInvoice(e))}const a=yield t.verifyCards();if(!e.belongCommerce&&!a)throw Error("Actualmente no cuentas con tarjetas activas para realizar pago QR");if(a&&(t.store.setSourceCard(a),t.store.setQuotas(a.numberQuotas)),e.belongCommerce){const o=yield t.verifyAccount(!1);if(!o&&!a)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");o&&t.store.setSourceProduct(o)}else t.store.setProducts([]);return s.Either.success(t.store.setInvoice(e))}catch({message:a}){return s.Either.failure({message:a})}})()}verifyAccount(e){var t=this;return(0,O.Z)(function*(){const a=(yield t.products.requestAccountsForTransfer()).sort((u,M)=>u.amount>M.amount?-1:1);t.store.setProducts(a);const[o]=a;if(!o&&e)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");return o})()}verifyCards(){var e=this;return(0,O.Z)(function*(){const[[t],[a]]=yield Promise.all([e.requestCreditCards(),e.requestDebitCards()]);return t||a})()}requestCreditCards(){var e=this;return(0,O.Z)(function*(){const t=yield e.products.requestCreditCards(),o=(yield Promise.all(t.map(u=>e.requestCreditCardInformation(u)))).filter(u=>(0,s.itIsDefined)(u)).sort((u,M)=>u.amount>M.amount?-1:1);return e.store.setCreditCards(o),o})()}requestCreditCardInformation(e){return this.productService.requestInformation(e).then(t=>t&&new D(e,t)).catch(()=>{})}requestDebitCards(){var e=this;return(0,O.Z)(function*(){const t=yield e.products.requestAccountsForTransfer(),o=(yield Promise.all(t.map(u=>e.requestDebitCardsInformation(u)))).reduce((u,M)=>u.concat(M),[]).sort((u,M)=>u.amount>M.amount?-1:1);return e.store.setDebitCards(o),o})()}requestDebitCardsInformation(e){return this.productService.requestDebitCards(e).then(t=>t.map(a=>new N(e,a))).catch(()=>[])}}return n.\u0275fac=function(e){return new(e||n)(l.\u0275\u0275inject(p.hM),l.\u0275\u0275inject(p.M5),l.\u0275\u0275inject(m),l.\u0275\u0275inject(I),l.\u0275\u0275inject(p.LQ))},n.\u0275prov=l.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},78337:(z,f,i)=>{i.r(f),i.d(f,{MboPaymentQrCancelPageModule:()=>v});var O=i(17007),p=i(78007),h=i(79798),s=i(30263),b=i(15861),E=i(39904),y=i(95437),P=i(69595),r=i(99877),Q=i(48774),S=i(2460),N=i(55944),D=i(17941),T=i(45542),L=i(10464),V=i(1027);function x(C,l){if(1&C&&r.\u0275\u0275element(0,"bocc-card-summary",21),2&C){const m=r.\u0275\u0275nextContext();r.\u0275\u0275property("amount",null==m.invoice?null:m.invoice.ivaAmount)("amountSmall",!0)}}function q(C,l){if(1&C&&r.\u0275\u0275element(0,"bocc-card-summary",22),2&C){const m=r.\u0275\u0275nextContext();r.\u0275\u0275property("amount",null==m.invoice?null:m.invoice.incAmount)("amountSmall",!0)}}function U(C,l){if(1&C&&r.\u0275\u0275element(0,"bocc-card-summary",23),2&C){const m=r.\u0275\u0275nextContext();r.\u0275\u0275property("amount",null==m.invoice?null:m.invoice.tipAmount)("amountSmall",!0)}}const R=E.Z6.PAYMENTS.QR;let A=(()=>{class C{constructor(m,d){this.mboProvider=m,this.requestConfiguration=d,this.backAction={id:"btn_payment-qr-cancel_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(R.SCAN)}}}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(R.RESULT)}initializatedConfiguration(){var m=this;return(0,b.Z)(function*(){(yield m.requestConfiguration.cancel()).when({success:({invoice:d})=>{m.invoice=d}})})()}}return C.\u0275fac=function(m){return new(m||C)(r.\u0275\u0275directiveInject(y.ZL),r.\u0275\u0275directiveInject(P.bE))},C.\u0275cmp=r.\u0275\u0275defineComponent({type:C,selectors:[["mbo-payment-qr-cancel-page"]],decls:28,vars:9,consts:[[1,"mbo-payment-qr-cancel-page__content","mbo-page__scroller"],[1,"mbo-payment-qr-cancel-page__header"],["title","Confirmar",3,"leftAction"],[1,"mbo-payment-qr-cancel-page__body"],[1,"mbo-payment-qr-cancel-page__title","subtitle2-medium"],[1,"mbo-payment-qr-cancel-page__information"],[1,"mbo-payment-qr-cancel-page__icon"],["icon","buy-shop"],[1,"mbo-payment-qr-cancel-page__merchant"],[1,"subtitle2-medium","truncate"],[1,"smalltext-medium","font-amathyst-700"],[1,"smalltext-medium"],[3,"amount","decimals"],["header","TIPO","title","PAGO QR",1,"mbo-payment-qr-cancel-page__type"],["class","mbo-payment-qr-cancel-page__iva","header","IVA",3,"amount","amountSmall",4,"ngIf"],["class","mbo-payment-qr-cancel-page__inc","header","VALOR INC",3,"amount","amountSmall",4,"ngIf"],["class","mbo-payment-qr-cancel-page__tip","header","PROPINA",3,"amount","amountSmall",4,"ngIf"],[1,"bocc-divider"],["header","TOTAL",1,"mbo-payment-qr-cancel-page__total",3,"amount"],[1,"mbo-payment-qr-cancel-page__footer"],["id","btn_payment-qr-cancel_submit","bocc-button","raised","prefixIcon","payment-buy-cancel",3,"click"],["header","IVA",1,"mbo-payment-qr-cancel-page__iva",3,"amount","amountSmall"],["header","VALOR INC",1,"mbo-payment-qr-cancel-page__inc",3,"amount","amountSmall"],["header","PROPINA",1,"mbo-payment-qr-cancel-page__tip",3,"amount","amountSmall"]],template:function(m,d){1&m&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),r.\u0275\u0275element(3,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information")(6,"label",4),r.\u0275\u0275text(7," \xbfDeseas realizar esta compra? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(8,"div",5)(9,"div",6),r.\u0275\u0275element(10,"bocc-icon",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(11,"div",8)(12,"label",9),r.\u0275\u0275text(13),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"label",10),r.\u0275\u0275text(15),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(16,"label",11),r.\u0275\u0275element(17,"bocc-amount",12),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275element(18,"bocc-card-summary",13),r.\u0275\u0275template(19,x,1,2,"bocc-card-summary",14),r.\u0275\u0275template(20,q,1,2,"bocc-card-summary",15),r.\u0275\u0275template(21,U,1,2,"bocc-card-summary",16),r.\u0275\u0275element(22,"div",17)(23,"bocc-card-summary",18),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(24,"div",19)(25,"button",20),r.\u0275\u0275listener("click",function(){return d.onSubmit()}),r.\u0275\u0275elementStart(26,"span"),r.\u0275\u0275text(27,"Anular compra"),r.\u0275\u0275elementEnd()()()()),2&m&&(r.\u0275\u0275advance(3),r.\u0275\u0275property("leftAction",d.backAction),r.\u0275\u0275advance(10),r.\u0275\u0275textInterpolate1(" ",null==d.invoice?null:d.invoice.merchant.name," "),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" Transacci\xf3n ",null==d.invoice?null:d.invoice.reference," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("amount",null==d.invoice?null:d.invoice.totalAmount)("decimals",!1),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngIf",null==d.invoice?null:d.invoice.codeDinamic),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==d.invoice?null:d.invoice.codeDinamic),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==d.invoice?null:d.invoice.codeDinamic),r.\u0275\u0275advance(2),r.\u0275\u0275property("amount",null==d.invoice?null:d.invoice.totalAmount))},dependencies:[O.NgIf,Q.J,S.Z,N.Q,D.D,T.P,L.K,V.A],styles:["/*!\n * MBO PaymentQrCancel Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 26/Oct/2022\n * Updated: 10/Ene/2024\n*/mbo-payment-qr-cancel-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__title{text-align:center}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__information{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__icon{position:relative;padding:var(--sizing-x2);box-sizing:border-box;border-radius:var(--sizing-x2);background:var(--color-blue-200)}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__icon bocc-icon{color:var(--color-blue-700)}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__merchant{display:flex;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__merchant bocc-amount{color:var(--color-carbon-lighter-700);font-size:inherit;letter-spacing:inherit}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box;background:var(--color-carbon-lighter-200)}mbo-payment-qr-cancel-page .mbo-payment-qr-cancel-page__footer button{width:100%}\n"],encapsulation:2}),C})(),v=(()=>{class C{}return C.\u0275fac=function(m){return new(m||C)},C.\u0275mod=r.\u0275\u0275defineNgModule({type:C}),C.\u0275inj=r.\u0275\u0275defineInjector({imports:[O.CommonModule,p.RouterModule.forChild([{path:"",component:A}]),s.Jx,s.Zl,s.Qg,s.D1,s.DM,s.P8,h.KI,h.A6]}),C})()}}]);