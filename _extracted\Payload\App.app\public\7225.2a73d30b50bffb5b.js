(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7225],{37225:(r,d,t)=>{t.r(d),t.d(d,{MboActivateCreditCardPageModule:()=>e});var n=t(17007),l=t(78007),o=t(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>t.e(3672).then(t.bind(t,23672)).then(a=>a.MboActivateCreditCardSourcePageModule)},{path:"result",loadChildren:()=>t.e(3544).then(t.bind(t,53544)).then(a=>a.MboActivateCreditCardResultPageModule)}];let e=(()=>{class a{}return a.\u0275fac=function(C){return new(C||a)},a.\u0275mod=o.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=o.\u0275\u0275defineInjector({imports:[n.CommonModule,l.RouterModule.forChild(M)]}),a})()}}]);