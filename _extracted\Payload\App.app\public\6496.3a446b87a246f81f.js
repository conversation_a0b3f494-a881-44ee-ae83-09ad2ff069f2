(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6496],{96496:(h,d,n)=>{n.r(d),n.d(d,{MboTransferTrustfundModule:()=>M});var l=n(17007),a=n(78007),t=n(99877);const u=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(6406).then(n.bind(n,36406)).then(o=>o.MboTransferTrustfundSourcePageModule)},{path:"destination",loadChildren:()=>n.e(5842).then(n.bind(n,95842)).then(o=>o.MboTransferTrustfundDestinationPageModule)},{path:"amount",loadChildren:()=>n.e(7600).then(n.bind(n,87600)).then(o=>o.MboTransferTrustfundAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(991).then(n.bind(n,90991)).then(o=>o.MboTransferTrustfundConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(6837).then(n.bind(n,16837)).then(o=>o.MboTransferTrustfundResultPageModule)}];let M=(()=>{class o{}return o.\u0275fac=function(f){return new(f||o)},o.\u0275mod=t.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=t.\u0275\u0275defineInjector({imports:[l.CommonModule,a.RouterModule.forChild(u)]}),o})()}}]);