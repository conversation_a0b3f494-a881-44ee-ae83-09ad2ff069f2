(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5055],{35055:(j,u,a)=>{a.r(u),a.d(u,{MboTransfiyaTransferAmountPageModule:()=>F});var y=a(17007),T=a(78007),i=a(30263),e=a(24495),f=a(39904),A=a(95437),C=a(87903),M=a(57544),I=a(40914),d=a(17698),N=a(73004),n=a(99877),S=a(83413),x=a(35641),P=a(48774),E=a(45542);const{MAX_TRANSFIYA:p,MIN_TRANSFIYA:g}=I.R,b=f.Z6.TRANSFERS.CELTOCEL.SEND,v=f.Z6.TRANSFERS.TRANSFIYA.TRANSFER;let z=(()=>{class o{constructor(r,t,m,l){this.mboProvider=r,this.requestConfiguration=t,this.managerTransfiya=m,this.cancelTransfiya=l,this.confirmation=!1,this.fromCelToCel=!1,this.hasAccounts=!1,this.backAction={id:"btn_transfiya-transfer-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(this.fromCelToCel?this.hasAccounts?b.ACCOUNT:b.DESTINATION:v.DESTINATION)}},this.cancelAction={id:"btn_transfiya-transfer-amount_cancel",label:"Cancelar",click:()=>{this.cancelTransfiya.execute()}},this.amountControl=new M.FormControl(void 0,[e.C1,(0,e.Go)(g),(0,e.VV)(p)])}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerTransfiya.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(v.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:r=>{const{amount:t,confirmation:m,fromCelToCel:l,hasAccounts:R,product:c}=r;t&&this.amountControl.setValue(t),this.fromCelToCel=l,this.hasAccounts=R,this.product=c,this.confirmation=m;const h=[e.C1,e.LU,(0,e.Go)(g),(0,e.VV)(p)];(0,C.VN)(c)&&h.push((0,e.vB)(c.amount)),this.amountControl.setValidators(h)}})}}return o.\u0275fac=function(r){return new(r||o)(n.\u0275\u0275directiveInject(A.ZL),n.\u0275\u0275directiveInject(d.ow),n.\u0275\u0275directiveInject(d.Pm),n.\u0275\u0275directiveInject(N.c))},o.\u0275cmp=n.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-transfer-amount-page"]],decls:12,vars:9,consts:[[1,"mbo-transfiya-transfer-amount-page__content"],[1,"mbo-transfiya-transfer-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfiya-transfer-amount-page__body"],[1,"mbo-transfiya-transfer-amount-page__message","subtitle2-medium"],["elementId","txt_transfiya-transfer-amount_value","label","Valor a transferir","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-transfiya-transfer-amount-page__footer"],["id","btn_transfiya-transfer-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(r,t){1&r&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"p",4),n.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas transferir? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(8,"div",7)(9,"button",8),n.\u0275\u0275listener("click",function(){return t.onSubmit()}),n.\u0275\u0275elementStart(10,"span"),n.\u0275\u0275text(11,"Continuar"),n.\u0275\u0275elementEnd()()()),2&r&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",t.backAction)("rightAction",t.cancelAction),n.\u0275\u0275advance(4),n.\u0275\u0275property("formControl",t.amountControl),n.\u0275\u0275advance(1),n.\u0275\u0275property("color",null==t.product?null:t.product.color)("icon",null==t.product?null:t.product.logo)("title",null==t.product?null:t.product.nickname)("number",null==t.product?null:t.product.publicNumber)("amount",null==t.product?null:t.product.amount),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",t.amountControl.invalid))},dependencies:[S.D,x.d,P.J,E.P],styles:["/*!\n * MBO TransfiyaTransferAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 14/Jun/2022\n * Updated: 09/Feb/2024\n*/mbo-transfiya-transfer-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);--pvt-checkbox-margin-top: var(--sizing-x20);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__body bocc-checkbox-label{margin-top:var(--pvt-checkbox-margin-top)}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-transfer-amount-page .mbo-transfiya-transfer-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfiya-transfer-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20);--pvt-checkbox-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),o})(),F=(()=>{class o{}return o.\u0275fac=function(r){return new(r||o)},o.\u0275mod=n.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=n.\u0275\u0275defineInjector({imports:[y.CommonModule,T.RouterModule.forChild([{path:"",component:z}]),i.D1,i.dH,i.Jx,i.P8]}),o})()}}]);