(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9654],{44896:(B,f,u)=>{u.d(f,{a:()=>l,b:()=>I,c:()=>E,d:()=>_,e:()=>s,f:()=>b,g:()=>S,h:()=>h,i:()=>a,j:()=>o,k:()=>n,l:()=>y,m:()=>m,n:()=>M,o:()=>k,p:()=>p,q:()=>w,r:()=>e,s:()=>i,t:()=>A,u:()=>v,v:()=>t,w:()=>C});const l="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-miterlimit='10' stroke-width='48' d='M244 400L100 256l144-144M120 256h292' class='ionicon-fill-none'/></svg>",a="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 268l144 144 144-144M256 392V100' class='ionicon-fill-none'/></svg>",h="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M368 64L144 256l224 192V64z'/></svg>",w="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 144l192 224 192-224H64z'/></svg>",p="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 368L256 144 64 368h384z'/></svg>",b="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M416 128L192 384l-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>",E="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>",y="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",C="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M136 208l120-104 120 104M136 304l120 104 120-104' stroke-width='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none'/></svg>",k="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",m="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>",A="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z'/></svg>",I="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",_="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>",S="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='192' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>",M="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='48'/><circle cx='416' cy='256' r='48'/><circle cx='96' cy='256' r='48'/></svg>",v="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>",t="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 384h384v-42.67H64zm0-106.67h384v-42.66H64zM64 128v42.67h384V128z'/></svg>",e="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>",o="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M96 256h320M96 176h320M96 336h320' class='ionicon-fill-none ionicon-stroke-width'/></svg>",n="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='44' d='M118 304h276M118 208h276' class='ionicon-fill-none'/></svg>",i="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>",s="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 428L339.92 303.9a160.48 160.48 0 0030.72-94.58C370.64 120.37 298.27 48 209.32 48S48 120.37 48 209.32s72.37 161.32 161.32 161.32a160.48 160.48 0 0094.58-30.72L428 464zM209.32 319.69a110.38 110.38 0 11110.37-110.37 110.5 110.5 0 01-110.37 110.37z'/></svg>"},89654:(B,f,u)=>{u.r(f),u.d(f,{ion_accordion:()=>m,ion_accordion_group:()=>v});var l=u(15861),a=u(42477),h=u(78635),w=u(44896),p=u(37943),b=u(28909);const m=class{constructor(t){var e=this;(0,a.r)(this,t),this.updateListener=()=>this.updateState(!1),this.setItemDefaults=()=>{const o=this.getSlottedHeaderIonItem();o&&(o.button=!0,o.detail=!1,void 0===o.lines&&(o.lines="full"))},this.getSlottedHeaderIonItem=()=>{const{headerEl:o}=this;if(!o)return;const n=o.querySelector("slot");return n&&void 0!==n.assignedElements?n.assignedElements().find(i=>"ION-ITEM"===i.tagName):void 0},this.setAria=(o=!1)=>{const n=this.getSlottedHeaderIonItem();if(!n)return;const s=(0,h.g)(n).querySelector("button");s&&s.setAttribute("aria-expanded",`${o}`)},this.slotToggleIcon=()=>{const o=this.getSlottedHeaderIonItem();if(!o)return;const{toggleIconSlot:n,toggleIcon:i}=this;if(o.querySelector(".ion-accordion-toggle-icon"))return;const r=document.createElement("ion-icon");r.slot=n,r.lazy=!1,r.classList.add("ion-accordion-toggle-icon"),r.icon=i,r.setAttribute("aria-hidden","true"),o.appendChild(r)},this.expandAccordion=(o=!1)=>{const{contentEl:n,contentElWrapper:i}=this;o||void 0===n||void 0===i?this.state=4:4!==this.state&&(void 0!==this.currentRaf&&cancelAnimationFrame(this.currentRaf),this.shouldAnimate()?(0,h.r)(()=>{this.state=8,this.currentRaf=(0,h.r)((0,l.Z)(function*(){const s=i.offsetHeight,r=(0,h.t)(n,2e3);n.style.setProperty("max-height",`${s}px`),yield r,e.state=4,n.style.removeProperty("max-height")}))}):this.state=4)},this.collapseAccordion=(o=!1)=>{const{contentEl:n}=this;o||void 0===n?this.state=1:1!==this.state&&(void 0!==this.currentRaf&&cancelAnimationFrame(this.currentRaf),this.shouldAnimate()?this.currentRaf=(0,h.r)((0,l.Z)(function*(){n.style.setProperty("max-height",`${n.offsetHeight}px`),(0,h.r)((0,l.Z)(function*(){const s=(0,h.t)(n,2e3);e.state=2,yield s,e.state=1,n.style.removeProperty("max-height")}))})):this.state=1)},this.shouldAnimate=()=>!(typeof window>"u"||matchMedia("(prefers-reduced-motion: reduce)").matches||!p.c.get("animated",!0)||this.accordionGroupEl&&!this.accordionGroupEl.animated),this.updateState=(0,l.Z)(function*(o=!1){const n=e.accordionGroupEl,i=e.value;if(!n)return;const s=n.value;if(Array.isArray(s)?s.includes(i):s===i)e.expandAccordion(o),e.isNext=e.isPrevious=!1;else{e.collapseAccordion(o);const c=e.getNextSibling()?.value;void 0!==c&&(e.isPrevious=Array.isArray(s)?s.includes(c):s===c);const x=e.getPreviousSibling()?.value;void 0!==x&&(e.isNext=Array.isArray(s)?s.includes(x):s===x)}}),this.getNextSibling=()=>{if(!this.el)return;const o=this.el.nextElementSibling;return"ION-ACCORDION"===o?.tagName?o:void 0},this.getPreviousSibling=()=>{if(!this.el)return;const o=this.el.previousElementSibling;return"ION-ACCORDION"===o?.tagName?o:void 0},this.state=1,this.isNext=!1,this.isPrevious=!1,this.value="ion-accordion-"+A++,this.disabled=!1,this.readonly=!1,this.toggleIcon=w.l,this.toggleIconSlot="end"}valueChanged(){this.updateState()}connectedCallback(){var t;const e=this.accordionGroupEl=null===(t=this.el)||void 0===t?void 0:t.closest("ion-accordion-group");e&&(this.updateState(!0),(0,h.a)(e,"ionValueChange",this.updateListener))}disconnectedCallback(){const t=this.accordionGroupEl;t&&(0,h.b)(t,"ionValueChange",this.updateListener)}componentDidLoad(){this.setItemDefaults(),this.slotToggleIcon(),(0,h.r)(()=>{this.setAria(4===this.state||8===this.state)})}toggleExpanded(){const{accordionGroupEl:t,disabled:e,readonly:o,value:n,state:i}=this;e||o||!t||t.requestAccordionToggle(n,1===i||2===i)}render(){const{disabled:t,readonly:e}=this,o=(0,p.b)(this),n=4===this.state||8===this.state,i=n?"header expanded":"header",s=n?"content expanded":"content";return this.setAria(n),(0,a.h)(a.H,{key:"073e1d02c18dcbc20c68648426e87c14750c031d",class:{[o]:!0,"accordion-expanding":8===this.state,"accordion-expanded":4===this.state,"accordion-collapsing":2===this.state,"accordion-collapsed":1===this.state,"accordion-next":this.isNext,"accordion-previous":this.isPrevious,"accordion-disabled":t,"accordion-readonly":e,"accordion-animated":this.shouldAnimate()}},(0,a.h)("div",{key:"9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc",onClick:()=>this.toggleExpanded(),id:"header",part:i,"aria-controls":"content",ref:r=>this.headerEl=r},(0,a.h)("slot",{key:"464c32a37f64655eacf4218284214f5f30b14a1e",name:"header"})),(0,a.h)("div",{key:"8bb52e6a62d7de0106b253201a89a32e79d9a594",id:"content",part:s,role:"region","aria-labelledby":"header",ref:r=>this.contentEl=r},(0,a.h)("div",{key:"1d9dfd952ad493754aaeea7a8f625b33c2dd90a0",id:"content-wrapper",ref:r=>this.contentElWrapper=r},(0,a.h)("slot",{key:"970dfbc55a612d739d0ca3b7b1a08e5c96d0c479",name:"content"}))))}static get delegatesFocus(){return!0}get el(){return(0,a.f)(this)}static get watchers(){return{value:["valueChanged"]}}};let A=0;m.style={ios:":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}",md:":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}"};const v=class{constructor(t){(0,a.r)(this,t),this.ionChange=(0,a.d)(this,"ionChange",7),this.ionValueChange=(0,a.d)(this,"ionValueChange",7),this.animated=!0,this.multiple=void 0,this.value=void 0,this.disabled=!1,this.readonly=!1,this.expand="compact"}valueChanged(){const{value:t,multiple:e}=this;!e&&Array.isArray(t)&&(0,b.p)(`ion-accordion-group was passed an array of values, but multiple="false". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the "value" property when multiple="false".\n\n  Value Passed: [${t.map(o=>`'${o}'`).join(", ")}]\n`,this.el),this.ionValueChange.emit({value:this.value})}disabledChanged(){var t=this;return(0,l.Z)(function*(){const{disabled:e}=t,o=yield t.getAccordions();for(const n of o)n.disabled=e})()}readonlyChanged(){var t=this;return(0,l.Z)(function*(){const{readonly:e}=t,o=yield t.getAccordions();for(const n of o)n.readonly=e})()}onKeydown(t){var e=this;return(0,l.Z)(function*(){const o=document.activeElement;if(!o||!o.closest('ion-accordion [slot="header"]'))return;const i="ION-ACCORDION"===o.tagName?o:o.closest("ion-accordion");if(!i||i.closest("ion-accordion-group")!==e.el)return;const r=yield e.getAccordions(),d=r.findIndex(g=>g===i);if(-1===d)return;let c;"ArrowDown"===t.key?c=e.findNextAccordion(r,d):"ArrowUp"===t.key?c=e.findPreviousAccordion(r,d):"Home"===t.key?c=r[0]:"End"===t.key&&(c=r[r.length-1]),void 0!==c&&c!==o&&c.focus()})()}componentDidLoad(){var t=this;return(0,l.Z)(function*(){t.disabled&&t.disabledChanged(),t.readonly&&t.readonlyChanged(),t.valueChanged()})()}setValue(t){const e=this.value=t;this.ionChange.emit({value:e})}requestAccordionToggle(t,e){var o=this;return(0,l.Z)(function*(){const{multiple:n,value:i,readonly:s,disabled:r}=o;if(!s&&!r)if(e)if(n){const d=i??[],c=Array.isArray(d)?d:[d];void 0===c.find(x=>x===t)&&void 0!==t&&o.setValue([...c,t])}else o.setValue(t);else if(n){const d=i??[],c=Array.isArray(d)?d:[d];o.setValue(c.filter(g=>g!==t))}else o.setValue(void 0)})()}findNextAccordion(t,e){const o=t[e+1];return void 0===o?t[0]:o}findPreviousAccordion(t,e){const o=t[e-1];return void 0===o?t[t.length-1]:o}getAccordions(){var t=this;return(0,l.Z)(function*(){return Array.from(t.el.querySelectorAll(":scope > ion-accordion"))})()}render(){const{disabled:t,readonly:e,expand:o}=this,n=(0,p.b)(this);return(0,a.h)(a.H,{key:"ac2e26ac91c95d08172bd2b51daa7a711c21db9e",class:{[n]:!0,"accordion-group-disabled":t,"accordion-group-readonly":e,[`accordion-group-expand-${o}`]:!0},role:"presentation"},(0,a.h)("slot",{key:"444adc2af5730897eba57095da046d6bdb5af70f"}))}get el(){return(0,a.f)(this)}static get watchers(){return{value:["valueChanged"],disabled:["disabledChanged"],readonly:["readonlyChanged"]}}};v.style={ios:":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}",md:":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-bottom-right-radius:6px;border-bottom-left-radius:6px}:host-context([dir=rtl]):host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous),:host-context([dir=rtl]).accordion-group-expand-inset ::slotted(ion-accordion.accordion-previous){border-bottom-right-radius:6px;border-bottom-left-radius:6px}@supports selector(:dir(rtl)){:host(.accordion-group-expand-inset:dir(rtl)) ::slotted(ion-accordion.accordion-previous){border-bottom-right-radius:6px;border-bottom-left-radius:6px}}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-top-left-radius:6px;border-top-right-radius:6px}:host-context([dir=rtl]):host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next),:host-context([dir=rtl]).accordion-group-expand-inset ::slotted(ion-accordion.accordion-next){border-top-left-radius:6px;border-top-right-radius:6px}@supports selector(:dir(rtl)){:host(.accordion-group-expand-inset:dir(rtl)) ::slotted(ion-accordion.accordion-next){border-top-left-radius:6px;border-top-right-radius:6px}}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}"}}}]);