(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8181],{19102:(B,P,e)=>{e.d(P,{r:()=>s});var l=e(17007),t=e(99877);let s=(()=>{class c{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return c.\u0275fac=function(_){return new(_||c)},c.\u0275cmp=t.\u0275\u0275defineComponent({type:c,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(_,b){1&_&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275element(1,"img",1),t.\u0275\u0275elementEnd()),2&_&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("src",b.src,t.\u0275\u0275sanitizeUrl))},dependencies:[l.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),c})()},52701:(B,P,e)=>{e.d(P,{q:()=>c});var l=e(17007),t=e(30263),o=e(99877);let c=(()=>{class d{}return d.\u0275fac=function(b){return new(b||d)},d.\u0275cmp=o.\u0275\u0275defineComponent({type:d,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(b,i){1&b&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"bocc-icon",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()),2&b&&(o.\u0275\u0275classMap(i.classTheme),o.\u0275\u0275advance(3),o.\u0275\u0275property("icon",i.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",i.label," "))},dependencies:[l.CommonModule,t.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),d})()},55648:(B,P,e)=>{e.d(P,{u:()=>i});var l=e(15861),n=e(17007),o=e(30263),s=e(78506),c=e(99877);function _(p,r){if(1&p){const m=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",2),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(m);const x=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(x.onClick())}),c.\u0275\u0275elementStart(1,"span"),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd()()}if(2&p){const m=c.\u0275\u0275nextContext();c.\u0275\u0275property("prefixIcon",m.icon)("disabled",m.disabled),c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate1(" ",m.label," ")}}function b(p,r){if(1&p){const m=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"button",3),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(m);const x=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(x.onClick())}),c.\u0275\u0275elementEnd()}if(2&p){const m=c.\u0275\u0275nextContext();c.\u0275\u0275property("bocc-button-action",m.icon)("disabled",m.disabled)}}let i=(()=>{class p{constructor(m){this.preferences=m,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:m})=>{this.isIncognito=m||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var m=this;return(0,l.Z)(function*(){yield m.preferences.toggleIncognito()})()}}return p.\u0275fac=function(m){return new(m||p)(c.\u0275\u0275directiveInject(s.Bx))},p.\u0275cmp=c.\u0275\u0275defineComponent({type:p,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(m,v){1&m&&(c.\u0275\u0275template(0,_,3,3,"button",0),c.\u0275\u0275template(1,b,1,2,"button",1)),2&m&&(c.\u0275\u0275property("ngIf",!v.actionMode),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",v.actionMode))},dependencies:[n.CommonModule,n.NgIf,o.P8,o.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),p})()},72765:(B,P,e)=>{e.d(P,{rw:()=>l.r,qr:()=>n.q,uf:()=>t.u,Z:()=>_,t5:()=>x,$O:()=>v});var l=e(19102),n=e(52701),t=e(55648),o=e(17007),s=e(30263),c=e(99877);const d=["*"];let _=(()=>{class y{constructor(){this.disabled=!1}}return y.\u0275fac=function(E){return new(E||y)},y.\u0275cmp=c.\u0275\u0275defineComponent({type:y,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],ngContentSelectors:d,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(E,M){1&E&&(c.\u0275\u0275projectionDef(),c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275element(2,"bocc-icon",2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",3),c.\u0275\u0275projection(4),c.\u0275\u0275elementEnd()()),2&E&&(c.\u0275\u0275classProp("mbo-poster__content--disabled",M.disabled),c.\u0275\u0275advance(2),c.\u0275\u0275property("icon",M.icon))},dependencies:[o.CommonModule,s.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),y})();var b=e(33395),i=e(77279),p=e(87903),r=e(87956),m=e(25317);let v=(()=>{class y{constructor(E){this.eventBusService=E}onCopy(){this.value&&((0,p.Bn)(this.value),this.eventBusService.emit(i.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return y.\u0275fac=function(E){return new(E||y)(c.\u0275\u0275directiveInject(r.Yd))},y.\u0275cmp=c.\u0275\u0275defineComponent({type:y,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(E,M){1&E&&(c.\u0275\u0275elementStart(0,"bocc-icon",0),c.\u0275\u0275listener("click",function(){return M.onCopy()}),c.\u0275\u0275elementEnd()),2&E&&c.\u0275\u0275property("id",M.elementId)},dependencies:[o.CommonModule,s.Zl,b.kW,m.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),y})(),x=(()=>{class y{}return y.\u0275fac=function(E){return new(E||y)},y.\u0275cmp=c.\u0275\u0275defineComponent({type:y,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(E,M){1&E&&c.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&E&&(c.\u0275\u0275property("value",M.value),c.\u0275\u0275advance(1),c.\u0275\u0275property("value",M.value))},dependencies:[o.CommonModule,s.qd,v],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),y})()},79798:(B,P,e)=>{e.d(P,{Vc:()=>n.Vc,rw:()=>l.rw,k4:()=>n.k4,qr:()=>l.qr,uf:()=>l.uf,xO:()=>o.x,A6:()=>t.A,tu:()=>m,Tj:()=>v,GI:()=>U,Uy:()=>Z,To:()=>R,w7:()=>G,o2:()=>n.o2,B_:()=>n.B_,fi:()=>n.fi,XH:()=>n.XH,cN:()=>n.cN,Aj:()=>n.Aj,J5:()=>n.J5,DB:()=>H.D,NH:()=>O.N,ES:()=>V.E,Nu:()=>n.Nu,x6:()=>j.x,KI:()=>X.K,iF:()=>n.iF,u8:()=>Y.u,eM:()=>et.e,ZF:()=>ot.Z,wu:()=>nt.w,$n:()=>rt.$,KN:()=>it.K,cV:()=>at.c,t5:()=>l.t5,$O:()=>l.$O,ZS:()=>ct.Z,sO:()=>st.s,bL:()=>ut,zO:()=>tt.z});var l=e(72765),n=e(27302),t=e(1027),o=e(7427),c=(e(16442),e(17007)),d=e(30263),_=e(44487),b=e.n(_),i=e(13462),p=e(21498),r=e(99877);let m=(()=>{class z{}return z.\u0275fac=function(D){return new(D||z)},z.\u0275mod=r.\u0275\u0275defineNgModule({type:z}),z.\u0275inj=r.\u0275\u0275defineInjector({imports:[c.CommonModule,i.LottieModule.forRoot({player:()=>b()}),l.rw,d.P8,d.Dj,p.P]}),z})(),v=(()=>{class z{ngBoccPortal(D){this.portal=D}onSubmit(){this.portal?.close()}}return z.\u0275fac=function(D){return new(D||z)},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-icon",2),r.\u0275\u0275elementStart(3,"label"),r.\u0275\u0275text(4," \xa1Atenci\xf3n! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p"),r.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),r.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"li",4),r.\u0275\u0275text(11,"Transacciones a celulares."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"li",4),r.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"li",4),r.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(16,"p",5),r.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(18,"div",6)(19,"button",7),r.\u0275\u0275listener("click",function(){return k.onSubmit()}),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,"Continuar"),r.\u0275\u0275elementEnd()()())},dependencies:[c.CommonModule,d.Zl,d.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),z})();var x=e(7603),y=e(87956),f=e(74520),E=e(39904),M=e(87903);function T(z,K){if(1&z){const D=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",6)(1,"label",7),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"span",8),r.\u0275\u0275text(4,"Tu gerente asignado (a)"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",8),r.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"button",9),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(D);const $=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView($.onEmail($.manager.email))}),r.\u0275\u0275elementStart(8,"span",10),r.\u0275\u0275text(9),r.\u0275\u0275elementEnd()()()}if(2&z){const D=r.\u0275\u0275nextContext(2);r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",D.manager.name," "),r.\u0275\u0275advance(7),r.\u0275\u0275textInterpolate1(" ",D.manager.email," ")}}function a(z,K){if(1&z){const D=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),r.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"button",12),r.\u0275\u0275listener("click",function($){r.\u0275\u0275restoreView(D);const J=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(J.onRetryManager($))}),r.\u0275\u0275elementStart(4,"span"),r.\u0275\u0275text(5,"Recargar"),r.\u0275\u0275elementEnd()()()}}function u(z,K){if(1&z&&(r.\u0275\u0275elementStart(0,"div",3),r.\u0275\u0275template(1,T,10,2,"div",4),r.\u0275\u0275template(2,a,6,0,"div",5),r.\u0275\u0275elementEnd()),2&z){const D=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",D.manager),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!D.manager)}}function g(z,K){1&z&&(r.\u0275\u0275elementStart(0,"div",13),r.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),r.\u0275\u0275elementEnd()),2&z&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!0))}e(29306);let h=(()=>{class z{constructor(D){this.customerService=D,this.requesting=!1}onRetryManager(D){this.customerService.requestManager(),D.stopPropagation()}onEmail(D){(0,M.Gw)(`mailto:${D}`)}onWhatsapp(){(0,M.Gw)(E.BA.WHATSAPP)}}return z.\u0275fac=function(D){return new(D||z)(r.\u0275\u0275directiveInject(y.vZ))},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275template(1,u,3,2,"div",1),r.\u0275\u0275template(2,g,5,4,"div",2),r.\u0275\u0275elementEnd()),2&D&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!k.requesting),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",k.requesting))},dependencies:[c.CommonModule,c.NgIf,d.P8,d.Dj,n.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),z})();const I={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},F={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},L={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function W(z,K){if(1&z&&(r.\u0275\u0275elementStart(0,"div",7),r.\u0275\u0275element(1,"mbo-contact-manager",8),r.\u0275\u0275elementEnd()),2&z){const D=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("manager",D.manager)("requesting",D.requesting)}}function A(z,K){if(1&z){const D=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"li",9)(1,"div",10),r.\u0275\u0275listener("click",function($){const pt=r.\u0275\u0275restoreView(D).$implicit,bt=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(bt.onOption(pt,$))}),r.\u0275\u0275elementStart(2,"label",11),r.\u0275\u0275text(3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",12)(5,"div",13),r.\u0275\u0275element(6,"bocc-icon",14),r.\u0275\u0275elementEnd()()()()}if(2&z){const D=K.$implicit;r.\u0275\u0275property("id",D.id),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",D.label," "),r.\u0275\u0275advance(1),r.\u0275\u0275attribute("bocc-theme",D.boccTheme),r.\u0275\u0275advance(2),r.\u0275\u0275property("icon",D.icon)}}let R=(()=>{class z{constructor(D,k,$){this.utagService=D,this.customerStore=k,this.customerService=$,this.isManagerEnabled=!1,this.requesting=!1,this.options=[I,F,L]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:D})=>{this.isManagerEnabled=D?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(k=>{this.manager=k.manager,this.requesting=k.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(D){this.portal=D}onOption(D,k){this.utagService.link("click",D.id),this.portal?.send({action:"option",value:D}),k.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return z.\u0275fac=function(D){return new(D||z)(r.\u0275\u0275directiveInject(x.D),r.\u0275\u0275directiveInject(f.f),r.\u0275\u0275directiveInject(y.vZ))},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-contact-information"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275listener("click",function(){return k.onClose()}),r.\u0275\u0275template(1,W,2,2,"div",1),r.\u0275\u0275elementStart(2,"ul",2),r.\u0275\u0275template(3,A,7,4,"li",3),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),r.\u0275\u0275listener("click",function(){return k.onClose()}),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(6,"div",6),r.\u0275\u0275listener("click",function(){return k.onClose()}),r.\u0275\u0275elementEnd()),2&D&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",k.isManagerEnabled),r.\u0275\u0275advance(2),r.\u0275\u0275property("ngForOf",k.options))},dependencies:[c.CommonModule,c.NgForOf,c.NgIf,d.Zl,h],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),z})();var N=e(95437);let G=(()=>{class z{constructor(D,k){this.floatingService=D,this.mboProvider=k,this.contactsFloating=this.floatingService.create(R),this.contactsFloating?.subscribe(({action:$,value:J})=>{"option"===$?this.dispatchOption(J):this.close()})}subscribe(D){this.subscriber=D}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(D){"PQRS"===D.action?this.mboProvider.openUrl(E.BA.PQRS):this.subscriber&&this.subscriber(D)}}return z.\u0275fac=function(D){return new(D||z)(r.\u0275\u0275inject(d.B7),r.\u0275\u0275inject(N.ZL))},z.\u0275prov=r.\u0275\u0275defineInjectable({token:z,factory:z.\u0275fac,providedIn:"root"}),z})(),U=(()=>{class z{constructor(){this.defenderLineNumber=E._L.DEFENDER_LINE,this.defenderLinePhone=E.WB.DEFENDER_LINE}ngBoccPortal(D){}onEmail(){(0,M.Gw)("mailto:<EMAIL>")}}return z.\u0275fac=function(D){return new(D||z)},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-contact-phones"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0),r.\u0275\u0275element(1,"mbo-attention-lines-form"),r.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),r.\u0275\u0275element(5,"bocc-icon",4),r.\u0275\u0275elementStart(6,"span",5),r.\u0275\u0275text(7,"Defensor del consumidor financiero"),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),r.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"label",8)(13,"span"),r.\u0275\u0275text(14,"Lorena Cerchar Rosado"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(15,"bocc-badge",9),r.\u0275\u0275text(16," Suplente "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(17,"div",10),r.\u0275\u0275element(18,"bocc-icon",11),r.\u0275\u0275elementStart(19,"div",12)(20,"span",13),r.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(22,"div",10),r.\u0275\u0275element(23,"bocc-icon",14),r.\u0275\u0275elementStart(24,"div",12)(25,"a",15),r.\u0275\u0275text(26),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(27,"span",13),r.\u0275\u0275text(28," Ext. 15318 - 15311 "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(29,"div",10),r.\u0275\u0275element(30,"bocc-icon",16),r.\u0275\u0275elementStart(31,"div",12)(32,"span",17),r.\u0275\u0275listener("click",function(){return k.onEmail()}),r.\u0275\u0275text(33," <EMAIL> "),r.\u0275\u0275elementEnd()()(),r.\u0275\u0275elementStart(34,"div",10),r.\u0275\u0275element(35,"bocc-icon",18),r.\u0275\u0275elementStart(36,"div",12)(37,"span",13),r.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),r.\u0275\u0275elementEnd()()()()()()),2&D&&(r.\u0275\u0275advance(25),r.\u0275\u0275property("href",k.defenderLinePhone,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",k.defenderLineNumber," "))},dependencies:[c.CommonModule,d.Zl,d.Oh,n.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),z})(),Z=(()=>{class z{constructor(){this.whatsappNumber=E._L.WHATSAPP}ngBoccPortal(D){}onClick(){(0,M.Gw)(E.BA.WHATSAPP)}}return z.\u0275fac=function(D){return new(D||z)},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",2)(4,"button",3),r.\u0275\u0275listener("click",function(){return k.onClick()}),r.\u0275\u0275elementStart(5,"span"),r.\u0275\u0275text(6),r.\u0275\u0275elementEnd()()()()),2&D&&(r.\u0275\u0275advance(6),r.\u0275\u0275textInterpolate(k.whatsappNumber))},dependencies:[c.CommonModule,d.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),z})();var O=e(10119),j=(e(87677),e(68789)),V=e(10455),H=e(91642),X=e(10464),Y=e(75221),tt=e(88649),et=e(13043),ot=e(38116),nt=e(68819),rt=e(19310),it=e(94614),at=(e(70957),e(91248),e(4663)),ct=e(13961),st=e(66709),Q=e(24495),q=e(57544),lt=e(53113);class dt extends q.FormGroup{constructor(){const K=new q.FormControl("",[Q.zf,Q.O_,Q.Y2,(0,Q.Mv)(24)]),D=new q.FormControl("",[Q.C1,Q.zf,Q.O_,Q.Y2,(0,Q.Mv)(24)]);super({controls:{description:D,reference:K}}),this.description=D,this.reference=K}setNote(K){this.description.setValue(K?.description),this.reference.setValue(K?.reference)}getNote(){return new lt.$H(this.description.value,this.reference.value)}}function mt(z,K){if(1&z&&r.\u0275\u0275element(0,"bocc-input-box",7),2&z){const D=r.\u0275\u0275nextContext();r.\u0275\u0275property("formControl",D.formControls.reference)}}let ut=(()=>{class z{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new dt}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(D){this.portal=D}}return z.\u0275fac=function(D){return new(D||z)},z.\u0275cmp=r.\u0275\u0275defineComponent({type:z,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(D,k){1&D&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275element(2,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",3)(4,"div",4),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"bocc-input-box",5),r.\u0275\u0275template(7,mt,1,1,"bocc-input-box",6),r.\u0275\u0275elementEnd()()),2&D&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("leftAction",k.cancelAction)("rightAction",k.saveAction),r.\u0275\u0275advance(3),r.\u0275\u0275textInterpolate1(" ",k.title," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("formControl",k.formControls.description),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",k.requiredReference))},dependencies:[c.CommonModule,c.NgIf,d.Jx,d.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),z})()},35324:(B,P,e)=>{e.d(P,{V:()=>b});var l=e(17007),t=e(30263),o=e(39904),s=e(87903),c=e(99877);function _(i,p){if(1&i){const r=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"a",9),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(r);const v=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(v.onWhatsapp())}),c.\u0275\u0275elementStart(1,"div",3),c.\u0275\u0275element(2,"bocc-icon",10),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",5)(4,"label",6),c.\u0275\u0275text(5," Whatsapp "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(6,"label",7),c.\u0275\u0275text(7),c.\u0275\u0275elementEnd()()()}if(2&i){const r=c.\u0275\u0275nextContext();c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",r.whatsappNumber," ")}}let b=(()=>{class i{constructor(){this.whatsapp=!1,this.whatsappNumber=o._L.WHATSAPP,this.nationalLineNumber=o._L.NATIONAL_LINE,this.bogotaLineNumber=o._L.BOGOTA_LINE,this.nationalLinePhone=o.WB.NATIONAL_LINE,this.bogotaLinePhone=o.WB.BOGOTA_LINE}onWhatsapp(){(0,s.Gw)(o.BA.WHATSAPP)}}return i.\u0275fac=function(r){return new(r||i)},i.\u0275cmp=c.\u0275\u0275defineComponent({type:i,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(r,m){1&r&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275template(1,_,8,1,"a",1),c.\u0275\u0275elementStart(2,"a",2)(3,"div",3),c.\u0275\u0275element(4,"bocc-icon",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"div",5)(6,"label",6),c.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(8,"label",7),c.\u0275\u0275text(9),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(10,"a",8)(11,"div",3),c.\u0275\u0275element(12,"bocc-icon",4),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(13,"div",5)(14,"label",6),c.\u0275\u0275text(15," Bogot\xe1 "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(16,"label",7),c.\u0275\u0275text(17),c.\u0275\u0275elementEnd()()()()),2&r&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",m.whatsapp),c.\u0275\u0275advance(1),c.\u0275\u0275property("href",m.nationalLinePhone,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",m.nationalLineNumber," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("href",m.bogotaLinePhone,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",m.bogotaLineNumber," "))},dependencies:[l.CommonModule,l.NgIf,t.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),i})()},9593:(B,P,e)=>{e.d(P,{k:()=>_});var l=e(17007),t=e(30263),o=e(39904),s=e(95437),c=e(99877);let _=(()=>{class b{constructor(p){this.mboProvider=p,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(o.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(o.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(o.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return b.\u0275fac=function(p){return new(p||b)(c.\u0275\u0275directiveInject(s.ZL))},b.\u0275cmp=c.\u0275\u0275defineComponent({type:b,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(p,r){1&p&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),c.\u0275\u0275listener("click",function(){return r.onProducts()}),c.\u0275\u0275element(3,"bocc-icon",3),c.\u0275\u0275elementStart(4,"label",4),c.\u0275\u0275text(5," Productos "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(6,"div",5),c.\u0275\u0275listener("click",function(){return r.onTransfers()}),c.\u0275\u0275element(7,"bocc-icon",6),c.\u0275\u0275elementStart(8,"label",4),c.\u0275\u0275text(9," Transferir "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(10,"div",7),c.\u0275\u0275listener("click",function(){return r.onPaymentQR()}),c.\u0275\u0275elementStart(11,"div",8)(12,"div",9),c.\u0275\u0275element(13,"bocc-icon",10),c.\u0275\u0275elementEnd()(),c.\u0275\u0275element(14,"bocc-icon",11),c.\u0275\u0275elementStart(15,"label",4),c.\u0275\u0275text(16," Pago QR "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(17,"div",12),c.\u0275\u0275listener("click",function(){return r.onPayments()}),c.\u0275\u0275element(18,"bocc-icon",13),c.\u0275\u0275elementStart(19,"label",4),c.\u0275\u0275text(20," Pagar "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(21,"div",14),c.\u0275\u0275listener("click",function(){return r.onToken()}),c.\u0275\u0275element(22,"bocc-icon",15),c.\u0275\u0275elementStart(23,"label",4),c.\u0275\u0275text(24," Token "),c.\u0275\u0275elementEnd()()()()),2&p&&(c.\u0275\u0275advance(2),c.\u0275\u0275classProp("bocc-footer-form__element--active",r.isProducts),c.\u0275\u0275advance(4),c.\u0275\u0275classProp("bocc-footer-form__element--active",r.isTransfers),c.\u0275\u0275advance(11),c.\u0275\u0275classProp("bocc-footer-form__element--active",r.isPayments),c.\u0275\u0275advance(4),c.\u0275\u0275classProp("bocc-footer-form__element--active",r.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[l.CommonModule,t.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),b})()},83867:(B,P,e)=>{e.d(P,{o:()=>y});var l=e(17007),t=e(30263),o=e(8834),s=e(98699),b=(e(57544),e(99877));function p(f,E){if(1&f&&(b.\u0275\u0275elementStart(0,"label",11),b.\u0275\u0275text(1),b.\u0275\u0275elementEnd()),2&f){const M=b.\u0275\u0275nextContext();b.\u0275\u0275classProp("mbo-currency-box__rate--active",M.hasValue),b.\u0275\u0275advance(1),b.\u0275\u0275textInterpolate2(" ",M.valueFormat," ",M.rateCode," ")}}function r(f,E){if(1&f&&(b.\u0275\u0275elementStart(0,"div",12),b.\u0275\u0275element(1,"img",13),b.\u0275\u0275elementEnd()),2&f){const M=b.\u0275\u0275nextContext();b.\u0275\u0275advance(1),b.\u0275\u0275property("src",M.icon,b.\u0275\u0275sanitizeUrl)}}function m(f,E){if(1&f&&(b.\u0275\u0275elementStart(0,"div",14),b.\u0275\u0275text(1),b.\u0275\u0275elementEnd()),2&f){const M=b.\u0275\u0275nextContext();b.\u0275\u0275advance(1),b.\u0275\u0275textInterpolate1(" ",M.currencyCode," ")}}function v(f,E){if(1&f&&(b.\u0275\u0275elementStart(0,"div",15),b.\u0275\u0275element(1,"bocc-icon",16),b.\u0275\u0275elementStart(2,"span",17),b.\u0275\u0275text(3),b.\u0275\u0275elementEnd()()),2&f){const M=b.\u0275\u0275nextContext();b.\u0275\u0275advance(3),b.\u0275\u0275textInterpolate1(" ",null==M.formControl.error?null:M.formControl.error.message," ")}}function x(f,E){if(1&f&&(b.\u0275\u0275elementStart(0,"div",18),b.\u0275\u0275element(1,"bocc-icon",19),b.\u0275\u0275elementStart(2,"span",17),b.\u0275\u0275text(3),b.\u0275\u0275elementEnd()()),2&f){const M=b.\u0275\u0275nextContext();b.\u0275\u0275advance(3),b.\u0275\u0275textInterpolate1(" ",M.helperInfo," ")}}let y=(()=>{class f{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,s.itIsDefined)(this.rate)}get value(){const M=+this.formControl?.value;return isNaN(M)?0:this.hasRate?M/this.rate:0}get valueFormat(){return(0,o.b)({value:this.value,symbol:"$",decimals:!0})}}return f.\u0275fac=function(M){return new(M||f)},f.\u0275cmp=b.\u0275\u0275defineComponent({type:f,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[b.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(M,C){1&M&&(b.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),b.\u0275\u0275text(3),b.\u0275\u0275elementEnd(),b.\u0275\u0275template(4,p,2,4,"label",3),b.\u0275\u0275elementEnd(),b.\u0275\u0275elementStart(5,"div",4)(6,"div",5),b.\u0275\u0275template(7,r,2,1,"div",6),b.\u0275\u0275element(8,"bocc-currency-field",7),b.\u0275\u0275template(9,m,2,1,"div",8),b.\u0275\u0275elementEnd()(),b.\u0275\u0275template(10,v,4,1,"div",9),b.\u0275\u0275template(11,x,4,1,"div",10),b.\u0275\u0275elementEnd()),2&M&&(b.\u0275\u0275classProp("mbo-currency-box--focused",C.formControl.focused)("mbo-currency-box--error",C.formControl.invalid&&C.formControl.touched)("mbo-currency-box--disabled",C.formControl.disabled||C.disabled),b.\u0275\u0275advance(2),b.\u0275\u0275property("for",C.elementId),b.\u0275\u0275advance(1),b.\u0275\u0275textInterpolate1(" ",C.label," "),b.\u0275\u0275advance(1),b.\u0275\u0275property("ngIf",C.hasRate),b.\u0275\u0275advance(3),b.\u0275\u0275property("ngIf",C.icon),b.\u0275\u0275advance(1),b.\u0275\u0275property("elementId",C.elementId)("placeholder",C.placeholder)("disabled",C.disabled)("formControl",C.formControl),b.\u0275\u0275advance(1),b.\u0275\u0275property("ngIf",C.currencyCode),b.\u0275\u0275advance(1),b.\u0275\u0275property("ngIf",C.formControl.invalid&&C.formControl.touched),b.\u0275\u0275advance(1),b.\u0275\u0275property("ngIf",C.helperInfo&&!(C.formControl.invalid&&C.formControl.touched)))},dependencies:[l.CommonModule,l.NgIf,t.XZ,t.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),f})()},85070:(B,P,e)=>{e.d(P,{f:()=>d});var l=e(17007),t=e(78506),o=e(99877);const c=["*"];let d=(()=>{class _{constructor(i){this.session=i}ngOnInit(){this.session.customer().then(i=>this.customer=i)}}return _.\u0275fac=function(i){return new(i||_)(o.\u0275\u0275directiveInject(t._I))},_.\u0275cmp=o.\u0275\u0275defineComponent({type:_,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(i,p){1&i&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"span",2),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()),2&i&&(o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(null==p.customer?null:p.customer.shortName))},dependencies:[l.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),_})()},65887:(B,P,e)=>{e.d(P,{X:()=>i});var l=e(17007),t=e(99877),s=e(30263),c=e(24495);function b(p,r){1&p&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275text(1,"Identificaci\xf3n"),t.\u0275\u0275elementEnd())}e(57544);let i=(()=>{class p{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[c.C1]:[]),this.unsubscription=this.documentType.subscribe(m=>{m&&(this.updateNumber(m,this.required),this.inputType=this.getInputType(m))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(m){if(m.required){const v=m.required.currentValue;this.documentType.setValidators(v?[c.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,v)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(m){return"PA"===m.code?"text":"number"}updateNumber(m,v){const x=this.validatorsForNumber(m,v);this.documentNumber.setValidators(x),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(m,v){return this.validatorsFromType(m).concat(v?[c.C1]:[])}maxLength(m){return v=>v&&v.length>m?{id:"maxLength",message:`Debe tener m\xe1ximo ${m} caracteres`}:null}validatorsFromType(m){switch(m.code){case"PA":return[c.JF];case"NIT":return[c.X1,this.maxLength(15)];default:return[c.X1]}}}return p.\u0275fac=function(m){return new(m||p)},p.\u0275cmp=t.\u0275\u0275defineComponent({type:p,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[t.\u0275\u0275NgOnChangesFeature,t.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(m,v){1&m&&(t.\u0275\u0275template(0,b,2,0,"div",0),t.\u0275\u0275elementStart(1,"div",1),t.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),t.\u0275\u0275elementEnd()),2&m&&(t.\u0275\u0275property("ngIf",v.condense),t.\u0275\u0275advance(1),t.\u0275\u0275classProp("mbo-document-customer__content--condense",v.condense),t.\u0275\u0275advance(1),t.\u0275\u0275property("elementId",v.elementSelectId)("label",v.labelType)("suggestions",v.documents)("disabled",v.disabled)("formControl",v.documentType),t.\u0275\u0275advance(1),t.\u0275\u0275property("elementId",v.elementInputId)("label",v.labelNumber)("type",v.inputType)("disabled",v.disabled)("formControl",v.documentNumber))},dependencies:[l.CommonModule,l.NgIf,s.DT,s.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),p})()},78021:(B,P,e)=>{e.d(P,{c:()=>p});var l=e(17007),t=e(30263),o=e(7603),s=e(98699),d=e(99877);function b(r,m){if(1&r){const v=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",5),d.\u0275\u0275listener("click",function(){d.\u0275\u0275restoreView(v);const y=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(y.onAction(y.leftAction))}),d.\u0275\u0275elementStart(1,"span"),d.\u0275\u0275text(2),d.\u0275\u0275elementEnd()()}if(2&r){const v=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",v.leftAction.id)("bocc-button",v.leftAction.type||"flat")("prefixIcon",v.leftAction.prefixIcon)("disabled",v.itIsDisabled(v.leftAction))("hidden",v.itIsHidden(v.leftAction)),d.\u0275\u0275advance(2),d.\u0275\u0275textInterpolate(v.leftAction.label)}}function i(r,m){if(1&r){const v=d.\u0275\u0275getCurrentView();d.\u0275\u0275elementStart(0,"button",6),d.\u0275\u0275listener("click",function(){const f=d.\u0275\u0275restoreView(v).$implicit,E=d.\u0275\u0275nextContext();return d.\u0275\u0275resetView(E.onAction(f))}),d.\u0275\u0275elementEnd()}if(2&r){const v=m.$implicit,x=d.\u0275\u0275nextContext();d.\u0275\u0275property("id",v.id)("type",v.type||"flat")("bocc-button-action",v.icon)("disabled",x.itIsDisabled(v))("hidden",x.itIsHidden(v))}}let p=(()=>{class r{constructor(v){this.utagService=v,this.rightActions=[]}itIsDisabled({disabled:v}){return(0,s.evalValueOrFunction)(v)}itIsHidden({hidden:v}){return(0,s.evalValueOrFunction)(v)}onAction(v){const{id:x}=v;x&&this.utagService.link("click",x),v.click()}}return r.\u0275fac=function(v){return new(v||r)(d.\u0275\u0275directiveInject(o.D))},r.\u0275cmp=d.\u0275\u0275defineComponent({type:r,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(v,x){1&v&&(d.\u0275\u0275elementStart(0,"div",0)(1,"div",1),d.\u0275\u0275template(2,b,3,6,"button",2),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(3,"div",3),d.\u0275\u0275template(4,i,1,5,"button",4),d.\u0275\u0275elementEnd()()),2&v&&(d.\u0275\u0275advance(2),d.\u0275\u0275property("ngIf",x.leftAction),d.\u0275\u0275advance(2),d.\u0275\u0275property("ngForOf",x.rightActions))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,t.P8,t.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),r})()},27302:(B,P,e)=>{e.d(P,{Vc:()=>l.V,k4:()=>n.k,o2:()=>t.o,B_:()=>_,fi:()=>b.f,XH:()=>i.X,cN:()=>v.c,Aj:()=>x.A,J5:()=>u.J,Nu:()=>I,iF:()=>G});var l=e(35324),n=e(9593),t=e(83867),o=e(17007),s=e(99877);function d(U,Z){if(1&U){const O=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"div",2),s.\u0275\u0275listener("click",function(){const V=s.\u0275\u0275restoreView(O).$implicit,H=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView(H.onClickCurrency(V))}),s.\u0275\u0275elementStart(1,"div",3),s.\u0275\u0275element(2,"img",4)(3,"img",5),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"label",6),s.\u0275\u0275text(5),s.\u0275\u0275elementEnd()()}if(2&U){const O=Z.$implicit,w=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",w.isEnabled(O)),s.\u0275\u0275advance(2),s.\u0275\u0275property("src",O.enabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(1),s.\u0275\u0275property("src",O.disabledIcon,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate1(" ",O.label," ")}}e(57544);let _=(()=>{class U{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[O]=this.currencies;this.formControl.setValue(O)}}ngOnChanges(O){const{currencies:w}=O;if(w){const[j]=w.currentValue;this.formControl&&this.formControl.setValue(j)}}isEnabled(O){return O===this.formControl?.value}onClickCurrency(O){this.formControl&&!this.disabled&&this.formControl.setValue(O)}changeCurriencies(O){if(O.currencies){const w=O.currencies.currentValue,[j]=w;this.formControl&&this.formControl.setValue(j)}}}return U.\u0275fac=function(O){return new(O||U)},U.\u0275cmp=s.\u0275\u0275defineComponent({type:U,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275NgOnChangesFeature,s.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(O,w){1&O&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,d,6,5,"div",1),s.\u0275\u0275elementEnd()),2&O&&(s.\u0275\u0275classProp("mbo-currency-toggle--disabled",w.disabled),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngForOf",w.currencies))},dependencies:[o.CommonModule,o.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),U})();var b=e(85070),i=e(65887),p=e(30263),v=e(78021),x=e(50689),E=(e(7603),e(98699),e(72765)),u=e(88014);function g(U,Z){if(1&U&&(s.\u0275\u0275elementStart(0,"div",4),s.\u0275\u0275element(1,"img",5),s.\u0275\u0275elementEnd()),2&U){const O=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("src",O.src,s.\u0275\u0275sanitizeUrl)}}const h=["*"];let I=(()=>{class U{}return U.\u0275fac=function(O){return new(O||U)},U.\u0275cmp=s.\u0275\u0275defineComponent({type:U,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],ngContentSelectors:h,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(O,w){1&O&&(s.\u0275\u0275projectionDef(),s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,g,2,1,"div",1),s.\u0275\u0275elementStart(2,"div",2)(3,"div",3),s.\u0275\u0275projection(4),s.\u0275\u0275elementEnd()()()),2&O&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",w.src))},dependencies:[o.CommonModule,o.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),U})();var F=e(24495);const L=/[A-Z]/,S=/[a-z]/,W=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,A=U=>U&&!L.test(U)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,R=U=>U&&!S.test(U)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,N=U=>U&&!W.test(U)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let G=(()=>{class U{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([F.C1,R,A,N,(0,F.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const O=this.formControl.errors.reduce((j,{id:V})=>[...j,V],[]),w=O.includes("required");this.smallInvalid=O.includes("smallCase")||w,this.capitalInvalid=O.includes("capitalCase")||w,this.specialCharInvalid=O.includes("specialChar")||w,this.minLengthInvalid=O.includes("minlength")||w})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return U.\u0275fac=function(O){return new(O||U)},U.\u0275cmp=s.\u0275\u0275defineComponent({type:U,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(O,w){1&O&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275element(1,"bocc-password-box",1),s.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),s.\u0275\u0275text(4," Min\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"mbo-poster",4),s.\u0275\u0275text(6," May\xfascula "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(7,"mbo-poster",5),s.\u0275\u0275text(8," Especial "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"mbo-poster",6),s.\u0275\u0275text(10," Caracteres "),s.\u0275\u0275elementEnd()()()),2&O&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",w.elementId)("disabled",w.disabled)("formControl",w.formControl),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",w.smallInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",w.capitalInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",w.specialCharInvalid),s.\u0275\u0275advance(2),s.\u0275\u0275property("disabled",w.minLengthInvalid))},dependencies:[o.CommonModule,p.sC,E.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),U})()},50689:(B,P,e)=>{e.d(P,{A:()=>c});var l=e(17007),t=e(99877);const s=["*"];let c=(()=>{class d{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return d.\u0275fac=function(b){return new(b||d)},d.\u0275cmp=t.\u0275\u0275defineComponent({type:d,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(b,i){1&b&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),t.\u0275\u0275element(2,"img",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"p",3),t.\u0275\u0275projection(4),t.\u0275\u0275elementEnd()()),2&b&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("src",i.logo,t.\u0275\u0275sanitizeUrl))},dependencies:[l.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),d})()},88014:(B,P,e)=>{e.d(P,{J:()=>s});var l=e(17007),t=e(99877);let s=(()=>{class c{}return c.\u0275fac=function(_){return new(_||c)},c.\u0275cmp=t.\u0275\u0275defineComponent({type:c,selectors:[["mbo-message-greeting"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(_,b){1&_&&(t.\u0275\u0275elementStart(0,"div",0)(1,"label",1),t.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"span",2),t.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),t.\u0275\u0275elementEnd()())},dependencies:[l.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),c})()},21498:(B,P,e)=>{e.d(P,{P:()=>r});var l=e(17007),t=e(30263),o=e(99877);function c(m,v){if(1&m&&o.\u0275\u0275element(0,"bocc-card-product-summary",7),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",x.information.product.color)("icon",x.information.product.icon)("number",x.information.product.number)("title",x.information.product.title)("subtitle",x.information.product.subtitle)}}function d(m,v){if(1&m&&o.\u0275\u0275element(0,"bocc-card-summary",8),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",x.information.standard.header)("title",x.information.standard.title)("subtitle",x.information.standard.subtitle)("detail",x.information.standard.detail)}}function _(m,v){if(1&m&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",x.information.amount.header)("amount",x.information.amount.value)("symbol",x.information.amount.symbol)("amountSmall",x.information.amount.small)}}function b(m,v){if(1&m&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",x.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(x.information.text.content)}}function i(m,v){if(1&m&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",12),o.\u0275\u0275element(1,"bocc-icon",13),o.\u0275\u0275elementStart(2,"span",14),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",15),o.\u0275\u0275elementStart(5,"span",14),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",x.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",x.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",x.information.datetime.time," ")}}function p(m,v){if(1&m&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&m){const x=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",x.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",x.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",x.information.badge.label," ")}}let r=(()=>{class m{}return m.\u0275fac=function(x){return new(x||m)},m.\u0275cmp=o.\u0275\u0275defineComponent({type:m,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(x,y){1&x&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,c,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,d,1,4,"bocc-card-summary",2),o.\u0275\u0275template(3,_,1,4,"bocc-card-summary",3),o.\u0275\u0275template(4,b,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,i,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,p,3,4,"bocc-card-summary",6),o.\u0275\u0275elementEnd()),2&x&&(o.\u0275\u0275property("ngSwitch",y.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[l.CommonModule,l.NgSwitch,l.NgSwitchCase,t.Zl,t.Oh,t.DM,t.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),m})()},7427:(B,P,e)=>{e.d(P,{x:()=>r});var l=e(17007),t=e(30263),o=e(87903),c=(e(29306),e(77279)),d=e(87956),_=e(68789),b=e(13961),i=e(99877);let r=(()=>{class m{constructor(x,y){this.eventBusService=x,this.onboardingScreenService=y,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,o.Bn)(this.product.tagAval),this.eventBusService.emit(c.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(b.Z)),this.tagAvalonboarding.open()}}return m.\u0275fac=function(x){return new(x||m)(i.\u0275\u0275directiveInject(d.Yd),i.\u0275\u0275directiveInject(_.x))},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(x,y){1&x&&(i.\u0275\u0275elementStart(0,"bocc-card-product",0),i.\u0275\u0275listener("key",function(){return y.onTagAval()})("onboarding",function(){return y.onBoarding()}),i.\u0275\u0275elementEnd()),2&x&&(i.\u0275\u0275classMap(y.product.bank.className),i.\u0275\u0275property("iconTitle",y.iconTitle)("title",y.product.nickname||y.product.name)("icon",y.product.logo)("tagAval",y.product.tagAvalFormat)("actions",y.actions)("color",y.product.color)("code",y.product.shortNumber)("label",y.product.label)("amount",y.product.amount)("incognito",y.incognito)("displayCard",!0)("statusLabel",null==y.product.status?null:y.product.status.label)("statusColor",null==y.product.status?null:y.product.status.color)("cromaline",!0)("msgError",y.msgError))},dependencies:[l.CommonModule,t.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),m})()},1027:(B,P,e)=>{e.d(P,{A:()=>x});var l=e(17007),n=e(72765),t=e(30263),o=e(99877);function s(y,f){if(1&y&&o.\u0275\u0275element(0,"bocc-card-product-summary",8),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("color",E.information.product.color)("icon",E.information.product.icon)("number",E.information.product.number)("title",E.information.product.title)("subtitle",E.information.product.subtitle)}}function c(y,f){if(1&y&&o.\u0275\u0275element(0,"bocc-card-summary",9),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.standard.header)("title",E.information.standard.title)("subtitle",E.information.standard.subtitle)}}function d(y,f){if(1&y&&o.\u0275\u0275element(0,"bocc-card-summary",10),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.amount.header)("amount",E.information.amount.value)("symbol",E.information.amount.symbol)}}function _(y,f){if(1&y&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.text.header)("customizedContent",!0),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(E.information.text.content)}}function b(y,f){if(1&y&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",13),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(4,"bocc-icon",16),o.\u0275\u0275elementStart(5,"span",15),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.datetime.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",E.information.datetime.date," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",E.information.datetime.time," ")}}function i(y,f){if(1&y&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",17),o.\u0275\u0275element(1,"bocc-icon",14),o.\u0275\u0275elementStart(2,"span",15),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd()()),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.date.header)("customizedContent",!0),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",E.information.date.date," ")}}function p(y,f){if(1&y&&(o.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()),2&y){const E=o.\u0275\u0275nextContext();o.\u0275\u0275property("header",E.information.badge.header)("customizedContent",!0),o.\u0275\u0275advance(1),o.\u0275\u0275attribute("bocc-theme",E.information.badge.color),o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",E.information.badge.label," ")}}let r=(()=>{class y{}return y.\u0275fac=function(E){return new(E||y)},y.\u0275cmp=o.\u0275\u0275defineComponent({type:y,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(E,M){1&E&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,s,1,5,"bocc-card-product-summary",1),o.\u0275\u0275template(2,c,1,3,"bocc-card-summary",2),o.\u0275\u0275template(3,d,1,3,"bocc-card-summary",3),o.\u0275\u0275template(4,_,3,3,"bocc-card-summary",4),o.\u0275\u0275template(5,b,7,4,"bocc-card-summary",5),o.\u0275\u0275template(6,i,4,3,"bocc-card-summary",6),o.\u0275\u0275template(7,p,3,4,"bocc-card-summary",7),o.\u0275\u0275elementEnd()),2&E&&(o.\u0275\u0275property("ngSwitch",M.information.type),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","product"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","standard"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","amount"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","text"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","datetime"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","date"),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[l.CommonModule,l.NgSwitch,l.NgSwitchCase,t.Zl,t.Oh,t.DM,t.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),y})();function m(y,f){1&y&&o.\u0275\u0275element(0,"mbo-card-information-element",8),2&y&&o.\u0275\u0275property("information",f.$implicit)}const v=["*"];let x=(()=>{class y{constructor(){this.skeleton=!1,this.informations=[]}}return y.\u0275fac=function(E){return new(E||y)},y.\u0275cmp=o.\u0275\u0275defineComponent({type:y,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:v,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(E,M){1&E&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),o.\u0275\u0275element(3,"mbo-bank-logo",3),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(4,"div",4),o.\u0275\u0275element(5,"div",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"div",6),o.\u0275\u0275projection(7),o.\u0275\u0275template(8,m,1,1,"mbo-card-information-element",7),o.\u0275\u0275elementEnd()()),2&E&&(o.\u0275\u0275advance(3),o.\u0275\u0275property("result",!0),o.\u0275\u0275advance(5),o.\u0275\u0275property("ngForOf",M.informations))},dependencies:[l.CommonModule,l.NgForOf,n.rw,r],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),y})()},16442:(B,P,e)=>{e.d(P,{u:()=>E});var l=e(99877),t=e(17007),s=e(13462),d=e(19102),_=e(45542),b=e(65467),i=e(21498);function p(M,C){if(1&M&&(l.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),l.\u0275\u0275text(1),l.\u0275\u0275elementEnd()),2&M){const T=l.\u0275\u0275nextContext();l.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",T.template.skeleton),l.\u0275\u0275property("secondary",!0)("active",T.template.skeleton),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate1(" ",T.template.header.subtitle," ")}}function r(M,C){1&M&&l.\u0275\u0275element(0,"mbo-card-information",16),2&M&&l.\u0275\u0275property("information",C.$implicit)}function m(M,C){if(1&M&&(l.\u0275\u0275elementStart(0,"div",14),l.\u0275\u0275projection(1),l.\u0275\u0275template(2,r,1,1,"mbo-card-information",15),l.\u0275\u0275elementEnd()),2&M){const T=l.\u0275\u0275nextContext();l.\u0275\u0275advance(2),l.\u0275\u0275property("ngForOf",T.template.informations)}}function v(M,C){1&M&&(l.\u0275\u0275elementStart(0,"div",17),l.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),l.\u0275\u0275elementEnd()),2&M&&(l.\u0275\u0275advance(1),l.\u0275\u0275property("active",!0),l.\u0275\u0275advance(1),l.\u0275\u0275property("active",!0)("secondary",!0),l.\u0275\u0275advance(1),l.\u0275\u0275property("active",!0)("secondary",!0))}function x(M,C){if(1&M){const T=l.\u0275\u0275getCurrentView();l.\u0275\u0275elementStart(0,"button",23),l.\u0275\u0275listener("click",function(){const g=l.\u0275\u0275restoreView(T).$implicit,h=l.\u0275\u0275nextContext(2);return l.\u0275\u0275resetView(h.onAction(g))}),l.\u0275\u0275elementStart(1,"span"),l.\u0275\u0275text(2),l.\u0275\u0275elementEnd()()}if(2&M){const T=C.$implicit;l.\u0275\u0275property("bocc-button",T.type)("prefixIcon",T.prefixIcon),l.\u0275\u0275advance(2),l.\u0275\u0275textInterpolate(T.label)}}function y(M,C){if(1&M&&(l.\u0275\u0275elementStart(0,"div",21),l.\u0275\u0275template(1,x,3,3,"button",22),l.\u0275\u0275elementEnd()),2&M){const T=l.\u0275\u0275nextContext();l.\u0275\u0275advance(1),l.\u0275\u0275property("ngForOf",T.template.actions)}}const f=["*"];let E=(()=>{class M{constructor(){this.disabled=!1,this.action=new l.EventEmitter}onAction({event:T}){this.action.emit(T)}}return M.\u0275fac=function(T){return new(T||M)},M.\u0275cmp=l.\u0275\u0275defineComponent({type:M,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:f,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(T,a){1&T&&(l.\u0275\u0275projectionDef(),l.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),l.\u0275\u0275element(3,"mbo-bank-logo",3),l.\u0275\u0275elementStart(4,"div",4),l.\u0275\u0275element(5,"ng-lottie",5),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),l.\u0275\u0275text(7),l.\u0275\u0275elementEnd(),l.\u0275\u0275template(8,p,2,5,"bocc-skeleton-text",7),l.\u0275\u0275elementEnd()(),l.\u0275\u0275elementStart(9,"div",8),l.\u0275\u0275element(10,"div",9),l.\u0275\u0275elementEnd(),l.\u0275\u0275template(11,m,3,1,"div",10),l.\u0275\u0275template(12,v,4,5,"div",11),l.\u0275\u0275elementEnd(),l.\u0275\u0275template(13,y,2,1,"div",12)),2&T&&(l.\u0275\u0275classProp("animation",!a.template.skeleton),l.\u0275\u0275advance(3),l.\u0275\u0275property("result",!0),l.\u0275\u0275advance(2),l.\u0275\u0275property("options",a.template.header.animation),l.\u0275\u0275advance(1),l.\u0275\u0275property("active",a.template.skeleton),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate1(" ",a.template.header.title," "),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",a.template.skeleton||a.template.header.subtitle),l.\u0275\u0275advance(3),l.\u0275\u0275property("ngIf",!a.template.skeleton),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",a.template.skeleton),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf",a.template.actions.length&&!a.disabled))},dependencies:[t.NgForOf,t.NgIf,s.LottieComponent,d.r,_.P,b.D,i.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),M})()},10119:(B,P,e)=>{e.d(P,{N:()=>v});var l=e(17007),t=e(99877),s=e(30263),c=e(7603),d=e(98699);function b(x,y){if(1&x&&t.\u0275\u0275element(0,"bocc-diamond",14),2&x){const f=y.$implicit,E=t.\u0275\u0275nextContext();t.\u0275\u0275property("active",E.itIsSelected(f))}}function i(x,y){if(1&x){const f=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",15),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(f);const M=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(M.onAction(M.footerActionLeft))}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()}if(2&x){const f=t.\u0275\u0275nextContext();t.\u0275\u0275property("id",f.footerActionLeft.id)("bocc-button",f.footerActionLeft.type)("prefixIcon",f.footerActionLeft.prefixIcon)("disabled",f.itIsDisabled(f.footerActionLeft))("hidden",f.itIsHidden(f.footerActionLeft)),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(f.footerActionLeft.label)}}function p(x,y){if(1&x){const f=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",15),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(f);const M=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(M.onAction(M.footerActionRight))}),t.\u0275\u0275elementStart(1,"span"),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd()()}if(2&x){const f=t.\u0275\u0275nextContext();t.\u0275\u0275property("id",f.footerActionRight.id)("bocc-button",f.footerActionRight.type)("prefixIcon",f.footerActionRight.prefixIcon)("disabled",f.itIsDisabled(f.footerActionRight))("hidden",f.itIsHidden(f.footerActionRight)),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(f.footerActionRight.label)}}const r=["*"];let v=(()=>{class x{constructor(f,E){this.ref=f,this.utagService=E,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new t.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((f,E)=>E),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(f){return f===this.currentPosition}itIsDisabled({disabled:f}){return(0,d.evalValueOrFunction)(f)}itIsHidden({hidden:f}){return(0,d.evalValueOrFunction)(f)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(f){const{id:E}=f;E&&this.utagService.link("click",E),f.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(f){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(f),this.automatic=!1,this.setTranslatePosition(f)}setTranslatePosition(f){this.translateX=f*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(f){this.transformContent=`translateX(${f}px)`}emitPosition(f){this.finished||(this.finished=f+1===this.elements.length),this.position.emit({position:f,finished:this.finished})}getPositionSlide(f){return f>=this.elements.length?this.elements.length-1:f<0?0:f}setTouchHandler(f){let E=0,M=0;f.addEventListener("touchstart",C=>{if(C.changedTouches.length){const{clientX:T}=C.changedTouches.item(0);E=0,this.touched=!0,M=T}}),f.addEventListener("touchmove",C=>{if(C.changedTouches.length){const T=C.changedTouches.item(0),a=T.clientX-M;M=T.clientX,this.translateX+=a,E+=a,this.setTranslateContent(this.translateX)}}),f.addEventListener("touchend",C=>{this.touched=!1,C.changedTouches.length&&(Math.abs(E)/this.widthBody*100>=40&&(E>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return x.\u0275fac=function(f){return new(f||x)(t.\u0275\u0275directiveInject(t.ElementRef),t.\u0275\u0275directiveInject(c.D))},x.\u0275cmp=t.\u0275\u0275defineComponent({type:x,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:r,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(f,E){1&f&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275listener("resize",function(){return E.onResize()},!1,t.\u0275\u0275resolveWindow),t.\u0275\u0275elementStart(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),t.\u0275\u0275projection(6),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),t.\u0275\u0275listener("click",function(){return E.onPrevious()}),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(10,"div",9),t.\u0275\u0275template(11,b,1,1,"bocc-diamond",10),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(12,"div",7)(13,"button",11),t.\u0275\u0275listener("click",function(){return E.onNext()}),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(14,"div",12),t.\u0275\u0275template(15,i,3,6,"button",13),t.\u0275\u0275template(16,p,3,6,"button",13),t.\u0275\u0275elementEnd()()),2&f&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",E.headerActionLeft)("rightAction",E.headerActionRight),t.\u0275\u0275advance(1),t.\u0275\u0275classProp("initialized",E.initialized),t.\u0275\u0275advance(1),t.\u0275\u0275classProp("gradient",E.gradient),t.\u0275\u0275advance(1),t.\u0275\u0275styleProp("width",E.widthContent)("transform",E.transformContent),t.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",E.touched),t.\u0275\u0275advance(4),t.\u0275\u0275property("hidden",!E.canPrevious),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngForOf",E.diamonds),t.\u0275\u0275advance(2),t.\u0275\u0275property("hidden",!E.canNext),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",E.footerActionLeft),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",E.footerActionRight))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,s.P8,s.u1,s.ou,s.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),x})()},68789:(B,P,e)=>{e.d(P,{x:()=>c});var l=e(7603),n=e(10455),t=e(87677),o=e(99877);let c=(()=>{class d{constructor(b){this.portalService=b}information(){this.portal||(this.portal=this.portalService.container({component:n.E,container:t.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(b,i){return this.portalService.container({component:b,container:t.C,props:{container:i?.containerProps,component:i?.componentProps}})}}return d.\u0275fac=function(b){return new(b||d)(o.\u0275\u0275inject(l.v))},d.\u0275prov=o.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},87677:(B,P,e)=>{e.d(P,{C:()=>t});var l=e(99877);let t=(()=>{class o{constructor(c){this.ref=c,this.visible=!1,this.visibleChange=new l.EventEmitter}open(c=0){setTimeout(()=>{this.changeVisible(!0)},c)}close(c=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},c)}append(c){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(c)}ngBoccPortal(c){this.portal=c}changeVisible(c){this.visible=c,this.visibleChange.emit(c)}}return o.\u0275fac=function(c){return new(c||o)(l.\u0275\u0275directiveInject(l.ElementRef))},o.\u0275cmp=l.\u0275\u0275defineComponent({type:o,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(c,d){1&c&&(l.\u0275\u0275elementStart(0,"div",0),l.\u0275\u0275element(1,"div",1),l.\u0275\u0275elementEnd()),2&c&&l.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",d.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),o})()},10455:(B,P,e)=>{e.d(P,{E:()=>d});var l=e(17007),t=e(99877),s=e(27302),c=e(10119);let d=(()=>{class _{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new t.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(i){this.portal=i}onPosition({finished:i}){this.finished=i,i&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return _.\u0275fac=function(i){return new(i||_)},_.\u0275cmp=t.\u0275\u0275defineComponent({type:_,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(i,p){1&i&&(t.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),t.\u0275\u0275listener("position",function(m){return p.onPosition(m)}),t.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),t.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"p"),t.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),t.\u0275\u0275elementEnd()()()),2&i&&t.\u0275\u0275property("footerActionLeft",p.footerLeft)("footerActionRight",p.footerRight)("gradient",!0)},dependencies:[l.CommonModule,s.Nu,c.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),_})()},91642:(B,P,e)=>{e.d(P,{D:()=>E});var l=e(17007),t=e(99877),s=e(30263),c=e(87542),d=e(70658),_=e(3372),b=e(87956),i=e(72765);function p(M,C){1&M&&t.\u0275\u0275element(0,"mbo-bank-logo")}function r(M,C){1&M&&(t.\u0275\u0275elementStart(0,"div",10),t.\u0275\u0275projection(1),t.\u0275\u0275elementEnd())}function m(M,C){if(1&M&&(t.\u0275\u0275elementStart(0,"button",11)(1,"span"),t.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),t.\u0275\u0275elementEnd()()),2&M){const T=t.\u0275\u0275nextContext();t.\u0275\u0275property("disabled",T.verifying)}}function v(M,C){1&M&&(t.\u0275\u0275elementStart(0,"div",12)(1,"p"),t.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),t.\u0275\u0275elementStart(3,"b"),t.\u0275\u0275text(4,'"Permitir"'),t.\u0275\u0275elementEnd(),t.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(6,"img",13),t.\u0275\u0275elementEnd())}function x(M,C){if(1&M){const T=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",14)(1,"p"),t.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),t.\u0275\u0275elementStart(3,"b"),t.\u0275\u0275text(4,'"Mostrar Clave"'),t.\u0275\u0275elementEnd(),t.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"button",15),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(T);const u=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(u.onShowKeyboard())}),t.\u0275\u0275elementStart(7,"span"),t.\u0275\u0275text(8,"Mostrar clave"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275element(9,"img",16),t.\u0275\u0275elementEnd()}if(2&M){const T=t.\u0275\u0275nextContext();t.\u0275\u0275advance(6),t.\u0275\u0275property("disabled",T.verifying)}}const y=["*"],{OtpInputSuperuser:f}=_.M;let E=(()=>{class M{constructor(T,a,u,g){this.ref=T,this.otpService=a,this.deviceService=u,this.preferencesService=g,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=c.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new t.EventEmitter,this.otpControls=new c.yV}ngOnInit(){this.otpService.onCode(T=>{this.otpControls.setCode(T),this.otpControls.valid&&this.onAutocomplete(T)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(T){const{documentNumber:a}=T;a&&this.preferencesService.applyFunctionality(f,a.currentValue).then(u=>{this.itIsDocumentSuperuser=u})}get otpVisible(){return!d.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return d.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&d.N.otpReadonlyMobile}onAutocomplete(T){this.code.emit(T)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return M.\u0275fac=function(T){return new(T||M)(t.\u0275\u0275directiveInject(t.ElementRef),t.\u0275\u0275directiveInject(b.no),t.\u0275\u0275directiveInject(b.U8),t.\u0275\u0275directiveInject(b.yW))},M.\u0275cmp=t.\u0275\u0275defineComponent({type:M,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[t.\u0275\u0275NgOnChangesFeature,t.\u0275\u0275StandaloneFeature],ngContentSelectors:y,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(T,a){1&T&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0)(1,"div",1),t.\u0275\u0275template(2,p,1,0,"mbo-bank-logo",2),t.\u0275\u0275template(3,r,2,0,"div",3),t.\u0275\u0275elementStart(4,"p",4),t.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),t.\u0275\u0275elementStart(6,"b"),t.\u0275\u0275text(7,"registrado"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(8,"bocc-otp-box",5),t.\u0275\u0275listener("autocomplete",function(g){return a.onAutocomplete(g)}),t.\u0275\u0275text(9," Ingresa tu clave "),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(10,m,3,1,"button",6),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"div",7),t.\u0275\u0275template(12,v,7,0,"div",8),t.\u0275\u0275template(13,x,10,1,"div",9),t.\u0275\u0275elementEnd()()),2&T&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",a.bankLogo),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",a.title),t.\u0275\u0275advance(5),t.\u0275\u0275classProp("mbo-otp-form--visible",a.otpVisible),t.\u0275\u0275property("formControls",a.otpControls)("readonly",a.otpReadonly)("mobile",a.otpMobile)("disabled",a.verifying),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",!1),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",a.isAndroid),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",a.isIos))},dependencies:[l.CommonModule,l.NgIf,s.P8,s.Yx,i.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),M})()},10464:(B,P,e)=>{e.d(P,{K:()=>d});var l=e(17007),t=e(99877),s=e(22816);const c=["*"];let d=(()=>{class _{constructor(i){this.ref=i,this.scroller=new s.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(i){this.scroller.reset(i.target)}}return _.\u0275fac=function(i){return new(i||_)(t.\u0275\u0275directiveInject(t.ElementRef))},_.\u0275cmp=t.\u0275\u0275defineComponent({type:_,selectors:[["mbo-page"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(i,p){1&i&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275listener("scroll",function(m){return p.onScroll(m)}),t.\u0275\u0275projection(1),t.\u0275\u0275elementEnd()),2&i&&t.\u0275\u0275classProp("mbo-page__content--start",p.scrollStart)("mbo-page__content--end",p.scrollEnd)},dependencies:[l.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),_})()},75221:(B,P,e)=>{e.d(P,{u:()=>_});var l=e(17007),t=e(30263),o=e(27302),c=(e(88649),e(99877));let _=(()=>{class b{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return b.\u0275fac=function(p){return new(p||b)},b.\u0275cmp=c.\u0275\u0275defineComponent({type:b,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(p,r){1&p&&c.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&p&&(c.\u0275\u0275property("formControl",r.passwordControl.controls.password)("disabled",r.disabled)("elementId",r.elementPasswordId),c.\u0275\u0275advance(1),c.\u0275\u0275property("elementId",r.elementConfirmId)("disabled",r.disabled)("formControl",r.passwordControl.controls.repeatPassword))},dependencies:[l.CommonModule,t.sC,o.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),b})()},88649:(B,P,e)=>{e.d(P,{z:()=>o});var l=e(57544),n=e(24495);class o extends l.FormGroup{constructor(){const c=new l.FormControl(""),d=new l.FormControl("",[n.C1,(s=c,c=>c&&c!==s.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var s;super({controls:{password:c,repeatPassword:d}})}get password(){return this.controls.password.value}}},13043:(B,P,e)=>{e.d(P,{e:()=>x});var l=e(17007),t=e(99877),s=e(30263),_=(e(57544),e(27302));function b(y,f){1&y&&(t.\u0275\u0275elementStart(0,"div",4)(1,"p",5),t.\u0275\u0275projection(2),t.\u0275\u0275elementEnd()())}function i(y,f){if(1&y&&(t.\u0275\u0275elementStart(0,"div",11),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&y){const E=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",E.title," ")}}function p(y,f){if(1&y){const E=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),t.\u0275\u0275listener("click",function(){const T=t.\u0275\u0275restoreView(E).$implicit,a=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(a.onProduct(T))}),t.\u0275\u0275elementEnd()}if(2&y){const E=f.$implicit,M=t.\u0275\u0275nextContext(2);t.\u0275\u0275property("color",E.color)("icon",E.logo)("title",E.nickname)("number",E.publicNumber)("detail",E.bank.name)("ghost",M.ghost)}}function r(y,f){if(1&y&&(t.\u0275\u0275elementStart(0,"div",6)(1,"div",7),t.\u0275\u0275template(2,i,2,1,"div",8),t.\u0275\u0275template(3,p,1,6,"bocc-card-product-selector",9),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"mbo-message-empty",10),t.\u0275\u0275text(5),t.\u0275\u0275elementEnd()()),2&y){const E=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",E.hasMessage),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",E.title),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",E.products),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",!E.hasMessage),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",E.msgError," ")}}function m(y,f){1&y&&(t.\u0275\u0275elementStart(0,"div",13),t.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),t.\u0275\u0275elementEnd()),2&y&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}const v=["*"];let x=(()=>{class y{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new t.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(E){return this.productControl?.value?.id===E.id}onProduct(E){this.select.emit(E),this.productControl?.setValue(E)}}return y.\u0275fac=function(E){return new(E||y)},y.\u0275cmp=t.\u0275\u0275defineComponent({type:y,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:v,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(E,M){1&E&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,b,3,0,"div",1),t.\u0275\u0275template(2,r,6,5,"div",2),t.\u0275\u0275template(3,m,3,2,"div",3),t.\u0275\u0275elementEnd()),2&E&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",M.header),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!M.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",M.skeleton))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,s.w_,_.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),y})()},38116:(B,P,e)=>{e.d(P,{Z:()=>d});var l=e(17007),t=e(99877),s=e(30263);function c(_,b){if(1&_){const i=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",1),t.\u0275\u0275listener("click",function(){const m=t.\u0275\u0275restoreView(i).$implicit,v=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(v.onAction(m))}),t.\u0275\u0275elementStart(1,"div",2)(2,"div",3),t.\u0275\u0275element(3,"bocc-icon",4),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(4,"label",5),t.\u0275\u0275text(5),t.\u0275\u0275elementEnd()()}if(2&_){const i=b.$implicit,p=t.\u0275\u0275nextContext();t.\u0275\u0275classMap(p.classWidthAction),t.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",p.itIsDisabled(i)),t.\u0275\u0275advance(1),t.\u0275\u0275classMap(p.theme(i)),t.\u0275\u0275advance(2),t.\u0275\u0275property("icon",i.icon),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",i.label," ")}}let d=(()=>{class _{constructor(){this.actions=[],this.action=new t.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(i){return i.requiredInformation&&i.errorInformation}theme(i){return this.itIsDisabled(i)?"none":i.theme}onAction(i){!this.itIsDisabled(i)&&this.action.emit(i.type)}}return _.\u0275fac=function(i){return new(i||_)},_.\u0275cmp=t.\u0275\u0275defineComponent({type:_,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(i,p){1&i&&t.\u0275\u0275template(0,c,6,8,"div",0),2&i&&t.\u0275\u0275property("ngForOf",p.actions)},dependencies:[l.CommonModule,l.NgForOf,s.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),_})()},68819:(B,P,e)=>{e.d(P,{w:()=>h});var l=e(17007),t=e(99877),s=e(30263),c=e(39904),b=(e(57544),e(78506)),p=(e(29306),e(87903)),r=e(95437),m=e(27302),v=e(70957),x=e(91248),y=e(13961),f=e(68789),E=e(33395),M=e(25317);function C(I,F){if(1&I){const L=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",17)(1,"div",18),t.\u0275\u0275listener("click",function(W){t.\u0275\u0275restoreView(L);const A=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(A.onBoarding(W))}),t.\u0275\u0275elementStart(2,"div",19),t.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(5,"label",22),t.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(7,"div",23)(8,"button",24),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(L);const W=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(W.onCopyKey(W.product.tagAval))}),t.\u0275\u0275elementStart(9,"span"),t.\u0275\u0275text(10,"Copiar"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275element(11,"div",25),t.\u0275\u0275elementStart(12,"button",26),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(L);const W=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(W.onEditTagAval())}),t.\u0275\u0275elementStart(13,"span"),t.\u0275\u0275text(14,"Personalizar"),t.\u0275\u0275elementEnd()()()()}if(2&I){const L=t.\u0275\u0275nextContext();t.\u0275\u0275property("hidden",L.itIsVisibleMovements),t.\u0275\u0275advance(4),t.\u0275\u0275property("value",L.product.tagAval)}}function T(I,F){if(1&I&&t.\u0275\u0275element(0,"mbo-product-info-section",27),2&I){const L=t.\u0275\u0275nextContext();t.\u0275\u0275property("section",L.digitalSection)("currencyCode",null==L.currencyControl.value?null:L.currencyControl.value.code)("hidden",L.itIsVisibleMovements)}}function a(I,F){if(1&I&&(t.\u0275\u0275elementStart(0,"div",8),t.\u0275\u0275element(1,"mbo-product-info-section",28),t.\u0275\u0275elementEnd()),2&I){const L=F.$implicit,S=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("section",L)("currencyCode",null==S.currencyControl.value?null:S.currencyControl.value.code)}}const u=[[["","header",""]],"*"],g=["[header]","*"];let h=(()=>{class I{constructor(L,S,W){this.mboProvider=L,this.managerInformation=S,this.onboardingScreenService=W,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(L){const{movements:S,sections:W}=L;S&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!S.currentValue),W&&this.product&&this.refreshComponent(this.product,W.currentValue),this.managerInformation.requestInfoBody().then(A=>{A.when({success:({canEditTagAval:R})=>{this.canEditTagAval=R}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(L){(0,p.Bn)(L),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(c.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(L,S){const W=(0,p.A2)(L);if(this.sectionPosition=0,S?.length){const A=S.map(({title:R},N)=>({label:R,value:N}));W&&(this.headerMovements.value=this.sections.length,A.push(this.headerMovements)),this.headers=A}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const A=[{label:"Error",value:1}];W&&A.unshift(this.headerMovements),this.headers=A}}onBoarding(L){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(y.Z)),this.tagAvalonboarding.open(),L.stopPropagation()}}return I.\u0275fac=function(L){return new(L||I)(t.\u0275\u0275directiveInject(r.ZL),t.\u0275\u0275directiveInject(b.vu),t.\u0275\u0275directiveInject(f.x))},I.\u0275cmp=t.\u0275\u0275defineComponent({type:I,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[t.\u0275\u0275NgOnChangesFeature,t.\u0275\u0275StandaloneFeature],ngContentSelectors:g,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(L,S){1&L&&(t.\u0275\u0275projectionDef(u),t.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),t.\u0275\u0275listener("valueChange",function(A){return S.sectionPosition=A}),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3),t.\u0275\u0275projection(4),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(5,C,15,2,"div",4),t.\u0275\u0275template(6,T,1,3,"mbo-product-info-section",5),t.\u0275\u0275elementStart(7,"bocc-tab-form",6),t.\u0275\u0275template(8,a,2,2,"div",7),t.\u0275\u0275elementStart(9,"div",8),t.\u0275\u0275element(10,"mbo-product-info-movements",9),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),t.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(16,"button",11)(17,"span"),t.\u0275\u0275text(18,"Reintentar"),t.\u0275\u0275elementEnd()()()()(),t.\u0275\u0275elementStart(19,"div",12),t.\u0275\u0275projection(20,1),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(21,"div",13)(22,"div",14),t.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(25,"div",14),t.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(28,"div",14),t.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),t.\u0275\u0275elementEnd()()()),2&L&&(t.\u0275\u0275classProp("mbo-product-info-body__content--condense",S.condense),t.\u0275\u0275advance(1),t.\u0275\u0275classProp("requesting",S.requesting),t.\u0275\u0275advance(1),t.\u0275\u0275property("tabs",S.headers)("value",S.sectionPosition),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",S.itIsVisibleMovements),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",null==S.product?null:S.product.tagAval),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",S.digitalSection),t.\u0275\u0275advance(1),t.\u0275\u0275property("position",S.sectionPosition),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",S.sections),t.\u0275\u0275advance(2),t.\u0275\u0275property("movements",S.movements)("header",S.header)("product",S.product)("currencyCode",null==S.currencyControl.value?null:S.currencyControl.value.code),t.\u0275\u0275advance(6),t.\u0275\u0275property("hidden",!0),t.\u0275\u0275advance(3),t.\u0275\u0275property("hidden",S.itIsVisibleMovements),t.\u0275\u0275advance(2),t.\u0275\u0275property("hidden",!S.requesting),t.\u0275\u0275advance(2),t.\u0275\u0275property("active",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("active",!0),t.\u0275\u0275advance(2),t.\u0275\u0275property("active",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("active",!0),t.\u0275\u0275advance(2),t.\u0275\u0275property("active",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("active",!0))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,s.Gf,s.qw,s.P8,s.Dj,s.qd,v.K,x.I,m.Aj,E.kW,M.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),I})()},19310:(B,P,e)=>{e.d(P,{$:()=>T});var l=e(17007),n=e(99877),t=e(30263),o=e(87903);let s=(()=>{class a{transform(g,h,I=" "){return(0,o.rd)(g,h,I)}}return a.\u0275fac=function(g){return new(g||a)},a.\u0275pipe=n.\u0275\u0275definePipe({name:"codeSplit",type:a,pure:!0}),a})(),c=(()=>{class a{}return a.\u0275fac=function(g){return new(g||a)},a.\u0275mod=n.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule]}),a})();e(57544);var _=e(70658),b=e(78506),p=(e(29306),e(87956)),r=e(72765),m=e(27302);function v(a,u){if(1&a){const g=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",18),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(g);const I=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(I.onDigital())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()}if(2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275property("prefixIcon",g.digitalIcon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(g.digitalIncognito?"Ver datos":"Ocultar datos")}}function x(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"span"),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate(null==g.product?null:g.product.publicNumber)}}function y(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"span",19),n.\u0275\u0275text(1),n.\u0275\u0275pipe(2,"codeSplit"),n.\u0275\u0275elementEnd()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind2(2,1,g.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":g.digitalNumber,4)," ")}}function f(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"bocc-badge",20),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275attribute("bocc-theme",null==g.product||null==g.product.status?null:g.product.status.color),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",null==g.product||null==g.product.status?null:g.product.status.label," ")}}function E(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"div",21),n.\u0275\u0275element(1,"bocc-progress-bar",22),n.\u0275\u0275elementEnd()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("theme",g.progressBarTheme)("width",g.progressBarStatus)}}function M(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"div",23)(1,"label",24),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),n.\u0275\u0275element(5,"bocc-amount",26),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"mbo-button-incognito-mode",27),n.\u0275\u0275elementEnd()()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate1(" ",null==g.product?null:g.product.label," "),n.\u0275\u0275advance(2),n.\u0275\u0275property("active",!g.product),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==g.product?null:g.product.amount)("incognito",g.incognito),n.\u0275\u0275advance(1),n.\u0275\u0275property("actionMode",!0)("hidden",!g.product)}}function C(a,u){if(1&a&&(n.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),n.\u0275\u0275text(3,"Vence"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"span",19),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(6,"div",29)(7,"label",20),n.\u0275\u0275text(8,"CVC"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(9,"span",19),n.\u0275\u0275text(10),n.\u0275\u0275elementEnd()()()),2&a){const g=n.\u0275\u0275nextContext();n.\u0275\u0275advance(5),n.\u0275\u0275textInterpolate1(" ",g.digitalIncognito?"\u2022\u2022 | \u2022\u2022":g.digitalExpAt," "),n.\u0275\u0275advance(5),n.\u0275\u0275textInterpolate1(" ",g.digitalIncognito?"\u2022\u2022\u2022":g.digitalCVC," ")}}let T=(()=>{class a{constructor(g,h){this.managerPreferences=g,this.digitalService=h,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new n.EventEmitter,this.digital=new n.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:g})=>{this.incognito=g})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:g,value:h})=>{this.product&&this.product.id===g&&this.refreshDigitalState(h)}))}ngOnChanges(g){const{product:h}=g;if(h&&h.currentValue){const I=h.currentValue;this.refreshDigitalState(this.digitalService.request(I.id)),this.activateDigitalCountdown(I)}}ngOnDestroy(){this.unsubscriptions.forEach(g=>g())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(g){const{incognito:h,requiredRequest:I,cvc:F,expirationAt:L,number:S}=g;this.digitalIncognito=h,this.digitalExpAt=L,this.digitalCVC=F,this.digitalNumber=S,this.digitalIcon=h?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=I}activateDigitalCountdown(g){const{countdown$:h}=this.digitalService.request(g.id);h?(this.progressBarRequired=!0,this.progressBarPercent=100,h.subscribe(I=>{this.progressBarRequired=!(I>=_.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-I/_.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return a.\u0275fac=function(g){return new(g||a)(n.\u0275\u0275directiveInject(b.Bx),n.\u0275\u0275directiveInject(p.ZP))},a.\u0275cmp=n.\u0275\u0275defineComponent({type:a,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[n.\u0275\u0275NgOnChangesFeature,n.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(g,h){1&g&&(n.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),n.\u0275\u0275listener("click",function(){return h.onClose()}),n.\u0275\u0275elementStart(3,"span"),n.\u0275\u0275text(4,"Atr\xe1s"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275element(5,"mbo-currency-toggle",3),n.\u0275\u0275template(6,v,3,2,"button",4),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(7,"div",5)(8,"div",6),n.\u0275\u0275element(9,"img",7),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),n.\u0275\u0275text(12),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),n.\u0275\u0275template(15,x,2,1,"span",12),n.\u0275\u0275template(16,y,3,4,"span",13),n.\u0275\u0275template(17,f,2,2,"bocc-badge",14),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(18,E,2,2,"div",15),n.\u0275\u0275elementEnd()(),n.\u0275\u0275template(19,M,7,6,"div",16),n.\u0275\u0275template(20,C,11,2,"div",17),n.\u0275\u0275elementEnd()),2&g&&(n.\u0275\u0275classMap(null==h.product?null:h.product.bank.className),n.\u0275\u0275property("color",null==h.product?null:h.product.color),n.\u0275\u0275advance(5),n.\u0275\u0275property("formControl",h.currencyControl)("currencies",h.currencies)("hidden",!(null!=h.product&&h.product.bank.isOccidente)||h.currencies.length<2),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==h.product?null:h.product.isDigital),n.\u0275\u0275advance(3),n.\u0275\u0275property("src",null==h.product?null:h.product.logo,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(2),n.\u0275\u0275property("active",!h.product),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",(null==h.product?null:h.product.nickname)||(null==h.product?null:h.product.name)," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!h.product),n.\u0275\u0275advance(1),n.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!h.product),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!(null!=h.product&&h.product.isDigital)),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==h.product?null:h.product.isDigital),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",(null==h.product||null==h.product.status?null:h.product.status.label)&&h.digitalIncognito),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",h.progressBarRequired),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!(null!=h.product&&h.product.isDigital)),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",null==h.product?null:h.product.isDigital))},dependencies:[l.CommonModule,l.NgIf,t.X6,t.Qg,t.Oh,t.P8,t.cp,t.Dj,c,s,r.uf,m.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),a})()},94614:(B,P,e)=>{e.d(P,{K:()=>b});var l=e(17007),t=e(30263),o=e(39904),c=(e(29306),e(95437)),d=e(99877);let b=(()=>{class i{constructor(r){this.mboProvider=r,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:r,id:m,parentProduct:v}=this.product;"covered"===r&&v?this.goToPage(v.id,m):this.goToPage(m)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(r,m){this.mboProvider.navigation.next(o.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:r,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:m})}}return i.\u0275fac=function(r){return new(r||i)(d.\u0275\u0275directiveInject(c.ZL))},i.\u0275cmp=d.\u0275\u0275defineComponent({type:i,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(r,m){1&r&&(d.\u0275\u0275elementStart(0,"div",0),d.\u0275\u0275listener("click",function(){return m.onComponent()}),d.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),d.\u0275\u0275text(5),d.\u0275\u0275elementEnd()(),d.\u0275\u0275elementStart(6,"div",4),d.\u0275\u0275element(7,"bocc-amount",5),d.\u0275\u0275elementEnd()()),2&r&&(d.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",m.skeleton),d.\u0275\u0275advance(2),d.\u0275\u0275property("active",m.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==m.movement?null:m.movement.dateFormat," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("active",m.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",null==m.movement?null:m.movement.description," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("hidden",m.skeleton),d.\u0275\u0275advance(1),d.\u0275\u0275property("amount",null==m.movement?null:m.movement.value)("currencyCode",null==m.movement?null:m.movement.currencyCode)("theme",!0))},dependencies:[l.CommonModule,t.Qg,t.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),i})()},70957:(B,P,e)=>{e.d(P,{K:()=>E});var l=e(15861),n=e(17007),o=e(99877),c=e(30263),d=e(78506),_=e(39904),i=(e(29306),e(87903)),p=e(95437),r=e(27302),m=e(94614);function v(M,C){if(1&M){const T=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",8),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(T);const u=o.\u0275\u0275nextContext(2);return o.\u0275\u0275resetView(u.onRedirectAll())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2,"Ver todos"),o.\u0275\u0275elementEnd()()}}function x(M,C){if(1&M&&(o.\u0275\u0275elementStart(0,"div",5)(1,"label",6),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(3,v,3,0,"button",7),o.\u0275\u0275elementEnd()),2&M){const T=o.\u0275\u0275nextContext();o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",(null==T.productMovements||null==T.productMovements.range?null:T.productMovements.range.label)||"Sin resultados"," "),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==T.productMovements?null:T.productMovements.range)}}function y(M,C){if(1&M&&o.\u0275\u0275element(0,"mbo-product-info-movement",9),2&M){const T=C.$implicit,a=o.\u0275\u0275nextContext();o.\u0275\u0275property("movement",T)("product",a.product)}}function f(M,C){if(1&M&&(o.\u0275\u0275elementStart(0,"mbo-message-empty",10),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&M){const T=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",T.msgError," ")}}let E=(()=>{class M{constructor(T,a){this.mboProvider=T,this.managerProductMovements=a,this.header=!0,this.requesting=!1}ngOnChanges(T){const{currencyCode:a,product:u}=T;if(!this.movements&&(u||a)){const g=a?.currentValue||this.currencyCode,h=u?.currentValue||this.product;this.currentMovements=void 0,(0,i.A2)(h)&&this.requestFirstPage(h,g)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:T,id:a,parentProduct:u}=this.product;this.mboProvider.navigation.next(_.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===T?{productId:u?.id,coveredCardId:a}:{productId:a},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(T,a){var u=this;return(0,l.Z)(function*(){u.requesting=!0,(yield u.managerProductMovements.requestForProduct({product:T,currencyCode:a})).when({success:g=>{u.currentMovements=g},failure:()=>{u.currentMovements=void 0}},()=>{u.requesting=!1})})()}}return M.\u0275fac=function(T){return new(T||M)(o.\u0275\u0275directiveInject(p.ZL),o.\u0275\u0275directiveInject(d.sy))},M.\u0275cmp=o.\u0275\u0275defineComponent({type:M,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(T,a){1&T&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,x,4,2,"div",1),o.\u0275\u0275elementStart(2,"div",2),o.\u0275\u0275template(3,y,1,2,"mbo-product-info-movement",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275template(4,f,2,1,"mbo-message-empty",4),o.\u0275\u0275elementEnd()),2&T&&(o.\u0275\u0275property("hidden",a.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",a.header),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",null==a.productMovements?null:a.productMovements.firstPage),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",null==a.productMovements?null:a.productMovements.isEmpty))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,c.P8,m.K,r.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),M})()},91248:(B,P,e)=>{e.d(P,{I:()=>i});var l=e(17007),t=e(30263),o=e(99877);function c(p,r){if(1&p&&o.\u0275\u0275element(0,"bocc-amount",10),2&p){const m=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("amount",m.value)("currencyCode",m.currencyCode)}}function d(p,r){if(1&p&&(o.\u0275\u0275elementStart(0,"span"),o.\u0275\u0275text(1),o.\u0275\u0275elementEnd()),2&p){const m=o.\u0275\u0275nextContext().$implicit,v=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275textInterpolate1(" ",v.incognito||v.section.incognito?m.mask:m.value," ")}}function _(p,r){if(1&p){const m=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"button",11),o.\u0275\u0275listener("click",function(){o.\u0275\u0275restoreView(m);const x=o.\u0275\u0275nextContext().$implicit;return o.\u0275\u0275resetView(x.action.click())}),o.\u0275\u0275elementStart(1,"span"),o.\u0275\u0275text(2),o.\u0275\u0275elementEnd()()}if(2&p){const m=o.\u0275\u0275nextContext().$implicit;o.\u0275\u0275property("suffixIcon",m.action.icon),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(m.action.label)}}function b(p,r){if(1&p&&(o.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275template(5,c,1,2,"bocc-amount",7),o.\u0275\u0275template(6,d,2,1,"span",8),o.\u0275\u0275elementEnd()(),o.\u0275\u0275template(7,_,3,2,"button",9),o.\u0275\u0275elementEnd()),2&p){const m=r.$implicit,v=o.\u0275\u0275nextContext();o.\u0275\u0275property("hidden",(null==m?null:m.currencyCode)!==v.currencyCode),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",m.label," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",m.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!m.money),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",m.action&&!(v.incognito||v.section.incognito))}}let i=(()=>{class p{constructor(){this.currencyCode="COP"}}return p.\u0275fac=function(m){return new(m||p)},p.\u0275cmp=o.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(m,v){1&m&&(o.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),o.\u0275\u0275template(2,b,8,5,"li",2),o.\u0275\u0275elementEnd()()),2&m&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",v.section.datas))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,t.Qg,t.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),p})()},4663:(B,P,e)=>{e.d(P,{c:()=>r});var l=e(17007),t=e(99877),s=e(30263),c=e(27302);function d(m,v){1&m&&(t.\u0275\u0275elementStart(0,"div",10)(1,"p",11),t.\u0275\u0275projection(2),t.\u0275\u0275elementEnd()())}function _(m,v){if(1&m&&(t.\u0275\u0275elementStart(0,"div",12),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&m){const x=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",x.title," ")}}function b(m,v){if(1&m){const x=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),t.\u0275\u0275listener("click",function(){const E=t.\u0275\u0275restoreView(x).$implicit,M=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(M.onProduct(E))}),t.\u0275\u0275elementEnd()}if(2&m){const x=v.$implicit,y=t.\u0275\u0275nextContext();t.\u0275\u0275property("color",x.color)("icon",x.logo)("title",x.nickname)("number",x.publicNumber)("ghost",y.ghost)("amount",x.amount)("tagAval",x.tagAvalFormat),t.\u0275\u0275attribute("amount-status",y.amountColorProduct(x))}}function i(m,v){1&m&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",14),t.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),t.\u0275\u0275elementEnd())}const p=["*"];let r=(()=>{class m{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new t.EventEmitter}amountColorProduct(x){return x.amount>0?"success":x.amount<0?"danger":"empty"}onProduct(x){this.select.emit(x)}}return m.\u0275fac=function(x){return new(x||m)},m.\u0275cmp=t.\u0275\u0275defineComponent({type:m,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],ngContentSelectors:p,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(x,y){1&x&&(t.\u0275\u0275projectionDef(),t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,d,3,0,"div",1),t.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),t.\u0275\u0275text(5,"Ver topes"),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(6,"div",4),t.\u0275\u0275template(7,_,2,1,"div",5),t.\u0275\u0275template(8,b,1,8,"bocc-card-product-selector",6),t.\u0275\u0275template(9,i,2,0,"mbo-message-empty",7),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(10,"div",8),t.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),t.\u0275\u0275elementEnd()()),2&x&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",y.header),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",!0),t.\u0275\u0275advance(4),t.\u0275\u0275property("hidden",y.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",y.title),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",y.products),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!y.products.length),t.\u0275\u0275advance(1),t.\u0275\u0275property("hidden",!y.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))},dependencies:[l.CommonModule,l.NgForOf,l.NgIf,s.w_,s.P8,c.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),m})()},13961:(B,P,e)=>{e.d(P,{Z:()=>d});var l=e(17007),t=e(27302),o=e(10119),s=e(99877);let d=(()=>{class _{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(i){this.portal=i}}return _.\u0275fac=function(i){return new(i||_)},_.\u0275cmp=s.\u0275\u0275defineComponent({type:_,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(i,p){1&i&&(s.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),s.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"p"),s.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),s.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(9,"p"),s.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),s.\u0275\u0275elementEnd()()()),2&i&&s.\u0275\u0275property("headerActionRight",p.headerAction)("gradient",!0)},dependencies:[l.CommonModule,t.Nu,o.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),_})()},66709:(B,P,e)=>{e.d(P,{s:()=>d});var l=e(17007),n=e(99877),t=e(30263),o=e(87542);let s=(()=>{class _{ngBoccPortal(i){}}return _.\u0275fac=function(i){return new(i||_)},_.\u0275cmp=n.\u0275\u0275defineComponent({type:_,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(i,p){1&i&&(n.\u0275\u0275elementStart(0,"div",0)(1,"label",1),n.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),n.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),n.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(11,"p",4),n.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),n.\u0275\u0275element(13,"br"),n.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),n.\u0275\u0275element(15,"br"),n.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),n.\u0275\u0275elementStart(17,"span"),n.\u0275\u0275text(18,">"),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(19," Configuraci\xf3n "),n.\u0275\u0275elementStart(20,"span"),n.\u0275\u0275text(21,">"),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(22," Seguridad "),n.\u0275\u0275elementStart(23,"span"),n.\u0275\u0275text(24,">"),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(25," Activar Token Mobile."),n.\u0275\u0275element(26,"br"),n.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),n.\u0275\u0275elementEnd()()()())},dependencies:[l.CommonModule,t.FL,t.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),_})();const c=["*"];let d=(()=>{class _{constructor(i,p){this.ref=i,this.bottomSheetService=p,this.verifying=!1,this.tokenLength=o.Xi,this.code=new n.EventEmitter,this.tokenControls=new o.b2}ngOnInit(){const i=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(s),setTimeout(()=>{i?.focus()},120)}onAutocomplete(i){this.code.emit(i)}onInfo(){this.infoSheet?.open()}}return _.\u0275fac=function(i){return new(i||_)(n.\u0275\u0275directiveInject(n.ElementRef),n.\u0275\u0275directiveInject(t.fG))},_.\u0275cmp=n.\u0275\u0275defineComponent({type:_,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(i,p){1&i&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275projection(2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"p",2),n.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"p",2),n.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),n.\u0275\u0275elementStart(7,"a"),n.\u0275\u0275text(8),n.\u0275\u0275elementEnd(),n.\u0275\u0275text(9,". "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"div",3),n.\u0275\u0275element(11,"img",4),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),n.\u0275\u0275listener("autocomplete",function(m){return p.onAutocomplete(m)}),n.\u0275\u0275text(13," Ingresa tu clave "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(14,"button",6),n.\u0275\u0275listener("click",function(){return p.onInfo()}),n.\u0275\u0275elementStart(15,"span"),n.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),n.\u0275\u0275elementEnd()()()),2&i&&(n.\u0275\u0275advance(8),n.\u0275\u0275textInterpolate1("",p.tokenLength," d\xedgitos"),n.\u0275\u0275advance(4),n.\u0275\u0275property("disabled",p.verifying)("formControls",p.tokenControls),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",p.verifying))},dependencies:[l.CommonModule,t.P8,t.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),_})()},18632:(B,P,e)=>{e.d(P,{a:()=>s});var l=e(30263),n=e(79798),t=e(74520),o=e(99877);let s=(()=>{class c{constructor(_,b){this.sessionStore=b,this.blueScreen=_.create(n.Tj)}canActivate(){const _=this.sessionStore.hasComplementaryServicesActivited();return _||this.blueScreen.open(120),_}}return c.\u0275fac=function(_){return new(_||c)(o.\u0275\u0275inject(l.Dl),o.\u0275\u0275inject(t.f))},c.\u0275prov=o.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},64847:(B,P,e)=>{e.d(P,{a:()=>y,m:()=>T});var l=e(15861),n=e(87956),t=e(53113),o=e(98699);class s{constructor(u,g,h){this.source=u,this.destination=g,this.amount=h}}function d(a){return new s(a.source,a.destination,a.amount)}var _=e(71776),b=e(39904),i=e(87903),p=e(42168),r=e(84757),m=e(99877);let v=(()=>{class a{constructor(g){this.http=g}send(g){return(0,p.firstValueFrom)(this.http.post(b.bV.TRANSFERS.ADVANCE,function c(a){return{curAmt:a.amount,fromDepAcctId:a.source.id,fromNickName:a.source.nickname,toDepAcctBankId:a.destination.bank.id,toDepAcctId:a.destination.id,toNickName:a.destination.nickname}}(g)).pipe((0,r.map)(h=>(0,i.l1)(h,"SUCCESS")))).catch(h=>(0,i.rU)(h))}}return a.\u0275fac=function(g){return new(g||a)(m.\u0275\u0275inject(_.HttpClient))},a.\u0275prov=m.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var x=e(18101);let y=(()=>{class a{constructor(g,h,I,F){this.productsService=g,this.eventBusService=h,this.repository=I,this.store=F}setSource(g){var h=this;return(0,l.Z)(function*(){try{return yield h.productsService.requestInformation(g),o.Either.success(h.store.setSource(g))}catch({message:I}){return o.Either.failure({message:I})}})()}setDestination(g){try{return o.Either.success(this.store.setDestination(g))}catch({message:h}){return o.Either.failure({message:h})}}setAmount(g){try{return o.Either.success(this.store.setAmount(g))}catch({message:h}){return o.Either.failure({message:h})}}reset(){try{const g=this.store.itIsFromCustomer(),h=this.store.getSource();return this.store.reset(),o.Either.success({fromCustomer:g,source:h})}catch({message:g}){return o.Either.failure({message:g})}}send(){var g=this;return(0,l.Z)(function*(){const h=d(g.store.currentState),I=yield g.execute(h);return g.eventBusService.emit(I.channel),o.Either.success({advance:h,status:I})})()}execute(g){try{return this.repository.send(g)}catch({message:h}){return Promise.resolve(t.LN.error(h))}}}return a.\u0275fac=function(g){return new(g||a)(m.\u0275\u0275inject(n.M5),m.\u0275\u0275inject(n.Yd),m.\u0275\u0275inject(v),m.\u0275\u0275inject(x.l))},a.\u0275prov=m.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var f=e(89148),E=e(74520);const{CcaQuotaAvailableCop:M,CcaQuotaAvailableUsd:C}=f.Av;let T=(()=>{class a{constructor(g,h,I,F){this.products=g,this.productService=h,this.store=I,this.customerStore=F}source(){var g=this;return(0,l.Z)(function*(){try{const h=g.store.itIsConfirmation(),I=yield g.requestCreditCards();return o.Either.success({confirmation:h,products:I})}catch({message:h}){return o.Either.failure({message:h})}})()}destination(g){var h=this;return(0,l.Z)(function*(){try{const I=h.store.itIsConfirmation(),F=yield h.requestCreditCard(g),L=yield h.requestCreditCards(),{session:{customer:S}}=h.customerStore.currentState;let W=[];return F&&(W=(yield h.productService.requestAffiliations(F)).filter(({ownerDocument:A,bank:R})=>A.equals(S.document)&&R.isOccidente)),o.Either.success({affiliations:W,confirmation:I,products:L,source:F})}catch({message:I}){return o.Either.failure({message:I})}})()}amount(){var g=this;return(0,l.Z)(function*(){try{const{amount:h,confirmation:I,source:F}=g.store.selectForAmount(),L=yield g.requestSection(F,"COP");return o.Either.success({amount:h,confirmation:I,section:L,source:F})}catch({message:h}){return o.Either.failure({message:h})}})()}confirmation(){try{const g=d(this.store.currentState);return o.Either.success({advance:g})}catch({message:g}){return o.Either.failure({message:g})}}requestCreditCards(){return this.products.requestCreditCards()}requestCreditCard(g){var h=this;return(0,l.Z)(function*(){let I=h.store.getSource();return!I&&g&&(I=(yield h.requestCreditCards()).find(({id:F})=>g===F),h.store.setSource(I,!0)),I})()}requestSection(g,h){return(0,o.catchPromise)(this.productService.requestInformation(g).then(I=>I.getSection("COP"===h?M:C)))}}return a.\u0275fac=function(g){return new(g||a)(m.\u0275\u0275inject(n.hM),m.\u0275\u0275inject(n.M5),m.\u0275\u0275inject(x.l),m.\u0275\u0275inject(E.f))},a.\u0275prov=m.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},80045:(B,P,e)=>{e.d(P,{j:()=>d});var l=e(30263),n=e(39904),t=e(95437),o=e(64847),s=e(99877);let d=(()=>{class _{constructor(i,p,r){this.modalConfirmation=i,this.mboProvider=p,this.managerAdvance=r}execute(i=!0){i?this.confirmation():this.cancelConfirmed(!0)}confirmation(i=!0){return this.modalConfirmation.execute({title:"Abandonar avance",message:"\xbfEstas seguro que deseas cancelar el avance de tarjeta de cr\xe9dito actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(i)}},decline:{label:"Continuar"}}).then(p=>"accept"===p)}cancelConfirmed(i){const p=this.managerAdvance.reset();i&&p.when({success:({fromCustomer:r,source:m})=>{r?this.mboProvider.navigation.back(n.Z6.CUSTOMER.PRODUCTS.INFO,{productId:m.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(l.$e),s.\u0275\u0275inject(t.ZL),s.\u0275\u0275inject(o.a))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},18101:(B,P,e)=>{e.d(P,{l:()=>d});var l=e(39904),n=e(87956),t=e(20691),s=e(99877);let d=(()=>{class _ extends t.Store{constructor(i){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=i,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(i,p=!1){this.reduce(r=>({...r,source:i,fromCustomer:p}))}getSource(){return this.select(({source:i})=>i)}itIsFromCustomer(){return this.select(({fromCustomer:i})=>i)}setDestination(i){this.reduce(p=>({...p,destination:i}))}getDestination(){return this.select(({destination:i})=>i)}setAmount(i){this.reduce(p=>({...p,amount:i,confirmation:!0}))}selectForAmount(){return this.select(({amount:i,confirmation:p,source:r})=>({amount:i,confirmation:p,source:r}))}itIsConfirmation(){return this.select(({confirmation:i})=>i)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(n.Yd))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},56868:(B,P,e)=>{e.d(P,{A$:()=>o,CB:()=>d,K6:()=>s,cw:()=>t,hk:()=>c});var l=e(87903),n=e(77478);class t{constructor(b,i){this.number=b,this.contact=i,this.numberFormat=(0,l.wW)(b)}}class o{constructor(b,i,p){this.indicator=b,this.number=i,this.name=p,this.title=this.name,this.subtitle=(0,l.wW)(this.number)}get phone(){return`${this.indicator}${this.number}`}}class s{constructor(b,i,p){this.documentType=b,this.documentNumber=i,this.fullName=p,this.maskName=(0,n.U)(p)}}class c{constructor(b,i,p,r){this.number=b,this.type=i,this.bank=p,this.customer=r}}class d{constructor(b,i,p,r){this.source=b,this.account=i,this.contact=p,this.amount=r}}},10334:(B,P,e)=>{e.d(P,{L9:()=>o,MM:()=>s,eZ:()=>c});var l=e(29306),n=e(87903),t=e(56868);function o(d){const _=new t.K6((0,n.nX)(d.PersonInfo.GovIssueIdent.GovIssueIdentType),d.PersonInfo.GovIssueIdent.IdentSerialNum,d.PersonInfo.PersonName.FullName);return new t.hk(d.DepAcctId.AcctId,d.DepAcctId.AcctType,l.Br.fromId(d.DepAcctId.BankInfo.BankId),_)}function s(d){return{fromDepAcctId:d.source.id,fromDepAcctName:d.source.name,fromDepAcctType:d.source.type,fromNickName:d.source.nickname,toDepAcctBankId:d.account.bank.id,toDepAcctType:d.account.type,toDepAcctId:d.account.number,toDepAcctName:d.account.customer.fullName,toNickName:"",toUserIdNumber:d.account.customer.documentNumber,toUserIdType:d.account.customer.documentType.code,curAmt:d.amount.toString(),refId:"",memo:"",category:"3",typeTransfer:"CEL_PHONE_TRANS"}}function c(d){return new t.CB(d.source,d.account,new t.A$("57",d.phone.number,d.phone.contact||"Celular"),d.amount)}},77478:(B,P,e)=>{e.d(P,{R:()=>s,U:()=>c});var l=e(87903),n=e(53113);function t(d){const{isError:_,message:b}=d;return{animation:(0,l.jY)(d),title:_?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:b}}function o({isError:d}){return d?[(0,l.wT)("Finalizar","finish","outline"),(0,l.wT)("Volver a intentar","retry")]:[(0,l.wT)("Hacer otra transferencia","retry","outline"),(0,l.wT)("Finalizar","finish")]}function s(d){const{dateFormat:_,timeFormat:b}=new n.ou,{status:i,celToCel:p}=d;return{actions:o(i),error:i.isError,header:t(i),informations:[(0,l.SP)("DESTINO",p.account.customer.maskName,p.account.number,p.account.bank.name),(0,l._f)("SUMA DE",p.amount),(0,l.cZ)(_,b)],skeleton:!1}}function c(d){const _=d.split(" "),[b]=_,i=_[_.length-1],p=i.length,m=p>3?3:2;return`${b.substring(0,b.length>4?4:2)}*****${i.substring(p-m,p)}`}},17758:(B,P,e)=>{e.d(P,{Ey:()=>l.E,Pm:()=>s,ZW:()=>p});var l=e(97549),n=e(98699),t=e(17881),o=e(99877);let s=(()=>{class r{constructor(v){this.store=v}transfer(){try{return n.Either.success(this.store.setType("TRANSFER"))}catch({message:v}){return n.Either.failure({message:v})}}approved(){try{return n.Either.success(this.store.setType("APPROVED"))}catch({message:v}){return n.Either.failure({message:v})}}request(){try{return n.Either.success(this.store.setType("REQUEST"))}catch({message:v}){return n.Either.failure({message:v})}}pending(){try{return n.Either.success(this.store.setType("PENDING"))}catch({message:v}){return n.Either.failure({message:v})}}contacts(){try{return n.Either.success(this.store.setType("CONTACTS"))}catch({message:v}){return n.Either.failure({message:v})}}}return r.\u0275fac=function(v){return new(v||r)(o.\u0275\u0275inject(t.d))},r.\u0275prov=o.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();var c=e(15861),d=e(27236),_=e(87956),b=e(10334),i=e(56350);let p=(()=>{class r{constructor(v,x,y){this.storageService=v,this.store=x,this.products=y}allowAccess(){var v=this;return(0,c.Z)(function*(){try{return n.Either.success((yield v.storageService.get(d.Z.CelToCelTyC))||!1)}catch{return n.Either.success(!1)}})()}source(){var v=this;return(0,c.Z)(function*(){try{const x=yield v.products.requestAccountsForTransfer(),y=v.store.itIsConfirmation();return n.Either.success({confirmation:y,products:x})}catch({message:x}){return n.Either.failure({message:x})}})()}destination(){var v=this;return(0,c.Z)(function*(){try{const x=yield v.products.requestAccountsForTransfer(),y=v.store.itIsConfirmation();return n.Either.success({confirmation:y,hasOneSource:x.length<2,phone:v.store.getPhone()})}catch({message:x}){return n.Either.failure({message:x})}})()}account(){try{const v=this.store.getAccounts()||[],x=this.store.getAccount(),y=this.store.itIsConfirmation();return n.Either.success({account:x,accounts:v,confirmation:y})}catch({message:v}){return n.Either.failure({message:v})}}amount(){try{const v=this.store.itIsConfirmation(),x=this.store.getSource(),y=this.store.getAmount(),f=this.store.getAccount();return n.Either.success({confirmation:v,account:f,amount:y,source:x})}catch({message:v}){return n.Either.failure({message:v})}}confirmation(){try{const v=(0,b.eZ)(this.store.currentState);return n.Either.success({transfer:v})}catch({message:v}){return n.Either.failure({message:v})}}}return r.\u0275fac=function(v){return new(v||r)(o.\u0275\u0275inject(_.V1),o.\u0275\u0275inject(i.z),o.\u0275\u0275inject(_.hM))},r.\u0275prov=o.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},97549:(B,P,e)=>{e.d(P,{E:()=>E});var l=e(15861),n=e(27236),t=e(87956),o=e(53113),s=e(98699),c=e(42789),d=e(17881),_=e(10334),b=e(71776),i=e(78506),p=e(39904),r=e(87903),m=e(42168),v=e(84757),x=e(99877);let y=(()=>{class M{constructor(T,a){this.http=T,this.managerSession=a}verifyPhone(T){var a=this;return(0,l.Z)(function*(){const{documentType:u,documentNumber:g}=yield a.managerSession.customer();return(0,m.firstValueFrom)(a.http.post(p.bV.TRANSFERS.CEL_TO_CEL.VALIDATE_NUMBER,{Phone:T,GovIssueIdentType:u.code,IdentSerialNum:g}).pipe((0,v.map)(({data:{PartyAcctRelRec:h}})=>h?h.map(({PartyAcctRelInfo:I})=>(0,_.L9)(I)):[])))})()}send(T){const a=(0,_.MM)(T);return(0,m.firstValueFrom)(this.http.post(p.bV.TRANSFERS.CEL_TO_CEL.SEND,a).pipe((0,v.map)(u=>(0,r.l1)(u,"SUCCESS")))).catch(u=>(0,r.rU)(u))}}return M.\u0275fac=function(T){return new(T||M)(x.\u0275\u0275inject(b.HttpClient),x.\u0275\u0275inject(i._I))},M.\u0275prov=x.\u0275\u0275defineInjectable({token:M,factory:M.\u0275fac,providedIn:"root"}),M})();var f=e(56350);let E=(()=>{class M{constructor(T,a,u,g,h){this.repository=T,this.store=a,this.transfiyaStore=u,this.eventBusService=g,this.storageService=h}approvedTyC(){var T=this;return(0,l.Z)(function*(){try{return yield T.storageService.set(n.Z.CelToCelTyC,!0),s.Either.success()}catch({message:a}){return s.Either.failure({message:a})}})()}setSource(T,a){try{return this.transfiyaStore.setProduct(T,a),s.Either.success(this.store.setSource(T,a))}catch({message:u}){return s.Either.failure({message:u})}}verfiyContact(T){var a=this;return(0,l.Z)(function*(){try{const u=yield a.requestAccounts(T),g=new c.Ap(T.number,T.contact);return a.transfiyaStore.setPhone(g,!0),a.transfiyaStore.setType("TRANSFER"),a.store.setPhone(T),s.Either.success(u.length>0)}catch({message:u}){return s.Either.failure({message:u})}})()}setAccount(T){try{return s.Either.success(this.store.setAccount(T))}catch({message:a}){return s.Either.failure({message:a})}}setAmount(T){try{return s.Either.success(this.store.setAmount(T))}catch({message:a}){return s.Either.failure({message:a})}}reset(){try{const T=this.store.itIsFromCustomer(),a=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:T,source:a})}catch({message:T}){return s.Either.failure({message:T})}}send(){var T=this;return(0,l.Z)(function*(){const a=(0,_.eZ)(T.store.currentState),u=yield T.execute(a);return T.eventBusService.emit(u.channel),s.Either.success({celToCel:a,status:u})})()}execute(T){try{return this.repository.send(T)}catch({message:a}){return Promise.resolve(o.LN.error(a))}}requestAccounts(T){var a=this;return(0,l.Z)(function*(){const u=a.store.getPhone();let g=a.store.getAccounts();return(u?.number!==T.number||!g?.length)&&(g=yield a.repository.verifyPhone(T.number),a.store.setAccounts(g)),g})()}}return M.\u0275fac=function(T){return new(T||M)(x.\u0275\u0275inject(y),x.\u0275\u0275inject(f.z),x.\u0275\u0275inject(d.d),x.\u0275\u0275inject(t.Yd),x.\u0275\u0275inject(t.V1))},M.\u0275prov=x.\u0275\u0275defineInjectable({token:M,factory:M.\u0275fac,providedIn:"root"}),M})()},98487:(B,P,e)=>{e.d(P,{T:()=>d});var l=e(30263),n=e(39904),t=e(95437),o=e(17758),s=e(99877);let d=(()=>{class _{constructor(i,p,r){this.modalConfirmation=i,this.mboProvider=p,this.managerCelToCel=r}execute(i=!0){i?this.confirmation():this.cancelConfirmed(!0)}backCustomer(i=!0){i?this.mboProvider.navigation.back(n.Z6.TRANSFERS.GENERIC.DESTINATION):this.backConfirmed(!0)}confirmation(i=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre celulares actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(i)}},decline:{label:"Continuar"}}).then(p=>"accept"===p)}cancelConfirmed(i){const p=this.managerCelToCel.reset();i&&p.when({success:({fromCustomer:r,source:m})=>{r?this.mboProvider.navigation.back(n.Z6.CUSTOMER.PRODUCTS.INFO,{productId:m.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}backConfirmed(i){const p=this.managerCelToCel.reset();i&&p.when({success:({fromCustomer:r,source:m})=>{r?this.mboProvider.navigation.back(n.Z6.TRANSFERS.GENERIC.DESTINATION,{productId:m.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.CELTOCEL.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(l.$e),s.\u0275\u0275inject(t.ZL),s.\u0275\u0275inject(o.Ey))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},56350:(B,P,e)=>{e.d(P,{z:()=>d});var l=e(39904),n=e(87956),t=e(20691),s=e(99877);let d=(()=>{class _ extends t.Store{constructor(i){super({confirmation:!1,fromCustomer:!1}),this.eventBusService=i,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(i,p=!1){this.reduce(r=>({...r,source:i,fromCustomer:p}))}getSource(){return this.select(({source:i})=>i)}itIsFromCustomer(){return this.select(({fromCustomer:i})=>i)}setPhone(i){this.reduce(p=>({...p,phone:i}))}getPhone(){return this.select(({phone:i})=>i)}setAccount(i){this.reduce(p=>({...p,account:i}))}getAccount(){return this.select(({account:i})=>i)}setAccounts(i){this.reduce(p=>({...p,accounts:i}))}getAccounts(){return this.select(({accounts:i})=>i)}setAmount(i){this.reduce(p=>({...p,amount:i,confirmation:!0}))}getAmount(){return this.select(({amount:i})=>i)}itIsConfirmation(){return this.select(({confirmation:i})=>i)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(n.Yd))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},4669:(B,P,e)=>{e.d(P,{AT:()=>n,FE:()=>o,YG:()=>t});var l=e(87903);class n{constructor(c,d,_,b){this.bank=c,this.accountType=d,this.accountNumber=_,this.owner=b}}class t{constructor(c,d,_,b,i,p,r,m){this.id=c,this.type=d,this.number=_,this.productType=b,this.nickname=i,this.bankId=p,this.bankName=r,this.owner=m}}class o{constructor(c,d,_,b){this.source=c,this.destination=d,this.amount=_,this.note=b}get destinationName(){const{nickname:c,productType:d}=this.destination;return c||(0,l.nk)(d)}get itIsAval(){return(0,l.bw)(this.destination.bankId)}}},99013:(B,P,e)=>{e.d(P,{eF:()=>_,Al:()=>h,ow:()=>L});var l=e(15861),n=e(27236),t=e(87956),o=e(74520),s=e(98699),c=e(99877);const d=n.Z.TransferUnregisteredInformation;let _=(()=>{class S{constructor(A,R){this.storageService=A,this.sessionStore=R}verify(){var A=this;return(0,l.Z)(function*(){try{return(yield A.storageService.get(d))?s.Either.success(!0):s.Either.failure({message:"",value:A.sessionStore.hasComplementaryServicesActivited()})}catch{return s.Either.success(!1)}})()}hidden(){var A=this;return(0,l.Z)(function*(){try{return yield A.storageService.set(d,!0),s.Either.success(!0)}catch{return s.Either.success(!1)}})()}}return S.\u0275fac=function(A){return new(A||S)(c.\u0275\u0275inject(t.V1),c.\u0275\u0275inject(o.f))},S.\u0275prov=c.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})();var b=e(53113),i=e(4669);function y(S){const{type:W,affiliation:A,amount:R,note:N,source:G,unregistered:U}=S;return new i.FE(G,"unregistered"===W?function x(S){return new i.YG(S.accountNumber,"unregistered",S.accountNumber,S.accountType,S.owner?.name||"",S.bank.id,S.bank.name,S.owner)}(U):function v(S){return new i.YG(S.id,"affiliation",S.number,S.type,S.nickname,S.bank.id,S.bank.name)}(A),R,N)}var f=e(71776),E=e(39904),M=e(87903);class C{constructor(W,A,R){this.approvalId=W,this.amount=A,this.currencyCode=R}}var T=e(42168),a=e(84757);let u=(()=>{class S{constructor(A){this.http=A}send(A){return"affiliation"===A.destination.type?this.affiliation(A):this.unregistered(A)}requestCost(A){const R=function m(S){const{amount:W,destination:A,source:R}=S;return{curAmt:W,fromDepAcctId:R.id,fromDepAcctType:R.type,rapidTransfer:"unregistered"===A.type,toDepAcctBankId:A.bankId,toDepAcctId:A.id,toDepAcctType:A.productType}}(A);return(0,T.firstValueFrom)(this.http.post(E.bV.TRANSFERS.COST,R).pipe((0,a.map)(({approvalId:N,athResponseError:G,curAmt:U})=>G?null:new C(N,U.amt,U.curCode))))}affiliation(A){const R=function p(S){const{amount:W,destination:A,source:R,note:N}=S;return{fromDepAcctId:R.id,fromDepAcctName:R.name,fromDepAcctType:R.type,fromNickName:R.nickname,toDepAcctId:A.id,toDepAcctBankId:A.bankId,toDepAcctName:A.bankName,toDepAcctType:A.productType,toNickName:A.nickname,curAmt:String(W),refId:N?.reference||"",memo:N?.description||""}}(A);return(0,T.firstValueFrom)(this.http.post(E.bV.TRANSFERS.AFFILIATIONS,R).pipe((0,a.map)(N=>(0,M.l1)(N,A.itIsAval?"SUCCESS":"PENDING")))).catch(N=>(0,M.rU)(N))}unregistered(A){const R=function r(S){const{amount:W,destination:A,source:R,note:N}=S;return{fromDepAcctId:R.id,fromDepAcctName:R.name,fromDepAcctType:R.type,fromNickName:R.nickname,toDepAcctBankId:A.bankId,toDepAcctType:A.productType,toDepAcctId:A.number,toDepAcctName:A.owner?.name,toNickName:"",toUserIdNumber:A.owner?.document.number,toUserIdType:A.owner?.document.type.code,curAmt:String(W),refId:N?.reference||"",memo:N?.description||"",category:"0",typeTransfer:"FAST_TRANS"}}(A);return(0,T.firstValueFrom)(this.http.post(E.bV.TRANSFERS.QUICKLY,R).pipe((0,a.map)(N=>(0,M.l1)(N,A.itIsAval?"SUCCESS":"PENDING")))).catch(N=>(0,M.rU)(N))}}return S.\u0275fac=function(A){return new(A||S)(c.\u0275\u0275inject(f.HttpClient))},S.\u0275prov=c.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})();var g=e(93424);let h=(()=>{class S{constructor(A,R,N){this.store=A,this.repository=R,this.eventBusService=N}setSource(A){try{return s.Either.success(this.store.setSource(A))}catch({message:R}){return s.Either.failure({message:R})}}setAffiliation(A){try{return s.Either.success(this.store.setAffiliation(A))}catch({message:R}){return s.Either.failure({message:R})}}setUnregistered(A){try{return s.Either.success(this.store.setUnregistered(A))}catch({message:R}){return s.Either.failure({message:R})}}setAmount(A,R){try{return s.Either.success(this.store.setAmount(A,R))}catch({message:N}){return s.Either.failure({message:N})}}setNote(A){try{return s.Either.success(this.store.setNote(A))}catch({message:R}){return s.Either.failure({message:R})}}removeNote(){try{return s.Either.success(this.store.removeNote())}catch({message:A}){return s.Either.failure({message:A})}}reset(){try{const A=this.store.itIsFromCustomer(),R=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:A,source:R})}catch({message:A}){return s.Either.failure({message:A})}}send(){var A=this;return(0,l.Z)(function*(){const R=y(A.store.currentState),N=yield A.execute(R);return A.eventBusService.emit(N.channel),s.Either.success({transfer:R,status:N})})()}execute(A){try{return this.repository.send(A)}catch({message:R}){return Promise.resolve(b.LN.error(R))}}}return S.\u0275fac=function(A){return new(A||S)(c.\u0275\u0275inject(g.J),c.\u0275\u0275inject(u),c.\u0275\u0275inject(t.Yd))},S.\u0275prov=c.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})();var I=e(78506),F=e(81536);let L=(()=>{class S{constructor(A,R,N,G,U,Z){this.products=A,this.productService=R,this.banks=N,this.repository=G,this.store=U,this.session=Z}source(){var A=this;return(0,l.Z)(function*(){try{const R=A.store.itIsConfirmation(),N=yield A.requestAccounts();return s.Either.success({confirmation:R,products:N})}catch({message:R}){return s.Either.failure({message:R})}})()}destination(A){var R=this;return(0,l.Z)(function*(){try{const N=R.store.itIsConfirmation(),G=R.store.getAffiliation(),U=yield R.requestAccount(A),Z=yield R.requestAccounts();let O=[];return U&&(O=yield R.productService.requestAffiliations(U)),s.Either.success({affiliations:O,confirmation:N,destination:G,products:Z,source:U})}catch({message:N}){return s.Either.failure({message:N})}})()}unregistered(){var A=this;return(0,l.Z)(function*(){try{const R=yield A.banks.requestTransactionals(),N=yield A.session.customer(),G=A.store.getUnregistered(),U=A.store.itIsConfirmation();return s.Either.success({banks:R,confirmation:U,customer:N,unregistered:G})}catch({message:R}){return s.Either.failure({message:R})}})()}amount(){try{return s.Either.success(this.store.selectForAmount())}catch({message:A}){return s.Either.failure({message:A})}}confirmation(){try{const A=y(this.store.currentState),{requiredCost:R}=this.store.currentState;return s.Either.success({transfer:A,cost$:R?this.repository.requestCost(A):void 0})}catch({message:A}){return s.Either.failure({message:A})}}requestAccounts(){return this.products.requestAccountsForTransfer()}requestAccount(A){var R=this;return(0,l.Z)(function*(){let N=R.store.getSource();return!N&&A&&(N=(yield R.requestAccounts()).find(({id:G})=>A===G),R.store.setSource(N,!0)),N})()}}return S.\u0275fac=function(A){return new(A||S)(c.\u0275\u0275inject(t.hM),c.\u0275\u0275inject(t.M5),c.\u0275\u0275inject(F.PI),c.\u0275\u0275inject(u),c.\u0275\u0275inject(g.J),c.\u0275\u0275inject(I._I))},S.\u0275prov=c.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})()},18767:(B,P,e)=>{e.d(P,{S:()=>_});var l=e(30263),n=e(39904),t=e(95437),o=e(99013),s=e(99877);const d=n.Z6.TRANSFERS.GENERIC;let _=(()=>{class b{constructor(p,r,m){this.modalConfirmation=p,this.mboProvider=r,this.managerTransfer=m}execute(p=!0){p?this.confirmation():this.cancelConfirmed(!0)}backCustomer(p=!0){p?this.mboProvider.navigation.back(d.DESTINATION):this.backConfirmed(!0)}confirmation(p=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre cuentas actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(p)}},decline:{label:"Continuar"}}).then(r=>"accept"===r)}cancelConfirmed(p){const r=this.managerTransfer.reset();p&&r.when({success:({fromCustomer:m,source:v})=>{m?this.mboProvider.navigation.back(n.Z6.CUSTOMER.PRODUCTS.INFO,{productId:v.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}backConfirmed(p){const r=this.managerTransfer.reset();p&&r.when({success:({fromCustomer:m,source:v})=>{m?this.mboProvider.navigation.back(d.DESTINATION,{productId:v.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}}return b.\u0275fac=function(p){return new(p||b)(s.\u0275\u0275inject(l.$e),s.\u0275\u0275inject(t.ZL),s.\u0275\u0275inject(o.Al))},b.\u0275prov=s.\u0275\u0275defineInjectable({token:b,factory:b.\u0275fac,providedIn:"root"}),b})()},93424:(B,P,e)=>{e.d(P,{J:()=>d});var l=e(39904),n=e(87956),t=e(20691),s=e(99877);let d=(()=>{class _ extends t.Store{constructor(i){super({confirmation:!1,fromCustomer:!1,type:"affiliation"}),this.eventBusService=i,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(i,p=!1){this.reduce(r=>({...r,source:i,fromCustomer:p}))}getSource(){return this.select(({source:i})=>i)}itIsFromCustomer(){return this.select(({fromCustomer:i})=>i)}setAffiliation(i){this.reduce(p=>({...p,affiliation:i,type:"affiliation"}))}getAffiliation(){return this.select(({affiliation:i})=>i)}setUnregistered(i){this.reduce(p=>({...p,unregistered:i,type:"unregistered"}))}getUnregistered(){return this.select(({unregistered:i})=>i)}setAmount(i,p){this.reduce(r=>({...r,amount:i,requiredCost:p,confirmation:!0}))}selectForAmount(){return this.select(({confirmation:i,amount:p,requiredCost:r,source:m,type:v})=>({amount:p,confirmation:i,requiredCost:r,source:m,type:v}))}setNote(i){this.reduce(p=>({...p,note:i}))}removeNote(){this.reduce(i=>({...i,note:void 0}))}itIsConfirmation(){return this.select(({confirmation:i})=>i)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(n.Yd))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},95137:(B,P,e)=>{e.d(P,{M:()=>y,X:()=>M});var l=e(15861),n=e(87956),t=e(53113),o=e(98699);class s{constructor(T,a,u,g,h){this.source=T,this.destination=a,this.amount=u,this.withDrawal=g,this.note=h}}function d(C){return new s(C.source,C.destination,C.amount,C.withDrawal,C.note)}var _=e(71776),b=e(39904),i=e(87903),p=e(42168),r=e(84757),m=e(99877);let v=(()=>{class C{constructor(a){this.http=a}send(a){return(0,p.firstValueFrom)(this.http.post(b.bV.TRANSFERS.TRUSTFUND,function c(C){return{curAmt:C.amount,fromDepAcctId:C.source.id,fromNickName:C.source.nickname,toDepAcctId:C.destination.id,toNickName:C.destination.nickname,withDrawal:C.withDrawal,refId:C.note?.reference||"",memo:C.note?.description||""}}(a)).pipe((0,r.map)(u=>(0,i.l1)(u,"SUCCESS")))).catch(u=>(0,i.rU)(u))}penality(a,u,g){return(0,p.firstValueFrom)(this.http.post(b.bV.TRANSFERS.TRUSTFUND_PENALITY,{curAmt:g,fromDepAcctId:a.id,fromNickName:a.nickname,toDepAcctId:u.id,toNickName:u.nickname,withDrawal:!0,refId:"",memo:""}).pipe((0,r.map)(({isError:h,fiduciaryInfo:I})=>{if(h)throw Error("Error al consultar penalizaci\xf3n de fiducias");return I.penality.curAmt.amt})))}}return C.\u0275fac=function(a){return new(a||C)(m.\u0275\u0275inject(_.HttpClient))},C.\u0275prov=m.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})();var x=e(71478);let y=(()=>{class C{constructor(a,u,g){this.store=a,this.repository=u,this.eventBusService=g}setSource(a,u){try{return o.Either.success(this.store.setSource(a,u))}catch({message:g}){return o.Either.failure({message:g})}}setDestination(a){try{return o.Either.success(this.store.setDestination(a))}catch({message:u}){return o.Either.failure({message:u})}}setAmount(a){try{return o.Either.success(this.store.setAmount(a))}catch({message:u}){return o.Either.failure({message:u})}}setNote(a){try{return o.Either.success(this.store.setNote(a))}catch({message:u}){return o.Either.failure({message:u})}}removeNote(){try{return o.Either.success(this.store.removeNote())}catch({message:a}){return o.Either.failure({message:a})}}reset(){try{return o.Either.success(this.store.reset())}catch({message:a}){return o.Either.failure({message:a})}}penality(a){var u=this;return(0,l.Z)(function*(){try{const{destination:g,source:h}=u.store.currentState;return o.Either.success(yield u.repository.penality(h,g,a))}catch({message:g}){return o.Either.failure({message:g})}})()}send(){var a=this;return(0,l.Z)(function*(){const u=d(a.store.currentState),g=yield a.execute(u);return a.eventBusService.emit(g.channel),o.Either.success({trustfund:u,status:g})})()}execute(a){try{return this.repository.send(a)}catch({message:u}){return Promise.resolve(t.LN.error(u))}}}return C.\u0275fac=function(a){return new(a||C)(m.\u0275\u0275inject(x.X),m.\u0275\u0275inject(v),m.\u0275\u0275inject(n.Yd))},C.\u0275prov=m.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})();var f=e(89148);const E=[f.E$.FicRentaPlus,f.E$.FicCerradoAPF3,f.E$.FicCerradoAPF5,f.E$.FicCerradoAPF6,f.E$.FicAPF7,f.E$.Occidecol];let M=(()=>{class C{constructor(a,u){this.products=a,this.store=u}source(){var a=this;return(0,l.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer(),g=yield a.getTrustfunds();return o.Either.success({products:u,trustfunds:g})}catch({message:u}){return o.Either.failure({message:u})}})()}destination(){var a=this;return(0,l.Z)(function*(){try{const{withDrawal:u}=a.store.getSource(),g=a.store.itIsConfirmation(),h=yield a.products.requestAccountsForTransfer(),I=yield a.getTrustfunds();return o.Either.success({products:u?h:I,confirmation:g})}catch({message:u}){return o.Either.failure({message:u})}})()}amount(){try{const{source:a}=this.store.getSource(),u=this.store.getAmount(),g=this.store.itIsConfirmation();return o.Either.success({source:a,amount:u,confirmation:g})}catch({message:a}){return o.Either.failure({message:a})}}confirmation(){try{const a=d(this.store.currentState);return o.Either.success({trustfund:a})}catch({message:a}){return o.Either.failure({message:a})}}getTrustfunds(){var a=this;return(0,l.Z)(function*(){try{return(yield a.products.requestTrustfunds()).filter(u=>!(0,i.G_)(u,E))}catch{return[]}})()}}return C.\u0275fac=function(a){return new(a||C)(m.\u0275\u0275inject(n.hM),m.\u0275\u0275inject(x.X))},C.\u0275prov=m.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})()},12884:(B,P,e)=>{e.d(P,{S:()=>d});var l=e(30263),n=e(39904),t=e(95437),o=e(95137),s=e(99877);let d=(()=>{class _{constructor(i,p,r){this.modalConfirmation=i,this.mboProvider=p,this.managerTrustfund=r}execute(i=!0){i?this.confirmation():this.cancelConfirmed(!0)}confirmation(i=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia de fiducia actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(i)}},decline:{label:"Continuar"}}).then(p=>"accept"===p)}cancelConfirmed(i){this.managerTrustfund.reset(),i&&this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(l.$e),s.\u0275\u0275inject(t.ZL),s.\u0275\u0275inject(o.M))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},71478:(B,P,e)=>{e.d(P,{X:()=>d});var l=e(39904),n=e(87956),t=e(20691),s=e(99877);let d=(()=>{class _ extends t.Store{constructor(i){super({confirmation:!1,withDrawal:!1}),this.eventBusService=i,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setSource(i,p){this.reduce(r=>({...r,source:i,withDrawal:p}))}getSource(){return this.select(({source:i,withDrawal:p})=>({source:i,withDrawal:p}))}setDestination(i){this.reduce(p=>({...p,destination:i}))}getDestination(){return this.select(({destination:i})=>i)}setAmount(i){this.reduce(p=>({...p,amount:i,confirmation:!0}))}getAmount(){return this.select(({amount:i})=>i)}setNote(i){this.reduce(p=>({...p,note:i}))}getNote(){return this.select(({note:i})=>i)}removeNote(){this.reduce(i=>({...i,note:void 0}))}itIsConfirmation(){return this.select(({confirmation:i})=>i)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(n.Yd))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},42789:(B,P,e)=>{e.d(P,{Ap:()=>o,Mj:()=>_,Z0:()=>d,bq:()=>p,gl:()=>i,sF:()=>c,vt:()=>s});var l=e(29306),n=e(87903),t=e(8834);class o{constructor(m,v){this.number=m,this.contact=v,this.numberFormat=(0,n.wW)(m)}}class s{constructor(m,v,x){this.indicator=m,this.number=v,this.name=x,this.title=this.name,this.subtitle=(0,n.wW)(this.number)}get phone(){return`${this.indicator}${this.number}`}}class c{constructor(m,v,x,y,f,E,M){this.uuid=m,this.approvalId=v,this.number=x,this.amount=y,this.currencyCode=f,this.description=E,this.bank=M;const C=(0,t.K)(y);this.phone=(0,n.wW)(x),this.title=`El ${this.phone} te envi\xf3 ${C}`}}class d{constructor(m,v,x,y,f,E,M){this.uuid=m,this.approvalId=v,this.number=x,this.amount=y,this.currencyCode=f,this.description=E,this.bank=M;const C=(0,t.K)(y);this.phone=(0,n.wW)(x),this.title=`El ${this.phone} te solicita ${C}`}}class _{constructor(m,v,x,y,f,E,M,C){this.type=m,this.product=v,this.contact=x,this.amount=y,this.description=f,this.trustedContact=E,this.approved=M,this.pending=C}}class i{constructor(m,v,x,y,f,E,M){this.uuid=m,this.reference=v,this.phone=x,this.amount=y,this.date=f,this.category=E,this.description=M,this.phoneFormat=(0,n.wW)(x.length>12?x.slice(0,10):x),this.color=function b(r){return"Solicitar"===r?"info":"success"}(E),this.title=`Ref. ${this.reference}`}}class p extends l.Ay{static empty(){return new p([],0,!0)}}},29044:(B,P,e)=>{e.d(P,{CA:()=>p,Li:()=>_,Me:()=>x,PA:()=>b,S7:()=>d,SS:()=>m,Tb:()=>i,av:()=>c,f3:()=>r,vI:()=>y});var l=e(29306),n=e(53113),t=e(33876),s=e(42789);function c(f){return{acctIdFrom:f.product.id,cellNumTo:f.contact.phone,curAmt:f.amount,desc:f.description||""}}function d(f){return{acctIdTo:f.product.id,cellNumTo:f.contact.phone,curAmt:f.amount,desc:f.description||""}}function _(f){const E=f.RefInfo.filter(C=>"CUS"===C.RefType),M=f.RefInfo.filter(C=>"TRANSFER"===C.RefId);return new s.Z0((0,t.v4)(),E[0]?.RefId,f.PhoneNum.Phone.toString(),f.CurAmt.Amt,"COP",M[0]?.RefType,l.Br.fromId(f.DepAcctIdTo.DepAcctId.BankInfo.BankId))}function b(f){return{curAmt:{Amt:f.pending?.amount,CurCode:f.pending?.currencyCode},description:f.pending?.description,phone:f.pending?.number,productHash:f.product.id,transaction:{ApplyTrx:"true",ApprovalId:f.pending?.approvalId,Desc:f.pending?.description||""}}}function i(f){return{curAmt:{Amt:f.amount,CurCode:f.currencyCode},description:f.description,phone:f.number,productHash:"",transaction:{ApplyTrx:"false",ApprovalId:f.approvalId,Desc:f.description||""}}}function p(f){const E=f.refInfo.filter(C=>"CUS"===C.refType),M=f.refInfo.filter(C=>"TRANSFER"===C.refId);return new s.sF((0,t.v4)(),E[0]?.refId,f.phoneNum.phone.toString(),f.curAmt.amt,"COP",M[0]?.refType)}function r(f){return{acctIdTo:f.product.id,amt:f.approved?.amount,cellNumFrom:f.approved?.number,description:f.approved?.description||"",favorite:f.trustedContact,refId:f.approved?.approvalId}}function m(f){return{amt:f.amount,cellNumFrom:f.number,refId:f.approvalId}}function x({content:f,totalPage:E}){return new s.bq(f.map(M=>function v(f){return new s.gl((0,t.v4)(),f.cus,f.cellphone,+f.amount,new n.ou(f.date),f.category,f.description)}(M)),E)}function y(f){return new s.Mj(f.type,f.product,f.phone?new s.vt("57",f.phone.number,f.phone.contact||"Celular"):void 0,f.amount,f.description||"",f.trustedContact||!1,f.approved,f.pending)}},17698:(B,P,e)=>{e.d(P,{_y:()=>o,Pm:()=>m,NF:()=>v,UT:()=>x,UD:()=>y,xt:()=>f,ow:()=>E,tE:()=>M});var l=e(87956),n=e(98699),t=e(99877);let o=(()=>{class C{constructor(a){this.productsService=a}configuration(){return this.productsService.requestAccountsForTransfer().then(a=>Promise.resolve(n.Either.success({accounts:a})))}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})();var s=e(15861),c=e(27236),d=e(53113),_=e(56350);const b={ask:!1,transfer:!1};var i=e(29044),p=e(19198),r=e(17881);let m=(()=>{class C{constructor(a,u,g,h,I,F,L){this.repository=a,this.approveds=u,this.pendings=g,this.store=h,this.celToCelStore=I,this.storageService=F,this.eventBusService=L}approvedTyC(){var a=this;return(0,s.Z)(function*(){try{return yield a.storageService.set(c.Z.TransfiyaTyC,!0),n.Either.success(a.store.getType())}catch({message:u}){return n.Either.failure({message:u})}})()}ignoreTransfer(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.requestStateHelpers();return u.transfer=!0,yield a.saveStateHelpers(u),n.Either.success(!0)}catch{return n.Either.success(!1)}})()}ignoreAsk(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.requestStateHelpers();return u.ask=!0,yield a.saveStateHelpers(u),n.Either.success(!0)}catch{return n.Either.success(!1)}})()}setProduct(a){try{return n.Either.success(this.store.setProduct(a))}catch({message:u}){return n.Either.failure({message:u})}}setContact(a){try{return n.Either.success(this.store.setPhone(a))}catch({message:u}){return n.Either.failure({message:u})}}setAmount(a){try{return n.Either.success(this.store.setAmount(a))}catch({message:u}){return n.Either.failure({message:u})}}setDescription(a){try{return n.Either.success(this.store.setDescription(a))}catch({message:u}){return n.Either.failure({message:u})}}removeDescription(){try{return n.Either.success(this.store.removeDescription())}catch({message:a}){return n.Either.failure({message:a})}}setApproved(a){try{return n.Either.success(this.store.setApproved(a))}catch({message:u}){return n.Either.failure({message:u})}}setPending(a){try{return n.Either.success(this.store.setPending(a))}catch({message:u}){return n.Either.failure({message:u})}}setTrustedContact(a){try{return n.Either.success(this.store.setTrustedContact(a))}catch({message:u}){return n.Either.failure({message:u})}}reset(){try{const a=this.store.itIsFromCustomer(),u=this.store.getProduct();return this.celToCelStore.reset(),n.Either.success({fromCustomer:a,source:u})}catch({message:a}){return n.Either.failure({message:a})}}confirmTransfer(){var a=this;return(0,s.Z)(function*(){const u=(0,i.vI)(a.store.currentState),g=yield a.executeTransfer(u);return a.eventBusService.emit(g.channel),n.Either.success({transfiya:u,status:g})})()}confirmAsk(){var a=this;return(0,s.Z)(function*(){const u=(0,i.vI)(a.store.currentState),g=yield a.executeAsk(u);return a.eventBusService.emit(g.channel),n.Either.success({transfiya:u,status:g})})()}confirmApproved(){var a=this;return(0,s.Z)(function*(){const u=(0,i.vI)(a.store.currentState),g=yield a.executeApproved(u);return a.eventBusService.emit(g.channel),n.Either.success({transfiya:u,status:g})})()}rejectApproved(a){return this.approveds.reject(a).then(u=>"ERROR"===u.type?n.Either.failure(u):n.Either.success(u))}confirmPending(){var a=this;return(0,s.Z)(function*(){const u=(0,i.vI)(a.store.currentState),g=yield a.executePending(u);return a.eventBusService.emit(g.channel),n.Either.success({transfiya:u,status:g})})()}rejectPending(a){return this.pendings.reject(a).then(u=>"ERROR"===u.type?n.Either.failure(u):n.Either.success(u))}requestStateHelpers(){var a=this;return(0,s.Z)(function*(){return(yield a.storageService.get(c.Z.TransfiyaHelpers))||b})()}saveStateHelpers(a){return this.storageService.set(c.Z.TransfiyaHelpers,a)}executeTransfer(a){try{return this.repository.transfer(a)}catch({message:u}){return Promise.resolve(d.LN.error(u))}}executeAsk(a){try{return this.repository.ask(a)}catch({message:u}){return Promise.resolve(d.LN.error(u))}}executeApproved(a){try{return this.approveds.approved(a)}catch({message:u}){return Promise.resolve(d.LN.error(u))}}executePending(a){try{return this.pendings.approved(a)}catch({message:u}){return Promise.resolve(d.LN.error(u))}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(p.JD),t.\u0275\u0275inject(p.gQ),t.\u0275\u0275inject(p.ZM),t.\u0275\u0275inject(r.d),t.\u0275\u0275inject(_.z),t.\u0275\u0275inject(l.V1),t.\u0275\u0275inject(l.Yd))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),v=(()=>{class C{constructor(a,u,g){this.productsService=a,this.repository=u,this.store=g}home(){var a=this;return(0,s.Z)(function*(){try{return n.Either.success(yield a.repository.request())}catch{return n.Either.success([])}})()}information(){const a=this.store.getApproved();return a?n.Either.success(a):n.Either.failure({message:"Approved empty"})}destination(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.productsService.requestAccountsForTransfer();return n.Either.success({products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}confirmation(){try{const a=(0,i.vI)(this.store.currentState);return n.Either.success({transfiya:a})}catch({message:a}){return n.Either.failure({message:a})}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM),t.\u0275\u0275inject(p.gQ),t.\u0275\u0275inject(r.d))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),x=(()=>{class C{constructor(a,u){this.products=a,this.store=u}destination(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer();return n.Either.success({products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}source(){try{const a=this.store.itIsConfirmation(),u=this.store.getPhone(),g=this.store.getProduct();return n.Either.success({confirmation:a,phone:u,product:g})}catch({message:a}){return n.Either.failure({message:a})}}amount(){try{const a=this.store.itIsConfirmation(),u=this.store.getAmount(),g=this.store.getProduct();return n.Either.success({confirmation:a,product:g,amount:u})}catch({message:a}){return n.Either.failure({message:a})}}confirmation(){try{const a=(0,i.vI)(this.store.currentState);return n.Either.success({transfiya:a})}catch({message:a}){return n.Either.failure({message:a})}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM),t.\u0275\u0275inject(r.d))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),y=(()=>{class C{constructor(a){this.histories=a}firstPage(){var a=this;return(0,s.Z)(function*(){try{return n.Either.success(yield a.histories.request())}catch({message:u}){return n.Either.failure({message:u})}})()}nextPage(){var a=this;return(0,s.Z)(function*(){try{return n.Either.success(yield a.histories.nextPage())}catch({message:u}){return n.Either.failure({message:u})}})()}refresh(a){var u=this;return(0,s.Z)(function*(){try{return n.Either.success(yield u.histories.refresh(a))}catch({message:g}){return n.Either.failure({message:g})}})()}historyForUuid(a){try{const u=this.histories.requestForUuid(a);return u?n.Either.success(u):n.Either.failure()}catch({message:u}){return n.Either.failure({message:u})}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(p._y))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),f=(()=>{class C{constructor(a,u,g){this.products=a,this.pendings=u,this.store=g}home(){var a=this;return(0,s.Z)(function*(){try{return n.Either.success(yield a.pendings.request())}catch{return n.Either.success([])}})()}source(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer();return n.Either.success({products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}confirmation(){try{const a=(0,i.vI)(this.store.currentState);return n.Either.success({transfiya:a})}catch({message:a}){return n.Either.failure({message:a})}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM),t.\u0275\u0275inject(p.ZM),t.\u0275\u0275inject(r.d))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),E=(()=>{class C{constructor(a,u,g){this.products=a,this.store=u,this.celToCelStore=g}source(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer();return n.Either.success({products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}destination(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer(),g=a.store.itIsConfirmation(),h=a.store.getPhone(),I=a.store.getProduct();return n.Either.success({confirmation:g,phone:h,product:I,products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}amount(){try{const a=this.store.itIsConfirmation(),u=this.store.getAmount(),g=this.store.getProduct(),h=this.store.itIsFromCelToCel(),I=this.celToCelStore.getAccounts();return n.Either.success({amount:u,confirmation:a,fromCelToCel:h,hasAccounts:!!I&&I.length>0,product:g})}catch({message:a}){return n.Either.failure({message:a})}}confirmation(){try{const a=(0,i.vI)(this.store.currentState);return n.Either.success({transfiya:a})}catch({message:a}){return n.Either.failure({message:a})}}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM),t.\u0275\u0275inject(r.d),t.\u0275\u0275inject(_.z))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})(),M=(()=>{class C{constructor(a,u,g){this.products=a,this.histories=u,this.storageService=g}allowAccess(){var a=this;return(0,s.Z)(function*(){try{return n.Either.success((yield a.storageService.get(c.Z.TransfiyaTyC))||!1)}catch{return n.Either.success(!1)}})()}home(){var a=this;return(0,s.Z)(function*(){try{const u=a.histories.request();return n.Either.success({history$:u})}catch({message:u}){return n.Either.failure({message:u})}})()}contacts(){var a=this;return(0,s.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer();return n.Either.success({products:u})}catch({message:u}){return n.Either.failure({message:u})}})()}requiredHelperTransfer(){var a=this;return(0,s.Z)(function*(){try{const{transfer:u}=yield a.getHelpersState(),g=(yield a.storageService.get(c.Z.TransfiyaTyC))||!1;return n.Either.success({helper:u,transfiya:g})}catch{return n.Either.success({helper:!1,transfiya:!1})}})()}requiredHelperAsk(){var a=this;return(0,s.Z)(function*(){try{const{ask:u}=yield a.getHelpersState(),g=(yield a.storageService.get(c.Z.TransfiyaTyC))||!1;return n.Either.success({helper:u,transfiya:g})}catch{return n.Either.success({helper:!1,transfiya:!1})}})()}getHelpersState(){var a=this;return(0,s.Z)(function*(){return(yield a.storageService.get(c.Z.TransfiyaHelpers))||b})()}}return C.\u0275fac=function(a){return new(a||C)(t.\u0275\u0275inject(l.hM),t.\u0275\u0275inject(p._y),t.\u0275\u0275inject(l.V1))},C.\u0275prov=t.\u0275\u0275defineInjectable({token:C,factory:C.\u0275fac,providedIn:"root"}),C})()},73004:(B,P,e)=>{e.d(P,{c:()=>d});var l=e(30263),n=e(39904),t=e(95437),o=e(17698),s=e(99877);let d=(()=>{class _{constructor(i,p,r){this.modalConfirmation=i,this.mboProvider=p,this.managerTransfiya=r}execute(i=!0){i?this.confirmation():this.cancelConfirmed(!0)}confirmation(i=!0){return this.modalConfirmation.execute({title:"Abandonar TransfiYa",message:"\xbfEstas seguro que deseas cancelar la transacci\xf3n de TransfiYa actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(i)}},decline:{label:"Continuar"}}).then(p=>"accept"===p)}cancelConfirmed(i){const p=this.managerTransfiya.reset();i&&p.when({success:({fromCustomer:r,source:m})=>{r?this.mboProvider.navigation.back(n.Z6.CUSTOMER.PRODUCTS.INFO,{productId:m.id}):this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(n.Z6.TRANSFERS.HOME)}})}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(l.$e),s.\u0275\u0275inject(t.ZL),s.\u0275\u0275inject(o.Pm))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},19198:(B,P,e)=>{e.d(P,{JD:()=>E,ZM:()=>C,_y:()=>a,gQ:()=>M,pv:()=>T});var l=e(15861),n=e(71776),o=e(39904),s=e(87903),c=e(87956),d=e(53113),_=e(42168),i=e(84757),r=e(42789),m=e(29044),v=e(99877);const y=o.bV.TRANSFERS.TRANSFIYA,f={items:"10",order:"DESC",orderField:"xferEffDt"};let E=(()=>{class u{constructor(h,I){this.http=h,this.fingerprintService=I}transfer(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.av)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.SEND,L).pipe((0,i.map)(()=>new d.LN("PENDING","Si en 12 horas tu contacto no acepta el dinero, regresar\xe1 a tu cuenta.")))).catch(S=>S instanceof n.HttpErrorResponse&&200===S.status?new d.LN("PENDING","Si en 12 horas tu contacto no acepta el dinero, regresar\xe1 a tu cuenta."):(0,s.rU)(S))})()}ask(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.S7)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.REQUEST,L).pipe((0,i.map)(()=>new d.LN("PENDING","La solicitud se est\xe1 procesando.")))).catch(S=>S instanceof n.HttpErrorResponse&&200===S.status?new d.LN("PENDING","La solicitud se est\xe1 procesando."):(0,s.rU)(S))})()}}return u.\u0275fac=function(h){return new(h||u)(v.\u0275\u0275inject(n.HttpClient),v.\u0275\u0275inject(c.ew))},u.\u0275prov=v.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),M=(()=>{class u{constructor(h,I){this.http=h,this.fingerprintService=I}request(){return(0,_.firstValueFrom)(this.http.get(y.APPROVED.CATALOG).pipe((0,i.map)(({xferInfo:h})=>h.reduce((I,F)=>{const L=(0,m.CA)(F);return L.approvalId&&I.push(L),I},[]))))}approved(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.f3)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.APPROVED.RECEIVE,L).pipe((0,i.map)(S=>(0,s.l1)(S,"SUCCESS")))).catch(S=>(0,s.rU)(S))})()}reject(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.SS)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.APPROVED.REJECT,L).pipe((0,i.map)(S=>(0,s.l1)(S,"SUCCESS")))).catch(S=>(0,s.rU)(S))})()}}return u.\u0275fac=function(h){return new(h||u)(v.\u0275\u0275inject(n.HttpClient),v.\u0275\u0275inject(c.ew))},u.\u0275prov=v.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),C=(()=>{class u{constructor(h,I){this.http=h,this.fingerprintService=I}request(){return(0,_.firstValueFrom)(this.http.post(y.PENDING.CATALOG,{}).pipe((0,i.map)(h=>h?h.reduce((I,F)=>{const L=(0,m.Li)(F);return L.approvalId&&I.push(L),I},[]):[])))}approved(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.PA)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.PENDING.RESPONSE,L).pipe((0,i.map)(()=>new d.LN("PENDING","Tu contacto recibir\xe1 el dinero en su cuenta.")))).catch(S=>S instanceof n.HttpErrorResponse&&200===S.status?new d.LN("PENDING","Tu contacto recibir\xe1 el dinero en su cuenta."):(0,s.rU)(S))})()}reject(h){var I=this;return(0,l.Z)(function*(){const F=yield I.fingerprintService.getInfo(),L={...(0,m.Tb)(h),deviceFingerPrint:F};return(0,_.firstValueFrom)(I.http.post(y.PENDING.RESPONSE,L).pipe((0,i.map)(()=>new d.LN("SUCCESS","La solicitud fue eliminada exitosamente.")))).catch(S=>S instanceof n.HttpErrorResponse&&200===S.status?new d.LN("SUCCESS","La solicitud fue eliminada exitosamente."):(0,s.rU)(S))})()}}return u.\u0275fac=function(h){return new(h||u)(v.\u0275\u0275inject(n.HttpClient),v.\u0275\u0275inject(c.ew))},u.\u0275prov=v.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),T=(()=>{class u{constructor(h){this.http=h}request(h){return(0,_.firstValueFrom)(this.http.post(y.CONTACTS,{productHash:h.id,requestType:"POST"}).pipe((0,i.map)(({message:I})=>{if("string"==typeof I)throw Error("Error in request phones of product");return I.TrustRelationshipInfo?.map(({ContactInfo:F})=>new r.Ap(F.PhoneNum.Phone,(0,s.wW)(F.PhoneNum.Phone)))||[]})))}remove(h,I){return(0,_.firstValueFrom)(this.http.post(y.CONTACTS,{phoneContact:I.number,productHash:h.id,requestType:"DELETE"}).pipe((0,i.map)(({message:F})=>F)))}}return u.\u0275fac=function(h){return new(h||u)(v.\u0275\u0275inject(n.HttpClient))},u.\u0275prov=v.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})(),a=(()=>{class u{constructor(h,I){this.http=h,I.subscribes(o.PU,()=>{this.history=void 0})}request(h){if(this.history)return Promise.resolve(this.history);const I=h||o.cC,{end:F,start:L}=I.getFormat();return(0,_.firstValueFrom)(this.remote({...f,page:"0",StartDt:L,EndDt:F}).pipe((0,i.tap)(S=>{S.range=I,this.history=S})))}refresh(h){const{end:I,start:F}=h.getFormat();return(0,_.firstValueFrom)(this.remote({...f,page:"0",EndDt:I,StartDt:F}).pipe((0,i.tap)(L=>{L.range=h,this.history=L})))}requestForUuid(h){return this.history?.requestForUuid(h)}nextPage(){var h=this;return(0,l.Z)(function*(){if(!h.history)return h.request().then(({collection:F})=>F);const I=h.history.range.getFormat();return(0,_.firstValueFrom)(h.remote({...f,page:h.history.currentPage.toString(),StartDt:I.start,EndDt:I.end}).pipe((0,i.map)(({collection:F})=>(h.history.merge(F),h.history.collection))))})()}remote(h){return this.http.post(y.HISTORY,h).pipe((0,i.map)(I=>"failed"===I.Response?r.bq.empty():(0,m.Me)(I)),(0,i.catchError)(I=>{if(this.history)return(0,_.of)(r.bq.empty());throw I}))}}return u.\u0275fac=function(h){return new(h||u)(v.\u0275\u0275inject(n.HttpClient),v.\u0275\u0275inject(c.Yd))},u.\u0275prov=v.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()},17881:(B,P,e)=>{e.d(P,{d:()=>d});var l=e(39904),n=e(87956),t=e(20691),s=e(99877);let d=(()=>{class _ extends t.Store{constructor(i){super({confirmation:!1,fromCustomer:!1,type:"TRANSFER"}),this.eventBusService=i,this.eventBusService.subscribes(l.PU,()=>{this.reset()})}setType(i){this.reduce(p=>({...p,type:i}))}getType(){return this.select(({type:i})=>i)}itIsFromCustomer(){return this.select(({fromCustomer:i})=>i)}setProduct(i,p=!1){this.reduce(r=>({...r,product:i,fromCustomer:p}))}getProduct(){return this.select(({product:i})=>i)}setPhone(i,p=!1){this.reduce(r=>({...r,phone:i,fromCelToCel:p}))}getPhone(){return this.select(({phone:i})=>i)}setAmount(i){this.reduce(p=>({...p,amount:i,confirmation:!0}))}getAmount(){return this.select(({amount:i})=>i)}itIsFromCelToCel(){return this.select(({fromCelToCel:i})=>i)}setDescription(i){this.reduce(p=>({...p,description:i}))}removeDescription(){this.reduce(i=>({...i,description:void 0}))}setApproved(i){this.reduce(p=>({...p,approved:i}))}getApproved(){return this.select(({approved:i})=>i)}setPending(i){this.reduce(p=>({...p,pending:i}))}getPending(){return this.select(({pending:i})=>i)}setTrustedContact(i){this.reduce(p=>({...p,trustedContact:i}))}itIsConfirmation(){return this.select(({confirmation:i})=>i)}}return _.\u0275fac=function(i){return new(i||_)(s.\u0275\u0275inject(n.Yd))},_.\u0275prov=s.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},48181:(B,P,e)=>{e.r(P),e.d(P,{MboTransfersModule:()=>Z});var l=e(17007),n=e(78007),t=e(18632),o=e(39904),s=e(80045),c=e(18101),d=e(99877);const{RESULT:_,SOURCE:b}=o.Z6.TRANSFERS.ADVANCE;let i=(()=>{class O{constructor(j,V){this.store=j,this.cancelProvider=V}canDeactivate(j,V,H,X){const[Y]=X.url.split("?");return o.YG.includes(Y)?Promise.resolve(!0):this.requireConfirmation(H)?this.cancelProvider.confirmation(!1):Promise.resolve(!0)}requireConfirmation(j){const[V]=j.url.split("?");if(V===b){const H=this.store.itIsConfirmation();return H||this.store.reset(),H}return V!==_&&!!this.store.getSource()}}return O.\u0275fac=function(j){return new(j||O)(d.\u0275\u0275inject(c.l),d.\u0275\u0275inject(s.j))},O.\u0275prov=d.\u0275\u0275defineInjectable({token:O,factory:O.\u0275fac,providedIn:"root"}),O})();e(98487),e(56350);var y=e(18767),f=e(93424);const{RESULT:E,SOURCE:M}=o.Z6.TRANSFERS.GENERIC,{DESTINATION:C}=o.Z6.TRANSFERS.TAG_AVAL,{DESTINATION:T}=o.Z6.TRANSFERS.CELTOCEL.SEND,a=[C,T];let u=(()=>{class O{constructor(j,V){this.store=j,this.cancelProvider=V}canDeactivate(j,V,H,X){const[Y]=X.url.split("?");return o.YG.includes(Y)||a.includes(Y)?Promise.resolve(!0):this.requireConfirmation(H)?this.cancelProvider.confirmation(!1):Promise.resolve(!0)}requireConfirmation(j){const[V]=j.url.split("?");if(V===M){const H=this.store.itIsConfirmation();return H||this.store.reset(),H}return V!==E&&!!this.store.getSource()}}return O.\u0275fac=function(j){return new(j||O)(d.\u0275\u0275inject(f.J),d.\u0275\u0275inject(y.S))},O.\u0275prov=d.\u0275\u0275defineInjectable({token:O,factory:O.\u0275fac,providedIn:"root"}),O})();e(73004),e(17881);var W=e(12884),A=e(71478);const{RESULT:R,SOURCE:N}=o.Z6.TRANSFERS.TRUSTFUND,U=[{path:"",loadChildren:()=>e.e(8705).then(e.bind(e,18705)).then(O=>O.MboTransfersHomeModule)},{path:"generic",loadChildren:()=>e.e(7420).then(e.bind(e,97420)).then(O=>O.MboTransferGenericModule),canDeactivate:[u]},{path:"tag-aval",loadChildren:()=>e.e(7356).then(e.bind(e,47356)).then(O=>O.MboTransferTagAvalModule),canActivate:[t.a]},{path:"cel-to-cel",loadChildren:()=>e.e(2021).then(e.bind(e,72021)).then(O=>O.MboTransferCelToCelModule),canActivate:[t.a]},{path:"advance",loadChildren:()=>e.e(3245).then(e.bind(e,83245)).then(O=>O.MboTransferAdvanceModule),canDeactivate:[i]},{path:"trustfund",loadChildren:()=>e.e(6496).then(e.bind(e,96496)).then(O=>O.MboTransferTrustfundModule),canDeactivate:[(()=>{class O{constructor(j,V){this.store=j,this.cancelProvider=V}canDeactivate(j,V,H,X){const[Y]=X.url.split("?");return o.YG.includes(Y)?Promise.resolve(!0):this.requireConfirmation(H)?this.cancelProvider.confirmation(!1):Promise.resolve(!0)}requireConfirmation(j){const[V]=j.url.split("?");if(V===N){const H=this.store.itIsConfirmation();return H||this.store.reset(),H}return V!==R&&!!this.store.getSource()}}return O.\u0275fac=function(j){return new(j||O)(d.\u0275\u0275inject(A.X),d.\u0275\u0275inject(W.S))},O.\u0275prov=d.\u0275\u0275defineInjectable({token:O,factory:O.\u0275fac,providedIn:"root"}),O})()]},{path:"transfiya",loadChildren:()=>e.e(1638).then(e.bind(e,71638)).then(O=>O.MboTransfiyaModule),canActivate:[t.a]}];let Z=(()=>{class O{}return O.\u0275fac=function(j){return new(j||O)},O.\u0275mod=d.\u0275\u0275defineNgModule({type:O}),O.\u0275inj=d.\u0275\u0275defineInjector({imports:[l.CommonModule,n.RouterModule.forChild(U)]}),O})()},22816:(B,P,e)=>{e.d(P,{S:()=>l});class l{constructor(t=4){this.scrollError=t,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(t){this.scrollLeft=t.scrollLeft,this.scrollTop=t.scrollTop,this.scrollWidth=t.scrollWidth,this.scrollHeight=t.scrollHeight,this.clientWidth=t.clientWidth,this.clientHeight=t.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const t=this.scrollHeight-this.clientHeight;return t>0?this.scrollTop/t*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const t=this.scrollWidth-this.clientWidth;return t>0?this.scrollLeft/t*100:0}}}}]);