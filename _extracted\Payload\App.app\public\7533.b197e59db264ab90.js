(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7533],{30021:(N,p,e)=>{e.d(p,{L0:()=>h,QO:()=>s,pF:()=>d});var m=e(29306),a=e(31707);class h{constructor(l,v,D,P){this.number=l,this.type=v,this.name=D,this.bank=P}}class s{constructor(l,v,D,P,R,y,f,I,u,t,c,o){this.uuid=l,this.reference=v,this.date=D,this.type=P,this.source=R,this.nickname=y,this.destination=f,this.amount=I,this.currencyCode=u,this.method=t,this.status=c,this.approvedCode=o,this.typeLabel=a.f[this.type],this.title=this.nickname||`Pago ${this.typeLabel||"Desconocido"}`,this.supplier=this.nickname||this.destination}get dateFormat(){return this.date.dateFormat}get timeFormat(){return this.date.timeFormat}}class d extends m.Ay{static empty(){return new d([],0,!0)}}},82716:(N,p,e)=>{e.d(p,{UD:()=>A,Y_:()=>M,co:()=>S});var m=e(15861),a=e(98699),h=e(19799),s=e(71776),d=e(39904),C=e(87956),l=e(42168),v=e(84757),P=e(30021),R=e(29306),y=e(53113),f=e(70658),I=e(33876);const u={SUCCESS:{color:"success",label:"Exitoso"},ERROR:{color:"danger",label:"Fallido"}};var o=e(99877);const g={items:String(10),order:"DESC",orderField:"effDt"};let E=(()=>{class i{constructor(r,n){this.http=r,this.eventBusService=n,this.eventBusService.subscribes(d.PU,()=>{this.history=void 0})}request(r){var n=this;return(0,m.Z)(function*(){if(n.history)return n.history;const b=r||d.cC,{end:F,start:U}=b.getFormat();return(0,l.firstValueFrom)(n.remote({...g,page:"0",StartDt:U,EndDt:F}).pipe((0,v.tap)(j=>{j.range=b,n.history=j})))})()}refresh(r){const{end:n,start:b}=r.getFormat();return(0,l.firstValueFrom)(this.remote({...g,page:"0",EndDt:n,StartDt:b}).pipe((0,v.tap)(F=>{F.range=r,this.history=F})))}requestForUuid(r){return this.history?.requestForUuid(r)}nextPage(){var r=this;return(0,m.Z)(function*(){if(!r.history)return r.request().then(({collection:b})=>b);const n=r.history.range.getFormat();return(0,l.firstValueFrom)(r.remote({...g,page:r.history.currentPage.toString(),StartDt:n.start,EndDt:n.end}).pipe((0,v.map)(b=>(r.history.merge(b.collection),r.history.collection))))})()}remote(r){return this.http.get(d.bV.PAYMENTS.HISTORY,{params:{...r}}).pipe((0,v.map)(n=>function c(i){return new P.pF(i.content.map(O=>function t(i){return new P.QO((0,I.v4)(),i.nie||i.paymentReference,new y.ou(i.effDt),i.paymentType,new P.L0(i.fromProductId,i.fromProductType,i.fromNickName,new R.Br(f.N.bankId,f.N.bankName)),i.toNickname,i.toPaymentName,+i.amt,i.curCode||"COP",i.pmtMethod,u[i.trnState],i.approvalId)}(O)),i.totalPage)}(n)),(0,v.catchError)(n=>{if(this.history)return(0,l.of)(P.pF.empty());throw n}))}}return i.\u0275fac=function(r){return new(r||i)(o.\u0275\u0275inject(s.HttpClient),o.\u0275\u0275inject(C.Yd))},i.\u0275prov=o.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),M=(()=>{class i{constructor(r){this.histories=r}payments(){var r=this;return(0,m.Z)(function*(){try{const n=r.histories.request();return a.Either.success({history$:n})}catch({message:n}){return a.Either.failure({message:n})}})()}}return i.\u0275fac=function(r){return new(r||i)(o.\u0275\u0275inject(E))},i.\u0275prov=o.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),A=(()=>{class i{constructor(r){this.histories=r}firstPage(){var r=this;return(0,m.Z)(function*(){try{return a.Either.success(yield r.histories.request())}catch({message:n}){return a.Either.failure({message:n})}})()}nextPage(){var r=this;return(0,m.Z)(function*(){try{return a.Either.success(yield r.histories.nextPage())}catch({message:n}){return a.Either.failure({message:n})}})()}refresh(r){var n=this;return(0,m.Z)(function*(){try{return a.Either.success(yield n.histories.refresh(r))}catch({message:b}){return a.Either.failure({message:b})}})()}historyForUuid(r){try{const n=this.histories.requestForUuid(r);return n?a.Either.success(n):a.Either.failure()}catch({message:n}){return a.Either.failure({message:n})}}}return i.\u0275fac=function(r){return new(r||i)(o.\u0275\u0275inject(E))},i.\u0275prov=o.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),S=(()=>{class i{constructor(r,n){this.invoices=r,this.billers=n}execute(){var r=this;return(0,m.Z)(function*(){try{const[n,b]=yield Promise.all([r.requestInvoices(),r.requestUnbillers()]);return a.Either.success({invoices:n,unbillers:b})}catch({message:n}){return a.Either.failure({message:n})}})()}requestInvoices(){return this.invoices.request().then(r=>({collection:r,error:!1})).catch(()=>({collection:[],error:!0}))}requestUnbillers(){var r=this;return(0,m.Z)(function*(){return r.billers.request().then(n=>n.filter(({isBiller:b})=>!b)).then(n=>({collection:n,error:!1})).catch(()=>({collection:[],error:!0}))})()}}return i.\u0275fac=function(r){return new(r||i)(o.\u0275\u0275inject(h.W),o.\u0275\u0275inject(h.e))},i.\u0275prov=o.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},17533:(N,p,e)=>{e.r(p),e.d(p,{MboPaymentHistoryInformationPageModule:()=>I});var m=e(17007),a=e(78007),h=e(79798),s=e(99877),d=e(39904),C=e(95437),l=e(87903),D=e(82716),P=e(10464),R=e(78021),y=e(1027);let f=(()=>{class u{constructor(c,o,g,E){this.ref=c,this.activateRoute=o,this.mboProvider=g,this.requestConfiguration=E,this.redirectPage=d.Z6.PAYMENTS.HOME,this.informations=[],this.backAction={id:"btn_payment-history-information_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(this.redirectPage)}},this.rightActions=[{id:"btn_transfer-history-information_download",prefixIcon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){const{redirect:c,uuid:o}=this.activateRoute.snapshot.queryParams;this.redirectPage="history"===c?d.Z6.PAYMENTS.HISTORY:d.Z6.PAYMENTS.HOME,this.element=this.ref.nativeElement.querySelector("#crd_payment-history-information_result"),this.ref.nativeElement.classList.add(d.fc),this.requestConfiguration.historyForUuid(o).when({success:g=>{this.informations=function v(u){const t=[],{amount:c,approvedCode:o,dateFormat:g,method:E,reference:M,source:{name:A,number:S},status:{color:i,label:O},supplier:r,timeFormat:n}=u;return t.push((0,l.SP)("PAGO A",r,M)),t.push((0,l._f)("SUMA DE",c)),t.push((0,l.SP)("DESDE",A,S)),t.push((0,l.SP)("TIPO DE PAGO",E)),t.push((0,l.cZ)(g,n)),o&&t.push((0,l.SP)("N\xdaMERO DE PAGO",o)),t.push((0,l.fW)("RESULTADO",i,O)),t}(g)},failure:()=>{this.mboProvider.navigation.back(this.redirectPage)}})}}return u.\u0275fac=function(c){return new(c||u)(s.\u0275\u0275directiveInject(s.ElementRef),s.\u0275\u0275directiveInject(a.ActivatedRoute),s.\u0275\u0275directiveInject(C.ZL),s.\u0275\u0275directiveInject(D.UD))},u.\u0275cmp=s.\u0275\u0275defineComponent({type:u,selectors:[["mbo-payment-history-information-page"]],decls:8,vars:3,consts:[[1,"mbo-payment-history-information-page__content","mbo-page__scroller"],[1,"mbo-page__header"],[3,"leftAction","rightActions"],[1,"mbo-payment-history-information-page__body"],["id","crd_payment-history-information_result",3,"informations"],[1,"mbo-payment-history-information-page__title","subtitle2-medium"]],template:function(c,o){1&c&&(s.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),s.\u0275\u0275element(3,"mbo-header-result",2),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(4,"div",3)(5,"mbo-card-record-information",4)(6,"label",5),s.\u0275\u0275text(7," Datos del pago "),s.\u0275\u0275elementEnd()()()()()),2&c&&(s.\u0275\u0275advance(3),s.\u0275\u0275property("leftAction",o.backAction)("rightActions",o.rightActions),s.\u0275\u0275advance(2),s.\u0275\u0275property("informations",o.informations))},dependencies:[P.K,R.c,y.A],styles:["/*!\n * MBO PaymentHistoryInformation Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 20/Oct/2022\n * Updated: 10/Jul/2024\n*/mbo-payment-history-information-page{--mbo-header-result-padding: calc( var(--mbo-application-body-safe-spacing) + var(--sizing-x4) ) var(--sizing-x4) var(--sizing-x4) var(--sizing-x4);position:relative;width:100%;height:100%;display:block}mbo-payment-history-information-page .mbo-payment-history-information-page__content{position:relative;display:flex;width:100%;flex-direction:column}mbo-payment-history-information-page .mbo-payment-history-information-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-payment-history-information-page .mbo-payment-history-information-page__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),u})(),I=(()=>{class u{}return u.\u0275fac=function(c){return new(c||u)},u.\u0275mod=s.\u0275\u0275defineNgModule({type:u}),u.\u0275inj=s.\u0275\u0275defineInjector({imports:[m.CommonModule,a.RouterModule.forChild([{path:"",component:f}]),h.KI,h.cN,h.A6]}),u})()},63674:(N,p,e)=>{e.d(p,{Eg:()=>v,Lo:()=>s,Wl:()=>d,ZC:()=>C,_f:()=>a,br:()=>l,tl:()=>h});var m=e(29306);const a={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},h=new m.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),s={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},C={color:"danger",key:"expired",label:"Vencida"},l={color:"info",key:"recurring",label:"Pago recurrente"},v={color:"info",key:"programmed",label:"Programado"}},66067:(N,p,e)=>{e.d(p,{S6:()=>D,T2:()=>l,UQ:()=>P,mZ:()=>v});var m=e(39904),a=e(6472),s=e(63674),d=e(31707);class l{constructor(y,f,I,u,t,c,o,g,E,M,A){this.id=y,this.type=f,this.name=I,this.nickname=u,this.number=t,this.bank=c,this.isAval=o,this.isProtected=g,this.isOwner=E,this.ownerName=M,this.ownerDocument=A,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[m.y1],this.initialsName=(0,a.initials)(u),this.shortNumber=t.substring(t.length-4),this.descriptionNumber=`${I} ${t}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(y){this.informationValue||(this.informationValue=y)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(y){return this.currenciesValue.includes(y)}}class v{constructor(y,f){this.id=y,this.type=f}}class D{constructor(y,f,I,u,t,c,o,g,E,M,A,S){this.uuid=y,this.number=f,this.nie=I,this.nickname=u,this.companyId=t,this.companyName=c,this.amount=o,this.registerDate=g,this.expirationDate=E,this.paid=M,this.statusCode=A,this.references=S,this.recurring=S.length>0,this.status=function C(R){switch(R){case d.U.EXPIRED:return s.ZC;case d.U.PENDING:return s.Wl;case d.U.PROGRAMMED:return s.Eg;case d.U.RECURRING:return s.br;default:return s.Lo}}(A)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class P{constructor(y,f,I,u,t,c,o,g){this.uuid=y,this.number=f,this.nickname=I,this.companyId=u,this.companyName=t,this.city=c,this.amount=o,this.isBiller=g}}},19799:(N,p,e)=>{e.d(p,{e:()=>I,W:()=>u});var m=e(71776),a=e(39904),h=e(87956),s=e(98699),d=e(42168),C=e(84757),l=e(53113),v=e(33876),D=e(66067);var f=e(99877);let I=(()=>{class t{constructor(o,g){this.http=o,g.subscribes(a.PU,()=>{this.destroy()}),this.billers$=(0,s.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(a.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,C.map)(({content:E})=>E.map(M=>function y(t){return new D.UQ((0,v.v4)(),t.nie,t.nickname,t.orgIdNum,t.orgName,t.city,+t.amt,(0,s.parseBoolean)(t.biller))}(M))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return t.\u0275fac=function(o){return new(o||t)(f.\u0275\u0275inject(m.HttpClient),f.\u0275\u0275inject(h.Yd))},t.\u0275prov=f.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),u=(()=>{class t{constructor(o,g){this.http=o,g.subscribes(a.PU,()=>{this.destroy()}),this.invoices$=(0,s.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(a.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,C.map)(({content:E})=>E.map(M=>function R(t){const c=t.refInfo.map(o=>function P(t){return new D.mZ(t.refId,t.refType)}(o));return new D.S6((0,v.v4)(),t.invoiceNum,t.nie,t.nickName,t.orgIdNum,t.orgName,+t.totalCurAmt,new l.ou(t.effDt),new l.ou(t.expDt),(0,s.parseBoolean)(t.payDone),t.state,c)}(M))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return t.\u0275fac=function(o){return new(o||t)(f.\u0275\u0275inject(m.HttpClient),f.\u0275\u0275inject(h.Yd))},t.\u0275prov=f.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},31707:(N,p,e)=>{e.d(p,{U:()=>m,f:()=>a});var m=(()=>{return(h=m||(m={})).RECURRING="1",h.EXPIRED="2",h.PENDING="3",h.PROGRAMMED="4",m;var h})(),a=(()=>{return(h=a||(a={})).BILLER="Servicio",h.NON_BILLER="Servicio",h.PSE="Servicio",h.TAX="Impuesto",h.LOAN="Obligaci\xf3n financiera",h.CREDIT_CARD="Obligaci\xf3n financiera",a;var h})()}}]);