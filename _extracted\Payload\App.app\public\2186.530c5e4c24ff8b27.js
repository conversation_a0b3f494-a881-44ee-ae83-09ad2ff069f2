(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2186,8529],{8529:(C,p,r)=>{r.r(p),r.d(p,{AnimationLoader:()=>c,BaseDirective:()=>h,LottieCacheModule:()=>M,LottieComponent:()=>_,LottieDirective:()=>A,LottieModule:()=>D,LottieTransferState:()=>I,transformAnimationFilenameToKey:()=>v,\u0275b:()=>m,\u0275c:()=>g});var n=r(99877),l=r(17007),d=r(42168),s=r(84757),u=r(99428);const y=["container"],m=new n.InjectionToken("LottieOptions");let c=(()=>{class e{constructor(t,i){this.ngZone=t,this.options=i,this.player$=function L(e){const o=e();return o instanceof Promise?(0,d.from)(o).pipe((0,s.map)(t=>t.default||t),(0,s.publishReplay)(1),(0,s.refCount)()):(0,d.of)(o)}(this.options.player).pipe((0,s.observeOn)(d.animationFrameScheduler))}loadAnimation(t){return this.player$.pipe((0,s.map)(i=>this.createAnimationItem(i,t)))}resolveOptions(t,i){return Object.assign({container:i,renderer:"svg",loop:!0,autoplay:!0},t)}createAnimationItem(t,i){return this.ngZone.runOutsideAngular(()=>t.loadAnimation(i))}}return e.\u0275fac=function(t){return new(t||e)(n.\u0275\u0275inject(n.NgZone),n.\u0275\u0275inject(m))},e.\u0275prov=n.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})(),h=(()=>{class e{constructor(t,i,a){this.ngZone=t,this.platformId=i,this.animationLoader=a,this.options=null,this.containerClass=null,this.styles=null,this.animationCreated=this.getAnimationItem(),this.complete=this.awaitAnimationItemAndStartListening("complete"),this.loopComplete=this.awaitAnimationItemAndStartListening("loopComplete"),this.enterFrame=this.awaitAnimationItemAndStartListening("enterFrame"),this.segmentStart=this.awaitAnimationItemAndStartListening("segmentStart"),this.configReady=this.awaitAnimationItemAndStartListening("config_ready"),this.dataReady=this.awaitAnimationItemAndStartListening("data_ready"),this.domLoaded=this.awaitAnimationItemAndStartListening("DOMLoaded"),this.destroy=this.awaitAnimationItemAndStartListening("destroy"),this.error=this.awaitAnimationItemAndStartListening("error"),this.destroy$=new d.Subject,this.loadAnimation$=new d.Subject,this.animationItem$=new d.BehaviorSubject(null),this.setupLoadAnimationListener()}ngOnDestroy(){this.destroy$.next(),this.destroyAnimation()}loadAnimation(t,i){this.loadAnimation$.next([t,i])}getAnimationItem(){return(0,d.defer)(()=>this.animationItem$).pipe((0,s.filter)(t=>null!==t))}awaitAnimationItemAndStartListening(t){return this.getAnimationItem().pipe((0,s.switchMap)(i=>new d.Observable(a=>{i.addEventListener(t,f=>{this.ngZone.runOutsideAngular(()=>{a.next(f)})})})))}setupLoadAnimationListener(){this.loadAnimation$.pipe((0,s.filter)(([t])=>(0,l.isPlatformBrowser)(this.platformId)&&void 0!==t.options),(0,s.switchMap)(([t,i])=>(this.destroyAnimation(),this.animationLoader.loadAnimation(this.animationLoader.resolveOptions(t.options.currentValue,i)))),(0,s.takeUntil)(this.destroy$)).subscribe(t=>{this.animationItem$.next(t)})}destroyAnimation(){const t=this.animationItem$.getValue();null!==t&&(t.destroy(),this.animationItem$.next(null))}}return e.\u0275fac=function(t){return new(t||e)(n.\u0275\u0275directiveInject(n.NgZone),n.\u0275\u0275directiveInject(n.PLATFORM_ID),n.\u0275\u0275directiveInject(c))},e.\u0275dir=n.\u0275\u0275defineDirective({type:e,selectors:[["","lottie",""]],inputs:{options:"options",containerClass:"containerClass",styles:"styles"},outputs:{animationCreated:"animationCreated",complete:"complete",loopComplete:"loopComplete",enterFrame:"enterFrame",segmentStart:"segmentStart",configReady:"configReady",dataReady:"dataReady",domLoaded:"domLoaded",destroy:"destroy",error:"error"}}),e})(),A=(()=>{class e extends h{constructor(t,i,a,f){super(t,i,f),this.host=a}ngOnChanges(t){super.loadAnimation(t,this.host.nativeElement)}}return e.\u0275fac=function(t){return new(t||e)(n.\u0275\u0275directiveInject(n.NgZone),n.\u0275\u0275directiveInject(n.PLATFORM_ID),n.\u0275\u0275directiveInject(n.ElementRef,2),n.\u0275\u0275directiveInject(c))},e.\u0275dir=n.\u0275\u0275defineDirective({type:e,selectors:[["","lottie",""]],features:[n.\u0275\u0275InheritDefinitionFeature,n.\u0275\u0275NgOnChangesFeature]}),e})(),_=(()=>{class e extends h{constructor(t,i,a){super(t,i,a),this.width=null,this.height=null,this.container=null}ngOnChanges(t){super.loadAnimation(t,this.container.nativeElement)}}return e.\u0275fac=function(t){return new(t||e)(n.\u0275\u0275directiveInject(n.NgZone),n.\u0275\u0275directiveInject(n.PLATFORM_ID),n.\u0275\u0275directiveInject(c))},e.\u0275cmp=n.\u0275\u0275defineComponent({type:e,selectors:[["ng-lottie"]],viewQuery:function(t,i){if(1&t&&n.\u0275\u0275viewQuery(y,7),2&t){let a;n.\u0275\u0275queryRefresh(a=n.\u0275\u0275loadQuery())&&(i.container=a.first)}},inputs:{width:"width",height:"height"},features:[n.\u0275\u0275InheritDefinitionFeature,n.\u0275\u0275NgOnChangesFeature],decls:2,vars:6,consts:[[3,"ngStyle","ngClass"],["container",""]],template:function(t,i){1&t&&n.\u0275\u0275element(0,"div",0,1),2&t&&(n.\u0275\u0275styleProp("width",i.width||"100%")("height",i.height||"100%"),n.\u0275\u0275property("ngStyle",i.styles)("ngClass",i.containerClass))},dependencies:[l.NgClass,l.NgStyle],encapsulation:2,changeDetection:0}),e})(),D=(()=>{class e{static forRoot(t){return{ngModule:e,providers:[c,{provide:m,useValue:t}]}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=n.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule]}),e})(),g=(()=>{class e extends c{constructor(){super(...arguments),this.cache=new Map}ngOnDestroy(){this.cache.clear()}loadAnimation(t){return this.player$.pipe((0,s.map)(i=>{const a=this.createAnimationItem(i,this.transformOptions(t));return this.awaitConfigAndCache(t,a),a}))}awaitConfigAndCache(t,i){if(this.isAnimationConfigWithPath(t)){if(this.cache.has(t.path))return;i.addEventListener("config_ready",()=>{this.cache.set(t.path,JSON.stringify(i.animationData))})}}transformOptions(t){return this.isAnimationConfigWithPath(t)&&this.cache.has(t.path)?Object.assign(Object.assign({},t),{path:void 0,animationData:JSON.parse(this.cache.get(t.path))}):t}isAnimationConfigWithPath(t){return"string"==typeof t.path}}return e.\u0275fac=function(){let o;return function(i){return(o||(o=n.\u0275\u0275getInheritedFactory(e)))(i||e)}}(),e.\u0275prov=n.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})(),M=(()=>{class e{static forRoot(){return{ngModule:e,providers:[{provide:c,useClass:g}]}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=n.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=n.\u0275\u0275defineInjector({}),e})();function v(e){const[o]=e.split(".json");return`animation-${o}`}let I=(()=>{class e{constructor(t){this.transferState=t}get(t){const i=v(t),a=(0,u.makeStateKey)(i);return this.transferState.get(a,null)}}return e.\u0275fac=function(t){return new(t||e)(n.\u0275\u0275inject(u.TransferState))},e.\u0275prov=n.\u0275\u0275defineInjectable({factory:function(){return new e(n.\u0275\u0275inject(u.TransferState))},token:e,providedIn:"root"}),e})()}}]);