(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7648],{37648:(ye,j,a)=>{a.r(j),a.d(j,{MboMicrofrontendBrebPageModule:()=>me});var x=a(17007),d=a(78007),l=a(15861),s=a(99877);let u={};const f={},F={};let w=!1;function m(){return(m=(0,l.Z)(function*(e,t){return(yield f[e].get(t))()})).apply(this,arguments)}function B(e,t){return y.apply(this,arguments)}function y(){return(y=(0,l.Z)(function*(e,t){return F[t]||(w||(yield a.I("default"),w=!0),yield e.init(a.S.default),F[t]=!0),e})).apply(this,arguments)}function g(){return g=(0,l.Z)(function*(e,t){if("string"==typeof e)return yield I(e,t);if("script"===e.type){const n=e;return yield I(n.remoteEntry,n.remoteName)}"module"===e.type&&(yield function K(e){return M.apply(this,arguments)}(e.remoteEntry))}),g.apply(this,arguments)}function M(){return(M=(0,l.Z)(function*(e){return f[e]?Promise.resolve():yield import(e).then(t=>{B(t,e),f[e]=t})})).apply(this,arguments)}function I(e,t){return v.apply(this,arguments)}function v(){return(v=(0,l.Z)(function*(e,t){return new Promise((n,o)=>{if(f[t])return void n();const r=document.createElement("script");r.src=e,r.onerror=o,r.onload=()=>{const i=window[t];B(i,t),f[t]=i,n()},document.body.appendChild(r)})})).apply(this,arguments)}function E(){return E=(0,l.Z)(function*(e,t){let n,o,r,i;if(i="string"==typeof e?{type:"manifest",remoteName:e,exposedModule:t}:e,!i.type){const c=Object.keys(u).length>0;i.type=c?"manifest":"script"}if("manifest"===i.type){const c=u[i.remoteName];if(!c)throw new Error("Manifest does not contain "+i.remoteName);i={type:c.type,exposedModule:i.exposedModule,remoteEntry:c.remoteEntry,remoteName:"script"===c.type?i.remoteName:void 0},r=c.remoteEntry}else r=i.remoteEntry;return"script"===i.type?(n={type:"script",remoteEntry:i.remoteEntry,remoteName:i.remoteName},o=i.remoteName):"module"===i.type&&(n={type:"module",remoteEntry:i.remoteEntry},o=i.remoteEntry),r&&(yield function h(e,t){return g.apply(this,arguments)}(n)),yield function H(e,t){return m.apply(this,arguments)}(o,i.exposedModule)}),E.apply(this,arguments)}a(99428);const J=["vc"];let X=(()=>{class e{constructor(n){this.route=n}ngOnChanges(){this.element&&this.populateProps()}populateProps(){for(const n in this.props)this.element[n]=this.props[n]}setupEvents(){for(const n in this.events)this.element.addEventListener(n,this.events[n])}ngAfterContentInit(){var n=this;return(0,l.Z)(function*(){const o=n.options??n.route.snapshot.data;try{yield function Y(e,t){return E.apply(this,arguments)}(o),n.element=document.createElement(o.elementName),n.populateProps(),n.setupEvents(),n.vc.nativeElement.appendChild(n.element)}catch(r){console.error(r)}})()}}return e.\u0275fac=function(n){return new(n||e)(s.\u0275\u0275directiveInject(d.ActivatedRoute))},e.\u0275cmp=s.\u0275\u0275defineComponent({type:e,selectors:[["mft-wc-wrapper"]],viewQuery:function(n,o){if(1&n&&s.\u0275\u0275viewQuery(J,7,s.ElementRef),2&n){let r;s.\u0275\u0275queryRefresh(r=s.\u0275\u0275loadQuery())&&(o.vc=r.first)}},inputs:{options:"options",props:"props",events:"events"},features:[s.\u0275\u0275NgOnChangesFeature],decls:2,vars:0,consts:[["vc",""]],template:function(n,o){1&n&&s.\u0275\u0275element(0,"div",null,0)},encapsulation:2}),e})();var A=a(70658),Q=a(10342),ce=a(39904),de=a(64892),fe=a(95437),T=a(87956);let $=(()=>{class e{constructor(n,o,r,i){this.mboProvider=n,this.deviceService=o,this.customerService=r,this.tokenService=i,this.eventBus=Q.EventBus.getInstance(!A.N.production,Q.EventDriven.PostMessage),this.spiSession=this.eventBus.accessTopic("spiSessionTopic"),this.spiCloseSession=this.eventBus.accessTopic("spiCloseSession")}ngOnInit(){this.spiCloseSession.clearStoredTopic(),Promise.all([this.customerService.request(),this.deviceService.getFingerprint(),this.tokenService.value()]).then(([n,o,r])=>{this.spiSession.publish({channel:"MB",complementaryServices:n.hasComplementaryServicesActivited,date:(new Date).toISOString(),entity:"bocc",entityCode:de.qE.Occidente,entityNit:"**********",ipAddress:n.ip,token:r},!0)}),this.spiCloseSession.subscribe(n=>{"exit"===n?.topicValue?.action&&this.mboProvider.navigation.back(ce.Z6.CUSTOMER.PRODUCTS.HOME)})}ngOnDestroy(){this.spiSession.unsubscribe(),this.spiCloseSession.unsubscribe()}}return e.\u0275fac=function(n){return new(n||e)(s.\u0275\u0275directiveInject(fe.ZL),s.\u0275\u0275directiveInject(T.U8),s.\u0275\u0275directiveInject(T.NY),s.\u0275\u0275directiveInject(T.id))},e.\u0275cmp=s.\u0275\u0275defineComponent({type:e,selectors:[["mbo-microfrontend-breb"]],standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:1,vars:0,template:function(n,o){1&n&&s.\u0275\u0275element(0,"router-outlet")},dependencies:[x.CommonModule,d.RouterModule,d.RouterOutlet],encapsulation:2}),e})();const pe=[{path:"",component:$,children:[{matcher:function q(e){return t=>t.map(o=>o.path).join("/").startsWith(e)?{consumed:t}:null}("remotes/spi"),component:X,data:{type:"script",remoteEntry:A.N.microfrontends.spi,exposedModule:"./routes",remoteName:"adl_spi_frontend_mfe_shell",elementName:"adl-spi-frontend-mfe-shell"}}]}];let me=(()=>{class e{}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=s.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=s.\u0275\u0275defineInjector({imports:[x.CommonModule,d.RouterModule.forChild(pe),$,d.RouterModule]}),e})()}}]);