(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2634],{22634:(u,l,n)=>{n.r(l),n.d(l,{Clipboard:()=>p});var s=n(17737),o=n(15861);class b extends s.WebPlugin{write(e){var t=this;return(0,o.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard)throw t.unavailable("Clipboard API not available in this browser");if(void 0!==e.string)yield t.writeText(e.string);else if(e.url)yield t.writeText(e.url);else{if(!e.image)throw new Error("Nothing to write");if(!(typeof ClipboardItem<"u"))throw t.unavailable("Writing images to the clipboard is not supported in this browser");try{const r=yield(yield fetch(e.image)).blob(),a=new ClipboardItem({[r.type]:r});yield navigator.clipboard.write([a])}catch{throw new Error("Failed to write image")}}})()}read(){var e=this;return(0,o.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard)throw e.unavailable("Clipboard API not available in this browser");if(!(typeof ClipboardItem<"u"))return e.readText();try{const t=yield navigator.clipboard.read(),r=t[0].types[0],a=yield t[0].getType(r);return{value:yield e._getBlobData(a,r),type:r}}catch{return e.readText()}})()}readText(){var e=this;return(0,o.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard||!navigator.clipboard.readText)throw e.unavailable("Reading from clipboard not supported in this browser");return{value:yield navigator.clipboard.readText(),type:"text/plain"}})()}writeText(e){var t=this;return(0,o.Z)(function*(){if(typeof navigator>"u"||!navigator.clipboard||!navigator.clipboard.writeText)throw t.unavailable("Writting to clipboard not supported in this browser");yield navigator.clipboard.writeText(e)})()}_getBlobData(e,t){return new Promise((r,a)=>{const i=new FileReader;t.includes("image")?i.readAsDataURL(e):i.readAsText(e),i.onloadend=()=>{r(i.result)},i.onerror=d=>{a(d)}})}}const p=(0,s.registerPlugin)("Clipboard",{web:()=>new b})}}]);