(self.webpackChunkapp=self.webpackChunkapp||[]).push([[53],{36319:(O,k,s)=>{s.d(k,{g:()=>n});var d=s(72972);const n=()=>{if(void 0!==d.w)return d.w.Capacitor}},56879:(O,k,s)=>{s.d(k,{i:()=>d});const d=n=>n&&""!==n.dir?"rtl"===n.dir.toLowerCase():"rtl"===document?.dir.toLowerCase()},37003:(O,k,s)=>{s.d(k,{I:()=>p,a:()=>T,b:()=>l,c:()=>I,d:()=>B,f:()=>E,g:()=>v,i:()=>f,p:()=>M,r:()=>A,s:()=>C});var d=s(15861),n=s(78635),y=s(28909);const l="ion-content",p=".ion-content-scroll-host",w=`${l}, ${p}`,f=b=>"ION-CONTENT"===b.tagName,v=function(){var b=(0,d.Z)(function*(u){return f(u)?(yield new Promise(m=>(0,n.c)(u,m)),u.getScrollElement()):u});return function(m){return b.apply(this,arguments)}}(),T=b=>b.querySelector(p)||b.querySelector(w),E=b=>b.closest(w),C=(b,u)=>f(b)?b.scrollToTop(u):Promise.resolve(b.scrollTo({top:0,left:0,behavior:u>0?"smooth":"auto"})),I=(b,u,m,_)=>f(b)?b.scrollByPoint(u,m,_):Promise.resolve(b.scrollBy({top:m,left:u,behavior:_>0?"smooth":"auto"})),M=b=>(0,y.b)(b,l),B=b=>{if(f(b)){const m=b.scrollY;return b.scrollY=!1,m}return b.style.setProperty("overflow","hidden"),!0},A=(b,u)=>{f(b)?b.scrollY=u:b.style.removeProperty("overflow")}},80053:(O,k,s)=>{s.r(k),s.d(k,{ion_app:()=>_,ion_buttons:()=>Z,ion_content:()=>j,ion_footer:()=>F,ion_header:()=>V,ion_router_outlet:()=>G,ion_title:()=>J,ion_toolbar:()=>Q});var d=s(15861),n=s(42477),y=s(33006),S=s(28909),l=s(37943),p=s(78635),w=s(56879),f=s(23814),v=s(37003),T=s(2930),E=s(65069),C=s(25030),I=s(37389),M=s(39721);s(72972),s(93037),s(36319);const _=class{constructor(t){(0,n.r)(this,t)}componentDidLoad(){var t=this;R((0,d.Z)(function*(){const o=(0,l.a)(window,"hybrid");if(l.c.getBoolean("_testing")||s.e(6881).then(s.bind(s,56881)).then(r=>r.startTapClick(l.c)),l.c.getBoolean("statusTap",o)&&s.e(9590).then(s.bind(s,19590)).then(r=>r.startStatusTap()),l.c.getBoolean("inputShims",H())){const r=(0,l.a)(window,"ios")?"ios":"android";s.e(5733).then(s.bind(s,95733)).then(a=>a.startInputShims(l.c,r))}const e=yield Promise.resolve().then(s.bind(s,33006)),i=o||(0,y.shouldUseCloseWatcher)();l.c.getBoolean("hardwareBackButton",i)?e.startHardwareBackButton():((0,y.shouldUseCloseWatcher)()&&(0,S.p)("experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),e.blockHardwareBackButton()),typeof window<"u"&&s.e(922).then(s.bind(s,90922)).then(r=>r.startKeyboardAssist(window)),s.e(6390).then(s.bind(s,96390)).then(r=>t.focusVisible=r.startFocusVisible())}))}setFocus(t){var o=this;return(0,d.Z)(function*(){o.focusVisible&&o.focusVisible.setFocus(t)})()}render(){const t=(0,l.b)(this);return(0,n.h)(n.H,{key:"6d7c57453b4be454690e8f1a0721f1e3da8f92aa",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":l.c.getBoolean("_forceStatusbarPadding")}})}get el(){return(0,n.f)(this)}},H=()=>!!((0,l.a)(window,"ios")&&(0,l.a)(window,"mobile")||(0,l.a)(window,"android")&&(0,l.a)(window,"mobileweb")),R=t=>{"requestIdleCallback"in window?window.requestIdleCallback(t):setTimeout(t,32)};_.style="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}";const Z=class{constructor(t){(0,n.r)(this,t),this.collapse=!1}render(){const t=(0,l.b)(this);return(0,n.h)(n.H,{key:"2929fd8c4469bab2953c23d47f601706acb104f1",class:{[t]:!0,"buttons-collapse":this.collapse}})}};Z.style={ios:".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}",md:".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}"};const j=class{constructor(t){(0,n.r)(this,t),this.ionScrollStart=(0,n.d)(this,"ionScrollStart",7),this.ionScroll=(0,n.d)(this,"ionScroll",7),this.ionScrollEnd=(0,n.d)(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}connectedCallback(){if(this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal"),(0,p.m)(this.el)){const t=this.tabsElement=this.el.closest("ion-tabs");null!==t&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),(0,p.m)(this.el)){const{tabsElement:t,tabsLoadCallback:o}=this;null!==t&&void 0!==o&&t.removeEventListener("ionTabBarLoaded",o),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{null!==this.el.offsetParent&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:t}=this,o=(0,l.b)(this);return void 0===t?"ios"===o&&(0,l.a)("ios"):t}resize(){this.fullscreen?(0,n.e)(()=>this.readDimensions()):(0!==this.cTop||0!==this.cBottom)&&(this.cTop=this.cBottom=0,(0,n.i)(this))}readDimensions(){const t=nt(this.el),o=Math.max(this.el.offsetTop,0),e=Math.max(t.offsetHeight-o-this.el.offsetHeight,0);(o!==this.cTop||e!==this.cBottom)&&(this.cTop=o,this.cBottom=e,(0,n.i)(this))}onScroll(t){const o=Date.now(),e=!this.isScrolling;this.lastScroll=o,e&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,(0,n.e)(i=>{this.queued=!1,this.detail.event=t,it(this.detail,this.scrollEl,i,e),this.ionScroll.emit(this.detail)}))}getScrollElement(){var t=this;return(0,d.Z)(function*(){return t.scrollEl||(yield new Promise(o=>(0,p.c)(t.el,o))),Promise.resolve(t.scrollEl)})()}getBackgroundElement(){var t=this;return(0,d.Z)(function*(){return t.backgroundContentEl||(yield new Promise(o=>(0,p.c)(t.el,o))),Promise.resolve(t.backgroundContentEl)})()}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(t=0){var o=this;return(0,d.Z)(function*(){const e=yield o.getScrollElement();return o.scrollToPoint(void 0,e.scrollHeight-e.clientHeight,t)})()}scrollByPoint(t,o,e){var i=this;return(0,d.Z)(function*(){const r=yield i.getScrollElement();return i.scrollToPoint(t+r.scrollLeft,o+r.scrollTop,e)})()}scrollToPoint(t,o,e=0){var i=this;return(0,d.Z)(function*(){const r=yield i.getScrollElement();if(e<32)return null!=o&&(r.scrollTop=o),void(null!=t&&(r.scrollLeft=t));let a,c=0;const h=new Promise(P=>a=P),g=r.scrollTop,x=r.scrollLeft,z=null!=o?o-g:0,D=null!=t?t-x:0,W=P=>{const yt=Math.min(1,(P-c)/e)-1,U=Math.pow(yt,3)+1;0!==z&&(r.scrollTop=Math.floor(U*z+g)),0!==D&&(r.scrollLeft=Math.floor(U*D+x)),U<1?requestAnimationFrame(W):a()};return requestAnimationFrame(P=>{c=P,W(P)}),h})()}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{isMainContent:t,scrollX:o,scrollY:e,el:i}=this,r=(0,w.i)(i)?"rtl":"ltr",a=(0,l.b)(this),c=this.shouldForceOverscroll(),h="ios"===a,g=t?"main":"div";return this.resize(),(0,n.h)(n.H,{key:"e13815c0e6f6095150b112d3a1aaf2f509aa0d0b",class:(0,f.c)(this.color,{[a]:!0,"content-sizing":(0,f.h)("ion-popover",this.el),overscroll:c,[`content-${r}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},(0,n.h)("div",{key:"8006c4a10d8f7dc83c646246961d018a8097236e",ref:x=>this.backgroundContentEl=x,id:"background-content",part:"background"}),(0,n.h)(g,{key:"4dd2f58421493f7a4ca42f8f5d7b85cda8e320ea",class:{"inner-scroll":!0,"scroll-x":o,"scroll-y":e,overscroll:(o||e)&&c},ref:x=>this.scrollEl=x,onScroll:this.scrollEvents?x=>this.onScroll(x):void 0,part:"scroll"},(0,n.h)("slot",{key:"37904f8f1d8319156cd901feb21930ef674fe0f7"})),h?(0,n.h)("div",{class:"transition-effect"},(0,n.h)("div",{class:"transition-cover"}),(0,n.h)("div",{class:"transition-shadow"})):null,(0,n.h)("slot",{key:"8f696583903af0548d064dca1a6bae060e127485",name:"fixed"}))}get el(){return(0,n.f)(this)}},nt=t=>{const o=t.closest("ion-tabs");return o||(t.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content")||(t=>{var o;return t.parentElement?t.parentElement:null!==(o=t.parentNode)&&void 0!==o&&o.host?t.parentNode.host:null})(t))},it=(t,o,e,i)=>{const r=t.currentX,a=t.currentY,h=o.scrollLeft,g=o.scrollTop,x=e-t.currentTime;if(i&&(t.startTime=e,t.startX=h,t.startY=g,t.velocityX=t.velocityY=0),t.currentTime=e,t.currentX=t.scrollLeft=h,t.currentY=t.scrollTop=g,t.deltaX=h-t.startX,t.deltaY=g-t.startY,x>0&&x<100){const D=(g-a)/x;t.velocityX=(h-r)/x*.7+.3*t.velocityX,t.velocityY=.7*D+.3*t.velocityY}};j.style=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.outer-content){--background:var(--ion-color-step-50, #f2f2f2)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}';const K=(t,o)=>{(0,n.e)(()=>{const h=(0,p.l)(0,1-(t.scrollTop-(t.scrollHeight-t.clientHeight-10))/10,1);(0,n.w)(()=>{o.style.setProperty("--opacity-scale",h.toString())})})},F=class{constructor(t){var o=this;(0,n.r)(this,t),this.keyboardCtrl=null,this.checkCollapsibleFooter=()=>{if("ios"!==(0,l.b)(this))return;const{collapse:i}=this,r="fade"===i;if(this.destroyCollapsibleFooter(),r){const a=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;if(!c)return void(0,v.p)(this.el);this.setupFadeFooter(c)}},this.setupFadeFooter=function(){var e=(0,d.Z)(function*(i){const r=o.scrollEl=yield(0,v.g)(i);o.contentScrollCallback=()=>{K(r,o.el)},r.addEventListener("scroll",o.contentScrollCallback),K(r,o.el)});return function(i){return e.apply(this,arguments)}}(),this.keyboardVisible=!1,this.collapse=void 0,this.translucent=!1}componentDidLoad(){this.checkCollapsibleFooter()}componentDidUpdate(){this.checkCollapsibleFooter()}connectedCallback(){var t=this;return(0,d.Z)(function*(){t.keyboardCtrl=yield(0,T.c)(function(){var o=(0,d.Z)(function*(e,i){!1===e&&void 0!==i&&(yield i),t.keyboardVisible=e});return function(e,i){return o.apply(this,arguments)}}())})()}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}destroyCollapsibleFooter(){this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0)}render(){const{translucent:t,collapse:o}=this,e=(0,l.b)(this),r=this.el.closest("ion-tabs")?.querySelector(":scope > ion-tab-bar");return(0,n.h)(n.H,{key:"dd8fa96901e8a09759a9621b6513f0492b3a6197",role:"contentinfo",class:{[e]:!0,[`footer-${e}`]:!0,"footer-translucent":t,[`footer-translucent-${e}`]:t,"footer-toolbar-padding":!(this.keyboardVisible||r&&"bottom"===r.slot),[`footer-collapse-${o}`]:void 0!==o}},"ios"===e&&t&&(0,n.h)("div",{key:"0fbb4ebf8e3951ff399f843dc11aab37fc48f8b7",class:"footer-background"}),(0,n.h)("slot",{key:"ecb14a65e3b6960670446c4428e3095b3231a3b0"}))}get el(){return(0,n.f)(this)}};F.style={ios:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}",md:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"};const Y=t=>{const o=document.querySelector(`${t}.ion-cloned-element`);if(null!==o)return o;const e=document.createElement(t);return e.classList.add("ion-cloned-element"),e.style.setProperty("display","none"),document.body.appendChild(e),e},X=t=>{if(!t)return;const o=t.querySelectorAll("ion-toolbar");return{el:t,toolbars:Array.from(o).map(e=>{const i=e.querySelector("ion-title");return{el:e,background:e.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:i,innerTitleEl:i?i.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(e.querySelectorAll("ion-buttons"))}})}},N=(t,o)=>{"fade"!==t.collapse&&(void 0===o?t.style.removeProperty("--opacity-scale"):t.style.setProperty("--opacity-scale",o.toString()))},L=(t,o=!0)=>{const e=t.el;o?(e.classList.remove("header-collapse-condense-inactive"),e.removeAttribute("aria-hidden")):(e.classList.add("header-collapse-condense-inactive"),e.setAttribute("aria-hidden","true"))},$=(t,o,e)=>{(0,n.e)(()=>{const i=t.scrollTop,r=o.clientHeight,a=e?e.clientHeight:0;if(null!==e&&i<a)return o.style.setProperty("--opacity-scale","0"),void t.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);const g=(0,p.l)(0,(i-a)/10,1);(0,n.w)(()=>{t.style.removeProperty("clip-path"),o.style.setProperty("--opacity-scale",g.toString())})})},V=class{constructor(t){var o=this;(0,n.r)(this,t),this.inheritedAttributes={},this.setupFadeHeader=function(){var e=(0,d.Z)(function*(i,r){const a=o.scrollEl=yield(0,v.g)(i);o.contentScrollCallback=()=>{$(o.scrollEl,o.el,r)},a.addEventListener("scroll",o.contentScrollCallback),$(o.scrollEl,o.el,r)});return function(i,r){return e.apply(this,arguments)}}(),this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=(0,p.i)(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){var t=this;return(0,d.Z)(function*(){if("ios"!==(0,l.b)(t))return;const{collapse:e}=t,i="condense"===e,r="fade"===e;if(t.destroyCollapsibleHeader(),i){const a=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;(0,n.w)(()=>{Y("ion-title").size="large",Y("ion-back-button")}),yield t.setupCondenseHeader(c,a)}else if(r){const a=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;if(!c)return void(0,v.p)(t.el);const h=c.querySelector('ion-header[collapse="condense"]');yield t.setupFadeHeader(c,h)}})()}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,o){var e=this;return(0,d.Z)(function*(){if(!t||!o)return void(0,v.p)(e.el);if(typeof IntersectionObserver>"u")return;e.scrollEl=yield(0,v.g)(t);const i=o.querySelectorAll("ion-header");if(e.collapsibleMainHeader=Array.from(i).find(h=>"condense"!==h.collapse),!e.collapsibleMainHeader)return;const r=X(e.collapsibleMainHeader),a=X(e.el);r&&a&&(L(r,!1),N(r.el,0),e.intersectionObserver=new IntersectionObserver(h=>{((t,o,e,i)=>{(0,n.w)(()=>{const r=i.scrollTop;((t,o,e)=>{if(!t[0].isIntersecting)return;const i=t[0].intersectionRatio>.9||e<=0?0:100*(1-t[0].intersectionRatio)/75;N(o.el,1===i?void 0:i)})(t,o,r);const a=t[0],c=a.intersectionRect,h=c.width*c.height,x=0===h&&0==a.rootBounds.width*a.rootBounds.height,z=Math.abs(c.left-a.boundingClientRect.left),D=Math.abs(c.right-a.boundingClientRect.right);x||h>0&&(z>=5||D>=5)||(a.isIntersecting?(L(o,!1),L(e)):(0===c.x&&0===c.y||0!==c.width&&0!==c.height)&&r>0&&(L(o),L(e,!1),N(o.el)))})})(h,r,a,e.scrollEl)},{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),e.intersectionObserver.observe(a.toolbars[a.toolbars.length-1].el),e.contentScrollCallback=()=>{((t,o,e)=>{(0,n.e)(()=>{const r=(0,p.l)(1,1+-t.scrollTop/500,1.1);null===e.querySelector("ion-refresher.refresher-native")&&(0,n.w)(()=>{((t=[],o=1,e=!1)=>{t.forEach(i=>{const r=i.ionTitleEl,a=i.innerTitleEl;!r||"large"!==r.size||(a.style.transition=e?"all 0.2s ease-in-out":"",a.style.transform=`scale3d(${o}, ${o}, 1)`)})})(o.toolbars,r)})})})(e.scrollEl,a,t)},e.scrollEl.addEventListener("scroll",e.contentScrollCallback),(0,n.w)(()=>{void 0!==e.collapsibleMainHeader&&e.collapsibleMainHeader.classList.add("header-collapse-main")}))})()}render(){const{translucent:t,inheritedAttributes:o}=this,e=(0,l.b)(this),i=this.collapse||"none",r=(0,f.h)("ion-menu",this.el)?"none":"banner";return(0,n.h)(n.H,Object.assign({key:"9fa0af97b605f9fe98b13361bc3d1289745c549f",role:r,class:{[e]:!0,[`header-${e}`]:!0,"header-translucent":this.translucent,[`header-collapse-${i}`]:!0,[`header-translucent-${e}`]:this.translucent}},o),"ios"===e&&t&&(0,n.h)("div",{key:"1a780d2625302f2465718e304bdd3794c89c9845",class:"header-background"}),(0,n.h)("slot",{key:"b2b8557b44be40c590bfcc362ac4350f9f8b889e"}))}get el(){return(0,n.f)(this)}};V.style={ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",md:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"};const G=class{constructor(t){(0,n.r)(this,t),this.ionNavWillLoad=(0,n.d)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,n.d)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,n.d)(this,"ionNavDidChange",3),this.lockController=(0,I.c)(),this.gestureOrAnimationInProgress=!1,this.mode=(0,l.b)(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}connectedCallback(){var t=this;return(0,d.Z)(function*(){t.gesture=(yield s.e(414).then(s.bind(s,68299))).createSwipeBackGesture(t.el,()=>!t.gestureOrAnimationInProgress&&!!t.swipeHandler&&t.swipeHandler.canStart(),()=>(t.gestureOrAnimationInProgress=!0,void(t.swipeHandler&&t.swipeHandler.onStart())),e=>{var i;return null===(i=t.ani)||void 0===i?void 0:i.progressStep(e)},(e,i,r)=>{if(t.ani){t.ani.onFinish(()=>{t.gestureOrAnimationInProgress=!1,t.swipeHandler&&t.swipeHandler.onEnd(e)},{oneTimeCallback:!0});let a=e?-.001:.001;e?a+=(0,E.g)([0,0],[.32,.72],[0,1],[1,1],i)[0]:(t.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),a+=(0,E.g)([0,0],[1,0],[.68,.28],[1,1],i)[0]),t.ani.progressEnd(e?1:0,a,r)}else t.gestureOrAnimationInProgress=!1}),t.swipeHandlerChanged()})()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,o,e){var i=this;return(0,d.Z)(function*(){const r=yield i.lockController.lock();let a=!1;try{a=yield i.transition(t,o,e)}catch(c){console.error(c)}return r(),a})()}setRouteId(t,o,e,i){var r=this;return(0,d.Z)(function*(){return{changed:yield r.setRoot(t,o,{duration:"root"===e?0:void 0,direction:"back"===e?"back":"forward",animationBuilder:i}),element:r.activeEl}})()}getRouteId(){var t=this;return(0,d.Z)(function*(){const o=t.activeEl;return o?{id:o.tagName,element:o,params:t.activeParams}:void 0})()}setRoot(t,o,e){var i=this;return(0,d.Z)(function*(){if(i.activeComponent===t&&(0,p.s)(o,i.activeParams))return!1;const r=i.activeEl,a=yield(0,C.a)(i.delegate,i.el,t,["ion-page","ion-page-invisible"],o);return i.activeComponent=t,i.activeEl=a,i.activeParams=o,yield i.commit(a,r,e),yield(0,C.d)(i.delegate,r),!0})()}transition(t,o,e={}){var i=this;return(0,d.Z)(function*(){if(o===t)return!1;i.ionNavWillChange.emit();const{el:r,mode:a}=i,c=i.animated&&l.c.getBoolean("animated",!0),h=e.animationBuilder||i.animation||l.c.get("navAnimation");return yield(0,M.t)(Object.assign(Object.assign({mode:a,animated:c,enteringEl:t,leavingEl:o,baseEl:r,deepWait:(0,p.m)(r),progressCallback:e.progressAnimation?g=>{void 0===g||i.gestureOrAnimationInProgress?i.ani=g:(i.gestureOrAnimationInProgress=!0,g.onFinish(()=>{i.gestureOrAnimationInProgress=!1,i.swipeHandler&&i.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),g.progressEnd(0,0,0))}:void 0},e),{animationBuilder:h})),i.ionNavDidChange.emit(),!0})()}render(){return(0,n.h)("slot",{key:"0949db1bcfde67b462abe9cae72c7a7fd70ea678"})}get el(){return(0,n.f)(this)}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}};G.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}";const J=class{constructor(t){(0,n.r)(this,t),this.ionStyle=(0,n.d)(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const t=(0,l.b)(this),o=this.getSize();return(0,n.h)(n.H,{key:"6f43362b782ef7d340c241bb66f1469663c03cc1",class:(0,f.c)(this.color,{[t]:!0,[`title-${o}`]:!0,"title-rtl":"rtl"===document.dir})},(0,n.h)("div",{key:"9c3ff1a289e533ee3426b71ab5560fbea3529502",class:"toolbar-title"},(0,n.h)("slot",{key:"50d5cc5a1519ad58f1994d2f8c8f08f62baac1fe"})))}get el(){return(0,n.f)(this)}static get watchers(){return{size:["sizeChanged"]}}};J.style={ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}@supports (inset-inline-start: 0){:host{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host{left:0}:host-context([dir=rtl]){left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)){left:unset;right:unset;right:0}}}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}"};const Q=class{constructor(t){(0,n.r)(this,t),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){const t=Array.from(this.el.querySelectorAll("ion-buttons")),o=t.find(r=>"start"===r.slot);o&&o.classList.add("buttons-first-slot");const e=t.reverse(),i=e.find(r=>"end"===r.slot)||e.find(r=>"primary"===r.slot)||e.find(r=>"secondary"===r.slot);i&&i.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();const o=t.target.tagName,e=t.detail,i={},r=this.childrenStyles.get(o)||{};let a=!1;Object.keys(e).forEach(c=>{const h=`toolbar-${c}`,g=e[c];g!==r[h]&&(a=!0),g&&(i[h]=!0)}),a&&(this.childrenStyles.set(o,i),(0,n.i)(this))}render(){const t=(0,l.b)(this),o={};return this.childrenStyles.forEach(e=>{Object.assign(o,e)}),(0,n.h)(n.H,{key:"8907ed75fbb2b1dced55c481bba6363f1dca815b",class:Object.assign(Object.assign({},o),(0,f.c)(this.color,{[t]:!0,"in-toolbar":(0,f.h)("ion-toolbar",this.el)}))},(0,n.h)("div",{key:"6bfa09b08d6517f0d680f53b739854cecd631bc9",class:"toolbar-background"}),(0,n.h)("div",{key:"1531bd6dd9e0a5843309bba854b744c453037ad0",class:"toolbar-container"},(0,n.h)("slot",{key:"881b41697d386eae651b019128573f0fa432cd33",name:"start"}),(0,n.h)("slot",{key:"64a284e6eae5311ac3125dfadb4bb32bdba9d089",name:"secondary"}),(0,n.h)("div",{key:"c1f47503563b38084b27d7ba54f17ec478482b94",class:"toolbar-content"},(0,n.h)("slot",{key:"9a85acfba72252705619ae32acae9c14f81aa57d"})),(0,n.h)("slot",{key:"89e08bd761dc6940dbebc5d06f5f080af204aa72",name:"primary"}),(0,n.h)("slot",{key:"a1cb7d95627f8a3d24dd4b9c11718fc164f53674",name:"end"})))}get el(){return(0,n.f)(this)}};Q.style={ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, #f7f7f7));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, #c1c4cd)));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"}},93037:(O,k,s)=>{s.d(k,{K:()=>S,a:()=>y});var d=s(36319),n=(()=>{return(l=n||(n={})).Unimplemented="UNIMPLEMENTED",l.Unavailable="UNAVAILABLE",n;var l})(),y=(()=>{return(l=y||(y={})).Body="body",l.Ionic="ionic",l.Native="native",l.None="none",y;var l})();const S={getEngine(){const l=(0,d.g)();if(l?.isPluginAvailable("Keyboard"))return l.Plugins.Keyboard},getResizeMode(){const l=this.getEngine();return l?.getResizeMode?l.getResizeMode().catch(p=>{if(p.code!==n.Unimplemented)throw p}):Promise.resolve(void 0)}}},2930:(O,k,s)=>{s.d(k,{c:()=>p});var d=s(15861),n=s(72972),y=s(93037);const S=w=>void 0===n.d||w===y.a.None||void 0===w?null:n.d.querySelector("ion-app")??n.d.body,l=w=>{const f=S(w);return null===f?0:f.clientHeight},p=function(){var w=(0,d.Z)(function*(f){let v,T,E,C;const I=function(){var u=(0,d.Z)(function*(){const m=yield y.K.getResizeMode(),_=void 0===m?void 0:m.mode;v=()=>{void 0===C&&(C=l(_)),E=!0,M(E,_)},T=()=>{E=!1,M(E,_)},null==n.w||n.w.addEventListener("keyboardWillShow",v),null==n.w||n.w.addEventListener("keyboardWillHide",T)});return function(){return u.apply(this,arguments)}}(),M=(u,m)=>{f&&f(u,B(m))},B=u=>{if(0===C||C===l(u))return;const m=S(u);return null!==m?new Promise(_=>{const R=new ResizeObserver(()=>{m.clientHeight===C&&(R.disconnect(),_())});R.observe(m)}):void 0};return yield I(),{init:I,destroy:()=>{null==n.w||n.w.removeEventListener("keyboardWillShow",v),null==n.w||n.w.removeEventListener("keyboardWillHide",T),v=T=void 0},isKeyboardVisible:()=>E}});return function(v){return w.apply(this,arguments)}}()},37389:(O,k,s)=>{s.d(k,{c:()=>n});var d=s(15861);const n=()=>{let y;return{lock:function(){var l=(0,d.Z)(function*(){const p=y;let w;return y=new Promise(f=>w=f),void 0!==p&&(yield p),w});return function(){return l.apply(this,arguments)}}()}}}}]);