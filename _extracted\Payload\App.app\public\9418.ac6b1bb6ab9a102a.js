(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9418],{99418:(I,l,i)=>{i.r(l),i.d(l,{arrayEach:()=>g,firstElement:()=>m,inArray:()=>p,lastElement:()=>E,mapToReduce:()=>A,pushElement:()=>v,reduceDistinct:()=>k,removeElement:()=>w,removeIndex:()=>y,updateElement:()=>x});class h extends Error{constructor(t,n){super("ForEach Exception"),this.element=t,this.index=n}}const p=(e,t)=>-1!==e.indexOf(t),m=e=>0===e.length?null:e[0],E=e=>0===e.length?null:e[e.length-1],v=(e,t)=>[...e,t],x=(e,t,n)=>e.map(r=>n(r)?t:r),w=(e,t)=>e.filter(n=>!t(n)),y=(e,t)=>e.filter((n,r)=>t!==r),g=e=>{const{array:t,each:n,stop:r}=e;try{return t.forEach((c,o)=>{if(n(c,o))throw new h(c,o)}),!0}catch(c){if(r&&c instanceof h){const{element:o,index:a}=c;r(o,a)}return!1}},k=(e,t)=>e.reduce((n,r)=>{const c=t(r);return n.includes(c)||n.push(c),n},[]),A=(e,t)=>{const{factory:n,identifier:r,reducer:c}=t,o=new Map;return e.forEach(s=>{c(s,function a(s){const u=r(s),d=o.get(u);if(d)return d;const f=n(s);return o.set(u,f),f}(s))}),Array.from(o.entries()).map(([s,u])=>u)}}}]);