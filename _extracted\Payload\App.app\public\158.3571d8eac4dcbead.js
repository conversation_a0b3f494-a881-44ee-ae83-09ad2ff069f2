(self.webpackChunkapp=self.webpackChunkapp||[]).push([[158],{10158:(C,l,a)=>{a.r(l),a.d(l,{MboTransferAdvanceResultPageModule:()=>E});var m=a(17007),v=a(78007),c=a(79798),u=a(15861),e=a(99877),d=a(39904),f=a(95437),s=a(87903),p=a(53113);function g(t){const{isError:o,message:n}=t;return{animation:(0,s.jY)(t),title:o?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:n}}function h({isError:t}){return t?[(0,s.wT)("Finalizar","finish","outline"),(0,s.wT)("Volver a intentar","retry")]:[(0,s.wT)("Hacer otra transferencia","retry","outline"),(0,s.wT)("Finalizar","finish")]}var A=a(64847),T=a(10464),M=a(78021),R=a(16442);function y(t,o){if(1&t&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275element(1,"mbo-header-result",5),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("rightActions",n.rightActions)}}let P=(()=>{class t{constructor(n,r,i){this.ref=n,this.mboProvider=r,this.managerAdvance=i,this.requesting=!0,this.template=d.$d,this.rightActions=[{id:"btn_transfer-advance-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfer-advance-result-page_template"),this.initializatedTransaction()}onAction(n){this.mboProvider.navigation.next("finish"===n?d.Z6.CUSTOMER.PRODUCTS.HOME:d.Z6.TRANSFERS.ADVANCE.SOURCE)}initializatedTransaction(){var n=this;return(0,u.Z)(function*(){(yield n.managerAdvance.send()).when({success:r=>{n.template=function b(t){const{dateFormat:o,timeFormat:n}=new p.ou,{status:r,advance:i}=t;return{actions:h(r),error:r.isError,header:g(r),informations:[(0,s.SP)("DESTINO",i.destination.nickname,i.destination.number),(0,s._f)("SUMA DE",i.amount),(0,s.cZ)(o,n)],skeleton:!1}}(r)}},()=>{n.requesting=!1,n.managerAdvance.reset()})})()}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(f.ZL),e.\u0275\u0275directiveInject(A.a))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfer-advance-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfer-advance-result-page__content","mbo-page__scroller"],["class","mbo-transfer-advance-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfer-advance-result-page__body"],["id","crd_transfer-advance-result-page_template",3,"template","action"],[1,"mbo-transfer-advance-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(n,r){1&n&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),e.\u0275\u0275template(2,y,2,1,"div",1),e.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),e.\u0275\u0275listener("action",function(S){return r.onAction(S)}),e.\u0275\u0275elementEnd()()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!r.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("template",r.template))},dependencies:[m.NgIf,T.K,M.c,R.u],styles:["/*!\n * MBO TransferAdvanceResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 09/Jul/2022\n * Updated: 07/Ene/2024\n*/mbo-transfer-advance-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-advance-result-page .mbo-transfer-advance-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfer-advance-result-page .mbo-transfer-advance-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),t})(),E=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[m.CommonModule,v.RouterModule.forChild([{path:"",component:P}]),c.KI,c.cN,c.tu]}),t})()}}]);