(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6338],{54747:(j,I,t)=>{t.d(I,{_L:()=>o,r$:()=>C,gv:()=>v.g,$Z:()=>f,mb:()=>N,Yp:()=>Y});var n=t(15861),r=t(87956),e=t(98699),i=t(99877);let o=(()=>{class U{constructor(A,w,F){this.customerProducts=A,this.customerPreferencesService=w,this.productService=F}requestAll(A){var w=this;return(0,n.Z)(function*(){try{const F=yield w.requestCoveredCards(A),B=w.preferences();return F.length>0?e.Either.success({coveredCards:F,preferences$:B}):e.Either.failure({message:"No se encontraron tarjetas Amparadas"})}catch({message:F}){return e.Either.failure({message:F})}})()}requestForId(A,w){var F=this;return(0,n.Z)(function*(){try{return(yield F.requestCoveredCards(A)).find(({id:B})=>B===w)}catch{return}})()}requestCoveredCards(A){var w=this;return(0,n.Z)(function*(){const F=yield w.customerProducts.requestProductForId(A);if(!F)throw Error("No se encontraron tarjetas Amparadas");return w.productService.requestCoveredCards(F)})()}preferences(){return this.customerPreferencesService.request(),A=>this.customerPreferencesService.subscribe(A)}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(r.hM),i.\u0275\u0275inject(r.fT),i.\u0275\u0275inject(r.M5))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})();var s=t(53113),m=t(71776),y=t(39904),d=t(89148),p=t(87903),u=t(42168),c=t(84757);let a=(()=>{class U{constructor(A){this.http=A}digitalCard(A){return(0,u.firstValueFrom)(this.http.post(y.bV.PRODUCTS.BLOCK_CARD,[{cardId:A.id,productType:d.Gt.CreditCard}]).pipe((0,c.map)(([w])=>w?(0,p.l1)(w,"SUCCESS"):new s.LN("ERROR","No fue posible generar los nuevos datos de tu tarjeta, int\xe9ntalo m\xe1s tarde.")),(0,c.catchError)(w=>(0,u.of)((0,p.rU)(w)))))}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(m.HttpClient))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})(),C=(()=>{class U{constructor(A,w){this.repository=A,this.eventBusService=w}reset(A){var w=this;return(0,n.Z)(function*(){const F=yield w.resetCard(A);return w.eventBusService.emit(F.channel),e.Either.success({creditCard:A,status:F})})()}resetCard(A){try{return this.repository.digitalCard(A)}catch({message:w}){return Promise.resolve(s.LN.error(w))}}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(a),i.\u0275\u0275inject(r.Yd))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})();var v=t(96845);let f=(()=>{class U{constructor(A){this.customerProductService=A}editInAccount(A,w){var F=this;return(0,n.Z)(function*(){try{return F.customerProductService.editTagAvalInAccount(A,w).then(({success:B,message:P})=>B?e.Either.success():e.Either.failure({message:P}))}catch({}){return e.Either.failure({message:"Tu Tag Aval no pudo ser cambiado, int\xe9ntalo m\xe1s tarde."})}})()}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(r.hM))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})();var h=t(7479),g=t(78506),_=t(27236),M=t(3372),b=t(31711),l=t(81536),T=t(61980);const{BiometricLinked:S,CustomerFirstLogin:O}=_.Z;let N=(()=>{class U{constructor(A,w,F,B,P,W,Q,oe,z){this.customerProducts=A,this.financialRepository=w,this.notificationsRepository=F,this.managerSession=B,this.customerPreferencesService=P,this.storageService=W,this.biometricService=Q,this.preferencesService=oe,this.avalAdviserService=z,this.enabledAvalAdviser=!1}home(){var A=this;return(0,n.Z)(function*(){try{const[w,F,B,P,W,Q,oe]=yield Promise.all([A.managerSession.customer(),A.storageService.get(O),A.storageService.get(S),A.preferencesService.requestBoolean(M.M.AvalAdviser),A.preferencesService.requestBoolean(M.M.TuPlus),A.preferencesService.requestBoolean(M.M.Remittains),A.preferencesService.requestBoolean(M.M.EnabledSpi)]);A.enabledAvalAdviser=P;const z=(yield A.biometricService.getMode())!==b._.None&&!B&&!F;F||(yield A.storageService.set(O,!0));const $=Promise.all([A.customerProducts.requestProductsByGroups([d.Nb.Account,d.Nb.CreditCard,d.Nb.Loan,d.Nb.Investment]),A.customerProducts.requestProductsByGroup(d.Nb.Trustfund),A.customerProducts.requestProductsByGroup(d.Nb.Aval)]),x=A.preferences();return A.financialRepository.request(),A.notificationsRepository.request(),e.Either.success({activateBiometric:z,customer:w,enabledAvalAdviser:P,enabledBreb:oe,enabledRemittains:Q,enabledTuPlus:W,preferences$:x,products$:$})}catch({message:w}){return e.Either.failure({message:w})}})()}requestProductAnyForId(A){return this.customerProducts.requestProductAnyForId(A).catch(()=>{})}products(){var A=this;return(0,n.Z)(function*(){try{const w=A.customerProducts.requestProductsByGroups([d.Nb.Account,d.Nb.CreditCard,d.Nb.Loan,d.Nb.Investment]);return e.Either.success({products$:w})}catch({message:w}){return e.Either.failure({message:w})}})()}trustfunds(){var A=this;return(0,n.Z)(function*(){try{const w=yield A.customerProducts.requestTrustfunds(),F=A.preferences();return e.Either.success({products:w,preferences$:F})}catch({message:w}){return e.Either.failure({message:w})}})()}avals(){var A=this;return(0,n.Z)(function*(){try{const w=yield A.customerProducts.requestAvals(),F=A.preferences();return e.Either.success({banks:w,preferences$:F})}catch({message:w}){return e.Either.failure({message:w})}})()}requestTrustfunds(){var A=this;return(0,n.Z)(function*(){try{const w=yield A.customerProducts.requestTrustfunds();return e.Either.success({products:w})}catch({message:w}){return e.Either.failure({message:w})}})()}requestAvals(){var A=this;return(0,n.Z)(function*(){try{const w=yield A.customerProducts.requestAvals();return e.Either.success({products:w?.products})}catch({message:w}){return e.Either.failure({message:w})}})()}preferences(){return this.customerPreferencesService.request(),A=>this.customerPreferencesService.subscribe(A)}requestAdviserAval(){return this.preferencesService.requestBoolean(M.M.AvalAdviser).then(A=>A?this.avalAdviserService.getToken():Promise.resolve({access_token:""}))}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(r.hM),i.\u0275\u0275inject(l.rm),i.\u0275\u0275inject(h.d),i.\u0275\u0275inject(g._I),i.\u0275\u0275inject(r.fT),i.\u0275\u0275inject(r.V1),i.\u0275\u0275inject(r.x1),i.\u0275\u0275inject(r.yW),i.\u0275\u0275inject(T.Qc))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})();var te=t(30263),Z=t(10533),V=t(74561);let Y=(()=>{class U{constructor(A,w){this.modalService=A,this.consentSPIService=w}execute(){this.consentSPIService.verifyStatus().then(A=>{A&&this.modalService.create(V.vd).open()})}}return U.\u0275fac=function(A){return new(A||U)(i.\u0275\u0275inject(te.iM),i.\u0275\u0275inject(Z.J))},U.\u0275prov=i.\u0275\u0275defineInjectable({token:U,factory:U.\u0275fac,providedIn:"root"}),U})()},96845:(j,I,t)=>{t.d(I,{g:()=>y});var n=t(15861),r=t(27236),e=t(87956),i=t(98699),s=t(99877);let y=(()=>{class d{constructor(u){this.storageService=u}allowAccess(){var u=this;return(0,n.Z)(function*(){try{return i.Either.success((yield u.storageService.get(r.Z.RemittancesTyC))||!1)}catch{return i.Either.success(!1)}})()}approvedTyC(){var u=this;return(0,n.Z)(function*(){try{return yield u.storageService.set(r.Z.RemittancesTyC,!0),i.Either.success()}catch({message:c}){return i.Either.failure({message:c})}})()}}return d.\u0275fac=function(u){return new(u||d)(s.\u0275\u0275inject(e.V1))},d.\u0275prov=s.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},74558:(j,I,t)=>{t.d(I,{t:()=>g,z:()=>_});var n=t(15861),r=t(71776),i=t(39904),o=t(38165),s=t(87903),m=t(95437),y=t(21260),d=t(87956),p=t(87528),u=t(98699),a=t(42168),v=t(99877);const h={challenge:"SOFT",digitNumber:6,prefixUrl:"/development/two-factor-auth/transaction",txId:"26250a01-83db-4f33-abc4-9071b67653a1"};let g=(()=>{class M{constructor(l,T,S,O,N){this.http=l,this.mboProvider=T,this.sessionService=S,this.twofaProvider=O,this.digitalSecureService=N}execute(l){return this.twofaProvider.configuration({header:"Autorizaci\xf3n",title:"Ver datos Tarjeta de Cr\xe9dito Credencial Occiflex",waitingMessage:"Solicitando informaci\xf3n, por favor espere...",errorMessage:"Ocurrio un error al tratar de solicitar los datos de la tarjeta, por favor intente m\xe1s tarde",onCancel:()=>{this.mboProvider.navigation.back(i.Z6.CUSTOMER.PRODUCTS.INFO,{productId:l.id})},onError:()=>{this.mboProvider.toast.error("Ocurrio un error al tratar de consultar datos, por favor intente m\xe1s tarde"),this.mboProvider.navigation.back(i.Z6.CUSTOMER.PRODUCTS.INFO,{productId:l.id})},onSuccess:T=>{T?this.setDigital(l.id,T).then(()=>this.mboProvider.navigation.back(i.Z6.CUSTOMER.PRODUCTS.INFO,{productId:l.id})):this.mboProvider.navigation.back(i.Z6.CUSTOMER.PRODUCTS.INFO,{productId:l.id})},onInterceptor:T=>({response:h,value:T}),onFailure:()=>(this.digitalSecureService.setState(l.id,{cvc:"591",incognito:!1,number:"****************",requiredRequest:!1}),h)}),this.twofaProvider.execute(()=>this.request(l))}request(l){return this.sessionService.request().then(({customer:T})=>(0,a.firstValueFrom)(this.http.post(i.bV.TWOFA.DIGITAL_CREDITCARD,{nameClient:T.clientName,productId:l.id})))}setDigital(l,T){const{productNumber:S,secureValue:O}=T;return(0,u.voidPromise)(Promise.all([this.digitalSecureService.setNumber(l,S),this.digitalSecureService.setCVC(l,O),this.digitalSecureService.setIncognito(l,!1),this.digitalSecureService.activateCountdown(l)]))}}return M.\u0275fac=function(l){return new(l||M)(v.\u0275\u0275inject(r.HttpClient),v.\u0275\u0275inject(m.ZL),v.\u0275\u0275inject(d.NY),v.\u0275\u0275inject(p.v),v.\u0275\u0275inject(d.ZP))},M.\u0275prov=v.\u0275\u0275defineInjectable({token:M,factory:M.\u0275fac,providedIn:"root"}),M})(),_=(()=>{class M{constructor(l,T,S,O){this.http=l,this.fileManagerService=T,this.mboProvider=S,this.documentErrorModal=O}execute(l){var T=this;return(0,n.Z)(function*(){try{return T.mboProvider.loader.open("Generando documento, por favor espere..."),(0,u.voidPromise)((0,a.firstValueFrom)(T.http.get(`${i.bV.PRODUCTS.PAY_PLAN_LOAN}/${l.type}/${l.id}`)).then(S=>{const O=`${l.shortNumber}-pay-plan-loan-${(0,s.CW)()}.pdf`;return T.fileManagerService.downloadPdf({base64:S.binData,name:O}).then(({status:N})=>{N===o.K.Saved?T.mboProvider.loader.close():T.errorDocumentFile()})}).catch(()=>{T.errorDocumentFile()}))}catch{T.errorDocumentFile()}})()}errorDocumentFile(){this.mboProvider.loader.close(),this.documentErrorModal.execute()}}return M.\u0275fac=function(l){return new(l||M)(v.\u0275\u0275inject(r.HttpClient),v.\u0275\u0275inject(d.j5),v.\u0275\u0275inject(m.ZL),v.\u0275\u0275inject(y.$))},M.\u0275prov=v.\u0275\u0275defineInjectable({token:M,factory:M.\u0275fac,providedIn:"root"}),M})()},61980:(j,I,t)=>{t.d(I,{Qc:()=>h,hH:()=>g,su:()=>f});var n=t(15861),r=t(71776),i=t(39904),o=t(25329),s=t(3372),m=t(89148),y=t(87956),d=t(98699),u=t(42168),a=t(99877);class v extends d.PartialSealed{static product(M){return new v("product",M)}static products(M){return new v("products",M)}}let f=(()=>{class _{constructor(){this.subject=new u.Subject}emitProduct(b){this.subject.next(v.product(b))}emitProducts(b){this.subject.next(v.products(b))}subscribe(b){const l=this.subject.subscribe(b);return()=>{l.unsubscribe()}}}return _.\u0275fac=function(b){return new(b||_)},_.\u0275prov=a.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})(),h=(()=>{class _{constructor(b){this.http=b,this.token$=(0,d.securePromise)(()=>(0,u.firstValueFrom)(this.http.post(i.bV.AVAL_ADVISER.START_CONVERSATION,{}).pipe((0,u.map)(({data:l})=>{if(!l)throw new Error("El token de Consejero Aval no se obtuvo correctamente.");return l}))))}getToken(){return this.token$.resolve()}}return _.\u0275fac=function(b){return new(b||_)(a.\u0275\u0275inject(r.HttpClient))},_.\u0275prov=a.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})(),g=(()=>{class _{constructor(b,l,T,S){this.storageService=b,this.preferencesService=l,this.sessionService=T,this.digitalWalletService=S,this.isPushCompleted=!1}pushCardsInWallet(b){var l=this;return(0,n.Z)(function*(){yield l.requiredResetWallet(),(yield l.canEnrollCards())&&l.startEnrollment(b)})()}requiredResetWallet(){var b=this;return(0,n.Z)(function*(){const l=yield b.storageService.get(o.c.UserWallet),{customer:T}=yield b.sessionService.request();l&&T.documentNumber!==l&&(b.isPushCompleted=!1,yield b.digitalWalletService.destroy())})()}canEnrollCards(){var b=this;return(0,n.Z)(function*(){return!b.isPushCompleted&&(yield b.preferencesService.requestBoolean(s.M.DigitalWallet))})()}startEnrollment(b){var l=this;return(0,n.Z)(function*(){const T=b.filter(({type:S})=>S===m.Gt.CreditCard);T.length&&((yield l.digitalWalletService.isConnected())?l.enrollCards(T):l.digitalWalletService.connect().then(S=>{S&&l.digitalWalletService.subscribeWallet(({connected:O,hasWallet:N})=>{O&&N&&l.enrollCards(T)})}))})()}enrollCards(b){var l=this;return(0,n.Z)(function*(){const T=yield Promise.all(b.map(function(){var S=(0,n.Z)(function*(O){return[yield l.itIsEnrollCard(O),O]});return function(O){return S.apply(this,arguments)}}()));yield Promise.all(T.filter(([S])=>S).map(([S,O])=>l.digitalWalletService.setDisplayCard(O))),l.isPushCompleted=!0})()}itIsEnrollCard(b){var l=this;return(0,n.Z)(function*(){return(yield l.digitalWalletService.isCardEnrolled(b))||(yield l.digitalWalletService.canTokenizedCard(b))&&l.enrollCard(b)})()}enrollCard(b){return new Promise(l=>{let T=0;this.digitalWalletService.enrollCard(b).then(()=>{const S=setInterval(()=>{T++,T<5?this.digitalWalletService.isCardEnrolled(b).then(O=>{clearInterval(S),O&&l(!0)}):(clearInterval(S),l(!1))},7500)})})}}return _.\u0275fac=function(b){return new(b||_)(a.\u0275\u0275inject(y.Is),a.\u0275\u0275inject(y.yW),a.\u0275\u0275inject(y.NY),a.\u0275\u0275inject(y.SE))},_.\u0275prov=a.\u0275\u0275defineInjectable({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},20586:(j,I,t)=>{t.d(I,{y:()=>e});var n=t(99877);let e=(()=>{class i{constructor(){this.showAltTitle=!1}}return i.\u0275fac=function(s){return new(s||i)},i.\u0275cmp=n.\u0275\u0275defineComponent({type:i,selectors:[["mbo-banner-card"]],inputs:{avatarSrc:"avatarSrc",title:"title",subtitle:"subtitle",altTitle:"altTitle",showAltTitle:"showAltTitle"},standalone:!0,features:[n.\u0275\u0275StandaloneFeature],decls:10,vars:3,consts:[[1,"mbo-banner-card"],[1,"mbo-banner-card__content"],[1,"mbo-banner-card__avatar"],[3,"src"],[1,"mbo-banner-card__body"],[1,"smalltext-medium"],[1,"overline-medium"]],template:function(s,m){1&s&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"img",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",4)(5,"label",5)(6,"p"),n.\u0275\u0275text(7),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(8,"span",6),n.\u0275\u0275text(9),n.\u0275\u0275elementEnd()()()()),2&s&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("src",m.avatarSrc,n.\u0275\u0275sanitizeUrl),n.\u0275\u0275advance(4),n.\u0275\u0275textInterpolate(m.altTitle||m.title),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(m.subtitle))},styles:["/*!\n * MBO BannerCard Component\n * v1.0\n * Author: MB Frontend Developers\n * Created: 29/Apr/2025\n * Updated: 02/May/2025\n*/mbo-banner-card{border-radius:var(--sizing-x8);background:var(--color-carbon-lighter-50);border:var(--border-1-lighter-300)}mbo-banner-card .mbo-banner-card{padding:var(--sizing-x4);text-align:center;box-sizing:border-box}mbo-banner-card .mbo-banner-card__avatar{position:absolute;transform:translateY(-25rem)}mbo-banner-card .mbo-banner-card__avatar img{width:100%;height:auto}mbo-banner-card .mbo-banner-card__content{padding-top:var(--sizing-x10);display:flex;flex-direction:column;align-items:center}mbo-banner-card .mbo-banner-card__body{display:flex;flex-direction:column;align-items:center}mbo-banner-card .mbo-banner-card__body span{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),i})()},57384:(j,I,t)=>{t.d(I,{$:()=>d});var n=t(30263),r=t(79798),e=t(87956),i=t(98699),s=t(99877),y=t(61383);let d=(()=>{class p{constructor(c,a,C){this.bottomSheetService=c,this.contactsInfoService=a,this.customerService=C,this.condense=!1,this.hidden=!1}ngOnInit(){this.contactsInfoService.subscribe(c=>{if("string"!=typeof c)return"PHONES"===c.action?(this.contactsSheet.open(),void this.contactsInfoService.close()):"WHATSAPP"===c.action?(this.whatsappSheet.open(),void this.contactsInfoService.close()):void 0;this.hidden="close"!==c}),this.contactsSheet=this.bottomSheetService.create(r.GI),this.whatsappSheet=this.bottomSheetService.create(r.Uy)}ngOnDestroy(){this.contactsSheet?.destroy(),this.whatsappSheet?.destroy()}onOpen(){(0,i.catchPromise)(this.customerService.requestManager()),this.contactsInfoService.open(),this.hidden=!0}}return p.\u0275fac=function(c){return new(c||p)(s.\u0275\u0275directiveInject(n.fG),s.\u0275\u0275directiveInject(r.w7),s.\u0275\u0275directiveInject(e.vZ))},p.\u0275cmp=s.\u0275\u0275defineComponent({type:p,selectors:[["mbo-button-contacts"]],inputs:{condense:"condense"},decls:4,vars:5,consts:[[1,"mbo-button-contacts__component",3,"click"],["icon","user-manager","status","success",3,"rounded"],[1,"smalltext-medium"]],template:function(c,a){1&c&&(s.\u0275\u0275elementStart(0,"button",0),s.\u0275\u0275listener("click",function(){return a.onOpen()}),s.\u0275\u0275element(1,"bocc-avatar-icon",1),s.\u0275\u0275elementStart(2,"span",2),s.\u0275\u0275text(3,"Cont\xe1ctanos/Gerente"),s.\u0275\u0275elementEnd()()),2&c&&(s.\u0275\u0275classProp("mbo-button-contacts__component--condense",a.condense)("mbo-button-contacts__component--hidden",a.hidden),s.\u0275\u0275advance(1),s.\u0275\u0275property("rounded",!0))},dependencies:[y.j],styles:["mbo-button-contacts{--pvt-span-display: block;position:relative;display:block}mbo-button-contacts .mbo-button-contacts__component{display:flex;align-items:center;padding:var(--sizing-x4) var(--sizing-x6) var(--sizing-x4) var(--sizing-x4);border-radius:var(--sizing-x20) var(--sizing-x8) var(--sizing-x8) var(--sizing-x20);background:var(--color-navy-700);box-shadow:var(--z-bottom-darker-8)}mbo-button-contacts .mbo-button-contacts__component--condense{--pvt-span-display: none;padding:var(--sizing-x4);border-radius:var(--sizing-x20)}mbo-button-contacts .mbo-button-contacts__component--hidden{display:none}mbo-button-contacts .mbo-button-contacts__component .bocc-avatar-icon--rounded{box-shadow:none}mbo-button-contacts .mbo-button-contacts__component .bocc-avatar-icon__status{box-shadow:none}mbo-button-contacts .mbo-button-contacts__component span{display:var(--pvt-span-display);padding-left:var(--sizing-x4);box-sizing:border-box;color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),p})()},90005:(j,I,t)=>{t.d(I,{C:()=>g});var n=t(15861),r=t(17007),e=t(30263),i=t(3372),o=t(27236),s=t(95437),m=t(87956),y=t(31981);const d={provisionInitializationError:"No fue posible aprovisionar billetera digital en tu dispositivo (PRO03)",provisionProcessError:"No fue posible aprovisionar billetera digital en tu dispositivo (PRO01)",provisionDeviceError:"No fue posible activar billetera digital en tu dispositivo (PRO02)",walletCreatedError:"No fue posible iniciar la billetera digital en tu dispositivo (WAL01)",walletCredentialsRequired:"No fue posible autenticar la billetera digital en tu dispositivo (WAL02)",walletConnectionError:"No fue posible conectar con la billetera digital en tu dispositivo (WAL03)"},p={cardDisabled:"Estamos presentado fallas para verificar registro en la billetera digital. Por favor intente m\xe1s tarde",cardNotSupport:"Tu dispositivo no es compatible para realizar registro en la billetera digital",cardNotConfigure:"Tu dispositivo no tiene configurado un usuario en la billetera digital"},u={enrollCardsError:"No fue posible registrar la tarjeta en la billetera, por favor intente nuevamente",enrollCardsTimeout:"No fue posible verificar registro de la tarjeta en la billetera, por favor intente nuevamente"};var c=t(39904),a=t(99877);function C(_,M){if(1&_){const b=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",5),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(b);const T=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(T.onTokenization())}),a.\u0275\u0275elementStart(1,"span",6),a.\u0275\u0275text(2,"Agregar en"),a.\u0275\u0275elementEnd(),a.\u0275\u0275element(3,"img",7),a.\u0275\u0275elementEnd()}if(2&_){const b=a.\u0275\u0275nextContext(2);a.\u0275\u0275classMap(b.buttonType),a.\u0275\u0275property("disabled",b.tokenizing),a.\u0275\u0275advance(3),a.\u0275\u0275property("src",b.imgWalletStore,a.\u0275\u0275sanitizeUrl)}}function v(_,M){if(1&_){const b=a.\u0275\u0275getCurrentView();a.\u0275\u0275elementStart(0,"button",8),a.\u0275\u0275listener("click",function(){a.\u0275\u0275restoreView(b);const T=a.\u0275\u0275nextContext(2);return a.\u0275\u0275resetView(T.onManagerToken())}),a.\u0275\u0275element(1,"img",7),a.\u0275\u0275elementStart(2,"span",9),a.\u0275\u0275text(3," Dispositivo vinculados "),a.\u0275\u0275elementEnd()()}if(2&_){const b=a.\u0275\u0275nextContext(2);a.\u0275\u0275advance(1),a.\u0275\u0275property("src",b.imgWalletDevices,a.\u0275\u0275sanitizeUrl)}}function f(_,M){if(1&_&&(a.\u0275\u0275elementStart(0,"div",10),a.\u0275\u0275element(1,"bocc-progress-bar",11),a.\u0275\u0275elementStart(2,"span",12),a.\u0275\u0275text(3),a.\u0275\u0275elementEnd()()),2&_){const b=a.\u0275\u0275nextContext(2);a.\u0275\u0275advance(1),a.\u0275\u0275property("indeterminate",!0),a.\u0275\u0275advance(2),a.\u0275\u0275textInterpolate1(" ",b.msgTokenizing," ")}}function h(_,M){if(1&_&&(a.\u0275\u0275elementStart(0,"div",1),a.\u0275\u0275template(1,C,4,4,"button",2),a.\u0275\u0275template(2,v,4,1,"button",3),a.\u0275\u0275template(3,f,4,2,"div",4),a.\u0275\u0275elementEnd()),2&_){const b=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",b.requiredTokenized&&!b.tokenized),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",b.tokenized),a.\u0275\u0275advance(1),a.\u0275\u0275property("ngIf",b.tokenizing)}}let g=(()=>{class _{constructor(b,l,T,S,O,N){this.bottomSheetService=b,this.storageService=l,this.deviceService=T,this.preferencesService=S,this.digitalWalletService=O,this.mboProvider=N,this.unsubscriptions=[],this.nameSupplierStore="",this.canTokenized=!1,this.errorTokenized=!1,this.pauseFromApp=!1,this.buttonType="",this.imgWalletStore="",this.imgWalletDevices="",this.requiredTokenized=!1,this.tokenizing=!0,this.tokenized=!1,this.msgTokenizing="",this.tryVerifyEnroll=0,this.enrollVerify=!1,this.enrollIntervalId=void 0}ngOnInit(){this.deviceService.controller.itIsIos?(this.nameSupplierStore="Apple Pay",this.buttonType="ios",this.imgWalletStore="assets/shared/logos/apple-pay-light.svg",this.imgWalletDevices="assets/shared/logos/apple-pay-dark.svg"):(this.nameSupplierStore="Google Pay",this.buttonType="android",this.imgWalletStore="assets/shared/logos/google-pay-light.svg",this.imgWalletDevices="assets/shared/logos/google-pay-dark.svg"),this.preferencesService.requestBoolean(i.M.DigitalWallet).then(b=>{b&&this.product&&this.startTokenization(this.product)})}ngOnDestroy(){this.enrollIntervalId&&clearInterval(this.enrollIntervalId),this.unsubscriptions.forEach(b=>{b()}),this.bottomSheet?.destroy()}get enabledTokenized(){return this.canTokenized&&!this.errorTokenized}onTokenization(){this.product&&this.registerCard(this.product)}onManagerToken(){this.mboProvider.navigation.next(c.Z6.CUSTOMER.MANAGER_TOKEN)}startTokenization(b){this.canCardTokenized(b),this.deviceService.controller.resume(()=>{this.verifyStatusForResume(b)}).then(l=>{this.unsubscriptions.push(l)})}canCardTokenized(b){this.digitalWalletService.canTokenizedCard(b).then(l=>{this.canTokenized=l,l&&this.initCardTokenized(b)})}initCardTokenized(b){var l=this;return(0,n.Z)(function*(){l.msgTokenizing="Conectando con la billetera",(yield l.digitalWalletService.isConnected())?l.verifyCardTokenized(b):l.digitalWalletService.connect().then(T=>{l.tokenizing=T,T?l.subscribeWallet():l.mboProvider.toast.warning("No fue posible iniciar registro en la billetera digital, por favor intente nuevamente","Registro fallido")})})()}verifyCardTokenized(b){this.msgTokenizing="Verificando tarjeta en la billetera",this.tokenizing=!0,this.digitalWalletService.getCardStatus(b).then(l=>{if("cardAvailable"===l)this.requiredTokenized=!0,this.tokenized=!1,this.tokenizing=!1;else{const T="cardTokenized"===l;this.tokenizing=!T,this.tokenized=T,!T&&this.enrollProduct(b)}})}enrollProduct(b){this.msgTokenizing="Registrando tarjeta en la billetera",this.tokenizing=!0,this.digitalWalletService.subscribeEnroll(({card:l,status:T})=>{if(l&&l?.id===this.product?.id&&!this.enrollVerify)if(this.enrollIntervalId&&clearInterval(this.enrollIntervalId),this.enrollVerify=!0,"enrollCardsUpdated"===T)this.verifyCardStatus(l);else{const S=u[T];S&&(this.mboProvider.toast.error(S,"Registro fallido"),this.tokenizing=!1)}}),this.digitalWalletService.enrollCard(b),this.enrollIntervalId=setInterval(()=>{this.tryVerifyEnroll++,this.tryVerifyEnroll<5?this.digitalWalletService.verifyCardEnrolled(b):(clearInterval(this.enrollIntervalId),this.tokenizing=!1,this.mboProvider.toast.error(u.enrollCardsTimeout,"Registro fallido"))},7500)}subscribeWallet(){const b=this.digitalWalletService.subscribeWallet(l=>{const{connected:T,hasWallet:S,status:O}=l;T?S?this.product&&this.verifyCardTokenized(this.product):this.walletWithError("No fue posible aprovisionar la billetera digital en tu dispositivo"):d[O]&&this.walletWithError(d[O])});this.unsubscriptions.push(b)}verifyCardStatus(b){var l=this;return(0,n.Z)(function*(){l.msgTokenizing=`Verificando tarjeta en ${l.nameSupplierStore}`,l.tokenizing=!0;const T=yield l.digitalWalletService.verifyCardStatus(b);switch(T){case"cardAvailable":l.requiredTokenized=!0,l.tokenized=!1,l.tokenizing=!1;break;case"cardTokenized":l.tokenized=!0,l.tokenizing=!1;break;default:l.mboProvider.toast.warning(p[T]||"No fue posible completar registro en la billetera digital"),l.tokenized=!1,l.tokenizing=!1}})()}registerCard(b){var l=this;return(0,n.Z)(function*(){((yield l.storageService.get(o.Z.DigitalWalletTyC))||(yield l.approvedTermsAndConditions()))&&(l.tokenized||(l.msgTokenizing=`Enviando tarjeta a ${l.nameSupplierStore}`),l.pauseFromApp=!0,l.digitalWalletService.pushCard(b).then(S=>{l.tokenized=S,S?l.mboProvider.toast.success("La tarjeta fue registrada exitosamente en la billetera digital","Registro exitoso"):l.mboProvider.toast.warning("No fue posible completar registro en la billetera digital, por favor intente nuevamente","Registro fallido"),l.tokenizing=!1}).finally(()=>{setTimeout(()=>{l.pauseFromApp=!1},1e3)}))})()}verifyStatusForResume(b){this.enabledTokenized&&!this.tokenizing&&!this.pauseFromApp&&this.verifyCardTokenized(b)}walletWithError(b){this.mboProvider.toast.error(b,"Registro fallido"),this.tokenizing=!1,this.errorTokenized=!0}approvedTermsAndConditions(){return this.bottomSheet||(this.bottomSheet=this.bottomSheetService.create(y.W)),this.msgTokenizing="Aprobando t\xe9rminos y condiciones",this.bottomSheet.open(),this.bottomSheet.waiting().catch(()=>!1).then(b=>(this.tokenizing=b,b))}}return _.\u0275fac=function(b){return new(b||_)(a.\u0275\u0275directiveInject(e.fG),a.\u0275\u0275directiveInject(m.V1),a.\u0275\u0275directiveInject(m.U8),a.\u0275\u0275directiveInject(m.yW),a.\u0275\u0275directiveInject(m.SE),a.\u0275\u0275directiveInject(s.ZL))},_.\u0275cmp=a.\u0275\u0275defineComponent({type:_,selectors:[["mbo-button-tokenization-card"]],inputs:{product:"product"},standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-button-tokenization-card__content",4,"ngIf"],[1,"mbo-button-tokenization-card__content"],["class","mbo-button-tokenization-card__store",3,"class","disabled","click",4,"ngIf"],["class","mbo-button-tokenization-card__devices",3,"click",4,"ngIf"],["class","mbo-button-tokenization-card__indicator",4,"ngIf"],[1,"mbo-button-tokenization-card__store",3,"disabled","click"],[1,"body1-medium"],[3,"src"],[1,"mbo-button-tokenization-card__devices",3,"click"],[1,"smalltext-medium"],[1,"mbo-button-tokenization-card__indicator"],[3,"indeterminate"],[1,"overline-medium"]],template:function(b,l){1&b&&a.\u0275\u0275template(0,h,4,3,"div",0),2&b&&a.\u0275\u0275property("ngIf",l.enabledTokenized)},dependencies:[r.CommonModule,r.NgIf,e.cp],styles:["mbo-button-tokenization-card{position:relative;display:block;margin:var(--sizing-x4) 0rem var(--sizing-x8) 0rem}mbo-button-tokenization-card .mbo-button-tokenization-card__content{position:relative;display:flex;flex-direction:column;justify-content:center;row-gap:var(--sizing-x8)}mbo-button-tokenization-card .mbo-button-tokenization-card__store{--pvt-span-font-color: var(--color-carbon-lighter-50);--pvt-span-display: block;--pvt-img-height: 12rem;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);outline:none;border-radius:var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-darker-1000)}mbo-button-tokenization-card .mbo-button-tokenization-card__store.android{padding:var(--sizing-x6) var(--sizing-x8);border-radius:var(--sizing-x16)}mbo-button-tokenization-card .mbo-button-tokenization-card__store.ios{padding:var(--sizing-x6) var(--sizing-x8);border-radius:var(--sizing-x4)}mbo-button-tokenization-card .mbo-button-tokenization-card__store:disabled{opacity:.5}mbo-button-tokenization-card .mbo-button-tokenization-card__store>span{display:var(--pvt-span-display);color:var(--pvt-span-font-color)}mbo-button-tokenization-card .mbo-button-tokenization-card__store>img{height:var(--pvt-img-height)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices{display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);outline:none;padding:var(--sizing-x6) var(--sizing-x4);background:var(--color-carbon-darker-50);border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices>span{color:var(--color-carbon-lighter-700)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices>img{height:12rem}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator{display:flex;flex-direction:column;row-gap:var(--sizing-x3)}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator bocc-progress-bar{box-shadow:none;padding:0rem var(--sizing-x8);box-sizing:border-box;height:var(--sizing-x1)}\n"],encapsulation:2}),_})()},74561:(j,I,t)=>{t.d(I,{yb:()=>ee.y,Y6:()=>s,vd:()=>y,CO:()=>f,D9:()=>b,Ep:()=>J,Pq:()=>Z,zF:()=>F,E8:()=>B,Gd:()=>z,QH:()=>$,YT:()=>w}),t(57384);var r=t(17007),e=t(79798),i=t(30263),o=t(99877);let s=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.Zl,i.jK,i.b6,e.To]}),E})();var m=t(10533);let y=(()=>{class E{constructor(R){this.consentSPIService=R}ngBoccPortal(R){this.portal=R}onSubmit(){this.consentSPIService.approve(),this.portal?.destroy()}onDecline(){this.consentSPIService.decline(),this.portal?.destroy()}}return E.\u0275fac=function(R){return new(R||E)(o.\u0275\u0275directiveInject(m.J))},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-consent-spi-modal"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:13,vars:0,consts:[[1,"mbo-consent-spi-modal__content"],[1,"mbo-consent-spi-modal__body"],[1,"mbo-consent-spi-modal__title","smalltext-medium"],[1,"mbo-consent-spi-modal__message"],[1,"mbo-consent-spi-modal__footer"],["id","btn_consent-spi-modal_submit","bocc-button","raised",3,"click"],["id","btn_consent-spi-modal_decline","bocc-button","outline",3,"click"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),o.\u0275\u0275text(3," CONSENTIMIENTO TRATAMIENTO DE DATOS SISTEMA DE PAGOS INMEDIATOS "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"p",3),o.\u0275\u0275text(5," Autorizo a Banco de Occidente para que incorpore, actualice, consulte, modifique, habilite, comparta, traslade mi nombre, documento de identificaci\xf3n, n\xfamero de telefon\xeda m\xf3vil, correo electr\xf3nico, llave alfanum\xe9rica, n\xfamero de productos financieros y cualquier otro dato asociado a mis medios de pago, con el fin de poblar los directorios federados y centralizados de los sistemas de pagos de bajo valor inmediato, entre ellos, el Banco de la Rep\xfablica con el prop\xf3sito de adelantar todos y cada uno de los procesos relacionados con el funcionamiento del SPBVI. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(6,"div",4)(7,"button",5),o.\u0275\u0275listener("click",function(){return q.onSubmit()}),o.\u0275\u0275elementStart(8,"span"),o.\u0275\u0275text(9,"Aceptar"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(10,"button",6),o.\u0275\u0275listener("click",function(){return q.onDecline()}),o.\u0275\u0275elementStart(11,"span"),o.\u0275\u0275text(12,"No aceptar"),o.\u0275\u0275elementEnd()()()())},dependencies:[r.CommonModule,i.P8],styles:["mbo-consent-spi-modal{--pvt-message-font-size: var(--body2-size);position:relative;display:block;box-sizing:border-box}mbo-consent-spi-modal .mbo-consent-spi-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-consent-spi-modal .mbo-consent-spi-modal__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x6)}mbo-consent-spi-modal .mbo-consent-spi-modal__title{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-consent-spi-modal .mbo-consent-spi-modal__message{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-lighter-700);font-size:var(--pvt-message-font-size);letter-spacing:var(--body2-letter-spacing);min-height:var(--body2-line-height);line-height:var(--body2-line-height);font-weight:var(--font-weight-medium)}mbo-consent-spi-modal .mbo-consent-spi-modal__footer{--bocc-button-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}@media screen and (max-height: 600px){mbo-consent-spi-modal{--pvt-message-font-size: var(--smalltext-size)}}\n"],encapsulation:2}),E})();var d=t(39904),p=t(27236),u=t(95437),c=t(87956),a=t(2460),C=t(45542),v=t(66613);let f=(()=>{class E{constructor(R,q){this.mboProvider=R,this.storageService=q}ngBoccPortal(R){this.portal=R}onSubmit(){this.storageService.set(p.Z.DigitalInformation,!0),this.portal?.close(),setTimeout(()=>{this.portal?.destroy()},240)}onLink(){this.mboProvider.openUrl(d.BA.BANK)}}return E.\u0275fac=function(R){return new(R||E)(o.\u0275\u0275directiveInject(u.ZL),o.\u0275\u0275directiveInject(c.V1))},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-occiflex-bluescreen"]],decls:44,vars:1,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","e-commerce"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],["icon","error"],[1,"subtitle1-medium"],[1,"body1-medium"],[3,"click"],["bocc-theme","alert","icon","warning","title","No incluye",3,"visible"],[1,"bocc-bluescreen__footer"],["id","btn_occiflex-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-icon",2),o.\u0275\u0275elementStart(3,"label"),o.\u0275\u0275text(4," \xa1Beneficios Credencial Occiflex! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"p"),o.\u0275\u0275text(6," Disponibilidad inmediata en Banca M\xf3vil para ser activada por el cliente y realizar compras On-Line o Presentes mediante QR din\xe1mico. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),o.\u0275\u0275text(9," Aprobaci\xf3n en 5 Minutos sin documentos f\xedsicos a trav\xe9s de la plataforma digital. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(10,"li",4),o.\u0275\u0275text(11," 50% descuento permanente en la tasa de inter\xe9s para compras en 1 de las siguientes categor\xedas: "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(12,"div",1),o.\u0275\u0275element(13,"bocc-icon",5),o.\u0275\u0275elementStart(14,"label",6),o.\u0275\u0275text(15," Importante "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(16,"p",7),o.\u0275\u0275text(17," Las categor\xedas de comercio las puede cambiar el cliente cada 6 meses de acuerdo con sus necesidades de consumo. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(18,"ul",3)(19,"li",4),o.\u0275\u0275text(20," Cuota de Manejo Din\xe1mica sujeta cumplimiento de meta de facturaci\xf3n: (Consultar la meta de facturaci\xf3n en "),o.\u0275\u0275elementStart(21,"a",8),o.\u0275\u0275listener("click",function(){return q.onLink()}),o.\u0275\u0275text(22,"www.bancodeoccidente.com.co"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(23,"). "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(24,"li",4),o.\u0275\u0275text(25," Seguro de protecci\xf3n de precios hasta $200 USD: Esta protecci\xf3n compensar\xe1 al cliente por diferencia precio cuando en los 30 d\xedas luego de la compra con la TC identifica el mismo art\xedculo a precio m\xe1s bajo. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(26,"li",4),o.\u0275\u0275text(27," Participar en diferentes campa\xf1as con nuestros aliados con beneficios de descuentos, campa\xf1as de temporadas y de cashback. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(28,"li",4),o.\u0275\u0275text(29," Pago m\xednimo alterno: disminuye el valor de la cuota mensual de tu Tarjeta de Cr\xe9dito Credencial, ampliando el plazo de pago a 48 cuotas tanto para las compras m\xe1s antiguas y las m\xe1s recientes, respetando la tasa de inter\xe9s inicial de cada compra. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(30,"li",4),o.\u0275\u0275text(31," Seguro de vida deudor: cubre el 100% del saldo de la Tarjeta en caso de fallecimiento o incapacidad total y permanentemente del titular. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(32,"li",4),o.\u0275\u0275text(33,"Experiencias Aval y preventas."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(34,"bocc-alert",9)(35,"ul")(36,"li"),o.\u0275\u0275text(37,"Acumulaci\xf3n de Puntos Tupl\xfas."),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(38,"li"),o.\u0275\u0275text(39," No aplica para compra de cartera, avances con abono a cuenta u operaciones a tasa especial. "),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(40,"div",10)(41,"button",11),o.\u0275\u0275listener("click",function(){return q.onSubmit()}),o.\u0275\u0275elementStart(42,"span"),o.\u0275\u0275text(43,"Continuar"),o.\u0275\u0275elementEnd()()()),2&R&&(o.\u0275\u0275advance(34),o.\u0275\u0275property("visible",!0))},dependencies:[a.Z,C.P,v.B],styles:["mbo-occiflex-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-occiflex-bluescreen bocc-alert ul{list-style-type:initial;display:flex;flex-direction:column;row-gap:var(--sizing-x2)}\n"],encapsulation:2}),E})();t(38487);var _=t(90005);let M=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.Gf,i.qw,i.FL,i.u]}),E})(),b=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.Gf,i.qw,i.P8,i.Zl,i.Dj,_.C,e.$n,e.wu,e.ZF,M]}),E})();t(56204);var T=t(33395);let S=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,e.xO,i.P8,T.kW]}),E})(),O=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.P8,e.xO]}),E})(),N=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,e.xO]}),E})(),te=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,N,S,O]}),E})(),Z=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.Dj]}),E})(),V=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule]}),E})(),Y=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.X6]}),E})(),U=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.P8]}),E})(),J=(()=>{class E{}return E.\u0275fac=function(R){return new(R||E)},E.\u0275mod=o.\u0275\u0275defineNgModule({type:E}),E.\u0275inj=o.\u0275\u0275defineInjector({imports:[r.CommonModule,i.ud,i.P8,te,V,Y,U,Z]}),E})();t(224);var w=(()=>{return(E=w||(w={}))[E.Requirements=0]="Requirements",E[E.Beneficiaries=1]="Beneficiaries",w;var E})();let F=(()=>{class E{constructor(){this.position=w.Requirements,this.headers=[{label:"Requisitos",value:w.Requirements},{label:"Beneficios",value:w.Beneficiaries}]}ngBoccPortal(R){R.receive(q=>{this.position=q})}}return E.\u0275fac=function(R){return new(R||E)},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-remittances-bottom-sheet"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:31,vars:4,consts:[[1,"mbo-remittances-bottom-sheet__content"],[3,"tabs","invert","value","valueChange"],[3,"position"],[1,"bocc-tab-form__view"],[1,"mbo-remittances-bottom-sheet__requirements"],[1,"subtitle2-medium"],[1,"mbo-remittances-bottom-sheet__list"],[1,"mbo-remittances-bottom-sheet__beneficiaries"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"div",0)(1,"bocc-tab-header",1),o.\u0275\u0275listener("valueChange",function(ce){return q.position=ce}),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(2,"bocc-tab-form",2)(3,"div",3)(4,"div",4)(5,"label",5),o.\u0275\u0275text(6," Esto necesitas para recibir tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(7,"ul",6)(8,"li")(9,"span"),o.\u0275\u0275text(10," Tener una cuenta de ahorros o corriente y una tarjeta d\xe9bito activa asociada a la cuenta. "),o.\u0275\u0275elementEnd()()()()(),o.\u0275\u0275elementStart(11,"div",3)(12,"div",7)(13,"label",5),o.\u0275\u0275text(14," Estos son los beneficios al recibir tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(15,"ul",6)(16,"li")(17,"span"),o.\u0275\u0275text(18," Recibe el dinero en pesos a tu cuenta de ahorros o corriente. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(19,"li")(20,"span"),o.\u0275\u0275text(21,"Costo cero $0."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(22,"li")(23,"span"),o.\u0275\u0275text(24," Usa el mismo enlace de pago para enviar a diferentes contactos. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(25,"li")(26,"span"),o.\u0275\u0275text(27,"Recibe hasta USD 1.000 al mes y 6 transferencias por semana."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(28,"li")(29,"span"),o.\u0275\u0275text(30,"Aplica \xfanicamente desde Estados Unidos."),o.\u0275\u0275elementEnd()()()()()()()),2&R&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("tabs",q.headers)("invert",!0)("value",q.position),o.\u0275\u0275advance(1),o.\u0275\u0275property("position",q.position))},dependencies:[r.CommonModule,i.Gf,i.qw],styles:["mbo-remittances-bottom-sheet{position:relative;width:100%;display:block}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__requirements,mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__beneficiaries{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__requirements>label,mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__beneficiaries>label{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__requirements{margin-bottom:var(--sizing-x16)}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__list{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding-inline-start:var(--sizing-x8);list-style-type:square}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__list>li{font-size:var(--body2-size);letter-spacing:var(--body2-letter-spacing);line-height:var(--body2-line-height);color:var(--color-carbon-lighter-700)}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__list>li span{display:block;margin-left:var(--sizing-x2)}mbo-remittances-bottom-sheet .mbo-remittances-bottom-sheet__list>li::marker{color:var(--color-blue-700)}\n"],encapsulation:2}),E})(),B=(()=>{class E{constructor(R){this.mboProvider=R,this.headerAction={id:"btn_remittances-onboarding_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.portal?.close()}},this.footerAction={id:"btn_remittances-onboarding_open",type:"raised",prefixIcon:"money-hand",label:"Recibir remesas",click:()=>{this.mboProvider.openUrl(d.BA.REMITTANCES),this.portal?.close()}}}ngBoccPortal(R){this.portal=R}}return E.\u0275fac=function(R){return new(R||E)(o.\u0275\u0275directiveInject(u.ZL))},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-remittances-onboarding"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:24,vars:2,consts:[[3,"headerActionLeft","footerActionRight"],["src","assets/shared/onboardings/remittances/remittances-01.jpg"],["src","assets/shared/onboardings/remittances/remittances-02.jpg"],["src","assets/shared/onboardings/remittances/remittances-03.jpg"],["src","assets/shared/onboardings/remittances/remittances-04.jpg"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),o.\u0275\u0275text(3," Recibe tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"p"),o.\u0275\u0275text(5,"Dar click en el bot\xf3n recibir remesas."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),o.\u0275\u0275text(8," Recibe tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"p"),o.\u0275\u0275text(10," Registrar los siguientes datos para el env\xedo del c\xf3digo de autenticaci\xf3n "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(11,"mbo-onboarding-element",3)(12,"label"),o.\u0275\u0275text(13," Recibe tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(14,"p"),o.\u0275\u0275text(15," Una vez registres tus datos personales, ingresa a la opci\xf3n "),o.\u0275\u0275elementStart(16,"b"),o.\u0275\u0275text(17,"A\xf1adir tarjeta"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(18," donde deber\xe1s diligenciar los datos de t\xfa tarjeta d\xe9bito. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(19,"mbo-onboarding-element",4)(20,"label"),o.\u0275\u0275text(21," Recibe tus remesas "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(22,"p"),o.\u0275\u0275text(23," Al finalizar la inscripci\xf3n de t\xfa tarjeta d\xe9bito comparte el link a tus familiares y amigos de Estados Unidos para recibir el giro. "),o.\u0275\u0275elementEnd()()()),2&R&&o.\u0275\u0275property("headerActionLeft",q.headerAction)("footerActionRight",q.footerAction)},dependencies:[e.Nu,e.NH],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}\n"],encapsulation:2}),E})();var P=t(15861),W=t(57544),Q=t(54747);const{REMITTANCES:oe}=d.Z6.CUSTOMER.PRODUCTS;let z=(()=>{class E{constructor(R,q,re){this.mboProvider=R,this.managerRemittances=q,this.legalDocumentProvider=re,this.downloading=!1,this.termAndConditionControl=new W.FormControl(!1)}ngBoccPortal(R){this.portal=R}onSubmit(){var R=this;return(0,P.Z)(function*(){(yield R.managerRemittances.approvedTyC()).when({success:()=>{R.mboProvider.navigation.next(oe)}},()=>{R.portal?.close(),R.portal?.destroy()})})()}onTermAndConditions(){this.downloading||(this.downloading=!0,this.legalDocumentProvider.termsAndConditionsRemittances().finally(()=>{this.downloading=!1}))}}return E.\u0275fac=function(R){return new(R||E)(o.\u0275\u0275directiveInject(u.ZL),o.\u0275\u0275directiveInject(Q.gv),o.\u0275\u0275directiveInject(u.uD))},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-remittances-tyc-modal"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:14,vars:4,consts:[[1,"mbo-remittances-tyc-modal__content"],[1,"mbo-remittances-tyc-modal__body"],[1,"mbo-remittances-tyc-modal__title","smalltext-medium"],[1,"mbo-remittances-tyc-modal__message","body2-medium"],["elementId","chck_remittances-tyc-modal_accept",3,"formControl"],["id","lnk_remittances-modal_tyc",3,"click"],[1,"mbo-remittances-tyc-modal__footer"],["bocc-button","raised",3,"disabled","click"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),o.\u0275\u0275text(3," \xa1TE DAMOS LA BIENVENIDA AL SERVICIO DE REMESAS EN L\xcdNEA! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"p",3),o.\u0275\u0275text(5," Para continuar debes "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"bocc-checkbox-label",4),o.\u0275\u0275text(7," Aceptar "),o.\u0275\u0275elementStart(8,"a",5),o.\u0275\u0275listener("click",function(){return q.onTermAndConditions()}),o.\u0275\u0275text(9," T\xe9rminos y condiciones. "),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275elementStart(10,"div",6)(11,"button",7),o.\u0275\u0275listener("click",function(){return q.onSubmit()}),o.\u0275\u0275elementStart(12,"span"),o.\u0275\u0275text(13,"Continuar"),o.\u0275\u0275elementEnd()()()()),2&R&&(o.\u0275\u0275advance(6),o.\u0275\u0275property("formControl",q.termAndConditionControl),o.\u0275\u0275advance(2),o.\u0275\u0275classProp("downloading",q.downloading),o.\u0275\u0275advance(3),o.\u0275\u0275property("disabled",!q.termAndConditionControl.value))},dependencies:[r.CommonModule,i.aR,i.P8],styles:["mbo-remittances-tyc-modal{position:relative;display:block;box-sizing:border-box}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__body bocc-checkbox-label a.downloading{pointer-events:none;opacity:.36}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__title{position:relative;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__footer{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-remittances-tyc-modal .mbo-remittances-tyc-modal__footer>button{width:100%}\n"],encapsulation:2}),E})(),$=(()=>{class E{ngBoccPortal(R){}}return E.\u0275fac=function(R){return new(R||E)},E.\u0275cmp=o.\u0275\u0275defineComponent({type:E,selectors:[["mbo-tag-aval-bottom-sheet"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:24,vars:0,consts:[[1,"mbo-tag-aval-bottom-sheet__content"],[1,"mbo-tag-aval-bottom-sheet__title","subtitle2-medium"],[1,"mbo-tag-aval-bottom-sheet__list"]],template:function(R,q){1&R&&(o.\u0275\u0275elementStart(0,"div",0)(1,"label",1),o.\u0275\u0275text(2," Recomendaciones para tu Tag Aval: "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"ul",2)(4,"li")(5,"span")(6,"b"),o.\u0275\u0275text(7,"Usa letras y n\xfameros f\xe1ciles de recordar:"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(8," Elige combinaciones que puedas recordar sin dificultad. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(9,"li")(10,"span")(11,"b"),o.\u0275\u0275text(12,"Elige algo cercano a ti:"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(13," Puedes usar nombres de personas, mascotas o apodos que sean significativos para ti. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(14,"li")(15,"span")(16,"b"),o.\u0275\u0275text(17,"Evita palabras sensibles o conflictivas:"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(18," No uses t\xe9rminos ofensivos, pol\xedticos o de cualquier tipo discriminatorio. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(19,"li")(20,"span")(21,"b"),o.\u0275\u0275text(22,"No incluyas nombres comerciales o de marcas:"),o.\u0275\u0275elementEnd(),o.\u0275\u0275text(23," No uses nombres de empresas o productos registrados. "),o.\u0275\u0275elementEnd()()()())},dependencies:[r.CommonModule],styles:["mbo-tag-aval-bottom-sheet{position:relative;width:100%;display:block}mbo-tag-aval-bottom-sheet .mbo-tag-aval-bottom-sheet__content{display:flex;flex-direction:column;row-gap:var(--sizing-x16);padding:var(--sizing-x16);box-sizing:border-box}mbo-tag-aval-bottom-sheet .mbo-tag-aval-bottom-sheet__list{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding-inline-start:var(--sizing-x8);list-style-type:square}mbo-tag-aval-bottom-sheet .mbo-tag-aval-bottom-sheet__list>li{font-size:var(--body2-size);letter-spacing:var(--body2-letter-spacing);line-height:var(--body2-line-height);color:var(--color-carbon-lighter-700)}mbo-tag-aval-bottom-sheet .mbo-tag-aval-bottom-sheet__list>li span{display:block;margin-left:var(--sizing-x2)}mbo-tag-aval-bottom-sheet .mbo-tag-aval-bottom-sheet__list>li::marker{color:var(--color-blue-700)}\n"],encapsulation:2}),E})();t(31981),t(87777),t(48117);var ee=t(20586)},38487:(j,I,t)=>{t.d(I,{G:()=>A});var n=t(15861),r=t(99877),e=t(30263),i=t(78506),o=t(39904),s=t(29306),m=t(89148),y=t(87903),d=t(95437),p=t(21260),u=t(87956),c=t(57544),a=t(54747),C=t(74558),v=t(52808),f=t(75283),h=t(16857),g=t(30876),_=(()=>{return(w=_||(_={}))[w.Functions=0]="Functions",w[w.Beneficiaries=1]="Beneficiaries",_;var w})();let M=(()=>{class w{constructor(B){this.mboProvider=B,this.digitalName=o.CG,this.position=_.Functions,this.headers=[{label:"Funcionamiento",value:_.Functions},{label:"Beneficios",value:_.Beneficiaries}]}ngBoccPortal(B){}onLink(){this.mboProvider.openUrl(o.BA.BANK)}}return w.\u0275fac=function(B){return new(B||w)(r.\u0275\u0275directiveInject(d.ZL))},w.\u0275cmp=r.\u0275\u0275defineComponent({type:w,selectors:[["mbo-occiflex-bottom-sheet"]],decls:84,vars:6,consts:[[1,"mbo-occiflex-bottom-sheet__content"],[3,"tabs","invert","value","valueChange"],[3,"position"],[1,"bocc-tab-form__view"],[1,"mbo-occiflex-bottom-sheet__functions"],[1,"subtitle2-medium"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-regular","align-justify"],["title","\xbfTengo que recargar esta tarjeta para hacer compras?"],["title","\xbfEsta tarjeta tiene comisiones?"],["title","\xbfPuedo sacar dinero de mi tarjeta digital en un cajero?"],["title","\xbfPuedo hacer compras en tiendas f\xedsicas?"],[1,"mbo-occiflex-bottom-sheet__beneficiaries"],[1,"mbo-occiflex-bottom-sheet__beneficiaries__component"],[1,"mbo-occiflex-bottom-sheet__diamonds"],[1,"mbo-occiflex-bottom-sheet__catalog"],[1,"body2-regular"],[1,"mbo-occiflex-bottom-sheet__enumerators"],[3,"click"]],template:function(B,P){1&B&&(r.\u0275\u0275elementStart(0,"div",0)(1,"bocc-tab-header",1),r.\u0275\u0275listener("valueChange",function(Q){return P.position=Q}),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(2,"bocc-tab-form",2)(3,"div",3)(4,"div",4)(5,"label",5),r.\u0275\u0275text(6),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"bocc-accordion")(8,"bocc-accordion-element",6)(9,"p",7),r.\u0275\u0275text(10," Es una tarjeta de cr\xe9dito virtual, por lo que no existe f\xedsicamente. Esta tarjeta solo estar\xe1 disponible en los Canales Digitales (Portal Transaccional y Banca M\xf3vil)."),r.\u0275\u0275element(11,"br")(12,"br"),r.\u0275\u0275text(13," Para poder ver la informaci\xf3n de tu Tarjeta como el n\xfamero completo, fecha de vencimiento y CVV haz click en la tarjeta e ingresa el Token Mobile para validar tu identidad. "),r.\u0275\u0275element(14,"br")(15,"br"),r.\u0275\u0275text(16,"Por seguridad, podr\xe1s ver esta informaci\xf3n durante un lapso de tiempo. Posteriormente, esta informaci\xf3n ser\xe1 cifrada hasta que vuelvas a abrir tu tarjeta. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(17,"bocc-accordion-element",8)(18,"p",7),r.\u0275\u0275text(19," No, estas tarjetas nacen con un cupo de acuerdo a la capacidad de endeudamiento luego del estudio crediticio que se realiza en el proceso de otorgamiento, as\xed como como hoy en d\xeda funciona para las solicitudes de Tarjeta de cr\xe9dito f\xedsica. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(20,"bocc-accordion-element",9)(21,"p",7),r.\u0275\u0275text(22," No, solo se efectuara cobro de cuota de manejo y seguro, es importante aclarar que el cobro de la cuota de manejo opera de manera din\xe1mica y esta sujeta al cumplimiento o metas de facturaci\xf3n. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(23,"bocc-accordion-element",10)(24,"p",7),r.\u0275\u0275text(25,"No."),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(26,"bocc-accordion-element",11)(27,"p",7),r.\u0275\u0275text(28," La tarjeta est\xe1 dise\xf1ada para que puedan efectuar compras por medio de los canales digitales, sin embargo las redes tienen habilitados los datafonos con la opci\xf3n de compra con QR, el cual se puede escanear a trav\xe9s de la App del Banco para realizar el pago por este medio. "),r.\u0275\u0275elementEnd()()()()(),r.\u0275\u0275elementStart(29,"div",3)(30,"div",12)(31,"div",13)(32,"label",5),r.\u0275\u0275text(33),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(34,"ul",14)(35,"li"),r.\u0275\u0275text(36," Disponibilidad inmediata en Banca M\xf3vil para ser activada por el cliente y realizar compras On-Line o Presentes mediante QR din\xe1mico. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(37,"li"),r.\u0275\u0275text(38," Aprobaci\xf3n en 5 Minutos sin documentos f\xedsicos a trav\xe9s de la plataforma digital. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(39,"li")(40,"b"),r.\u0275\u0275text(41,"50% descuento permanente en la tasa de inter\xe9s"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(42," para compras en 1 de las siguientes categor\xedas: "),r.\u0275\u0275elementStart(43,"ul",15)(44,"li"),r.\u0275\u0275text(45,"Comercio Electr\xf3nico"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(46,"li"),r.\u0275\u0275text(47,"Restaurantes"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(48,"li"),r.\u0275\u0275text(49,"Vestuario"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(50,"li"),r.\u0275\u0275text(51,"Supermercados"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(52,"li"),r.\u0275\u0275text(53,"Entretenimiento"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(54,"li"),r.\u0275\u0275text(55,"Hoteles"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(56,"li"),r.\u0275\u0275text(57,"Aerol\xedneas"),r.\u0275\u0275elementEnd()()()()(),r.\u0275\u0275elementStart(58,"div",13)(59,"label",5),r.\u0275\u0275text(60,"Importante"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(61,"p",16),r.\u0275\u0275text(62," Las categor\xedas de comercio las puede cambiar el cliente cada 6 meses de acuerdo con sus necesidades de consumo. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(63,"ul",17)(64,"li"),r.\u0275\u0275text(65," Cuota de Manejo Din\xe1mica sujeta cumplimiento de meta de facturaci\xf3n: (Consultar la meta de facturaci\xf3n en "),r.\u0275\u0275elementStart(66,"a",18),r.\u0275\u0275listener("click",function(){return P.onLink()}),r.\u0275\u0275text(67,"www.bancodeoccidente.com.co"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(68,"). "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(69,"li"),r.\u0275\u0275text(70," Seguro de protecci\xf3n de precios hasta $200 USD: Esta protecci\xf3n compensar\xe1 al cliente por diferencia precio cuando en los 30 d\xedas luego de la compra con la TC identifica el mismo art\xedculo a precio m\xe1s bajo. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(71,"li"),r.\u0275\u0275text(72," Participar en diferentes campa\xf1as con nuestros aliados con beneficios de descuentos, campa\xf1as de temporadas y de cashback. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(73,"li"),r.\u0275\u0275text(74," Pago m\xednimo alterno: disminuye el valor de la cuota mensual de tu Tarjeta de Cr\xe9dito Credencial, ampliando el plazo de pago a 48 cuotas tanto para las compras m\xe1s antiguas y las m\xe1s recientes, respetando la tasa de inter\xe9s inicial de cada compra. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(75,"li"),r.\u0275\u0275text(76," Seguro de vida deudor: cubre el 100% del saldo de la Tarjeta en caso de fallecimiento o incapacidad total y permanentemente del titular. Experiencias Aval y preventas. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(77,"p",16),r.\u0275\u0275text(78,"No incluye:"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(79,"ul",17)(80,"li"),r.\u0275\u0275text(81,"Acumulaci\xf3n de Puntos Tupl\xfas."),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(82,"li"),r.\u0275\u0275text(83," No aplica para compra de cartera, avances con abono a cuenta u operaciones a tasa especial. "),r.\u0275\u0275elementEnd()()()()()()()),2&B&&(r.\u0275\u0275advance(1),r.\u0275\u0275property("tabs",P.headers)("invert",!0)("value",P.position),r.\u0275\u0275advance(1),r.\u0275\u0275property("position",P.position),r.\u0275\u0275advance(4),r.\u0275\u0275textInterpolate1(" As\xed funciona tu tarjeta de cr\xe9dito ",P.digitalName," "),r.\u0275\u0275advance(27),r.\u0275\u0275textInterpolate1(" As\xed son los beneficios de tu ",P.digitalName," "))},dependencies:[v.G,f.q,h.F,g.u],styles:["mbo-occiflex-bottom-sheet{position:relative;width:100%;display:block}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__functions{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__beneficiaries{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__beneficiaries__component{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__beneficiaries__component>p{color:var(--color-carbon-lighter-700)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__beneficiaries ul{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding-inline-start:var(--sizing-x12)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__beneficiaries ul>li{font-size:var(--body2-size);letter-spacing:var(--body2-letter-spacing);line-height:var(--body2-line-height);color:var(--color-carbon-lighter-700)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__diamonds{list-style-type:square}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__diamonds>li::marker{color:var(--color-blue-700)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__catalog{list-style-type:initial;margin-top:var(--sizing-x12)}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__enumerators{list-style-type:auto}mbo-occiflex-bottom-sheet .mbo-occiflex-bottom-sheet__enumerators>li::marker{font-weight:var(--font-weight-semibold)}\n"],encapsulation:2}),w})();var b=t(17007),l=t(45542),T=t(90005),S=t(19310),O=t(68819),N=t(38116);function te(w,F){if(1&w&&r.\u0275\u0275element(0,"mbo-button-tokenization-card",8),2&w){const B=r.\u0275\u0275nextContext();r.\u0275\u0275property("product",B.product)}}function Z(w,F){if(1&w){const B=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"div",9)(1,"button",10),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(B);const W=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(W.onDigitalFunctions())}),r.\u0275\u0275elementStart(2,"span"),r.\u0275\u0275text(3,"\xbfC\xf3mo funciona tu tarjeta?"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(4,"button",10),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(B);const W=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(W.onDigitalBeneficiaries())}),r.\u0275\u0275elementStart(5,"span"),r.\u0275\u0275text(6,"Beneficios Credencial OcciFlex"),r.\u0275\u0275elementEnd()()()}}const{PAYMENTS:V,TRANSACTIONS:Y,TRANSFERS:U}=o.Z6,J={ADVANCE:U.ADVANCE.DESTINATION,BLOCK_DEBIT_CARD:Y.BLOCK_DEBIT_CARD,QUICK_WITHDRAWAL:Y.QUICK_WITHDRAWAL.BENEFICIARY,TRANSFER:U.GENERIC.DESTINATION,BLOCK_CREDIT_CARD:Y.BLOCK_CREDIT_CARD,PAY_CREDIT_CARD:V.CREDIT_CARD.SOURCE,PAY_LOAN:V.LOAN.SOURCE,USE_QUOTA:Y.CREDIT_USE_QUOTA.DESTINATION,STATEMENT:Y.STATEMENT_PRODUCT};let A=(()=>{class w{constructor(B,P,W,Q,oe,z,$,x,D,G){this.modalConfirmation=B,this.bottomSheetService=P,this.mboProvider=W,this.digitalProvider=Q,this.documentErrorModal=oe,this.deviceService=z,this.digitalSecureService=$,this.managerInformation=x,this.managerDigitalCard=D,this.downloadPayPlanLoan=G,this.unsubscriptions=[],this.canTokenizationOS=!1,this.requesting=!1,this.actions=[],this.back=new r.EventEmitter,this.currencyControl=new c.FormControl(o.y1)}ngOnInit(){var B=this;this.unsubscriptions.push(this.digitalSecureService.subscribe(function(){var P=(0,n.Z)(function*(W){B.product?.id===W.productId&&B.requestDigitalSection(B.product)});return function(W){return P.apply(this,arguments)}}())),this.unsubscriptions.push(this.currencyControl.subscribe(P=>{if(P&&this.product){this.movements=void 0;const W=this.managerInformation.requestMovements({product:this.product,currencyCode:P.code});this.resolveMovements(W)}})),this.deviceService.itIsAvailabilityHuaweiServices().then(P=>{this.canTokenizationOS=!P})}ngOnDestroy(){this.unsubscriptions.forEach(B=>{B()}),this.occiflexSheet?.destroy(),this.tokenizationSheet?.destroy()}ngOnChanges(B){const{product:P}=B;P?.currentValue&&(this.requestInformation(P.currentValue),P.currentValue.isDigital&&this.requestDigitalSection(P.currentValue))}get productIsTokenizable(){return this.canTokenizationOS&&!!this.product&&"product"===this.product.categoryType&&"CCA"===this.product.type}onClose(){this.back.emit()}onDigital(B){this.digitalProvider.execute(B)}onAction(B){switch(B){case m.Xv.StatementFailed:this.documentErrorModal.execute();break;case m.Xv.ResetDigitalCard:this.resetDigitalCard();break;case m.Xv.PayPlanLoan:this.product&&this.downloadPayPlanLoan.execute(this.product);break;default:J[B]&&this.product&&this.mboProvider.navigation.next(J[B],{productId:this.product.id})}}onDigitalFunctions(){this.openOcciflexSheet(_.Functions)}onDigitalBeneficiaries(){this.openOcciflexSheet(_.Beneficiaries)}requestInformation(B){var P=this;return(0,n.Z)(function*(){P.requesting=!0;const{movements$:W,sections:Q,digitalSection:oe}=(yield P.managerInformation.request({product:B,currencyCode:P.currencyControl.value?.code})).when({success:z=>z,failure:({value:z})=>({movements$:z,sections:[]})},()=>{P.requesting=!1});P.resolveMovements(W),P.digitalSection=oe,P.sections=Q,P.actions=(0,y.rz)(B)})()}resolveMovements(B){B.then(P=>{this.movements=P}).catch(()=>{this.movements=s.kq.empty()})}openOcciflexSheet(B){this.occiflexSheet||(this.occiflexSheet=this.bottomSheetService.create(M,{componentProps:{position:B}})),this.occiflexSheet.open(120)}requestDigitalSection(B){var P=this;return(0,n.Z)(function*(){(yield P.managerInformation.requestDigitalSection(B)).when({success:({digitalSection:W})=>{P.digitalSection=W}})})()}resetDigitalCard(){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/reset-digital-card.svg",title:"GENERAR NUEVOS DATOS",message:"Por seguridad, puedes cambiar los datos de tu Credencial Occiflex, recuerda que este cambio tiene un costo.<br><br>Deber\xe1s volver a inscribir los datos de tu tarjeta en los comercios donde la tengas registrada.",accept:{label:"Generar",click:()=>{this.confirmResetDigitalCard()}},decline:{label:"Cancelar"}})}confirmResetDigitalCard(){var B=this;return(0,n.Z)(function*(){B.mboProvider.loader.open("Generando nuevos datos, por favor espere..."),(yield B.managerDigitalCard.reset(B.product)).when({success:()=>{B.mboProvider.toast.success("Se generaron los nuevos datos de tu tarjeta, recuerda que este cambio tiene un costo.","Cambio de datos exitoso")},failure:({message:P})=>{B.mboProvider.toast.error(P,"Cambio de datos no exitoso")}},()=>{B.mboProvider.loader.close()})})()}}return w.\u0275fac=function(B){return new(B||w)(r.\u0275\u0275directiveInject(e.$e),r.\u0275\u0275directiveInject(e.fG),r.\u0275\u0275directiveInject(d.ZL),r.\u0275\u0275directiveInject(C.t),r.\u0275\u0275directiveInject(p.$),r.\u0275\u0275directiveInject(u.U8),r.\u0275\u0275directiveInject(u.ZP),r.\u0275\u0275directiveInject(i.vu),r.\u0275\u0275directiveInject(a.r$),r.\u0275\u0275directiveInject(C.z))},w.\u0275cmp=r.\u0275\u0275defineComponent({type:w,selectors:[["mbo-product-info-modal"]],inputs:{product:"product"},outputs:{back:"back"},features:[r.\u0275\u0275NgOnChangesFeature],decls:8,vars:15,consts:[[1,"mbo-product-info-modal__content"],[1,"mbo-product-info-modal__header"],[3,"product","currencyControl","close","digital"],[3,"currencyControl","product","sections","digitalSection","movements","requesting","condense"],["header","",3,"product",4,"ngIf"],["footer","","class","mbo-product-info-modal__digital-actions",4,"ngIf"],[1,"mbo-product-info-modal__footer",3,"hidden"],[3,"actions","action"],["header","",3,"product"],["footer","",1,"mbo-product-info-modal__digital-actions"],["bocc-button","flat","bocc-theme","amathyst",3,"click"]],template:function(B,P){1&B&&(r.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"mbo-product-info-card",2),r.\u0275\u0275listener("close",function(){return P.onClose()})("digital",function(Q){return P.onDigital(Q)}),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(3,"mbo-product-info-body",3),r.\u0275\u0275template(4,te,1,1,"mbo-button-tokenization-card",4),r.\u0275\u0275template(5,Z,7,0,"div",5),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",6)(7,"mbo-product-info-actions",7),r.\u0275\u0275listener("action",function(Q){return P.onAction(Q)}),r.\u0275\u0275elementEnd()()),2&B&&(r.\u0275\u0275advance(2),r.\u0275\u0275property("product",P.product)("currencyControl",P.currencyControl),r.\u0275\u0275advance(1),r.\u0275\u0275classProp("actionables",P.actions.length),r.\u0275\u0275property("currencyControl",P.currencyControl)("product",P.product)("sections",P.sections)("digitalSection",P.digitalSection)("movements",P.movements)("requesting",!P.product||P.requesting)("condense",!P.actions.length),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",P.productIsTokenizable),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",P.digitalSection),r.\u0275\u0275advance(1),r.\u0275\u0275property("hidden",!P.actions.length),r.\u0275\u0275advance(1),r.\u0275\u0275property("actions",P.actions))},dependencies:[b.NgIf,l.P,T.C,S.$,O.w,N.Z],styles:["mbo-product-info-modal{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--color-carbon-lighter-50);z-index:var(--z-index-16)}mbo-product-info-modal .mbo-product-info-modal__content{position:relative;width:100%;height:100%;overflow:auto}mbo-product-info-modal .mbo-product-info-modal__header{position:relative;width:100%;overflow:hidden}mbo-product-info-modal .mbo-product-info-modal__header .mbo-product-info-card .bocc-card-product-background__content{padding-bottom:var(--sizing-x10)}mbo-product-info-modal .mbo-product-info-modal__digital-actions{display:flex;flex-direction:column;row-gap:var(--sizing-x2);margin-top:var(--sizing-x12)}mbo-product-info-modal .mbo-product-info-modal__footer{position:absolute;width:100%;left:0rem;bottom:0rem;border-radius:var(--sizing-x6) var(--sizing-x6) 0rem 0rem;box-sizing:border-box;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-top:var(--border-1-lighter-300)}mbo-product-info-modal .mbo-product-info-modal__footer mbo-product-info-actions{margin-bottom:var(--sizing-safe-bottom)}\n"],encapsulation:2}),w})()},56204:(j,I,t)=>{t.d(I,{V:()=>oe});var n=t(99877),r=t(4360),e=t(17007),o=(t(29306),t(61980)),s=t(7427);let m=(()=>{class z{constructor(x){this.productsService=x}onProduct(){this.productsService.emitProduct(this.product)}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(o.su))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-standard"]],inputs:{product:"product",incognito:"incognito"},decls:1,vars:2,consts:[[3,"incognito","product","click"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"mbo-card-product",0),n.\u0275\u0275listener("click",function(){return D.onProduct()}),n.\u0275\u0275elementEnd()),2&x&&n.\u0275\u0275property("incognito",D.incognito)("product",D.product)},dependencies:[s.x],styles:["mbo-product-card-standard{position:relative;display:block}\n"],encapsulation:2}),z})();var y=t(78506),d=t(39904),p=t(95437),u=t(45542),c=t(25317);function a(z,$){if(1&z){const x=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",4),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(x);const G=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(G.onShare())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Compartir Tag"),n.\u0275\u0275elementEnd()()}if(2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("disabled",x.disabled)}}function C(z,$){if(1&z){const x=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",5),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(x);const G=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(G.onEditTagAval())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Personalizar Tag"),n.\u0275\u0275elementEnd()()}}let v=(()=>{class z{constructor(x,D,G){this.productsService=x,this.productInformation=D,this.mboProvider=G}onProduct(){this.productsService.emitProduct(this.product)}onShare(){this.disabled=!0,this.productInformation.shared(this.product).finally(()=>{this.disabled=!1})}onEditTagAval(){this.mboProvider.navigation.next(d.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(o.su),n.\u0275\u0275directiveInject(y.vu),n.\u0275\u0275directiveInject(p.ZL))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-account"]],inputs:{product:"product",incognito:"incognito"},decls:6,vars:4,consts:[[3,"incognito","product","click"],[1,"product-card-account__box-options"],["id","btn_product-card_share","bocc-button","flat","prefixIcon","share","boccUtagComponent","click",3,"disabled","click",4,"ngIf"],["id","btn_product-card_edit-tag","bocc-button","flat","prefixIcon","edit-pencil","boccUtagComponent","click",3,"click",4,"ngIf"],["id","btn_product-card_share","bocc-button","flat","prefixIcon","share","boccUtagComponent","click",3,"disabled","click"],["id","btn_product-card_edit-tag","bocc-button","flat","prefixIcon","edit-pencil","boccUtagComponent","click",3,"click"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"mbo-card-product",0),n.\u0275\u0275listener("click",function(){return D.onProduct()}),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(1,"div",1)(2,"div"),n.\u0275\u0275template(3,a,3,1,"button",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div"),n.\u0275\u0275template(5,C,3,0,"button",3),n.\u0275\u0275elementEnd()()),2&x&&(n.\u0275\u0275property("incognito",D.incognito)("product",D.product),n.\u0275\u0275advance(3),n.\u0275\u0275property("ngIf",D.product.bank.isOccidente),n.\u0275\u0275advance(2),n.\u0275\u0275property("ngIf",D.product.bank.isOccidente&&!!D.product.tagAval))},dependencies:[e.NgIf,s.x,u.P,c.I],styles:['mbo-product-card-account{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;display:flex;flex-direction:column;align-items:baseline;row-gap:var(--sizing-x6)}mbo-product-card-account .product-card-account__box-options{display:flex}mbo-product-card-account .product-card-account__box-options #btn_product-card_edit-tag .bocc-button__content:before{content:"";position:absolute;left:0;top:50%;height:50%;width:1px;background-color:var(--color-carbon-lighter-300);transform:translateY(-50%)}\n'],encapsulation:2}),z})();var f=t(15861),h=t(30263),g=t(87956),_=t(74558);function M(z,$){if(1&z){const x=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",2),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(x);const G=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(G.onCovereds())}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2,"Consultar tarjetas amp\xe1radas"),n.\u0275\u0275elementEnd()()}}let b=(()=>{class z{constructor(x,D,G,ee,E,K){this.modalConfirmation=x,this.mboProvider=D,this.productsService=G,this.digitalProvider=ee,this.digitalService=E,this.managerInformation=K,this.actions=[],this.iconTitle=""}ngOnInit(){this.product.isDigital&&(this.iconTitle="digital-tc",this.actions=[{prefixIcon:"eye-show-visible",label:"Ver datos",click:x=>{this.onDigital(this.product),x.stopPropagation()}}])}onProduct(){this.productsService.emitProduct(this.product)}onCovereds(){var x=this;return(0,f.Z)(function*(){x.mboProvider.loader.open("Solicitando datos, por favor espere..."),(yield x.managerInformation.requestCoveredCards(x.product)).when({success:D=>{D.length>0?x.mboProvider.navigation.next(d.Z6.CUSTOMER.PRODUCTS.COVERED_CARDS,{productId:x.product.id}):x.modalConfirmation.execute({title:"SIN TARJETAS AMPARADAS",message:"Lo sentimos, este producto no cuentas con tarjetas amparadas.",accept:{label:"Aceptar"}})},failure:()=>{x.mboProvider.toast.error("En este momento no fue posible realizar la consulta de tus tarjetas amparadas, int\xe9ntalo m\xe1s tarde.","Consulta fallida")}},()=>{x.mboProvider.loader.close()})})()}onDigital(x){var D=this;return(0,f.Z)(function*(){const{requiredRequest:G}=D.digitalService.request(x.id);if(!G)return D.productsService.emitProduct(x);D.mboProvider.loader.open("Solicitando datos, por favor espere..."),(yield D.managerInformation.requestInformation(x)).when({success:()=>{D.digitalProvider.execute(x)},failure:({message:ee})=>{D.mboProvider.toast.error(ee)}},()=>{D.mboProvider.loader.close()})})()}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(h.$e),n.\u0275\u0275directiveInject(p.ZL),n.\u0275\u0275directiveInject(o.su),n.\u0275\u0275directiveInject(_.t),n.\u0275\u0275directiveInject(g.ZP),n.\u0275\u0275directiveInject(y.vu))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-creditcard"]],inputs:{product:"product",incognito:"incognito"},decls:2,vars:5,consts:[[3,"incognito","iconTitle","product","actions","click"],["bocc-button","flat","prefixIcon","credit-card-protected",3,"click",4,"ngIf"],["bocc-button","flat","prefixIcon","credit-card-protected",3,"click"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"mbo-card-product",0),n.\u0275\u0275listener("click",function(){return D.onProduct()}),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(1,M,3,0,"button",1)),2&x&&(n.\u0275\u0275property("incognito",D.incognito)("iconTitle",D.iconTitle)("product",D.product)("actions",D.actions),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",D.product.bank.isOccidente&&!D.product.isDigital))},dependencies:[e.NgIf,u.P,s.x],styles:["mbo-product-card-creditcard{position:relative;display:flex;flex-direction:column;align-items:baseline;row-gap:var(--sizing-x6)}mbo-product-card-creditcard .bocc-button__content{padding:0rem var(--sizing-x4)}\n"],encapsulation:2}),z})();function l(z,$){if(1&z&&n.\u0275\u0275element(0,"mbo-product-card-account",3),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("product",x.product)("incognito",x.incognito)}}function T(z,$){if(1&z&&n.\u0275\u0275element(0,"mbo-product-card-account",3),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("product",x.product)("incognito",x.incognito)}}function S(z,$){if(1&z&&n.\u0275\u0275element(0,"mbo-product-card-account",3),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("product",x.product)("incognito",x.incognito)}}function O(z,$){if(1&z&&n.\u0275\u0275element(0,"mbo-product-card-creditcard",3),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("product",x.product)("incognito",x.incognito)}}function N(z,$){if(1&z&&n.\u0275\u0275element(0,"mbo-product-card-standard",3),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275property("product",x.product)("incognito",x.incognito)}}let te=(()=>{class z{}return z.\u0275fac=function(x){return new(x||z)},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-products-carousel-element"]],inputs:{product:"product",incognito:"incognito"},decls:6,vars:5,consts:[[3,"ngSwitch"],[3,"product","incognito",4,"ngSwitchCase"],[3,"product","incognito",4,"ngSwitchDefault"],[3,"product","incognito"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275template(1,l,1,2,"mbo-product-card-account",1),n.\u0275\u0275template(2,T,1,2,"mbo-product-card-account",1),n.\u0275\u0275template(3,S,1,2,"mbo-product-card-account",1),n.\u0275\u0275template(4,O,1,2,"mbo-product-card-creditcard",1),n.\u0275\u0275template(5,N,1,2,"mbo-product-card-standard",2),n.\u0275\u0275elementEnd()),2&x&&(n.\u0275\u0275property("ngSwitch",D.product.type),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","SDA"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","DDA"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","AFC"),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngSwitchCase","CCA"))},dependencies:[e.NgSwitch,e.NgSwitchCase,e.NgSwitchDefault,m,v,b],styles:["mbo-products-carousel-element{position:relative;display:block}\n"],encapsulation:2}),z})();var Z=t(89148);let V=(()=>{class z{constructor(x,D,G,ee){this.modalConfirmation=x,this.mboProvider=D,this.requestProducts=G,this.productsService=ee}onComponent(){var x=this;return(0,f.Z)(function*(){x.mboProvider.loader.open("Solicitando productos AVAL, por favor espere..."),(yield x.requestProducts.avals()).when({success:({products:D})=>{x.productsService.emitProducts({type:Z.Gt.Aval,value:D})},failure:({message:D})=>{x.modalConfirmation.execute({title:"SIN PRODUCTOS",message:D,accept:{label:"Aceptar"}})}},()=>{x.mboProvider.loader.close()})})()}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(h.$e),n.\u0275\u0275directiveInject(p.ZL),n.\u0275\u0275directiveInject(y.cF),n.\u0275\u0275directiveInject(o.su))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-aval"]],decls:6,vars:0,consts:[[1,"mbo-product-card-aval__content",3,"click"],[1,"mbo-product-card-aval__header"],[1,"body2-medium"],["src","assets/shared/logos/aval.svg"],["src","assets/shared/logos/aval-banks.svg",1,"mbo-product-card-aval__logo"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("click",function(){return D.onComponent()}),n.\u0275\u0275elementStart(1,"div",1)(2,"label",2),n.\u0275\u0275text(3,"Productos AVAL"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"img",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(5,"img",4),n.\u0275\u0275elementEnd())},styles:['mbo-product-card-aval{--pvt-maxwidth-logo: 100rem;position:relative;display:block;width:100%;overflow:hidden;border-radius:var(--sizing-x8);background:var(--color-carbon-lighter-50)}mbo-product-card-aval:before{position:relative;display:block;content:"";width:100%;padding-top:var(--mbo-product-carousel-height)}mbo-product-card-aval .mbo-product-card-aval__content{position:absolute;top:0rem;left:0rem;width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-between;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x8);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-product-card-aval .mbo-product-card-aval__header{display:flex;justify-content:space-between}mbo-product-card-aval .mbo-product-card-aval__header label{color:var(--color-carbon-darker-1000);margin:auto 0rem}mbo-product-card-aval .mbo-product-card-aval__header img{width:var(--sizing-x28)}mbo-product-card-aval .mbo-product-card-aval__logo{max-width:var(--pvt-maxwidth-logo)}\n'],encapsulation:2}),z})();var Y=t(21260),U=t(44926);let J=(()=>{class z{constructor(x,D,G,ee){this.mboProvider=x,this.requestTrustfundModal=D,this.requestProducts=G,this.productsService=ee}onComponent(){var x=this;return(0,f.Z)(function*(){x.mboProvider.loader.open("Solicitando fiducias, por favor espere..."),(yield x.requestProducts.trustfunds()).when({success:D=>{x.productsService.emitProducts({type:Z.Gt.Trustfund,value:D})},failure:()=>{x.requestTrustfundModal.execute()}},()=>{x.mboProvider.loader.close()})})()}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(p.ZL),n.\u0275\u0275directiveInject(Y.L),n.\u0275\u0275directiveInject(y.cF),n.\u0275\u0275directiveInject(o.su))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-trustfund"]],decls:6,vars:0,consts:[["color","none"],[1,"mbo-product-card-trustfund__content",3,"click"],[1,"body2-medium"],["src","assets/shared/logos/fiduoccidente.svg"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1),n.\u0275\u0275listener("click",function(){return D.onComponent()}),n.\u0275\u0275elementStart(2,"label",2),n.\u0275\u0275text(3,"Fiduciaria"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"picture"),n.\u0275\u0275element(5,"img",3),n.\u0275\u0275elementEnd()()())},dependencies:[U.X],styles:['mbo-product-card-trustfund{position:relative;display:block;width:100%;overflow:hidden;border-radius:var(--sizing-x8)}mbo-product-card-trustfund:before{position:relative;display:block;content:"";width:100%;padding-top:var(--mbo-product-carousel-height)}mbo-product-card-trustfund bocc-card-product-background{position:absolute;top:0rem;left:0rem;width:100%;height:100%;border-radius:var(--sizing-x8);overflow:hidden;box-sizing:border-box}mbo-product-card-trustfund bocc-card-product-background .bocc-card-product-background{display:flex;flex-direction:column;justify-content:space-between;height:100%;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x8)}mbo-product-card-trustfund .mbo-product-card-trustfund__content{display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between}mbo-product-card-trustfund .mbo-product-card-trustfund__content label{color:var(--color-blue-700)}mbo-product-card-trustfund .mbo-product-card-trustfund__content picture{display:flex;justify-content:flex-end}mbo-product-card-trustfund .mbo-product-card-trustfund__content picture img{max-width:60rem}\n'],encapsulation:2}),z})(),A=(()=>{class z{constructor(x){this.mboProvider=x}onComponent(){var x=this;return(0,f.Z)(function*(){x.mboProvider.navigation.next(d.Z6.CUSTOMER.TUPLUS.HOME)})()}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(p.ZL))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-product-card-tuplus"]],decls:11,vars:0,consts:[[1,"mbo-product-card-tuplus__content",3,"click"],[1,"mbo-product-card-tuplus__header"],[1,"body2-medium"],["src","assets/shared/logos/tuplus.svg"],[1,"mbo-product-card-tuplus__footer"],[1,"caption-medium"],["bocc-button","flat","suffixIcon","next-page"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("click",function(){return D.onComponent()}),n.\u0275\u0275elementStart(1,"div",1)(2,"label",2),n.\u0275\u0275text(3,"Puntos tupl\xfas"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(4,"img",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"div",4)(6,"label",5),n.\u0275\u0275text(7," Ahora tus puntos est\xe1n en tupl\xfas "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"button",6)(9,"span"),n.\u0275\u0275text(10,"Ver saldo disponible"),n.\u0275\u0275elementEnd()()()())},dependencies:[u.P],styles:['mbo-product-card-tuplus{position:relative;display:block;width:100%;overflow:hidden;border-radius:var(--sizing-x8);background:var(--color-carbon-lighter-50)}mbo-product-card-tuplus:before{position:relative;display:block;content:"";width:100%;padding-top:var(--mbo-product-carousel-height)}mbo-product-card-tuplus .mbo-product-card-tuplus__content{position:absolute;top:0rem;left:0rem;width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-between;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x8);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-product-card-tuplus .mbo-product-card-tuplus__header{display:flex;justify-content:space-between}mbo-product-card-tuplus .mbo-product-card-tuplus__header label{color:var(--color-product-tuplus-700);margin:auto 0rem}mbo-product-card-tuplus .mbo-product-card-tuplus__header img{width:var(--sizing-x28)}mbo-product-card-tuplus .mbo-product-card-tuplus__footer{display:flex;flex-direction:column;align-items:flex-end}mbo-product-card-tuplus .mbo-product-card-tuplus__footer>label{color:var(--color-carbon-lighter-700)}mbo-product-card-tuplus .mbo-product-card-tuplus__footer .bocc-button{--bocc-button-padding: 0rem var(--sizing-x2);height:var(--sizing-x16)}\n'],encapsulation:2}),z})();var w=t(224);function F(z,$){if(1&z&&(n.\u0275\u0275elementStart(0,"div",4),n.\u0275\u0275element(1,"mbo-products-carousel-element",5),n.\u0275\u0275elementEnd()),2&z){const x=$.$implicit,D=n.\u0275\u0275nextContext();n.\u0275\u0275styleProp("width",D.widthElement),n.\u0275\u0275advance(1),n.\u0275\u0275property("product",x)("incognito",D.incognito)}}function B(z,$){if(1&z&&(n.\u0275\u0275elementStart(0,"div",4),n.\u0275\u0275element(1,"mbo-product-card-trustfund"),n.\u0275\u0275elementEnd()),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275styleProp("width",x.widthElement)}}function P(z,$){if(1&z&&(n.\u0275\u0275elementStart(0,"div",4),n.\u0275\u0275element(1,"mbo-product-card-aval"),n.\u0275\u0275elementEnd()),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275styleProp("width",x.widthElement)}}function W(z,$){if(1&z&&(n.\u0275\u0275elementStart(0,"div",4),n.\u0275\u0275element(1,"mbo-product-card-tuplus"),n.\u0275\u0275elementEnd()),2&z){const x=n.\u0275\u0275nextContext();n.\u0275\u0275styleProp("width",x.widthElement)}}let oe=(()=>{class z{constructor(x){this.ref=x,this.products=[],this.productId="",this.skeleton=!1,this.incognito=!1,this.additionals=!0,this.enabledTuPlus=!0,this.width=0,this.translateX=0,this.baseFont=(0,r.B)(),this.totalElements=0,this.currentPosition=0,this.widthContent="0px",this.transformContent="translateX(0px)",this.widthElement="100%",this.active=!1,this.touched=!1,this.baseSeparator=this.baseFont/2,this.minSlideValue=this.baseFont,this.maxSlideValue=-1*this.baseFont}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-products-carousel__content"),this.mutation=new MutationObserver(()=>{this.totalElements=Array.from(this.contentElement.querySelectorAll(".mbo-products-carousel__element").values()).length,this.calculateProperties(this.width)}),this.mutation.observe(this.contentElement,{childList:!0})}ngOnDestroy(){this.mutation?.disconnect()}ngOnChanges(x){const{productId:D}=x;if(D&&D.currentValue){const G=this.products.findIndex(({id:ee})=>D.currentValue===ee);-1!==G&&setTimeout(()=>{this.currentPosition=G,this.renderForPosition(G)},120)}}ngAfterViewInit(){setTimeout(()=>{this.width=this.ref.nativeElement.offsetWidth,this.totalElements=Array.from(this.contentElement.querySelectorAll(".mbo-products-carousel__element").values()).length,this.calculateProperties(this.width),this.active=!0,this.setTouchHandler(this.contentElement)},120)}onResize(){this.active&&(this.width=this.ref.nativeElement.offsetWidth,this.calculateProperties(this.width,!1))}calculateProperties(x,D=!0){const{baseSeparator:G,totalElements:ee}=this,K=ee*x+(ee-1)*G;this.maxSlideValue=-((ee-1)*x+ee*G)-this.baseFont,this.widthContent=`${K}px`,D?(this.currentPosition=0,this.translateX=0,this.setTranslateContent(0)):this.renderForPosition(this.currentPosition)}setTouchHandler(x){let D=0,G=0;x.addEventListener("touchstart",ee=>{if(ee.changedTouches.length){const{clientX:E}=ee.changedTouches.item(0);D=0,this.touched=!0,G=E}}),x.addEventListener("touchmove",ee=>{if(ee.changedTouches.length){const E=ee.changedTouches.item(0),K=E.clientX-G;G=E.clientX,this.translateX+=K,D+=K;const R=this.getTranslateSlide(this.translateX);this.setTranslateContent(R)}}),x.addEventListener("touchend",ee=>{this.touched=!1,ee.changedTouches.length&&(Math.abs(D)/this.width*100>=40&&(D>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}renderForPosition(x){let D=0;x>0&&(D=-(this.width*x+this.baseSeparator*x)),this.translateX=D,this.setTranslateContent(D)}getTranslateSlide(x){return x>this.minSlideValue?this.minSlideValue:x<this.maxSlideValue?this.maxSlideValue:x}getPositionSlide(x){return x>=this.totalElements?this.totalElements-1:x<0?0:x}setTranslateContent(x){this.transformContent=`translateX(${x}px)`}}return z.\u0275fac=function(x){return new(x||z)(n.\u0275\u0275directiveInject(n.ElementRef))},z.\u0275cmp=n.\u0275\u0275defineComponent({type:z,selectors:[["mbo-products-carousel"]],inputs:{products:"products",productId:"productId",skeleton:"skeleton",incognito:"incognito",additionals:"additionals",enabledTuPlus:"enabledTuPlus"},features:[n.\u0275\u0275NgOnChangesFeature],decls:7,vars:14,consts:[[1,"mbo-products-carousel__content",3,"hidden","resize"],["class","mbo-products-carousel__element",3,"width",4,"ngFor","ngForOf"],["class","mbo-products-carousel__element",3,"width",4,"ngIf"],[1,"mbo-products-carousel__skeleton",3,"hidden"],[1,"mbo-products-carousel__element"],[3,"product","incognito"]],template:function(x,D){1&x&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275listener("resize",function(){return D.onResize()},!1,n.\u0275\u0275resolveWindow),n.\u0275\u0275template(1,F,2,4,"div",1),n.\u0275\u0275template(2,B,2,2,"div",2),n.\u0275\u0275template(3,P,2,2,"div",2),n.\u0275\u0275template(4,W,2,2,"div",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"div",3),n.\u0275\u0275element(6,"mbo-products-skeleton"),n.\u0275\u0275elementEnd()),2&x&&(n.\u0275\u0275styleProp("width",D.widthContent)("transform",D.transformContent),n.\u0275\u0275classProp("mbo-products-carousel__content--touched",D.touched)("mbo-products-carousel__content--active",D.active),n.\u0275\u0275property("hidden",D.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngForOf",D.products),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",D.additionals),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",D.additionals),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",D.enabledTuPlus&&D.additionals),n.\u0275\u0275advance(1),n.\u0275\u0275property("hidden",!D.skeleton))},dependencies:[e.NgForOf,e.NgIf,te,V,J,A,w.g],styles:['/*!\n * MBO ProductsCarousel Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 23/Jun/2022\n * Updated: 02/Abr/2025\n*/mbo-products-carousel{position:relative;width:100%;display:block}mbo-products-carousel .mbo-products-carousel__content{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);visibility:hidden;transition:transform .16s var(--standard-curve)}mbo-products-carousel .mbo-products-carousel__content--active{visibility:visible}mbo-products-carousel .mbo-products-carousel__content--touched{transition:none}mbo-products-carousel .mbo-products-carousel__skeleton{position:relative;width:100%;margin-top:var(--sizing-x4)}mbo-products-carousel .mbo-products-carousel__element{overflow-x:hidden}mbo-products-carousel .mbo-products-carousel__element--buttons{position:relative;background:var(--color-carbon-lighter-300);border-radius:var(--sizing-x8)}mbo-products-carousel .mbo-products-carousel__element--buttons:before{position:relative;content:"";width:100%;padding-top:56.25%}mbo-products-carousel .mbo-products-carousel__element__buttons{position:absolute;top:0rem;left:0rem;width:100%;height:100%;display:flex;justify-content:space-evenly}mbo-products-carousel .mbo-products-carousel__element__buttons .bocc-button-product{margin:auto 0rem}mbo-products-carousel .mbo-products-carousel__element__actions{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;width:100%}mbo-products-carousel .mbo-products-carousel__element__actions .bocc-button{margin-top:var(--sizing-x4);height:var(--sizing-x16)}\n'],encapsulation:2}),z})()},224:(j,I,t)=>{t.d(I,{g:()=>i});var n=t(99877),e=t(65467);let i=(()=>{class o{}return o.\u0275fac=function(m){return new(m||o)},o.\u0275cmp=n.\u0275\u0275defineComponent({type:o,selectors:[["mbo-products-skeleton"]],decls:13,vars:5,consts:[[1,"mbo-products-skeleton__content"],[1,"mbo-products-skeleton__card"],[1,"mbo-products-skeleton__card__content"],[1,"mbo-products-skeleton__card__header"],[3,"active"],[1,"mbo-products-skeleton__card__icon"],[1,"mbo-products-skeleton__card__footer"],[1,"mbo-products-skeleton__card__status"],[1,"first",3,"active"],[1,"last",3,"active"],[1,"mbo-products-skeleton__card__detail"]],template:function(m,y){1&m&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),n.\u0275\u0275element(4,"bocc-skeleton-text",4)(5,"div",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div",6)(7,"div",7),n.\u0275\u0275element(8,"bocc-skeleton-text",8)(9,"bocc-skeleton-text",9),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(10,"div",10),n.\u0275\u0275element(11,"bocc-skeleton-text",8)(12,"bocc-skeleton-text",9),n.\u0275\u0275elementEnd()()()()()),2&m&&(n.\u0275\u0275advance(4),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(4),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(2),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0))},dependencies:[e.D],styles:['mbo-products-skeleton{--pvt-product-padding: var(--sizing-x6);--pvt-product-height: 56.25%;--pvt-button-height: var(--sizing-x20);position:relative;display:block}mbo-products-skeleton .mbo-products-skeleton__content{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}mbo-products-skeleton .mbo-products-skeleton__card{position:relative;overflow:hidden;border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);box-sizing:border-box;background:var(--color-carbon-lighter-50)}mbo-products-skeleton .mbo-products-skeleton__card:before{position:relative;display:block;content:"";width:100%;padding-top:var(--pvt-product-height)}mbo-products-skeleton .mbo-products-skeleton__card__content{position:absolute;display:flex;top:0rem;left:0rem;width:100%;height:100%;flex-direction:column;justify-content:space-between;padding:var(--pvt-product-padding);box-sizing:border-box}mbo-products-skeleton .mbo-products-skeleton__card__header{display:flex;justify-content:space-between}mbo-products-skeleton .mbo-products-skeleton__card__header bocc-skeleton-text{width:85%;min-height:var(--sizing-x12)}mbo-products-skeleton .mbo-products-skeleton__card__icon{width:var(--sizing-x12);height:var(--sizing-x12);background:var(--color-carbon-lighter-300);border-radius:var(--sizing-x2)}mbo-products-skeleton .mbo-products-skeleton__card__footer{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-products-skeleton .mbo-products-skeleton__card__status{display:flex;justify-content:space-between}mbo-products-skeleton .mbo-products-skeleton__card__status bocc-skeleton-text{min-height:var(--sizing-x8)}mbo-products-skeleton .mbo-products-skeleton__card__status bocc-skeleton-text.first{width:35%}mbo-products-skeleton .mbo-products-skeleton__card__status bocc-skeleton-text.last{width:35%}mbo-products-skeleton .mbo-products-skeleton__card__detail{display:flex;justify-content:space-between}mbo-products-skeleton .mbo-products-skeleton__card__detail bocc-skeleton-text{min-height:var(--sizing-x12)}mbo-products-skeleton .mbo-products-skeleton__card__detail bocc-skeleton-text.first{width:50%}mbo-products-skeleton .mbo-products-skeleton__card__detail bocc-skeleton-text.last{width:35%}@media screen and (max-width: 320px){mbo-products-skeleton{--pvt-button-height: var(--sizing-x16);--pvt-product-height: 62.5%}}\n'],encapsulation:2}),o})()},48117:(j,I,t)=>{t.d(I,{n:()=>m});var n=t(17007),e=t(30263),i=t(39904),o=t(99877);let m=(()=>{class y{constructor(){this.bogotaLineNumber=i._L.BOGOTA_LINE,this.bogotaLinePhone=i.WB.BOGOTA_LINE}ngBoccPortal(p){this.portal=p}onAgree(){this.portal.close()}}return y.\u0275fac=function(p){return new(p||y)},y.\u0275cmp=o.\u0275\u0275defineComponent({type:y,selectors:[["mbo-remittances-help-modal"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:21,vars:2,consts:[[1,"mbo-remittances-help-modal__content"],[1,"mbo-remittances-help-modal__body"],["src","assets/shared/logos/modals/support.svg","alt","BOCC Support"],[1,"mbo-remittances-help-modal__title","smalltext-medium"],[1,"mbo-remittances-help-modal__message","body2-medium"],[1,"mbo-remittances-help-modal__contact"],[1,"mbo-remittances-help-modal__contact__wrapper"],["id","lnk_attention-lines_local",1,"mbo-remittances-help-modal__contact__wrapper__element",3,"href"],[1,"mbo-remittances-help-modal__contact__wrapper__element__icon"],["icon","phone-call"],[1,"mbo-remittances-help-modal__contact__wrapper__element__content"],[1,"mbo-remittances-help-modal__contact__wrapper__element__title","body2-medium"],[1,"mbo-remittances-help-modal__contact__wrapper__element__subtitle","body2-medium"],[1,"mbo-remittances-help-modal__footer"],["bocc-button","raised",3,"click"]],template:function(p,u){1&p&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"img",2),o.\u0275\u0275elementStart(3,"label",3),o.\u0275\u0275text(4," \xbfREQUIERES ACOMPA\xd1AMIENTO? "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"p",4),o.\u0275\u0275text(6," Comun\xedcate con nuestra l\xednea de atenci\xf3n. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(7,"div",5)(8,"div",6)(9,"a",7)(10,"div",8),o.\u0275\u0275element(11,"bocc-icon",9),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(12,"div",10)(13,"label",11),o.\u0275\u0275text(14," Bogot\xe1 "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(15,"label",12),o.\u0275\u0275text(16),o.\u0275\u0275elementEnd()()()()(),o.\u0275\u0275elementStart(17,"div",13)(18,"button",14),o.\u0275\u0275listener("click",function(){return u.onAgree()}),o.\u0275\u0275elementStart(19,"span"),o.\u0275\u0275text(20,"Entendido"),o.\u0275\u0275elementEnd()()()()),2&p&&(o.\u0275\u0275advance(9),o.\u0275\u0275property("href",u.bogotaLinePhone,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(7),o.\u0275\u0275textInterpolate1(" ",u.bogotaLineNumber," "))},dependencies:[n.CommonModule,e.P8,e.Zl],styles:["mbo-remittances-help-modal{position:relative;display:block;box-sizing:border-box}mbo-remittances-help-modal .mbo-remittances-help-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-remittances-help-modal .mbo-remittances-help-modal__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-remittances-help-modal .mbo-remittances-help-modal__body img{height:var(--sizing-x32)}mbo-remittances-help-modal .mbo-remittances-help-modal__contact{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__title{color:var(--color-carbon-lighter-700)}mbo-remittances-help-modal .mbo-remittances-help-modal__contact__wrapper__element__subtitle{color:var(--color-bocc-700)}mbo-remittances-help-modal .mbo-remittances-help-modal__title{position:relative;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-remittances-help-modal .mbo-remittances-help-modal__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-remittances-help-modal .mbo-remittances-help-modal__footer{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-remittances-help-modal .mbo-remittances-help-modal__footer>button{width:100%}\n"],encapsulation:2}),y})()},87777:(j,I,t)=>{t.d(I,{a:()=>d});var n=t(15861),r=t(17007),i=t(30263),o=t(99877);function m(p,u){1&p&&o.\u0275\u0275element(0,"img",18)}function y(p,u){1&p&&o.\u0275\u0275element(0,"img",19)}let d=(()=>{class p{ngOnInit(){}ngBoccPortal(c){this.portal=c,this.portal.receive(a=>{this.product=a?.product,this.tagAval=a?.tagAval})}onSubmit(){var c=this;return(0,n.Z)(function*(){c.portal?.send(!0),c.portal?.close()})()}onCancel(){this.portal?.close()}}return p.\u0275fac=function(c){return new(c||p)},p.\u0275cmp=o.\u0275\u0275defineComponent({type:p,selectors:[["mbo-tag-aval-verify-modal"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:28,vars:6,consts:[[1,"mbo-tag-aval-verify-modal__body"],[1,"mbo-tag-aval-verify-modal__body__logo"],["src","assets/transfers/logos/tag-aval-verify.svg",4,"ngIf"],["src","assets/transfers/logos/tag-key-verify.svg",4,"ngIf"],[1,"mbo-tag-aval-verify-modal__title","smalltext-semibold"],[1,"mbo-tag-aval-verify-modal__description--grey","smalltext-regular"],[1,"mbo-tag-aval-verify-modal__box-receptor"],[1,"mbo-tag-aval-verify-modal__box-receptor__img"],[3,"src"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--name","caption-regular"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--bank-name","caption-regular"],[1,"mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container","smalltext-bold"],[1,"body1-semibold"],[1,"mbo-tag-aval-verify-modal__description--blue","smalltext-regular"],[1,"mbo-tag-aval-verify-modal__footer"],["id","btn_tag-aval-verify-modal_submit","bocc-button","raised","suffixIcon","success",3,"click"],["id","btn_tag-aval-verify-modal_cancel","bocc-button","outline",3,"click"],["src","assets/transfers/logos/tag-aval-verify.svg"],["src","assets/transfers/logos/tag-key-verify.svg"]],template:function(c,a){1&c&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275template(2,m,1,0,"img",2),o.\u0275\u0275template(3,y,1,0,"img",3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",4),o.\u0275\u0275text(5," CONFIRMA TU NUEVO TAG AVAL "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(6,"label",5),o.\u0275\u0275text(7," Si cambias tu Tag Aval, el anterior podr\xe1 ser utilizado por otra persona y deber\xe1s compartir el nuevo Tag con tus contactos para recibir dinero. "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(8,"div",6)(9,"div",7),o.\u0275\u0275element(10,"img",8),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(11,"div",9)(12,"label",10),o.\u0275\u0275text(13),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(14,"label",11),o.\u0275\u0275text(15),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(16,"div",12)(17,"span",13),o.\u0275\u0275text(18),o.\u0275\u0275elementEnd()()()(),o.\u0275\u0275elementStart(19,"label",14),o.\u0275\u0275text(20," Al confirmar, aceptas cumplir con las recomendaciones proporcionadas para la modificaci\xf3n de tu Tag. "),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(21,"div",15)(22,"button",16),o.\u0275\u0275listener("click",function(){return a.onSubmit()}),o.\u0275\u0275elementStart(23,"span"),o.\u0275\u0275text(24,"Confirmar"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(25,"button",17),o.\u0275\u0275listener("click",function(){return a.onCancel()}),o.\u0275\u0275elementStart(26,"span"),o.\u0275\u0275text(27,"Cancelar"),o.\u0275\u0275elementEnd()()()),2&c&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",a.product.bank.isAval),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",!a.product.bank.isAval),o.\u0275\u0275advance(7),o.\u0275\u0275property("src",null==a.product?null:a.product.bank.logo,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",null==a.product?null:a.product.name," "),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",null==a.product?null:a.product.shortNumber," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate(a.tagAval))},dependencies:[r.CommonModule,r.NgIf,i.P8],styles:["mbo-tag-aval-verify-modal{position:relative;display:flex;flex-direction:column;min-width:140rem;box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x12);box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__body__logo{text-align:center}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__title{position:relative;width:100%;text-align:center;margin:var(--sizing-x4) 0rem;color:var(--color-carbon-darker-1000)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__message{position:relative;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);margin:var(--sizing-x6)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__img{width:18rem;border-radius:var(--sizing-x4);padding:var(--sizing-x1);box-sizing:border-box;border:var(--border-1-lighter-300)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__img img{width:100%}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor{display:flex;flex-direction:column;width:calc(100% - 22rem);row-gap:var(--sizing-x2)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--bank-name{color:var(--color-amathyst-400);width:100%}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden;white-space:nowrap}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key-container>span{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__box-receptor__info-receptor--key{color:var(--color-carbon-lighter-700)}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__description--blue{color:var(--color-blue-700);text-align:center}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__description--grey{color:var(--color-carbon-lighter-700);text-align:center}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x8);box-sizing:border-box}mbo-tag-aval-verify-modal .mbo-tag-aval-verify-modal__footer button{width:100%}\n"],encapsulation:2}),p})()},31981:(j,I,t)=>{t.d(I,{W:()=>f});var n=t(17007),e=t(71776),o=t(30263),s=t(27236),m=t(87903),y=t(87956),d=t(57544),u=t(42168),a=t(99877),v=(()=>{return(h=v||(v={}))[h.TyC=0]="TyC",h[h.Next=1]="Next",v;var h})();let f=(()=>{class h{constructor(_,M,b,l){this.http=_,this.deviceService=M,this.fileManagerService=b,this.storageService=l,this.position=v.TyC,this.nameSupplierStore="",this.imgSupplierStore="",this.tycControl=new d.FormControl(!1)}ngOnInit(){this.deviceService.controller.itIsIos?(this.nameSupplierStore="Apple Pay",this.imgSupplierStore="assets/shared/logos/apple-pay-mark.svg"):(this.nameSupplierStore="Google Pay",this.imgSupplierStore="assets/shared/logos/google-pay-dark.svg")}ngBoccPortal(_){this.portal=_}onDownloadTyC(){(0,u.firstValueFrom)(this.http.get("/assets/shared/files/tyc-digital-wallet.pdf",{responseType:"blob"})).then(_=>{const M=new FileReader;M.readAsDataURL(_),M.onloadend=()=>{this.fileManagerService.downloadPdf({base64:M.result,name:`tokenization-tyc-${(0,m.CW)()}.pdf`})}})}onNext(){this.storageService.set(s.Z.DigitalWalletTyC,!0).then(()=>{this.position=v.Next})}onSubmit(){this.portal?.resolve(!0),this.portal?.close()}}return h.\u0275fac=function(_){return new(_||h)(a.\u0275\u0275directiveInject(e.HttpClient),a.\u0275\u0275directiveInject(y.U8),a.\u0275\u0275directiveInject(y.j5),a.\u0275\u0275directiveInject(y.V1))},h.\u0275cmp=a.\u0275\u0275defineComponent({type:h,selectors:[["mbo-tokenization-bottom-sheet"]],standalone:!0,features:[a.\u0275\u0275StandaloneFeature],decls:38,vars:8,consts:[[1,"mbo-tokenization-bottom-sheet__content"],[3,"src"],[3,"position"],[1,"bocc-tab-form__view"],[1,"mbo-tokenization-bottom-sheet__component"],[1,"smalltext-semibold"],[1,"mbo-tokenization-bottom-sheet__info"],[1,"mbo-tokenization-bottom-sheet__element"],["icon","wi-fi"],[1,"mbo-tokenization-bottom-sheet__description"],[1,"body2-medium"],["icon","lock"],[3,"formControl"],[3,"click"],["id","btn_tokenization_next","bocc-button","raised",3,"disabled","click"],["id","btn_tokenization_store","bocc-button","raised","suffixIcon","arrow-right",3,"click"]],template:function(_,M){1&_&&(a.\u0275\u0275elementStart(0,"div",0),a.\u0275\u0275element(1,"img",1),a.\u0275\u0275elementStart(2,"bocc-tab-form",2)(3,"div",3)(4,"div",4)(5,"label",5),a.\u0275\u0275text(6,"PAGA CON TU BILLETERA DIGITAL"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(7,"div",6)(8,"div",7),a.\u0275\u0275element(9,"bocc-icon",8),a.\u0275\u0275elementStart(10,"div",9)(11,"label",10),a.\u0275\u0275text(12,"Paga sin contacto"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(13,"p",10),a.\u0275\u0275text(14," Acerca tu dispositivo al dat\xe1fono. \xa1Paga f\xe1cil y r\xe1pido!. "),a.\u0275\u0275elementEnd()()(),a.\u0275\u0275elementStart(15,"div",7),a.\u0275\u0275element(16,"bocc-icon",11),a.\u0275\u0275elementStart(17,"div",9)(18,"label",10),a.\u0275\u0275text(19,"Seguridad y privacidad"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(20,"p",10),a.\u0275\u0275text(21," Confirma tus pagos con los m\xe9todos de seguridad de tu dispositivo. "),a.\u0275\u0275elementEnd()()()(),a.\u0275\u0275elementStart(22,"bocc-checkbox-label",12),a.\u0275\u0275text(23," Acepto los "),a.\u0275\u0275elementStart(24,"a",13),a.\u0275\u0275listener("click",function(){return M.onDownloadTyC()}),a.\u0275\u0275text(25,"terminos y condiciones"),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(26,"button",14),a.\u0275\u0275listener("click",function(){return M.onNext()}),a.\u0275\u0275elementStart(27,"span"),a.\u0275\u0275text(28,"Continuar"),a.\u0275\u0275elementEnd()()()(),a.\u0275\u0275elementStart(29,"div",3)(30,"div",4)(31,"label",5),a.\u0275\u0275text(32,"CONTINUAR EN LA BILLETERA"),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(33,"p",10),a.\u0275\u0275text(34),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(35,"button",15),a.\u0275\u0275listener("click",function(){return M.onSubmit()}),a.\u0275\u0275elementStart(36,"span"),a.\u0275\u0275text(37),a.\u0275\u0275elementEnd()()()()()()),2&_&&(a.\u0275\u0275advance(1),a.\u0275\u0275property("src",M.imgSupplierStore,a.\u0275\u0275sanitizeUrl),a.\u0275\u0275advance(1),a.\u0275\u0275property("position",M.position),a.\u0275\u0275advance(7),a.\u0275\u0275styleProp("transform","rotate(90deg)"),a.\u0275\u0275advance(13),a.\u0275\u0275property("formControl",M.tycControl),a.\u0275\u0275advance(4),a.\u0275\u0275property("disabled",!M.tycControl.value),a.\u0275\u0275advance(8),a.\u0275\u0275textInterpolate1(" Para terminar la configuraci\xf3n de tu billetera digital te enviaremos a ",M.nameSupplierStore,". "),a.\u0275\u0275advance(3),a.\u0275\u0275textInterpolate1("Ir a ",M.nameSupplierStore,""))},dependencies:[n.CommonModule,o.P8,o.aR,o.Zl,o.qw],styles:["mbo-tokenization-bottom-sheet{position:relative;width:100%;display:block}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__content{display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x4);margin-top:var(--sizing-x8)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__content>img{width:35rem}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component{display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component>p{color:var(--color-carbon-lighter-700);text-align:center}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component>button{width:100%}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__info{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__element{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__element bocc-icon{color:var(--color-ocher-700)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__description{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__description>p{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),h})()},16338:(j,I,t)=>{t.r(I),t.d(I,{MboCustomerProductsModule:()=>p});var n=t(17007),r=t(78007),e=t(15861),i=t(30263),o=t(74561),s=t(96845),m=t(99877);const d=[{path:"",loadChildren:()=>t.e(5241).then(t.bind(t,5241)).then(u=>u.MboCustomerProductsPageModule)},{path:"info",loadChildren:()=>t.e(312).then(t.bind(t,50312)).then(u=>u.MboProductInfoPageModule)},{path:"tag-aval-edit",loadComponent:()=>t.e(6956).then(t.bind(t,46956)).then(u=>u.MboTagAvalEditPage)},{path:"movements",loadChildren:()=>t.e(4940).then(t.bind(t,64940)).then(u=>u.MboProductMovementsPageModule)},{path:"movements/information",loadChildren:()=>t.e(2455).then(t.bind(t,22455)).then(u=>u.MboProductMovementInformationPageModule)},{path:"covered-cards",loadChildren:()=>t.e(1264).then(t.bind(t,41264)).then(u=>u.MboCustomerCoveredCardsPageModule)},{path:"remittances",loadComponent:()=>t.e(2157).then(t.bind(t,42157)).then(u=>u.MboRemittancesPage),canActivate:[(()=>{class u{constructor(a,C){this.modalService=a,this.managerRemittances=C}canActivate(){var a=this;return(0,e.Z)(function*(){return(yield a.managerRemittances.allowAccess()).when({success:C=>(C||a.modalService.create(o.Gd).open(120),Promise.resolve(C)),failure:()=>Promise.resolve(!1)})})()}}return u.\u0275fac=function(a){return new(a||u)(m.\u0275\u0275inject(i.iM),m.\u0275\u0275inject(s.g))},u.\u0275prov=m.\u0275\u0275defineInjectable({token:u,factory:u.\u0275fac,providedIn:"root"}),u})()]}];let p=(()=>{class u{}return u.\u0275fac=function(a){return new(a||u)},u.\u0275mod=m.\u0275\u0275defineNgModule({type:u}),u.\u0275inj=m.\u0275\u0275defineInjector({imports:[n.CommonModule,r.RouterModule.forChild(d)]}),u})()},10533:(j,I,t)=>{t.d(I,{J:()=>h});var n=t(15861),r=t(71776),i=t(39904),o=t(77279),s=t(27236),m=t(87956),y=t(98699),p=t(98017),c=t(42168),C=t(70658),v=t(99877);let h=(()=>{class g{constructor(M,b,l){this.http=M,this.storageService=b,this.status$=(0,y.securePromise)(()=>this.requestStatus()),l.subscribe(o.q.Logout,()=>{this.status$.reset()})}verifyStatus(){return this.status$.resolve().then(M=>M&&Promise.all([this.storageService.get(s.Z.ConsentSpiStatusDecline),this.storageService.get(s.Z.ConsentSpiDateDecline)]).then(([b,l])=>"false"!==b&&!l||(0,p.dateIsAfter)((0,p.increaseTimestampInDate)(new Date(l),36e5*C.N.consentSpiExpiration),new Date)))}approve(){var M=this;return(0,n.Z)(function*(){yield Promise.all([M.storageService.set(s.Z.ConsentSpiStatusDecline,"false"),M.approveConsent()])})()}decline(){var M=this;return(0,n.Z)(function*(){yield Promise.all([M.storageService.set(s.Z.ConsentSpiStatusDecline,"true"),M.storageService.set(s.Z.ConsentSpiDateDecline,(new Date).toISOString())])})()}requestStatus(){return(0,c.firstValueFrom)(this.http.post(i.bV.CONSENT_SPI.REQUEST,{}).pipe((0,c.map)(M=>"1"!==M?.AgreeIndicator),(0,c.catchError)(()=>(0,c.of)(!1))))}approveConsent(){return(0,c.firstValueFrom)(this.http.post(i.bV.CONSENT_SPI.SAVE,{AgreeIndicator:"1"}).pipe((0,c.map)(()=>(this.status$.reset(),!0)),(0,c.catchError)(()=>(0,c.of)(!1))))}}return g.\u0275fac=function(M){return new(M||g)(v.\u0275\u0275inject(r.HttpClient),v.\u0275\u0275inject(m.V1),v.\u0275\u0275inject(m.Yd))},g.\u0275prov=v.\u0275\u0275defineInjectable({token:g,factory:g.\u0275fac,providedIn:"root"}),g})()},19102:(j,I,t)=>{t.d(I,{r:()=>o});var n=t(17007),e=t(99877);let o=(()=>{class s{constructor(){this.result=!1,this.src="assets/shared/logos/banco-occidente.svg"}ngOnInit(){this.result&&(this.src="assets/shared/logos/banco-occidente-result.svg")}}return s.\u0275fac=function(y){return new(y||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-bank-logo"]],inputs:{result:"result"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:2,vars:1,consts:[[1,"mbo-bank-logo__content"],["alt","Banco de Occidente Logo",1,"mbo-bank-logo__image",3,"src"]],template:function(y,d){1&y&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275element(1,"img",1),e.\u0275\u0275elementEnd()),2&y&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("src",d.src,e.\u0275\u0275sanitizeUrl))},dependencies:[n.CommonModule],styles:["mbo-bank-logo{position:relative;width:100%;display:block}mbo-bank-logo .mbo-bank-logo__content{position:relative;display:flex;justify-content:center;width:100%;height:var(--mbo-bank-logo-height);overflow:hidden;box-sizing:border-box;margin:0rem}mbo-bank-logo .mbo-bank-logo__image{height:100%}\n"],encapsulation:2}),s})()},52701:(j,I,t)=>{t.d(I,{q:()=>s});var n=t(17007),e=t(30263),i=t(99877);let s=(()=>{class m{}return m.\u0275fac=function(d){return new(d||m)},m.\u0275cmp=i.\u0275\u0275defineComponent({type:m,selectors:[["mbo-button-diamond-action"]],inputs:{classTheme:"classTheme",icon:"icon",label:"label"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:6,vars:4,consts:[[1,"mbo-button-diamond-action__component"],[1,"mbo-button-diamond-action__icon"],[1,"mbo-button-diamond-action__icon__content"],[3,"icon"],[1,"mbo-button-diamond-action__label","smalltext-medium"]],template:function(d,p){1&d&&(i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275element(3,"bocc-icon",3),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(4,"label",4),i.\u0275\u0275text(5),i.\u0275\u0275elementEnd()()),2&d&&(i.\u0275\u0275classMap(p.classTheme),i.\u0275\u0275advance(3),i.\u0275\u0275property("icon",p.icon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",p.label," "))},dependencies:[n.CommonModule,e.Zl],styles:["mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x24);--pvt-action-icon-height: var(--sizing-x24);--pvt-action-icon-size: var(--sizing-x12);--pvt-action-icon-color: var(--color-blue-700);position:relative}mbo-button-diamond-action[bocc-theme]{--pvt-action-icon-color: var(--color-bocc-700)}mbo-button-diamond-action .mbo-button-diamond-action__component{position:relative;display:flex;flex-direction:column;justify-content:flex-end;height:100%;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon{position:absolute;left:50%;width:var(--pvt-action-icon-width);height:var(--pvt-action-icon-height);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg)}mbo-button-diamond-action .mbo-button-diamond-action__icon__content{--bocc-icon-dimension: var(--pvt-action-icon-size);position:relative;display:flex;width:100%;height:100%;justify-content:center;align-items:center;box-sizing:border-box}mbo-button-diamond-action .mbo-button-diamond-action__icon__content>bocc-icon{color:var(--pvt-action-icon-color);transform:rotate(-45deg)}mbo-button-diamond-action .mbo-button-diamond-action__label{text-align:center}@media screen and (max-width: 320px){mbo-button-diamond-action{--pvt-action-icon-width: var(--sizing-x20);--pvt-action-icon-height: var(--sizing-x20);--pvt-action-icon-size: var(--sizing-x10)}}\n"],encapsulation:2}),m})()},55648:(j,I,t)=>{t.d(I,{u:()=>p});var n=t(15861),r=t(17007),i=t(30263),o=t(78506),s=t(99877);function y(u,c){if(1&u){const a=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"button",2),s.\u0275\u0275listener("click",function(){s.\u0275\u0275restoreView(a);const v=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView(v.onClick())}),s.\u0275\u0275elementStart(1,"span"),s.\u0275\u0275text(2),s.\u0275\u0275elementEnd()()}if(2&u){const a=s.\u0275\u0275nextContext();s.\u0275\u0275property("prefixIcon",a.icon)("disabled",a.disabled),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate1(" ",a.label," ")}}function d(u,c){if(1&u){const a=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"button",3),s.\u0275\u0275listener("click",function(){s.\u0275\u0275restoreView(a);const v=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView(v.onClick())}),s.\u0275\u0275elementEnd()}if(2&u){const a=s.\u0275\u0275nextContext();s.\u0275\u0275property("bocc-button-action",a.icon)("disabled",a.disabled)}}let p=(()=>{class u{constructor(a){this.preferences=a,this.actionMode=!1,this.disabled=!1,this.isIncognito=!1}ngOnInit(){this.unsubscription=this.preferences.subscribe(({isIncognito:a})=>{this.isIncognito=a||!1})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get icon(){return this.isIncognito?"eye-incognito-on":"eye-show-visible"}get label(){return this.isIncognito?"Mostrar saldos":"Ocultar saldos"}onClick(){var a=this;return(0,n.Z)(function*(){yield a.preferences.toggleIncognito()})()}}return u.\u0275fac=function(a){return new(a||u)(s.\u0275\u0275directiveInject(o.Bx))},u.\u0275cmp=s.\u0275\u0275defineComponent({type:u,selectors:[["mbo-button-incognito-mode"]],inputs:{actionMode:"actionMode",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[["bocc-button","flat",3,"prefixIcon","disabled","click",4,"ngIf"],[3,"bocc-button-action","disabled","click",4,"ngIf"],["bocc-button","flat",3,"prefixIcon","disabled","click"],[3,"bocc-button-action","disabled","click"]],template:function(a,C){1&a&&(s.\u0275\u0275template(0,y,3,3,"button",0),s.\u0275\u0275template(1,d,1,2,"button",1)),2&a&&(s.\u0275\u0275property("ngIf",!C.actionMode),s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",C.actionMode))},dependencies:[r.CommonModule,r.NgIf,i.P8,i.u1],styles:["mbo-button-incognito-mode{position:relative;display:block}mbo-button-incognito-mode .bocc-button{width:100%;height:inherit}mbo-button-incognito-mode .bocc-button:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action{width:var(--sizing-x12);height:var(--sizing-x12);color:inherit}mbo-button-incognito-mode .bocc-button-action:focus{background:transparent}mbo-button-incognito-mode .bocc-button-action:not(:disabled):hover{color:inherit}\n"],encapsulation:2}),u})()},72765:(j,I,t)=>{t.d(I,{rw:()=>n.r,qr:()=>r.q,uf:()=>e.u,Z:()=>y,t5:()=>v,$O:()=>C});var n=t(19102),r=t(52701),e=t(55648),i=t(17007),o=t(30263),s=t(99877);const m=["*"];let y=(()=>{class f{constructor(){this.disabled=!1}}return f.\u0275fac=function(g){return new(g||f)},f.\u0275cmp=s.\u0275\u0275defineComponent({type:f,selectors:[["mbo-poster"]],inputs:{disabled:"disabled",icon:"icon"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],ngContentSelectors:m,decls:5,vars:3,consts:[[1,"mbo-poster__content"],[1,"mbo-poster__icon"],[3,"icon"],[1,"mbo-poster__label"]],template:function(g,_){1&g&&(s.\u0275\u0275projectionDef(),s.\u0275\u0275elementStart(0,"div",0)(1,"div",1),s.\u0275\u0275element(2,"bocc-icon",2),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(3,"div",3),s.\u0275\u0275projection(4),s.\u0275\u0275elementEnd()()),2&g&&(s.\u0275\u0275classProp("mbo-poster__content--disabled",_.disabled),s.\u0275\u0275advance(2),s.\u0275\u0275property("icon",_.icon))},dependencies:[i.CommonModule,o.Zl],styles:[":root{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x4) var(--sizing-x2) var(--sizing-x4);--mbo-poster-font-size: var(--caption-size)}mbo-poster{position:relative;display:block}mbo-poster .mbo-poster__content{position:relative;display:flex;width:100%;flex-direction:column;padding:var(--mbo-poster-content-padding);box-sizing:border-box}mbo-poster .mbo-poster__content--disabled{opacity:.5;pointer-events:none}mbo-poster .mbo-poster__icon{margin:auto;color:var(--color-blue-700)}mbo-poster .mbo-poster__label{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center;color:var(--color-blue-700);font-size:var(--mbo-poster-font-size);font-weight:var(--font-weight-medium);letter-spacing:var(--caption-letter-spacing);min-height:var(--caption-line-height);line-height:var(--caption-line-height)}\n"],encapsulation:2}),f})();var d=t(33395),p=t(77279),u=t(87903),c=t(87956),a=t(25317);let C=(()=>{class f{constructor(g){this.eventBusService=g}onCopy(){this.value&&((0,u.Bn)(this.value),this.eventBusService.emit(p.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}}return f.\u0275fac=function(g){return new(g||f)(s.\u0275\u0275directiveInject(c.Yd))},f.\u0275cmp=s.\u0275\u0275defineComponent({type:f,selectors:[["mbo-tag-aval-copy"]],inputs:{elementId:"elementId",value:"value"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["icon","copy","boccUtagComponent","click",3,"id","click"]],template:function(g,_){1&g&&(s.\u0275\u0275elementStart(0,"bocc-icon",0),s.\u0275\u0275listener("click",function(){return _.onCopy()}),s.\u0275\u0275elementEnd()),2&g&&s.\u0275\u0275property("id",_.elementId)},dependencies:[i.CommonModule,o.Zl,d.kW,a.I],styles:["/*!\n * MBO TagAvalCopy Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 27/Nov/2024\n * Updated: 27/Nov/2024\n*/:root{--mbo-tag-aval-copy-icon-dimension: var(--sizing-x8);--mbo-tag-aval-copy-icon-color: var(--color-blue-700)}mbo-tag-aval-copy{--bocc-icon-dimension: var(--mbo-tag-aval-copy-icon-dimension);position:relative;display:block}mbo-tag-aval-copy bocc-icon{color:var(--mbo-tag-aval-copy-icon-color)}\n"],encapsulation:2}),f})(),v=(()=>{class f{}return f.\u0275fac=function(g){return new(g||f)},f.\u0275cmp=s.\u0275\u0275defineComponent({type:f,selectors:[["mbo-tag-aval"]],inputs:{value:"value"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:2,vars:2,consts:[[3,"value"]],template:function(g,_){1&g&&s.\u0275\u0275element(0,"bocc-tag-aval",0)(1,"mbo-tag-aval-copy",0),2&g&&(s.\u0275\u0275property("value",_.value),s.\u0275\u0275advance(1),s.\u0275\u0275property("value",_.value))},dependencies:[i.CommonModule,o.qd,C],styles:["/*!\n * MBO TagAval Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 25/Nov/2024\n * Updated: 25/Nov/2024\n*/mbo-tag-aval{--bocc-icon-dimension: var(--sizing-x8);display:flex;align-items:center;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-tag-aval .mbo-tag-aval__copy{color:var(--color-blue-700)}\n"],encapsulation:2}),f})()},79798:(j,I,t)=>{t.d(I,{Vc:()=>r.Vc,rw:()=>n.rw,k4:()=>r.k4,qr:()=>n.qr,uf:()=>n.uf,xO:()=>i.x,A6:()=>e.A,tu:()=>a,Tj:()=>C,GI:()=>F,Uy:()=>B,To:()=>J,w7:()=>w,o2:()=>r.o2,B_:()=>r.B_,fi:()=>r.fi,XH:()=>r.XH,cN:()=>r.cN,Aj:()=>r.Aj,J5:()=>r.J5,DB:()=>z.D,NH:()=>P.N,ES:()=>oe.E,Nu:()=>r.Nu,x6:()=>Q.x,KI:()=>$.K,iF:()=>r.iF,u8:()=>x.u,eM:()=>G.e,ZF:()=>ee.Z,wu:()=>E.w,$n:()=>K.$,KN:()=>R.K,cV:()=>ce.c,t5:()=>n.t5,$O:()=>n.$O,ZS:()=>le.Z,sO:()=>de.s,bL:()=>be,zO:()=>D.z});var n=t(72765),r=t(27302),e=t(1027),i=t(7427),s=(t(16442),t(17007)),m=t(30263),y=t(44487),d=t.n(y),p=t(13462),u=t(21498),c=t(99877);let a=(()=>{class L{}return L.\u0275fac=function(k){return new(k||L)},L.\u0275mod=c.\u0275\u0275defineNgModule({type:L}),L.\u0275inj=c.\u0275\u0275defineInjector({imports:[s.CommonModule,p.LottieModule.forRoot({player:()=>d()}),n.rw,m.P8,m.Dj,u.P]}),L})(),C=(()=>{class L{ngBoccPortal(k){this.portal=k}onSubmit(){this.portal?.close()}}return L.\u0275fac=function(k){return new(k||L)},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-complementary-services-bluescreen"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:22,vars:0,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","error"],[1,"bocc-bluescreen__terms","body2-medium"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__message"],[1,"bocc-bluescreen__footer"],["id","btn_complementary-services-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275element(2,"bocc-icon",2),c.\u0275\u0275elementStart(3,"label"),c.\u0275\u0275text(4," \xa1Atenci\xf3n! "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"p"),c.\u0275\u0275text(6,"Tus siguientes servicios de la App M\xf3vil se encuentran desactivados:"),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(7,"ul",3)(8,"li",4),c.\u0275\u0275text(9,"Retiro f\xe1cil y Compras con QR."),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(10,"li",4),c.\u0275\u0275text(11,"Transacciones a celulares."),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(12,"li",4),c.\u0275\u0275text(13," Transferencias r\xe1pidas Banco de Occidente y sin inscripci\xf3n ACH. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(14,"li",4),c.\u0275\u0275text(15," Pago de servicios p\xfablicos no inscritos manualmente e impuestos. "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(16,"p",5),c.\u0275\u0275text(17," Si deseas activarlos ingresa en tu celular a la App Token Mobile del Banco de Occidente, luego ingresa al Portal Transaccional y busca la opci\xf3n Configuraciones > Servicios app m\xf3vil. "),c.\u0275\u0275elementEnd()(),c.\u0275\u0275elementStart(18,"div",6)(19,"button",7),c.\u0275\u0275listener("click",function(){return X.onSubmit()}),c.\u0275\u0275elementStart(20,"span"),c.\u0275\u0275text(21,"Continuar"),c.\u0275\u0275elementEnd()()())},dependencies:[s.CommonModule,m.Zl,m.P8],styles:["mbo-complementary-services-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),L})();var v=t(7603),f=t(87956),h=t(74520),g=t(39904),_=t(87903);function b(L,H){if(1&L){const k=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"div",6)(1,"label",7),c.\u0275\u0275text(2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"span",8),c.\u0275\u0275text(4,"Tu gerente asignado (a)"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(5,"p",8),c.\u0275\u0275text(6," Comun\xedcate conmigo para resolver tus solicitudes y acompa\xf1arte a cumplir tus metas \xa1Crezcamos juntos! "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(7,"button",9),c.\u0275\u0275listener("click",function(){c.\u0275\u0275restoreView(k);const ne=c.\u0275\u0275nextContext(2);return c.\u0275\u0275resetView(ne.onEmail(ne.manager.email))}),c.\u0275\u0275elementStart(8,"span",10),c.\u0275\u0275text(9),c.\u0275\u0275elementEnd()()()}if(2&L){const k=c.\u0275\u0275nextContext(2);c.\u0275\u0275advance(2),c.\u0275\u0275textInterpolate1(" \xa1HOLA! SOY ",k.manager.name," "),c.\u0275\u0275advance(7),c.\u0275\u0275textInterpolate1(" ",k.manager.email," ")}}function l(L,H){if(1&L){const k=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"div",11)(1,"mbo-message-empty"),c.\u0275\u0275text(2," No fue posible cargar los datos de tu gerente, por favor intenta mas tarde. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"button",12),c.\u0275\u0275listener("click",function(ne){c.\u0275\u0275restoreView(k);const ae=c.\u0275\u0275nextContext(2);return c.\u0275\u0275resetView(ae.onRetryManager(ne))}),c.\u0275\u0275elementStart(4,"span"),c.\u0275\u0275text(5,"Recargar"),c.\u0275\u0275elementEnd()()()}}function T(L,H){if(1&L&&(c.\u0275\u0275elementStart(0,"div",3),c.\u0275\u0275template(1,b,10,2,"div",4),c.\u0275\u0275template(2,l,6,0,"div",5),c.\u0275\u0275elementEnd()),2&L){const k=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",k.manager),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!k.manager)}}function S(L,H){1&L&&(c.\u0275\u0275elementStart(0,"div",13),c.\u0275\u0275element(1,"bocc-skeleton-text",14)(2,"bocc-skeleton-text",15)(3,"bocc-skeleton-text",16)(4,"bocc-skeleton-text",17),c.\u0275\u0275elementEnd()),2&L&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0),c.\u0275\u0275advance(1),c.\u0275\u0275property("active",!0))}t(29306);let O=(()=>{class L{constructor(k){this.customerService=k,this.requesting=!1}onRetryManager(k){this.customerService.requestManager(),k.stopPropagation()}onEmail(k){(0,_.Gw)(`mailto:${k}`)}onWhatsapp(){(0,_.Gw)(g.BA.WHATSAPP)}}return L.\u0275fac=function(k){return new(k||L)(c.\u0275\u0275directiveInject(f.vZ))},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-contact-manager"]],inputs:{manager:"manager",requesting:"requesting"},standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[[1,"mbo-contact-manager__content"],["class","mbo-contact-manager__information",4,"ngIf"],["class","mbo-contact-manager__skeleton",4,"ngIf"],[1,"mbo-contact-manager__information"],["class","mbo-contact-manager__contact",4,"ngIf"],["class","mbo-contact-manager__empty",4,"ngIf"],[1,"mbo-contact-manager__contact"],[1,"body2-medium","truncate"],[1,"smalltext-medium"],["bocc-button","raised","prefixIcon","email",3,"click"],[1,"mbo-contact-manager__email","truncate"],[1,"mbo-contact-manager__empty"],["bocc-button","flat","prefixIcon","reload",3,"click"],[1,"mbo-contact-manager__skeleton"],[1,"mbo-contact-manager__skeleton--title",3,"active"],[1,"mbo-contact-manager__skeleton--subtitle",3,"active"],[1,"mbo-contact-manager__skeleton--text",3,"active"],[1,"mbo-contact-manager__skeleton--button",3,"active"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275template(1,T,3,2,"div",1),c.\u0275\u0275template(2,S,5,4,"div",2),c.\u0275\u0275elementEnd()),2&k&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",!X.requesting),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",X.requesting))},dependencies:[s.CommonModule,s.NgIf,m.P8,m.Dj,r.Aj],styles:["mbo-contact-manager{position:relative;width:100%;display:block}mbo-contact-manager .mbo-contact-manager__content{position:relative;width:100%;padding:var(--sizing-x8);box-sizing:border-box}mbo-contact-manager .mbo-contact-manager__contact{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);overflow:hidden}mbo-contact-manager .mbo-contact-manager__contact>span{color:var(--color-blue-700)}mbo-contact-manager .mbo-contact-manager__contact>p{color:var(--color-carbon-lighter-700)}mbo-contact-manager .mbo-contact-manager__email{text-transform:lowercase;font-size:5rem}mbo-contact-manager .mbo-contact-manager__empty{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);justify-content:center}mbo-contact-manager .mbo-contact-manager__message{color:var(--color-carbon-lighter-700);text-align:center}mbo-contact-manager .mbo-contact-manager__whatsapp{padding:0rem var(--sizing-x4)}mbo-contact-manager .mbo-contact-manager__whatsapp button{background:var(--color-bocc-700);width:100%}mbo-contact-manager .mbo-contact-manager__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--title{min-height:var(--sizing-x12)}mbo-contact-manager .mbo-contact-manager__skeleton--subtitle{width:60%;min-height:var(--sizing-x8)}mbo-contact-manager .mbo-contact-manager__skeleton--text{min-height:var(--sizing-x16)}mbo-contact-manager .mbo-contact-manager__skeleton--button{min-height:var(--sizing-x20)}\n"],encapsulation:2}),L})();const N={id:"btn_contacts-information_whatsapp",label:"L\xednea de Whatsapp",icon:"whatsapp",action:"WHATSAPP",boccTheme:"success"},te={id:"btn_contacts-information_pqrs",label:"Solicitudes y reclamos",icon:"pqrs",action:"PQRS"},Z={id:"btn_contacts-information_phones",label:"L\xedneas de atenci\xf3n",icon:"phone",action:"PHONES"};function Y(L,H){if(1&L&&(c.\u0275\u0275elementStart(0,"div",7),c.\u0275\u0275element(1,"mbo-contact-manager",8),c.\u0275\u0275elementEnd()),2&L){const k=c.\u0275\u0275nextContext();c.\u0275\u0275advance(1),c.\u0275\u0275property("manager",k.manager)("requesting",k.requesting)}}function U(L,H){if(1&L){const k=c.\u0275\u0275getCurrentView();c.\u0275\u0275elementStart(0,"li",9)(1,"div",10),c.\u0275\u0275listener("click",function(ne){const _e=c.\u0275\u0275restoreView(k).$implicit,ve=c.\u0275\u0275nextContext();return c.\u0275\u0275resetView(ve.onOption(_e,ne))}),c.\u0275\u0275elementStart(2,"label",11),c.\u0275\u0275text(3),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(4,"div",12)(5,"div",13),c.\u0275\u0275element(6,"bocc-icon",14),c.\u0275\u0275elementEnd()()()()}if(2&L){const k=H.$implicit;c.\u0275\u0275property("id",k.id),c.\u0275\u0275advance(3),c.\u0275\u0275textInterpolate1(" ",k.label," "),c.\u0275\u0275advance(1),c.\u0275\u0275attribute("bocc-theme",k.boccTheme),c.\u0275\u0275advance(2),c.\u0275\u0275property("icon",k.icon)}}let J=(()=>{class L{constructor(k,X,ne){this.utagService=k,this.customerStore=X,this.customerService=ne,this.isManagerEnabled=!1,this.requesting=!1,this.options=[N,te,Z]}ngOnInit(){this.sessionUnsubscription=this.customerStore.subscribe(({session:k})=>{this.isManagerEnabled=k?.customer?.segment.visible,this.isManagerEnabled?this.managerUnsubscription=this.customerService.subcribeManager(X=>{this.manager=X.manager,this.requesting=X.requesting}):this.managerUnsubscription&&this.managerUnsubscription()})}ngOnDestroy(){this.sessionUnsubscription&&this.sessionUnsubscription(),this.managerUnsubscription&&this.managerUnsubscription()}ngBoccPortal(k){this.portal=k}onOption(k,X){this.utagService.link("click",k.id),this.portal?.send({action:"option",value:k}),X.stopPropagation()}onClose(){this.portal?.send({action:"close"})}}return L.\u0275fac=function(k){return new(k||L)(c.\u0275\u0275directiveInject(v.D),c.\u0275\u0275directiveInject(h.f),c.\u0275\u0275directiveInject(f.vZ))},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-contact-information"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:7,vars:2,consts:[[1,"mbo-contact-information__component",3,"click"],["class","mbo-contact-information__component__manager",4,"ngIf"],[1,"mbo-contact-information__options"],["class","mbo-contact-information__option",3,"id",4,"ngFor","ngForOf"],[1,"mbo-contact-information__close"],["icon","close",3,"click"],[1,"mbo-contact-information__backdrop",3,"click"],[1,"mbo-contact-information__component__manager"],[3,"manager","requesting"],[1,"mbo-contact-information__option",3,"id"],[1,"mbo-contact-information__option__content",3,"click"],[1,"mbo-contact-information__option__label","smalltext-medium"],[1,"mbo-contact-information__option__avatar"],[1,"mbo-contact-information__option__avatar__content"],[3,"icon"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275listener("click",function(){return X.onClose()}),c.\u0275\u0275template(1,Y,2,2,"div",1),c.\u0275\u0275elementStart(2,"ul",2),c.\u0275\u0275template(3,U,7,4,"li",3),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(4,"button",4)(5,"bocc-icon",5),c.\u0275\u0275listener("click",function(){return X.onClose()}),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(6,"div",6),c.\u0275\u0275listener("click",function(){return X.onClose()}),c.\u0275\u0275elementEnd()),2&k&&(c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",X.isManagerEnabled),c.\u0275\u0275advance(2),c.\u0275\u0275property("ngForOf",X.options))},dependencies:[s.CommonModule,s.NgForOf,s.NgIf,m.Zl,O],styles:["mbo-contact-information{--pvt-avatar-transform: rotateZ(90deg);--pvt-backdrop-opacity: 0;--pvt-backdrop-delay: 0ms;position:fixed;top:0rem;left:0rem;width:100%;height:100%}mbo-contact-information .mbo-contact-information__component{position:absolute;display:flex;left:0rem;bottom:0rem;width:100%;flex-direction:column;padding-bottom:var(--sizing-x12);z-index:var(--z-index-4)}mbo-contact-information .mbo-contact-information__component__manager{position:relative;width:calc(100% - var(--sizing-x12));border-radius:var(--sizing-x8);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box;margin:var(--sizing-x6);box-shadow:var(--z-bottom-lighter-4);background:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__options{position:relative;display:flex;flex-direction:column-reverse;list-style-type:none;padding-inline:var(--sizing-x8);padding-block-end:var(--sizing-x12);margin-block:0rem;box-sizing:border-box}mbo-contact-information .mbo-contact-information__option{position:relative;display:flex;width:100%;justify-content:flex-end;padding-block:var(--sizing-x4);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option:nth-child(1){transform:translateY(100%)}mbo-contact-information .mbo-contact-information__option:nth-child(2){transform:translateY(200%)}mbo-contact-information .mbo-contact-information__option:nth-child(3){transform:translateY(300%)}mbo-contact-information .mbo-contact-information__option:nth-child(4){transform:translateY(400%)}mbo-contact-information .mbo-contact-information__option:nth-child(5){transform:translateY(500%)}mbo-contact-information .mbo-contact-information__option:nth-child(6){transform:translateY(600%)}mbo-contact-information .mbo-contact-information__option:nth-child(7){transform:translateY(700%)}mbo-contact-information .mbo-contact-information__option:nth-child(8){transform:translateY(800%)}mbo-contact-information .mbo-contact-information__option:nth-child(9){transform:translateY(900%)}mbo-contact-information .mbo-contact-information__option:nth-child(10){transform:translateY(1000%)}mbo-contact-information .mbo-contact-information__option__content{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-information .mbo-contact-information__option__label{position:relative;text-align:center;color:var(--color-navy-900);padding:6px var(--sizing-x8);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x4);filter:drop-shadow(var(--z-bottom-darker-4))}mbo-contact-information .mbo-contact-information__option__avatar{display:flex;justify-content:center;align-items:center;width:var(--sizing-x24);height:var(--sizing-x24);background:var(--color-navy-900);box-shadow:var(--z-bottom-darker-8);border-radius:var(--sizing-x20);transform:var(--pvt-avatar-transform);transition:transform .64s cubic-bezier(.075,.82,.165,1);transition-delay:80ms}mbo-contact-information .mbo-contact-information__option__avatar__content{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x16);height:var(--sizing-x16);background:var(--color-bocc-200);border-radius:var(--sizing-x20)}mbo-contact-information .mbo-contact-information__option__avatar__content bocc-icon{color:var(--color-bocc-700)}mbo-contact-information .mbo-contact-information__close{--bocc-icon-dimension: var(--sizing-x10);display:flex;justify-content:center;align-items:center;width:var(--sizing-x12);height:var(--sizing-x12);padding:0rem;margin-left:calc(100% - 26rem);background:var(--gradient-bocc-bottom-700);border-radius:var(--sizing-x2);box-shadow:var(--z-bottom-darker-4)}mbo-contact-information .mbo-contact-information__close bocc-icon{color:var(--color-carbon-lighter-50)}mbo-contact-information .mbo-contact-information__backdrop{position:absolute;top:0rem;left:0rem;width:100%;height:100%;background:var(--overlay-blue-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);z-index:var(--z-index-2);opacity:var(--pvt-backdrop-opacity);transition:opacity .16s cubic-bezier(.075,.82,.165,1);transition-delay:var(--pvt-backdrop-delay)}.bocc-floating-element__content--visible mbo-contact-information{--pvt-avatar-transform: rotateZ(0deg);--pvt-backdrop-opacity: 1}.bocc-floating-element__content--visible mbo-contact-information .mbo-contact-information__option{transform:translateY(0)}.bocc-floating-element__content--hidden mbo-contact-information{--pvt-backdrop-delay: .72s}\n"],encapsulation:2}),L})();var A=t(95437);let w=(()=>{class L{constructor(k,X){this.floatingService=k,this.mboProvider=X,this.contactsFloating=this.floatingService.create(J),this.contactsFloating?.subscribe(({action:ne,value:ae})=>{"option"===ne?this.dispatchOption(ae):this.close()})}subscribe(k){this.subscriber=k}open(){this.contactsFloating?.open()}close(){this.contactsFloating?.close(),this.subscriber&&this.subscriber("close")}destroy(){this.contactsFloating?.destroy()}dispatchOption(k){"PQRS"===k.action?this.mboProvider.openUrl(g.BA.PQRS):this.subscriber&&this.subscriber(k)}}return L.\u0275fac=function(k){return new(k||L)(c.\u0275\u0275inject(m.B7),c.\u0275\u0275inject(A.ZL))},L.\u0275prov=c.\u0275\u0275defineInjectable({token:L,factory:L.\u0275fac,providedIn:"root"}),L})(),F=(()=>{class L{constructor(){this.defenderLineNumber=g._L.DEFENDER_LINE,this.defenderLinePhone=g.WB.DEFENDER_LINE}ngBoccPortal(k){}onEmail(){(0,_.Gw)("mailto:<EMAIL>")}}return L.\u0275fac=function(k){return new(k||L)},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-contact-phones"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:39,vars:2,consts:[[1,"mbo-contact-phones__content"],[1,"mbo-contact-phones__defender"],[1,"mbo-contact-phones__defender__link"],[1,"mbo-contact-phones__defender__link__data"],["icon","chat-message"],[1,"body2-medium"],[1,"mbo-contact-phones__defender__data"],[1,"mbo-contact-phones__defender__name"],[1,"secondary","body2-medium"],["bocc-theme","alert",1,"caption-medium"],[1,"mbo-contact-phones__defender__info"],["icon","location"],[1,"mbo-contact-phones__defender__info__section"],[1,"subtitle","body2-medium"],["icon","phone"],[1,"reference","body2-medium",3,"href"],["icon","email"],[1,"reference","body2-medium",3,"click"],["icon","user-profile-time"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0),c.\u0275\u0275element(1,"mbo-attention-lines-form"),c.\u0275\u0275elementStart(2,"div",1)(3,"div",2)(4,"div",3),c.\u0275\u0275element(5,"bocc-icon",4),c.\u0275\u0275elementStart(6,"span",5),c.\u0275\u0275text(7,"Defensor del consumidor financiero"),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(8,"div",6)(9,"div",7)(10,"label",5),c.\u0275\u0275text(11,"LINA MARIA ZORRO CERON"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(12,"label",8)(13,"span"),c.\u0275\u0275text(14,"Lorena Cerchar Rosado"),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(15,"bocc-badge",9),c.\u0275\u0275text(16," Suplente "),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(17,"div",10),c.\u0275\u0275element(18,"bocc-icon",11),c.\u0275\u0275elementStart(19,"div",12)(20,"span",13),c.\u0275\u0275text(21," Cra 7 no. 71 - 52 Torre A Piso 1, Bogot\xe1 "),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(22,"div",10),c.\u0275\u0275element(23,"bocc-icon",14),c.\u0275\u0275elementStart(24,"div",12)(25,"a",15),c.\u0275\u0275text(26),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(27,"span",13),c.\u0275\u0275text(28," Ext. 15318 - 15311 "),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(29,"div",10),c.\u0275\u0275element(30,"bocc-icon",16),c.\u0275\u0275elementStart(31,"div",12)(32,"span",17),c.\u0275\u0275listener("click",function(){return X.onEmail()}),c.\u0275\u0275text(33," <EMAIL> "),c.\u0275\u0275elementEnd()()(),c.\u0275\u0275elementStart(34,"div",10),c.\u0275\u0275element(35,"bocc-icon",18),c.\u0275\u0275elementStart(36,"div",12)(37,"span",13),c.\u0275\u0275text(38," Lunes a viernes: 8:30 a.m - 5:30 p.m "),c.\u0275\u0275elementEnd()()()()()()),2&k&&(c.\u0275\u0275advance(25),c.\u0275\u0275property("href",X.defenderLinePhone,c.\u0275\u0275sanitizeUrl),c.\u0275\u0275advance(1),c.\u0275\u0275textInterpolate1(" ",X.defenderLineNumber," "))},dependencies:[s.CommonModule,m.Zl,m.Oh,r.Vc],styles:["mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x12);--pvt-defender-font-size: var(--smalltext-size);--pvt-defender-height: var(--body2-line-height);--pvt-defender-letter-spacing: var(--body2-letter-spacing);--pvt-defender-rowgap: var(--sizing-x12);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-phones .mbo-contact-phones__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);overflow:hidden;padding:var(--pvt-content-padding);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__link{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);overflow:hidden}mbo-contact-phones .mbo-contact-phones__defender__link__data{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-contact-phones .mbo-contact-phones__defender__link__data span{width:calc(100% - var(--sizing-x20));color:var(--color-blue-700);font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__link__data bocc-icon{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__data{position:relative;width:100%;display:flex;flex-direction:column;row-gap:var(--pvt-defender-rowgap)}mbo-contact-phones .mbo-contact-phones__defender__name{position:relative;width:100%;display:flex;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-contact-phones .mbo-contact-phones__defender__name>label{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-contact-phones .mbo-contact-phones__defender__name>label:not(.secondary){font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing)}mbo-contact-phones .mbo-contact-phones__defender__name>label>span{font-size:var(--caption-size);letter-spacing:var(--caption-letter-spacing);overflow:hidden;color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info{display:flex;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);align-items:center}mbo-contact-phones .mbo-contact-phones__defender__info__section{display:flex;width:calc(100% - var(--sizing-x16));flex-direction:column;row-gap:var(--sizing-x2)}mbo-contact-phones .mbo-contact-phones__defender__info__section .title{color:var(--color-carbon-darker-1000)}mbo-contact-phones .mbo-contact-phones__defender__info__section .reference{color:var(--color-blue-700)}mbo-contact-phones .mbo-contact-phones__defender__info__section .subtitle{color:var(--color-carbon-lighter-700)}mbo-contact-phones .mbo-contact-phones__defender__info bocc-icon{color:var(--color-carbon-lighter-400)}mbo-contact-phones .mbo-contact-phones__defender a,mbo-contact-phones .mbo-contact-phones__defender span{font-size:var(--pvt-defender-font-size);line-height:var(--pvt-defender-height);letter-spacing:var(--pvt-defender-letter-spacing);text-decoration:none}@media only screen and (max-width: 320px){mbo-contact-phones{--pvt-content-padding: 0rem var(--sizing-x8);--pvt-defender-height: var(--smalltext-line-height);--pvt-defender-letter-spacing: var(--smalltext-letter-spacing);--pvt-defender-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),L})(),B=(()=>{class L{constructor(){this.whatsappNumber=g._L.WHATSAPP}ngBoccPortal(k){}onClick(){(0,_.Gw)(g.BA.WHATSAPP)}}return L.\u0275fac=function(k){return new(k||L)},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-contact-whatsapp"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:7,vars:1,consts:[[1,"mbo-contact-whatsapp__content"],[1,"mbo-contact-whatsapp__body","body2-medium"],[1,"mbo-contact-whatsapp__footer"],["bocc-button","raised","prefixIcon","whatsapp","bocc-theme","success",3,"click"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275text(2," Habla con uno de nuestros ejecutivos para resolver tus inquietudes y solicitudes. "),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",2)(4,"button",3),c.\u0275\u0275listener("click",function(){return X.onClick()}),c.\u0275\u0275elementStart(5,"span"),c.\u0275\u0275text(6),c.\u0275\u0275elementEnd()()()()),2&k&&(c.\u0275\u0275advance(6),c.\u0275\u0275textInterpolate(X.whatsappNumber))},dependencies:[s.CommonModule,m.P8],styles:["mbo-contact-whatsapp{--pvt-body-font-size: var(--body2-size);position:relative;width:100%;display:block;margin:var(--sizing-safe-body-x8)}mbo-contact-whatsapp .mbo-contact-whatsapp__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-contact-whatsapp .mbo-contact-whatsapp__body{position:relative;width:100%;color:var(--color-carbon-lighter-700);font-size:var(--pvt-body-font-size)}mbo-contact-whatsapp .mbo-contact-whatsapp__footer{position:relative;width:100%}mbo-contact-whatsapp .mbo-contact-whatsapp__footer .bocc-button--raised{width:100%;background:var(--color-bocc-700)}@media screen and (max-width: 320px){mbo-contact-whatsapp{--pvt-body-font-size: var(--caption-size)}}\n"],encapsulation:2}),L})();var P=t(10119),Q=(t(87677),t(68789)),oe=t(10455),z=t(91642),$=t(10464),x=t(75221),D=t(88649),G=t(13043),ee=t(38116),E=t(68819),K=t(19310),R=t(94614),ce=(t(70957),t(91248),t(4663)),le=t(13961),de=t(66709),ie=t(24495),se=t(57544),me=t(53113);class ue extends se.FormGroup{constructor(){const H=new se.FormControl("",[ie.zf,ie.O_,ie.Y2,(0,ie.Mv)(24)]),k=new se.FormControl("",[ie.C1,ie.zf,ie.O_,ie.Y2,(0,ie.Mv)(24)]);super({controls:{description:k,reference:H}}),this.description=k,this.reference=H}setNote(H){this.description.setValue(H?.description),this.reference.setValue(H?.reference)}getNote(){return new me.$H(this.description.value,this.reference.value)}}function pe(L,H){if(1&L&&c.\u0275\u0275element(0,"bocc-input-box",7),2&L){const k=c.\u0275\u0275nextContext();c.\u0275\u0275property("formControl",k.formControls.reference)}}let be=(()=>{class L{constructor(){this.title="A\xf1ade una descripci\xf3n a tu transacci\xf3n",this.requiredReference=!0,this.cancelAction={id:"btn_transaction-note-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transaction-note-sheet_save",label:"Guardar",disabled:()=>this.formControls.invalid,click:()=>{this.portal?.resolve(this.formControls.getNote()),this.portal?.destroy()}},this.formControls=new ue}ngOnInit(){this.formControls.setNote(this.initialValue)}ngBoccPortal(k){this.portal=k}}return L.\u0275fac=function(k){return new(k||L)},L.\u0275cmp=c.\u0275\u0275defineComponent({type:L,selectors:[["mbo-transaction-note-sheet"]],standalone:!0,features:[c.\u0275\u0275StandaloneFeature],decls:8,vars:5,consts:[[1,"mbo-transaction-note-sheet__content"],[1,"mbo-transaction-note-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transaction-note-sheet__body"],[1,"mbo-transaction-note-sheet__title","subtitle2-medium"],["elementId","txt_transaction-note-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl",4,"ngIf"],["elementId","txt_transaction-note-sheet_reference","label","Referencia","placeholder","Ej: Pago de arriendo",3,"formControl"]],template:function(k,X){1&k&&(c.\u0275\u0275elementStart(0,"div",0)(1,"div",1),c.\u0275\u0275element(2,"bocc-header-form",2),c.\u0275\u0275elementEnd(),c.\u0275\u0275elementStart(3,"div",3)(4,"div",4),c.\u0275\u0275text(5),c.\u0275\u0275elementEnd(),c.\u0275\u0275element(6,"bocc-input-box",5),c.\u0275\u0275template(7,pe,1,1,"bocc-input-box",6),c.\u0275\u0275elementEnd()()),2&k&&(c.\u0275\u0275advance(2),c.\u0275\u0275property("leftAction",X.cancelAction)("rightAction",X.saveAction),c.\u0275\u0275advance(3),c.\u0275\u0275textInterpolate1(" ",X.title," "),c.\u0275\u0275advance(1),c.\u0275\u0275property("formControl",X.formControls.description),c.\u0275\u0275advance(1),c.\u0275\u0275property("ngIf",X.requiredReference))},dependencies:[s.CommonModule,s.NgIf,m.Jx,m.DT],styles:["mbo-transaction-note-sheet{position:relative;width:100%;display:block}mbo-transaction-note-sheet .mbo-transaction-note-sheet__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-transaction-note-sheet .mbo-transaction-note-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-transaction-note-sheet .mbo-transaction-note-sheet__title{position:relative;width:100%;text-align:center;box-sizing:border-box}\n"],encapsulation:2}),L})()},35324:(j,I,t)=>{t.d(I,{V:()=>d});var n=t(17007),e=t(30263),i=t(39904),o=t(87903),s=t(99877);function y(p,u){if(1&p){const c=s.\u0275\u0275getCurrentView();s.\u0275\u0275elementStart(0,"a",9),s.\u0275\u0275listener("click",function(){s.\u0275\u0275restoreView(c);const C=s.\u0275\u0275nextContext();return s.\u0275\u0275resetView(C.onWhatsapp())}),s.\u0275\u0275elementStart(1,"div",3),s.\u0275\u0275element(2,"bocc-icon",10),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(3,"div",5)(4,"label",6),s.\u0275\u0275text(5," Whatsapp "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(6,"label",7),s.\u0275\u0275text(7),s.\u0275\u0275elementEnd()()()}if(2&p){const c=s.\u0275\u0275nextContext();s.\u0275\u0275advance(7),s.\u0275\u0275textInterpolate1(" ",c.whatsappNumber," ")}}let d=(()=>{class p{constructor(){this.whatsapp=!1,this.whatsappNumber=i._L.WHATSAPP,this.nationalLineNumber=i._L.NATIONAL_LINE,this.bogotaLineNumber=i._L.BOGOTA_LINE,this.nationalLinePhone=i.WB.NATIONAL_LINE,this.bogotaLinePhone=i.WB.BOGOTA_LINE}onWhatsapp(){(0,o.Gw)(i.BA.WHATSAPP)}}return p.\u0275fac=function(c){return new(c||p)},p.\u0275cmp=s.\u0275\u0275defineComponent({type:p,selectors:[["mbo-attention-lines-form"]],inputs:{whatsapp:"whatsapp"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:18,vars:5,consts:[[1,"mbo-attention-lines-form__content"],["id","lnk_attention-lines_whatsapp","class","mbo-attention-lines-form__element","bocc-theme","success",3,"click",4,"ngIf"],["id","lnk_attention-lines_national",1,"mbo-attention-lines-form__element",3,"href"],[1,"mbo-attention-lines-form__element__icon"],["icon","phone-call"],[1,"mbo-attention-lines-form__element__content"],[1,"mbo-attention-lines-form__element__title","body2-medium"],[1,"mbo-attention-lines-form__element__subtitle","body2-medium"],["id","lnk_attention-lines_local",1,"mbo-attention-lines-form__element",3,"href"],["id","lnk_attention-lines_whatsapp","bocc-theme","success",1,"mbo-attention-lines-form__element",3,"click"],["icon","whatsapp"]],template:function(c,a){1&c&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275template(1,y,8,1,"a",1),s.\u0275\u0275elementStart(2,"a",2)(3,"div",3),s.\u0275\u0275element(4,"bocc-icon",4),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"div",5)(6,"label",6),s.\u0275\u0275text(7," L\xednea de atenci\xf3n nacional "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(8,"label",7),s.\u0275\u0275text(9),s.\u0275\u0275elementEnd()()(),s.\u0275\u0275elementStart(10,"a",8)(11,"div",3),s.\u0275\u0275element(12,"bocc-icon",4),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(13,"div",5)(14,"label",6),s.\u0275\u0275text(15," Bogot\xe1 "),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(16,"label",7),s.\u0275\u0275text(17),s.\u0275\u0275elementEnd()()()()),2&c&&(s.\u0275\u0275advance(1),s.\u0275\u0275property("ngIf",a.whatsapp),s.\u0275\u0275advance(1),s.\u0275\u0275property("href",a.nationalLinePhone,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(7),s.\u0275\u0275textInterpolate1(" ",a.nationalLineNumber," "),s.\u0275\u0275advance(1),s.\u0275\u0275property("href",a.bogotaLinePhone,s.\u0275\u0275sanitizeUrl),s.\u0275\u0275advance(7),s.\u0275\u0275textInterpolate1(" ",a.bogotaLineNumber," "))},dependencies:[n.CommonModule,n.NgIf,e.Zl],styles:["mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x4);--pvt-content-rowgap: var(--sizing-x6);--pvt-element-font-size: var(--body2-size);--pvt-element-height: var(--body2-line-height);--pvt-element-padding: var(--sizing-x4) var(--sizing-x8);position:relative;width:100%;display:flex;justify-content:center}mbo-attention-lines-form .mbo-attention-lines-form__content{position:relative;display:flex;width:min-content;flex-direction:column;row-gap:var(--pvt-content-rowgap);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);text-decoration:none}mbo-attention-lines-form .mbo-attention-lines-form__element__icon{position:relative;display:flex;justify-self:center;align-items:center;width:var(--sizing-x18);border-right:var(--border-1-lighter-300)}mbo-attention-lines-form .mbo-attention-lines-form__element__icon bocc-icon{color:var(--color-bocc-700);padding:var(--sizing-x4) 0rem}mbo-attention-lines-form .mbo-attention-lines-form__element__content{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20));padding:var(--pvt-element-padding);box-sizing:border-box}mbo-attention-lines-form .mbo-attention-lines-form__element__content label{position:relative;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:var(--pvt-element-font-size);min-height:var(--pvt-element-height);line-height:var(--pvt-element-height)}mbo-attention-lines-form .mbo-attention-lines-form__element__title{color:var(--color-carbon-lighter-700)}mbo-attention-lines-form .mbo-attention-lines-form__element__subtitle{color:var(--color-bocc-700)}@media only screen and (max-height: 700px){mbo-attention-lines-form{--pvt-content-padding: 0rem var(--sizing-x2);--pvt-element-padding: var(--sizing-x2) var(--sizing-x4);--pvt-element-font-size: var(--smalltext-size);--pvt-element-height: var(--smalltext-line-height)}}\n"],encapsulation:2}),p})()},9593:(j,I,t)=>{t.d(I,{k:()=>y});var n=t(17007),e=t(30263),i=t(39904),o=t(95437),s=t(99877);let y=(()=>{class d{constructor(u){this.mboProvider=u,this.active="products"}get isProducts(){return"products"===this.active}get isTransfers(){return"transfers"===this.active}get isPaymentsQr(){return"payments_qr"===this.active}get isPayments(){return"payments"===this.active}get isToken(){return"token"===this.active}onProducts(){!this.isProducts&&this.mboProvider.navigation.next(i.Z6.CUSTOMER.PRODUCTS.HOME),this.active="products"}onTransfers(){!this.isTransfers&&this.mboProvider.navigation.next(i.Z6.TRANSFERS.HOME),this.active="transfers"}onPaymentQR(){this.mboProvider.navigation.next(i.Z6.PAYMENTS.QR.SCAN),this.active="payments_qr"}onPayments(){!this.isPayments&&this.mboProvider.navigation.next(i.Z6.PAYMENTS.HOME),this.active="payments"}onToken(){}}return d.\u0275fac=function(u){return new(u||d)(s.\u0275\u0275directiveInject(o.ZL))},d.\u0275cmp=s.\u0275\u0275defineComponent({type:d,selectors:[["mbo-bottom-navigation"]],inputs:{active:"active"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:25,vars:10,consts:[[1,"bocc-footer-form"],[1,"bocc-footer-form__content"],["id","btn_botnavigation_products",1,"bocc-footer-form__element",3,"click"],["icon","credit-card"],[1,"bocc-footer-form__label"],["id","btn_botnavigation_transfers",1,"bocc-footer-form__element",3,"click"],["icon","arrow-transfer"],["id","btn_botnavigation_payqr",1,"bocc-footer-form__element",3,"click"],[1,"bocc-footer-form__button"],[1,"bocc-footer-form__button__content"],["icon","qr-code-aval"],["icon",""],["id","btn_botnavigation_payments",1,"bocc-footer-form__element",3,"click"],["icon","checking-account"],["id","btn_botnavigation_token",1,"bocc-footer-form__element",3,"click"],["icon","key"]],template:function(u,c){1&u&&(s.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),s.\u0275\u0275listener("click",function(){return c.onProducts()}),s.\u0275\u0275element(3,"bocc-icon",3),s.\u0275\u0275elementStart(4,"label",4),s.\u0275\u0275text(5," Productos "),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(6,"div",5),s.\u0275\u0275listener("click",function(){return c.onTransfers()}),s.\u0275\u0275element(7,"bocc-icon",6),s.\u0275\u0275elementStart(8,"label",4),s.\u0275\u0275text(9," Transferir "),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(10,"div",7),s.\u0275\u0275listener("click",function(){return c.onPaymentQR()}),s.\u0275\u0275elementStart(11,"div",8)(12,"div",9),s.\u0275\u0275element(13,"bocc-icon",10),s.\u0275\u0275elementEnd()(),s.\u0275\u0275element(14,"bocc-icon",11),s.\u0275\u0275elementStart(15,"label",4),s.\u0275\u0275text(16," Pago QR "),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(17,"div",12),s.\u0275\u0275listener("click",function(){return c.onPayments()}),s.\u0275\u0275element(18,"bocc-icon",13),s.\u0275\u0275elementStart(19,"label",4),s.\u0275\u0275text(20," Pagar "),s.\u0275\u0275elementEnd()(),s.\u0275\u0275elementStart(21,"div",14),s.\u0275\u0275listener("click",function(){return c.onToken()}),s.\u0275\u0275element(22,"bocc-icon",15),s.\u0275\u0275elementStart(23,"label",4),s.\u0275\u0275text(24," Token "),s.\u0275\u0275elementEnd()()()()),2&u&&(s.\u0275\u0275advance(2),s.\u0275\u0275classProp("bocc-footer-form__element--active",c.isProducts),s.\u0275\u0275advance(4),s.\u0275\u0275classProp("bocc-footer-form__element--active",c.isTransfers),s.\u0275\u0275advance(11),s.\u0275\u0275classProp("bocc-footer-form__element--active",c.isPayments),s.\u0275\u0275advance(4),s.\u0275\u0275classProp("bocc-footer-form__element--active",c.isToken)("bocc-footer-form__element--disabled",!0))},dependencies:[n.CommonModule,e.Zl],styles:["mbo-bottom-navigation{--pvt-label-color: var(--color-carbon-lighter-400);--pvt-label-font-size: var(--sizing-x6);--pvt-icon-color: var(--color-carbon-lighter-400);position:fixed;width:100%;left:0rem;bottom:0rem;padding:0rem var(--sizing-x2);box-sizing:border-box;z-index:var(--z-index-8);background:var(--overlay-white-80);border-top:var(--border-1-lighter-300);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem}mbo-bottom-navigation .bocc-footer-form{margin-bottom:var(--sizing-safe-bottom)}mbo-bottom-navigation .bocc-footer-form__element{width:20%}mbo-bottom-navigation .bocc-footer-form__element>bocc-icon{color:var(--pvt-icon-color)}mbo-bottom-navigation .bocc-footer-form__element--active{--pvt-label-color: var(--color-carbon-darker-1000);--pvt-icon-color: var(--color-blue-700)}mbo-bottom-navigation .bocc-footer-form__label{font-size:var(--pvt-label-font-size);letter-spacing:0em;color:var(--pvt-label-color)}mbo-bottom-navigation .bocc-footer-form__button{padding:var(--sizing-x2)}@media screen and (max-width: 320px){mbo-bottom-navigation{--pvt-label-font-size: 4.8rem}mbo-bottom-navigation .bocc-footer-form__button{transform:translate(-50%,-50%) rotate(45deg);width:var(--sizing-x24);height:var(--sizing-x24)}mbo-bottom-navigation .bocc-footer-form__button__content>.bocc-icon{font-size:var(--sizing-x12);width:var(--sizing-x12);height:var(--sizing-x12)}}@media screen and (min-width: 321px) and (max-width: 375px){mbo-bottom-navigation{--pvt-label-font-size: 5.25rem}}@media screen and (min-width: 376px) and (max-width: 389px){mbo-bottom-navigation{--pvt-label-font-size: 5.125rem;padding:0rem var(--sizing-x4)}}@media screen and (min-width: 390px){mbo-bottom-navigation{--pvt-label-font-size: 5rem}}\n"],encapsulation:2}),d})()},83867:(j,I,t)=>{t.d(I,{o:()=>f});var n=t(17007),e=t(30263),i=t(8834),o=t(98699),d=(t(57544),t(99877));function u(h,g){if(1&h&&(d.\u0275\u0275elementStart(0,"label",11),d.\u0275\u0275text(1),d.\u0275\u0275elementEnd()),2&h){const _=d.\u0275\u0275nextContext();d.\u0275\u0275classProp("mbo-currency-box__rate--active",_.hasValue),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate2(" ",_.valueFormat," ",_.rateCode," ")}}function c(h,g){if(1&h&&(d.\u0275\u0275elementStart(0,"div",12),d.\u0275\u0275element(1,"img",13),d.\u0275\u0275elementEnd()),2&h){const _=d.\u0275\u0275nextContext();d.\u0275\u0275advance(1),d.\u0275\u0275property("src",_.icon,d.\u0275\u0275sanitizeUrl)}}function a(h,g){if(1&h&&(d.\u0275\u0275elementStart(0,"div",14),d.\u0275\u0275text(1),d.\u0275\u0275elementEnd()),2&h){const _=d.\u0275\u0275nextContext();d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",_.currencyCode," ")}}function C(h,g){if(1&h&&(d.\u0275\u0275elementStart(0,"div",15),d.\u0275\u0275element(1,"bocc-icon",16),d.\u0275\u0275elementStart(2,"span",17),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd()()),2&h){const _=d.\u0275\u0275nextContext();d.\u0275\u0275advance(3),d.\u0275\u0275textInterpolate1(" ",null==_.formControl.error?null:_.formControl.error.message," ")}}function v(h,g){if(1&h&&(d.\u0275\u0275elementStart(0,"div",18),d.\u0275\u0275element(1,"bocc-icon",19),d.\u0275\u0275elementStart(2,"span",17),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd()()),2&h){const _=d.\u0275\u0275nextContext();d.\u0275\u0275advance(3),d.\u0275\u0275textInterpolate1(" ",_.helperInfo," ")}}let f=(()=>{class h{constructor(){this.disabled=!1,this.rateCode="USD",this.placeholder=""}get hasValue(){return this.value>0}get hasRate(){return(0,o.itIsDefined)(this.rate)}get value(){const _=+this.formControl?.value;return isNaN(_)?0:this.hasRate?_/this.rate:0}get valueFormat(){return(0,i.b)({value:this.value,symbol:"$",decimals:!0})}}return h.\u0275fac=function(_){return new(_||h)},h.\u0275cmp=d.\u0275\u0275defineComponent({type:h,selectors:[["mbo-currency-box"]],inputs:{elementId:"elementId",formControl:"formControl",disabled:"disabled",label:"label",icon:"icon",currencyCode:"currencyCode",rate:"rate",rateCode:"rateCode",helperInfo:"helperInfo",placeholder:"placeholder"},standalone:!0,features:[d.\u0275\u0275StandaloneFeature],decls:12,vars:17,consts:[[1,"mbo-currency-box__content"],[1,"mbo-currency-box__title"],[1,"mbo-currency-box__label",3,"for"],["class","mbo-currency-box__rate",3,"mbo-currency-box__rate--active",4,"ngIf"],[1,"mbo-currency-box__component"],[1,"mbo-currency-box__body"],["class","mbo-currency-box__body__flat",4,"ngIf"],[1,"mbo-currency-box__control","align-right",3,"elementId","placeholder","disabled","formControl"],["class","mbo-currency-box__body__code",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--error",4,"ngIf"],["class","mbo-currency-box__alert mbo-currency-box__alert--info",4,"ngIf"],[1,"mbo-currency-box__rate"],[1,"mbo-currency-box__body__flat"],[3,"src"],[1,"mbo-currency-box__body__code"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--error"],["icon","error"],[1,"mbo-currency-box__span"],[1,"mbo-currency-box__alert","mbo-currency-box__alert--info"],["icon","information"]],template:function(_,M){1&_&&(d.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),d.\u0275\u0275text(3),d.\u0275\u0275elementEnd(),d.\u0275\u0275template(4,u,2,4,"label",3),d.\u0275\u0275elementEnd(),d.\u0275\u0275elementStart(5,"div",4)(6,"div",5),d.\u0275\u0275template(7,c,2,1,"div",6),d.\u0275\u0275element(8,"bocc-currency-field",7),d.\u0275\u0275template(9,a,2,1,"div",8),d.\u0275\u0275elementEnd()(),d.\u0275\u0275template(10,C,4,1,"div",9),d.\u0275\u0275template(11,v,4,1,"div",10),d.\u0275\u0275elementEnd()),2&_&&(d.\u0275\u0275classProp("mbo-currency-box--focused",M.formControl.focused)("mbo-currency-box--error",M.formControl.invalid&&M.formControl.touched)("mbo-currency-box--disabled",M.formControl.disabled||M.disabled),d.\u0275\u0275advance(2),d.\u0275\u0275property("for",M.elementId),d.\u0275\u0275advance(1),d.\u0275\u0275textInterpolate1(" ",M.label," "),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",M.hasRate),d.\u0275\u0275advance(3),d.\u0275\u0275property("ngIf",M.icon),d.\u0275\u0275advance(1),d.\u0275\u0275property("elementId",M.elementId)("placeholder",M.placeholder)("disabled",M.disabled)("formControl",M.formControl),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",M.currencyCode),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",M.formControl.invalid&&M.formControl.touched),d.\u0275\u0275advance(1),d.\u0275\u0275property("ngIf",M.helperInfo&&!(M.formControl.invalid&&M.formControl.touched)))},dependencies:[n.CommonModule,n.NgIf,e.XZ,e.Zl],styles:["mbo-currency-box{position:relative;display:block;width:100%;box-sizing:border-box}mbo-currency-box .mbo-currency-box--focused .mbo-currency-box__body{border:var(--border-1) solid var(--color-blue-700)}mbo-currency-box .mbo-currency-box--error .mbo-currency-box__body{border:var(--border-1) solid var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__label{color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box--disabled .mbo-currency-box__body{border:var(--border-1) solid var(--color-carbon-lighter-400);background:var(--color-carbon-lighter-200)}mbo-currency-box .mbo-currency-box__content{position:relative;width:100%;box-sizing:border-box;transition:height .16s 0ms var(--standard-curve)}mbo-currency-box .mbo-currency-box__title{position:relative;display:flex;width:100%;justify-content:space-between;margin-bottom:var(--sizing-x4);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);height:var(--smalltext-line-height);line-height:var(--smalltext-line-height);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-currency-box .mbo-currency-box__label{display:block;font-size:inherit;font-weight:inherit;letter-spacing:inherit;height:inherit;line-height:inherit;color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__rate{font-weight:var(--font-weight-regular);color:var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__rate--active{color:var(--color-amathyst-700)}mbo-currency-box .mbo-currency-box__component{position:relative;width:100%}mbo-currency-box .mbo-currency-box__body{position:relative;display:flex;width:100%;box-sizing:border-box;height:var(--sizing-x20);background:var(--color-carbon-lighter-50);border-radius:var(--sizing-x2);border:var(--border-1) solid var(--color-carbon-lighter-400)}mbo-currency-box .mbo-currency-box__body__flat{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-start:var(--sizing-x4)}mbo-currency-box .mbo-currency-box__body__flat img{width:var(--sizing-x12);height:var(--sizing-x12)}mbo-currency-box .mbo-currency-box__body__code{padding-block:calc(var(--sizing-x4) - 1px);padding-inline-end:var(--sizing-x4);height:var(--sizing-x12);line-height:var(--sizing-x12);font-size:var(--body2-size);font-weight:var(--font-weight-medium);letter-spacing:var(--body2-letter-spacing);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__body bocc-currency-field{margin:auto 0rem;width:100%;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-currency-box .mbo-currency-box__alert{--bocc-icon-dimension: var(--sizing-x8);position:relative;width:100%;float:left;margin-top:var(--sizing-x4);font-size:var(--caption-size);font-weight:var(--font-weight-regular);letter-spacing:var(--caption-letter-spacing);line-height:var(--caption-line-height);color:var(--color-carbon-lighter-700)}mbo-currency-box .mbo-currency-box__alert--info{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--info bocc-icon{color:var(--color-semantic-info-700)}mbo-currency-box .mbo-currency-box__alert--error bocc-icon{color:var(--color-semantic-danger-700)}mbo-currency-box .mbo-currency-box__alert bocc-icon{display:block;float:left;margin-right:var(--sizing-x2)}mbo-currency-box .mbo-currency-box__alert span{position:relative;float:left;width:calc(100% - var(--sizing-x10))}\n"],encapsulation:2}),h})()},85070:(j,I,t)=>{t.d(I,{f:()=>m});var n=t(17007),e=t(78506),i=t(99877);const s=["*"];let m=(()=>{class y{constructor(p){this.session=p}ngOnInit(){this.session.customer().then(p=>this.customer=p)}}return y.\u0275fac=function(p){return new(p||y)(i.\u0275\u0275directiveInject(e._I))},y.\u0275cmp=i.\u0275\u0275defineComponent({type:y,selectors:[["mbo-customer-greeting"]],standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:5,vars:1,consts:[[1,"mbo-customer-greeting__content"],[1,"subtitle1-medium"],[1,"body1-medium"]],template:function(p,u){1&p&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"label",1),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(3,"span",2),i.\u0275\u0275projection(4),i.\u0275\u0275elementEnd()()),2&p&&(i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(null==u.customer?null:u.customer.shortName))},dependencies:[n.CommonModule],styles:["mbo-customer-greeting{position:relative;width:100%;display:block}mbo-customer-greeting .mbo-customer-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x1);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-customer-greeting .mbo-customer-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),y})()},65887:(j,I,t)=>{t.d(I,{X:()=>p});var n=t(17007),e=t(99877),o=t(30263),s=t(24495);function d(u,c){1&u&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275text(1,"Identificaci\xf3n"),e.\u0275\u0275elementEnd())}t(57544);let p=(()=>{class u{constructor(){this.documents=[],this.required=!0,this.disabled=!1,this.condense=!1,this.inputType="number",this.elementSelectId="",this.elementInputId=""}ngOnInit(){this.pageId&&(this.elementSelectId=`lst_${this.pageId}_document-type`,this.elementInputId=`txt_${this.pageId}_document-number`),this.documentType.setValidators(this.required?[s.C1]:[]),this.unsubscription=this.documentType.subscribe(a=>{a&&(this.updateNumber(a,this.required),this.inputType=this.getInputType(a))})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(a){if(a.required){const C=a.required.currentValue;this.documentType.setValidators(C?[s.C1]:[]),this.documentType.value&&this.updateNumber(this.documentType.value,C)}}get labelType(){return this.condense?"":"Tipo de documento"}get labelNumber(){return this.condense?"":"N\xfamero de identificaci\xf3n"}getInputType(a){return"PA"===a.code?"text":"number"}updateNumber(a,C){const v=this.validatorsForNumber(a,C);this.documentNumber.setValidators(v),this.documentNumber.invalid&&this.documentNumber.setValue("")}validatorsForNumber(a,C){return this.validatorsFromType(a).concat(C?[s.C1]:[])}maxLength(a){return C=>C&&C.length>a?{id:"maxLength",message:`Debe tener m\xe1ximo ${a} caracteres`}:null}validatorsFromType(a){switch(a.code){case"PA":return[s.JF];case"NIT":return[s.X1,this.maxLength(15)];default:return[s.X1]}}}return u.\u0275fac=function(a){return new(a||u)},u.\u0275cmp=e.\u0275\u0275defineComponent({type:u,selectors:[["mbo-document-customer"]],inputs:{pageId:"pageId",documentType:"documentType",documentNumber:"documentNumber",documents:"documents",required:"required",disabled:"disabled",condense:"condense"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],decls:4,vars:13,consts:[["class","mbo-document-customer__title",4,"ngIf"],[1,"mbo-document-customer__content"],["placeholder","Seleccione tipo de documento",3,"elementId","label","suggestions","disabled","formControl"],["placeholder","Ej: 1065642202",3,"elementId","label","type","disabled","formControl"],[1,"mbo-document-customer__title"]],template:function(a,C){1&a&&(e.\u0275\u0275template(0,d,2,0,"div",0),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-select-box",2)(3,"bocc-input-box",3),e.\u0275\u0275elementEnd()),2&a&&(e.\u0275\u0275property("ngIf",C.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("mbo-document-customer__content--condense",C.condense),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",C.elementSelectId)("label",C.labelType)("suggestions",C.documents)("disabled",C.disabled)("formControl",C.documentType),e.\u0275\u0275advance(1),e.\u0275\u0275property("elementId",C.elementInputId)("label",C.labelNumber)("type",C.inputType)("disabled",C.disabled)("formControl",C.documentNumber))},dependencies:[n.CommonModule,n.NgIf,o.DT,o.tv],styles:["mbo-document-customer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content{--pvt-select-width: 100%;--pvt-input-width: 100%;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-document-customer .mbo-document-customer__content--condense{--pvt-select-width: 30%;--pvt-input-width: 70%;flex-direction:row;row-gap:0rem;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-document-customer .mbo-document-customer__content bocc-select-box{width:var(--pvt-select-width)}mbo-document-customer .mbo-document-customer__content bocc-input-box{width:var(--pvt-input-width)}mbo-document-customer .mbo-document-customer__title{display:block;width:100%;height:var(--sizing-x8);padding:0rem var(--sizing-x2);box-sizing:border-box;line-height:var(--sizing-x8);font-size:var(--smalltext-size);font-weight:var(--font-weight-semibold);letter-spacing:var(--smalltext-letter-spacing);color:var(--bocc-field-box-label-color)}\n"],encapsulation:2}),u})()},78021:(j,I,t)=>{t.d(I,{c:()=>u});var n=t(17007),e=t(30263),i=t(7603),o=t(98699),m=t(99877);function d(c,a){if(1&c){const C=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",5),m.\u0275\u0275listener("click",function(){m.\u0275\u0275restoreView(C);const f=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(f.onAction(f.leftAction))}),m.\u0275\u0275elementStart(1,"span"),m.\u0275\u0275text(2),m.\u0275\u0275elementEnd()()}if(2&c){const C=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",C.leftAction.id)("bocc-button",C.leftAction.type||"flat")("prefixIcon",C.leftAction.prefixIcon)("disabled",C.itIsDisabled(C.leftAction))("hidden",C.itIsHidden(C.leftAction)),m.\u0275\u0275advance(2),m.\u0275\u0275textInterpolate(C.leftAction.label)}}function p(c,a){if(1&c){const C=m.\u0275\u0275getCurrentView();m.\u0275\u0275elementStart(0,"button",6),m.\u0275\u0275listener("click",function(){const h=m.\u0275\u0275restoreView(C).$implicit,g=m.\u0275\u0275nextContext();return m.\u0275\u0275resetView(g.onAction(h))}),m.\u0275\u0275elementEnd()}if(2&c){const C=a.$implicit,v=m.\u0275\u0275nextContext();m.\u0275\u0275property("id",C.id)("type",C.type||"flat")("bocc-button-action",C.icon)("disabled",v.itIsDisabled(C))("hidden",v.itIsHidden(C))}}let u=(()=>{class c{constructor(C){this.utagService=C,this.rightActions=[]}itIsDisabled({disabled:C}){return(0,o.evalValueOrFunction)(C)}itIsHidden({hidden:C}){return(0,o.evalValueOrFunction)(C)}onAction(C){const{id:v}=C;v&&this.utagService.link("click",v),C.click()}}return c.\u0275fac=function(C){return new(C||c)(m.\u0275\u0275directiveInject(i.D))},c.\u0275cmp=m.\u0275\u0275defineComponent({type:c,selectors:[["mbo-header-result"]],inputs:{leftAction:"leftAction",rightActions:"rightActions"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:5,vars:2,consts:[[1,"mbo-header-result__content"],[1,"mbo-header-result__left-action"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[1,"mbo-header-result__actions"],[3,"id","type","bocc-button-action","disabled","hidden","click",4,"ngFor","ngForOf"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"],[3,"id","type","bocc-button-action","disabled","hidden","click"]],template:function(C,v){1&C&&(m.\u0275\u0275elementStart(0,"div",0)(1,"div",1),m.\u0275\u0275template(2,d,3,6,"button",2),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(3,"div",3),m.\u0275\u0275template(4,p,1,5,"button",4),m.\u0275\u0275elementEnd()()),2&C&&(m.\u0275\u0275advance(2),m.\u0275\u0275property("ngIf",v.leftAction),m.\u0275\u0275advance(2),m.\u0275\u0275property("ngForOf",v.rightActions))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,e.P8,e.u1],styles:[":root{--mbo-header-result-padding: var(--sizing-x4)}mbo-header-result{position:relative;width:100%;display:block}mbo-header-result .mbo-header-result__content{position:relative;display:flex;width:100%;padding:var(--mbo-header-result-padding);box-sizing:border-box;justify-content:space-between;background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-header-result .mbo-header-result__left-action{--bocc-button-padding: 0rem var(--sizing-x2)}mbo-header-result .mbo-header-result__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}\n"],encapsulation:2}),c})()},27302:(j,I,t)=>{t.d(I,{Vc:()=>n.V,k4:()=>r.k,o2:()=>e.o,B_:()=>y,fi:()=>d.f,XH:()=>p.X,cN:()=>C.c,Aj:()=>v.A,J5:()=>T.J,Nu:()=>N,iF:()=>w});var n=t(35324),r=t(9593),e=t(83867),i=t(17007),o=t(99877);function m(F,B){if(1&F){const P=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"div",2),o.\u0275\u0275listener("click",function(){const oe=o.\u0275\u0275restoreView(P).$implicit,z=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(z.onClickCurrency(oe))}),o.\u0275\u0275elementStart(1,"div",3),o.\u0275\u0275element(2,"img",4)(3,"img",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"label",6),o.\u0275\u0275text(5),o.\u0275\u0275elementEnd()()}if(2&F){const P=B.$implicit,W=o.\u0275\u0275nextContext();o.\u0275\u0275classProp("mbo-currency-toggle__element--enabled",W.isEnabled(P)),o.\u0275\u0275advance(2),o.\u0275\u0275property("src",P.enabledIcon,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(1),o.\u0275\u0275property("src",P.disabledIcon,o.\u0275\u0275sanitizeUrl),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate1(" ",P.label," ")}}t(57544);let y=(()=>{class F{constructor(){this.currencies=[],this.automatic=!0,this.disabled=!1}ngOnInit(){if(this.automatic){const[P]=this.currencies;this.formControl.setValue(P)}}ngOnChanges(P){const{currencies:W}=P;if(W){const[Q]=W.currentValue;this.formControl&&this.formControl.setValue(Q)}}isEnabled(P){return P===this.formControl?.value}onClickCurrency(P){this.formControl&&!this.disabled&&this.formControl.setValue(P)}changeCurriencies(P){if(P.currencies){const W=P.currencies.currentValue,[Q]=W;this.formControl&&this.formControl.setValue(Q)}}}return F.\u0275fac=function(P){return new(P||F)},F.\u0275cmp=o.\u0275\u0275defineComponent({type:F,selectors:[["mbo-currency-toggle"]],inputs:{formControl:"formControl",currencies:"currencies",automatic:"automatic",disabled:"disabled"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature,o.\u0275\u0275StandaloneFeature],decls:2,vars:3,consts:[[1,"mbo-currency-toggle__content"],["class","mbo-currency-toggle__element",3,"mbo-currency-toggle__element--enabled","click",4,"ngFor","ngForOf"],[1,"mbo-currency-toggle__element",3,"click"],[1,"mbo-currency-toggle__avatar"],[1,"mbo-currency-toggle__avatar--enabled",3,"src"],[1,"mbo-currency-toggle__avatar--disabled",3,"src"],[1,"mbo-currency-toggle__label","overline-medium"]],template:function(P,W){1&P&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,m,6,5,"div",1),o.\u0275\u0275elementEnd()),2&P&&(o.\u0275\u0275classProp("mbo-currency-toggle--disabled",W.disabled),o.\u0275\u0275advance(1),o.\u0275\u0275property("ngForOf",W.currencies))},dependencies:[i.CommonModule,i.NgForOf],styles:["/*!\n * MBO CurrencyToggle Component\n * v1.1.0\n * Author: MB Frontend Developers\n * Created: 23/Jul/2022\n * Updated: 20/Dic/2023\n*/mbo-currency-toggle{position:relative;display:block;overflow:hidden;border-radius:var(--sizing-x16);border:var(--border-1) solid var(--color-carbon-lighter-50);box-shadow:var(--z-bottom-lighter-2)}mbo-currency-toggle .mbo-currency-toggle__content{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:var(--gradient-blue-bottom-700)}mbo-currency-toggle .mbo-currency-toggle__content--disabled{opacity:.5;pointer-events:none}mbo-currency-toggle .mbo-currency-toggle__element{position:relative;float:left;width:100%;display:flex;padding:var(--sizing-x1);box-sizing:border-box;background:transparent;color:var(--color-carbon-lighter-50);border-radius:var(--sizing-x16)}mbo-currency-toggle .mbo-currency-toggle__element:last-child:not(:first-child){flex-direction:row-reverse}mbo-currency-toggle .mbo-currency-toggle__element--enabled{background:var(--color-carbon-lighter-50);color:var(--color-carbon-lighter-700)}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--disabled{display:none}mbo-currency-toggle .mbo-currency-toggle__element--enabled .mbo-currency-toggle__avatar--enabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar{width:var(--sizing-x8);height:var(--sizing-x8);overflow:hidden;border-radius:50%}mbo-currency-toggle .mbo-currency-toggle__avatar--enabled{display:none}mbo-currency-toggle .mbo-currency-toggle__avatar--disabled{display:block}mbo-currency-toggle .mbo-currency-toggle__avatar img{width:100%;float:left}mbo-currency-toggle .mbo-currency-toggle__label{padding:0rem var(--sizing-x2);box-sizing:border-box;text-transform:uppercase}\n"],encapsulation:2}),F})();var d=t(85070),p=t(65887),u=t(30263),C=t(78021),v=t(50689),g=(t(7603),t(98699),t(72765)),T=t(88014);function S(F,B){if(1&F&&(o.\u0275\u0275elementStart(0,"div",4),o.\u0275\u0275element(1,"img",5),o.\u0275\u0275elementEnd()),2&F){const P=o.\u0275\u0275nextContext();o.\u0275\u0275advance(1),o.\u0275\u0275property("src",P.src,o.\u0275\u0275sanitizeUrl)}}const O=["*"];let N=(()=>{class F{}return F.\u0275fac=function(P){return new(P||F)},F.\u0275cmp=o.\u0275\u0275defineComponent({type:F,selectors:[["mbo-onboarding-element"]],inputs:{src:"src"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],ngContentSelectors:O,decls:5,vars:1,consts:[[1,"mbo-onboarding-element__content"],["class","mbo-onboarding-element__background",4,"ngIf"],[1,"mbo-onboarding-element__body"],[1,"mbo-onboarding-element__component"],[1,"mbo-onboarding-element__background"],[3,"src"]],template:function(P,W){1&P&&(o.\u0275\u0275projectionDef(),o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275template(1,S,2,1,"div",1),o.\u0275\u0275elementStart(2,"div",2)(3,"div",3),o.\u0275\u0275projection(4),o.\u0275\u0275elementEnd()()()),2&P&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("ngIf",W.src))},dependencies:[i.CommonModule,i.NgIf],styles:["mbo-onboarding-element{position:relative;display:block;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__content{position:relative;width:100%;height:100%}mbo-onboarding-element .mbo-onboarding-element__background{width:100%;overflow:hidden}mbo-onboarding-element .mbo-onboarding-element__background img{width:100%;-webkit-mask-image:linear-gradient(white 97.5%,transparent);mask-image:linear-gradient(white 97.5%,transparent)}mbo-onboarding-element .mbo-onboarding-element__body{position:absolute;width:100%;bottom:0rem;padding:40rem 0rem var(--sizing-x8) 0rem;background:linear-gradient(180deg,transparent 0%,var(--color-navy-900) 75%)}mbo-onboarding-element .mbo-onboarding-element__component{position:relative;display:flex;width:100%;min-height:50rem;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-onboarding-element .mbo-onboarding-element__component label{font-size:var(--subtitle1-size);text-align:center;line-height:var(--subtitle1-line-height);letter-spacing:var(--subtitle1-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-50)}mbo-onboarding-element .mbo-onboarding-element__component p{font-size:var(--smalltext-size);text-align:center;line-height:var(--smalltext-line-height);letter-spacing:var(--smalltext-letter-spacing);color:var(--color-carbon-lighter-50)}\n"],encapsulation:2}),F})();var te=t(24495);const Z=/[A-Z]/,V=/[a-z]/,Y=/[\xa3`~!\xa1\xbf@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,U=F=>F&&!Z.test(F)?{id:"capitalCase",message:"Campo debe tener una may\xfascula"}:null,J=F=>F&&!V.test(F)?{id:"smallCase",message:"Campo debe tener una min\xfascula"}:null,A=F=>F&&!Y.test(F)?{id:"specialChar",message:"Campo debe tener un car\xe1cter especial"}:null;let w=(()=>{class F{constructor(){this.disabled=!1,this.smallInvalid=!0,this.capitalInvalid=!0,this.specialCharInvalid=!0,this.minLengthInvalid=!0}ngOnInit(){this.formControl.setValidators([te.C1,J,U,A,(0,te.Je)(8)]),this.unsubscription=this.formControl.subscribe(()=>{const P=this.formControl.errors.reduce((Q,{id:oe})=>[...Q,oe],[]),W=P.includes("required");this.smallInvalid=P.includes("smallCase")||W,this.capitalInvalid=P.includes("capitalCase")||W,this.specialCharInvalid=P.includes("specialChar")||W,this.minLengthInvalid=P.includes("minlength")||W})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}}return F.\u0275fac=function(P){return new(P||F)},F.\u0275cmp=o.\u0275\u0275defineComponent({type:F,selectors:[["mbo-password-customer"]],inputs:{formControl:"formControl",elementId:"elementId",disabled:"disabled"},standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:11,vars:7,consts:[[1,"mbo-password-customer__content"],["placeholder","Digita tu nueva contrase\xf1a","label","Contrase\xf1a",3,"elementId","disabled","formControl"],[1,"mbo-password-customer__validation"],["icon","lowercase",3,"disabled"],["icon","uppercase",3,"disabled"],["icon","special-character",3,"disabled"],["icon","numerical-character",3,"disabled"]],template:function(P,W){1&P&&(o.\u0275\u0275elementStart(0,"div",0),o.\u0275\u0275element(1,"bocc-password-box",1),o.\u0275\u0275elementStart(2,"div",2)(3,"mbo-poster",3),o.\u0275\u0275text(4," Min\xfascula "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"mbo-poster",4),o.\u0275\u0275text(6," May\xfascula "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(7,"mbo-poster",5),o.\u0275\u0275text(8," Especial "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"mbo-poster",6),o.\u0275\u0275text(10," Caracteres "),o.\u0275\u0275elementEnd()()()),2&P&&(o.\u0275\u0275advance(1),o.\u0275\u0275property("elementId",W.elementId)("disabled",W.disabled)("formControl",W.formControl),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",W.smallInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",W.capitalInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",W.specialCharInvalid),o.\u0275\u0275advance(2),o.\u0275\u0275property("disabled",W.minLengthInvalid))},dependencies:[i.CommonModule,u.sC,g.Z],styles:["mbo-password-customer{position:relative;width:100%;display:block}mbo-password-customer .mbo-password-customer__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-password-customer .mbo-password-customer__validation{position:relative;display:flex;width:100%;justify-content:space-around}mbo-password-customer .mbo-password-customer__validation mbo-poster{--mbo-poster-font-size: 5rem;width:25%}@media screen and (max-width: 375px){mbo-password-customer{--mbo-poster-content-padding: var(--sizing-x4) var(--sizing-x2) var(--sizing-x2) var(--sizing-x2)}}\n"],encapsulation:2}),F})()},50689:(j,I,t)=>{t.d(I,{A:()=>s});var n=t(17007),e=t(99877);const o=["*"];let s=(()=>{class m{constructor(){this.logo="assets/shared/logos/modals/search-result-none.svg"}}return m.\u0275fac=function(d){return new(d||m)},m.\u0275cmp=e.\u0275\u0275defineComponent({type:m,selectors:[["mbo-message-empty"]],inputs:{logo:"logo"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:o,decls:5,vars:1,consts:[[1,"mbo-message-empty__content"],[1,"mbo-message-empty__picture"],[3,"src"],[1,"mbo-message-empty__text","body2-medium"]],template:function(d,p){1&d&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"picture",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"p",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd()()),2&d&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("src",p.logo,e.\u0275\u0275sanitizeUrl))},dependencies:[n.CommonModule],styles:["mbo-message-empty{position:relative;width:100%;display:block}mbo-message-empty .mbo-message-empty__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-message-empty .mbo-message-empty__picture{position:relative;width:100%;display:flex;justify-content:center}mbo-message-empty .mbo-message-empty__picture>img{width:44rem;height:44rem}mbo-message-empty .mbo-message-empty__text{color:var(--color-carbon-lighter-700);text-align:center}\n"],encapsulation:2}),m})()},88014:(j,I,t)=>{t.d(I,{J:()=>o});var n=t(17007),e=t(99877);let o=(()=>{class s{}return s.\u0275fac=function(y){return new(y||s)},s.\u0275cmp=e.\u0275\u0275defineComponent({type:s,selectors:[["mbo-message-greeting"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:5,vars:0,consts:[[1,"mbo-message-greeting__content"],[1,"body1-regular"],[1,"subtitle2-regular"]],template:function(y,d){1&y&&(e.\u0275\u0275elementStart(0,"div",0)(1,"label",1),e.\u0275\u0275text(2,"Bienvenid@ \u{1f44b}"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"span",2),e.\u0275\u0275text(4," \xa1Estamos contigo donde est\xe9s! "),e.\u0275\u0275elementEnd()())},dependencies:[n.CommonModule],styles:["mbo-message-greeting{position:relative;width:100%;display:block}mbo-message-greeting .mbo-message-greeting__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-message-greeting .mbo-message-greeting__content label{color:var(--color-blue-700)}\n"],encapsulation:2}),s})()},21498:(j,I,t)=>{t.d(I,{P:()=>c});var n=t(17007),e=t(30263),i=t(99877);function s(a,C){if(1&a&&i.\u0275\u0275element(0,"bocc-card-product-summary",7),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("color",v.information.product.color)("icon",v.information.product.icon)("number",v.information.product.number)("title",v.information.product.title)("subtitle",v.information.product.subtitle)}}function m(a,C){if(1&a&&i.\u0275\u0275element(0,"bocc-card-summary",8),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",v.information.standard.header)("title",v.information.standard.title)("subtitle",v.information.standard.subtitle)("detail",v.information.standard.detail)}}function y(a,C){if(1&a&&i.\u0275\u0275element(0,"bocc-card-summary",9),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",v.information.amount.header)("amount",v.information.amount.value)("symbol",v.information.amount.symbol)("amountSmall",v.information.amount.small)}}function d(a,C){if(1&a&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",10)(1,"p",11),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",v.information.text.header)("customizedContent",!0),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(v.information.text.content)}}function p(a,C){if(1&a&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",12),i.\u0275\u0275element(1,"bocc-icon",13),i.\u0275\u0275elementStart(2,"span",14),i.\u0275\u0275text(3),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(4,"bocc-icon",15),i.\u0275\u0275elementStart(5,"span",14),i.\u0275\u0275text(6),i.\u0275\u0275elementEnd()()),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",v.information.datetime.header)("customizedContent",!0),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",v.information.datetime.date," "),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",v.information.datetime.time," ")}}function u(a,C){if(1&a&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",16)(1,"bocc-badge"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()),2&a){const v=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",v.information.badge.header)("customizedContent",!0),i.\u0275\u0275advance(1),i.\u0275\u0275attribute("bocc-theme",v.information.badge.color),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",v.information.badge.label," ")}}let c=(()=>{class a{}return a.\u0275fac=function(v){return new(v||a)},a.\u0275cmp=i.\u0275\u0275defineComponent({type:a,selectors:[["mbo-card-information"]],inputs:{information:"information"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:7,vars:7,consts:[[3,"ngSwitch"],["class","mbo-card-information__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information__standard",3,"header","title","subtitle","detail",4,"ngSwitchCase"],["class","mbo-card-information__amount",3,"header","amount","symbol","amountSmall",4,"ngSwitchCase"],["class","mbo-card-information__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information__standard",3,"header","title","subtitle","detail"],[1,"mbo-card-information__amount",3,"header","amount","symbol","amountSmall"],[1,"mbo-card-information__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information__badge",3,"header","customizedContent"]],template:function(v,f){1&v&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,s,1,5,"bocc-card-product-summary",1),i.\u0275\u0275template(2,m,1,4,"bocc-card-summary",2),i.\u0275\u0275template(3,y,1,4,"bocc-card-summary",3),i.\u0275\u0275template(4,d,3,3,"bocc-card-summary",4),i.\u0275\u0275template(5,p,7,4,"bocc-card-summary",5),i.\u0275\u0275template(6,u,3,4,"bocc-card-summary",6),i.\u0275\u0275elementEnd()),2&v&&(i.\u0275\u0275property("ngSwitch",f.information.type),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","product"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","standard"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","amount"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","text"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","datetime"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[n.CommonModule,n.NgSwitch,n.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information{position:relative;width:100%;display:block}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information .mbo-card-information__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),a})()},7427:(j,I,t)=>{t.d(I,{x:()=>c});var n=t(17007),e=t(30263),i=t(87903),s=(t(29306),t(77279)),m=t(87956),y=t(68789),d=t(13961),p=t(99877);let c=(()=>{class a{constructor(v,f){this.eventBusService=v,this.onboardingScreenService=f,this.actions=[]}get msgError(){return this.product.hasErrorPreview?"Error de visualizaci\xf3n":""}onTagAval(){this.product.tagAval&&((0,i.Bn)(this.product.tagAval),this.eventBusService.emit(s.q.ToastSuccess,{message:"P\xe9galo donde quieras compartirlo.",title:"Tag Aval copiado con \xe9xito"}))}onBoarding(){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(d.Z)),this.tagAvalonboarding.open()}}return a.\u0275fac=function(v){return new(v||a)(p.\u0275\u0275directiveInject(m.Yd),p.\u0275\u0275directiveInject(y.x))},a.\u0275cmp=p.\u0275\u0275defineComponent({type:a,selectors:[["mbo-card-product"]],inputs:{product:"product",iconTitle:"iconTitle",incognito:"incognito",actions:"actions"},standalone:!0,features:[p.\u0275\u0275StandaloneFeature],decls:1,vars:17,consts:[[3,"iconTitle","title","icon","tagAval","actions","color","code","label","amount","incognito","displayCard","statusLabel","statusColor","cromaline","msgError","key","onboarding"]],template:function(v,f){1&v&&(p.\u0275\u0275elementStart(0,"bocc-card-product",0),p.\u0275\u0275listener("key",function(){return f.onTagAval()})("onboarding",function(){return f.onBoarding()}),p.\u0275\u0275elementEnd()),2&v&&(p.\u0275\u0275classMap(f.product.bank.className),p.\u0275\u0275property("iconTitle",f.iconTitle)("title",f.product.nickname||f.product.name)("icon",f.product.logo)("tagAval",f.product.tagAvalFormat)("actions",f.actions)("color",f.product.color)("code",f.product.shortNumber)("label",f.product.label)("amount",f.product.amount)("incognito",f.incognito)("displayCard",!0)("statusLabel",null==f.product.status?null:f.product.status.label)("statusColor",null==f.product.status?null:f.product.status.color)("cromaline",!0)("msgError",f.msgError))},dependencies:[n.CommonModule,e.ud],styles:["mbo-card-product{position:relative;width:100%;display:block}\n"],encapsulation:2}),a})()},1027:(j,I,t)=>{t.d(I,{A:()=>v});var n=t(17007),r=t(72765),e=t(30263),i=t(99877);function o(f,h){if(1&f&&i.\u0275\u0275element(0,"bocc-card-product-summary",8),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("color",g.information.product.color)("icon",g.information.product.icon)("number",g.information.product.number)("title",g.information.product.title)("subtitle",g.information.product.subtitle)}}function s(f,h){if(1&f&&i.\u0275\u0275element(0,"bocc-card-summary",9),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.standard.header)("title",g.information.standard.title)("subtitle",g.information.standard.subtitle)}}function m(f,h){if(1&f&&i.\u0275\u0275element(0,"bocc-card-summary",10),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.amount.header)("amount",g.information.amount.value)("symbol",g.information.amount.symbol)}}function y(f,h){if(1&f&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",11)(1,"p",12),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.text.header)("customizedContent",!0),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(g.information.text.content)}}function d(f,h){if(1&f&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",13),i.\u0275\u0275element(1,"bocc-icon",14),i.\u0275\u0275elementStart(2,"span",15),i.\u0275\u0275text(3),i.\u0275\u0275elementEnd(),i.\u0275\u0275element(4,"bocc-icon",16),i.\u0275\u0275elementStart(5,"span",15),i.\u0275\u0275text(6),i.\u0275\u0275elementEnd()()),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.datetime.header)("customizedContent",!0),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",g.information.datetime.date," "),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",g.information.datetime.time," ")}}function p(f,h){if(1&f&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",17),i.\u0275\u0275element(1,"bocc-icon",14),i.\u0275\u0275elementStart(2,"span",15),i.\u0275\u0275text(3),i.\u0275\u0275elementEnd()()),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.date.header)("customizedContent",!0),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",g.information.date.date," ")}}function u(f,h){if(1&f&&(i.\u0275\u0275elementStart(0,"bocc-card-summary",18)(1,"bocc-badge"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()),2&f){const g=i.\u0275\u0275nextContext();i.\u0275\u0275property("header",g.information.badge.header)("customizedContent",!0),i.\u0275\u0275advance(1),i.\u0275\u0275attribute("bocc-theme",g.information.badge.color),i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",g.information.badge.label," ")}}let c=(()=>{class f{}return f.\u0275fac=function(g){return new(g||f)},f.\u0275cmp=i.\u0275\u0275defineComponent({type:f,selectors:[["mbo-card-information-element"]],inputs:{information:"information"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:8,vars:8,consts:[[3,"ngSwitch"],["class","mbo-card-information-element__product",3,"color","icon","number","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__standard",3,"header","title","subtitle",4,"ngSwitchCase"],["class","mbo-card-information-element__amount",3,"header","amount","symbol",4,"ngSwitchCase"],["class","mbo-card-information-element__text",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__datetime",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__date",3,"header","customizedContent",4,"ngSwitchCase"],["class","mbo-card-information-element__badge",3,"header","customizedContent",4,"ngSwitchCase"],[1,"mbo-card-information-element__product",3,"color","icon","number","title","subtitle"],[1,"mbo-card-information-element__standard",3,"header","title","subtitle"],[1,"mbo-card-information-element__amount",3,"header","amount","symbol"],[1,"mbo-card-information-element__text",3,"header","customizedContent"],[1,"smalltext-medium"],[1,"mbo-card-information-element__datetime",3,"header","customizedContent"],["icon","calendar"],[1,"caption-medium"],["icon","alarm-clock"],[1,"mbo-card-information-element__date",3,"header","customizedContent"],[1,"mbo-card-information-element__badge",3,"header","customizedContent"]],template:function(g,_){1&g&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,o,1,5,"bocc-card-product-summary",1),i.\u0275\u0275template(2,s,1,3,"bocc-card-summary",2),i.\u0275\u0275template(3,m,1,3,"bocc-card-summary",3),i.\u0275\u0275template(4,y,3,3,"bocc-card-summary",4),i.\u0275\u0275template(5,d,7,4,"bocc-card-summary",5),i.\u0275\u0275template(6,p,4,3,"bocc-card-summary",6),i.\u0275\u0275template(7,u,3,4,"bocc-card-summary",7),i.\u0275\u0275elementEnd()),2&g&&(i.\u0275\u0275property("ngSwitch",_.information.type),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","product"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","standard"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","amount"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","text"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","datetime"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","date"),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngSwitchCase","badge"))},dependencies:[n.CommonModule,n.NgSwitch,n.NgSwitchCase,e.Zl,e.Oh,e.DM,e.D1],styles:["mbo-card-information-element{position:relative;width:100%;display:block}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>bocc-icon,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>bocc-icon{color:var(--color-blue-700)}mbo-card-information-element .mbo-card-information-element__date .bocc-card-summary__customized>span,mbo-card-information-element .mbo-card-information-element__datetime .bocc-card-summary__customized>span{color:var(--color-carbon-lighter-700);line-height:inherit}\n"],encapsulation:2}),f})();function a(f,h){1&f&&i.\u0275\u0275element(0,"mbo-card-information-element",8),2&f&&i.\u0275\u0275property("information",h.$implicit)}const C=["*"];let v=(()=>{class f{constructor(){this.skeleton=!1,this.informations=[]}}return f.\u0275fac=function(g){return new(g||f)},f.\u0275cmp=i.\u0275\u0275defineComponent({type:f,selectors:[["mbo-card-record-information"]],inputs:{skeleton:"skeleton",informations:"informations"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],ngContentSelectors:C,decls:9,vars:2,consts:[[1,"mbo-card-record-information__content"],[1,"mbo-card-record-information__header"],[1,"mbo-card-record-information__header__content"],[3,"result"],[1,"mbo-card-record-information__divider"],[1,"bocc-divider"],[1,"mbo-card-record-information__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"]],template:function(g,_){1&g&&(i.\u0275\u0275projectionDef(),i.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),i.\u0275\u0275element(3,"mbo-bank-logo",3),i.\u0275\u0275elementEnd()(),i.\u0275\u0275elementStart(4,"div",4),i.\u0275\u0275element(5,"div",5),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(6,"div",6),i.\u0275\u0275projection(7),i.\u0275\u0275template(8,a,1,1,"mbo-card-information-element",7),i.\u0275\u0275elementEnd()()),2&g&&(i.\u0275\u0275advance(3),i.\u0275\u0275property("result",!0),i.\u0275\u0275advance(5),i.\u0275\u0275property("ngForOf",_.informations))},dependencies:[n.CommonModule,n.NgForOf,r.rw,c],styles:["mbo-card-record-information{--pvt-template-border: var(--border-1-lighter-300);position:relative;width:100%;display:block}mbo-card-record-information.snapshot{--pvt-template-border: none}mbo-card-record-information .mbo-card-record-information__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__header{position:relative;width:100%}mbo-card-record-information .mbo-card-record-information__header__content{position:relative;width:100%;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;border:var(--pvt-template-border);border-bottom:none;border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;background:var(--color-carbon-lighter-50)}mbo-card-record-information .mbo-card-record-information__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x4);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-record-information .mbo-card-record-information__divider .bocc-divider{margin:auto 0rem}mbo-card-record-information .mbo-card-record-information__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-record-information .mbo-card-record-information__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}\n"],encapsulation:2}),f})()},16442:(j,I,t)=>{t.d(I,{u:()=>g});var n=t(99877),e=t(17007),o=t(13462),m=t(19102),y=t(45542),d=t(65467),p=t(21498);function u(_,M){if(1&_&&(n.\u0275\u0275elementStart(0,"bocc-skeleton-text",13),n.\u0275\u0275text(1),n.\u0275\u0275elementEnd()),2&_){const b=n.\u0275\u0275nextContext();n.\u0275\u0275classProp("mbo-card-transaction-template__subtitle--skeleton",b.template.skeleton),n.\u0275\u0275property("secondary",!0)("active",b.template.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",b.template.header.subtitle," ")}}function c(_,M){1&_&&n.\u0275\u0275element(0,"mbo-card-information",16),2&_&&n.\u0275\u0275property("information",M.$implicit)}function a(_,M){if(1&_&&(n.\u0275\u0275elementStart(0,"div",14),n.\u0275\u0275projection(1),n.\u0275\u0275template(2,c,1,1,"mbo-card-information",15),n.\u0275\u0275elementEnd()),2&_){const b=n.\u0275\u0275nextContext();n.\u0275\u0275advance(2),n.\u0275\u0275property("ngForOf",b.template.informations)}}function C(_,M){1&_&&(n.\u0275\u0275elementStart(0,"div",17),n.\u0275\u0275element(1,"bocc-skeleton-text",18)(2,"bocc-skeleton-text",19)(3,"bocc-skeleton-text",20),n.\u0275\u0275elementEnd()),2&_&&(n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0)("secondary",!0),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",!0)("secondary",!0))}function v(_,M){if(1&_){const b=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"button",23),n.\u0275\u0275listener("click",function(){const S=n.\u0275\u0275restoreView(b).$implicit,O=n.\u0275\u0275nextContext(2);return n.\u0275\u0275resetView(O.onAction(S))}),n.\u0275\u0275elementStart(1,"span"),n.\u0275\u0275text(2),n.\u0275\u0275elementEnd()()}if(2&_){const b=M.$implicit;n.\u0275\u0275property("bocc-button",b.type)("prefixIcon",b.prefixIcon),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(b.label)}}function f(_,M){if(1&_&&(n.\u0275\u0275elementStart(0,"div",21),n.\u0275\u0275template(1,v,3,3,"button",22),n.\u0275\u0275elementEnd()),2&_){const b=n.\u0275\u0275nextContext();n.\u0275\u0275advance(1),n.\u0275\u0275property("ngForOf",b.template.actions)}}const h=["*"];let g=(()=>{class _{constructor(){this.disabled=!1,this.action=new n.EventEmitter}onAction({event:b}){this.action.emit(b)}}return _.\u0275fac=function(b){return new(b||_)},_.\u0275cmp=n.\u0275\u0275defineComponent({type:_,selectors:[["mbo-card-transaction-template"]],inputs:{template:"template",disabled:"disabled"},outputs:{action:"action"},ngContentSelectors:h,decls:14,vars:10,consts:[[1,"mbo-card-transaction-template__content"],[1,"mbo-card-transaction-template__header"],[1,"mbo-card-transaction-template__header__content"],[3,"result"],[1,"mbo-card-transaction-template__status"],[3,"options"],[1,"mbo-card-transaction-template__title","subtitle2-medium",3,"active"],["class","mbo-card-transaction-template__subtitle body2-medium",3,"mbo-card-transaction-template__subtitle--skeleton","secondary","active",4,"ngIf"],[1,"mbo-card-transaction-template__divider"],[1,"bocc-divider"],["class","mbo-card-transaction-template__body",4,"ngIf"],["class","mbo-card-transaction-template__skeleton",4,"ngIf"],["class","mbo-card-transaction-template__footer",4,"ngIf"],[1,"mbo-card-transaction-template__subtitle","body2-medium",3,"secondary","active"],[1,"mbo-card-transaction-template__body"],[3,"information",4,"ngFor","ngForOf"],[3,"information"],[1,"mbo-card-transaction-template__skeleton"],[1,"mbo-card-transaction-template__skeleton__title",3,"active"],[1,"mbo-card-transaction-template__skeleton__subtitle",3,"active","secondary"],[1,"mbo-card-transaction-template__skeleton__detail",3,"active","secondary"],[1,"mbo-card-transaction-template__footer"],[3,"bocc-button","prefixIcon","click",4,"ngFor","ngForOf"],[3,"bocc-button","prefixIcon","click"]],template:function(b,l){1&b&&(n.\u0275\u0275projectionDef(),n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"mbo-bank-logo",3),n.\u0275\u0275elementStart(4,"div",4),n.\u0275\u0275element(5,"ng-lottie",5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"bocc-skeleton-text",6),n.\u0275\u0275text(7),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(8,u,2,5,"bocc-skeleton-text",7),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(9,"div",8),n.\u0275\u0275element(10,"div",9),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(11,a,3,1,"div",10),n.\u0275\u0275template(12,C,4,5,"div",11),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(13,f,2,1,"div",12)),2&b&&(n.\u0275\u0275classProp("animation",!l.template.skeleton),n.\u0275\u0275advance(3),n.\u0275\u0275property("result",!0),n.\u0275\u0275advance(2),n.\u0275\u0275property("options",l.template.header.animation),n.\u0275\u0275advance(1),n.\u0275\u0275property("active",l.template.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",l.template.header.title," "),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.template.skeleton||l.template.header.subtitle),n.\u0275\u0275advance(3),n.\u0275\u0275property("ngIf",!l.template.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.template.skeleton),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",l.template.actions.length&&!l.disabled))},dependencies:[e.NgForOf,e.NgIf,o.LottieComponent,m.r,y.P,d.D,p.P],styles:["mbo-card-transaction-template{--pvt-template-border: var(--border-1-lighter-300);--pvt-header-border-radius: var(--sizing-x8) var(--sizing-x8) 0rem 0rem;--pvt-body-border-radius: 0rem 0rem var(--sizing-x8) var(--sizing-x8);--pvt-footer-display: flex;position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-card-transaction-template.snapshot{--pvt-template-border: none;--pvt-header-border-radius: 0rem;--pvt-body-border-radius: 0rem;--pvt-footer-display: none}mbo-card-transaction-template .mbo-card-transaction-template__content{position:relative;width:100%;background:var(--color-carbon-lighter-50)}mbo-card-transaction-template .mbo-card-transaction-template__header{position:relative;width:100%}mbo-card-transaction-template .mbo-card-transaction-template__header__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);align-items:center;padding:var(--sizing-x12) var(--sizing-x4) var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-lighter-50);border:var(--pvt-template-border);border-bottom:none;border-radius:var(--pvt-header-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24);margin:var(--sizing-x4) calc(50% - var(--sizing-x12))}mbo-card-transaction-template .mbo-card-transaction-template__title{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-darker-1000)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle{width:calc(100% - var(--sizing-x8));text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-transaction-template .mbo-card-transaction-template__subtitle--skeleton{width:50%;max-height:var(--body2-line-height)}mbo-card-transaction-template .mbo-card-transaction-template__divider{position:relative;display:flex;width:100%;height:var(--sizing-x8);overflow:hidden;padding:0rem var(--sizing-x3);box-sizing:border-box;border-left:var(--border-1-lighter-300);border-right:var(--border-1-lighter-300)}mbo-card-transaction-template .mbo-card-transaction-template__divider .bocc-divider{margin:auto 0rem}mbo-card-transaction-template .mbo-card-transaction-template__divider:before{position:absolute;display:block;top:0rem;left:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__divider:after{position:absolute;display:block;top:0rem;right:calc(var(--sizing-x4) * -1);width:var(--sizing-x8);height:var(--sizing-x8);background:var(--color-carbon-lighter-200);clip-path:circle(50% at 50%);box-shadow:inset 0 0 var(--sizing-x2) #adbae666}mbo-card-transaction-template .mbo-card-transaction-template__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8) var(--sizing-x4);box-sizing:border-box;overflow:hidden;border:var(--pvt-template-border);border-top:none;border-radius:var(--pvt-body-border-radius)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-x12) var(--sizing-x8);box-sizing:border-box;overflow:hidden;border:var(--border-1-lighter-300);border-top:none;border-radius:0rem 0rem var(--sizing-x8) var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__title{width:80%;min-height:var(--sizing-x12)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__subtitle{width:30%;min-height:var(--sizing-x10)}mbo-card-transaction-template .mbo-card-transaction-template__skeleton__detail{width:50%;min-height:var(--sizing-x8)}mbo-card-transaction-template .mbo-card-transaction-template__footer{position:relative;display:var(--pvt-footer-display);width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-transaction-template .mbo-card-transaction-template__footer button{width:100%}\n"],encapsulation:2}),_})()},10119:(j,I,t)=>{t.d(I,{N:()=>C});var n=t(17007),e=t(99877),o=t(30263),s=t(7603),m=t(98699);function d(v,f){if(1&v&&e.\u0275\u0275element(0,"bocc-diamond",14),2&v){const h=f.$implicit,g=e.\u0275\u0275nextContext();e.\u0275\u0275property("active",g.itIsSelected(h))}}function p(v,f){if(1&v){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(h);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onAction(_.footerActionLeft))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&v){const h=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",h.footerActionLeft.id)("bocc-button",h.footerActionLeft.type)("prefixIcon",h.footerActionLeft.prefixIcon)("disabled",h.itIsDisabled(h.footerActionLeft))("hidden",h.itIsHidden(h.footerActionLeft)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(h.footerActionLeft.label)}}function u(v,f){if(1&v){const h=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(h);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onAction(_.footerActionRight))}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&v){const h=e.\u0275\u0275nextContext();e.\u0275\u0275property("id",h.footerActionRight.id)("bocc-button",h.footerActionRight.type)("prefixIcon",h.footerActionRight.prefixIcon)("disabled",h.itIsDisabled(h.footerActionRight))("hidden",h.itIsHidden(h.footerActionRight)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(h.footerActionRight.label)}}const c=["*"];let C=(()=>{class v{constructor(h,g){this.ref=h,this.utagService=g,this.gradient=!1,this.currentPosition=0,this.automatic=!0,this.finished=!1,this.translateX=0,this.touched=!1,this.initialized=!1,this.transformContent="translateX(0)",this.widthContent="0px",this.diamonds=[],this.position=new e.EventEmitter}ngAfterViewInit(){setTimeout(()=>{this.bodyElement=this.ref.nativeElement.querySelector(".mbo-onboarding-carousel__body"),this.elements=this.bodyElement.querySelectorAll("mbo-onboarding-element"),this.widthContent=this.widthContentValue,this.diamonds=Array(this.elements.length).fill("").map((h,g)=>g),this.initialized=!0,this.emitPosition(0),(this.elements?.length||0)>1?(this.nextPositionAutomatic(),this.setTouchHandler(this.bodyElement.querySelector(".mbo-onboarding-carousel__elements"))):this.automatic=!1},0)}get widthBody(){return this.bodyElement?.offsetWidth||0}get widthContentValue(){return(this.elements?.length||0)*this.widthBody+"px"}get canPrevious(){return this.currentPosition>0}get canNext(){return this.currentPosition<(this.elements?.length||0)-1}itIsSelected(h){return h===this.currentPosition}itIsDisabled({disabled:h}){return(0,m.evalValueOrFunction)(h)}itIsHidden({hidden:h}){return(0,m.evalValueOrFunction)(h)}onResize(){this.widthContent=this.widthContentValue,this.setTranslatePosition(this.currentPosition)}onAction(h){const{id:g}=h;g&&this.utagService.link("click",g),h.click()}onPrevious(){this.currentPosition--,this.renderForPosition(this.currentPosition)}onNext(){this.currentPosition++,this.renderForPosition(this.currentPosition)}nextPositionAutomatic(){this.automatic&&(this.timeoutId=setTimeout(()=>{this.currentPosition++,this.setTranslatePosition(this.currentPosition),this.emitPosition(this.currentPosition),this.canNext?this.nextPositionAutomatic():this.automatic=!1},7500))}renderForPosition(h){this.timeoutId&&clearTimeout(this.timeoutId),this.emitPosition(h),this.automatic=!1,this.setTranslatePosition(h)}setTranslatePosition(h){this.translateX=h*this.widthBody*-1,this.transformContent=`translateX(${this.translateX}px)`}setTranslateContent(h){this.transformContent=`translateX(${h}px)`}emitPosition(h){this.finished||(this.finished=h+1===this.elements.length),this.position.emit({position:h,finished:this.finished})}getPositionSlide(h){return h>=this.elements.length?this.elements.length-1:h<0?0:h}setTouchHandler(h){let g=0,_=0;h.addEventListener("touchstart",M=>{if(M.changedTouches.length){const{clientX:b}=M.changedTouches.item(0);g=0,this.touched=!0,_=b}}),h.addEventListener("touchmove",M=>{if(M.changedTouches.length){const b=M.changedTouches.item(0),l=b.clientX-_;_=b.clientX,this.translateX+=l,g+=l,this.setTranslateContent(this.translateX)}}),h.addEventListener("touchend",M=>{this.touched=!1,M.changedTouches.length&&(Math.abs(g)/this.widthBody*100>=40&&(g>0?this.currentPosition--:this.currentPosition++,this.currentPosition=this.getPositionSlide(this.currentPosition)),this.renderForPosition(this.currentPosition))})}}return v.\u0275fac=function(h){return new(h||v)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(s.D))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["mbo-onboarding-carousel"]],inputs:{headerActionRight:"headerActionRight",headerActionLeft:"headerActionLeft",footerActionRight:"footerActionRight",footerActionLeft:"footerActionLeft",gradient:"gradient"},outputs:{position:"position"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:c,decls:17,vars:17,consts:[["bocc-buttons-theme","white",1,"mbo-onboarding-carousel__component",3,"resize"],[1,"mbo-onboarding-carousel__header"],[3,"leftAction","rightAction"],[1,"mbo-onboarding-carousel__content"],[1,"mbo-onboarding-carousel__body"],[1,"mbo-onboarding-carousel__elements"],[1,"mbo-onboarding-carousel__actions"],[1,"mbo-onboarding-carousel__actions__button"],["bocc-button-action","prev-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__actions__dots"],[3,"active",4,"ngFor","ngForOf"],["bocc-button-action","next-page",3,"hidden","click"],[1,"mbo-onboarding-carousel__footer"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click",4,"ngIf"],[3,"active"],[3,"id","bocc-button","prefixIcon","disabled","hidden","click"]],template:function(h,g){1&h&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("resize",function(){return g.onResize()},!1,e.\u0275\u0275resolveWindow),e.\u0275\u0275elementStart(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"div",5),e.\u0275\u0275projection(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",6)(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return g.onPrevious()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",9),e.\u0275\u0275template(11,d,1,1,"bocc-diamond",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",7)(13,"button",11),e.\u0275\u0275listener("click",function(){return g.onNext()}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",12),e.\u0275\u0275template(15,p,3,6,"button",13),e.\u0275\u0275template(16,u,3,6,"button",13),e.\u0275\u0275elementEnd()()),2&h&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",g.headerActionLeft)("rightAction",g.headerActionRight),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("initialized",g.initialized),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("gradient",g.gradient),e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("width",g.widthContent)("transform",g.transformContent),e.\u0275\u0275classProp("mbo-onboarding-carousel__elements--touched",g.touched),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",!g.canPrevious),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",g.diamonds),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!g.canNext),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",g.footerActionLeft),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.footerActionRight))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,o.P8,o.u1,o.ou,o.Jx],styles:["mbo-onboarding-carousel{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__component{position:relative;display:flex;width:100%;height:100%;flex-direction:column}mbo-onboarding-carousel .mbo-onboarding-carousel__header{position:absolute;width:100%;min-height:30rem;z-index:var(--z-index-2);padding:calc(var(--sizing-x2) + var(--sizing-safe-top)) 0rem var(--sizing-x8) 0rem;background:linear-gradient(0deg,transparent 5%,rgba(2,32,71,.77) 70%,rgb(2,32,71))}mbo-onboarding-carousel .mbo-onboarding-carousel__header .bocc-header-form__action{width:35%}mbo-onboarding-carousel .mbo-onboarding-carousel__content{display:flex;flex-direction:column;height:calc(100vh - 32rem - var(--sizing-safe-bottom));visibility:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__content.initialized{visibility:visible}mbo-onboarding-carousel .mbo-onboarding-carousel__body{height:calc(100vh - 28rem);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__body.gradient{background:linear-gradient(90deg,#012e65 0%,#0063dc 100%)}mbo-onboarding-carousel .mbo-onboarding-carousel__elements{display:flex;height:100%;transition:transform .24s var(--acceleration-curve);overflow:hidden}mbo-onboarding-carousel .mbo-onboarding-carousel__elements--touched{transition:none}mbo-onboarding-carousel .mbo-onboarding-carousel__actions{display:flex;justify-content:space-between;padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__dots{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2);align-items:center}mbo-onboarding-carousel .mbo-onboarding-carousel__actions__button{min-width:var(--sizing-x20)}mbo-onboarding-carousel .mbo-onboarding-carousel__footer{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x8) calc(var(--sizing-x8) + var(--sizing-safe-bottom)) var(--sizing-x8);box-sizing:border-box}mbo-onboarding-carousel .mbo-onboarding-carousel__footer button{width:100%}\n"],encapsulation:2}),v})()},68789:(j,I,t)=>{t.d(I,{x:()=>s});var n=t(7603),r=t(10455),e=t(87677),i=t(99877);let s=(()=>{class m{constructor(d){this.portalService=d}information(){this.portal||(this.portal=this.portalService.container({component:r.E,container:e.C,props:{component:{fromPortal:!0}}})),this.portal.open()}create(d,p){return this.portalService.container({component:d,container:e.C,props:{container:p?.containerProps,component:p?.componentProps}})}}return m.\u0275fac=function(d){return new(d||m)(i.\u0275\u0275inject(n.v))},m.\u0275prov=i.\u0275\u0275defineInjectable({token:m,factory:m.\u0275fac,providedIn:"root"}),m})()},87677:(j,I,t)=>{t.d(I,{C:()=>e});var n=t(99877);let e=(()=>{class i{constructor(s){this.ref=s,this.visible=!1,this.visibleChange=new n.EventEmitter}open(s=0){setTimeout(()=>{this.changeVisible(!0)},s)}close(s=0){setTimeout(()=>{this.changeVisible(!1),this.portal?.reject()},s)}append(s){this.component=this.ref.nativeElement.querySelector(".mbo-onboarding-screen__component"),this.component.appendChild(s)}ngBoccPortal(s){this.portal=s}changeVisible(s){this.visible=s,this.visibleChange.emit(s)}}return i.\u0275fac=function(s){return new(s||i)(n.\u0275\u0275directiveInject(n.ElementRef))},i.\u0275cmp=n.\u0275\u0275defineComponent({type:i,selectors:[["mbo-onboarding-screen"]],inputs:{visible:"visible"},outputs:{visibleChange:"visibleChange"},decls:2,vars:2,consts:[[1,"mbo-onboarding-screen__container"],[1,"mbo-onboarding-screen__component"]],template:function(s,m){1&s&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"div",1),n.\u0275\u0275elementEnd()),2&s&&n.\u0275\u0275classProp("mbo-onboarding-screen__container--visible",m.visible)},styles:["mbo-onboarding-screen{--pvt-body-padding-top: calc(20rem + var(--sizing-safe-top, 0rem))}mbo-onboarding-screen .mbo-onboarding-screen__container{position:fixed;width:100%;height:100%;top:0rem;left:0rem;visibility:hidden;z-index:var(--z-index-32);background:var(--color-navy-900);will-change:transform;transform:translateY(100%);transition:all .24s 0ms var(--acceleration-curve)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible{visibility:visible;transform:translateY(0)}mbo-onboarding-screen .mbo-onboarding-screen__container--visible .mbo-onboarding-screen__component{visibility:visible;opacity:1}mbo-onboarding-screen .mbo-onboarding-screen__component{position:absolute;width:100%;height:100%;opacity:0;visibility:hidden;z-index:var(--z-index-4)}\n"],encapsulation:2}),i})()},10455:(j,I,t)=>{t.d(I,{E:()=>m});var n=t(17007),e=t(99877),o=t(27302),s=t(10119);let m=(()=>{class y{constructor(){this.finished=!1,this.fromPortal=!1,this.close=new e.EventEmitter,this.footerLeft={id:"btn_onboarding_skip",label:"Omitir",type:"flat",hidden:()=>this.fromPortal,click:()=>{this.close.emit("finish"),this.portal?.close()}},this.footerRight={id:"btn_onboarding_after",type:"outline",label:"Verlo luego",hidden:()=>this.finished,click:()=>{this.close.emit("skip"),this.portal?.close()}}}ngOnInit(){this.fromPortal&&(this.footerRight={id:"btn_onboarding_cerrar",type:"outline",label:"Cerrar",click:()=>{this.portal?.close()}})}ngBoccPortal(p){this.portal=p}onPosition({finished:p}){this.finished=p,p&&!this.fromPortal&&(this.footerLeft={id:"btn_onboarding_finish",label:"Entendido",type:"raised",click:()=>{this.close.emit("finish"),this.portal?.close()}})}}return y.\u0275fac=function(p){return new(p||y)},y.\u0275cmp=e.\u0275\u0275defineComponent({type:y,selectors:[["mbo-onboarding"]],outputs:{close:"close"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:6,vars:3,consts:[[3,"footerActionLeft","footerActionRight","gradient","position"],["src","assets/shared/onboardings/caps/onboarding-inicial-topes-1.png"]],template:function(p,u){1&p&&(e.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0),e.\u0275\u0275listener("position",function(a){return u.onPosition(a)}),e.\u0275\u0275elementStart(1,"mbo-onboarding-element",1)(2,"label"),e.\u0275\u0275text(3,"Visualizaci\xf3n de topes consumidos"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p"),e.\u0275\u0275text(5," Ahora podr\xe1s ver f\xe1cilmente cu\xe1nto has consumido de tus topes digitales en cada transacci\xf3n. Mantente al tanto de tus l\xedmites con solo un vistazo y evita rechazos. "),e.\u0275\u0275elementEnd()()()),2&p&&e.\u0275\u0275property("footerActionLeft",u.footerLeft)("footerActionRight",u.footerRight)("gradient",!0)},dependencies:[n.CommonModule,o.Nu,s.N],styles:["mbo-onboarding{position:relative;display:flex;height:100%;background:var(--color-navy-900)}\n"],encapsulation:2}),y})()},91642:(j,I,t)=>{t.d(I,{D:()=>g});var n=t(17007),e=t(99877),o=t(30263),s=t(87542),m=t(70658),y=t(3372),d=t(87956),p=t(72765);function u(_,M){1&_&&e.\u0275\u0275element(0,"mbo-bank-logo")}function c(_,M){1&_&&(e.\u0275\u0275elementStart(0,"div",10),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd())}function a(_,M){if(1&_&&(e.\u0275\u0275elementStart(0,"button",11)(1,"span"),e.\u0275\u0275text(2,"\xbfNo lo recibiste? Reenviar clave"),e.\u0275\u0275elementEnd()()),2&_){const b=e.\u0275\u0275nextContext();e.\u0275\u0275property("disabled",b.verifying)}}function C(_,M){1&_&&(e.\u0275\u0275elementStart(0,"div",12)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, ver\xe1s una solicitud de permiso de tu dispositivo como se muestra en la imagen de ejemplo. Pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Permitir"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5," para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"img",13),e.\u0275\u0275elementEnd())}function v(_,M){if(1&_){const b=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"p"),e.\u0275\u0275text(2," Cuando llegue, pulsa el bot\xf3n "),e.\u0275\u0275elementStart(3,"b"),e.\u0275\u0275text(4,'"Mostrar Clave"'),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(5,", esto desplegar\xe1 tu teclado en cuya parte superior estar\xe1 la clave. Haz tap sobre esta para ingresar el c\xf3digo autom\xe1ticamente. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(b);const T=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(T.onShowKeyboard())}),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8,"Mostrar clave"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",16),e.\u0275\u0275elementEnd()}if(2&_){const b=e.\u0275\u0275nextContext();e.\u0275\u0275advance(6),e.\u0275\u0275property("disabled",b.verifying)}}const f=["*"],{OtpInputSuperuser:h}=y.M;let g=(()=>{class _{constructor(b,l,T,S){this.ref=b,this.otpService=l,this.deviceService=T,this.preferencesService=S,this.verifying=!1,this.title=!0,this.bankLogo=!1,this.documentNumber="",this.itIsDocumentSuperuser=!1,this.isAndroid=!1,this.isIos=!1,this.otpLength=s.cq,this.isIos=this.deviceService.controller.itIsIos,this.isAndroid=this.deviceService.controller.itIsAndroid,this.code=new e.EventEmitter,this.otpControls=new s.yV}ngOnInit(){this.otpService.onCode(b=>{this.otpControls.setCode(b),this.otpControls.valid&&this.onAutocomplete(b)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}ngOnChanges(b){const{documentNumber:l}=b;l&&this.preferencesService.applyFunctionality(h,l.currentValue).then(T=>{this.itIsDocumentSuperuser=T})}get otpVisible(){return!m.N.production||this.itIsDocumentSuperuser||this.verifying}get otpReadonly(){return m.N.production&&!this.itIsDocumentSuperuser&&this.deviceService.controller.itIsMobile&&this.isAndroid}get otpMobile(){return!this.itIsDocumentSuperuser&&m.N.otpReadonlyMobile}onAutocomplete(b){this.code.emit(b)}onShowKeyboard(){this.isIos&&this.ref.nativeElement.querySelector("bocc-otp-box")?.focus()}}return _.\u0275fac=function(b){return new(b||_)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(d.no),e.\u0275\u0275directiveInject(d.U8),e.\u0275\u0275directiveInject(d.yW))},_.\u0275cmp=e.\u0275\u0275defineComponent({type:_,selectors:[["mbo-otp-form"]],inputs:{otpControls:"otpControls",verifying:"verifying",title:"title",bankLogo:"bankLogo",documentNumber:"documentNumber"},outputs:{code:"code"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:f,decls:14,vars:11,consts:[[1,"mbo-otp-form__content"],[1,"mbo-otp-form__body"],[4,"ngIf"],["class","mbo-otp-form__title subtitle2-medium",4,"ngIf"],[1,"mbo-otp-form__message"],["id","txt_otp-form_input",3,"formControls","readonly","mobile","disabled","autocomplete"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled",4,"ngIf"],[1,"mbo-otp-form__helper"],["class","mbo-otp-form__helper__content",4,"ngIf"],["class","mbo-otp-form__helper__content","bocc-buttons-theme","white",4,"ngIf"],[1,"mbo-otp-form__title","subtitle2-medium"],["id","btn_otp-form_resend","bocc-button","flat","prefixIcon","chat-message",3,"disabled"],[1,"mbo-otp-form__helper__content"],["src","assets/shared/imgs/sms-android.png",1,"android"],["bocc-buttons-theme","white",1,"mbo-otp-form__helper__content"],["id","btn_otp-form_show-keyboard","bocc-button","raised",3,"disabled","click"],["src","assets/shared/imgs/keyboard-ios.png"]],template:function(b,l){1&b&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275template(2,u,1,0,"mbo-bank-logo",2),e.\u0275\u0275template(3,c,2,0,"div",3),e.\u0275\u0275elementStart(4,"p",4),e.\u0275\u0275text(5," Enviamos un SMS con una clave temporal de seguridad a tu celular "),e.\u0275\u0275elementStart(6,"b"),e.\u0275\u0275text(7,"registrado"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"bocc-otp-box",5),e.\u0275\u0275listener("autocomplete",function(S){return l.onAutocomplete(S)}),e.\u0275\u0275text(9," Ingresa tu clave "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,a,3,1,"button",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",7),e.\u0275\u0275template(12,C,7,0,"div",8),e.\u0275\u0275template(13,v,10,1,"div",9),e.\u0275\u0275elementEnd()()),2&b&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",l.bankLogo),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.title),e.\u0275\u0275advance(5),e.\u0275\u0275classProp("mbo-otp-form--visible",l.otpVisible),e.\u0275\u0275property("formControls",l.otpControls)("readonly",l.otpReadonly)("mobile",l.otpMobile)("disabled",l.verifying),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",l.isAndroid),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.isIos))},dependencies:[n.CommonModule,n.NgIf,o.P8,o.Yx,p.rw],styles:["mbo-otp-form{--pvt-logo-multiple-margin: .5;--pvt-otp-opacity: 0;--pvt-title-font-size: var(--subtitle1-size);--pvt-helper-font-size: var(--body2-size);--pvt-helper-line-height: var(--body2-line-height);--pvt-helper-img-width: 120rem;position:relative;width:100%;display:block}mbo-otp-form .mbo-otp-form--visible{--pvt-otp-opacity: 1}mbo-otp-form .mbo-otp-form__content{position:relative;display:flex;width:100%;height:100%;justify-content:space-between;flex-direction:column;row-gap:var(--mbo-otp-form-content-rowgap)}mbo-otp-form .mbo-otp-form__content mbo-bank-logo{margin-bottom:calc(var(--mbo-otp-form-body-rowgap) * var(--pvt-logo-multiple-margin))}mbo-otp-form .mbo-otp-form__body{position:relative;display:flex;flex-direction:column;row-gap:var(--mbo-otp-form-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-otp-form .mbo-otp-form__body bocc-otp-box{width:100%;opacity:var(--pvt-otp-opacity)}mbo-otp-form .mbo-otp-form__body button{width:100%}mbo-otp-form .mbo-otp-form__title{position:relative;width:100%;text-align:center;font-size:var(--pvt-title-font-size);letter-spacing:var(--subtitle1-letter-spacing);min-height:var(--subtitle1-line-height);line-height:var(--subtitle1-line-height)}mbo-otp-form .mbo-otp-form__message{position:relative;text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium);color:var(--color-carbon-lighter-700)}mbo-otp-form .mbo-otp-form__helper{padding:var(--sizing-x8) var(--sizing-x8) var(--sizing-safe-bottom-x8) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4) var(--sizing-x4) 0rem 0rem;background:var(--gradient-blue-bottom-900)}mbo-otp-form .mbo-otp-form__helper__content{display:flex;flex-direction:column;row-gap:var(--sizing-x8);align-items:center}mbo-otp-form .mbo-otp-form__helper__content p{color:var(--color-carbon-lighter-50);text-align:center;font-size:var(--pvt-helper-font-size);line-height:var(--pvt-helper-line-height);letter-spacing:var(--body2-letter-spacing);font-weight:var(--font-weight-medium)}mbo-otp-form .mbo-otp-form__helper__content img{max-width:var(--pvt-helper-img-width)}mbo-otp-form .mbo-otp-form__helper__content img.android{border-radius:var(--sizing-x4);max-width:80%;overflow:hidden}mbo-otp-form .mbo-otp-form__helper__content button{width:100%}@media screen and (max-height: 800px){mbo-otp-form{--pvt-helper-font-size: var(--smalltext-size);--pvt-helper-line-height: var(--smalltext-line-height);--pvt-helper-img-width: 110rem}}@media screen and (max-height: 700px){mbo-otp-form{--pvt-title-font-size: var(--subtitle2-size);--pvt-helper-line-height: 9rem;--pvt-helper-img-width: 105rem}}@media screen and (max-height: 600px){mbo-otp-form{--pvt-helper-font-size: var(--caption-size);--pvt-helper-img-width: 100rem;--pvt-logo-multiple-margin: 1}}\n"],encapsulation:2}),_})()},10464:(j,I,t)=>{t.d(I,{K:()=>m});var n=t(17007),e=t(99877),o=t(22816);const s=["*"];let m=(()=>{class y{constructor(p){this.ref=p,this.scroller=new o.S}ngOnInit(){this.contentElement=this.ref.nativeElement.querySelector(".mbo-page__content"),this.scrollerElement=this.ref.nativeElement.querySelector(".mbo-page__scroller")}ngAfterContentInit(){this.resizeScroller=new ResizeObserver(()=>{this.scroller.reset(this.contentElement)}),this.scrollerElement&&this.resizeScroller.observe(this.scrollerElement)}ngOnDestroy(){this.resizeScroller.disconnect()}get scrollStart(){return this.scroller.verticalStart}get scrollEnd(){return this.scroller.verticalEnd}onScroll(p){this.scroller.reset(p.target)}}return y.\u0275fac=function(p){return new(p||y)(e.\u0275\u0275directiveInject(e.ElementRef))},y.\u0275cmp=e.\u0275\u0275defineComponent({type:y,selectors:[["mbo-page"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:2,vars:4,consts:[[1,"mbo-page__content",3,"scroll"]],template:function(p,u){1&p&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("scroll",function(a){return u.onScroll(a)}),e.\u0275\u0275projection(1),e.\u0275\u0275elementEnd()),2&p&&e.\u0275\u0275classProp("mbo-page__content--start",u.scrollStart)("mbo-page__content--end",u.scrollEnd)},dependencies:[n.CommonModule],styles:["/*!\n * BOCC Page Component\n * v2.0.3\n * Author: MB Frontend Developers\n * Created: 12/Oct/2022\n * Updated: 09/Jul/2024\n*/mbo-page{--page-header-shadow: var(--z-bottom-lighter-4);--page-footer-shadow: var(--z-top-lighter-4);position:relative;width:100%;height:100%;display:block}mbo-page .mbo-page__content{position:relative;display:flex;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-page .mbo-page__content--start{--page-header-shadow: none}mbo-page .mbo-page__content--end{--page-footer-shadow: none}mbo-page .mbo-page__header{position:sticky;top:0rem;width:100%;z-index:var(--z-index-8)}mbo-page .mbo-page__footer{position:sticky;bottom:0rem;width:100%;z-index:var(--z-index-8)}\n"],encapsulation:2}),y})()},75221:(j,I,t)=>{t.d(I,{u:()=>y});var n=t(17007),e=t(30263),i=t(27302),s=(t(88649),t(99877));let y=(()=>{class d{constructor(){this.disabled=!1,this.elementPasswordId="",this.elementConfirmId=""}ngOnInit(){this.pageId&&(this.elementPasswordId=`pwd_${this.pageId}_current`,this.elementConfirmId=`pwd_${this.pageId}_repeat`)}}return d.\u0275fac=function(u){return new(u||d)},d.\u0275cmp=s.\u0275\u0275defineComponent({type:d,selectors:[["mbo-password-form"]],inputs:{passwordControl:"passwordControl",pageId:"pageId",disabled:"disabled"},standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:2,vars:6,consts:[[3,"formControl","disabled","elementId"],["placeholder","Confirmar tu contrase\xf1a","label","Confirmar contrase\xf1a",3,"elementId","disabled","formControl"]],template:function(u,c){1&u&&s.\u0275\u0275element(0,"mbo-password-customer",0)(1,"bocc-password-box",1),2&u&&(s.\u0275\u0275property("formControl",c.passwordControl.controls.password)("disabled",c.disabled)("elementId",c.elementPasswordId),s.\u0275\u0275advance(1),s.\u0275\u0275property("elementId",c.elementConfirmId)("disabled",c.disabled)("formControl",c.passwordControl.controls.repeatPassword))},dependencies:[n.CommonModule,e.sC,i.iF],styles:["mbo-password-form{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);width:100%}mbo-password-form bocc-password-customer{width:100%}mbo-password-form bocc-password-box{width:100%}\n"],encapsulation:2}),d})()},88649:(j,I,t)=>{t.d(I,{z:()=>i});var n=t(57544),r=t(24495);class i extends n.FormGroup{constructor(){const s=new n.FormControl(""),m=new n.FormControl("",[r.C1,(o=s,s=>s&&s!==o.value?{id:"passwordRepeat",message:"Las contrase\xf1as no coinciden"}:null)]);var o;super({controls:{password:s,repeatPassword:m}})}get password(){return this.controls.password.value}}},13043:(j,I,t)=>{t.d(I,{e:()=>v});var n=t(17007),e=t(99877),o=t(30263),y=(t(57544),t(27302));function d(f,h){1&f&&(e.\u0275\u0275elementStart(0,"div",4)(1,"p",5),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function p(f,h){if(1&f&&(e.\u0275\u0275elementStart(0,"div",11),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&f){const g=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.title," ")}}function u(f,h){if(1&f){const g=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",12),e.\u0275\u0275listener("click",function(){const b=e.\u0275\u0275restoreView(g).$implicit,l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.onProduct(b))}),e.\u0275\u0275elementEnd()}if(2&f){const g=h.$implicit,_=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("color",g.color)("icon",g.logo)("title",g.nickname)("number",g.publicNumber)("detail",g.bank.name)("ghost",_.ghost)}}function c(f,h){if(1&f&&(e.\u0275\u0275elementStart(0,"div",6)(1,"div",7),e.\u0275\u0275template(2,p,2,1,"div",8),e.\u0275\u0275template(3,u,1,6,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"mbo-message-empty",10),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&f){const g=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",g.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",g.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",g.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!g.hasMessage),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",g.msgError," ")}}function a(f,h){1&f&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"bocc-card-product-selector",14)(2,"bocc-card-product-selector",14),e.\u0275\u0275elementEnd()),2&f&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))}const C=["*"];let v=(()=>{class f{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.automaticSelection=!1,this.msgError="Lo sentimos, por el momento no cuentas con productos para realizar transacciones.",this.ghost=!1,this.select=new e.EventEmitter}get hasMessage(){return 0===this.products.length&&!this.skeleton}checked(g){return this.productControl?.value?.id===g.id}onProduct(g){this.select.emit(g),this.productControl?.setValue(g)}}return f.\u0275fac=function(g){return new(g||f)},f.\u0275cmp=e.\u0275\u0275defineComponent({type:f,selectors:[["mbo-product-destination-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",automaticSelection:"automaticSelection",productControl:"productControl",msgError:"msgError",ghost:"ghost"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:C,decls:4,vars:3,consts:[[1,"mbo-product-destination-selector__content"],["class","mbo-product-destination-selector__header",4,"ngIf"],["class","mbo-product-destination-selector__component",4,"ngIf"],["class","mbo-product-destination-selector__skeleton",4,"ngIf"],[1,"mbo-product-destination-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-destination-selector__component"],[1,"mbo-product-destination-selector__catalog",3,"hidden"],["class","mbo-product-destination-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","detail","ghost","click",4,"ngFor","ngForOf"],[1,"mbo-product-destination-selector__empty",3,"hidden"],[1,"mbo-product-destination-selector__title","overline-medium"],[3,"color","icon","title","number","detail","ghost","click"],[1,"mbo-product-destination-selector__skeleton"],[3,"skeleton"]],template:function(g,_){1&g&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,d,3,0,"div",1),e.\u0275\u0275template(2,c,6,5,"div",2),e.\u0275\u0275template(3,a,3,2,"div",3),e.\u0275\u0275elementEnd()),2&g&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!_.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",_.skeleton))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,o.w_,y.Aj],styles:["/*!\n * BOCC ProductDestinationSelector Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 14/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-product-destination-selector{position:relative;width:100%;display:block}mbo-product-destination-selector .mbo-product-destination-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-destination-selector .mbo-product-destination-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-destination-selector .mbo-product-destination-selector__component{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12)}mbo-product-destination-selector .mbo-product-destination-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-destination-selector .mbo-product-destination-selector__catalog,mbo-product-destination-selector .mbo-product-destination-selector__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6)}\n"],encapsulation:2}),f})()},38116:(j,I,t)=>{t.d(I,{Z:()=>m});var n=t(17007),e=t(99877),o=t(30263);function s(y,d){if(1&y){const p=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",1),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(p).$implicit,C=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(C.onAction(a))}),e.\u0275\u0275elementStart(1,"div",2)(2,"div",3),e.\u0275\u0275element(3,"bocc-icon",4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"label",5),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()}if(2&y){const p=d.$implicit,u=e.\u0275\u0275nextContext();e.\u0275\u0275classMap(u.classWidthAction),e.\u0275\u0275classProp("mbo-product-info-actions__button--disabled",u.itIsDisabled(p)),e.\u0275\u0275advance(1),e.\u0275\u0275classMap(u.theme(p)),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",p.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",p.label," ")}}let m=(()=>{class y{constructor(){this.actions=[],this.action=new e.EventEmitter}get classWidthAction(){return`button-width-${this.actions.length}`}itIsDisabled(p){return p.requiredInformation&&p.errorInformation}theme(p){return this.itIsDisabled(p)?"none":p.theme}onAction(p){!this.itIsDisabled(p)&&this.action.emit(p.type)}}return y.\u0275fac=function(p){return new(p||y)},y.\u0275cmp=e.\u0275\u0275defineComponent({type:y,selectors:[["mbo-product-info-actions"]],inputs:{actions:"actions"},outputs:{action:"action"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-product-info-actions__button",3,"class","mbo-product-info-actions__button--disabled","click",4,"ngFor","ngForOf"],[1,"mbo-product-info-actions__button",3,"click"],[1,"mbo-product-info-actions__button__icon"],[1,"mbo-product-info-actions__button__icon__content"],[3,"icon"],[1,"mbo-product-info-actions__button__label","smalltext-medium"]],template:function(p,u){1&p&&e.\u0275\u0275template(0,s,6,8,"div",0),2&p&&e.\u0275\u0275property("ngForOf",u.actions)},dependencies:[n.CommonModule,n.NgForOf,o.Zl],styles:["mbo-product-info-actions{--pvt-component-height: var(--sizing-x32);--pvt-button-size: var(--sizing-x24);--pvt-icon-dimension: var(--sizing-x12);--pvt-icon-color: var(--color-blue-700);--pvt-label-color: var(--color-carbon-darker-1000);--pvt-label-font-size: var(--smalltext-size);position:relative;display:flex;justify-content:space-around;width:100%;height:var(--pvt-component-height);padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button{position:relative;display:flex;width:auto;flex-direction:column;justify-content:flex-end;padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button.button-width-2{width:calc(50% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-3{width:calc(33% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button.button-width-4{width:calc(25% - var(--sizing-x1))}mbo-product-info-actions .mbo-product-info-actions__button--disabled{--pvt-icon-color: var(--color-carbon-lighter-300);--pvt-label-color: var(--color-carbon-lighter-400);pointer-events:none}mbo-product-info-actions .mbo-product-info-actions__button__icon{position:absolute;left:50%;transform:translate(-50%,calc(-50% - var(--sizing-x6))) rotate(45deg);width:var(--pvt-button-size);height:var(--pvt-button-size);padding:var(--sizing-x2);box-sizing:border-box;margin:0rem auto;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50)}mbo-product-info-actions .mbo-product-info-actions__button__icon.success{--pvt-icon-color: var(--color-semantic-success-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.info{--pvt-icon-color: var(--color-semantic-info-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon.danger{--pvt-icon-color: var(--color-semantic-danger-700)}mbo-product-info-actions .mbo-product-info-actions__button__icon__content{--bocc-icon-dimension: var(--pvt-icon-dimension);position:relative;width:100%;height:100%;display:flex;box-sizing:border-box}mbo-product-info-actions .mbo-product-info-actions__button__icon__content>bocc-icon{color:var(--pvt-icon-color);margin:auto;transform:rotate(-45deg)}mbo-product-info-actions .mbo-product-info-actions__button__label{color:var(--pvt-label-color);text-align:center;overflow:hidden;display:block;text-overflow:ellipsis;white-space:nowrap;font-size:var(--pvt-label-font-size);letter-spacing:var(--smalltext-letter-spacing);min-height:var(--smalltext-line-height);line-height:var(--smalltext-line-height)}@media screen and (max-width: 320px){mbo-product-info-actions{--pvt-component-height: var(--sizing-x28);--pvt-icon-dimension: var(--sizing-x10);--pvt-button-size: var(--sizing-x20);--pvt-label-font-size: var(--caption-size)}}\n"],encapsulation:2}),y})()},68819:(j,I,t)=>{t.d(I,{w:()=>O});var n=t(17007),e=t(99877),o=t(30263),s=t(39904),d=(t(57544),t(78506)),u=(t(29306),t(87903)),c=t(95437),a=t(27302),C=t(70957),v=t(91248),f=t(13961),h=t(68789),g=t(33395),_=t(25317);function M(N,te){if(1&N){const Z=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275listener("click",function(Y){e.\u0275\u0275restoreView(Z);const U=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(U.onBoarding(Y))}),e.\u0275\u0275elementStart(2,"div",19),e.\u0275\u0275element(3,"img",20)(4,"bocc-tag-aval",21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"label",22),e.\u0275\u0275text(6," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",23)(8,"button",24),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(Z);const Y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(Y.onCopyKey(Y.product.tagAval))}),e.\u0275\u0275elementStart(9,"span"),e.\u0275\u0275text(10,"Copiar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(11,"div",25),e.\u0275\u0275elementStart(12,"button",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(Z);const Y=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(Y.onEditTagAval())}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Personalizar"),e.\u0275\u0275elementEnd()()()()}if(2&N){const Z=e.\u0275\u0275nextContext();e.\u0275\u0275property("hidden",Z.itIsVisibleMovements),e.\u0275\u0275advance(4),e.\u0275\u0275property("value",Z.product.tagAval)}}function b(N,te){if(1&N&&e.\u0275\u0275element(0,"mbo-product-info-section",27),2&N){const Z=e.\u0275\u0275nextContext();e.\u0275\u0275property("section",Z.digitalSection)("currencyCode",null==Z.currencyControl.value?null:Z.currencyControl.value.code)("hidden",Z.itIsVisibleMovements)}}function l(N,te){if(1&N&&(e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275element(1,"mbo-product-info-section",28),e.\u0275\u0275elementEnd()),2&N){const Z=te.$implicit,V=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("section",Z)("currencyCode",null==V.currencyControl.value?null:V.currencyControl.value.code)}}const T=[[["","header",""]],"*"],S=["[header]","*"];let O=(()=>{class N{constructor(Z,V,Y){this.mboProvider=Z,this.managerInformation=V,this.onboardingScreenService=Y,this.sections=[],this.header=!0,this.condense=!0,this.requesting=!1,this.sectionPosition=0,this.headers=[],this.canEditTagAval=!1,this.headerMovements={label:"Movimientos",value:0,loading:!0}}ngOnChanges(Z){const{movements:V,sections:Y}=Z;V&&this.product&&(this.headerMovements.value=this.sections?.length||0,this.headerMovements.loading=!V.currentValue),Y&&this.product&&this.refreshComponent(this.product,Y.currentValue),this.managerInformation.requestInfoBody().then(U=>{U.when({success:({canEditTagAval:J})=>{this.canEditTagAval=J}})})}get itIsVisibleMovements(){return this.headerMovements.value===this.sectionPosition}onCopyKey(Z){(0,u.Bn)(Z),this.mboProvider.toast.success("P\xe9galo donde quieras compartirlo.","Tag Aval copiado con \xe9xito")}onEditTagAval(){this.mboProvider.navigation.next(s.Z6.CUSTOMER.PRODUCTS.TAG_AVAL_EDIT,{productId:this.product?.id})}onShared(){this.managerInformation.shared(this.product)}refreshComponent(Z,V){const Y=(0,u.A2)(Z);if(this.sectionPosition=0,V?.length){const U=V.map(({title:J},A)=>({label:J,value:A}));Y&&(this.headerMovements.value=this.sections.length,U.push(this.headerMovements)),this.headers=U}else{this.sectionPosition=1,this.headerMovements.loading=!this.movements,this.headerMovements.value=0;const U=[{label:"Error",value:1}];Y&&U.unshift(this.headerMovements),this.headers=U}}onBoarding(Z){this.tagAvalonboarding||(this.tagAvalonboarding=this.onboardingScreenService.create(f.Z)),this.tagAvalonboarding.open(),Z.stopPropagation()}}return N.\u0275fac=function(Z){return new(Z||N)(e.\u0275\u0275directiveInject(c.ZL),e.\u0275\u0275directiveInject(d.vu),e.\u0275\u0275directiveInject(h.x))},N.\u0275cmp=e.\u0275\u0275defineComponent({type:N,selectors:[["mbo-product-info-body"]],inputs:{product:"product",sections:"sections",digitalSection:"digitalSection",movements:"movements",header:"header",condense:"condense",requesting:"requesting",currencyControl:"currencyControl"},standalone:!0,features:[e.\u0275\u0275NgOnChangesFeature,e.\u0275\u0275StandaloneFeature],ngContentSelectors:S,decls:31,vars:24,consts:[[1,"mbo-product-info-body__content"],[1,"mbo-product-info-body__component"],[3,"tabs","value","valueChange"],[1,"mbo-product-info-body__header",3,"hidden"],["class","mbo-product-info-body__key",3,"hidden",4,"ngIf"],[3,"section","currencyCode","hidden",4,"ngIf"],[3,"position"],["class","bocc-tab-form__view",4,"ngFor","ngForOf"],[1,"bocc-tab-form__view"],[3,"movements","header","product","currencyCode"],[1,"mbo-product-info-body__error"],["bocc-button","flat","prefixIcon","refresh",3,"hidden"],[1,"mbo-product-info-body__additional",3,"hidden"],[1,"mbo-product-info-body__request",3,"hidden"],[1,"mbo-product-info-body__skeleton"],[1,"mbo-product-info-body__skeleton__title",3,"active"],[1,"mbo-product-info-body__skeleton__subtitle",3,"active"],[1,"mbo-product-info-body__key",3,"hidden"],[1,"mbo-product-info-body__key__title",3,"click"],[1,"mbo-product-info-body__key__title-container"],["src","assets/shared/logos/tag-aval/tag-aval-line-original.png","alt","TagAvalLogo"],[3,"value"],[1,"mbo-product-info-body__key__title-help","caption-medium"],[1,"mbo-product-info-body__key__actions"],["id","btn_product-info_copy-tag","bocc-button","flat","suffixIcon","copy","boccUtagComponent","click",3,"click"],[1,"divider"],["id","btn_product-info_edit-tag","bocc-button","flat","suffixIcon","edit-pencil","boccUtagComponent","click",3,"click"],[3,"section","currencyCode","hidden"],[3,"section","currencyCode"]],template:function(Z,V){1&Z&&(e.\u0275\u0275projectionDef(T),e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"bocc-tab-header",2),e.\u0275\u0275listener("valueChange",function(U){return V.sectionPosition=U}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275projection(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(5,M,15,2,"div",4),e.\u0275\u0275template(6,b,1,3,"mbo-product-info-section",5),e.\u0275\u0275elementStart(7,"bocc-tab-form",6),e.\u0275\u0275template(8,l,2,2,"div",7),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"mbo-product-info-movements",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8)(12,"div",10)(13,"mbo-message-empty")(14,"p"),e.\u0275\u0275text(15," Ocurrio un error al tratar de consultar la informaci\xf3n del producto "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"button",11)(17,"span"),e.\u0275\u0275text(18,"Reintentar"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(19,"div",12),e.\u0275\u0275projection(20,1),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",13)(22,"div",14),e.\u0275\u0275element(23,"bocc-skeleton-text",15)(24,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div",14),e.\u0275\u0275element(26,"bocc-skeleton-text",15)(27,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",14),e.\u0275\u0275element(29,"bocc-skeleton-text",15)(30,"bocc-skeleton-text",16),e.\u0275\u0275elementEnd()()()),2&Z&&(e.\u0275\u0275classProp("mbo-product-info-body__content--condense",V.condense),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("requesting",V.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("tabs",V.headers)("value",V.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",V.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==V.product?null:V.product.tagAval),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",V.digitalSection),e.\u0275\u0275advance(1),e.\u0275\u0275property("position",V.sectionPosition),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",V.sections),e.\u0275\u0275advance(2),e.\u0275\u0275property("movements",V.movements)("header",V.header)("product",V.product)("currencyCode",null==V.currencyControl.value?null:V.currencyControl.value.code),e.\u0275\u0275advance(6),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("hidden",V.itIsVisibleMovements),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",!V.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(2),e.\u0275\u0275property("active",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("active",!0))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,o.Gf,o.qw,o.P8,o.Dj,o.qd,C.K,v.I,a.Aj,g.kW,_.I],styles:["mbo-product-info-body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x8);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom);border-radius:var(--sizing-x8) var(--sizing-x8) 0rem 0rem;border-top:var(--border-1-lighter-300);background:var(--color-carbon-lighter-50);transform:translateY(calc(var(--sizing-x8) * -1))}mbo-product-info-body.actionables{margin-bottom:calc(44rem + var(--sizing-safe-bottom))}mbo-product-info-body mbo-product-info-movements{margin-top:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__content{position:relative;width:100%}mbo-product-info-body .mbo-product-info-body__content--condense{margin-bottom:0rem}mbo-product-info-body .mbo-product-info-body__component{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-body .mbo-product-info-body__component.requesting{opacity:0;height:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key{border-radius:var(--sizing-x8);border:var(--border-1-lighter-300);margin:var(--sizing-x4) 0rem}mbo-product-info-body .mbo-product-info-body__key__title{display:flex;flex-direction:column;align-items:center;padding:var(--sizing-x8);box-sizing:border-box;border-bottom:var(--border-1-lighter-300)}mbo-product-info-body .mbo-product-info-body__key__title-container{display:flex;align-items:center;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__key__title img{height:var(--sizing-x8);width:auto;max-width:100%}mbo-product-info-body .mbo-product-info-body__key__title-help{text-align:center;margin-bottom:var(--sizing-x2);margin-top:var(--sizing-x4);color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__title>bocc-icon{color:var(--color-blue-700)}mbo-product-info-body .mbo-product-info-body__key__actions{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box}mbo-product-info-body .mbo-product-info-body__key__actions button{width:100%}mbo-product-info-body .mbo-product-info-body__key__actions .divider{background:var(--color-carbon-lighter-300);width:var(--border-1);height:var(--sizing-x16)}mbo-product-info-body .mbo-product-info-body__error{display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-body .mbo-product-info-body__request{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin:var(--sizing-x8) 0rem}mbo-product-info-body .mbo-product-info-body__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-body .mbo-product-info-body__skeleton__title{width:100%;min-height:var(--sizing-x12)}mbo-product-info-body .mbo-product-info-body__skeleton__subtitle{width:60%;min-height:var(--sizing-x10)}\n"],encapsulation:2}),N})()},19310:(j,I,t)=>{t.d(I,{$:()=>b});var n=t(17007),r=t(99877),e=t(30263),i=t(87903);let o=(()=>{class l{transform(S,O,N=" "){return(0,i.rd)(S,O,N)}}return l.\u0275fac=function(S){return new(S||l)},l.\u0275pipe=r.\u0275\u0275definePipe({name:"codeSplit",type:l,pure:!0}),l})(),s=(()=>{class l{}return l.\u0275fac=function(S){return new(S||l)},l.\u0275mod=r.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=r.\u0275\u0275defineInjector({imports:[n.CommonModule]}),l})();t(57544);var y=t(70658),d=t(78506),u=(t(29306),t(87956)),c=t(72765),a=t(27302);function C(l,T){if(1&l){const S=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"button",18),r.\u0275\u0275listener("click",function(){r.\u0275\u0275restoreView(S);const N=r.\u0275\u0275nextContext();return r.\u0275\u0275resetView(N.onDigital())}),r.\u0275\u0275elementStart(1,"span"),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd()()}if(2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275property("prefixIcon",S.digitalIcon),r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate(S.digitalIncognito?"Ver datos":"Ocultar datos")}}function v(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"span"),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate(null==S.product?null:S.product.publicNumber)}}function f(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"span",19),r.\u0275\u0275text(1),r.\u0275\u0275pipe(2,"codeSplit"),r.\u0275\u0275elementEnd()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",r.\u0275\u0275pipeBind2(2,1,S.digitalIncognito?"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022":S.digitalNumber,4)," ")}}function h(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"bocc-badge",20),r.\u0275\u0275text(1),r.\u0275\u0275elementEnd()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275attribute("bocc-theme",null==S.product||null==S.product.status?null:S.product.status.color),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",null==S.product||null==S.product.status?null:S.product.status.label," ")}}function g(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"div",21),r.\u0275\u0275element(1,"bocc-progress-bar",22),r.\u0275\u0275elementEnd()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("theme",S.progressBarTheme)("width",S.progressBarStatus)}}function _(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"div",23)(1,"label",24),r.\u0275\u0275text(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"div",25)(4,"bocc-skeleton-text",10),r.\u0275\u0275element(5,"bocc-amount",26),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(6,"mbo-button-incognito-mode",27),r.\u0275\u0275elementEnd()()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275advance(2),r.\u0275\u0275textInterpolate1(" ",null==S.product?null:S.product.label," "),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!S.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("amount",null==S.product?null:S.product.amount)("incognito",S.incognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("actionMode",!0)("hidden",!S.product)}}function M(l,T){if(1&l&&(r.\u0275\u0275elementStart(0,"div",28)(1,"div",29)(2,"label",20),r.\u0275\u0275text(3,"Vence"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"span",19),r.\u0275\u0275text(5),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(6,"div",29)(7,"label",20),r.\u0275\u0275text(8,"CVC"),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(9,"span",19),r.\u0275\u0275text(10),r.\u0275\u0275elementEnd()()()),2&l){const S=r.\u0275\u0275nextContext();r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",S.digitalIncognito?"\u2022\u2022 | \u2022\u2022":S.digitalExpAt," "),r.\u0275\u0275advance(5),r.\u0275\u0275textInterpolate1(" ",S.digitalIncognito?"\u2022\u2022\u2022":S.digitalCVC," ")}}let b=(()=>{class l{constructor(S,O){this.managerPreferences=S,this.digitalService=O,this.unsubscriptions=[],this.progressBarRequired=!1,this.progressBarPercent=100,this.incognito=!1,this.digitalRequiredRequest=!0,this.digitalIncognito=!0,this.digitalIcon="eye-show-visible",this.close=new r.EventEmitter,this.digital=new r.EventEmitter}ngOnInit(){this.unsubscriptions.push(this.managerPreferences.subscribe(({isIncognito:S})=>{this.incognito=S})),this.unsubscriptions.push(this.digitalService.subscribe(({productId:S,value:O})=>{this.product&&this.product.id===S&&this.refreshDigitalState(O)}))}ngOnChanges(S){const{product:O}=S;if(O&&O.currentValue){const N=O.currentValue;this.refreshDigitalState(this.digitalService.request(N.id)),this.activateDigitalCountdown(N)}}ngOnDestroy(){this.unsubscriptions.forEach(S=>S())}get progressBarStatus(){return`${this.progressBarPercent||0}%`}get currencies(){return this.product?.currencies||[]}onClose(){this.close.emit(!0)}onDigital(){this.digitalRequiredRequest?this.digital.emit(this.product):this.digitalService.setIncognito(this.product?.id,!this.digitalIncognito)}refreshDigitalState(S){const{incognito:O,requiredRequest:N,cvc:te,expirationAt:Z,number:V}=S;this.digitalIncognito=O,this.digitalExpAt=Z,this.digitalCVC=te,this.digitalNumber=V,this.digitalIcon=O?"eye-show-visible":"eye-hidden",this.digitalRequiredRequest=N}activateDigitalCountdown(S){const{countdown$:O}=this.digitalService.request(S.id);O?(this.progressBarRequired=!0,this.progressBarPercent=100,O.subscribe(N=>{this.progressBarRequired=!(N>=y.N.inactivityDigital),this.progressBarRequired&&(this.progressBarPercent=100-N/y.N.inactivityDigital*100,this.progressBarTheme=this.progressBarPercent>=50?"success":this.progressBarPercent<25?"danger":"alert")})):this.progressBarRequired=!1}}return l.\u0275fac=function(S){return new(S||l)(r.\u0275\u0275directiveInject(d.Bx),r.\u0275\u0275directiveInject(u.ZP))},l.\u0275cmp=r.\u0275\u0275defineComponent({type:l,selectors:[["mbo-product-info-card"]],inputs:{product:"product",currencyControl:"currencyControl"},outputs:{close:"close",digital:"digital"},standalone:!0,features:[r.\u0275\u0275NgOnChangesFeature,r.\u0275\u0275StandaloneFeature],decls:21,vars:19,consts:[[1,"mbo-product-info-card",3,"color"],[1,"mbo-product-info-card__header"],["id","btn_product-info-card_back","bocc-button","flat","prefixIcon","prev-page",3,"click"],[3,"formControl","currencies","hidden"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click",4,"ngIf"],[1,"mbo-product-info-card__body"],[1,"mbo-product-info-card__body__avatar"],[3,"src"],[1,"mbo-product-info-card__body__content"],[1,"mbo-product-info-card__title","body2-medium",3,"active"],[3,"active"],[1,"mbo-product-info-card__number","body1-regular"],[4,"ngIf"],["class","body1-medium",4,"ngIf"],["class","caption-medium",4,"ngIf"],["class","mbo-product-info-card__progressbar",4,"ngIf"],["class","mbo-product-info-card__footer",4,"ngIf"],["class","mbo-product-info-card__digital",4,"ngIf"],["id","btn_product-info-card_digital","bocc-button","flat",3,"prefixIcon","click"],[1,"body1-medium"],[1,"caption-medium"],[1,"mbo-product-info-card__progressbar"],[3,"theme","width"],[1,"mbo-product-info-card__footer"],[1,"mbo-product-info-card__subtitle","caption-medium"],[1,"mbo-product-info-card__amount"],[3,"amount","incognito"],[3,"actionMode","hidden"],[1,"mbo-product-info-card__digital"],[1,"mbo-product-info-card__digital__info"]],template:function(S,O){1&S&&(r.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1)(2,"button",2),r.\u0275\u0275listener("click",function(){return O.onClose()}),r.\u0275\u0275elementStart(3,"span"),r.\u0275\u0275text(4,"Atr\xe1s"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275element(5,"mbo-currency-toggle",3),r.\u0275\u0275template(6,C,3,2,"button",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"div",5)(8,"div",6),r.\u0275\u0275element(9,"img",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",8)(11,"bocc-skeleton-text",9),r.\u0275\u0275text(12),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"bocc-skeleton-text",10)(14,"div",11),r.\u0275\u0275template(15,v,2,1,"span",12),r.\u0275\u0275template(16,f,3,4,"span",13),r.\u0275\u0275template(17,h,2,2,"bocc-badge",14),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(18,g,2,2,"div",15),r.\u0275\u0275elementEnd()(),r.\u0275\u0275template(19,_,7,6,"div",16),r.\u0275\u0275template(20,M,11,2,"div",17),r.\u0275\u0275elementEnd()),2&S&&(r.\u0275\u0275classMap(null==O.product?null:O.product.bank.className),r.\u0275\u0275property("color",null==O.product?null:O.product.color),r.\u0275\u0275advance(5),r.\u0275\u0275property("formControl",O.currencyControl)("currencies",O.currencies)("hidden",!(null!=O.product&&O.product.bank.isOccidente)||O.currencies.length<2),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital),r.\u0275\u0275advance(3),r.\u0275\u0275property("src",null==O.product?null:O.product.logo,r.\u0275\u0275sanitizeUrl),r.\u0275\u0275advance(2),r.\u0275\u0275property("active",!O.product),r.\u0275\u0275advance(1),r.\u0275\u0275textInterpolate1(" ",(null==O.product?null:O.product.nickname)||(null==O.product?null:O.product.name)," "),r.\u0275\u0275advance(1),r.\u0275\u0275property("active",!O.product),r.\u0275\u0275advance(1),r.\u0275\u0275classProp("mbo-product-info-card__number--skeleton",!O.product),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=O.product&&O.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",(null==O.product||null==O.product.status?null:O.product.status.label)&&O.digitalIncognito),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",O.progressBarRequired),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",!(null!=O.product&&O.product.isDigital)),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",null==O.product?null:O.product.isDigital))},dependencies:[n.CommonModule,n.NgIf,e.X6,e.Qg,e.Oh,e.P8,e.cp,e.Dj,s,o,c.uf,a.B_],styles:["mbo-product-info-card{position:relative;width:100%;display:block}mbo-product-info-card .bocc-card-product-background{--bocc-card-product-color-title: var(--color-blue-700);display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x8) var(--sizing-x6);box-sizing:border-box;border:none!important}mbo-product-info-card .bocc-card-product-background.av-villas{--bocc-card-product-color-title: var(--color-semantic-danger-700)}mbo-product-info-card .bocc-card-product-background.bogota{--bocc-card-product-color-title: var(--color-semantic-info-900)}mbo-product-info-card .bocc-card-product-background.facilpass{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background.occidente{--bocc-card-product-color-title: var(--color-blue-700)}mbo-product-info-card .bocc-card-product-background.popular{--bocc-card-product-color-title: var(--color-semantic-success-700)}mbo-product-info-card .bocc-card-product-background.porvenir{--bocc-card-product-color-title: var(--color-semantic-alert-700)}mbo-product-info-card .bocc-card-product-background.dale{--bocc-card-product-color-title: var(--color-carbon-darker-1000)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle{box-shadow:none;border:var(--border-1) solid var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__content{background:transparent}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element{color:var(--bocc-card-product-color-title)}mbo-product-info-card .bocc-card-product-background mbo-currency-toggle .mbo-currency-toggle__element--enabled{color:var(--bocc-card-product-color-theme)}mbo-product-info-card .mbo-product-info-card__header{--bocc-button-padding: 0rem var(--sizing-x2);display:flex;justify-content:space-between;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__header .bocc-button{color:var(--bocc-card-product-color-button);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body{position:relative;display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__body__avatar{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-product-info-card .mbo-product-info-card__body__avatar img{width:100%;filter:var(--img-filter)}mbo-product-info-card .mbo-product-info-card__body__content{display:flex;flex-direction:column;row-gap:var(--sizing-x2);width:calc(100% - var(--sizing-x20))}mbo-product-info-card .mbo-product-info-card__title{color:var(--bocc-card-product-color-title)}mbo-product-info-card .mbo-product-info-card__number{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;color:var(--bocc-card-product-color-code)}mbo-product-info-card .mbo-product-info-card__number--skeleton{width:60%}mbo-product-info-card .mbo-product-info-card__progressbar bocc-progress-bar{width:60%}mbo-product-info-card .mbo-product-info-card__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__footer label{text-align:center}mbo-product-info-card .mbo-product-info-card__subtitle{color:var(--bocc-card-product-color-subtitle)}mbo-product-info-card .mbo-product-info-card__amount{position:relative;width:100%;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-product-info-card .mbo-product-info-card__amount bocc-amount{color:var(--bocc-card-product-color-amount);font-size:var(--heading5-size);letter-spacing:var(--heading5-letter-spacing)}mbo-product-info-card .mbo-product-info-card__amount mbo-button-incognito-mode{color:var(--bocc-card-product-color-button);width:var(--sizing-x12);height:var(--sizing-x12)}mbo-product-info-card .mbo-product-info-card__digital{position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x20);column-gap:var(--sizing-x20);padding:0rem var(--sizing-x20);box-sizing:border-box;margin-bottom:var(--sizing-x6)}mbo-product-info-card .mbo-product-info-card__digital__info{display:flex;flex-direction:column;row-gap:var(--sizing-x2);color:var(--bocc-card-product-color-subtitle)}\n"],encapsulation:2}),l})()},94614:(j,I,t)=>{t.d(I,{K:()=>d});var n=t(17007),e=t(30263),i=t(39904),s=(t(29306),t(95437)),m=t(99877);let d=(()=>{class p{constructor(c){this.mboProvider=c,this.redirect="movements",this.skeleton=!1}onComponent(){if(this.product){const{categoryType:c,id:a,parentProduct:C}=this.product;"covered"===c&&C?this.goToPage(C.id,a):this.goToPage(a)}else this.productId&&this.goToPage(this.productId,this.coveredCardId)}goToPage(c,a){this.mboProvider.navigation.next(i.Z6.CUSTOMER.PRODUCTS.MOVEMENT_INFORMATION,{uuid:this.movement.uuid,productId:c,currencyCode:this.movement.currencyCode,redirect:this.redirect,coveredCardId:a})}}return p.\u0275fac=function(c){return new(c||p)(m.\u0275\u0275directiveInject(s.ZL))},p.\u0275cmp=m.\u0275\u0275defineComponent({type:p,selectors:[["mbo-product-info-movement"]],inputs:{movement:"movement",product:"product",productId:"productId",coveredCardId:"coveredCardId",redirect:"redirect",skeleton:"skeleton"},standalone:!0,features:[m.\u0275\u0275StandaloneFeature],decls:8,vars:10,consts:[[1,"mbo-product-info-movement__content",3,"click"],[1,"mbo-product-info-movement__body"],[1,"mbo-product-info-movement__date","body2-medium",3,"active"],[1,"mbo-product-info-movement__description","body2-medium",3,"active"],[1,"mbo-product-info-movement__amount",3,"hidden"],[3,"amount","currencyCode","theme"]],template:function(c,a){1&c&&(m.\u0275\u0275elementStart(0,"div",0),m.\u0275\u0275listener("click",function(){return a.onComponent()}),m.\u0275\u0275elementStart(1,"div",1)(2,"bocc-skeleton-text",2),m.\u0275\u0275text(3),m.\u0275\u0275elementEnd(),m.\u0275\u0275elementStart(4,"bocc-skeleton-text",3),m.\u0275\u0275text(5),m.\u0275\u0275elementEnd()(),m.\u0275\u0275elementStart(6,"div",4),m.\u0275\u0275element(7,"bocc-amount",5),m.\u0275\u0275elementEnd()()),2&c&&(m.\u0275\u0275classProp("mbo-product-info-movement__content--skeleton",a.skeleton),m.\u0275\u0275advance(2),m.\u0275\u0275property("active",a.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==a.movement?null:a.movement.dateFormat," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("active",a.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275textInterpolate1(" ",null==a.movement?null:a.movement.description," "),m.\u0275\u0275advance(1),m.\u0275\u0275property("hidden",a.skeleton),m.\u0275\u0275advance(1),m.\u0275\u0275property("amount",null==a.movement?null:a.movement.value)("currencyCode",null==a.movement?null:a.movement.currencyCode)("theme",!0))},dependencies:[n.CommonModule,e.Qg,e.Dj],styles:["mbo-product-info-movement{--movement-body-width: 100%;--movement-description-width: 100%;position:relative;width:100%;display:block;border-bottom:var(--border-1-lighter-300)}mbo-product-info-movement .mbo-product-info-movement__content{position:relative;display:flex;width:100%;justify-content:space-between;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) var(--sizing-x2);box-sizing:border-box}mbo-product-info-movement .mbo-product-info-movement__content--skeleton{--movement-body-width: 80%;--movement-description-width: 60%}mbo-product-info-movement .mbo-product-info-movement__body{width:var(--movement-body-width);display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-movement .mbo-product-info-movement__description{width:var(--movement-description-width)}mbo-product-info-movement .mbo-product-info-movement__date{color:var(--color-carbon-lighter-700)}mbo-product-info-movement .mbo-product-info-movement__amount{display:flex;align-items:flex-end;white-space:nowrap}\n"],encapsulation:2}),p})()},70957:(j,I,t)=>{t.d(I,{K:()=>g});var n=t(15861),r=t(17007),i=t(99877),s=t(30263),m=t(78506),y=t(39904),p=(t(29306),t(87903)),u=t(95437),c=t(27302),a=t(94614);function C(_,M){if(1&_){const b=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",8),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(b);const T=i.\u0275\u0275nextContext(2);return i.\u0275\u0275resetView(T.onRedirectAll())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2,"Ver todos"),i.\u0275\u0275elementEnd()()}}function v(_,M){if(1&_&&(i.\u0275\u0275elementStart(0,"div",5)(1,"label",6),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(3,C,3,0,"button",7),i.\u0275\u0275elementEnd()),2&_){const b=i.\u0275\u0275nextContext();i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate1(" ",(null==b.productMovements||null==b.productMovements.range?null:b.productMovements.range.label)||"Sin resultados"," "),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==b.productMovements?null:b.productMovements.range)}}function f(_,M){if(1&_&&i.\u0275\u0275element(0,"mbo-product-info-movement",9),2&_){const b=M.$implicit,l=i.\u0275\u0275nextContext();i.\u0275\u0275property("movement",b)("product",l.product)}}function h(_,M){if(1&_&&(i.\u0275\u0275elementStart(0,"mbo-message-empty",10),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&_){const b=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",b.msgError," ")}}let g=(()=>{class _{constructor(b,l){this.mboProvider=b,this.managerProductMovements=l,this.header=!0,this.requesting=!1}ngOnChanges(b){const{currencyCode:l,product:T}=b;if(!this.movements&&(T||l)){const S=l?.currentValue||this.currencyCode,O=T?.currentValue||this.product;this.currentMovements=void 0,(0,p.A2)(O)&&this.requestFirstPage(O,S)}}get productMovements(){return this.movements||this.currentMovements}get msgError(){return this.movements?.isError?"Lo sentimos, por el momento no pudimos consultar tus registros realizados.":"Lo sentimos, no encontramos registros para este periodo."}onRedirectAll(){if(this.product){const{categoryType:b,id:l,parentProduct:T}=this.product;this.mboProvider.navigation.next(y.Z6.CUSTOMER.PRODUCTS.MOVEMENTS,{..."covered"===b?{productId:T?.id,coveredCardId:l}:{productId:l},currencyCode:this.currencyCode||"COP"})}}requestFirstPage(b,l){var T=this;return(0,n.Z)(function*(){T.requesting=!0,(yield T.managerProductMovements.requestForProduct({product:b,currencyCode:l})).when({success:S=>{T.currentMovements=S},failure:()=>{T.currentMovements=void 0}},()=>{T.requesting=!1})})()}}return _.\u0275fac=function(b){return new(b||_)(i.\u0275\u0275directiveInject(u.ZL),i.\u0275\u0275directiveInject(m.sy))},_.\u0275cmp=i.\u0275\u0275defineComponent({type:_,selectors:[["mbo-product-info-movements"]],inputs:{product:"product",currencyCode:"currencyCode",movements:"movements",header:"header"},standalone:!0,features:[i.\u0275\u0275NgOnChangesFeature,i.\u0275\u0275StandaloneFeature],decls:5,vars:4,consts:[[1,"mbo-product-info-movements__content",3,"hidden"],["class","mbo-product-info-movements__header bocc-subheader",4,"ngIf"],[1,"mbo-product-info-movements__list"],["redirect","product",3,"movement","product",4,"ngFor","ngForOf"],["class","mbo-product-info-movements__empty",4,"ngIf"],[1,"mbo-product-info-movements__header","bocc-subheader"],[1,"caption-medium"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_product-info-movements_open-all","bocc-button","flat",3,"click"],["redirect","product",3,"movement","product"],[1,"mbo-product-info-movements__empty"]],template:function(b,l){1&b&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275template(1,v,4,2,"div",1),i.\u0275\u0275elementStart(2,"div",2),i.\u0275\u0275template(3,f,1,2,"mbo-product-info-movement",3),i.\u0275\u0275elementEnd(),i.\u0275\u0275template(4,h,2,1,"mbo-message-empty",4),i.\u0275\u0275elementEnd()),2&b&&(i.\u0275\u0275property("hidden",l.requesting),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",l.header),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngForOf",null==l.productMovements?null:l.productMovements.firstPage),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",null==l.productMovements?null:l.productMovements.isEmpty))},dependencies:[r.CommonModule,r.NgForOf,r.NgIf,s.P8,a.K,c.Aj],styles:["mbo-product-info-movements{position:relative;width:100%;display:block}mbo-product-info-movements .mbo-product-info-movements__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-info-movements .mbo-product-info-movements__header{--bocc-button-padding: 0rem var(--sizing-x4)}\n"],encapsulation:2}),_})()},91248:(j,I,t)=>{t.d(I,{I:()=>p});var n=t(17007),e=t(30263),i=t(99877);function s(u,c){if(1&u&&i.\u0275\u0275element(0,"bocc-amount",10),2&u){const a=i.\u0275\u0275nextContext().$implicit;i.\u0275\u0275property("amount",a.value)("currencyCode",a.currencyCode)}}function m(u,c){if(1&u&&(i.\u0275\u0275elementStart(0,"span"),i.\u0275\u0275text(1),i.\u0275\u0275elementEnd()),2&u){const a=i.\u0275\u0275nextContext().$implicit,C=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275textInterpolate1(" ",C.incognito||C.section.incognito?a.mask:a.value," ")}}function y(u,c){if(1&u){const a=i.\u0275\u0275getCurrentView();i.\u0275\u0275elementStart(0,"button",11),i.\u0275\u0275listener("click",function(){i.\u0275\u0275restoreView(a);const v=i.\u0275\u0275nextContext().$implicit;return i.\u0275\u0275resetView(v.action.click())}),i.\u0275\u0275elementStart(1,"span"),i.\u0275\u0275text(2),i.\u0275\u0275elementEnd()()}if(2&u){const a=i.\u0275\u0275nextContext().$implicit;i.\u0275\u0275property("suffixIcon",a.action.icon),i.\u0275\u0275advance(2),i.\u0275\u0275textInterpolate(a.action.label)}}function d(u,c){if(1&u&&(i.\u0275\u0275elementStart(0,"li",3)(1,"div",4)(2,"label",5),i.\u0275\u0275text(3),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"label",6),i.\u0275\u0275template(5,s,1,2,"bocc-amount",7),i.\u0275\u0275template(6,m,2,1,"span",8),i.\u0275\u0275elementEnd()(),i.\u0275\u0275template(7,y,3,2,"button",9),i.\u0275\u0275elementEnd()),2&u){const a=c.$implicit,C=i.\u0275\u0275nextContext();i.\u0275\u0275property("hidden",(null==a?null:a.currencyCode)!==C.currencyCode),i.\u0275\u0275advance(3),i.\u0275\u0275textInterpolate1(" ",a.label," "),i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",a.money),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",!a.money),i.\u0275\u0275advance(1),i.\u0275\u0275property("ngIf",a.action&&!(C.incognito||C.section.incognito))}}let p=(()=>{class u{constructor(){this.currencyCode="COP"}}return u.\u0275fac=function(a){return new(a||u)},u.\u0275cmp=i.\u0275\u0275defineComponent({type:u,selectors:[["mbo-product-info-section"]],inputs:{section:"section",incognito:"incognito",currencyCode:"currencyCode"},standalone:!0,features:[i.\u0275\u0275StandaloneFeature],decls:3,vars:1,consts:[[1,"mbo-product-info-section__content"],[1,"mbo-product-info-section__list"],["class","mbo-product-info-section__element",3,"hidden",4,"ngFor","ngForOf"],[1,"mbo-product-info-section__element",3,"hidden"],[1,"mbo-product-info-section__body"],[1,"mbo-product-info-section__title","body2-medium"],[1,"mbo-product-info-section__info","body1-medium"],[3,"amount","currencyCode",4,"ngIf"],[4,"ngIf"],["bocc-button","flat",3,"suffixIcon","click",4,"ngIf"],[3,"amount","currencyCode"],["bocc-button","flat",3,"suffixIcon","click"]],template:function(a,C){1&a&&(i.\u0275\u0275elementStart(0,"div",0)(1,"ul",1),i.\u0275\u0275template(2,d,8,5,"li",2),i.\u0275\u0275elementEnd()()),2&a&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("ngForOf",C.section.datas))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,e.Qg,e.P8],styles:["mbo-product-info-section{position:relative;width:100%;display:block}mbo-product-info-section .mbo-product-info-section__content{position:relative;width:100%}mbo-product-info-section .mbo-product-info-section__list{position:relative;display:flex;width:100%;flex-direction:column}mbo-product-info-section .mbo-product-info-section__element{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;display:flex;width:100%;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);justify-content:space-between;align-items:center;padding:var(--sizing-x4) 0rem;border-bottom:var(--border-1-lighter-300)}mbo-product-info-section .mbo-product-info-section__title{color:var(--color-carbon-lighter-700)}mbo-product-info-section .mbo-product-info-section__body{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-product-info-section .mbo-product-info-section__info{width:100%;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),u})()},4663:(j,I,t)=>{t.d(I,{c:()=>c});var n=t(17007),e=t(99877),o=t(30263),s=t(27302);function m(a,C){1&a&&(e.\u0275\u0275elementStart(0,"div",10)(1,"p",11),e.\u0275\u0275projection(2),e.\u0275\u0275elementEnd()())}function y(a,C){if(1&a&&(e.\u0275\u0275elementStart(0,"div",12),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&a){const v=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",v.title," ")}}function d(a,C){if(1&a){const v=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-card-product-selector",13),e.\u0275\u0275listener("click",function(){const g=e.\u0275\u0275restoreView(v).$implicit,_=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.onProduct(g))}),e.\u0275\u0275elementEnd()}if(2&a){const v=C.$implicit,f=e.\u0275\u0275nextContext();e.\u0275\u0275property("color",v.color)("icon",v.logo)("title",v.nickname)("number",v.publicNumber)("ghost",f.ghost)("amount",v.amount)("tagAval",v.tagAvalFormat),e.\u0275\u0275attribute("amount-status",f.amountColorProduct(v))}}function p(a,C){1&a&&(e.\u0275\u0275elementStart(0,"mbo-message-empty",14),e.\u0275\u0275text(1," Lo sentimos, por el momento no cuentas con productos para realizar transacciones. "),e.\u0275\u0275elementEnd())}const u=["*"];let c=(()=>{class a{constructor(){this.header=!0,this.products=[],this.skeleton=!1,this.ghost=!1,this.automatic=!0,this.select=new e.EventEmitter}amountColorProduct(v){return v.amount>0?"success":v.amount<0?"danger":"empty"}onProduct(v){this.select.emit(v)}}return a.\u0275fac=function(v){return new(v||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-product-source-selector"]],inputs:{header:"header",title:"title",products:"products",skeleton:"skeleton",ghost:"ghost",automatic:"automatic"},outputs:{select:"select"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],ngContentSelectors:u,decls:13,vars:9,consts:[[1,"mbo-product-source-selector__content"],["class","mbo-product-source-selector__header",4,"ngIf"],[1,"mbo-product-source-selector__action",3,"hidden"],["bocc-button","flat","suffixIcon","arrow-right"],[1,"mbo-product-source-selector__products",3,"hidden"],["class","mbo-product-source-selector__title overline-medium",4,"ngIf"],[3,"color","icon","title","number","ghost","amount","tagAval","click",4,"ngFor","ngForOf"],["class","mbo-product-source-selector__empty",4,"ngIf"],[1,"mbo-product-source-selector__skeleton",3,"hidden"],[3,"skeleton"],[1,"mbo-product-source-selector__header"],[1,"subtitle2-medium"],[1,"mbo-product-source-selector__title","overline-medium"],[3,"color","icon","title","number","ghost","amount","tagAval","click"],[1,"mbo-product-source-selector__empty"]],template:function(v,f){1&v&&(e.\u0275\u0275projectionDef(),e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,m,3,0,"div",1),e.\u0275\u0275elementStart(2,"div",2)(3,"button",3)(4,"span"),e.\u0275\u0275text(5,"Ver topes"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275template(7,y,2,1,"div",5),e.\u0275\u0275template(8,d,1,8,"bocc-card-product-selector",6),e.\u0275\u0275template(9,p,2,0,"mbo-message-empty",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-card-product-selector",9)(12,"bocc-card-product-selector",9),e.\u0275\u0275elementEnd()()),2&v&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.header),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!0),e.\u0275\u0275advance(4),e.\u0275\u0275property("hidden",f.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",f.title),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",f.products),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!f.products.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("hidden",!f.skeleton),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("skeleton",!0))},dependencies:[n.CommonModule,n.NgForOf,n.NgIf,o.w_,o.P8,s.Aj],styles:["mbo-product-source-selector{position:relative;width:100%;display:block}mbo-product-source-selector .mbo-product-source-selector__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products{--pvt-amount-color: var(--color-carbon-lighter-900);--pvt-amount-background: var(--color-carbon-lighter-300);--pvt-product-opacity: 1;position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector{opacity:var(--pvt-product-opacity)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=success]{--pvt-amount-color: var(--color-semantic-success-900);--pvt-amount-background: var(--color-semantic-success-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector[amount-status=danger]{--pvt-amount-color: var(--color-semantic-danger-900);--pvt-amount-background: var(--color-semantic-danger-200)}mbo-product-source-selector .mbo-product-source-selector__products bocc-card-product-selector .bocc-card-product-selector__amount{color:var(--pvt-amount-color);background:var(--pvt-amount-background)}mbo-product-source-selector .mbo-product-source-selector__header p{color:var(--color-carbon-darker-1000)}mbo-product-source-selector .mbo-product-source-selector__title{position:relative;width:100%;height:var(--sizing-x16);line-height:var(--sizing-x16);padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-product-source-selector .mbo-product-source-selector__action{position:relative;display:flex;width:100%;justify-content:flex-end}mbo-product-source-selector .mbo-product-source-selector__action button{padding:0rem var(--sizing-x2) 0rem var(--sizing-x4)}mbo-product-source-selector .mbo-product-source-selector__skeleton{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);margin-top:var(--sizing-x4)}\n"],encapsulation:2}),a})()},13961:(j,I,t)=>{t.d(I,{Z:()=>m});var n=t(17007),e=t(27302),i=t(10119),o=t(99877);let m=(()=>{class y{constructor(){this.headerAction={id:"btn_tag-aval-onboarding_back",prefixIcon:"close",click:()=>{this.portal?.close()}}}ngBoccPortal(p){this.portal=p}}return y.\u0275fac=function(p){return new(p||y)},y.\u0275cmp=o.\u0275\u0275defineComponent({type:y,selectors:[["mbo-tag-aval-onboarding"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:11,vars:2,consts:[[3,"headerActionRight","gradient"],["src","assets/shared/onboardings/tag-aval/tag-aval-01.png"],["src","assets/shared/onboardings/tag-aval/tag-aval-02.png"]],template:function(p,u){1&p&&(o.\u0275\u0275elementStart(0,"mbo-onboarding-carousel",0)(1,"mbo-onboarding-element",1)(2,"label"),o.\u0275\u0275text(3," \xbfQu\xe9 es Tag Aval? "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"p"),o.\u0275\u0275text(5,"Es un c\xf3digo \xfanico que te permite recibir dinero de otros bancos."),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(6,"mbo-onboarding-element",2)(7,"label"),o.\u0275\u0275text(8," \xa1C\xe1mbialo como quieras! "),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"p"),o.\u0275\u0275text(10,"Personaliza tu Tag Aval y env\xedalo por WhatsApp, email... etc."),o.\u0275\u0275elementEnd()()()),2&p&&o.\u0275\u0275property("headerActionRight",u.headerAction)("gradient",!0)},dependencies:[n.CommonModule,e.Nu,i.N],styles:["mbo-onboarding{position:relative;display:flex;background:var(--color-navy-900)}#btn_tag-aval-onboarding_back .bocc-button__content{justify-content:flex-end}\n"],encapsulation:2}),y})()},66709:(j,I,t)=>{t.d(I,{s:()=>m});var n=t(17007),r=t(99877),e=t(30263),i=t(87542);let o=(()=>{class y{ngBoccPortal(p){}}return y.\u0275fac=function(p){return new(p||y)},y.\u0275cmp=r.\u0275\u0275defineComponent({type:y,selectors:[["mbo-token-mobile-form-info"]],standalone:!0,features:[r.\u0275\u0275StandaloneFeature],decls:28,vars:0,consts:[[1,"mbo-token-mobile-form-info__header"],[1,"subtitle2-medium"],[1,"mbo-token-mobile-form-info__content"],["title","\xbfQu\xe9 es Token Mobile?"],[1,"body2-medium","align-justify"],["title","\xbfC\xf3mo puedo activar mi Token Mobile?"]],template:function(p,u){1&p&&(r.\u0275\u0275elementStart(0,"div",0)(1,"label",1),r.\u0275\u0275text(2,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(3,"div",2)(4,"bocc-accordion")(5,"bocc-accordion-element",3)(6,"p",4),r.\u0275\u0275text(7," Token Mobile es la aplicaci\xf3n de seguridad del Banco de Occidente que genera una clave aleatoria de corta duraci\xf3n para validar tu identidad en las transacciones que realices. "),r.\u0275\u0275elementEnd()(),r.\u0275\u0275elementStart(8,"bocc-accordion-element",5)(9,"p",4),r.\u0275\u0275text(10," 1. Descarga la aplicaci\xf3n Token Mobile de Banco de Occidente desde la tienda de tu dispositivo m\xf3vil. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(11,"p",4),r.\u0275\u0275text(12," 2. Ingresa a la aplicaci\xf3n Token Mobile. "),r.\u0275\u0275element(13,"br"),r.\u0275\u0275text(14," 3. Selecciona el bot\xf3n de C\xf3digo QR."),r.\u0275\u0275element(15,"br"),r.\u0275\u0275text(16," 4. Activar la aplicaci\xf3n en tu dispositivo desde tu Portal Transaccional "),r.\u0275\u0275elementStart(17,"span"),r.\u0275\u0275text(18,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(19," Configuraci\xf3n "),r.\u0275\u0275elementStart(20,"span"),r.\u0275\u0275text(21,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(22," Seguridad "),r.\u0275\u0275elementStart(23,"span"),r.\u0275\u0275text(24,">"),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(25," Activar Token Mobile."),r.\u0275\u0275element(26,"br"),r.\u0275\u0275text(27," 5. Asignar una contrase\xf1a para el ingreso a la aplicaci\xf3n. "),r.\u0275\u0275elementEnd()()()())},dependencies:[n.CommonModule,e.FL,e.u],styles:["mbo-token-mobile-form-info{position:relative;width:100%;display:block}mbo-token-mobile-form-info .mbo-token-mobile-form-info__header{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x12);box-sizing:border-box;border-bottom:1px solid var(--color-carbon-lighter-300)}mbo-token-mobile-form-info .mbo-token-mobile-form-info__content{position:relative;width:100%;padding:var(--sizing-x6) var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),y})();const s=["*"];let m=(()=>{class y{constructor(p,u){this.ref=p,this.bottomSheetService=u,this.verifying=!1,this.tokenLength=i.Xi,this.code=new r.EventEmitter,this.tokenControls=new i.b2}ngOnInit(){const p=this.ref.nativeElement.querySelector("bocc-token-mobile-box");this.infoSheet=this.bottomSheetService.create(o),setTimeout(()=>{p?.focus()},120)}onAutocomplete(p){this.code.emit(p)}onInfo(){this.infoSheet?.open()}}return y.\u0275fac=function(p){return new(p||y)(r.\u0275\u0275directiveInject(r.ElementRef),r.\u0275\u0275directiveInject(e.fG))},y.\u0275cmp=r.\u0275\u0275defineComponent({type:y,selectors:[["mbo-token-mobile-form"]],inputs:{tokenControls:"tokenControls",verifying:"verifying"},outputs:{code:"code"},standalone:!0,features:[r.\u0275\u0275StandaloneFeature],ngContentSelectors:s,decls:17,vars:4,consts:[["body","",1,"mbo-token-mobile-form__body"],[1,"mbo-token-mobile-form__title","subtitle2-medium"],[1,"mbo-token-mobile-form__message","body2-medium"],[1,"mbo-token-mobile-form__logo"],["src","assets/shared/logos/components/token-form-key.svg"],["id","txt_token-mobile-form_input",3,"disabled","formControls","autocomplete"],["id","btn_token-mobile-form_question","bocc-button","flat","prefixIcon","chat-message",3,"disabled","click"]],template:function(p,u){1&p&&(r.\u0275\u0275projectionDef(),r.\u0275\u0275elementStart(0,"div",0)(1,"div",1),r.\u0275\u0275projection(2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(3,"p",2),r.\u0275\u0275text(4," Por tu seguridad necesitamos validar tu identidad con el Token Mobile. "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(5,"p",2),r.\u0275\u0275text(6," A continuaci\xf3n ingresa la clave generada de "),r.\u0275\u0275elementStart(7,"a"),r.\u0275\u0275text(8),r.\u0275\u0275elementEnd(),r.\u0275\u0275text(9,". "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(10,"div",3),r.\u0275\u0275element(11,"img",4),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(12,"bocc-token-mobile-box",5),r.\u0275\u0275listener("autocomplete",function(a){return u.onAutocomplete(a)}),r.\u0275\u0275text(13," Ingresa tu clave "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"button",6),r.\u0275\u0275listener("click",function(){return u.onInfo()}),r.\u0275\u0275elementStart(15,"span"),r.\u0275\u0275text(16,"\xbfNo tienes Token Mobile?"),r.\u0275\u0275elementEnd()()()),2&p&&(r.\u0275\u0275advance(8),r.\u0275\u0275textInterpolate1("",u.tokenLength," d\xedgitos"),r.\u0275\u0275advance(4),r.\u0275\u0275property("disabled",u.verifying)("formControls",u.tokenControls),r.\u0275\u0275advance(2),r.\u0275\u0275property("disabled",u.verifying))},dependencies:[n.CommonModule,e.P8,e.z3],styles:["mbo-token-mobile-form{position:relative;width:100%;display:block}mbo-token-mobile-form .mbo-token-mobile-form__body{position:relative;width:100%}mbo-token-mobile-form .mbo-token-mobile-form__body bocc-token-mobile-box{width:120rem;margin:var(--sizing-x8) calc(50% - 58rem)}mbo-token-mobile-form .mbo-token-mobile-form__body button{width:100%;margin-top:var(--sizing-x8)}mbo-token-mobile-form .mbo-token-mobile-form__title{position:relative;width:100%;text-align:center}mbo-token-mobile-form .mbo-token-mobile-form__message{position:relative;text-align:center;color:var(--color-carbon-lighter-700);margin:var(--sizing-x6) 0rem}mbo-token-mobile-form .mbo-token-mobile-form__message a{color:var(--color-blue-700)}mbo-token-mobile-form .mbo-token-mobile-form__logo{position:relative;display:flex;width:100%;justify-content:space-evenly}mbo-token-mobile-form .mbo-token-mobile-form__logo img{width:41rem;height:41rem}\n"],encapsulation:2}),y})()},21260:(j,I,t)=>{t.d(I,{$:()=>u,L:()=>c});var n=t(30263),r=t(39904),e=t(75652),i=t(99877),o=t(45542),s=t(35324);let m=(()=>{class a{constructor(v){this.mboProvider=v}ngBoccPortal(v){this.portal=v}onLocation(){this.mboProvider.openUrl(r.BA.GEOLOCATION)}onClose(){this.portal?.close()}}return a.\u0275fac=function(v){return new(v||a)(i.\u0275\u0275directiveInject(e.Z))},a.\u0275cmp=i.\u0275\u0275defineComponent({type:a,selectors:[["mbo-document-error-modal"]],decls:12,vars:1,consts:[[1,"mbo-document-error-modal"],["src","assets/shared/logos/document-error.svg"],[1,"mbo-document-error-modal__body"],[1,"smalltext-medium"],[1,"body2-medium"],[3,"whatsapp"],[1,"mbo-document-error-modal__footer"],["id","btn_statement-error-modal_close","bocc-button","raised",3,"click"]],template:function(v,f){1&v&&(i.\u0275\u0275elementStart(0,"div",0),i.\u0275\u0275element(1,"img",1),i.\u0275\u0275elementStart(2,"div",2)(3,"label",3),i.\u0275\u0275text(4," DOCUMENTO NO DISPONIBLE "),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(5,"p",4),i.\u0275\u0275text(6," Puedes solicitarlo mediante nuestro canal de Whatsapp o en cualquiera de nuestras l\xedneas de atenci\xf3n "),i.\u0275\u0275elementEnd()(),i.\u0275\u0275element(7,"mbo-attention-lines-form",5),i.\u0275\u0275elementStart(8,"div",6)(9,"button",7),i.\u0275\u0275listener("click",function(){return f.onClose()}),i.\u0275\u0275elementStart(10,"span"),i.\u0275\u0275text(11,"Entendido"),i.\u0275\u0275elementEnd()()()()),2&v&&(i.\u0275\u0275advance(7),i.\u0275\u0275property("whatsapp",!0))},dependencies:[o.P,s.V],styles:["mbo-document-error-modal{--pvt-img-width: 44rem;--pvt-row-spacing: var(--sizing-x12)}mbo-document-error-modal .mbo-document-error-modal{display:flex;flex-direction:column;row-gap:var(--pvt-row-spacing)}mbo-document-error-modal .mbo-document-error-modal img{align-self:center;width:var(--pvt-img-width)}mbo-document-error-modal .mbo-document-error-modal__body{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x12);box-sizing:border-box}mbo-document-error-modal .mbo-document-error-modal__body label{text-align:center}mbo-document-error-modal .mbo-document-error-modal__body p{color:var(--color-carbon-lighter-700);text-align:center}mbo-document-error-modal .mbo-document-error-modal__location{width:auto;align-self:center}mbo-document-error-modal .mbo-document-error-modal__footer{padding:var(--sizing-x4);box-sizing:border-box;border-top:1px solid var(--color-carbon-lighter-400)}mbo-document-error-modal .mbo-document-error-modal__footer button{width:100%}@media screen and (max-height: 600px){mbo-document-error-modal{--pvt-img-width: 40rem;--pvt-row-spacing: var(--sizing-x8)}}\n"],encapsulation:2}),a})();t(17007),t(79798);let u=(()=>{class a{constructor(v){this.errorModal=v.create(m)}execute(){this.errorModal.open()}}return a.\u0275fac=function(v){return new(v||a)(i.\u0275\u0275inject(n.iM))},a.\u0275prov=i.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})(),c=(()=>{class a{constructor(v,f){this.modalConfirmation=v,this.mboProvider=f}execute(){this.modalConfirmation.execute({logo:"assets/shared/logos/modals/search-result-none.svg",title:"SIN PRODUCTOS FIDUCIARIOS",description:"\xa1Tranquilo! Est\xe1s a un clic de empezar a cumplir tus metas",message:'Da clic en "Quiero invertir" y crea tu inversi\xf3n',accept:{label:"Quiero invertir",click:()=>{this.mboProvider.openUrl(r.BA.TRUSTFUND)}},decline:{label:"Cancelar"}})}}return a.\u0275fac=function(v){return new(v||a)(i.\u0275\u0275inject(n.$e),i.\u0275\u0275inject(e.Z))},a.\u0275prov=i.\u0275\u0275defineInjectable({token:a,factory:a.\u0275fac,providedIn:"root"}),a})()},22816:(j,I,t)=>{t.d(I,{S:()=>n});class n{constructor(e=4){this.scrollError=e,this.scrollLeft=0,this.scrollTop=0,this.scrollWidth=0,this.scrollHeight=0,this.clientWidth=0,this.clientHeight=0}reset(e){this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollWidth=e.scrollWidth,this.scrollHeight=e.scrollHeight,this.clientWidth=e.clientWidth,this.clientHeight=e.clientHeight}get verticalStart(){return 0===this.scrollTop}get verticalEnd(){return this.scrollTop+this.clientHeight>=this.scrollHeight-this.scrollError}get verticalPercentage(){const e=this.scrollHeight-this.clientHeight;return e>0?this.scrollTop/e*100:0}get horizontalStart(){return 0===this.scrollLeft}get horizontalEnd(){return this.scrollLeft+this.clientWidth>=this.scrollWidth-this.scrollError}get horizontalPercentage(){const e=this.scrollWidth-this.clientWidth;return e>0?this.scrollLeft/e*100:0}}}}]);