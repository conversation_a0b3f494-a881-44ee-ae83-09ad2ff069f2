(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9846],{29846:(E,t,o)=>{o.r(t),o.d(t,{BoccProductBlockModule:()=>M});var n=o(17007),a=o(78007),l=o(99877);const c=[{path:"",redirectTo:"credit-card",pathMatch:"full"},{path:"credit-card",loadChildren:()=>o.e(3832).then(o.bind(o,83832)).then(d=>d.MboCreditCardBlockPageModule)},{path:"debit-card",loadChildren:()=>o.e(2643).then(o.bind(o,22643)).then(d=>d.MboDebitCardBlockPageModule)}];let M=(()=>{class d{}return d.\u0275fac=function(r){return new(r||d)},d.\u0275mod=l.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=l.\u0275\u0275defineInjector({imports:[n.CommonModule,a.RouterModule.forChild(c)]}),d})()}}]);