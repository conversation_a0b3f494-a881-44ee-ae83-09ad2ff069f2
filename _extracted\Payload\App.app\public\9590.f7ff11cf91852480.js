(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9590],{19590:(h,s,t)=>{t.r(s),t.d(s,{startStatusTap:()=>l});var d=t(15861),o=t(42477),_=t(37003),i=t(78635);const l=()=>{const e=window;e.addEventListener("statusTap",()=>{(0,o.e)(()=>{const a=document.elementFromPoint(e.innerWidth/2,e.innerHeight/2);if(!a)return;const n=(0,_.f)(a);n&&new Promise(c=>(0,i.c)(n,c)).then(()=>{(0,o.w)((0,d.Z)(function*(){n.style.setProperty("--overflow","hidden"),yield(0,_.s)(n,300),n.style.removeProperty("--overflow")}))})})})}}}]);