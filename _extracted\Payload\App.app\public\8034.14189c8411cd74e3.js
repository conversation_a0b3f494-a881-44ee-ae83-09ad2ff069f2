(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8034],{37003:(T,g,o)=>{o.d(g,{I:()=>m,a:()=>b,b:()=>d,c:()=>u,d:()=>t,f:()=>x,g:()=>C,i:()=>r,p:()=>n,r:()=>a,s:()=>E});var p=o(15861),s=o(78635),f=o(28909);const d="ion-content",m=".ion-content-scroll-host",_=`${d}, ${m}`,r=i=>"ION-CONTENT"===i.tagName,C=function(){var i=(0,p.Z)(function*(e){return r(e)?(yield new Promise(l=>(0,s.c)(e,l)),e.getScrollElement()):e});return function(l){return i.apply(this,arguments)}}(),b=i=>i.querySelector(m)||i.querySelector(_),x=i=>i.closest(_),E=(i,e)=>r(i)?i.scrollToTop(e):Promise.resolve(i.scrollTo({top:0,left:0,behavior:e>0?"smooth":"auto"})),u=(i,e,l,h)=>r(i)?i.scrollByPoint(e,l,h):Promise.resolve(i.scrollBy({top:l,left:e,behavior:h>0?"smooth":"auto"})),n=i=>(0,f.b)(i,d),t=i=>{if(r(i)){const l=i.scrollY;return i.scrollY=!1,l}return i.style.setProperty("overflow","hidden"),!0},a=(i,e)=>{r(i)?i.scrollY=e:i.style.removeProperty("overflow")}},8034:(T,g,o)=>{o.r(g),o.d(g,{ion_infinite_scroll:()=>r,ion_infinite_scroll_content:()=>u});var p=o(15861),s=o(42477),f=o(37003),c=o(37943),d=o(87036);const r=class{constructor(n){(0,s.r)(this,n),this.ionInfinite=(0,s.d)(this,"ionInfinite",7),this.thrPx=0,this.thrPc=0,this.didFire=!1,this.isBusy=!1,this.onScroll=()=>{const t=this.scrollEl;if(!t||!this.canStart())return 1;const a=this.el.offsetHeight;if(0===a)return 2;const i=t.scrollTop,l=t.offsetHeight,h=0!==this.thrPc?l*this.thrPc:this.thrPx;return("bottom"===this.position?t.scrollHeight-a-i-h-l:i-a-h)<0&&!this.didFire?(this.isLoading=!0,this.didFire=!0,this.ionInfinite.emit(),3):4},this.isLoading=!1,this.threshold="15%",this.disabled=!1,this.position="bottom"}thresholdChanged(){const n=this.threshold;n.lastIndexOf("%")>-1?(this.thrPx=0,this.thrPc=parseFloat(n)/100):(this.thrPx=parseFloat(n),this.thrPc=0)}disabledChanged(){const n=this.disabled;n&&(this.isLoading=!1,this.isBusy=!1),this.enableScrollEvents(!n)}connectedCallback(){var n=this;return(0,p.Z)(function*(){const t=(0,f.f)(n.el);t?(n.scrollEl=yield(0,f.g)(t),n.thresholdChanged(),n.disabledChanged(),"top"===n.position&&(0,s.w)(()=>{n.scrollEl&&(n.scrollEl.scrollTop=n.scrollEl.scrollHeight-n.scrollEl.clientHeight)})):(0,f.p)(n.el)})()}disconnectedCallback(){this.enableScrollEvents(!1),this.scrollEl=void 0}complete(){var n=this;return(0,p.Z)(function*(){const t=n.scrollEl;if(n.isLoading&&t)if(n.isLoading=!1,"top"===n.position){n.isBusy=!0;const a=t.scrollHeight-t.scrollTop;requestAnimationFrame(()=>{(0,s.e)(()=>{const e=t.scrollHeight-a;requestAnimationFrame(()=>{(0,s.w)(()=>{t.scrollTop=e,n.isBusy=!1,n.didFire=!1})})})})}else n.didFire=!1})()}canStart(){return!(this.disabled||this.isBusy||!this.scrollEl||this.isLoading)}enableScrollEvents(n){this.scrollEl&&(n?this.scrollEl.addEventListener("scroll",this.onScroll):this.scrollEl.removeEventListener("scroll",this.onScroll))}render(){const n=(0,c.b)(this);return(0,s.h)(s.H,{key:"c2248d06232dd7771dd155693ec75f9258dc969e",class:{[n]:!0,"infinite-scroll-loading":this.isLoading,"infinite-scroll-enabled":!this.disabled}})}get el(){return(0,s.f)(this)}static get watchers(){return{threshold:["thresholdChanged"],disabled:["disabledChanged"]}}};r.style="ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}";const u=class{constructor(n){(0,s.r)(this,n),this.customHTMLEnabled=c.c.get("innerHTMLTemplatesEnabled",d.E),this.loadingSpinner=void 0,this.loadingText=void 0}componentDidLoad(){if(void 0===this.loadingSpinner){const n=(0,c.b)(this);this.loadingSpinner=c.c.get("infiniteLoadingSpinner",c.c.get("spinner","ios"===n?"lines":"crescent"))}}renderLoadingText(){const{customHTMLEnabled:n,loadingText:t}=this;return n?(0,s.h)("div",{class:"infinite-loading-text",innerHTML:(0,d.a)(t)}):(0,s.h)("div",{class:"infinite-loading-text"},this.loadingText)}render(){const n=(0,c.b)(this);return(0,s.h)(s.H,{key:"2f4afb07bcfe3e12528eb9cee8646a097e0b359f",class:{[n]:!0,[`infinite-scroll-content-${n}`]:!0}},(0,s.h)("div",{key:"af038177bf10c88c8970682487a4328689aaa5f2",class:"infinite-loading"},this.loadingSpinner&&(0,s.h)("div",{key:"1da5d419bc6a978b6a509fdab47dae347fc8d221",class:"infinite-loading-spinner"},(0,s.h)("ion-spinner",{key:"60cc5c64e0a317ac0005d5afe42c4bb8da58136f",name:this.loadingSpinner})),void 0!==this.loadingText&&this.renderLoadingText()))}};u.style={ios:"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}",md:"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}"}}}]);