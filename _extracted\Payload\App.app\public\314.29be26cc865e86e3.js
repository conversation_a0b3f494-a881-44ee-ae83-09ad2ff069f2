(self.webpackChunkapp=self.webpackChunkapp||[]).push([[314,1881],{51881:(i,c,t)=>{t.r(c),t.d(c,{AbstractStore:()=>b,Store:()=>h});var s=t(98699),r=t(42168);class n{constructor(e){this.value=e,this.subject=new r.BehaviorSubject((0,s.deepFreeze)(this.value))}getCurrent(){return this.subject.value}reset(){this.reduce(()=>this.value)}reduce(e){try{return this.subject.next((0,s.deepFreeze)(e(this.subject.value))),!0}catch{return!1}}select(e){return e(this.subject.value)}observe(){return this.subject.asObservable()}subscribe(e){return this.observe().subscribe(e)}}class b{}class h{constructor(e){this.state=new n(e)}get currentState(){return this.state.getCurrent()}reset(){this.state.reset()}subscribe(e){const u=this.state.subscribe(e);return()=>{u.unsubscribe()}}reduce(e){return this.state.reduce(e)}select(e){return this.state.select(e)}observe(e){return this.state.observe().pipe((0,r.map)(u=>e(u)))}}}}]);