(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6149],{46149:(P,r,n)=>{n.r(r),n.d(r,{MboTransfiyaApprovedDestinationPageModule:()=>A});var c=n(17007),l=n(78007),v=n(79798),m=n(30263),f=n(15861),u=n(39904),g=n(95437),s=n(17698),e=n(99877),y=n(48774),h=n(4663);const d=u.Z6.TRANSFERS.TRANSFIYA.APPROVED;let b=(()=>{class o{constructor(t,a,p){this.mboProvider=t,this.requestConfiguration=a,this.managerTransfiya=p,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_transfiya-approved-destination_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.next(d.HOME)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(t){this.managerTransfiya.setProduct(t).when({success:()=>{this.mboProvider.navigation.next(d.CONFIRMATION)}})}initializatedConfiguration(){var t=this;return(0,f.Z)(function*(){(yield t.requestConfiguration.destination()).when({success:({products:a})=>{t.products=a}},()=>{t.requesting=!1})})()}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(g.ZL),e.\u0275\u0275directiveInject(s.NF),e.\u0275\u0275directiveInject(s.Pm))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfiya-approved-destination-page"]],decls:6,vars:3,consts:[[1,"mbo-transfiya-approved-destination-page__content"],[1,"mbo-transfiya-approved-destination-page__header"],["title","Destino","progress","75%",3,"rightAction"],[1,"mbo-transfiya-approved-destination-page__body"],[3,"skeleton","products","select"]],template:function(t,a){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),e.\u0275\u0275listener("select",function(M){return a.onProduct(M)}),e.\u0275\u0275text(5," Selecciona la cuenta d\xf3nde deseas recibir el dinero "),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("rightAction",a.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",a.requesting)("products",a.products))},dependencies:[y.J,h.c],styles:["/*!\n * MBO TransfiyaApprovedDestination Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-approved-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-approved-destination-page .mbo-transfiya-approved-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-approved-destination-page .mbo-transfiya-approved-destination-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),o})(),A=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[c.CommonModule,l.RouterModule.forChild([{path:"",component:b}]),m.Jx,v.cV]}),o})()}}]);