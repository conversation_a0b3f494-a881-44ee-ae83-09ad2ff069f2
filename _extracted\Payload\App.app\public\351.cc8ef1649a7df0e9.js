(self.webpackChunkapp=self.webpackChunkapp||[]).push([[351],{20351:(D,l,t)=>{t.r(l),t.d(l,{MboTransferAdvanceDestinationPageModule:()=>T});var f=t(17007),v=t(78007),u=t(79798),g=t(30263),p=t(15861),h=t(39904),b=t(95437),m=t(64847),A=t(80045),e=t(99877),M=t(48774),P=t(13043);const r=h.Z6.TRANSFERS.ADVANCE;let I=(()=>{class a{constructor(n,o,c,i,d){this.activateRoute=n,this.mboProvider=o,this.requestConfiguration=c,this.managerAdvance=i,this.cancelProvider=d,this.confirmation=!1,this.backInvalid=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_transfer-advance-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.requesting||this.confirmation||this.backInvalid,click:()=>{this.mboProvider.navigation.back(r.SOURCE)}},this.cancelAction={id:"btn_transfer-advance-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(n){this.managerAdvance.setDestination(n).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?r.CONFIRMATION:r.AMOUNT)}})}initializatedConfiguration(){var n=this;return(0,p.Z)(function*(){const{productId:o}=n.activateRoute.snapshot.queryParams;(yield n.requestConfiguration.destination(o)).when({success:({affiliations:c,confirmation:i,products:d,source:C})=>{if(!C)return n.mboProvider.navigation.back(r.SOURCE);n.backInvalid=d.length<2,n.products=c,n.confirmation=i}},()=>{n.requesting=!1})})()}}return a.\u0275fac=function(n){return new(n||a)(e.\u0275\u0275directiveInject(v.ActivatedRoute),e.\u0275\u0275directiveInject(b.ZL),e.\u0275\u0275directiveInject(m.m),e.\u0275\u0275directiveInject(m.a),e.\u0275\u0275directiveInject(A.j))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfer-advance-destination-page"]],decls:6,vars:5,consts:[[1,"mbo-transfer-advance-destination-page__content"],[1,"mbo-transfer-advance-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-transfer-advance-destination-page__body"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","automaticSelection","select"]],template:function(n,o){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-destination-selector",4),e.\u0275\u0275listener("select",function(i){return o.onProduct(i)}),e.\u0275\u0275text(5," \xbfA cual cuenta deseas enviar el avance? "),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",o.requesting)("products",o.products)("automaticSelection",!0))},dependencies:[M.J,P.e],styles:["/*!\n * MBO TransferAdvanceDestination Page\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 08/Jul/2022\n * Updated: 09/Ene/2024\n*/mbo-transfer-advance-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfer-advance-destination-page .mbo-transfer-advance-destination-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-advance-destination-page .mbo-transfer-advance-destination-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}\n"],encapsulation:2}),a})(),T=(()=>{class a{}return a.\u0275fac=function(n){return new(n||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[f.CommonModule,v.RouterModule.forChild([{path:"",component:I}]),g.Jx,u.eM]}),a})()}}]);