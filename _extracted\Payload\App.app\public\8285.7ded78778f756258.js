(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8285],{8285:(h,l,o)=>{o.r(l),o.d(l,{MboSignoutModule:()=>d});var M=o(17007),a=o(78007),t=o(99877);const u=[{path:"",redirectTo:"inactivity",pathMatch:"full"},{path:"inactivity",loadChildren:()=>o.e(5833).then(o.bind(o,15833)).then(n=>n.MboSignoutInactivityPageModule)},{path:"timeout",loadChildren:()=>o.e(283).then(o.bind(o,20283)).then(n=>n.MboSignoutTimeoutPageModule)}];let d=(()=>{class n{}return n.\u0275fac=function(g){return new(g||n)},n.\u0275mod=t.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=t.\u0275\u0275defineInjector({imports:[M.CommonModule,a.RouterModule.forChild(u)]}),n})()}}]);