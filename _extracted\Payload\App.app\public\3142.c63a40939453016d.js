(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3142],{13142:(T,t,n)=>{n.r(t),n.d(t,{MboTransfiyaTransferModule:()=>s});var l=n(17007),d=n(78007),a=n(99877);const M=[{path:"",redirectTo:"source",pathMatch:"full"},{path:"source",loadChildren:()=>n.e(688).then(n.bind(n,80688)).then(o=>o.MboTransfiyaTransferSourcePageModule)},{path:"destination",loadChildren:()=>n.e(9609).then(n.bind(n,39609)).then(o=>o.MboTransfiyaTransferDestinationPageModule)},{path:"amount",loadChildren:()=>n.e(5055).then(n.bind(n,35055)).then(o=>o.MboTransfiyaTransferAmountPageModule)},{path:"confirmation",loadChildren:()=>n.e(5773).then(n.bind(n,65773)).then(o=>o.MboTransfiyaTransferConfirmationPageModule)},{path:"result",loadChildren:()=>n.e(8535).then(n.bind(n,18535)).then(o=>o.MboTransfiyaTransferResultPageModule)}];let s=(()=>{class o{}return o.\u0275fac=function(h){return new(h||o)},o.\u0275mod=a.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=a.\u0275\u0275defineInjector({imports:[l.CommonModule,d.RouterModule.forChild(M)]}),o})()}}]);