(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5107],{34753:(l0,R,k)=>{let _;k.r(R),k.d(R,{NIL:()=>o0,parse:()=>S,stringify:()=>O,v1:()=>K,v3:()=>z,v4:()=>e0,v5:()=>f0,validate:()=>I,version:()=>s0});const V=new Uint8Array(16);function C(){if(!_&&(_=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!_))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return _(V)}const H=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,I=function j(n){return"string"==typeof n&&H.test(n)},d=[];for(let n=0;n<256;++n)d.push((n+256).toString(16).slice(1));function y(n,c=0){return d[n[c+0]]+d[n[c+1]]+d[n[c+2]]+d[n[c+3]]+"-"+d[n[c+4]]+d[n[c+5]]+"-"+d[n[c+6]]+d[n[c+7]]+"-"+d[n[c+8]]+d[n[c+9]]+"-"+d[n[c+10]]+d[n[c+11]]+d[n[c+12]]+d[n[c+13]]+d[n[c+14]]+d[n[c+15]]}const O=function B(n,c=0){const e=y(n,c);if(!I(e))throw TypeError("Stringified UUID is invalid");return e};let M,x,D=0,T=0;const K=function W(n,c,e){let t=c&&e||0;const r=c||new Array(16);let f=(n=n||{}).node||M,o=void 0!==n.clockseq?n.clockseq:x;if(null==f||null==o){const a=n.random||(n.rng||C)();null==f&&(f=M=[1|a[0],a[1],a[2],a[3],a[4],a[5]]),null==o&&(o=x=16383&(a[6]<<8|a[7]))}let s=void 0!==n.msecs?n.msecs:Date.now(),i=void 0!==n.nsecs?n.nsecs:T+1;const l=s-D+(i-T)/1e4;if(l<0&&void 0===n.clockseq&&(o=o+1&16383),(l<0||s>D)&&void 0===n.nsecs&&(i=0),i>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");D=s,T=i,x=o,s+=122192928e5;const p=(1e4*(268435455&s)+i)%4294967296;r[t++]=p>>>24&255,r[t++]=p>>>16&255,r[t++]=p>>>8&255,r[t++]=255&p;const U=s/4294967296*1e4&268435455;r[t++]=U>>>8&255,r[t++]=255&U,r[t++]=U>>>24&15|16,r[t++]=U>>>16&255,r[t++]=o>>>8|128,r[t++]=255&o;for(let a=0;a<6;++a)r[t+a]=f[a];return c||y(r)},S=function $(n){if(!I(n))throw TypeError("Invalid UUID");let c;const e=new Uint8Array(16);return e[0]=(c=parseInt(n.slice(0,8),16))>>>24,e[1]=c>>>16&255,e[2]=c>>>8&255,e[3]=255&c,e[4]=(c=parseInt(n.slice(9,13),16))>>>8,e[5]=255&c,e[6]=(c=parseInt(n.slice(14,18),16))>>>8,e[7]=255&c,e[8]=(c=parseInt(n.slice(19,23),16))>>>8,e[9]=255&c,e[10]=(c=parseInt(n.slice(24,36),16))/1099511627776&255,e[11]=c/4294967296&255,e[12]=c>>>24&255,e[13]=c>>>16&255,e[14]=c>>>8&255,e[15]=255&c,e},G="6ba7b810-9dad-11d1-80b4-00c04fd430c8",J="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function E(n,c,e){function t(r,f,o,s){var i;if("string"==typeof r&&(r=function F(n){n=unescape(encodeURIComponent(n));const c=[];for(let e=0;e<n.length;++e)c.push(n.charCodeAt(e));return c}(r)),"string"==typeof f&&(f=S(f)),16!==(null===(i=f)||void 0===i?void 0:i.length))throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+r.length);if(l.set(f),l.set(r,f.length),l=e(l),l[6]=15&l[6]|c,l[8]=63&l[8]|128,o){s=s||0;for(let p=0;p<16;++p)o[s+p]=l[p];return o}return y(l)}try{t.name=n}catch{}return t.DNS=G,t.URL=J,t}function L(n){return 14+(n+64>>>9<<4)+1}function v(n,c){const e=(65535&n)+(65535&c);return(n>>16)+(c>>16)+(e>>16)<<16|65535&e}function A(n,c,e,t,r,f){return v(function Z(n,c){return n<<c|n>>>32-c}(v(v(c,n),v(t,f)),r),e)}function h(n,c,e,t,r,f,o){return A(c&e|~c&t,n,c,r,f,o)}function m(n,c,e,t,r,f,o){return A(c&t|e&~t,n,c,r,f,o)}function g(n,c,e,t,r,f,o){return A(c^e^t,n,c,r,f,o)}function w(n,c,e,t,r,f,o){return A(e^(c|~t),n,c,r,f,o)}const z=E("v3",48,function P(n){if("string"==typeof n){const c=unescape(encodeURIComponent(n));n=new Uint8Array(c.length);for(let e=0;e<c.length;++e)n[e]=c.charCodeAt(e)}return function Q(n){const c=[],e=32*n.length,t="0123456789abcdef";for(let r=0;r<e;r+=8){const f=n[r>>5]>>>r%32&255,o=parseInt(t.charAt(f>>>4&15)+t.charAt(15&f),16);c.push(o)}return c}(function X(n,c){n[c>>5]|=128<<c%32,n[L(c)-1]=c;let e=1732584193,t=-271733879,r=-1732584194,f=271733878;for(let o=0;o<n.length;o+=16){const s=e,i=t,l=r,p=f;e=h(e,t,r,f,n[o],7,-680876936),f=h(f,e,t,r,n[o+1],12,-389564586),r=h(r,f,e,t,n[o+2],17,606105819),t=h(t,r,f,e,n[o+3],22,-1044525330),e=h(e,t,r,f,n[o+4],7,-176418897),f=h(f,e,t,r,n[o+5],12,1200080426),r=h(r,f,e,t,n[o+6],17,-1473231341),t=h(t,r,f,e,n[o+7],22,-45705983),e=h(e,t,r,f,n[o+8],7,1770035416),f=h(f,e,t,r,n[o+9],12,-1958414417),r=h(r,f,e,t,n[o+10],17,-42063),t=h(t,r,f,e,n[o+11],22,-1990404162),e=h(e,t,r,f,n[o+12],7,1804603682),f=h(f,e,t,r,n[o+13],12,-40341101),r=h(r,f,e,t,n[o+14],17,-1502002290),t=h(t,r,f,e,n[o+15],22,1236535329),e=m(e,t,r,f,n[o+1],5,-165796510),f=m(f,e,t,r,n[o+6],9,-1069501632),r=m(r,f,e,t,n[o+11],14,643717713),t=m(t,r,f,e,n[o],20,-373897302),e=m(e,t,r,f,n[o+5],5,-701558691),f=m(f,e,t,r,n[o+10],9,38016083),r=m(r,f,e,t,n[o+15],14,-660478335),t=m(t,r,f,e,n[o+4],20,-405537848),e=m(e,t,r,f,n[o+9],5,568446438),f=m(f,e,t,r,n[o+14],9,-1019803690),r=m(r,f,e,t,n[o+3],14,-187363961),t=m(t,r,f,e,n[o+8],20,1163531501),e=m(e,t,r,f,n[o+13],5,-1444681467),f=m(f,e,t,r,n[o+2],9,-51403784),r=m(r,f,e,t,n[o+7],14,1735328473),t=m(t,r,f,e,n[o+12],20,-1926607734),e=g(e,t,r,f,n[o+5],4,-378558),f=g(f,e,t,r,n[o+8],11,-2022574463),r=g(r,f,e,t,n[o+11],16,1839030562),t=g(t,r,f,e,n[o+14],23,-35309556),e=g(e,t,r,f,n[o+1],4,-1530992060),f=g(f,e,t,r,n[o+4],11,1272893353),r=g(r,f,e,t,n[o+7],16,-155497632),t=g(t,r,f,e,n[o+10],23,-1094730640),e=g(e,t,r,f,n[o+13],4,681279174),f=g(f,e,t,r,n[o],11,-358537222),r=g(r,f,e,t,n[o+3],16,-722521979),t=g(t,r,f,e,n[o+6],23,76029189),e=g(e,t,r,f,n[o+9],4,-640364487),f=g(f,e,t,r,n[o+12],11,-421815835),r=g(r,f,e,t,n[o+15],16,530742520),t=g(t,r,f,e,n[o+2],23,-995338651),e=w(e,t,r,f,n[o],6,-198630844),f=w(f,e,t,r,n[o+7],10,1126891415),r=w(r,f,e,t,n[o+14],15,-1416354905),t=w(t,r,f,e,n[o+5],21,-57434055),e=w(e,t,r,f,n[o+12],6,1700485571),f=w(f,e,t,r,n[o+3],10,-1894986606),r=w(r,f,e,t,n[o+10],15,-1051523),t=w(t,r,f,e,n[o+1],21,-2054922799),e=w(e,t,r,f,n[o+8],6,1873313359),f=w(f,e,t,r,n[o+15],10,-30611744),r=w(r,f,e,t,n[o+6],15,-1560198380),t=w(t,r,f,e,n[o+13],21,1309151649),e=w(e,t,r,f,n[o+4],6,-145523070),f=w(f,e,t,r,n[o+11],10,-1120210379),r=w(r,f,e,t,n[o+2],15,718787259),t=w(t,r,f,e,n[o+9],21,-343485551),e=v(e,s),t=v(t,i),r=v(r,l),f=v(f,p)}return[e,t,r,f]}(function Y(n){if(0===n.length)return[];const c=8*n.length,e=new Uint32Array(L(c));for(let t=0;t<c;t+=8)e[t>>5]|=(255&n[t/8])<<t%32;return e}(n),8*n.length))}),N={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},e0=function n0(n,c,e){if(N.randomUUID&&!c&&!n)return N.randomUUID();const t=(n=n||{}).random||(n.rng||C)();if(t[6]=15&t[6]|64,t[8]=63&t[8]|128,c){e=e||0;for(let r=0;r<16;++r)c[e+r]=t[r];return c}return y(t)};function t0(n,c,e,t){switch(n){case 0:return c&e^~c&t;case 1:case 3:return c^e^t;case 2:return c&e^c&t^e&t}}function b(n,c){return n<<c|n>>>32-c}const f0=E("v5",80,function r0(n){const c=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof n){const o=unescape(encodeURIComponent(n));n=[];for(let s=0;s<o.length;++s)n.push(o.charCodeAt(s))}else Array.isArray(n)||(n=Array.prototype.slice.call(n));n.push(128);const r=Math.ceil((n.length/4+2)/16),f=new Array(r);for(let o=0;o<r;++o){const s=new Uint32Array(16);for(let i=0;i<16;++i)s[i]=n[64*o+4*i]<<24|n[64*o+4*i+1]<<16|n[64*o+4*i+2]<<8|n[64*o+4*i+3];f[o]=s}f[r-1][14]=8*(n.length-1)/Math.pow(2,32),f[r-1][14]=Math.floor(f[r-1][14]),f[r-1][15]=8*(n.length-1)&4294967295;for(let o=0;o<r;++o){const s=new Uint32Array(80);for(let u=0;u<16;++u)s[u]=f[o][u];for(let u=16;u<80;++u)s[u]=b(s[u-3]^s[u-8]^s[u-14]^s[u-16],1);let i=e[0],l=e[1],p=e[2],U=e[3],a=e[4];for(let u=0;u<80;++u){const q=Math.floor(u/20),i0=b(i,5)+t0(q,l,p,U)+a+c[q]+s[u]>>>0;a=U,U=p,p=b(l,30)>>>0,l=i,i=i0}e[0]=e[0]+i>>>0,e[1]=e[1]+l>>>0,e[2]=e[2]+p>>>0,e[3]=e[3]+U>>>0,e[4]=e[4]+a>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,255&e[0],e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,255&e[1],e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,255&e[2],e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,255&e[3],e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,255&e[4]]}),o0="00000000-0000-0000-0000-000000000000",s0=function c0(n){if(!I(n))throw TypeError("Invalid UUID");return parseInt(n.slice(14,15),16)}}}]);