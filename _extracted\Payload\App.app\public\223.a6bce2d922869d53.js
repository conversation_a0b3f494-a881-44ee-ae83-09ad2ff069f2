(self.webpackChunkapp=self.webpackChunkapp||[]).push([[223],{30223:(i,a,e)=>{e.r(a),e.d(a,{DeviceManagerWeb:()=>r});var s=e(17737);class r extends s.WebPlugin{requestInformation(){return Promise.resolve({compilationCode:"1054",services:"web",versionCode:"1.0.0"})}hasGoogleServices(){return Promise.resolve({availability:!1})}hasHuaweiServices(){return Promise.resolve({availability:!1})}hasAppleServices(){return Promise.resolve({availability:!1})}}}}]);