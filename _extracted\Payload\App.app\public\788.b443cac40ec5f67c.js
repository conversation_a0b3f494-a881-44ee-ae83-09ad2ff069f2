(self.webpackChunkapp=self.webpackChunkapp||[]).push([[788],{90788:(z,c,t)=>{t.r(c),t.d(c,{MboTransferAdvanceAmountPageModule:()=>I});var v=t(17007),p=t(78007),i=t(30263),g=t(15861),s=t(24495),f=t(39904),b=t(95437),h=t(57544),l=t(64847),A=t(80045),n=t(99877),C=t(35641),y=t(48774),M=t(83413),P=t(45542);const u=f.Z6.TRANSFERS.ADVANCE;let T=(()=>{class o{constructor(a,e,m,r){this.mboProvider=a,this.requestConfiguration=e,this.managerAdvance=m,this.cancelProvider=r,this.confirmation=!1,this.requesting=!0,this.backAction={id:"btn_transfer-advance-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(u.DESTINATION)}},this.cancelAction={id:"btn_transfer-advance-amount_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.amountControl=new h.FormControl}ngOnInit(){this.initializatedConfiguration()}get disabled(){return this.amountControl.invalid||this.requesting}onSubmit(){this.managerAdvance.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(u.CONFIRMATION)}})}initializatedConfiguration(){var a=this;return(0,g.Z)(function*(){(yield a.requestConfiguration.amount()).when({success:({amount:e,confirmation:m,section:r,source:j})=>{a.confirmation=m,a.product=j,a.quotaAvailable=r?.value,a.quotaLabel=r.label,e&&a.amountControl.setValue(e),a.setValidators(r)}},()=>{a.requesting=!1})})()}setValidators(a){const e=[s.C1,s.LU,s.PO];a&&e.push((0,s.v6)(a.value)),this.amountControl.setValidators(e)}}return o.\u0275fac=function(a){return new(a||o)(n.\u0275\u0275directiveInject(b.ZL),n.\u0275\u0275directiveInject(l.m),n.\u0275\u0275directiveInject(l.a),n.\u0275\u0275directiveInject(A.j))},o.\u0275cmp=n.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfer-advance-amount-page"]],decls:13,vars:13,consts:[[1,"mbo-transfer-advance-amount-page__content"],[1,"mbo-transfer-advance-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfer-advance-amount-page__body"],[1,"mbo-transfer-advance-amount-page__message","subtitle2-medium"],["id","txt_transfer-advance-amount_value","label","Valor del avance","placeholder","0","type","money","prefix","$",3,"formControl"],[3,"color","icon","title","number","subtitle","amount","hidden"],[3,"skeleton","hidden"],[1,"mbo-transfer-advance-amount-page__footer"],["id","btn_transfer-advance-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(a,e){1&a&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(3,"div",3)(4,"p",4),n.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas usar en el avance? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6)(8,"bocc-card-product-summary",7),n.\u0275\u0275elementEnd()(),n.\u0275\u0275elementStart(9,"div",8)(10,"button",9),n.\u0275\u0275listener("click",function(){return e.onSubmit()}),n.\u0275\u0275elementStart(11,"span"),n.\u0275\u0275text(12,"Continuar"),n.\u0275\u0275elementEnd()()()),2&a&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("leftAction",e.backAction)("rightAction",e.cancelAction),n.\u0275\u0275advance(4),n.\u0275\u0275property("formControl",e.amountControl),n.\u0275\u0275advance(1),n.\u0275\u0275property("color",null==e.product?null:e.product.color)("icon",null==e.product?null:e.product.logo)("title",null==e.product?null:e.product.nickname)("number",null==e.product?null:e.product.shortNumber)("subtitle",e.quotaLabel)("amount",e.quotaAvailable)("hidden",e.requesting),n.\u0275\u0275advance(1),n.\u0275\u0275property("skeleton",!0)("hidden",!e.requesting),n.\u0275\u0275advance(2),n.\u0275\u0275property("disabled",e.disabled))},dependencies:[C.d,y.J,M.D,P.P],styles:["/*!\n * MBO TransferAdvanceAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 09/Jul/2022\n * Updated: 07/Ene/2024\n*/mbo-transfer-advance-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-transfer-advance-amount-page .mbo-transfer-advance-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfer-advance-amount-page .mbo-transfer-advance-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfer-advance-amount-page .mbo-transfer-advance-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfer-advance-amount-page .mbo-transfer-advance-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-advance-amount-page .mbo-transfer-advance-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfer-advance-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20)}}\n"],encapsulation:2}),o})(),I=(()=>{class o{}return o.\u0275fac=function(a){return new(a||o)},o.\u0275mod=n.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=n.\u0275\u0275defineInjector({imports:[v.CommonModule,p.RouterModule.forChild([{path:"",component:T}]),i.dH,i.Jx,i.D1,i.P8]}),o})()}}]);