(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9221],{80174:(T,m,t)=>{t.d(m,{HV:()=>e,Js:()=>d,Pt:()=>s});var n=t(89148);const s={accountNumberSize:20,products:[]},d={accountNumberSize:20,products:[n.Gt.SavingAccount,n.Gt.CheckingAccount,n.Gt.ElectronicDeposit]},e={"0283":{accountNumberSize:8,products:[n.Gt.SavingAccount,n.Gt.ElectronicDeposit]},"0809":{accountNumberSize:8,products:[n.Gt.SavingAccount]},"0819":{accountNumberSize:17,products:[n.Gt.SavingAccount,n.Gt.ElectronicDeposit]}}},99076:(T,m,t)=>{t.d(m,{I:()=>v,m:()=>u});var n=t(87903),E=t(53113),s=t(80174);function d(a){const{isError:g,message:f,type:c}=a;return{animation:(0,n.jY)(a),title:g?"\xa1Transferencia fallida!":"PENDING"===c?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:f}}function e({isError:a}){return a?[(0,n.wT)("Finalizar","finish","outline"),(0,n.wT)("Volver a intentar","retry")]:[(0,n.wT)("Hacer otra transferencia","retry","outline"),(0,n.wT)("Finalizar","finish")]}function u(a){const{dateFormat:g,timeFormat:f}=new E.ou,{status:c,transfer:i}=a,p=[(0,n.SP)("DESTINO",i.destinationName,i.destination.number,i.destination.bankName),(0,n._f)("SUMA DE",i.amount)];return i.note&&p.push((0,n.SP)("DESCRIPCI\xd3N",i.note.description,"",i.note.reference)),p.push((0,n.cZ)(g,f)),{actions:e(c),error:c.isError,header:d(c),informations:p,skeleton:!1}}function v(a){return a?s.HV[a.id]||s.Js:s.Pt}},49221:(T,m,t)=>{t.r(m),t.d(m,{MboTransferGenericResultPageModule:()=>A});var n=t(17007),E=t(78007),s=t(79798),d=t(15861),e=t(99877),u=t(39904),v=t(95437),a=t(99076),g=t(99013),f=t(10464),c=t(78021),i=t(16442);function p(o,h){if(1&o&&(e.\u0275\u0275elementStart(0,"div",4),e.\u0275\u0275element(1,"mbo-header-result",5),e.\u0275\u0275elementEnd()),2&o){const r=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("rightActions",r.rightActions)}}let R=(()=>{class o{constructor(r,l,b){this.ref=r,this.mboProvider=l,this.managerTransfer=b,this.requesting=!0,this.template=u.$d,this.rightActions=[{id:"btn_transfer-generic-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfer-generic-result-page_template"),this.initializatedTransaction()}onAction(r){this.mboProvider.navigation.next("finish"===r?u.Z6.CUSTOMER.PRODUCTS.HOME:u.Z6.TRANSFERS.GENERIC.SOURCE)}initializatedTransaction(){var r=this;return(0,d.Z)(function*(){(yield r.managerTransfer.send()).when({success:l=>{r.template=(0,a.m)(l)}},()=>{r.requesting=!1,r.managerTransfer.reset()})})()}}return o.\u0275fac=function(r){return new(r||o)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(v.ZL),e.\u0275\u0275directiveInject(g.Al))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["mbo-transfer-generic-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfer-generic-result-page__content","mbo-page__scroller"],["class","mbo-transfer-generic-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfer-generic-result-page__body"],["id","crd_transfer-generic-result-page_template",3,"template","action"],[1,"mbo-transfer-generic-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(r,l){1&r&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),e.\u0275\u0275template(2,p,2,1,"div",1),e.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),e.\u0275\u0275listener("action",function(M){return l.onAction(M)}),e.\u0275\u0275elementEnd()()()()),2&r&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!l.requesting),e.\u0275\u0275advance(2),e.\u0275\u0275property("template",l.template))},dependencies:[n.NgIf,f.K,c.c,i.u],styles:["/*!\n * MBO TransferGenericResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 28/Jun/2022\n * Updated: 07/Ene/2024\n*/mbo-transfer-generic-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-generic-result-page .mbo-transfer-generic-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfer-generic-result-page .mbo-transfer-generic-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),o})(),A=(()=>{class o{}return o.\u0275fac=function(r){return new(r||o)},o.\u0275mod=e.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=e.\u0275\u0275defineInjector({imports:[n.CommonModule,E.RouterModule.forChild([{path:"",component:R}]),s.KI,s.cN,s.tu]}),o})()}}]);