(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5138],{37907:(P,v,r)=>{r.d(v,{L:()=>p,m:()=>M});var l=r(15861),u=r(87956),g=r(53113),s=r(98699);class m{constructor(c,t,e){this.source=c,this.destination=t,this.amount=e}}function b(o){return new m(o.source,o.destination,o.amount)}var U=r(71776),d=r(39904),C=r(87903),n=r(42168),h=r(84757),a=r(99877);let Q=(()=>{class o{constructor(t){this.http=t}send(t){const e={hashCheckingAcct:t.destination.id,hashLoanAcct:t.source.id,amount:String(t.amount)};return(0,n.firstValueFrom)(this.http.post(d.bV.TRANSACTIONS.CREDIT_QUOTA,e).pipe((0,h.map)(i=>(0,C.l1)(i,"SUCCESS")))).catch(i=>(0,C.rU)(i))}}return o.\u0275fac=function(t){return new(t||o)(a.\u0275\u0275inject(U.HttpClient))},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var S=r(20691);let E=(()=>{class o extends S.Store{constructor(){super({confirmation:!1,fromCustomer:!1})}setSource(t,e=!1){this.reduce(i=>({...i,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setDestination(t){this.reduce(e=>({...e,destination:t}))}setAmount(t){this.reduce(e=>({...e,amount:t}))}selectForAmount(){return this.select(({confirmation:t,amount:e,source:i})=>({amount:e,confirmation:t,source:i}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),p=(()=>{class o{constructor(t,e,i,f){this.products=t,this.eventBusService=e,this.repository=i,this.store=f}setSource(t){var e=this;return(0,l.Z)(function*(){try{return yield e.products.requestInformation(t),s.Either.success(e.store.setSource(t))}catch({message:i}){return s.Either.failure({message:i})}})()}setDestination(t){try{return s.Either.success(this.store.setDestination(t))}catch({message:e}){return s.Either.failure({message:e})}}setAmount(t){try{return s.Either.success(this.store.setAmount(t))}catch({message:e}){return s.Either.failure({message:e})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getSource();return this.store.reset(),s.Either.success({fromCustomer:t,source:e})}catch({message:t}){return s.Either.failure({message:t})}}send(){var t=this;return(0,l.Z)(function*(){const e=b(t.store.currentState),i=yield t.save(e);return t.eventBusService.emit(i.channel),s.Either.success({creditUseQuota:e,status:i})})()}save(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(g.LN.error(e))}}}return o.\u0275fac=function(t){return new(t||o)(a.\u0275\u0275inject(u.M5),a.\u0275\u0275inject(u.Yd),a.\u0275\u0275inject(Q),a.\u0275\u0275inject(E))},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var y=r(89148);let M=(()=>{class o{constructor(t,e,i){this.products=t,this.productService=e,this.store=i}source(){var t=this;return(0,l.Z)(function*(){try{const e=t.store.itIsConfirmation(),i=yield t.requestCredits();return s.Either.success({confirmation:e,products:i})}catch({message:e}){return s.Either.failure({message:e})}})()}destination(t){var e=this;return(0,l.Z)(function*(){try{const i=yield e.products.requestAccountsForTransfer(),f=yield e.requestCredits(),A=e.store.itIsConfirmation(),I=e.requestCredit(f,t);return s.Either.success({accounts:i,confirmation:A,products:f,source:I})}catch({message:i}){return s.Either.failure({message:i})}})()}amount(){var t=this;return(0,l.Z)(function*(){try{const{amount:e,confirmation:i,source:f}=t.store.selectForAmount(),A=yield t.requestSection(f);return s.Either.success({confirmation:i,section:A,source:f,value:e})}catch({message:e}){return s.Either.failure({message:e})}})()}confirmation(){try{const t=b(this.store.currentState);return s.Either.success({creditUseQuota:t})}catch({message:t}){return s.Either.failure({message:t})}}requestCredits(){return this.products.requestProducts([y.Gt.ResolvingCredit])}requestCredit(t,e){let i=this.store.getSource();return!i&&e&&(i=t.find(({id:f})=>e===f),this.store.setSource(i,!0)),i}requestSection(t){return this.productService.requestInformation(t).then(e=>e?.getSection(y.Av.LoanQuotaAvailable))}}return o.\u0275fac=function(t){return new(t||o)(a.\u0275\u0275inject(u.hM),a.\u0275\u0275inject(u.M5),a.\u0275\u0275inject(E))},o.\u0275prov=a.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},44793:(P,v,r)=>{r.d(v,{Z:()=>U});var l=r(30263),u=r(39904),g=r(95437),s=r(37907),m=r(99877);let U=(()=>{class d{constructor(n,h,a){this.modalConfirmation=n,this.mboProvider=h,this.managerUseQuota=a}execute(n=!0){n?this.modalConfirmation.execute({title:"Cancelar transacci\xf3n",message:"\xbfEstas seguro que deseas cancelar la transferencia de cupo actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerUseQuota.reset().when({success:({fromCustomer:n,source:h})=>{n?this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.INFO,{productId:h.id}):this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.HOME)},failure:()=>{this.mboProvider.navigation.back(u.Z6.CUSTOMER.PRODUCTS.HOME)}})}}return d.\u0275fac=function(n){return new(n||d)(m.\u0275\u0275inject(l.$e),m.\u0275\u0275inject(g.ZL),m.\u0275\u0275inject(s.L))},d.\u0275prov=m.\u0275\u0275defineInjectable({token:d,factory:d.\u0275fac,providedIn:"root"}),d})()},85138:(P,v,r)=>{r.r(v),r.d(v,{MboCreditUseQuotaConfirmationPageModule:()=>M});var l=r(17007),u=r(78007),g=r(79798),s=r(30263),m=r(15861),b=r(39904),U=r(95437),d=r(37907),C=r(44793),n=r(99877),h=r(10464),a=r(48774),Q=r(45542),S=r(17941),E=r(66613);const p=b.Z6.TRANSACTIONS.CREDIT_USE_QUOTA;let y=(()=>{class o{constructor(t,e,i){this.mboProvider=t,this.requestConfiguration=e,this.creditQuotaCancel=i,this.backAction={id:"btn_credit-quota-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(p.AMOUNT)}},this.cancelAction={id:"btn_credit-quota-confirmation_cancel",label:"Cancelar",click:()=>{this.creditQuotaCancel.execute()}},this.destinationActions=[{id:"btn_credit-quota-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(p.DESTINATION)}}],this.amountActions=[{id:"btn_credit-quota-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(p.AMOUNT)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(p.RESULT)}initializatedConfiguration(){var t=this;return(0,m.Z)(function*(){(yield t.requestConfiguration.confirmation()).when({success:({creditUseQuota:e})=>{t.creditUseQuota=e}})})()}}return o.\u0275fac=function(t){return new(t||o)(n.\u0275\u0275directiveInject(U.ZL),n.\u0275\u0275directiveInject(d.m),n.\u0275\u0275directiveInject(C.Z))},o.\u0275cmp=n.\u0275\u0275defineComponent({type:o,selectors:[["mbo-credit-use-quota-confirmation-page"]],decls:20,vars:11,consts:[[1,"mbo-credit-use-quota-confirmation-page__content","mbo-page__scroller"],[1,"mbo-credit-use-quota-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-credit-use-quota-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],["icon","bell",3,"visible"],[1,"mbo-credit-use-quota-confirmation-page__footer"],["id","btn_credit-quota-confirmation_submit","bocc-button","raised","prefixIcon","transaction-next",3,"click"]],template:function(t,e){1&t&&(n.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),n.\u0275\u0275element(3,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),n.\u0275\u0275text(7," \xbfDeseas transferirle a? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"div",6),n.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),n.\u0275\u0275elementStart(12,"bocc-alert",10),n.\u0275\u0275text(13," Esta transacci\xf3n tiene un costo de "),n.\u0275\u0275elementStart(14,"b"),n.\u0275\u0275text(15,"$ 0 pesos"),n.\u0275\u0275elementEnd()()()()()(),n.\u0275\u0275elementStart(16,"div",11)(17,"button",12),n.\u0275\u0275listener("click",function(){return e.onSubmit()}),n.\u0275\u0275elementStart(18,"span"),n.\u0275\u0275text(19,"Utilizar cupo"),n.\u0275\u0275elementEnd()()()()),2&t&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("leftAction",e.backAction)("rightAction",e.cancelAction),n.\u0275\u0275advance(6),n.\u0275\u0275property("title",null==e.creditUseQuota||null==e.creditUseQuota.destination?null:e.creditUseQuota.destination.nickname)("subtitle",null==e.creditUseQuota||null==e.creditUseQuota.destination?null:e.creditUseQuota.destination.number)("detail",null==e.creditUseQuota||null==e.creditUseQuota.destination?null:e.creditUseQuota.destination.name)("actions",e.destinationActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==e.creditUseQuota?null:e.creditUseQuota.amount)("actions",e.amountActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("title",null==e.creditUseQuota||null==e.creditUseQuota.source?null:e.creditUseQuota.source.nickname)("subtitle",null==e.creditUseQuota||null==e.creditUseQuota.source?null:e.creditUseQuota.source.number),n.\u0275\u0275advance(1),n.\u0275\u0275property("visible",!0))},dependencies:[h.K,a.J,Q.P,S.D,E.B],styles:["/*!\n * MBO CreditQuotaConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 15/Nov/2022\n * Updated: 22/Feb/2024\n*/mbo-credit-use-quota-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-credit-use-quota-confirmation-page .mbo-credit-use-quota-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-credit-use-quota-confirmation-page .mbo-credit-use-quota-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-credit-use-quota-confirmation-page .mbo-credit-use-quota-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-credit-use-quota-confirmation-page .mbo-credit-use-quota-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),o})(),M=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=n.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,u.RouterModule.forChild([{path:"",component:y}]),g.KI,s.Jx,s.P8,s.DM,s.B4,s.Dj]}),o})()}}]);