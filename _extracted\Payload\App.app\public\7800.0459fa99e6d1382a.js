(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7800],{47800:(E,a,o)=>{o.r(a),o.d(a,{MboTwoFactorAuthenticationModule:()=>u});var l=o(17007),M=o(78007),n=o(99877);const d=[{path:"",redirectTo:"otp-verification",pathMatch:"full"},{path:"otp-verification",loadChildren:()=>o.e(2966).then(o.bind(o,22966)).then(t=>t.MboTwoFactorOTPVerificationPageModule)},{path:"token-verification",loadChildren:()=>o.e(1982).then(o.bind(o,11982)).then(t=>t.MboTwoFactorTokenVerificationPageModule)}];let u=(()=>{class t{}return t.\u0275fac=function(h){return new(h||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,M.RouterModule.forChild(d)]}),t})()}}]);