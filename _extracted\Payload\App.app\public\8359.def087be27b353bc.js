(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8359],{48359:(m,l,a)=>{a.r(l),a.d(l,{PreferencesWeb:()=>c});var s=a(15861),f=a(17737);class c extends f.WebPlugin{constructor(){super(...arguments),this.group="CapacitorStorage"}configure({group:e}){var t=this;return(0,s.Z)(function*(){"string"==typeof e&&(t.group=e)})()}get(e){var t=this;return(0,s.Z)(function*(){return{value:t.impl.getItem(t.applyPrefix(e.key))}})()}set(e){var t=this;return(0,s.Z)(function*(){t.impl.setItem(t.applyPrefix(e.key),e.value)})()}remove(e){var t=this;return(0,s.Z)(function*(){t.impl.removeItem(t.applyPrefix(e.key))})()}keys(){var e=this;return(0,s.Z)(function*(){return{keys:e.rawKeys().map(r=>r.substring(e.prefix.length))}})()}clear(){var e=this;return(0,s.Z)(function*(){for(const t of e.rawKeys())e.impl.removeItem(t)})()}migrate(){var e=this;return(0,s.Z)(function*(){var t;const r=[],n=[],i="_cap_",o=Object.keys(e.impl).filter(u=>0===u.indexOf(i));for(const u of o){const h=u.substring(i.length),y=null!==(t=e.impl.getItem(u))&&void 0!==t?t:"",{value:d}=yield e.get({key:h});"string"==typeof d?n.push(h):(yield e.set({key:h,value:y}),r.push(h))}return{migrated:r,existing:n}})()}removeOld(){var e=this;return(0,s.Z)(function*(){const r=Object.keys(e.impl).filter(n=>0===n.indexOf("_cap_"));for(const n of r)e.impl.removeItem(n)})()}get impl(){return window.localStorage}get prefix(){return"NativeStorage"===this.group?"":`${this.group}.`}rawKeys(){return Object.keys(this.impl).filter(e=>0===e.indexOf(this.prefix))}applyPrefix(e){return this.prefix+e}}},15861:(m,l,a)=>{function s(_,c,p,e,t,r,n){try{var i=_[r](n),o=i.value}catch(u){return void p(u)}i.done?c(o):Promise.resolve(o).then(e,t)}function f(_){return function(){var c=this,p=arguments;return new Promise(function(e,t){var r=_.apply(c,p);function n(o){s(r,e,t,n,i,"next",o)}function i(o){s(r,e,t,n,i,"throw",o)}n(void 0)})}}a.d(l,{Z:()=>f})}}]);