(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4087],{10954:(z,x,t)=>{t.d(x,{V:()=>I,Ws:()=>m,YH:()=>P,d6:()=>A,uJ:()=>f});var n=t(39904),h=t(87903),o=t(53113),s=t(66067);class m extends s.T2{constructor(y,b,v,_,l,e,C,g,U,R,p,O,S){super(y,b,v,_,l,C,g,U,R,O,S),this.colorValue=e,this.franchise=p,this.bank.isOccidente&&U&&(this.statusValue={label:"Amparada",color:"amathyst"}),this.logoValue=(0,h.mm)(this,p),this.currenciesValue=p?.currencies||[n.y1],this.digitalValue="DIGITAL"===e}get color(){return this.colorValue}get logo(){return this.logoValue}get publicNumber(){return this.shortNumber}}class f{constructor(y,b,v){this.code=y,this.amount=b,this.amountCurrency=v||0}}class P{constructor(y,b,v,_,l){this.label=y,this.mode=b,this.copTotal=_?.value||0,this.usdTotal=l?.value||0,this.copUsdTotal=v?.toCop(this.usdTotal)||0,this.amountTotal=this.copTotal+this.copUsdTotal}get copValid(){return this.copTotal>0}get usdValid(){return this.usdTotal>0}get valid(){return this.copValid||this.usdValid}copValue(){return new f("COP",this.copTotal,this.copTotal)}usdValue(){return new f("USD",this.copUsdTotal,this.usdTotal)}}class E{constructor(y,b,v){this.destination=y,this.source=b,this.currency=v}}class I{constructor(y,b,v,_,l,e){this.destination=y,this.source=b,this.isManual=v,this.trm=_,this.cop=l,this.usd=e,this.copAmount=this.cop?.amount||0,this.copUsdAmount=this.usd?.amount||0,this.usdAmount=this.trm?.toCurrency(this.copUsdAmount)||0,this.totalAmount=this.copAmount+this.copUsdAmount}getPaymentCop(){return this.cop&&this.cop.amount>0?new E(this.destination,this.source,this.cop):void 0}getPaymentUsd(){return this.usd&&this.usd.amount>0?new E(this.destination,this.source,this.usd):void 0}}class A extends o.LN{constructor(y,b,v){super(y,b),this.currency=v}}},96381:(z,x,t)=>{t.d(x,{T:()=>S,P:()=>Q});var n=t(15861),h=t(77279),o=t(81536),s=t(87956),m=t(98699),f=t(10954),P=t(39904),E=t(29306),I=t(7464),A=t(87903),M=t(53113),y=t(1131);function _(c,T){return new f.V(c.destination,c.source,c.mode===y.o.MANUAL,T,c.cop,c.usd)}var e=t(71776),C=t(42168),g=t(99877);let U=(()=>{class c{constructor(r,a){this.http=r,a.subscribes(P.PU,()=>{this.creditCards=void 0})}request(){return this.creditCards?Promise.resolve(this.creditCards):(0,C.firstValueFrom)(this.http.get(P.bV.PAYMENTS.DEBTS.CATALOG,{params:{filter:"CCA",items:"100",order:"ASC",orderField:"loanName",page:"0"}}).pipe((0,C.map)(({content:r})=>r.map(a=>function v(c){return new f.Ws(c.id,c.acctType,c.acctTypeName,"DIGITAL"===c.color?P.CG:c.loanName,c.acctId,c.isOwner&&c.color||"NONE",(0,I.RO)(c.bankId,c.bankName),c.isAval,c.dynamo||!1,c.isOwner,c.creditCardType?function b(c){return new E.dD(c?.code,c?.description,0,0)}(c.creditCardType):void 0,c.isOwner?void 0:c.owner,c.isOwner?void 0:new M.dp((0,A.nX)(c.ownerIdType),c.ownerId))}(a))),(0,C.tap)(r=>{this.creditCards=r})))}}return c.\u0275fac=function(r){return new(r||c)(g.\u0275\u0275inject(e.HttpClient),g.\u0275\u0275inject(s.Yd))},c.\u0275prov=g.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})(),R=(()=>{class c{constructor(r){this.http=r}send(r){return(0,A.EC)([r.getPaymentCop(),r.getPaymentUsd()].filter(a=>!!a).map(a=>()=>this.sendCurrency(a)))}sendCurrency(r){return(0,C.firstValueFrom)(this.http.post(P.bV.PAYMENTS.CREDIT_CARD,function l(c){return{acctIdFrom:c.source.id,acctNickNameFrom:c.source.nickname,bankIdFrom:c.source.bank.id,acctIdTo:c.destination.id,acctNameTo:c.destination.nickname,bankIdTo:c.destination.bank.id,bankNameTo:c.destination.bank.name,amt:Math.ceil(c.currency.amount),curCode:c.currency.code,paymentDesc:""}}(r)).pipe((0,C.map)(a=>{const u=(0,A.l1)(a,"SUCCESS"),{type:i,message:d}=u;return new f.d6(i,d,r.currency)}),(0,C.catchError)(a=>{const{message:u}=(0,A.rU)(a);return(0,C.of)(new f.d6("ERROR",u,r.currency))})))}}return c.\u0275fac=function(r){return new(r||c)(g.\u0275\u0275inject(e.HttpClient))},c.\u0275prov=g.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();var p=t(20691);let O=(()=>{class c extends p.Store{constructor(r){super({confirmation:!1,fromCustomer:!1,mode:y.o.PAY_MIN}),this.eventBusService=r,this.eventBusService.subscribes(P.PU,()=>{this.reset()})}setDestination(r,a=!1){this.reduce(u=>({...u,destination:r,fromCustomer:a}))}getDestination(){return this.select(({destination:r})=>r)}itIsFromCustomer(){return this.select(({fromCustomer:r})=>r)}setSource(r){this.reduce(a=>({...a,source:r}))}getSource(){return this.select(({source:r})=>r)}setAmount(r){const{cop:a,mode:u,usd:i}=r;this.reduce(d=>({...d,cop:a,mode:u,usd:i,confirmation:!0}))}getAmount(){return this.select(({mode:r,cop:a,usd:u})=>({cop:a,mode:r,usd:u}))}setCurrencyCode(r){this.reduce(a=>({...a,currencyCode:r}))}itIsConfirmation(){return this.select(({confirmation:r})=>r)}}return c.\u0275fac=function(r){return new(r||c)(g.\u0275\u0275inject(s.Yd))},c.\u0275prov=g.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})(),S=(()=>{class c{constructor(r,a,u,i,d){this.financials=r,this.productService=a,this.repository=u,this.store=i,this.eventBusService=d}setDestination(r){var a=this;return(0,n.Z)(function*(){try{return r.isRequiredInformation&&(yield a.productService.requestInformation(r)),m.Either.success(a.store.setDestination(r))}catch{return m.Either.failure({message:"Lo sentimos, no pudimos verificar los datos del producto, por favor vuelva a intertarlo"})}})()}setSource(r){try{return m.Either.success(this.store.setSource(r))}catch({message:a}){return m.Either.failure({message:a})}}setAmount(r){try{return m.Either.success(this.store.setAmount(r))}catch({message:a}){return m.Either.failure({message:a})}}setCurrencyCode(r){try{return m.Either.success(this.store.setCurrencyCode(r))}catch({message:a}){return m.Either.failure({message:a})}}reset(){try{const r=this.store.itIsFromCustomer(),a=this.store.getDestination();return this.store.reset(),m.Either.success({fromCustomer:r,destination:a})}catch({message:r}){return m.Either.failure({message:r})}}send(){var r=this;return(0,n.Z)(function*(){const a=_(r.store.currentState,yield r.requestTrmUsd()),u=yield r.execute(a),i=u.reduce((d,{isError:D})=>d&&!D,!0);return r.eventBusService.emit(i?h.q.TransactionSuccess:h.q.TransactionFailed),m.Either.success({creditCard:a,status:u})})()}requestTrmUsd(){return(0,m.catchPromise)(this.financials.request().then(([r])=>r))}execute(r){try{return this.repository.send(r)}catch({message:a}){return Promise.resolve([new f.d6("ERROR",a)])}}}return c.\u0275fac=function(r){return new(r||c)(g.\u0275\u0275inject(o.rm),g.\u0275\u0275inject(s.M5),g.\u0275\u0275inject(R),g.\u0275\u0275inject(O),g.\u0275\u0275inject(s.Yd))},c.\u0275prov=g.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();var F=t(89148);const{MANUAL:N,PAY_ALTERNATIVE:V,PAY_MIN:j,PAY_TOTAL:W}=y.o,{CcaDatePayMinCop:$,CcaDatePayMinUsd:J,CcaPayAltMinCop:K,CcaPayAltMinUsd:w,CcaPayMinCop:Y,CcaPayMinUsd:Z,CcaPayTotalCop:G,CcaPayTotalUsd:H}=F.Av;let Q=(()=>{class c{constructor(r,a,u,i,d){this.products=r,this.productService=a,this.financials=u,this.repository=i,this.store=d}destination(){var r=this;return(0,n.Z)(function*(){try{return m.Either.success((yield r.repository.request()).reduce((a,u)=>{const{others:i,principals:d}=a;return(u.bank.isOccidente?d:i).push(u),a},{others:[],principals:[]}))}catch({message:a}){return m.Either.failure({message:a})}})()}source(r){var a=this;return(0,n.Z)(function*(){try{const u=yield a.products.requestAccountsForTransfer(),i=a.store.itIsConfirmation(),d=yield a.requestCreditCard(r);return m.Either.success({confirmation:i,destination:d,products:u})}catch({message:u}){return m.Either.failure({message:u})}})()}information(){var r=this;return(0,n.Z)(function*(){try{const a=r.store.getDestination(),u=yield r.productService.requestInformation(a),i=yield r.requestTrmUsd(),d=u?.getSection($),D=u?.getSection(J);return m.Either.success({destination:a,min:new f.YH("VALOR M\xcdNIMO A PAGAR",j,i,u?.getSection(Y),u?.getSection(Z)),alternative:new f.YH("VALOR M\xcdNIMO ALTERNO",V,i,u?.getSection(K),u?.getSection(w)),total:new f.YH("SALDO ACTUAL",W,i,u?.getSection(G),u?.getSection(H)),dateCop:d?.valueFormat,dateUsd:D?.valueFormat})}catch({message:a}){return m.Either.failure({message:a})}})()}selectAmount(){var r=this;return(0,n.Z)(function*(){try{const a=r.store.itIsConfirmation(),u=r.store.getAmount(),i=r.store.getSource(),d=r.store.getDestination(),D=yield r.productService.requestInformation(d),L=yield r.requestTrmUsd();return m.Either.success({destination:d,amount:u,confirmation:a,trm:L,source:i,min:new f.YH("Pago m\xednimo",j,L,D?.getSection(Y),D?.getSection(Z)),alternative:new f.YH("Pago m\xednimo alterno",V,L,D?.getSection(K),D?.getSection(w)),total:new f.YH("Saldo actual",W,L,D?.getSection(G),D?.getSection(H)),manual:new f.YH("Otro valor",N)})}catch({message:a}){return m.Either.failure({message:a})}})()}amount(){try{const r=this.store.itIsConfirmation(),a=this.store.getSource(),u=this.store.getDestination(),{cop:i}=this.store.getAmount();return m.Either.success({amount:i?.amount||0,confirmation:r,destination:u,source:a})}catch({message:r}){return m.Either.failure({message:r})}}confirmation(){var r=this;return(0,n.Z)(function*(){try{const a=_(r.store.currentState,yield r.requestTrmUsd());return m.Either.success({payment:a})}catch({message:a}){return m.Either.failure({message:a})}})()}requestCreditCard(r){var a=this;return(0,n.Z)(function*(){let u=a.store.getDestination();return!u&&r&&(u=(yield a.products.requestCreditCards()).find(({id:i})=>i===r),a.store.setDestination(u,!0)),u})()}requestTrmUsd(){return(0,m.catchPromise)(this.financials.request().then(([r])=>r))}}return c.\u0275fac=function(r){return new(r||c)(g.\u0275\u0275inject(s.hM),g.\u0275\u0275inject(s.M5),g.\u0275\u0275inject(o.rm),g.\u0275\u0275inject(U),g.\u0275\u0275inject(O))},c.\u0275prov=g.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},55351:(z,x,t)=>{t.d(x,{t:()=>P});var n=t(30263),h=t(39904),o=t(95437),s=t(96381),m=t(99877);let P=(()=>{class E{constructor(A,M,y){this.modalConfirmation=A,this.mboProvider=M,this.managerCreditCard=y}execute(A=!0){A?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago de tarjeta actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerCreditCard.reset().when({success:({fromCustomer:A,destination:M})=>{A?this.mboProvider.navigation.back(h.Z6.CUSTOMER.PRODUCTS.INFO,{productId:M.id}):this.mboProvider.navigation.back(h.Z6.PAYMENTS.HOME)},failure:()=>{this.mboProvider.navigation.back(h.Z6.PAYMENTS.HOME)}})}}return E.\u0275fac=function(A){return new(A||E)(m.\u0275\u0275inject(n.$e),m.\u0275\u0275inject(o.ZL),m.\u0275\u0275inject(s.T))},E.\u0275prov=m.\u0275\u0275defineInjectable({token:E,factory:E.\u0275fac,providedIn:"root"}),E})()},1131:(z,x,t)=>{t.d(x,{o:()=>n});var n=(()=>{return(h=n||(n={}))[h.PAY_MIN=0]="PAY_MIN",h[h.PAY_ALTERNATIVE=1]="PAY_ALTERNATIVE",h[h.PAY_TOTAL=2]="PAY_TOTAL",h[h.MANUAL=3]="MANUAL",n;var h})()},63111:(z,x,t)=>{t.d(x,{m:()=>s});var n=t(99877),o=t(3235);let s=(()=>{class m{constructor(){this.copAmount=0,this.copUsdAmount=0,this.usdAmount=0}}return m.\u0275fac=function(P){return new(P||m)},m.\u0275cmp=n.\u0275\u0275defineComponent({type:m,selectors:[["mbo-card-currency-amount"]],inputs:{copAmount:"copAmount",copUsdAmount:"copUsdAmount",usdAmount:"usdAmount",theme:"theme"},decls:18,vars:15,consts:[[1,"mbo-card-currency-amount__content"],[1,"mbo-card-currency-amount__currency"],[1,"mbo-card-currency-amount__currency__logo"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"mbo-card-currency-amount__currency__content"],[1,"mbo-card-currency-amount__currency__detail","caption-medium"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"mbo-card-currency-amount__currency__value","caption-medium"]],template:function(P,E){1&P&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),n.\u0275\u0275element(3,"img",3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",4)(5,"span",5),n.\u0275\u0275text(6),n.\u0275\u0275pipe(7,"boccCurrency"),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(8,"div",1)(9,"div",2),n.\u0275\u0275element(10,"img",6),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(11,"div",4)(12,"span",5),n.\u0275\u0275text(13),n.\u0275\u0275pipe(14,"boccCurrency"),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(15,"span",7),n.\u0275\u0275text(16),n.\u0275\u0275pipe(17,"boccCurrency"),n.\u0275\u0275elementEnd()()()()),2&P&&(n.\u0275\u0275advance(6),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind3(7,3,null==E.copAmount?null:E.copAmount.toString(),"$",!1)," "),n.\u0275\u0275advance(7),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind3(14,7,null==E.copUsdAmount?null:E.copUsdAmount.toString(),"$",!1)," "),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1(" ",n.\u0275\u0275pipeBind3(17,11,null==E.usdAmount?null:E.usdAmount.toString(),"USD",!0)," "))},dependencies:[o.T],styles:["/*!\n * MBO CardCurrencyAmount Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 28/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-card-currency-amount{position:relative;width:100%;display:block}mbo-card-currency-amount .mbo-card-currency-amount__content{position:relative;display:flex;width:100%;padding:var(--sizing-x6);box-sizing:border-box}mbo-card-currency-amount .mbo-card-currency-amount__currency{display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo{max-width:var(--sizing-x8);max-height:var(--sizing-x8)}mbo-card-currency-amount .mbo-card-currency-amount__currency__logo img{width:100%;height:100%}mbo-card-currency-amount .mbo-card-currency-amount__currency__content{display:flex;width:calc(100% - 14rem);flex-direction:column;row-gap:var(--sizing-x1)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail{position:relative;width:100%;color:var(--color-carbon-lighter-700)}mbo-card-currency-amount .mbo-card-currency-amount__currency__detail:before{margin-right:var(--sizing-x1);text-align:left;width:var(--sizing-x4)}mbo-card-currency-amount .mbo-card-currency-amount__currency__value{position:relative;width:100%;color:var(--color-amathyst-700)}\n"],encapsulation:2}),m})()},2297:(z,x,t)=>{t.d(x,{p:()=>y});var n=t(30263),o=(t(10954),t(99877)),m=t(17007),P=t(2460),E=t(45542),I=t(3235),A=t(16450);function M(b,v){if(1&b&&(o.\u0275\u0275elementStart(0,"div",7)(1,"div",8),o.\u0275\u0275element(2,"img",15),o.\u0275\u0275elementStart(3,"span",10),o.\u0275\u0275text(4,"Deuda en d\xf3lares"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(5,"div",11)(6,"span",10),o.\u0275\u0275text(7),o.\u0275\u0275pipe(8,"boccCurrencyCop"),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(9,"span",16),o.\u0275\u0275text(10),o.\u0275\u0275pipe(11,"boccCurrency"),o.\u0275\u0275elementEnd()()()),2&b){const _=o.\u0275\u0275nextContext();o.\u0275\u0275advance(7),o.\u0275\u0275textInterpolate1(" ",o.\u0275\u0275pipeBind2(8,2,null==_.currency?null:_.currency.copUsdTotal.toString(),!1)," "),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",o.\u0275\u0275pipeBind3(11,5,null==_.currency?null:_.currency.usdTotal.toString(),"USD",!1)," ")}}let y=(()=>{class b{constructor(_){this.modalConfirmation=_,this.canUsd=!0,this.condense=!0}onHeader(){this.condense=!this.condense}onInformation(){this.modalConfirmation.execute({message:this.message,title:this.title,accept:{label:"Aceptar"}})}}return b.\u0275fac=function(_){return new(_||b)(o.\u0275\u0275directiveInject(n.$e))},b.\u0275cmp=o.\u0275\u0275defineComponent({type:b,selectors:[["mbo-creditcard-information"]],inputs:{currency:"currency",title:"title",message:"message",canUsd:"canUsd"},decls:24,vars:13,consts:[[1,"mbo-creditcard-information__content"],[1,"mbo-creditcard-information__header",3,"click"],[1,"overline-medium"],[1,"mbo-creditcard-information__header__amount"],[1,"body1-medium"],[3,"icon"],[1,"mbo-creditcard-information__body",3,"hidden"],[1,"mbo-creditcard-information__currency"],[1,"mbo-creditcard-information__currency__label"],["src","assets/shared/logos/currencies/cop-enabled.svg"],[1,"caption-medium"],[1,"mbo-creditcard-information__currency__amount"],["class","mbo-creditcard-information__currency",4,"ngIf"],["bocc-button","flat","prefixIcon","chat-info",3,"click"],[1,"bocc-divider"],["src","assets/shared/logos/currencies/usd-enabled.svg"],[1,"usd","caption-medium"]],template:function(_,l){1&_&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275listener("click",function(){return l.onHeader()}),o.\u0275\u0275elementStart(2,"label",2),o.\u0275\u0275text(3),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(4,"div",3)(5,"span",4),o.\u0275\u0275text(6),o.\u0275\u0275pipe(7,"boccCurrencyCop"),o.\u0275\u0275elementEnd(),o.\u0275\u0275element(8,"bocc-icon",5),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(9,"div",6)(10,"div",7)(11,"div",8),o.\u0275\u0275element(12,"img",9),o.\u0275\u0275elementStart(13,"span",10),o.\u0275\u0275text(14,"Deuda en pesos"),o.\u0275\u0275elementEnd()(),o.\u0275\u0275elementStart(15,"div",11)(16,"span",10),o.\u0275\u0275text(17),o.\u0275\u0275pipe(18,"boccCurrencyCop"),o.\u0275\u0275elementEnd()()(),o.\u0275\u0275template(19,M,12,9,"div",12),o.\u0275\u0275elementStart(20,"button",13),o.\u0275\u0275listener("click",function(){return l.onInformation()}),o.\u0275\u0275elementStart(21,"span"),o.\u0275\u0275text(22),o.\u0275\u0275elementEnd()(),o.\u0275\u0275element(23,"div",14),o.\u0275\u0275elementEnd()()),2&_&&(o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate(null==l.currency?null:l.currency.label),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate1(" ",o.\u0275\u0275pipeBind2(7,7,null==l.currency?null:l.currency.amountTotal.toString(),!1)," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("icon",l.condense?"list-open":"list-close"),o.\u0275\u0275advance(1),o.\u0275\u0275property("hidden",l.condense),o.\u0275\u0275advance(8),o.\u0275\u0275textInterpolate1(" ",o.\u0275\u0275pipeBind2(18,10,null==l.currency?null:l.currency.copTotal.toString(),!1)," "),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngIf",l.canUsd),o.\u0275\u0275advance(3),o.\u0275\u0275textInterpolate(l.title))},dependencies:[m.NgIf,P.Z,E.P,I.T,A.f],styles:["/*!\n * MBO CreditCardInformation Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Mar/2023\n * Updated: 08/Jul/2024\n*/mbo-creditcard-information{position:relative;width:100%;display:block}mbo-creditcard-information .mbo-creditcard-information__content{position:relative;width:100%}mbo-creditcard-information .mbo-creditcard-information__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header>label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__header__amount{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__header__amount>span{width:100%;text-align:right}mbo-creditcard-information .mbo-creditcard-information__header__amount>bocc-icon{color:var(--color-blue-700)}mbo-creditcard-information .mbo-creditcard-information__body{--bocc-button-padding: 0rem var(--sizing-x4);position:relative;display:flex;width:100%;margin-top:var(--sizing-x6);flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button{width:100%}mbo-creditcard-information .mbo-creditcard-information__body>.bocc-button__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency{--bocc-button-padding: 0rem var(--sizing-x2);position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label{display:flex;align-items:center}mbo-creditcard-information .mbo-creditcard-information__currency__label img{margin-right:var(--sizing-x4);width:var(--sizing-x8);height:var(--sizing-x8)}mbo-creditcard-information .mbo-creditcard-information__currency__label span{color:var(--color-carbon-lighter-700)}mbo-creditcard-information .mbo-creditcard-information__currency__amount{padding-right:var(--sizing-x6);box-sizing:border-box;margin:auto 0rem;display:flex;flex-direction:column}mbo-creditcard-information .mbo-creditcard-information__currency__amount span{color:var(--color-carbon-lighter-700);text-align:right}mbo-creditcard-information .mbo-creditcard-information__currency__amount span.usd{color:var(--color-amathyst-700)}\n"],encapsulation:2}),b})()},33022:(z,x,t)=>{t.d(x,{H:()=>_});var n=t(99877),o=t(39904),m=(t(10954),t(17007)),P=t(13462),I=t(45542),A=t(92275),M=t(55944),y=t(19102);function b(l,e){if(1&l){const C=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",14)(1,"div",15)(2,"label",16),n.\u0275\u0275text(3),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"span",17),n.\u0275\u0275text(5),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(6,"div")(7,"bocc-badge"),n.\u0275\u0275text(8),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(9,"div",18)(10,"button",19),n.\u0275\u0275listener("click",function(){const R=n.\u0275\u0275restoreView(C).$implicit,p=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(p.onSelect(R))}),n.\u0275\u0275text(11," Ver recibo "),n.\u0275\u0275elementEnd()()()}if(2&l){const C=e.$implicit,g=e.index,U=n.\u0275\u0275nextContext();n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate1("PAGO ",g+1,""),n.\u0275\u0275advance(2),n.\u0275\u0275textInterpolate(U.getPayLabel(C)),n.\u0275\u0275advance(2),n.\u0275\u0275attribute("bocc-theme",U.getPayColor(C)),n.\u0275\u0275advance(1),n.\u0275\u0275textInterpolate1(" ",U.getPayStatus(C)," ")}}var v=(()=>{return(l=v||(v={}))[l.FAILURE=0]="FAILURE",l[l.INCOMPLETE=1]="INCOMPLETE",l[l.SUCCESS=2]="SUCCESS",v;var l})();let _=(()=>{class l{constructor(){this.status=[],this.select=new n.EventEmitter}get state(){return this.status.reduce((C,{isError:g})=>g?C:C+1,0)}get title(){switch(this.state){case v.SUCCESS:return"\xa1Pagos exitosos!";case v.INCOMPLETE:return"\xa1Pagos parcialmente exitosos!";default:return"\xa1Pagos fallidos!"}}get animation(){switch(this.state){case v.SUCCESS:case v.INCOMPLETE:return o.F6;default:return o.cj}}getPayLabel(C){return"USD"===C.currency?.code?"Pago en d\xf3lares":"Pago en pesos"}getPayColor(C){return C.isError?"danger":"success"}getPayStatus(C){return C.isError?"Fallido":"Exitoso"}onSelect(C){this.select.emit(C)}}return l.\u0275fac=function(C){return new(C||l)},l.\u0275cmp=n.\u0275\u0275defineComponent({type:l,selectors:[["mbo-creditcard-payments-card"]],inputs:{payment:"payment",status:"status"},outputs:{select:"select"},decls:17,vars:5,consts:[[1,"mbo-creditcard-payments-card__content"],[1,"mbo-creditcard-payments-card__header"],[3,"result"],[1,"mbo-creditcard-payments-card__status"],[3,"options"],[1,"mbo-creditcard-payments-card__subheader"],[1,"subtitle2-medium"],[1,"body2-medium"],[1,"bocc-divider"],[1,"mbo-creditcard-payments-card__body"],["class","mbo-creditcard-payments-card__payment",4,"ngFor","ngForOf"],[1,"mbo-creditcard-payments-card__amount"],[1,"smalltext-bold"],[3,"amount"],[1,"mbo-creditcard-payments-card__payment"],[1,"mbo-creditcard-payments-card__payment__content"],[1,"caption-medium"],[1,"smalltext-medium"],[1,"mbo-creditcard-payments-card__payment__action"],["bocc-button","flat","prefixIcon","extract-receive",3,"click"]],template:function(C,g){1&C&&(n.\u0275\u0275elementStart(0,"div",0)(1,"div",1),n.\u0275\u0275element(2,"mbo-bank-logo",2),n.\u0275\u0275elementStart(3,"div",3),n.\u0275\u0275element(4,"ng-lottie",4),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(5,"div",5)(6,"label",6),n.\u0275\u0275text(7),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"span",7),n.\u0275\u0275text(9,"Revis\xe1 los detalles de los pagos."),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275element(10,"div",8),n.\u0275\u0275elementStart(11,"div",9),n.\u0275\u0275template(12,b,12,4,"div",10),n.\u0275\u0275elementStart(13,"div",11)(14,"label",12),n.\u0275\u0275text(15,"TOTAL PAGADO"),n.\u0275\u0275elementEnd(),n.\u0275\u0275element(16,"bocc-amount",13),n.\u0275\u0275elementEnd()()()),2&C&&(n.\u0275\u0275advance(2),n.\u0275\u0275property("result",!0),n.\u0275\u0275advance(2),n.\u0275\u0275property("options",g.animation),n.\u0275\u0275advance(3),n.\u0275\u0275textInterpolate(g.title),n.\u0275\u0275advance(5),n.\u0275\u0275property("ngForOf",g.status),n.\u0275\u0275advance(4),n.\u0275\u0275property("amount",null==g.payment?null:g.payment.totalAmount))},dependencies:[m.NgForOf,P.LottieComponent,I.P,A.O,M.Q,y.r],styles:["/*!\n * MBO CreditCardPaymentsCard Component\n * v2.0.1\n * Author: MB Frontend Developers\n * Created: 09/Feb/2024\n * Updated: 08/Jul/2024\n*/mbo-creditcard-payments-card{position:relative;display:block;padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x8);box-shadow:var(--z-bottom-lighter-8);background:var(--color-carbon-lighter-50)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__content{display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__header{position:relative;display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__status{position:relative;width:var(--sizing-x24);height:var(--sizing-x24)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>label{color:var(--color-carbon-darker-1000);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__subheader>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-creditcard-payments-card .mbo-creditcard-payments-card__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x4);box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment{position:relative;display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);padding:var(--sizing-x4) 0rem;box-sizing:border-box}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>label{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-lighter-400)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__content>span{padding:0rem var(--sizing-x2);box-sizing:border-box;color:var(--color-carbon-darker-1000)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__payment__action{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x2);padding:var(--sizing-x4) var(--sizing-x8);box-sizing:border-box;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-60)}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount label{color:var(--color-carbon-lighter-400);text-align:right}mbo-creditcard-payments-card .mbo-creditcard-payments-card__amount bocc-amount{text-align:right}\n"],encapsulation:2}),l})()},50773:(z,x,t)=>{t.d(x,{n:()=>y}),t(57544),t(10954);var s=t(99877),f=t(17007),E=t(80349),I=t(63111),A=t(3235);function M(b,v){if(1&b&&(s.\u0275\u0275elementStart(0,"div",6),s.\u0275\u0275element(1,"mbo-card-currency-amount",7),s.\u0275\u0275elementEnd()),2&b){const _=s.\u0275\u0275nextContext();s.\u0275\u0275classProp("mbo-creditcard-radiobutton__footer--disabled",_.disabled),s.\u0275\u0275advance(1),s.\u0275\u0275property("copAmount",null==_.value?null:_.value.copTotal)("copUsdAmount",null==_.value?null:_.value.copUsdTotal)("usdAmount",null==_.value?null:_.value.usdTotal)}}let y=(()=>{class b{constructor(){this.disabled=!1,this.hasFooter=!1,this.skeleton=!1}get checked(){return this.radioControl&&this.radioControl.value===this.value}onComponent(){this.disabled||this.radioControl?.setValue(this.value)}}return b.\u0275fac=function(_){return new(_||b)},b.\u0275cmp=s.\u0275\u0275defineComponent({type:b,selectors:[["mbo-creditcard-radiobutton"]],inputs:{radioControl:"radioControl",value:"value",theme:"theme",disabled:"disabled",hasFooter:"hasFooter",skeleton:"skeleton"},decls:9,vars:10,consts:[[1,"mbo-creditcard-radiobutton__content",3,"click"],[3,"checked"],[1,"mbo-creditcard-radiobutton__body"],[1,"mbo-creditcard-radiobutton__label","body2-medium"],[1,"mbo-creditcard-radiobutton__amount","subtitle1-medium"],["class","mbo-creditcard-radiobutton__footer",3,"mbo-creditcard-radiobutton__footer--disabled",4,"ngIf"],[1,"mbo-creditcard-radiobutton__footer"],[3,"copAmount","copUsdAmount","usdAmount"]],template:function(_,l){1&_&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275listener("click",function(){return l.onComponent()}),s.\u0275\u0275element(1,"bocc-radiobutton",1),s.\u0275\u0275elementStart(2,"div",2)(3,"span",3),s.\u0275\u0275text(4),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(5,"span",4),s.\u0275\u0275text(6),s.\u0275\u0275pipe(7,"boccCurrency"),s.\u0275\u0275elementEnd()()(),s.\u0275\u0275template(8,M,2,5,"div",5)),2&_&&(s.\u0275\u0275classProp("mbo-creditcard-radiobutton__content--disabled",l.disabled),s.\u0275\u0275advance(1),s.\u0275\u0275property("checked",l.checked),s.\u0275\u0275advance(3),s.\u0275\u0275textInterpolate1(" ",null==l.value?null:l.value.label," "),s.\u0275\u0275advance(2),s.\u0275\u0275textInterpolate1(" ",s.\u0275\u0275pipeBind3(7,6,null==l.value?null:l.value.amountTotal.toString(),"$",!1)," "),s.\u0275\u0275advance(2),s.\u0275\u0275property("ngIf",!l.hasFooter))},dependencies:[f.NgIf,E.V,I.m,A.T],styles:["/*!\n * MBO CreditCardRadiobutton Component\n * v2.2.0\n * Author: MB Frontend Developers\n * Created: 17/Feb/2023\n * Updated: 27/Jun/2024\n*/mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x8);--pvt-body-padding: var(--sizing-x8);--pvt-amount-padding: 0rem var(--sizing-x2);position:relative;display:block;width:100%;box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content{position:relative;display:flex;width:100%;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);padding:var(--pvt-content-padding);box-sizing:border-box}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__content--disabled{opacity:.5;pointer-events:none}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);overflow:hidden}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__label{color:var(--color-carbon-lighter-700)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer{position:relative;display:flex;width:100%;padding:var(--pvt-body-padding);box-sizing:border-box;border-top:var(--border-1-lighter-300);background:var(--overlay-lgrey-40)}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer--disabled{pointer-events:none;opacity:.5}mbo-creditcard-radiobutton .mbo-creditcard-radiobutton__footer .mbo-card-currency-amount__content{padding:var(--pvt-amount-padding)}@media screen and (max-width: 320px){mbo-creditcard-radiobutton{--pvt-content-padding: var(--sizing-x6);--pvt-body-padding: var(--sizing-x6)}}\n"],encapsulation:2}),b})()},11747:(z,x,t)=>{t.d(x,{PF:()=>m,Hf:()=>v,xm:()=>b,kz:()=>l,Cq:()=>R}),t(63111);var h=t(17007),o=t(83651),s=t(99877);let m=(()=>{class p{}return p.\u0275fac=function(S){return new(S||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[h.CommonModule,o.P6]}),p})();t(2297),t(33022);var E=t(79798),I=t(30263),A=t(44487),M=t.n(A),y=t(13462);let b=(()=>{class p{}return p.\u0275fac=function(S){return new(S||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[h.CommonModule,y.LottieModule.forRoot({player:()=>M()}),I.P8,I.Oh,I.Qg,E.rw]}),p})(),v=(()=>{class p{}return p.\u0275fac=function(S){return new(S||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[h.CommonModule,I.Zl,I.P8,I.oc,o.P6]}),p})();t(50773);let l=(()=>{class p{}return p.\u0275fac=function(S){return new(S||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[h.CommonModule,I.Dj,I.V6,I.Qg,o.P6,m]}),p})();t(57544);let R=(()=>{class p{}return p.\u0275fac=function(S){return new(S||p)},p.\u0275mod=s.\u0275\u0275defineNgModule({type:p}),p.\u0275inj=s.\u0275\u0275defineInjector({imports:[h.CommonModule]}),p})()},14087:(z,x,t)=>{t.r(x),t.d(x,{MboPaymentCreditCardSelectAmountPageModule:()=>r});var n=t(17007),h=t(78007),o=t(79798),s=t(30263),m=t(83651),f=t(11747),P=t(15861),E=t(39904),I=t(87903),A=t(95437),M=t(98699),y=t(57544),b=t(10954),v=t(96381),_=t(55351),l=t(1131),e=t(99877),C=t(70016),g=t(48774),U=t(66613),R=t(2460),p=t(45542),O=t(83413),S=t(99194),F=t(50773),N=t(83867),V=t(3235),j=t(16450);function W(a,u){1&a&&(e.\u0275\u0275elementStart(0,"bocc-alert",17),e.\u0275\u0275text(1," Esta tarjeta diferencia tus compras realizadas en pesos y d\xf3lares. El pago m\xednimo, m\xednimo alterno o total ser\xe1 abonado a ambas deudas. "),e.\u0275\u0275elementEnd()),2&a&&e.\u0275\u0275property("visible",!0)}function $(a,u){if(1&a&&(e.\u0275\u0275elementStart(0,"div",18)(1,"div",19)(2,"span",20),e.\u0275\u0275text(3,"TRM"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(4,"br"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",21),e.\u0275\u0275element(6,"bocc-icon",22),e.\u0275\u0275elementStart(7,"span"),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"boccCurrencyCop"),e.\u0275\u0275elementEnd()()()),2&a){const i=e.\u0275\u0275nextContext();e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind2(9,1,null==i.trm?null:i.trm.currentAmount,!1),"")}}function J(a,u){1&a&&(e.\u0275\u0275elementStart(0,"bocc-alert",17),e.\u0275\u0275text(1," La opci\xf3n "),e.\u0275\u0275elementStart(2,"b"),e.\u0275\u0275text(3,"Otro valor"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(4," permite que hagas un abono a tu deuda en pesos, deuda en d\xf3lares o ambas. "),e.\u0275\u0275elementEnd()),2&a&&e.\u0275\u0275property("visible",!0)}function K(a,u){if(1&a&&e.\u0275\u0275element(0,"mbo-currency-box",29),2&a){const i=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("formControl",i.usdControl)("rate",null==i.trm?null:i.trm.currentAmount)}}function w(a,u){if(1&a&&(e.\u0275\u0275elementStart(0,"div",23),e.\u0275\u0275template(1,J,5,1,"bocc-alert",5),e.\u0275\u0275element(2,"mbo-currency-box",24),e.\u0275\u0275template(3,K,1,2,"mbo-currency-box",25),e.\u0275\u0275elementStart(4,"div",26)(5,"span",27),e.\u0275\u0275text(6,"TOTAL"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"label",28),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"boccCurrency"),e.\u0275\u0275elementEnd()()()),2&a){const i=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.canUsd),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControl",i.copControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.trm&&i.canUsd),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind3(9,4,i.copTotal.toString(),"$",!0)," ")}}function Y(a,u){if(1&a&&(e.\u0275\u0275elementStart(0,"bocc-message-icon",30),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"boccCurrencyCop"),e.\u0275\u0275elementEnd()),2&a){const i=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" Valor a pagar supera saldo disponible ",e.\u0275\u0275pipeBind2(2,1,null==i.source?null:i.source.amount.toString(),!1)," ")}}const{MANUAL:Z,PAY_ALTERNATIVE:G,PAY_MIN:H,PAY_TOTAL:Q}=l.o,c=E.Z6.PAYMENTS.CREDIT_CARD;let T=(()=>{class a{constructor(i,d,D,L){this.mboProvider=i,this.requestConfiguration=d,this.managerCreditCard=D,this.paymentCancel=L,this.confirmation=!1,this.requesting=!0,this.canUsd=!1,this.backAction={id:"btn_payment-creditcard-select-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(c.SOURCE)}},this.cancelAction={id:"btn_payment-creditcard-select-amount_cancel",label:"Cancelar",click:()=>{this.paymentCancel.execute()}},this.amountControl=new y.FormControl,this.copControl=new y.FormControl,this.usdControl=new y.FormControl}ngOnInit(){this.initializatedConfiguration()}get isManual(){return this.amountControl.value===this.manual}get disabled(){return this.requesting||this.isManual?!this.copControl.value&&!this.usdControl.value:(0,M.itIsUndefined)(this.amountControl.value)}get copTotal(){return(+this.copControl.value||0)+(+this.usdControl.value||0)}get sourceValid(){const i=this.amountControl.value;if(!i)return!1;if((0,I.VN)(this.source)){const{cop:d,usd:D}=this.getPaymentAmount(i);return d.amount+D.amount<=this.source.amount}return!0}onSubmit(){const i=this.getPaymentAmount(this.amountControl.value);this.managerCreditCard.setAmount(i).when({success:()=>{this.mboProvider.navigation.next(c.CONFIRMATION)}})}getPayAmount(i){return{cop:i.copValue(),usd:i.usdValue(),mode:i.mode}}getManualAmount(){return{cop:new b.uJ("COP",+this.copControl.value||0),usd:new b.uJ("USD",+this.usdControl.value||0,this.trm?.toCurrency(+this.usdControl.value||0)||0),mode:l.o.MANUAL}}getPaymentAmount(i){return i.mode!==l.o.MANUAL?this.getPayAmount(i):this.getManualAmount()}initializatedConfiguration(){var i=this;return(0,P.Z)(function*(){(yield i.requestConfiguration.selectAmount()).when({success:d=>{const{amount:D,alternative:L,confirmation:te,destination:X,min:k,manual:B,source:ne,total:q,trm:ee}=d;switch(i.canUsd=!!ee&&X.hasCurrency(E.qB),i.destination=X,i.source=ne,i.min=k,i.alternative=L,i.total=q,i.manual=B,i.trm=ee,i.confirmation=te,D.mode){case H:i.setCurrentAmount(k,B);break;case G:i.setCurrentAmount(L,B);break;case Q:i.setCurrentAmount(q,B);break;case Z:i.amountControl.setValue(B),i.copControl.setValue(D.cop?.amount),i.usdControl.setValue(D.usd?.amount)}}},()=>{i.requesting=!1})})()}setCurrentAmount(i,d){i.valid?this.amountControl.setValue(i):(this.amountControl.setValue(d),this.copControl.setValue(0),this.usdControl.setValue(0))}}return a.\u0275fac=function(i){return new(i||a)(e.\u0275\u0275directiveInject(A.ZL),e.\u0275\u0275directiveInject(v.P),e.\u0275\u0275directiveInject(v.T),e.\u0275\u0275directiveInject(_.t))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-payment-creditcard-select-amount-page"]],decls:20,vars:33,consts:[[1,"mbo-payment-creditcard-select-amount-page__content"],[1,"mbo-payment-creditcard-select-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-payment-creditcard-select-amount-page__body"],[3,"color","icon","title","number","detail","statusColor","statusLabel","skeleton"],["icon","bell",3,"visible",4,"ngIf"],["class","mbo-payment-creditcard-select-amount-page__trm",4,"ngIf"],[1,"mbo-payment-creditcard-select-amount-page__options"],["id","rdb_payment-creditcard-select-amount_pay-min",3,"value","radioControl","hidden","hasFooter","disabled"],["id","rdb_payment-creditcard-select-amount_pay-alternative",3,"value","radioControl","hidden","hasFooter","disabled"],["id","rdb_payment-creditcard-select-amount_pay-total",3,"value","radioControl","hidden","hasFooter","disabled"],["id","rdb_payment-creditcard-select-amount_pay-manual",3,"value","formControl","hidden"],[1,"bocc-card-radiobutton__label","subtitle2-medium"],["class","mbo-payment-creditcard-select-amount-page__manual",4,"ngIf"],[1,"mbo-payment-creditcard-select-amount-page__footer"],["icon","error","theme","danger",4,"ngIf"],["id","btn_payment-creditcard-select-amount_submit","bocc-button","raised",3,"disabled","click"],["icon","bell",3,"visible"],[1,"mbo-payment-creditcard-select-amount-page__trm"],[1,"mbo-payment-creditcard-select-amount-page__trm__label"],[1,"body2-medium"],[1,"mbo-payment-creditcard-select-amount-page__trm__value","body1-medium"],["icon","arrow-trm"],[1,"mbo-payment-creditcard-select-amount-page__manual"],["label","Deuda en pesos","placeholder","$ 0","icon","assets/shared/logos/currencies/cop-enabled.svg","currencyCode","COP",3,"formControl"],["label","Deuda en d\xf3lares","placeholder","$ 0","icon","assets/shared/logos/currencies/usd-enabled.svg","currencyCode","COP","rateCode","USD","infoHelper","Ingresa el valor en pesos",3,"formControl","rate",4,"ngIf"],[1,"mbo-payment-creditcard-select-amount-page__manual__total"],[1,"overline-medium"],[1,"subtitle1-medium"],["label","Deuda en d\xf3lares","placeholder","$ 0","icon","assets/shared/logos/currencies/usd-enabled.svg","currencyCode","COP","rateCode","USD","infoHelper","Ingresa el valor en pesos",3,"formControl","rate"],["icon","error","theme","danger"]],template:function(i,d){1&i&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3),e.\u0275\u0275element(4,"bocc-card-product-summary",4),e.\u0275\u0275template(5,W,2,1,"bocc-alert",5),e.\u0275\u0275template(6,$,10,4,"div",6),e.\u0275\u0275elementStart(7,"div",7),e.\u0275\u0275element(8,"mbo-creditcard-radiobutton",8)(9,"mbo-creditcard-radiobutton",9)(10,"mbo-creditcard-radiobutton",10),e.\u0275\u0275elementStart(11,"bocc-card-radiobutton",11)(12,"span",12),e.\u0275\u0275text(13," Otro valor "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(14,w,10,8,"div",13),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",14),e.\u0275\u0275template(16,Y,3,4,"bocc-message-icon",15),e.\u0275\u0275elementStart(17,"button",16),e.\u0275\u0275listener("click",function(){return d.onSubmit()}),e.\u0275\u0275elementStart(18,"span"),e.\u0275\u0275text(19,"Continuar"),e.\u0275\u0275elementEnd()()()),2&i&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",d.backAction)("rightAction",d.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("color",null==d.destination?null:d.destination.color)("icon",null==d.destination?null:d.destination.logo)("title",null==d.destination?null:d.destination.nickname)("number",null==d.destination?null:d.destination.publicNumber)("detail",null==d.destination?null:d.destination.bank.name)("statusColor",null==d.destination||null==d.destination.status?null:d.destination.status.color)("statusLabel",null==d.destination||null==d.destination.status?null:d.destination.status.label)("skeleton",d.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",d.canUsd),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",d.trm&&d.canUsd),e.\u0275\u0275advance(2),e.\u0275\u0275property("value",d.min)("radioControl",d.amountControl)("hidden",d.requesting)("hasFooter",!d.canUsd)("disabled",!(null!=d.min&&d.min.valid)),e.\u0275\u0275advance(1),e.\u0275\u0275property("value",d.alternative)("radioControl",d.amountControl)("hidden",d.requesting)("hasFooter",!d.canUsd)("disabled",!(null!=d.alternative&&d.alternative.valid)),e.\u0275\u0275advance(1),e.\u0275\u0275property("value",d.total)("radioControl",d.amountControl)("hidden",d.requesting)("hasFooter",!d.canUsd)("disabled",!(null!=d.total&&d.total.valid)),e.\u0275\u0275advance(1),e.\u0275\u0275property("value",d.manual)("formControl",d.amountControl)("hidden",d.requesting),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",d.isManual),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!d.sourceValid),e.\u0275\u0275advance(1),e.\u0275\u0275property("disabled",!d.sourceValid||d.disabled))},dependencies:[n.NgIf,C.D,g.J,U.B,R.Z,p.P,O.D,S.m,F.n,N.o,V.T,j.f],styles:["/*!\n * MBO PaymentCreditCardSelectAmount Component\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 18/Jul/2022\n * Updated: 20/Jun/2024\n*/mbo-payment-creditcard-select-amount-page{--page-margin-spacing: var(--sizing-x8);--page-padding-currency: 0rem var(--sizing-x8);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__body bocc-card-product-avatar{background:var(--overlay-lgrey-60);overflow:hidden;border-radius:var(--sizing-x2)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__title{color:var(--color-carbon-darker-1000)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__trm{position:relative;display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--sizing-x2) var(--sizing-x6);box-sizing:border-box;overflow:hidden;border-radius:var(--sizing-x4);background:var(--overlay-lgrey-80)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__trm__label{color:var(--color-carbon-darker-1000)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__trm__value{display:flex;-moz-column-gap:var(--sizing-x2);column-gap:var(--sizing-x2)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__trm__value span{color:var(--color-carbon-darker-1000)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__trm__value bocc-icon{color:var(--color-blue-700)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__options{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__options .bocc-creditcard-radiobutton__footer .bocc-card-currency-amount__content{padding:var(--page-padding-currency)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__options .bocc-card-radiobutton__content{border:none;box-shadow:none}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__manual{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x16)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__manual__total{position:relative;display:flex;width:100%;flex-direction:column;padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__manual__total span,mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__manual__total label{text-align:right}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__manual__total span{color:var(--color-carbon-lighter-700)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-safe-footer-x8);box-sizing:border-box;background:var(--color-carbon-lighter-50)}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__footer bocc-message-icon{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-payment-creditcard-select-amount-page .mbo-payment-creditcard-select-amount-page__footer button{width:100%}@media screen and (max-width: 320px){.mbo-payment-creditcard-select-amount-page{--page-margin-spacing: var(--sizing-x6);--page-padding-currency: 0rem var(--sizing-x4)}.mbo-payment-creditcard-select-amount-page__options .bocc-creditcard-radiobutton__content,.mbo-payment-creditcard-select-amount-page__options .bocc-creditcard-radiobutton__footer{padding:var(--sizing-x4) var(--sizing-x6)}}\n"],encapsulation:2}),a})(),r=(()=>{class a{}return a.\u0275fac=function(i){return new(i||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[n.CommonModule,h.RouterModule.forChild([{path:"",component:T}]),s.DO,s.Jx,s.B4,s.Zl,s.Dj,m.P6,s.P8,s.D1,s.mn,f.kz,f.Cq,o.o2]}),a})()},63674:(z,x,t)=>{t.d(x,{Eg:()=>E,Lo:()=>s,Wl:()=>m,ZC:()=>f,_f:()=>h,br:()=>P,tl:()=>o});var n=t(29306);const h={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},o=new n.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),s={color:"success",key:"paid",label:"Pagada"},m={color:"alert",key:"pending",label:"Por pagar"},f={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},E={color:"info",key:"programmed",label:"Programado"}},66067:(z,x,t)=>{t.d(x,{S6:()=>I,T2:()=>P,UQ:()=>A,mZ:()=>E});var n=t(39904),h=t(6472),s=t(63674),m=t(31707);class P{constructor(y,b,v,_,l,e,C,g,U,R,p){this.id=y,this.type=b,this.name=v,this.nickname=_,this.number=l,this.bank=e,this.isAval=C,this.isProtected=g,this.isOwner=U,this.ownerName=R,this.ownerDocument=p,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[n.y1],this.initialsName=(0,h.initials)(_),this.shortNumber=l.substring(l.length-4),this.descriptionNumber=`${v} ${l}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:e.logo,light:e.logo,standard:e.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(y){this.informationValue||(this.informationValue=y)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(y){return this.currenciesValue.includes(y)}}class E{constructor(y,b){this.id=y,this.type=b}}class I{constructor(y,b,v,_,l,e,C,g,U,R,p,O){this.uuid=y,this.number=b,this.nie=v,this.nickname=_,this.companyId=l,this.companyName=e,this.amount=C,this.registerDate=g,this.expirationDate=U,this.paid=R,this.statusCode=p,this.references=O,this.recurring=O.length>0,this.status=function f(M){switch(M){case m.U.EXPIRED:return s.ZC;case m.U.PENDING:return s.Wl;case m.U.PROGRAMMED:return s.Eg;case m.U.RECURRING:return s.br;default:return s.Lo}}(p)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class A{constructor(y,b,v,_,l,e,C,g){this.uuid=y,this.number=b,this.nickname=v,this.companyId=_,this.companyName=l,this.city=e,this.amount=C,this.isBiller=g}}},31707:(z,x,t)=>{t.d(x,{U:()=>n,f:()=>h});var n=(()=>{return(o=n||(n={})).RECURRING="1",o.EXPIRED="2",o.PENDING="3",o.PROGRAMMED="4",n;var o})(),h=(()=>{return(o=h||(h={})).BILLER="Servicio",o.NON_BILLER="Servicio",o.PSE="Servicio",o.TAX="Impuesto",o.LOAN="Obligaci\xf3n financiera",o.CREDIT_CARD="Obligaci\xf3n financiera",h;var o})()}}]);