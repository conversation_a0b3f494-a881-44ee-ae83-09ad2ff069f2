(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7432],{108:(E,h,r)=>{r.d(h,{a:()=>y,z:()=>v});var e=r(87903),p=r(53113);function f(n){const{isError:t,message:s,type:d}=n;return{animation:(0,e.jY)(n),title:t?"\xa1Transferencia fallida!":"PENDING"===d?"\xa1Transferencia en proceso!":"\xa1Transferencia exitosa!",subtitle:s}}function P({isError:n}){return n?[(0,e.wT)("Finalizar","finish","outline"),(0,e.wT)("Volver a intentar","retry")]:[(0,e.wT)("Hacer otra transferencia","retry","outline"),(0,e.wT)("Finalizar","finish")]}function T(n){const{approved:t,pending:s}=n;if(t)return function a(n,t){const{dateFormat:s,timeFormat:d}=new p.ou,c=[(0,e.SP)("DESTINO",n.product.nickname,n.product.bank.name,n.product.publicNumber),(0,e._f)("SUMA DE",t.amount),(0,e.SP)("DESDE",t.phone)];return t.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",t.description)),c.push((0,e.cZ)(s,d)),c}(n,t);if(s)return function g(n,t){const{dateFormat:s,timeFormat:d}=new p.ou,c=[(0,e.SP)("DESTINO",n.product.nickname,n.product.bank.name,n.product.publicNumber),(0,e._f)("SUMA DE",t.amount),(0,e.SP)("DESDE",t.phone)];return t.description&&c.push((0,e.SP)("DESCRIPCI\xd3N",t.description)),c.push((0,e.cZ)(s,d)),c}(n,s);const{dateFormat:d,timeFormat:c}=new p.ou,u=[(0,e.SP)("TRANSFER"===n.type?"ENVIADO A":"SOLICITADO A",n.contact?.name,n.contact?.number),(0,e._f)("SUMA DE",n.amount)];return n.description&&u.push((0,e.SP)("DESCRIPCI\xd3N","","",n.description)),u.push((0,e.cZ)(d,c)),u}function v(n){const{status:t,transfiya:s}=n;return{actions:P(t),error:t.isError,header:f(t),informations:T(s),skeleton:!1}}function y(n){const t=[],{amount:s,category:d,color:c,date:{dateFormat:u,timeFormat:i},description:l,phoneFormat:o,reference:m}=n;return t.push((0,e.SP)("REFERENCIA",m)),t.push((0,e.fW)("TIPO DE TRANSACCI\xd3N",c,d)),t.push((0,e.SP)("CONTACTO",o)),t.push((0,e._f)("LA SUMA DE",s)),l&&t.push((0,e.Kt)("DESCRIPCI\xd3N",l)),t.push((0,e.cZ)(u,i)),t}},67432:(E,h,r)=>{r.r(h),r.d(h,{MboTransfiyaPendingResultPageModule:()=>u});var e=r(17007),p=r(78007),f=r(79798),P=r(15861),a=r(99877),g=r(39904),T=r(95437),v=r(108),y=r(17698),n=r(10464),t=r(78021),s=r(16442);function d(i,l){if(1&i&&(a.\u0275\u0275elementStart(0,"div",4),a.\u0275\u0275element(1,"mbo-header-result",5),a.\u0275\u0275elementEnd()),2&i){const o=a.\u0275\u0275nextContext();a.\u0275\u0275advance(1),a.\u0275\u0275property("rightActions",o.rightActions)}}let c=(()=>{class i{constructor(o,m,b){this.ref=o,this.mboProvider=m,this.managerTransfiya=b,this.requesting=!0,this.template=g.$d,this.rightActions=[{id:"btn_transfiya-pending-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_transfiya-pending-result-page_template"),this.initializatedTransaction()}onAction(o){this.mboProvider.navigation.next("finish"===o?g.Z6.CUSTOMER.PRODUCTS.HOME:g.Z6.TRANSFERS.TRANSFIYA.PENDING.HOME)}initializatedTransaction(){var o=this;return(0,P.Z)(function*(){(yield o.managerTransfiya.confirmPending()).when({success:m=>{o.template=(0,v.z)(m)}},()=>{o.requesting=!1,o.managerTransfiya.reset()})})()}}return i.\u0275fac=function(o){return new(o||i)(a.\u0275\u0275directiveInject(a.ElementRef),a.\u0275\u0275directiveInject(T.ZL),a.\u0275\u0275directiveInject(y.Pm))},i.\u0275cmp=a.\u0275\u0275defineComponent({type:i,selectors:[["mbo-transfiya-pending-result-page"]],decls:5,vars:2,consts:[[1,"mbo-transfiya-pending-result-page__content","mbo-page__scroller"],["class","mbo-transfiya-pending-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-transfiya-pending-result-page__body"],["id","crd_transfiya-pending-result-page_template",3,"template","action"],[1,"mbo-transfiya-pending-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(o,m){1&o&&(a.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),a.\u0275\u0275template(2,d,2,1,"div",1),a.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),a.\u0275\u0275listener("action",function(I){return m.onAction(I)}),a.\u0275\u0275elementEnd()()()()),2&o&&(a.\u0275\u0275advance(2),a.\u0275\u0275property("ngIf",!m.requesting),a.\u0275\u0275advance(2),a.\u0275\u0275property("template",m.template))},dependencies:[e.NgIf,n.K,t.c,s.u],styles:["/*!\n * MBO TransfiyaPendingResult Page\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-pending-result-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-pending-result-page .mbo-transfiya-pending-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-pending-result-page .mbo-transfiya-pending-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),i})(),u=(()=>{class i{}return i.\u0275fac=function(o){return new(o||i)},i.\u0275mod=a.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=a.\u0275\u0275defineInjector({imports:[e.CommonModule,p.RouterModule.forChild([{path:"",component:c}]),f.KI,f.cN,f.tu]}),i})()}}]);