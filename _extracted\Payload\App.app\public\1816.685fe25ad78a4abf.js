(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1816],{21816:(w,c,p)=>{function d(r){this.message=r}p.r(c),p.d(c,{InvalidTokenError:()=>a,default:()=>h}),(d.prototype=new Error).name="InvalidCharacterError";var s=typeof window<"u"&&window.atob&&window.atob.bind(window)||function(r){var e=String(r).replace(/=+$/,"");if(e.length%4==1)throw new d("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,t,i=0,o=0,l="";t=e.charAt(o++);~t&&(n=i%4?64*n+t:t,i++%4)?l+=String.fromCharCode(255&n>>(-2*i&6)):0)t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(t);return l};function a(r){this.message=r}(a.prototype=new Error).name="InvalidTokenError";const h=function u(r,e){if("string"!=typeof r)throw new a("Invalid token specified");var n=!0===(e=e||{}).header?0:1;try{return JSON.parse(function f(r){var e=r.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:e+="==";break;case 3:e+="=";break;default:throw"Illegal base64url string!"}try{return decodeURIComponent(s(e).replace(/(.)/g,function(t,i){var o=i.charCodeAt(0).toString(16).toUpperCase();return o.length<2&&(o="0"+o),"%"+o}))}catch{return s(e)}}(r.split(".")[n]))}catch(t){throw new a("Invalid token specified: "+t.message)}}}}]);