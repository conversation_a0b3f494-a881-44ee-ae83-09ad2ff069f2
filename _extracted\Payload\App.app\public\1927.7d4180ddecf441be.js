(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1927,4059],{86437:(ce,H,E)=>{E.d(H,{f:()=>G});var v=E(97582),m=64,k=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),w=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],I=Math.pow(2,53)-1,S=function(){function N(){this.state=Int32Array.from(w),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return N.prototype.update=function(b){if(this.finished)throw new Error("Attempted to update an already finished hash.");var R=0,x=b.byteLength;if(this.bytesHashed+=x,8*this.bytesHashed>I)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;x>0;)this.buffer[this.bufferLength++]=b[R++],x--,this.bufferLength===m&&(this.hashBuffer(),this.bufferLength=0)},N.prototype.digest=function(){if(!this.finished){var b=8*this.bytesHashed,R=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),x=this.bufferLength;if(R.setUint8(this.bufferLength++,128),x%m>=56){for(var z=this.bufferLength;z<m;z++)R.setUint8(z,0);this.hashBuffer(),this.bufferLength=0}for(z=this.bufferLength;z<56;z++)R.setUint8(z,0);R.setUint32(56,Math.floor(b/4294967296),!0),R.setUint32(60,b),this.hashBuffer(),this.finished=!0}var se=new Uint8Array(32);for(z=0;z<8;z++)se[4*z]=this.state[z]>>>24&255,se[4*z+1]=this.state[z]>>>16&255,se[4*z+2]=this.state[z]>>>8&255,se[4*z+3]=this.state[z]>>>0&255;return se},N.prototype.hashBuffer=function(){for(var R=this.buffer,x=this.state,z=x[0],se=x[1],de=x[2],ie=x[3],ee=x[4],Se=x[5],_e=x[6],he=x[7],ae=0;ae<m;ae++){if(ae<16)this.temp[ae]=(255&R[4*ae])<<24|(255&R[4*ae+1])<<16|(255&R[4*ae+2])<<8|255&R[4*ae+3];else{var fe=this.temp[ae-2];this.temp[ae]=(((fe>>>17|fe<<15)^(fe>>>19|fe<<13)^fe>>>10)+this.temp[ae-7]|0)+((((fe=this.temp[ae-15])>>>7|fe<<25)^(fe>>>18|fe<<14)^fe>>>3)+this.temp[ae-16]|0)}var Re=(((ee>>>6|ee<<26)^(ee>>>11|ee<<21)^(ee>>>25|ee<<7))+(ee&Se^~ee&_e)|0)+(he+(k[ae]+this.temp[ae]|0)|0)|0,Pe=((z>>>2|z<<30)^(z>>>13|z<<19)^(z>>>22|z<<10))+(z&se^z&de^se&de)|0;he=_e,_e=Se,Se=ee,ee=ie+Re|0,ie=de,de=se,se=z,z=Re+Pe|0}x[0]+=z,x[1]+=se,x[2]+=de,x[3]+=ie,x[4]+=ee,x[5]+=Se,x[6]+=_e,x[7]+=he},N}(),W=typeof Buffer<"u"&&Buffer.from?function(N){return Buffer.from(N,"utf8")}:N=>(new TextEncoder).encode(N);function L(N){return N instanceof Uint8Array?N:"string"==typeof N?W(N):ArrayBuffer.isView(N)?new Uint8Array(N.buffer,N.byteOffset,N.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(N)}var G=function(){function N(b){this.secret=b,this.hash=new S,this.reset()}return N.prototype.update=function(b){if(!function D(N){return"string"==typeof N?0===N.length:0===N.byteLength}(b)&&!this.error)try{this.hash.update(L(b))}catch(R){this.error=R}},N.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},N.prototype.digest=function(){return(0,v.mG)(this,void 0,void 0,function(){return(0,v.Jh)(this,function(b){return[2,this.digestSync()]})})},N.prototype.reset=function(){if(this.hash=new S,this.secret){this.outer=new S;var b=function j(N){var b=L(N);if(b.byteLength>m){var R=new S;R.update(b),b=R.digest()}var x=new Uint8Array(m);return x.set(b),x}(this.secret),R=new Uint8Array(m);R.set(b);for(var x=0;x<m;x++)b[x]^=54,R[x]^=92;for(this.hash.update(b),this.outer.update(R),x=0;x<b.byteLength;x++)b[x]=0}},N}()},95472:(ce,H,E)=>{E.d(H,{N:()=>k});const v={},m={};for(let w=0;w<256;w++){let I=w.toString(16).toLowerCase();1===I.length&&(I=`0${I}`),v[w]=I,m[I]=w}function k(w){let I="";for(let S=0;S<w.byteLength;S++)I+=v[w[S]];return I}},34059:(ce,H,E)=>{E.r(H),E.d(H,{CONNECTION_STATE_CHANGE:()=>W,CONTROL_MSG:()=>p,ConnectionState:()=>h,GraphQLAPI:()=>vr,GraphQLAPIClass:()=>tn,GraphQLAuthError:()=>De,__amplify:()=>wt,__apiKey:()=>Pt,__authMode:()=>Nt,__authToken:()=>Rt,__endpoint:()=>xt,__headers:()=>Mt,events:()=>v,getInternals:()=>xn,graphqlOperation:()=>Sr});var v={};E.r(v),E.d(v,{closeAll:()=>Wn,connect:()=>Qn,post:()=>Bn});var m=E(15861),U=E(98778),k=E(92261),w=E(5919);const S=[400,401,403],F=["BadRequestException","UnauthorizedException"],W="ConnectionStateChange";var L=(()=>{return(e=L||(L={})).GQL_CONNECTION_INIT="connection_init",e.GQL_CONNECTION_ERROR="connection_error",e.GQL_CONNECTION_ACK="connection_ack",e.GQL_START="start",e.GQL_START_ACK="start_ack",e.DATA="data",e.GQL_CONNECTION_KEEP_ALIVE="ka",e.GQL_STOP="stop",e.GQL_COMPLETE="complete",e.GQL_ERROR="error",e.EVENT_SUBSCRIBE="subscribe",e.EVENT_PUBLISH="publish",e.EVENT_SUBSCRIBE_ACK="subscribe_success",e.EVENT_PUBLISH_ACK="publish_success",e.EVENT_STOP="unsubscribe",e.EVENT_COMPLETE="unsubscribe_success",L;var e})(),D=(()=>{return(e=D||(D={}))[e.PENDING=0]="PENDING",e[e.CONNECTED=1]="CONNECTED",e[e.FAILED=2]="FAILED",D;var e})(),G=(()=>{return(e=G||(G={}))[e.CLOSED=0]="CLOSED",e[e.READY=1]="READY",e[e.CONNECTING=2]="CONNECTING",G;var e})();const j={accept:"application/json, text/javascript","content-encoding":"amz-1.0","content-type":"application/json; charset=UTF-8"},R=3e5;var ie=E(42168),ee=E(54646),Se=E(23192);class _e extends Error{constructor(){super(...arguments),this.nonRetryable=!0}}var he=E(38261);const ae=e=>e&&e.nonRetryable;var fe=E(10180);const Oe={convert(e,t={urlSafe:!1,skipPadding:!1}){const n="string"==typeof e?e:function Ce(e){return Array.from(e,t=>String.fromCodePoint(t)).join("")}(e);let r=(0,fe.Ds)()(n);return t.urlSafe&&(r=r.replace(/\+/g,"-").replace(/\//g,"_")),t.skipPadding&&(r=r.replace(/=/g,"")),r}};var Re=E(90206),Pe=E(75599);const Me=new(E(55502).k)("retryUtil");function pe(){return pe=(0,m.Z)(function*(e,t,n,r){if("function"!=typeof e)throw Error("functionToRetry must be a function");return new Promise(function(){var o=(0,m.Z)(function*(s,i){let c,g,a=0,l=!1,u=()=>{};for(r&&r.then(()=>{l=!0,clearTimeout(c),u()});!l;){a++,Me.debug(`${e.name} attempt #${a} with this vars: ${JSON.stringify(t)}`);try{return void s(yield e(...t))}catch(y){if(g=y,Me.debug(`error on ${e.name}`,y),ae(y))return Me.debug(`${e.name} non retryable error`,y),void i(y);const C=n(a,t,y);if(Me.debug(`${e.name} retrying in ${C} ms`),!1===C||l)return void i(y);yield new Promise(O=>{u=O,c=setTimeout(u,C)})}}i(g)});return function(s,i){return o.apply(this,arguments)}}())}),pe.apply(this,arguments)}const f=(e,t,n=Re.t,r)=>function Le(e,t,n,r){return pe.apply(this,arguments)}(e,t,(0,Pe.k)(n),r);var p=(()=>{return(e=p||(p={})).CONNECTION_CLOSED="Connection closed",e.CONNECTION_FAILED="Connection failed",e.REALTIME_SUBSCRIPTION_INIT_ERROR="AppSync Realtime subscription init error",e.SUBSCRIPTION_ACK="Subscription ack",e.TIMEOUT_DISCONNECT="Timeout disconnect",p;var e})(),h=(()=>{return(e=h||(h={})).Connected="Connected",e.ConnectedPendingNetwork="ConnectedPendingNetwork",e.ConnectionDisrupted="ConnectionDisrupted",e.ConnectionDisruptedPendingNetwork="ConnectionDisruptedPendingNetwork",e.Connecting="Connecting",e.ConnectedPendingDisconnect="ConnectedPendingDisconnect",e.Disconnected="Disconnected",e.ConnectedPendingKeepAlive="ConnectedPendingKeepAlive",h;var e})();let _=(()=>{class e{networkMonitor(n){const r=(()=>{if(typeof self>"u")return!1;const e=self;return typeof e.WorkerGlobalScope<"u"&&self instanceof e.WorkerGlobalScope})()?self:typeof window<"u"&&window;return r?new ie.Observable(o=>{o.next({online:r.navigator.onLine});const s=()=>{o.next({online:!0})},i=()=>{o.next({online:!1})};return r.addEventListener("online",s),r.addEventListener("offline",i),e._observers.push(o),()=>{r.removeEventListener("online",s),r.removeEventListener("offline",i),e._observers=e._observers.filter(a=>a!==o)}}):(0,ie.from)([{online:!0}])}static _observerOverride(n){for(const r of this._observers)r.closed?this._observers=this._observers.filter(o=>o!==r):r?.next&&r.next(n)}}return e._observers=[],e})();const A=()=>(new _).networkMonitor(),M={KEEP_ALIVE_MISSED:{keepAliveState:"unhealthy"},KEEP_ALIVE:{keepAliveState:"healthy"},CONNECTION_ESTABLISHED:{connectionState:"connected"},CONNECTION_FAILED:{intendedConnectionState:"disconnected",connectionState:"disconnected"},CLOSING_CONNECTION:{intendedConnectionState:"disconnected"},OPENING_CONNECTION:{intendedConnectionState:"connected",connectionState:"connecting"},CLOSED:{connectionState:"disconnected"},ONLINE:{networkState:"connected"},OFFLINE:{networkState:"disconnected"}};class Z{constructor(){this._networkMonitoringSubscription=void 0,this._linkedConnectionState={networkState:"connected",connectionState:"disconnected",intendedConnectionState:"disconnected",keepAliveState:"healthy"},this._initialNetworkStateSubscription=A().subscribe(({online:t})=>{this.record(t?M.ONLINE:M.OFFLINE),this._initialNetworkStateSubscription?.unsubscribe()}),this._linkedConnectionStateObservable=new ie.Observable(t=>{t.next(this._linkedConnectionState),this._linkedConnectionStateObserver=t})}enableNetworkMonitoring(){this._initialNetworkStateSubscription?.unsubscribe(),void 0===this._networkMonitoringSubscription&&(this._networkMonitoringSubscription=A().subscribe(({online:t})=>{this.record(t?M.ONLINE:M.OFFLINE)}))}disableNetworkMonitoring(){this._networkMonitoringSubscription?.unsubscribe(),this._networkMonitoringSubscription=void 0}get connectionStateObservable(){let t;return this._linkedConnectionStateObservable.pipe((0,ie.map)(n=>this.connectionStatesTranslator(n))).pipe((0,ie.filter)(n=>{const r=n!==t;return t=n,r}))}record(t){"connected"===t.intendedConnectionState?this.enableNetworkMonitoring():"disconnected"===t.intendedConnectionState&&this.disableNetworkMonitoring();const n={...this._linkedConnectionState,...t};this._linkedConnectionState={...n},this._linkedConnectionStateObserver?.next(this._linkedConnectionState)}connectionStatesTranslator({connectionState:t,networkState:n,intendedConnectionState:r,keepAliveState:o}){return"connected"===t&&"disconnected"===n?h.ConnectedPendingNetwork:"connected"===t&&"disconnected"===r?h.ConnectedPendingDisconnect:"disconnected"===t&&"connected"===r&&"disconnected"===n?h.ConnectionDisruptedPendingNetwork:"disconnected"===t&&"connected"===r?h.ConnectionDisrupted:"connected"===t&&"unhealthy"===o?h.ConnectedPendingKeepAlive:"connecting"===t?h.Connecting:"disconnected"===t?h.Disconnected:h.Connected}}var re=(()=>{return(e=re||(re={})).START_RECONNECT="START_RECONNECT",e.HALT_RECONNECT="HALT_RECONNECT",re;var e})();class B{constructor(){this.reconnectObservers=[]}addObserver(t){this.reconnectObservers.push(t)}record(t){t===re.START_RECONNECT&&void 0===this.reconnectSetTimeoutId&&void 0===this.reconnectIntervalId&&(this.reconnectSetTimeoutId=setTimeout(()=>{this._triggerReconnect(),this.reconnectIntervalId=setInterval(()=>{this._triggerReconnect()},6e4)},5e3)),t===re.HALT_RECONNECT&&(this.reconnectIntervalId&&(clearInterval(this.reconnectIntervalId),this.reconnectIntervalId=void 0),this.reconnectSetTimeoutId&&(clearTimeout(this.reconnectSetTimeoutId),this.reconnectSetTimeoutId=void 0))}close(){this.reconnectObservers.forEach(t=>{t.complete?.()})}_triggerReconnect(){this.reconnectObservers.forEach(t=>{t.next?.()})}}var K=E(99120);const ve=/^https:\/\/\w{26}\.appsync-api\.\w{2}(?:(?:-\w{2,})+)-\d\.amazonaws.com(?:\.cn)?\/graphql$/i,te=/^https:\/\/\w{26}\.\w+-api\.\w{2}(?:(?:-\w{2,})+)-\d\.amazonaws.com(?:\.cn)?\/event$/i,be="/realtime",Xe=function(){var e=(0,m.Z)(function*(t){const{appSyncGraphqlEndpoint:n,query:r,libraryConfigHeaders:o=(()=>({})),additionalHeaders:s={},authToken:i}=t;let a={};const l=yield o();return a="function"==typeof s?yield s({url:n||"",queryString:r||""}):s,i&&(a={...a,Authorization:i}),{additionalCustomHeaders:a,libraryConfigHeaders:l}});return function(n){return e.apply(this,arguments)}}();var sn=E(74109);const mt=new U.ConsoleLogger("AWSAppSyncRealTimeProvider Auth"),_t=function(){var e=(0,m.Z)(function*({host:t}){return{Authorization:(yield(0,U.fetchAuthSession)())?.tokens?.accessToken?.toString(),host:t}});return function(n){return e.apply(this,arguments)}}(),an=function(){var e=(0,m.Z)(function*({apiKey:t,host:n}){return{host:n,"x-amz-date":(new Date).toISOString().replace(/[:-]|\.\d{3}/g,""),"x-api-key":t}});return function(n){return e.apply(this,arguments)}}(),cn=function(){var e=(0,m.Z)(function*({payload:t,canonicalUri:n,appSyncGraphqlEndpoint:r,region:o}){const s={region:o,service:"appsync"},i=(yield(0,U.fetchAuthSession)()).credentials,a={url:`${r}${n}`,data:t,method:"POST",headers:{...j}};return(0,sn.C)({headers:a.headers,method:a.method,url:new K.a(a.url),body:a.data},{credentials:i,signingRegion:s.region,signingService:s.service}).headers});return function(n){return e.apply(this,arguments)}}(),bt=function(){var e=(0,m.Z)(function*({host:t,additionalCustomHeaders:n}){if(!n?.Authorization)throw new Error("No auth token specified");return{Authorization:n.Authorization,host:t}});return function(n){return e.apply(this,arguments)}}(),et=function(){var e=(0,m.Z)(function*({apiKey:t,authenticationType:n,canonicalUri:r,appSyncGraphqlEndpoint:o,region:s,additionalCustomHeaders:i,payload:a}){const l={apiKey:an,iam:cn,oidc:_t,userPool:_t,lambda:bt,none:bt};if(n&&l[n]){const c=l[n],u=o?new K.a(o).host:void 0,g="apiKey"===n?t:void 0;return mt.debug(`Authenticating with ${JSON.stringify(n)}`),yield c({payload:a,canonicalUri:r,appSyncGraphqlEndpoint:o,apiKey:g,region:s,host:u,additionalCustomHeaders:i})}mt.debug(`Authentication type ${n} not supported`)});return function(n){return e.apply(this,arguments)}}(),At=e=>{U.Hub.dispatch("api",e,"PubSub",Se.SQ)};class Et{constructor(t){var n=this;this.subscriptionObserverMap=new Map,this.socketStatus=G.CLOSED,this.keepAliveTimestamp=Date.now(),this.promiseArray=[],this.connectionStateMonitor=new Z,this.reconnectionMonitor=new B,this._establishConnection=function(){var r=(0,m.Z)(function*(o,s){n.logger.debug(`Establishing WebSocket connection to ${o}`);try{yield n._openConnection(o,s),yield n._initiateHandshake()}catch(i){const{errorType:a,errorCode:l}=i;throw S.includes(l)||F.includes(a)?new _e(a):a?new Error(a):i}});return function(o,s){return r.apply(this,arguments)}}(),this.logger=new U.ConsoleLogger(t.providerName),this.wsProtocolName=t.wsProtocolName,this.wsConnectUri=t.connectUri,this.connectionStateMonitorSubscription=this._startConnectionStateMonitoring()}close(){return this.socketStatus=G.CLOSED,this.connectionStateMonitor.record(M.CONNECTION_FAILED),this.connectionStateMonitorSubscription.unsubscribe(),this.reconnectionMonitor.close(),new Promise((t,n)=>{this.awsRealTimeSocket?(this.awsRealTimeSocket.onclose=r=>{this._closeSocket(),this.subscriptionObserverMap=new Map,this.awsRealTimeSocket=void 0,t()},this.awsRealTimeSocket.onerror=r=>{n(r)},this.awsRealTimeSocket.close()):t()})}subscribe(t,n){var r=this;return new ie.Observable(o=>{if(!t?.appSyncGraphqlEndpoint)return o.error({errors:[{...new ee.GraphQLError("Subscribe only available for AWS AppSync endpoint")}]}),void o.complete();let s=!1;const i=(0,he.r)(),a=()=>{s||(s=!0,this._startSubscriptionWithAWSAppSyncRealTime({options:t,observer:o,subscriptionId:i,customUserAgentDetails:n}).catch(c=>{this.logger.debug(`${p.REALTIME_SUBSCRIPTION_INIT_ERROR}: ${c}`),this._closeSocket()}).finally(()=>{s=!1}))},l=new ie.Observable(c=>{this.reconnectionMonitor.addObserver(c)}).subscribe(()=>{a()});return a(),(0,m.Z)(function*(){yield r._cleanupSubscription(i,l)})})}connect(t){var n=this;return(0,m.Z)(function*(){n.socketStatus!==G.READY&&(yield n._connectWebSocket(t))})()}publish(t,n){var r=this;return(0,m.Z)(function*(){if(r.socketStatus!==G.READY)throw new Error("Subscription has not been initialized");return r._publishMessage(t,n)})()}_connectWebSocket(t){var n=this;return(0,m.Z)(function*(){const{apiKey:r,appSyncGraphqlEndpoint:o,authenticationType:s,region:i}=t,{additionalCustomHeaders:a}=yield Xe(t);n.connectionStateMonitor.record(M.OPENING_CONNECTION),yield n._initializeWebSocketConnection({apiKey:r,appSyncGraphqlEndpoint:o,authenticationType:s,region:i,additionalCustomHeaders:a})})()}_publishMessage(t,n){var r=this;return(0,m.Z)(function*(){const o=(0,he.r)(),{additionalCustomHeaders:s,libraryConfigHeaders:i}=yield Xe(t),a=yield r._prepareSubscriptionPayload({options:t,subscriptionId:o,customUserAgentDetails:n,additionalCustomHeaders:s,libraryConfigHeaders:i,publish:!0});return new Promise((l,c)=>{if(r.awsRealTimeSocket){const u=g=>{const y=JSON.parse(g.data);y.id===o&&"publish_success"===y.type&&(r.awsRealTimeSocket&&r.awsRealTimeSocket.removeEventListener("message",u),l())};r.awsRealTimeSocket.addEventListener("message",u),r.awsRealTimeSocket.addEventListener("close",()=>{c(new Error("WebSocket is closed"))}),r.awsRealTimeSocket.send(a)}})})()}_cleanupSubscription(t,n){var r=this;return(0,m.Z)(function*(){n?.unsubscribe();try{yield r._waitForSubscriptionToBeConnected(t);const{subscriptionState:o}=r.subscriptionObserverMap.get(t)||{};if(!o)return;if(o!==D.CONNECTED)throw new Error("Subscription never connected");r._sendUnsubscriptionMessage(t)}catch(o){r.logger.debug(`Error while unsubscribing ${o}`)}finally{r._removeSubscriptionObserver(t)}})()}_startConnectionStateMonitoring(){return this.connectionStateMonitor.connectionStateObservable.subscribe(t=>{At({event:W,data:{provider:this,connectionState:t},message:`Connection state is ${t}`}),this.connectionState=t,t===h.ConnectionDisrupted&&this.reconnectionMonitor.record(re.START_RECONNECT),[h.Connected,h.ConnectedPendingDisconnect,h.ConnectedPendingKeepAlive,h.ConnectedPendingNetwork,h.ConnectionDisruptedPendingNetwork,h.Disconnected].includes(t)&&this.reconnectionMonitor.record(re.HALT_RECONNECT)})}_startSubscriptionWithAWSAppSyncRealTime({options:t,observer:n,subscriptionId:r,customUserAgentDetails:o}){var s=this;return(0,m.Z)(function*(){const{query:i,variables:a}=t,{additionalCustomHeaders:l,libraryConfigHeaders:c}=yield Xe(t);s.subscriptionObserverMap.set(r,{observer:n,query:i??"",variables:a??{},subscriptionState:D.PENDING,startAckTimeoutId:void 0});const u=yield s._prepareSubscriptionPayload({options:t,subscriptionId:r,customUserAgentDetails:o,additionalCustomHeaders:l,libraryConfigHeaders:c});try{yield s._connectWebSocket(t)}catch(C){return void s._logStartSubscriptionError(r,n,C)}const{subscriptionFailedCallback:g,subscriptionReadyCallback:y}=s.subscriptionObserverMap.get(r)??{};s.subscriptionObserverMap.set(r,{observer:n,subscriptionState:D.PENDING,query:i??"",variables:a??{},subscriptionReadyCallback:y,subscriptionFailedCallback:g,startAckTimeoutId:setTimeout(()=>{s._timeoutStartSubscriptionAck(r)},15e3)}),s.awsRealTimeSocket&&s.awsRealTimeSocket.send(u)})()}_logStartSubscriptionError(t,n,r){this.logger.debug({err:r});const o=String(r.message??"");if(this._closeSocket(),this.connectionState!==h.ConnectionDisruptedPendingNetwork){ae(r)?n.error({errors:[{...new ee.GraphQLError(`${p.CONNECTION_FAILED}: ${o}`)}]}):this.logger.debug(`${p.CONNECTION_FAILED}: ${o}`);const{subscriptionFailedCallback:s}=this.subscriptionObserverMap.get(t)||{};"function"==typeof s&&s()}}_waitForSubscriptionToBeConnected(t){var n=this;return(0,m.Z)(function*(){const r=n.subscriptionObserverMap.get(t);if(r){const{subscriptionState:o}=r;if(o===D.PENDING)return new Promise((s,i)=>{const{observer:a,subscriptionState:l,variables:c,query:u}=r;n.subscriptionObserverMap.set(t,{observer:a,subscriptionState:l,variables:c,query:u,subscriptionReadyCallback:s,subscriptionFailedCallback:i})})}})()}_sendUnsubscriptionMessage(t){try{if(this.awsRealTimeSocket&&this.awsRealTimeSocket.readyState===WebSocket.OPEN&&this.socketStatus===G.READY){const n=this._unsubscribeMessage(t),r=JSON.stringify(n);this.awsRealTimeSocket.send(r)}}catch(n){this.logger.debug({err:n})}}_removeSubscriptionObserver(t){this.subscriptionObserverMap.delete(t),setTimeout(this._closeSocketIfRequired.bind(this),1e3)}_closeSocketIfRequired(){if(!(this.subscriptionObserverMap.size>0)){if(!this.awsRealTimeSocket)return void(this.socketStatus=G.CLOSED);if(this.connectionStateMonitor.record(M.CLOSING_CONNECTION),this.awsRealTimeSocket.bufferedAmount>0)setTimeout(this._closeSocketIfRequired.bind(this),1e3);else{this.logger.debug("closing WebSocket...");const t=this.awsRealTimeSocket;t.onclose=null,t.onerror=null,t.close(1e3),this.awsRealTimeSocket=void 0,this.socketStatus=G.CLOSED,this._closeSocket()}}}maintainKeepAlive(){this.keepAliveTimestamp=Date.now()}keepAliveHeartbeat(t){const n=Date.now();this.connectionStateMonitor.record(n-this.keepAliveTimestamp>65e3?M.KEEP_ALIVE_MISSED:M.KEEP_ALIVE),n-this.keepAliveTimestamp>t&&this._errorDisconnect(p.TIMEOUT_DISCONNECT)}_handleIncomingSubscriptionMessage(t){if("string"!=typeof t.data)return;const[n,r]=this._handleSubscriptionData(t);if(n)return void this.maintainKeepAlive();const{type:o,id:s,payload:i}=r,{observer:a=null,query:l="",variables:c={},startAckTimeoutId:u,subscriptionReadyCallback:g,subscriptionFailedCallback:y}=this.subscriptionObserverMap.get(s)||{};if(o===L.GQL_START_ACK||o===L.EVENT_SUBSCRIBE_ACK)return this.logger.debug(`subscription ready for ${JSON.stringify({query:l,variables:c})}`),"function"==typeof g&&g(),u&&clearTimeout(u),At({event:p.SUBSCRIPTION_ACK,data:{query:l,variables:c},message:"Connection established for subscription"}),a&&this.subscriptionObserverMap.set(s,{observer:a,query:l,variables:c,startAckTimeoutId:void 0,subscriptionState:D.CONNECTED,subscriptionReadyCallback:g,subscriptionFailedCallback:y}),void this.connectionStateMonitor.record(M.CONNECTION_ESTABLISHED);o!==L.GQL_CONNECTION_KEEP_ALIVE?o===L.GQL_ERROR&&a&&(this.subscriptionObserverMap.set(s,{observer:a,query:l,variables:c,startAckTimeoutId:u,subscriptionReadyCallback:g,subscriptionFailedCallback:y,subscriptionState:D.FAILED}),this.logger.debug(`${p.CONNECTION_FAILED}: ${JSON.stringify(i??r)}`),a.error({errors:[{...new ee.GraphQLError(`${p.CONNECTION_FAILED}: ${JSON.stringify(i??r)}`)}]}),u&&clearTimeout(u),"function"==typeof y&&y()):this.maintainKeepAlive()}_errorDisconnect(t){this.logger.debug(`Disconnect error: ${t}`),this.awsRealTimeSocket&&(this._closeSocket(),this.awsRealTimeSocket.close()),this.socketStatus=G.CLOSED}_closeSocket(){this.keepAliveHeartbeatIntervalId&&(clearInterval(this.keepAliveHeartbeatIntervalId),this.keepAliveHeartbeatIntervalId=void 0),this.connectionStateMonitor.record(M.CLOSED)}_timeoutStartSubscriptionAck(t){const n=this.subscriptionObserverMap.get(t);if(n){const{observer:r,query:o,variables:s}=n;if(!r)return;this.subscriptionObserverMap.set(t,{observer:r,query:o,variables:s,subscriptionState:D.FAILED}),this._closeSocket(),this.logger.debug("timeoutStartSubscription",JSON.stringify({query:o,variables:s}))}}_initializeWebSocketConnection({appSyncGraphqlEndpoint:t,authenticationType:n,apiKey:r,region:o,additionalCustomHeaders:s}){var i=this;if(this.socketStatus!==G.READY)return new Promise(function(){var a=(0,m.Z)(function*(l,c){if(i.promiseArray.push({res:l,rej:c}),i.socketStatus===G.CLOSED)try{i.socketStatus=G.CONNECTING;const g=yield et({authenticationType:n,payload:"{}",canonicalUri:i.wsConnectUri,apiKey:r,appSyncGraphqlEndpoint:t,region:o,additionalCustomHeaders:s}),y=g?JSON.stringify(g):"",O=`header-${Oe.convert(y,{urlSafe:!0,skipPadding:!0})}`,T=(e=>{const t=(e=>{if(!e)return{};if("Authorization"in e){const{Authorization:t,...n}=e;return n}return e})(e),n=new K.z;return Object.entries(t).forEach(([r,o])=>{n.append(r,o)}),n})(s),P=((e,t)=>{const n=(e=>{let t=e??"";return t=(e=>null!==e.match(te))(t)?t.concat(be).replace("ddpg-api","grt-gamma").replace("appsync-api","appsync-realtime-api"):(e=>null===e.match(ve))(t)?t.concat(be):t.replace("appsync-api","appsync-realtime-api").replace("gogi-beta","grt-beta").replace("ddpg-api","grt-gamma"),t=t.replace("https://","wss://").replace("http://","wss://"),new K.a(t)})(e),r=new K.z(n.search);for(const[o,s]of t.entries())r.append(o,s);return n.search=r.toString(),n.toString()})(t,T);yield i._establishRetryableConnection(P,O),i.promiseArray.forEach(({res:$})=>{i.logger.debug("Notifying connection successful"),$()}),i.socketStatus=G.READY,i.promiseArray=[]}catch(u){i.logger.debug("Connection exited with",u),i.promiseArray.forEach(({rej:g})=>{g(u)}),i.promiseArray=[],i.awsRealTimeSocket&&i.awsRealTimeSocket.readyState===WebSocket.OPEN&&i.awsRealTimeSocket.close(3001),i.awsRealTimeSocket=void 0,i.socketStatus=G.CLOSED}});return function(l,c){return a.apply(this,arguments)}}())}_establishRetryableConnection(t,n){var r=this;return(0,m.Z)(function*(){r.logger.debug("Establishing retryable connection"),yield f(r._establishConnection.bind(r),[t,n],5e3)})()}_openConnection(t,n){var r=this;return(0,m.Z)(function*(){return new Promise((o,s)=>{const i=r._getNewWebSocket(t,[r.wsProtocolName,n]);i.onerror=()=>{r.logger.debug("WebSocket connection error")},i.onclose=()=>{r._closeSocket(),s(new Error("Connection handshake error"))},i.onopen=()=>{r.awsRealTimeSocket=i,o()}})})()}_getNewWebSocket(t,n){return new WebSocket(t,n)}_initiateHandshake(){var t=this;return(0,m.Z)(function*(){return new Promise((n,r)=>{if(!t.awsRealTimeSocket)return void r(new Error("awsRealTimeSocket undefined"));let o=!1;t.awsRealTimeSocket.onerror=a=>{t.logger.debug(`WebSocket error ${JSON.stringify(a)}`)},t.awsRealTimeSocket.onclose=a=>{t.logger.debug(`WebSocket closed ${a.reason}`),t._closeSocket(),r(new Error(JSON.stringify(a)))},t.awsRealTimeSocket.onmessage=a=>{if("string"!=typeof a.data)return;t.logger.debug(`subscription message from AWS AppSyncRealTime: ${a.data} `);const l=JSON.parse(a.data),{type:c}=l,u=t._extractConnectionTimeout(l);if(c===L.GQL_CONNECTION_ACK)return o=!0,t._registerWebsocketHandlers(u),void n("Connected to AWS AppSyncRealTime");if(c===L.GQL_CONNECTION_ERROR){const{errorType:g,errorCode:y}=t._extractErrorCodeAndType(l);r({errorType:g,errorCode:y})}},t.awsRealTimeSocket.send(JSON.stringify({type:L.GQL_CONNECTION_INIT})),setTimeout(()=>{o||(t.connectionStateMonitor.record(M.CONNECTION_FAILED),r(new Error("Connection timeout: ack from AWSAppSyncRealTime was not received after 15000 ms")))},15e3)})})()}_registerWebsocketHandlers(t){this.awsRealTimeSocket&&(this.keepAliveHeartbeatIntervalId=setInterval(()=>{this.keepAliveHeartbeat(t)},5e3),this.awsRealTimeSocket.onmessage=this._handleIncomingSubscriptionMessage.bind(this),this.awsRealTimeSocket.onerror=n=>{this.logger.debug(n),this._errorDisconnect(p.CONNECTION_CLOSED)},this.awsRealTimeSocket.onclose=n=>{this.logger.debug(`WebSocket closed ${n.reason}`),this._closeSocket(),this._errorDisconnect(p.CONNECTION_CLOSED)})}}const St="AWSAppSyncEventsProvider",tt=new class dn extends Et{constructor(){super({providerName:St,wsProtocolName:"aws-appsync-event-ws",connectUri:""})}getProviderName(){return St}connect(t){var n=()=>super.connect,r=this;return(0,m.Z)(function*(){n().call(r,t)})()}subscribe(t,n){return super.subscribe(t,n).pipe()}publish(t,n){var r=()=>super.publish,o=this;return(0,m.Z)(function*(){r().call(o,t,n)})()}_prepareSubscriptionPayload({options:t,subscriptionId:n,customUserAgentDetails:r,additionalCustomHeaders:o,libraryConfigHeaders:s,publish:i}){return(0,m.Z)(function*(){const{appSyncGraphqlEndpoint:a,authenticationType:l,query:c,apiKey:u,region:g}=t,y=JSON.stringify({channel:c}),C={...yield et({apiKey:u,appSyncGraphqlEndpoint:a,authenticationType:l,payload:y,canonicalUri:"",region:g,additionalCustomHeaders:o}),...s,...o,[k.Mt]:(0,w.Zm)(r)},O={id:n,channel:c,authorization:{...C},type:i?L.EVENT_PUBLISH:L.EVENT_SUBSCRIBE};return JSON.stringify(O)})()}_handleSubscriptionData(t){this.logger.debug(`subscription message from AWS AppSync Events: ${t.data}`);const{id:n="",event:r,type:o}=JSON.parse(String(t.data)),{observer:s=null,query:i="",variables:a={}}=this.subscriptionObserverMap.get(n)||{};if(this.logger.debug({id:n,observer:s,query:i,variables:a}),o===L.DATA&&r){const l=JSON.parse(r);return s?s.next({id:n,type:o,event:l}):this.logger.debug(`observer not found for id: ${n}`),[!0,{id:n,type:o,payload:l}]}return[!1,{id:n,type:o,payload:r}]}_unsubscribeMessage(t){return{id:t,type:L.EVENT_STOP}}_extractConnectionTimeout(t){const{connectionTimeoutMs:n=R}=t;return n}_extractErrorCodeAndType(t){const{errors:[{errorType:n="",errorCode:r=0}={}]=[]}=t;return{errorCode:r,errorType:n}}};var vt=E(91935);class Ke extends vt._{get response(){return this._response?fn(this._response):void 0}constructor(t){super(t),this.constructor=Ke,Object.setPrototypeOf(this,Ke.prototype),t.response&&(this._response=t.response)}}const fn=e=>({...e,headers:{...e.headers}});class Be extends Ke{constructor(t){super(t),this.constructor=Be,Object.setPrototypeOf(this,Be.prototype)}}class We extends Be{constructor(t={}){super({name:"CanceledError",message:"Request is canceled by user",...t}),this.constructor=We,Object.setPrototypeOf(this,We.prototype)}}const pn=e=>!!e&&e instanceof We;var hn=E(54974);const Tt=function(){var e=(0,m.Z)(function*(t){if(!t)return;const n=yield(0,hn.f)(yn(t));if(n){const r=yield t.body?.text();return gn(n,{statusCode:t.statusCode,headers:t.headers,body:r})}});return function(n){return e.apply(this,arguments)}}(),yn=e=>{let t;const n=new Proxy(e.body,{get:(o,s,i)=>"json"===s?(0,m.Z)(function*(){t||(t=o.text());try{return JSON.parse(yield t)}catch{return{}}}):"text"===s?(0,m.Z)(function*(){return t||(t=o.text()),t}):Reflect.get(o,s,i)});return new Proxy(e,{get:(o,s,i)=>"body"===s?n:Reflect.get(o,s,i)})},gn=(e,t)=>{const n=new Be({name:e?.name,message:e.message,underlyingError:e,response:t});return Object.assign(n,{$metadata:e.$metadata})},nt=new U.ConsoleLogger("RestApis"),_n=({headers:e},t)=>!e.authorization&&!e["x-api-key"]&&!!t;var bn=E(67834),An=E(79987),En=E(6639),Sn=E(54473);const Ct="execute-api",vn="us-east-1",Tn=/^.+\.([a-z0-9-]+)\.([a-z0-9-]+)\.amazonaws\.com/,wn=function(){var e=(0,m.Z)(function*(t,n,r,o){const{url:s,method:i,headers:a,body:l,withCredentials:c,abortSignal:u}=n,g=l?l instanceof FormData?l:JSON.stringify(l??""):void 0,y=((e,t)=>{const n={};for(const r in e)n[r.toLowerCase()]=e[r];return t&&(n["content-type"]="application/json; charset=UTF-8",t instanceof FormData&&delete n["content-type"]),n})(a,l),C={url:s,headers:y,method:i,body:g},O={retryDecider:(0,bn.j)(Tt),computeDelay:An.k,withCrossDomainCredentials:c,abortSignal:u},T=r(C,o);let P;const $=yield Nn(t);if(T&&$){const q=((e,t)=>{const{service:n=Ct,region:r=vn}={},{hostname:o}=e,[,s,i]=Tn.exec(o)??[];return s===Ct?{service:s,region:i??r}:"appsync-api"===s?{service:"appsync",region:i??r}:{service:n,region:r}})(s),Q=o?.service??q.service,Y=o?.region??q.region;P=yield(0,En.Z)(C,{...O,credentials:$,region:Y,service:Q})}else P=yield(0,Sn.y)(C,{...O});return{statusCode:P.statusCode,headers:P.headers,body:P.body}});return function(n,r,o,s){return e.apply(this,arguments)}}(),Nn=function(){var e=(0,m.Z)(function*(t){try{const{credentials:n}=yield t.Auth.fetchAuthSession();if(n)return n}catch{nt.debug("No credentials available, the request will be unsigned.")}return null});return function(n){return e.apply(this,arguments)}}(),rt=new WeakMap,Ot=(e,{url:t,options:n,abortController:r})=>{const o=r??new AbortController,i=function mn(e,t){const n=l=>!!t,r=new AbortController,o=r.signal,s=t?.signal;let i;const a=function(){var l=(0,m.Z)(function*(){try{const c=yield n()?e():e(o);if(c.statusCode>=300)throw yield Tt(c);return c}catch(c){const u=s??o,g=i??u.reason;if("AbortError"===c.name||!0===u?.aborted){const y=new We({...g&&{message:g},underlyingError:c,recoverySuggestion:"The API request was explicitly canceled. If this is not intended, validate if you called the `cancel()` function on the API request erroneously."});throw nt.debug(c),y}throw nt.debug(c),c}});return function(){return l.apply(this,arguments)}}();if(n())return a();{const l=c=>{!0!==o.aborted&&(r.abort(c),c&&o.reason!==c&&(i=c))};return{response:a(),cancel:l}}}((0,m.Z)(function*(){return wn(e,{url:t,method:"POST",...n,abortSignal:o.signal},_n,n?.signingServiceInfo)}),o).finally(()=>{rt.delete(i)});return i},Rn=(e,t)=>{const n=rt.get(e);return!!n&&(n.abort(t),t&&n.signal.reason!==t&&(n.signal.reason=t),!0)},Pn=(e,t)=>{rt.set(e,t)},Ve=e=>{const t=e.libraryOptions?.API?.GraphQL?.headers,n=e.libraryOptions?.API?.GraphQL?.withCredentials;return{headers:t,withCredentials:n}};function ot(e){return e.errors&&Array.isArray(e.errors)&&e.errors.forEach(t=>{(function Mn(e){return!!(e?.originalError?.name?.startsWith("UnauthorizedException")||e.message?.startsWith("Connection failed:")&&e.message?.includes("Permission denied"))})(t)&&(t.message="Unauthorized",t.recoverySuggestion="If you're calling an Amplify-generated API, make sure to set the \"authMode\" in generateClient({ authMode: '...' }) to the backend authorization rule's auth provider ('apiKey', 'userPool', 'iam', 'oidc', 'lambda')")}),e}class xe extends vt._{constructor(t){super(t),this.constructor=xe,Object.setPrototypeOf(this,xe.prototype)}}var De=(()=>{return(e=De||(De={})).NO_API_KEY="No api-key configured",e.NO_CURRENT_USER="No current user",e.NO_CREDENTIALS="No credentials",e.NO_FEDERATED_JWT="No federated jwt",e.NO_AUTH_TOKEN="No auth token specified",De;var e})();const wt=Symbol("amplify"),Nt=Symbol("authMode"),Rt=Symbol("authToken"),Pt=Symbol("apiKey"),Mt=Symbol("headers"),xt=Symbol("endpoint");function xn(e){return{amplify:e[wt],apiKey:e[Pt],authMode:e[Nt],authToken:e[Rt],endpoint:e[xt],headers:e[Mt]}}const In={name:"NoApiKey",message:De.NO_API_KEY,recoverySuggestion:'The API request was made with `authMode: "apiKey"` but no API Key was passed into `Amplify.configure()`. Review if your API key is passed into the `Amplify.configure()` function.'},kn={name:"NoCredentials",message:De.NO_CREDENTIALS,recoverySuggestion:'The API request was made with `authMode: "iam"` but no authentication credentials are available.\n\nIf you intended to make a request using an authenticated role, review if your user is signed in before making the request.\n\nIf you intend to make a request using an unauthenticated role or also known as "guest access", verify if "Auth.Cognito.allowGuestAccess" is set to "true" in the `Amplify.configure()` function.'},Dn={name:"NoValidAuthTokens",message:De.NO_FEDERATED_JWT,recoverySuggestion:"If you intended to make an authenticated API request, review if the current user is signed in."},Ln={name:"NoSignedUser",message:De.NO_CURRENT_USER,recoverySuggestion:"Review the underlying exception field for more details. If you intended to make an authenticated API request, review if the current user is signed in."},Un={name:"NoAuthorizationHeader",message:De.NO_AUTH_TOKEN,recoverySuggestion:'The API request was made with `authMode: "lambda"` but no `authToken` is set. Review if a valid authToken is passed into the request options or in the `Amplify.configure()` function.'},jn={name:"NoEndpoint",message:"No GraphQL endpoint configured in `Amplify.configure()`.",recoverySuggestion:"Review if the GraphQL API endpoint is set in the `Amplify.configure()` function."};function It(e,t,n){return st.apply(this,arguments)}function st(){return(st=(0,m.Z)(function*(e,t,n,r={}){let o={};switch(t){case"apiKey":if(!n)throw new xe(In);o={"X-Api-Key":n};break;case"iam":if(void 0===(yield e.Auth.fetchAuthSession()).credentials)throw new xe(kn);break;case"oidc":case"userPool":{let s;try{s=(yield e.Auth.fetchAuthSession()).tokens?.accessToken.toString()}catch(i){throw new xe({...Ln,underlyingError:i})}if(!s)throw new xe(Dn);o={Authorization:s};break}case"lambda":if("object"==typeof r&&!r.Authorization)throw new xe(Un);o={Authorization:r.Authorization}}return o})).apply(this,arguments)}function kt(e){if(!e)return!1;const t=e;return Array.isArray(t.errors)&&t.errors.length>0}const Fn="x-amz-user-agent";function it(){return it=(0,m.Z)(function*(e,t,n={},r,o){const{region:s,appSyncGraphqlEndpoint:i,authenticationType:a,query:l,variables:c}=t;if(!i)throw new Error("No endpoint");const{withCredentials:u}=Ve(e),g=yield function $n(e,t,n,r){return at.apply(this,arguments)}(e,t,n,o),y={channel:l,events:c},C=["apiKey","none"].includes(a)?void 0:{service:"appsync",region:s},{body:O}=yield Ot(e,{url:new K.a(i),options:{headers:g,body:y,signingServiceInfo:C,withCredentials:u},abortController:r}),T=yield O.json();if(kt(T))throw ot(T);return T}),it.apply(this,arguments)}function at(){return(at=(0,m.Z)(function*(e,t,n,r){const{apiKey:o,appSyncGraphqlEndpoint:s,authenticationType:i,query:a,variables:l,authToken:c}=t,{headers:u}=Ve(e);let g;if("function"==typeof n){const T={method:"POST",url:new K.a(s).toString(),queryString:a};g=yield n(T)}else g=n;return c&&(g={...g,Authorization:c}),{...yield It(e,i,o,g),...u&&(yield u({query:a,variables:l})),...g,[Fn]:(0,w.Zm)(r)}})).apply(this,arguments)}const Ze=(e,t)=>e?"identityPool"===e?"iam":e:t,Dt=()=>{const t=U.Amplify.getConfig().API?.Events;if(!t)throw new Error("Amplify configuration is missing. Have you called Amplify.configure()?");const n=Ze(t.defaultAuthMode,"apiKey");return{appSyncGraphqlEndpoint:t.endpoint,region:t.region,authenticationType:n,apiKey:t.apiKey}},Hn=e=>{if(Array.isArray(e))return e.map((n,r)=>{const o=JSON.stringify(n);if(void 0===o)throw new Error(`Event must be a valid JSON value. Received ${n} at index ${r}`);return o});const t=JSON.stringify(e);if(void 0===t)throw new Error(`Event must be a valid JSON value. Received ${e}`);return[t]};function Qn(e,t){return ct.apply(this,arguments)}function ct(){return(ct=(0,m.Z)(function*(e,t){const n=Dt();let r;return n.authenticationType=Ze(t?.authMode,n.authenticationType),yield tt.connect(n),{subscribe:(i,a)=>{const l={...n,query:e};return l.authenticationType=Ze(a?.authMode,l.authenticationType),r=tt.subscribe(l).subscribe(i),r},close:()=>{r&&r.unsubscribe()}}})).apply(this,arguments)}function Bn(e,t,n){return ut.apply(this,arguments)}function ut(){return ut=(0,m.Z)(function*(e,t,n){const r=Dt();r.authenticationType=Ze(n?.authMode,r.authenticationType);const o="/"===e[0]?e:`/${e}`,s={...r,query:o,variables:Hn(t),authToken:n?.authToken},i=new AbortController,a=yield function Gn(e,t){return it.apply(this,arguments)}(U.Amplify,s,{},i);if(a.failed?.length>0)return a.failed}),ut.apply(this,arguments)}function Wn(){return lt.apply(this,arguments)}function lt(){return(lt=(0,m.Z)(function*(){yield tt.close()})).apply(this,arguments)}var Lt=E(81220);Symbol();const ht=Symbol("INTERNAL_USER_AGENT_OVERRIDE"),He={},ze=new Array(64);for(let e=0,t="A".charCodeAt(0),n="Z".charCodeAt(0);e+t<=n;e++){const r=String.fromCharCode(e+t);He[r]=e,ze[e]=r}for(let e=0,t="a".charCodeAt(0),n="z".charCodeAt(0);e+t<=n;e++){const r=String.fromCharCode(e+t),o=e+26;He[r]=o,ze[o]=r}for(let e=0;e<10;e++){He[e.toString(10)]=e+52;const t=e.toString(10),n=e+52;He[t]=n,ze[n]=t}He["+"]=62,ze[62]="+",He["/"]=63,ze[63]="/";const Jt="AWSAppSyncRealTimeProvider";class yr extends Et{constructor(){super({providerName:Jt,wsProtocolName:"graphql-ws",connectUri:"/connect"})}getProviderName(){return Jt}subscribe(t,n){return super.subscribe(t,n)}_prepareSubscriptionPayload({options:t,subscriptionId:n,customUserAgentDetails:r,additionalCustomHeaders:o,libraryConfigHeaders:s}){return(0,m.Z)(function*(){const{appSyncGraphqlEndpoint:i,authenticationType:a,query:l,variables:c,apiKey:u,region:g}=t,C=JSON.stringify({query:l,variables:c}),O={...yield et({apiKey:u,appSyncGraphqlEndpoint:i,authenticationType:a,payload:C,canonicalUri:"",region:g,additionalCustomHeaders:o}),...s,...o,[k.Mt]:(0,w.Zm)(r)},T={id:n,payload:{data:C,extensions:{authorization:{...O}}},type:L.GQL_START};return JSON.stringify(T)})()}_handleSubscriptionData(t){this.logger.debug(`subscription message from AWS AppSync RealTime: ${t.data}`);const{id:n="",payload:r,type:o}=JSON.parse(String(t.data)),{observer:s=null,query:i="",variables:a={}}=this.subscriptionObserverMap.get(n)||{};return this.logger.debug({id:n,observer:s,query:i,variables:a}),o===L.DATA&&r&&r.data?(s?s.next(r):this.logger.debug(`observer not found for id: ${n}`),[!0,{id:n,type:o,payload:r}]):[!1,{id:n,type:o,payload:r}]}_unsubscribeMessage(t){return{id:t,type:L.GQL_STOP}}_extractConnectionTimeout(t){const{payload:{connectionTimeoutMs:n=R}={}}=t;return n}_extractErrorCodeAndType(t){const{payload:{errors:[{errorType:n="",errorCode:r=0}={}]=[]}={}}=t;return{errorCode:r,errorType:n}}}var je=(()=>{return(e=je||(je={})).NoAuthSession="NoAuthSession",e.NoRegion="NoRegion",e.NoCustomEndpoint="NoCustomEndpoint",je;var e})();const gr={[je.NoAuthSession]:{message:"Auth session should not be empty."},[je.NoRegion]:{message:"Missing region."},[je.NoCustomEndpoint]:{message:"Custom endpoint region is present without custom endpoint."}},_r=new U.ConsoleLogger("GraphQLAPI resolveConfig"),Yt=e=>{const t=e.getConfig();t.API?.GraphQL||_r.warn("The API configuration is missing. This is likely due to Amplify.configure() not being called prior to generateClient().");const{apiKey:n,customEndpoint:r,customEndpointRegion:o,defaultAuthMode:s,endpoint:i,region:a}=t.API?.GraphQL??{};return function mr(e,t){const{message:n,recoverySuggestion:r}=gr[t];if(!e)throw new xe({name:t,message:n,recoverySuggestion:r})}(!(!r&&o),je.NoCustomEndpoint),{apiKey:n,customEndpoint:r,customEndpointRegion:o,defaultAuthMode:s,endpoint:i,region:a}},Xt=e=>({data:{},errors:[new ee.GraphQLError(e.message,null,null,null,null,e)]});class en{constructor(){this.appSyncRealTime=new Map,this._api={post:Ot,cancelREST:Rn,isCancelErrorREST:pn,updateRequestToBeCancellable:Pn}}getModuleName(){return"InternalGraphQLAPI"}getGraphqlOperationType(t){const r=(0,ee.parse)(t).definitions,[{operation:o}]=r;return o}graphql(t,{query:n,variables:r={},authMode:o,authToken:s,endpoint:i,apiKey:a},l,c){var u=this;const g=(0,ee.parse)("string"==typeof n?n:(0,ee.print)(n)),[y={}]=g.definitions.filter(T=>"OperationDefinition"===T.kind),{operation:C}=y,O=l||{};switch(C){case"query":case"mutation":{const T=new AbortController;let P;return P="function"!=typeof t?this._graphql(t,{query:g,variables:r,authMode:o,apiKey:a,endpoint:i},O,T,c,s):t(function(){var q=(0,m.Z)(function*(Q){return yield u._graphql(Q,{query:g,variables:r,authMode:o,apiKey:a,endpoint:i},O,T,c,s)});return function(Y){return q.apply(this,arguments)}}()),this._api.updateRequestToBeCancellable(P,T),P}case"subscription":return this._graphqlSubscribe(t,{query:g,variables:r,authMode:o,apiKey:a,endpoint:i},O,c,s);default:throw new Error(`invalid operation type: ${C}`)}}_graphql(t,{query:n,variables:r,authMode:o,endpoint:s,apiKey:i},a={},l,c,u){var g=this;return(0,m.Z)(function*(){const{apiKey:y,region:C,endpoint:O,customEndpoint:T,customEndpointRegion:P,defaultAuthMode:$}=Yt(t),q=o||$||"iam",Q="identityPool"===q?"iam":q,{headers:Y,withCredentials:J}=Ve(t);let X;if("function"==typeof a){const ne={method:"POST",url:new K.a(s||T||O||"").toString(),queryString:(0,ee.print)(n)};X=yield a(ne)}else X=a;u&&(X={...X,Authorization:u});const oe=yield It(t,Q,i??y,X),ge={...!T&&oe,...T&&(P?oe:{})||{},...Y&&(yield Y({query:(0,ee.print)(n),variables:r})),...X,...!T&&{"x-amz-user-agent":(0,w.Zm)(c)}},Ae={query:(0,ee.print)(n),variables:r||null};let Te;Te=T&&!P||"oidc"!==Q&&"userPool"!==Q&&"iam"!==Q&&"lambda"!==Q?void 0:{service:P?"execute-api":"appsync",region:P||C};const le=s||T||O;if(!le)throw Xt(new xe(jn));let V;try{const{body:ne}=yield g._api.post(t,{url:new K.a(le),options:{headers:ge,body:Ae,signingServiceInfo:Te,withCredentials:J},abortController:l});V=yield ne.json()}catch(ne){if(g.isCancelError(ne))throw ne;V=Xt(ne)}if(kt(V))throw ot(V);return V})()}isCancelError(t){return this._api.isCancelErrorREST(t)}cancel(t,n){return this._api.cancelREST(t,n)}_graphqlSubscribe(t,{query:n,variables:r,authMode:o,apiKey:s,endpoint:i},a={},l,c){const u=Yt(t),g=o||u?.defaultAuthMode||"iam",y="identityPool"===g?"iam":g,{headers:C}=Ve(t),O=i??u?.endpoint,T=O??"none",P=this.appSyncRealTime.get(T)??new yr;return this.appSyncRealTime.set(T,P),P.subscribe({query:(0,ee.print)(n),variables:r,appSyncGraphqlEndpoint:O,region:u?.region,authenticationType:y,apiKey:s??u?.apiKey,additionalHeaders:a,authToken:c,libraryConfigHeaders:C},l).pipe((0,ie.catchError)($=>{throw $.errors?ot($):$}))}}new en;const Sr=(e,t={},n)=>({query:e,variables:t,authToken:n});class tn extends en{getModuleName(){return"GraphQLAPI"}graphql(t,n,r){const o={category:Lt.WD.API,action:Lt.gq.GraphQl};if(function Er(e){return ht in e}(n)){const{[ht]:s,...i}=n;return super.graphql(t,i,r,{...o,...s})}return super.graphql(t,n,r,{...o})}isCancelError(t){return super.isCancelError(t)}cancel(t,n){return super.cancel(t,n)}}const vr=new tn},23192:(ce,H,E)=>{E.d(H,{SQ:()=>k,Xb:()=>S});var v=E(55502),m=E(92261),U=E(91935);const k=typeof Symbol<"u"?Symbol("amplify_default"):"@@amplify_default",w=new v.k("Hub");class I{constructor(L){this.listeners=new Map,this.protectedChannels=["core","auth","api","analytics","interactions","pubsub","storage","ui","xr"],this.name=L}_remove(L,D){const G=this.listeners.get(L);G?this.listeners.set(L,[...G.filter(({callback:j})=>j!==D)]):w.warn(`No listeners for ${L}`)}dispatch(L,D,G,j){"string"==typeof L&&this.protectedChannels.indexOf(L)>-1&&(j===k||w.warn(`WARNING: ${L} is protected and dispatching on it can have unintended consequences`));const N={channel:L,payload:{...D},source:G,patternInfo:[]};try{this._toListeners(N)}catch(b){w.error(b)}}listen(L,D,G="noname"){let j;if("function"!=typeof D)throw new U._({name:m.z2,message:"No callback supplied to Hub"});j=D;let N=this.listeners.get(L);return N||(N=[],this.listeners.set(L,N)),N.push({name:G,callback:j}),()=>{this._remove(L,j)}}_toListeners(L){const{channel:D,payload:G}=L,j=this.listeners.get(D);j&&j.forEach(N=>{w.debug(`Dispatching to ${D} with `,G);try{N.callback(L)}catch(b){w.error(b)}})}}const S=new I("__default__");new I("internal-hub")},55502:(ce,H,E)=>{E.d(H,{k:()=>k});var v=E(92261),m=(()=>{return(w=m||(m={})).DEBUG="DEBUG",w.ERROR="ERROR",w.INFO="INFO",w.WARN="WARN",w.VERBOSE="VERBOSE",w.NONE="NONE",m;var w})();const U={VERBOSE:1,DEBUG:2,INFO:3,WARN:4,ERROR:5,NONE:6};let k=(()=>{class w{constructor(S,F=m.WARN){this.name=S,this.level=F,this._pluggables=[]}_padding(S){return S<10?"0"+S:""+S}_ts(){const S=new Date;return[this._padding(S.getMinutes()),this._padding(S.getSeconds())].join(":")+"."+S.getMilliseconds()}configure(S){return S?(this._config=S,this._config):this._config}_log(S,...F){let W=this.level;if(w.LOG_LEVEL&&(W=w.LOG_LEVEL),typeof window<"u"&&window.LOG_LEVEL&&(W=window.LOG_LEVEL),!(U[S]>=U[W]))return;let G=console.log.bind(console);S===m.ERROR&&console.error&&(G=console.error.bind(console)),S===m.WARN&&console.warn&&(G=console.warn.bind(console)),w.BIND_ALL_LOG_LEVELS&&(S===m.INFO&&console.info&&(G=console.info.bind(console)),S===m.DEBUG&&console.debug&&(G=console.debug.bind(console)));const j=`[${S}] ${this._ts()} ${this.name}`;let N="";if(1===F.length&&"string"==typeof F[0])N=`${j} - ${F[0]}`,G(N);else if(1===F.length)N=`${j} ${F[0]}`,G(j,F[0]);else if("string"==typeof F[0]){let b=F.slice(1);1===b.length&&(b=b[0]),N=`${j} - ${F[0]} ${b}`,G(`${j} - ${F[0]}`,b)}else N=`${j} ${F}`,G(j,F);for(const b of this._pluggables){const R={message:N,timestamp:Date.now()};b.pushLogs([R])}}log(...S){this._log(m.INFO,...S)}info(...S){this._log(m.INFO,...S)}warn(...S){this._log(m.WARN,...S)}error(...S){this._log(m.ERROR,...S)}debug(...S){this._log(m.DEBUG,...S)}verbose(...S){this._log(m.VERBOSE,...S)}addPluggable(S){S&&S.getCategoryName()===v.YG&&(this._pluggables.push(S),S.configure(this._config))}listPluggables(){return this._pluggables}}return w.LOG_LEVEL=null,w.BIND_ALL_LOG_LEVELS=!1,w})()},91396:(ce,H,E)=>{E.d(H,{Cj:()=>Pe,QW:()=>Ue});var v=E(81220);const m=()=>typeof global<"u",k=()=>typeof window<"u",w=()=>typeof document<"u",I=()=>typeof process<"u",S=(pe,f)=>!!Object.keys(pe).find(p=>p.startsWith(f)),Se=[{platform:v.gQ.Expo,detectionMethod:function ie(){return m()&&typeof global.expo<"u"}},{platform:v.gQ.ReactNative,detectionMethod:function de(){return typeof navigator<"u"&&typeof navigator.product<"u"&&"ReactNative"===navigator.product}},{platform:v.gQ.NextJs,detectionMethod:function N(){return k()&&window.next&&"object"==typeof window.next}},{platform:v.gQ.Nuxt,detectionMethod:function R(){return k()&&(void 0!==window.__NUXT__||void 0!==window.$nuxt)}},{platform:v.gQ.Angular,detectionMethod:function z(){const pe=Boolean(w()&&document.querySelector("[ng-version]")),f=Boolean(k()&&typeof window.ng<"u");return pe||f}},{platform:v.gQ.React,detectionMethod:function F(){const pe=h=>h.startsWith("_react")||h.startsWith("__react");return w()&&Array.from(document.querySelectorAll("[id]")).some(h=>Object.keys(h).find(pe))}},{platform:v.gQ.VueJs,detectionMethod:function L(){return k()&&S(window,"__VUE")}},{platform:v.gQ.Svelte,detectionMethod:function G(){return k()&&S(window,"__SVELTE")}},{platform:v.gQ.WebUnknown,detectionMethod:function ee(){return k()}},{platform:v.gQ.NextJsSSR,detectionMethod:function b(){return m()&&(S(global,"__next")||S(global,"__NEXT"))}},{platform:v.gQ.NuxtSSR,detectionMethod:function x(){return m()&&typeof global.__NUXT_PATHS__<"u"}},{platform:v.gQ.ReactSSR,detectionMethod:function W(){return I()&&typeof process.env<"u"&&!!Object.keys(process.env).find(pe=>pe.includes("react"))}},{platform:v.gQ.VueJsSSR,detectionMethod:function D(){return m()&&S(global,"__VUE")}},{platform:v.gQ.AngularSSR,detectionMethod:function se(){return I()&&"object"==typeof process.env&&process.env.npm_lifecycle_script?.startsWith("ng ")||!1}},{platform:v.gQ.SvelteSSR,detectionMethod:function j(){return I()&&typeof process.env<"u"&&!!Object.keys(process.env).find(pe=>pe.includes("svelte"))}}];let he;const ae=[];let fe=!1;const Re=1e3,Pe=()=>{if(!he){if(he=function _e(){return Se.find(pe=>pe.detectionMethod())?.platform||v.gQ.ServerSideUnknown}(),fe)for(;ae.length;)ae.pop()?.();else ae.forEach(pe=>{pe()});Le(v.gQ.ServerSideUnknown,10),Le(v.gQ.WebUnknown,10)}return he},Ue=pe=>{fe||ae.push(pe)};function Le(pe,f){he===pe&&!fe&&setTimeout(()=>{(function Me(){he=void 0})(),fe=!0,setTimeout(Pe,Re)},f)}},5919:(ce,H,E)=>{E.d(H,{Zm:()=>G});var v=E(81220);const m="6.13.1";var U=E(91396);const k={},S="aws-amplify",F=j=>j.replace(/\+.*/,"");new class W{constructor(){this.userAgent=`${S}/${F(m)}`}get framework(){return(0,U.Cj)()}get isReactNative(){return this.framework===v.gQ.ReactNative||this.framework===v.gQ.Expo}observeFrameworkChanges(N){(0,U.QW)(N)}};const G=j=>(({category:j,action:N}={})=>{const b=[[S,F(m)]];if(j&&b.push([j,N]),b.push(["framework",(0,U.Cj)()]),j&&N){const R=((j,N)=>k[j]?.[N]?.additionalDetails)(j,N);R&&R.forEach(x=>{b.push(x)})}return b})(j).map(([R,x])=>R&&x?`${R}/${x}`:R).join(" ")},81220:(ce,H,E)=>{E.d(H,{WD:()=>m,gQ:()=>v,gq:()=>w});var v=(()=>{return(b=v||(v={})).WebUnknown="0",b.React="1",b.NextJs="2",b.Angular="3",b.VueJs="4",b.Nuxt="5",b.Svelte="6",b.ServerSideUnknown="100",b.ReactSSR="101",b.NextJsSSR="102",b.AngularSSR="103",b.VueJsSSR="104",b.NuxtSSR="105",b.SvelteSSR="106",b.ReactNative="201",b.Expo="202",v;var b})(),m=(()=>{return(b=m||(m={})).AI="ai",b.API="api",b.Auth="auth",b.Analytics="analytics",b.DataStore="datastore",b.Geo="geo",b.InAppMessaging="inappmessaging",b.Interactions="interactions",b.Predictions="predictions",b.PubSub="pubsub",b.PushNotification="pushnotification",b.Storage="storage",m;var b})(),w=(()=>{return(b=w||(w={})).GraphQl="1",b.Get="2",b.Post="3",b.Put="4",b.Patch="5",b.Del="6",b.Head="7",w;var b})()},6639:(ce,H,E)=>{E.d(H,{Z:()=>b});var v=E(3454),m=E(15861),U=E(74109);E(86437),E(95472);const I=R=>new Date(Date.now()+R);var G=E(46570),j=E(4079),N=E(6990);const b=(0,j.V)(N.S,[G.n,v.d,({credentials:R,region:x,service:z,uriEscapePath:se=!0})=>{let de;return(ie,ee)=>function(){var Se=(0,m.Z)(function*(he){de=de??0;const ae={credentials:"function"==typeof R?yield R({forceRefresh:!!ee?.isCredentialsExpired}):R,signingDate:I(de),signingRegion:x,signingService:z,uriEscapePath:se},fe=yield(0,U.C)(he,ae),Ce=yield ie(fe),Oe=(({headers:R}={})=>R?.date??R?.Date??R?.["x-amz-date"])(Ce);return Oe&&(de=((R,x)=>((R,x)=>Math.abs(I(x).getTime()-R)>=3e5)(R,x)?R-Date.now():x)(Date.parse(Oe),de)),Ce});return function _e(he){return Se.apply(this,arguments)}}()}])},6990:(ce,H,E)=>{E.d(H,{S:()=>I});var v=E(15861),m=E(91935),U=E(87199);const k=S=>{let F;return()=>(F||(F=S()),F)},w=S=>!["HEAD","GET","DELETE"].includes(S.toUpperCase()),I=function(){var S=(0,v.Z)(function*({url:F,method:W,headers:L,body:D},{abortSignal:G,cache:j,withCrossDomainCredentials:N}){let b;try{b=yield fetch(F,{method:W,headers:L,body:w(W)?D:void 0,signal:G,cache:j,credentials:N?"include":"same-origin"})}catch(se){throw se instanceof TypeError?new m._({name:U.Z.NetworkError,message:"A network error has occurred.",underlyingError:se}):se}const R={};return b.headers?.forEach((se,de)=>{R[de.toLowerCase()]=se}),{statusCode:b.status,headers:R,body:null,body:Object.assign(b.body??{},{text:k(()=>b.text()),blob:k(()=>b.blob()),json:k(()=>b.json())})}});return function(W,L){return S.apply(this,arguments)}}()},54473:(ce,H,E)=>{E.d(H,{y:()=>w});var v=E(3454),m=E(46570),U=E(4079),k=E(6990);const w=(0,U.V)(k.S,[m.n,v.d])},4079:(ce,H,E)=>{E.d(H,{V:()=>v});const v=(m,U)=>(k,w)=>{const I={};let S=F=>m(F,w);for(let F=U.length-1;F>=0;F--)S=(0,U[F])(w)(S,I);return S(k)}},67834:(ce,H,E)=>{E.d(H,{j:()=>w});var v=E(15861),m=E(87199);const U=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch","BadRequestException"],k=D=>!!D&&U.includes(D),w=D=>function(){var G=(0,v.Z)(function*(j,N){const b=N??(yield D(j))??void 0,R=b?.code||b?.name,x=j?.statusCode;return{retryable:W(N)||F(x,R)||k(R)||L(x,R)}});return function(j,N){return G.apply(this,arguments)}}(),I=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException"],S=["TimeoutError","RequestTimeout","RequestTimeoutException"],F=(D,G)=>429===D||!!G&&I.includes(G),W=D=>[m.Z.NetworkError,"ERR_NETWORK"].includes(D?.name),L=(D,G)=>!!D&&[500,502,503,504].includes(D)||!!G&&S.includes(G)},79987:(ce,H,E)=>{E.d(H,{k:()=>U});var v=E(75599);const m=3e5,U=k=>{const I=(0,v.k)(m)(k);return!1===I?m:I}},3454:(ce,H,E)=>{E.d(H,{d:()=>U});var v=E(15861);const U=({maxAttempts:I=3,retryDecider:S,computeDelay:F,abortSignal:W})=>{if(I<1)throw new Error("maxAttempts must be greater than 0");return(L,D)=>function(){var G=(0,v.Z)(function*(N){let b,x,R=D.attemptsCount??0;const z=()=>{if(x)return w(x,R),x;throw w(b,R),b};for(;!W?.aborted&&R<I;){try{x=yield L(N),b=void 0}catch(ie){b=ie,x=void 0}R=(D.attemptsCount??0)>R?D.attemptsCount??0:R+1,D.attemptsCount=R;const{isCredentialsExpiredError:se,retryable:de}=yield S(x,b,D);if(!de)return z();if(D.isCredentialsExpired=!!se,!W?.aborted&&R<I){const ie=F(R);yield k(ie,W)}}if(W?.aborted)throw new Error("Request aborted.");return z()});return function j(N){return G.apply(this,arguments)}}()},k=(I,S)=>{if(S?.aborted)return Promise.resolve();let F,W;const L=new Promise(D=>{W=D,F=setTimeout(D,I)});return S?.addEventListener("abort",function D(G){clearTimeout(F),S?.removeEventListener("abort",D),W()}),L},w=(I,S)=>{"[object Object]"===Object.prototype.toString.call(I)&&(I.$metadata={...I.$metadata??{},attempts:S})}},74109:(ce,H,E)=>{E.d(H,{C:()=>h});const v=d=>Object.keys(d).map(_=>_.toLowerCase()).sort().join(";"),G="X-Amz-Date".toLowerCase(),j="X-Amz-Security-Token".toLowerCase(),N="aws4_request",b="AWS4-HMAC-SHA256";var ee=E(86437),Se=E(95472);const _e=(d,_)=>{const A=new ee.f(d??void 0);return A.update(_),A.digestSync()},he=(d,_)=>{const A=_e(d,_);return(0,Se.N)(A)},ae=d=>Object.entries(d).map(([_,A])=>({key:_.toLowerCase(),value:A?.trim().replace(/\s+/g," ")??""})).sort((_,A)=>_.key<A.key?-1:1).map(_=>`${_.key}:${_.value}\n`).join(""),fe=d=>Array.from(d).sort(([_,A],[M,Z])=>_===M?A<Z?-1:1:_<M?-1:1).map(([_,A])=>`${Ce(_)}=${Ce(A)}`).join("&"),Ce=d=>encodeURIComponent(d).replace(/[!'()*]/g,Oe),Oe=d=>`%${d.charCodeAt(0).toString(16).toUpperCase()}`,Re=(d,_=!0)=>d?_?encodeURIComponent(d).replace(/%2F/g,"/"):d:"/",Pe=d=>null==d?"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855":Ue(d)?he(null,d):"UNSIGNED-PAYLOAD",Ue=d=>"string"==typeof d||ArrayBuffer.isView(d)||Me(d),Me=d=>"function"==typeof ArrayBuffer&&d instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(d),h=(d,_)=>{const A=(({credentials:d,signingDate:_=new Date,signingRegion:A,signingService:M,uriEscapePath:Z=!0})=>{const{accessKeyId:re,secretAccessKey:B,sessionToken:K}=d,{longDate:ue,shortDate:ve}=(d=>{const _=d.toISOString().replace(/[:-]|\.\d{3}/g,"");return{longDate:_,shortDate:_.slice(0,8)}})(_),te=((d,_,A)=>`${d}/${_}/${A}/${N}`)(ve,A,M);return{accessKeyId:re,credentialScope:te,longDate:ue,secretAccessKey:B,sessionToken:K,shortDate:ve,signingRegion:A,signingService:M,uriEscapePath:Z}})(_),{accessKeyId:M,credentialScope:Z,longDate:re,sessionToken:B}=A,K={...d.headers};K.host=d.url.host,K[G]=re,B&&(K[j]=B);const ue={...d,headers:K},ve=((d,{credentialScope:_,longDate:A,secretAccessKey:M,shortDate:Z,signingRegion:re,signingService:B,uriEscapePath:K})=>{const ue=(({body:d,headers:_,method:A,url:M},Z=!0)=>[A,Re(M.pathname,Z),fe(M.searchParams),ae(_),v(_),Pe(d)].join("\n"))(d,K),te=((d,_,A)=>[b,d,_,A].join("\n"))(A,_,he(null,ue));return he(((d,_,A,M)=>{const re=_e(`AWS4${d}`,_),B=_e(re,A),K=_e(B,M);return _e(K,N)})(M,Z,re,B),te)})(ue,A),te=`Credential=${M}/${Z}`,be=`SignedHeaders=${v(K)}`;return K.authorization=`${b} ${te}, ${be}, Signature=${ve}`,ue}},46570:(ce,H,E)=>{E.d(H,{n:()=>m});var v=E(15861);const m=({userAgentHeader:U="x-amz-user-agent",userAgentValue:k=""})=>w=>function(){var I=(0,v.Z)(function*(F){if(0===k.trim().length)return yield w(F);{const W=U.toLowerCase();return F.headers[W]=F.headers[W]?`${F.headers[W]} ${k}`:k,yield w(F)}});return function S(F){return I.apply(this,arguments)}}()},54974:(ce,H,E)=>{E.d(H,{e:()=>k,f:()=>U});var v=E(15861),m=E(97282);const U=function(){var w=(0,v.Z)(function*(I){if(!I||I.statusCode<300)return;const S=yield k(I),W=(G=>{const[j]=G.toString().split(/[,:]+/);return j.includes("#")?j.split("#")[1]:j})(I.headers["x-amzn-errortype"]??S.code??S.__type??"UnknownError"),D=new Error(S.message??S.Message??"Unknown error");return Object.assign(D,{name:W,$metadata:(0,m.B)(I)})});return function(S){return w.apply(this,arguments)}}(),k=function(){var w=(0,v.Z)(function*(I){if(!I.body)throw new Error("Missing response payload");const S=yield I.body.json();return Object.assign(S,{$metadata:(0,m.B)(I)})});return function(S){return w.apply(this,arguments)}}()},97282:(ce,H,E)=>{E.d(H,{B:()=>v});const v=U=>{const{headers:k,statusCode:w}=U;return{...m(U)?U.$metadata:{},httpStatusCode:w,requestId:k["x-amzn-requestid"]??k["x-amzn-request-id"]??k["x-amz-request-id"],extendedRequestId:k["x-amz-id-2"],cfId:k["x-amz-cf-id"]}},m=U=>"object"==typeof U?.$metadata},92261:(ce,H,E)=>{E.d(H,{Mt:()=>m,YG:()=>v,z2:()=>U});const v="Logging",m="x-amz-user-agent",U="NoHubcallbackProvidedException"},91935:(ce,H,E)=>{E.d(H,{_:()=>v});class v extends Error{constructor({message:U,name:k,recoverySuggestion:w,underlyingError:I}){super(U),this.name=k,this.underlyingError=I,this.recoverySuggestion=w,this.constructor=v,Object.setPrototypeOf(this,v.prototype)}}},87199:(ce,H,E)=>{E.d(H,{Z:()=>v});var v=(()=>{return(m=v||(v={})).NoEndpointId="NoEndpointId",m.PlatformNotSupported="PlatformNotSupported",m.Unknown="Unknown",m.NetworkError="NetworkError",v;var m})()},99120:(ce,H,E)=>{E.d(H,{a:()=>v,z:()=>m});const v=URL,m=URLSearchParams},38261:(ce,H,E)=>{E.d(H,{r:()=>m});const m=E(83364).v4},10180:(ce,H,E)=>{E.d(H,{Ds:()=>U,tl:()=>k});var v=E(91935);const U=()=>{if(typeof window<"u"&&"function"==typeof window.btoa)return window.btoa;if("function"==typeof btoa)return btoa;throw new v._({name:"Base64EncoderError",message:"Cannot resolve the `btoa` function from the environment."})},k=()=>{if(typeof window<"u"&&"function"==typeof window.atob)return window.atob;if("function"==typeof atob)return atob;throw new v._({name:"Base64EncoderError",message:"Cannot resolve the `atob` function from the environment."})}},90206:(ce,H,E)=>{E.d(H,{t:()=>v});const v=3e5},75599:(ce,H,E)=>{E.d(H,{k:()=>m});var v=E(90206);function m(U=v.t){return I=>{const S=2**I*100+100*Math.random();return!(S>U)&&S}}},15861:(ce,H,E)=>{function v(U,k,w,I,S,F,W){try{var L=U[F](W),D=L.value}catch(G){return void w(G)}L.done?k(D):Promise.resolve(D).then(I,S)}function m(U){return function(){var k=this,w=arguments;return new Promise(function(I,S){var F=U.apply(k,w);function W(D){v(F,I,S,W,L,"next",D)}function L(D){v(F,I,S,W,L,"throw",D)}W(void 0)})}}E.d(H,{Z:()=>m})},97582:(ce,H,E)=>{E.d(H,{FC:()=>ee,Jh:()=>j,KL:()=>_e,ZT:()=>m,_T:()=>k,cy:()=>he,ev:()=>de,gn:()=>w,mG:()=>G,pi:()=>U,pr:()=>se,qq:()=>ie});var v=function(f,p){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,d){h.__proto__=d}||function(h,d){for(var _ in d)Object.prototype.hasOwnProperty.call(d,_)&&(h[_]=d[_])})(f,p)};function m(f,p){if("function"!=typeof p&&null!==p)throw new TypeError("Class extends value "+String(p)+" is not a constructor or null");function h(){this.constructor=f}v(f,p),f.prototype=null===p?Object.create(p):(h.prototype=p.prototype,new h)}var U=function(){return U=Object.assign||function(p){for(var h,d=1,_=arguments.length;d<_;d++)for(var A in h=arguments[d])Object.prototype.hasOwnProperty.call(h,A)&&(p[A]=h[A]);return p},U.apply(this,arguments)};function k(f,p){var h={};for(var d in f)Object.prototype.hasOwnProperty.call(f,d)&&p.indexOf(d)<0&&(h[d]=f[d]);if(null!=f&&"function"==typeof Object.getOwnPropertySymbols){var _=0;for(d=Object.getOwnPropertySymbols(f);_<d.length;_++)p.indexOf(d[_])<0&&Object.prototype.propertyIsEnumerable.call(f,d[_])&&(h[d[_]]=f[d[_]])}return h}function w(f,p,h,d){var M,_=arguments.length,A=_<3?p:null===d?d=Object.getOwnPropertyDescriptor(p,h):d;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)A=Reflect.decorate(f,p,h,d);else for(var Z=f.length-1;Z>=0;Z--)(M=f[Z])&&(A=(_<3?M(A):_>3?M(p,h,A):M(p,h))||A);return _>3&&A&&Object.defineProperty(p,h,A),A}function G(f,p,h,d){return new(h||(h=Promise))(function(A,M){function Z(K){try{B(d.next(K))}catch(ue){M(ue)}}function re(K){try{B(d.throw(K))}catch(ue){M(ue)}}function B(K){K.done?A(K.value):function _(A){return A instanceof h?A:new h(function(M){M(A)})}(K.value).then(Z,re)}B((d=d.apply(f,p||[])).next())})}function j(f,p){var d,_,A,h={label:0,sent:function(){if(1&A[0])throw A[1];return A[1]},trys:[],ops:[]},M=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return M.next=Z(0),M.throw=Z(1),M.return=Z(2),"function"==typeof Symbol&&(M[Symbol.iterator]=function(){return this}),M;function Z(B){return function(K){return function re(B){if(d)throw new TypeError("Generator is already executing.");for(;M&&(M=0,B[0]&&(h=0)),h;)try{if(d=1,_&&(A=2&B[0]?_.return:B[0]?_.throw||((A=_.return)&&A.call(_),0):_.next)&&!(A=A.call(_,B[1])).done)return A;switch(_=0,A&&(B=[2&B[0],A.value]),B[0]){case 0:case 1:A=B;break;case 4:return h.label++,{value:B[1],done:!1};case 5:h.label++,_=B[1],B=[0];continue;case 7:B=h.ops.pop(),h.trys.pop();continue;default:if(!(A=(A=h.trys).length>0&&A[A.length-1])&&(6===B[0]||2===B[0])){h=0;continue}if(3===B[0]&&(!A||B[1]>A[0]&&B[1]<A[3])){h.label=B[1];break}if(6===B[0]&&h.label<A[1]){h.label=A[1],A=B;break}if(A&&h.label<A[2]){h.label=A[2],h.ops.push(B);break}A[2]&&h.ops.pop(),h.trys.pop();continue}B=p.call(f,h)}catch(K){B=[6,K],_=0}finally{d=A=0}if(5&B[0])throw B[1];return{value:B[0]?B[1]:void 0,done:!0}}([B,K])}}}function se(){for(var f=0,p=0,h=arguments.length;p<h;p++)f+=arguments[p].length;var d=Array(f),_=0;for(p=0;p<h;p++)for(var A=arguments[p],M=0,Z=A.length;M<Z;M++,_++)d[_]=A[M];return d}function de(f,p,h){if(h||2===arguments.length)for(var A,d=0,_=p.length;d<_;d++)(A||!(d in p))&&(A||(A=Array.prototype.slice.call(p,0,d)),A[d]=p[d]);return f.concat(A||Array.prototype.slice.call(p))}function ie(f){return this instanceof ie?(this.v=f,this):new ie(f)}function ee(f,p,h){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var _,d=h.apply(f,p||[]),A=[];return _=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),Z("next"),Z("throw"),Z("return",function M(te){return function(be){return Promise.resolve(be).then(te,ue)}}),_[Symbol.asyncIterator]=function(){return this},_;function Z(te,be){d[te]&&(_[te]=function(Ee){return new Promise(function(Ne,ke){A.push([te,Ee,Ne,ke])>1||re(te,Ee)})},be&&(_[te]=be(_[te])))}function re(te,be){try{!function B(te){te.value instanceof ie?Promise.resolve(te.value.v).then(K,ue):ve(A[0][2],te)}(d[te](be))}catch(Ee){ve(A[0][3],Ee)}}function K(te){re("next",te)}function ue(te){re("throw",te)}function ve(te,be){te(be),A.shift(),A.length&&re(A[0][0],A[0][1])}}function _e(f){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var h,p=f[Symbol.asyncIterator];return p?p.call(f):(f=function R(f){var p="function"==typeof Symbol&&Symbol.iterator,h=p&&f[p],d=0;if(h)return h.call(f);if(f&&"number"==typeof f.length)return{next:function(){return f&&d>=f.length&&(f=void 0),{value:f&&f[d++],done:!f}}};throw new TypeError(p?"Object is not iterable.":"Symbol.iterator is not defined.")}(f),h={},d("next"),d("throw"),d("return"),h[Symbol.asyncIterator]=function(){return this},h);function d(A){h[A]=f[A]&&function(M){return new Promise(function(Z,re){!function _(A,M,Z,re){Promise.resolve(re).then(function(B){A({value:B,done:Z})},M)}(Z,re,(M=f[A](M)).done,M.value)})}}}function he(f,p){return Object.defineProperty?Object.defineProperty(f,"raw",{value:p}):f.raw=p,f}"function"==typeof SuppressedError&&SuppressedError}}]);