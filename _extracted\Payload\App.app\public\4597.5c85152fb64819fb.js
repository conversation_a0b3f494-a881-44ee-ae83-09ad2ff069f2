(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4597],{94597:(H,m,o)=>{o.r(m),o.d(m,{MboTransfiyaPendingHomePageModule:()=>E});var c=o(17007),f=o(78007),v=o(79798),s=o(30263),l=o(15861),g=o(39904),h=o(95437),u=o(3334),p=o(17698),e=o(99877),b=o(48774),y=o(16621),P=o(50689);function x(i,a){if(1&i){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-cell-transaction",9),e.\u0275\u0275listener("event",function(r){const j=e.\u0275\u0275restoreView(n).$implicit,A=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(A.onPending(r,j))}),e.\u0275\u0275elementEnd()}if(2&i){const n=a.$implicit,t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("title",n.title)("subtitle",n.description)("action",t.action)}}function T(i,a){if(1&i&&(e.\u0275\u0275elementStart(0,"div",7),e.\u0275\u0275template(1,x,1,3,"bocc-cell-transaction",8),e.\u0275\u0275elementEnd()),2&i){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",n.pendings)}}function C(i,a){1&i&&(e.\u0275\u0275elementStart(0,"mbo-message-empty"),e.\u0275\u0275text(1," A\xfan no has recibido ninguna solicitud "),e.\u0275\u0275elementEnd())}let M=(()=>{class i{constructor(n,t,r,d){this.modalConfirmation=n,this.mboProvider=t,this.requestConfiguration=r,this.managerTransfiya=d,this.pendings=[],this.backAction={id:"btn_transfiya-pending-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(g.Z6.TRANSFERS.CELTOCEL.HOME)}},this.action={icon:"block"}}ngOnInit(){setTimeout(()=>{this.initializatedConfiguration()},120)}onPending(n,t){"action"===n?this.rejectPendingConfirm(t):this.responsePending(t)}initializatedConfiguration(){var n=this;return(0,l.Z)(function*(){n.mboProvider.loader.open("Consultando, por favor espere..."),(yield n.requestConfiguration.home()).when({success:t=>{n.pendings=t}},()=>{n.mboProvider.loader.close()})})()}responsePending(n){this.managerTransfiya.setPending(n).when({success:()=>{this.mboProvider.navigation.next(g.Z6.TRANSFERS.TRANSFIYA.PENDING.SOURCE)}})}rejectPendingConfirm(n){this.modalConfirmation.execute({title:"Rechazar solicitud",message:"\xbfEst\xe1s seguro que quieres rechazar la solicitud de dinero?",accept:{label:"Rechazar",theme:"danger",click:()=>{this.rejectPending(n)}},decline:{label:"Continuar"}})}rejectPending(n){var t=this;return(0,l.Z)(function*(){t.mboProvider.loader.open("Eliminando solicitud, por favor espere..."),(yield t.managerTransfiya.rejectPending(n)).when({success:({message:r})=>{t.pendings=(0,u.removeElement)(t.pendings,({uuid:d})=>n.uuid===d),t.mboProvider.toast.success(r)},failure:({message:r})=>{t.mboProvider.toast.error(r,"Error")}},()=>{t.mboProvider.loader.close()})})()}}return i.\u0275fac=function(n){return new(n||i)(e.\u0275\u0275directiveInject(s.$e),e.\u0275\u0275directiveInject(h.ZL),e.\u0275\u0275directiveInject(p.xt),e.\u0275\u0275directiveInject(p.Pm))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["mbo-transfiya-pending-home-page"]],decls:8,vars:3,consts:[[1,"mbo-transfiya-pending-home-page__content"],[1,"mbo-transfiya-pending-home-page__header"],["title","Solicitudes",3,"leftAction"],[1,"mbo-transfiya-pending-home-page__body"],[1,"mbo-transfiya-pending-home-page__title","subtitle2-medium"],["class","mbo-transfiya-pending-home-page__list",4,"ngIf"],[4,"ngIf"],[1,"mbo-transfiya-pending-home-page__list"],["icon","exchange-data",3,"title","subtitle","action","event",4,"ngFor","ngForOf"],["icon","exchange-data",3,"title","subtitle","action","event"]],template:function(n,t){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," \xbfQu\xe9 solicitud deseas responder? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,T,2,1,"div",5),e.\u0275\u0275template(7,C,2,0,"mbo-message-empty",6),e.\u0275\u0275elementEnd()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",t.backAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",t.pendings.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.pendings.length))},dependencies:[c.NgForOf,c.NgIf,b.J,y.m,P.A],styles:["/*!\n * MBO TransfiyaPendingHome Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 06/Jul/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-pending-home-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-pending-home-page .mbo-transfiya-pending-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-transfiya-pending-home-page .mbo-transfiya-pending-home-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);margin:var(--sizing-safe-body-x8)}mbo-transfiya-pending-home-page .mbo-transfiya-pending-home-page__title{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),i})(),E=(()=>{class i{}return i.\u0275fac=function(n){return new(n||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[c.CommonModule,f.RouterModule.forChild([{path:"",component:M}]),s.Jx,s.mS,v.Aj,s.oc]}),i})()}}]);