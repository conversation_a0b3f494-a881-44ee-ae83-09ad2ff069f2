(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6292],{12188:(M,y,r)=>{r.d(y,{I:()=>h,T:()=>p});var g=r(15861),u=r(87956),c=r(53113),o=r(98699);class s{constructor(l,e,i){this.biller=l,this.source=e,this.amount=i}}function P(t){return new s(t.biller,t.source,t.amount)}var f=r(71776),B=r(39904),D=r(87903),R=r(42168),v=r(84757),a=r(99877);let I=(()=>{class t{constructor(e){this.http=e}send(e){return(0,R.firstValueFrom)(this.http.post(B.bV.PAYMENTS.BILLER,function b(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:String(t.amount),nie:t.biller.number,pmtCodServ:t.biller.companyId,toEntity:t.biller.companyName,toNickname:t.biller.nickname}]}(e)).pipe((0,v.map)(([i])=>(0,D.l1)(i,"SUCCESS")))).catch(i=>(0,D.rU)(i))}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(f.HttpClient))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var E=r(20691);let n=(()=>{class t extends E.Store{constructor(e){super({confirmation:!1}),e.subscribes(B.PU,()=>{this.reset()})}setBiller(e){this.reduce(i=>({...i,biller:e}))}getBiller(){return this.select(({biller:e})=>e)}setSource(e){this.reduce(i=>({...i,source:e}))}getSource(){return this.select(({source:e})=>e)}setAmount(e){this.reduce(i=>({...i,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(u.Yd))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),h=(()=>{class t{constructor(e,i,m){this.repository=e,this.store=i,this.eventBusService=m}setBiller(e){try{return o.Either.success(this.store.setBiller(e))}catch({message:i}){return o.Either.failure({message:i})}}setSource(e){try{return o.Either.success(this.store.setSource(e))}catch({message:i}){return o.Either.failure({message:i})}}setAmount(e){try{return o.Either.success(this.store.setAmount(e))}catch({message:i}){return o.Either.failure({message:i})}}reset(){try{return o.Either.success(this.store.reset())}catch({message:e}){return o.Either.failure({message:e})}}send(){var e=this;return(0,g.Z)(function*(){const i=P(e.store.currentState),m=yield e.execute(i);return e.eventBusService.emit(m.channel),o.Either.success({biller:i,status:m})})()}execute(e){try{return this.repository.send(e)}catch({message:i}){return Promise.resolve(c.LN.error(i))}}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(I),a.\u0275\u0275inject(n),a.\u0275\u0275inject(u.Yd))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var d=r(19799);let p=(()=>{class t{constructor(e,i,m){this.products=e,this.billers=i,this.store=m}source(e){var i=this;return(0,g.Z)(function*(){try{const m=yield i.products.requestAccountsForTransfer();let C=i.store.getBiller();return!C&&e&&(C=(yield i.billers.request()).find(({uuid:N})=>e===N),i.store.setBiller(C)),o.Either.success({biller:C,products:m})}catch({message:m}){return o.Either.failure({message:m})}})()}amount(){try{const e=this.store.getSource(),i=this.store.getBiller(),m=this.store.getAmount();return o.Either.success({amount:m,biller:i,source:e})}catch({message:e}){return o.Either.failure({message:e})}}confirmation(){try{const e=P(this.store.currentState);return o.Either.success({payment:e})}catch({message:e}){return o.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(u.hM),a.\u0275\u0275inject(d.e),a.\u0275\u0275inject(n))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},6292:(M,y,r)=>{r.r(y),r.d(y,{MboPaymentBillerResultPageModule:()=>p});var g=r(17007),u=r(78007),c=r(79798),o=r(15861),s=r(99877),b=r(39904),P=r(95437),f=r(87903),B=r(53113);function D(t){const{isError:l,message:e}=t;return{animation:(0,f.jY)(t),title:l?"\xa1Pago fallido!":"\xa1Pago exitoso!",subtitle:e}}function R({isError:t}){return t?[(0,f.wT)("Finalizar","finish","outline"),(0,f.wT)("Volver a intentar","retry")]:[(0,f.wT)("Hacer otro pago","retry","outline"),(0,f.wT)("Finalizar","finish")]}var a=r(12188),I=r(10464),E=r(78021),n=r(16442);function h(t,l){if(1&t&&(s.\u0275\u0275elementStart(0,"div",4),s.\u0275\u0275element(1,"mbo-header-result",5),s.\u0275\u0275elementEnd()),2&t){const e=s.\u0275\u0275nextContext();s.\u0275\u0275advance(1),s.\u0275\u0275property("rightActions",e.rightActions)}}let d=(()=>{class t{constructor(e,i,m){this.ref=e,this.mboProvider=i,this.managerBiller=m,this.requesting=!0,this.template=b.$d,this.rightActions=[{id:"btn_payment-biller-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_payment-biller-result-page_template"),this.initializatedTransaction()}onAction(e){this.mboProvider.navigation.next("finish"===e?b.Z6.CUSTOMER.PRODUCTS.HOME:b.Z6.PAYMENTS.SERVICES.BILLER.SOURCE)}initializatedTransaction(){var e=this;return(0,o.Z)(function*(){(yield e.managerBiller.send()).when({success:i=>{e.template=function v(t){const{dateFormat:l,timeFormat:e}=new B.ou,{status:i,biller:m}=t;return{actions:R(i),error:i.isError,header:D(i),informations:[(0,f.SP)("DESTINO",m.biller.nickname,m.biller.number,m.biller.companyName),(0,f._f)("SUMA DE",m.amount),(0,f.cZ)(l,e)],skeleton:!1}}(i)}},()=>{e.requesting=!1,e.managerBiller.reset()})})()}}return t.\u0275fac=function(e){return new(e||t)(s.\u0275\u0275directiveInject(s.ElementRef),s.\u0275\u0275directiveInject(P.ZL),s.\u0275\u0275directiveInject(a.I))},t.\u0275cmp=s.\u0275\u0275defineComponent({type:t,selectors:[["mbo-payment-biller-result-page"]],decls:5,vars:2,consts:[[1,"mbo-payment-biller-result-page__content","mbo-page__scroller"],["class","mbo-payment-biller-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-payment-biller-result-page__body"],["id","crd_payment-biller-result-page_template",3,"template","action"],[1,"mbo-payment-biller-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(e,i){1&e&&(s.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),s.\u0275\u0275template(2,h,2,1,"div",1),s.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),s.\u0275\u0275listener("action",function(C){return i.onAction(C)}),s.\u0275\u0275elementEnd()()()()),2&e&&(s.\u0275\u0275advance(2),s.\u0275\u0275property("ngIf",!i.requesting),s.\u0275\u0275advance(2),s.\u0275\u0275property("template",i.template))},dependencies:[g.NgIf,I.K,E.c,n.u],styles:["/*!\n * MBO PaymentBillerResult Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 01/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-biller-result-page{position:relative;display:block;width:100%;height:100%}mbo-payment-biller-result-page .mbo-payment-biller-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-biller-result-page .mbo-payment-biller-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}\n"],encapsulation:2}),t})(),p=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=s.\u0275\u0275defineInjector({imports:[g.CommonModule,u.RouterModule.forChild([{path:"",component:d}]),c.KI,c.cN,c.tu]}),t})()},63674:(M,y,r)=>{r.d(y,{Eg:()=>f,Lo:()=>o,Wl:()=>s,ZC:()=>b,_f:()=>u,br:()=>P,tl:()=>c});var g=r(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},c=new g.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),o={color:"success",key:"paid",label:"Pagada"},s={color:"alert",key:"pending",label:"Por pagar"},b={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},f={color:"info",key:"programmed",label:"Programado"}},66067:(M,y,r)=>{r.d(y,{S6:()=>B,T2:()=>P,UQ:()=>D,mZ:()=>f});var g=r(39904),u=r(6472),o=r(63674),s=r(31707);class P{constructor(v,a,I,E,n,h,d,p,t,l,e){this.id=v,this.type=a,this.name=I,this.nickname=E,this.number=n,this.bank=h,this.isAval=d,this.isProtected=p,this.isOwner=t,this.ownerName=l,this.ownerDocument=e,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[g.y1],this.initialsName=(0,u.initials)(E),this.shortNumber=n.substring(n.length-4),this.descriptionNumber=`${I} ${n}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:h.logo,light:h.logo,standard:h.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(v){this.informationValue||(this.informationValue=v)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(v){return this.currenciesValue.includes(v)}}class f{constructor(v,a){this.id=v,this.type=a}}class B{constructor(v,a,I,E,n,h,d,p,t,l,e,i){this.uuid=v,this.number=a,this.nie=I,this.nickname=E,this.companyId=n,this.companyName=h,this.amount=d,this.registerDate=p,this.expirationDate=t,this.paid=l,this.statusCode=e,this.references=i,this.recurring=i.length>0,this.status=function b(R){switch(R){case s.U.EXPIRED:return o.ZC;case s.U.PENDING:return o.Wl;case s.U.PROGRAMMED:return o.Eg;case s.U.RECURRING:return o.br;default:return o.Lo}}(e)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class D{constructor(v,a,I,E,n,h,d,p){this.uuid=v,this.number=a,this.nickname=I,this.companyId=E,this.companyName=n,this.city=h,this.amount=d,this.isBiller=p}}},19799:(M,y,r)=>{r.d(y,{e:()=>I,W:()=>E});var g=r(71776),u=r(39904),c=r(87956),o=r(98699),s=r(42168),b=r(84757),P=r(53113),f=r(33876),B=r(66067);var a=r(99877);let I=(()=>{class n{constructor(d,p){this.http=d,p.subscribes(u.PU,()=>{this.destroy()}),this.billers$=(0,o.securePromise)(()=>(0,s.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,b.map)(({content:t})=>t.map(l=>function v(n){return new B.UQ((0,f.v4)(),n.nie,n.nickname,n.orgIdNum,n.orgName,n.city,+n.amt,(0,o.parseBoolean)(n.biller))}(l))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return n.\u0275fac=function(d){return new(d||n)(a.\u0275\u0275inject(g.HttpClient),a.\u0275\u0275inject(c.Yd))},n.\u0275prov=a.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),E=(()=>{class n{constructor(d,p){this.http=d,p.subscribes(u.PU,()=>{this.destroy()}),this.invoices$=(0,o.securePromise)(()=>(0,s.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,b.map)(({content:t})=>t.map(l=>function R(n){const h=n.refInfo.map(d=>function D(n){return new B.mZ(n.refId,n.refType)}(d));return new B.S6((0,f.v4)(),n.invoiceNum,n.nie,n.nickName,n.orgIdNum,n.orgName,+n.totalCurAmt,new P.ou(n.effDt),new P.ou(n.expDt),(0,o.parseBoolean)(n.payDone),n.state,h)}(l))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return n.\u0275fac=function(d){return new(d||n)(a.\u0275\u0275inject(g.HttpClient),a.\u0275\u0275inject(c.Yd))},n.\u0275prov=a.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},31707:(M,y,r)=>{r.d(y,{U:()=>g,f:()=>u});var g=(()=>{return(c=g||(g={})).RECURRING="1",c.EXPIRED="2",c.PENDING="3",c.PROGRAMMED="4",g;var c})(),u=(()=>{return(c=u||(u={})).BILLER="Servicio",c.NON_BILLER="Servicio",c.PSE="Servicio",c.TAX="Impuesto",c.LOAN="Obligaci\xf3n financiera",c.CREDIT_CARD="Obligaci\xf3n financiera",u;var c})()}}]);