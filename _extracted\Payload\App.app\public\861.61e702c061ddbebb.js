(self.webpackChunkapp=self.webpackChunkapp||[]).push([[861],{88844:(w,f,o)=>{o.d(f,{YI:()=>D,tc:()=>P,iR:()=>R,jq:()=>p,Hv:()=>g,S6:()=>v,E2:()=>t,V4:()=>I,wp:()=>U,CE:()=>L,YQ:()=>m,ND:()=>C,t1:()=>M});var d=o(6472);class T{constructor(e){this.value=e}get title(){return this.value.name}get description(){return this.value.name}get logo(){return this.value.logo}compareTo({id:e}){return this.value.id===e}filtrable(e){return(0,d.hasPattern)(this.value.name,e)}}function m(c){return c.map(e=>new T(e))}class E{constructor(e){this.currency=e}get value(){return this.currency}get title(){return`Deuda en ${this.currency.label}`}get description(){return`Deuda en ${this.currency.label}`}compareTo(e){return this.currency.code===e?.code}filtrable(e){return!0}}function C(c){return c.map(e=>new E(e))}var i=o(39904);class g{constructor(e){this.value=e}get title(){return this.value.label}get description(){return this.value.label}compareTo(e){return this.value.reference===e.reference}filtrable(e){return!0}}const D=i.Bf.map(c=>new g(c));class u{constructor(e,G){this.value=e,this.title=this.value.label,this.description=G?this.value.code:this.value.label}compareTo(e){return this.value===e}filtrable(e){return!0}}const p=new u(i.Gd),v=new u(i.XU),A=new u(i.t$),t=new u(i.j1),I=new u(i.k7),R=[p,v,A,t],P=[new u(i.Gd,!0),new u(i.XU,!0),new u(i.t$,!0),new u(i.j1,!0)];class _{constructor(e){this.product=e}get value(){return this.product}get title(){return this.product.nickname||this.product.label}get description(){return`${this.title} - ${this.product.shortNumber}`}compareTo(e){return this.product.id===e?.id}filtrable(e){return!0}}function M(c){return c.map(e=>new _(e))}var a=o(89148);class l{constructor(e){this.filter=e}get value(){return this.filter.value}get title(){return this.filter.label}get description(){return this.filter.short}compareTo(e){return this.value===e}filtrable(e){return!0}}const L=new l({label:"Todos los productos",short:"Todos",value:a.Gt.None}),N=new l({label:"Cuentas de ahorro",short:"Ahorros",value:a.Gt.SavingAccount}),O=new l({label:"Cuentas corriente",short:"Corrientes",value:a.Gt.CheckingAccount}),F=new l({label:"Depositos electr\xf3nicos",short:"Depositos",value:a.Gt.ElectronicDeposit}),r=new l({label:"Cuentas AFC",short:"AFC",value:a.Gt.AfcAccount}),h=new l({label:"Tarjetas de cr\xe9dito",short:"TC",value:a.Gt.CreditCard}),n=new l({label:"Inversiones",short:"Inversiones",value:a.Gt.CdtAccount}),s=new l({label:"Cr\xe9ditos",short:"Cr\xe9ditos",value:a.Gt.Loan}),y=new l({label:"Cr\xe9ditos rotativos",short:"Rotativos",value:a.Gt.ResolvingCredit}),S=new l({label:"Productos Aval",short:"Aval",value:a.Gt.Aval}),x=new l({label:"Productos fiduciarios",short:"Fiducias",value:a.Gt.Trustfund}),b=new l({label:"Otros productos",short:"Otros",value:a.Gt.None}),U={SDA:N,DDA:O,EDA:F,AFC:r,CCA:h,CDA:n,DLA:s,LOC:y,AVAL:S,80:x,MDA:b,NONE:b,SBA:b,VDA:b}},90861:(w,f,o)=>{o.r(f),o.d(f,{MboTransfiyaHistoryPageModule:()=>F});var d=o(17007),T=o(78007),m=o(30263),E=o(79798),C=o(65715),i=o(15861),g=o(22816),D=o(39904),u=o(88844),p=o(95437),v=o(57544),A=o(17698),t=o(99877),I=o(48774),R=o(2460),P=o(55491),_=o(50689),M=o(64561);function a(r,h){1&r&&t.\u0275\u0275element(0,"mbo-transfiya-history-card",14),2&r&&t.\u0275\u0275property("history",h.$implicit)}function l(r,h){if(1&r&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",15),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&r){const n=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",n.msgError," ")}}function L(r,h){if(1&r&&(t.\u0275\u0275elementStart(0,"div",11),t.\u0275\u0275template(1,a,1,1,"mbo-transfiya-history-card",12),t.\u0275\u0275template(2,l,2,1,"mbo-message-empty",13),t.\u0275\u0275elementEnd()),2&r){const n=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",n.collection),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",n.isEmpty)}}function N(r,h){1&r&&(t.\u0275\u0275elementStart(0,"div",16),t.\u0275\u0275element(1,"mbo-transfiya-history-card",17)(2,"mbo-transfiya-history-card",17),t.\u0275\u0275elementEnd()),2&r&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}let O=(()=>{class r{constructor(n,s){this.mboProvider=n,this.requestConfiguration=s,this.requesting=!1,this.collection=[],this.ranges=u.YI,this.backAction={id:"btn_transfers-history_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(D.Z6.TRANSFERS.CELTOCEL.HOME)}},this.scroller=new g.S,this.rangeControl=new v.FormControl}ngOnInit(){this.initializatedConfiguration(),this.unsubscription=this.rangeControl.subscribe(n=>{this.history&&n&&!n.equals(this.history.range)&&this.refresh(n)})}ngOnDestroy(){this.unsubscription&&this.unsubscription()}get isEmpty(){return this.history&&(0===this.history.collection.length||this.history.isError)}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transferencias realizadas.":"Lo sentimos, por el momento no cuentas con transferencias realizadas."}get isSkeleton(){return this.requesting||!this.history}onScroll(n){this.scroller.reset(n.target),this.scroller.verticalPercentage>90&&!this.history.finished&&!this.requesting&&this.nextPage()}initializatedConfiguration(){var n=this;return(0,i.Z)(function*(){(yield n.requestConfiguration.firstPage()).when({success:s=>{n.collection=s.collection,n.history=s,n.rangeControl.setValue(s.range)}})})()}refresh(n){var s=this;return(0,i.Z)(function*(){s.history=void 0,s.requesting=!0,(yield s.requestConfiguration.refresh(n)).when({success:y=>{s.collection=y.collection,s.history=y}},()=>{s.requesting=!1})})()}nextPage(){var n=this;return(0,i.Z)(function*(){n.requesting=!0,(yield n.requestConfiguration.nextPage()).when({success:s=>{n.collection=s}},()=>{n.requesting=!1})})()}}return r.\u0275fac=function(n){return new(n||r)(t.\u0275\u0275directiveInject(p.ZL),t.\u0275\u0275directiveInject(A.UD))},r.\u0275cmp=t.\u0275\u0275defineComponent({type:r,selectors:[["mbo-transfiya-history-page"]],decls:12,vars:7,consts:[[1,"mbo-transfiya-history-page__content",3,"scroll"],[1,"mbo-transfiya-history-page__header"],["title","Historial",3,"leftAction"],[1,"mbo-transfiya-history-page__body"],[1,"mbo-transfiya-history-page__title"],[1,"subtitle2-medium"],[1,"mbo-transfiya-history-page__subheader","bocc-subheader",3,"hidden"],["icon","filter-settings"],[3,"suggestions","formControl","disabled"],["class","mbo-transfiya-history-page__list",4,"ngIf"],["class","mbo-transfiya-history-page__skeleton",4,"ngIf"],[1,"mbo-transfiya-history-page__list"],[3,"history",4,"ngFor","ngForOf"],["class","mbo-transfer-history-page__empty",4,"ngIf"],[3,"history"],[1,"mbo-transfer-history-page__empty"],[1,"mbo-transfiya-history-page__skeleton"],[3,"skeleton"]],template:function(n,s){1&n&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275listener("scroll",function(S){return s.onScroll(S)}),t.\u0275\u0275elementStart(1,"div",1),t.\u0275\u0275element(2,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"div",3)(4,"div",4)(5,"label",5),t.\u0275\u0275text(6,"Mis \xfaltimas transferencias"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(7,"div",6),t.\u0275\u0275element(8,"bocc-icon",7)(9,"bocc-select-button",8),t.\u0275\u0275elementEnd(),t.\u0275\u0275template(10,L,3,2,"div",9),t.\u0275\u0275template(11,N,3,2,"div",10),t.\u0275\u0275elementEnd()()),2&n&&(t.\u0275\u0275advance(2),t.\u0275\u0275property("leftAction",s.backAction),t.\u0275\u0275advance(5),t.\u0275\u0275property("hidden",s.isSkeleton),t.\u0275\u0275advance(2),t.\u0275\u0275property("suggestions",s.ranges)("formControl",s.rangeControl)("disabled",s.requesting),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",s.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",s.isSkeleton))},dependencies:[d.NgForOf,d.NgIf,I.J,R.Z,P.G,_.A,M.r],styles:["/*!\n * MBO TransfiyaHistory Page\n * v2.0.2\n * Author: MB Frontend Developers\n * Created: 06/Jul/2023\n * Updated: 08/Jul/2024\n*/mbo-transfiya-history-page{position:relative;display:flex;width:100%;height:100%;flex-direction:column;justify-content:space-between}mbo-transfiya-history-page .mbo-transfiya-history-page__content{position:relative;width:100%;overflow:auto}mbo-transfiya-history-page .mbo-transfiya-history-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-x4);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}mbo-transfiya-history-page .mbo-transfiya-history-page__title{position:relative;width:100%;text-align:center}mbo-transfiya-history-page .mbo-transfiya-history-page__subheader{width:calc(100% - var(--sizing-x8));margin:0rem var(--sizing-x4)}mbo-transfiya-history-page .mbo-transfiya-history-page__subheader bocc-icon{color:var(--color-blue-700)}mbo-transfiya-history-page .mbo-transfiya-history-page__subheader bocc-select-button{--bocc-button-padding: 0rem var(--sizing-x1);width:100%}mbo-transfiya-history-page .mbo-transfiya-history-page__subheader bocc-select-button .bocc-button__content{justify-content:space-between}\n"],encapsulation:2}),r})(),F=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=t.\u0275\u0275defineNgModule({type:r}),r.\u0275inj=t.\u0275\u0275defineInjector({imports:[d.CommonModule,T.RouterModule.forChild([{path:"",component:O}]),m.Jx,m.Zl,m.GM,E.Aj,C.fc]}),r})()}}]);