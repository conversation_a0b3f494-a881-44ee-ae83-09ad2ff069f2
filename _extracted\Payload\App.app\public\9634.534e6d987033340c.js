(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9634],{70179:(l,i,e)=>{e.r(i),e.d(i,{NativeBiometricWeb:()=>n});var o=e(17737),r=e(72042);class n extends o.WebPlugin{isAvailable(){return Promise.resolve({biometricType:r.p.NONE,isAvailable:!1})}verifyIdentity(t){throw new Error("Plugin web NativeBiometric method not implemented.")}getCredentials(t){throw new Error("Plugin web NativeBiometric method not implemented.")}setCredentials(t){throw new Error("Plugin web NativeBiometric method not implemented.")}deleteCredentials(t){throw new Error("Plugin web NativeBiometric method not implemented.")}}}}]);