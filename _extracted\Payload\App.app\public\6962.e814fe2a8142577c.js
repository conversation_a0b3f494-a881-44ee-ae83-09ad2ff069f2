(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6962],{12188:(M,y,r)=>{r.d(y,{I:()=>c,T:()=>l});var f=r(15861),u=r(87956),m=r(53113),s=r(98699);class g{constructor(d,e,i){this.biller=d,this.source=e,this.amount=i}}function P(t){return new g(t.biller,t.source,t.amount)}var p=r(71776),I=r(39904),o=r(87903),E=r(42168),v=r(84757),a=r(99877);let b=(()=>{class t{constructor(e){this.http=e}send(e){return(0,E.firstValueFrom)(this.http.post(I.bV.PAYMENTS.BILLER,function D(t){return[{acctIdFrom:t.source.id,acctNickname:t.source.nickname,acctTypeFrom:t.source.type,amt:String(t.amount),nie:t.biller.number,pmtCodServ:t.biller.companyId,toEntity:t.biller.companyName,toNickname:t.biller.nickname}]}(e)).pipe((0,v.map)(([i])=>(0,o.l1)(i,"SUCCESS")))).catch(i=>(0,o.rU)(i))}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(p.HttpClient))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var C=r(20691);let n=(()=>{class t extends C.Store{constructor(e){super({confirmation:!1}),e.subscribes(I.PU,()=>{this.reset()})}setBiller(e){this.reduce(i=>({...i,biller:e}))}getBiller(){return this.select(({biller:e})=>e)}setSource(e){this.reduce(i=>({...i,source:e}))}getSource(){return this.select(({source:e})=>e)}setAmount(e){this.reduce(i=>({...i,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getConfirmation(){return this.select(({confirmation:e})=>e)}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(u.Yd))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),c=(()=>{class t{constructor(e,i,B){this.repository=e,this.store=i,this.eventBusService=B}setBiller(e){try{return s.Either.success(this.store.setBiller(e))}catch({message:i}){return s.Either.failure({message:i})}}setSource(e){try{return s.Either.success(this.store.setSource(e))}catch({message:i}){return s.Either.failure({message:i})}}setAmount(e){try{return s.Either.success(this.store.setAmount(e))}catch({message:i}){return s.Either.failure({message:i})}}reset(){try{return s.Either.success(this.store.reset())}catch({message:e}){return s.Either.failure({message:e})}}send(){var e=this;return(0,f.Z)(function*(){const i=P(e.store.currentState),B=yield e.execute(i);return e.eventBusService.emit(B.channel),s.Either.success({biller:i,status:B})})()}execute(e){try{return this.repository.send(e)}catch({message:i}){return Promise.resolve(m.LN.error(i))}}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(b),a.\u0275\u0275inject(n),a.\u0275\u0275inject(u.Yd))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var h=r(19799);let l=(()=>{class t{constructor(e,i,B){this.products=e,this.billers=i,this.store=B}source(e){var i=this;return(0,f.Z)(function*(){try{const B=yield i.products.requestAccountsForTransfer();let R=i.store.getBiller();return!R&&e&&(R=(yield i.billers.request()).find(({uuid:O})=>e===O),i.store.setBiller(R)),s.Either.success({biller:R,products:B})}catch({message:B}){return s.Either.failure({message:B})}})()}amount(){try{const e=this.store.getSource(),i=this.store.getBiller(),B=this.store.getAmount();return s.Either.success({amount:B,biller:i,source:e})}catch({message:e}){return s.Either.failure({message:e})}}confirmation(){try{const e=P(this.store.currentState);return s.Either.success({payment:e})}catch({message:e}){return s.Either.failure({message:e})}}}return t.\u0275fac=function(e){return new(e||t)(a.\u0275\u0275inject(u.hM),a.\u0275\u0275inject(h.e),a.\u0275\u0275inject(n))},t.\u0275prov=a.\u0275\u0275defineInjectable({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},61566:(M,y,r)=>{r.d(y,{w:()=>P});var f=r(39904),u=r(95437),m=r(30263),s=r(12188),g=r(99877);let P=(()=>{class p{constructor(o,E,v){this.modalConfirmation=o,this.mboProvider=E,this.managerBiller=v}execute(o=!0){o?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerBiller.reset(),this.mboProvider.navigation.back(f.Z6.PAYMENTS.HOME)}}return p.\u0275fac=function(o){return new(o||p)(g.\u0275\u0275inject(m.$e),g.\u0275\u0275inject(u.ZL),g.\u0275\u0275inject(s.I))},p.\u0275prov=g.\u0275\u0275defineInjectable({token:p,factory:p.\u0275fac,providedIn:"root"}),p})()},86962:(M,y,r)=>{r.r(y),r.d(y,{MboPaymentBillerSourcePageModule:()=>n});var f=r(17007),u=r(78007),m=r(79798),s=r(30263),g=r(15861),D=r(39904),P=r(95437),p=r(12188),I=r(61566),o=r(99877),E=r(6661),v=r(4663),a=r(48774);const b=D.Z6.PAYMENTS.SERVICES;let C=(()=>{class c{constructor(l,t,d,e,i){this.activateRoute=l,this.mboProvider=t,this.requestConfiguration=d,this.managerBiller=e,this.cancelProvider=i,this.confirmation=!1,this.products=[],this.requesting=!0,this.backAction={id:"btn_payments-biller-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(b.HOME)}},this.cancelAction={id:"btn_payments-biller-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(l){this.managerBiller.setSource(l).when({success:()=>{this.mboProvider.navigation.next(b.BILLER.AMOUNT)}})}initializatedConfiguration(){var l=this;return(0,g.Z)(function*(){const{billerUuid:t}=l.activateRoute.snapshot.queryParams;(yield l.requestConfiguration.source(t)).when({success:({biller:d,products:e})=>{if(!d)return l.mboProvider.navigation.back(b.HOME);l.biller=d,l.products=e}},()=>{l.requesting=!1})})()}}return c.\u0275fac=function(l){return new(l||c)(o.\u0275\u0275directiveInject(u.ActivatedRoute),o.\u0275\u0275directiveInject(P.ZL),o.\u0275\u0275directiveInject(p.T),o.\u0275\u0275directiveInject(p.I),o.\u0275\u0275directiveInject(I.w))},c.\u0275cmp=o.\u0275\u0275defineComponent({type:c,selectors:[["mbo-payment-biller-source-page"]],decls:7,vars:8,consts:[[1,"mbo-payment-biller-source-page__content"],[1,"mbo-payment-biller-source-page__header"],["title","Origen","progress","50%",3,"leftAction","rightAction"],[1,"mbo-payment-biller-source-page__body"],[3,"title","number","subtitle","hidden"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","select"]],template:function(l,t){1&l&&(o.\u0275\u0275elementStart(0,"div",0)(1,"div",1),o.\u0275\u0275element(2,"bocc-header-form",2),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"div",3),o.\u0275\u0275element(4,"bocc-card-service",4),o.\u0275\u0275elementStart(5,"mbo-product-source-selector",5),o.\u0275\u0275listener("select",function(e){return t.onProduct(e)}),o.\u0275\u0275text(6," \xbfDesde d\xf3nde deseas pagar? "),o.\u0275\u0275elementEnd()()()),2&l&&(o.\u0275\u0275advance(2),o.\u0275\u0275property("leftAction",t.backAction)("rightAction",t.cancelAction),o.\u0275\u0275advance(2),o.\u0275\u0275property("title",null==t.biller?null:t.biller.nickname)("number",null==t.biller?null:t.biller.number)("subtitle",null==t.biller?null:t.biller.companyName)("hidden",t.requesting),o.\u0275\u0275advance(1),o.\u0275\u0275property("skeleton",t.requesting)("products",t.products))},dependencies:[E.S,v.c,a.J],styles:["/*!\n * MBO PaymentBillerSource Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 01/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-biller-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-biller-source-page .mbo-payment-biller-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-biller-source-page .mbo-payment-biller-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box}\n"],encapsulation:2}),c})(),n=(()=>{class c{}return c.\u0275fac=function(l){return new(l||c)},c.\u0275mod=o.\u0275\u0275defineNgModule({type:c}),c.\u0275inj=o.\u0275\u0275defineInjector({imports:[f.CommonModule,u.RouterModule.forChild([{path:"",component:C}]),s.S8,m.cV,s.Jx]}),c})()},63674:(M,y,r)=>{r.d(y,{Eg:()=>p,Lo:()=>s,Wl:()=>g,ZC:()=>D,_f:()=>u,br:()=>P,tl:()=>m});var f=r(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},m=new f.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),s={color:"success",key:"paid",label:"Pagada"},g={color:"alert",key:"pending",label:"Por pagar"},D={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},p={color:"info",key:"programmed",label:"Programado"}},66067:(M,y,r)=>{r.d(y,{S6:()=>I,T2:()=>P,UQ:()=>o,mZ:()=>p});var f=r(39904),u=r(6472),s=r(63674),g=r(31707);class P{constructor(v,a,b,C,n,c,h,l,t,d,e){this.id=v,this.type=a,this.name=b,this.nickname=C,this.number=n,this.bank=c,this.isAval=h,this.isProtected=l,this.isOwner=t,this.ownerName=d,this.ownerDocument=e,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[f.y1],this.initialsName=(0,u.initials)(C),this.shortNumber=n.substring(n.length-4),this.descriptionNumber=`${b} ${n}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:c.logo,light:c.logo,standard:c.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(v){this.informationValue||(this.informationValue=v)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(v){return this.currenciesValue.includes(v)}}class p{constructor(v,a){this.id=v,this.type=a}}class I{constructor(v,a,b,C,n,c,h,l,t,d,e,i){this.uuid=v,this.number=a,this.nie=b,this.nickname=C,this.companyId=n,this.companyName=c,this.amount=h,this.registerDate=l,this.expirationDate=t,this.paid=d,this.statusCode=e,this.references=i,this.recurring=i.length>0,this.status=function D(E){switch(E){case g.U.EXPIRED:return s.ZC;case g.U.PENDING:return s.Wl;case g.U.PROGRAMMED:return s.Eg;case g.U.RECURRING:return s.br;default:return s.Lo}}(e)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class o{constructor(v,a,b,C,n,c,h,l){this.uuid=v,this.number=a,this.nickname=b,this.companyId=C,this.companyName=n,this.city=c,this.amount=h,this.isBiller=l}}},19799:(M,y,r)=>{r.d(y,{e:()=>b,W:()=>C});var f=r(71776),u=r(39904),m=r(87956),s=r(98699),g=r(42168),D=r(84757),P=r(53113),p=r(33876),I=r(66067);var a=r(99877);let b=(()=>{class n{constructor(h,l){this.http=h,l.subscribes(u.PU,()=>{this.destroy()}),this.billers$=(0,s.securePromise)(()=>(0,g.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,D.map)(({content:t})=>t.map(d=>function v(n){return new I.UQ((0,p.v4)(),n.nie,n.nickname,n.orgIdNum,n.orgName,n.city,+n.amt,(0,s.parseBoolean)(n.biller))}(d))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return n.\u0275fac=function(h){return new(h||n)(a.\u0275\u0275inject(f.HttpClient),a.\u0275\u0275inject(m.Yd))},n.\u0275prov=a.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),C=(()=>{class n{constructor(h,l){this.http=h,l.subscribes(u.PU,()=>{this.destroy()}),this.invoices$=(0,s.securePromise)(()=>(0,g.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,D.map)(({content:t})=>t.map(d=>function E(n){const c=n.refInfo.map(h=>function o(n){return new I.mZ(n.refId,n.refType)}(h));return new I.S6((0,p.v4)(),n.invoiceNum,n.nie,n.nickName,n.orgIdNum,n.orgName,+n.totalCurAmt,new P.ou(n.effDt),new P.ou(n.expDt),(0,s.parseBoolean)(n.payDone),n.state,c)}(d))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return n.\u0275fac=function(h){return new(h||n)(a.\u0275\u0275inject(f.HttpClient),a.\u0275\u0275inject(m.Yd))},n.\u0275prov=a.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},31707:(M,y,r)=>{r.d(y,{U:()=>f,f:()=>u});var f=(()=>{return(m=f||(f={})).RECURRING="1",m.EXPIRED="2",m.PENDING="3",m.PROGRAMMED="4",f;var m})(),u=(()=>{return(m=u||(u={})).BILLER="Servicio",m.NON_BILLER="Servicio",m.PSE="Servicio",m.TAX="Impuesto",m.LOAN="Obligaci\xf3n financiera",m.CREDIT_CARD="Obligaci\xf3n financiera",u;var m})()}}]);