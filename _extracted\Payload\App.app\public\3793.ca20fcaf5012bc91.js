(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3793],{54753:(h,i,t)=>{t.r(i),t.d(i,{ion_avatar:()=>r,ion_badge:()=>n,ion_thumbnail:()=>e});var o=t(42477),d=t(37943),s=t(23814);const r=class{constructor(a){(0,o.r)(this,a)}render(){return(0,o.h)(o.H,{key:"f6014b524497bb18ae919ba6f6928407310d6870",class:(0,d.b)(this)},(0,o.h)("slot",{key:"192ff4a8e10c0b0a4a2ed795ff2675afa8b23449"}))}};r.style={ios:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}",md:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}"};const n=class{constructor(a){(0,o.r)(this,a),this.color=void 0}render(){const a=(0,d.b)(this);return(0,o.h)(o.H,{key:"22d41ceefb76f40dfbf739fd71483f1272a45858",class:(0,s.c)(this.color,{[a]:!0})},(0,o.h)("slot",{key:"e7e65463bac5903971a8f9f6be55515f42b81a83"}))}};n.style={ios:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}",md:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}"};const e=class{constructor(a){(0,o.r)(this,a)}render(){return(0,o.h)(o.H,{key:"d2667635930e4c0896805f452357e7dc9086bc72",class:(0,d.b)(this)},(0,o.h)("slot",{key:"66eb1487f3da4da2ef71b812a8d0f0fe884c7d81"}))}};e.style=":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}"}}]);