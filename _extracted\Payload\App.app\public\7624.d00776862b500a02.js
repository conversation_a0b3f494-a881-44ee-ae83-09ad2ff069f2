(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7624],{57624:(f,i,o)=>{o.r(i),o.d(i,{MboSecurityTravelerHomePageModule:()=>b});var m=o(17007),s=o(78007),c=o(30263),d=o(39904),v=o(95437),e=o(99877),g=o(48774),u=o(23436);const{HOME:p,TRAVELER:l}=d.Z6.CUSTOMER.SECURITY;let y=(()=>{class t{constructor(r){this.mboProvider=r,this.backAction={id:"btn_traveler-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(p)}}}onRegister(){this.mboProvider.navigation.next(l.REGISTER)}onHistory(){this.mboProvider.navigation.next(l.HISTORY)}}return t.\u0275fac=function(r){return new(r||t)(e.\u0275\u0275directiveInject(v.ZL))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-security-traveler-home-page"]],decls:10,vars:1,consts:[[1,"mbo-security-traveler-home-page__content"],[1,"mbo-security-traveler-home-page__header"],["title","Viajes",3,"leftAction"],[1,"mbo-security-traveler-home-page__body"],["id","btn_traveler-home_register","icon","travel-register",3,"click"],["id","btn_traveler-home_history","icon","travel-history",3,"click"]],template:function(r,n){1&r&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"bocc-card-category",4),e.\u0275\u0275listener("click",function(){return n.onRegister()}),e.\u0275\u0275elementStart(5,"span"),e.\u0275\u0275text(6,"Nuevo viaje"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"bocc-card-category",5),e.\u0275\u0275listener("click",function(){return n.onHistory()}),e.\u0275\u0275elementStart(8,"span"),e.\u0275\u0275text(9,"Mis viajes"),e.\u0275\u0275elementEnd()()()()),2&r&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",n.backAction))},dependencies:[g.J,u.D],styles:["/*!\n * MBO SecurityTravelerHome Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 03/Oct/2023\n * Updated: 31/Dic/2023\n*/mbo-security-traveler-home-page{position:relative;display:block;width:100%;height:100%;overflow:hidden}mbo-security-traveler-home-page .mbo-security-traveler-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-security-traveler-home-page .mbo-security-traveler-home-page__body{position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);float:left;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),t})(),b=(()=>{class t{}return t.\u0275fac=function(r){return new(r||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[m.CommonModule,s.RouterModule.forChild([{path:"",component:y}]),c.Jx,c.D0]}),t})()}}]);