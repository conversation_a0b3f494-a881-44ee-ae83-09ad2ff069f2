(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3650],{41179:(be,H,I)=>{I.r(H),I.d(H,{DAY_LABELS:()=>U,DAY_NAMES:()=>A,DateRange:()=>m,Day:()=>f,MONTH_DAYS:()=>W,MONTH_LABELS:()=>B,MONTH_NAMES:()=>k,Miliseconds:()=>n,Month:()=>d,MonthDay:()=>s,assignDayInDate:()=>ye,assignMonthInDate:()=>De,assignYearInDate:()=>de,cloneDate:()=>Z,createDate:()=>fe,dateFormatForHumans:()=>$,dateFormatTemplate:()=>me,dateIsAfter:()=>O,dateIsAfterOrEquals:()=>P,dateIsBefore:()=>b,dateIsBeforeOrEquals:()=>_,dateIsBetween:()=>C,dateIsBetweenOrEquals:()=>se,dateIsEquals:()=>E,dateIsEqualsWeight:()=>ue,dateToJson:()=>R,decreaseDaysInDate:()=>ne,decreaseTimestampInDate:()=>h,decreaseWeeksInDate:()=>ae,getDateWeight:()=>F,getDaysOfMonth:()=>N,getPendingTime:()=>ee,getTimeDifference:()=>T,getTimeDifferenceForHumans:()=>ie,increaseDaysInDate:()=>te,increaseTimestampInDate:()=>p,increaseWeeksInDate:()=>re,isLeapYear:()=>G,normalizeMaxTime:()=>oe,normalizeMinTime:()=>g});var V=I(6472);const L=[],a=function Q(e){return(t,r)=>{const i=e[r?.language||"es"];return i?(0,V.interpolation)(i[t],r?.interpolators):""}}({es:{january:"Enero",february:"Febrero",march:"Marzo",april:"Abril",may:"Mayo",june:"Junio",july:"Julio",august:"Agosto",september:"Septiembre",october:"Octubre",november:"Noviembre",december:"Diciembre",monday:"Lunes",tuesday:"Martes",wednesday:"Mi\xe9rcoles",thursday:"Jueves",friday:"Viernes",saturday:"S\xe1bado",sunday:"Domingo",year:"a\xf1o",month:"mes",week:"semana",day:"d\xeda",hour:"hora",minute:"minuto",second:"segundo"},en:{january:"January",february:"February",march:"March",april:"April",may:"May",june:"June",july:"July",august:"August",september:"September",october:"October",november:"November",december:"December",monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday",year:"year",month:"month",week:"week",day:"day",hour:"hour",minute:"minute",second:"second"}});var n=(()=>{return(e=n||(n={}))[e.Year=31536e6]="Year",e[e.Month=2592e6]="Month",e[e.Week=6048e5]="Week",e[e.Day=864e5]="Day",e[e.Hour=36e5]="Hour",e[e.Minute=6e4]="Minute",e[e.Second=1e3]="Second",n;var e})(),f=(()=>{return(e=f||(f={}))[e.Sunday=0]="Sunday",e[e.Monday=1]="Monday",e[e.Tuesday=2]="Tuesday",e[e.Wednesday=3]="Wednesday",e[e.Thursday=4]="Thursday",e[e.Friday=5]="Friday",e[e.Saturday=6]="Saturday",f;var e})(),d=(()=>{return(e=d||(d={}))[e.January=0]="January",e[e.February=1]="February",e[e.March=2]="March",e[e.April=3]="April",e[e.May=4]="May",e[e.June=5]="June",e[e.July=6]="July",e[e.August=7]="August",e[e.September=8]="September",e[e.October=9]="October",e[e.November=10]="November",e[e.December=11]="December",d;var e})(),s=(()=>{return(e=s||(s={}))[e.January=31]="January",e[e.February=28]="February",e[e.March=31]="March",e[e.April=30]="April",e[e.May=31]="May",e[e.June=30]="June",e[e.July=31]="July",e[e.August=31]="August",e[e.September=30]="September",e[e.October=31]="October",e[e.November=30]="November",e[e.December=31]="December",s;var e})();let D=[],S=[],y=[],w=[];function v(e="es"){D=[a("january",{language:e}),a("february",{language:e}),a("march",{language:e}),a("april",{language:e}),a("may",{language:e}),a("june",{language:e}),a("july",{language:e}),a("august",{language:e}),a("september",{language:e}),a("october",{language:e}),a("november",{language:e}),a("december",{language:e})],S=D.map(t=>t.substring(0,3)),y=[a("sunday",{language:e}),a("monday",{language:e}),a("tuesday",{language:e}),a("wednesday",{language:e}),a("thursday",{language:e}),a("friday",{language:e}),a("saturday",{language:e})],w=y.map(t=>t.substring(0,3))}function l(e){return null!=e}v(),function K(e){L.push(e)}(e=>v(e));const W=[s.January,s.February,s.March,s.April,s.May,s.June,s.July,s.August,s.September,s.October,s.November,s.December];function k(e){return l(e)?D[e]||"":D}function B(e){return l(e)?S[e]||"":S}function A(e){return l(e)?y[e]||"":y}function U(e){return l(e)?w[e]||"":w}function o(e,t){return e.toString().padStart(t,"0")}function j(e,t){const r=N(t,e.getMonth());r<e.getDate()&&e.setDate(r),e.setFullYear(t)}function x(e,t){const r=N(e.getFullYear(),t);r<e.getDate()&&e.setDate(r),e.setMonth(t)}const z={dd:e=>o(e.getDate(),2),dw:e=>A()[e.getDay()],dx:e=>A()[e.getDay()],mm:e=>o(e.getMonth()+1,2),mn:e=>k(e.getMonth()),mx:e=>B(e.getMonth()),aa:e=>o(e.getFullYear(),4),hh:e=>o(e.getHours(),2),ii:e=>o(e.getMinutes(),2),ss:e=>o(e.getSeconds(),2),hz:e=>o(function X(e){const t=e.getHours();return t>12?t-12:0===t?12:t}(e),2),zz:e=>e.getHours()>11?"PM":"AM"},c=(e,t,r="s",u)=>({value:e,label:`${t}(${r})`,single:t,plural:u=u||`${t}${r}`}),q=[c(n.Year,"a\xf1o"),c(n.Month,"mes","es"),c(n.Week,"semana"),c(n.Day,"d\xeda","s","dias"),c(n.Hour,"hora"),c(n.Minute,"minuto"),c(n.Second,"segundo")];function Z(e){return new Date(e.getTime())}function R(e){return{day:e.getDate(),month:e.getMonth(),year:e.getFullYear()}}function $(e){const t=e>0?"Falta":"Hace",r=Math.abs(e);if(r<1e3)return`${t} 1 segundo`;let u="",i=0;for(;""===u&&i<q.length;){const Y=q[i],J=Math.floor(r/Y.value);J>=1&&(u=`${t} ${J} ${1===J?Y.single:Y.plural}`),i++}return u}function ee(e,t=new Date){const r=t.getTime()-e.getTime();return{years:Math.floor(r/n.Year),months:Math.floor(r/n.Month),weeks:Math.floor(r/n.Week),days:Math.floor(r/n.Day),hours:Math.floor(r/n.Hour),minutes:Math.floor(r/n.Minute),seconds:Math.floor(r/n.Second)}}function p(e,t){return new Date(e.getTime()+t)}function te(e,t=1){return p(e,t*n.Day)}function re(e,t=1){return p(e,t*n.Week)}function h(e,t){return new Date(e.getTime()-t)}function ne(e,t=1){return h(e,t*n.Day)}function ae(e,t=1){return h(e,t*n.Week)}function F(e){return 365*e.getFullYear()+30*(e.getMonth()+1)+e.getDate()}function E(e,t=new Date){return e.getTime()===t.getTime()}function ue(e,t=new Date){return F(e)===F(t)}function b(e,t=new Date){return e.getTime()>t.getTime()}function _(e,t=new Date){return e.getTime()>=t.getTime()}function O(e,t=new Date){return e.getTime()<t.getTime()}function P(e,t=new Date){return e.getTime()<=t.getTime()}function C(e,t,r=new Date){return O(e,r)&&b(t,r)}function se(e,t,r=new Date){return P(e,r)||_(t,r)}function T(e,t=new Date){return e.getTime()-t.getTime()}function ie(e,t=new Date){return $(T(e,t))}function g(e){const t=new Date(e.getTime());return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t}function oe(e){const t=new Date(e.getTime());return t.setHours(23),t.setMinutes(59),t.setSeconds(59),t.setMilliseconds(0),t}function N(e,t){return 1===t&&G(e)?29:W[t]}function G(e){const t=e instanceof Date?e.getFullYear():e;return t%4==0&&(t%100!=0||t%400==0)}const ce=/{([^{}]*)}/g;function me(e,t){return t.replace(ce,(r,u)=>z[u]?z[u](e):r)}function fe({day:e,month:t,year:r}){const u=new Date;return r&&j(u,r),t&&x(u,t),e&&u.setDate(e),u}function de(e,t){const r=new Date(e.getTime());return j(r,t),r.setFullYear(t),r}function De(e,t){const r=new Date(e.getTime());return x(r,t),r.setMonth(t),r}function ye(e,t){const r=new Date(e.getTime());return r.setDate(t),r}class m{constructor(t,r){this.minDate=g(t),this.maxDate=r&&b(r,t)?g(r):g(t)}get minISOFormat(){return this.minDate.toISOString()}get maxISOFormat(){return this.maxDate.toISOString()}between(t){return C(this.minDate,this.maxDate,t)}equals({maxDate:t,minDate:r}){return E(this.minDate,r)&&E(this.maxDate,t)}recalculate(t){return b(this.minDate,t)?new m(t,this.maxDate):O(this.maxDate,t)||T(t,this.minDate)>T(this.maxDate,t)?new m(this.minDate,t):new m(t,this.maxDate)}static now(){return new m(new Date)}}}}]);