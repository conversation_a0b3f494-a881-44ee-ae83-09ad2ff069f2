(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4203],{54203:(_,u,o)=>{o.r(u),o.d(u,{Network:()=>r,NetworkWeb:()=>c});var l=o(15861),d=o(17737);function i(){const t=window.navigator.connection||window.navigator.mozConnection||window.navigator.webkitConnection;let n="unknown";const e=t?t.type||t.effectiveType:null;if(e&&"string"==typeof e)switch(e){case"bluetooth":case"cellular":case"slow-2g":case"2g":case"3g":n="cellular";break;case"none":n="none";break;case"ethernet":case"wifi":case"wimax":case"4g":n="wifi";break;case"other":case"unknown":n="unknown"}return n}class c extends d.WebPlugin{constructor(){super(),this.handleOnline=()=>{const e={connected:!0,connectionType:i()};this.notifyListeners("networkStatusChange",e)},this.handleOffline=()=>{this.notifyListeners("networkStatusChange",{connected:!1,connectionType:"none"})},typeof window<"u"&&(window.addEventListener("online",this.handleOnline),window.addEventListener("offline",this.handleOffline))}getStatus(){var n=this;return(0,l.Z)(function*(){if(!window.navigator)throw n.unavailable("Browser does not support the Network Information API");const e=window.navigator.onLine,a=i();return{connected:e,connectionType:e?a:"none"}})()}}const r=new c},15861:(_,u,o)=>{function l(w,i,c,r,t,n,e){try{var a=w[n](e),s=a.value}catch(f){return void c(f)}a.done?i(s):Promise.resolve(s).then(r,t)}function d(w){return function(){var i=this,c=arguments;return new Promise(function(r,t){var n=w.apply(i,c);function e(s){l(n,r,t,e,a,"next",s)}function a(s){l(n,r,t,e,a,"throw",s)}e(void 0)})}}o.d(u,{Z:()=>d})}}]);