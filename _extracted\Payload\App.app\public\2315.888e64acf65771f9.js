(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2315],{92315:(F,s,a)=>{a.r(s),a.d(s,{MboTransfiyaApprovedHomePageModule:()=>C});var i=a(17007),c=a(78007),l=a(79798),p=a(30263),v=a(15861),f=a(39904),g=a(95437),d=a(17698),e=a(99877),y=a(48774),b=a(16621),h=a(50689);function u(t,n){if(1&t){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"bocc-cell-transaction",9),e.\u0275\u0275listener("event",function(){const H=e.\u0275\u0275restoreView(o).$implicit,I=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(I.onApproved(H))}),e.\u0275\u0275elementEnd()}if(2&t){const o=n.$implicit;e.\u0275\u0275property("title",o.title)("subtitle",o.description)}}function A(t,n){if(1&t&&(e.\u0275\u0275elementStart(0,"div",7),e.\u0275\u0275template(1,u,1,2,"bocc-cell-transaction",8),e.\u0275\u0275elementEnd()),2&t){const o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",o.approveds)}}function T(t,n){1&t&&(e.\u0275\u0275elementStart(0,"mbo-message-empty"),e.\u0275\u0275text(1," No tienes ninguna transferencia pendiente "),e.\u0275\u0275elementEnd())}const{CELTOCEL:x,TRANSFIYA:M}=f.Z6.TRANSFERS;let P=(()=>{class t{constructor(o,r,m){this.mboProvider=o,this.requestConfiguration=r,this.managerTransfiya=m,this.approveds=[],this.backAction={id:"btn_transfiya-approved-home_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(x.HOME)}}}ngOnInit(){setTimeout(()=>{this.initializatedConfiguration()},120)}onApproved(o){this.managerTransfiya.setApproved(o).when({success:()=>{this.mboProvider.navigation.next(M.APPROVED.INFORMATION)}})}initializatedConfiguration(){var o=this;return(0,v.Z)(function*(){o.mboProvider.loader.open("Consultando, por favor espere..."),(yield o.requestConfiguration.home()).when({success:r=>{o.approveds=r}},()=>{o.mboProvider.loader.close()})})()}}return t.\u0275fac=function(o){return new(o||t)(e.\u0275\u0275directiveInject(g.ZL),e.\u0275\u0275directiveInject(d.NF),e.\u0275\u0275directiveInject(d.Pm))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-transfiya-approved-home-page"]],decls:8,vars:3,consts:[[1,"mbo-transfiya-approved-home-page__content"],[1,"mbo-transfiya-approved-home-page__header"],["title","Aceptar dinero",3,"leftAction"],[1,"mbo-transfiya-approved-home-page__body"],[1,"mbo-transfiya-approved-home-page__title","subtitle2-medium"],["class","mbo-transfiya-approved-home-page__list",4,"ngIf"],[4,"ngIf"],[1,"mbo-transfiya-approved-home-page__list"],["icon","exchange-data",3,"title","subtitle","event",4,"ngFor","ngForOf"],["icon","exchange-data",3,"title","subtitle","event"]],template:function(o,r){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5," Selecciona la transferencia que deseas aceptar "),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(6,A,2,1,"div",5),e.\u0275\u0275template(7,T,2,0,"mbo-message-empty",6),e.\u0275\u0275elementEnd()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",r.backAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",r.approveds.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!r.approveds.length))},dependencies:[i.NgForOf,i.NgIf,y.J,b.m,h.A],styles:["/*!\n * MBO TransfiyaApprovedHome Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 10/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-approved-home-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-transfiya-approved-home-page .mbo-transfiya-approved-home-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-transfiya-approved-home-page .mbo-transfiya-approved-home-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x6);margin:var(--sizing-safe-body-x8)}mbo-transfiya-approved-home-page .mbo-transfiya-approved-home-page__title{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;color:var(--color-carbon-darker-1000)}\n"],encapsulation:2}),t})(),C=(()=>{class t{}return t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[i.CommonModule,c.RouterModule.forChild([{path:"",component:P}]),p.Jx,p.mS,l.Aj,p.oc]}),t})()}}]);