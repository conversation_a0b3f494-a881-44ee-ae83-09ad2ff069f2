(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3159],{73159:(Ir,Fe,ge)=>{ge.r(Fe),ge.d(Fe,{ClientCacheState:()=>I,DataGovernance:()=>H,FormattableLogMessage:()=>A,LogLevel:()=>y,OverrideBehaviour:()=>w,PollingMode:()=>D,PrerequisiteFlagComparator:()=>M,RefreshResult:()=>K,SegmentComparator:()=>R,SettingKeyValue:()=>G,SettingType:()=>L,User:()=>nr,UserComparator:()=>c,createConsoleLogger:()=>pr,createFlagOverridesFromMap:()=>dr,default:()=>br,disposeAllClients:()=>vr,getClient:()=>Dt});var h=ge(97582),x=(()=>{return(t=x||(x={}))[t.Fetched=0]="Fetched",t[t.NotModified=1]="NotModified",t[t.Errored=2]="Errored",x;var t})(),W=function(){function t(e,n,r,i){this.status=e,this.config=n,this.errorMessage=r,this.errorException=i}return t.success=function(e){return new t(x.Fetched,e)},t.notModified=function(e){return new t(x.NotModified,e)},t.error=function(e,n,r){return new t(x.Errored,e,n??"Unknown error.",r)},t}(),Q=function(t){function e(n){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];var o=t.call(this,function(s,a){switch(s){case"abort":return"Request was aborted.";case"timeout":return"Request timed out. Timeout value: "+a[0]+"ms";case"failure":var l=a[0],f="Request failed due to a network or protocol error.";return l?f+" "+(l instanceof Error?l.message:l+""):f}}(n,r))||this;return o.cause=n,o instanceof e||(Object.setPrototypeOf||function(s,a){return s.__proto__=a})(o,e.prototype),o.args=r,o}return(0,h.ZT)(e,t),e}(Error),B=(()=>{return(t=B||(B={}))[t.No=0]="No",t[t.Should=1]="Should",t[t.Force=2]="Force",B;var t})(),L=(()=>{return(t=L||(L={}))[t.Boolean=0]="Boolean",t[t.String=1]="String",t[t.Int=2]="Int",t[t.Double=3]="Double",L;var t})(),c=(()=>{return(t=c||(c={}))[t.TextIsOneOf=0]="TextIsOneOf",t[t.TextIsNotOneOf=1]="TextIsNotOneOf",t[t.TextContainsAnyOf=2]="TextContainsAnyOf",t[t.TextNotContainsAnyOf=3]="TextNotContainsAnyOf",t[t.SemVerIsOneOf=4]="SemVerIsOneOf",t[t.SemVerIsNotOneOf=5]="SemVerIsNotOneOf",t[t.SemVerLess=6]="SemVerLess",t[t.SemVerLessOrEquals=7]="SemVerLessOrEquals",t[t.SemVerGreater=8]="SemVerGreater",t[t.SemVerGreaterOrEquals=9]="SemVerGreaterOrEquals",t[t.NumberEquals=10]="NumberEquals",t[t.NumberNotEquals=11]="NumberNotEquals",t[t.NumberLess=12]="NumberLess",t[t.NumberLessOrEquals=13]="NumberLessOrEquals",t[t.NumberGreater=14]="NumberGreater",t[t.NumberGreaterOrEquals=15]="NumberGreaterOrEquals",t[t.SensitiveTextIsOneOf=16]="SensitiveTextIsOneOf",t[t.SensitiveTextIsNotOneOf=17]="SensitiveTextIsNotOneOf",t[t.DateTimeBefore=18]="DateTimeBefore",t[t.DateTimeAfter=19]="DateTimeAfter",t[t.SensitiveTextEquals=20]="SensitiveTextEquals",t[t.SensitiveTextNotEquals=21]="SensitiveTextNotEquals",t[t.SensitiveTextStartsWithAnyOf=22]="SensitiveTextStartsWithAnyOf",t[t.SensitiveTextNotStartsWithAnyOf=23]="SensitiveTextNotStartsWithAnyOf",t[t.SensitiveTextEndsWithAnyOf=24]="SensitiveTextEndsWithAnyOf",t[t.SensitiveTextNotEndsWithAnyOf=25]="SensitiveTextNotEndsWithAnyOf",t[t.SensitiveArrayContainsAnyOf=26]="SensitiveArrayContainsAnyOf",t[t.SensitiveArrayNotContainsAnyOf=27]="SensitiveArrayNotContainsAnyOf",t[t.TextEquals=28]="TextEquals",t[t.TextNotEquals=29]="TextNotEquals",t[t.TextStartsWithAnyOf=30]="TextStartsWithAnyOf",t[t.TextNotStartsWithAnyOf=31]="TextNotStartsWithAnyOf",t[t.TextEndsWithAnyOf=32]="TextEndsWithAnyOf",t[t.TextNotEndsWithAnyOf=33]="TextNotEndsWithAnyOf",t[t.ArrayContainsAnyOf=34]="ArrayContainsAnyOf",t[t.ArrayNotContainsAnyOf=35]="ArrayNotContainsAnyOf",c;var t})(),M=(()=>{return(t=M||(M={}))[t.Equals=0]="Equals",t[t.NotEquals=1]="NotEquals",M;var t})(),R=(()=>{return(t=R||(R={}))[t.IsIn=0]="IsIn",t[t.IsNotIn=1]="IsNotIn",R;var t})(),F=function(){function t(e,n,r,i){this.configJson=e,this.config=n,this.timestamp=r,this.httpETag=i}return t.equals=function(e,n){return e.httpETag&&n.httpETag?e.httpETag===n.httpETag:e.configJson===n.configJson},t.prototype.with=function(e){return new t(this.configJson,this.config,e,this.httpETag)},Object.defineProperty(t.prototype,"isEmpty",{get:function(){return!this.config},enumerable:!1,configurable:!0}),t.prototype.isExpired=function(e){return this===t.empty||this.timestamp+e<t.generateTimestamp()},t.generateTimestamp=function(){return(new Date).getTime()},t.serialize=function(e){var n,r;return e.timestamp+"\n"+(null!==(n=e.httpETag)&&void 0!==n?n:"")+"\n"+(null!==(r=e.configJson)&&void 0!==r?r:"")},t.deserialize=function(e){for(var n=Array(2),r=0,i=0;i<n.length;i++){if((r=e.indexOf("\n",r))<0)throw new Error("Number of values is fewer than expected.");n[i]=r++}var o=n[0],s=e.substring(0,o),a=parseInt(s);if(isNaN(a))throw new Error("Invalid fetch time: "+s);var l,f,u=(s=e.substring(r=o+1,o=n[1])).length>0?s:void 0;return(s=e.substring(r=o+1)).length>0&&(l=ee.deserialize(s),f=s),new t(f,l,a,u)},t.serializationFormatVersion="v2",t.empty=new t(void 0,void 0,0,void 0),t}(),ee=function(){function t(e){var r,i,n=this;this.preferences=null!=e.p?new xt(e.p):void 0,this.segments=null!==(i=null===(r=e.s)||void 0===r?void 0:r.map(function(o){return new Mt(o)}))&&void 0!==i?i:[],this.settings=null!=e.f?Object.fromEntries(Object.entries(e.f).map(function(o){return[o[0],new De(o[1],n)]})):{}}return t.deserialize=function(e){var n=JSON.parse(e);if("object"!=typeof n||!n)throw new Error("Invalid config JSON content:"+e);return new t(n)},Object.defineProperty(t.prototype,"salt",{get:function(){var e;return null===(e=this.preferences)||void 0===e?void 0:e.salt},enumerable:!1,configurable:!0}),t}(),xt=function t(e){this.baseUrl=e.u,this.redirectMode=e.r,this.salt=e.s},Mt=function t(e){var n,r;this.name=e.n,this.conditions=null!==(r=null===(n=e.r)||void 0===n?void 0:n.map(function(i){return new Pe(i)}))&&void 0!==r?r:[]},ve=function t(e,n){void 0===n&&(n=!1),this.value=n?e.v:xe(e.v),this.variationId=e.i},De=function(t){function e(n,r){var i,o,s,a,u,l,f=t.call(this,n,n.t<0)||this;return f.type=n.t,f.percentageOptionsAttribute=null!==(i=n.a)&&void 0!==i?i:"Identifier",f.targetingRules=null!==(s=null===(o=n.r)||void 0===o?void 0:o.map(function(v){return new Ut(v,r)}))&&void 0!==s?s:[],f.percentageOptions=null!==(u=null===(a=n.p)||void 0===a?void 0:a.map(function(v){return new ke(v)}))&&void 0!==u?u:[],f.configJsonSalt=null!==(l=r?.salt)&&void 0!==l?l:"",f}return(0,h.ZT)(e,t),e.fromValue=function(n){return new e({t:-1,v:n})},e}(ve),Ut=function t(e,n){var r,i;this.conditions=null!==(i=null===(r=e.c)||void 0===r?void 0:r.map(function(o){return null!=o.u?new Pe(o.u):null!=o.p?new qt(o.p):null!=o.s?new Wt(o.s,n):void 0}))&&void 0!==i?i:[],this.then=null!=e.p?e.p.map(function(o){return new ke(o)}):new ve(e.s)},ke=function(t){function e(n){var r=t.call(this,n)||this;return r.percentage=n.p,r}return(0,h.ZT)(e,t),e}(ve),Pe=function t(e){var n,r;this.type="UserCondition",this.comparisonAttribute=e.a,this.comparator=e.c,this.comparisonValue=null!==(r=null!==(n=e.s)&&void 0!==n?n:e.d)&&void 0!==r?r:e.l},qt=function t(e){this.type="PrerequisiteFlagCondition",this.prerequisiteFlagKey=e.f,this.comparator=e.c,this.comparisonValue=xe(e.v)},Wt=function t(e,n){this.type="SegmentCondition",this.segment=n.segments[e.s],this.comparator=e.c};function xe(t){var e,n,r;return null!==(r=null!==(n=null!==(e=t.b)&&void 0!==e?e:t.s)&&void 0!==n?n:t.i)&&void 0!==r?r:t.d}var K=function(){function t(e,n){this.errorMessage=e,this.errorException=n}return Object.defineProperty(t.prototype,"isSuccess",{get:function(){return null===this.errorMessage},enumerable:!1,configurable:!0}),t.from=function(e){return e.status!==x.Errored?t.success():t.failure(e.errorMessage,e.errorException)},t.success=function(){return new t(null)},t.failure=function(e,n){return new t(e,n)},t}(),I=(()=>{return(t=I||(I={}))[t.NoFlagData=0]="NoFlagData",t[t.HasLocalOverrideFlagDataOnly=1]="HasLocalOverrideFlagDataOnly",t[t.HasCachedFlagDataOnly=2]="HasCachedFlagDataOnly",t[t.HasUpToDateFlagData=3]="HasUpToDateFlagData",I;var t})(),C=(()=>{return(t=C||(C={}))[t.Online=0]="Online",t[t.Offline=1]="Offline",t[t.Disposed=2]="Disposed",C;var t})(),pe=function(){function t(e,n){this.configFetcher=e,this.options=n,this.pendingFetch=null,this.cacheKey=n.getCacheKey(),this.configFetcher=e,this.options=n,this.status=n.offline?C.Offline:C.Online}return t.prototype.dispose=function(){this.status=C.Disposed},Object.defineProperty(t.prototype,"disposed",{get:function(){return this.status===C.Disposed},enumerable:!1,configurable:!0}),t.prototype.refreshConfigAsync=function(){return(0,h.mG)(this,void 0,void 0,function(){var e,n,i,o;return(0,h.Jh)(this,function(s){switch(s.label){case 0:return[4,this.options.cache.get(this.cacheKey)];case 1:return e=s.sent(),this.isOffline?[3,3]:[4,this.refreshConfigCoreAsync(e)];case 2:return n=s.sent(),i=n[1],[2,[K.from(n[0]),i]];case 3:return o=this.options.logger.configServiceCannotInitiateHttpCalls().toString(),[2,[K.failure(o),e]]}})})},t.prototype.refreshConfigCoreAsync=function(e){return(0,h.mG)(this,void 0,void 0,function(){var n,r,i;return(0,h.Jh)(this,function(o){switch(o.label){case 0:return[4,this.fetchAsync(e)];case 1:return n=o.sent(),r=!1,(i=n.status===x.Fetched)||n.config.timestamp>e.timestamp&&(!n.config.isEmpty||e.isEmpty)?[4,this.options.cache.set(this.cacheKey,n.config)]:[3,3];case 2:o.sent(),r=i&&!F.equals(n.config,e),e=n.config,o.label=3;case 3:return this.onConfigFetched(n.config),r&&this.onConfigChanged(n.config),[2,[n,e]]}})})},t.prototype.onConfigFetched=function(e){},t.prototype.onConfigChanged=function(e){var n;this.options.logger.debug("config changed"),this.options.hooks.emit("configChanged",null!==(n=e.config)&&void 0!==n?n:new ee({}))},t.prototype.fetchAsync=function(e){var r;return null!==(r=this.pendingFetch)&&void 0!==r?r:this.pendingFetch=(0,h.mG)(this,void 0,void 0,function(){return(0,h.Jh)(this,function(i){switch(i.label){case 0:return i.trys.push([0,,2,3]),[4,this.fetchLogicAsync(e)];case 1:return[2,i.sent()];case 2:return this.pendingFetch=null,[7];case 3:return[2]}})})},t.prototype.fetchLogicAsync=function(e){var n;return(0,h.mG)(this,void 0,void 0,function(){var r,i,o,s,a,u;return(0,h.Jh)(this,function(l){switch(l.label){case 0:(r=this.options).logger.debug("ConfigServiceBase.fetchLogicAsync() - called."),l.label=1;case 1:return l.trys.push([1,3,,4]),[4,this.fetchRequestAsync(null!==(n=e.httpETag)&&void 0!==n?n:null)];case 2:switch(o=l.sent(),a=o[1],(s=o[0]).statusCode){case 200:return a instanceof ee?(r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was successful. Returning new config."),[2,W.success(new F(s.body,a,F.generateTimestamp(),s.eTag))]):(i=r.logger.fetchReceived200WithInvalidBody(a).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): "+s.statusCode+" "+s.reasonPhrase+" was received but the HTTP response content was invalid. Returning null."),[2,W.error(e,i,a)]);case 304:return e?(r.logger.debug("ConfigServiceBase.fetchLogicAsync(): content was not modified. Returning last config with updated timestamp."),[2,W.notModified(e.with(F.generateTimestamp()))]):(i=r.logger.fetchReceived304WhenLocalCacheIsEmpty(s.statusCode,s.reasonPhrase).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): "+s.statusCode+" "+s.reasonPhrase+" was received when no config is cached locally. Returning null."),[2,W.error(e,i)]);case 403:case 404:return i=r.logger.fetchFailedDueToInvalidSdkKey().toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning last config (if any) with updated timestamp."),[2,W.error(e.with(F.generateTimestamp()),i)];default:return i=r.logger.fetchFailedDueToUnexpectedHttpResponse(s.statusCode,s.reasonPhrase).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning null."),[2,W.error(e,i)]}return[3,4];case 3:return u=l.sent(),i=(u instanceof Q&&"timeout"===u.cause?r.logger.fetchFailedDueToRequestTimeout(u.args[0],u):r.logger.fetchFailedDueToUnexpectedError(u)).toString(),r.logger.debug("ConfigServiceBase.fetchLogicAsync(): fetch was unsuccessful. Returning null."),[2,W.error(e,i,u)];case 4:return[2]}})})},t.prototype.fetchRequestAsync=function(e,n){return void 0===n&&(n=2),(0,h.mG)(this,void 0,void 0,function(){var r,i,o,s,a,u,l;return(0,h.Jh)(this,function(f){switch(f.label){case 0:(r=this.options).logger.debug("ConfigServiceBase.fetchRequestAsync() - called."),i=0,f.label=1;case 1:return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): calling fetchLogic()"+(i>0?", retry "+i+"/"+n:"")),[4,this.configFetcher.fetchLogic(r,e)];case 2:if(200!==(o=f.sent()).statusCode)return[2,[o]];if(!o.body)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): no response body."),[2,[o,new Error("No response body.")]];s=void 0;try{s=ee.deserialize(o.body)}catch(g){return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): invalid response body."),[2,[o,g]]}if(!(a=s.preferences))return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): preferences is empty."),[2,[o,s]];if(!(u=a.baseUrl)||u===r.baseUrl)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): baseUrl OK."),[2,[o,s]];if(l=a.redirectMode,r.baseUrlOverriden&&l!==B.Force)return r.logger.debug("ConfigServiceBase.fetchRequestAsync(): options.baseUrlOverriden && redirect !== 2."),[2,[o,s]];if(r.baseUrl=u,l===B.No)return[2,[o,s]];if(l===B.Should&&r.logger.dataGovernanceIsOutOfSync(),i>=n)return r.logger.fetchFailedDueToRedirectLoop(),[2,[o,s]];f.label=3;case 3:return i++,[3,1];case 4:return[2]}})})},Object.defineProperty(t.prototype,"isOfflineExactly",{get:function(){return this.status===C.Offline},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isOffline",{get:function(){return this.status!==C.Online},enumerable:!1,configurable:!0}),t.prototype.setOnlineCore=function(){},t.prototype.setOnline=function(){this.status===C.Offline?(this.setOnlineCore(),this.status=C.Online,this.options.logger.configServiceStatusChanged(C[this.status])):this.disposed&&this.options.logger.configServiceMethodHasNoEffectDueToDisposedClient("setOnline")},t.prototype.setOfflineCore=function(){},t.prototype.setOffline=function(){this.status===C.Online?(this.setOfflineCore(),this.status=C.Offline,this.options.logger.configServiceStatusChanged(C[this.status])):this.disposed&&this.options.logger.configServiceMethodHasNoEffectDueToDisposedClient("setOffline")},t.prototype.syncUpWithCache=function(){return this.options.cache.get(this.cacheKey)},t.prototype.getReadyPromise=function(e,n){return(0,h.mG)(this,void 0,void 0,function(){var r;return(0,h.Jh)(this,function(i){switch(i.label){case 0:return[4,n(e)];case 1:return r=i.sent(),this.options.hooks.emit("clientReady",r),[2,r]}})})},t}(),de=function(){function t(){this.callbacks=[]}return Object.defineProperty(t.prototype,"aborted",{get:function(){return!this.callbacks},enumerable:!1,configurable:!0}),t.prototype.abort=function(){if(!this.aborted){var e=this.callbacks;this.callbacks=null;for(var n=0,r=e;n<r.length;n++)(0,r[n])()}},t.prototype.registerCallback=function(e){var n=this;return this.aborted?(e(),function(){}):(this.callbacks.push(e),function(){var i,r=n.callbacks;r&&(i=r.indexOf(e))>=0&&r.splice(i,1)})},t}();function Me(t,e){var n;return new Promise(function(r){var i=e?.registerCallback(function(){clearTimeout(n),r(!1)});n=setTimeout(function(){i?.(),r(!0)},t)})}function V(t,e){return void 0===e&&(e=!1),t instanceof Error?e&&t.stack?t.stack:t.toString():t+""}function Ue(t){throw t}function te(t){return Array.isArray(t)}function ye(t){return te(t)&&!t.some(function(e){return"string"!=typeof e})}function me(t,e,n,r){void 0===e&&(e=0),void 0===r&&(r=", ");var i=t.length;if(!i)return"";var o="";return e>0&&i>e&&(t=t.slice(0,e),n&&(o=n(i-e))),"'"+t.join("'"+r+"'")+"'"+o}function z(t){function e(a,u){var l=a.charCodeAt(u);if(55296<=l&&l<56320){var f=a.charCodeAt(u+1);if(56320<=f&&f<=57343)return(l<<10)+f-56613888}return l}var o,n="",r=0,i=String.fromCharCode;for(o=0;o<t.length;o++){var s=e(t,o);s<=127||(n+=t.slice(r,o),s<=2047?(n+=i(192|s>>6),n+=i(128|63&s)):s<=65535?(n+=i(224|s>>12),n+=i(128|s>>6&63),n+=i(128|63&s)):(n+=i(240|s>>18),n+=i(128|s>>12&63),n+=i(128|s>>6&63),n+=i(128|63&s),++o),r=o+1)}return n+t.slice(r,o)}function qe(t){return"number"==typeof t?t:"string"!=typeof t||!t.length||/^\s*$|^\s*0[^\d.e]/.test(t)?NaN:+t}var je,Be,He,Je,Ge,Ke,ze,_e,Ye,Ze,$e,Xe,Qe,et,tt,rt,nt,it,ot,st,jt=500,Bt=function(t){function e(n,r){var i=t.call(this,n,r)||this;i.signalInitialization=function(){},i.stopToken=new de,i.pollIntervalMs=1e3*r.pollIntervalSeconds,i.pollExpirationMs=i.pollIntervalMs-jt;var o=i.syncUpWithCache();if(0!==r.maxInitWaitTimeSeconds){i.initialized=!1;var s=new Promise(function(a){return i.signalInitialization=a});i.initializationPromise=i.waitForInitializationAsync(s).then(function(a){return i.initialized=!0,a})}else i.initialized=!0,i.initializationPromise=Promise.resolve(!1);return i.readyPromise=i.getReadyPromise(i.initializationPromise,function(a){return(0,h.mG)(i,void 0,void 0,function(){return(0,h.Jh)(this,function(u){switch(u.label){case 0:return[4,a];case 1:return u.sent(),[2,this.getCacheState(this.options.cache.getInMemory())]}})})}),r.offline||i.startRefreshWorker(o,i.stopToken),i}return(0,h.ZT)(e,t),e.prototype.waitForInitializationAsync=function(n){return(0,h.mG)(this,void 0,void 0,function(){var r,i;return(0,h.Jh)(this,function(o){switch(o.label){case 0:return this.options.maxInitWaitTimeSeconds<0?[4,n]:[3,2];case 1:return o.sent(),[2,!0];case 2:return r=new de,[4,Promise.race([n.then(function(){return!0}),Me(1e3*this.options.maxInitWaitTimeSeconds,r).then(function(){return!1})])];case 3:return i=o.sent(),r.abort(),[2,i]}})})},e.prototype.getConfig=function(){return(0,h.mG)(this,void 0,void 0,function(){function n(i){i.debug("AutoPollConfigService.getConfig() - returning value from cache.")}var r;return(0,h.Jh)(this,function(i){switch(i.label){case 0:return this.options.logger.debug("AutoPollConfigService.getConfig() called."),this.isOffline||this.initialized?[3,3]:[4,this.options.cache.get(this.cacheKey)];case 1:return(r=i.sent()).isExpired(this.pollIntervalMs)?(this.options.logger.debug("AutoPollConfigService.getConfig() - cache is empty or expired, waiting for initialization."),[4,this.initializationPromise]):(n(this.options.logger),[2,r]);case 2:i.sent(),i.label=3;case 3:return[4,this.options.cache.get(this.cacheKey)];case 4:return(r=i.sent()).isExpired(this.pollIntervalMs)?this.options.logger.debug("AutoPollConfigService.getConfig() - cache is empty or expired."):n(this.options.logger),[2,r]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("AutoPollConfigService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e.prototype.dispose=function(){this.options.logger.debug("AutoPollConfigService.dispose() called."),t.prototype.dispose.call(this),this.stopToken.aborted||this.stopRefreshWorker()},e.prototype.onConfigFetched=function(n){t.prototype.onConfigFetched.call(this,n),this.signalInitialization()},e.prototype.setOnlineCore=function(){this.startRefreshWorker(null,this.stopToken)},e.prototype.setOfflineCore=function(){this.stopRefreshWorker(),this.stopToken=new de},e.prototype.startRefreshWorker=function(n,r){return(0,h.mG)(this,void 0,void 0,function(){var i,o,s,a,u;return(0,h.Jh)(this,function(l){switch(l.label){case 0:this.options.logger.debug("AutoPollConfigService.startRefreshWorker() called."),i=!0,l.label=1;case 1:if(r.aborted)return[3,11];l.label=2;case 2:l.trys.push([2,9,,10]),o=(new Date).getTime()+this.pollIntervalMs,l.label=3;case 3:return l.trys.push([3,5,,6]),[4,this.refreshWorkerLogic(i,n)];case 4:return l.sent(),[3,6];case 5:return s=l.sent(),this.options.logger.autoPollConfigServiceErrorDuringPolling(s),[3,6];case 6:return(a=o-(new Date).getTime())>0?[4,Me(a,r)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:return u=l.sent(),this.options.logger.autoPollConfigServiceErrorDuringPolling(u),[3,10];case 10:return i=!1,n=null,[3,1];case 11:return[2]}})})},e.prototype.stopRefreshWorker=function(){this.options.logger.debug("AutoPollConfigService.stopRefreshWorker() called."),this.stopToken.abort()},e.prototype.refreshWorkerLogic=function(n,r){return(0,h.mG)(this,void 0,void 0,function(){var i;return(0,h.Jh)(this,function(o){switch(o.label){case 0:return this.options.logger.debug("AutoPollConfigService.refreshWorkerLogic() - called."),[4,r??this.options.cache.get(this.cacheKey)];case 1:return(i=o.sent()).isExpired(this.pollExpirationMs)?(n?this.isOfflineExactly:this.isOffline)?[3,3]:[4,this.refreshConfigCoreAsync(i)]:[3,4];case 2:o.sent(),o.label=3;case 3:return[3,5];case 4:n&&this.signalInitialization(),o.label=5;case 5:return[2]}})})},e.prototype.getCacheState=function(n){return n.isEmpty?I.NoFlagData:n.isExpired(this.pollIntervalMs)?I.HasCachedFlagDataOnly:I.HasUpToDateFlagData},e}(pe),Ht=function(){function t(){this.cachedConfig=F.empty}return t.prototype.set=function(e,n){this.cachedConfig=n},t.prototype.get=function(e){return this.cachedConfig},t.prototype.getInMemory=function(){return this.cachedConfig},t}(),We=function(){function t(e,n){this.cache=e,this.logger=n,this.cachedConfig=F.empty}return t.prototype.set=function(e,n){return(0,h.mG)(this,void 0,void 0,function(){var r;return(0,h.Jh)(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),n.isEmpty?(this.cachedSerializedConfig=void 0,this.cachedConfig=n,[2]):(this.cachedSerializedConfig=F.serialize(n),this.cachedConfig=n,[4,this.cache.set(e,this.cachedSerializedConfig)]);case 1:return i.sent(),[3,3];case 2:return r=i.sent(),this.logger.configServiceCacheWriteError(r),[3,3];case 3:return[2]}})})},t.prototype.updateCachedConfig=function(e){null==e||e===this.cachedSerializedConfig||(this.cachedConfig=F.deserialize(e),this.cachedSerializedConfig=e)},t.prototype.get=function(e){var i;try{var r=this.cache.get(e);if(function Vt(t){var e;return"function"==typeof(null===(e=t)||void 0===e?void 0:e.then)}(r))return i=r,(0,h.mG)(this,void 0,void 0,function(){var o,s;return(0,h.Jh)(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),o=this.updateCachedConfig,[4,i];case 1:return o.apply(this,[a.sent()]),[3,3];case 2:return s=a.sent(),this.logger.configServiceCacheReadError(s),[3,3];case 3:return[2,this.cachedConfig]}})});this.updateCachedConfig(r)}catch(i){this.logger.configServiceCacheReadError(i)}return Promise.resolve(this.cachedConfig)},t.prototype.getInMemory=function(){return this.cachedConfig},t}(),y=(()=>{return(t=y||(y={}))[t.Debug=4]="Debug",t[t.Info=3]="Info",t[t.Warn=2]="Warn",t[t.Error=1]="Error",t[t.Off=-1]="Off",y;var t})(),A=function(){function t(e,n,r){this.strings=e,this.argNames=n,this.argValues=r}return t.from=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(r){for(var i=[],o=1;o<arguments.length;o++)i[o-1]=arguments[o];return new t(r,e,i)}},Object.defineProperty(t.prototype,"defaultFormattedMessage",{get:function(){var e=this.cachedDefaultFormattedMessage;if(void 0===e){e="";for(var r=this.strings,i=this.argValues,o=0;o<r.length-1;o++)e+=r[o],e+=i[o];this.cachedDefaultFormattedMessage=e+=r[o]}return e},enumerable:!1,configurable:!0}),t.prototype.toString=function(){return this.defaultFormattedMessage},t}(),Jt=function(){function t(e,n){this.logger=e,this.hooks=n}return Object.defineProperty(t.prototype,"level",{get:function(){var e;return null!==(e=this.logger.level)&&void 0!==e?e:y.Warn},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"eol",{get:function(){var e;return null!==(e=this.logger.eol)&&void 0!==e?e:"\n"},enumerable:!1,configurable:!0}),t.prototype.isEnabled=function(e){return this.level>=e},t.prototype.log=function(e,n,r,i){var o;return this.isEnabled(e)&&this.logger.log(e,n,r,i),e===y.Error&&(null===(o=this.hooks)||void 0===o||o.emit("clientError",r.toString(),i)),r},t.prototype.debug=function(e){this.log(y.Debug,0,e)},t.prototype.configJsonIsNotPresent=function(e){return this.log(y.Error,1e3,A.from("DEFAULT_RETURN_VALUE")(je||(je=(0,h.cy)(["Config JSON is not present. Returning ","."],["Config JSON is not present. Returning ","."])),e))},t.prototype.configJsonIsNotPresentSingle=function(e,n,r){return this.log(y.Error,1e3,A.from("KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE")(Be||(Be=(0,h.cy)(["Config JSON is not present when evaluating setting '","'. Returning the `","` parameter that you specified in your application: '","'."],["Config JSON is not present when evaluating setting '","'. Returning the \\`","\\` parameter that you specified in your application: '","'."])),e,n,r))},t.prototype.settingEvaluationFailedDueToMissingKey=function(e,n,r,i){return this.log(y.Error,1001,A.from("KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE","AVAILABLE_KEYS")(He||(He=(0,h.cy)(["Failed to evaluate setting '","' (the key was not found in config JSON). Returning the `","` parameter that you specified in your application: '","'. Available keys: [","]."],["Failed to evaluate setting '","' (the key was not found in config JSON). Returning the \\`","\\` parameter that you specified in your application: '","'. Available keys: [","]."])),e,n,r,i))},t.prototype.settingEvaluationError=function(e,n,r){return this.log(y.Error,1002,A.from("METHOD_NAME","DEFAULT_RETURN_VALUE")(Je||(Je=(0,h.cy)(["Error occurred in the `","` method. Returning ","."],["Error occurred in the \\`","\\` method. Returning ","."])),e,n),r)},t.prototype.settingEvaluationErrorSingle=function(e,n,r,i,o){return this.log(y.Error,1002,A.from("METHOD_NAME","KEY","DEFAULT_PARAM_NAME","DEFAULT_PARAM_VALUE")(Ge||(Ge=(0,h.cy)(["Error occurred in the `","` method while evaluating setting '","'. Returning the `","` parameter that you specified in your application: '","'."],["Error occurred in the \\`","\\` method while evaluating setting '","'. Returning the \\`","\\` parameter that you specified in your application: '","'."])),e,n,r,i),o)},t.prototype.forceRefreshError=function(e,n){return this.log(y.Error,1003,A.from("METHOD_NAME")(Ke||(Ke=(0,h.cy)(["Error occurred in the `","` method."],["Error occurred in the \\`","\\` method."])),e),n)},t.prototype.fetchFailedDueToInvalidSdkKey=function(){return this.log(y.Error,1100,"Your SDK Key seems to be wrong. You can find the valid SDK Key at https://app.configcat.com/sdkkey")},t.prototype.fetchFailedDueToUnexpectedHttpResponse=function(e,n){return this.log(y.Error,1101,A.from("STATUS_CODE","REASON_PHRASE")(ze||(ze=(0,h.cy)(["Unexpected HTTP response was received while trying to fetch config JSON: "," ",""],["Unexpected HTTP response was received while trying to fetch config JSON: "," ",""])),e,n))},t.prototype.fetchFailedDueToRequestTimeout=function(e,n){return this.log(y.Error,1102,A.from("TIMEOUT")(_e||(_e=(0,h.cy)(["Request timed out while trying to fetch config JSON. Timeout value: ","ms"],["Request timed out while trying to fetch config JSON. Timeout value: ","ms"])),e),n)},t.prototype.fetchFailedDueToUnexpectedError=function(e){return this.log(y.Error,1103,"Unexpected error occurred while trying to fetch config JSON. It is most likely due to a local network issue. Please make sure your application can reach the ConfigCat CDN servers (or your proxy server) over HTTP.",e)},t.prototype.fetchFailedDueToRedirectLoop=function(){return this.log(y.Error,1104,"Redirection loop encountered while trying to fetch config JSON. Please contact us at https://configcat.com/support/")},t.prototype.fetchReceived200WithInvalidBody=function(e){return this.log(y.Error,1105,"Fetching config JSON was successful but the HTTP response content was invalid.",e)},t.prototype.fetchReceived304WhenLocalCacheIsEmpty=function(e,n){return this.log(y.Error,1106,A.from("STATUS_CODE","REASON_PHRASE")(Ye||(Ye=(0,h.cy)(["Unexpected HTTP response was received when no config JSON is cached locally: "," ",""],["Unexpected HTTP response was received when no config JSON is cached locally: "," ",""])),e,n))},t.prototype.autoPollConfigServiceErrorDuringPolling=function(e){return this.log(y.Error,1200,"Error occurred during auto polling.",e)},t.prototype.settingForVariationIdIsNotPresent=function(e){return this.log(y.Error,2011,A.from("VARIATION_ID")(Ze||(Ze=(0,h.cy)(["Could not find the setting for the specified variation ID: '","'."],["Could not find the setting for the specified variation ID: '","'."])),e))},t.prototype.configServiceCacheReadError=function(e){return this.log(y.Error,2200,"Error occurred while reading the cache.",e)},t.prototype.configServiceCacheWriteError=function(e){return this.log(y.Error,2201,"Error occurred while writing the cache.",e)},t.prototype.clientIsAlreadyCreated=function(e){return this.log(y.Warn,3e3,A.from("SDK_KEY")($e||($e=(0,h.cy)(["There is an existing client instance for the specified SDK Key. No new client instance will be created and the specified options are ignored. Returning the existing client instance. SDK Key: '","'."],["There is an existing client instance for the specified SDK Key. No new client instance will be created and the specified options are ignored. Returning the existing client instance. SDK Key: '","'."])),e))},t.prototype.userObjectIsMissing=function(e){return this.log(y.Warn,3001,A.from("KEY")(Xe||(Xe=(0,h.cy)(["Cannot evaluate targeting rules and % options for setting '","' (User Object is missing). You should pass a User Object to the evaluation methods like `getValueAsync()` in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate targeting rules and % options for setting '","' (User Object is missing). You should pass a User Object to the evaluation methods like \\`getValueAsync()\\` in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e))},t.prototype.dataGovernanceIsOutOfSync=function(){return this.log(y.Warn,3002,"The `dataGovernance` parameter specified at the client initialization is not in sync with the preferences on the ConfigCat Dashboard. Read more: https://configcat.com/docs/advanced/data-governance/")},t.prototype.userObjectAttributeIsMissingPercentage=function(e,n){return this.log(y.Warn,3003,A.from("KEY","ATTRIBUTE_NAME","ATTRIBUTE_NAME")(Qe||(Qe=(0,h.cy)(["Cannot evaluate % options for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate % options for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e,n,n))},t.prototype.userObjectAttributeIsMissingCondition=function(e,n,r){return this.log(y.Warn,3003,A.from("CONDITION","KEY","ATTRIBUTE_NAME","ATTRIBUTE_NAME")(et||(et=(0,h.cy)(["Cannot evaluate condition (",") for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"],["Cannot evaluate condition (",") for setting '","' (the User."," attribute is missing). You should set the User."," attribute in order to make targeting work properly. Read more: https://configcat.com/docs/advanced/user-object/"])),e,n,r,r))},t.prototype.userObjectAttributeIsInvalid=function(e,n,r,i){return this.log(y.Warn,3004,A.from("CONDITION","KEY","REASON","ATTRIBUTE_NAME")(tt||(tt=(0,h.cy)(["Cannot evaluate condition (",") for setting '","' (","). Please check the User."," attribute and make sure that its value corresponds to the comparison operator."],["Cannot evaluate condition (",") for setting '","' (","). Please check the User."," attribute and make sure that its value corresponds to the comparison operator."])),e,n,r,i))},t.prototype.userObjectAttributeIsAutoConverted=function(e,n,r,i){return this.log(y.Warn,3005,A.from("CONDITION","KEY","ATTRIBUTE_NAME","ATTRIBUTE_VALUE")(rt||(rt=(0,h.cy)(["Evaluation of condition (",") for setting '","' may not produce the expected result (the User."," attribute is not a string value, thus it was automatically converted to the string value '","'). Please make sure that using a non-string value was intended."],["Evaluation of condition (",") for setting '","' may not produce the expected result (the User."," attribute is not a string value, thus it was automatically converted to the string value '","'). Please make sure that using a non-string value was intended."])),e,n,r,i))},t.prototype.configServiceCannotInitiateHttpCalls=function(){return this.log(y.Warn,3200,"Client is in offline mode, it cannot initiate HTTP calls.")},t.prototype.configServiceMethodHasNoEffectDueToDisposedClient=function(e){return this.log(y.Warn,3201,A.from("METHOD_NAME")(nt||(nt=(0,h.cy)(["The client object is already disposed, thus `","()` has no effect."],["The client object is already disposed, thus \\`","()\\` has no effect."])),e))},t.prototype.configServiceMethodHasNoEffectDueToOverrideBehavior=function(e,n){return this.log(y.Warn,3202,A.from("OVERRIDE_BEHAVIOR","METHOD_NAME")(it||(it=(0,h.cy)(["Client is configured to use the `","` override behavior, thus `","()` has no effect."],["Client is configured to use the \\`","\\` override behavior, thus \\`","()\\` has no effect."])),e,n))},t.prototype.settingEvaluated=function(e){return this.log(y.Info,5e3,A.from("EVALUATE_LOG")(ot||(ot=(0,h.cy)(["",""],["",""])),e))},t.prototype.configServiceStatusChanged=function(e){return this.log(y.Info,5200,A.from("MODE")(st||(st=(0,h.cy)(["Switched to "," mode."],["Switched to "," mode."])),e.toUpperCase()))},t}(),Ve=function(){function t(e,n){void 0===e&&(e=y.Warn),void 0===n&&(n="\n"),this.level=e,this.eol=n,this.SOURCE="ConfigCat"}return t.prototype.log=function(e,n,r,i){var o=e===y.Debug?[console.info,"DEBUG"]:e===y.Info?[console.info,"INFO"]:e===y.Warn?[console.warn,"WARN"]:e===y.Error?[console.error,"ERROR"]:[console.log,y[e].toUpperCase()],s=o[0],a=o[1],u=void 0!==i?this.eol+V(i,!0):"";s(this.SOURCE+" - "+a+" - ["+n+"] "+r+u)},t}();function _(t){return!!t.fn}var Gt=function(){function t(){this.events={},this.eventCount=0,this.addListener=this.on,this.off=this.removeListener}return t.prototype.addListenerCore=function(e,n,r){if("function"!=typeof n)throw new TypeError("Listener must be a function");var i=this.events[e],o={fn:n,once:r};return i?_(i)?this.events[e]=[i,o]:i.push(o):(this.events[e]=o,this.eventCount++),this},t.prototype.removeListenerCore=function(e,n,r){var i=this.events[e];if(!i)return this;if(_(i))r(i,n)&&this.removeEvent(e);else for(var o=i.length-1;o>=0;o--)if(r(i[o],n)){i.splice(o,1),i.length?1===i.length&&(this.events[e]=i[0]):this.removeEvent(e);break}return this},t.prototype.removeEvent=function(e){0==--this.eventCount?this.events={}:delete this.events[e]},t.prototype.on=function(e,n){return this.addListenerCore(e,n,!1)},t.prototype.once=function(e,n){return this.addListenerCore(e,n,!0)},t.prototype.removeListener=function(e,n){if("function"!=typeof n)throw new TypeError("Listener must be a function");return this.removeListenerCore(e,n,function(r,i){return r.fn===i})},t.prototype.removeAllListeners=function(e){return e?this.events[e]&&this.removeEvent(e):(this.events={},this.eventCount=0),this},t.prototype.listeners=function(e){var n=this.events[e];if(!n)return[];if(_(n))return[n.fn];for(var r=n.length,i=new Array(r),o=0;o<r;o++)i[o]=n[o].fn;return i},t.prototype.listenerCount=function(e){var n=this.events[e];return n?_(n)?1:n.length:0},t.prototype.eventNames=function(){var e=[];if(0===this.eventCount)return e;var n=this.events;for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.push(r);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(n)):e},t.prototype.emit=function(e,n,r,i,o){for(var s,a,u=[],l=5;l<arguments.length;l++)u[l-5]=arguments[l];var g,v,f=this.events[e];if(!f)return!1;_(f)?(g=(s=[f,1])[0],v=s[1]):(g=(a=[(f=f.slice())[0],f.length])[0],v=a[1]);for(var p=arguments.length-1,d=0;;){switch(g.once&&this.removeListenerCore(e,g,function(b,T){return b===T}),p){case 0:g.fn.call(this);break;case 1:g.fn.call(this,n);break;case 2:g.fn.call(this,n,r);break;case 3:g.fn.call(this,n,r,i);break;case 4:g.fn.call(this,n,r,i,o);break;default:for(var m=new Array(p),S=0;S<p;S++)m[S]=arguments[S+1];g.fn.apply(this,m)}if(++d>=v)break;g=f[d]}return!0},t}(),at=function(){function t(){this.addListener=this.on,this.off=this.removeListener}return t.prototype.on=function(){return this},t.prototype.once=function(){return this},t.prototype.removeListener=function(){return this},t.prototype.removeAllListeners=function(){return this},t.prototype.listeners=function(){return[]},t.prototype.listenerCount=function(){return 0},t.prototype.eventNames=function(){return[]},t.prototype.emit=function(){return!1},t}();function ut(t){function e(U,q){return U<<q|U>>>32-q}var n,r,i,g,v,p,d,m,S,o=new Array(80),s=1732584193,a=4023233417,u=2562383102,l=271733878,f=3285377520,b=(t=z(t)).length,T=new Array;for(r=0;r<b-3;r+=4)i=t.charCodeAt(r)<<24|t.charCodeAt(r+1)<<16|t.charCodeAt(r+2)<<8|t.charCodeAt(r+3),T.push(i);switch(b%4){case 0:r=2147483648;break;case 1:r=t.charCodeAt(b-1)<<24|8388608;break;case 2:r=t.charCodeAt(b-2)<<24|t.charCodeAt(b-1)<<16|32768;break;case 3:r=t.charCodeAt(b-3)<<24|t.charCodeAt(b-2)<<16|t.charCodeAt(b-1)<<8|128}for(T.push(r);T.length%16!=14;)T.push(0);for(T.push(b>>>29),T.push(b<<3&4294967295),n=0;n<T.length;n+=16){for(r=0;r<16;r++)o[r]=T[n+r];for(r=16;r<=79;r++)o[r]=e(o[r-3]^o[r-8]^o[r-14]^o[r-16],1);for(g=s,v=a,p=u,d=l,m=f,r=0;r<=19;r++)S=e(g,5)+(v&p|~v&d)+m+o[r]+1518500249&4294967295,m=d,d=p,p=e(v,30),v=g,g=S;for(r=20;r<=39;r++)S=e(g,5)+(v^p^d)+m+o[r]+1859775393&4294967295,m=d,d=p,p=e(v,30),v=g,g=S;for(r=40;r<=59;r++)S=e(g,5)+(v&p|v&d|p&d)+m+o[r]+2400959708&4294967295,m=d,d=p,p=e(v,30),v=g,g=S;for(r=60;r<=79;r++)S=e(g,5)+(v^p^d)+m+o[r]+3395469782&4294967295,m=d,d=p,p=e(v,30),v=g,g=S;s=s+g&4294967295,a=a+v&4294967295,u=u+p&4294967295,l=l+d&4294967295,f=f+m&4294967295}return ct([s,a,u,l,f])}function lt(t){function e(kt,Pt){return kt>>>Pt|kt<<32-Pt}var o,s,n="length",r=Math.pow,i=r(2,32),a=lt,u=a.h,l=a.k;if(!l){u=[],l=[];for(var f={},g=2,v=0;v<64;g++)if(!f[g]){for(o=0;o<313;o+=g)f[o]=g;u[v]=r(g,.5)*i|0,l[v++]=r(g,1/3)*i|0}a.h=u=u.slice(0,8),a.k=l}var p=8*t[n];t+="\x80";for(var d=[];t[n]%64-56;)t+="\0";for(o=0;o<t[n];o++)s=t.charCodeAt(o),d[o>>2]|=s<<(3-o)%4*8;for(d[d[n]]=p/i|0,d[d[n]]=p,s=0;s<d[n];){var m=d.slice(s,s+=16),S=u;for(u=u.slice(0,8),o=0;o<64;o++){var b=m[o-15],T=m[o-2],U=u[0],q=u[4],he=u[7]+(e(q,6)^e(q,11)^e(q,25))+(q&u[5]^~q&u[6])+l[o]+(m[o]=o<16?m[o]:m[o-16]+(e(b,7)^e(b,18)^b>>>3)+m[o-7]+(e(T,17)^e(T,19)^T>>>10)|0);(u=[he+((e(U,2)^e(U,13)^e(U,22))+(U&u[1]^U&u[2]^u[1]&u[2]))|0].concat(u))[4]=u[4]+he|0}for(o=0;o<8;o++)u[o]=u[o]+S[o]|0}return ct(u,8)}function ct(t,e){var n="0123456789abcdef",r="";e??(e=t.length);for(var i=0;i<e;i++)for(var o=3;o>=0;o--){var s=t[i]>>(o<<3)&255;r+=n[s>>4],r+=n[15&s]}return r}var ft=new at,ht=function(){function t(e){this.addListener=this.on,this.off=this.removeListener,this.eventEmitter=e}return t.prototype.tryDisconnect=function(){var e=this.eventEmitter;return this.eventEmitter=ft,e!==ft},t.prototype.on=function(e,n){return this.eventEmitter.on(e,n),this},t.prototype.once=function(e,n){return this.eventEmitter.once(e,n),this},t.prototype.removeListener=function(e,n){return this.eventEmitter.removeListener(e,n),this},t.prototype.removeAllListeners=function(e){return this.eventEmitter.removeAllListeners(e),this},t.prototype.listeners=function(e){return this.eventEmitter.listeners(e)},t.prototype.listenerCount=function(e){return this.eventEmitter.listenerCount(e)},t.prototype.eventNames=function(){return this.eventEmitter.eventNames()},t.prototype.emit=function(e){for(var n,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return(n=this.eventEmitter).emit.apply(n,(0,h.pr)([e],r))},t}();function zt(t){for(var e=[],n=0,r=Object.keys(t);n<r.length;n++)e.push(t[r[n]]);return e}function _t(t){for(var e=[],n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];e.push([i,t[i]])}return e}function Yt(t){var e,n={};if(Array.isArray(t))for(var r=0,i=t;r<i.length;r++){var o=i[r];n[o[0]]=o[1]}else{if(!(typeof Symbol<"u"&&t?.[Symbol.iterator]))throw new TypeError("Object.fromEntries() requires a single iterable argument");for(var u=t[Symbol.iterator](),l=void 0;l=(e=u.next()).value,!e.done;)n[l[0]]=l[1]}return n}function gt(){var t=function(e){this.target=e};return t.prototype.deref=function(){return this.target},t.isFallback=!0,t}var vt=function(){return"function"==typeof WeakRef},D=(()=>{return(t=D||(D={}))[t.AutoPoll=0]="AutoPoll",t[t.LazyLoad=1]="LazyLoad",t[t.ManualPoll=2]="ManualPoll",D;var t})(),H=(()=>{return(t=H||(H={}))[t.Global=0]="Global",t[t.EuOnly=1]="EuOnly",H;var t})(),Ee=function(){function t(e,n,r,i,o){var s,a,u;if(this.requestTimeoutMs=3e4,this.baseUrlOverriden=!1,this.proxy="",this.offline=!1,!e)throw new Error("Invalid 'sdkKey' value");this.sdkKey=e,this.clientVersion=n,this.dataGovernance=null!==(s=r?.dataGovernance)&&void 0!==s?s:H.Global,this.baseUrl=this.dataGovernance===H.EuOnly?"https://cdn-eu.configcat.com":"https://cdn-global.configcat.com";var v,p,l=null!==(a=o?.())&&void 0!==a?a:new Gt,f=new ht(l),g=new(vt()?WeakRef:gt())(f);if(this.hooks={hooks:f,hooksWeakRef:g,emit:function(d){for(var m,S,b=[],T=1;T<arguments.length;T++)b[T-1]=arguments[T];return null!==(S=null===(m=this.hooksWeakRef.deref())||void 0===m?void 0:m.emit.apply(m,(0,h.pr)([d],b)))&&void 0!==S&&S}},r){if(v=r.logger,p=r.cache,r.requestTimeoutMs){if(r.requestTimeoutMs<0)throw new Error("Invalid 'requestTimeoutMs' value");this.requestTimeoutMs=r.requestTimeoutMs}r.baseUrl&&(this.baseUrl=r.baseUrl,this.baseUrlOverriden=!0),r.proxy&&(this.proxy=r.proxy),r.flagOverrides&&(this.flagOverrides=r.flagOverrides),r.defaultUser&&(this.defaultUser=r.defaultUser),r.offline&&(this.offline=r.offline),null===(u=r.setupHooks)||void 0===u||u.call(r,f)}this.logger=new Jt(v??new Ve,this.hooks),this.cache=p?new We(p,this.logger):i?i(this):new Ht}return t.prototype.yieldHooks=function(){var e=this.hooks,n=e.hooks;return delete e.hooks,n??new ht(new at)},t.prototype.getUrl=function(){return this.baseUrl+"/configuration-files/"+this.sdkKey+"/"+t.configFileName+"?sdk="+this.clientVersion},t.prototype.getCacheKey=function(){return ut(this.sdkKey+"_"+t.configFileName+"_"+F.serializationFormatVersion)},t.configFileName="config_v6.json",t}(),pt=function(t){function e(n,r,i,o,s,a){var u=t.call(this,n,r+"/a-"+i,o,s,a)||this;u.pollIntervalSeconds=60,u.maxInitWaitTimeSeconds=5,o&&(null!=o.pollIntervalSeconds&&(u.pollIntervalSeconds=o.pollIntervalSeconds),null!=o.maxInitWaitTimeSeconds&&(u.maxInitWaitTimeSeconds=o.maxInitWaitTimeSeconds));var l=2147483;if(!("number"==typeof u.pollIntervalSeconds&&1<=u.pollIntervalSeconds&&u.pollIntervalSeconds<=l))throw new Error("Invalid 'pollIntervalSeconds' value");if(!("number"==typeof u.maxInitWaitTimeSeconds&&u.maxInitWaitTimeSeconds<=l))throw new Error("Invalid 'maxInitWaitTimeSeconds' value");return u}return(0,h.ZT)(e,t),e}(Ee),dt=function(t){function e(n,r,i,o,s,a){return t.call(this,n,r+"/m-"+i,o,s,a)||this}return(0,h.ZT)(e,t),e}(Ee),yt=function(t){function e(n,r,i,o,s,a){var u=t.call(this,n,r+"/l-"+i,o,s,a)||this;if(u.cacheTimeToLiveSeconds=60,o&&null!=o.cacheTimeToLiveSeconds&&(u.cacheTimeToLiveSeconds=o.cacheTimeToLiveSeconds),!("number"==typeof u.cacheTimeToLiveSeconds&&1<=u.cacheTimeToLiveSeconds&&u.cacheTimeToLiveSeconds<=2147483647))throw new Error("Invalid 'cacheTimeToLiveSeconds' value");return u}return(0,h.ZT)(e,t),e}(Ee),w=(()=>{return(t=w||(w={}))[t.LocalOnly=0]="LocalOnly",t[t.LocalOverRemote=1]="LocalOverRemote",t[t.RemoteOverLocal=2]="RemoteOverLocal",w;var t})(),Zt=function(){function t(e,n){this.initialSettings=this.constructor.getCurrentSettings(e),n&&(this.map=e)}return t.getCurrentSettings=function(e){return Object.fromEntries(Object.entries(e).map(function(n){return[n[0],De.fromValue(n[1])]}))},t.prototype.getOverrides=function(){return Promise.resolve(this.getOverridesSync())},t.prototype.getOverridesSync=function(){return this.map?this.constructor.getCurrentSettings(this.map):this.initialSettings},t}(),$t=function t(e,n){this.dataSource=e,this.behaviour=n},Xt=function(t){function e(n,r){var i=t.call(this,n,r)||this;i.cacheTimeToLiveMs=1e3*r.cacheTimeToLiveSeconds;var o=i.syncUpWithCache();return i.readyPromise=i.getReadyPromise(o,function(s){return(0,h.mG)(i,void 0,void 0,function(){var a;return(0,h.Jh)(this,function(u){switch(u.label){case 0:return a=this.getCacheState,[4,s];case 1:return[2,a.apply(this,[u.sent()])]}})})}),i}return(0,h.ZT)(e,t),e.prototype.getConfig=function(){return(0,h.mG)(this,void 0,void 0,function(){function n(o,s){void 0===s&&(s=""),o.debug("LazyLoadConfigService.getConfig(): cache is empty or expired"+s+".")}var r,i;return(0,h.Jh)(this,function(o){switch(o.label){case 0:return this.options.logger.debug("LazyLoadConfigService.getConfig() called."),[4,this.options.cache.get(this.cacheKey)];case 1:return(r=o.sent()).isExpired(this.cacheTimeToLiveMs)?this.isOffline?[3,3]:(n(this.options.logger,", calling refreshConfigCoreAsync()"),[4,this.refreshConfigCoreAsync(r)]):[3,5];case 2:return i=o.sent(),r=i[1],[3,4];case 3:n(this.options.logger),o.label=4;case 4:return[2,r];case 5:return this.options.logger.debug("LazyLoadConfigService.getConfig(): cache is valid, returning from cache."),[2,r]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("LazyLoadConfigService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e.prototype.getCacheState=function(n){return n.isEmpty?I.NoFlagData:n.isExpired(this.cacheTimeToLiveMs)?I.HasCachedFlagDataOnly:I.HasUpToDateFlagData},e}(pe),Qt=function(t){function e(n,r){var i=t.call(this,n,r)||this,o=i.syncUpWithCache();return i.readyPromise=i.getReadyPromise(o,function(s){return(0,h.mG)(i,void 0,void 0,function(){var a;return(0,h.Jh)(this,function(u){switch(u.label){case 0:return a=this.getCacheState,[4,s];case 1:return[2,a.apply(this,[u.sent()])]}})})}),i}return(0,h.ZT)(e,t),e.prototype.getCacheState=function(n){return n.isEmpty?I.NoFlagData:I.HasCachedFlagDataOnly},e.prototype.getConfig=function(){return(0,h.mG)(this,void 0,void 0,function(){return(0,h.Jh)(this,function(n){switch(n.label){case 0:return this.options.logger.debug("ManualPollService.getConfig() called."),[4,this.options.cache.get(this.cacheKey)];case 1:return[2,n.sent()]}})})},e.prototype.refreshConfigAsync=function(){return this.options.logger.debug("ManualPollService.refreshConfigAsync() called."),t.prototype.refreshConfigAsync.call(this)},e}(pe),mt="<invalid value>",Oe="<invalid name>",Se="<invalid operator>",Et="<invalid reference>",Ot=function(){function t(e){this.eol=e,this.log="",this.indent=""}return t.prototype.resetIndent=function(){return this.indent="",this},t.prototype.increaseIndent=function(){return this.indent+="  ",this},t.prototype.decreaseIndent=function(){return this.indent=this.indent.slice(0,-2),this},t.prototype.newLine=function(e){return this.log+=this.eol+this.indent+(e??""),this},t.prototype.append=function(e){return this.log+=e,this},t.prototype.toString=function(){return this.log},t.prototype.appendUserConditionCore=function(e,n,r){return this.append("User."+e+" "+Ae(n)+" '"+(r??mt)+"'")},t.prototype.appendUserConditionString=function(e,n,r,i){return"string"!=typeof r?this.appendUserConditionCore(e,n):this.appendUserConditionCore(e,n,i?"<hashed value>":r)},t.prototype.appendUserConditionStringList=function(e,n,r,i){if(!ye(r))return this.appendUserConditionCore(e,n);var o="value",s="values",a=Ae(n);if(i)return this.append("User."+e+" "+a+" [<"+r.length+" hashed "+(1===r.length?o:s)+">]");var u=me(r,10,function(l){return", ... <"+l+" more "+(1===l?o:s)+">"});return this.append("User."+e+" "+a+" ["+u+"]")},t.prototype.appendUserConditionNumber=function(e,n,r,i){if("number"!=typeof r)return this.appendUserConditionCore(e,n);var s,o=Ae(n);return i&&!isNaN(s=new Date(1e3*r))?this.append("User."+e+" "+o+" '"+r+"' ("+s.toISOString()+" UTC)"):this.append("User."+e+" "+o+" '"+r+"'")},t.prototype.appendUserCondition=function(e){var n="string"==typeof e.comparisonAttribute?e.comparisonAttribute:Oe,r=e.comparator;switch(e.comparator){case c.TextIsOneOf:case c.TextIsNotOneOf:case c.TextContainsAnyOf:case c.TextNotContainsAnyOf:case c.SemVerIsOneOf:case c.SemVerIsNotOneOf:case c.TextStartsWithAnyOf:case c.TextNotStartsWithAnyOf:case c.TextEndsWithAnyOf:case c.TextNotEndsWithAnyOf:case c.ArrayContainsAnyOf:case c.ArrayNotContainsAnyOf:return this.appendUserConditionStringList(n,r,e.comparisonValue,!1);case c.SemVerLess:case c.SemVerLessOrEquals:case c.SemVerGreater:case c.SemVerGreaterOrEquals:case c.TextEquals:case c.TextNotEquals:return this.appendUserConditionString(n,r,e.comparisonValue,!1);case c.NumberEquals:case c.NumberNotEquals:case c.NumberLess:case c.NumberLessOrEquals:case c.NumberGreater:case c.NumberGreaterOrEquals:return this.appendUserConditionNumber(n,r,e.comparisonValue);case c.SensitiveTextIsOneOf:case c.SensitiveTextIsNotOneOf:case c.SensitiveTextStartsWithAnyOf:case c.SensitiveTextNotStartsWithAnyOf:case c.SensitiveTextEndsWithAnyOf:case c.SensitiveTextNotEndsWithAnyOf:case c.SensitiveArrayContainsAnyOf:case c.SensitiveArrayNotContainsAnyOf:return this.appendUserConditionStringList(n,r,e.comparisonValue,!0);case c.DateTimeBefore:case c.DateTimeAfter:return this.appendUserConditionNumber(n,r,e.comparisonValue,!0);case c.SensitiveTextEquals:case c.SensitiveTextNotEquals:return this.appendUserConditionString(n,r,e.comparisonValue,!0);default:return this.appendUserConditionCore(n,r,e.comparisonValue)}},t.prototype.appendPrerequisiteFlagCondition=function(e,n){var o=e.comparisonValue;return this.append("Flag '"+("string"!=typeof e.prerequisiteFlagKey?Oe:e.prerequisiteFlagKey in n?e.prerequisiteFlagKey:Et)+"' "+function tr(t){switch(t){case M.Equals:return"EQUALS";case M.NotEquals:return"NOT EQUALS";default:return Se}}(e.comparator)+" '"+re(o)+"'")},t.prototype.appendSegmentCondition=function(e){var n=e.segment,i=null==n?Et:"string"==typeof n.name&&n.name?n.name:Oe;return this.append("User "+St(e.comparator)+" '"+i+"'")},t.prototype.appendConditionResult=function(e){return this.append(""+e)},t.prototype.appendConditionConsequence=function(e){return this.append(" => ").appendConditionResult(e),e?this:this.append(", skipping the remaining AND conditions")},t.prototype.appendTargetingRuleThenPart=function(e,n){(n?this.newLine():this.append(" ")).append("THEN");var r=e.then;return this.append(te(r)?" % options":" '"+re(r.value)+"'")},t.prototype.appendTargetingRuleConsequence=function(e,n,r){return this.increaseIndent(),this.appendTargetingRuleThenPart(e,r).append(" => ").append(!0===n?"MATCH, applying rule":!1===n?"no match":n),this.decreaseIndent()},t}();function Ae(t){switch(t){case c.TextIsOneOf:case c.SensitiveTextIsOneOf:case c.SemVerIsOneOf:return"IS ONE OF";case c.TextIsNotOneOf:case c.SensitiveTextIsNotOneOf:case c.SemVerIsNotOneOf:return"IS NOT ONE OF";case c.TextContainsAnyOf:return"CONTAINS ANY OF";case c.TextNotContainsAnyOf:return"NOT CONTAINS ANY OF";case c.SemVerLess:case c.NumberLess:return"<";case c.SemVerLessOrEquals:case c.NumberLessOrEquals:return"<=";case c.SemVerGreater:case c.NumberGreater:return">";case c.SemVerGreaterOrEquals:case c.NumberGreaterOrEquals:return">=";case c.NumberEquals:return"=";case c.NumberNotEquals:return"!=";case c.DateTimeBefore:return"BEFORE";case c.DateTimeAfter:return"AFTER";case c.TextEquals:case c.SensitiveTextEquals:return"EQUALS";case c.TextNotEquals:case c.SensitiveTextNotEquals:return"NOT EQUALS";case c.TextStartsWithAnyOf:case c.SensitiveTextStartsWithAnyOf:return"STARTS WITH ANY OF";case c.TextNotStartsWithAnyOf:case c.SensitiveTextNotStartsWithAnyOf:return"NOT STARTS WITH ANY OF";case c.TextEndsWithAnyOf:case c.SensitiveTextEndsWithAnyOf:return"ENDS WITH ANY OF";case c.TextNotEndsWithAnyOf:case c.SensitiveTextNotEndsWithAnyOf:return"NOT ENDS WITH ANY OF";case c.ArrayContainsAnyOf:case c.SensitiveArrayContainsAnyOf:return"ARRAY CONTAINS ANY OF";case c.ArrayNotContainsAnyOf:case c.SensitiveArrayNotContainsAnyOf:return"ARRAY NOT CONTAINS ANY OF";default:return Se}}function be(t){return new Ot("").appendUserCondition(t).toString()}function St(t){switch(t){case R.IsIn:return"IS IN SEGMENT";case R.IsNotIn:return"IS NOT IN SEGMENT";default:return Se}}function re(t){return $(t)?t.toString():mt}var At=/^[0-9]+$/,J=function(t,e){var n=At.test(t),r=At.test(e);return n&&r&&(t=+t,e=+e),t===e?0:n&&!r?-1:r&&!n?1:t<e?-1:1},Te=256,ne=Number.MAX_SAFE_INTEGER||9007199254740991,Y=[],O=[],E={},rr=0,N=function(t,e){var n=rr++;E[t]=n,O[n]=e,Y[n]=new RegExp(e)};N("NUMERICIDENTIFIER","0|[1-9]\\d*"),N("NUMERICIDENTIFIERLOOSE","[0-9]+"),N("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),N("MAINVERSION","("+O[E.NUMERICIDENTIFIER]+")\\.("+O[E.NUMERICIDENTIFIER]+")\\.("+O[E.NUMERICIDENTIFIER]+")"),N("MAINVERSIONLOOSE","("+O[E.NUMERICIDENTIFIERLOOSE]+")\\.("+O[E.NUMERICIDENTIFIERLOOSE]+")\\.("+O[E.NUMERICIDENTIFIERLOOSE]+")"),N("PRERELEASEIDENTIFIER","(?:"+O[E.NUMERICIDENTIFIER]+"|"+O[E.NONNUMERICIDENTIFIER]+")"),N("PRERELEASEIDENTIFIERLOOSE","(?:"+O[E.NUMERICIDENTIFIERLOOSE]+"|"+O[E.NONNUMERICIDENTIFIER]+")"),N("PRERELEASE","(?:-("+O[E.PRERELEASEIDENTIFIER]+"(?:\\."+O[E.PRERELEASEIDENTIFIER]+")*))"),N("PRERELEASELOOSE","(?:-?("+O[E.PRERELEASEIDENTIFIERLOOSE]+"(?:\\."+O[E.PRERELEASEIDENTIFIERLOOSE]+")*))"),N("BUILDIDENTIFIER","[0-9A-Za-z-]+"),N("BUILD","(?:\\+("+O[E.BUILDIDENTIFIER]+"(?:\\."+O[E.BUILDIDENTIFIER]+")*))"),N("FULLPLAIN","v?"+O[E.MAINVERSION]+O[E.PRERELEASE]+"?"+O[E.BUILD]+"?"),N("FULL","^"+O[E.FULLPLAIN]+"$"),N("LOOSEPLAIN","[v=\\s]*"+O[E.MAINVERSIONLOOSE]+O[E.PRERELEASELOOSE]+"?"+O[E.BUILD]+"?"),N("LOOSE","^"+O[E.LOOSEPLAIN]+"$");var bt=function(){function t(e,n){if((!n||"object"!=typeof n)&&(n={loose:!!n,includePrerelease:!1}),e instanceof t){if(e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>Te)throw new TypeError("version is longer than "+Te+" characters");this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;var r=e.trim().match(n.loose?Y[E.LOOSE]:Y[E.FULL]);if(!r)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>ne||this.major<0)throw new TypeError("Invalid major version");if(this.minor>ne||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>ne||this.patch<0)throw new TypeError("Invalid patch version");this.prerelease=r[4]?r[4].split(".").map(function(i){if(/^[0-9]+$/.test(i)){var o=+i;if(o>=0&&o<ne)return o}return i}):[],this.build=r[5]?r[5].split("."):[],this.format()}return t.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},t.prototype.toString=function(){return this.version},t.prototype.compare=function(e){if(!(e instanceof t)){if("string"==typeof e&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)},t.prototype.compareMain=function(e){return e instanceof t||(e=new t(e,this.options)),J(this.major,e.major)||J(this.minor,e.minor)||J(this.patch,e.patch)},t.prototype.comparePre=function(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var n=0;do{var r=this.prerelease[n],i=e.prerelease[n];if(void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return J(r,i)}while(++n)},t.prototype.compareBuild=function(e){e instanceof t||(e=new t(e,this.options));var n=0;do{var r=this.build[n],i=e.build[n];if(void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return-1;if(r!==i)return J(r,i)}while(++n)},t.prototype.inc=function(e,n){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n),this.inc("pre",n);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",n),this.inc("pre",n);break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var r=this.prerelease.length;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);-1===r&&this.prerelease.push(0)}n&&(this.prerelease[0]===n?isNaN(this.prerelease[1])&&(this.prerelease=[n,0]):this.prerelease=[n,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this},t}(),Ie=function(t,e){if((!e||"object"!=typeof e)&&(e={loose:!!e,includePrerelease:!1}),t instanceof bt)return t;if("string"!=typeof t||t.length>Te)return null;if(!(e.loose?Y[E.LOOSE]:Y[E.FULL]).test(t))return null;try{return new bt(t,e)}catch{return null}},nr=function t(e,n,r,i){void 0===i&&(i={}),this.identifier=e,this.email=n,this.country=r,this.custom=i};function Tt(t,e){var n,r;switch(e){case"Identifier":return null!==(n=t.identifier)&&void 0!==n?n:"";case"Email":return t.email;case"Country":return t.country;default:return null===(r=t.custom)||void 0===r?void 0:r[e]}}var Ce=function(){function t(e,n,r,i){this.key=e,this.setting=n,this.user=r,this.settings=i}return Object.defineProperty(t.prototype,"visitedFlags",{get:function(){var e;return null!==(e=this.$visitedFlags)&&void 0!==e?e:this.$visitedFlags=[]},enumerable:!1,configurable:!0}),t.forPrerequisiteFlag=function(e,n,r){var i=new t(e,n,r.user,r.settings);return i.$visitedFlags=r.visitedFlags,i.logBuilder=r.logBuilder,i},t}(),It="The current targeting rule is ignored and the evaluation continues with the next rule.",Ne="cannot evaluate, User Object is missing",sr=function(t,e){return"cannot evaluate, the User."+t+" attribute is invalid ("+e+")"},ar=function(){function t(e){this.logger=e}return t.prototype.evaluate=function(e,n){this.logger.debug("RolloutEvaluator.evaluate() called.");var i,r=n.logBuilder;this.logger.isEnabled(y.Info)&&(n.logBuilder=r=new Ot(this.logger.eol),r.append("Evaluating '"+n.key+"'"),n.user&&r.append(" for User '"+JSON.stringify(function ir(t){var e,n={},r="Identifier",i="Email",o="Country";if(n[r]=null!==(e=t.identifier)&&void 0!==e?e:"",null!=t.email&&(n[i]=t.email),null!=t.country&&(n[o]=t.country),null!=t.custom)for(var s=[r,i,o],a=0,u=Object.entries(t.custom);a<u.length;a++){var l=u[a],f=l[0],g=l[1];null!=g&&s.indexOf(f)<0&&(n[f]=g)}return n}(n.user))+"'"),r.increaseIndent());try{var o=void 0,s=void 0;if(null!=e){var a=n.setting.type;if(a>=0&&!function cr(t,e){switch(e){case L.Boolean:return"boolean"==typeof t;case L.String:return"string"==typeof t;case L.Int:case L.Double:return"number"==typeof t;default:return!1}}(e,a))throw new TypeError("The type of a setting must match the type of the specified default value. Setting's type was "+L[a]+" but the default value's type was "+typeof e+". Please use a default value which corresponds to the setting type "+L[a]+". Learn more: https://configcat.com/docs/sdk-reference/js/#setting-type-mapping");s=typeof(i=(o=this.evaluateSetting(n)).selectedValue.value)==typeof e}else s=$(i=(o=this.evaluateSetting(n)).selectedValue.value);return s||Re(i),o}catch(u){throw r?.resetIndent().increaseIndent(),i=e,u}finally{r&&(r.newLine("Returning '"+i+"'.").decreaseIndent(),this.logger.settingEvaluated(r.toString()))}},t.prototype.evaluateSetting=function(e){var n,r=e.setting.targetingRules;if(r.length>0&&(n=this.evaluateTargetingRules(r,e)))return n;var i=e.setting.percentageOptions;return i.length>0&&(n=this.evaluatePercentageOptions(i,void 0,e))?n:{selectedValue:e.setting}},t.prototype.evaluateTargetingRules=function(e,n){var r=n.logBuilder;r?.newLine("Evaluating targeting rules and applying the first match if any:");for(var i=0;i<e.length;i++){var o=e[i],a=this.evaluateConditions(o.conditions,o,n.key,n);if(!0===a){if(!te(o.then))return{selectedValue:o.then,matchedTargetingRule:o};var u=o.then;r?.increaseIndent();var l=this.evaluatePercentageOptions(u,o,n);if(l)return r?.decreaseIndent(),l;r?.newLine(It).decreaseIndent()}else Z(a)&&r?.increaseIndent().newLine(It).decreaseIndent()}},t.prototype.evaluatePercentageOptions=function(e,n,r){var i,o=r.logBuilder;if(!r.user)return o?.newLine("Skipping % options because the User Object is missing."),void(r.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(r.key),r.isMissingUserObjectLogged=!0));var a,s=r.setting.percentageOptionsAttribute;if(null==s?(s="Identifier",a=null!==(i=r.user.identifier)&&void 0!==i?i:""):a=Tt(r.user,s),null==a)return o?.newLine("Skipping % options because the User."+s+" attribute is missing."),void(r.isMissingUserObjectAttributeLogged||(this.logger.userObjectAttributeIsMissingPercentage(r.key,s),r.isMissingUserObjectAttributeLogged=!0));o?.newLine("Evaluating % options based on the User."+s+" attribute:");var u=ut(r.key+Nt(a)),l=parseInt(u.substring(0,7),16)%100;o?.newLine("- Computing hash in the [0..99] range from User."+s+" => "+l+" (this value is sticky and consistent across all SDKs)");for(var f=0,g=0;g<e.length;g++){var v=e[g];if(!(l>=(f+=v.percentage)))return o?.newLine("- Hash value "+l+" selects % option "+(g+1)+" ("+v.percentage+"%), '"+re(v.value)+"'."),{selectedValue:v,matchedTargetingRule:n,matchedPercentageOption:v}}throw new Error("Sum of percentage option percentages is less than 100.")},t.prototype.evaluateConditions=function(e,n,r,i){var o=!0,s=i.logBuilder,a=!1;s?.newLine("- ");for(var u=0;u<e.length;u++){var l=e[u];switch(s&&(u?s.increaseIndent().newLine("AND "):s.append("IF ").increaseIndent()),l.type){case"UserCondition":o=this.evaluateUserCondition(l,r,i),a=e.length>1;break;case"PrerequisiteFlagCondition":o=this.evaluatePrerequisiteFlagCondition(l,i),a=!0;break;case"SegmentCondition":a=!Z(o=this.evaluateSegmentCondition(l,i))||o!==Ne||e.length>1;break;default:throw new Error}var f=!0===o;if(s&&((!n||e.length>1)&&s.appendConditionConsequence(f),s.decreaseIndent()),!f)break}return n&&s?.appendTargetingRuleConsequence(n,o,a),o},t.prototype.evaluateUserCondition=function(e,n,r){if(r.logBuilder?.appendUserCondition(e),!r.user)return r.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(r.key),r.isMissingUserObjectLogged=!0),Ne;var a,u,l,f,o=e.comparisonAttribute,s=Tt(r.user,o);if(null==s||""===s)return this.logger.userObjectAttributeIsMissingCondition(be(e),r.key,o),function(t){return"cannot evaluate, the User."+t+" attribute is missing"}(o);switch(e.comparator){case c.TextEquals:case c.TextNotEquals:return a=k(o,s,e,r.key,this.logger),this.evaluateTextEquals(a,e.comparisonValue,e.comparator===c.TextNotEquals);case c.SensitiveTextEquals:case c.SensitiveTextNotEquals:return a=k(o,s,e,r.key,this.logger),this.evaluateSensitiveTextEquals(a,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===c.SensitiveTextNotEquals);case c.TextIsOneOf:case c.TextIsNotOneOf:return a=k(o,s,e,r.key,this.logger),this.evaluateTextIsOneOf(a,e.comparisonValue,e.comparator===c.TextIsNotOneOf);case c.SensitiveTextIsOneOf:case c.SensitiveTextIsNotOneOf:return a=k(o,s,e,r.key,this.logger),this.evaluateSensitiveTextIsOneOf(a,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===c.SensitiveTextIsNotOneOf);case c.TextStartsWithAnyOf:case c.TextNotStartsWithAnyOf:return a=k(o,s,e,r.key,this.logger),this.evaluateTextSliceEqualsAnyOf(a,e.comparisonValue,!0,e.comparator===c.TextNotStartsWithAnyOf);case c.SensitiveTextStartsWithAnyOf:case c.SensitiveTextNotStartsWithAnyOf:return a=k(o,s,e,r.key,this.logger),this.evaluateSensitiveTextSliceEqualsAnyOf(a,e.comparisonValue,r.setting.configJsonSalt,n,!0,e.comparator===c.SensitiveTextNotStartsWithAnyOf);case c.TextEndsWithAnyOf:case c.TextNotEndsWithAnyOf:return a=k(o,s,e,r.key,this.logger),this.evaluateTextSliceEqualsAnyOf(a,e.comparisonValue,!1,e.comparator===c.TextNotEndsWithAnyOf);case c.SensitiveTextEndsWithAnyOf:case c.SensitiveTextNotEndsWithAnyOf:return a=k(o,s,e,r.key,this.logger),this.evaluateSensitiveTextSliceEqualsAnyOf(a,e.comparisonValue,r.setting.configJsonSalt,n,!1,e.comparator===c.SensitiveTextNotEndsWithAnyOf);case c.TextContainsAnyOf:case c.TextNotContainsAnyOf:return a=k(o,s,e,r.key,this.logger),this.evaluateTextContainsAnyOf(a,e.comparisonValue,e.comparator===c.TextNotContainsAnyOf);case c.SemVerIsOneOf:case c.SemVerIsNotOneOf:return"string"!=typeof(u=wt(o,s,e,r.key,this.logger))?this.evaluateSemVerIsOneOf(u,e.comparisonValue,e.comparator===c.SemVerIsNotOneOf):u;case c.SemVerLess:case c.SemVerLessOrEquals:case c.SemVerGreater:case c.SemVerGreaterOrEquals:return"string"!=typeof(u=wt(o,s,e,r.key,this.logger))?this.evaluateSemVerRelation(u,e.comparator,e.comparisonValue):u;case c.NumberEquals:case c.NumberNotEquals:case c.NumberLess:case c.NumberLessOrEquals:case c.NumberGreater:case c.NumberGreaterOrEquals:return l=function ur(t,e,n,r,i){return"number"==typeof e?e:"string"!=typeof e||isNaN(o=qe(e.replace(",",".")))&&"NaN"!==e.trim()?ie(i,n,r,t,"'"+e+"' is not a valid decimal number"):o;var o}(o,s,e,r.key,this.logger),"string"!=typeof l?this.evaluateNumberRelation(l,e.comparator,e.comparisonValue):l;case c.DateTimeBefore:case c.DateTimeAfter:return l=function lr(t,e,n,r,i){return e instanceof Date?e.getTime()/1e3:"number"==typeof e?e:"string"!=typeof e||isNaN(o=qe(e.replace(",",".")))&&"NaN"!==e.trim()?ie(i,n,r,t,"'"+e+"' is not a valid Unix timestamp (number of seconds elapsed since Unix epoch)"):o;var o}(o,s,e,r.key,this.logger),"string"!=typeof l?this.evaluateDateTimeRelation(l,e.comparisonValue,e.comparator===c.DateTimeBefore):l;case c.ArrayContainsAnyOf:case c.ArrayNotContainsAnyOf:return"string"!=typeof(f=Lt(o,s,e,r.key,this.logger))?this.evaluateArrayContainsAnyOf(f,e.comparisonValue,e.comparator===c.ArrayNotContainsAnyOf):f;case c.SensitiveArrayContainsAnyOf:case c.SensitiveArrayNotContainsAnyOf:return"string"!=typeof(f=Lt(o,s,e,r.key,this.logger))?this.evaluateSensitiveArrayContainsAnyOf(f,e.comparisonValue,r.setting.configJsonSalt,n,e.comparator===c.SensitiveArrayNotContainsAnyOf):f;default:throw new Error}},t.prototype.evaluateTextEquals=function(e,n,r){return e===n!==r},t.prototype.evaluateSensitiveTextEquals=function(e,n,r,i,o){return we(e,r,i)===n!==o},t.prototype.evaluateTextIsOneOf=function(e,n,r){return n.indexOf(e)>=0!==r},t.prototype.evaluateSensitiveTextIsOneOf=function(e,n,r,i,o){var s=we(e,r,i);return n.indexOf(s)>=0!==o},t.prototype.evaluateTextSliceEqualsAnyOf=function(e,n,r,i){for(var o=0;o<n.length;o++){var s=n[o];if(!(e.length<s.length)&&(r?e.lastIndexOf(s,0):e.indexOf(s,e.length-s.length))>=0)return!i}return i},t.prototype.evaluateSensitiveTextSliceEqualsAnyOf=function(e,n,r,i,o,s){for(var a=z(e),u=0;u<n.length;u++){var l=n[u],f=l.indexOf("_"),g=parseInt(l.slice(0,f));if(!(a.length<g)&&Ct(o?a.slice(0,g):a.slice(a.length-g),r,i)===l.slice(f+1))return!s}return s},t.prototype.evaluateTextContainsAnyOf=function(e,n,r){for(var i=0;i<n.length;i++)if(e.indexOf(n[i])>=0)return!r;return r},t.prototype.evaluateSemVerIsOneOf=function(e,n,r){for(var i=!1,o=0;o<n.length;o++){var s=n[o];if(s.length){var a=Ie(s.trim());if(!a)return!1;!i&&0===e.compare(a)&&(i=!0)}}return i!==r},t.prototype.evaluateSemVerRelation=function(e,n,r){var i=Ie(r.trim());if(!i)return!1;var o=e.compare(i);switch(n){case c.SemVerLess:return o<0;case c.SemVerLessOrEquals:return o<=0;case c.SemVerGreater:return o>0;case c.SemVerGreaterOrEquals:return o>=0}},t.prototype.evaluateNumberRelation=function(e,n,r){switch(n){case c.NumberEquals:return e===r;case c.NumberNotEquals:return e!==r;case c.NumberLess:return e<r;case c.NumberLessOrEquals:return e<=r;case c.NumberGreater:return e>r;case c.NumberGreaterOrEquals:return e>=r}},t.prototype.evaluateDateTimeRelation=function(e,n,r){return r?e<n:e>n},t.prototype.evaluateArrayContainsAnyOf=function(e,n,r){for(var i=0;i<e.length;i++)if(n.indexOf(e[i])>=0)return!r;return r},t.prototype.evaluateSensitiveArrayContainsAnyOf=function(e,n,r,i,o){for(var s=0;s<e.length;s++){var a=we(e[s],r,i);if(n.indexOf(a)>=0)return!o}return o},t.prototype.evaluatePrerequisiteFlagCondition=function(e,n){var r=n.logBuilder;r?.appendPrerequisiteFlagCondition(e,n.settings);var i=e.prerequisiteFlagKey,o=n.settings[i];if(n.visitedFlags.push(n.key),n.visitedFlags.indexOf(i)>=0){n.visitedFlags.push(i);var s=me(n.visitedFlags,void 0,void 0," -> ");throw new Error("Circular dependency detected between the following depending flags: "+s+".")}var a=Ce.forPrerequisiteFlag(i,o,n);r?.newLine("(").increaseIndent().newLine("Evaluating prerequisite flag '"+i+"':");var u=this.evaluateSetting(a);n.visitedFlags.pop();var f,l=u.selectedValue.value;if(typeof l!=typeof e.comparisonValue){if($(l))throw new Error("Type mismatch between comparison value '"+e.comparisonValue+"' and prerequisite flag '"+i+"'.");Re(l)}switch(e.comparator){case M.Equals:f=l===e.comparisonValue;break;case M.NotEquals:f=l!==e.comparisonValue;break;default:throw new Error}return r?.newLine("Prerequisite flag evaluation result: '"+re(l)+"'.").newLine("Condition (").appendPrerequisiteFlagCondition(e,n.settings).append(") evaluates to ").appendConditionResult(f).append(".").decreaseIndent().newLine(")"),f},t.prototype.evaluateSegmentCondition=function(e,n){var r=n.logBuilder;if(r?.appendSegmentCondition(e),!n.user)return n.isMissingUserObjectLogged||(this.logger.userObjectIsMissing(n.key),n.isMissingUserObjectLogged=!0),Ne;var i=e.segment;r?.newLine("(").increaseIndent().newLine("Evaluating segment '"+i.name+"':");var o=this.evaluateConditions(i.conditions,void 0,i.name,n),s=o;if(!Z(s))switch(e.comparator){case R.IsIn:break;case R.IsNotIn:s=!s;break;default:throw new Error}return r&&(r.newLine("Segment evaluation result: "),(Z(s)?r.append(s):r.append("User "+St(o?R.IsIn:R.IsNotIn))).append("."),r.newLine("Condition (").appendSegmentCondition(e).append(")"),(Z(s)?r.append(" failed to evaluate"):r.append(" evaluates to ").appendConditionResult(s)).append("."),r.decreaseIndent().newLine(")")),s},t}();function Z(t){return"string"==typeof t}function we(t,e,n){return Ct(z(t),e,n)}function Ct(t,e,n){return lt(t+z(e)+z(n))}function Nt(t){return"string"==typeof t?t:t instanceof Date?t.getTime()/1e3+"":ye(t)?JSON.stringify(t):t+""}function k(t,e,n,r,i){return"string"==typeof e||(e=Nt(e),i.userObjectAttributeIsAutoConverted(be(n),r,t,e)),e}function wt(t,e,n,r,i){var o;return"string"==typeof e&&(o=Ie(e.trim()))?o:ie(i,n,r,t,"'"+e+"' is not a valid semantic version")}function Lt(t,e,n,r,i){var o=e;if("string"==typeof o)try{o=JSON.parse(o)}catch{}return ye(o)?o:ie(i,n,r,t,"'"+e+"' is not a valid string array")}function ie(t,e,n,r,i){return t.userObjectAttributeIsInvalid(be(e),n,i,r),sr(r,i)}function Rt(t,e,n,r){return{key:t,value:e.selectedValue.value,variationId:e.selectedValue.variationId,fetchTime:n,user:r,isDefaultValue:!1,matchedTargetingRule:e.matchedTargetingRule,matchedPercentageOption:e.matchedPercentageOption}}function j(t,e,n,r,i,o){return{key:t,value:e,fetchTime:n,user:r,isDefaultValue:!0,errorMessage:i,errorException:o}}function oe(t,e,n,r,i,o,s){var a;if(!e)return a=s.configJsonIsNotPresentSingle(n,"defaultValue",r).toString(),j(n,r,P(o),i,a);var u=e[n];return u?Rt(n,t.evaluate(r,new Ce(n,u,i,e)),P(o),i):(a=s.settingEvaluationFailedDueToMissingKey(n,"defaultValue",r,me(Object.keys(e))).toString(),j(n,r,P(o),i,a))}function Ft(t,e,n,r,i,o){var s;if(!Le(e,i,o))return[[],s];for(var a=[],u=0,l=Object.entries(e);u<l.length;u++){var f=l[u],g=f[0],v=f[1],p=void 0;try{p=Rt(g,t.evaluate(null,new Ce(g,v,n,e)),P(r),n)}catch(m){s??(s=[]),s.push(m),p=j(g,null,P(r),n,V(m),m)}a.push(p)}return[a,s]}function Le(t,e,n){return!!t||(e.configJsonIsNotPresent(n),!1)}function $(t){return"boolean"==typeof t||"string"==typeof t||"number"==typeof t}function Re(t){throw new TypeError(null===t?"Setting value is null.":void 0===t?"Setting value is undefined.":"Setting value '"+t+"' is of an unsupported type ("+typeof t+").")}function P(t){return t?new Date(t.timestamp):void 0}var X=new(function(){function t(){this.instances={}}return t.prototype.getOrCreate=function(e,n){var r,i=this.instances[e.sdkKey];if(i&&(r=i[0].deref()))return[r,!0];var s={};r=new se(e,n,s);var a=vt()?WeakRef:gt();return this.instances[e.sdkKey]=[new a(r),s],[r,!1]},t.prototype.remove=function(e,n){var r=this.instances[e];if(r){var o=r[1],s=!!r[0].deref();if(!s||o===n)return delete this.instances[e],s}return!1},t.prototype.clear=function(){for(var e=[],n=0,r=Object.entries(this.instances);n<r.length;n++){var i=r[n],o=i[0],a=i[1][0].deref();a&&e.push(a),delete this.instances[o]}return e},t}()),se=function(){function t(e,n,r){var i;if(this.cacheToken=r,this.addListener=this.on,this.off=this.removeListener,!e)throw new Error("Invalid 'options' value");if(this.options=e,this.options.logger.debug("Initializing ConfigCatClient. Options: "+JSON.stringify(this.options)),!n)throw new Error("Invalid 'configCatKernel' value");if(!n.configFetcher)throw new Error("Invalid 'configCatKernel.configFetcher' value");this.hooks=e.yieldHooks(),e.defaultUser&&this.setDefaultUser(e.defaultUser),this.evaluator=new ar(e.logger),(null===(i=e.flagOverrides)||void 0===i?void 0:i.behaviour)!==w.LocalOnly?this.configService=e instanceof pt?new Bt(n.configFetcher,e):e instanceof dt?new Qt(n.configFetcher,e):e instanceof yt?new Xt(n.configFetcher,e):Ue(new Error("Invalid 'options' value")):this.hooks.emit("clientReady",I.HasLocalOverrideFlagDataOnly),this.suppressFinalize=fe(this,{sdkKey:e.sdkKey,cacheToken:r,configService:this.configService,logger:e.logger})}return Object.defineProperty(t,"instanceCache",{get:function(){return X},enumerable:!1,configurable:!0}),t.get=function(e,n,r,i){var o,s="Invalid 'sdkKey' value";if(!e)throw new Error(s);var u=new(n===D.AutoPoll?pt:n===D.ManualPoll?dt:n===D.LazyLoad?yt:Ue(new Error("Invalid 'pollingMode' value")))(e,i.sdkType,i.sdkVersion,r,i.defaultCacheFactory,i.eventEmitterFactory);if((null===(o=u.flagOverrides)||void 0===o?void 0:o.behaviour)!==w.LocalOnly&&!function hr(t,e){var n="configcat-proxy/";if(e&&t.length>n.length&&0===t.lastIndexOf(n,0))return!0;var r=t.split("/");switch(r.length){case 2:return 22===r[0].length&&22===r[1].length;case 3:return"configcat-sdk-1"===r[0]&&22===r[1].length&&22===r[2].length;default:return!1}}(e,u.baseUrlOverriden))throw new Error(s);var l=X.getOrCreate(u,i),f=l[0];return l[1]&&r&&u.logger.clientIsAlreadyCreated(e),f},t.finalize=function(e){var n;null===(n=e.logger)||void 0===n||n.debug("finalize() called"),e.cacheToken&&X.remove(e.sdkKey,e.cacheToken),t.close(e.configService,e.logger)},t.close=function(e,n,r){n?.debug("close() called"),r?.tryDisconnect(),e?.dispose()},t.prototype.dispose=function(){var e=this.options;e.logger.debug("dispose() called"),this.cacheToken&&X.remove(e.sdkKey,this.cacheToken),t.close(this.configService,e.logger,this.hooks),this.suppressFinalize()},t.disposeAll=function(){for(var n,r=0,i=X.clear();r<i.length;r++){var o=i[r];try{t.close(o.configService,o.options.logger,o.hooks),o.suppressFinalize()}catch(s){n??(n=[]),n.push(s)}}if(n)throw typeof AggregateError<"u"?new AggregateError(n):n.pop()},t.prototype.getValueAsync=function(e,n,r){return(0,h.mG)(this,void 0,void 0,function(){var i,o,s,u,l;return(0,h.Jh)(this,function(f){switch(f.label){case 0:this.options.logger.debug("getValueAsync() called."),ue(e),le(n),s=null,r??(r=this.defaultUser),f.label=1;case 1:return f.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return l=f.sent(),o=oe(this.evaluator,l[0],e,n,r,s=l[1],this.options.logger),i=o.value,[3,4];case 3:return u=f.sent(),this.options.logger.settingEvaluationErrorSingle("getValueAsync",e,"defaultValue",n,u),o=j(e,n,P(s),r,V(u),u),i=n,[3,4];case 4:return this.hooks.emit("flagEvaluated",o),[2,i]}})})},t.prototype.getValueDetailsAsync=function(e,n,r){return(0,h.mG)(this,void 0,void 0,function(){var i,o,a,u;return(0,h.Jh)(this,function(l){switch(l.label){case 0:this.options.logger.debug("getValueDetailsAsync() called."),ue(e),le(n),o=null,r??(r=this.defaultUser),l.label=1;case 1:return l.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return u=l.sent(),i=oe(this.evaluator,u[0],e,n,r,o=u[1],this.options.logger),[3,4];case 3:return a=l.sent(),this.options.logger.settingEvaluationErrorSingle("getValueDetailsAsync",e,"defaultValue",n,a),i=j(e,n,P(o),r,V(a),a),[3,4];case 4:return this.hooks.emit("flagEvaluated",i),[2,i]}})})},t.prototype.getAllKeysAsync=function(){return(0,h.mG)(this,void 0,void 0,function(){var e,n,r;return(0,h.Jh)(this,function(i){switch(i.label){case 0:this.options.logger.debug("getAllKeysAsync() called."),e="empty array",i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return Le(n=i.sent()[0],this.options.logger,e)?[2,Object.keys(n)]:[2,[]];case 3:return r=i.sent(),this.options.logger.settingEvaluationError("getAllKeysAsync",e,r),[2,[]];case 4:return[2]}})})},t.prototype.getAllValuesAsync=function(e){return(0,h.mG)(this,void 0,void 0,function(){var n,r,i,o,s,l,f,g,p;return(0,h.Jh)(this,function(d){switch(d.label){case 0:this.options.logger.debug("getAllValuesAsync() called."),n="empty array",e??(e=this.defaultUser),d.label=1;case 1:return d.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return s=d.sent(),p=Ft(this.evaluator,s[0],e,s[1],this.options.logger,n),o=p[1],r=(i=p[0]).map(function(m){return new G(m.key,m.value)}),[3,4];case 3:return l=d.sent(),this.options.logger.settingEvaluationError("getAllValuesAsync",n,l),[2,[]];case 4:for(o?.length&&this.options.logger.settingEvaluationError("getAllValuesAsync","evaluation result",typeof AggregateError<"u"?new AggregateError(o):o.pop()),f=0,g=i;f<g.length;f++)this.hooks.emit("flagEvaluated",g[f]);return[2,r]}})})},t.prototype.getAllValueDetailsAsync=function(e){return(0,h.mG)(this,void 0,void 0,function(){var n,r,i,o,u,l,f,v;return(0,h.Jh)(this,function(p){switch(p.label){case 0:this.options.logger.debug("getAllValueDetailsAsync() called."),n="empty array",e??(e=this.defaultUser),p.label=1;case 1:return p.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:return o=p.sent(),v=Ft(this.evaluator,o[0],e,o[1],this.options.logger,n),r=v[0],i=v[1],[3,4];case 3:return u=p.sent(),this.options.logger.settingEvaluationError("getAllValueDetailsAsync",n,u),[2,[]];case 4:for(i?.length&&this.options.logger.settingEvaluationError("getAllValueDetailsAsync","evaluation result",typeof AggregateError<"u"?new AggregateError(i):i.pop()),l=0,f=r;l<f.length;l++)this.hooks.emit("flagEvaluated",f[l]);return[2,r]}})})},t.prototype.getKeyAndValueAsync=function(e){return(0,h.mG)(this,void 0,void 0,function(){var n,r,i,o,s,a,u,l,f,g,v,p,d,m;return(0,h.Jh)(this,function(S){switch(S.label){case 0:this.options.logger.debug("getKeyAndValueAsync() called."),n="null",S.label=1;case 1:return S.trys.push([1,3,,4]),[4,this.getSettingsAsync()];case 2:if(!Le(r=S.sent()[0],this.options.logger,n))return[2,null];for(i=0,o=Object.entries(r);i<o.length;i++){if(a=(s=o[i])[0],e===(u=s[1]).variationId)return[2,new G(a,ce(u.value))];if((l=r[a].targetingRules)&&l.length>0)for(p=0;p<l.length;p++)if(te(f=l[p].then)){for(g=0;g<f.length;g++)if(e===(d=f[g]).variationId)return[2,new G(a,ce(d.value))]}else if(e===f.variationId)return[2,new G(a,ce(f.value))];if((v=r[a].percentageOptions)&&v.length>0)for(p=0;p<v.length;p++)if(e===(d=v[p]).variationId)return[2,new G(a,ce(d.value))]}return this.options.logger.settingForVariationIdIsNotPresent(e),[3,4];case 3:return m=S.sent(),this.options.logger.settingEvaluationError("getKeyAndValueAsync",n,m),[3,4];case 4:return[2,null]}})})},t.prototype.forceRefreshAsync=function(){return(0,h.mG)(this,void 0,void 0,function(){var n;return(0,h.Jh)(this,function(r){switch(r.label){case 0:if(this.options.logger.debug("forceRefreshAsync() called."),!this.configService)return[3,5];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.configService.refreshConfigAsync()];case 2:return[2,r.sent()[0]];case 3:return n=r.sent(),this.options.logger.forceRefreshError("forceRefreshAsync",n),[2,K.failure(V(n),n)];case 4:return[3,6];case 5:return[2,K.failure("Client is configured to use the LocalOnly override behavior, which prevents making HTTP requests.")];case 6:return[2]}})})},t.prototype.setDefaultUser=function(e){this.defaultUser=e},t.prototype.clearDefaultUser=function(){this.defaultUser=void 0},Object.defineProperty(t.prototype,"isOffline",{get:function(){var e,n;return null===(n=null===(e=this.configService)||void 0===e?void 0:e.isOffline)||void 0===n||n},enumerable:!1,configurable:!0}),t.prototype.setOnline=function(){this.configService?this.configService.setOnline():this.options.logger.configServiceMethodHasNoEffectDueToOverrideBehavior(w[w.LocalOnly],"setOnline")},t.prototype.setOffline=function(){var e;null===(e=this.configService)||void 0===e||e.setOffline()},t.prototype.waitForReady=function(){var e=this.configService;return e?e.readyPromise:Promise.resolve(I.HasLocalOverrideFlagDataOnly)},t.prototype.snapshot=function(){var e,n,r,o,a,u,i=this,s=function(){var g=i.options.cache.getInMemory();return[g.isEmpty?null:g.config.settings,g]},l=null===(o=this.options)||void 0===o?void 0:o.flagOverrides;if(l){var f=l.dataSource.getOverridesSync();switch(l.behaviour){case w.LocalOnly:return new ae(f,null,this);case w.LocalOverRemote:return u=(e=s())[1],new ae((0,h.pi)((0,h.pi)({},(a=e[0])??{}),f),u,this);case w.RemoteOverLocal:return a=(n=s())[0],u=n[1],new ae((0,h.pi)((0,h.pi)({},f),a??{}),u,this)}}return r=s(),new ae(a=r[0],u=r[1],this)},t.prototype.getSettingsAsync=function(){var e;return(0,h.mG)(this,void 0,void 0,function(){var n,r,i,o,s,u,l,f=this;return(0,h.Jh)(this,function(g){switch(g.label){case 0:return this.options.logger.debug("getSettingsAsync() called."),n=function(){return(0,h.mG)(f,void 0,void 0,function(){var v;return(0,h.Jh)(this,function(d){switch(d.label){case 0:return[4,this.configService.getConfig()];case 1:return[2,[(v=d.sent()).isEmpty?null:v.config.settings,v]]}})})},(r=null===(e=this.options)||void 0===e?void 0:e.flagOverrides)?(i=void 0,o=void 0,[4,r.dataSource.getOverrides()]):[3,7];case 1:switch(s=g.sent(),r.behaviour){case w.LocalOnly:return[3,2];case w.LocalOverRemote:return[3,3];case w.RemoteOverLocal:return[3,5]}return[3,7];case 2:return[2,[s,null]];case 3:case 5:case 7:return[4,n()];case 4:return u=g.sent(),o=u[1],[2,[(0,h.pi)((0,h.pi)({},(i=u[0])??{}),s),o]];case 6:return l=g.sent(),i=l[0],o=l[1],[2,[(0,h.pi)((0,h.pi)({},s),i??{}),o]];case 8:return[2,g.sent()]}})})},t.prototype.on=function(e,n){return this.hooks.on(e,n),this},t.prototype.once=function(e,n){return this.hooks.once(e,n),this},t.prototype.removeListener=function(e,n){return this.hooks.removeListener(e,n),this},t.prototype.removeAllListeners=function(e){return this.hooks.removeAllListeners(e),this},t.prototype.listeners=function(e){return this.hooks.listeners(e)},t.prototype.listenerCount=function(e){return this.hooks.listenerCount(e)},t.prototype.eventNames=function(){return this.hooks.eventNames()},t}(),ae=function(){function t(e,n,r){this.mergedSettings=e,this.remoteConfig=n,this.defaultUser=r.defaultUser,this.evaluator=r.evaluator,this.options=r.options,this.cacheState=n?r.configService.getCacheState(n):I.HasLocalOverrideFlagDataOnly}return Object.defineProperty(t.prototype,"fetchedConfig",{get:function(){var e=this.remoteConfig;return e&&!e.isEmpty?e.config:null},enumerable:!1,configurable:!0}),t.prototype.getAllKeys=function(){return this.mergedSettings?Object.keys(this.mergedSettings):[]},t.prototype.getValue=function(e,n,r){var i,o;this.options.logger.debug("Snapshot.getValue() called."),ue(e),le(n),r??(r=this.defaultUser);try{i=(o=oe(this.evaluator,this.mergedSettings,e,n,r,this.remoteConfig,this.options.logger)).value}catch(s){this.options.logger.settingEvaluationErrorSingle("Snapshot.getValue",e,"defaultValue",n,s),o=j(e,n,P(this.remoteConfig),r,V(s),s),i=n}return this.options.hooks.emit("flagEvaluated",o),i},t.prototype.getValueDetails=function(e,n,r){var i;this.options.logger.debug("Snapshot.getValueDetails() called."),ue(e),le(n),r??(r=this.defaultUser);try{i=oe(this.evaluator,this.mergedSettings,e,n,r,this.remoteConfig,this.options.logger)}catch(o){this.options.logger.settingEvaluationErrorSingle("Snapshot.getValueDetails",e,"defaultValue",n,o),i=j(e,n,P(this.remoteConfig),r,V(o),o)}return this.options.hooks.emit("flagEvaluated",i),i},t}(),G=function t(e,n){this.settingKey=e,this.settingValue=n};function ue(t){if(!t)throw new Error("Invalid 'key' value")}function le(t){if(null!=t&&!$(t))throw new TypeError("The default value must be boolean, number, string, null or undefined.")}function ce(t){return $(t)?t:Re(t)}var fe=function(t,e){if(typeof FinalizationRegistry<"u"){var n=new FinalizationRegistry(function(r){return se.finalize(r)});fe=function(r,i){var o={};return n.register(r,i,o),function(){return n.unregister(o)}}}else fe=function(){return function(){}};return fe(t,e)};function vr(){se.disposeAll()}function pr(t,e){return new Ve(t,e)}function dr(t,e,n){return new $t(new Zt(t,n),e)}!function Kt(){typeof Object.values>"u"&&(Object.values=zt),typeof Object.entries>"u"&&(Object.entries=_t),typeof Object.fromEntries>"u"&&(Object.fromEntries=Yt)}();var yr=function(){function t(e){this.storage=e}return t.setup=function(e,n){var r=(n??mr)();return r&&(e.defaultCacheFactory=function(i){return new We(new t(r),i.logger)}),e},t.prototype.set=function(e,n){this.storage.setItem(e,function Er(t){return t=(t=encodeURIComponent(t)).replace(/%([0-9A-F]{2})/g,function(e,n){return String.fromCharCode(parseInt(n,16))}),btoa(t)}(n))},t.prototype.get=function(e){var n=this.storage.getItem(e);if(n)return function Or(t){return t=(t=atob(t)).replace(/[%\x80-\xFF]/g,function(e){return"%"+e.charCodeAt(0).toString(16)}),decodeURIComponent(t)}(n)},t}();function mr(){var t="__configcat_localStorage_test";try{var e=window.localStorage;e.setItem(t,t);var n=void 0;try{n=e.getItem(t)}finally{e.removeItem(t)}if(n===t)return e}catch{}return null}var Sr=function(){function t(){}return t.prototype.handleStateChange=function(e,n,r){var i;try{if(4===e.readyState){var o=e.status,s=e.statusText;200===o?n({statusCode:o,reasonPhrase:s,eTag:null!==(i=e.getResponseHeader("ETag"))&&void 0!==i?i:void 0,body:e.responseText}):o&&n({statusCode:o,reasonPhrase:s})}}catch(u){r(u)}},t.prototype.fetchLogic=function(e,n){var r=this;return new Promise(function(i,o){try{e.logger.debug("HttpConfigFetcher.fetchLogic() called.");var s=new XMLHttpRequest;s.onreadystatechange=function(){return r.handleStateChange(s,i,o)},s.ontimeout=function(){return o(new Q("timeout",e.requestTimeoutMs))},s.onabort=function(){return o(new Q("abort"))},s.onerror=function(){return o(new Q("failure"))};var a=e.getUrl();n&&(a+="&ccetag="+encodeURIComponent(n)),s.open("GET",a,!0),s.timeout=e.requestTimeoutMs,s.send(null)}catch(u){o(u)}})},t}();const Ar="9.5.1";function Dt(t,e,n){return function gr(t,e,n,r){return se.get(t,e,n,r)}(t,e??D.AutoPoll,n,yr.setup({configFetcher:new Sr,sdkType:"ConfigCat-JS",sdkVersion:Ar}))}function br(t,e){return Dt(t,D.AutoPoll,e)}}}]);