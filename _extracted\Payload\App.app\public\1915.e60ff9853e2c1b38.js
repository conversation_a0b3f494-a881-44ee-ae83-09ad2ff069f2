(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1915],{21915:(p,i,n)=>{n.r(i),n.d(i,{OneSpanDigipassWeb:()=>c});var t=n(15861),_=n(17737);class c extends _.WebPlugin{multiDeviceActivateLicense(e){return(0,t.Z)(function*(){return{deviceCode:"devicecode_mock",dynamicVector:"dynamicVector_mock",staticVector:"staticVector_mock"}})()}multiDeviceActivateInstance(e){return(0,t.Z)(function*(){return{signatureCode:"signatureCode_mock",dynamicVector:"dynamicVector_mock",staticVector:"staticVector_mock"}})()}decryptSecureChannelMessageBody(e){return(0,t.Z)(function*(){return{decryptedBody:"decryptedBody"}})()}generateSignatureFromSecureChannelMessage(e){return(0,t.Z)(function*(){return{dynamicCode:"62613049"}})()}}},15861:(p,i,n)=>{function t(s,c,u,e,d,m,r){try{var a=s[m](r),o=a.value}catch(l){return void u(l)}a.done?c(o):Promise.resolve(o).then(e,d)}function _(s){return function(){var c=this,u=arguments;return new Promise(function(e,d){var m=s.apply(c,u);function r(o){t(m,e,d,r,a,"next",o)}function a(o){t(m,e,d,r,a,"throw",o)}r(void 0)})}}n.d(i,{Z:()=>_})}}]);