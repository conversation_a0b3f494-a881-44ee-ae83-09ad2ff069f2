(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1992],{12188:(M,y,e)=>{e.d(y,{I:()=>v,T:()=>c});var g=e(15861),u=e(87956),m=e(53113),a=e(98699);class d{constructor(l,t,o){this.biller=l,this.source=t,this.amount=o}}function P(n){return new d(n.biller,n.source,n.amount)}var p=e(71776),E=e(39904),b=e(87903),i=e(42168),h=e(84757),s=e(99877);let I=(()=>{class n{constructor(t){this.http=t}send(t){return(0,i.firstValueFrom)(this.http.post(E.bV.PAYMENTS.BILLER,function B(n){return[{acctIdFrom:n.source.id,acctNickname:n.source.nickname,acctTypeFrom:n.source.type,amt:String(n.amount),nie:n.biller.number,pmtCodServ:n.biller.companyId,toEntity:n.biller.companyName,toNickname:n.biller.nickname}]}(t)).pipe((0,h.map)(([o])=>(0,b.l1)(o,"SUCCESS")))).catch(o=>(0,b.rU)(o))}}return n.\u0275fac=function(t){return new(t||n)(s.\u0275\u0275inject(p.HttpClient))},n.\u0275prov=s.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var C=e(20691);let r=(()=>{class n extends C.Store{constructor(t){super({confirmation:!1}),t.subscribes(E.PU,()=>{this.reset()})}setBiller(t){this.reduce(o=>({...o,biller:t}))}getBiller(){return this.select(({biller:t})=>t)}setSource(t){this.reduce(o=>({...o,source:t}))}getSource(){return this.select(({source:t})=>t)}setAmount(t){this.reduce(o=>({...o,amount:t}))}getAmount(){return this.select(({amount:t})=>t)}getConfirmation(){return this.select(({confirmation:t})=>t)}}return n.\u0275fac=function(t){return new(t||n)(s.\u0275\u0275inject(u.Yd))},n.\u0275prov=s.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),v=(()=>{class n{constructor(t,o,D){this.repository=t,this.store=o,this.eventBusService=D}setBiller(t){try{return a.Either.success(this.store.setBiller(t))}catch({message:o}){return a.Either.failure({message:o})}}setSource(t){try{return a.Either.success(this.store.setSource(t))}catch({message:o}){return a.Either.failure({message:o})}}setAmount(t){try{return a.Either.success(this.store.setAmount(t))}catch({message:o}){return a.Either.failure({message:o})}}reset(){try{return a.Either.success(this.store.reset())}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,g.Z)(function*(){const o=P(t.store.currentState),D=yield t.execute(o);return t.eventBusService.emit(D.channel),a.Either.success({biller:o,status:D})})()}execute(t){try{return this.repository.send(t)}catch({message:o}){return Promise.resolve(m.LN.error(o))}}}return n.\u0275fac=function(t){return new(t||n)(s.\u0275\u0275inject(I),s.\u0275\u0275inject(r),s.\u0275\u0275inject(u.Yd))},n.\u0275prov=s.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var f=e(19799);let c=(()=>{class n{constructor(t,o,D){this.products=t,this.billers=o,this.store=D}source(t){var o=this;return(0,g.Z)(function*(){try{const D=yield o.products.requestAccountsForTransfer();let R=o.store.getBiller();return!R&&t&&(R=(yield o.billers.request()).find(({uuid:A})=>t===A),o.store.setBiller(R)),a.Either.success({biller:R,products:D})}catch({message:D}){return a.Either.failure({message:D})}})()}amount(){try{const t=this.store.getSource(),o=this.store.getBiller(),D=this.store.getAmount();return a.Either.success({amount:D,biller:o,source:t})}catch({message:t}){return a.Either.failure({message:t})}}confirmation(){try{const t=P(this.store.currentState);return a.Either.success({payment:t})}catch({message:t}){return a.Either.failure({message:t})}}}return n.\u0275fac=function(t){return new(t||n)(s.\u0275\u0275inject(u.hM),s.\u0275\u0275inject(f.e),s.\u0275\u0275inject(r))},n.\u0275prov=s.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},61566:(M,y,e)=>{e.d(y,{w:()=>P});var g=e(39904),u=e(95437),m=e(30263),a=e(12188),d=e(99877);let P=(()=>{class p{constructor(b,i,h){this.modalConfirmation=b,this.mboProvider=i,this.managerBiller=h}execute(b=!0){b?this.modalConfirmation.execute({title:"Cancelar pago",message:"\xbfEstas seguro que deseas cancelar el pago actualmente en progreso?",accept:{label:"Aceptar",click:()=>this.cancelConfirmed()},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerBiller.reset(),this.mboProvider.navigation.back(g.Z6.PAYMENTS.HOME)}}return p.\u0275fac=function(b){return new(b||p)(d.\u0275\u0275inject(m.$e),d.\u0275\u0275inject(u.ZL),d.\u0275\u0275inject(a.I))},p.\u0275prov=d.\u0275\u0275defineInjectable({token:p,factory:p.\u0275fac,providedIn:"root"}),p})()},11992:(M,y,e)=>{e.r(y),e.d(y,{MboPaymentBillerConfirmationPageModule:()=>f});var g=e(17007),u=e(78007),m=e(79798),a=e(30263),d=e(83651),B=e(15861),P=e(39904),p=e(95437),E=e(12188),b=e(61566),i=e(99877),h=e(10464),s=e(48774),I=e(17941),C=e(45542);const r=P.Z6.PAYMENTS.SERVICES;let v=(()=>{class c{constructor(l,t,o){this.mboProvider=l,this.requestConfiguration=t,this.cancelProvider=o,this.backAction={id:"btn_payment-biller-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(r.BILLER.AMOUNT)}},this.cancelAction={id:"btn_payment-biller-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_payment-biller-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(r.HOME)}}],this.amountActions=[{id:"btn_payment-biller-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(r.BILLER.AMOUNT)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(r.BILLER.RESULT)}initializatedConfiguration(){var l=this;return(0,B.Z)(function*(){(yield l.requestConfiguration.confirmation()).when({success:({payment:t})=>{l.payment=t}})})()}}return c.\u0275fac=function(l){return new(l||c)(i.\u0275\u0275directiveInject(p.ZL),i.\u0275\u0275directiveInject(E.T),i.\u0275\u0275directiveInject(b.w))},c.\u0275cmp=i.\u0275\u0275defineComponent({type:c,selectors:[["mbo-payment-biller-confirmation-page"]],decls:16,vars:10,consts:[[1,"mbo-payment-biller-confirmation-page__content","mbo-page__scroller"],[1,"mbo-payment-biller-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-payment-biller-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],[1,"mbo-payment-biller-confirmation-page__footer"],["id","btn_payment-biller-confirmation_submit","bocc-button","raised","prefixIcon","checking-account",3,"click"]],template:function(l,t){1&l&&(i.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),i.\u0275\u0275element(3,"bocc-header-form",2),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),i.\u0275\u0275text(7,"\xbfDeseas pagar?"),i.\u0275\u0275elementEnd(),i.\u0275\u0275elementStart(8,"div",6),i.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),i.\u0275\u0275elementEnd()()()(),i.\u0275\u0275elementStart(12,"div",10)(13,"button",11),i.\u0275\u0275listener("click",function(){return t.onSubmit()}),i.\u0275\u0275elementStart(14,"span"),i.\u0275\u0275text(15,"Pagar"),i.\u0275\u0275elementEnd()()()()),2&l&&(i.\u0275\u0275advance(3),i.\u0275\u0275property("leftAction",t.backAction)("rightAction",t.cancelAction),i.\u0275\u0275advance(6),i.\u0275\u0275property("title",null==t.payment||null==t.payment.biller?null:t.payment.biller.nickname)("subtitle",null==t.payment||null==t.payment.biller?null:t.payment.biller.number)("detail",null==t.payment||null==t.payment.biller?null:t.payment.biller.companyName)("actions",t.destinationActions),i.\u0275\u0275advance(1),i.\u0275\u0275property("amount",null==t.payment?null:t.payment.amount)("actions",t.amountActions),i.\u0275\u0275advance(1),i.\u0275\u0275property("title",null==t.payment||null==t.payment.source?null:t.payment.source.nickname)("subtitle",null==t.payment||null==t.payment.source?null:t.payment.source.number))},dependencies:[h.K,s.J,I.D,C.P],styles:["/*!\n * MBO PaymentBillerConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 01/Ago/2022\n * Updated: 11/Ene/2024\n*/mbo-payment-biller-confirmation-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-biller-confirmation-page .mbo-payment-biller-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-biller-confirmation-page .mbo-payment-biller-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-payment-biller-confirmation-page .mbo-payment-biller-confirmation-page__footer{padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-payment-biller-confirmation-page .mbo-payment-biller-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),c})(),f=(()=>{class c{}return c.\u0275fac=function(l){return new(l||c)},c.\u0275mod=i.\u0275\u0275defineNgModule({type:c}),c.\u0275inj=i.\u0275\u0275defineInjector({imports:[g.CommonModule,u.RouterModule.forChild([{path:"",component:v}]),m.KI,a.Jx,a.DM,a.B4,a.Dj,d.P6,a.P8]}),c})()},63674:(M,y,e)=>{e.d(y,{Eg:()=>p,Lo:()=>a,Wl:()=>d,ZC:()=>B,_f:()=>u,br:()=>P,tl:()=>m});var g=e(29306);const u={MAX_QR_DINAMIC:125e4,MAX_QR_STATIC:125e4,MIN_QR_STATIC:1e3},m=new g.lC("NONE","CLA_PAGO_MANUAL","CURRENCY",void 0,void 0,"0","COP","Pago manual",0),a={color:"success",key:"paid",label:"Pagada"},d={color:"alert",key:"pending",label:"Por pagar"},B={color:"danger",key:"expired",label:"Vencida"},P={color:"info",key:"recurring",label:"Pago recurrente"},p={color:"info",key:"programmed",label:"Programado"}},66067:(M,y,e)=>{e.d(y,{S6:()=>E,T2:()=>P,UQ:()=>b,mZ:()=>p});var g=e(39904),u=e(6472),a=e(63674),d=e(31707);class P{constructor(h,s,I,C,r,v,f,c,n,l,t){this.id=h,this.type=s,this.name=I,this.nickname=C,this.number=r,this.bank=v,this.isAval=f,this.isProtected=c,this.isOwner=n,this.ownerName=l,this.ownerDocument=t,this.hasErrorPreview=!1,this.categoryType="bussiness",this.belongOccidenteValue=!1,this.digitalValue=!1,this.currenciesValue=[g.y1],this.initialsName=(0,u.initials)(C),this.shortNumber=r.substring(r.length-4),this.descriptionNumber=`${I} ${r}`,this.belongOccidenteValue=this.bank.belongOccidente,this.statusValue={color:"info",label:"Habilitada"},this.icon={dark:v.logo,light:v.logo,standard:v.logo}}get legalName(){return this.ownerName}get documentCustomer(){return this.ownerDocument}get amount(){return 0}get nature(){return"credit"}get color(){return"NONE"}get logo(){return this.bank.logo}get isDigital(){return this.digitalValue}get isAvalGroup(){return this.isAval||this.bank.isOccidente}get publicNumber(){return this.number}get belongOccidente(){return this.belongOccidenteValue}get currencies(){return this.currenciesValue}get isRequiredInformation(){return this.bank.isOccidente&&!this.isProtected}set information(h){this.informationValue||(this.informationValue=h)}get information(){return this.informationValue}get status(){return this.informationValue?.status||this.statusValue}hasCurrency(h){return this.currenciesValue.includes(h)}}class p{constructor(h,s){this.id=h,this.type=s}}class E{constructor(h,s,I,C,r,v,f,c,n,l,t,o){this.uuid=h,this.number=s,this.nie=I,this.nickname=C,this.companyId=r,this.companyName=v,this.amount=f,this.registerDate=c,this.expirationDate=n,this.paid=l,this.statusCode=t,this.references=o,this.recurring=o.length>0,this.status=function B(i){switch(i){case d.U.EXPIRED:return a.ZC;case d.U.PENDING:return a.Wl;case d.U.PROGRAMMED:return a.Eg;case d.U.RECURRING:return a.br;default:return a.Lo}}(t)}get registerFormat(){return this.registerDate.dateFormat}get expirationFormat(){return this.expirationDate.dateFormat}}class b{constructor(h,s,I,C,r,v,f,c){this.uuid=h,this.number=s,this.nickname=I,this.companyId=C,this.companyName=r,this.city=v,this.amount=f,this.isBiller=c}}},19799:(M,y,e)=>{e.d(y,{e:()=>I,W:()=>C});var g=e(71776),u=e(39904),m=e(87956),a=e(98699),d=e(42168),B=e(84757),P=e(53113),p=e(33876),E=e(66067);var s=e(99877);let I=(()=>{class r{constructor(f,c){this.http=f,c.subscribes(u.PU,()=>{this.destroy()}),this.billers$=(0,a.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.BILLERS,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,B.map)(({content:n})=>n.map(l=>function h(r){return new E.UQ((0,p.v4)(),r.nie,r.nickname,r.orgIdNum,r.orgName,r.city,+r.amt,(0,a.parseBoolean)(r.biller))}(l))))))}request(){return this.billers$.resolve()}destroy(){this.billers$.reset()}}return r.\u0275fac=function(f){return new(f||r)(s.\u0275\u0275inject(g.HttpClient),s.\u0275\u0275inject(m.Yd))},r.\u0275prov=s.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})(),C=(()=>{class r{constructor(f,c){this.http=f,c.subscribes(u.PU,()=>{this.destroy()}),this.invoices$=(0,a.securePromise)(()=>(0,d.firstValueFrom)(this.http.get(u.bV.PAYMENTS.DEBTS.INVOICES,{params:{items:"100",order:"ASC",orderField:"nickname",page:"0"}}).pipe((0,B.map)(({content:n})=>n.map(l=>function i(r){const v=r.refInfo.map(f=>function b(r){return new E.mZ(r.refId,r.refType)}(f));return new E.S6((0,p.v4)(),r.invoiceNum,r.nie,r.nickName,r.orgIdNum,r.orgName,+r.totalCurAmt,new P.ou(r.effDt),new P.ou(r.expDt),(0,a.parseBoolean)(r.payDone),r.state,v)}(l))))))}request(){return this.invoices$.resolve()}destroy(){this.invoices$.reset()}}return r.\u0275fac=function(f){return new(f||r)(s.\u0275\u0275inject(g.HttpClient),s.\u0275\u0275inject(m.Yd))},r.\u0275prov=s.\u0275\u0275defineInjectable({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()},31707:(M,y,e)=>{e.d(y,{U:()=>g,f:()=>u});var g=(()=>{return(m=g||(g={})).RECURRING="1",m.EXPIRED="2",m.PENDING="3",m.PROGRAMMED="4",g;var m})(),u=(()=>{return(m=u||(u={})).BILLER="Servicio",m.NON_BILLER="Servicio",m.PSE="Servicio",m.TAX="Impuesto",m.LOAN="Obligaci\xf3n financiera",m.CREDIT_CARD="Obligaci\xf3n financiera",u;var m})()}}]);