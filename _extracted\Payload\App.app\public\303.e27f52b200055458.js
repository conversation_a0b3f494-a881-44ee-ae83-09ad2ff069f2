(self.webpackChunkapp=self.webpackChunkapp||[]).push([[303],{57929:(U,P,a)=>{a.d(P,{Be:()=>u,q3:()=>p,qQ:()=>g});const p=36,g=0,u=[{code:"0002015502010102125802CO5921DIEGO ANDRES CORREDOR49250103RBM0014CO.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124O2dhtQbToI1IP7xYOHkR1SUm0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099360041740013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064310002ES0121DIEGO ANDRES CORREDOR54061200006304C1DE",name:"DIEGO ANDRES CORREDOR",type:"onlyAccount"},{code:"000201550202010211560105802CO5922ALMACEN Y SASTRERIA *******************.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124bAK0LiWIA7a7GGPS3ZTGmmCv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601315238 DUITAMA8223010100014CO.COM.RBM.IVA503001099100104110013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573154033178070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122ALMACEN Y SASTRERIA IN6304EA05",name:"ALMACEN Y SASTRERIA IN",type:"onlyAccount"},{code:"000201550202010211560105802CO5917SACOS AZULES BOCC49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124cJbE3StP8jyiME/aY+8d/Qo80014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601505664 SAN PEDRO8223010100014CO.COM.RBM.IVA503001099353177830013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573108133170070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064270002ES0117SACOS AZULES BOCC6304C439",name:"SACOS AZULES BOCC",type:"onlyAccount"},{code:"00020101021249250014CO.COM.CRB.RED0103CRB50300013CO.COM.CRB.CU01090164845945204581453031705405150005802CO5914Cci*boxBurguer6011BOGOTA D.C.622501031530708000DE40808020080270016CO.COM.CRB.CANAL0103POS81250015CO.COM.CRB.CIVA01020282230014CO.COM.CRB.IVA0101083240015CO.COM.CRB.BASE0101084250015CO.COM.CRB.CINC01020285230014CO.COM.CRB.INC0101090300016CO.COM.CRB.TRXID010600015491260014CO.COM.CRB.SEC0104627c6304df69",name:"CCI BOX BURGER",type:"onlyCards"},{code:"00020155020201021256076750.005802CO5918PRUEBAS QR REDEBAN49250103RBM0014CO.COM.RBM.RED902701035360016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124oC4KvIdTb9ouPsgVLNsLPwj00014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.*********************.000015CO.COM.RBM.BASE62180708SRB0008208020084250102020015CO.COM.RBM.CINC52040004852601040.000014CO.COM.RBM.INC530317064280002ES0118PRUEBAS QR REDEBAN5405450006304FAD5",name:"PRUEBAS QR REDEBAN",type:"cardsAndAccounts"},{code:"00020155020201021256040.005802CO5912COMERCIO POS49250103RBM0014CO.COM.RBM.RED9028010432620016CO.COM.RBM.TRXID80270103POS0016CO.COM.RBM.CANAL91460124HYImT9C9mng/eqME88+mrObw0014CO.COM.RBM.SEC81250102020015CO.COM.RBM.CIVA601211001 BOGOTA822601040.000014CO.COM.RBM.IVA50290108102030400013CO.COM.RBM.********************.000015CO.COM.RBM.BASE5125010454220013CO.COM.RBM.CA62180708SRB0068308020084250102020015CO.COM.RBM.CINC52040000852601040.000014CO.COM.RBM.INC530317064220002ES0112COMERCIO POS5405112206304B678",name:"COMERCIO POS",type:"cardsAndAccounts"},{code:"0002010102115802CO5915BOGOTA BICYCLES49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80260102IM0016CO.COM.RBM.CANAL91270105477470014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA60131100100 BOGOT8226010419.00014CO.COM.RBM.IVA50290108165934100013CO.COM.RBM.*****************.COM.RBM.BASE62180708BL01222608020084250102030015CO.COM.RBM.CINC520459418523010100014CO.COM.RBM.INC530317064250002ES0115BOGOTA BICYCLES6304E56D",name:"BOGOTA BICYCLES",type:"cardsAndAccounts"},{code:"000201550202010211560105802CO5922Hierros de occidente m49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124eH1E5X5opSOneQRXjtmvYMIX0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601227099 BOJAYA8223010100014CO.COM.RBM.IVA503001099353192840013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444454070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064320002ES0122Hierros de occidente m63049CC9",name:"HIERROS OCCIDENTE M",type:"onlyAccount"},{code:"000201550202010211560105802CO5914CAMISAS BOGOTA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL914601244nOdGGoa7JhbdMHsdz6/ZTfw0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601550001 VILLAVICE8223010100014CO.COM.RBM.IVA503001099353189710013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573004444450070300008020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064240002ES0114CAMISAS BOGOTA63049B14",name:"CAMISAS BOGOTA",type:"onlyAccount"},{code:"000201550202010211560105802CO5911LA PLAZUELA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124kLbT32m0FcJ/Ws+o6IsRzz/C0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099236850430013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573215009881070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064210002ES0111LA PLAZUELA63041E06",name:"LA PLAZUELA",type:"onlyAccount"},{code:"000201550202010211560105802CO5912FLORES JUANA49250103RBM0014CO.COM.RBM.RED903001060000000016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124sz0Z69d6TSQ0H2oXwdNV1JzE0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601211001 BOGOTA8223010100014CO.COM.RBM.IVA503001099233753060013CO.COM.RBM.**************************.COM.RBM.BASE62300213+573124012500070300108020084250102030015CO.COM.RBM.CINC520400008523010100014CO.COM.RBM.INC530317064220002ES0112FLORES JUANA6304CD95",name:"FLORES JUANA",type:"onlyAccount"},{code:"0002015502010102115802CO5922COMERCIO DE SIEMPRE *******************.COM.RBM.RED903001060880010016CO.COM.RBM.TRXID80270103APP0016CO.COM.RBM.CANAL91460124YOCYxVWUOwVMrGNJJvp2u8Uv0014CO.COM.RBM.SEC81250102030015CO.COM.RBM.CIVA601366001 PEREIRA8223010100014CO.COM.RBM.IVA503001099102030400013CO.COM.RBM.**************************.COM.RBM.BASE6213070300008020084250102030015CO.COM.RBM.CINC520405118523010100014CO.COM.RBM.INC530317064320002ES0122COMERCIO DE SIEMPRE BC63040D54",name:"COMERCIO DE SIEMPRE BC",type:"onlyAccount"}]},69595:(U,P,a)=>{a.d(P,{tG:()=>m,bE:()=>M,PY:()=>B});var p=a(15861),g=a(87956),u=a(53113),o=a(98699),E=a(89148),x=a(57929),R=(()=>{return(n=R||(R={})).Static="11",n.Dinamic="12",R;var n})();const{CcaBuyAvailableCop:S}=E.Av;class I{constructor(c){this.name=c}}class h{constructor(c,e,t,s,i,C,f,V,z,Z,G,Y){this.reference=c,this.codeQr=e,this.type=t,this.merchant=s,this.baseAmount=i,this.ivaAmount=C,this.incAmount=f,this.tipAmount=V,this.totalAmount=z,this.betweenAccounts=Z,this.belongCommerce=G,this.approvalId=Y}get codeDinamic(){return this.type===R.Dinamic}get codeStatic(){return this.type===R.Static}}class r{constructor(c,e,t,s,i,C,f,V,z,Z,G){this.product=c,this.id=e,this.name=t,this.number=s,this.amountValue=i,this.logo=C,this.icon=f,this.color=V,this.requiredQuotas=z,this.numberQuotas=Z,this.type=G,this.shortNumber=s.substring(s.length-4)}get amount(){return this.amountValue}isAvailableAmount(c){return this.amountValue>=c}}class A extends r{constructor(c,e){super(c,e.id,e.name,e.number,c.amount,e.franchise?e.franchise.getLogoContrast(e.color):c.bank.logo,e.franchise?{dark:e.franchise.logo.dark,light:e.franchise.logo.light,standard:e.franchise.getLogoContrast(e.color)}:{dark:c.bank.logo,light:c.bank.logo,standard:c.bank.logo},e.color,!1,x.qQ,"debit")}}class l extends r{constructor(c,e){if(super(c,c.id,c.name,c.publicNumber,c.amount,c.logo,c.icon,c.color,!0,x.q3,"credit"),e){const t=e.getSection(S);this.amountValue=+t?.value||c.amount}}}class _{constructor(c,e,t,s,i){this.invoice=c,this.type=e,this.amount=t,this.source=s,this.quotas=i}}var F=a(71776),D=a(39904),T=a(87903),q=a(42168),N=a(84757),v=a(99877);let w=(()=>{class n{constructor(e,t){this.http=e,this.fingerprintService=t}read(e){var t=this;return(0,q.firstValueFrom)(this.http.post(D.bV.PAYMENTS.QR.READ,{metadata:e}).pipe((0,N.tap)(s=>{if(Object.keys(s).every(C=>!s[C]))throw new Error("Invalid QR")}),(0,N.switchMap)(function(){var s=(0,p.Z)(function*(i){const{acquirerCode:C,merchantCode:f}=i,V=("Redeban"===C||"RBM"===C)&&9===f.length&&"9"===f.charAt(0),z=!V&&(yield t.verifyCommerce(e));return function O(n,c,e,t){return new h(c.billingNumber||c.trnConsecutiveCode,n,c.qrType,new I(c.merchantName),+c.netTrxAmount,+c.ivaValue,+c.incValue,+c.tipValue,+c.totalTrxAmount,e,t,c.approvalId)}(e,i,V,z)});return function(i){return s.apply(this,arguments)}}())))}verifyCommerce(e){return(0,q.firstValueFrom)(this.http.post(D.bV.PAYMENTS.QR.COMMERCE,{metadata:e}).pipe((0,N.map)(t=>"320"===t?.msgRsHdr?.status?.serverStatusCode)))}send(e){return"card"===e.type?this.sendForQr(e):this.sendForAccount(e)}cancel(e){return(0,q.firstValueFrom)(this.http.post(D.bV.PAYMENTS.QR.CANCEL,{code:e.invoice.codeQr}).pipe((0,N.map)(t=>(0,T.l1)(t,"SUCCESS")))).catch(t=>(0,T.rU)(t))}sendForQr(e){return(0,q.firstValueFrom)(this.http.post(D.bV.PAYMENTS.QR.PAY,function y(n){return{code:n.invoice.codeQr,installments:n.quotas,origin:n.source.id}}(e)).pipe((0,N.map)(t=>(0,T.l1)(t,"SUCCESS")))).catch(t=>(0,T.rU)(t))}sendForAccount(e){var t=this;return(0,p.Z)(function*(){const s=yield t.fingerprintService.getInfo();return(0,q.firstValueFrom)(t.http.post(D.bV.PAYMENTS.QR.PAY_ACCOUNT,function L(n,c){return{code:n.invoice.codeQr,curAmt:{amt:n.amount,curCode:"COP"},deviceAdmin:c,origin:n.source.id}}(e,s)).pipe((0,N.map)(i=>[201,"201"].includes(i.msgRsHdr?.status?.statusCode)?new u.LN("SUCCESS",`Ref: ${i.approvalId}`):"397"===i.msgRsHdr?.status?.additionalStatus?.statusCode?new u.LN("INFO",i.msgRsHdr.status.additionalStatus.statusDesc):(0,T.l1)(i,"SUCCESS")))).catch(i=>(0,T.rU)(i))})()}}return n.\u0275fac=function(e){return new(e||n)(v.\u0275\u0275inject(F.HttpClient),v.\u0275\u0275inject(g.ew))},n.\u0275prov=v.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var d=a(20691);let b=(()=>{class n extends d.Store{constructor(e){super({amount:0,creditCards:[],debitCards:[],products:[],pay:!0,requiredQuotas:!0,type:"card"}),e.subscribes(D.PU,()=>{this.reset()})}setProducts(e){this.reduce(t=>({...t,products:e}))}getProducts(){return this.select(({products:e})=>e)}setCreditCards(e){this.reduce(t=>({...t,creditCards:e}))}addCreditCard(e){const t=this.getCreditCards();t.filter(({id:i})=>i===e.id).length||this.reduce(i=>({...i,creditCards:[...t,e]}))}getCreditCards(){return this.select(({creditCards:e})=>e)}setDebitCards(e){this.reduce(t=>({...t,debitCards:e}))}addDebitCard(e){const t=this.getDebitCards();t.filter(({id:i})=>i===e.id).length||this.reduce(i=>({...i,debitCards:[...t,e]}))}getDebitCards(){return this.select(({debitCards:e})=>e)}setInvoice(e,t=!0){this.reduce(s=>({...s,invoice:e,pay:t}))}getInvoice(){return this.select(({invoice:e})=>e)}setSourceCard(e){this.reduce(t=>({...t,source:e,requiredQuotas:e.requiredQuotas,type:"card"}))}setSourceProduct(e){this.reduce(t=>({...t,source:e,quotas:0,requiredQuotas:!1,type:"account"}))}getSource(){return this.select(({source:e})=>e)}selectForSource(){return this.select(({amount:e,creditCards:t,debitCards:s,invoice:i,products:C,source:f})=>({amount:i.codeDinamic?i.totalAmount:e,creditCards:t,codeStatic:i.codeStatic,debitCards:s,invoice:i,products:C,source:f}))}setQuotas(e){this.reduce(t=>({...t,quotas:e}))}getQuotas(){return this.select(({quotas:e})=>e)}setAmount(e){this.reduce(t=>({...t,amount:e}))}getAmount(){return this.select(({amount:e})=>e)}getPay(){return this.select(({pay:e})=>e)}selectForConfirmation(){return this.select(({amount:e,invoice:t,quotas:s,requiredQuotas:i,source:C})=>({amount:e,invoice:t,quotas:s,requiredQuotas:i,source:C}))}}return n.\u0275fac=function(e){return new(e||n)(v.\u0275\u0275inject(g.Yd))},n.\u0275prov=v.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),m=(()=>{class n{constructor(e,t,s){this.repository=e,this.store=t,this.eventBusService=s}setSourceCard(e){try{const t=this.store.getSource();return t&&t.type!==e.type&&this.store.setQuotas(e.numberQuotas),o.Either.success(this.store.setSourceCard(e))}catch{return o.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setSourceProduct(e){try{return o.Either.success(this.store.setSourceProduct(e))}catch{return o.Either.failure({message:"Ocurrio un error al tratar de establecer su selecci\xf3n"})}}setQuotas(e){try{return o.Either.success(this.store.setQuotas(e))}catch({message:t}){return o.Either.failure({message:t})}}setAmount(e){try{return o.Either.success(this.store.setAmount(e))}catch({message:t}){return o.Either.failure({message:t})}}reset(){try{return o.Either.success(this.store.reset())}catch({message:e}){return o.Either.failure({message:e})}}send(){var e=this;return(0,p.Z)(function*(){const t=function j(n){return new _(n.invoice,n.type,n.amount,n.source,n.quotas)}(e.store.currentState),s=yield e.execute(t);return e.eventBusService.emit(s.channel),o.Either.success({paymentQr:t,status:s})})()}execute(e){try{return this.repository.send(e)}catch({message:t}){return Promise.resolve(u.LN.error(t))}}}return n.\u0275fac=function(e){return new(e||n)(v.\u0275\u0275inject(w),v.\u0275\u0275inject(b),v.\u0275\u0275inject(g.Yd))},n.\u0275prov=v.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})(),M=(()=>{class n{constructor(e){this.store=e}source(){var e=this;return(0,p.Z)(function*(){try{return o.Either.success(e.store.selectForSource())}catch({message:t}){return o.Either.failure({message:t})}})()}cancel(){var e=this;return(0,p.Z)(function*(){try{const t=e.store.getInvoice();return o.Either.success({invoice:t})}catch({message:t}){return o.Either.failure({message:t})}})()}confirmation(){var e=this;return(0,p.Z)(function*(){try{return o.Either.success(e.store.selectForConfirmation())}catch({message:t}){return o.Either.failure({message:t})}})()}}return n.\u0275fac=function(e){return new(e||n)(v.\u0275\u0275inject(b))},n.\u0275prov=v.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var Q=a(70658);let B=(()=>{class n{constructor(e,t,s,i,C){this.products=e,this.productService=t,this.repository=s,this.store=i,this.scannerService=C}execute(){var e=this;return(0,p.Z)(function*(){try{const{code:t,cancelled:s,granted:i}=yield e.scanQrCode();return i||Q.N.navigatorEnabled?s?o.Either.failure({value:!1,message:"Escaneo del c\xf3digo QR cancelado para realizar pago"}):o.Either.success(t):o.Either.failure({value:!1,message:"Banca m\xf3vil no cuenta con permisos para escanear de c\xf3digo QR, por favor habilite los permisos y vuelva a intentarlo"})}catch({message:t}){return o.Either.failure({value:!0,message:t})}})()}payment(e){var t=this;return(0,p.Z)(function*(){try{return t.verifyInvoice(yield t.repository.read(e))}catch({message:s}){return t.store.reset(),o.Either.failure({message:s})}})()}cancel(e){var t=this;return(0,p.Z)(function*(){try{const s=yield t.repository.read(e);return s.approvalId?o.Either.success(t.store.setInvoice(s,!1)):o.Either.failure({message:"C\xf3digo escaneado para pago QR es inv\xe1lido"})}catch({message:s}){return t.store.reset(),o.Either.failure({message:s})}})()}scanQrCode(){var e=this;return(0,p.Z)(function*(){return e.scannerService.qrCode({orientation:"portrait"})})()}verifyInvoice(e){var t=this;return(0,p.Z)(function*(){try{if(e.betweenAccounts){const i=yield t.verifyAccount(!0);return i&&t.store.setSourceProduct(i),o.Either.success(t.store.setInvoice(e))}const s=yield t.verifyCards();if(!e.belongCommerce&&!s)throw Error("Actualmente no cuentas con tarjetas activas para realizar pago QR");if(s&&(t.store.setSourceCard(s),t.store.setQuotas(s.numberQuotas)),e.belongCommerce){const i=yield t.verifyAccount(!1);if(!i&&!s)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");i&&t.store.setSourceProduct(i)}else t.store.setProducts([]);return o.Either.success(t.store.setInvoice(e))}catch({message:s}){return o.Either.failure({message:s})}})()}verifyAccount(e){var t=this;return(0,p.Z)(function*(){const s=(yield t.products.requestAccountsForTransfer()).sort((C,f)=>C.amount>f.amount?-1:1);t.store.setProducts(s);const[i]=s;if(!i&&e)throw Error("Actualmente no cuentas con productos disponibles para realizar pago QR");return i})()}verifyCards(){var e=this;return(0,p.Z)(function*(){const[[t],[s]]=yield Promise.all([e.requestCreditCards(),e.requestDebitCards()]);return t||s})()}requestCreditCards(){var e=this;return(0,p.Z)(function*(){const t=yield e.products.requestCreditCards(),i=(yield Promise.all(t.map(C=>e.requestCreditCardInformation(C)))).filter(C=>(0,o.itIsDefined)(C)).sort((C,f)=>C.amount>f.amount?-1:1);return e.store.setCreditCards(i),i})()}requestCreditCardInformation(e){return this.productService.requestInformation(e).then(t=>t&&new l(e,t)).catch(()=>{})}requestDebitCards(){var e=this;return(0,p.Z)(function*(){const t=yield e.products.requestAccountsForTransfer(),i=(yield Promise.all(t.map(C=>e.requestDebitCardsInformation(C)))).reduce((C,f)=>C.concat(f),[]).sort((C,f)=>C.amount>f.amount?-1:1);return e.store.setDebitCards(i),i})()}requestDebitCardsInformation(e){return this.productService.requestDebitCards(e).then(t=>t.map(s=>new A(e,s))).catch(()=>[])}}return n.\u0275fac=function(e){return new(e||n)(v.\u0275\u0275inject(g.hM),v.\u0275\u0275inject(g.M5),v.\u0275\u0275inject(w),v.\u0275\u0275inject(b),v.\u0275\u0275inject(g.LQ))},n.\u0275prov=v.\u0275\u0275defineInjectable({token:n,factory:n.\u0275fac,providedIn:"root"}),n})()},50965:(U,P,a)=>{a.d(P,{q:()=>R});var p=a(30263),g=a(39904),u=a(95437),o=a(69595),E=a(99877);let R=(()=>{class S{constructor(h,r,A){this.modalConfirmation=h,this.mboProvider=r,this.managerQr=A}execute(h=!0){h?this.modalConfirmation.execute({title:"Abandonar pago",message:"\xbfEstas seguro que deseas cancelar el pago QR actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed()}},decline:{label:"Continuar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerQr.reset(),this.mboProvider.navigation.back(g.Z6.PAYMENTS.QR.SCAN)}}return S.\u0275fac=function(h){return new(h||S)(E.\u0275\u0275inject(p.$e),E.\u0275\u0275inject(u.ZL),E.\u0275\u0275inject(o.tG))},S.\u0275prov=E.\u0275\u0275defineInjectable({token:S,factory:S.\u0275fac,providedIn:"root"}),S})()},7747:(U,P,a)=>{a.d(P,{NJ:()=>E,w2:()=>A}),a(74221);var g=a(17007),u=a(30263),o=a(99877);let E=(()=>{class l{}return l.\u0275fac=function(O){return new(O||l)},l.\u0275mod=o.\u0275\u0275defineNgModule({type:l}),l.\u0275inj=o.\u0275\u0275defineInjector({imports:[g.CommonModule,u.Qg]}),l})();var x=a(15861),R=a(39904),S=a(95437),I=a(57929),h=a(69595);function r(l,_){if(1&l){const O=o.\u0275\u0275getCurrentView();o.\u0275\u0275elementStart(0,"div",3),o.\u0275\u0275listener("click",function(){const j=o.\u0275\u0275restoreView(O).$implicit,F=o.\u0275\u0275nextContext();return o.\u0275\u0275resetView(F.onSelect(j))}),o.\u0275\u0275elementStart(1,"div",4),o.\u0275\u0275element(2,"bocc-icon",5),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(3,"label",6),o.\u0275\u0275text(4),o.\u0275\u0275elementEnd(),o.\u0275\u0275elementStart(5,"span",7),o.\u0275\u0275text(6),o.\u0275\u0275elementEnd()()}if(2&l){const O=_.$implicit,y=o.\u0275\u0275nextContext();o.\u0275\u0275attribute("bocc-theme",y.getBoccTheme(O)),o.\u0275\u0275advance(4),o.\u0275\u0275textInterpolate(O.name),o.\u0275\u0275advance(2),o.\u0275\u0275textInterpolate(y.getLabelType(O))}}let A=(()=>{class l{constructor(O,y){this.mboProvider=O,this.scannerQr=y,this.qrCodes=I.Be,this.cancelAction={id:"btn_payment-select-qr-code_cancel",label:"Cancelar",click:()=>{this.portal?.close()}}}ngBoccPortal(O){this.portal=O}getLabelType({type:O}){switch(O){case"onlyAccount":return"Entre cuentas";case"onlyCards":return"Solo tarjetas";default:return"Adquiriencia"}}getBoccTheme({type:O}){switch(O){case"onlyAccount":return"blue";case"onlyCards":return"amathyst";default:return"success"}}onSelect({code:O}){var y=this;return(0,x.Z)(function*(){y.portal?.close(),y.mboProvider.loader.open("Verificando datos, por favor espere..."),(yield y.scannerQr.payment(O)).when({success:()=>{y.mboProvider.navigation.next(R.Z6.PAYMENTS.QR.CONFIRMATION)},failure:({message:L})=>{y.mboProvider.toast.error(L)}},()=>{y.mboProvider.loader.close()})})()}}return l.\u0275fac=function(O){return new(O||l)(o.\u0275\u0275directiveInject(S.ZL),o.\u0275\u0275directiveInject(h.PY))},l.\u0275cmp=o.\u0275\u0275defineComponent({type:l,selectors:[["mbo-payment-select-qr-code"]],standalone:!0,features:[o.\u0275\u0275StandaloneFeature],decls:3,vars:2,consts:[["title","Seleccionar QR",3,"rightAction"],[1,"mbo-payment-select-qr-code__list"],["class","mbo-payment-select-qr-code__element",3,"click",4,"ngFor","ngForOf"],[1,"mbo-payment-select-qr-code__element",3,"click"],[1,"mbo-payment-select-qr-code__avatar"],["icon","invoice-barcode"],[1,"smalltext-medium"],[1,"caption-semibold"]],template:function(O,y){1&O&&(o.\u0275\u0275element(0,"bocc-header-form",0),o.\u0275\u0275elementStart(1,"div",1),o.\u0275\u0275template(2,r,7,3,"div",2),o.\u0275\u0275elementEnd()),2&O&&(o.\u0275\u0275property("rightAction",y.cancelAction),o.\u0275\u0275advance(2),o.\u0275\u0275property("ngForOf",y.qrCodes))},dependencies:[g.CommonModule,g.NgForOf,u.Zl,u.Jx],styles:["/*!\n * MBO PaymentSelectQrCode Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 18/Jul/2024\n * Updated: 18/Jul/2024\n*/mbo-payment-select-qr-code{position:relative;display:flex;flex-direction:column}mbo-payment-select-qr-code bocc-header-form{position:sticky;top:0rem;z-index:var(--z-index-2);background:var(--overlay-white-80);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__list{display:grid;grid-template-columns:1fr 1fr;padding:var(--sizing-safe-footer-x8);box-sizing:border-box;row-gap:var(--sizing-x8);-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element{display:flex;border-radius:var(--sizing-x4);padding:var(--sizing-x6) var(--sizing-x4);box-sizing:border-box;justify-content:center;align-items:center;flex-direction:column;row-gap:var(--sizing-x2);border:var(--border-1-lighter-300)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element label{text-align:center;color:var(--color-carbon-darker-1000)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__element span{text-align:center;color:var(--color-carbon-lighter-700)}mbo-payment-select-qr-code .mbo-payment-select-qr-code__avatar{padding:var(--sizing-x2);border-radius:50%;background:var(--color-bocc-200);color:var(--color-bocc-700);margin-bottom:var(--sizing-x4)}\n"],encapsulation:2}),l})()},74221:(U,P,a)=>{a.d(P,{s:()=>I});var p=a(98699),u=a(99877),E=a(17007),R=a(55944);function S(h,r){if(1&h&&u.\u0275\u0275element(0,"img",7),2&h){const A=u.\u0275\u0275nextContext();u.\u0275\u0275property("src",null==A.source?null:A.source.icon.light,u.\u0275\u0275sanitizeUrl)}}let I=(()=>{class h{constructor(){this.amount=0}get hasAmount(){return(0,p.itIsDefined)(this.source.amount)&&this.source.amount>0}get theme(){return this.source.amount>=this.amount?"success":"danger"}}return h.\u0275fac=function(A){return new(A||h)},h.\u0275cmp=u.\u0275\u0275defineComponent({type:h,selectors:[["mbo-payment-qr-source-card"]],inputs:{source:"source",amount:"amount"},decls:9,vars:9,consts:[[1,"mbo-payment-qr-source-card__content"],[1,"mbo-payment-qr-source-card__header"],[1,"subtitle2-medium","truncate"],[3,"src",4,"ngIf"],[1,"mbo-payment-qr-source-card__account"],[1,"smalltext-medium","truncate"],[3,"badgeMode","decimals","amount"],[3,"src"]],template:function(A,l){1&A&&(u.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),u.\u0275\u0275text(3),u.\u0275\u0275elementEnd(),u.\u0275\u0275template(4,S,1,1,"img",3),u.\u0275\u0275elementEnd(),u.\u0275\u0275elementStart(5,"div",4)(6,"label",5),u.\u0275\u0275text(7),u.\u0275\u0275elementEnd(),u.\u0275\u0275element(8,"bocc-amount",6),u.\u0275\u0275elementEnd()()),2&A&&(u.\u0275\u0275classProp("mbo-payment-qr-source-card__content--disabled",!l.hasAmount),u.\u0275\u0275advance(3),u.\u0275\u0275textInterpolate(null==l.source?null:l.source.name),u.\u0275\u0275advance(1),u.\u0275\u0275property("ngIf",null==l.source?null:l.source.icon),u.\u0275\u0275advance(3),u.\u0275\u0275textInterpolate(null==l.source?null:l.source.shortNumber),u.\u0275\u0275advance(1),u.\u0275\u0275property("badgeMode",!0)("decimals",!0)("amount",null==l.source?null:l.source.amount),u.\u0275\u0275attribute("bocc-theme",l.theme))},dependencies:[E.NgIf,R.Q],styles:["/*!\n * MBO PaymentQrSourceCard Component\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 07/Jul/2023\n * Updated: 10/Ene/2024\n*/mbo-payment-qr-source-card{position:relative;width:100%;display:block}mbo-payment-qr-source-card .mbo-payment-qr-source-card__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x6);box-sizing:border-box;border:var(--border-1-lighter-300);border-radius:var(--sizing-x4)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__content--disabled{opacity:.5}mbo-payment-qr-source-card .mbo-payment-qr-source-card__header{position:relative;width:100%;display:flex;justify-content:space-between;align-items:center}mbo-payment-qr-source-card .mbo-payment-qr-source-card__header img{height:var(--sizing-x12);width:var(--sizing-x12);filter:var(--img-filter)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account{position:relative;display:flex;width:100%;justify-content:space-between;align-items:center}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account>label{color:var(--color-amathyst-700)}mbo-payment-qr-source-card .mbo-payment-qr-source-card__account bocc-amount{font-weight:var(--font-weight-semibold)}\n"],encapsulation:2}),h})()},50303:(U,P,a)=>{a.r(P),a.d(P,{MboPaymentQrSourcePageModule:()=>w});var p=a(17007),g=a(78007),u=a(79798),o=a(30263),E=a(7747),x=a(15861),R=a(39904),S=a(95437),I=a(69595),h=a(50965),r=a(99877),A=a(10464),l=a(48774),_=a(55944),O=a(52808),y=a(75283),L=a(74221);function j(d,b){if(1&d){const m=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"mbo-payment-qr-source-card",14),r.\u0275\u0275listener("click",function(){const B=r.\u0275\u0275restoreView(m).$implicit,n=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(n.onProduct(B))}),r.\u0275\u0275elementEnd()}if(2&d){const m=b.$implicit,M=r.\u0275\u0275nextContext(2);r.\u0275\u0275property("source",m)("amount",M.amount)}}function F(d,b){if(1&d&&(r.\u0275\u0275elementStart(0,"div",12),r.\u0275\u0275template(1,j,1,2,"mbo-payment-qr-source-card",13),r.\u0275\u0275elementEnd()),2&d){const m=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngForOf",m.products)}}function D(d,b){if(1&d){const m=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"mbo-payment-qr-source-card",14),r.\u0275\u0275listener("click",function(){const B=r.\u0275\u0275restoreView(m).$implicit,n=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(n.onCard(B))}),r.\u0275\u0275elementEnd()}if(2&d){const m=b.$implicit,M=r.\u0275\u0275nextContext(2);r.\u0275\u0275property("source",m)("amount",M.amount)}}function T(d,b){if(1&d&&(r.\u0275\u0275elementStart(0,"div",12),r.\u0275\u0275template(1,D,1,2,"mbo-payment-qr-source-card",13),r.\u0275\u0275elementEnd()),2&d){const m=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngForOf",m.creditCards)}}function q(d,b){if(1&d){const m=r.\u0275\u0275getCurrentView();r.\u0275\u0275elementStart(0,"mbo-payment-qr-source-card",14),r.\u0275\u0275listener("click",function(){const B=r.\u0275\u0275restoreView(m).$implicit,n=r.\u0275\u0275nextContext(2);return r.\u0275\u0275resetView(n.onCard(B))}),r.\u0275\u0275elementEnd()}if(2&d){const m=b.$implicit,M=r.\u0275\u0275nextContext(2);r.\u0275\u0275property("source",m)("amount",M.amount)}}function N(d,b){if(1&d&&(r.\u0275\u0275elementStart(0,"div",12),r.\u0275\u0275template(1,q,1,2,"mbo-payment-qr-source-card",13),r.\u0275\u0275elementEnd()),2&d){const m=r.\u0275\u0275nextContext();r.\u0275\u0275advance(1),r.\u0275\u0275property("ngForOf",m.debitCards)}}let v=(()=>{class d{constructor(m,M,Q,B){this.mboProvider=m,this.requestConfiguration=M,this.managerPaymentQr=Q,this.cancelProvider=B,this.creditCards=[],this.debitCards=[],this.products=[],this.tabs=[],this.amount=0,this.position=0,this.backAction={id:"btn_payment-qr-source_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(R.Z6.PAYMENTS.QR.CONFIRMATION)}},this.cancelAction={id:"btn_payment-qr-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}}}ngOnInit(){this.initializatedConfiguration()}onCard(m){m.amount>0&&this.managerPaymentQr.setSourceCard(m).when({success:()=>{this.mboProvider.navigation.back(R.Z6.PAYMENTS.QR.CONFIRMATION)}})}onProduct(m){m.amount>0&&this.managerPaymentQr.setSourceProduct(m).when({success:()=>{this.mboProvider.navigation.back(R.Z6.PAYMENTS.QR.CONFIRMATION)}})}initializatedConfiguration(){var m=this;return(0,x.Z)(function*(){(yield m.requestConfiguration.source()).when({success:({amount:M,creditCards:Q,debitCards:B,products:n})=>{m.creditCards=Q,m.products=n,m.debitCards=B,m.amount=M;const c=[];n.length&&c.push({label:"Cuentas",value:c.length}),Q.length&&c.push({label:"T. Cr\xe9dito",value:c.length}),B.length&&c.push({label:"T. D\xe9bito",value:c.length}),m.tabs=c}})})()}}return d.\u0275fac=function(m){return new(m||d)(r.\u0275\u0275directiveInject(S.ZL),r.\u0275\u0275directiveInject(I.bE),r.\u0275\u0275directiveInject(I.tG),r.\u0275\u0275directiveInject(h.q))},d.\u0275cmp=r.\u0275\u0275defineComponent({type:d,selectors:[["mbo-payment-qr-source-page"]],decls:18,vars:10,consts:[[1,"mbo-payment-qr-source-page__content","mbo-page__scroller"],[1,"mbo-payment-qr-source-page__header"],["title","Origen",3,"leftAction","rightAction"],[1,"mbo-payment-qr-source-page__body"],[1,"mbo-payment-qr-source-page__title","subtitle2-medium"],[1,"mbo-payment-qr-source-page__amount"],[1,"body2-medium"],[3,"amount","decimals"],[1,"mbo-payment-qr-source-page__subheader","overline-medium"],[1,"bocc-tab-header--invert",3,"tabs","value","valueChange"],[3,"position"],["class","bocc-tab-form__view",4,"ngIf"],[1,"bocc-tab-form__view"],[3,"source","amount","click",4,"ngFor","ngForOf"],[3,"source","amount","click"]],template:function(m,M){1&m&&(r.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),r.\u0275\u0275element(3,"bocc-header-form",2),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(4,"div",3)(5,"label",4),r.\u0275\u0275text(6," \xbfCon cual tarjeta deseas pagar? "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(7,"div",5)(8,"label",6),r.\u0275\u0275text(9,"Total a pagar"),r.\u0275\u0275elementEnd(),r.\u0275\u0275element(10,"bocc-amount",7),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(11,"label",8),r.\u0275\u0275text(12," PRODUCTOS DISPONIBLES PARA COMPRAS "),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(13,"bocc-tab-header",9),r.\u0275\u0275listener("valueChange",function(B){return M.position=B}),r.\u0275\u0275elementEnd(),r.\u0275\u0275elementStart(14,"bocc-tab-form",10),r.\u0275\u0275template(15,F,2,1,"div",11),r.\u0275\u0275template(16,T,2,1,"div",11),r.\u0275\u0275template(17,N,2,1,"div",11),r.\u0275\u0275elementEnd()()()()),2&m&&(r.\u0275\u0275advance(3),r.\u0275\u0275property("leftAction",M.backAction)("rightAction",M.cancelAction),r.\u0275\u0275advance(7),r.\u0275\u0275property("amount",M.amount)("decimals",!1),r.\u0275\u0275advance(3),r.\u0275\u0275property("tabs",M.tabs)("value",M.position),r.\u0275\u0275advance(1),r.\u0275\u0275property("position",M.position),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",M.products.length),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",M.creditCards.length),r.\u0275\u0275advance(1),r.\u0275\u0275property("ngIf",M.debitCards.length))},dependencies:[p.NgForOf,p.NgIf,A.K,l.J,_.Q,O.G,y.q,L.s],styles:["/*!\n * MBO PaymentQrSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 28/Oct/2022\n * Updated: 10/Ene/2024\n*/mbo-payment-qr-source-page{--pvt-body-rowgap: var(--sizing-x12);position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-payment-qr-source-page .mbo-payment-qr-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--pvt-body-rowgap);padding:0rem var(--sizing-form);box-sizing:border-box;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__body bocc-tab-header{border-bottom:var(--border-1-lighter-300)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__body .bocc-tab-form__view{display:flex;flex-direction:column;row-gap:var(--sizing-x6)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__title{text-align:center}mbo-payment-qr-source-page .mbo-payment-qr-source-page__subheader{color:var(--color-carbon-lighter-400);height:var(--sizing-x16);line-height:var(--sizing-x16)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__amount{position:relative;display:flex;width:100%;justify-content:space-between;align-items:center;padding:var(--sizing-x4) var(--sizing-x6);box-sizing:border-box;background:var(--overlay-lgrey-40);border-radius:var(--sizing-x4)}mbo-payment-qr-source-page .mbo-payment-qr-source-page__amount bocc-amount{font-weight:var(--font-weight-semibold)}@media screen and (max-height: 600px){mbo-payment-qr-source-page{--pvt-body-rowgap: var(--sizing-x8)}}\n"],encapsulation:2}),d})(),w=(()=>{class d{}return d.\u0275fac=function(m){return new(m||d)},d.\u0275mod=r.\u0275\u0275defineNgModule({type:d}),d.\u0275inj=r.\u0275\u0275defineInjector({imports:[p.CommonModule,g.RouterModule.forChild([{path:"",component:v}]),u.KI,o.Jx,o.Qg,o.Gf,o.qw,E.NJ,u.Aj,o.P8]}),d})()}}]);