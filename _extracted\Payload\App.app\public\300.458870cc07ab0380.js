(self.webpackChunkapp=self.webpackChunkapp||[]).push([[300],{10300:(s,l,o)=>{o.r(l),o.d(l,{MboAuthenticationErrorsModule:()=>h});var d=o(17007),a=o(78007),t=o(99877);const M=[{path:"",redirectTo:"default-message",pathMatch:"full"},{path:"default-message",loadChildren:()=>o.e(1562).then(o.bind(o,51562)).then(n=>n.MboErrorDefaultMessagePageModule)},{path:"service-failure",loadChildren:()=>o.e(9541).then(o.bind(o,39541)).then(n=>n.MboErrorServiceFailurePageModule)},{path:"channel-blocked",loadChildren:()=>o.e(3923).then(o.bind(o,53923)).then(n=>n.MboErrorChannelBlockedPageModule)},{path:"sim-invalid",loadChildren:()=>o.e(5412).then(o.bind(o,35412)).then(n=>n.MboErrorSimInvalidPageModule)},{path:"exceed-attempts",loadChildren:()=>o.e(2788).then(o.bind(o,52788)).then(n=>n.MboErrorExceedAttemptsPageModule)},{path:"max-devices",loadChildren:()=>o.e(4985).then(o.bind(o,74985)).then(n=>n.MboErrorMaxDevicesPageModule)}];let h=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=t.\u0275\u0275defineInjector({imports:[d.CommonModule,a.RouterModule.forChild(M)]}),n})()}}]);