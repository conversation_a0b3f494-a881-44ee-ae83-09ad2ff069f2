(self.webpackChunkapp=self.webpackChunkapp||[]).push([[364,5038],{86437:(G,T,a)=>{a.d(T,{f:()=>M});var g=a(97582),p=64,v=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),m=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],E=Math.pow(2,53)-1,h=function(){function w(){this.state=Int32Array.from(m),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return w.prototype.update=function(l){if(this.finished)throw new Error("Attempted to update an already finished hash.");var S=0,b=l.byteLength;if(this.bytesHashed+=b,8*this.bytesHashed>E)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;b>0;)this.buffer[this.bufferLength++]=l[S++],b--,this.bufferLength===p&&(this.hashBuffer(),this.bufferLength=0)},w.prototype.digest=function(){if(!this.finished){var l=8*this.bytesHashed,S=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),b=this.bufferLength;if(S.setUint8(this.bufferLength++,128),b%p>=56){for(var k=this.bufferLength;k<p;k++)S.setUint8(k,0);this.hashBuffer(),this.bufferLength=0}for(k=this.bufferLength;k<56;k++)S.setUint8(k,0);S.setUint32(56,Math.floor(l/4294967296),!0),S.setUint32(60,l),this.hashBuffer(),this.finished=!0}var F=new Uint8Array(32);for(k=0;k<8;k++)F[4*k]=this.state[k]>>>24&255,F[4*k+1]=this.state[k]>>>16&255,F[4*k+2]=this.state[k]>>>8&255,F[4*k+3]=this.state[k]>>>0&255;return F},w.prototype.hashBuffer=function(){for(var S=this.buffer,b=this.state,k=b[0],F=b[1],Y=b[2],q=b[3],ee=b[4],ce=b[5],se=b[6],I=b[7],R=0;R<p;R++){if(R<16)this.temp[R]=(255&S[4*R])<<24|(255&S[4*R+1])<<16|(255&S[4*R+2])<<8|255&S[4*R+3];else{var L=this.temp[R-2];this.temp[R]=(((L>>>17|L<<15)^(L>>>19|L<<13)^L>>>10)+this.temp[R-7]|0)+((((L=this.temp[R-15])>>>7|L<<25)^(L>>>18|L<<14)^L>>>3)+this.temp[R-16]|0)}var X=(((ee>>>6|ee<<26)^(ee>>>11|ee<<21)^(ee>>>25|ee<<7))+(ee&ce^~ee&se)|0)+(I+(v[R]+this.temp[R]|0)|0)|0,te=((k>>>2|k<<30)^(k>>>13|k<<19)^(k>>>22|k<<10))+(k&F^k&Y^F&Y)|0;I=se,se=ce,ce=ee,ee=q+X|0,q=Y,Y=F,F=k,k=X+te|0}b[0]+=k,b[1]+=F,b[2]+=Y,b[3]+=q,b[4]+=ee,b[5]+=ce,b[6]+=se,b[7]+=I},w}(),_=typeof Buffer<"u"&&Buffer.from?function(w){return Buffer.from(w,"utf8")}:w=>(new TextEncoder).encode(w);function U(w){return w instanceof Uint8Array?w:"string"==typeof w?_(w):ArrayBuffer.isView(w)?new Uint8Array(w.buffer,w.byteOffset,w.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(w)}var M=function(){function w(l){this.secret=l,this.hash=new h,this.reset()}return w.prototype.update=function(l){if(!function A(w){return"string"==typeof w?0===w.length:0===w.byteLength}(l)&&!this.error)try{this.hash.update(U(l))}catch(S){this.error=S}},w.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},w.prototype.digest=function(){return(0,g.mG)(this,void 0,void 0,function(){return(0,g.Jh)(this,function(l){return[2,this.digestSync()]})})},w.prototype.reset=function(){if(this.hash=new h,this.secret){this.outer=new h;var l=function P(w){var l=U(w);if(l.byteLength>p){var S=new h;S.update(l),l=S.digest()}var b=new Uint8Array(p);return b.set(l),b}(this.secret),S=new Uint8Array(p);S.set(l);for(var b=0;b<p;b++)l[b]^=54,S[b]^=92;for(this.hash.update(l),this.outer.update(S),b=0;b<l.byteLength;b++)l[b]=0}},w}()},95472:(G,T,a)=>{a.d(T,{N:()=>v});const g={},p={};for(let m=0;m<256;m++){let E=m.toString(16).toLowerCase();1===E.length&&(E=`0${E}`),g[m]=E,p[E]=m}function v(m){let E="";for(let h=0;h<m.byteLength;h++)E+=g[m[h]];return E}},23192:(G,T,a)=>{a.d(T,{SQ:()=>v,Xb:()=>h});var g=a(55502),p=a(92261),D=a(91935);const v=typeof Symbol<"u"?Symbol("amplify_default"):"@@amplify_default",m=new g.k("Hub");class E{constructor(U){this.listeners=new Map,this.protectedChannels=["core","auth","api","analytics","interactions","pubsub","storage","ui","xr"],this.name=U}_remove(U,A){const M=this.listeners.get(U);M?this.listeners.set(U,[...M.filter(({callback:P})=>P!==A)]):m.warn(`No listeners for ${U}`)}dispatch(U,A,M,P){"string"==typeof U&&this.protectedChannels.indexOf(U)>-1&&(P===v||m.warn(`WARNING: ${U} is protected and dispatching on it can have unintended consequences`));const w={channel:U,payload:{...A},source:M,patternInfo:[]};try{this._toListeners(w)}catch(l){m.error(l)}}listen(U,A,M="noname"){let P;if("function"!=typeof A)throw new D._({name:p.z2,message:"No callback supplied to Hub"});P=A;let w=this.listeners.get(U);return w||(w=[],this.listeners.set(U,w)),w.push({name:M,callback:P}),()=>{this._remove(U,P)}}_toListeners(U){const{channel:A,payload:M}=U,P=this.listeners.get(A);P&&P.forEach(w=>{m.debug(`Dispatching to ${A} with `,M);try{w.callback(U)}catch(l){m.error(l)}})}}const h=new E("__default__");new E("internal-hub")},55502:(G,T,a)=>{a.d(T,{k:()=>v});var g=a(92261),p=(()=>{return(m=p||(p={})).DEBUG="DEBUG",m.ERROR="ERROR",m.INFO="INFO",m.WARN="WARN",m.VERBOSE="VERBOSE",m.NONE="NONE",p;var m})();const D={VERBOSE:1,DEBUG:2,INFO:3,WARN:4,ERROR:5,NONE:6};let v=(()=>{class m{constructor(h,x=p.WARN){this.name=h,this.level=x,this._pluggables=[]}_padding(h){return h<10?"0"+h:""+h}_ts(){const h=new Date;return[this._padding(h.getMinutes()),this._padding(h.getSeconds())].join(":")+"."+h.getMilliseconds()}configure(h){return h?(this._config=h,this._config):this._config}_log(h,...x){let _=this.level;if(m.LOG_LEVEL&&(_=m.LOG_LEVEL),typeof window<"u"&&window.LOG_LEVEL&&(_=window.LOG_LEVEL),!(D[h]>=D[_]))return;let M=console.log.bind(console);h===p.ERROR&&console.error&&(M=console.error.bind(console)),h===p.WARN&&console.warn&&(M=console.warn.bind(console)),m.BIND_ALL_LOG_LEVELS&&(h===p.INFO&&console.info&&(M=console.info.bind(console)),h===p.DEBUG&&console.debug&&(M=console.debug.bind(console)));const P=`[${h}] ${this._ts()} ${this.name}`;let w="";if(1===x.length&&"string"==typeof x[0])w=`${P} - ${x[0]}`,M(w);else if(1===x.length)w=`${P} ${x[0]}`,M(P,x[0]);else if("string"==typeof x[0]){let l=x.slice(1);1===l.length&&(l=l[0]),w=`${P} - ${x[0]} ${l}`,M(`${P} - ${x[0]}`,l)}else w=`${P} ${x}`,M(P,x);for(const l of this._pluggables){const S={message:w,timestamp:Date.now()};l.pushLogs([S])}}log(...h){this._log(p.INFO,...h)}info(...h){this._log(p.INFO,...h)}warn(...h){this._log(p.WARN,...h)}error(...h){this._log(p.ERROR,...h)}debug(...h){this._log(p.DEBUG,...h)}verbose(...h){this._log(p.VERBOSE,...h)}addPluggable(h){h&&h.getCategoryName()===g.YG&&(this._pluggables.push(h),h.configure(this._config))}listPluggables(){return this._pluggables}}return m.LOG_LEVEL=null,m.BIND_ALL_LOG_LEVELS=!1,m})()},91396:(G,T,a)=>{a.d(T,{Cj:()=>te,QW:()=>he});var g=a(81220);const p=()=>typeof global<"u",v=()=>typeof window<"u",m=()=>typeof document<"u",E=()=>typeof process<"u",h=(Z,o)=>!!Object.keys(Z).find(i=>i.startsWith(o)),ce=[{platform:g.gQ.Expo,detectionMethod:function q(){return p()&&typeof global.expo<"u"}},{platform:g.gQ.ReactNative,detectionMethod:function Y(){return typeof navigator<"u"&&typeof navigator.product<"u"&&"ReactNative"===navigator.product}},{platform:g.gQ.NextJs,detectionMethod:function w(){return v()&&window.next&&"object"==typeof window.next}},{platform:g.gQ.Nuxt,detectionMethod:function S(){return v()&&(void 0!==window.__NUXT__||void 0!==window.$nuxt)}},{platform:g.gQ.Angular,detectionMethod:function k(){const Z=Boolean(m()&&document.querySelector("[ng-version]")),o=Boolean(v()&&typeof window.ng<"u");return Z||o}},{platform:g.gQ.React,detectionMethod:function x(){const Z=c=>c.startsWith("_react")||c.startsWith("__react");return m()&&Array.from(document.querySelectorAll("[id]")).some(c=>Object.keys(c).find(Z))}},{platform:g.gQ.VueJs,detectionMethod:function U(){return v()&&h(window,"__VUE")}},{platform:g.gQ.Svelte,detectionMethod:function M(){return v()&&h(window,"__SVELTE")}},{platform:g.gQ.WebUnknown,detectionMethod:function ee(){return v()}},{platform:g.gQ.NextJsSSR,detectionMethod:function l(){return p()&&(h(global,"__next")||h(global,"__NEXT"))}},{platform:g.gQ.NuxtSSR,detectionMethod:function b(){return p()&&typeof global.__NUXT_PATHS__<"u"}},{platform:g.gQ.ReactSSR,detectionMethod:function _(){return E()&&typeof process.env<"u"&&!!Object.keys(process.env).find(Z=>Z.includes("react"))}},{platform:g.gQ.VueJsSSR,detectionMethod:function A(){return p()&&h(global,"__VUE")}},{platform:g.gQ.AngularSSR,detectionMethod:function F(){return E()&&"object"==typeof process.env&&process.env.npm_lifecycle_script?.startsWith("ng ")||!1}},{platform:g.gQ.SvelteSSR,detectionMethod:function P(){return E()&&typeof process.env<"u"&&!!Object.keys(process.env).find(Z=>Z.includes("svelte"))}}];let I;const R=[];let L=!1;const X=1e3,te=()=>{if(!I){if(I=function se(){return ce.find(Z=>Z.detectionMethod())?.platform||g.gQ.ServerSideUnknown}(),L)for(;R.length;)R.pop()?.();else R.forEach(Z=>{Z()});ue(g.gQ.ServerSideUnknown,10),ue(g.gQ.WebUnknown,10)}return I},he=Z=>{L||R.push(Z)};function ue(Z,o){I===Z&&!L&&setTimeout(()=>{(function ie(){I=void 0})(),L=!0,setTimeout(te,X)},o)}},5919:(G,T,a)=>{a.d(T,{Zm:()=>M});var g=a(81220);const p="6.13.1";var D=a(91396);const v={},h="aws-amplify",x=P=>P.replace(/\+.*/,"");new class _{constructor(){this.userAgent=`${h}/${x(p)}`}get framework(){return(0,D.Cj)()}get isReactNative(){return this.framework===g.gQ.ReactNative||this.framework===g.gQ.Expo}observeFrameworkChanges(w){(0,D.QW)(w)}};const M=P=>(({category:P,action:w}={})=>{const l=[[h,x(p)]];if(P&&l.push([P,w]),l.push(["framework",(0,D.Cj)()]),P&&w){const S=((P,w)=>v[P]?.[w]?.additionalDetails)(P,w);S&&S.forEach(b=>{l.push(b)})}return l})(P).map(([S,b])=>S&&b?`${S}/${b}`:S).join(" ")},81220:(G,T,a)=>{a.d(T,{WD:()=>p,gQ:()=>g,gq:()=>m});var g=(()=>{return(l=g||(g={})).WebUnknown="0",l.React="1",l.NextJs="2",l.Angular="3",l.VueJs="4",l.Nuxt="5",l.Svelte="6",l.ServerSideUnknown="100",l.ReactSSR="101",l.NextJsSSR="102",l.AngularSSR="103",l.VueJsSSR="104",l.NuxtSSR="105",l.SvelteSSR="106",l.ReactNative="201",l.Expo="202",g;var l})(),p=(()=>{return(l=p||(p={})).AI="ai",l.API="api",l.Auth="auth",l.Analytics="analytics",l.DataStore="datastore",l.Geo="geo",l.InAppMessaging="inappmessaging",l.Interactions="interactions",l.Predictions="predictions",l.PubSub="pubsub",l.PushNotification="pushnotification",l.Storage="storage",p;var l})(),m=(()=>{return(l=m||(m={})).GraphQl="1",l.Get="2",l.Post="3",l.Put="4",l.Patch="5",l.Del="6",l.Head="7",m;var l})()},40454:(G,T,a)=>{a.d(T,{b:()=>D});const g={id:"aws",outputs:{dnsSuffix:"amazonaws.com"},regionRegex:"^(us|eu|ap|sa|ca|me|af)\\-\\w+\\-\\d+$",regions:["aws-global"]},p={partitions:[g,{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn"},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:["aws-cn-global"]}]},D=v=>{const{partitions:m}=p;for(const{regions:E,outputs:h,regionRegex:x}of m){const _=new RegExp(x);if(E.includes(v)||_.test(v))return h.dnsSuffix}return g.outputs.dnsSuffix}},6639:(G,T,a)=>{a.d(T,{Z:()=>l});var g=a(3454),p=a(15861),D=a(74109);a(86437),a(95472);const E=S=>new Date(Date.now()+S);var M=a(46570),P=a(4079),w=a(6990);const l=(0,P.V)(w.S,[M.n,g.d,({credentials:S,region:b,service:k,uriEscapePath:F=!0})=>{let Y;return(q,ee)=>function(){var ce=(0,p.Z)(function*(I){Y=Y??0;const R={credentials:"function"==typeof S?yield S({forceRefresh:!!ee?.isCredentialsExpired}):S,signingDate:E(Y),signingRegion:b,signingService:k,uriEscapePath:F},L=yield(0,D.C)(I,R),$=yield q(L),H=(({headers:S}={})=>S?.date??S?.Date??S?.["x-amz-date"])($);return H&&(Y=((S,b)=>((S,b)=>Math.abs(E(b).getTime()-S)>=3e5)(S,b)?S-Date.now():b)(Date.parse(H),Y)),$});return function se(I){return ce.apply(this,arguments)}}()}])},6990:(G,T,a)=>{a.d(T,{S:()=>E});var g=a(15861),p=a(91935),D=a(87199);const v=h=>{let x;return()=>(x||(x=h()),x)},m=h=>!["HEAD","GET","DELETE"].includes(h.toUpperCase()),E=function(){var h=(0,g.Z)(function*({url:x,method:_,headers:U,body:A},{abortSignal:M,cache:P,withCrossDomainCredentials:w}){let l;try{l=yield fetch(x,{method:_,headers:U,body:m(_)?A:void 0,signal:M,cache:P,credentials:w?"include":"same-origin"})}catch(F){throw F instanceof TypeError?new p._({name:D.Z.NetworkError,message:"A network error has occurred.",underlyingError:F}):F}const S={};return l.headers?.forEach((F,Y)=>{S[Y.toLowerCase()]=F}),{statusCode:l.status,headers:S,body:null,body:Object.assign(l.body??{},{text:v(()=>l.text()),blob:v(()=>l.blob()),json:v(()=>l.json())})}});return function(_,U){return h.apply(this,arguments)}}()},54473:(G,T,a)=>{a.d(T,{y:()=>m});var g=a(3454),p=a(46570),D=a(4079),v=a(6990);const m=(0,D.V)(v.S,[p.n,g.d])},62942:(G,T,a)=>{a.d(T,{z:()=>p});var g=a(15861);const p=(D,v,m,E)=>function(){var h=(0,g.Z)(function*(x,_){const U={...E,...x},A=yield U.endpointResolver(U,_),M=yield v(_,A),P=yield D(M,{...U});return m(P)});return function(x,_){return h.apply(this,arguments)}}()},4079:(G,T,a)=>{a.d(T,{V:()=>g});const g=(p,D)=>(v,m)=>{const E={};let h=x=>p(x,m);for(let x=D.length-1;x>=0;x--)h=(0,D[x])(m)(h,E);return h(v)}},67834:(G,T,a)=>{a.d(T,{j:()=>m});var g=a(15861),p=a(87199);const D=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch","BadRequestException"],v=A=>!!A&&D.includes(A),m=A=>function(){var M=(0,g.Z)(function*(P,w){const l=w??(yield A(P))??void 0,S=l?.code||l?.name,b=P?.statusCode;return{retryable:_(w)||x(b,S)||v(S)||U(b,S)}});return function(P,w){return M.apply(this,arguments)}}(),E=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException"],h=["TimeoutError","RequestTimeout","RequestTimeoutException"],x=(A,M)=>429===A||!!M&&E.includes(M),_=A=>[p.Z.NetworkError,"ERR_NETWORK"].includes(A?.name),U=(A,M)=>!!A&&[500,502,503,504].includes(A)||!!M&&h.includes(M)},79987:(G,T,a)=>{a.d(T,{k:()=>D});var g=a(75599);const p=3e5,D=v=>{const E=(0,g.k)(p)(v);return!1===E?p:E}},3454:(G,T,a)=>{a.d(T,{d:()=>D});var g=a(15861);const D=({maxAttempts:E=3,retryDecider:h,computeDelay:x,abortSignal:_})=>{if(E<1)throw new Error("maxAttempts must be greater than 0");return(U,A)=>function(){var M=(0,g.Z)(function*(w){let l,b,S=A.attemptsCount??0;const k=()=>{if(b)return m(b,S),b;throw m(l,S),l};for(;!_?.aborted&&S<E;){try{b=yield U(w),l=void 0}catch(q){l=q,b=void 0}S=(A.attemptsCount??0)>S?A.attemptsCount??0:S+1,A.attemptsCount=S;const{isCredentialsExpiredError:F,retryable:Y}=yield h(b,l,A);if(!Y)return k();if(A.isCredentialsExpired=!!F,!_?.aborted&&S<E){const q=x(S);yield v(q,_)}}if(_?.aborted)throw new Error("Request aborted.");return k()});return function P(w){return M.apply(this,arguments)}}()},v=(E,h)=>{if(h?.aborted)return Promise.resolve();let x,_;const U=new Promise(A=>{_=A,x=setTimeout(A,E)});return h?.addEventListener("abort",function A(M){clearTimeout(x),h?.removeEventListener("abort",A),_()}),U},m=(E,h)=>{"[object Object]"===Object.prototype.toString.call(E)&&(E.$metadata={...E.$metadata??{},attempts:h})}},74109:(G,T,a)=>{a.d(T,{C:()=>c});const g=n=>Object.keys(n).map(d=>d.toLowerCase()).sort().join(";"),M="X-Amz-Date".toLowerCase(),P="X-Amz-Security-Token".toLowerCase(),w="aws4_request",l="AWS4-HMAC-SHA256";var ee=a(86437),ce=a(95472);const se=(n,d)=>{const f=new ee.f(n??void 0);return f.update(d),f.digestSync()},I=(n,d)=>{const f=se(n,d);return(0,ce.N)(f)},R=n=>Object.entries(n).map(([d,f])=>({key:d.toLowerCase(),value:f?.trim().replace(/\s+/g," ")??""})).sort((d,f)=>d.key<f.key?-1:1).map(d=>`${d.key}:${d.value}\n`).join(""),L=n=>Array.from(n).sort(([d,f],[O,j])=>d===O?f<j?-1:1:d<O?-1:1).map(([d,f])=>`${$(d)}=${$(f)}`).join("&"),$=n=>encodeURIComponent(n).replace(/[!'()*]/g,H),H=n=>`%${n.charCodeAt(0).toString(16).toUpperCase()}`,X=(n,d=!0)=>n?d?encodeURIComponent(n).replace(/%2F/g,"/"):n:"/",te=n=>null==n?"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855":he(n)?I(null,n):"UNSIGNED-PAYLOAD",he=n=>"string"==typeof n||ArrayBuffer.isView(n)||ie(n),ie=n=>"function"==typeof ArrayBuffer&&n instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(n),c=(n,d)=>{const f=(({credentials:n,signingDate:d=new Date,signingRegion:f,signingService:O,uriEscapePath:j=!0})=>{const{accessKeyId:V,secretAccessKey:N,sessionToken:B}=n,{longDate:J,shortDate:le}=(n=>{const d=n.toISOString().replace(/[:-]|\.\d{3}/g,"");return{longDate:d,shortDate:d.slice(0,8)}})(d),z=((n,d,f)=>`${n}/${d}/${f}/${w}`)(le,f,O);return{accessKeyId:V,credentialScope:z,longDate:J,secretAccessKey:N,sessionToken:B,shortDate:le,signingRegion:f,signingService:O,uriEscapePath:j}})(d),{accessKeyId:O,credentialScope:j,longDate:V,sessionToken:N}=f,B={...n.headers};B.host=n.url.host,B[M]=V,N&&(B[P]=N);const J={...n,headers:B},le=((n,{credentialScope:d,longDate:f,secretAccessKey:O,shortDate:j,signingRegion:V,signingService:N,uriEscapePath:B})=>{const J=(({body:n,headers:d,method:f,url:O},j=!0)=>[f,X(O.pathname,j),L(O.searchParams),R(d),g(d),te(n)].join("\n"))(n,B),z=((n,d,f)=>[l,n,d,f].join("\n"))(f,d,I(null,J));return I(((n,d,f,O)=>{const V=se(`AWS4${n}`,d),N=se(V,f),B=se(N,O);return se(B,w)})(O,j,V,N),z)})(J,f),z=`Credential=${O}/${j}`,ne=`SignedHeaders=${g(B)}`;return B.authorization=`${l} ${z}, ${ne}, Signature=${le}`,J}},46570:(G,T,a)=>{a.d(T,{n:()=>p});var g=a(15861);const p=({userAgentHeader:D="x-amz-user-agent",userAgentValue:v=""})=>m=>function(){var E=(0,g.Z)(function*(x){if(0===v.trim().length)return yield m(x);{const _=D.toLowerCase();return x.headers[_]=x.headers[_]?`${x.headers[_]} ${v}`:v,yield m(x)}});return function h(x){return E.apply(this,arguments)}}()},54974:(G,T,a)=>{a.d(T,{e:()=>v,f:()=>D});var g=a(15861),p=a(97282);const D=function(){var m=(0,g.Z)(function*(E){if(!E||E.statusCode<300)return;const h=yield v(E),_=(M=>{const[P]=M.toString().split(/[,:]+/);return P.includes("#")?P.split("#")[1]:P})(E.headers["x-amzn-errortype"]??h.code??h.__type??"UnknownError"),A=new Error(h.message??h.Message??"Unknown error");return Object.assign(A,{name:_,$metadata:(0,p.B)(E)})});return function(h){return m.apply(this,arguments)}}(),v=function(){var m=(0,g.Z)(function*(E){if(!E.body)throw new Error("Missing response payload");const h=yield E.body.json();return Object.assign(h,{$metadata:(0,p.B)(E)})});return function(h){return m.apply(this,arguments)}}()},97282:(G,T,a)=>{a.d(T,{B:()=>g});const g=D=>{const{headers:v,statusCode:m}=D;return{...p(D)?D.$metadata:{},httpStatusCode:m,requestId:v["x-amzn-requestid"]??v["x-amzn-request-id"]??v["x-amz-request-id"],extendedRequestId:v["x-amz-id-2"],cfId:v["x-amz-cf-id"]}},p=D=>"object"==typeof D?.$metadata},92261:(G,T,a)=>{a.d(T,{Mt:()=>p,YG:()=>g,z2:()=>D});const g="Logging",p="x-amz-user-agent",D="NoHubcallbackProvidedException"},91935:(G,T,a)=>{a.d(T,{_:()=>g});class g extends Error{constructor({message:D,name:v,recoverySuggestion:m,underlyingError:E}){super(D),this.name=v,this.underlyingError=E,this.recoverySuggestion=m,this.constructor=g,Object.setPrototypeOf(this,g.prototype)}}},82847:(G,T,a)=>{a.d(T,{$:()=>p});var g=a(91935);const p=(D,v=g._)=>(m,E,h)=>{const{message:x,recoverySuggestion:_}=D[E];if(!m)throw new v({name:E,message:h?`${x} ${h}`:x,recoverySuggestion:_})}},70364:(G,T,a)=>{a.r(T),a.d(T,{Amplify:()=>M,AmplifyClassV6:()=>A,Cache:()=>rt,ConsoleLogger:()=>N.k,CookieStorage:()=>rn,Hub:()=>g.Xb,I18n:()=>be,ServiceWorker:()=>tn,clearCredentials:()=>l,decodeJWT:()=>p.xp,defaultStorage:()=>de,fetchAuthSession:()=>w,getCredentialsForIdentity:()=>d,getId:()=>o,sessionStorage:()=>Ue,sharedInMemoryStorage:()=>et,syncSessionStorage:()=>Le});var g=a(23192),p=a(74173);const D=u=>{const e=Reflect.ownKeys(u);for(const t of e){const r=u[t];(r&&"object"==typeof r||"function"==typeof r)&&D(r)}return Object.freeze(u)},v=Symbol("oauth-listener");a(83364);var E=a(52458),_=(a(86437),a(95472),a(15861));class U{configure(e,t){this.authConfig=e,this.authOptions=t}fetchAuthSession(e={}){var t=this;return(0,_.Z)(function*(){let r,s;const y=yield t.getTokens(e);return y?(s=y.accessToken?.payload?.sub,r=yield t.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({authConfig:t.authConfig,tokens:y,authenticated:!0,forceRefresh:e.forceRefresh})):r=yield t.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({authConfig:t.authConfig,authenticated:!1,forceRefresh:e.forceRefresh}),{tokens:y,credentials:r?.credentials,identityId:r?.identityId,userSub:s}})()}clearCredentials(){var e=this;return(0,_.Z)(function*(){yield e.authOptions?.credentialsProvider?.clearCredentialsAndIdentityId()})()}getTokens(e){var t=this;return(0,_.Z)(function*(){return(yield t.authOptions?.tokenProvider?.getTokens(e))??void 0})()}}class A{constructor(){this.oAuthListener=void 0,this.resourcesConfig={},this.libraryOptions={},this.Auth=new U}configure(e,t){const r=(0,E.h)(e);this.resourcesConfig=r,t&&(this.libraryOptions=t),this.resourcesConfig=D(this.resourcesConfig),this.Auth.configure(this.resourcesConfig.Auth,this.libraryOptions.Auth),g.Xb.dispatch("core",{event:"configure",data:this.resourcesConfig},"Configure",g.SQ),this.notifyOAuthListener()}getConfig(){return this.resourcesConfig}[v](e){this.resourcesConfig.Auth?.Cognito.loginWith?.oauth?e(this.resourcesConfig.Auth?.Cognito):this.oAuthListener=e}notifyOAuthListener(){!this.resourcesConfig.Auth?.Cognito.loginWith?.oauth||!this.oAuthListener||(this.oAuthListener(this.resourcesConfig.Auth?.Cognito),this.oAuthListener=void 0)}}const M=new A,w=u=>((u,e)=>u.Auth.fetchAuthSession(e))(M,u);function l(){return M.Auth.clearCredentials()}var S=a(97282),b=a(54974),k=a(62942),F=a(40454),Y=a(54473),q=a(79987),ee=a(67834),ce=a(99120),se=a(4079),I=a(5919),R=a(91396);const X=(0,se.V)(Y.y,[()=>u=>function(){var e=(0,_.Z)(function*(r){return r.headers["cache-control"]="no-store",u(r)});return function t(r){return e.apply(this,arguments)}}()]),te={service:"cognito-identity",endpointResolver:({region:u})=>({url:new ce.a(`https://cognito-identity.${u}.${(0,F.b)(u)}`)}),retryDecider:(0,ee.j)(b.f),computeDelay:q.k,userAgentValue:(0,I.Zm)(),cache:"no-store"};(0,R.QW)(()=>{te.userAgentValue=(0,I.Zm)()});const he=u=>({"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${u}`}),ie=({url:u},e,t)=>({headers:e,url:u,body:t,method:"POST"}),o=(0,k.z)(X,(u,e)=>{const t=he("GetId"),r=JSON.stringify(u);return ie(e,t,r)},function(){var u=(0,_.Z)(function*(e){if(e.statusCode>=300)throw yield(0,b.f)(e);return{IdentityId:(yield(0,b.e)(e)).IdentityId,$metadata:(0,S.B)(e)}});return function(t){return u.apply(this,arguments)}}(),te),c=function(){var u=(0,_.Z)(function*(e){if(e.statusCode>=300)throw yield(0,b.f)(e);{const t=yield(0,b.e)(e);return{IdentityId:t.IdentityId,Credentials:n(t.Credentials),$metadata:(0,S.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),n=({AccessKeyId:u,SecretKey:e,SessionToken:t,Expiration:r}={})=>({AccessKeyId:u,SecretKey:e,SessionToken:t,Expiration:r&&new Date(1e3*r)}),d=(0,k.z)(X,(u,e)=>{const t=he("GetCredentialsForIdentity"),r=JSON.stringify(u);return ie(e,t,r)},c,te);var f=a(87199),O=a(91935);class j extends O._{constructor(){super({name:f.Z.PlatformNotSupported,message:"Function not supported on current platform"})}}class V{constructor(e){this.storage=e}setItem(e,t){var r=this;return(0,_.Z)(function*(){if(!r.storage)throw new j;r.storage.setItem(e,t)})()}getItem(e){var t=this;return(0,_.Z)(function*(){if(!t.storage)throw new j;return t.storage.getItem(e)})()}removeItem(e){var t=this;return(0,_.Z)(function*(){if(!t.storage)throw new j;t.storage.removeItem(e)})()}clear(){var e=this;return(0,_.Z)(function*(){if(!e.storage)throw new j;e.storage.clear()})()}}var N=a(55502);class B{constructor(){this.storage=new Map}get length(){return this.storage.size}key(e){return e>this.length-1?null:Array.from(this.storage.keys())[e]}setItem(e,t){this.storage.set(e,t)}getItem(e){return this.storage.get(e)??null}removeItem(e){this.storage.delete(e)}clear(){this.storage.clear()}}const J=new N.k("CoreStorageUtils"),le=()=>{try{if(typeof window<"u"&&window.localStorage)return window.localStorage}catch{J.info("localStorage not found. InMemoryStorage is used as a fallback.")}return new B},z=()=>{try{if(typeof window<"u"&&window.sessionStorage)return window.sessionStorage.getItem("test"),window.sessionStorage;throw new Error("sessionStorage is not defined")}catch{return J.info("sessionStorage not found. InMemoryStorage is used as a fallback."),new B}};class ge{constructor(e){this._storage=e}get storage(){if(!this._storage)throw new j;return this._storage}setItem(e,t){this.storage.setItem(e,t)}getItem(e){return this.storage.getItem(e)}removeItem(e){this.storage.removeItem(e)}clear(){this.storage.clear()}}const de=new class ne extends V{constructor(){super(le())}},Ue=new class fe extends V{constructor(){super(z())}},Le=new class pe extends ge{constructor(){super(z())}},et=new V(new B),Ie={keyPrefix:"aws-amplify-cache",capacityInBytes:1048576,itemMaxSize:21e4,defaultTTL:2592e5,defaultPriority:5,warningThreshold:.8},Ce="CurSize";function $e(u){let e=0;e=u.length;for(let t=u.length;t>=0;t-=1){const r=u.charCodeAt(t);r>127&&r<=2047?e+=1:r>2047&&r<=65535&&(e+=2),r>=56320&&r<=57343&&(t-=1)}return e}function Ae(){return(new Date).getTime()}const Re=u=>`${u}${Ce}`;var Oe=a(82847),Q=(()=>{return(u=Q||(Q={})).NoCacheItem="NoCacheItem",u.NullNextNode="NullNextNode",u.NullPreviousNode="NullPreviousNode",Q;var u})();const Ge=(0,Oe.$)({[Q.NoCacheItem]:{message:"Item not found in the cache storage."},[Q.NullNextNode]:{message:"Next node is null."},[Q.NullPreviousNode]:{message:"Previous node is null."}}),re=new N.k("StorageCache");class Fe{constructor({config:e,keyValueStorage:t}){this.config={...Ie,...e},this.keyValueStorage=t,this.sanitizeConfig()}getModuleName(){return"Cache"}configure(e){return e&&(e.keyPrefix&&re.warn("keyPrefix can not be re-configured on an existing Cache instance."),this.config={...this.config,...e}),this.sanitizeConfig(),this.config}getCurrentCacheSize(){var e=this;return(0,_.Z)(function*(){let t=yield e.getStorage().getItem(Re(e.config.keyPrefix));return t||(yield e.getStorage().setItem(Re(e.config.keyPrefix),"0"),t="0"),Number(t)})()}setItem(e,t,r){var s=this;return(0,_.Z)(function*(){if(re.debug(`Set item: key is ${e}, value is ${t} with options: ${r}`),!e||e===Ce)return void re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`);if(typeof t>"u")return void re.warn("The value of item should not be undefined!");const y={priority:void 0!==r?.priority?r.priority:s.config.defaultPriority,expires:void 0!==r?.expires?r.expires:s.config.defaultTTL+Ae()};if(y.priority<1||y.priority>5)return void re.warn("Invalid parameter: priority due to out or range. It should be within 1 and 5.");const C=`${s.config.keyPrefix}${e}`,W=s.fillCacheItem(C,t,y);if(W.byteSize>s.config.itemMaxSize)re.warn(`Item with key: ${e} you are trying to put into is too big!`);else try{const K=yield s.getStorage().getItem(C);if(K&&(yield s.removeCacheItem(C,JSON.parse(K).byteSize)),yield s.isCacheFull(W.byteSize)){const oe=yield s.clearInvalidAndGetRemainingKeys();if(yield s.isCacheFull(W.byteSize)){const ae=yield s.sizeToPop(W.byteSize);yield s.popOutItems(oe,ae)}}return s.setCacheItem(C,W)}catch(K){re.warn(`setItem failed! ${K}`)}})()}getItem(e,t){var r=this;return(0,_.Z)(function*(){let s;if(re.debug(`Get item: key is ${e} with options ${t}`),!e||e===Ce)return re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`),null;const y=`${r.config.keyPrefix}${e}`;try{if(s=yield r.getStorage().getItem(y),null!=s){if(!(yield r.isExpired(y)))return(yield r.updateVisitedTime(JSON.parse(s),y)).data;yield r.removeCacheItem(y,JSON.parse(s).byteSize)}if(t?.callback){const C=t.callback();return null!==C&&(yield r.setItem(e,C,t)),C}return null}catch(C){return re.warn(`getItem failed! ${C}`),null}})()}removeItem(e){var t=this;return(0,_.Z)(function*(){if(re.debug(`Remove item: key is ${e}`),!e||e===Ce)return void re.warn(`Invalid key: should not be empty or reserved key: '${Ce}'`);const r=`${t.config.keyPrefix}${e}`;try{const s=yield t.getStorage().getItem(r);s&&(yield t.removeCacheItem(r,JSON.parse(s).byteSize))}catch(s){re.warn(`removeItem failed! ${s}`)}})()}getAllKeys(){var e=this;return(0,_.Z)(function*(){try{return yield e.getAllCacheKeys()}catch(t){return re.warn(`getAllkeys failed! ${t}`),[]}})()}getStorage(){return this.keyValueStorage}isExpired(e){var t=this;return(0,_.Z)(function*(){const r=yield t.getStorage().getItem(e);Ge(null!==r,Q.NoCacheItem,`Key: ${e}`);const s=JSON.parse(r);return Ae()>=s.expires})()}removeCacheItem(e,t){var r=this;return(0,_.Z)(function*(){const s=yield r.getStorage().getItem(e);Ge(null!==s,Q.NoCacheItem,`Key: ${e}`);const y=t??JSON.parse(s).byteSize;yield r.decreaseCurrentSizeInBytes(y);try{yield r.getStorage().removeItem(e)}catch(C){yield r.increaseCurrentSizeInBytes(y),re.error(`Failed to remove item: ${C}`)}})()}fillCacheItem(e,t,r){const s={key:e,data:t,timestamp:Ae(),visitedTime:Ae(),priority:r.priority??0,expires:r.expires??0,type:typeof t,byteSize:0};return s.byteSize=$e(JSON.stringify(s)),s.byteSize=$e(JSON.stringify(s)),s}sanitizeConfig(){this.config.itemMaxSize>this.config.capacityInBytes&&(re.error("Invalid parameter: itemMaxSize. It should be smaller than capacityInBytes. Setting back to default."),this.config.itemMaxSize=Ie.itemMaxSize),(this.config.defaultPriority>5||this.config.defaultPriority<1)&&(re.error("Invalid parameter: defaultPriority. It should be between 1 and 5. Setting back to default."),this.config.defaultPriority=Ie.defaultPriority),(Number(this.config.warningThreshold)>1||Number(this.config.warningThreshold)<0)&&(re.error("Invalid parameter: warningThreshold. It should be between 0 and 1. Setting back to default."),this.config.warningThreshold=Ie.warningThreshold),this.config.capacityInBytes>5242880&&(re.error("Cache Capacity should be less than 5MB. Setting back to default. Setting back to default."),this.config.capacityInBytes=Ie.capacityInBytes)}increaseCurrentSizeInBytes(e){var t=this;return(0,_.Z)(function*(){const r=yield t.getCurrentCacheSize();yield t.getStorage().setItem(Re(t.config.keyPrefix),(r+e).toString())})()}decreaseCurrentSizeInBytes(e){var t=this;return(0,_.Z)(function*(){const r=yield t.getCurrentCacheSize();yield t.getStorage().setItem(Re(t.config.keyPrefix),(r-e).toString())})()}updateVisitedTime(e,t){var r=this;return(0,_.Z)(function*(){return e.visitedTime=Ae(),yield r.getStorage().setItem(t,JSON.stringify(e)),e})()}setCacheItem(e,t){var r=this;return(0,_.Z)(function*(){yield r.increaseCurrentSizeInBytes(t.byteSize);try{yield r.getStorage().setItem(e,JSON.stringify(t))}catch(s){yield r.decreaseCurrentSizeInBytes(t.byteSize),re.error(`Failed to set item ${s}`)}})()}sizeToPop(e){var t=this;return(0,_.Z)(function*(){const s=(yield t.getCurrentCacheSize())+e-t.config.capacityInBytes,y=(1-t.config.warningThreshold)*t.config.capacityInBytes;return s>y?s:y})()}isCacheFull(e){var t=this;return(0,_.Z)(function*(){const r=yield t.getCurrentCacheSize();return e+r>t.config.capacityInBytes})()}popOutItems(e,t){var r=this;return(0,_.Z)(function*(){const s=[];let y=t;for(const C of e){const W=yield r.getStorage().getItem(C);if(null!=W){const K=JSON.parse(W);s.push(K)}}s.sort((C,W)=>C.priority>W.priority?-1:C.priority<W.priority?1:C.visitedTime<W.visitedTime?-1:1);for(const C of s)if(yield r.removeCacheItem(C.key,C.byteSize),y-=C.byteSize,y<=0)return})()}clearInvalidAndGetRemainingKeys(){var e=this;return(0,_.Z)(function*(){const t=[],r=yield e.getAllCacheKeys({omitSizeKey:!0});for(const s of r)(yield e.isExpired(s))?yield e.removeCacheItem(s):t.push(s);return t})()}clear(){var e=this;return(0,_.Z)(function*(){re.debug("Clear Cache");try{const t=yield e.getAllKeys();for(const r of t){const s=`${e.config.keyPrefix}${r}`;yield e.getStorage().removeItem(s)}}catch(t){re.warn(`clear failed! ${t}`)}})()}}const He=new N.k("StorageCache");class nt extends Fe{constructor(e){const t=le();super({config:e,keyValueStorage:new V(t)}),this.storage=t,this.getItem=this.getItem.bind(this),this.setItem=this.setItem.bind(this),this.removeItem=this.removeItem.bind(this)}getAllCacheKeys(e){var t=this;return(0,_.Z)(function*(){const{omitSizeKey:r}=e??{},s=[];for(let y=0;y<t.storage.length;y++){const C=t.storage.key(y);r&&C===Re(t.config.keyPrefix)||C?.startsWith(t.config.keyPrefix)&&s.push(C.substring(t.config.keyPrefix.length))}return s})()}createInstance(e){return(!e.keyPrefix||e.keyPrefix===Ie.keyPrefix)&&(He.error("invalid keyPrefix, setting keyPrefix with timeStamp"),e.keyPrefix=Ae.toString()),new nt(e)}}const rt=new nt,vt=new N.k("I18n");var Pe=(()=>((Pe||(Pe={})).NotConfigured="NotConfigured",Pe))();const Ze=(0,Oe.$)({[Pe.NotConfigured]:{message:"i18n is not configured."}}),dt=new N.k("I18n");let Ke={language:null},ye=null;class be{static configure(e){return dt.debug("configure I18n"),e&&(Ke=Object.assign({},Ke,e.I18n||e),be.createInstance()),Ke}static getModuleName(){return"I18n"}static createInstance(){dt.debug("create I18n instance"),!ye&&(ye=new class{constructor(){this._options=null,this._lang=null,this._dict={}}setDefaultLanguage(){!this._lang&&typeof window<"u"&&window&&window.navigator&&(this._lang=window.navigator.language),vt.debug(this._lang)}setLanguage(e){this._lang=e}get(e,t){if(this.setDefaultLanguage(),!this._lang)return typeof t<"u"?t:e;const r=this._lang;let s=this.getByLanguage(e,r);return s||(r.indexOf("-")>0&&(s=this.getByLanguage(e,r.split("-")[0])),s)?s:typeof t<"u"?t:e}getByLanguage(e,t,r=null){if(!t)return r;const s=this._dict[t];return s?s[e]:r}putVocabulariesForLanguage(e,t){let r=this._dict[e];r||(r=this._dict[e]={}),this._dict[e]={...r,...t}}putVocabularies(e){Object.keys(e).forEach(t=>{this.putVocabulariesForLanguage(t,e[t])})}})}static setLanguage(e){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.setLanguage(e)}static get(e,t){return be.checkConfig()?(Ze(!!ye,Pe.NotConfigured),ye.get(e,t)):typeof t>"u"?e:t}static putVocabulariesForLanguage(e,t){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.putVocabulariesForLanguage(e,t)}static putVocabularies(e){be.checkConfig(),Ze(!!ye,Pe.NotConfigured),ye.putVocabularies(e)}static checkConfig(){return ye||be.createInstance(),!0}}be.createInstance();var bt=a(96594),Ve=a(38261);const wt=(0,Oe.$)({[f.Z.NoEndpointId]:{message:"Endpoint ID was not found and was unable to be created."},[f.Z.PlatformNotSupported]:{message:"Function not supported on current platform."},[f.Z.Unknown]:{message:"An unknown error occurred."},[f.Z.NetworkError]:{message:"A network error has occurred."}}),lt=new N.k("getClientInfo");var ft=a(6639);const ot=u=>encodeURIComponent(u).replace(/[!'()*]/g,Rt),Rt=u=>`%${u.charCodeAt(0).toString(16).toUpperCase()}`,ht={service:"mobiletargeting",endpointResolver:({region:u})=>({url:new ce.a(`https://pinpoint.${u}.${(0,F.b)(u)}`)}),retryDecider:(0,ee.j)(b.f),computeDelay:q.k,userAgentValue:(0,I.Zm)()},Dt=(0,k.z)(ft.Z,({ApplicationId:u="",EndpointId:e="",EndpointRequest:t},r)=>{const y=new ce.a(r.url);return y.pathname=`v1/apps/${ot(u)}/endpoints/${ot(e)}`,{method:"PUT",headers:{"content-type":"application/json"},url:y,body:JSON.stringify(t)}},function(){var u=(0,_.Z)(function*(e){if(e.statusCode>=300)throw yield(0,b.f)(e);{const{Message:t,RequestID:r}=yield(0,b.e)(e);return{MessageBody:{Message:t,RequestID:r},$metadata:(0,S.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),ht),Qe=(u,e)=>`${e}:pinpoint:${u}`,Nt=function(){var u=(0,_.Z)(function*(e,t,r){const s=Qe(e,t),C=(new Date).getTime()+31536e8;return rt.setItem(s,r,{expires:C,priority:1})});return function(t,r,s){return u.apply(this,arguments)}}(),Je={},it=function(){var u=(0,_.Z)(function*(e,t){const r=Qe(e,t);return(yield rt.getItem(r))??void 0});return function(t,r){return u.apply(this,arguments)}}(),kt=function(){var u=(0,_.Z)(function*({address:e,appId:t,category:r,channelType:s,credentials:y,identityId:C,optOut:W,region:K,userAttributes:oe,userId:ae,userProfile:me,userAgentValue:De}){const Ee=yield it(t,r),ve=Ee?void 0:((u,e)=>{const t=Qe(u,e);return Je[t]||(Je[t]=(0,Ve.r)()),Je[t]})(t,r),{customProperties:Me,demographic:qe,email:We,location:we,metrics:yt,name:ct,plan:ut}=me??{},Ne={},mt=ve?ae??C:ae;if(ve){const ze=function It(){return typeof window>"u"?{}:function Ct(){if(typeof window>"u")return lt.warn("No window object available to get browser client info"),{};const u=window.navigator;if(!u)return lt.warn("No navigator object available to get browser client info"),{};const{platform:e,product:t,vendor:r,userAgent:s,language:y}=u,C=function xt(u){const e=/.+(Opera[\s[A-Z]*|OPR[\sA-Z]*)\/([0-9.]+).*/i.exec(u);if(e)return{type:e[1],version:e[2]};const t=/.+(Trident|Edge|Edg|EdgA|EdgiOS)\/([0-9.]+).*/i.exec(u);if(t)return{type:t[1],version:t[2]};const r=/.+(Chrome|CriOS|Firefox|FxiOS)\/([0-9.]+).*/i.exec(u);if(r)return{type:r[1],version:r[2]};const s=/.+(Safari)\/([0-9.]+).*/i.exec(u);if(s)return{type:s[1],version:s[2]};const y=/.+(AppleWebKit)\/([0-9.]+).*/i.exec(u);if(y)return{type:y[1],version:y[2]};const C=/.*([A-Z]+)\/([0-9.]+).*/i.exec(u);return C?{type:C[1],version:C[2]}:{type:"",version:""}}(s),W=function Pt(){const u=/\(([A-Za-z\s].*)\)/.exec((new Date).toString());return u&&u[1]||""}();return{platform:e,make:t||r,model:C.type,version:C.version,appVersion:[C.type,C.version].join("/"),language:y,timezone:W}}()}();Ne.appVersion=ze.appVersion,Ne.make=ze.make,Ne.model=ze.model,Ne.modelVersion=ze.version,Ne.platform=ze.platform}const xe={...Ne,...qe},on={...We&&{email:[We]},...ct&&{name:[ct]},...ut&&{plan:[ut]},...Me},sn=ve||qe,an=We||Me||ct||ut,cn=mt||oe,un={ApplicationId:t,EndpointId:Ee??ve,EndpointRequest:{RequestId:(0,Ve.r)(),EffectiveDate:(new Date).toISOString(),ChannelType:s,Address:e,...an&&{Attributes:on},...sn&&{Demographic:{AppVersion:xe.appVersion,Locale:xe.locale,Make:xe.make,Model:xe.model,ModelVersion:xe.modelVersion,Platform:xe.platform,PlatformVersion:xe.platformVersion,Timezone:xe.timezone}},...we&&{Location:{City:we.city,Country:we.country,Latitude:we.latitude,Longitude:we.longitude,PostalCode:we.postalCode,Region:we.region}},Metrics:yt,OptOut:W,...cn&&{User:{UserId:mt,UserAttributes:oe}}}};try{yield Dt({credentials:y,region:K,userAgentValue:De},un),ve&&(yield Nt(t,r,ve))}finally{ve&&((u,e)=>{const t=Qe(u,e);delete Je[t]})(t,r)}});return function(t){return u.apply(this,arguments)}}(),jt=function(){var u=(0,_.Z)(function*({address:e,appId:t,category:r,channelType:s,credentials:y,identityId:C,region:W,userAgentValue:K}){let oe=yield it(t,r);return oe||(yield kt({address:e,appId:t,category:r,channelType:s,credentials:y,identityId:C,region:W,userAgentValue:K}),oe=yield it(t,r)),wt(!!oe,f.Z.NoEndpointId),oe});return function(t){return u.apply(this,arguments)}}();var ke=(()=>((ke||(ke={})).NoAppId="NoAppId",ke))();const Gt=(0,Oe.$)({[ke.NoAppId]:{message:"Missing application id."}}),Zt=(0,k.z)(ft.Z,({ApplicationId:u,EventsRequest:e},t)=>{Gt(!!u,ke.NoAppId);const s=new ce.a(t.url);return s.pathname=`v1/apps/${ot(u)}/events`,{method:"POST",headers:{"content-type":"application/json"},url:s,body:JSON.stringify(e??{})}},function(){var u=(0,_.Z)(function*(e){if(e.statusCode>=300)throw yield(0,b.f)(e);{const{Results:t}=yield(0,b.e)(e);return{EventsResponse:{Results:t},$metadata:(0,S.B)(e)}}});return function(t){return u.apply(this,arguments)}}(),ht),je=new N.k("PinpointEventBuffer"),_t=[429,500],Vt=[202];class Qt{constructor(e){this._interval=void 0,this._pause=!1,this._flush=!1,this._buffer=[],this._config=e,this._sendBatch=this._sendBatch.bind(this),this._startLoop()}push(e){this._buffer.length>=this._config.bufferSize?je.debug("Exceeded Pinpoint event buffer limits, event dropped.",{eventId:e.eventId}):this._buffer.push({[e.eventId]:e})}pause(){this._pause=!0}resume(){this._pause=!1}flush(){this._flush=!0}identityHasChanged(e){return this._config.identityId!==e}flushAll(){this._putEvents(this._buffer.splice(0,this._buffer.length))}_startLoop(){this._interval&&clearInterval(this._interval);const{flushInterval:e}=this._config;this._interval=setInterval(this._sendBatch,e)}_sendBatch(){const e=this._buffer.length;if(this._flush&&!e&&this._interval&&clearInterval(this._interval),this._pause||!e)return;const{flushSize:t}=this._config,r=Math.min(t,e),s=this._buffer.splice(0,r);this._putEvents(s)}_putEvents(e){var t=this;return(0,_.Z)(function*(){const r=t._bufferToMap(e),s=t._generateBatchEventParams(r);try{const{credentials:y,region:C,userAgentValue:W}=t._config,K=yield Zt({credentials:y,region:C,userAgentValue:W},s);t._processPutEventsSuccessResponse(K,r)}catch(y){t._handlePutEventsFailure(y,r)}})()}_generateBatchEventParams(e){const t={};return Object.values(e).forEach(r=>{const{event:s,timestamp:y,endpointId:C,eventId:W,session:K}=r,{name:oe,attributes:ae,metrics:me}=s;t[C]={Endpoint:{...t[C]?.Endpoint},Events:{...t[C]?.Events,[W]:{EventType:oe,Timestamp:new Date(y).toISOString(),Attributes:ae,Metrics:me,Session:K}}}}),{ApplicationId:this._config.appId,EventsRequest:{BatchItem:t}}}_handlePutEventsFailure(e,t){if(je.debug("putEvents call to Pinpoint failed.",e),_t.includes(e.$metadata&&e.$metadata.httpStatusCode)){const s=Object.values(t);this._retry(s)}}_processPutEventsSuccessResponse(e,t){const{Results:r={}}=e.EventsResponse??{},s=[];Object.entries(r).forEach(([y,C])=>{Object.entries(C.EventsItemResponse??{}).forEach(([K,oe])=>{const ae=t[K];if(!ae)return;const{StatusCode:me,Message:De}=oe??{};if(me&&Vt.includes(me))return;if(me&&_t.includes(me))return void s.push(ae);const{name:Ee}=ae.event;je.warn("Pinpoint event failed to send.",{eventId:K,name:Ee,message:De})})}),s.length&&this._retry(s)}_retry(e){const t=[];e.forEach(r=>{const{eventId:s}=r,{name:y}=r.event;if(r.resendLimit-- >0)return je.debug("Resending event.",{eventId:s,name:y,remainingAttempts:r.resendLimit}),void t.push({[s]:r});je.debug("No retry attempts remaining for event.",{eventId:s,name:y})}),this._buffer.unshift(...t)}_bufferToMap(e){return e.reduce((t,r)=>{const[[s,y]]=Object.entries(r);return t[s]=y,t},{})}}const Be={};let Te;const qt=function(){var u=(0,_.Z)(function*({appId:e,category:t,channelType:r,credentials:s,event:y,identityId:C,region:W,userAgentValue:K,bufferSize:oe,flushInterval:ae,flushSize:me,resendLimit:De}){let Ee=Te;const ve=new Date,Me=ve.toISOString(),qe=(0,Ve.r)(),We=(({appId:u,region:e,credentials:t,bufferSize:r,flushInterval:s,flushSize:y,resendLimit:C,identityId:W,userAgentValue:K})=>{if(Be[e]?.[u]){const ae=Be[e][u];if(!ae.identityHasChanged(W))return ae;ae.flush()}const oe=new Qt({appId:u,bufferSize:r,credentials:t,flushInterval:s,flushSize:y,identityId:W,region:e,resendLimit:C,userAgentValue:K});return Be[e]||(Be[e]={}),Be[e][u]=oe,oe})({appId:e,region:W,credentials:s,bufferSize:oe??1e3,flushInterval:ae??5e3,flushSize:me??100,resendLimit:De??5,identityId:C,userAgentValue:K}),we=yield jt({appId:e,category:t,channelType:r,credentials:s,identityId:C,region:W,userAgentValue:K});(!Ee||"_session.start"===y.name)&&(Te={Id:(0,Ve.r)(),StartTimestamp:Me},Ee=Te),Te&&"_session.stop"===y.name&&(Ee={...Te,StopTimestamp:Me,Duration:ve.getTime()-new Date(Te.StartTimestamp).getTime()},Te=void 0),We.push({eventId:qe,endpointId:we,event:y,session:Ee,timestamp:Me,resendLimit:De??5})});return function(t){return u.apply(this,arguments)}}();var _e=(()=>{return(u=_e||(_e={})).UndefinedInstance="UndefinedInstance",u.UndefinedRegistration="UndefinedRegistration",u.Unavailable="Unavailable",_e;var u})();const st=(0,Oe.$)({[_e.UndefinedInstance]:{message:"Service Worker instance is undefined."},[_e.UndefinedRegistration]:{message:"Service Worker registration is undefined."},[_e.Unavailable]:{message:"Service Worker not available."}});class tn{constructor(){this._logger=new N.k("ServiceWorker")}get serviceWorker(){return st(void 0!==this._serviceWorker,_e.UndefinedInstance),this._serviceWorker}register(e="/service-worker.js",t="/"){return this._logger.debug(`registering ${e}`),this._logger.debug(`registering service worker with scope ${t}`),new Promise((r,s)=>{navigator&&"serviceWorker"in navigator?navigator.serviceWorker.register(e,{scope:t}).then(y=>{y.installing?this._serviceWorker=y.installing:y.waiting?this._serviceWorker=y.waiting:y.active&&(this._serviceWorker=y.active),this._registration=y,this._setupListeners(),this._logger.debug(`Service Worker Registration Success: ${y}`),r(y)}).catch(y=>{this._logger.debug(`Service Worker Registration Failed ${y}`),s(new O._({name:_e.Unavailable,message:"Service Worker not available",underlyingError:y}))}):s(new O._({name:_e.Unavailable,message:"Service Worker not available"}))})}enablePush(e){return st(void 0!==this._registration,_e.UndefinedRegistration),this._publicKey=e,new Promise((t,r)=>{(0,bt.j)()?(st(void 0!==this._registration,_e.UndefinedRegistration),this._registration.pushManager.getSubscription().then(s=>{if(!s)return this._logger.debug("User is NOT subscribed to push"),this._registration.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this._urlB64ToUint8Array(e)}).then(y=>{this._subscription=y,this._logger.debug(`User subscribed: ${JSON.stringify(y)}`),t(y)}).catch(y=>{this._logger.error(y)});this._subscription=s,this._logger.debug(`User is subscribed to push: ${JSON.stringify(s)}`),t(s)})):r(new O._({name:_e.Unavailable,message:"Service Worker not available"}))})}_urlB64ToUint8Array(e){const r=(e+"=".repeat((4-e.length%4)%4)).replace(/-/g,"+").replace(/_/g,"/"),s=window.atob(r),y=new Uint8Array(s.length);for(let C=0;C<s.length;++C)y[C]=s.charCodeAt(C);return y}send(e){this._serviceWorker&&this._serviceWorker.postMessage("object"==typeof e?JSON.stringify(e):e)}_setupListeners(){var e=this;this.serviceWorker.addEventListener("statechange",(0,_.Z)(function*(){const t=e.serviceWorker.state;e._logger.debug(`ServiceWorker statechange: ${t}`);const{appId:r,region:s,bufferSize:y,flushInterval:C,flushSize:W,resendLimit:K}=M.getConfig().Analytics?.Pinpoint??{},{credentials:oe}=yield w();r&&s&&oe&&qt({appId:r,region:s,category:"Core",credentials:oe,bufferSize:y,flushInterval:C,flushSize:W,resendLimit:K,event:{name:"ServiceWorker",attributes:{state:t}}})})),this.serviceWorker.addEventListener("message",t=>{this._logger.debug(`ServiceWorker message event: ${t}`)})}}function Ye(u){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)u[r]=t[r]}return u}var Xe=function at(u,e){function t(s,y,C){if(!(typeof document>"u")){"number"==typeof(C=Ye({},e,C)).expires&&(C.expires=new Date(Date.now()+864e5*C.expires)),C.expires&&(C.expires=C.expires.toUTCString()),s=encodeURIComponent(s).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var W="";for(var K in C)C[K]&&(W+="; "+K,!0!==C[K]&&(W+="="+C[K].split(";")[0]));return document.cookie=s+"="+u.write(y,s)+W}}return Object.create({set:t,get:function r(s){if(!(typeof document>"u"||arguments.length&&!s)){for(var y=document.cookie?document.cookie.split("; "):[],C={},W=0;W<y.length;W++){var K=y[W].split("="),oe=K.slice(1).join("=");try{var ae=decodeURIComponent(K[0]);if(C[ae]=u.read(oe,ae),s===ae)break}catch{}}return s?C[s]:C}},remove:function(s,y){t(s,"",Ye({},y,{expires:-1}))},withAttributes:function(s){return at(this.converter,Ye({},this.attributes,s))},withConverter:function(s){return at(Ye({},this.converter,s),this.attributes)}},{attributes:{value:Object.freeze(e)},converter:{value:Object.freeze(u)}})}({read:function(u){return'"'===u[0]&&(u=u.slice(1,-1)),u.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(u){return encodeURIComponent(u).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});class rn{constructor(e={}){const{path:t,domain:r,expires:s,sameSite:y,secure:C}=e;if(this.domain=r,this.path=t||"/",this.expires=Object.prototype.hasOwnProperty.call(e,"expires")?s:365,this.secure=!Object.prototype.hasOwnProperty.call(e,"secure")||C,Object.prototype.hasOwnProperty.call(e,"sameSite")){if(!y||!["strict","lax","none"].includes(y))throw new Error('The sameSite value of cookieStorage must be "lax", "strict" or "none".');if("none"===y&&!this.secure)throw new Error("sameSite = None requires the Secure attribute in latest browser versions.");this.sameSite=y}}setItem(e,t){var r=this;return(0,_.Z)(function*(){Xe.set(e,t,r.getData())})()}getItem(e){return(0,_.Z)(function*(){return Xe.get(e)??null})()}removeItem(e){var t=this;return(0,_.Z)(function*(){Xe.remove(e,t.getData())})()}clear(){var e=this;return(0,_.Z)(function*(){const t=Xe.get(),r=Object.keys(t).map(s=>e.removeItem(s));yield Promise.all(r)})()}getData(){return{path:this.path,expires:this.expires,domain:this.domain,secure:this.secure,...this.sameSite&&{sameSite:this.sameSite}}}}},74173:(G,T,a)=>{a.d(T,{YE:()=>_,FG:()=>h,xp:()=>U});var g=a(10180);const p={convert(A,M){let P=A;return M?.urlSafe&&(P=P.replace(/-/g,"+").replace(/_/g,"/")),(0,g.tl)()(P)}};var D=a(82847),v=(()=>{return(A=v||(v={})).AuthTokenConfigException="AuthTokenConfigException",A.AuthUserPoolAndIdentityPoolException="AuthUserPoolAndIdentityPoolException",A.AuthUserPoolException="AuthUserPoolException",A.InvalidIdentityPoolIdException="InvalidIdentityPoolIdException",A.OAuthNotConfigureException="OAuthNotConfigureException",v;var A})();const E=(0,D.$)({[v.AuthTokenConfigException]:{message:"Auth Token Provider not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app."},[v.AuthUserPoolAndIdentityPoolException]:{message:"Auth UserPool or IdentityPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with UserPoolId and IdentityPoolId."},[v.AuthUserPoolException]:{message:"Auth UserPool not configured.",recoverySuggestion:"Make sure to call Amplify.configure in your app with userPoolId and userPoolClientId."},[v.InvalidIdentityPoolIdException]:{message:"Invalid identity pool id provided.",recoverySuggestion:"Make sure a valid identityPoolId is given in the config."},[v.OAuthNotConfigureException]:{message:"oauth param not configured.",recoverySuggestion:"Make sure to call Amplify.configure with oauth parameter in your app."}});function h(A){let M=!0;M=!!A&&!!A.userPoolId&&!!A.userPoolClientId,E(M,v.AuthUserPoolException)}function _(A){E(!!A?.identityPoolId,v.InvalidIdentityPoolIdException)}function U(A){const M=A.split(".");if(3!==M.length)throw new Error("Invalid token");try{const w=M[1].replace(/-/g,"+").replace(/_/g,"/"),l=decodeURIComponent(p.convert(w).split("").map(b=>`%${`00${b.charCodeAt(0).toString(16)}`.slice(-2)}`).join(""));return{toString:()=>A,payload:JSON.parse(l)}}catch{throw new Error("Invalid token payload")}}},87199:(G,T,a)=>{a.d(T,{Z:()=>g});var g=(()=>{return(p=g||(g={})).NoEndpointId="NoEndpointId",p.PlatformNotSupported="PlatformNotSupported",p.Unknown="Unknown",p.NetworkError="NetworkError",g;var p})()},99120:(G,T,a)=>{a.d(T,{a:()=>g,z:()=>p});const g=URL,p=URLSearchParams},38261:(G,T,a)=>{a.d(T,{r:()=>p});const p=a(83364).v4},10180:(G,T,a)=>{a.d(T,{Ds:()=>D,tl:()=>v});var g=a(91935);const D=()=>{if(typeof window<"u"&&"function"==typeof window.btoa)return window.btoa;if("function"==typeof btoa)return btoa;throw new g._({name:"Base64EncoderError",message:"Cannot resolve the `btoa` function from the environment."})},v=()=>{if(typeof window<"u"&&"function"==typeof window.atob)return window.atob;if("function"==typeof atob)return atob;throw new g._({name:"Base64EncoderError",message:"Cannot resolve the `atob` function from the environment."})}},96594:(G,T,a)=>{a.d(T,{j:()=>g});const g=()=>typeof window<"u"&&typeof window.document<"u"},52458:(G,T,a)=>{a.d(T,{h:()=>se});var g=a(55502),p=a(91935);const D=new g.k("parseAWSExports"),v={API_KEY:"apiKey",AWS_IAM:"iam",AMAZON_COGNITO_USER_POOLS:"userPool",OPENID_CONNECT:"oidc",NONE:"none",AWS_LAMBDA:"lambda",LAMBDA:"lambda"},E=I=>I?.split(",")??[],h=({domain:I,scope:R,redirectSignIn:L,redirectSignOut:$,responseType:H})=>({domain:I,scopes:R,redirectSignIn:E(L),redirectSignOut:E($),responseType:H}),x=I=>I.map(R=>{const L=R.toLowerCase();return L.charAt(0).toUpperCase()+L.slice(1)});const k={AMAZON_COGNITO_USER_POOLS:"userPool",API_KEY:"apiKey",AWS_IAM:"iam",AWS_LAMBDA:"lambda",OPENID_CONNECT:"oidc"};function F(I){return k[I]}const Y={GOOGLE:"Google",LOGIN_WITH_AMAZON:"Amazon",FACEBOOK:"Facebook",SIGN_IN_WITH_APPLE:"Apple"};function q(I=[]){return I.reduce((R,L)=>(void 0!==Y[L]&&R.push(Y[L]),R),[])}function ee(I){return"OPTIONAL"===I?"optional":"REQUIRED"===I?"on":"off"}function ce(I){const R={};return I.forEach(({name:L,bucket_name:$,aws_region:H,paths:X})=>{if(L in R)throw new Error(`Duplicate friendly name found: ${L}. Name must be unique.`);const te=X?Object.entries(X).reduce((he,[ie,ue])=>(void 0!==ue&&(he[ie]=ue),he),{}):void 0;R[L]={bucketName:$,region:H,paths:te}}),R}const se=I=>Object.keys(I).some(R=>R.startsWith("aws_"))?((I={})=>{if(!Object.prototype.hasOwnProperty.call(I,"aws_project_region"))throw new p._({name:"InvalidParameterException",message:"Invalid config parameter.",recoverySuggestion:"Ensure passing the config object imported from  `amplifyconfiguration.json`."});const{aws_appsync_apiKey:R,aws_appsync_authenticationType:L,aws_appsync_graphqlEndpoint:$,aws_appsync_region:H,aws_bots_config:X,aws_cognito_identity_pool_id:te,aws_cognito_sign_up_verification_method:he,aws_cognito_mfa_configuration:ie,aws_cognito_mfa_types:ue,aws_cognito_password_protection_settings:Z,aws_cognito_verification_mechanisms:o,aws_cognito_signup_attributes:i,aws_cognito_social_providers:c,aws_cognito_username_attributes:n,aws_mandatory_sign_in:d,aws_mobile_analytics_app_id:f,aws_mobile_analytics_app_region:O,aws_user_files_s3_bucket:j,aws_user_files_s3_bucket_region:V,aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing:N,aws_user_pools_id:B,aws_user_pools_web_client_id:J,geo:le,oauth:z,predictions:ne,aws_cloud_logic_custom:fe,Notifications:ge,modelIntrospection:pe}=I,de={};f&&(de.Analytics={Pinpoint:{appId:f,region:O}});const{InAppMessaging:Ue,Push:Le}=ge??{};if(Ue?.AWSPinpoint||Le?.AWSPinpoint){if(Ue?.AWSPinpoint){const{appId:Q,region:Se}=Ue.AWSPinpoint;de.Notifications={InAppMessaging:{Pinpoint:{appId:Q,region:Se}}}}if(Le?.AWSPinpoint){const{appId:Q,region:Se}=Le.AWSPinpoint;de.Notifications={...de.Notifications,PushNotification:{Pinpoint:{appId:Q,region:Se}}}}}if(Array.isArray(X)&&(de.Interactions={LexV1:Object.fromEntries(X.map(Q=>[Q.name,Q]))}),$){const Q=v[L];Q||D.debug(`Invalid authentication type ${L}. Falling back to IAM.`),de.API={GraphQL:{endpoint:$,apiKey:R,region:H,defaultAuthMode:Q??"iam"}},pe&&(de.API.GraphQL.modelIntrospection=pe)}const et=ie?{status:ie&&ie.toLowerCase(),totpEnabled:ue?.includes("TOTP")??!1,smsEnabled:ue?.includes("SMS")??!1}:void 0,Ie=Z?{minLength:Z.passwordPolicyMinLength,requireLowercase:Z.passwordPolicyCharacters?.includes("REQUIRES_LOWERCASE")??!1,requireUppercase:Z.passwordPolicyCharacters?.includes("REQUIRES_UPPERCASE")??!1,requireNumbers:Z.passwordPolicyCharacters?.includes("REQUIRES_NUMBERS")??!1,requireSpecialCharacters:Z.passwordPolicyCharacters?.includes("REQUIRES_SYMBOLS")??!1}:void 0,$e=Array.from(new Set([...o??[],...i??[]])).reduce((Q,Se)=>({...Q,[Se.toLowerCase()]:{required:!0}}),{}),Ae=n?.includes("EMAIL")??!1,tt=n?.includes("PHONE_NUMBER")??!1;(te||B)&&(de.Auth={Cognito:{identityPoolId:te,allowGuestAccess:"enable"!==d,signUpVerificationMethod:he,userAttributes:$e,userPoolClientId:J,userPoolId:B,mfa:et,passwordFormat:Ie,loginWith:{username:!(Ae||tt),email:Ae,phone:tt}}});const Re=!!z&&Object.keys(z).length>0,Oe=!!c&&c.length>0;if(de.Auth&&Re&&(de.Auth.Cognito.loginWith={...de.Auth.Cognito.loginWith,oauth:{...h(z),...Oe&&{providers:x(c)}}}),j&&(de.Storage={S3:{bucket:j,region:V,dangerouslyConnectToHttpEndpointForTesting:N}}),le){const{amazon_location_service:Q}=le;de.Geo={LocationService:{maps:Q.maps,geofenceCollections:Q.geofenceCollections,searchIndices:Q.search_indices,region:Q.region}}}if(fe&&(de.API={...de.API,REST:fe.reduce((Q,Se)=>{const{name:Ge,endpoint:re,region:Fe,service:He}=Se;return{...Q,[Ge]:{endpoint:re,...He?{service:He}:void 0,...Fe?{region:Fe}:void 0}}},{})}),ne){const{VoiceId:Q}=ne?.convert?.speechGenerator?.defaults??{};de.Predictions=Q?{...ne,convert:{...ne.convert,speechGenerator:{...ne.convert.speechGenerator,defaults:{voiceId:Q}}}}:ne}return de})(I):function _(I){const{version:R}=I;return!!R&&R.startsWith("1")}(I)?function b(I){const R={};if(I.storage&&(R.Storage=function U(I){if(!I)return;const{bucket_name:R,aws_region:L,buckets:$}=I;return{S3:{bucket:R,region:L,buckets:$&&ce($)}}}(I.storage)),I.auth&&(R.Auth=function A(I){if(!I)return;const{user_pool_id:R,user_pool_client_id:L,identity_pool_id:$,password_policy:H,mfa_configuration:X,mfa_methods:te,unauthenticated_identities_enabled:he,oauth:ie,username_attributes:ue,standard_required_attributes:Z,groups:o}=I,i={Cognito:{userPoolId:R,userPoolClientId:L,groups:o}};return $&&(i.Cognito={...i.Cognito,identityPoolId:$}),H&&(i.Cognito.passwordFormat={requireLowercase:H.require_lowercase,requireNumbers:H.require_numbers,requireUppercase:H.require_uppercase,requireSpecialCharacters:H.require_symbols,minLength:H.min_length??6}),X&&(i.Cognito.mfa={status:ee(X),smsEnabled:te?.includes("SMS"),totpEnabled:te?.includes("TOTP")}),he&&(i.Cognito.allowGuestAccess=he),ie&&(i.Cognito.loginWith={oauth:{domain:ie.domain,redirectSignIn:ie.redirect_sign_in_uri,redirectSignOut:ie.redirect_sign_out_uri,responseType:"token"===ie.response_type?"token":"code",scopes:ie.scopes,providers:q(ie.identity_providers)}}),ue&&(i.Cognito.loginWith={...i.Cognito.loginWith,email:ue.includes("email"),phone:ue.includes("phone_number"),username:ue.includes("username")}),Z&&(i.Cognito.userAttributes=Z.reduce((c,n)=>({...c,[n]:{required:!0}}),{})),i}(I.auth)),I.analytics&&(R.Analytics=function M(I){if(!I?.amazon_pinpoint)return;const{amazon_pinpoint:R}=I;return{Pinpoint:{appId:R.app_id,region:R.aws_region}}}(I.analytics)),I.geo&&(R.Geo=function P(I){if(!I)return;const{aws_region:R,geofence_collections:L,maps:$,search_indices:H}=I;return{LocationService:{region:R,searchIndices:H,geofenceCollections:L,maps:$}}}(I.geo)),I.data&&(R.API=function w(I){if(!I)return;const{aws_region:R,default_authorization_type:L,url:$,api_key:H,model_introspection:X}=I;return{GraphQL:{endpoint:$,defaultAuthMode:F(L),region:R,apiKey:H,modelIntrospection:X}}}(I.data)),I.custom){const L=function l(I){if(!I?.events)return;const{url:R,aws_region:L,api_key:$,default_authorization_type:H}=I.events;return{Events:{endpoint:R,defaultAuthMode:F(H),region:L,apiKey:$}}}(I.custom);L&&"Events"in L&&(R.API={...R.API,...L})}return I.notifications&&(R.Notifications=function S(I){if(!I)return;const{aws_region:R,channels:L,amazon_pinpoint_app_id:$}=I,H=L.includes("IN_APP_MESSAGING"),X=L.includes("APNS")||L.includes("FCM");if(!H&&!X)return;const te={};return H&&(te.InAppMessaging={Pinpoint:{appId:$,region:R}}),X&&(te.PushNotification={Pinpoint:{appId:$,region:R}}),te}(I.notifications)),R}(I):I},90206:(G,T,a)=>{a.d(T,{t:()=>g});const g=3e5},75599:(G,T,a)=>{a.d(T,{k:()=>p});var g=a(90206);function p(D=g.t){return E=>{const h=2**E*100+100*Math.random();return!(h>D)&&h}}},15861:(G,T,a)=>{function g(D,v,m,E,h,x,_){try{var U=D[x](_),A=U.value}catch(M){return void m(M)}U.done?v(A):Promise.resolve(A).then(E,h)}function p(D){return function(){var v=this,m=arguments;return new Promise(function(E,h){var x=D.apply(v,m);function _(A){g(x,E,h,_,U,"next",A)}function U(A){g(x,E,h,_,U,"throw",A)}_(void 0)})}}a.d(T,{Z:()=>p})},97582:(G,T,a)=>{a.d(T,{FC:()=>ee,Jh:()=>P,KL:()=>se,ZT:()=>p,_T:()=>v,cy:()=>I,ev:()=>Y,gn:()=>m,mG:()=>M,pi:()=>D,pr:()=>F,qq:()=>q});var g=function(o,i){return(g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,n){c.__proto__=n}||function(c,n){for(var d in n)Object.prototype.hasOwnProperty.call(n,d)&&(c[d]=n[d])})(o,i)};function p(o,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function c(){this.constructor=o}g(o,i),o.prototype=null===i?Object.create(i):(c.prototype=i.prototype,new c)}var D=function(){return D=Object.assign||function(i){for(var c,n=1,d=arguments.length;n<d;n++)for(var f in c=arguments[n])Object.prototype.hasOwnProperty.call(c,f)&&(i[f]=c[f]);return i},D.apply(this,arguments)};function v(o,i){var c={};for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&i.indexOf(n)<0&&(c[n]=o[n]);if(null!=o&&"function"==typeof Object.getOwnPropertySymbols){var d=0;for(n=Object.getOwnPropertySymbols(o);d<n.length;d++)i.indexOf(n[d])<0&&Object.prototype.propertyIsEnumerable.call(o,n[d])&&(c[n[d]]=o[n[d]])}return c}function m(o,i,c,n){var O,d=arguments.length,f=d<3?i:null===n?n=Object.getOwnPropertyDescriptor(i,c):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)f=Reflect.decorate(o,i,c,n);else for(var j=o.length-1;j>=0;j--)(O=o[j])&&(f=(d<3?O(f):d>3?O(i,c,f):O(i,c))||f);return d>3&&f&&Object.defineProperty(i,c,f),f}function M(o,i,c,n){return new(c||(c=Promise))(function(f,O){function j(B){try{N(n.next(B))}catch(J){O(J)}}function V(B){try{N(n.throw(B))}catch(J){O(J)}}function N(B){B.done?f(B.value):function d(f){return f instanceof c?f:new c(function(O){O(f)})}(B.value).then(j,V)}N((n=n.apply(o,i||[])).next())})}function P(o,i){var n,d,f,c={label:0,sent:function(){if(1&f[0])throw f[1];return f[1]},trys:[],ops:[]},O=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return O.next=j(0),O.throw=j(1),O.return=j(2),"function"==typeof Symbol&&(O[Symbol.iterator]=function(){return this}),O;function j(N){return function(B){return function V(N){if(n)throw new TypeError("Generator is already executing.");for(;O&&(O=0,N[0]&&(c=0)),c;)try{if(n=1,d&&(f=2&N[0]?d.return:N[0]?d.throw||((f=d.return)&&f.call(d),0):d.next)&&!(f=f.call(d,N[1])).done)return f;switch(d=0,f&&(N=[2&N[0],f.value]),N[0]){case 0:case 1:f=N;break;case 4:return c.label++,{value:N[1],done:!1};case 5:c.label++,d=N[1],N=[0];continue;case 7:N=c.ops.pop(),c.trys.pop();continue;default:if(!(f=(f=c.trys).length>0&&f[f.length-1])&&(6===N[0]||2===N[0])){c=0;continue}if(3===N[0]&&(!f||N[1]>f[0]&&N[1]<f[3])){c.label=N[1];break}if(6===N[0]&&c.label<f[1]){c.label=f[1],f=N;break}if(f&&c.label<f[2]){c.label=f[2],c.ops.push(N);break}f[2]&&c.ops.pop(),c.trys.pop();continue}N=i.call(o,c)}catch(B){N=[6,B],d=0}finally{n=f=0}if(5&N[0])throw N[1];return{value:N[0]?N[1]:void 0,done:!0}}([N,B])}}}function F(){for(var o=0,i=0,c=arguments.length;i<c;i++)o+=arguments[i].length;var n=Array(o),d=0;for(i=0;i<c;i++)for(var f=arguments[i],O=0,j=f.length;O<j;O++,d++)n[d]=f[O];return n}function Y(o,i,c){if(c||2===arguments.length)for(var f,n=0,d=i.length;n<d;n++)(f||!(n in i))&&(f||(f=Array.prototype.slice.call(i,0,n)),f[n]=i[n]);return o.concat(f||Array.prototype.slice.call(i))}function q(o){return this instanceof q?(this.v=o,this):new q(o)}function ee(o,i,c){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var d,n=c.apply(o,i||[]),f=[];return d=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),j("next"),j("throw"),j("return",function O(z){return function(ne){return Promise.resolve(ne).then(z,J)}}),d[Symbol.asyncIterator]=function(){return this},d;function j(z,ne){n[z]&&(d[z]=function(fe){return new Promise(function(ge,pe){f.push([z,fe,ge,pe])>1||V(z,fe)})},ne&&(d[z]=ne(d[z])))}function V(z,ne){try{!function N(z){z.value instanceof q?Promise.resolve(z.value.v).then(B,J):le(f[0][2],z)}(n[z](ne))}catch(fe){le(f[0][3],fe)}}function B(z){V("next",z)}function J(z){V("throw",z)}function le(z,ne){z(ne),f.shift(),f.length&&V(f[0][0],f[0][1])}}function se(o){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c,i=o[Symbol.asyncIterator];return i?i.call(o):(o=function S(o){var i="function"==typeof Symbol&&Symbol.iterator,c=i&&o[i],n=0;if(c)return c.call(o);if(o&&"number"==typeof o.length)return{next:function(){return o&&n>=o.length&&(o=void 0),{value:o&&o[n++],done:!o}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")}(o),c={},n("next"),n("throw"),n("return"),c[Symbol.asyncIterator]=function(){return this},c);function n(f){c[f]=o[f]&&function(O){return new Promise(function(j,V){!function d(f,O,j,V){Promise.resolve(V).then(function(N){f({value:N,done:j})},O)}(j,V,(O=o[f](O)).done,O.value)})}}}function I(o,i){return Object.defineProperty?Object.defineProperty(o,"raw",{value:i}):o.raw=i,o}"function"==typeof SuppressedError&&SuppressedError}}]);