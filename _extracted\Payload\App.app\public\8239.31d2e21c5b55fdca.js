(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8239],{90005:(E,h,i)=>{i.d(h,{C:()=>S});var m=i(15861),f=i(17007),g=i(30263),z=i(3372),s=i(27236),k=i(95437),b=i(87956),c=i(31981);const u={provisionInitializationError:"No fue posible aprovisionar billetera digital en tu dispositivo (PRO03)",provisionProcessError:"No fue posible aprovisionar billetera digital en tu dispositivo (PRO01)",provisionDeviceError:"No fue posible activar billetera digital en tu dispositivo (PRO02)",walletCreatedError:"No fue posible iniciar la billetera digital en tu dispositivo (WAL01)",walletCredentialsRequired:"No fue posible autenticar la billetera digital en tu dispositivo (WAL02)",walletConnectionError:"No fue posible conectar con la billetera digital en tu dispositivo (WAL03)"},l={cardDisabled:"Estamos presentado fallas para verificar registro en la billetera digital. Por favor intente m\xe1s tarde",cardNotSupport:"Tu dispositivo no es compatible para realizar registro en la billetera digital",cardNotConfigure:"Tu dispositivo no tiene configurado un usuario en la billetera digital"},_={enrollCardsError:"No fue posible registrar la tarjeta en la billetera, por favor intente nuevamente",enrollCardsTimeout:"No fue posible verificar registro de la tarjeta en la billetera, por favor intente nuevamente"};var x=i(39904),t=i(99877);function y(n,a){if(1&n){const e=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",5),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(e);const r=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(r.onTokenization())}),t.\u0275\u0275elementStart(1,"span",6),t.\u0275\u0275text(2,"Agregar en"),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(3,"img",7),t.\u0275\u0275elementEnd()}if(2&n){const e=t.\u0275\u0275nextContext(2);t.\u0275\u0275classMap(e.buttonType),t.\u0275\u0275property("disabled",e.tokenizing),t.\u0275\u0275advance(3),t.\u0275\u0275property("src",e.imgWalletStore,t.\u0275\u0275sanitizeUrl)}}function v(n,a){if(1&n){const e=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"button",8),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(e);const r=t.\u0275\u0275nextContext(2);return t.\u0275\u0275resetView(r.onManagerToken())}),t.\u0275\u0275element(1,"img",7),t.\u0275\u0275elementStart(2,"span",9),t.\u0275\u0275text(3," Dispositivo vinculados "),t.\u0275\u0275elementEnd()()}if(2&n){const e=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(1),t.\u0275\u0275property("src",e.imgWalletDevices,t.\u0275\u0275sanitizeUrl)}}function T(n,a){if(1&n&&(t.\u0275\u0275elementStart(0,"div",10),t.\u0275\u0275element(1,"bocc-progress-bar",11),t.\u0275\u0275elementStart(2,"span",12),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd()()),2&n){const e=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(1),t.\u0275\u0275property("indeterminate",!0),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",e.msgTokenizing," ")}}function d(n,a){if(1&n&&(t.\u0275\u0275elementStart(0,"div",1),t.\u0275\u0275template(1,y,4,4,"button",2),t.\u0275\u0275template(2,v,4,1,"button",3),t.\u0275\u0275template(3,T,4,2,"div",4),t.\u0275\u0275elementEnd()),2&n){const e=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",e.requiredTokenized&&!e.tokenized),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",e.tokenized),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",e.tokenizing)}}let S=(()=>{class n{constructor(e,o,r,p,C,I){this.bottomSheetService=e,this.storageService=o,this.deviceService=r,this.preferencesService=p,this.digitalWalletService=C,this.mboProvider=I,this.unsubscriptions=[],this.nameSupplierStore="",this.canTokenized=!1,this.errorTokenized=!1,this.pauseFromApp=!1,this.buttonType="",this.imgWalletStore="",this.imgWalletDevices="",this.requiredTokenized=!1,this.tokenizing=!0,this.tokenized=!1,this.msgTokenizing="",this.tryVerifyEnroll=0,this.enrollVerify=!1,this.enrollIntervalId=void 0}ngOnInit(){this.deviceService.controller.itIsIos?(this.nameSupplierStore="Apple Pay",this.buttonType="ios",this.imgWalletStore="assets/shared/logos/apple-pay-light.svg",this.imgWalletDevices="assets/shared/logos/apple-pay-dark.svg"):(this.nameSupplierStore="Google Pay",this.buttonType="android",this.imgWalletStore="assets/shared/logos/google-pay-light.svg",this.imgWalletDevices="assets/shared/logos/google-pay-dark.svg"),this.preferencesService.requestBoolean(z.M.DigitalWallet).then(e=>{e&&this.product&&this.startTokenization(this.product)})}ngOnDestroy(){this.enrollIntervalId&&clearInterval(this.enrollIntervalId),this.unsubscriptions.forEach(e=>{e()}),this.bottomSheet?.destroy()}get enabledTokenized(){return this.canTokenized&&!this.errorTokenized}onTokenization(){this.product&&this.registerCard(this.product)}onManagerToken(){this.mboProvider.navigation.next(x.Z6.CUSTOMER.MANAGER_TOKEN)}startTokenization(e){this.canCardTokenized(e),this.deviceService.controller.resume(()=>{this.verifyStatusForResume(e)}).then(o=>{this.unsubscriptions.push(o)})}canCardTokenized(e){this.digitalWalletService.canTokenizedCard(e).then(o=>{this.canTokenized=o,o&&this.initCardTokenized(e)})}initCardTokenized(e){var o=this;return(0,m.Z)(function*(){o.msgTokenizing="Conectando con la billetera",(yield o.digitalWalletService.isConnected())?o.verifyCardTokenized(e):o.digitalWalletService.connect().then(r=>{o.tokenizing=r,r?o.subscribeWallet():o.mboProvider.toast.warning("No fue posible iniciar registro en la billetera digital, por favor intente nuevamente","Registro fallido")})})()}verifyCardTokenized(e){this.msgTokenizing="Verificando tarjeta en la billetera",this.tokenizing=!0,this.digitalWalletService.getCardStatus(e).then(o=>{if("cardAvailable"===o)this.requiredTokenized=!0,this.tokenized=!1,this.tokenizing=!1;else{const r="cardTokenized"===o;this.tokenizing=!r,this.tokenized=r,!r&&this.enrollProduct(e)}})}enrollProduct(e){this.msgTokenizing="Registrando tarjeta en la billetera",this.tokenizing=!0,this.digitalWalletService.subscribeEnroll(({card:o,status:r})=>{if(o&&o?.id===this.product?.id&&!this.enrollVerify)if(this.enrollIntervalId&&clearInterval(this.enrollIntervalId),this.enrollVerify=!0,"enrollCardsUpdated"===r)this.verifyCardStatus(o);else{const p=_[r];p&&(this.mboProvider.toast.error(p,"Registro fallido"),this.tokenizing=!1)}}),this.digitalWalletService.enrollCard(e),this.enrollIntervalId=setInterval(()=>{this.tryVerifyEnroll++,this.tryVerifyEnroll<5?this.digitalWalletService.verifyCardEnrolled(e):(clearInterval(this.enrollIntervalId),this.tokenizing=!1,this.mboProvider.toast.error(_.enrollCardsTimeout,"Registro fallido"))},7500)}subscribeWallet(){const e=this.digitalWalletService.subscribeWallet(o=>{const{connected:r,hasWallet:p,status:C}=o;r?p?this.product&&this.verifyCardTokenized(this.product):this.walletWithError("No fue posible aprovisionar la billetera digital en tu dispositivo"):u[C]&&this.walletWithError(u[C])});this.unsubscriptions.push(e)}verifyCardStatus(e){var o=this;return(0,m.Z)(function*(){o.msgTokenizing=`Verificando tarjeta en ${o.nameSupplierStore}`,o.tokenizing=!0;const r=yield o.digitalWalletService.verifyCardStatus(e);switch(r){case"cardAvailable":o.requiredTokenized=!0,o.tokenized=!1,o.tokenizing=!1;break;case"cardTokenized":o.tokenized=!0,o.tokenizing=!1;break;default:o.mboProvider.toast.warning(l[r]||"No fue posible completar registro en la billetera digital"),o.tokenized=!1,o.tokenizing=!1}})()}registerCard(e){var o=this;return(0,m.Z)(function*(){((yield o.storageService.get(s.Z.DigitalWalletTyC))||(yield o.approvedTermsAndConditions()))&&(o.tokenized||(o.msgTokenizing=`Enviando tarjeta a ${o.nameSupplierStore}`),o.pauseFromApp=!0,o.digitalWalletService.pushCard(e).then(p=>{o.tokenized=p,p?o.mboProvider.toast.success("La tarjeta fue registrada exitosamente en la billetera digital","Registro exitoso"):o.mboProvider.toast.warning("No fue posible completar registro en la billetera digital, por favor intente nuevamente","Registro fallido"),o.tokenizing=!1}).finally(()=>{setTimeout(()=>{o.pauseFromApp=!1},1e3)}))})()}verifyStatusForResume(e){this.enabledTokenized&&!this.tokenizing&&!this.pauseFromApp&&this.verifyCardTokenized(e)}walletWithError(e){this.mboProvider.toast.error(e,"Registro fallido"),this.tokenizing=!1,this.errorTokenized=!0}approvedTermsAndConditions(){return this.bottomSheet||(this.bottomSheet=this.bottomSheetService.create(c.W)),this.msgTokenizing="Aprobando t\xe9rminos y condiciones",this.bottomSheet.open(),this.bottomSheet.waiting().catch(()=>!1).then(e=>(this.tokenizing=e,e))}}return n.\u0275fac=function(e){return new(e||n)(t.\u0275\u0275directiveInject(g.fG),t.\u0275\u0275directiveInject(b.V1),t.\u0275\u0275directiveInject(b.U8),t.\u0275\u0275directiveInject(b.yW),t.\u0275\u0275directiveInject(b.SE),t.\u0275\u0275directiveInject(k.ZL))},n.\u0275cmp=t.\u0275\u0275defineComponent({type:n,selectors:[["mbo-button-tokenization-card"]],inputs:{product:"product"},standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:1,vars:1,consts:[["class","mbo-button-tokenization-card__content",4,"ngIf"],[1,"mbo-button-tokenization-card__content"],["class","mbo-button-tokenization-card__store",3,"class","disabled","click",4,"ngIf"],["class","mbo-button-tokenization-card__devices",3,"click",4,"ngIf"],["class","mbo-button-tokenization-card__indicator",4,"ngIf"],[1,"mbo-button-tokenization-card__store",3,"disabled","click"],[1,"body1-medium"],[3,"src"],[1,"mbo-button-tokenization-card__devices",3,"click"],[1,"smalltext-medium"],[1,"mbo-button-tokenization-card__indicator"],[3,"indeterminate"],[1,"overline-medium"]],template:function(e,o){1&e&&t.\u0275\u0275template(0,d,4,3,"div",0),2&e&&t.\u0275\u0275property("ngIf",o.enabledTokenized)},dependencies:[f.CommonModule,f.NgIf,g.cp],styles:["mbo-button-tokenization-card{position:relative;display:block;margin:var(--sizing-x4) 0rem var(--sizing-x8) 0rem}mbo-button-tokenization-card .mbo-button-tokenization-card__content{position:relative;display:flex;flex-direction:column;justify-content:center;row-gap:var(--sizing-x8)}mbo-button-tokenization-card .mbo-button-tokenization-card__store{--pvt-span-font-color: var(--color-carbon-lighter-50);--pvt-span-display: block;--pvt-img-height: 12rem;display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);outline:none;border-radius:var(--sizing-x4);box-sizing:border-box;background:var(--color-carbon-darker-1000)}mbo-button-tokenization-card .mbo-button-tokenization-card__store.android{padding:var(--sizing-x6) var(--sizing-x8);border-radius:var(--sizing-x16)}mbo-button-tokenization-card .mbo-button-tokenization-card__store.ios{padding:var(--sizing-x6) var(--sizing-x8);border-radius:var(--sizing-x4)}mbo-button-tokenization-card .mbo-button-tokenization-card__store:disabled{opacity:.5}mbo-button-tokenization-card .mbo-button-tokenization-card__store>span{display:var(--pvt-span-display);color:var(--pvt-span-font-color)}mbo-button-tokenization-card .mbo-button-tokenization-card__store>img{height:var(--pvt-img-height)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices{display:flex;justify-content:center;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);outline:none;padding:var(--sizing-x6) var(--sizing-x4);background:var(--color-carbon-darker-50);border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices>span{color:var(--color-carbon-lighter-700)}mbo-button-tokenization-card .mbo-button-tokenization-card__devices>img{height:12rem}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator{display:flex;flex-direction:column;row-gap:var(--sizing-x3)}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator>span{color:var(--color-carbon-lighter-700);text-align:center}mbo-button-tokenization-card .mbo-button-tokenization-card__indicator bocc-progress-bar{box-shadow:none;padding:0rem var(--sizing-x8);box-sizing:border-box;height:var(--sizing-x1)}\n"],encapsulation:2}),n})()},31981:(E,h,i)=>{i.d(h,{W:()=>T});var m=i(17007),g=i(71776),s=i(30263),k=i(27236),b=i(87903),c=i(87956),u=i(57544),_=i(42168),t=i(99877),v=(()=>{return(d=v||(v={}))[d.TyC=0]="TyC",d[d.Next=1]="Next",v;var d})();let T=(()=>{class d{constructor(n,a,e,o){this.http=n,this.deviceService=a,this.fileManagerService=e,this.storageService=o,this.position=v.TyC,this.nameSupplierStore="",this.imgSupplierStore="",this.tycControl=new u.FormControl(!1)}ngOnInit(){this.deviceService.controller.itIsIos?(this.nameSupplierStore="Apple Pay",this.imgSupplierStore="assets/shared/logos/apple-pay-mark.svg"):(this.nameSupplierStore="Google Pay",this.imgSupplierStore="assets/shared/logos/google-pay-dark.svg")}ngBoccPortal(n){this.portal=n}onDownloadTyC(){(0,_.firstValueFrom)(this.http.get("/assets/shared/files/tyc-digital-wallet.pdf",{responseType:"blob"})).then(n=>{const a=new FileReader;a.readAsDataURL(n),a.onloadend=()=>{this.fileManagerService.downloadPdf({base64:a.result,name:`tokenization-tyc-${(0,b.CW)()}.pdf`})}})}onNext(){this.storageService.set(k.Z.DigitalWalletTyC,!0).then(()=>{this.position=v.Next})}onSubmit(){this.portal?.resolve(!0),this.portal?.close()}}return d.\u0275fac=function(n){return new(n||d)(t.\u0275\u0275directiveInject(g.HttpClient),t.\u0275\u0275directiveInject(c.U8),t.\u0275\u0275directiveInject(c.j5),t.\u0275\u0275directiveInject(c.V1))},d.\u0275cmp=t.\u0275\u0275defineComponent({type:d,selectors:[["mbo-tokenization-bottom-sheet"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:38,vars:8,consts:[[1,"mbo-tokenization-bottom-sheet__content"],[3,"src"],[3,"position"],[1,"bocc-tab-form__view"],[1,"mbo-tokenization-bottom-sheet__component"],[1,"smalltext-semibold"],[1,"mbo-tokenization-bottom-sheet__info"],[1,"mbo-tokenization-bottom-sheet__element"],["icon","wi-fi"],[1,"mbo-tokenization-bottom-sheet__description"],[1,"body2-medium"],["icon","lock"],[3,"formControl"],[3,"click"],["id","btn_tokenization_next","bocc-button","raised",3,"disabled","click"],["id","btn_tokenization_store","bocc-button","raised","suffixIcon","arrow-right",3,"click"]],template:function(n,a){1&n&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275element(1,"img",1),t.\u0275\u0275elementStart(2,"bocc-tab-form",2)(3,"div",3)(4,"div",4)(5,"label",5),t.\u0275\u0275text(6,"PAGA CON TU BILLETERA DIGITAL"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"div",6)(8,"div",7),t.\u0275\u0275element(9,"bocc-icon",8),t.\u0275\u0275elementStart(10,"div",9)(11,"label",10),t.\u0275\u0275text(12,"Paga sin contacto"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(13,"p",10),t.\u0275\u0275text(14," Acerca tu dispositivo al dat\xe1fono. \xa1Paga f\xe1cil y r\xe1pido!. "),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(15,"div",7),t.\u0275\u0275element(16,"bocc-icon",11),t.\u0275\u0275elementStart(17,"div",9)(18,"label",10),t.\u0275\u0275text(19,"Seguridad y privacidad"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(20,"p",10),t.\u0275\u0275text(21," Confirma tus pagos con los m\xe9todos de seguridad de tu dispositivo. "),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(22,"bocc-checkbox-label",12),t.\u0275\u0275text(23," Acepto los "),t.\u0275\u0275elementStart(24,"a",13),t.\u0275\u0275listener("click",function(){return a.onDownloadTyC()}),t.\u0275\u0275text(25,"terminos y condiciones"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(26,"button",14),t.\u0275\u0275listener("click",function(){return a.onNext()}),t.\u0275\u0275elementStart(27,"span"),t.\u0275\u0275text(28,"Continuar"),t.\u0275\u0275elementEnd()()()(),t.\u0275\u0275elementStart(29,"div",3)(30,"div",4)(31,"label",5),t.\u0275\u0275text(32,"CONTINUAR EN LA BILLETERA"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(33,"p",10),t.\u0275\u0275text(34),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(35,"button",15),t.\u0275\u0275listener("click",function(){return a.onSubmit()}),t.\u0275\u0275elementStart(36,"span"),t.\u0275\u0275text(37),t.\u0275\u0275elementEnd()()()()()()),2&n&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("src",a.imgSupplierStore,t.\u0275\u0275sanitizeUrl),t.\u0275\u0275advance(1),t.\u0275\u0275property("position",a.position),t.\u0275\u0275advance(7),t.\u0275\u0275styleProp("transform","rotate(90deg)"),t.\u0275\u0275advance(13),t.\u0275\u0275property("formControl",a.tycControl),t.\u0275\u0275advance(4),t.\u0275\u0275property("disabled",!a.tycControl.value),t.\u0275\u0275advance(8),t.\u0275\u0275textInterpolate1(" Para terminar la configuraci\xf3n de tu billetera digital te enviaremos a ",a.nameSupplierStore,". "),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1("Ir a ",a.nameSupplierStore,""))},dependencies:[m.CommonModule,s.P8,s.aR,s.Zl,s.qw],styles:["mbo-tokenization-bottom-sheet{position:relative;width:100%;display:block}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__content{display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x4);margin-top:var(--sizing-x8)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__content>img{width:35rem}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component{display:flex;flex-direction:column;align-items:center;row-gap:var(--sizing-x12);padding:var(--sizing-safe-form-x8);box-sizing:border-box}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component>p{color:var(--color-carbon-lighter-700);text-align:center}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__component>button{width:100%}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__info{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__element{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__element bocc-icon{color:var(--color-ocher-700)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__description{display:flex;flex-direction:column;row-gap:var(--sizing-x2)}mbo-tokenization-bottom-sheet .mbo-tokenization-bottom-sheet__description>p{color:var(--color-carbon-lighter-700)}\n"],encapsulation:2}),d})()},88239:(E,h,i)=>{i.r(h),i.d(h,{MboAppDemoPage:()=>b});var m=i(17007),g=i(90005),z=i(87956),s=i(99877);let b=(()=>{class c{constructor(l){this.digitalWallet=l,this.cards=[]}ngOnInit(){this.digitalWallet.subscribeWallet(({status:l})=>{"walletConnectionSuccess"===l&&this.requestCards()})}onRequestCards(){this.digitalWallet.connect()}onCard(l){}requestCards(){this.digitalWallet.getCards().then(l=>{this.cards=l})}}return c.\u0275fac=function(l){return new(l||c)(s.\u0275\u0275directiveInject(z.SE))},c.\u0275cmp=s.\u0275\u0275defineComponent({type:c,selectors:[["mbo-application-demo-page"]],standalone:!0,features:[s.\u0275\u0275StandaloneFeature],decls:4,vars:0,consts:[[1,"app-demo-content"],["bocc-button","outline"]],template:function(l,_){1&l&&(s.\u0275\u0275elementStart(0,"div",0),s.\u0275\u0275element(1,"mbo-button-tokenization-card"),s.\u0275\u0275elementStart(2,"button",1),s.\u0275\u0275text(3,"Solicitar tarjetas"),s.\u0275\u0275elementEnd()())},dependencies:[m.CommonModule,g.C],styles:["mbo-onboarding-carousel{background:var(--color-navy-900)}.mbo-application-demo-page__content{position:relative;display:flex;flex-direction:column;justify-content:center;width:100%;padding:var(--sizing-x8);box-sizing:border-box;row-gap:var(--sizing-x8)}.mbo-application-demo-page__content h4{text-align:center;margin-bottom:var(--sizing-x8)}.grid{display:grid;grid-template-columns:1fr 1fr 1fr 1fr;row-gap:var(--sizing-x8)}.grid .bocc-button-diamond{justify-self:center}.app-demo-content{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}\n"],encapsulation:2}),c})()}}]);