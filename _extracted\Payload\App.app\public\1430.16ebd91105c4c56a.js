(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1430],{84415:(L,y,o)=>{o.d(y,{Hc:()=>s,IV:()=>w,KR:()=>E,iD:()=>G,sT:()=>e,ve:()=>x,xg:()=>B,z8:()=>C});var u=o(83136);const C={label:"Espa\xf1ol",value:"ES"},s={label:"Ingl\xe9s",value:"EN"},A={value:u.k.RetentionGmf,fileName:"retention-gmf",label:"Gravamen movimiento financiero",tooltip:"Valores retenidos por impuesto Gravamen al Movimiento Financiero <b>(4x1.000)</b> aplicado a las transacciones financieras realizadas por el cliente",years:5,discountYear:!0},v={value:u.k.CapitalCdtCaf,fileName:"capital-cdt-caf",label:"Saldos de capital en CDT - CAF",tooltip:"Certificado de dep\xf3sito a t\xe9rmino que le permite invertir su dinero para recibir rendimientos de acuerdo al plazo establecido en el momento en que se constituye",years:5,discountYear:!0},F={value:u.k.CapitalBalance,fileName:"capital-balance",label:"Saldos de cartera e intereses pagados",tooltip:"Certificado de las obligaciones (Veh\xedculos o Motos) que los clientes tienen con el Banco",years:5,discountYear:!0},P={value:u.k.Accounts,fileName:"tax-accounts",label:"Saldos de cuentas de ahorros y corrientes",tooltip:"Constancia que contiene la informaci\xf3n de los intereses pagados y el saldo a diciembre 31 de sus productos de cuentas de ahorros y corrientes",years:5,discountYear:!0},E=[P,A,F,v,{value:u.k.ConsumerCardInterests,fileName:"customer-card-interests-mastercard",label:"Consumo e intereses de tarjeta cr\xe9dito (Mastercard)",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito MasterCard",years:5,discountYear:!0,franchise:!0,franchiseValue:"2"},{value:u.k.ConsumerCardInterests,fileName:"customer-card-interests-visa",label:"Consumo e intereses de tarjeta cr\xe9dito (Visa)",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito Visa",years:5,discountYear:!0,franchise:!0,franchiseValue:"1"}],w=[P,A,F,v,{value:u.k.ConsumerCardInterests,fileName:"customer-card-interests-visa",label:"Consumo e intereses de tarjeta cr\xe9dito",tooltip:"Certificado que contiene la informaci\xf3n del total de consumo y de los intereses pagados por su tarjeta de cr\xe9dito Visa o MasterCard",years:5,discountYear:!0,franchise:!0},{label:"Retenci\xf3n en la fuente",fileName:"retefuente",value:u.k.ReteFuente,tooltip:"Valores retenidos por concepto de impuesto de RENTA, aplicado a los clientes y/o proveedores detallado por cada tipo de pago realizado",years:5,discountYear:!0},{value:u.k.ReteIvaBank,fileName:"reteiva-bank",label:"Retenci\xf3n por IVA en Banco",tooltip:"Valores retenidos por concepto de impuesto de IVA, aplicado a los proveedores en la compra de bienes y servicios",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"},{value:u.k.ReteIcaBank,fileName:"reteica-bank",label:"Retenci\xf3n por ICA de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de industria y comercio, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito, seg\xfan las tarifas establecidas en cada municipio",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"},{value:u.k.Establishments,fileName:"tax-establishments",label:"Recaudo y cobro de comisiones de dat\xe1fonos",tooltip:" Corresponde a las comisiones cobradas durante el a\xf1o de las ventas realizadas por su comercio con tarjetas de cr\xe9dito o d\xe9bito de las franquicias MasterCard, Visa y American Express",years:5,discountYear:!0},{value:u.k.RetefuenteCard,fileName:"retefuente-card",label:"Retenci\xf3n en la fuente de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de RENTA, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito",years:5,discountYear:!0},{value:u.k.ReteIvaBankCards,fileName:"reteiva-bank-cards",label:"Retenci\xf3n por IVA de tarjetas de cr\xe9dito y/o d\xe9bito",tooltip:"Valores retenidos por concepto de impuesto de IVA, aplicado a las ventas realizadas por los clientes en sus establecimientos con tarjetas d\xe9bito y/o cr\xe9dito",years:6,discountYear:!1,periodicity:!0,transactionSource:"1"}],B=[{value:"B",label:"Bimestral",period:!0},{value:"A",label:"Anual",period:!1}],x=[{value:"1",label:"Enero - Febrero"},{value:"2",label:"Marzo - Abril"},{value:"3",label:"Mayo - Junio"},{value:"4",label:"Julio - Agosto"},{value:"5",label:"Septiembre - Octubre"},{value:"6",label:"Noviembre - Diciembre"}],G=[{value:"1",label:"Visa"},{value:"2",label:"Mastercard"}],e=[{value:"pdf",label:"PDF"},{value:"zip",label:"ZIP"}]},83136:(L,y,o)=>{o.d(y,{S:()=>C,k:()=>u});var u=(()=>{return(s=u||(u={})).ConsumerCardInterests="CERTIFICATE_CONSUMER_CARD_INTERESTS",s.CapitalBalance="CERTIFICATE_OF_CAPITAL_BALANCE",s.CapitalCdtCaf="CERTIFICATE_OF_CAPITAL_CDT_CAF",s.ReteIvaBank="CERTIFICATE_RETEIVA_BANK",s.ReteIcaBank="CERTIFICATE_RETEICA_BANK",s.ReteIvaBankCards="CERTIFICATE_RETEIVA_BANK_TC_TD",s.RetentionGmf="GMF_RETENTION_CERTIFICATE",s.ReteFuente="RETEFUENT_CERTIFICATE",s.RetefuenteCard="RETEFUENT_CERTIFICATE_TC",s.Establishments="TAX_CERTIFICATE_ESTABLISHMENTS",s.Accounts="SAVINGS_CHECKING_ACCOUNTS_CERTIFICATE",u;var s})(),C=(()=>{return(s=C||(C={})).CheckingAccount="CHECKING_ACCOUNT_CERTIFICATE",s.SavingAccount="SAVINGS_ACCOUNT_CERTIFICATE",C;var s})()},14934:(L,y,o)=>{o.d(y,{H:()=>e,w:()=>G});var u=o(15861),C=o(38165),s=o(87903),I=o(87956),m=o(98699),S=o(28640),D=o.n(S),A=o(83121),v=o(84415),F=o(89148),N=o(83136),V=o(71776),_=o(39904),M=o(42168),z=o(84757),E=o(99877);let w=(()=>{class c{constructor(a){this.http=a}requestProduct(a){return(0,M.firstValueFrom)(this.http.post(_.bV.CERTIFICATES,a).pipe((0,z.map)(({file:l})=>l)))}requestTax(a){return(0,M.firstValueFrom)(this.http.post(_.bV.CERTIFICATES,a).pipe((0,z.map)(({file:l})=>l)))}}return c.\u0275fac=function(a){return new(a||c)(E.\u0275\u0275inject(V.HttpClient))},c.\u0275prov=E.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();const B={DDA:"corriente",SDA:"ahorros"};function x({status:c},h){return c!==C.K.Saved?m.Either.failure({message:`${h} (${C.K.NotCreated?"RTC01":"RTC02"})`,value:"Generaci\xf3n no exitosa"}):m.Either.success(void 0)}let G=(()=>{class c{constructor(a){this.customerProducts=a}products(){try{return this.customerProducts.requestAccountsForTransfer().then(a=>m.Either.success(a))}catch({message:a}){return Promise.resolve(m.Either.failure({message:a}))}}taxes(){return Promise.resolve(m.Either.success({certificates:{all:v.IV,rents:v.KR},files:v.sT,franchises:v.iD,periodicities:v.xg,periods:v.ve}))}}return c.\u0275fac=function(a){return new(a||c)(E.\u0275\u0275inject(I.hM))},c.\u0275prov=E.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})(),e=(()=>{class c{constructor(a,l){this.fileManagerService=a,this.repository=l,this.productsName={},this.taxsName={}}product(a){return this.repository.requestProduct(function P(c){const{addressedto:h,balance:a,language:l,product:f}=c;return{addressedto:h,currentBalanceValue:a?f.amount:0,key:f.type===F.Gt.SavingAccount?N.S.SavingAccount:N.S.CheckingAccount,id:f.number,language:l,period:{periodDesc:null,periodCode:null,validityPeriod:null},transactionSource:null,withBalanceValue:a}}(a)).then(l=>{const{balance:f,product:d}=a;let p=this.productsName[d.type];return p||(p=`${B[d.type]}${f?"-saldo-":""}${d.shortNumber}-${(0,s.CW)()}.pdf`,this.productsName[d.type]=p),this.saveCertificate(l,p)}).catch(()=>m.Either.failure({message:"Lo sentimos, ocurrio un error al tratar de generar certificado, por favor intente m\xe1s tarde.",value:"Generaci\xf3n no exitosa"}))}tax(a){return this.requestTax({...a,single:!0}).then(({base64:l,name:f})=>this.saveCertificate(l,f)).catch(()=>m.Either.failure({message:"No pudimos generar certificado, por favor intente m\xe1s tarde.",value:"Generaci\xf3n no exitosa"}))}taxsForRent(a){const{certificates:l,type:f,year:d}=a;return(0,m.zipPromise)(l.map(p=>()=>this.requestTax({certificate:p,year:d,single:!1}).catch(()=>{}))).then(p=>{const g=p.filter(R=>!!R);return g.length?"pdf"===f?this.saveCertificatesRentWithPdf(g,d):this.saveCertificatesRentWithZip(g,d):m.Either.failure({message:"No cuentas con certificados para la declaraci\xf3n de renta",value:"Generaci\xf3n no exitosa"})})}requestTax(a){return this.repository.requestTax(function j(c){const{certificate:h,year:a,franchise:l,period:f,periodicity:d}=c;return{currentBalanceValue:0,key:h.value,id:null,period:{periodDesc:String(a),periodCode:d?.value||null,validityPeriod:f?.value&&"A"===d?.value?"1":null},transactionSource:(h.franchise?l?.value||h.franchiseValue:h.transactionSource)||null,withBalanceValue:!1}}(a)).then(l=>{const{certificate:f,single:d}=a,p=f.value;let g=this.taxsName[p];return g||(g=d?`${f.fileName}-${(0,s.CW)()}.pdf`:`${f.fileName}.pdf`,this.taxsName[p]=g),{base64:l,name:g}})}saveCertificate(a,l){return this.fileManagerService.downloadPdf({base64:a,name:l}).then(f=>x(f,"Lo sentimos, ocurrio un error al tratar de guardar certificado en el dispositivo. Por favor volver a intentarlo"))}saveCertificatesRentWithPdf(a,l){var f=this;return(0,u.Z)(function*(){const d=yield A.PDFDocument.create();return d.setProducer("com.grupoavaloc1.bancamovil"),d.setCreationDate(new Date),yield Promise.all(a.map(function(){var p=(0,u.Z)(function*({base64:g}){const R=yield A.PDFDocument.load(g,{ignoreEncryption:!0});(yield d.copyPages(R,R.getPageIndices())).forEach(k=>{d.addPage(k)})});return function(g){return p.apply(this,arguments)}}())),d.saveAsBase64().then(p=>f.fileManagerService.downloadPdf({base64:p,name:`occ-certificados-renta-${l}-${(0,s.CW)()}.pdf`}).then(g=>x(g,"No pudimos almacenar los certificados en el dispositivo. Por favor volver a intentarlo")))})()}saveCertificatesRentWithZip(a,l){const f=new(D());return a.forEach(({base64:d,name:p})=>{f.file(p,d,{base64:!0})}),f.generateAsync({type:"base64"}).then(d=>this.fileManagerService.downloadZip({base64:d,name:`occ-certificados-renta-${l}-${(0,s.CW)()}.zip`}).then(p=>x(p,"No pudimos almacenar los certificados en el dispositivo. Por favor volver a intentarlo")))}}return c.\u0275fac=function(a){return new(a||c)(E.\u0275\u0275inject(I.j5),E.\u0275\u0275inject(w))},c.\u0275prov=E.\u0275\u0275defineInjectable({token:c,factory:c.\u0275fac,providedIn:"root"}),c})()},51430:(L,y,o)=>{o.r(y),o.d(y,{MboTaxCertificationsPageModule:()=>H});var u=o(17007),C=o(78007),s=o(30263),I=o(15861),m=o(24495),S=o(39904),D=o(87903),A=o(95437),v=o(57544);class F{constructor(t){this.value=t}get title(){return this.value.label}get description(){return this.value.label}compareTo({value:t}){return this.value.value===t}filtrable(t){return!0}}class N{constructor(t){this.value=t}get title(){return String(this.value)}get description(){return String(this.value)}compareTo(t){return this.value===t}filtrable(t){return!0}}class P{constructor(t){this.value=t}get title(){return this.value.label}get description(){return this.value.label}compareTo({value:t}){return this.value.value===t}filtrable(t){return!0}}class j{constructor(t){this.value=t}get title(){return this.value.label}get description(){return this.value.label}compareTo({value:t}){return this.value.value===t}filtrable(t){return!0}}class V{constructor(t){this.value=t}get title(){return this.value.label}get description(){return this.value.label}compareTo({value:t}){return this.value.value===t}filtrable(t){return!0}}class _{constructor(t){this.file=t}get title(){return this.file.label}get description(){return this.file.label}get value(){return this.file.value}compareTo(t){return this.file.value===t}filtrable(t){return!0}}var x=o(14934);class G extends v.FormGroup{constructor(){super({controls:{certificate:new v.FormControl(void 0,[m.C1]),year:new v.FormControl(void 0,[m.C1]),periodicity:new v.FormControl,period:new v.FormControl,franchise:new v.FormControl}})}get certificate(){return this.controls.certificate}get year(){return this.controls.year}get periodicity(){return this.controls.periodicity}get period(){return this.controls.period}get franchise(){return this.controls.franchise}resetForCertificate(){const t=this.certificate?.value?.periodicity,i=this.certificate?.value?.franchise;this.periodicity.reset(),this.year.reset(),this.period.reset(),this.franchise.reset(),this.periodicity.setValidators(t?[m.C1]:[]),this.period.setValidators([]),this.franchise.setValidators(i?[m.C1]:[])}resetForPeriodicity(){const t=this.periodicity.value;this.period.reset(),this.period.setValidators(t?.period?[m.C1]:null)}certification(){return{certificate:this.certificate.value,year:this.year.value,periodicity:this.periodicity.value,period:this.period.value,franchise:this.franchise.value}}}var e=o(99877),c=o(48774),h=o(66613),a=o(45542),l=o(48030),f=o(75283),d=o(52808);function p(n,t){if(1&n&&(e.\u0275\u0275elementStart(0,"bocc-alert",15),e.\u0275\u0275element(1,"p",26),e.\u0275\u0275elementEnd()),2&n){const i=e.\u0275\u0275nextContext();e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(1),e.\u0275\u0275property("innerHTML",i.controls.certificate.value.tooltip,e.\u0275\u0275sanitizeHtml)}}const{CERTIFICATIONS:g,PRODUCTS:R}=S.Z6.CUSTOMER;var T=(()=>{return(n=T||(T={}))[n.All=0]="All",n[n.Rent=1]="Rent",T;var n})();let k=(()=>{class n{constructor(i,r,b){this.mboProvider=i,this.requestConfiguration=r,this.downloaderCertificates=b,this.unsubscriptions=[],this.position=T.All,this.certificatesRent=[],this.yearsRent=[],this.files=[],this.certificates=[],this.years=[],this.periodicities=[],this.periods=[],this.franchises=[],this.backAction={id:"btn_tax-certifications_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(g.HOME)}},this.cancelAction={id:"btn_tax-certifications_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.back(R.HOME)}},this.headers=[{label:"Para tu declaraci\xf3n de renta",value:T.Rent},{label:"Todos los certificados",value:T.All}],this.controls=new G}ngOnInit(){this.unsubscriptions.push(this.controls.certificate.subscribe(i=>{if(i){const{years:r,discountYear:b}=i;this.years=this.defineCertificateYears(r,b)}this.controls.resetForCertificate()})),this.unsubscriptions.push(this.controls.periodicity.subscribe(()=>{this.controls.resetForPeriodicity()})),this.yearsRent=this.defineCertificateYears(5,!0),this.initializatedConfiguration()}ngOnDestroy(){this.unsubscriptions.map(i=>i())}get requestInvalid(){return this.position===T.Rent?this.yearRentControl.invalid:this.controls.invalid}onWhatsapp(){(0,D.Gw)(S.BA.WHATSAPP)}onRequest(){this.position===T.Rent?this.requestCertificatesForRent():this.requestCertificateSingle()}defineCertificateYears(i,r){const b=(new Date).getFullYear(),O=r?b-1:b,Y=[];for(let U=0;U<i;U++)Y.push(new N(O-U));return Y}initializatedConfiguration(){var i=this;return(0,I.Z)(function*(){(yield i.requestConfiguration.taxes()).when({success:r=>{const{certificates:b,files:O,franchises:Y,periodicities:U,periods:K}=r;i.certificates=function M(n){return n.map(t=>new F(t))}(b.all),i.files=function B(n){return n.map(t=>new _(t))}(O),i.certificatesRent=b.rents,i.periodicities=function z(n){return n.map(t=>new P(t))}(U),i.periods=function E(n){return n.map(t=>new j(t))}(K),i.franchises=function w(n){return n.map(t=>new V(t))}(Y);const[W]=i.yearsRent;i.yearRentControl=new v.FormControl(W.value,[m.C1]),i.fileRentControl=new v.FormControl("pdf")}})})()}requestCertificatesForRent(){var i=this;return(0,I.Z)(function*(){i.mboProvider.loader.open("Solicitando certificados, por favor espere..."),(yield i.downloaderCertificates.taxsForRent({certificates:i.certificatesRent,year:i.yearRentControl.value,type:i.fileRentControl.value})).when({success:()=>{i.mboProvider.toast.success("Los certificados han sido generados y descargados en tu dispositivo.","Generaci\xf3n exitosa")},failure:({message:r,value:b})=>{i.mboProvider.toast.error(r,b)}},()=>{i.mboProvider.loader.close()})})()}requestCertificateSingle(){var i=this;return(0,I.Z)(function*(){i.mboProvider.loader.open("Solicitando certificado, por favor espere..."),(yield i.downloaderCertificates.tax(i.controls.certification())).when({failure:({message:r,value:b})=>{i.mboProvider.toast.error(r,b)}},()=>{i.mboProvider.loader.close()})})()}}return n.\u0275fac=function(i){return new(i||n)(e.\u0275\u0275directiveInject(A.ZL),e.\u0275\u0275directiveInject(x.w),e.\u0275\u0275directiveInject(x.H))},n.\u0275cmp=e.\u0275\u0275defineComponent({type:n,selectors:[["mbo-tax-certifications-page"]],decls:62,vars:27,consts:[[1,"mbo-tax-certifications-page__content"],[1,"mbo-tax-certifications-page__header"],["title","C. tributarias",3,"leftAction","rightAction"],[1,"mbo-tax-certifications-page__body"],[3,"tabs","invert","value","valueChange"],[1,"mbo-tax-certifications-page__forms"],[3,"position"],[1,"bocc-tab-form__view"],[1,"mbo-tax-certifications-page__form"],["elementId","lst_tax-certifications_type","placeholder","Seleccione el certificado","label","Tipo de certificado",3,"suggestions","formControl"],["icon","bell",3,"visible",4,"ngIf"],["elementId","lst_tax-certifications_year","placeholder","Seleccionar a\xf1o","label","A\xf1o",3,"suggestions","formControl","disabled"],["elementId","lst_tax-certifications_periodicity","placeholder","Seleccionar periodicidad","label","Periodicidad",3,"suggestions","formControl","hidden"],["elementId","lst_tax-certifications_period","placeholder","Seleccionar periodo","label","Periodo",3,"suggestions","formControl","hidden"],["elementId","lst_tax-certifications_franchise","placeholder","Seleccionar franquicia","label","Franquicia",3,"suggestions","formControl","hidden"],["icon","bell",3,"visible"],[3,"click"],[1,"mbo-tax-certifications-page__dropdowns"],["elementId","lst_tax-certifications-rent_year","label","A\xf1o gravable",3,"suggestions","formControl"],["elementId","lst_tax-certifications-rent_file","label","Descargar como",3,"suggestions","formControl"],[1,"subheader"],[1,"flex"],["src","assets/shared/icons/credit-cards/mastercard-regular.svg"],["src","assets/shared/icons/credit-cards/visa-blue.svg"],[1,"mbo-tax-certifications-page__footer"],["id","btn_tax-certifications_submit","bocc-button","raised","prefixIcon","certificate-products",3,"disabled","click"],[3,"innerHTML"]],template:function(i,r){1&i&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"bocc-tab-header",4),e.\u0275\u0275listener("valueChange",function(O){return r.position=O}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"bocc-tab-form",6)(7,"div",7)(8,"div",8),e.\u0275\u0275element(9,"bocc-select-box",9),e.\u0275\u0275template(10,p,2,2,"bocc-alert",10),e.\u0275\u0275element(11,"bocc-select-box",11)(12,"bocc-select-box",12)(13,"bocc-select-box",13)(14,"bocc-select-box",14),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",7)(16,"div",8)(17,"bocc-alert",15)(18,"p"),e.\u0275\u0275text(19," Ponemos a tu disposici\xf3n los certificados que necesitar\xe1s cuando vayas a presentar tu declaraci\xf3n de renta ante la DIAN. Solicita los certificados de vivienda, en nuestro canal de "),e.\u0275\u0275elementStart(20,"a",16),e.\u0275\u0275listener("click",function(){return r.onWhatsapp()}),e.\u0275\u0275text(21,"WhatsApp"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(22,". "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(23,"div",17),e.\u0275\u0275element(24,"bocc-select-box",18)(25,"bocc-select-box",19),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"ul")(27,"li",20)(28,"span"),e.\u0275\u0275text(29,"DE TUS TARJETAS DE CR\xc9DITO"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(30,"li")(31,"div",21)(32,"span"),e.\u0275\u0275text(33," Consumo e intereses de tarjeta de cr\xe9dito."),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(34,"div",21),e.\u0275\u0275element(35,"img",22)(36,"img",23),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(37,"li",20)(38,"span"),e.\u0275\u0275text(39,"DE TUS CUENTAS"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(40,"li")(41,"span"),e.\u0275\u0275text(42,"Saldos de cuentas."),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(43,"li")(44,"span"),e.\u0275\u0275text(45,"Gravamen al movimiento financiero."),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(46,"li",20)(47,"span"),e.\u0275\u0275text(48,"DE TUS CR\xc9DITOS"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(49,"li")(50,"span"),e.\u0275\u0275text(51,"Saldos de cartera e intereses pagados."),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(52,"li",20)(53,"span"),e.\u0275\u0275text(54,"DE TUS INVERSIONES"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(55,"li")(56,"span"),e.\u0275\u0275text(57,"Saldo en CDTs-CAF."),e.\u0275\u0275elementEnd()()()()()()()()(),e.\u0275\u0275elementStart(58,"div",24)(59,"button",25),e.\u0275\u0275listener("click",function(){return r.onRequest()}),e.\u0275\u0275elementStart(60,"span"),e.\u0275\u0275text(61,"Generar certificado"),e.\u0275\u0275elementEnd()()()),2&i&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",r.backAction)("rightAction",r.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("tabs",r.headers)("invert",!0)("value",r.position),e.\u0275\u0275advance(2),e.\u0275\u0275property("position",r.position),e.\u0275\u0275advance(3),e.\u0275\u0275property("suggestions",r.certificates)("formControl",r.controls.certificate),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",r.controls.certificate.value),e.\u0275\u0275advance(1),e.\u0275\u0275property("suggestions",r.years)("formControl",r.controls.year)("disabled",r.controls.certificate.invalid),e.\u0275\u0275advance(1),e.\u0275\u0275property("suggestions",r.periodicities)("formControl",r.controls.periodicity)("hidden",!(null!=r.controls.certificate.value&&r.controls.certificate.value.periodicity)),e.\u0275\u0275advance(1),e.\u0275\u0275property("suggestions",r.periods)("formControl",r.controls.period)("hidden",!(null!=r.controls.periodicity.value&&r.controls.periodicity.value.period)),e.\u0275\u0275advance(1),e.\u0275\u0275property("suggestions",r.franchises)("formControl",r.controls.franchise)("hidden",!(null!=r.controls.certificate.value&&r.controls.certificate.value.franchise)),e.\u0275\u0275advance(3),e.\u0275\u0275property("visible",!0),e.\u0275\u0275advance(7),e.\u0275\u0275property("suggestions",r.yearsRent)("formControl",r.yearRentControl),e.\u0275\u0275advance(1),e.\u0275\u0275property("suggestions",r.files)("formControl",r.fileRentControl),e.\u0275\u0275advance(34),e.\u0275\u0275property("disabled",r.requestInvalid))},dependencies:[u.NgIf,c.J,h.B,a.P,l.t,f.q,d.G],styles:['/*!\n * MBO TaxCertifications Page\n * v2.2.0\n * Author: MB Frontend Developers\n * Created: 04/Oct/2022\n * Updated: 30/Sep/2024\n*/mbo-tax-certifications-page{--bocc-tab-header-border-color: var(--color-carbon-lighter-300);--bocc-tab-header-indicator-color: var(--color-ocher-700);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x8);justify-content:space-between}mbo-tax-certifications-page .mbo-tax-certifications-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-tax-certifications-page .mbo-tax-certifications-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__body bocc-alert{padding:0rem var(--sizing-x2);box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__forms{width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__form{display:flex;flex-direction:column;row-gap:var(--sizing-x12)}mbo-tax-certifications-page .mbo-tax-certifications-page__form bocc-alert p>a{color:var(--color-amathyst-700);text-decoration:underline}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul{display:flex;flex-direction:column;row-gap:var(--sizing-x8);list-style-type:none;padding-inline-start:0rem}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li{display:flex;align-items:center;-moz-column-gap:var(--sizing-x6);column-gap:var(--sizing-x6);font-size:var(--body2-size);letter-spacing:var(--body2-letter-spacing);line-height:var(--body2-line-height);color:var(--color-carbon-lighter-700);font-weight:var(--font-weight-medium)}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li.subheader{font-size:var(--overline-size);letter-spacing:var(--overline-letter-spacing);line-height:var(--overline-line-height);color:var(--color-carbon-lighter-400);font-weight:var(--font-weight-semibold)}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li:not(.subheader){padding-left:var(--sizing-x6);box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li:not(.subheader):before{display:block;content:"";width:var(--sizing-x2);height:var(--sizing-x2);background:var(--color-blue-700);transform:rotate(45deg)}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li .flex{display:flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4)}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li>.flex{width:100%}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li span{display:block;width:100%;box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__form>ul>li>*:first-child{padding-left:var(--sizing-x2)}mbo-tax-certifications-page .mbo-tax-certifications-page__dropdowns{display:grid;grid-template-columns:1fr 2fr;-moz-column-gap:var(--sizing-x8);column-gap:var(--sizing-x8)}mbo-tax-certifications-page .mbo-tax-certifications-page__footer{width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-tax-certifications-page .mbo-tax-certifications-page__footer button{width:100%}\n'],encapsulation:2}),n})(),H=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=e.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=e.\u0275\u0275defineInjector({imports:[u.CommonModule,C.RouterModule.forChild([{path:"",component:k}]),s.Jx,s.B4,s.P8,s.tv,s.qw,s.Gf,s.T9]}),n})()}}]);