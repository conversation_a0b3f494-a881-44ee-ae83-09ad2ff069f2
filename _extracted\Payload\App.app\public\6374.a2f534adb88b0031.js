(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6374],{16374:(y,l,o)=>{o.r(l),o.d(l,{MboCelToCelSendSourcePageModule:()=>M});var d=o(17007),u=o(78007),m=o(30263),g=o(79798),p=o(15861),v=o(39904),C=o(95437),i=o(17758),h=o(98487),e=o(99877),f=o(4663),S=o(48774);const b=v.Z6.TRANSFERS.CELTOCEL;let T=(()=>{class t{constructor(n,c,a,s){this.mboProvider=n,this.requestConfiguration=c,this.managerCelToCel=a,this.cancelProvider=s,this.confirmation=!1,this.products=[],this.requesting=!0,this.cancelAction={id:"btn_cel-to-cel-send-source_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(n){this.managerCelToCel.setSource(n,!1).when({success:()=>{this.mboProvider.navigation.next(b.SEND.DESTINATION)}})}initializatedConfiguration(){var n=this;return(0,p.Z)(function*(){(yield n.requestConfiguration.source()).when({success:({products:c})=>{n.products=c}},()=>{n.requesting=!1})})()}}return t.\u0275fac=function(n){return new(n||t)(e.\u0275\u0275directiveInject(C.ZL),e.\u0275\u0275directiveInject(i.ZW),e.\u0275\u0275directiveInject(i.Ey),e.\u0275\u0275directiveInject(h.T))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-cel-to-cel-send-source-page"]],decls:6,vars:3,consts:[[1,"mbo-cel-to-cel-send-source-page__content"],[1,"mbo-cel-to-cel-send-source-page__header"],["title","Origen","progress","25%",3,"rightAction"],[1,"mbo-cel-to-cel-send-source-page__body"],[3,"skeleton","products","select"]],template:function(n,c){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),e.\u0275\u0275listener("select",function(s){return c.onProduct(s)}),e.\u0275\u0275text(5," \xbfDesde d\xf3nde deseas transferir hoy? "),e.\u0275\u0275elementEnd()()()),2&n&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("rightAction",c.cancelAction),e.\u0275\u0275advance(2),e.\u0275\u0275property("skeleton",c.requesting)("products",c.products))},dependencies:[f.c,S.J],styles:["/*!\n * MBO CelToCelSendSource Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 04/Nov/2022\n * Updated: 06/Feb/2024\n*/mbo-cel-to-cel-send-source-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-cel-to-cel-send-source-page .mbo-cel-to-cel-send-source-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2)}mbo-cel-to-cel-send-source-page .mbo-cel-to-cel-send-source-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),t})(),M=(()=>{class t{}return t.\u0275fac=function(n){return new(n||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,u.RouterModule.forChild([{path:"",component:T}]),g.cV,m.Jx]}),t})()}}]);