(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6895],{36895:(ji,Me,le)=>{le.r(Me),le.d(Me,{APP_BASE_HREF:()=>De,AsyncPipe:()=>wt,BrowserPlatformLocation:()=>fe,CommonModule:()=>Zn,CurrencyPipe:()=>Nt,DATE_PIPE_DEFAULT_OPTIONS:()=>Lt,DATE_PIPE_DEFAULT_TIMEZONE:()=>bt,DOCUMENT:()=>k,DatePipe:()=>It,DecimalPipe:()=>Ot,FormStyle:()=>E,FormatWidth:()=>F,HashLocationStrategy:()=>un,I18nPluralPipe:()=>vt,I18nSelectPipe:()=>Pt,IMAGE_CONFIG:()=>Qt,IMAGE_LOADER:()=>ve,JsonPipe:()=>Tt,KeyValuePipe:()=>Mt,LOCATION_INITIALIZED:()=>on,Location:()=>Ne,LocationStrategy:()=>V,LowerCasePipe:()=>_t,NgClass:()=>at,NgComponentOutlet:()=>ct,NgFor:()=>_e,NgForOf:()=>_e,NgForOfContext:()=>ft,NgIf:()=>Dt,NgIfContext:()=>gt,NgLocaleLocalization:()=>st,NgLocalization:()=>se,NgOptimizedImage:()=>ki,NgPlural:()=>Ae,NgPluralCase:()=>Ft,NgStyle:()=>Et,NgSwitch:()=>ue,NgSwitchCase:()=>mt,NgSwitchDefault:()=>Ct,NgTemplateOutlet:()=>yt,NumberFormatStyle:()=>I,NumberSymbol:()=>c,PRECONNECT_CHECK_BLOCKLIST:()=>Wt,PathLocationStrategy:()=>Re,PercentPipe:()=>Rt,PlatformLocation:()=>q,Plural:()=>w,SlicePipe:()=>kt,TitleCasePipe:()=>St,TranslationWidth:()=>f,UpperCasePipe:()=>At,VERSION:()=>Qn,ViewportScroller:()=>ei,WeekDay:()=>b,XhrFactory:()=>ri,formatCurrency:()=>nt,formatDate:()=>Xe,formatNumber:()=>rt,formatPercent:()=>it,getCurrencySymbol:()=>Ke,getLocaleCurrencyCode:()=>gn,getLocaleCurrencyName:()=>Dn,getLocaleCurrencySymbol:()=>hn,getLocaleDateFormat:()=>G,getLocaleDateTimeFormat:()=>W,getLocaleDayNames:()=>Ue,getLocaleDayPeriods:()=>ze,getLocaleDirection:()=>mn,getLocaleEraNames:()=>Ve,getLocaleExtraDayPeriodRules:()=>We,getLocaleExtraDayPeriods:()=>Ye,getLocaleFirstDayOfWeek:()=>dn,getLocaleId:()=>$e,getLocaleMonthNames:()=>je,getLocaleNumberFormat:()=>Q,getLocaleNumberSymbol:()=>v,getLocalePluralCase:()=>Ge,getLocaleTimeFormat:()=>H,getLocaleWeekEndRange:()=>fn,getNumberOfCurrencyDigits:()=>Ze,isPlatformBrowser:()=>Xn,isPlatformServer:()=>jt,isPlatformWorkerApp:()=>Jn,isPlatformWorkerUi:()=>qn,provideCloudflareLoader:()=>li,provideCloudinaryLoader:()=>gi,provideImageKitLoader:()=>Ei,provideImgixLoader:()=>Ai,registerLocaleData:()=>xn,\u0275DomAdapter:()=>nn,\u0275NullViewportScroller:()=>ii,\u0275PLATFORM_BROWSER_ID:()=>xt,\u0275PLATFORM_SERVER_ID:()=>$t,\u0275PLATFORM_WORKER_APP_ID:()=>zt,\u0275PLATFORM_WORKER_UI_ID:()=>Ut,\u0275getDOM:()=>J,\u0275parseCookieValue:()=>$n,\u0275setRootDomAdapter:()=>tn});var o=le(99877);let de=null;function J(){return de}function tn(e){de||(de=e)}class nn{}const k=new o.InjectionToken("DocumentToken");let q=(()=>{class e{historyGo(t){throw new Error("Not implemented")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:function(){return function rn(){return(0,o.\u0275\u0275inject)(fe)}()},providedIn:"platform"}),e})();const on=new o.InjectionToken("Location Initialized");let fe=(()=>{class e extends q{constructor(t){super(),this._doc=t,this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return J().getBaseHref(this._doc)}onPopState(t){const i=J().getGlobalEventTarget(this._doc,"window");return i.addEventListener("popstate",t,!1),()=>i.removeEventListener("popstate",t)}onHashChange(t){const i=J().getGlobalEventTarget(this._doc,"window");return i.addEventListener("hashchange",t,!1),()=>i.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,i,r){Be()?this._history.pushState(t,i,r):this._location.hash=r}replaceState(t,i,r){Be()?this._history.replaceState(t,i,r):this._location.hash=r}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(k))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:function(){return function sn(){return new fe((0,o.\u0275\u0275inject)(k))}()},providedIn:"platform"}),e})();function Be(){return!!window.history.pushState}function he(e,n){if(0==e.length)return n;if(0==n.length)return e;let t=0;return e.endsWith("/")&&t++,n.startsWith("/")&&t++,2==t?e+n.substring(1):1==t?e+n:e+"/"+n}function Oe(e){const n=e.match(/#|\?|$/),t=n&&n.index||e.length;return e.slice(0,t-("/"===e[t-1]?1:0))+e.slice(t)}function O(e){return e&&"?"!==e[0]?"?"+e:e}let V=(()=>{class e{historyGo(t){throw new Error("Not implemented")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:function(){return(0,o.inject)(Re)},providedIn:"root"}),e})();const De=new o.InjectionToken("appBaseHref");let Re=(()=>{class e extends V{constructor(t,i){super(),this._platformLocation=t,this._removeListenerFns=[],this._baseHref=i??this._platformLocation.getBaseHrefFromDOM()??(0,o.inject)(k).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return he(this._baseHref,t)}path(t=!1){const i=this._platformLocation.pathname+O(this._platformLocation.search),r=this._platformLocation.hash;return r&&t?`${i}${r}`:i}pushState(t,i,r,s){const u=this.prepareExternalUrl(r+O(s));this._platformLocation.pushState(t,i,u)}replaceState(t,i,r,s){const u=this.prepareExternalUrl(r+O(s));this._platformLocation.replaceState(t,i,u)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(q),o.\u0275\u0275inject(De,8))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),un=(()=>{class e extends V{constructor(t,i){super(),this._platformLocation=t,this._baseHref="",this._removeListenerFns=[],null!=i&&(this._baseHref=i)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let i=this._platformLocation.hash;return null==i&&(i="#"),i.length>0?i.substring(1):i}prepareExternalUrl(t){const i=he(this._baseHref,t);return i.length>0?"#"+i:i}pushState(t,i,r,s){let u=this.prepareExternalUrl(r+O(s));0==u.length&&(u=this._platformLocation.pathname),this._platformLocation.pushState(t,i,u)}replaceState(t,i,r,s){let u=this.prepareExternalUrl(r+O(s));0==u.length&&(u=this._platformLocation.pathname),this._platformLocation.replaceState(t,i,u)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(q),o.\u0275\u0275inject(De,8))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})(),Ne=(()=>{class e{constructor(t){this._subject=new o.EventEmitter,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=t;const i=this._locationStrategy.getBaseHref();this._basePath=function ln(e){if(new RegExp("^(https?:)?//").test(e)){const[,t]=e.split(/\/\/[^\/]+/);return t}return e}(Oe(ke(i))),this._locationStrategy.onPopState(r=>{this._subject.emit({url:this.path(!0),pop:!0,state:r.state,type:r.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,i=""){return this.path()==this.normalize(t+O(i))}normalize(t){return e.stripTrailingSlash(function cn(e,n){if(!e||!n.startsWith(e))return n;const t=n.substring(e.length);return""===t||["/",";","?","#"].includes(t[0])?t:n}(this._basePath,ke(t)))}prepareExternalUrl(t){return t&&"/"!==t[0]&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,i="",r=null){this._locationStrategy.pushState(r,"",t,i),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+O(i)),r)}replaceState(t,i="",r=null){this._locationStrategy.replaceState(r,"",t,i),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+O(i)),r)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(i=>{this._notifyUrlChangeListeners(i.url,i.state)})),()=>{const i=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(i,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",i){this._urlChangeListeners.forEach(r=>r(t,i))}subscribe(t,i,r){return this._subject.subscribe({next:t,error:i,complete:r})}}return e.normalizeQueryParams=O,e.joinWithSlash=he,e.stripTrailingSlash=Oe,e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(V))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:function(){return function an(){return new Ne((0,o.\u0275\u0275inject)(V))}()},providedIn:"root"}),e})();function ke(e){return e.replace(/\/index.html$/,"")}const xe={ADP:[void 0,void 0,0],AFN:[void 0,"\u060b",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058f",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20bc"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09f3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xa5","\xa5"],COP:[void 0,"$",2],CRC:[void 0,"\u20a1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010d",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xa3"],ESP:[void 0,"\u20a7",0],EUR:["\u20ac"],FJD:[void 0,"$"],FKP:[void 0,"\xa3"],GBP:["\xa3"],GEL:[void 0,"\u20be"],GHS:[void 0,"GH\u20b5"],GIP:[void 0,"\xa3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20aa"],INR:["\u20b9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xa5",void 0,0],KHR:[void 0,"\u17db"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20a9",0],KRW:["\u20a9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20b8"],LAK:[void 0,"\u20ad",0],LBP:[void 0,"L\xa3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20ae",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20a6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20b1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20b2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20bd"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xa3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xa3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xa3",0],THB:[void 0,"\u0e3f"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20ba"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20b4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20ab",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202fCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xa4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]};var I=(()=>((I=I||{})[I.Decimal=0]="Decimal",I[I.Percent=1]="Percent",I[I.Currency=2]="Currency",I[I.Scientific=3]="Scientific",I))(),w=(()=>((w=w||{})[w.Zero=0]="Zero",w[w.One=1]="One",w[w.Two=2]="Two",w[w.Few=3]="Few",w[w.Many=4]="Many",w[w.Other=5]="Other",w))(),E=(()=>((E=E||{})[E.Format=0]="Format",E[E.Standalone=1]="Standalone",E))(),f=(()=>((f=f||{})[f.Narrow=0]="Narrow",f[f.Abbreviated=1]="Abbreviated",f[f.Wide=2]="Wide",f[f.Short=3]="Short",f))(),F=(()=>((F=F||{})[F.Short=0]="Short",F[F.Medium=1]="Medium",F[F.Long=2]="Long",F[F.Full=3]="Full",F))(),c=(()=>((c=c||{})[c.Decimal=0]="Decimal",c[c.Group=1]="Group",c[c.List=2]="List",c[c.PercentSign=3]="PercentSign",c[c.PlusSign=4]="PlusSign",c[c.MinusSign=5]="MinusSign",c[c.Exponential=6]="Exponential",c[c.SuperscriptingExponent=7]="SuperscriptingExponent",c[c.PerMille=8]="PerMille",c[c.Infinity=9]="Infinity",c[c.NaN=10]="NaN",c[c.TimeSeparator=11]="TimeSeparator",c[c.CurrencyDecimal=12]="CurrencyDecimal",c[c.CurrencyGroup=13]="CurrencyGroup",c))(),b=(()=>((b=b||{})[b.Sunday=0]="Sunday",b[b.Monday=1]="Monday",b[b.Tuesday=2]="Tuesday",b[b.Wednesday=3]="Wednesday",b[b.Thursday=4]="Thursday",b[b.Friday=5]="Friday",b[b.Saturday=6]="Saturday",b))();function $e(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.LocaleId]}function ze(e,n,t){const i=(0,o.\u0275findLocaleData)(e),s=P([i[o.\u0275LocaleDataIndex.DayPeriodsFormat],i[o.\u0275LocaleDataIndex.DayPeriodsStandalone]],n);return P(s,t)}function Ue(e,n,t){const i=(0,o.\u0275findLocaleData)(e),s=P([i[o.\u0275LocaleDataIndex.DaysFormat],i[o.\u0275LocaleDataIndex.DaysStandalone]],n);return P(s,t)}function je(e,n,t){const i=(0,o.\u0275findLocaleData)(e),s=P([i[o.\u0275LocaleDataIndex.MonthsFormat],i[o.\u0275LocaleDataIndex.MonthsStandalone]],n);return P(s,t)}function Ve(e,n){return P((0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.Eras],n)}function dn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.FirstDayOfWeek]}function fn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.WeekendRange]}function G(e,n){return P((0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.DateFormat],n)}function H(e,n){return P((0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.TimeFormat],n)}function W(e,n){return P((0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.DateTimeFormat],n)}function v(e,n){const t=(0,o.\u0275findLocaleData)(e),i=t[o.\u0275LocaleDataIndex.NumberSymbols][n];if(typeof i>"u"){if(n===c.CurrencyDecimal)return t[o.\u0275LocaleDataIndex.NumberSymbols][c.Decimal];if(n===c.CurrencyGroup)return t[o.\u0275LocaleDataIndex.NumberSymbols][c.Group]}return i}function Q(e,n){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.NumberFormats][n]}function hn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.CurrencySymbol]||null}function Dn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.CurrencyName]||null}function gn(e){return(0,o.\u0275getLocaleCurrencyCode)(e)}const Ge=o.\u0275getLocalePluralCase;function He(e){if(!e[o.\u0275LocaleDataIndex.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[o.\u0275LocaleDataIndex.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function We(e){const n=(0,o.\u0275findLocaleData)(e);return He(n),(n[o.\u0275LocaleDataIndex.ExtraData][2]||[]).map(i=>"string"==typeof i?ge(i):[ge(i[0]),ge(i[1])])}function Ye(e,n,t){const i=(0,o.\u0275findLocaleData)(e);He(i);const s=P([i[o.\u0275LocaleDataIndex.ExtraData][0],i[o.\u0275LocaleDataIndex.ExtraData][1]],n)||[];return P(s,t)||[]}function mn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.Directionality]}function P(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function ge(e){const[n,t]=e.split(":");return{hours:+n,minutes:+t}}function Ke(e,n,t="en"){const i=function pn(e){return(0,o.\u0275findLocaleData)(e)[o.\u0275LocaleDataIndex.Currencies]}(t)[e]||xe[e]||[],r=i[1];return"narrow"===n&&"string"==typeof r?r:i[0]||e}const Cn=2;function Ze(e){let n;const t=xe[e];return t&&(n=t[2]),"number"==typeof n?n:Cn}const Fn=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Y={},En=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var A=(()=>((A=A||{})[A.Short=0]="Short",A[A.ShortGMT=1]="ShortGMT",A[A.Long=2]="Long",A[A.Extended=3]="Extended",A))(),l=(()=>((l=l||{})[l.FullYear=0]="FullYear",l[l.Month=1]="Month",l[l.Date=2]="Date",l[l.Hours=3]="Hours",l[l.Minutes=4]="Minutes",l[l.Seconds=5]="Seconds",l[l.FractionalSeconds=6]="FractionalSeconds",l[l.Day=7]="Day",l))(),d=(()=>((d=d||{})[d.DayPeriods=0]="DayPeriods",d[d.Days=1]="Days",d[d.Months=2]="Months",d[d.Eras=3]="Eras",d))();function Xe(e,n,t,i){let r=function vn(e){if(Qe(e))return e;if("number"==typeof e&&!isNaN(e))return new Date(e);if("string"==typeof e){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){const[r,s=1,u=1]=e.split("-").map(a=>+a);return ee(r,s-1,u)}const t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let i;if(i=e.match(Fn))return function Pn(e){const n=new Date(0);let t=0,i=0;const r=e[8]?n.setUTCFullYear:n.setFullYear,s=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),i=Number(e[9]+e[11])),r.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));const u=Number(e[4]||0)-t,a=Number(e[5]||0)-i,g=Number(e[6]||0),p=Math.floor(1e3*parseFloat("0."+(e[7]||0)));return s.call(n,u,a,g,p),n}(i)}const n=new Date(e);if(!Qe(n))throw new Error(`Unable to convert "${e}" into a date`);return n}(e);n=R(t,n)||n;let a,u=[];for(;n;){if(a=En.exec(n),!a){u.push(n);break}{u=u.concat(a.slice(1));const D=u.pop();if(!D)break;n=D}}let g=r.getTimezoneOffset();i&&(g=qe(i,g),r=function In(e,n,t){const i=t?-1:1,r=e.getTimezoneOffset();return function Ln(e,n){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+n),e}(e,i*(qe(n,r)-r))}(r,i,!0));let p="";return u.forEach(D=>{const h=function bn(e){if(me[e])return me[e];let n;switch(e){case"G":case"GG":case"GGG":n=m(d.Eras,f.Abbreviated);break;case"GGGG":n=m(d.Eras,f.Wide);break;case"GGGGG":n=m(d.Eras,f.Narrow);break;case"y":n=S(l.FullYear,1,0,!1,!0);break;case"yy":n=S(l.FullYear,2,0,!0,!0);break;case"yyy":n=S(l.FullYear,3,0,!1,!0);break;case"yyyy":n=S(l.FullYear,4,0,!1,!0);break;case"Y":n=re(1);break;case"YY":n=re(2,!0);break;case"YYY":n=re(3);break;case"YYYY":n=re(4);break;case"M":case"L":n=S(l.Month,1,1);break;case"MM":case"LL":n=S(l.Month,2,1);break;case"MMM":n=m(d.Months,f.Abbreviated);break;case"MMMM":n=m(d.Months,f.Wide);break;case"MMMMM":n=m(d.Months,f.Narrow);break;case"LLL":n=m(d.Months,f.Abbreviated,E.Standalone);break;case"LLLL":n=m(d.Months,f.Wide,E.Standalone);break;case"LLLLL":n=m(d.Months,f.Narrow,E.Standalone);break;case"w":n=pe(1);break;case"ww":n=pe(2);break;case"W":n=pe(1,!0);break;case"d":n=S(l.Date,1);break;case"dd":n=S(l.Date,2);break;case"c":case"cc":n=S(l.Day,1);break;case"ccc":n=m(d.Days,f.Abbreviated,E.Standalone);break;case"cccc":n=m(d.Days,f.Wide,E.Standalone);break;case"ccccc":n=m(d.Days,f.Narrow,E.Standalone);break;case"cccccc":n=m(d.Days,f.Short,E.Standalone);break;case"E":case"EE":case"EEE":n=m(d.Days,f.Abbreviated);break;case"EEEE":n=m(d.Days,f.Wide);break;case"EEEEE":n=m(d.Days,f.Narrow);break;case"EEEEEE":n=m(d.Days,f.Short);break;case"a":case"aa":case"aaa":n=m(d.DayPeriods,f.Abbreviated);break;case"aaaa":n=m(d.DayPeriods,f.Wide);break;case"aaaaa":n=m(d.DayPeriods,f.Narrow);break;case"b":case"bb":case"bbb":n=m(d.DayPeriods,f.Abbreviated,E.Standalone,!0);break;case"bbbb":n=m(d.DayPeriods,f.Wide,E.Standalone,!0);break;case"bbbbb":n=m(d.DayPeriods,f.Narrow,E.Standalone,!0);break;case"B":case"BB":case"BBB":n=m(d.DayPeriods,f.Abbreviated,E.Format,!0);break;case"BBBB":n=m(d.DayPeriods,f.Wide,E.Format,!0);break;case"BBBBB":n=m(d.DayPeriods,f.Narrow,E.Format,!0);break;case"h":n=S(l.Hours,1,-12);break;case"hh":n=S(l.Hours,2,-12);break;case"H":n=S(l.Hours,1);break;case"HH":n=S(l.Hours,2);break;case"m":n=S(l.Minutes,1);break;case"mm":n=S(l.Minutes,2);break;case"s":n=S(l.Seconds,1);break;case"ss":n=S(l.Seconds,2);break;case"S":n=S(l.FractionalSeconds,1);break;case"SS":n=S(l.FractionalSeconds,2);break;case"SSS":n=S(l.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":n=ne(A.Short);break;case"ZZZZZ":n=ne(A.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=ne(A.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":n=ne(A.Long);break;default:return null}return me[e]=n,n}(D);p+=h?h(r,t,g):"''"===D?"'":D.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),p}function ee(e,n,t){const i=new Date(0);return i.setFullYear(e,n,t),i.setHours(0,0,0),i}function R(e,n){const t=$e(e);if(Y[t]=Y[t]||{},Y[t][n])return Y[t][n];let i="";switch(n){case"shortDate":i=G(e,F.Short);break;case"mediumDate":i=G(e,F.Medium);break;case"longDate":i=G(e,F.Long);break;case"fullDate":i=G(e,F.Full);break;case"shortTime":i=H(e,F.Short);break;case"mediumTime":i=H(e,F.Medium);break;case"longTime":i=H(e,F.Long);break;case"fullTime":i=H(e,F.Full);break;case"short":const r=R(e,"shortTime"),s=R(e,"shortDate");i=te(W(e,F.Short),[r,s]);break;case"medium":const u=R(e,"mediumTime"),a=R(e,"mediumDate");i=te(W(e,F.Medium),[u,a]);break;case"long":const g=R(e,"longTime"),p=R(e,"longDate");i=te(W(e,F.Long),[g,p]);break;case"full":const D=R(e,"fullTime"),h=R(e,"fullDate");i=te(W(e,F.Full),[D,h])}return i&&(Y[t][n]=i),i}function te(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,i){return null!=n&&i in n?n[i]:t})),e}function T(e,n,t="-",i,r){let s="";(e<0||r&&e<=0)&&(r?e=1-e:(e=-e,s=t));let u=String(e);for(;u.length<n;)u="0"+u;return i&&(u=u.slice(u.length-n)),s+u}function S(e,n,t=0,i=!1,r=!1){return function(s,u){let a=function wn(e,n){switch(e){case l.FullYear:return n.getFullYear();case l.Month:return n.getMonth();case l.Date:return n.getDate();case l.Hours:return n.getHours();case l.Minutes:return n.getMinutes();case l.Seconds:return n.getSeconds();case l.FractionalSeconds:return n.getMilliseconds();case l.Day:return n.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}(e,s);if((t>0||a>-t)&&(a+=t),e===l.Hours)0===a&&-12===t&&(a=12);else if(e===l.FractionalSeconds)return function yn(e,n){return T(e,3).substring(0,n)}(a,n);const g=v(u,c.MinusSign);return T(a,n,g,i,r)}}function m(e,n,t=E.Format,i=!1){return function(r,s){return function _n(e,n,t,i,r,s){switch(t){case d.Months:return je(n,r,i)[e.getMonth()];case d.Days:return Ue(n,r,i)[e.getDay()];case d.DayPeriods:const u=e.getHours(),a=e.getMinutes();if(s){const p=We(n),D=Ye(n,r,i),h=p.findIndex(_=>{if(Array.isArray(_)){const[C,L]=_,x=u>=C.hours&&a>=C.minutes,N=u<L.hours||u===L.hours&&a<L.minutes;if(C.hours<L.hours){if(x&&N)return!0}else if(x||N)return!0}else if(_.hours===u&&_.minutes===a)return!0;return!1});if(-1!==h)return D[h]}return ze(n,r,i)[u<12?0:1];case d.Eras:return Ve(n,i)[e.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${t}`)}}(r,s,e,n,t,i)}}function ne(e){return function(n,t,i){const r=-1*i,s=v(t,c.MinusSign),u=r>0?Math.floor(r/60):Math.ceil(r/60);switch(e){case A.Short:return(r>=0?"+":"")+T(u,2,s)+T(Math.abs(r%60),2,s);case A.ShortGMT:return"GMT"+(r>=0?"+":"")+T(u,1,s);case A.Long:return"GMT"+(r>=0?"+":"")+T(u,2,s)+":"+T(Math.abs(r%60),2,s);case A.Extended:return 0===i?"Z":(r>=0?"+":"")+T(u,2,s)+":"+T(Math.abs(r%60),2,s);default:throw new Error(`Unknown zone width "${e}"`)}}}const Sn=0,ie=4;function Je(e){return ee(e.getFullYear(),e.getMonth(),e.getDate()+(ie-e.getDay()))}function pe(e,n=!1){return function(t,i){let r;if(n){const s=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,u=t.getDate();r=1+Math.floor((u+s)/7)}else{const s=Je(t),u=function An(e){const n=ee(e,Sn,1).getDay();return ee(e,0,1+(n<=ie?ie:ie+7)-n)}(s.getFullYear()),a=s.getTime()-u.getTime();r=1+Math.round(a/6048e5)}return T(r,e,v(i,c.MinusSign))}}function re(e,n=!1){return function(t,i){return T(Je(t).getFullYear(),e,v(i,c.MinusSign),n)}}const me={};function qe(e,n){e=e.replace(/:/g,"");const t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function Qe(e){return e instanceof Date&&!isNaN(e.valueOf())}const Tn=/^(\d+)?\.((\d+)(-(\d+))?)?$/,et=22,oe=".",K="0",Mn=";",Bn=",",Ce="#",tt="\xa4",On="%";function Fe(e,n,t,i,r,s,u=!1){let a="",g=!1;if(isFinite(e)){let p=function Nn(e){let i,r,s,u,a,n=Math.abs(e)+"",t=0;for((r=n.indexOf(oe))>-1&&(n=n.replace(oe,"")),(s=n.search(/e/i))>0?(r<0&&(r=s),r+=+n.slice(s+1),n=n.substring(0,s)):r<0&&(r=n.length),s=0;n.charAt(s)===K;s++);if(s===(a=n.length))i=[0],r=1;else{for(a--;n.charAt(a)===K;)a--;for(r-=s,i=[],u=0;s<=a;s++,u++)i[u]=Number(n.charAt(s))}return r>et&&(i=i.splice(0,et-1),t=r-1,r=1),{digits:i,exponent:t,integerLen:r}}(e);u&&(p=function Rn(e){if(0===e.digits[0])return e;const n=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(0===n?e.digits.push(0,0):1===n&&e.digits.push(0),e.integerLen+=2),e}(p));let D=n.minInt,h=n.minFrac,_=n.maxFrac;if(s){const B=s.match(Tn);if(null===B)throw new Error(`${s} is not a valid digit info`);const $=B[1],z=B[3],j=B[5];null!=$&&(D=ye($)),null!=z&&(h=ye(z)),null!=j?_=ye(j):null!=z&&h>_&&(_=h)}!function kn(e,n,t){if(n>t)throw new Error(`The minimum number of digits after fraction (${n}) is higher than the maximum (${t}).`);let i=e.digits,r=i.length-e.integerLen;const s=Math.min(Math.max(n,r),t);let u=s+e.integerLen,a=i[u];if(u>0){i.splice(Math.max(e.integerLen,u));for(let h=u;h<i.length;h++)i[h]=0}else{r=Math.max(0,r),e.integerLen=1,i.length=Math.max(1,u=s+1),i[0]=0;for(let h=1;h<u;h++)i[h]=0}if(a>=5)if(u-1<0){for(let h=0;h>u;h--)i.unshift(0),e.integerLen++;i.unshift(1),e.integerLen++}else i[u-1]++;for(;r<Math.max(0,s);r++)i.push(0);let g=0!==s;const p=n+e.integerLen,D=i.reduceRight(function(h,_,C,L){return L[C]=(_+=h)<10?_:_-10,g&&(0===L[C]&&C>=p?L.pop():g=!1),_>=10?1:0},0);D&&(i.unshift(D),e.integerLen++)}(p,h,_);let C=p.digits,L=p.integerLen;const x=p.exponent;let N=[];for(g=C.every(B=>!B);L<D;L++)C.unshift(0);for(;L<0;L++)C.unshift(0);L>0?N=C.splice(L,C.length):(N=C,C=[0]);const U=[];for(C.length>=n.lgSize&&U.unshift(C.splice(-n.lgSize,C.length).join(""));C.length>n.gSize;)U.unshift(C.splice(-n.gSize,C.length).join(""));C.length&&U.unshift(C.join("")),a=U.join(v(t,i)),N.length&&(a+=v(t,r)+N.join("")),x&&(a+=v(t,c.Exponential)+"+"+x)}else a=v(t,c.Infinity);return a=e<0&&!g?n.negPre+a+n.negSuf:n.posPre+a+n.posSuf,a}function nt(e,n,t,i,r){const u=Ee(Q(n,I.Currency),v(n,c.MinusSign));return u.minFrac=Ze(i),u.maxFrac=u.minFrac,Fe(e,u,n,c.CurrencyGroup,c.CurrencyDecimal,r).replace(tt,t).replace(tt,"").trim()}function it(e,n,t){return Fe(e,Ee(Q(n,I.Percent),v(n,c.MinusSign)),n,c.Group,c.Decimal,t,!0).replace(new RegExp(On,"g"),v(n,c.PercentSign))}function rt(e,n,t){return Fe(e,Ee(Q(n,I.Decimal),v(n,c.MinusSign)),n,c.Group,c.Decimal,t)}function Ee(e,n="-"){const t={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},i=e.split(Mn),r=i[0],s=i[1],u=-1!==r.indexOf(oe)?r.split(oe):[r.substring(0,r.lastIndexOf(K)+1),r.substring(r.lastIndexOf(K)+1)],a=u[0],g=u[1]||"";t.posPre=a.substring(0,a.indexOf(Ce));for(let D=0;D<g.length;D++){const h=g.charAt(D);h===K?t.minFrac=t.maxFrac=D+1:h===Ce?t.maxFrac=D+1:t.posSuf+=h}const p=a.split(Bn);if(t.gSize=p[1]?p[1].length:0,t.lgSize=p[2]||p[1]?(p[2]||p[1]).length:0,s){const D=r.length-t.posPre.length-t.posSuf.length,h=s.indexOf(Ce);t.negPre=s.substring(0,h).replace(/'/g,""),t.negSuf=s.slice(h+D).replace(/'/g,"")}else t.negPre=n+t.posPre,t.negSuf=t.posSuf;return t}function ye(e){const n=parseInt(e);if(isNaN(n))throw new Error("Invalid integer literal when parsing "+e);return n}let se=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:function(t){let i=null;return t?i=new t:(r=o.\u0275\u0275inject(o.LOCALE_ID),i=new st(r)),i;var r},providedIn:"root"}),e})();function ot(e,n,t,i){let r=`=${e}`;if(n.indexOf(r)>-1||(r=t.getPluralCategory(e,i),n.indexOf(r)>-1))return r;if(n.indexOf("other")>-1)return"other";throw new Error(`No plural message found for value "${e}"`)}let st=(()=>{class e extends se{constructor(t){super(),this.locale=t}getPluralCategory(t,i){switch(Ge(i||this.locale)(t)){case w.Zero:return"zero";case w.One:return"one";case w.Two:return"two";case w.Few:return"few";case w.Many:return"many";default:return"other"}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(o.LOCALE_ID))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})();function xn(e,n,t){return(0,o.\u0275registerLocaleData)(e,n,t)}function $n(e,n){n=encodeURIComponent(n);for(const t of e.split(";")){const i=t.indexOf("="),[r,s]=-1==i?[t,""]:[t.slice(0,i),t.slice(i+1)];if(r.trim()===n)return decodeURIComponent(s)}return null}const we=/\s+/,ut=[];let at=(()=>{class e{constructor(t,i,r,s){this._iterableDiffers=t,this._keyValueDiffers=i,this._ngEl=r,this._renderer=s,this.initialClasses=ut,this.stateMap=new Map}set klass(t){this.initialClasses=null!=t?t.trim().split(we):ut}set ngClass(t){this.rawClass="string"==typeof t?t.trim().split(we):t}ngDoCheck(){for(const i of this.initialClasses)this._updateState(i,!0);const t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(const i of t)this._updateState(i,!0);else if(null!=t)for(const i of Object.keys(t))this._updateState(i,Boolean(t[i]));this._applyStateDiff()}_updateState(t,i){const r=this.stateMap.get(t);void 0!==r?(r.enabled!==i&&(r.changed=!0,r.enabled=i),r.touched=!0):this.stateMap.set(t,{enabled:i,changed:!0,touched:!0})}_applyStateDiff(){for(const t of this.stateMap){const i=t[0],r=t[1];r.changed?(this._toggleClass(i,r.enabled),r.changed=!1):r.touched||(r.enabled&&this._toggleClass(i,!1),this.stateMap.delete(i)),r.touched=!1}}_toggleClass(t,i){(t=t.trim()).length>0&&t.split(we).forEach(r=>{i?this._renderer.addClass(this._ngEl.nativeElement,r):this._renderer.removeClass(this._ngEl.nativeElement,r)})}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.IterableDiffers),o.\u0275\u0275directiveInject(o.KeyValueDiffers),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.Renderer2))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0}),e})(),ct=(()=>{class e{constructor(t){this._viewContainerRef=t,this.ngComponentOutlet=null}ngOnChanges(t){const{_viewContainerRef:i,ngComponentOutletNgModule:r,ngComponentOutletNgModuleFactory:s}=this;if(i.clear(),this._componentRef=void 0,this.ngComponentOutlet){const u=this.ngComponentOutletInjector||i.parentInjector;(t.ngComponentOutletNgModule||t.ngComponentOutletNgModuleFactory)&&(this._moduleRef&&this._moduleRef.destroy(),this._moduleRef=r?(0,o.createNgModule)(r,lt(u)):s?s.create(lt(u)):void 0),this._componentRef=i.createComponent(this.ngComponentOutlet,{index:i.length,injector:u,ngModuleRef:this._moduleRef,projectableNodes:this.ngComponentOutletContent})}}ngOnDestroy(){this._moduleRef&&this._moduleRef.destroy()}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngComponentOutlet",""]],inputs:{ngComponentOutlet:"ngComponentOutlet",ngComponentOutletInjector:"ngComponentOutletInjector",ngComponentOutletContent:"ngComponentOutletContent",ngComponentOutletNgModule:"ngComponentOutletNgModule",ngComponentOutletNgModuleFactory:"ngComponentOutletNgModuleFactory"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature]}),e})();function lt(e){return e.get(o.NgModuleRef).injector}class ft{constructor(n,t,i,r){this.$implicit=n,this.ngForOf=t,this.index=i,this.count=r}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let _e=(()=>{class e{set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}constructor(t,i,r){this._viewContainer=t,this._template=i,this._differs=r,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){const t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){const i=this._viewContainer;t.forEachOperation((r,s,u)=>{if(null==r.previousIndex)i.createEmbeddedView(this._template,new ft(r.item,this._ngForOf,-1,-1),null===u?void 0:u);else if(null==u)i.remove(null===s?void 0:s);else if(null!==s){const a=i.get(s);i.move(a,u),ht(a,r)}});for(let r=0,s=i.length;r<s;r++){const a=i.get(r).context;a.index=r,a.count=s,a.ngForOf=this._ngForOf}t.forEachIdentityChange(r=>{ht(i.get(r.currentIndex),r)})}static ngTemplateContextGuard(t,i){return!0}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef),o.\u0275\u0275directiveInject(o.TemplateRef),o.\u0275\u0275directiveInject(o.IterableDiffers))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0}),e})();function ht(e,n){e.context.$implicit=n.item}let Dt=(()=>{class e{constructor(t,i){this._viewContainer=t,this._context=new gt,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=i}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){pt("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){pt("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,i){return!0}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef),o.\u0275\u0275directiveInject(o.TemplateRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0}),e})();class gt{constructor(){this.$implicit=null,this.ngIf=null}}function pt(e,n){if(n&&!n.createEmbeddedView)throw new Error(`${e} must be a TemplateRef, but received '${(0,o.\u0275stringify)(n)}'.`)}class Se{constructor(n,t){this._viewContainerRef=n,this._templateRef=t,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(n){n&&!this._created?this.create():!n&&this._created&&this.destroy()}}let ue=(()=>{class e{constructor(){this._defaultViews=[],this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(t){this._ngSwitch=t,0===this._caseCount&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(t){this._defaultViews.push(t)}_matchCase(t){const i=t==this._ngSwitch;return this._lastCasesMatched=this._lastCasesMatched||i,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),i}_updateDefaultCases(t){if(this._defaultViews.length>0&&t!==this._defaultUsed){this._defaultUsed=t;for(const i of this._defaultViews)i.enforceState(t)}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"},standalone:!0}),e})(),mt=(()=>{class e{constructor(t,i,r){this.ngSwitch=r,r._addCase(),this._view=new Se(t,i)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef),o.\u0275\u0275directiveInject(o.TemplateRef),o.\u0275\u0275directiveInject(ue,9))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"},standalone:!0}),e})(),Ct=(()=>{class e{constructor(t,i,r){r._addDefault(new Se(t,i))}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef),o.\u0275\u0275directiveInject(o.TemplateRef),o.\u0275\u0275directiveInject(ue,9))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngSwitchDefault",""]],standalone:!0}),e})(),Ae=(()=>{class e{constructor(t){this._localization=t,this._caseViews={}}set ngPlural(t){this._updateView(t)}addCase(t,i){this._caseViews[t]=i}_updateView(t){this._clearViews();const r=ot(t,Object.keys(this._caseViews),this._localization);this._activateView(this._caseViews[r])}_clearViews(){this._activeView&&this._activeView.destroy()}_activateView(t){t&&(this._activeView=t,this._activeView.create())}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(se))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngPlural",""]],inputs:{ngPlural:"ngPlural"},standalone:!0}),e})(),Ft=(()=>{class e{constructor(t,i,r,s){this.value=t;const u=!isNaN(Number(t));s.addCase(u?`=${t}`:t,new Se(r,i))}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275injectAttribute("ngPluralCase"),o.\u0275\u0275directiveInject(o.TemplateRef),o.\u0275\u0275directiveInject(o.ViewContainerRef),o.\u0275\u0275directiveInject(Ae,1))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngPluralCase",""]],standalone:!0}),e})(),Et=(()=>{class e{constructor(t,i,r){this._ngEl=t,this._differs=i,this._renderer=r,this._ngStyle=null,this._differ=null}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){const t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,i){const[r,s]=t.split("."),u=-1===r.indexOf("-")?void 0:o.RendererStyleFlags2.DashCase;null!=i?this._renderer.setStyle(this._ngEl.nativeElement,r,s?`${i}${s}`:i,u):this._renderer.removeStyle(this._ngEl.nativeElement,r,u)}_applyChanges(t){t.forEachRemovedItem(i=>this._setStyle(i.key,null)),t.forEachAddedItem(i=>this._setStyle(i.key,i.currentValue)),t.forEachChangedItem(i=>this._setStyle(i.key,i.currentValue))}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.KeyValueDiffers),o.\u0275\u0275directiveInject(o.Renderer2))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0}),e})(),yt=(()=>{class e{constructor(t){this._viewContainerRef=t,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(t){if(t.ngTemplateOutlet||t.ngTemplateOutletInjector){const i=this._viewContainerRef;if(this._viewRef&&i.remove(i.indexOf(this._viewRef)),this.ngTemplateOutlet){const{ngTemplateOutlet:r,ngTemplateOutletContext:s,ngTemplateOutletInjector:u}=this;this._viewRef=i.createEmbeddedView(r,s,u?{injector:u}:void 0)}else this._viewRef=null}else this._viewRef&&t.ngTemplateOutletContext&&this.ngTemplateOutletContext&&(this._viewRef.context=this.ngTemplateOutletContext)}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ViewContainerRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature]}),e})();function M(e,n){return new o.\u0275RuntimeError(2100,!1)}class Un{createSubscription(n,t){return n.subscribe({next:t,error:i=>{throw i}})}dispose(n){n.unsubscribe()}}class jn{createSubscription(n,t){return n.then(t,i=>{throw i})}dispose(n){}}const Vn=new jn,Gn=new Un;let wt=(()=>{class e{constructor(t){this._latestValue=null,this._subscription=null,this._obj=null,this._strategy=null,this._ref=t}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(t){return this._obj?t!==this._obj?(this._dispose(),this.transform(t)):this._latestValue:(t&&this._subscribe(t),this._latestValue)}_subscribe(t){this._obj=t,this._strategy=this._selectStrategy(t),this._subscription=this._strategy.createSubscription(t,i=>this._updateLatestValue(t,i))}_selectStrategy(t){if((0,o.\u0275isPromise)(t))return Vn;if((0,o.\u0275isSubscribable)(t))return Gn;throw M()}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(t,i){t===this._obj&&(this._latestValue=i,this._ref.markForCheck())}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ChangeDetectorRef,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"async",type:e,pure:!1,standalone:!0}),e})(),_t=(()=>{class e{transform(t){if(null==t)return null;if("string"!=typeof t)throw M();return t.toLowerCase()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"lowercase",type:e,pure:!0,standalone:!0}),e})();const Hn=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g;let St=(()=>{class e{transform(t){if(null==t)return null;if("string"!=typeof t)throw M();return t.replace(Hn,i=>i[0].toUpperCase()+i.slice(1).toLowerCase())}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"titlecase",type:e,pure:!0,standalone:!0}),e})(),At=(()=>{class e{transform(t){if(null==t)return null;if("string"!=typeof t)throw M();return t.toUpperCase()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"uppercase",type:e,pure:!0,standalone:!0}),e})();const bt=new o.InjectionToken("DATE_PIPE_DEFAULT_TIMEZONE"),Lt=new o.InjectionToken("DATE_PIPE_DEFAULT_OPTIONS");let It=(()=>{class e{constructor(t,i,r){this.locale=t,this.defaultTimezone=i,this.defaultOptions=r}transform(t,i,r,s){if(null==t||""===t||t!=t)return null;try{return Xe(t,i??this.defaultOptions?.dateFormat??"mediumDate",s||this.locale,r??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(u){throw M()}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.LOCALE_ID,16),o.\u0275\u0275directiveInject(bt,24),o.\u0275\u0275directiveInject(Lt,24))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"date",type:e,pure:!0,standalone:!0}),e})();const Yn=/#/g;let vt=(()=>{class e{constructor(t){this._localization=t}transform(t,i,r){if(null==t)return"";if("object"!=typeof i||null===i)throw M();return i[ot(t,Object.keys(i),this._localization,r)].replace(Yn,t.toString())}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(se,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"i18nPlural",type:e,pure:!0,standalone:!0}),e})(),Pt=(()=>{class e{transform(t,i){if(null==t)return"";if("object"!=typeof i||"string"!=typeof t)throw M();return i.hasOwnProperty(t)?i[t]:i.hasOwnProperty("other")?i.other:""}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"i18nSelect",type:e,pure:!0,standalone:!0}),e})(),Tt=(()=>{class e{transform(t){return JSON.stringify(t,null,2)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"json",type:e,pure:!1,standalone:!0}),e})(),Mt=(()=>{class e{constructor(t){this.differs=t,this.keyValues=[],this.compareFn=Bt}transform(t,i=Bt){if(!t||!(t instanceof Map)&&"object"!=typeof t)return null;this.differ||(this.differ=this.differs.find(t).create());const r=this.differ.diff(t),s=i!==this.compareFn;return r&&(this.keyValues=[],r.forEachItem(u=>{this.keyValues.push(function Kn(e,n){return{key:e,value:n}}(u.key,u.currentValue))})),(r||s)&&(this.keyValues.sort(i),this.compareFn=i),this.keyValues}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.KeyValueDiffers,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"keyvalue",type:e,pure:!1,standalone:!0}),e})();function Bt(e,n){const t=e.key,i=n.key;if(t===i)return 0;if(void 0===t)return 1;if(void 0===i)return-1;if(null===t)return 1;if(null===i)return-1;if("string"==typeof t&&"string"==typeof i)return t<i?-1:1;if("number"==typeof t&&"number"==typeof i)return t-i;if("boolean"==typeof t&&"boolean"==typeof i)return t<i?-1:1;const r=String(t),s=String(i);return r==s?0:r<s?-1:1}let Ot=(()=>{class e{constructor(t){this._locale=t}transform(t,i,r){if(!be(t))return null;r=r||this._locale;try{return rt(Le(t),r,i)}catch(s){throw M()}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.LOCALE_ID,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"number",type:e,pure:!0,standalone:!0}),e})(),Rt=(()=>{class e{constructor(t){this._locale=t}transform(t,i,r){if(!be(t))return null;r=r||this._locale;try{return it(Le(t),r,i)}catch(s){throw M()}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.LOCALE_ID,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"percent",type:e,pure:!0,standalone:!0}),e})(),Nt=(()=>{class e{constructor(t,i="USD"){this._locale=t,this._defaultCurrencyCode=i}transform(t,i=this._defaultCurrencyCode,r="symbol",s,u){if(!be(t))return null;u=u||this._locale,"boolean"==typeof r&&(r=r?"symbol":"code");let a=i||this._defaultCurrencyCode;"code"!==r&&(a="symbol"===r||"symbol-narrow"===r?Ke(a,"symbol"===r?"wide":"narrow",u):r);try{return nt(Le(t),u,a,i,s)}catch(g){throw M()}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.LOCALE_ID,16),o.\u0275\u0275directiveInject(o.DEFAULT_CURRENCY_CODE,16))},e.\u0275pipe=o.\u0275\u0275definePipe({name:"currency",type:e,pure:!0,standalone:!0}),e})();function be(e){return!(null==e||""===e||e!=e)}function Le(e){if("string"==typeof e&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if("number"!=typeof e)throw new Error(`${e} is not a number`);return e}let kt=(()=>{class e{transform(t,i,r){if(null==t)return null;if(!this.supports(t))throw M();return t.slice(i,r)}supports(t){return"string"==typeof t||Array.isArray(t)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o.\u0275\u0275definePipe({name:"slice",type:e,pure:!1,standalone:!0}),e})(),Zn=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=o.\u0275\u0275defineNgModule({type:e}),e.\u0275inj=o.\u0275\u0275defineInjector({}),e})();const xt="browser",$t="server",zt="browserWorkerApp",Ut="browserWorkerUi";function Xn(e){return e===xt}function jt(e){return e===$t}function Jn(e){return e===zt}function qn(e){return e===Ut}const Qn=new o.Version("15.2.10");let ei=(()=>{class e{}return e.\u0275prov=(0,o.\u0275\u0275defineInjectable)({token:e,providedIn:"root",factory:()=>new ti((0,o.\u0275\u0275inject)(k),window)}),e})();class ti{constructor(n,t){this.document=n,this.window=t,this.offset=()=>[0,0]}setOffset(n){this.offset=Array.isArray(n)?()=>n:n}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(n){this.supportsScrolling()&&this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){if(!this.supportsScrolling())return;const t=function ni(e,n){const t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if("function"==typeof e.createTreeWalker&&e.body&&(e.body.createShadowRoot||e.body.attachShadow)){const i=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT);let r=i.currentNode;for(;r;){const s=r.shadowRoot;if(s){const u=s.getElementById(n)||s.querySelector(`[name="${n}"]`);if(u)return u}r=i.nextNode()}}return null}(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){if(this.supportScrollRestoration()){const t=this.window.history;t&&t.scrollRestoration&&(t.scrollRestoration=n)}}scrollToElement(n){const t=n.getBoundingClientRect(),i=t.left+this.window.pageXOffset,r=t.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(i-s[0],r-s[1])}supportScrollRestoration(){try{if(!this.supportsScrolling())return!1;const n=Vt(this.window.history)||Vt(Object.getPrototypeOf(this.window.history));return!(!n||!n.writable&&!n.set)}catch{return!1}}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}function Vt(e){return Object.getOwnPropertyDescriptor(e,"scrollRestoration")}class ii{setOffset(n){}getScrollPosition(){return[0,0]}scrollToPosition(n){}scrollToAnchor(n){}setHistoryScrollRestoration(n){}}class ri{}function ui(e){return e.startsWith("/")?e.slice(1):e}const Z=e=>e.src,ve=new o.InjectionToken("ImageLoader",{providedIn:"root",factory:()=>Z});function ce(e,n){return function(i){return function oi(e){if("string"!=typeof e||""===e.trim())return!1;try{return new URL(e),!0}catch{return!1}}(i)||function ai(e,n){throw new o.\u0275RuntimeError(2959,!1)}(),i=function si(e){return e.endsWith("/")?e.slice(0,-1):e}(i),[{provide:ve,useValue:u=>(function Ie(e){return/^https?:\/\//.test(e)}(u.src)&&function ci(e,n){throw new o.\u0275RuntimeError(2959,!1)}(),e(i,{...u,src:ui(u.src)}))}]}}const li=ce(di);function di(e,n){let t="format=auto";return n.width&&(t+=`,width=${n.width}`),`${e}/cdn-cgi/image/${t}/${n.src}`}const gi=ce(pi);function pi(e,n){let t="f_auto,q_auto";return n.width&&(t+=`,w_${n.width}`),`${e}/image/upload/${t}/${n.src}`}const Ei=ce(yi);function yi(e,n){const{src:t,width:i}=n;let r;return r=i?[e,`tr:w-${i}`,t]:[e,t],r.join("/")}const Ai=ce(bi);function bi(e,n){const t=new URL(`${e}/${n.src}`);return t.searchParams.set("auto","format"),n.width&&t.searchParams.set("w",n.width.toString()),t.href}const Wt=new o.InjectionToken("PRECONNECT_CHECK_BLOCKLIST"),vi=new o.InjectionToken("NG_OPTIMIZED_PRELOADED_IMAGES",{providedIn:"root",factory:()=>new Set});let Pi=(()=>{class e{constructor(){this.preloadedImages=(0,o.inject)(vi),this.document=(0,o.inject)(k)}createPreloadLinkTag(t,i,r,s){if(this.preloadedImages.has(i))return;this.preloadedImages.add(i);const u=t.createElement("link");t.setAttribute(u,"as","image"),t.setAttribute(u,"href",i),t.setAttribute(u,"rel","preload"),t.setAttribute(u,"fetchpriority","high"),s&&t.setAttribute(u,"imageSizes",s),r&&t.setAttribute(u,"imageSrcset",r),t.appendChild(this.document.head,u)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const Zt=/^((\s*\d+w\s*(,|$)){1,})$/,Mi=[1,2],qt={breakpoints:[16,32,48,64,96,128,256,384,640,750,828,1080,1200,1920,2048,3840]},Qt=new o.InjectionToken("ImageConfig",{providedIn:"root",factory:()=>qt});let ki=(()=>{class e{constructor(){this.imageLoader=(0,o.inject)(ve),this.config=function xi(e){let n={};return e.breakpoints&&(n.breakpoints=e.breakpoints.sort((t,i)=>t-i)),Object.assign({},qt,e,n)}((0,o.inject)(Qt)),this.renderer=(0,o.inject)(o.Renderer2),this.imgElement=(0,o.inject)(o.ElementRef).nativeElement,this.injector=(0,o.inject)(o.Injector),this.isServer=jt((0,o.inject)(o.PLATFORM_ID)),this.preloadLinkChecker=(0,o.inject)(Pi),this.lcpObserver=null,this._renderedSrc=null,this._priority=!1,this._disableOptimizedSrcset=!1,this._fill=!1}set width(t){this._width=en(t)}get width(){return this._width}set height(t){this._height=en(t)}get height(){return this._height}set priority(t){this._priority=Te(t)}get priority(){return this._priority}set disableOptimizedSrcset(t){this._disableOptimizedSrcset=Te(t)}get disableOptimizedSrcset(){return this._disableOptimizedSrcset}set fill(t){this._fill=Te(t)}get fill(){return this._fill}ngOnInit(){this.setHostAttributes()}setHostAttributes(){this.fill?this.sizes||(this.sizes="100vw"):(this.setHostAttribute("width",this.width.toString()),this.setHostAttribute("height",this.height.toString())),this.setHostAttribute("loading",this.getLoadingBehavior()),this.setHostAttribute("fetchpriority",this.getFetchPriority()),this.setHostAttribute("ng-img","true");const t=this.getRewrittenSrc();let i;this.setHostAttribute("src",t),this.sizes&&this.setHostAttribute("sizes",this.sizes),this.ngSrcset?i=this.getRewrittenSrcset():this.shouldGenerateAutomaticSrcset()&&(i=this.getAutomaticSrcset()),i&&this.setHostAttribute("srcset",i),this.isServer&&this.priority&&this.preloadLinkChecker.createPreloadLinkTag(this.renderer,t,i,this.sizes)}ngOnChanges(t){}callImageLoader(t){let i=t;return this.loaderParams&&(i.loaderParams=this.loaderParams),this.imageLoader(i)}getLoadingBehavior(){return this.priority||void 0===this.loading?this.priority?"eager":"lazy":this.loading}getFetchPriority(){return this.priority?"high":"auto"}getRewrittenSrc(){return this._renderedSrc||(this._renderedSrc=this.callImageLoader({src:this.ngSrc})),this._renderedSrc}getRewrittenSrcset(){const t=Zt.test(this.ngSrcset);return this.ngSrcset.split(",").filter(r=>""!==r).map(r=>{r=r.trim();const s=t?parseFloat(r):parseFloat(r)*this.width;return`${this.callImageLoader({src:this.ngSrc,width:s})} ${r}`}).join(", ")}getAutomaticSrcset(){return this.sizes?this.getResponsiveSrcset():this.getFixedSrcset()}getResponsiveSrcset(){const{breakpoints:t}=this.config;let i=t;return"100vw"===this.sizes?.trim()&&(i=t.filter(s=>s>=640)),i.map(s=>`${this.callImageLoader({src:this.ngSrc,width:s})} ${s}w`).join(", ")}getFixedSrcset(){return Mi.map(i=>`${this.callImageLoader({src:this.ngSrc,width:this.width*i})} ${i}x`).join(", ")}shouldGenerateAutomaticSrcset(){return!this._disableOptimizedSrcset&&!this.srcset&&this.imageLoader!==Z&&!(this.width>1920||this.height>1080)}ngOnDestroy(){}setHostAttribute(t,i){this.renderer.setAttribute(this.imgElement,t,i)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["img","ngSrc",""]],hostVars:8,hostBindings:function(t,i){2&t&&o.\u0275\u0275styleProp("position",i.fill?"absolute":null)("width",i.fill?"100%":null)("height",i.fill?"100%":null)("inset",i.fill?"0px":null)},inputs:{ngSrc:"ngSrc",ngSrcset:"ngSrcset",sizes:"sizes",width:"width",height:"height",loading:"loading",priority:"priority",loaderParams:"loaderParams",disableOptimizedSrcset:"disableOptimizedSrcset",fill:"fill",src:"src",srcset:"srcset"},standalone:!0,features:[o.\u0275\u0275NgOnChangesFeature]}),e})();function en(e){return"string"==typeof e?parseInt(e,10):e}function Te(e){return null!=e&&"false"!=`${e}`}}}]);