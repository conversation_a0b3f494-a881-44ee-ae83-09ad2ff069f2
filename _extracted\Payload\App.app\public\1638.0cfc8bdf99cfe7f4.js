(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1638],{65715:(A,_,n)=>{n.d(_,{F8:()=>u,tM:()=>C,KW:()=>Y,$g:()=>p,QG:()=>i,fc:()=>R,vl:()=>O,x3:()=>j});var b=n(17007),f=n(30263),e=n(99877);function t(a,d){if(1&a&&e.\u0275\u0275element(0,"bocc-tag-aval",14),2&a){const o=e.\u0275\u0275nextContext();e.\u0275\u0275property("value",null==o.account?null:o.account.tagAval)}}let C=(()=>{class a{ngBoccPortal(o){this.portal=o}onApproved(){this.portal?.destroy()}onDecline(){this.portal?.destroy()}}return a.\u0275fac=function(o){return new(o||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-account-checked-modal"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:22,vars:3,consts:[["src","assets/shared/logos/modals/transfiya-account-checked.svg"],[1,"smalltext-semibold"],[1,"mbo-transfiya-account-checked-modal__body"],[1,"body2-medium"],["color","none"],[1,"mbo-transfiya-account-checked-modal__product"],[1,"mbo-transfiya-account-checked-modal__title"],[1,"smalltext-medium"],[3,"value",4,"ngIf"],[1,"mbo-transfiya-account-checked-modal__number"],[1,"body1-medium"],[1,"mbo-transfiya-account-checked-modal__footer"],["id","btn_transfiya-account-checked_approved","bocc-button","raised","prefixIcon","refresh",3,"click"],["id","btn_transfiya-account-checked_decline","bocc-button","flat",3,"click"],[3,"value"]],template:function(o,s){1&o&&(e.\u0275\u0275element(0,"bocc-logo-modal",0),e.\u0275\u0275elementStart(1,"label",1),e.\u0275\u0275text(2," CAMBIO DE CUENTA FAVORITA "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",2)(4,"p",3),e.\u0275\u0275text(5," Recibir\xe1s todas las transferencias de Transfiya en esta cuenta: "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-card-product-background",4)(7,"div",5)(8,"div",6)(9,"span",7),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(11,t,1,1,"bocc-tag-aval",8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",9)(13,"span",10),e.\u0275\u0275text(14),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(15,"div",11)(16,"button",12),e.\u0275\u0275listener("click",function(){return s.onApproved()}),e.\u0275\u0275elementStart(17,"span"),e.\u0275\u0275text(18,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"button",13),e.\u0275\u0275listener("click",function(){return s.onDecline()}),e.\u0275\u0275elementStart(20,"span"),e.\u0275\u0275text(21,"Cancelar"),e.\u0275\u0275elementEnd()()()),2&o&&(e.\u0275\u0275advance(10),e.\u0275\u0275textInterpolate(null==s.account?null:s.account.nickname),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==s.account?null:s.account.tagAval),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==s.account?null:s.account.publicNumber))},dependencies:[b.CommonModule,b.NgIf,f.bG,f.P8,f.qd,f.X6],styles:["mbo-transfiya-account-checked-modal{--bocc-logo-modal-translate-x:calc(-50% - 8rem);position:relative;display:flex;flex-direction:column;row-gap:var(--sizing-x8);padding:36rem 0rem var(--sizing-x8) 0rem;box-sizing:border-box}mbo-transfiya-account-checked-modal bocc-logo-modal{position:absolute;top:0rem;left:50%}mbo-transfiya-account-checked-modal bocc-card-product-background{margin:var(--sizing-x4) 0rem}mbo-transfiya-account-checked-modal bocc-card-product-background .bocc-card-product-background{border-radius:var(--sizing-x4)}mbo-transfiya-account-checked-modal>label{text-align:center}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__body{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__body p{color:var(--color-carbon-lighter-700);text-align:center}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__product{display:flex;flex-direction:column;row-gap:var(--sizing-x6);padding:var(--sizing-x4) var(--sizing-x6);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__title{display:flex;align-items:center;justify-content:space-between}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__title>span{color:var(--bocc-card-product-color-title)}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__number{color:var(--bocc-card-product-color-code)}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__footer{display:flex;flex-direction:column;row-gap:var(--sizing-x4);padding:0rem var(--sizing-x8);box-sizing:border-box}mbo-transfiya-account-checked-modal .mbo-transfiya-account-checked-modal__footer button{width:100%}\n"],encapsulation:2}),a})();function v(a,d){if(1&a&&(e.\u0275\u0275elementStart(0,"span",10),e.\u0275\u0275element(1,"bocc-tag-aval",11),e.\u0275\u0275elementEnd()),2&a){const o=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("value",null==o.product?null:o.product.tagAval)}}let p=(()=>{class a{constructor(){this.checked=!1}get icon(){return this.checked?"check":"favorite"}}return a.\u0275fac=function(o){return new(o||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-account-selector"]],inputs:{product:"product",checked:"checked"},standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:12,vars:8,consts:[[1,"mbo-transfiya-account-selector__content",3,"color"],[1,"mbo-transfiya-account-selector__avatar"],["alt","mbo-transfiya-account-selector-img",1,"mbo-transfiya-account-selector__avatar__icon",3,"src"],[1,"mbo-transfiya-account-selector__component"],[1,"mbo-transfiya-account-selector__title","body2-medium"],[1,"mbo-transfiya-account-selector__number","body1-medium"],[1,"mbo-transfiya-account-selector__number__value"],["class","mbo-transfiya-account-selector__aval-key",4,"ngIf"],[1,"mbo-transfiya-account-selector__check"],[3,"icon"],[1,"mbo-transfiya-account-selector__aval-key"],[3,"value"]],template:function(o,s){1&o&&(e.\u0275\u0275elementStart(0,"bocc-card-product-background",0)(1,"div",1),e.\u0275\u0275element(2,"img",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"label",4),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"label",5)(7,"span",6),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,v,2,1,"span",7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",8),e.\u0275\u0275element(11,"bocc-icon",9),e.\u0275\u0275elementEnd()()),2&o&&(e.\u0275\u0275classProp("mbo-transfiya-account-selector__content--checked",s.checked),e.\u0275\u0275property("color",null==s.product?null:s.product.color),e.\u0275\u0275advance(2),e.\u0275\u0275property("src",null==s.product?null:s.product.logo,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==s.product?null:s.product.nickname," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",null==s.product?null:s.product.publicNumber," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==s.product?null:s.product.tagAval),e.\u0275\u0275advance(2),e.\u0275\u0275property("icon",s.icon))},dependencies:[b.CommonModule,b.NgIf,f.X6,f.qd,f.Zl],styles:['/*!\n * MBO TransfiyaAccountSelector Component\n * v1.0.0\n * Author: MB Frontend Developers\n * Created: 09/Sep/2024\n * Updated: 09/Sep/2024\n*/mbo-transfiya-account-selector{--bocc-card-product-color-title: var(--color-carbon-darker-1000);--bocc-card-background-none: var(--color-carbon-lighter-50);--pvt-check-background-color: transparent;--pvt-check-font-color: var(--color-semantic-alert-700);position:relative;width:100%;display:block;border-radius:var(--sizing-x4)}mbo-transfiya-account-selector .bocc-card-product-background{display:flex;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);align-items:center;padding:var(--sizing-x6);box-sizing:border-box;border-radius:var(--sizing-x4)}mbo-transfiya-account-selector .bocc-card-product-background.none{--bocc-card-product-color-code: var(--color-amathyst-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__content--checked{--pvt-check-background-color: var(--color-semantic-success-700);--pvt-check-font-color: var(--color-carbon-lighter-50)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__avatar{z-index:var(--z-index-4)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__avatar__icon{width:var(--sizing-x16);height:var(--sizing-x16)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__component{display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x2);z-index:var(--z-index-4);overflow:hidden}mbo-transfiya-account-selector .mbo-transfiya-account-selector__title{position:relative;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--bocc-card-product-color-title)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__number{display:inline-flex;align-items:center;-moz-column-gap:var(--sizing-x4);column-gap:var(--sizing-x4);white-space:nowrap}mbo-transfiya-account-selector .mbo-transfiya-account-selector__number__value{color:var(--bocc-card-product-color-code)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key{--bocc-icon-dimension: var(--sizing-x10);display:flex;-moz-column-gap:var(--sizing-x1);column-gap:var(--sizing-x1)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key:before{display:block;content:"";width:1px;height:var(--sizing-x10);background-color:var(--color-carbon-lighter-400);margin-right:var(--sizing-x1)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key bocc-icon{color:var(--color-navy-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__aval-key span{color:var(--color-navy-700)}mbo-transfiya-account-selector .mbo-transfiya-account-selector__check{background:var(--pvt-check-background-color);border-radius:50%}mbo-transfiya-account-selector .mbo-transfiya-account-selector__check bocc-icon{color:var(--pvt-check-font-color)}\n'],encapsulation:2}),a})();var h=n(15861),M=n(17698),x=n(2460),T=n(45542);function m(a,d){if(1&a){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(o);const g=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(g.onIgnore())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"No mostrar m\xe1s"),e.\u0275\u0275elementEnd()()}}let i=(()=>{class a{constructor(o){this.managerTransfiya=o}ngBoccPortal(o){this.portal=o}onSubmit(){this.close()}onIgnore(){var o=this;return(0,h.Z)(function*(){(yield o.managerTransfiya.ignoreAsk()).when({},()=>o.close())})()}close(){this.portal?.close(),setTimeout(()=>this.portal?.destroy(),240)}}return a.\u0275fac=function(o){return new(o||a)(e.\u0275\u0275directiveInject(M.Pm))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-ask-bluescreen"]],decls:17,vars:1,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfiya-ask-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfiya-ask-bluescreen_hidden","class","bocc-bluescreen__button","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfiya-ask-bluescreen_hidden","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(o,s){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Tu contacto recibir\xe1 una notificaci\xf3n con la solicitud a trav\xe9s de mensaje de texto. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9," Realiza hasta 15 solicitudes al d\xeda que sumen un valor m\xe1ximo de 2'000.000 "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11," Tu contacto podr\xe1 enviar el dinero a trav\xe9s de la entidad bancaria que elija, siempre y cuando \xe9sta tenga activo el servicio de TransfiYa. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(12,"div",5)(13,"button",6),e.\u0275\u0275listener("click",function(){return s.onSubmit()}),e.\u0275\u0275elementStart(14,"span"),e.\u0275\u0275text(15,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(16,m,3,0,"button",7),e.\u0275\u0275elementEnd()),2&o&&(e.\u0275\u0275advance(16),e.\u0275\u0275property("ngIf",!1))},dependencies:[b.NgIf,x.Z,T.P],styles:["mbo-transfiya-ask-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),a})();var c=n(24495),y=n(57544),l=n(48774),E=n(64181);let u=(()=>{class a{constructor(o){this.managerTransfiya=o,this.initialValue="",this.cancelAction={id:"btn_transfiya-description-sheet_cancel",label:"Cancelar",click:()=>{this.portal?.reject(),this.portal?.destroy()}},this.saveAction={id:"btn_transfiya-description-sheet_save",label:"Guardar",disabled:()=>this.valueControl.invalid,click:()=>{this.resolveDescription(this.valueControl.value)}},this.valueControl=new y.FormControl("",[c.C1,c.zf,c.O_,c.Y2,(0,c.Mv)(24)])}ngOnInit(){this.valueControl.setValue(this.initialValue)}ngBoccPortal(o){this.portal=o}resolveDescription(o){this.managerTransfiya.setDescription(o).when({success:()=>{this.portal?.resolve(o),this.portal?.destroy()}})}}return a.\u0275fac=function(o){return new(o||a)(e.\u0275\u0275directiveInject(M.Pm))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-description-sheet"]],decls:7,vars:3,consts:[[1,"mbo-transfiya-description-sheet__content"],[1,"mbo-transfiya-description-sheet__header"],[3,"leftAction","rightAction"],[1,"mbo-transfiya-description-sheet__body"],[1,"mbo-transfiya-description-sheet__title","subtitle2-medium"],["elementId","txt_transfer-description-sheet_description","label","Descripci\xf3n","placeholder","M\xe1ximo 24 caracteres",3,"formControl"]],template:function(o,s){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4),e.\u0275\u0275text(5," A\xf1ade una descripci\xf3n a tu transferencia "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"bocc-input-box",5),e.\u0275\u0275elementEnd()()),2&o&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("leftAction",s.cancelAction)("rightAction",s.saveAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("formControl",s.valueControl))},dependencies:[l.J,E.D],styles:["mbo-transfiya-description-sheet{position:relative;width:100%;display:block}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);margin-bottom:var(--sizing-safe-bottom-x12)}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-description-sheet .mbo-transfiya-description-sheet__title{position:relative;width:100%;text-align:center}\n"],encapsulation:2}),a})();n(64561);let R=(()=>{class a{}return a.\u0275fac=function(o){return new(o||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[b.CommonModule,f.vB]}),a})();n(81502);let B=(()=>{class a{}return a.\u0275fac=function(o){return new(o||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[b.CommonModule,f.vB,f.Zl]}),a})();n(77691);var D=n(79798);let O=(()=>{class a{}return a.\u0275fac=function(o){return new(o||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[b.CommonModule,D.Aj,f.P8,B]}),a})();var F=n(8834),z=n(40914);function N(a,d){if(1&a){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(o);const g=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(g.onIgnore())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2,"No mostrar m\xe1s"),e.\u0275\u0275elementEnd()()}}const{MAX_TRANSFIYA:L}=z.R;let j=(()=>{class a{constructor(o){this.managerTransfiya=o,this.limit=(0,F.b)({value:L})}ngBoccPortal(o){this.portal=o}onSubmit(){this.portal?.close()}onIgnore(){var o=this;return(0,h.Z)(function*(){(yield o.managerTransfiya.ignoreTransfer()).when({success:()=>{o.portal?.close(),setTimeout(()=>o.portal?.destroy(),240)}})})()}}return a.\u0275fac=function(o){return new(o||a)(e.\u0275\u0275directiveInject(M.Pm))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-transfer-bluescreen"]],decls:19,vars:2,consts:[[1,"bocc-bluescreen__body"],[1,"bocc-bluescreen__title"],["icon","chat-info"],[1,"bocc-bluescreen__terms"],[1,"bocc-bluescreen__term"],[1,"bocc-bluescreen__footer"],["id","btn_transfiya-send-bluescreen_submit","bocc-button","outline",1,"bocc-bluescreen__button",3,"click"],["id","btn_transfiya-send-bluescreen_hidden","class","bocc-bluescreen__button","bocc-button","flat",3,"click",4,"ngIf"],["id","btn_transfiya-send-bluescreen_hidden","bocc-button","flat",1,"bocc-bluescreen__button",3,"click"]],template:function(o,s){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1),e.\u0275\u0275element(2,"bocc-icon",2),e.\u0275\u0275elementStart(3,"label"),e.\u0275\u0275text(4," \xbfC\xf3mo funciona? "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"ul",3)(6,"li",4),e.\u0275\u0275text(7," Le notificaremos a tu contacto el env\xedo del dinero por mensaje de texto. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"li",4),e.\u0275\u0275text(9," Si en 12 horas tu contacto no acepta el dinero, regresar\xe1 a tu cuenta. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"li",4),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"li",4),e.\u0275\u0275text(13," Tu contacto podr\xe1 recibir el dinero a trav\xe9s de la entidad bancaria que elija, siempre y cuando tenga activo el servicio de TransfiYa. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(14,"div",5)(15,"button",6),e.\u0275\u0275listener("click",function(){return s.onSubmit()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Continuar"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(18,N,3,0,"button",7),e.\u0275\u0275elementEnd()),2&o&&(e.\u0275\u0275advance(11),e.\u0275\u0275textInterpolate1(" Realiza hasta 15 transferencias al d\xeda que sumen un valor m\xe1ximo de $",s.limit," "),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngIf",!1))},dependencies:[b.NgIf,x.Z,T.P],styles:["mbo-transfiya-transfer-bluescreen{position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;justify-content:space-between}\n"],encapsulation:2}),a})();var I=n(39904),k=n(95437),H=n(60817);let Y=(()=>{class a{constructor(o,s,g){this.mboProvider=o,this.managerTransfiya=s,this.legalDocumentProvider=g,this.downloading=!1,this.termAndConditionControl=new y.FormControl(!1)}ngBoccPortal(o){this.portal=o}onSubmit(){var o=this;return(0,h.Z)(function*(){(yield o.managerTransfiya.approvedTyC()).when({success:s=>{switch(s){case"APPROVED":o.mboProvider.navigation.next(I.Z6.TRANSFERS.TRANSFIYA.APPROVED.HOME);break;case"REQUEST":o.mboProvider.navigation.next(I.Z6.TRANSFERS.TRANSFIYA.ASK.SOURCE);break;case"PENDING":o.mboProvider.navigation.next(I.Z6.TRANSFERS.TRANSFIYA.PENDING.HOME);break;case"CONTACTS":o.mboProvider.navigation.next(I.Z6.TRANSFERS.TRANSFIYA.CONTACTS);break;default:o.mboProvider.navigation.next(I.Z6.TRANSFERS.TRANSFIYA.TRANSFER.AMOUNT)}}},()=>{o.portal?.close(),o.portal?.destroy()})})()}onTermAndConditions(){this.downloading||(this.downloading=!0,this.legalDocumentProvider.termsAndConditions().finally(()=>{this.downloading=!1}))}}return a.\u0275fac=function(o){return new(o||a)(e.\u0275\u0275directiveInject(k.ZL),e.\u0275\u0275directiveInject(M.Pm),e.\u0275\u0275directiveInject(k.uD))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-tyc-modal"]],decls:14,vars:4,consts:[[1,"mbo-transfiya-tyc-modal__content"],[1,"mbo-transfiya-tyc-modal__body"],[1,"mbo-transfiya-tyc-modal__title","smalltext-medium"],[1,"mbo-transfiya-tyc-modal__message","body2-medium"],["elementId","chck_transfiya-tyc-modal_accept",3,"formControl"],["id","lnk_transfiya-modal_tyc",3,"click"],[1,"mbo-transfiya-tyc-modal__footer"],["id","btn_transfiya-tyc-modal_submit","bocc-button","raised",3,"disabled","click"]],template:function(o,s){1&o&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),e.\u0275\u0275text(3," \xa1Te damos la bienvenida al servicio de transferencias inmediatas a n\xfameros celulares! "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",3),e.\u0275\u0275text(5," Para continuar debes "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"bocc-checkbox-label",4),e.\u0275\u0275text(7," Aceptar "),e.\u0275\u0275elementStart(8,"a",5),e.\u0275\u0275listener("click",function(){return s.onTermAndConditions()}),e.\u0275\u0275text(9," T\xe9rminos y condiciones. "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(10,"div",6)(11,"button",7),e.\u0275\u0275listener("click",function(){return s.onSubmit()}),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13,"Continuar"),e.\u0275\u0275elementEnd()()()()),2&o&&(e.\u0275\u0275advance(6),e.\u0275\u0275property("formControl",s.termAndConditionControl),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("downloading",s.downloading),e.\u0275\u0275advance(3),e.\u0275\u0275property("disabled",!s.termAndConditionControl.value))},dependencies:[H.a,T.P],styles:["mbo-transfiya-tyc-modal{position:relative;display:block;box-sizing:border-box}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x16)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x6)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__body bocc-checkbox-label a.downloading{pointer-events:none;opacity:.36}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__title{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__message{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__footer{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-transfiya-tyc-modal .mbo-transfiya-tyc-modal__footer>button{width:100%}\n"],encapsulation:2}),a})()},64561:(A,_,n)=>{n.d(_,{r:()=>T});var b=n(39904),f=n(95437),t=(n(42789),n(99877)),v=n(17007),h=n(90521);function M(m,i){if(1&m){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),t.\u0275\u0275listener("event",function(y){t.\u0275\u0275restoreView(r);const l=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(l.onComponent(y))}),t.\u0275\u0275elementEnd()()}if(2&m){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==r.history?null:r.history.date.dateFormat)("statusLabel",null==r.history?null:r.history.category)("statusColor",null==r.history?null:r.history.color)("subtitle",null==r.history?null:r.history.title)("number",null==r.history?null:r.history.phoneFormat)("description",null==r.history?null:r.history.description)("amount",null==r.history?null:r.history.amount)}}function x(m,i){1&m&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"bocc-card-information",5),t.\u0275\u0275elementEnd()),2&m&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}let T=(()=>{class m{constructor(r){this.mboProvider=r,this.skeleton=!1}onComponent(r){"component"===r&&this.history&&this.mboProvider.navigation.next(b.Z6.TRANSFERS.TRANSFIYA.HISTORY_INFORMATION,{redirect:"history",uuid:this.history.uuid})}}return m.\u0275fac=function(r){return new(r||m)(t.\u0275\u0275directiveInject(f.ZL))},m.\u0275cmp=t.\u0275\u0275defineComponent({type:m,selectors:[["mbo-transfiya-history-card"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfiya-history-card",4,"ngIf"],["class","mbo-transfiya-history-card__skeleton",4,"ngIf"],[1,"mbo-transfiya-history-card"],[3,"title","statusLabel","statusColor","subtitle","number","description","amount","event"],[1,"mbo-transfiya-history-card__skeleton"],[3,"skeleton"]],template:function(r,c){1&r&&(t.\u0275\u0275template(0,M,2,7,"div",0),t.\u0275\u0275template(1,x,2,1,"div",1)),2&r&&(t.\u0275\u0275property("ngIf",!c.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",c.skeleton))},dependencies:[v.NgIf,h.v],styles:["mbo-transfiya-history-card{position:relative;width:100%;display:block}mbo-transfiya-history-card .mbo-transfiya-history-card{border-bottom:var(--border-1-lighter-300)}mbo-transfiya-history-card .mbo-transfiya-history-card .bocc-card-information__body{padding:var(--sizing-x6) var(--sizing-x4)}\n"],encapsulation:2}),m})()},81502:(A,_,n)=>{n.d(_,{s:()=>T});var b=n(39904),f=n(95437),t=(n(42789),n(99877)),v=n(17007),h=n(90521);function M(m,i){if(1&m){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",2)(1,"bocc-card-information",3),t.\u0275\u0275listener("event",function(y){t.\u0275\u0275restoreView(r);const l=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(l.onComponent(y))}),t.\u0275\u0275elementEnd()()}if(2&m){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==r.history?null:r.history.date.dateFormat)("statusColor",null==r.history?null:r.history.color)("statusLabel",null==r.history?null:r.history.category)("subtitle",null==r.history?null:r.history.title)("number",null==r.history?null:r.history.phoneFormat)("description",null==r.history?null:r.history.description)("amount",null==r.history?null:r.history.amount)("expanded",!0)}}function x(m,i){1&m&&(t.\u0275\u0275elementStart(0,"div",4),t.\u0275\u0275element(1,"bocc-card-information",5),t.\u0275\u0275elementEnd()),2&m&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0)("expanded",!0))}let T=(()=>{class m{constructor(r){this.mboProvider=r,this.skeleton=!1}onComponent(r){"component"===r&&this.history&&this.mboProvider.navigation.next(b.Z6.TRANSFERS.TRANSFIYA.HISTORY_INFORMATION,{uuid:this.history.uuid})}}return m.\u0275fac=function(r){return new(r||m)(t.\u0275\u0275directiveInject(f.ZL))},m.\u0275cmp=t.\u0275\u0275defineComponent({type:m,selectors:[["mbo-transfiya-history-element"]],inputs:{history:"history",skeleton:"skeleton"},decls:2,vars:2,consts:[["class","mbo-transfiya-history-element__content",4,"ngIf"],["class","mbo-transfiya-history-element__skeleton",4,"ngIf"],[1,"mbo-transfiya-history-element__content"],[3,"title","statusColor","statusLabel","subtitle","number","description","amount","expanded","event"],[1,"mbo-transfiya-history-element__skeleton"],[3,"skeleton","expanded"]],template:function(r,c){1&r&&(t.\u0275\u0275template(0,M,2,8,"div",0),t.\u0275\u0275template(1,x,2,2,"div",1)),2&r&&(t.\u0275\u0275property("ngIf",!c.skeleton),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",c.skeleton))},dependencies:[v.NgIf,h.v],styles:["mbo-transfiya-history-element{position:relative;width:100%;display:block}mbo-transfiya-history-element .mbo-transfiya-history-element__content{position:relative;width:100%;display:flex}mbo-transfiya-history-element .mbo-transfiya-history-element__content bocc-card-information{border:var(--border-1-lighter-300);overflow:hidden;border-radius:var(--sizing-x6)}mbo-transfiya-history-element .mbo-transfiya-history-element__skeleton{position:relative;width:100%;padding:var(--sizing-x4);box-sizing:border-box;border-radius:var(--sizing-x4);border:var(--border-1-lighter-300)}\n"],encapsulation:2}),m})()},77691:(A,_,n)=>{n.d(_,{L:()=>y});var b=n(39904),f=n(95437),t=(n(42789),n(99877)),v=n(17007),h=n(50689),M=n(45542),x=n(81502);function T(l,E){if(1&l){const u=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",5)(1,"label",6),t.\u0275\u0275text(2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(3,"button",7),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(u);const P=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(P.onRedirectAll())}),t.\u0275\u0275elementStart(4,"span"),t.\u0275\u0275text(5,"Ver todas"),t.\u0275\u0275elementEnd()()()}if(2&l){const u=t.\u0275\u0275nextContext();t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" ",(null==u.history||null==u.history.range?null:u.history.range.label)||"SIN RESULTADOS"," "),t.\u0275\u0275advance(1),t.\u0275\u0275property("disabled",u.isEmpty||!(null!=u.history&&null!=u.history.range&&u.history.range.priority))}}function m(l,E){1&l&&t.\u0275\u0275element(0,"mbo-transfiya-history-element",10),2&l&&t.\u0275\u0275property("history",E.$implicit)}function i(l,E){if(1&l&&(t.\u0275\u0275elementStart(0,"div",8),t.\u0275\u0275template(1,m,1,1,"mbo-transfiya-history-element",9),t.\u0275\u0275elementEnd()),2&l){const u=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",u.history.firstPage)}}function r(l,E){1&l&&(t.\u0275\u0275elementStart(0,"div",11),t.\u0275\u0275element(1,"mbo-transfiya-history-element",12)(2,"mbo-transfiya-history-element",12),t.\u0275\u0275elementEnd()),2&l&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0),t.\u0275\u0275advance(1),t.\u0275\u0275property("skeleton",!0))}function c(l,E){if(1&l&&(t.\u0275\u0275elementStart(0,"mbo-message-empty",13),t.\u0275\u0275text(1),t.\u0275\u0275elementEnd()),2&l){const u=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",u.msgError," ")}}let y=(()=>{class l{constructor(u){this.mboProvider=u}get isEmpty(){return this.history?.isError||0===this.history?.collection.length}get msgError(){return this.history?.isError?"Lo sentimos, por el momento no pudimos consultar tus transacciones realizadas.":"Lo sentimos, por el momento no cuentas con transacciones realizadas."}onRedirectAll(){this.mboProvider.navigation.next(b.Z6.TRANSFERS.TRANSFIYA.HISTORY)}}return l.\u0275fac=function(u){return new(u||l)(t.\u0275\u0275directiveInject(f.ZL))},l.\u0275cmp=t.\u0275\u0275defineComponent({type:l,selectors:[["mbo-transfiya-history-list"]],inputs:{history:"history"},decls:5,vars:4,consts:[[1,"mbo-transfiya-history-list__content"],["class","mbo-transfiya-history-list__header bocc-subheader",4,"ngIf"],["class","mbo-transfiya-history-list__component",4,"ngIf"],["class","mbo-transfiya-history-list__skeleton",4,"ngIf"],["class","mbo-transfiya-history-list__empty",4,"ngIf"],[1,"mbo-transfiya-history-list__header","bocc-subheader"],[1,"caption-medium"],["id","btn_transfiya-history-list_all","bocc-button","flat",3,"disabled","click"],[1,"mbo-transfiya-history-list__component"],[3,"history",4,"ngFor","ngForOf"],[3,"history"],[1,"mbo-transfiya-history-list__skeleton"],[3,"skeleton"],[1,"mbo-transfiya-history-list__empty"]],template:function(u,S){1&u&&(t.\u0275\u0275elementStart(0,"div",0),t.\u0275\u0275template(1,T,6,2,"div",1),t.\u0275\u0275template(2,i,2,1,"div",2),t.\u0275\u0275template(3,r,3,2,"div",3),t.\u0275\u0275template(4,c,2,1,"mbo-message-empty",4),t.\u0275\u0275elementEnd()),2&u&&(t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",S.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",S.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!S.history),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",S.isEmpty))},dependencies:[v.NgForOf,v.NgIf,h.A,M.P,x.s],styles:["mbo-transfiya-history-list{position:relative;width:100%;display:block}mbo-transfiya-history-list .mbo-transfiya-history-list__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8);padding:var(--sizing-x8);box-sizing:border-box}mbo-transfiya-history-list .mbo-transfiya-history-list__header{--bocc-button-padding: 0rem var(--sizing-x4)}mbo-transfiya-history-list .mbo-transfiya-history-list__header>label{text-transform:uppercase}mbo-transfiya-history-list .mbo-transfiya-history-list__component,mbo-transfiya-history-list .mbo-transfiya-history-list__skeleton{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}\n"],encapsulation:2}),l})()},71638:(A,_,n)=>{n.r(_),n.d(_,{MboTransfiyaModule:()=>m});var b=n(17007),f=n(78007),e=n(15861),t=n(30263),C=n(65715),v=n(17698),p=n(99877);let h=(()=>{class i{constructor(c,y){this.modalService=c,this.requestConfiguration=y}canActivate(){var c=this;return(0,e.Z)(function*(){return(yield c.requestConfiguration.allowAccess()).when({success:y=>(y||c.modalService.create(C.KW).open(120),y),failure:()=>!1})})()}}return i.\u0275fac=function(c){return new(c||i)(p.\u0275\u0275inject(t.iM),p.\u0275\u0275inject(v.tE))},i.\u0275prov=p.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})();const T=[{path:"",loadChildren:()=>n.e(2880).then(n.bind(n,92880)).then(i=>i.MboTransfiyaHomePageModule),canActivate:[h]},{path:"history",loadChildren:()=>n.e(861).then(n.bind(n,90861)).then(i=>i.MboTransfiyaHistoryPageModule),canActivate:[h]},{path:"history/information",loadChildren:()=>n.e(9041).then(n.bind(n,69041)).then(i=>i.MboTransfiyaHistoryInformationPageModule)},{path:"transfer",loadChildren:()=>n.e(3142).then(n.bind(n,13142)).then(i=>i.MboTransfiyaTransferModule),canActivate:[(()=>{class i{constructor(c,y,l){this.blueScreenService=c,this.modalService=y,this.requestConfiguration=l}canActivate(){var c=this;return(0,e.Z)(function*(){return(yield c.requestConfiguration.requiredHelperTransfer()).when({success:({helper:y,transfiya:l})=>(l?y||c.blueScreenService.create(C.x3).open(120):c.modalService.create(C.KW).open(120),l),failure:()=>!1})})()}}return i.\u0275fac=function(c){return new(c||i)(p.\u0275\u0275inject(t.Dl),p.\u0275\u0275inject(t.iM),p.\u0275\u0275inject(v.tE))},i.\u0275prov=p.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()]},{path:"ask",loadChildren:()=>n.e(7776).then(n.bind(n,27776)).then(i=>i.MboTransfiyaAskModule),canActivate:[(()=>{class i{constructor(c,y,l){this.blueScreenService=c,this.modalService=y,this.requestConfiguration=l}canActivate(){var c=this;return(0,e.Z)(function*(){return(yield c.requestConfiguration.requiredHelperAsk()).when({success:({helper:y,transfiya:l})=>(l?y||c.blueScreenService.create(C.QG).open(120):c.modalService.create(C.KW).open(120),l),failure:()=>!1})})()}}return i.\u0275fac=function(c){return new(c||i)(p.\u0275\u0275inject(t.Dl),p.\u0275\u0275inject(t.iM),p.\u0275\u0275inject(v.tE))},i.\u0275prov=p.\u0275\u0275defineInjectable({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()]},{path:"approved",loadChildren:()=>n.e(1618).then(n.bind(n,1618)).then(i=>i.MboTransfiyaApprovedModule),canActivate:[h]},{path:"pending",loadChildren:()=>n.e(4813).then(n.bind(n,14813)).then(i=>i.MboTransfiyaPendingModule),canActivate:[h]},{path:"contacts",loadChildren:()=>n.e(3692).then(n.bind(n,63692)).then(i=>i.MboTransfiyaContactsPageModule),canActivate:[h]},{path:"favorite-account",loadComponent:()=>n.e(9319).then(n.bind(n,99319)).then(i=>i.MboTransfiyaFavoriteAccountPage),canActivate:[h]}];let m=(()=>{class i{}return i.\u0275fac=function(c){return new(c||i)},i.\u0275mod=p.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=p.\u0275\u0275defineInjector({imports:[b.CommonModule,f.RouterModule.forChild(T)]}),i})()},40914:(A,_,n)=>{n.d(_,{R:()=>b,r:()=>f});const b={MAX_CEL_TO_CEL:5e6,MIN_CEL_TO_CEL:1,MIN_DALE:1e3,MAX_TAG_AVAL:5e6,MIN_TAG_AVAL:1,MAX_TRANSFIYA:3e6,MIN_TRANSFIYA:100,MAX_UNREGISTERED:45e5,MIN_UNREGISTERED:15e5},f={MIN_TRUSTFUND:2e5}}}]);