(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3284],{37907:(j,v,n)=>{n.d(v,{L:()=>I,m:()=>M});var d=n(15861),m=n(87956),h=n(53113),a=n(98699);class i{constructor(s,t,e){this.source=s,this.destination=t,this.amount=e}}function l(o){return new i(o.source,o.destination,o.amount)}var U=n(71776),f=n(39904),g=n(87903),S=n(42168),b=n(84757),u=n(99877);let Q=(()=>{class o{constructor(t){this.http=t}send(t){const e={hashCheckingAcct:t.destination.id,hashLoanAcct:t.source.id,amount:String(t.amount)};return(0,S.firstValueFrom)(this.http.post(f.bV.TRANSACTIONS.CREDIT_QUOTA,e).pipe((0,b.map)(r=>(0,g.l1)(r,"SUCCESS")))).catch(r=>(0,g.rU)(r))}}return o.\u0275fac=function(t){return new(t||o)(u.\u0275\u0275inject(U.HttpClient))},o.\u0275prov=u.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var E=n(20691);let C=(()=>{class o extends E.Store{constructor(){super({confirmation:!1,fromCustomer:!1})}setSource(t,e=!1){this.reduce(r=>({...r,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setDestination(t){this.reduce(e=>({...e,destination:t}))}setAmount(t){this.reduce(e=>({...e,amount:t}))}selectForAmount(){return this.select(({confirmation:t,amount:e,source:r})=>({amount:e,confirmation:t,source:r}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=u.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),I=(()=>{class o{constructor(t,e,r,c){this.products=t,this.eventBusService=e,this.repository=r,this.store=c}setSource(t){var e=this;return(0,d.Z)(function*(){try{return yield e.products.requestInformation(t),a.Either.success(e.store.setSource(t))}catch({message:r}){return a.Either.failure({message:r})}})()}setDestination(t){try{return a.Either.success(this.store.setDestination(t))}catch({message:e}){return a.Either.failure({message:e})}}setAmount(t){try{return a.Either.success(this.store.setAmount(t))}catch({message:e}){return a.Either.failure({message:e})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getSource();return this.store.reset(),a.Either.success({fromCustomer:t,source:e})}catch({message:t}){return a.Either.failure({message:t})}}send(){var t=this;return(0,d.Z)(function*(){const e=l(t.store.currentState),r=yield t.save(e);return t.eventBusService.emit(r.channel),a.Either.success({creditUseQuota:e,status:r})})()}save(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(h.LN.error(e))}}}return o.\u0275fac=function(t){return new(t||o)(u.\u0275\u0275inject(m.M5),u.\u0275\u0275inject(m.Yd),u.\u0275\u0275inject(Q),u.\u0275\u0275inject(C))},o.\u0275prov=u.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var y=n(89148);let M=(()=>{class o{constructor(t,e,r){this.products=t,this.productService=e,this.store=r}source(){var t=this;return(0,d.Z)(function*(){try{const e=t.store.itIsConfirmation(),r=yield t.requestCredits();return a.Either.success({confirmation:e,products:r})}catch({message:e}){return a.Either.failure({message:e})}})()}destination(t){var e=this;return(0,d.Z)(function*(){try{const r=yield e.products.requestAccountsForTransfer(),c=yield e.requestCredits(),p=e.store.itIsConfirmation(),R=e.requestCredit(c,t);return a.Either.success({accounts:r,confirmation:p,products:c,source:R})}catch({message:r}){return a.Either.failure({message:r})}})()}amount(){var t=this;return(0,d.Z)(function*(){try{const{amount:e,confirmation:r,source:c}=t.store.selectForAmount(),p=yield t.requestSection(c);return a.Either.success({confirmation:r,section:p,source:c,value:e})}catch({message:e}){return a.Either.failure({message:e})}})()}confirmation(){try{const t=l(this.store.currentState);return a.Either.success({creditUseQuota:t})}catch({message:t}){return a.Either.failure({message:t})}}requestCredits(){return this.products.requestProducts([y.Gt.ResolvingCredit])}requestCredit(t,e){let r=this.store.getSource();return!r&&e&&(r=t.find(({id:c})=>e===c),this.store.setSource(r,!0)),r}requestSection(t){return this.productService.requestInformation(t).then(e=>e?.getSection(y.Av.LoanQuotaAvailable))}}return o.\u0275fac=function(t){return new(t||o)(u.\u0275\u0275inject(m.hM),u.\u0275\u0275inject(m.M5),u.\u0275\u0275inject(C))},o.\u0275prov=u.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},73284:(j,v,n)=>{n.r(v),n.d(v,{MboCreditUseQuotaResultPageModule:()=>o});var d=n(17007),m=n(78007),h=n(79798),a=n(15861),i=n(99877),l=n(39904),U=n(95437),f=n(87903),g=n(53113);function S({isError:s,message:t}){return s?{animation:l.cj,title:"\xa1Uso de cupo fallido!",subtitle:t}:{animation:l.F6,title:"\xa1Cupo usado exitosamente!",subtitle:t}}function b({isError:s}){return[{event:s?"finish":"again",label:s?"Finalizar":"Hacer otra transferencia",type:"outline"},{event:s?"retry":"finish",label:s?"Volver a intentar":"Finalizar",type:"raised"}]}var Q=n(37907),E=n(10464),C=n(78021),I=n(16442);function y(s,t){if(1&s&&(i.\u0275\u0275elementStart(0,"div",4),i.\u0275\u0275element(1,"mbo-header-result",5),i.\u0275\u0275elementEnd()),2&s){const e=i.\u0275\u0275nextContext();i.\u0275\u0275advance(1),i.\u0275\u0275property("rightActions",e.rightActions)}}let M=(()=>{class s{constructor(e,r,c){this.ref=e,this.mboProvider=r,this.managerUseQuota=c,this.requesting=!0,this.template=l.$d,this.rightActions=[{id:"btn_credit-quota-result-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_credit-use-quota-result-page_template"),this.intializatedTransaction()}onAction(e){this.mboProvider.navigation.next("finish"===e?l.Z6.CUSTOMER.PRODUCTS.HOME:l.Z6.TRANSACTIONS.CREDIT_USE_QUOTA.SOURCE)}intializatedTransaction(){var e=this;return(0,a.Z)(function*(){(yield e.managerUseQuota.send()).when({success:r=>{e.template=function u({creditUseQuota:s,status:t}){const{dateFormat:e,timeFormat:r}=new g.ou;return{actions:b(t),error:t.isError,header:S(t),informations:[(0,f.SP)("DESTINO",s.destination.name,s.destination.shortNumber),(0,f._f)("SUMA DE",s.amount),(0,f.cZ)(e,r)],skeleton:!1}}(r)}},()=>{e.requesting=!1,e.managerUseQuota.reset()})})()}}return s.\u0275fac=function(e){return new(e||s)(i.\u0275\u0275directiveInject(i.ElementRef),i.\u0275\u0275directiveInject(U.ZL),i.\u0275\u0275directiveInject(Q.L))},s.\u0275cmp=i.\u0275\u0275defineComponent({type:s,selectors:[["mbo-credit-use-quota-result-page"]],decls:5,vars:2,consts:[[1,"mbo-credit-use-quota-result-page__content","mbo-page__scroller"],["class","mbo-credit-use-quota-result-page__header mbo-page__header",4,"ngIf"],[1,"mbo-credit-use-quota-result-page__body"],["id","crd_credit-use-quota-result-page_template",3,"template","action"],[1,"mbo-credit-use-quota-result-page__header","mbo-page__header"],[3,"rightActions"]],template:function(e,r){1&e&&(i.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0),i.\u0275\u0275template(2,y,2,1,"div",1),i.\u0275\u0275elementStart(3,"div",2)(4,"mbo-card-transaction-template",3),i.\u0275\u0275listener("action",function(p){return r.onAction(p)}),i.\u0275\u0275elementEnd()()()()),2&e&&(i.\u0275\u0275advance(2),i.\u0275\u0275property("ngIf",!r.requesting),i.\u0275\u0275advance(2),i.\u0275\u0275property("template",r.template))},dependencies:[d.NgIf,E.K,C.c,I.u],styles:["/*!\n * MBO CreditUseQuotaResult Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 15/Nov/2022\n * Updated: 05/Ene/2024\n*/mbo-credit-use-quota-result-page{position:relative;width:100%;height:100%}mbo-credit-use-quota-result-page .mbo-credit-use-quota-result-page__content{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-credit-use-quota-result-page .mbo-credit-use-quota-result-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-credit-use-quota-result-page .mbo-credit-use-quota-result-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}\n"],encapsulation:2}),s})(),o=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=i.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=i.\u0275\u0275defineInjector({imports:[d.CommonModule,m.RouterModule.forChild([{path:"",component:M}]),h.KI,h.cN,h.tu]}),s})()}}]);