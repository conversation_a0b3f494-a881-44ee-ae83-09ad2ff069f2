(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8205],{68205:(A,e,a)=>{a.r(e),a.d(e,{regAlphabetic:()=>r,regAlphanumber:()=>n,regDecimal:()=>s,regEmail:()=>l,regOnlyNumber:()=>g,regOnlyText:()=>c,regPassword:()=>t});const r=/^[a-z|A-Z| |\xf1|\xd1|\xe1|\xc1|\xe9|\xc9|\xed|\xcd|\xf3|\xd3|\xfa|\xda|\xfc|\xdc]*$/,n=/^[0-9|a-z|A-Z|\xf1|\xd1|\xe1|\xc1|\xe9|\xc9|\xed|\xcd|\xf3|\xd3|\xfa|\xda|\xfc|\xdc]*$/,s=/^[0-9|,|.|+|-]*$/,l=/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,g=/^[0-9]*$/,c=/^[a-z|A-Z|\xf1|\xd1|\xe1|\xc1|\xe9|\xc9|\xed|\xcd|\xf3|\xd3|\xfa|\xda|\xfc|\xdc]*$/,t=/^[a-z|A-Z|\xf1|\xd1|0-9|.|!|\xa1|@|_|-|#|$|&|%]*$/}}]);