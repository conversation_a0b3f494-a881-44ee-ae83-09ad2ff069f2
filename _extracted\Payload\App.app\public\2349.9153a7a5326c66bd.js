(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2349,922],{36319:(oe,P,a)=>{a.d(P,{g:()=>h});var y=a(72972);const h=()=>{if(void 0!==y.w)return y.w.Capacitor}},37003:(oe,P,a)=>{a.d(P,{I:()=>C,a:()=>re,b:()=>s,c:()=>Z,d:()=>J,f:()=>q,g:()=>b,i:()=>E,p:()=>u,r:()=>Q,s:()=>H});var y=a(15861),h=a(78635),_=a(28909);const s="ion-content",C=".ion-content-scroll-host",B=`${s}, ${C}`,E=c=>"ION-CONTENT"===c.tagName,b=function(){var c=(0,y.Z)(function*(g){return E(g)?(yield new Promise(N=>(0,h.c)(g,N)),g.getScrollElement()):g});return function(N){return c.apply(this,arguments)}}(),re=c=>c.querySelector(C)||c.querySelector(B),q=c=>c.closest(B),H=(c,g)=>E(c)?c.scrollToTop(g):Promise.resolve(c.scrollTo({top:0,left:0,behavior:g>0?"smooth":"auto"})),Z=(c,g,N,ee)=>E(c)?c.scrollByPoint(g,N,ee):Promise.resolve(c.scrollBy({top:N,left:g,behavior:ee>0?"smooth":"auto"})),u=c=>(0,_.b)(c,s),J=c=>{if(E(c)){const N=c.scrollY;return c.scrollY=!1,N}return c.style.setProperty("overflow","hidden"),!0},Q=(c,g)=>{E(c)?c.scrollY=g:c.style.removeProperty("overflow")}},42349:(oe,P,a)=>{a.r(P),a.d(P,{ion_modal:()=>ke});var y=a(15861),h=a(42477),_=a(37003),K=a(25030),s=a(78635),C=a(37389),B=a(28909),E=a(36319),b=a(57346),re=a(23814),q=a(39721),H=a(37943),Z=a(90922),u=a(44963),J=a(65069),Q=a(35067),c=a(72972),I=(a(33006),a(93037),a(22889),(()=>{return(e=I||(I={})).Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",I;var e})());const i={getEngine(){const e=(0,E.g)();if(e?.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},supportsDefaultStatusBarStyle:()=>!!(0,E.g)()?.PluginHeaders,setStyle(e){const t=this.getEngine();t&&t.setStyle(e)},getStyle:(e=(0,y.Z)(function*(){const t=this.getEngine();if(!t)return I.Default;const{style:n}=yield t.getInfo();return n}),function(){return e.apply(this,arguments)})},T=(e,t)=>{if(1===t)return 0;const n=1/(1-t);return e*n+-t*n},de=()=>{!c.w||c.w.innerWidth>=768||!i.supportsDefaultStatusBarStyle()||i.setStyle({style:I.Dark})},se=(e=I.Default)=>{!c.w||c.w.innerWidth>=768||!i.supportsDefaultStatusBarStyle()||i.setStyle({style:e})},me=function(){var e=(0,y.Z)(function*(t,n){"function"!=typeof t.canDismiss||!(yield t.canDismiss(void 0,b.G))||(n.isRunning()?n.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))});return function(n,o){return e.apply(this,arguments)}}(),he=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,be=(e,t)=>(0,s.l)(400,e/Math.abs(1.1*t),500),ge=e=>{const{currentBreakpoint:t,backdropBreakpoint:n}=e,o=void 0===n||n<t,d=o?`calc(var(--backdrop-opacity) * ${t})`:"0",r=(0,u.c)("backdropAnimation").fromTo("opacity",0,d);return o&&r.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),{wrapperAnimation:(0,u.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-100*t}%)`}]),backdropAnimation:r}},ye=e=>{const{currentBreakpoint:t,backdropBreakpoint:n}=e,o=`calc(var(--backdrop-opacity) * ${T(t,n)})`,d=[{offset:0,opacity:o},{offset:1,opacity:0}],r=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],l=(0,u.c)("backdropAnimation").keyframes(0!==n?r:d);return{wrapperAnimation:(0,u.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-100*t}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:l}},_e=(e,t)=>{const{presentingEl:n,currentBreakpoint:o}=t,d=(0,s.g)(e),{wrapperAnimation:r,backdropAnimation:l}=void 0!==o?ge(t):{backdropAnimation:(0,u.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,u.c)().fromTo("transform","translateY(100vh)","translateY(0vh)")};l.addElement(d.querySelector("ion-backdrop")),r.addElement(d.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});const p=(0,u.c)("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation(r);if(n){const f=window.innerWidth<768,M="ION-MODAL"===n.tagName&&void 0!==n.presentingElement,w=(0,s.g)(n),L=(0,u.c)().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),A=document.body;if(f){const D=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",O=`translateY(${M?"-10px":D}) scale(0.93)`;L.afterStyles({transform:O}).beforeAddWrite(()=>A.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:O,borderRadius:"10px 10px 0 0"}]),p.addAnimation(L)}else if(p.addAnimation(l),M){const x=`translateY(-10px) scale(${M?.93:1})`;L.afterStyles({transform:x}).addElement(w.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:x}]);const m=(0,u.c)().afterStyles({transform:x}).addElement(w.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:x}]);p.addAnimation([L,m])}else r.fromTo("opacity","0","1")}else p.addAnimation(l);return p},Ee=(e,t,n=500)=>{const{presentingEl:o,currentBreakpoint:d}=t,r=(0,s.g)(e),{wrapperAnimation:l,backdropAnimation:p}=void 0!==d?ye(t):{backdropAnimation:(0,u.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,u.c)().fromTo("transform","translateY(0vh)","translateY(100vh)")};p.addElement(r.querySelector("ion-backdrop")),l.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});const f=(0,u.c)("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(l);if(o){const M=window.innerWidth<768,w="ION-MODAL"===o.tagName&&void 0!==o.presentingElement,L=(0,s.g)(o),A=(0,u.c)().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(x=>{1===x&&(o.style.setProperty("overflow",""),Array.from(D.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(O=>void 0!==O.presentingElement).length<=1&&D.style.setProperty("background-color",""))}),D=document.body;if(M){const x=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",X=`translateY(${w?"-10px":x}) scale(0.93)`;A.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:X,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),f.addAnimation(A)}else if(f.addAnimation(p),w){const m=`translateY(-10px) scale(${w?.93:1})`;A.addElement(L.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:m},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);const O=(0,u.c)().addElement(L.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:m},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);f.addAnimation([A,O])}else l.fromTo("opacity","1","0")}else f.addAnimation(p);return f},Ae=(e,t)=>{const{currentBreakpoint:n}=t,o=(0,s.g)(e),{wrapperAnimation:d,backdropAnimation:r}=void 0!==n?ge(t):{backdropAnimation:(0,u.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,u.c)().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}])};return r.addElement(o.querySelector("ion-backdrop")),d.addElement(o.querySelector(".modal-wrapper")),(0,u.c)().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([r,d])},Be=(e,t)=>{const{currentBreakpoint:n}=t,o=(0,s.g)(e),{wrapperAnimation:d,backdropAnimation:r}=void 0!==n?ye(t):{backdropAnimation:(0,u.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,u.c)().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}])};return r.addElement(o.querySelector("ion-backdrop")),d.addElement(o.querySelector(".modal-wrapper")),(0,u.c)().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,d])},ke=class{constructor(e){(0,h.r)(this,e),this.didPresent=(0,h.d)(this,"ionModalDidPresent",7),this.willPresent=(0,h.d)(this,"ionModalWillPresent",7),this.willDismiss=(0,h.d)(this,"ionModalWillDismiss",7),this.didDismiss=(0,h.d)(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=(0,h.d)(this,"ionBreakpointDidChange",7),this.didPresentShorthand=(0,h.d)(this,"didPresent",7),this.willPresentShorthand=(0,h.d)(this,"willPresent",7),this.willDismissShorthand=(0,h.d)(this,"willDismiss",7),this.didDismissShorthand=(0,h.d)(this,"didDismiss",7),this.ionMount=(0,h.d)(this,"ionMount",7),this.lockController=(0,C.c)(),this.triggerController=(0,b.e)(),this.coreDelegate=(0,K.C)(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.onHandleClick=()=>{const{sheetTransition:t,handleBehavior:n}=this;"cycle"!==n||void 0!==t||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{const{sheetTransition:t}=this;void 0===t&&this.dismiss(void 0,b.B)},this.onLifecycle=t=>{const n=this.usersElement,o=Te[t.type];if(n&&o){const d=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(d)}},this.presented=!1,this.hasController=!1,this.overlayIndex=void 0,this.delegate=void 0,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.breakpoints=void 0,this.initialBreakpoint=void 0,this.backdropBreakpoint=0,this.handle=void 0,this.handleBehavior="none",this.component=void 0,this.componentProps=void 0,this.cssClass=void 0,this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.presentingElement=void 0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0,this.keepContentsMounted=!1,this.canDismiss=!0}onIsOpenChange(e,t){!0===e&&!1===t?this.present():!1===e&&!0===t&&this.dismiss()}triggerChanged(){const{trigger:e,el:t,triggerController:n}=this;e&&n.addClickListener(t,e)}breakpointsChanged(e){void 0!==e&&(this.sortedBreakpoints=e.sort((t,n)=>t-n))}connectedCallback(){const{el:e}=this;(0,b.j)(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){const{breakpoints:e,initialBreakpoint:t,el:n,htmlAttributes:o}=this,d=this.isSheetModal=void 0!==e&&void 0!==t,r=["aria-label","role"];this.inheritedAttributes=(0,s.k)(n,r),void 0!==o&&r.forEach(l=>{o[l]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[l]:o[l]}),delete o[l])}),d&&(this.currentBreakpoint=this.initialBreakpoint),void 0!==e&&void 0!==t&&!e.includes(t)&&(0,B.p)("Your breakpoints array must include the initialBreakpoint value."),(0,b.k)(n)}componentDidLoad(){!0===this.isOpen&&(0,s.r)(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};const n=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:n,delegate:this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate}}checkCanDismiss(e,t){var n=this;return(0,y.Z)(function*(){const{canDismiss:o}=n;return"function"==typeof o?o(e,t):o})()}present(){var e=this;return(0,y.Z)(function*(){const t=yield e.lockController.lock();if(e.presented)return void t();const{presentingElement:n,el:o}=e;e.currentBreakpoint=e.initialBreakpoint;const{inline:d,delegate:r}=e.getDelegate(!0);e.ionMount.emit(),e.usersElement=yield(0,K.a)(r,o,e.component,["ion-page"],e.componentProps,d),(0,s.m)(o)?yield(0,q.e)(e.usersElement):e.keepContentsMounted||(yield(0,q.w)()),(0,h.w)(()=>e.el.classList.add("show-modal"));const l=void 0!==n;l&&"ios"===(0,H.b)(e)&&(e.statusBarStyle=yield i.getStyle(),de()),yield(0,b.f)(e,"modalEnter",_e,Ae,{presentingEl:n,currentBreakpoint:e.initialBreakpoint,backdropBreakpoint:e.backdropBreakpoint}),typeof window<"u"&&(e.keyboardOpenCallback=()=>{e.gesture&&(e.gesture.enable(!1),(0,s.r)(()=>{e.gesture&&e.gesture.enable(!0)}))},window.addEventListener(Z.KEYBOARD_DID_OPEN,e.keyboardOpenCallback)),e.isSheetModal?e.initSheetGesture():l&&e.initSwipeToClose(),t()})()}initSwipeToClose(){var t,e=this;if("ios"!==(0,H.b)(this))return;const{el:n}=this,o=this.leaveAnimation||H.c.get("modalLeave",Ee),d=this.animation=o(n,{presentingEl:this.presentingElement});if(!(0,_.a)(n))return void(0,_.p)(n);const l=null!==(t=this.statusBarStyle)&&void 0!==t?t:I.Default;this.gesture=((e,t,n,o)=>{const r=e.offsetHeight;let l=!1,p=!1,f=null,M=null,L=!0,A=0;const ie=(0,Q.createGesture)({el:e,gestureName:"modalSwipeToClose",gesturePriority:b.O,direction:"y",threshold:10,canStart:v=>{const k=v.event.target;return null===k||!k.closest||(f=(0,_.f)(k),f?(M=(0,_.i)(f)?(0,s.g)(f).querySelector(".inner-scroll"):f,!f.querySelector("ion-refresher")&&0===M.scrollTop):null===k.closest("ion-footer"))},onStart:v=>{const{deltaY:k}=v;L=!f||!(0,_.i)(f)||f.scrollY,p=void 0!==e.canDismiss&&!0!==e.canDismiss,k>0&&f&&(0,_.d)(f),t.progressStart(!0,l?1:0)},onMove:v=>{const{deltaY:k}=v;k>0&&f&&(0,_.d)(f);const W=v.deltaY/r,G=W>=0&&p,z=G?.2:.9999,te=G?he(W/z):W,j=(0,s.l)(1e-4,te,z);t.progressStep(j),j>=.5&&A<.5?se(n):j<.5&&A>=.5&&de(),A=j},onEnd:v=>{const k=v.velocityY,W=v.deltaY/r,G=W>=0&&p,z=G?.2:.9999,te=G?he(W/z):W,j=(0,s.l)(1e-4,te,z),V=!G&&(v.deltaY+1e3*k)/r>=.5;let ae=V?-.001:.001;V?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),ae+=(0,J.g)([0,0],[.32,.72],[0,1],[1,1],j)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),ae+=(0,J.g)([0,0],[1,0],[.68,.28],[1,1],j)[0]);const ce=be(V?W*r:(1-j)*r,k);l=V,ie.enable(!1),f&&(0,_.r)(f,L),t.onFinish(()=>{V||ie.enable(!0)}).progressEnd(V?1:0,ae,ce),G&&j>z/4?me(e,t):V&&o()}});return ie})(n,d,l,()=>{this.gestureAnimationDismissing=!0,se(this.statusBarStyle),this.animation.onFinish((0,y.Z)(function*(){yield e.dismiss(void 0,b.G),e.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){const{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:n}=this;if(!e||void 0===t)return;const o=this.enterAnimation||H.c.get("modalEnter",_e),d=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:n});d.progressStart(!0,1);const{gesture:r,moveSheetToBreakpoint:l}=((e,t,n,o,d,r,l=[],p,f,M)=>{const A={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:0!==d?[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-d,opacity:0},{offset:1,opacity:0}]:[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}]},D=e.querySelector("ion-content"),x=n.clientHeight;let m=o,O=0,X=!1;const v=r.childAnimations.find(S=>"wrapperAnimation"===S.id),k=r.childAnimations.find(S=>"backdropAnimation"===S.id),W=l[l.length-1],G=l[0],z=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove("ion-disable-focus-trap")},te=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add("ion-disable-focus-trap")};v&&k&&(v.keyframes([...A.WRAPPER_KEYFRAMES]),k.keyframes([...A.BACKDROP_KEYFRAMES]),r.progressStart(!0,1-m),m>d?z():te()),D&&m!==W&&(D.scrollY=!1);const ce=S=>{const{breakpoint:R,canDismiss:Y,breakpointOffset:$,animated:ne}=S,F=Y&&0===R,U=F?m:R,ve=0!==U;return m=0,v&&k&&(v.keyframes([{offset:0,transform:`translateY(${100*$}%)`},{offset:1,transform:`translateY(${100*(1-U)}%)`}]),k.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${T(1-$,d)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${T(U,d)})`}]),r.progressStep(0)),pe.enable(!1),F?me(e,r):ve||f(),D&&U===l[l.length-1]&&(D.scrollY=!0),new Promise(ue=>{r.onFinish(()=>{ve?v&&k?(0,s.r)(()=>{v.keyframes([...A.WRAPPER_KEYFRAMES]),k.keyframes([...A.BACKDROP_KEYFRAMES]),r.progressStart(!0,1-U),m=U,M(m),m>d?z():te(),pe.enable(!0),ue()}):(pe.enable(!0),ue()):ue()},{oneTimeCallback:!0}).progressEnd(1,0,ne?500:0)})},pe=(0,Q.createGesture)({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:S=>{const R=(0,_.f)(S.event.target);if(m=p(),1===m&&R){const Y=(0,_.i)(R)?(0,s.g)(R).querySelector(".inner-scroll"):R;return!R.querySelector("ion-refresher")&&0===Y.scrollTop}return!0},onStart:S=>{X=void 0!==e.canDismiss&&!0!==e.canDismiss&&0===G,S.deltaY>0&&D&&(D.scrollY=!1),(0,s.r)(()=>{e.focus()}),r.progressStart(!0,1-m)},onMove:S=>{S.deltaY>0&&D&&(D.scrollY=!1);const Y=l.length>1?1-l[1]:void 0,$=1-m+S.deltaY/x,ne=void 0!==Y&&$>=Y&&X,F=ne?.95:.9999,U=ne&&void 0!==Y?Y+he(($-Y)/(F-Y)):$;O=(0,s.l)(1e-4,U,F),r.progressStep(O)},onEnd:S=>{const $=m-(S.deltaY+350*S.velocityY)/x,ne=l.reduce((F,U)=>Math.abs(U-$)<Math.abs(F-$)?U:F);ce({breakpoint:ne,breakpointOffset:O,canDismiss:X,animated:!0})}});return{gesture:pe,moveSheetToBreakpoint:ce}})(this.el,this.backdropEl,e,t,n,d,this.sortedBreakpoints,()=>{var p;return null!==(p=this.currentBreakpoint)&&void 0!==p?p:0},()=>this.sheetOnDismiss(),p=>{this.currentBreakpoint!==p&&(this.currentBreakpoint=p,this.ionBreakpointDidChange.emit({breakpoint:p}))});this.gesture=r,this.moveSheetToBreakpoint=l,this.gesture.enable(!0)}sheetOnDismiss(){var e=this;this.gestureAnimationDismissing=!0,this.animation.onFinish((0,y.Z)(function*(){e.currentBreakpoint=0,e.ionBreakpointDidChange.emit({breakpoint:e.currentBreakpoint}),yield e.dismiss(void 0,b.G),e.gestureAnimationDismissing=!1}))}dismiss(e,t){var n=this;return(0,y.Z)(function*(){var o;if(n.gestureAnimationDismissing&&t!==b.G)return!1;const d=yield n.lockController.lock();if("handler"!==t&&!(yield n.checkCanDismiss(e,t)))return d(),!1;const{presentingElement:r}=n;void 0!==r&&"ios"===(0,H.b)(n)&&se(n.statusBarStyle),typeof window<"u"&&n.keyboardOpenCallback&&(window.removeEventListener(Z.KEYBOARD_DID_OPEN,n.keyboardOpenCallback),n.keyboardOpenCallback=void 0);const p=yield(0,b.g)(n,e,t,"modalLeave",Ee,Be,{presentingEl:r,currentBreakpoint:null!==(o=n.currentBreakpoint)&&void 0!==o?o:n.initialBreakpoint,backdropBreakpoint:n.backdropBreakpoint});if(p){const{delegate:f}=n.getDelegate();yield(0,K.d)(f,n.usersElement),(0,h.w)(()=>n.el.classList.remove("show-modal")),n.animation&&n.animation.destroy(),n.gesture&&n.gesture.destroy()}return n.currentBreakpoint=void 0,n.animation=void 0,d(),p})()}onDidDismiss(){return(0,b.h)(this.el,"ionModalDidDismiss")}onWillDismiss(){return(0,b.h)(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){var t=this;return(0,y.Z)(function*(){if(!t.isSheetModal)return void(0,B.p)("setCurrentBreakpoint is only supported on sheet modals.");if(!t.breakpoints.includes(e))return void(0,B.p)(`Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);const{currentBreakpoint:n,moveSheetToBreakpoint:o,canDismiss:d,breakpoints:r,animated:l}=t;n!==e&&o&&(t.sheetTransition=o({breakpoint:e,breakpointOffset:1-n,canDismiss:void 0!==d&&!0!==d&&0===r[0],animated:l}),yield t.sheetTransition,t.sheetTransition=void 0)})()}getCurrentBreakpoint(){var e=this;return(0,y.Z)(function*(){return e.currentBreakpoint})()}moveToNextBreakpoint(){var e=this;return(0,y.Z)(function*(){const{breakpoints:t,currentBreakpoint:n}=e;if(!t||null==n)return!1;const o=t.filter(p=>0!==p),r=(o.indexOf(n)+1)%o.length,l=o[r];return yield e.setCurrentBreakpoint(l),!0})()}render(){const{handle:e,isSheetModal:t,presentingElement:n,htmlAttributes:o,handleBehavior:d,inheritedAttributes:r}=this,l=!1!==e&&t,p=(0,H.b)(this),f=void 0!==n&&"ios"===p,M="cycle"===d;return(0,h.h)(h.H,Object.assign({key:"e4ad28e6e794560d85252aebdca7f4752e4e7e99","no-router":!0,tabindex:"-1"},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[p]:!0,"modal-default":!f&&!t,"modal-card":f,"modal-sheet":t,"overlay-hidden":!0},(0,re.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),(0,h.h)("ion-backdrop",{key:"6efd67361a062d15488390f9f0d6c0841e541893",ref:w=>this.backdropEl=w,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),"ios"===p&&(0,h.h)("div",{key:"557b1c6b297df75acc80d1350b971e65ace6c343",class:"modal-shadow"}),(0,h.h)("div",Object.assign({key:"67f9b27b662303fbaadaee2ae89972caadfd9994",role:"dialog"},r,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:w=>this.wrapperEl=w}),l&&(0,h.h)("button",{key:"8f1eecc451b52467a8c3cfe500335cf6254bbfbc",class:"modal-handle",tabIndex:M?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:M?this.onHandleClick:void 0,part:"handle"}),(0,h.h)("slot",{key:"cdc923404f01a14b9071a434c68547da3b22c71e"})))}get el(){return(0,h.f)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Te={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};var e;ke.style={ios:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, #c0c0be);cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.modal-card) .modal-wrapper,:host-context([dir=rtl]).modal-card .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.modal-card:dir(rtl)) .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.modal-sheet) .modal-wrapper,:host-context([dir=rtl]).modal-sheet .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.modal-sheet:dir(rtl)) .modal-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0;border-bottom-left-radius:0}}',md:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, #c0c0be);cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}'}},90922:(oe,P,a)=>{a.r(P),a.d(P,{KEYBOARD_DID_CLOSE:()=>s,KEYBOARD_DID_OPEN:()=>K,copyVisualViewport:()=>I,keyboardDidClose:()=>c,keyboardDidOpen:()=>J,keyboardDidResize:()=>Q,resetKeyboardAssist:()=>re,setKeyboardClose:()=>u,setKeyboardOpen:()=>Z,startKeyboardAssist:()=>q,trackViewportChanges:()=>ee});var y=a(93037);a(36319),a(72972);const K="ionKeyboardDidShow",s="ionKeyboardDidHide";let B={},E={},b=!1;const re=()=>{B={},E={},b=!1},q=i=>{if(y.K.getEngine())H(i);else{if(!i.visualViewport)return;E=I(i.visualViewport),i.visualViewport.onresize=()=>{ee(i),J()||Q(i)?Z(i):c(i)&&u(i)}}},H=i=>{i.addEventListener("keyboardDidShow",T=>Z(i,T)),i.addEventListener("keyboardDidHide",()=>u(i))},Z=(i,T)=>{g(i,T),b=!0},u=i=>{N(i),b=!1},J=()=>!b&&B.width===E.width&&(B.height-E.height)*E.scale>150,Q=i=>b&&!c(i),c=i=>b&&E.height===i.innerHeight,g=(i,T)=>{const se=new CustomEvent(K,{detail:{keyboardHeight:T?T.keyboardHeight:i.innerHeight-E.height}});i.dispatchEvent(se)},N=i=>{const T=new CustomEvent(s);i.dispatchEvent(T)},ee=i=>{B=Object.assign({},E),E=I(i.visualViewport)},I=i=>({width:Math.round(i.width),height:Math.round(i.height),offsetTop:i.offsetTop,offsetLeft:i.offsetLeft,pageTop:i.pageTop,pageLeft:i.pageLeft,scale:i.scale})},93037:(oe,P,a)=>{a.d(P,{K:()=>K,a:()=>_});var y=a(36319),h=(()=>{return(s=h||(h={})).Unimplemented="UNIMPLEMENTED",s.Unavailable="UNAVAILABLE",h;var s})(),_=(()=>{return(s=_||(_={})).Body="body",s.Ionic="ionic",s.Native="native",s.None="none",_;var s})();const K={getEngine(){const s=(0,y.g)();if(s?.isPluginAvailable("Keyboard"))return s.Plugins.Keyboard},getResizeMode(){const s=this.getEngine();return s?.getResizeMode?s.getResizeMode().catch(C=>{if(C.code!==h.Unimplemented)throw C}):Promise.resolve(void 0)}}},37389:(oe,P,a)=>{a.d(P,{c:()=>h});var y=a(15861);const h=()=>{let _;return{lock:function(){var s=(0,y.Z)(function*(){const C=_;let B;return _=new Promise(E=>B=E),void 0!==C&&(yield C),B});return function(){return s.apply(this,arguments)}}()}}}}]);