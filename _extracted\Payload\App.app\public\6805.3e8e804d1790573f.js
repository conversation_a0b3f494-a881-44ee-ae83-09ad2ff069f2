(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6805],{66805:(s0,k,C)=>{let I;C.r(k),C.d(k,{NIL:()=>c0,parse:()=>E,stringify:()=>W,v1:()=>$,v3:()=>z,v4:()=>t0,v5:()=>r0,validate:()=>y,version:()=>i0});const H=new Uint8Array(16);function M(){if(!I&&(I=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!I))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return I(H)}const j=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,y=function B(n){return"string"==typeof n&&j.test(n)},d=[];for(let n=0;n<256;++n)d.push((n+256).toString(16).slice(1));function A(n,o=0){return d[n[o+0]]+d[n[o+1]]+d[n[o+2]]+d[n[o+3]]+"-"+d[n[o+4]]+d[n[o+5]]+"-"+d[n[o+6]]+d[n[o+7]]+"-"+d[n[o+8]]+d[n[o+9]]+"-"+d[n[o+10]]+d[n[o+11]]+d[n[o+12]]+d[n[o+13]]+d[n[o+14]]+d[n[o+15]]}const W=function O(n,o=0){const t=A(n,o);if(!y(t))throw TypeError("Stringified UUID is invalid");return t};let S,D,T=0,_=0;const $=function K(n,o,t){let e=o&&t||0;const f=o||new Array(16);let r=(n=n||{}).node||S,c=void 0!==n.clockseq?n.clockseq:D;if(null==r||null==c){const a=n.random||(n.rng||M)();null==r&&(r=S=[1|a[0],a[1],a[2],a[3],a[4],a[5]]),null==c&&(c=D=16383&(a[6]<<8|a[7]))}let i=void 0!==n.msecs?n.msecs:Date.now(),l=void 0!==n.nsecs?n.nsecs:_+1;const s=i-T+(l-_)/1e4;if(s<0&&void 0===n.clockseq&&(c=c+1&16383),(s<0||i>T)&&void 0===n.nsecs&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");T=i,_=l,D=c,i+=122192928e5;const U=(1e4*(268435455&i)+l)%4294967296;f[e++]=U>>>24&255,f[e++]=U>>>16&255,f[e++]=U>>>8&255,f[e++]=255&U;const v=i/4294967296*1e4&268435455;f[e++]=v>>>8&255,f[e++]=255&v,f[e++]=v>>>24&15|16,f[e++]=v>>>16&255,f[e++]=c>>>8|128,f[e++]=255&c;for(let a=0;a<6;++a)f[e+a]=r[a];return o||A(f)},E=function F(n){if(!y(n))throw TypeError("Invalid UUID");let o;const t=new Uint8Array(16);return t[0]=(o=parseInt(n.slice(0,8),16))>>>24,t[1]=o>>>16&255,t[2]=o>>>8&255,t[3]=255&o,t[4]=(o=parseInt(n.slice(9,13),16))>>>8,t[5]=255&o,t[6]=(o=parseInt(n.slice(14,18),16))>>>8,t[7]=255&o,t[8]=(o=parseInt(n.slice(19,23),16))>>>8,t[9]=255&o,t[10]=(o=parseInt(n.slice(24,36),16))/1099511627776&255,t[11]=o/4294967296&255,t[12]=o>>>24&255,t[13]=o>>>16&255,t[14]=o>>>8&255,t[15]=255&o,t},J="6ba7b810-9dad-11d1-80b4-00c04fd430c8",P="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function L(n,o,t){function e(f,r,c,i){var l;if("string"==typeof f&&(f=function G(n){n=unescape(encodeURIComponent(n));const o=[];for(let t=0;t<n.length;++t)o.push(n.charCodeAt(t));return o}(f)),"string"==typeof r&&(r=E(r)),16!==(null===(l=r)||void 0===l?void 0:l.length))throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let s=new Uint8Array(16+f.length);if(s.set(r),s.set(f,r.length),s=t(s),s[6]=15&s[6]|o,s[8]=63&s[8]|128,c){i=i||0;for(let U=0;U<16;++U)c[i+U]=s[U];return c}return A(s)}try{e.name=n}catch{}return e.DNS=J,e.URL=P,e}function N(n){return 14+(n+64>>>9<<4)+1}function w(n,o){const t=(65535&n)+(65535&o);return(n>>16)+(o>>16)+(t>>16)<<16|65535&t}function x(n,o,t,e,f,r){return w(function b(n,o){return n<<o|n>>>32-o}(w(w(o,n),w(e,r)),f),t)}function h(n,o,t,e,f,r,c){return x(o&t|~o&e,n,o,f,r,c)}function g(n,o,t,e,f,r,c){return x(o&e|t&~e,n,o,f,r,c)}function m(n,o,t,e,f,r,c){return x(o^t^e,n,o,f,r,c)}function p(n,o,t,e,f,r,c){return x(t^(o|~e),n,o,f,r,c)}const z=L("v3",48,function Q(n){if("string"==typeof n){const o=unescape(encodeURIComponent(n));n=new Uint8Array(o.length);for(let t=0;t<o.length;++t)n[t]=o.charCodeAt(t)}return function X(n){const o=[],t=32*n.length,e="0123456789abcdef";for(let f=0;f<t;f+=8){const r=n[f>>5]>>>f%32&255,c=parseInt(e.charAt(r>>>4&15)+e.charAt(15&r),16);o.push(c)}return o}(function Y(n,o){n[o>>5]|=128<<o%32,n[N(o)-1]=o;let t=1732584193,e=-271733879,f=-1732584194,r=271733878;for(let c=0;c<n.length;c+=16){const i=t,l=e,s=f,U=r;t=h(t,e,f,r,n[c],7,-680876936),r=h(r,t,e,f,n[c+1],12,-389564586),f=h(f,r,t,e,n[c+2],17,606105819),e=h(e,f,r,t,n[c+3],22,-1044525330),t=h(t,e,f,r,n[c+4],7,-176418897),r=h(r,t,e,f,n[c+5],12,1200080426),f=h(f,r,t,e,n[c+6],17,-1473231341),e=h(e,f,r,t,n[c+7],22,-45705983),t=h(t,e,f,r,n[c+8],7,1770035416),r=h(r,t,e,f,n[c+9],12,-1958414417),f=h(f,r,t,e,n[c+10],17,-42063),e=h(e,f,r,t,n[c+11],22,-1990404162),t=h(t,e,f,r,n[c+12],7,1804603682),r=h(r,t,e,f,n[c+13],12,-40341101),f=h(f,r,t,e,n[c+14],17,-1502002290),e=h(e,f,r,t,n[c+15],22,1236535329),t=g(t,e,f,r,n[c+1],5,-165796510),r=g(r,t,e,f,n[c+6],9,-1069501632),f=g(f,r,t,e,n[c+11],14,643717713),e=g(e,f,r,t,n[c],20,-373897302),t=g(t,e,f,r,n[c+5],5,-701558691),r=g(r,t,e,f,n[c+10],9,38016083),f=g(f,r,t,e,n[c+15],14,-660478335),e=g(e,f,r,t,n[c+4],20,-405537848),t=g(t,e,f,r,n[c+9],5,568446438),r=g(r,t,e,f,n[c+14],9,-1019803690),f=g(f,r,t,e,n[c+3],14,-187363961),e=g(e,f,r,t,n[c+8],20,1163531501),t=g(t,e,f,r,n[c+13],5,-1444681467),r=g(r,t,e,f,n[c+2],9,-51403784),f=g(f,r,t,e,n[c+7],14,1735328473),e=g(e,f,r,t,n[c+12],20,-1926607734),t=m(t,e,f,r,n[c+5],4,-378558),r=m(r,t,e,f,n[c+8],11,-2022574463),f=m(f,r,t,e,n[c+11],16,1839030562),e=m(e,f,r,t,n[c+14],23,-35309556),t=m(t,e,f,r,n[c+1],4,-1530992060),r=m(r,t,e,f,n[c+4],11,1272893353),f=m(f,r,t,e,n[c+7],16,-155497632),e=m(e,f,r,t,n[c+10],23,-1094730640),t=m(t,e,f,r,n[c+13],4,681279174),r=m(r,t,e,f,n[c],11,-358537222),f=m(f,r,t,e,n[c+3],16,-722521979),e=m(e,f,r,t,n[c+6],23,76029189),t=m(t,e,f,r,n[c+9],4,-640364487),r=m(r,t,e,f,n[c+12],11,-421815835),f=m(f,r,t,e,n[c+15],16,530742520),e=m(e,f,r,t,n[c+2],23,-995338651),t=p(t,e,f,r,n[c],6,-198630844),r=p(r,t,e,f,n[c+7],10,1126891415),f=p(f,r,t,e,n[c+14],15,-1416354905),e=p(e,f,r,t,n[c+5],21,-57434055),t=p(t,e,f,r,n[c+12],6,1700485571),r=p(r,t,e,f,n[c+3],10,-1894986606),f=p(f,r,t,e,n[c+10],15,-1051523),e=p(e,f,r,t,n[c+1],21,-2054922799),t=p(t,e,f,r,n[c+8],6,1873313359),r=p(r,t,e,f,n[c+15],10,-30611744),f=p(f,r,t,e,n[c+6],15,-1560198380),e=p(e,f,r,t,n[c+13],21,1309151649),t=p(t,e,f,r,n[c+4],6,-145523070),r=p(r,t,e,f,n[c+11],10,-1120210379),f=p(f,r,t,e,n[c+2],15,718787259),e=p(e,f,r,t,n[c+9],21,-343485551),t=w(t,i),e=w(e,l),f=w(f,s),r=w(r,U)}return[t,e,f,r]}(function Z(n){if(0===n.length)return[];const o=8*n.length,t=new Uint32Array(N(o));for(let e=0;e<o;e+=8)t[e>>5]|=(255&n[e/8])<<e%32;return t}(n),8*n.length))}),q={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},t0=function n0(n,o,t){if(q.randomUUID&&!o&&!n)return q.randomUUID();const e=(n=n||{}).random||(n.rng||M)();if(e[6]=15&e[6]|64,e[8]=63&e[8]|128,o){t=t||0;for(let f=0;f<16;++f)o[t+f]=e[f];return o}return A(e)};function e0(n,o,t,e){switch(n){case 0:return o&t^~o&e;case 1:case 3:return o^t^e;case 2:return o&t^o&e^t&e}}function R(n,o){return n<<o|n>>>32-o}const r0=L("v5",80,function f0(n){const o=[1518500249,1859775393,2400959708,3395469782],t=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof n){const c=unescape(encodeURIComponent(n));n=[];for(let i=0;i<c.length;++i)n.push(c.charCodeAt(i))}else Array.isArray(n)||(n=Array.prototype.slice.call(n));n.push(128);const f=Math.ceil((n.length/4+2)/16),r=new Array(f);for(let c=0;c<f;++c){const i=new Uint32Array(16);for(let l=0;l<16;++l)i[l]=n[64*c+4*l]<<24|n[64*c+4*l+1]<<16|n[64*c+4*l+2]<<8|n[64*c+4*l+3];r[c]=i}r[f-1][14]=8*(n.length-1)/Math.pow(2,32),r[f-1][14]=Math.floor(r[f-1][14]),r[f-1][15]=8*(n.length-1)&4294967295;for(let c=0;c<f;++c){const i=new Uint32Array(80);for(let u=0;u<16;++u)i[u]=r[c][u];for(let u=16;u<80;++u)i[u]=R(i[u-3]^i[u-8]^i[u-14]^i[u-16],1);let l=t[0],s=t[1],U=t[2],v=t[3],a=t[4];for(let u=0;u<80;++u){const V=Math.floor(u/20),l0=R(l,5)+e0(V,s,U,v)+a+o[V]+i[u]>>>0;a=v,v=U,U=R(s,30)>>>0,s=l,l=l0}t[0]=t[0]+l>>>0,t[1]=t[1]+s>>>0,t[2]=t[2]+U>>>0,t[3]=t[3]+v>>>0,t[4]=t[4]+a>>>0}return[t[0]>>24&255,t[0]>>16&255,t[0]>>8&255,255&t[0],t[1]>>24&255,t[1]>>16&255,t[1]>>8&255,255&t[1],t[2]>>24&255,t[2]>>16&255,t[2]>>8&255,255&t[2],t[3]>>24&255,t[3]>>16&255,t[3]>>8&255,255&t[3],t[4]>>24&255,t[4]>>16&255,t[4]>>8&255,255&t[4]]}),c0="00000000-0000-0000-0000-000000000000",i0=function o0(n){if(!y(n))throw TypeError("Invalid UUID");return parseInt(n.slice(14,15),16)}}}]);