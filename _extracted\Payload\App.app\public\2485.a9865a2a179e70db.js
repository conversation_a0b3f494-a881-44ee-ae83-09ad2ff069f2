(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2485],{42485:(y,c,o)=>{o.r(c),o.d(c,{MboCelToCelSendConfirmationPageModule:()=>M});var d=o(17007),m=o(78007),a=o(30263),f=o(79798),u=o(39904),b=o(95437),p=o(17758),v=o(98487),e=o(99877),g=o(48774),C=o(17941),h=o(66613),S=o(45542),A=o(10464);const l=u.Z6.TRANSFERS.CELTOCEL.SEND;let T=(()=>{class t{constructor(i,n,s){this.mboProvider=i,this.requestConfiguration=n,this.cancelProvider=s,this.backAction={id:"btn_cel-to-cel-send-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(l.AMOUNT)}},this.cancelAction={id:"btn_cel-to-cel-send-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_cel-to-cel-send-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(l.DESTINATION)}}],this.amountActions=[{id:"btn_cel-to-cel-send-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(l.AMOUNT)}}]}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.mboProvider.navigation.next(l.RESULT)}initializatedConfiguration(){this.requestConfiguration.confirmation().when({success:({transfer:i})=>{this.transfer=i}})}}return t.\u0275fac=function(i){return new(i||t)(e.\u0275\u0275directiveInject(b.ZL),e.\u0275\u0275directiveInject(p.ZW),e.\u0275\u0275directiveInject(v.T))},t.\u0275cmp=e.\u0275\u0275defineComponent({type:t,selectors:[["mbo-cel-to-cel-send-confirmation-page"]],decls:18,vars:11,consts:[[1,"mbo-cel-to-cel-send-confirmation-page__content","mbo-page__scroller"],[1,"mbo-cel-to-cel-send-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-cel-to-cel-send-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],["header","DESTINO",3,"title","subtitle","detail","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],["icon","bell",3,"visible"],[1,"mbo-cel-to-cel-send-confirmation-page__footer"],["id","btn_cel-to-cel-send-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"click"]],template:function(i,n){1&i&&(e.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),e.\u0275\u0275element(3,"bocc-header-form",2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),e.\u0275\u0275text(7," \xbfDeseas transferirle a? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",6),e.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),e.\u0275\u0275elementStart(12,"bocc-alert",10),e.\u0275\u0275text(13," A tu contacto le llegar\xe1 la transferencia directa al producto de la entidad seleccionada. "),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(14,"div",11)(15,"button",12),e.\u0275\u0275listener("click",function(){return n.onSubmit()}),e.\u0275\u0275elementStart(16,"span"),e.\u0275\u0275text(17,"Transferir"),e.\u0275\u0275elementEnd()()()()),2&i&&(e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",n.backAction)("rightAction",n.cancelAction),e.\u0275\u0275advance(6),e.\u0275\u0275property("title",null==n.transfer||null==n.transfer.account?null:n.transfer.account.customer.maskName)("subtitle",null==n.transfer||null==n.transfer.contact?null:n.transfer.contact.phone)("detail",null==n.transfer||null==n.transfer.account?null:n.transfer.account.bank.name)("actions",n.destinationActions),e.\u0275\u0275advance(1),e.\u0275\u0275property("amount",null==n.transfer?null:n.transfer.amount)("actions",n.amountActions),e.\u0275\u0275advance(1),e.\u0275\u0275property("title",null==n.transfer||null==n.transfer.source?null:n.transfer.source.nickname)("subtitle",null==n.transfer||null==n.transfer.source?null:n.transfer.source.number),e.\u0275\u0275advance(1),e.\u0275\u0275property("visible",!0))},dependencies:[g.J,C.D,h.B,S.P,A.K],styles:["/*!\n * MBO CelToCelSendConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 08/Nov/2022\n * Updated: 06/Feb/2024\n*/mbo-cel-to-cel-send-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-cel-to-cel-send-confirmation-page .mbo-cel-to-cel-send-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-cel-to-cel-send-confirmation-page .mbo-cel-to-cel-send-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-cel-to-cel-send-confirmation-page .mbo-cel-to-cel-send-confirmation-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-cel-to-cel-send-confirmation-page .mbo-cel-to-cel-send-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-cel-to-cel-send-confirmation-page .mbo-cel-to-cel-send-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),t})(),M=(()=>{class t{}return t.\u0275fac=function(i){return new(i||t)},t.\u0275mod=e.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=e.\u0275\u0275defineInjector({imports:[d.CommonModule,m.RouterModule.forChild([{path:"",component:T}]),a.Jx,a.DM,a.B4,a.P8,f.KI]}),t})()}}]);