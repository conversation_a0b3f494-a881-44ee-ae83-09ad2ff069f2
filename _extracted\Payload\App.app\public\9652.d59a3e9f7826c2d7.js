(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9652],{19652:(v,c,e)=>{e.r(c),e.d(c,{MboApplicationAuthenticationPageModule:()=>h});var s=e(78007),r=e(30263),d=e(95437),p=e(87956),o=e(82470),n=e(99877);let u=(()=>{class t{constructor(i,l,m){this.modalConfirmationService=i,this.mboProvider=l,this.deviceService=m}ngOnInit(){const{itIsIos:i,itIsMobile:l}=this.deviceService.controller;l&&i&&(o.StatusBar.setStyle({style:o.Style.Light}),this.modalConfirmationService.setOpening(()=>{o.StatusBar.setStyle({style:o.Style.Dark})}),this.modalConfirmationService.setClosing(()=>{o.StatusBar.setStyle({style:o.Style.Light})})),this.mboProvider.navigation.setAnimationElement(document.querySelector(".mbo-application__page"))}}return t.\u0275fac=function(i){return new(i||t)(n.\u0275\u0275directiveInject(r.$e),n.\u0275\u0275directiveInject(d.ZL),n.\u0275\u0275directiveInject(p.U8))},t.\u0275cmp=n.\u0275\u0275defineComponent({type:t,selectors:[["mbo-application-authentication"]],decls:2,vars:0,consts:[[1,"mbo-application__page__body"]],template:function(i,l){1&i&&(n.\u0275\u0275elementStart(0,"div",0),n.\u0275\u0275element(1,"router-outlet"),n.\u0275\u0275elementEnd())},dependencies:[s.RouterOutlet],encapsulation:2}),t})(),h=(()=>{class t{}return t.\u0275fac=function(i){return new(i||t)},t.\u0275mod=n.\u0275\u0275defineNgModule({type:t}),t.\u0275inj=n.\u0275\u0275defineInjector({imports:[s.RouterModule.forChild([{path:"",component:u,children:[{path:"",loadChildren:()=>e.e(4039).then(e.bind(e,44039)).then(a=>a.MboAuthenticationModule)}]}])]}),t})()}}]);