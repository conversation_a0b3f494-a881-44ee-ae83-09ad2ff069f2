(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2354],{53644:(b,_,n)=>{n.d(_,{MK:()=>p,NM:()=>g,Rm:()=>d,VS:()=>i,ay:()=>v});var m=n(90806);class v{constructor(l){this.tagAval=l}}class g{constructor(l,e){this.subtitle=l,this.title=e}}class i{constructor(l,e,o){this.fullName=l,this.documentType=e,this.documentNumber=o,this.maskName=(0,m.Z)(l)}}class d{constructor(l,e,o,t,c,f){this.keyType=l,this.tagAval=e,this.accountReceptor=o,this.type=t,this.bank=c,this.customer=f}get customerMaskName(){return this.customer.maskName}}class p{constructor(l,e,o,t,c,f,E){this.source=l,this.account=e,this.contact=o,this.customerName=t,this.ipAddress=c,this.amount=f,this.note=E}}},30786:(b,_,n)=>{n.d(_,{$:()=>p,Ry:()=>l,iK:()=>u});var m=n(29306),v=n(64892),g=n(87903),i=n(53644);const d={CC:"CC",CE:"CE",OTR:"CC",PA:"PA",PEP:"CC",TDI:"TI",TI:"TI"};function p(e){const{PartyAcctRelInfo:{PersonInfo:{CustIdentNum:o,CustIdentType:t,GovIssueIdent:c,PersonName:{FirstName:f}},DepAcctId:{AcctId:E,AcctType:I,BankInfo:h}},RefInfo:T}=e,s=c?.GovIssueIdentType||t,r=c?.IdentSerialNum||o,{RefId:a,RefType:A}=T[0];return new i.Rm(A,a,E,I,new m.Br(h.BankId,h.Name,h.BankId===v.qE.Occidente),new i.VS(f,(0,g.nX)(d[s]),r))}function u(e){return{fromDepAcctId:e.source.id,fromDepAcctName:e.source.name,fromDepAcctType:e.source.type,fromNickName:e.source.nickname,toDepAcctBankId:e.account.bank.id,toDepAcctType:e.account.type,toDepAcctId:e.account.accountReceptor,toDepAcctName:e.account.customer.fullName,toNickName:"",toUserIdNumber:e.account.customer.documentNumber,toUserIdType:e.account.customer.documentType.code,keyInfo:{key:e.account.tagAval,type:e.account.keyType},personInfoTo:{fullName:e.account.customer.fullName,govIssueIdentTo:{govIssueIdentType:e.account.customer.documentType.code,identSerialNum:e.account.customer.documentNumber}},personInfoFrom:{firstName:e.customerName.clientFirstName,lastName:e.customerName.clientLastName,legalName:`${e.customerName.clientFirstName} ${e.customerName.clientLastName}`},curAmt:e.amount.toString(),refId:e.note?.reference||"",memo:e.note?.description||"",category:"3",typeTransfer:"TAG_AVAL_TRANS"}}function l(e){return new i.MK(e.source,e.account,new i.NM(e.destination.tagAval,e.account.customer.fullName),e.customerName,e.ipAddress,e.amount,e.note)}},90806:(b,_,n)=>{n.d(_,{D:()=>d,Z:()=>p});var m=n(87903),v=n(53113);function g(u){const{isError:l,message:e}=u;return{animation:(0,m.jY)(u),title:l?"\xa1Transferencia fallida!":"\xa1Transferencia exitosa!",subtitle:e}}function i({isError:u}){return u?[(0,m.wT)("Finalizar","finish","outline"),(0,m.wT)("Volver a intentar","retry")]:[(0,m.wT)("Hacer otra transferencia","retry","outline"),(0,m.wT)("Finalizar","finish")]}function d(u){const{dateFormat:l,timeFormat:e}=new v.ou,{status:o,tagAval:t}=u,c=[(0,m.SP)("DESTINO",t.account.customer.maskName,t.account.tagAval,t.account.bank.name),(0,m._f)("SUMA DE",t.amount)];return t.note&&c.push((0,m.SP)("DESCRIPCI\xd3N",t.note.description,"",t.note.reference)),c.push((0,m.cZ)(l,e)),{actions:i(o),error:o.isError,header:g(o),informations:c,skeleton:!1}}function p(u){const l=u.split(" "),[e]=l,o=l[l.length-1],t=o.length,f=t>3?3:2;return`${e.substring(0,e.length>4?4:2)}*****${o.substring(t-f,t)}`}},90596:(b,_,n)=>{n.d(_,{$:()=>m.$,N:()=>l});var m=n(50142),v=n(15861),g=n(87956),i=n(98699),d=n(30786),p=n(23604),u=n(99877);let l=(()=>{class e{constructor(t,c){this.store=t,this.products=c}source(){var t=this;return(0,v.Z)(function*(){try{const c=yield t.products.requestAccountsForTransfer(),f=t.store.itIsConfirmation();return i.Either.success({confirmation:f,products:c})}catch({message:c}){return i.Either.failure({message:c})}})()}destination(){var t=this;return(0,v.Z)(function*(){try{const c=yield t.products.requestAccountsForTransfer(),f=t.store.itIsConfirmation();return i.Either.success({confirmation:f,hasOneSource:c.length<2,destination:t.store.getTagAval()})}catch({message:c}){return i.Either.failure({message:c})}})()}amount(){try{const t=this.store.itIsConfirmation(),c=this.store.getSource(),f=this.store.getAmount(),E=this.store.getAccount();return i.Either.success({account:E,amount:f,confirmation:t,source:c})}catch({message:t}){return i.Either.failure({message:t})}}confirmation(){try{const t=(0,d.Ry)(this.store.currentState);return i.Either.success({transfer:t})}catch({message:t}){return i.Either.failure({message:t})}}}return e.\u0275fac=function(t){return new(t||e)(u.\u0275\u0275inject(p.B),u.\u0275\u0275inject(g.hM))},e.\u0275prov=u.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},50142:(b,_,n)=>{n.d(_,{$:()=>I});var m=n(15861),v=n(87956),g=n(53113),i=n(98699),d=n(30786),p=n(71776),u=n(39904),l=n(87903),e=n(42168),o=n(84757),t=n(99877);let c=(()=>{class h{constructor(s){this.http=s}requestVerifyAccount(s){var r=this;return(0,m.Z)(function*(){return(0,e.firstValueFrom)(r.http.post(u.bV.PRODUCTS.TAG_AVAL_BY_CODE,{RefInfo:{RefType:"4",RefId:s},pilot:!0}).pipe((0,o.map)(a=>a.map(A=>(0,d.$)(A))),(0,o.catchError)(a=>{if("206"===a.error?.MsgRsHdr?.Status?.StatusCode)return(0,e.of)([]);throw a})))})()}send(s){return(0,e.firstValueFrom)(this.http.post(u.bV.TRANSFERS.TAG_AVAL,(0,d.iK)(s),{headers:{"X-Customer-Ip":s.ipAddress}}).pipe((0,o.map)(r=>(0,l.l1)(r,"SUCCESS")))).catch(r=>(0,l.rU)(r))}}return h.\u0275fac=function(s){return new(s||h)(t.\u0275\u0275inject(p.HttpClient))},h.\u0275prov=t.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h})();var f=n(23604),E=n(74520);let I=(()=>{class h{constructor(s,r,a,A){this.repository=s,this.store=r,this.eventBusService=a,this.customerStore=A}setSource(s,r=!1){try{return i.Either.success(this.store.setSource(s,r))}catch({message:a}){return i.Either.failure({message:a})}}verfiyAccount(s){var r=this;return(0,m.Z)(function*(){try{const a=yield r.requestAccount(s);return r.store.setTagAval(s),i.Either.success(!!a)}catch({message:a}){return i.Either.failure({message:a})}})()}setAmount(s){try{return i.Either.success(this.store.setAmount(s))}catch({message:r}){return i.Either.failure({message:r})}}reset(){try{const s=this.store.itIsFromCustomer(),r=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:s,source:r})}catch({message:s}){return i.Either.failure({message:s})}}send(){var s=this;return(0,m.Z)(function*(){const r=s.customerStore.currentState,{session:{ip:a,customer:{clientFirstName:A,clientLastName:C}}}=r;s.store.setIpAddress(a),s.store.setCustomerName({clientFirstName:A,clientLastName:C});const P=(0,d.Ry)(s.store.currentState),y=yield s.execute(P);return s.eventBusService.emit(y.channel),i.Either.success({tagAval:P,status:y})})()}execute(s){try{return this.repository.send(s)}catch({message:r}){return Promise.resolve(g.LN.error(r))}}requestAccount(s){var r=this;return(0,m.Z)(function*(){const a=r.store.getTagAval();let A=r.store.getAccount();const{tagAval:C}=s;return(a?.tagAval!==C||!A)&&([A]=yield r.repository.requestVerifyAccount(C),r.store.setAccount(A)),A})()}setNote(s){try{return i.Either.success(this.store.setNote(s))}catch({message:r}){return i.Either.failure({message:r})}}removeNote(){try{return i.Either.success(this.store.removeNote())}catch({message:s}){return i.Either.failure({message:s})}}}return h.\u0275fac=function(s){return new(s||h)(t.\u0275\u0275inject(c),t.\u0275\u0275inject(f.B),t.\u0275\u0275inject(v.Yd),t.\u0275\u0275inject(E.f))},h.\u0275prov=t.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h})()},32435:(b,_,n)=>{n.d(_,{Z:()=>l});var m=n(30263),v=n(39904),g=n(95437),i=n(90596),d=n(99877);const u=v.Z6.TRANSFERS.GENERIC;let l=(()=>{class e{constructor(t,c,f){this.modalConfirmation=t,this.mboProvider=c,this.managerTagAval=f}execute(t=!0){t?this.confirmation():this.cancelConfirmed(!0)}backCustomer(t=!0){t?this.mboProvider.navigation.back(v.Z6.TRANSFERS.TAG_AVAL.SOURCE):this.backConfirmed(!0)}confirmation(t=!0){return this.modalConfirmation.execute({title:"Abandonar transferencia",message:"\xbfEstas seguro que deseas cancelar la transferencia entre Tags Aval actualmente en progreso?",accept:{label:"Abandonar",click:()=>{this.cancelConfirmed(t)}},decline:{label:"Continuar"}}).then(c=>"accept"===c)}cancelConfirmed(t){const c=this.managerTagAval.reset();t&&c.when({success:({fromCustomer:f,source:E})=>{f?this.mboProvider.navigation.back(v.Z6.CUSTOMER.PRODUCTS.INFO,{productId:E.id}):this.mboProvider.navigation.back(v.Z6.TRANSFERS.HOME)},failure:()=>{this.mboProvider.navigation.back(v.Z6.TRANSFERS.HOME)}})}backConfirmed(t){const c=this.managerTagAval.reset();t&&c.when({success:({fromCustomer:f,source:E})=>{f?this.mboProvider.navigation.back(u.DESTINATION,{productId:E.id}):this.mboProvider.navigation.back(v.Z6.TRANSFERS.TAG_AVAL.SOURCE)},failure:()=>{this.mboProvider.navigation.back(v.Z6.TRANSFERS.HOME)}})}}return e.\u0275fac=function(t){return new(t||e)(d.\u0275\u0275inject(m.$e),d.\u0275\u0275inject(g.ZL),d.\u0275\u0275inject(i.$))},e.\u0275prov=d.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},23604:(b,_,n)=>{n.d(_,{B:()=>u});var m=n(39904),v=n(87956),g=n(20691),d=n(99877);let u=(()=>{class l extends g.Store{constructor(o){super({confirmation:!1,fromCustomer:!1}),o.subscribes(m.PU,()=>{this.reset()})}setIpAddress(o){this.reduce(t=>({...t,ipAddress:o}))}setCustomerName(o){this.reduce(t=>({...t,customerName:o}))}itIsFromCustomer(){return this.select(({fromCustomer:o})=>o)}setSource(o,t=!1){this.reduce(c=>({...c,source:o,fromCustomer:t}))}getSource(){return this.select(({source:o})=>o)}setTagAval(o){this.reduce(t=>({...t,destination:o}))}getTagAval(){return this.select(({destination:o})=>o)}setAccount(o){this.reduce(t=>({...t,account:o}))}getAccount(){return this.select(({account:o})=>o)}setAmount(o){this.reduce(t=>({...t,amount:o,confirmation:!0}))}getAmount(){return this.select(({amount:o})=>o)}itIsConfirmation(){return this.select(({confirmation:o})=>o)}setNote(o){this.reduce(t=>({...t,note:o}))}removeNote(){this.reduce(o=>({...o,note:void 0}))}}return l.\u0275fac=function(o){return new(o||l)(d.\u0275\u0275inject(v.Yd))},l.\u0275prov=d.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})()},82354:(b,_,n)=>{n.r(_),n.d(_,{MboTransferTagAvalConfirmationPage:()=>h});var m=n(17007),g=n(30263),i=n(79798),d=n(39904),p=n(95437),u=n(98699),e=n(90596),o=n(32435),t=n(99877);function f(T,s){if(1&T&&t.\u0275\u0275element(0,"bocc-card-summary",15),2&T){const r=t.\u0275\u0275nextContext();t.\u0275\u0275property("title",null==r.note?null:r.note.description)("detail",null==r.note?null:r.note.reference)("actions",r.noteActions)}}function E(T,s){if(1&T){const r=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",16)(1,"button",17),t.\u0275\u0275listener("click",function(){t.\u0275\u0275restoreView(r);const A=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(A.onNote())}),t.\u0275\u0275elementStart(2,"span"),t.\u0275\u0275text(3,"Agregar descripci\xf3n"),t.\u0275\u0275elementEnd()()()}}const I=d.Z6.TRANSFERS.TAG_AVAL;let h=(()=>{class T{constructor(r,a,A,C,P,y){this.confirmationService=r,this.bottomSheetService=a,this.mboProvider=A,this.requestConfiguration=C,this.cancelProvider=P,this.managerTransfer=y,this.backAction={id:"btn_transfer-tag-aval-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(I.AMOUNT)}},this.cancelAction={id:"btn_transfer-tag-aval-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_tag-aval-send-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(I.DESTINATION)}}],this.amountActions=[{id:"btn_tag-aval-send-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(I.AMOUNT)}}],this.noteActions=[{id:"btn_transfer-generic-confirmation_remove-description",icon:"remove",click:()=>{this.removeNote()}},{id:"btn_transfer-generic-confirmation_edit-description",icon:"edit-pencil",click:()=>{this.openNote()}}]}ngOnInit(){this.initializatedConfiguration()}onNote(){this.openNote()}onSubmit(){this.mboProvider.navigation.next(I.RESULT)}initializatedConfiguration(){this.requestConfiguration.confirmation().when({success:({transfer:r})=>{this.transfer=r,this.note=r.note}})}openNote(){const r=this.bottomSheetService.create(i.bL,{componentProps:{title:"A\xf1ade una descripci\xf3n a tu transferencia",initialValue:this.note}});r.open(),(0,u.catchPromise)(r.waiting().then(a=>{this.managerTransfer.setNote(a).when({success:()=>{this.note=a}})}))}removeNote(){this.confirmationService.execute({title:"\xbfBorrar descripci\xf3n?",message:"\xbfEsta seguro de borrar la descripci\xf3n agregada en la transferencia?",accept:{label:"Borrar descripci\xf3n",click:()=>{this.managerTransfer.removeNote().when({success:()=>{this.note=void 0}})}},decline:{label:"Cancelar"}})}}return T.\u0275fac=function(r){return new(r||T)(t.\u0275\u0275directiveInject(g.$e),t.\u0275\u0275directiveInject(g.fG),t.\u0275\u0275directiveInject(p.ZL),t.\u0275\u0275directiveInject(e.N),t.\u0275\u0275directiveInject(o.Z),t.\u0275\u0275directiveInject(e.$))},T.\u0275cmp=t.\u0275\u0275defineComponent({type:T,selectors:[["mbo-transfer-tag-aval-confirmation-page"]],standalone:!0,features:[t.\u0275\u0275StandaloneFeature],decls:20,vars:14,consts:[[1,"mbo-transfer-tag-aval-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfer-tag-aval-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfer-tag-aval-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],["header","DESTINO",3,"title","detail","tagAval","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","ORIGEN",3,"title","bankOrigin","subtitle"],["header","DESCRIPCI\xd3N",3,"title","detail","actions",4,"ngIf"],["icon","bell",3,"visible"],["class","bocc-card__footer",4,"ngIf"],[1,"mbo-transfer-tag-aval-confirmation-page__footer"],["id","btn_transfer-tag-aval-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"click"],["header","DESCRIPCI\xd3N",3,"title","detail","actions"],[1,"bocc-card__footer"],["id","btn_transfer-generic-confirmation_add-note","bocc-button","note","prefixIcon","edit-note",3,"click"]],template:function(r,a){1&r&&(t.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),t.\u0275\u0275element(3,"bocc-header-form",2),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),t.\u0275\u0275text(7," \xbfDeseas transferirle a? "),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(8,"div",6),t.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),t.\u0275\u0275template(12,f,1,3,"bocc-card-summary",10),t.\u0275\u0275elementStart(13,"bocc-alert",11),t.\u0275\u0275text(14," Costo de la transacci\xf3n $ 0 pesos. "),t.\u0275\u0275elementEnd()(),t.\u0275\u0275template(15,E,4,0,"div",12),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275elementStart(16,"div",13)(17,"button",14),t.\u0275\u0275listener("click",function(){return a.onSubmit()}),t.\u0275\u0275elementStart(18,"span"),t.\u0275\u0275text(19,"Transferir"),t.\u0275\u0275elementEnd()()()()),2&r&&(t.\u0275\u0275advance(3),t.\u0275\u0275property("leftAction",a.backAction)("rightAction",a.cancelAction),t.\u0275\u0275advance(6),t.\u0275\u0275property("title",null==a.transfer?null:a.transfer.account.customer.maskName)("detail",null==a.transfer?null:a.transfer.account.bank.name)("tagAval",null==a.transfer?null:a.transfer.account.tagAval)("actions",a.destinationActions),t.\u0275\u0275advance(1),t.\u0275\u0275property("amount",null==a.transfer?null:a.transfer.amount)("actions",a.amountActions),t.\u0275\u0275advance(1),t.\u0275\u0275property("title",null==a.transfer||null==a.transfer.source?null:a.transfer.source.nickname)("bankOrigin",null==a.transfer||null==a.transfer.source||null==a.transfer.source.bank?null:a.transfer.source.bank.name)("subtitle",null==a.transfer||null==a.transfer.source?null:a.transfer.source.number),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",a.note),t.\u0275\u0275advance(1),t.\u0275\u0275property("visible",!0),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf",!a.note))},dependencies:[m.CommonModule,m.NgIf,g.Jx,g.DM,g.B4,g.P8,i.KI],styles:["/*!\n * MBO TransferTagAvalConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 13/Aug/2024\n * Updated: 13/Aug/2024\n*/mbo-transfer-tag-aval-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfer-tag-aval-confirmation-page .mbo-transfer-tag-aval-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfer-tag-aval-confirmation-page .mbo-transfer-tag-aval-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfer-tag-aval-confirmation-page .mbo-transfer-tag-aval-confirmation-page__body bocc-alert{padding:0rem var(--sizing-x4);box-sizing:border-box}mbo-transfer-tag-aval-confirmation-page .mbo-transfer-tag-aval-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfer-tag-aval-confirmation-page .mbo-transfer-tag-aval-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),T})()}}]);