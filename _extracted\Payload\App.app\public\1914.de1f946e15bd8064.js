(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1914],{1914:(g,d,o)=>{o.r(d),o.d(d,{MboForgotPasswordModule:()=>s});var l=o(17007),a=o(78007),t=o(99877);const M=[{path:"",redirectTo:"welcome",pathMatch:"full"},{path:"welcome",loadChildren:()=>o.e(8865).then(o.bind(o,8865)).then(n=>n.MboForgotPasswordWelcomePageModule)},{path:"otp-verification",loadChildren:()=>o.e(7777).then(o.bind(o,17777)).then(n=>n.MboForgotPasswordOTPPageModule)},{path:"password-assignment",loadChildren:()=>o.e(3139).then(o.bind(o,13139)).then(n=>n.MboForgotPasswordAssignmentPageModule)},{path:"product-verification",loadChildren:()=>o.e(6230).then(o.bind(o,66230)).then(n=>n.MboForgotPasswordProductPageModule)}];let s=(()=>{class n{}return n.\u0275fac=function(h){return new(h||n)},n.\u0275mod=t.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=t.\u0275\u0275defineInjector({imports:[l.CommonModule,a.RouterModule.forChild(M)]}),n})()}}]);