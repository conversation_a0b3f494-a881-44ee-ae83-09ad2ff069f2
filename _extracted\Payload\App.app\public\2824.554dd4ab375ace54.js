(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2824],{42824:(e,l,o)=>{o.r(l),o.d(l,{MboMicrofrontendModule:()=>u});var t=o(17007),M=o(78007),d=o(99877);const r=[{path:"bre-b",loadChildren:()=>o.e(7648).then(o.bind(o,37648)).then(n=>n.MboMicrofrontendBrebPageModule)}];let u=(()=>{class n{}return n.\u0275fac=function(E){return new(E||n)},n.\u0275mod=d.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=d.\u0275\u0275defineInjector({imports:[t.CommonModule,M.RouterModule.forChild(r),M.RouterModule]}),n})()}}]);