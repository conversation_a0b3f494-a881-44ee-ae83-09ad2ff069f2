(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7907],{17907:(h,s,n)=>{n.r(s),n.d(s,{ShareWeb:()=>o});var t=n(15861),i=n(17737);class o extends i.WebPlugin{canShare(){return(0,t.Z)(function*(){return typeof navigator>"u"||!navigator.share?{value:!1}:{value:!0}})()}share(e){var a=this;return(0,t.Z)(function*(){if(typeof navigator>"u"||!navigator.share)throw a.unavailable("Share API not available in this browser");return yield navigator.share({title:e.title,text:e.text,url:e.url}),{}})()}}},15861:(h,s,n)=>{function t(l,o,c,e,a,f,_){try{var u=l[f](_),r=u.value}catch(d){return void c(d)}u.done?o(r):Promise.resolve(r).then(e,a)}function i(l){return function(){var o=this,c=arguments;return new Promise(function(e,a){var f=l.apply(o,c);function _(r){t(f,e,a,_,u,"next",r)}function u(r){t(f,e,a,_,u,"throw",r)}_(void 0)})}}n.d(s,{Z:()=>i})}}]);