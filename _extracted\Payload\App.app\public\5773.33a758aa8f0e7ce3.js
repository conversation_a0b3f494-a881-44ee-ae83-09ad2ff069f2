(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5773],{65773:(F,f,e)=>{e.r(f),e.d(f,{MboTransfiyaTransferConfirmationPageModule:()=>D});var l=e(17007),m=e(78007),p=e(79798),r=e(30263),u=e(15861),b=e(39904),y=e(95437),v=e(98699),d=e(17698),g=e(73004),h=e(65715),n=e(99877),T=e(10464),C=e(48774),A=e(17941),P=e(45542);function I(a,o){if(1&a&&n.\u0275\u0275element(0,"bocc-card-summary",14),2&a){const i=n.\u0275\u0275nextContext();n.\u0275\u0275property("actions",i.descriptionActions)("detail",i.description)}}function S(a,o){if(1&a){const i=n.\u0275\u0275getCurrentView();n.\u0275\u0275elementStart(0,"div",15)(1,"button",16),n.\u0275\u0275listener("click",function(){n.\u0275\u0275restoreView(i);const c=n.\u0275\u0275nextContext();return n.\u0275\u0275resetView(c.onDescription())}),n.\u0275\u0275elementStart(2,"span"),n.\u0275\u0275text(3,"Agregar descripci\xf3n"),n.\u0275\u0275elementEnd()()()}}const s=b.Z6.TRANSFERS.TRANSFIYA.TRANSFER;let M=(()=>{class a{constructor(i,t,c,E,N,j){this.bottomSheetService=i,this.modalConfirmationService=t,this.mboProvider=c,this.requestConfiguration=E,this.managerTransfiya=N,this.cancelProvider=j,this.description="",this.backAction={id:"btn_transfiya-transfer-confirmation_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(s.AMOUNT)}},this.cancelAction={id:"btn_transfiya-transfer-confirmation_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute()}},this.destinationActions=[{id:"btn_transfiya-transfer-confirmation_edit-destination",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(s.DESTINATION)}}],this.amountActions=[{id:"btn_transfiya-transfer-confirmation_edit-amount",icon:"edit-pencil",click:()=>{this.mboProvider.navigation.back(s.AMOUNT)}}],this.descriptionActions=[{id:"btn_transfiya-transfer-confirmation_remove-description",icon:"remove",click:()=>{this.removeDescription()}},{id:"btn_transfiya-transfer-confirmation_edit-description",icon:"edit-pencil",click:()=>{this.openDescription()}}]}ngOnInit(){this.initializatedConfiguration()}onDescription(){this.openDescription()}onSubmit(){this.mboProvider.navigation.next(s.RESULT)}initializatedConfiguration(){var i=this;return(0,u.Z)(function*(){(yield i.requestConfiguration.confirmation()).when({success:({transfiya:t})=>{i.transfiya=t,i.description=t.description}})})()}openDescription(){const i=this.bottomSheetService.create(h.F8,{componentProps:{initialValue:this.description}});i.open(),(0,v.catchPromise)(i.waiting().then(t=>{this.description=t}))}removeDescription(){this.modalConfirmationService.execute({title:"\xbfBorrar descripci\xf3n?",message:"\xbfEsta seguro de borrar la descripci\xf3n agregada en la transferencia?",accept:{label:"Borrar descripci\xf3n",click:()=>{this.managerTransfiya.removeDescription().when({success:()=>{this.description=""}})}},decline:{label:"Cancelar"}})}}return a.\u0275fac=function(i){return new(i||a)(n.\u0275\u0275directiveInject(r.fG),n.\u0275\u0275directiveInject(r.$e),n.\u0275\u0275directiveInject(y.ZL),n.\u0275\u0275directiveInject(d.ow),n.\u0275\u0275directiveInject(d.Pm),n.\u0275\u0275directiveInject(g.c))},a.\u0275cmp=n.\u0275\u0275defineComponent({type:a,selectors:[["mbo-transfiya-transfer-confirmation-page"]],decls:18,vars:11,consts:[[1,"mbo-transfiya-transfer-confirmation-page__content","mbo-page__scroller"],[1,"mbo-transfiya-transfer-confirmation-page__header"],["title","Confirmar","progress","100%",3,"leftAction","rightAction"],[1,"mbo-transfiya-transfer-confirmation-page__body"],[1,"bocc-card"],[1,"bocc-card__title","subtitle2-medium"],[1,"bocc-card__body"],[3,"title","subtitle","actions"],["header","LA SUMA DE",3,"amount","actions"],["header","DESDE",3,"title","subtitle"],["header","DESCRIPCI\xd3N",3,"actions","detail",4,"ngIf"],["class","bocc-card__footer",4,"ngIf"],[1,"mbo-transfiya-transfer-confirmation-page__footer"],["id","btn_transfiya-transfer-confirmation_submit","bocc-button","raised","prefixIcon","arrow-transfer",3,"click"],["header","DESCRIPCI\xd3N",3,"actions","detail"],[1,"bocc-card__footer"],["id","btn_transfiya-transfer-confirmation_add-description","bocc-button","note","prefixIcon","edit-note",3,"click"]],template:function(i,t){1&i&&(n.\u0275\u0275elementStart(0,"mbo-page")(1,"div",0)(2,"div",1),n.\u0275\u0275element(3,"bocc-header-form",2),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5),n.\u0275\u0275text(7," \xbfDeseas transferirle a? "),n.\u0275\u0275elementEnd(),n.\u0275\u0275elementStart(8,"div",6),n.\u0275\u0275element(9,"bocc-card-summary",7)(10,"bocc-card-summary",8)(11,"bocc-card-summary",9),n.\u0275\u0275template(12,I,1,2,"bocc-card-summary",10),n.\u0275\u0275elementEnd(),n.\u0275\u0275template(13,S,4,0,"div",11),n.\u0275\u0275elementEnd()()(),n.\u0275\u0275elementStart(14,"div",12)(15,"button",13),n.\u0275\u0275listener("click",function(){return t.onSubmit()}),n.\u0275\u0275elementStart(16,"span"),n.\u0275\u0275text(17,"Transferir"),n.\u0275\u0275elementEnd()()()()),2&i&&(n.\u0275\u0275advance(3),n.\u0275\u0275property("leftAction",t.backAction)("rightAction",t.cancelAction),n.\u0275\u0275advance(6),n.\u0275\u0275property("title",null==t.transfiya||null==t.transfiya.contact?null:t.transfiya.contact.title)("subtitle",null==t.transfiya||null==t.transfiya.contact?null:t.transfiya.contact.subtitle)("actions",t.destinationActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("amount",null==t.transfiya?null:t.transfiya.amount)("actions",t.amountActions),n.\u0275\u0275advance(1),n.\u0275\u0275property("title",null==t.transfiya||null==t.transfiya.product?null:t.transfiya.product.nickname)("subtitle",null==t.transfiya||null==t.transfiya.product?null:t.transfiya.product.number),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",t.description),n.\u0275\u0275advance(1),n.\u0275\u0275property("ngIf",!t.description))},dependencies:[l.NgIf,T.K,C.J,A.D,P.P],styles:["/*!\n * MBO TransfiyaTransferConfirmation Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 15/Jun/2022\n * Updated: 09/Feb/2024\n*/mbo-transfiya-transfer-confirmation-page{position:relative;display:block;width:100%;height:100%}mbo-transfiya-transfer-confirmation-page .mbo-transfiya-transfer-confirmation-page__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x8)}mbo-transfiya-transfer-confirmation-page .mbo-transfiya-transfer-confirmation-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box}mbo-transfiya-transfer-confirmation-page .mbo-transfiya-transfer-confirmation-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-transfer-confirmation-page .mbo-transfiya-transfer-confirmation-page__footer button{width:100%}\n"],encapsulation:2}),a})(),D=(()=>{class a{}return a.\u0275fac=function(i){return new(i||a)},a.\u0275mod=n.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=n.\u0275\u0275defineInjector({imports:[l.CommonModule,m.RouterModule.forChild([{path:"",component:M}]),p.KI,r.Jx,r.DM,r.P8,r.b6,r.oc]}),a})()}}]);