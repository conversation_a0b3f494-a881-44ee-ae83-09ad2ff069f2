(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5432],{59804:(ft,B,y)=>{y.d(B,{c:()=>b,r:()=>D});const b=(c,s)=>{c.componentOnReady?c.componentOnReady().then(l=>s(l)):D(()=>s(c))},D=c=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(c):"function"==typeof requestAnimationFrame?requestAnimationFrame(c):setTimeout(c)},93435:(ft,B,y)=>{y.d(B,{L:()=>_,a:()=>o,b:()=>b,c:()=>w,d:()=>N,g:()=>c});const _="ionViewWillEnter",o="ionViewDidEnter",b="ionViewWillLeave",w="ionViewDidLeave",N="ionViewWillUnload",c=s=>s.classList.contains("ion-page")?s:s.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||s},30958:(ft,B,y)=>{y.r(B),y.d(B,{AngularDelegate:()=>yt,Config:()=>st,ConfigToken:()=>Mt,DomController:()=>xt,IonBackButton:()=>se,IonModal:()=>Yt,IonNav:()=>de,IonPopover:()=>Gt,IonRouterOutlet:()=>Dt,IonTabs:()=>he,IonicRouteStrategy:()=>ge,MenuController:()=>Pt,NavController:()=>j,NavParams:()=>ct,OverlayBaseController:()=>ve,Platform:()=>pt,ProxyCmp:()=>G,RouterLinkDelegateDirective:()=>ae,RouterLinkWithHrefDelegateDirective:()=>ce,ValueAccessor:()=>fe,bindLifecycleEvents:()=>lt,provideComponentInputBinding:()=>ie,raf:()=>Rt,setIonicClasses:()=>L});var _=y(15861),o=y(99877),b=y(78007),w=y(17007);class N{constructor(){this.m=new Map}reset(n){this.m=new Map(Object.entries(n))}get(n,t){const i=this.m.get(n);return void 0!==i?i:t}getBoolean(n,t=!1){const i=this.m.get(n);return void 0===i?t:"string"==typeof i?"true"===i:!!i}getNumber(n,t){const i=parseFloat(this.m.get(n));return isNaN(i)?void 0!==t?t:NaN:i}set(n,t){this.m.set(n,t)}}const E=new N,H=e=>Z(e),Z=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return null==n&&(n=e.Ionic.platforms=nt(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},nt=e=>{const n=E.get("platform");return Object.keys(x).filter(t=>{const i=n?.[t];return"function"==typeof i?i(e):x[t](e)})},U=e=>!!(C(e,/iPad/i)||C(e,/Macintosh/i)&&c(e)),K=e=>C(e,/android|sink/i),c=e=>T(e,"(any-pointer:coarse)"),l=e=>u(e)||f(e),u=e=>!!(e.cordova||e.phonegap||e.PhoneGap),f=e=>!!e.Capacitor?.isNative,C=(e,n)=>n.test(e.navigator.userAgent),T=(e,n)=>{var t;return null===(t=e.matchMedia)||void 0===t?void 0:t.call(e,n).matches},x={ipad:U,iphone:e=>C(e,/iPhone/i),ios:e=>C(e,/iPhone|iPod/i)||U(e),android:K,phablet:e=>{const n=e.innerWidth,t=e.innerHeight,i=Math.min(n,t),r=Math.max(n,t);return i>390&&i<520&&r>620&&r<800},tablet:e=>{const n=e.innerWidth,t=e.innerHeight,i=Math.min(n,t),r=Math.max(n,t);return U(e)||(e=>K(e)&&!C(e,/mobile/i))(e)||i>460&&i<820&&r>780&&r<1400},cordova:u,capacitor:f,electron:e=>C(e,/electron/i),pwa:e=>{var n;return!!(null!==(n=e.matchMedia)&&void 0!==n&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},mobile:c,mobileweb:e=>c(e)&&!l(e),desktop:e=>!c(e),hybrid:l};var z=y(93435),Tt=y(59804),k=y(42168),J=y(97582),S=y(84757),Bt=y(88191);const _t=["tabsInner"];class Pt{constructor(n){this.menuController=n}open(n){return this.menuController.open(n)}close(n){return this.menuController.close(n)}toggle(n){return this.menuController.toggle(n)}enable(n,t){return this.menuController.enable(n,t)}swipeGesture(n,t){return this.menuController.swipeGesture(n,t)}isOpen(n){return this.menuController.isOpen(n)}isEnabled(n){return this.menuController.isEnabled(n)}get(n){return this.menuController.get(n)}getOpen(){return this.menuController.getOpen()}getMenus(){return this.menuController.getMenus()}registerAnimation(n,t){return this.menuController.registerAnimation(n,t)}isAnimating(){return this.menuController.isAnimating()}_getOpenSync(){return this.menuController._getOpenSync()}_createAnimation(n,t){return this.menuController._createAnimation(n,t)}_register(n){return this.menuController._register(n)}_unregister(n){return this.menuController._unregister(n)}_setOpen(n,t,i){return this.menuController._setOpen(n,t,i)}}let xt=(()=>{class e{read(t){mt().read(t)}write(t){mt().write(t)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const mt=()=>{const e=typeof window<"u"?window:null;if(null!=e){const n=e.Ionic;return n?.queue?n.queue:{read:t=>e.requestAnimationFrame(t),write:t=>e.requestAnimationFrame(t)}}return{read:n=>n(),write:n=>n()}};let pt=(()=>{class e{constructor(t,i){this.doc=t,this.backButton=new k.Subject,this.keyboardDidShow=new k.Subject,this.keyboardDidHide=new k.Subject,this.pause=new k.Subject,this.resume=new k.Subject,this.resize=new k.Subject,i.run(()=>{let r;this.win=t.defaultView,this.backButton.subscribeWithPriority=function(a,d){return this.subscribe(h=>h.register(a,m=>i.run(()=>d(m))))},M(this.pause,t,"pause",i),M(this.resume,t,"resume",i),M(this.backButton,t,"ionBackButton",i),M(this.resize,this.win,"resize",i),M(this.keyboardDidShow,this.win,"ionKeyboardDidShow",i),M(this.keyboardDidHide,this.win,"ionKeyboardDidHide",i),this._readyPromise=new Promise(a=>{r=a}),this.win?.cordova?t.addEventListener("deviceready",()=>{r("cordova")},{once:!0}):r("dom")})}is(t){return((e,n)=>("string"==typeof e&&(n=e,e=void 0),H(e).includes(n)))(this.win,t)}platforms(){return H(this.win)}ready(){return this._readyPromise}get isRTL(){return"rtl"===this.doc.dir}getQueryParam(t){return Ot(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){const i=this.win.navigator;return!!(i?.userAgent&&i.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(w.DOCUMENT),o.\u0275\u0275inject(o.NgZone))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const Ot=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");const i=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return i?decodeURIComponent(i[1].replace(/\+/g," ")):null},M=(e,n,t,i)=>{n&&n.addEventListener(t,r=>{i.run(()=>{const a=r?.detail;e.next(a)})})};let j=(()=>{class e{constructor(t,i,r,a){this.location=i,this.serializer=r,this.router=a,this.direction=gt,this.animated=vt,this.guessDirection="forward",this.lastNavId=-1,a&&a.events.subscribe(d=>{if(d instanceof b.NavigationStart){const h=d.restoredState?d.restoredState.navigationId:d.id;this.guessDirection=h<this.lastNavId?"back":"forward",this.guessAnimation=d.restoredState?void 0:this.guessDirection,this.lastNavId="forward"===this.guessDirection?d.id:h}}),t.backButton.subscribeWithPriority(0,d=>{this.pop(),d()})}navigateForward(t,i={}){return this.setDirection("forward",i.animated,i.animationDirection,i.animation),this.navigate(t,i)}navigateBack(t,i={}){return this.setDirection("back",i.animated,i.animationDirection,i.animation),this.navigate(t,i)}navigateRoot(t,i={}){return this.setDirection("root",i.animated,i.animationDirection,i.animation),this.navigate(t,i)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){var t=this;return(0,_.Z)(function*(){let i=t.topOutlet;for(;i;){if(yield i.pop())return!0;i=i.parentOutlet}return!1})()}setDirection(t,i,r,a){this.direction=t,this.animated=St(t,i,r),this.animationBuilder=a}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let i,t="root";const r=this.animationBuilder;return"auto"===this.direction?(t=this.guessDirection,i=this.guessAnimation):(i=this.animated,t=this.direction),this.direction=gt,this.animated=vt,this.animationBuilder=void 0,{direction:t,animation:i,animationBuilder:r}}navigate(t,i){if(Array.isArray(t))return this.router.navigate(t,i);{const r=this.serializer.parse(t.toString());return void 0!==i.queryParams&&(r.queryParams={...i.queryParams}),void 0!==i.fragment&&(r.fragment=i.fragment),this.router.navigateByUrl(r,i)}}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275inject(pt),o.\u0275\u0275inject(w.Location),o.\u0275\u0275inject(b.UrlSerializer),o.\u0275\u0275inject(b.Router,8))},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const St=(e,n,t)=>{if(!1!==n){if(void 0!==t)return t;if("forward"===e||"back"===e)return e;if("root"===e&&!0===n)return"forward"}},gt="auto",vt=void 0;let st=(()=>{class e{get(t,i){const r=at();return r?r.get(t,i):null}getBoolean(t,i){const r=at();return!!r&&r.getBoolean(t,i)}getNumber(t,i){const r=at();return r?r.getNumber(t,i):0}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const Mt=new o.InjectionToken("USERCONFIG"),at=()=>{if(typeof window<"u"){const e=window.Ionic;if(e?.config)return e.config}return null};class ct{constructor(n={}){this.data=n}get(n){return this.data[n]}}let yt=(()=>{class e{constructor(){this.zone=(0,o.inject)(o.NgZone),this.applicationRef=(0,o.inject)(o.ApplicationRef)}create(t,i,r){return new jt(t,i,this.applicationRef,this.zone,r)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})();class jt{constructor(n,t,i,r,a){this.environmentInjector=n,this.injector=t,this.applicationRef=i,this.zone=r,this.elementReferenceKey=a,this.elRefMap=new WeakMap,this.elEventsMap=new WeakMap}attachViewToDom(n,t,i,r){return this.zone.run(()=>new Promise(a=>{const d={...i};void 0!==this.elementReferenceKey&&(d[this.elementReferenceKey]=n),a(Lt(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,d,r,this.elementReferenceKey))}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(i=>{const r=this.elRefMap.get(t);if(r){r.destroy(),this.elRefMap.delete(t);const a=this.elEventsMap.get(t);a&&(a(),this.elEventsMap.delete(t))}i()}))}}const Lt=(e,n,t,i,r,a,d,h,m,g,I)=>{const q=o.Injector.create({providers:Vt(m),parent:t}),A=(0,o.createComponent)(h,{environmentInjector:n,elementInjector:q}),F=A.instance,V=A.location.nativeElement;if(m&&(I&&void 0!==F[I]&&console.error(`[Ionic Error]: ${I} is a reserved property when using ${d.tagName.toLowerCase()}. Rename or remove the "${I}" property from ${h.name}.`),Object.assign(F,m)),g)for(const be of g)V.classList.add(be);const ye=lt(e,F,V);return d.appendChild(V),i.attachView(A.hostView),r.set(V,A),a.set(V,ye),V},Ft=[z.L,z.a,z.b,z.c,z.d],lt=(e,n,t)=>e.run(()=>{const i=Ft.filter(r=>"function"==typeof n[r]).map(r=>{const a=d=>n[r](d.detail);return t.addEventListener(r,a),()=>t.removeEventListener(r,a)});return()=>i.forEach(r=>r())}),bt=new o.InjectionToken("NavParamsToken"),Vt=e=>[{provide:bt,useValue:e},{provide:ct,useFactory:Nt,deps:[bt]}],Nt=e=>new ct(e),Wt=(e,n)=>{const t=e.prototype;n.forEach(i=>{Object.defineProperty(t,i,{get(){return this.el[i]},set(r){this.z.runOutsideAngular(()=>this.el[i]=r)}})})},Ht=(e,n)=>{const t=e.prototype;n.forEach(i=>{t[i]=function(){const r=arguments;return this.z.runOutsideAngular(()=>this.el[i].apply(this.el,r))}})},ut=(e,n,t)=>{t.forEach(i=>e[i]=(0,k.fromEvent)(n,i))};function G(e){return function(t){const{defineCustomElementFn:i,inputs:r,methods:a}=e;return void 0!==i&&i(),r&&Wt(t,r),a&&Ht(t,a),t}}const Ut=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],zt=["present","dismiss","onDidDismiss","onWillDismiss"];let Gt=(()=>{let e=class{constructor(t,i,r){this.z=r,this.isCmpOpen=!1,this.el=i.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),ut(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ChangeDetectorRef),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.NgZone))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["ion-popover"]],contentQueries:function(t,i,r){if(1&t&&o.\u0275\u0275contentQuery(r,o.TemplateRef,5),2&t){let a;o.\u0275\u0275queryRefresh(a=o.\u0275\u0275loadQuery())&&(i.template=a.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"}}),e=(0,J.gn)([G({inputs:Ut,methods:zt})],e),e})();const qt=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","event","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],Qt=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"];let Yt=(()=>{let e=class{constructor(t,i,r){this.z=r,this.isCmpOpen=!1,this.el=i.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),ut(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}};return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ChangeDetectorRef),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.NgZone))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["ion-modal"]],contentQueries:function(t,i,r){if(1&t&&o.\u0275\u0275contentQuery(r,o.TemplateRef,5),2&t){let a;o.\u0275\u0275queryRefresh(a=o.\u0275\u0275loadQuery())&&(i.template=a.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",event:"event",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"}}),e=(0,J.gn)([G({inputs:qt,methods:Qt})],e),e})();const Ct=(e,n)=>((e=e.filter(t=>t.stackId!==n.stackId)).push(n),e),dt=(e,n)=>{const t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},It=(e,n)=>!n||e.stackId!==n.stackId,Xt=(e,n)=>{if(!e)return;const t=Et(n);for(let i=0;i<t.length;i++){if(i>=e.length)return t[i];if(t[i]!==e[i])return}},Et=e=>e.split("/").map(n=>n.trim()).filter(n=>""!==n),kt=e=>{e&&(e.ref.destroy(),e.unlistenEvents())};class Jt{constructor(n,t,i,r,a,d){this.containerEl=t,this.router=i,this.navCtrl=r,this.zone=a,this.location=d,this.views=[],this.skipTransition=!1,this.nextId=0,this.tabsPrefix=void 0!==n?Et(n):void 0}createView(n,t){const i=dt(this.router,t),r=n?.location?.nativeElement,a=lt(this.zone,n.instance,r);return{id:this.nextId++,stackId:Xt(this.tabsPrefix,i),unlistenEvents:a,element:r,ref:n,url:i}}getExistingView(n){const t=dt(this.router,n),i=this.views.find(r=>r.url===t);return i&&i.ref.changeDetectorRef.reattach(),i}setActive(n){const t=this.navCtrl.consumeTransition();let{direction:i,animation:r,animationBuilder:a}=t;const d=this.activeView,h=It(n,d);h&&(i="back",r=void 0);const m=this.views.slice();let g;const I=this.router;I.getCurrentNavigation?g=I.getCurrentNavigation():I.navigations?.value&&(g=I.navigations.value),g?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);const q=this.views.includes(n),A=this.insertView(n,i);q||n.ref.changeDetectorRef.detectChanges();const F=n.animationBuilder;return void 0===a&&"back"===i&&!h&&void 0!==F&&(a=F),d&&(d.animationBuilder=a),this.zone.runOutsideAngular(()=>this.wait(()=>(d&&d.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,d,r,this.canGoBack(1),!1,a).then(()=>te(n,A,m,this.location,this.zone)).then(()=>({enteringView:n,direction:i,animation:r,tabSwitch:h})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{const i=this.getStack(t);if(i.length<=n)return Promise.resolve(!1);const r=i[i.length-n-1];let a=r.url;const d=r.savedData;if(d){const m=d.get("primary");m?.route?._routerState?.snapshot.url&&(a=m.route._routerState.snapshot.url)}const{animationBuilder:h}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(a,{...r.savedExtras,animation:h}).then(()=>!0)})}startBackTransition(){const n=this.activeView;if(n){const t=this.getStack(n.stackId),i=t[t.length-2],r=i.animationBuilder;return this.wait(()=>this.transition(i,n,"back",this.canGoBack(2),!0,r))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&wt(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){const t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){const t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return void 0!==this.runningTask}destroy(){this.containerEl=void 0,this.views.forEach(kt),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=((e,n,t)=>"root"===t?Ct(e,n):"forward"===t?((e,n)=>(e.indexOf(n)>=0?e=e.filter(i=>i.stackId!==n.stackId||i.id<=n.id):e.push(n),e))(e,n):((e,n)=>e.indexOf(n)>=0?e.filter(i=>i.stackId!==n.stackId||i.id<=n.id):Ct(e,n))(e,n))(this.views,n,t),this.views.slice()}transition(n,t,i,r,a,d){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);const h=n?n.element:void 0,m=t?t.element:void 0,g=this.containerEl;return h&&h!==m&&(h.classList.add("ion-page"),h.classList.add("ion-page-invisible"),h.parentElement!==g&&g.appendChild(h),g.commit)?g.commit(h,m,{duration:void 0===i?0:void 0,direction:i,showGoBack:r,progressAnimation:a,animationBuilder:d}):Promise.resolve(!1)}wait(n){var t=this;return(0,_.Z)(function*(){void 0!==t.runningTask&&(yield t.runningTask,t.runningTask=void 0);const i=t.runningTask=n();return i.finally(()=>t.runningTask=void 0),i})()}}const te=(e,n,t,i,r)=>"function"==typeof requestAnimationFrame?new Promise(a=>{requestAnimationFrame(()=>{wt(e,n,t,i,r),a()})}):Promise.resolve(),wt=(e,n,t,i,r)=>{r.run(()=>t.filter(a=>!n.includes(a)).forEach(kt)),n.forEach(a=>{const h=i.path().split("?")[0].split("#")[0];if(a!==e&&a.url!==h){const m=a.element;m.setAttribute("aria-hidden","true"),m.classList.add("ion-page-hidden"),a.ref.changeDetectorRef.detach()}})};let Dt=(()=>{class e{constructor(t,i,r,a,d,h,m,g){this.parentOutlet=g,this.activatedView=null,this.proxyMap=new WeakMap,this.currentActivatedRoute$=new k.BehaviorSubject(null),this.activated=null,this._activatedRoute=null,this.name=b.PRIMARY_OUTLET,this.stackWillChange=new o.EventEmitter,this.stackDidChange=new o.EventEmitter,this.activateEvents=new o.EventEmitter,this.deactivateEvents=new o.EventEmitter,this.parentContexts=(0,o.inject)(b.ChildrenOutletContexts),this.location=(0,o.inject)(o.ViewContainerRef),this.environmentInjector=(0,o.inject)(o.EnvironmentInjector),this.inputBinder=(0,o.inject)(At,{optional:!0}),this.supportsBindingToComponentInputs=!0,this.config=(0,o.inject)(st),this.navCtrl=(0,o.inject)(j),this.nativeEl=a.nativeElement,this.name=t||b.PRIMARY_OUTLET,this.tabsPrefix="true"===i?dt(d,m):void 0,this.stackCtrl=new Jt(this.tabsPrefix,this.nativeEl,d,this.navCtrl,h,r),this.parentContexts.onChildOutletCreated(this.name,this)}get activatedComponentRef(){return this.activated}set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:i=>this.stackCtrl.endBackTransition(i)}:void 0}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){const t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>(0,Tt.c)(this.nativeEl,t)).then(()=>{void 0===this._swipeGesture&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled","ios"===this.nativeEl.mode))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,i){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){const i=this.getContext();this.activatedView.savedData=new Map(i.children.contexts);const r=this.activatedView.savedData.get("primary");if(r&&i.route&&(r.route={...i.route}),this.activatedView.savedExtras={},i.route){const a=i.route.snapshot;this.activatedView.savedExtras.queryParams=a.queryParams,this.activatedView.savedExtras.fragment=a.fragment}}const t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,i){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let r,a=this.stackCtrl.getExistingView(t);if(a){r=this.activated=a.ref;const h=a.savedData;h&&(this.getContext().children.contexts=h),this.updateActivatedRouteProxy(r.instance,t)}else{const h=t._futureSnapshot,m=this.parentContexts.getOrCreateContext(this.name).children,g=new k.BehaviorSubject(null),I=this.createActivatedRouteProxy(g,t),q=new ee(I,m,this.location.injector);r=this.activated=this.location.createComponent(h.routeConfig.component??h.component,{index:this.location.length,injector:q,environmentInjector:i??this.environmentInjector}),g.next(r.instance),a=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(r.instance,I),this.currentActivatedRoute$.next({component:r.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=a,this.navCtrl.setTopOutlet(this);const d=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:a,tabSwitch:It(a,d)}),this.stackCtrl.setActive(a).then(h=>{this.activateEvents.emit(r.instance),this.stackDidChange.emit(h)})}canGoBack(t=1,i){return this.stackCtrl.canGoBack(t,i)}pop(t=1,i){return this.stackCtrl.pop(t,i)}getLastUrl(t){const i=this.stackCtrl.getLastUrl(t);return i?i.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,i){const r=new b.ActivatedRoute;return r._futureSnapshot=i._futureSnapshot,r._routerState=i._routerState,r.snapshot=i.snapshot,r.outlet=i.outlet,r.component=i.component,r._paramMap=this.proxyObservable(t,"paramMap"),r._queryParamMap=this.proxyObservable(t,"queryParamMap"),r.url=this.proxyObservable(t,"url"),r.params=this.proxyObservable(t,"params"),r.queryParams=this.proxyObservable(t,"queryParams"),r.fragment=this.proxyObservable(t,"fragment"),r.data=this.proxyObservable(t,"data"),r}proxyObservable(t,i){return t.pipe((0,S.filter)(r=>!!r),(0,S.switchMap)(r=>this.currentActivatedRoute$.pipe((0,S.filter)(a=>null!==a&&a.component===r),(0,S.switchMap)(a=>a&&a.activatedRoute[i]),(0,S.distinctUntilChanged)())))}updateActivatedRouteProxy(t,i){const r=this.proxyMap.get(t);if(!r)throw new Error("Could not find activated route proxy for view");r._futureSnapshot=i._futureSnapshot,r._routerState=i._routerState,r.snapshot=i.snapshot,r.outlet=i.outlet,r.component=i.component,this.currentActivatedRoute$.next({component:t,activatedRoute:i})}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275injectAttribute("name"),o.\u0275\u0275injectAttribute("tabs"),o.\u0275\u0275directiveInject(w.Location),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(b.Router),o.\u0275\u0275directiveInject(o.NgZone),o.\u0275\u0275directiveInject(b.ActivatedRoute),o.\u0275\u0275directiveInject(e,12))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]}),e})();class ee{constructor(n,t,i){this.route=n,this.childContexts=t,this.parent=i}get(n,t){return n===b.ActivatedRoute?this.route:n===b.ChildrenOutletContexts?this.childContexts:this.parent.get(n,t)}}const At=new o.InjectionToken("");let ne=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){const{activatedRoute:i}=t,r=(0,k.combineLatest)([i.queryParams,i.params,i.data]).pipe((0,S.switchMap)(([a,d,h],m)=>(h={...a,...d,...h},0===m?(0,k.of)(h):Promise.resolve(h)))).subscribe(a=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==i||null===i.component)return void this.unsubscribeFromRouteData(t);const d=(0,o.reflectComponentType)(i.component);if(d)for(const{templateName:h}of d.inputs)t.activatedComponentRef.setInput(h,a[h]);else this.unsubscribeFromRouteData(t)});this.outletDataSubscriptions.set(t,r)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o.\u0275\u0275defineInjectable({token:e,factory:e.\u0275fac}),e})();const ie=()=>({provide:At,useFactory:re,deps:[b.Router]});function re(e){return e?.componentInputBindingEnabled?new ne:null}const oe=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"];let se=(()=>{let e=class{constructor(t,i,r,a,d,h){this.routerOutlet=t,this.navCtrl=i,this.config=r,this.r=a,this.z=d,h.detach(),this.el=this.r.nativeElement}onClick(t){const i=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):null!=i&&(this.navCtrl.navigateBack(i,{animation:this.routerAnimation}),t.preventDefault())}};return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(Dt,8),o.\u0275\u0275directiveInject(j),o.\u0275\u0275directiveInject(st),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.NgZone),o.\u0275\u0275directiveInject(o.ChangeDetectorRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,hostBindings:function(t,i){1&t&&o.\u0275\u0275listener("click",function(a){return i.onClick(a)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"}}),e=(0,J.gn)([G({inputs:oe})],e),e})(),ae=(()=>{class e{constructor(t,i,r,a,d){this.locationStrategy=t,this.navCtrl=i,this.elementRef=r,this.router=a,this.routerLink=d,this.routerDirection="forward"}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){const t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(w.LocationStrategy),o.\u0275\u0275directiveInject(j),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(b.Router),o.\u0275\u0275directiveInject(b.RouterLink,8))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(t,i){1&t&&o.\u0275\u0275listener("click",function(a){return i.onClick(a)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[o.\u0275\u0275NgOnChangesFeature]}),e})(),ce=(()=>{class e{constructor(t,i,r,a,d){this.locationStrategy=t,this.navCtrl=i,this.elementRef=r,this.router=a,this.routerLink=d,this.routerDirection="forward"}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){const t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(w.LocationStrategy),o.\u0275\u0275directiveInject(j),o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(b.Router),o.\u0275\u0275directiveInject(b.RouterLink,8))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(t,i){1&t&&o.\u0275\u0275listener("click",function(){return i.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[o.\u0275\u0275NgOnChangesFeature]}),e})();const le=["animated","animation","root","rootParams","swipeGesture"],ue=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"];let de=(()=>{let e=class{constructor(t,i,r,a,d,h){this.z=d,h.detach(),this.el=t.nativeElement,t.nativeElement.delegate=a.create(i,r),ut(this,this.el,["ionNavDidChange","ionNavWillChange"])}};return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.ElementRef),o.\u0275\u0275directiveInject(o.EnvironmentInjector),o.\u0275\u0275directiveInject(o.Injector),o.\u0275\u0275directiveInject(yt),o.\u0275\u0275directiveInject(o.NgZone),o.\u0275\u0275directiveInject(o.ChangeDetectorRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"}}),e=(0,J.gn)([G({inputs:le,methods:ue})],e),e})(),he=(()=>{class e{constructor(t){this.navCtrl=t,this.ionTabsWillChange=new o.EventEmitter,this.ionTabsDidChange=new o.EventEmitter,this.tabBarSlot="bottom"}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:i}){const r=t.stackId;i&&void 0!==r&&this.ionTabsWillChange.emit({tab:r})}onStackDidChange({enteringView:t,tabSwitch:i}){const r=t.stackId;i&&void 0!==r&&(this.tabBar&&(this.tabBar.selectedTab=r),this.ionTabsDidChange.emit({tab:r}))}select(t){const i="string"==typeof t,r=i?t:t.detail.tab,a=this.outlet.getActiveStackId()===r,d=`${this.outlet.tabsPrefix}/${r}`;if(i||t.stopPropagation(),a){const h=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(h)?.url===d)return;const g=this.outlet.getRootView(r);return this.navCtrl.navigateRoot(d,{...g&&d===g.url&&g.savedExtras,animated:!0,animationDirection:"back"})}{const h=this.outlet.getLastRouteView(r),g=h?.savedExtras;return this.navCtrl.navigateRoot(h?.url||d,{...g,animated:!0,animationDirection:"back"})}}getSelected(){return this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{const i=t.el.getAttribute("slot");i!==this.tabBarSlot&&(this.tabBarSlot=i,this.relocateTabBar())})}relocateTabBar(){const t=this.tabBar.el;"top"===this.tabBarSlot?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(j))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,selectors:[["ion-tabs"]],viewQuery:function(t,i){if(1&t&&o.\u0275\u0275viewQuery(_t,7,o.ElementRef),2&t){let r;o.\u0275\u0275queryRefresh(r=o.\u0275\u0275loadQuery())&&(i.tabsInner=r.first)}},hostBindings:function(t,i){1&t&&o.\u0275\u0275listener("ionTabButtonClick",function(a){return i.select(a)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"}}),e})();const Rt=e=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(e):"function"==typeof requestAnimationFrame?requestAnimationFrame(e):setTimeout(e);let fe=(()=>{class e{constructor(t,i){this.injector=t,this.elementRef=i,this.onChange=()=>{},this.onTouched=()=>{}}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,L(this.elementRef)}handleValueChange(t,i){t===this.elementRef.nativeElement&&(i!==this.lastValue&&(this.lastValue=i,this.onChange(i)),L(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement&&(this.onTouched(),L(this.elementRef))}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Bt.NgControl)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>L(this.elementRef)));const i=t.control;i&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(a=>{if(typeof i[a]<"u"){const d=i[a].bind(i);i[a]=(...h)=>{d(...h),L(this.elementRef)}}})}}return e.\u0275fac=function(t){return new(t||e)(o.\u0275\u0275directiveInject(o.Injector),o.\u0275\u0275directiveInject(o.ElementRef))},e.\u0275dir=o.\u0275\u0275defineDirective({type:e,hostBindings:function(t,i){1&t&&o.\u0275\u0275listener("ionBlur",function(a){return i._handleBlurEvent(a.target)})}}),e})();const L=e=>{Rt(()=>{const n=e.nativeElement,t=null!=n.value&&n.value.toString().length>0,i=me(n);ht(n,i);const r=n.closest("ion-item");r&&ht(r,t?[...i,"item-has-value"]:i)})},me=e=>{const n=e.classList,t=[];for(let i=0;i<n.length;i++){const r=n.item(i);null!==r&&pe(r,"ng-")&&t.push(`ion-${r.substring(3)}`)}return t},ht=(e,n)=>{const t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},pe=(e,n)=>e.substring(0,n.length)===n;class ge{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;const i=n.params,r=t.params,a=Object.keys(i),d=Object.keys(r);if(a.length!==d.length)return!1;for(const h of a)if(r[h]!==i[h])return!1;return!0}}class ve{constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,i){return this.ctrl.dismiss(n,t,i)}getTop(){return this.ctrl.getTop()}}}}]);