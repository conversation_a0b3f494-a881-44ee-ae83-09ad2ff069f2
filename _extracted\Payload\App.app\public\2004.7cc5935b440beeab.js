(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2004],{72004:(z,l,t)=>{t.r(l),t.d(l,{MboTransfiyaAskAmountPageModule:()=>I});var d=t(17007),p=t(78007),i=t(30263),m=t(24495),g=t(39904),f=t(95437),b=t(57544),v=t(40914),y=t(73004),c=t(17698),a=t(99877),h=t(83413),A=t(35641),k=t(48774),C=t(45542);const u=g.Z6.TRANSFERS.TRANSFIYA.ASK,{MAX_TRANSFIYA:T,MIN_TRANSFIYA:M}=v.R;let x=(()=>{class n{constructor(e,o,s,P){this.mboProvider=e,this.requestConfiguration=o,this.managerTransfiya=s,this.cancelTransfiya=P,this.confirmation=!1,this.backAction={id:"btn_transfiya-ask-amount_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation,click:()=>{this.mboProvider.navigation.back(u.SOURCE)}},this.cancelAction={id:"btn_transfiya-ask-amount_cancel",label:"Cancelar",click:()=>{this.cancelTransfiya.execute()}},this.amountControl=new b.FormControl({validators:[m.C1,(0,m.Go)(M),(0,m.VV)(T)]})}ngOnInit(){this.initializatedConfiguration()}onSubmit(){this.managerTransfiya.setAmount(this.amountControl.value).when({success:()=>{this.mboProvider.navigation.next(u.CONFIRMATION)}})}initializatedConfiguration(){this.requestConfiguration.amount().when({success:({confirmation:e,amount:o,product:s})=>{o&&this.amountControl.setValue(o),this.product=s,this.confirmation=e}})}}return n.\u0275fac=function(e){return new(e||n)(a.\u0275\u0275directiveInject(f.ZL),a.\u0275\u0275directiveInject(c.UT),a.\u0275\u0275directiveInject(c.Pm),a.\u0275\u0275directiveInject(y.c))},n.\u0275cmp=a.\u0275\u0275defineComponent({type:n,selectors:[["mbo-transfiya-ask-amount-page"]],decls:12,vars:9,consts:[[1,"mbo-transfiya-ask-amount-page__content"],[1,"mbo-transfiya-ask-amount-page__header"],["title","Valor","progress","75%",3,"leftAction","rightAction"],[1,"mbo-transfiya-ask-amount-page__body"],[1,"mbo-transfiya-ask-amount-page__message","subtitle2-medium"],["elementId","txt_transfiya-ask-amount_value","label","Valor a solicitar","placeholder","0","type","money","prefix","$",3,"formControl"],["subtitle","Saldo disponible",3,"color","icon","title","number","amount"],[1,"mbo-transfiya-ask-amount-page__footer"],["id","btn_transfiya-ask-amount_submit","bocc-button","raised",3,"disabled","click"]],template:function(e,o){1&e&&(a.\u0275\u0275elementStart(0,"div",0)(1,"div",1),a.\u0275\u0275element(2,"bocc-header-form",2),a.\u0275\u0275elementEnd(),a.\u0275\u0275elementStart(3,"div",3)(4,"p",4),a.\u0275\u0275text(5," \xbfCu\xe1nto dinero deseas solicitar? "),a.\u0275\u0275elementEnd(),a.\u0275\u0275element(6,"bocc-growing-box",5)(7,"bocc-card-product-summary",6),a.\u0275\u0275elementEnd()(),a.\u0275\u0275elementStart(8,"div",7)(9,"button",8),a.\u0275\u0275listener("click",function(){return o.onSubmit()}),a.\u0275\u0275elementStart(10,"span"),a.\u0275\u0275text(11,"Continuar"),a.\u0275\u0275elementEnd()()()),2&e&&(a.\u0275\u0275advance(2),a.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),a.\u0275\u0275advance(4),a.\u0275\u0275property("formControl",o.amountControl),a.\u0275\u0275advance(1),a.\u0275\u0275property("color",null==o.product?null:o.product.color)("icon",null==o.product?null:o.product.logo)("title",null==o.product?null:o.product.nickname)("number",null==o.product?null:o.product.publicNumber)("amount",null==o.product?null:o.product.amount),a.\u0275\u0275advance(2),a.\u0275\u0275property("disabled",o.amountControl.invalid))},dependencies:[h.D,A.d,k.J,C.P],styles:["/*!\n * MBO TransfiyaRequestAmount Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 08/Nov/2022\n * Updated: 08/Feb/2024\n*/mbo-transfiya-ask-amount-page{--pvt-message-margin-bottom: var(--sizing-x28);--pvt-product-margin-top: var(--sizing-x28);--pvt-checkbox-margin-top: var(--sizing-x20);position:relative;display:flex;width:100%;height:100%;overflow:auto;flex-direction:column;row-gap:var(--sizing-x4);justify-content:space-between}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-x8) 0rem}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__body bocc-card-product-summary{margin-top:var(--pvt-product-margin-top)}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__body bocc-checkbox-label{margin-top:var(--pvt-checkbox-margin-top)}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__message{color:var(--color-carbon-darker-100);margin-bottom:var(--pvt-message-margin-bottom)}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__footer{position:relative;width:100%;padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-transfiya-ask-amount-page .mbo-transfiya-ask-amount-page__footer button{width:100%}@media screen and (max-height: 600px){mbo-transfiya-ask-amount-page{--pvt-message-margin-bottom: var(--sizing-x20);--pvt-product-margin-top: var(--sizing-x20);--pvt-checkbox-margin-top: var(--sizing-x12)}}\n"],encapsulation:2}),n})(),I=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=a.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=a.\u0275\u0275defineInjector({imports:[d.CommonModule,p.RouterModule.forChild([{path:"",component:x}]),i.D1,i.dH,i.Jx,i.P8]}),n})()}}]);