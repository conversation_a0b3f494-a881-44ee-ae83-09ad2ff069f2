(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3832],{61589:(B,k,t)=>{t.d(k,{Q:()=>e,e:()=>y});var v=t(39904),h=t(87903),C=t(53113);function m(d){const{creditCard:b,status:{isError:a,message:u}}=d,p=b.franchise.description;return a?{animation:v.cj,title:`\xa1Tarjeta de cr\xe9dito ${p} no ha sido bloqueada!`,subtitle:u}:{animation:v.F6,title:`\xa1Tarjeta de cr\xe9dito ${p} ha sido bloqueada exitosamente!`}}function f({isError:d}){return d?[{event:"cancel",label:"Cancelar",type:"outline"},{event:"retry",label:"Volver a intentar",type:"raised"}]:[{event:"finish",label:"Finalizar",type:"raised"}]}function e(d){const{dateFormat:b,timeFormat:a}=new C.ou,{creditCard:u,status:p}=d;return{actions:f(p),error:p.isError,header:m(d),informations:[(0,h.SP)("TARJETA",u.nickname,u.number),(0,h.cZ)(b,a)],skeleton:!1}}function g(d){const{status:{isError:b,message:a}}=d;return b?{animation:v.cj,title:"\xa1Tarjeta de d\xe9bito Mastercard no ha sido bloqueada!",subtitle:a}:{animation:v.F6,title:"\xa1Tarjeta de d\xe9bito Mastercard ha sido bloqueada exitosamente!"}}function y(d){const{dateFormat:b,timeFormat:a}=new C.ou,{debitCard:u,status:p}=d;return{actions:f(p),error:p.isError,header:g(d),informations:[(0,h.SP)("TARJETA",u.name,u.number),(0,h.cZ)(b,a)],skeleton:!1}}},18275:(B,k,t)=>{t.d(k,{S:()=>p,a:()=>P});var v=t(15861),h=t(87956),C=t(53113),m=t(98699),f=t(71776),e=t(39904),g=t(89148),y=t(87903),d=t(42168),b=t(84757),a=t(99877);let u=(()=>{class l{constructor(o){this.http=o}creditCard(o){return this.card(o.id,g.Gt.CreditCard)}debitCard(o){return this.card(o.id,o.type)}card(o,s){return(0,d.firstValueFrom)(this.http.post(e.bV.PRODUCTS.BLOCK_CARD,[{cardId:o,productType:s}]).pipe((0,b.map)(([c])=>c?(0,y.l1)(c,"SUCCESS"):new C.LN("ERROR","Lo sentimos en este momento no podemos realizar esta operaci\xf3n.")),(0,b.catchError)(c=>(0,d.of)((0,y.rU)(c)))))}}return l.\u0275fac=function(o){return new(o||l)(a.\u0275\u0275inject(f.HttpClient))},l.\u0275prov=a.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),p=(()=>{class l{constructor(o,s,i){this.products=o,this.repository=s,this.eventBusService=i}configuration(o){var s=this;return(0,v.Z)(function*(){try{const i=(yield s.products.requestCreditCards()).find(({id:c})=>c===o);return m.Either.success(i)}catch({message:i}){return m.Either.failure({message:i})}})()}block(o){var s=this;return(0,v.Z)(function*(){const i=yield s.execute(o);return s.eventBusService.emit(i.channel),m.Either.success({creditCard:o,status:i})})()}execute(o){try{return this.repository.creditCard(o)}catch({message:s}){return Promise.resolve(C.LN.error(s))}}}return l.\u0275fac=function(o){return new(o||l)(a.\u0275\u0275inject(h.hM),a.\u0275\u0275inject(u),a.\u0275\u0275inject(h.Yd))},l.\u0275prov=a.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),P=(()=>{class l{constructor(o,s,i,c){this.products=o,this.productService=s,this.repository=i,this.eventBusService=c}configuration(o){var s=this;return(0,v.Z)(function*(){try{const i=(yield s.products.requestAccountsForTransfer()).find(({id:E})=>E===o);if(!i)return m.Either.success({product:i,cards:[]});const c=yield s.productService.requestDebitCards(i);return m.Either.success({product:i,cards:c})}catch({message:i}){return m.Either.failure({message:i})}})()}block(o){var s=this;return(0,v.Z)(function*(){const i=yield s.execute(o);return s.eventBusService.emit(i.channel),m.Either.success({debitCard:o,status:i})})()}execute(o){try{return this.repository.debitCard(o)}catch({message:s}){return Promise.resolve(C.LN.error(s))}}}return l.\u0275fac=function(o){return new(o||l)(a.\u0275\u0275inject(h.hM),a.\u0275\u0275inject(h.M5),a.\u0275\u0275inject(u),a.\u0275\u0275inject(h.Yd))},l.\u0275prov=a.\u0275\u0275defineInjectable({token:l,factory:l.\u0275fac,providedIn:"root"}),l})()},21339:(B,k,t)=>{t.d(k,{w:()=>y});var v=t(17007),C=t(30263),m=t(39904),f=t(95437),e=t(99877);let y=(()=>{class d{constructor(a){this.mboProvider=a}ngBoccPortal(a){this.portal=a}onLinkRates(){this.mboProvider.openUrl(m.BA.CARD_BLOCK_RATES)}onSubmit(){this.portal.resolve(),this.portal.destroy()}onCancel(){this.portal.destroy()}}return d.\u0275fac=function(a){return new(a||d)(e.\u0275\u0275directiveInject(f.ZL))},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["mbo-card-block-modal"]],standalone:!0,features:[e.\u0275\u0275StandaloneFeature],decls:15,vars:0,consts:[[1,"mbo-card-block-modal__content"],[1,"mbo-card-block-modal__body"],[1,"mbo-card-block-modal__title","smalltext-medium"],[1,"mbo-card-block-modal__message","body2-medium"],["bocc-button","flat","bocc-theme","amathyst",3,"click"],[1,"mbo-card-block-modal__footer"],["id","btn_card-block-modal_submit","bocc-button","raised","prefixIcon","block","bocc-theme","danger",3,"click"],["id","btn_card-block-modal_cancel","bocc-button","outline",3,"click"]],template:function(a,u){1&a&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"label",2),e.\u0275\u0275text(3," Bloquear tarjeta "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"p",3),e.\u0275\u0275text(5," Recuerda que una vez bloqueada la tarjeta dejar\xe1 de funcionar definitivamente, el proceso de reposici\xf3n de tu tarjeta tendr\xe1 un costo. "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"button",4),e.\u0275\u0275listener("click",function(){return u.onLinkRates()}),e.\u0275\u0275text(7," Ver tasas y tarifas "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",5)(9,"button",6),e.\u0275\u0275listener("click",function(){return u.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Bloquear"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"button",7),e.\u0275\u0275listener("click",function(){return u.onCancel()}),e.\u0275\u0275elementStart(13,"span"),e.\u0275\u0275text(14,"Cancelar"),e.\u0275\u0275elementEnd()()()())},dependencies:[v.CommonModule,C.P8],styles:["mbo-card-block-modal{position:relative;display:block;box-sizing:border-box}mbo-card-block-modal .mbo-card-block-modal__content{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4)}mbo-card-block-modal .mbo-card-block-modal__body{position:relative;display:flex;width:100%;padding:0rem var(--sizing-x6);box-sizing:border-box;flex-direction:column;row-gap:var(--sizing-x6)}mbo-card-block-modal .mbo-card-block-modal__title{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-darker-1000);text-transform:uppercase}mbo-card-block-modal .mbo-card-block-modal__message{position:relative;display:block;width:100%;text-align:center;color:var(--color-carbon-lighter-700)}mbo-card-block-modal .mbo-card-block-modal__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-x4);box-sizing:border-box;border-top:var(--border-1-lighter-300)}mbo-card-block-modal .mbo-card-block-modal__footer>button{width:100%}\n"],encapsulation:2}),d})()},83832:(B,k,t)=>{t.r(k),t.d(k,{MboCreditCardBlockPageModule:()=>i});var v=t(17007),h=t(78007),C=t(79798),m=t(30263),f=t(15861),e=t(99877),g=t(39904),y=t(95437),d=t(61589),b=t(18275),a=t(21339),u=t(48774),p=t(83413),P=t(45542),l=t(10464),M=t(78021),o=t(16442);let s=(()=>{class c{constructor(n,r,_,S,T){this.ref=n,this.activateRoute=r,this.modalService=_,this.mboProvider=S,this.managerCreditCardBlock=T,this.blocking=!1,this.requesting=!0,this.template=g.$d,this.backAction={id:"btn_credit-card-block_back",prefixIcon:"prev-page",label:"Atr\xe1s",click:()=>{this.mboProvider.navigation.back(g.Z6.CUSTOMER.PRODUCTS.INFO,{productId:this.creditCard?.id})}},this.cancelAction={id:"btn_credit-card-block_cancel",label:"Cancelar",click:()=>{this.mboProvider.navigation.back(g.Z6.CUSTOMER.PRODUCTS.HOME)}},this.rightActions=[{id:"btn_credit-card-block-page_download",icon:"arrow-download",click:()=>{this.mboProvider.snapshot(this.element)}}]}ngOnInit(){this.element=this.ref.nativeElement.querySelector("#crd_credit-card-block-page_template"),this.initializatedConfiguration()}onAction(n){"retry"===n?this.creditCardBlock():this.mboProvider.navigation.next(g.Z6.CUSTOMER.PRODUCTS.HOME)}onSubmit(){const n=this.modalService.create(a.w);n.open(),n.waiting().then(()=>{this.creditCardBlock()})}initializatedConfiguration(){var n=this;return(0,f.Z)(function*(){const{productId:r}=n.activateRoute.snapshot.queryParams;(yield n.managerCreditCardBlock.configuration(r)).when({success:_=>{if(!_)return n.mboProvider.navigation.next(g.Z6.CUSTOMER.PRODUCTS.HOME);n.creditCard=_}})})()}creditCardBlock(){var n=this;return(0,f.Z)(function*(){n.template=g.$d,n.blocking=!0,n.requesting=!0,(yield n.managerCreditCardBlock.block(n.creditCard)).when({success:r=>{n.template=(0,d.Q)(r)}},()=>{n.requesting=!1})})()}}return c.\u0275fac=function(n){return new(n||c)(e.\u0275\u0275directiveInject(e.ElementRef),e.\u0275\u0275directiveInject(h.ActivatedRoute),e.\u0275\u0275directiveInject(m.iM),e.\u0275\u0275directiveInject(y.ZL),e.\u0275\u0275directiveInject(b.S))},c.\u0275cmp=e.\u0275\u0275defineComponent({type:c,selectors:[["mbo-credit-card-block-page"]],decls:18,vars:14,consts:[[1,"mbo-credit-card-block-page__product",3,"hidden"],[1,"mbo-credit-card-block-page__content"],[1,"mbo-credit-card-block-page__header"],["title","Bloqueo",3,"leftAction","rightAction"],[1,"mbo-credit-card-block-page__body"],[1,"mbo-credit-card-block-page__title","subtitle2-medium"],[3,"color","icon","title","number","detail","statusColor","statusLabel"],[1,"mbo-credit-card-block-page__footer"],["id","btn_credit-card-block_submit","bocc-button","raised","bocc-theme","danger","prefixIcon","block-card",3,"click"],[3,"hidden"],[1,"mbo-credit-card-block-page__result","mbo-page__scroller"],[1,"mbo-credit-card-block-page__header","mbo-page__header",3,"hidden"],[3,"rightActions"],["id","crd_credit-card-block-page_template",3,"template","action"]],template:function(n,r){1&n&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),e.\u0275\u0275element(3,"bocc-header-form",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",4)(5,"label",5),e.\u0275\u0275text(6," \xbfDeseas bloquear la siguiente tarjeta de cr\xe9dito? "),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(7,"bocc-card-product-summary",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",7)(9,"button",8),e.\u0275\u0275listener("click",function(){return r.onSubmit()}),e.\u0275\u0275elementStart(10,"span"),e.\u0275\u0275text(11,"Bloquear"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(12,"mbo-page",9)(13,"div",10)(14,"div",11),e.\u0275\u0275element(15,"mbo-header-result",12),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"div",4)(17,"mbo-card-transaction-template",13),e.\u0275\u0275listener("action",function(S){return r.onAction(S)}),e.\u0275\u0275elementEnd()()()()),2&n&&(e.\u0275\u0275property("hidden",r.blocking),e.\u0275\u0275advance(3),e.\u0275\u0275property("leftAction",r.backAction)("rightAction",r.cancelAction),e.\u0275\u0275advance(4),e.\u0275\u0275property("color",null==r.creditCard?null:r.creditCard.color)("icon",null==r.creditCard?null:r.creditCard.logo)("title",null==r.creditCard?null:r.creditCard.nickname)("number",null==r.creditCard?null:r.creditCard.publicNumber)("detail",null==r.creditCard?null:r.creditCard.bank.name)("statusColor",null==r.creditCard||null==r.creditCard.status?null:r.creditCard.status.color)("statusLabel",null==r.creditCard||null==r.creditCard.status?null:r.creditCard.status.label),e.\u0275\u0275advance(5),e.\u0275\u0275property("hidden",!r.blocking),e.\u0275\u0275advance(2),e.\u0275\u0275property("hidden",r.requesting),e.\u0275\u0275advance(1),e.\u0275\u0275property("rightActions",r.rightActions),e.\u0275\u0275advance(2),e.\u0275\u0275property("template",r.template))},dependencies:[u.J,p.D,P.P,l.K,M.c,o.u],styles:["/*!\n * MBO ProductBlockCreditCard Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 17/Nov/2022\n * Updated: 10/Ene/2024\n*/mbo-credit-card-block-page{position:relative;display:block;width:100%;height:100%}mbo-credit-card-block-page mbo-page{background:var(--color-carbon-lighter-200)}mbo-credit-card-block-page .mbo-credit-card-block-page__product{position:relative;display:flex;width:100%;height:100%;overflow:hidden;flex-direction:column;justify-content:space-between}mbo-credit-card-block-page .mbo-credit-card-block-page__result{display:flex;flex-direction:column;margin-bottom:var(--sizing-safe-bottom-x12)}mbo-credit-card-block-page .mbo-credit-card-block-page__body{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x12);padding:0rem var(--sizing-form);box-sizing:border-box;margin-top:var(--sizing-x8)}mbo-credit-card-block-page .mbo-credit-card-block-page__title{color:var(--color-carbon-darker-1000)}mbo-credit-card-block-page .mbo-credit-card-block-page__footer{position:relative;display:flex;width:100%;flex-direction:column;row-gap:var(--sizing-x4);padding:var(--sizing-safe-footer-x8);box-sizing:border-box}mbo-credit-card-block-page .mbo-credit-card-block-page__footer button{width:100%}\n"],encapsulation:2}),c})(),i=(()=>{class c{}return c.\u0275fac=function(n){return new(n||c)},c.\u0275mod=e.\u0275\u0275defineNgModule({type:c}),c.\u0275inj=e.\u0275\u0275defineInjector({imports:[v.CommonModule,h.RouterModule.forChild([{path:"",component:s}]),m.Jx,m.D1,m.P8,C.KI,C.cN,C.tu]}),c})()}}]);