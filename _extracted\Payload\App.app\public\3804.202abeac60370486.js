(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3804],{83804:(ee,K,f)=>{f.r(K),f.d(K,{ion_popover:()=>H});var O=f(15861),d=f(42477),S=f(25030),y=f(78635),U=f(37389),W=f(28909),_=f(57346),R=f(37943),te=f(23814),V=f(39721),g=f(44963);f(72972),f(33006);const F=(t,e,o)=>{const r=e.getBoundingClientRect(),i=r.height;let n=r.width;return"cover"===t&&o&&(n=o.getBoundingClientRect().width),{contentWidth:n,contentHeight:i}},ne=(t,e,o)=>{let r=[];switch(e){case"hover":let i;r=[{eventName:"mouseenter",callback:(n=(0,O.Z)(function*(s){s.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{(0,y.r)(()=>{o.presentFromTrigger(s),i=void 0})},100)}),function(a){return n.apply(this,arguments)})},{eventName:"mouseleave",callback:n=>{i&&clearTimeout(i);const s=n.relatedTarget;s&&s.closest("ion-popover")!==o&&o.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}];break;case"context-menu":r=[{eventName:"contextmenu",callback:n=>{n.preventDefault(),o.presentFromTrigger(n)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}];break;default:r=[{eventName:"click",callback:n=>o.presentFromTrigger(n)},{eventName:"ionPopoverActivateTrigger",callback:n=>o.presentFromTrigger(n,!0)}]}var n;return r.forEach(({eventName:i,callback:n})=>t.addEventListener(i,n)),t.setAttribute("data-ion-popover-trigger","true"),()=>{r.forEach(({eventName:i,callback:n})=>t.removeEventListener(i,n)),t.removeAttribute("data-ion-popover-trigger")}},B=(t,e)=>e&&"ION-ITEM"===e.tagName?t.findIndex(o=>o===e):-1,N=t=>{const o=(0,y.g)(t).querySelector("button");o&&(0,y.r)(()=>o.focus())},pe=t=>{const e=function(){var o=(0,O.Z)(function*(r){var i;const n=document.activeElement;let s=[];const a=null===(i=r.target)||void 0===i?void 0:i.tagName;if("ION-POPOVER"===a||"ION-ITEM"===a){try{s=Array.from(t.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(r.key){case"ArrowLeft":(yield t.getParentPopover())&&t.dismiss(void 0,void 0,!1);break;case"ArrowDown":r.preventDefault();const l=((t,e)=>t[B(t,e)+1])(s,n);void 0!==l&&N(l);break;case"ArrowUp":r.preventDefault();const b=((t,e)=>t[B(t,e)-1])(s,n);void 0!==b&&N(b);break;case"Home":r.preventDefault();const h=s[0];void 0!==h&&N(h);break;case"End":r.preventDefault();const u=s[s.length-1];void 0!==u&&N(u);break;case"ArrowRight":case" ":case"Enter":if(n&&(t=>t.hasAttribute("data-ion-popover-trigger"))(n)){const v=new CustomEvent("ionPopoverActivateTrigger");n.dispatchEvent(v)}}}});return function(i){return o.apply(this,arguments)}}();return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},Z=(t,e,o,r,i,n,s,a,p,l,b)=>{var h;let u={top:0,left:0,width:0,height:0};if("event"===n){if(!b)return p;u={top:b.clientY,left:b.clientX,width:1,height:1}}else{const T=l||(null===(h=b?.detail)||void 0===h?void 0:h.ionShadowTarget)||b?.target;if(!T)return p;const P=T.getBoundingClientRect();u={top:P.top,left:P.left,width:P.width,height:P.height}}const v=le(s,u,e,o,r,i,t),x=fe(a,s,u,e,o),w=v.top+x.top,D=v.left+x.left,{arrowTop:m,arrowLeft:A}=de(s,r,i,w,D,e,o,t),{originX:k,originY:E}=ce(s,a,t);return{top:w,left:D,referenceCoordinates:u,arrowTop:m,arrowLeft:A,originX:k,originY:E}},ce=(t,e,o)=>{switch(t){case"top":return{originX:q(e),originY:"bottom"};case"bottom":return{originX:q(e),originY:"top"};case"left":return{originX:"right",originY:Y(e)};case"right":return{originX:"left",originY:Y(e)};case"start":return{originX:o?"left":"right",originY:Y(e)};case"end":return{originX:o?"right":"left",originY:Y(e)}}},q=t=>{switch(t){case"start":return"left";case"center":return"center";case"end":return"right"}},Y=t=>{switch(t){case"start":return"top";case"center":return"center";case"end":return"bottom"}},de=(t,e,o,r,i,n,s,a)=>{const p={arrowTop:r+s/2-e/2,arrowLeft:i+n-e/2},l={arrowTop:r+s/2-e/2,arrowLeft:i-1.5*e};switch(t){case"top":return{arrowTop:r+s,arrowLeft:i+n/2-e/2};case"bottom":return{arrowTop:r-o,arrowLeft:i+n/2-e/2};case"left":return p;case"right":return l;case"start":return a?l:p;case"end":return a?p:l;default:return{arrowTop:0,arrowLeft:0}}},le=(t,e,o,r,i,n,s)=>{const a={top:e.top,left:e.left-o-i},p={top:e.top,left:e.left+e.width+i};switch(t){case"top":return{top:e.top-r-n,left:e.left};case"right":return p;case"bottom":return{top:e.top+e.height+n,left:e.left};case"left":return a;case"start":return s?p:a;case"end":return s?a:p}},fe=(t,e,o,r,i)=>{switch(t){case"center":return ve(e,o,r,i);case"end":return he(e,o,r,i);default:return{top:0,left:0}}},he=(t,e,o,r)=>{switch(t){case"start":case"end":case"left":case"right":return{top:-(r-e.height),left:0};default:return{top:0,left:-(o-e.width)}}},ve=(t,e,o,r)=>{switch(t){case"start":case"end":case"left":case"right":return{top:-(r/2-e.height/2),left:0};default:return{top:0,left:-(o/2-e.width/2)}}},G=(t,e,o,r,i,n,s,a,p,l,b,h,u=0,v=0,x=0)=>{let w=u;const D=v;let k,m=o,A=e,E=l,I=b,c=!1,T=!1;const P=h?h.top+h.height:n/2-a/2,C=h?h.height:0;let M=!1;return m<r+p?(m=r,c=!0,E="left"):s+r+m+p>i&&(T=!0,m=i-s-r,E="right"),P+C+a>n&&("top"===t||"bottom"===t)&&(P-a>0?(A=Math.max(12,P-a-C-(x-1)),w=A+a,I="bottom",M=!0):k=r),{top:A,left:m,bottom:k,originX:E,originY:I,checkSafeAreaLeft:c,checkSafeAreaRight:T,arrowTop:w,arrowLeft:D,addPopoverBottomClass:M}},ge=(t,e)=>{var o;const{event:r,size:i,trigger:n,reference:s,side:a,align:p}=e,l=t.ownerDocument,b="rtl"===l.dir,h=l.defaultView.innerWidth,u=l.defaultView.innerHeight,v=(0,y.g)(t),x=v.querySelector(".popover-content"),w=v.querySelector(".popover-arrow"),D=n||(null===(o=r?.detail)||void 0===o?void 0:o.ionShadowTarget)||r?.target,{contentWidth:m,contentHeight:A}=F(i,x,D),{arrowWidth:k,arrowHeight:E}=(t=>{if(!t)return{arrowWidth:0,arrowHeight:0};const{width:e,height:o}=t.getBoundingClientRect();return{arrowWidth:e,arrowHeight:o}})(w),c=Z(b,m,A,k,E,s,a,p,{top:u/2-A/2,left:h/2-m/2,originX:b?"right":"left",originY:"top"},n,r),T="cover"===i?0:5,P="cover"===i?0:25,{originX:C,originY:M,top:$,left:L,bottom:j,checkSafeAreaLeft:z,checkSafeAreaRight:De,arrowTop:Ae,arrowLeft:Ee,addPopoverBottomClass:Ie}=G(a,c.top,c.left,T,h,u,m,A,P,c.originX,c.originY,c.referenceCoordinates,c.arrowTop,c.arrowLeft,E),Te=(0,g.c)(),J=(0,g.c)(),Q=(0,g.c)();return J.addElement(v.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),Q.addElement(v.querySelector(".popover-arrow")).addElement(v.querySelector(".popover-content")).fromTo("opacity",.01,1),Te.easing("ease").duration(100).beforeAddWrite(()=>{"cover"===i&&t.style.setProperty("--width",`${m}px`),Ie&&t.classList.add("popover-bottom"),void 0!==j&&x.style.setProperty("bottom",`${j}px`);let X=`${L}px`;z&&(X=`${L}px + var(--ion-safe-area-left, 0)`),De&&(X=`${L}px - var(--ion-safe-area-right, 0)`),x.style.setProperty("top",`calc(${$}px + var(--offset-y, 0))`),x.style.setProperty("left",`calc(${X} + var(--offset-x, 0))`),x.style.setProperty("transform-origin",`${M} ${C}`),null!==w&&(((t,e=!1,o,r)=>!(!o&&!r||"top"!==t&&"bottom"!==t&&e))(a,c.top!==$||c.left!==L,r,n)?(w.style.setProperty("top",`calc(${Ae}px + var(--offset-y, 0))`),w.style.setProperty("left",`calc(${Ee}px + var(--offset-x, 0))`)):w.style.setProperty("display","none"))}).addAnimation([J,Q])},be=t=>{const e=(0,y.g)(t),o=e.querySelector(".popover-content"),r=e.querySelector(".popover-arrow"),i=(0,g.c)(),n=(0,g.c)(),s=(0,g.c)();return n.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{t.style.removeProperty("--width"),t.classList.remove("popover-bottom"),o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("bottom"),o.style.removeProperty("transform-origin"),r&&(r.style.removeProperty("top"),r.style.removeProperty("left"),r.style.removeProperty("display"))}).duration(300).addAnimation([n,s])},we=(t,e)=>{var o;const{event:r,size:i,trigger:n,reference:s,side:a,align:p}=e,l=t.ownerDocument,b="rtl"===l.dir,h=l.defaultView.innerWidth,u=l.defaultView.innerHeight,v=(0,y.g)(t),x=v.querySelector(".popover-content"),w=n||(null===(o=r?.detail)||void 0===o?void 0:o.ionShadowTarget)||r?.target,{contentWidth:D,contentHeight:m}=F(i,x,w),k=Z(b,D,m,0,0,s,a,p,{top:u/2-m/2,left:h/2-D/2,originX:b?"right":"left",originY:"top"},n,r),E="cover"===i?0:12,{originX:I,originY:c,top:T,left:P,bottom:C}=G(a,k.top,k.left,E,h,u,D,m,0,k.originX,k.originY,k.referenceCoordinates),M=(0,g.c)(),$=(0,g.c)(),L=(0,g.c)(),j=(0,g.c)(),z=(0,g.c)();return $.addElement(v.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),L.addElement(v.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),j.addElement(x).beforeStyles({top:`calc(${T}px + var(--offset-y, 0px))`,left:`calc(${P}px + var(--offset-x, 0px))`,"transform-origin":`${c} ${I}`}).beforeAddWrite(()=>{void 0!==C&&x.style.setProperty("bottom",`${C}px`)}).fromTo("transform","scale(0.8)","scale(1)"),z.addElement(v.querySelector(".popover-viewport")).fromTo("opacity",.01,1),M.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{"cover"===i&&t.style.setProperty("--width",`${D}px`),"bottom"===c&&t.classList.add("popover-bottom")}).addAnimation([$,L,j,z])},ye=t=>{const e=(0,y.g)(t),o=e.querySelector(".popover-content"),r=(0,g.c)(),i=(0,g.c)(),n=(0,g.c)();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),n.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),r.easing("ease").afterAddWrite(()=>{t.style.removeProperty("--width"),t.classList.remove("popover-bottom"),o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("bottom"),o.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,n])},H=class{constructor(t){(0,d.r)(this,t),this.didPresent=(0,d.d)(this,"ionPopoverDidPresent",7),this.willPresent=(0,d.d)(this,"ionPopoverWillPresent",7),this.willDismiss=(0,d.d)(this,"ionPopoverWillDismiss",7),this.didDismiss=(0,d.d)(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=(0,d.d)(this,"didPresent",7),this.willPresentShorthand=(0,d.d)(this,"willPresent",7),this.willDismissShorthand=(0,d.d)(this,"willDismiss",7),this.didDismissShorthand=(0,d.d)(this,"didDismiss",7),this.ionMount=(0,d.d)(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=(0,S.C)(),this.lockController=(0,U.c)(),this.inline=!1,this.focusDescendantOnPresent=!1,this.onBackdropTap=()=>{this.dismiss(void 0,_.B)},this.onLifecycle=e=>{const o=this.usersElement,r=_e[e.type];if(o&&r){const i=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:e.detail});o.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{const{trigger:e,triggerAction:o,el:r,destroyTriggerInteraction:i}=this;if(i&&i(),void 0===e)return;const n=this.triggerEl=void 0!==e?document.getElementById(e):null;n?this.destroyTriggerInteraction=ne(n,o,r):(0,W.p)(`A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el)},this.configureKeyboardInteraction=()=>{const{destroyKeyboardInteraction:e,el:o}=this;e&&e(),this.destroyKeyboardInteraction=pe(o)},this.configureDismissInteraction=()=>{const{destroyDismissInteraction:e,parentPopover:o,triggerAction:r,triggerEl:i,el:n}=this;!o||!i||(e&&e(),this.destroyDismissInteraction=((t,e,o,r)=>{let i=[];const s=(0,y.g)(r).querySelector(".popover-content");return i="hover"===e?[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==t&&o.dismiss(void 0,void 0,!1)}}]:[{eventName:"click",callback:a=>{a.target.closest("[data-ion-popover-trigger]")!==t?o.dismiss(void 0,void 0,!1):a.stopPropagation()}}],i.forEach(({eventName:a,callback:p})=>s.addEventListener(a,p)),()=>{i.forEach(({eventName:a,callback:p})=>s.removeEventListener(a,p))}})(i,r,n,o))},this.presented=!1,this.hasController=!1,this.delegate=void 0,this.overlayIndex=void 0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.component=void 0,this.componentProps=void 0,this.keyboardClose=!0,this.cssClass=void 0,this.backdropDismiss=!0,this.event=void 0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.triggerAction="click",this.trigger=void 0,this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.alignment=void 0,this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.keepContentsMounted=!1}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(t,e){!0===t&&!1===e?this.present():!1===t&&!0===e&&this.dismiss()}connectedCallback(){const{configureTriggerInteraction:t,el:e}=this;(0,_.j)(e),t()}disconnectedCallback(){const{destroyTriggerInteraction:t}=this;t&&t()}componentWillLoad(){const{el:t}=this,e=(0,_.k)(t);this.parentPopover=t.closest(`ion-popover:not(#${e})`),void 0===this.alignment&&(this.alignment="ios"===(0,R.b)(this)?"center":"start")}componentDidLoad(){const{parentPopover:t,isOpen:e}=this;!0===e&&(0,y.r)(()=>this.present()),t&&(0,y.a)(t,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(t,e=!1){var o=this;return(0,O.Z)(function*(){o.focusDescendantOnPresent=e,yield o.present(t),o.focusDescendantOnPresent=!1})()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};const o=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:o,delegate:this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate}}present(t){var e=this;return(0,O.Z)(function*(){const o=yield e.lockController.lock();if(e.presented)return void o();const{el:r}=e,{inline:i,delegate:n}=e.getDelegate(!0);e.ionMount.emit(),e.usersElement=yield(0,S.a)(n,r,e.component,["popover-viewport"],e.componentProps,i),e.keyboardEvents||e.configureKeyboardInteraction(),e.configureDismissInteraction(),(0,y.m)(r)?yield(0,V.e)(e.usersElement):e.keepContentsMounted||(yield(0,V.w)()),yield(0,_.f)(e,"popoverEnter",ge,we,{event:t||e.event,size:e.size,trigger:e.triggerEl,reference:e.reference,side:e.side,align:e.alignment}),e.focusDescendantOnPresent&&(0,_.n)(e.el,e.el),o()})()}dismiss(t,e,o=!0){var r=this;return(0,O.Z)(function*(){const i=yield r.lockController.lock(),{destroyKeyboardInteraction:n,destroyDismissInteraction:s}=r;o&&r.parentPopover&&r.parentPopover.dismiss(t,e,o);const a=yield(0,_.g)(r,t,e,"popoverLeave",be,ye,r.event);if(a){n&&(n(),r.destroyKeyboardInteraction=void 0),s&&(s(),r.destroyDismissInteraction=void 0);const{delegate:p}=r.getDelegate();yield(0,S.d)(p,r.usersElement)}return i(),a})()}getParentPopover(){var t=this;return(0,O.Z)(function*(){return t.parentPopover})()}onDidDismiss(){return(0,_.h)(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return(0,_.h)(this.el,"ionPopoverWillDismiss")}render(){const t=(0,R.b)(this),{onLifecycle:e,parentPopover:o,dismissOnSelect:r,side:i,arrow:n,htmlAttributes:s}=this,a=(0,R.a)("desktop"),p=n&&!o;return(0,d.h)(d.H,Object.assign({key:"f3b86c7bc6ef6b1b27a6ac78e7ddd98e46223bd4","aria-modal":"true","no-router":!0,tabindex:"-1"},s,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},(0,te.g)(this.cssClass)),{[t]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":a,[`popover-side-${i}`]:!0,"popover-nested":!!o}),onIonPopoverDidPresent:e,onIonPopoverWillPresent:e,onIonPopoverWillDismiss:e,onIonPopoverDidDismiss:e,onIonBackdropTap:this.onBackdropTap}),!o&&(0,d.h)("ion-backdrop",{key:"f7fbd914100838ed7d419eedd19e6b1efa691127",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),(0,d.h)("div",{key:"a651cb4daa6556e761c2e2b672306ad25e4c9429",class:"popover-wrapper ion-overlay-wrapper",onClick:r?()=>this.dismiss():void 0},p&&(0,d.h)("div",{key:"7c5c7d8d6f9530535124e3fc75a38055f68b7589",class:"popover-arrow",part:"arrow"}),(0,d.h)("div",{key:"9f92fff4f36941e8f7de9774aef7d7508ca5cfe5",class:"popover-content",part:"content"},(0,d.h)("slot",{key:"26c4e3df40a4832caff996ead3321c656eb5704f"}))))}get el(){return(0,d.f)(this)}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}},_e={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};H.style={ios:':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, #e6e6e6)}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}@supports (inset-inline-start: 0){.popover-arrow::after{inset-inline-start:3px}}@supports not (inset-inline-start: 0){.popover-arrow::after{left:3px}:host-context([dir=rtl]) .popover-arrow::after{left:unset;right:unset;right:3px}[dir=rtl] .popover-arrow::after{left:unset;right:unset;right:3px}@supports selector(:dir(rtl)){.popover-arrow::after:dir(rtl){left:unset;right:unset;right:3px}}}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',md:":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}"}},37389:(ee,K,f)=>{f.d(K,{c:()=>d});var O=f(15861);const d=()=>{let S;return{lock:function(){var U=(0,O.Z)(function*(){const W=S;let _;return S=new Promise(R=>_=R),void 0!==W&&(yield W),_});return function(){return U.apply(this,arguments)}}()}}}}]);