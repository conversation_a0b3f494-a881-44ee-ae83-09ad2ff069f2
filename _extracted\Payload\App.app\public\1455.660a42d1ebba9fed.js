(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1455],{37907:(b,p,n)=>{n.d(p,{L:()=>u,m:()=>a});var m=n(15861),l=n(87956),y=n(53113),i=n(98699);class v{constructor(d,t,e){this.source=d,this.destination=t,this.amount=e}}function E(o){return new v(o.source,o.destination,o.amount)}var I=n(71776),h=n(39904),U=n(87903),s=n(42168),C=n(84757),c=n(99877);let M=(()=>{class o{constructor(t){this.http=t}send(t){const e={hashCheckingAcct:t.destination.id,hashLoanAcct:t.source.id,amount:String(t.amount)};return(0,s.firstValueFrom)(this.http.post(h.bV.TRANSACTIONS.CREDIT_QUOTA,e).pipe((0,C.map)(r=>(0,U.l1)(r,"SUCCESS")))).catch(r=>(0,U.rU)(r))}}return o.\u0275fac=function(t){return new(t||o)(c.\u0275\u0275inject(I.HttpClient))},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var S=n(20691);let P=(()=>{class o extends S.Store{constructor(){super({confirmation:!1,fromCustomer:!1})}setSource(t,e=!1){this.reduce(r=>({...r,source:t,fromCustomer:e}))}getSource(){return this.select(({source:t})=>t)}itIsFromCustomer(){return this.select(({fromCustomer:t})=>t)}setDestination(t){this.reduce(e=>({...e,destination:t}))}setAmount(t){this.reduce(e=>({...e,amount:t}))}selectForAmount(){return this.select(({confirmation:t,amount:e,source:r})=>({amount:e,confirmation:t,source:r}))}itIsConfirmation(){return this.select(({confirmation:t})=>t)}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})(),u=(()=>{class o{constructor(t,e,r,f){this.products=t,this.eventBusService=e,this.repository=r,this.store=f}setSource(t){var e=this;return(0,m.Z)(function*(){try{return yield e.products.requestInformation(t),i.Either.success(e.store.setSource(t))}catch({message:r}){return i.Either.failure({message:r})}})()}setDestination(t){try{return i.Either.success(this.store.setDestination(t))}catch({message:e}){return i.Either.failure({message:e})}}setAmount(t){try{return i.Either.success(this.store.setAmount(t))}catch({message:e}){return i.Either.failure({message:e})}}reset(){try{const t=this.store.itIsFromCustomer(),e=this.store.getSource();return this.store.reset(),i.Either.success({fromCustomer:t,source:e})}catch({message:t}){return i.Either.failure({message:t})}}send(){var t=this;return(0,m.Z)(function*(){const e=E(t.store.currentState),r=yield t.save(e);return t.eventBusService.emit(r.channel),i.Either.success({creditUseQuota:e,status:r})})()}save(t){try{return this.repository.send(t)}catch({message:e}){return Promise.resolve(y.LN.error(e))}}}return o.\u0275fac=function(t){return new(t||o)(c.\u0275\u0275inject(l.M5),c.\u0275\u0275inject(l.Yd),c.\u0275\u0275inject(M),c.\u0275\u0275inject(P))},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();var g=n(89148);let a=(()=>{class o{constructor(t,e,r){this.products=t,this.productService=e,this.store=r}source(){var t=this;return(0,m.Z)(function*(){try{const e=t.store.itIsConfirmation(),r=yield t.requestCredits();return i.Either.success({confirmation:e,products:r})}catch({message:e}){return i.Either.failure({message:e})}})()}destination(t){var e=this;return(0,m.Z)(function*(){try{const r=yield e.products.requestAccountsForTransfer(),f=yield e.requestCredits(),A=e.store.itIsConfirmation(),O=e.requestCredit(f,t);return i.Either.success({accounts:r,confirmation:A,products:f,source:O})}catch({message:r}){return i.Either.failure({message:r})}})()}amount(){var t=this;return(0,m.Z)(function*(){try{const{amount:e,confirmation:r,source:f}=t.store.selectForAmount(),A=yield t.requestSection(f);return i.Either.success({confirmation:r,section:A,source:f,value:e})}catch({message:e}){return i.Either.failure({message:e})}})()}confirmation(){try{const t=E(this.store.currentState);return i.Either.success({creditUseQuota:t})}catch({message:t}){return i.Either.failure({message:t})}}requestCredits(){return this.products.requestProducts([g.Gt.ResolvingCredit])}requestCredit(t,e){let r=this.store.getSource();return!r&&e&&(r=t.find(({id:f})=>e===f),this.store.setSource(r,!0)),r}requestSection(t){return this.productService.requestInformation(t).then(e=>e?.getSection(g.Av.LoanQuotaAvailable))}}return o.\u0275fac=function(t){return new(t||o)(c.\u0275\u0275inject(l.hM),c.\u0275\u0275inject(l.M5),c.\u0275\u0275inject(P))},o.\u0275prov=c.\u0275\u0275defineInjectable({token:o,factory:o.\u0275fac,providedIn:"root"}),o})()},44793:(b,p,n)=>{n.d(p,{Z:()=>I});var m=n(30263),l=n(39904),y=n(95437),i=n(37907),v=n(99877);let I=(()=>{class h{constructor(s,C,c){this.modalConfirmation=s,this.mboProvider=C,this.managerUseQuota=c}execute(s=!0){s?this.modalConfirmation.execute({title:"Cancelar transacci\xf3n",message:"\xbfEstas seguro que deseas cancelar la transferencia de cupo actualmente en progreso?",accept:{label:"Aceptar",click:()=>{this.cancelConfirmed()}},decline:{label:"Cancelar"}}):this.cancelConfirmed()}cancelConfirmed(){this.managerUseQuota.reset().when({success:({fromCustomer:s,source:C})=>{s?this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.INFO,{productId:C.id}):this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.HOME)},failure:()=>{this.mboProvider.navigation.back(l.Z6.CUSTOMER.PRODUCTS.HOME)}})}}return h.\u0275fac=function(s){return new(s||h)(v.\u0275\u0275inject(m.$e),v.\u0275\u0275inject(y.ZL),v.\u0275\u0275inject(i.L))},h.\u0275prov=v.\u0275\u0275defineInjectable({token:h,factory:h.\u0275fac,providedIn:"root"}),h})()},31455:(b,p,n)=>{n.r(p),n.d(p,{MboCreditUseQuotaDestinationPageModule:()=>P});var m=n(17007),l=n(78007),y=n(30263),i=n(79798),v=n(15861),E=n(39904),I=n(95437),h=n(37907),U=n(44793),s=n(99877),C=n(48774),c=n(4663);const M=E.Z6.TRANSACTIONS.CREDIT_USE_QUOTA;let S=(()=>{class u{constructor(a,o,d,t,e){this.activateRoute=a,this.mboProvider=o,this.requestConfiguration=d,this.managerUseQuota=t,this.cancelProvider=e,this.confirmation=!1,this.backInvalid=!1,this.accounts=[],this.requesting=!0,this.backAction={id:"btn_credit-use-quota-destination_back",prefixIcon:"prev-page",label:"Atr\xe1s",hidden:()=>this.confirmation||this.backInvalid,click:()=>{this.mboProvider.navigation.back(M.SOURCE)}},this.cancelAction={id:"btn_credit-use-quota-destination_cancel",label:"Cancelar",click:()=>{this.cancelProvider.execute(this.confirmation)}}}ngOnInit(){this.initializatedConfiguration()}onProduct(a){this.managerUseQuota.setDestination(a).when({success:()=>{this.mboProvider.navigation.next(this.confirmation?M.CONFIRMATION:M.AMOUNT)}})}initializatedConfiguration(){var a=this;return(0,v.Z)(function*(){const{productId:o}=a.activateRoute.snapshot.queryParams;(yield a.requestConfiguration.destination(o)).when({success:({accounts:d,confirmation:t,products:e,source:r})=>{if(!r)return a.mboProvider.navigation.back(E.Z6.CUSTOMER.PRODUCTS.HOME,{productId:o});a.backInvalid=e.length<2,a.accounts=d,a.confirmation=t}},()=>{a.requesting=!1})})()}}return u.\u0275fac=function(a){return new(a||u)(s.\u0275\u0275directiveInject(l.ActivatedRoute),s.\u0275\u0275directiveInject(I.ZL),s.\u0275\u0275directiveInject(h.m),s.\u0275\u0275directiveInject(h.L),s.\u0275\u0275directiveInject(U.Z))},u.\u0275cmp=s.\u0275\u0275defineComponent({type:u,selectors:[["mbo-credit-use-quota-destination-page"]],decls:6,vars:4,consts:[[1,"mbo-credit-use-quota-destination-page__content"],[1,"mbo-credit-use-quota-destination-page__header"],["title","Destino","progress","50%",3,"leftAction","rightAction"],[1,"mbo-credit-use-quota-destination-page__body"],["title","CUENTAS DISPONIBLES",3,"skeleton","products","select"]],template:function(a,o){1&a&&(s.\u0275\u0275elementStart(0,"div",0)(1,"div",1),s.\u0275\u0275element(2,"bocc-header-form",2),s.\u0275\u0275elementEnd(),s.\u0275\u0275elementStart(3,"div",3)(4,"mbo-product-source-selector",4),s.\u0275\u0275listener("select",function(t){return o.onProduct(t)}),s.\u0275\u0275text(5," \xbfA qu\xe9 cuenta vas a enviar el dinero? "),s.\u0275\u0275elementEnd()()()),2&a&&(s.\u0275\u0275advance(2),s.\u0275\u0275property("leftAction",o.backAction)("rightAction",o.cancelAction),s.\u0275\u0275advance(2),s.\u0275\u0275property("skeleton",o.requesting)("products",o.accounts))},dependencies:[C.J,c.c],styles:["/*!\n * MBO CreditUseQuotaDestination Page\n * v2.0.0\n * Author: MB Frontend Developers\n * Created: 15/Nov/2022\n * Updated: 05/Ene/2024\n*/mbo-credit-use-quota-destination-page{position:relative;display:block;width:100%;height:100%;overflow:auto}mbo-credit-use-quota-destination-page .mbo-credit-use-quota-destination-page__content{position:relative;width:100%;margin-bottom:var(--sizing-x4)}mbo-credit-use-quota-destination-page .mbo-credit-use-quota-destination-page__body{position:relative;width:100%;padding:0rem var(--sizing-form);box-sizing:border-box;margin:var(--sizing-safe-body-x8)}\n"],encapsulation:2}),u})(),P=(()=>{class u{}return u.\u0275fac=function(a){return new(a||u)},u.\u0275mod=s.\u0275\u0275defineNgModule({type:u}),u.\u0275inj=s.\u0275\u0275defineInjector({imports:[m.CommonModule,l.RouterModule.forChild([{path:"",component:S}]),y.Jx,i.cV]}),u})()}}]);